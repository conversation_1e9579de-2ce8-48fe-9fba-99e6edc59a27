2025-06-12 00:00:02,932 - INFO - =================使用默认全量同步=============
2025-06-12 00:00:04,657 - INFO - MySQL查询成功，共获取 3929 条记录
2025-06-12 00:00:04,658 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-12 00:00:04,691 - INFO - 开始处理日期: 2025-01
2025-06-12 00:00:04,694 - INFO - Request Parameters - Page 1:
2025-06-12 00:00:04,694 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:04,694 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:05,416 - INFO - Response - Page 1:
2025-06-12 00:00:05,616 - INFO - 第 1 页获取到 100 条记录
2025-06-12 00:00:05,616 - INFO - Request Parameters - Page 2:
2025-06-12 00:00:05,616 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:05,616 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:06,338 - INFO - Response - Page 2:
2025-06-12 00:00:06,541 - INFO - 第 2 页获取到 100 条记录
2025-06-12 00:00:06,541 - INFO - Request Parameters - Page 3:
2025-06-12 00:00:06,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:06,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:07,167 - INFO - Response - Page 3:
2025-06-12 00:00:07,368 - INFO - 第 3 页获取到 100 条记录
2025-06-12 00:00:07,368 - INFO - Request Parameters - Page 4:
2025-06-12 00:00:07,368 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:07,368 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:07,916 - INFO - Response - Page 4:
2025-06-12 00:00:08,117 - INFO - 第 4 页获取到 100 条记录
2025-06-12 00:00:08,117 - INFO - Request Parameters - Page 5:
2025-06-12 00:00:08,117 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:08,117 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:08,633 - INFO - Response - Page 5:
2025-06-12 00:00:08,838 - INFO - 第 5 页获取到 100 条记录
2025-06-12 00:00:08,838 - INFO - Request Parameters - Page 6:
2025-06-12 00:00:08,838 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:08,838 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:09,353 - INFO - Response - Page 6:
2025-06-12 00:00:09,557 - INFO - 第 6 页获取到 100 条记录
2025-06-12 00:00:09,557 - INFO - Request Parameters - Page 7:
2025-06-12 00:00:09,557 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:09,557 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:10,030 - INFO - Response - Page 7:
2025-06-12 00:00:10,244 - INFO - 第 7 页获取到 82 条记录
2025-06-12 00:00:10,244 - INFO - 查询完成，共获取到 682 条记录
2025-06-12 00:00:10,244 - INFO - 获取到 682 条表单数据
2025-06-12 00:00:10,244 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-12 00:00:10,260 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 00:00:10,260 - INFO - 开始处理日期: 2025-02
2025-06-12 00:00:10,260 - INFO - Request Parameters - Page 1:
2025-06-12 00:00:10,260 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:10,260 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:10,791 - INFO - Response - Page 1:
2025-06-12 00:00:10,994 - INFO - 第 1 页获取到 100 条记录
2025-06-12 00:00:10,994 - INFO - Request Parameters - Page 2:
2025-06-12 00:00:10,994 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:10,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:11,510 - INFO - Response - Page 2:
2025-06-12 00:00:11,713 - INFO - 第 2 页获取到 100 条记录
2025-06-12 00:00:11,713 - INFO - Request Parameters - Page 3:
2025-06-12 00:00:11,713 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:11,713 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:12,136 - INFO - Response - Page 3:
2025-06-12 00:00:12,338 - INFO - 第 3 页获取到 100 条记录
2025-06-12 00:00:12,338 - INFO - Request Parameters - Page 4:
2025-06-12 00:00:12,338 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:12,338 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:12,791 - INFO - Response - Page 4:
2025-06-12 00:00:12,994 - INFO - 第 4 页获取到 100 条记录
2025-06-12 00:00:12,994 - INFO - Request Parameters - Page 5:
2025-06-12 00:00:12,994 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:12,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:13,463 - INFO - Response - Page 5:
2025-06-12 00:00:13,663 - INFO - 第 5 页获取到 100 条记录
2025-06-12 00:00:13,663 - INFO - Request Parameters - Page 6:
2025-06-12 00:00:13,663 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:13,663 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:14,181 - INFO - Response - Page 6:
2025-06-12 00:00:14,384 - INFO - 第 6 页获取到 100 条记录
2025-06-12 00:00:14,384 - INFO - Request Parameters - Page 7:
2025-06-12 00:00:14,384 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:14,384 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:14,881 - INFO - Response - Page 7:
2025-06-12 00:00:15,082 - INFO - 第 7 页获取到 70 条记录
2025-06-12 00:00:15,082 - INFO - 查询完成，共获取到 670 条记录
2025-06-12 00:00:15,083 - INFO - 获取到 670 条表单数据
2025-06-12 00:00:15,095 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-12 00:00:15,107 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 00:00:15,107 - INFO - 开始处理日期: 2025-03
2025-06-12 00:00:15,108 - INFO - Request Parameters - Page 1:
2025-06-12 00:00:15,108 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:15,108 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:15,586 - INFO - Response - Page 1:
2025-06-12 00:00:15,786 - INFO - 第 1 页获取到 100 条记录
2025-06-12 00:00:15,786 - INFO - Request Parameters - Page 2:
2025-06-12 00:00:15,786 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:15,786 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:16,275 - INFO - Response - Page 2:
2025-06-12 00:00:16,478 - INFO - 第 2 页获取到 100 条记录
2025-06-12 00:00:16,478 - INFO - Request Parameters - Page 3:
2025-06-12 00:00:16,478 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:16,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:16,992 - INFO - Response - Page 3:
2025-06-12 00:00:17,195 - INFO - 第 3 页获取到 100 条记录
2025-06-12 00:00:17,195 - INFO - Request Parameters - Page 4:
2025-06-12 00:00:17,195 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:17,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:17,669 - INFO - Response - Page 4:
2025-06-12 00:00:17,870 - INFO - 第 4 页获取到 100 条记录
2025-06-12 00:00:17,870 - INFO - Request Parameters - Page 5:
2025-06-12 00:00:17,870 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:17,870 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:18,570 - INFO - Response - Page 5:
2025-06-12 00:00:18,770 - INFO - 第 5 页获取到 100 条记录
2025-06-12 00:00:18,770 - INFO - Request Parameters - Page 6:
2025-06-12 00:00:18,770 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:18,770 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:19,434 - INFO - Response - Page 6:
2025-06-12 00:00:19,634 - INFO - 第 6 页获取到 100 条记录
2025-06-12 00:00:19,634 - INFO - Request Parameters - Page 7:
2025-06-12 00:00:19,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:19,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:20,108 - INFO - Response - Page 7:
2025-06-12 00:00:20,324 - INFO - 第 7 页获取到 61 条记录
2025-06-12 00:00:20,324 - INFO - 查询完成，共获取到 661 条记录
2025-06-12 00:00:20,324 - INFO - 获取到 661 条表单数据
2025-06-12 00:00:20,324 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-12 00:00:20,339 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 00:00:20,339 - INFO - 开始处理日期: 2025-04
2025-06-12 00:00:20,339 - INFO - Request Parameters - Page 1:
2025-06-12 00:00:20,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:20,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:20,851 - INFO - Response - Page 1:
2025-06-12 00:00:21,051 - INFO - 第 1 页获取到 100 条记录
2025-06-12 00:00:21,051 - INFO - Request Parameters - Page 2:
2025-06-12 00:00:21,051 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:21,051 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:21,624 - INFO - Response - Page 2:
2025-06-12 00:00:21,825 - INFO - 第 2 页获取到 100 条记录
2025-06-12 00:00:21,825 - INFO - Request Parameters - Page 3:
2025-06-12 00:00:21,825 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:21,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:22,356 - INFO - Response - Page 3:
2025-06-12 00:00:22,559 - INFO - 第 3 页获取到 100 条记录
2025-06-12 00:00:22,559 - INFO - Request Parameters - Page 4:
2025-06-12 00:00:22,559 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:22,559 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:23,107 - INFO - Response - Page 4:
2025-06-12 00:00:23,308 - INFO - 第 4 页获取到 100 条记录
2025-06-12 00:00:23,308 - INFO - Request Parameters - Page 5:
2025-06-12 00:00:23,308 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:23,308 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:23,980 - INFO - Response - Page 5:
2025-06-12 00:00:24,184 - INFO - 第 5 页获取到 100 条记录
2025-06-12 00:00:24,184 - INFO - Request Parameters - Page 6:
2025-06-12 00:00:24,184 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:24,184 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:24,684 - INFO - Response - Page 6:
2025-06-12 00:00:24,886 - INFO - 第 6 页获取到 100 条记录
2025-06-12 00:00:24,886 - INFO - Request Parameters - Page 7:
2025-06-12 00:00:24,886 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:24,886 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:25,355 - INFO - Response - Page 7:
2025-06-12 00:00:25,556 - INFO - 第 7 页获取到 56 条记录
2025-06-12 00:00:25,556 - INFO - 查询完成，共获取到 656 条记录
2025-06-12 00:00:25,556 - INFO - 获取到 656 条表单数据
2025-06-12 00:00:25,558 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-12 00:00:25,574 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 00:00:25,574 - INFO - 开始处理日期: 2025-05
2025-06-12 00:00:25,574 - INFO - Request Parameters - Page 1:
2025-06-12 00:00:25,574 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:25,574 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:26,084 - INFO - Response - Page 1:
2025-06-12 00:00:26,286 - INFO - 第 1 页获取到 100 条记录
2025-06-12 00:00:26,286 - INFO - Request Parameters - Page 2:
2025-06-12 00:00:26,286 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:26,286 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:26,797 - INFO - Response - Page 2:
2025-06-12 00:00:26,997 - INFO - 第 2 页获取到 100 条记录
2025-06-12 00:00:26,997 - INFO - Request Parameters - Page 3:
2025-06-12 00:00:26,997 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:26,997 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:27,552 - INFO - Response - Page 3:
2025-06-12 00:00:27,753 - INFO - 第 3 页获取到 100 条记录
2025-06-12 00:00:27,753 - INFO - Request Parameters - Page 4:
2025-06-12 00:00:27,753 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:27,753 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:28,250 - INFO - Response - Page 4:
2025-06-12 00:00:28,450 - INFO - 第 4 页获取到 100 条记录
2025-06-12 00:00:28,450 - INFO - Request Parameters - Page 5:
2025-06-12 00:00:28,450 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:28,450 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:28,952 - INFO - Response - Page 5:
2025-06-12 00:00:29,153 - INFO - 第 5 页获取到 100 条记录
2025-06-12 00:00:29,153 - INFO - Request Parameters - Page 6:
2025-06-12 00:00:29,153 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:29,153 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:29,635 - INFO - Response - Page 6:
2025-06-12 00:00:29,841 - INFO - 第 6 页获取到 100 条记录
2025-06-12 00:00:29,841 - INFO - Request Parameters - Page 7:
2025-06-12 00:00:29,841 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:29,841 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:30,252 - INFO - Response - Page 7:
2025-06-12 00:00:30,466 - INFO - 第 7 页获取到 40 条记录
2025-06-12 00:00:30,466 - INFO - 查询完成，共获取到 640 条记录
2025-06-12 00:00:30,466 - INFO - 获取到 640 条表单数据
2025-06-12 00:00:30,466 - INFO - 当前日期 2025-05 有 640 条MySQL数据需要处理
2025-06-12 00:00:30,482 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 00:00:30,482 - INFO - 开始处理日期: 2025-06
2025-06-12 00:00:30,482 - INFO - Request Parameters - Page 1:
2025-06-12 00:00:30,482 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:30,482 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:31,080 - INFO - Response - Page 1:
2025-06-12 00:00:31,280 - INFO - 第 1 页获取到 100 条记录
2025-06-12 00:00:31,280 - INFO - Request Parameters - Page 2:
2025-06-12 00:00:31,280 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:31,280 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:31,919 - INFO - Response - Page 2:
2025-06-12 00:00:32,119 - INFO - 第 2 页获取到 100 条记录
2025-06-12 00:00:32,119 - INFO - Request Parameters - Page 3:
2025-06-12 00:00:32,119 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:32,119 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:32,858 - INFO - Response - Page 3:
2025-06-12 00:00:33,061 - INFO - 第 3 页获取到 100 条记录
2025-06-12 00:00:33,061 - INFO - Request Parameters - Page 4:
2025-06-12 00:00:33,061 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:33,061 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:33,652 - INFO - Response - Page 4:
2025-06-12 00:00:33,853 - INFO - 第 4 页获取到 100 条记录
2025-06-12 00:00:33,853 - INFO - Request Parameters - Page 5:
2025-06-12 00:00:33,853 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:33,853 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:34,498 - INFO - Response - Page 5:
2025-06-12 00:00:34,717 - INFO - 第 5 页获取到 100 条记录
2025-06-12 00:00:34,717 - INFO - Request Parameters - Page 6:
2025-06-12 00:00:34,717 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:34,717 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:35,247 - INFO - Response - Page 6:
2025-06-12 00:00:35,447 - INFO - 第 6 页获取到 100 条记录
2025-06-12 00:00:35,447 - INFO - Request Parameters - Page 7:
2025-06-12 00:00:35,447 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 00:00:35,447 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 00:00:35,904 - INFO - Response - Page 7:
2025-06-12 00:00:36,107 - INFO - 第 7 页获取到 20 条记录
2025-06-12 00:00:36,107 - INFO - 查询完成，共获取到 620 条记录
2025-06-12 00:00:36,108 - INFO - 获取到 620 条表单数据
2025-06-12 00:00:36,123 - INFO - 当前日期 2025-06 有 620 条MySQL数据需要处理
2025-06-12 00:00:36,123 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMRX
2025-06-12 00:00:36,545 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMRX
2025-06-12 00:00:36,545 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7760.0, 'new_value': 20120.0}, {'field': 'total_amount', 'old_value': 7760.0, 'new_value': 20120.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 5}]
2025-06-12 00:00:36,545 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM9Y
2025-06-12 00:00:37,001 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM9Y
2025-06-12 00:00:37,001 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5480.0, 'new_value': 6980.0}, {'field': 'total_amount', 'old_value': 25380.0, 'new_value': 26880.0}, {'field': 'order_count', 'old_value': 257, 'new_value': 275}]
2025-06-12 00:00:37,002 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNY
2025-06-12 00:00:37,467 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNY
2025-06-12 00:00:37,467 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30359.0, 'new_value': 33592.0}, {'field': 'offline_amount', 'old_value': 41826.0, 'new_value': 44958.0}, {'field': 'total_amount', 'old_value': 72185.0, 'new_value': 78550.0}, {'field': 'order_count', 'old_value': 1522, 'new_value': 1674}]
2025-06-12 00:00:37,467 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM5Z
2025-06-12 00:00:37,967 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM5Z
2025-06-12 00:00:37,967 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6423.9, 'new_value': 6483.7}, {'field': 'total_amount', 'old_value': 6423.9, 'new_value': 6483.7}, {'field': 'order_count', 'old_value': 79, 'new_value': 80}]
2025-06-12 00:00:37,967 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGZ
2025-06-12 00:00:38,356 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGZ
2025-06-12 00:00:38,356 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 321126.0, 'new_value': 358976.0}, {'field': 'total_amount', 'old_value': 321126.0, 'new_value': 358976.0}, {'field': 'order_count', 'old_value': 78, 'new_value': 85}]
2025-06-12 00:00:38,356 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMXZ
2025-06-12 00:00:38,841 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMXZ
2025-06-12 00:00:38,841 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9360.0, 'new_value': 9744.0}, {'field': 'total_amount', 'old_value': 9360.0, 'new_value': 9744.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-06-12 00:00:38,841 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM301
2025-06-12 00:00:39,325 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM301
2025-06-12 00:00:39,325 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3352.0, 'new_value': 3410.0}, {'field': 'offline_amount', 'old_value': 29898.0, 'new_value': 30656.0}, {'field': 'total_amount', 'old_value': 33250.0, 'new_value': 34066.0}, {'field': 'order_count', 'old_value': 226, 'new_value': 229}]
2025-06-12 00:00:39,325 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKB
2025-06-12 00:00:39,810 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKB
2025-06-12 00:00:39,810 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1958.0, 'new_value': 2327.0}, {'field': 'offline_amount', 'old_value': 6465.0, 'new_value': 6664.0}, {'field': 'total_amount', 'old_value': 8423.0, 'new_value': 8991.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 25}]
2025-06-12 00:00:39,810 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQB
2025-06-12 00:00:40,244 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQB
2025-06-12 00:00:40,244 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 73565.69, 'new_value': 80586.97}, {'field': 'offline_amount', 'old_value': 153284.72, 'new_value': 162406.11}, {'field': 'total_amount', 'old_value': 226850.41, 'new_value': 242993.08}, {'field': 'order_count', 'old_value': 1604, 'new_value': 1733}]
2025-06-12 00:00:40,245 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0C
2025-06-12 00:00:40,747 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0C
2025-06-12 00:00:40,747 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50060.0, 'new_value': 50640.0}, {'field': 'total_amount', 'old_value': 50060.0, 'new_value': 50640.0}, {'field': 'order_count', 'old_value': 189, 'new_value': 190}]
2025-06-12 00:00:40,748 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9C
2025-06-12 00:00:41,234 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9C
2025-06-12 00:00:41,235 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18609.55, 'new_value': 20795.7}, {'field': 'offline_amount', 'old_value': 260660.45, 'new_value': 289765.45}, {'field': 'total_amount', 'old_value': 279270.0, 'new_value': 310561.15}, {'field': 'order_count', 'old_value': 1196, 'new_value': 1318}]
2025-06-12 00:00:41,235 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUC
2025-06-12 00:00:41,670 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUC
2025-06-12 00:00:41,670 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5086.05, 'new_value': 5145.95}, {'field': 'offline_amount', 'old_value': 39096.0, 'new_value': 40460.0}, {'field': 'total_amount', 'old_value': 44182.05, 'new_value': 45605.95}, {'field': 'order_count', 'old_value': 873, 'new_value': 888}]
2025-06-12 00:00:41,670 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMVC
2025-06-12 00:00:42,129 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMVC
2025-06-12 00:00:42,130 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12969.0, 'new_value': 13945.0}, {'field': 'total_amount', 'old_value': 12969.0, 'new_value': 13945.0}, {'field': 'order_count', 'old_value': 60, 'new_value': 62}]
2025-06-12 00:00:42,130 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYC
2025-06-12 00:00:42,556 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYC
2025-06-12 00:00:42,556 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 151855.97, 'new_value': 159833.96}, {'field': 'total_amount', 'old_value': 151855.97, 'new_value': 159833.96}, {'field': 'order_count', 'old_value': 732, 'new_value': 769}]
2025-06-12 00:00:42,557 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCD
2025-06-12 00:00:42,951 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCD
2025-06-12 00:00:42,951 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17404.31, 'new_value': 19555.58}, {'field': 'offline_amount', 'old_value': 208074.16, 'new_value': 219892.29}, {'field': 'total_amount', 'old_value': 225478.47, 'new_value': 239447.87}, {'field': 'order_count', 'old_value': 1264, 'new_value': 1427}]
2025-06-12 00:00:42,951 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMED
2025-06-12 00:00:43,407 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMED
2025-06-12 00:00:43,407 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88321.0, 'new_value': 105643.0}, {'field': 'total_amount', 'old_value': 88321.0, 'new_value': 105643.0}, {'field': 'order_count', 'old_value': 50, 'new_value': 57}]
2025-06-12 00:00:43,407 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYD
2025-06-12 00:00:43,854 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYD
2025-06-12 00:00:43,855 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21145.69, 'new_value': 22884.7}, {'field': 'offline_amount', 'old_value': 134942.87, 'new_value': 140833.47}, {'field': 'total_amount', 'old_value': 156088.56, 'new_value': 163718.17}, {'field': 'order_count', 'old_value': 1045, 'new_value': 1129}]
2025-06-12 00:00:43,855 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1E
2025-06-12 00:00:44,286 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1E
2025-06-12 00:00:44,286 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48054.5, 'new_value': 50344.6}, {'field': 'total_amount', 'old_value': 48054.5, 'new_value': 50344.6}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-06-12 00:00:44,287 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWQ
2025-06-12 00:00:44,759 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWQ
2025-06-12 00:00:44,759 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3733.1, 'new_value': 4525.1}, {'field': 'offline_amount', 'old_value': 29493.0, 'new_value': 37493.0}, {'field': 'total_amount', 'old_value': 33226.1, 'new_value': 42018.1}, {'field': 'order_count', 'old_value': 28, 'new_value': 31}]
2025-06-12 00:00:44,759 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3R
2025-06-12 00:00:45,187 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3R
2025-06-12 00:00:45,187 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28103.06, 'new_value': 30128.27}, {'field': 'total_amount', 'old_value': 68096.21, 'new_value': 70121.42}, {'field': 'order_count', 'old_value': 53, 'new_value': 54}]
2025-06-12 00:00:45,188 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPR
2025-06-12 00:00:45,635 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPR
2025-06-12 00:00:45,635 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45220.26, 'new_value': 61418.62}, {'field': 'total_amount', 'old_value': 225840.8, 'new_value': 242039.16}, {'field': 'order_count', 'old_value': 785, 'new_value': 823}]
2025-06-12 00:00:45,636 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTR
2025-06-12 00:00:46,199 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTR
2025-06-12 00:00:46,199 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26443.2, 'new_value': 26943.2}, {'field': 'total_amount', 'old_value': 26443.2, 'new_value': 26943.2}, {'field': 'order_count', 'old_value': 173, 'new_value': 174}]
2025-06-12 00:00:46,200 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNS
2025-06-12 00:00:46,654 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNS
2025-06-12 00:00:46,654 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17188.0, 'new_value': 17918.0}, {'field': 'total_amount', 'old_value': 17407.0, 'new_value': 18137.0}, {'field': 'order_count', 'old_value': 65, 'new_value': 69}]
2025-06-12 00:00:46,655 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMXS
2025-06-12 00:00:47,112 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMXS
2025-06-12 00:00:47,112 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 93984.0, 'new_value': 94352.0}, {'field': 'total_amount', 'old_value': 93984.0, 'new_value': 94352.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-06-12 00:00:47,112 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYS
2025-06-12 00:00:47,463 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYS
2025-06-12 00:00:47,463 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14750.0, 'new_value': 15272.0}, {'field': 'offline_amount', 'old_value': 49227.06, 'new_value': 56296.06}, {'field': 'total_amount', 'old_value': 63977.06, 'new_value': 71568.06}, {'field': 'order_count', 'old_value': 94, 'new_value': 105}]
2025-06-12 00:00:47,463 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHT
2025-06-12 00:00:47,979 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHT
2025-06-12 00:00:47,979 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 164573.48, 'new_value': 165402.98}, {'field': 'offline_amount', 'old_value': 46773.46, 'new_value': 48849.48}, {'field': 'total_amount', 'old_value': 211346.94, 'new_value': 214252.46}, {'field': 'order_count', 'old_value': 425, 'new_value': 430}]
2025-06-12 00:00:47,979 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDK
2025-06-12 00:00:48,402 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDK
2025-06-12 00:00:48,402 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13945.22, 'new_value': 14818.91}, {'field': 'total_amount', 'old_value': 13945.22, 'new_value': 14818.91}, {'field': 'order_count', 'old_value': 61, 'new_value': 65}]
2025-06-12 00:00:48,403 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZK
2025-06-12 00:00:48,824 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZK
2025-06-12 00:00:48,824 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51174.0, 'new_value': 55448.0}, {'field': 'total_amount', 'old_value': 51174.0, 'new_value': 55448.0}, {'field': 'order_count', 'old_value': 178, 'new_value': 198}]
2025-06-12 00:00:48,824 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDL
2025-06-12 00:00:49,262 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDL
2025-06-12 00:00:49,262 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 70316.6, 'new_value': 76646.69}, {'field': 'offline_amount', 'old_value': 82000.0, 'new_value': 93000.0}, {'field': 'total_amount', 'old_value': 152316.6, 'new_value': 169646.69}, {'field': 'order_count', 'old_value': 488, 'new_value': 539}]
2025-06-12 00:00:49,263 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIL
2025-06-12 00:00:49,762 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIL
2025-06-12 00:00:49,762 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 306161.63, 'new_value': 328625.48}, {'field': 'total_amount', 'old_value': 306161.63, 'new_value': 328625.48}, {'field': 'order_count', 'old_value': 1854, 'new_value': 2021}]
2025-06-12 00:00:49,763 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMHM
2025-06-12 00:00:50,209 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMHM
2025-06-12 00:00:50,209 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18057.96, 'new_value': 20034.41}, {'field': 'offline_amount', 'old_value': 197712.45, 'new_value': 209206.31}, {'field': 'total_amount', 'old_value': 215770.41, 'new_value': 229240.72}, {'field': 'order_count', 'old_value': 986, 'new_value': 1052}]
2025-06-12 00:00:50,209 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPM
2025-06-12 00:00:50,688 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPM
2025-06-12 00:00:50,688 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28396.9, 'new_value': 31212.2}, {'field': 'offline_amount', 'old_value': 290446.2, 'new_value': 291446.2}, {'field': 'total_amount', 'old_value': 318843.1, 'new_value': 322658.4}, {'field': 'order_count', 'old_value': 1056, 'new_value': 1112}]
2025-06-12 00:00:50,688 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQM
2025-06-12 00:00:51,100 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQM
2025-06-12 00:00:51,100 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3097.0, 'new_value': 4192.75}, {'field': 'offline_amount', 'old_value': 14546.2, 'new_value': 14862.2}, {'field': 'total_amount', 'old_value': 17643.2, 'new_value': 19054.95}, {'field': 'order_count', 'old_value': 135, 'new_value': 142}]
2025-06-12 00:00:51,100 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSM
2025-06-12 00:00:51,512 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSM
2025-06-12 00:00:51,512 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 137289.8, 'new_value': 144624.87}, {'field': 'total_amount', 'old_value': 137289.8, 'new_value': 144624.87}, {'field': 'order_count', 'old_value': 1206, 'new_value': 1283}]
2025-06-12 00:00:51,514 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM731
2025-06-12 00:00:51,963 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM731
2025-06-12 00:00:51,963 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38937.0, 'new_value': 43848.0}, {'field': 'offline_amount', 'old_value': 58878.0, 'new_value': 64594.0}, {'field': 'total_amount', 'old_value': 97815.0, 'new_value': 108442.0}, {'field': 'order_count', 'old_value': 2007, 'new_value': 2232}]
2025-06-12 00:00:51,964 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX1
2025-06-12 00:00:52,420 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX1
2025-06-12 00:00:52,420 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41518.52, 'new_value': 50447.9}, {'field': 'total_amount', 'old_value': 133654.23, 'new_value': 142583.61}, {'field': 'order_count', 'old_value': 936, 'new_value': 1027}]
2025-06-12 00:00:52,421 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM82
2025-06-12 00:00:52,863 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM82
2025-06-12 00:00:52,863 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5553.0, 'new_value': 6203.0}, {'field': 'offline_amount', 'old_value': 116208.0, 'new_value': 116894.0}, {'field': 'total_amount', 'old_value': 121761.0, 'new_value': 123097.0}, {'field': 'order_count', 'old_value': 46, 'new_value': 48}]
2025-06-12 00:00:52,864 - INFO - 日期 2025-06 处理完成 - 更新: 37 条，插入: 0 条，错误: 0 条
2025-06-12 00:00:52,864 - INFO - 数据同步完成！更新: 37 条，插入: 0 条，错误: 0 条
2025-06-12 00:00:52,866 - INFO - =================同步完成====================
2025-06-12 03:00:02,714 - INFO - =================使用默认全量同步=============
2025-06-12 03:00:04,385 - INFO - MySQL查询成功，共获取 3929 条记录
2025-06-12 03:00:04,385 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-12 03:00:04,417 - INFO - 开始处理日期: 2025-01
2025-06-12 03:00:04,417 - INFO - Request Parameters - Page 1:
2025-06-12 03:00:04,417 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:04,417 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:05,885 - INFO - Response - Page 1:
2025-06-12 03:00:06,089 - INFO - 第 1 页获取到 100 条记录
2025-06-12 03:00:06,089 - INFO - Request Parameters - Page 2:
2025-06-12 03:00:06,089 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:06,089 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:06,667 - INFO - Response - Page 2:
2025-06-12 03:00:06,870 - INFO - 第 2 页获取到 100 条记录
2025-06-12 03:00:06,870 - INFO - Request Parameters - Page 3:
2025-06-12 03:00:06,870 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:06,870 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:07,385 - INFO - Response - Page 3:
2025-06-12 03:00:07,588 - INFO - 第 3 页获取到 100 条记录
2025-06-12 03:00:07,588 - INFO - Request Parameters - Page 4:
2025-06-12 03:00:07,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:07,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:08,135 - INFO - Response - Page 4:
2025-06-12 03:00:08,338 - INFO - 第 4 页获取到 100 条记录
2025-06-12 03:00:08,338 - INFO - Request Parameters - Page 5:
2025-06-12 03:00:08,338 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:08,338 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:08,901 - INFO - Response - Page 5:
2025-06-12 03:00:09,104 - INFO - 第 5 页获取到 100 条记录
2025-06-12 03:00:09,104 - INFO - Request Parameters - Page 6:
2025-06-12 03:00:09,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:09,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:09,620 - INFO - Response - Page 6:
2025-06-12 03:00:09,823 - INFO - 第 6 页获取到 100 条记录
2025-06-12 03:00:09,823 - INFO - Request Parameters - Page 7:
2025-06-12 03:00:09,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:09,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:10,354 - INFO - Response - Page 7:
2025-06-12 03:00:10,557 - INFO - 第 7 页获取到 82 条记录
2025-06-12 03:00:10,557 - INFO - 查询完成，共获取到 682 条记录
2025-06-12 03:00:10,557 - INFO - 获取到 682 条表单数据
2025-06-12 03:00:10,557 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-12 03:00:10,573 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 03:00:10,573 - INFO - 开始处理日期: 2025-02
2025-06-12 03:00:10,573 - INFO - Request Parameters - Page 1:
2025-06-12 03:00:10,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:10,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:11,213 - INFO - Response - Page 1:
2025-06-12 03:00:11,416 - INFO - 第 1 页获取到 100 条记录
2025-06-12 03:00:11,416 - INFO - Request Parameters - Page 2:
2025-06-12 03:00:11,416 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:11,416 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:11,901 - INFO - Response - Page 2:
2025-06-12 03:00:12,104 - INFO - 第 2 页获取到 100 条记录
2025-06-12 03:00:12,104 - INFO - Request Parameters - Page 3:
2025-06-12 03:00:12,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:12,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:12,620 - INFO - Response - Page 3:
2025-06-12 03:00:12,823 - INFO - 第 3 页获取到 100 条记录
2025-06-12 03:00:12,823 - INFO - Request Parameters - Page 4:
2025-06-12 03:00:12,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:12,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:13,370 - INFO - Response - Page 4:
2025-06-12 03:00:13,573 - INFO - 第 4 页获取到 100 条记录
2025-06-12 03:00:13,573 - INFO - Request Parameters - Page 5:
2025-06-12 03:00:13,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:13,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:14,104 - INFO - Response - Page 5:
2025-06-12 03:00:14,307 - INFO - 第 5 页获取到 100 条记录
2025-06-12 03:00:14,307 - INFO - Request Parameters - Page 6:
2025-06-12 03:00:14,307 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:14,307 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:14,807 - INFO - Response - Page 6:
2025-06-12 03:00:15,010 - INFO - 第 6 页获取到 100 条记录
2025-06-12 03:00:15,010 - INFO - Request Parameters - Page 7:
2025-06-12 03:00:15,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:15,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:15,510 - INFO - Response - Page 7:
2025-06-12 03:00:15,713 - INFO - 第 7 页获取到 70 条记录
2025-06-12 03:00:15,713 - INFO - 查询完成，共获取到 670 条记录
2025-06-12 03:00:15,713 - INFO - 获取到 670 条表单数据
2025-06-12 03:00:15,713 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-12 03:00:15,729 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 03:00:15,729 - INFO - 开始处理日期: 2025-03
2025-06-12 03:00:15,729 - INFO - Request Parameters - Page 1:
2025-06-12 03:00:15,729 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:15,729 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:16,213 - INFO - Response - Page 1:
2025-06-12 03:00:16,416 - INFO - 第 1 页获取到 100 条记录
2025-06-12 03:00:16,416 - INFO - Request Parameters - Page 2:
2025-06-12 03:00:16,416 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:16,416 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:16,932 - INFO - Response - Page 2:
2025-06-12 03:00:17,135 - INFO - 第 2 页获取到 100 条记录
2025-06-12 03:00:17,135 - INFO - Request Parameters - Page 3:
2025-06-12 03:00:17,135 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:17,135 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:17,744 - INFO - Response - Page 3:
2025-06-12 03:00:17,948 - INFO - 第 3 页获取到 100 条记录
2025-06-12 03:00:17,948 - INFO - Request Parameters - Page 4:
2025-06-12 03:00:17,948 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:17,948 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:18,510 - INFO - Response - Page 4:
2025-06-12 03:00:18,713 - INFO - 第 4 页获取到 100 条记录
2025-06-12 03:00:18,713 - INFO - Request Parameters - Page 5:
2025-06-12 03:00:18,713 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:18,713 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:19,229 - INFO - Response - Page 5:
2025-06-12 03:00:19,432 - INFO - 第 5 页获取到 100 条记录
2025-06-12 03:00:19,432 - INFO - Request Parameters - Page 6:
2025-06-12 03:00:19,432 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:19,432 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:19,901 - INFO - Response - Page 6:
2025-06-12 03:00:20,104 - INFO - 第 6 页获取到 100 条记录
2025-06-12 03:00:20,104 - INFO - Request Parameters - Page 7:
2025-06-12 03:00:20,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:20,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:20,541 - INFO - Response - Page 7:
2025-06-12 03:00:20,744 - INFO - 第 7 页获取到 61 条记录
2025-06-12 03:00:20,744 - INFO - 查询完成，共获取到 661 条记录
2025-06-12 03:00:20,744 - INFO - 获取到 661 条表单数据
2025-06-12 03:00:20,744 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-12 03:00:20,760 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 03:00:20,760 - INFO - 开始处理日期: 2025-04
2025-06-12 03:00:20,760 - INFO - Request Parameters - Page 1:
2025-06-12 03:00:20,760 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:20,760 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:21,338 - INFO - Response - Page 1:
2025-06-12 03:00:21,541 - INFO - 第 1 页获取到 100 条记录
2025-06-12 03:00:21,541 - INFO - Request Parameters - Page 2:
2025-06-12 03:00:21,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:21,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:22,010 - INFO - Response - Page 2:
2025-06-12 03:00:22,213 - INFO - 第 2 页获取到 100 条记录
2025-06-12 03:00:22,213 - INFO - Request Parameters - Page 3:
2025-06-12 03:00:22,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:22,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:22,744 - INFO - Response - Page 3:
2025-06-12 03:00:22,947 - INFO - 第 3 页获取到 100 条记录
2025-06-12 03:00:22,947 - INFO - Request Parameters - Page 4:
2025-06-12 03:00:22,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:22,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:23,510 - INFO - Response - Page 4:
2025-06-12 03:00:23,713 - INFO - 第 4 页获取到 100 条记录
2025-06-12 03:00:23,713 - INFO - Request Parameters - Page 5:
2025-06-12 03:00:23,713 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:23,713 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:24,260 - INFO - Response - Page 5:
2025-06-12 03:00:24,463 - INFO - 第 5 页获取到 100 条记录
2025-06-12 03:00:24,463 - INFO - Request Parameters - Page 6:
2025-06-12 03:00:24,463 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:24,463 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:24,947 - INFO - Response - Page 6:
2025-06-12 03:00:25,150 - INFO - 第 6 页获取到 100 条记录
2025-06-12 03:00:25,150 - INFO - Request Parameters - Page 7:
2025-06-12 03:00:25,150 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:25,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:25,541 - INFO - Response - Page 7:
2025-06-12 03:00:25,744 - INFO - 第 7 页获取到 56 条记录
2025-06-12 03:00:25,744 - INFO - 查询完成，共获取到 656 条记录
2025-06-12 03:00:25,744 - INFO - 获取到 656 条表单数据
2025-06-12 03:00:25,744 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-12 03:00:25,760 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 03:00:25,760 - INFO - 开始处理日期: 2025-05
2025-06-12 03:00:25,760 - INFO - Request Parameters - Page 1:
2025-06-12 03:00:25,760 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:25,760 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:26,260 - INFO - Response - Page 1:
2025-06-12 03:00:26,463 - INFO - 第 1 页获取到 100 条记录
2025-06-12 03:00:26,463 - INFO - Request Parameters - Page 2:
2025-06-12 03:00:26,463 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:26,463 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:26,994 - INFO - Response - Page 2:
2025-06-12 03:00:27,197 - INFO - 第 2 页获取到 100 条记录
2025-06-12 03:00:27,197 - INFO - Request Parameters - Page 3:
2025-06-12 03:00:27,197 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:27,197 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:27,760 - INFO - Response - Page 3:
2025-06-12 03:00:27,963 - INFO - 第 3 页获取到 100 条记录
2025-06-12 03:00:27,963 - INFO - Request Parameters - Page 4:
2025-06-12 03:00:27,963 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:27,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:28,478 - INFO - Response - Page 4:
2025-06-12 03:00:28,682 - INFO - 第 4 页获取到 100 条记录
2025-06-12 03:00:28,682 - INFO - Request Parameters - Page 5:
2025-06-12 03:00:28,682 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:28,682 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:29,166 - INFO - Response - Page 5:
2025-06-12 03:00:29,369 - INFO - 第 5 页获取到 100 条记录
2025-06-12 03:00:29,369 - INFO - Request Parameters - Page 6:
2025-06-12 03:00:29,369 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:29,369 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:29,900 - INFO - Response - Page 6:
2025-06-12 03:00:30,103 - INFO - 第 6 页获取到 100 条记录
2025-06-12 03:00:30,103 - INFO - Request Parameters - Page 7:
2025-06-12 03:00:30,103 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:30,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:30,463 - INFO - Response - Page 7:
2025-06-12 03:00:30,666 - INFO - 第 7 页获取到 40 条记录
2025-06-12 03:00:30,666 - INFO - 查询完成，共获取到 640 条记录
2025-06-12 03:00:30,666 - INFO - 获取到 640 条表单数据
2025-06-12 03:00:30,666 - INFO - 当前日期 2025-05 有 640 条MySQL数据需要处理
2025-06-12 03:00:30,681 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 03:00:30,681 - INFO - 开始处理日期: 2025-06
2025-06-12 03:00:30,681 - INFO - Request Parameters - Page 1:
2025-06-12 03:00:30,681 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:30,681 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:31,213 - INFO - Response - Page 1:
2025-06-12 03:00:31,416 - INFO - 第 1 页获取到 100 条记录
2025-06-12 03:00:31,416 - INFO - Request Parameters - Page 2:
2025-06-12 03:00:31,416 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:31,416 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:32,010 - INFO - Response - Page 2:
2025-06-12 03:00:32,213 - INFO - 第 2 页获取到 100 条记录
2025-06-12 03:00:32,213 - INFO - Request Parameters - Page 3:
2025-06-12 03:00:32,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:32,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:32,697 - INFO - Response - Page 3:
2025-06-12 03:00:32,900 - INFO - 第 3 页获取到 100 条记录
2025-06-12 03:00:32,900 - INFO - Request Parameters - Page 4:
2025-06-12 03:00:32,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:32,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:33,369 - INFO - Response - Page 4:
2025-06-12 03:00:33,572 - INFO - 第 4 页获取到 100 条记录
2025-06-12 03:00:33,572 - INFO - Request Parameters - Page 5:
2025-06-12 03:00:33,572 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:33,572 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:34,041 - INFO - Response - Page 5:
2025-06-12 03:00:34,244 - INFO - 第 5 页获取到 100 条记录
2025-06-12 03:00:34,244 - INFO - Request Parameters - Page 6:
2025-06-12 03:00:34,244 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:34,244 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:34,759 - INFO - Response - Page 6:
2025-06-12 03:00:34,963 - INFO - 第 6 页获取到 100 条记录
2025-06-12 03:00:34,963 - INFO - Request Parameters - Page 7:
2025-06-12 03:00:34,963 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 03:00:34,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 03:00:35,275 - INFO - Response - Page 7:
2025-06-12 03:00:35,478 - INFO - 第 7 页获取到 20 条记录
2025-06-12 03:00:35,478 - INFO - 查询完成，共获取到 620 条记录
2025-06-12 03:00:35,478 - INFO - 获取到 620 条表单数据
2025-06-12 03:00:35,478 - INFO - 当前日期 2025-06 有 620 条MySQL数据需要处理
2025-06-12 03:00:35,494 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 03:00:35,494 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 03:00:35,494 - INFO - =================同步完成====================
2025-06-12 06:00:02,601 - INFO - =================使用默认全量同步=============
2025-06-12 06:00:04,226 - INFO - MySQL查询成功，共获取 3929 条记录
2025-06-12 06:00:04,226 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-12 06:00:04,257 - INFO - 开始处理日期: 2025-01
2025-06-12 06:00:04,257 - INFO - Request Parameters - Page 1:
2025-06-12 06:00:04,257 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:04,257 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:05,757 - INFO - Response - Page 1:
2025-06-12 06:00:05,960 - INFO - 第 1 页获取到 100 条记录
2025-06-12 06:00:05,960 - INFO - Request Parameters - Page 2:
2025-06-12 06:00:05,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:05,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:06,398 - INFO - Response - Page 2:
2025-06-12 06:00:06,601 - INFO - 第 2 页获取到 100 条记录
2025-06-12 06:00:06,601 - INFO - Request Parameters - Page 3:
2025-06-12 06:00:06,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:06,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:07,132 - INFO - Response - Page 3:
2025-06-12 06:00:07,335 - INFO - 第 3 页获取到 100 条记录
2025-06-12 06:00:07,335 - INFO - Request Parameters - Page 4:
2025-06-12 06:00:07,335 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:07,335 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:07,866 - INFO - Response - Page 4:
2025-06-12 06:00:08,069 - INFO - 第 4 页获取到 100 条记录
2025-06-12 06:00:08,069 - INFO - Request Parameters - Page 5:
2025-06-12 06:00:08,069 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:08,069 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:08,601 - INFO - Response - Page 5:
2025-06-12 06:00:08,804 - INFO - 第 5 页获取到 100 条记录
2025-06-12 06:00:08,804 - INFO - Request Parameters - Page 6:
2025-06-12 06:00:08,804 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:08,804 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:09,351 - INFO - Response - Page 6:
2025-06-12 06:00:09,554 - INFO - 第 6 页获取到 100 条记录
2025-06-12 06:00:09,554 - INFO - Request Parameters - Page 7:
2025-06-12 06:00:09,554 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:09,554 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:10,007 - INFO - Response - Page 7:
2025-06-12 06:00:10,210 - INFO - 第 7 页获取到 82 条记录
2025-06-12 06:00:10,210 - INFO - 查询完成，共获取到 682 条记录
2025-06-12 06:00:10,210 - INFO - 获取到 682 条表单数据
2025-06-12 06:00:10,210 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-12 06:00:10,226 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 06:00:10,226 - INFO - 开始处理日期: 2025-02
2025-06-12 06:00:10,226 - INFO - Request Parameters - Page 1:
2025-06-12 06:00:10,226 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:10,226 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:10,757 - INFO - Response - Page 1:
2025-06-12 06:00:10,960 - INFO - 第 1 页获取到 100 条记录
2025-06-12 06:00:10,960 - INFO - Request Parameters - Page 2:
2025-06-12 06:00:10,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:10,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:11,460 - INFO - Response - Page 2:
2025-06-12 06:00:11,663 - INFO - 第 2 页获取到 100 条记录
2025-06-12 06:00:11,663 - INFO - Request Parameters - Page 3:
2025-06-12 06:00:11,663 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:11,663 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:12,194 - INFO - Response - Page 3:
2025-06-12 06:00:12,397 - INFO - 第 3 页获取到 100 条记录
2025-06-12 06:00:12,397 - INFO - Request Parameters - Page 4:
2025-06-12 06:00:12,397 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:12,397 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:12,944 - INFO - Response - Page 4:
2025-06-12 06:00:13,147 - INFO - 第 4 页获取到 100 条记录
2025-06-12 06:00:13,147 - INFO - Request Parameters - Page 5:
2025-06-12 06:00:13,147 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:13,147 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:13,694 - INFO - Response - Page 5:
2025-06-12 06:00:13,897 - INFO - 第 5 页获取到 100 条记录
2025-06-12 06:00:13,897 - INFO - Request Parameters - Page 6:
2025-06-12 06:00:13,897 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:13,897 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:14,460 - INFO - Response - Page 6:
2025-06-12 06:00:14,663 - INFO - 第 6 页获取到 100 条记录
2025-06-12 06:00:14,663 - INFO - Request Parameters - Page 7:
2025-06-12 06:00:14,663 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:14,663 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:15,085 - INFO - Response - Page 7:
2025-06-12 06:00:15,288 - INFO - 第 7 页获取到 70 条记录
2025-06-12 06:00:15,288 - INFO - 查询完成，共获取到 670 条记录
2025-06-12 06:00:15,288 - INFO - 获取到 670 条表单数据
2025-06-12 06:00:15,288 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-12 06:00:15,304 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 06:00:15,304 - INFO - 开始处理日期: 2025-03
2025-06-12 06:00:15,304 - INFO - Request Parameters - Page 1:
2025-06-12 06:00:15,304 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:15,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:15,929 - INFO - Response - Page 1:
2025-06-12 06:00:16,132 - INFO - 第 1 页获取到 100 条记录
2025-06-12 06:00:16,132 - INFO - Request Parameters - Page 2:
2025-06-12 06:00:16,132 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:16,132 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:16,632 - INFO - Response - Page 2:
2025-06-12 06:00:16,835 - INFO - 第 2 页获取到 100 条记录
2025-06-12 06:00:16,835 - INFO - Request Parameters - Page 3:
2025-06-12 06:00:16,835 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:16,835 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:17,413 - INFO - Response - Page 3:
2025-06-12 06:00:17,616 - INFO - 第 3 页获取到 100 条记录
2025-06-12 06:00:17,616 - INFO - Request Parameters - Page 4:
2025-06-12 06:00:17,616 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:17,616 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:18,147 - INFO - Response - Page 4:
2025-06-12 06:00:18,350 - INFO - 第 4 页获取到 100 条记录
2025-06-12 06:00:18,350 - INFO - Request Parameters - Page 5:
2025-06-12 06:00:18,350 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:18,350 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:18,913 - INFO - Response - Page 5:
2025-06-12 06:00:19,116 - INFO - 第 5 页获取到 100 条记录
2025-06-12 06:00:19,116 - INFO - Request Parameters - Page 6:
2025-06-12 06:00:19,116 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:19,116 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:19,710 - INFO - Response - Page 6:
2025-06-12 06:00:19,913 - INFO - 第 6 页获取到 100 条记录
2025-06-12 06:00:19,913 - INFO - Request Parameters - Page 7:
2025-06-12 06:00:19,913 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:19,913 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:20,428 - INFO - Response - Page 7:
2025-06-12 06:00:20,632 - INFO - 第 7 页获取到 61 条记录
2025-06-12 06:00:20,632 - INFO - 查询完成，共获取到 661 条记录
2025-06-12 06:00:20,632 - INFO - 获取到 661 条表单数据
2025-06-12 06:00:20,632 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-12 06:00:20,647 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 06:00:20,647 - INFO - 开始处理日期: 2025-04
2025-06-12 06:00:20,647 - INFO - Request Parameters - Page 1:
2025-06-12 06:00:20,647 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:20,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:21,225 - INFO - Response - Page 1:
2025-06-12 06:00:21,428 - INFO - 第 1 页获取到 100 条记录
2025-06-12 06:00:21,428 - INFO - Request Parameters - Page 2:
2025-06-12 06:00:21,428 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:21,428 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:21,960 - INFO - Response - Page 2:
2025-06-12 06:00:22,163 - INFO - 第 2 页获取到 100 条记录
2025-06-12 06:00:22,163 - INFO - Request Parameters - Page 3:
2025-06-12 06:00:22,163 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:22,163 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:22,741 - INFO - Response - Page 3:
2025-06-12 06:00:22,944 - INFO - 第 3 页获取到 100 条记录
2025-06-12 06:00:22,944 - INFO - Request Parameters - Page 4:
2025-06-12 06:00:22,944 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:22,944 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:23,460 - INFO - Response - Page 4:
2025-06-12 06:00:23,663 - INFO - 第 4 页获取到 100 条记录
2025-06-12 06:00:23,663 - INFO - Request Parameters - Page 5:
2025-06-12 06:00:23,663 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:23,663 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:24,163 - INFO - Response - Page 5:
2025-06-12 06:00:24,366 - INFO - 第 5 页获取到 100 条记录
2025-06-12 06:00:24,366 - INFO - Request Parameters - Page 6:
2025-06-12 06:00:24,366 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:24,366 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:24,850 - INFO - Response - Page 6:
2025-06-12 06:00:25,053 - INFO - 第 6 页获取到 100 条记录
2025-06-12 06:00:25,053 - INFO - Request Parameters - Page 7:
2025-06-12 06:00:25,053 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:25,053 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:25,475 - INFO - Response - Page 7:
2025-06-12 06:00:25,678 - INFO - 第 7 页获取到 56 条记录
2025-06-12 06:00:25,678 - INFO - 查询完成，共获取到 656 条记录
2025-06-12 06:00:25,678 - INFO - 获取到 656 条表单数据
2025-06-12 06:00:25,678 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-12 06:00:25,694 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 06:00:25,694 - INFO - 开始处理日期: 2025-05
2025-06-12 06:00:25,694 - INFO - Request Parameters - Page 1:
2025-06-12 06:00:25,694 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:25,694 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:26,147 - INFO - Response - Page 1:
2025-06-12 06:00:26,350 - INFO - 第 1 页获取到 100 条记录
2025-06-12 06:00:26,350 - INFO - Request Parameters - Page 2:
2025-06-12 06:00:26,350 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:26,350 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:26,959 - INFO - Response - Page 2:
2025-06-12 06:00:27,163 - INFO - 第 2 页获取到 100 条记录
2025-06-12 06:00:27,163 - INFO - Request Parameters - Page 3:
2025-06-12 06:00:27,163 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:27,163 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:27,663 - INFO - Response - Page 3:
2025-06-12 06:00:27,866 - INFO - 第 3 页获取到 100 条记录
2025-06-12 06:00:27,866 - INFO - Request Parameters - Page 4:
2025-06-12 06:00:27,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:27,866 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:28,397 - INFO - Response - Page 4:
2025-06-12 06:00:28,600 - INFO - 第 4 页获取到 100 条记录
2025-06-12 06:00:28,600 - INFO - Request Parameters - Page 5:
2025-06-12 06:00:28,600 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:28,600 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:29,116 - INFO - Response - Page 5:
2025-06-12 06:00:29,319 - INFO - 第 5 页获取到 100 条记录
2025-06-12 06:00:29,319 - INFO - Request Parameters - Page 6:
2025-06-12 06:00:29,319 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:29,319 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:29,819 - INFO - Response - Page 6:
2025-06-12 06:00:30,022 - INFO - 第 6 页获取到 100 条记录
2025-06-12 06:00:30,022 - INFO - Request Parameters - Page 7:
2025-06-12 06:00:30,022 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:30,022 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:30,413 - INFO - Response - Page 7:
2025-06-12 06:00:30,616 - INFO - 第 7 页获取到 40 条记录
2025-06-12 06:00:30,616 - INFO - 查询完成，共获取到 640 条记录
2025-06-12 06:00:30,616 - INFO - 获取到 640 条表单数据
2025-06-12 06:00:30,616 - INFO - 当前日期 2025-05 有 640 条MySQL数据需要处理
2025-06-12 06:00:30,631 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 06:00:30,631 - INFO - 开始处理日期: 2025-06
2025-06-12 06:00:30,631 - INFO - Request Parameters - Page 1:
2025-06-12 06:00:30,631 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:30,631 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:31,162 - INFO - Response - Page 1:
2025-06-12 06:00:31,366 - INFO - 第 1 页获取到 100 条记录
2025-06-12 06:00:31,366 - INFO - Request Parameters - Page 2:
2025-06-12 06:00:31,366 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:31,366 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:31,881 - INFO - Response - Page 2:
2025-06-12 06:00:32,084 - INFO - 第 2 页获取到 100 条记录
2025-06-12 06:00:32,084 - INFO - Request Parameters - Page 3:
2025-06-12 06:00:32,084 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:32,084 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:32,569 - INFO - Response - Page 3:
2025-06-12 06:00:32,772 - INFO - 第 3 页获取到 100 条记录
2025-06-12 06:00:32,772 - INFO - Request Parameters - Page 4:
2025-06-12 06:00:32,772 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:32,772 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:33,350 - INFO - Response - Page 4:
2025-06-12 06:00:33,553 - INFO - 第 4 页获取到 100 条记录
2025-06-12 06:00:33,553 - INFO - Request Parameters - Page 5:
2025-06-12 06:00:33,553 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:33,553 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:34,084 - INFO - Response - Page 5:
2025-06-12 06:00:34,287 - INFO - 第 5 页获取到 100 条记录
2025-06-12 06:00:34,287 - INFO - Request Parameters - Page 6:
2025-06-12 06:00:34,287 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:34,287 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:34,803 - INFO - Response - Page 6:
2025-06-12 06:00:35,006 - INFO - 第 6 页获取到 100 条记录
2025-06-12 06:00:35,006 - INFO - Request Parameters - Page 7:
2025-06-12 06:00:35,006 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 06:00:35,006 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 06:00:35,334 - INFO - Response - Page 7:
2025-06-12 06:00:35,537 - INFO - 第 7 页获取到 20 条记录
2025-06-12 06:00:35,537 - INFO - 查询完成，共获取到 620 条记录
2025-06-12 06:00:35,537 - INFO - 获取到 620 条表单数据
2025-06-12 06:00:35,537 - INFO - 当前日期 2025-06 有 620 条MySQL数据需要处理
2025-06-12 06:00:35,553 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC31
2025-06-12 06:00:36,006 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC31
2025-06-12 06:00:36,006 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32789.0, 'new_value': 36906.0}, {'field': 'total_amount', 'old_value': 32789.0, 'new_value': 36906.0}, {'field': 'order_count', 'old_value': 180, 'new_value': 200}]
2025-06-12 06:00:36,006 - INFO - 日期 2025-06 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-06-12 06:00:36,006 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-06-12 06:00:36,006 - INFO - =================同步完成====================
2025-06-12 09:00:02,613 - INFO - =================使用默认全量同步=============
2025-06-12 09:00:04,254 - INFO - MySQL查询成功，共获取 3930 条记录
2025-06-12 09:00:04,254 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-12 09:00:04,301 - INFO - 开始处理日期: 2025-01
2025-06-12 09:00:04,301 - INFO - Request Parameters - Page 1:
2025-06-12 09:00:04,301 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:04,301 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:05,754 - INFO - Response - Page 1:
2025-06-12 09:00:05,957 - INFO - 第 1 页获取到 100 条记录
2025-06-12 09:00:05,957 - INFO - Request Parameters - Page 2:
2025-06-12 09:00:05,957 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:05,957 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:06,488 - INFO - Response - Page 2:
2025-06-12 09:00:06,691 - INFO - 第 2 页获取到 100 条记录
2025-06-12 09:00:06,691 - INFO - Request Parameters - Page 3:
2025-06-12 09:00:06,691 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:06,691 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:07,269 - INFO - Response - Page 3:
2025-06-12 09:00:07,472 - INFO - 第 3 页获取到 100 条记录
2025-06-12 09:00:07,472 - INFO - Request Parameters - Page 4:
2025-06-12 09:00:07,472 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:07,472 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:08,004 - INFO - Response - Page 4:
2025-06-12 09:00:08,207 - INFO - 第 4 页获取到 100 条记录
2025-06-12 09:00:08,207 - INFO - Request Parameters - Page 5:
2025-06-12 09:00:08,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:08,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:08,707 - INFO - Response - Page 5:
2025-06-12 09:00:08,910 - INFO - 第 5 页获取到 100 条记录
2025-06-12 09:00:08,910 - INFO - Request Parameters - Page 6:
2025-06-12 09:00:08,910 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:08,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:09,457 - INFO - Response - Page 6:
2025-06-12 09:00:09,660 - INFO - 第 6 页获取到 100 条记录
2025-06-12 09:00:09,660 - INFO - Request Parameters - Page 7:
2025-06-12 09:00:09,660 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:09,660 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:10,144 - INFO - Response - Page 7:
2025-06-12 09:00:10,347 - INFO - 第 7 页获取到 82 条记录
2025-06-12 09:00:10,347 - INFO - 查询完成，共获取到 682 条记录
2025-06-12 09:00:10,347 - INFO - 获取到 682 条表单数据
2025-06-12 09:00:10,347 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-12 09:00:10,363 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 09:00:10,363 - INFO - 开始处理日期: 2025-02
2025-06-12 09:00:10,363 - INFO - Request Parameters - Page 1:
2025-06-12 09:00:10,363 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:10,363 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:10,894 - INFO - Response - Page 1:
2025-06-12 09:00:11,097 - INFO - 第 1 页获取到 100 条记录
2025-06-12 09:00:11,097 - INFO - Request Parameters - Page 2:
2025-06-12 09:00:11,097 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:11,097 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:11,675 - INFO - Response - Page 2:
2025-06-12 09:00:11,878 - INFO - 第 2 页获取到 100 条记录
2025-06-12 09:00:11,878 - INFO - Request Parameters - Page 3:
2025-06-12 09:00:11,878 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:11,878 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:12,394 - INFO - Response - Page 3:
2025-06-12 09:00:12,597 - INFO - 第 3 页获取到 100 条记录
2025-06-12 09:00:12,597 - INFO - Request Parameters - Page 4:
2025-06-12 09:00:12,597 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:12,597 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:13,191 - INFO - Response - Page 4:
2025-06-12 09:00:13,394 - INFO - 第 4 页获取到 100 条记录
2025-06-12 09:00:13,394 - INFO - Request Parameters - Page 5:
2025-06-12 09:00:13,394 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:13,394 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:13,878 - INFO - Response - Page 5:
2025-06-12 09:00:14,081 - INFO - 第 5 页获取到 100 条记录
2025-06-12 09:00:14,081 - INFO - Request Parameters - Page 6:
2025-06-12 09:00:14,081 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:14,081 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:14,566 - INFO - Response - Page 6:
2025-06-12 09:00:14,769 - INFO - 第 6 页获取到 100 条记录
2025-06-12 09:00:14,769 - INFO - Request Parameters - Page 7:
2025-06-12 09:00:14,769 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:14,769 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:15,269 - INFO - Response - Page 7:
2025-06-12 09:00:15,472 - INFO - 第 7 页获取到 70 条记录
2025-06-12 09:00:15,472 - INFO - 查询完成，共获取到 670 条记录
2025-06-12 09:00:15,472 - INFO - 获取到 670 条表单数据
2025-06-12 09:00:15,488 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-12 09:00:15,488 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 09:00:15,488 - INFO - 开始处理日期: 2025-03
2025-06-12 09:00:15,488 - INFO - Request Parameters - Page 1:
2025-06-12 09:00:15,488 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:15,488 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:16,081 - INFO - Response - Page 1:
2025-06-12 09:00:16,284 - INFO - 第 1 页获取到 100 条记录
2025-06-12 09:00:16,284 - INFO - Request Parameters - Page 2:
2025-06-12 09:00:16,284 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:16,284 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:16,816 - INFO - Response - Page 2:
2025-06-12 09:00:17,019 - INFO - 第 2 页获取到 100 条记录
2025-06-12 09:00:17,019 - INFO - Request Parameters - Page 3:
2025-06-12 09:00:17,019 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:17,019 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:17,534 - INFO - Response - Page 3:
2025-06-12 09:00:17,738 - INFO - 第 3 页获取到 100 条记录
2025-06-12 09:00:17,738 - INFO - Request Parameters - Page 4:
2025-06-12 09:00:17,738 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:17,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:18,284 - INFO - Response - Page 4:
2025-06-12 09:00:18,488 - INFO - 第 4 页获取到 100 条记录
2025-06-12 09:00:18,488 - INFO - Request Parameters - Page 5:
2025-06-12 09:00:18,488 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:18,488 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:19,019 - INFO - Response - Page 5:
2025-06-12 09:00:19,222 - INFO - 第 5 页获取到 100 条记录
2025-06-12 09:00:19,222 - INFO - Request Parameters - Page 6:
2025-06-12 09:00:19,222 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:19,222 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:19,816 - INFO - Response - Page 6:
2025-06-12 09:00:20,019 - INFO - 第 6 页获取到 100 条记录
2025-06-12 09:00:20,019 - INFO - Request Parameters - Page 7:
2025-06-12 09:00:20,019 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:20,019 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:20,534 - INFO - Response - Page 7:
2025-06-12 09:00:20,737 - INFO - 第 7 页获取到 61 条记录
2025-06-12 09:00:20,737 - INFO - 查询完成，共获取到 661 条记录
2025-06-12 09:00:20,737 - INFO - 获取到 661 条表单数据
2025-06-12 09:00:20,737 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-12 09:00:20,753 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 09:00:20,753 - INFO - 开始处理日期: 2025-04
2025-06-12 09:00:20,753 - INFO - Request Parameters - Page 1:
2025-06-12 09:00:20,753 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:20,753 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:21,269 - INFO - Response - Page 1:
2025-06-12 09:00:21,472 - INFO - 第 1 页获取到 100 条记录
2025-06-12 09:00:21,472 - INFO - Request Parameters - Page 2:
2025-06-12 09:00:21,472 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:21,472 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:22,081 - INFO - Response - Page 2:
2025-06-12 09:00:22,284 - INFO - 第 2 页获取到 100 条记录
2025-06-12 09:00:22,284 - INFO - Request Parameters - Page 3:
2025-06-12 09:00:22,284 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:22,284 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:22,800 - INFO - Response - Page 3:
2025-06-12 09:00:23,003 - INFO - 第 3 页获取到 100 条记录
2025-06-12 09:00:23,003 - INFO - Request Parameters - Page 4:
2025-06-12 09:00:23,003 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:23,003 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:23,519 - INFO - Response - Page 4:
2025-06-12 09:00:23,722 - INFO - 第 4 页获取到 100 条记录
2025-06-12 09:00:23,722 - INFO - Request Parameters - Page 5:
2025-06-12 09:00:23,722 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:23,722 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:24,222 - INFO - Response - Page 5:
2025-06-12 09:00:24,425 - INFO - 第 5 页获取到 100 条记录
2025-06-12 09:00:24,425 - INFO - Request Parameters - Page 6:
2025-06-12 09:00:24,425 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:24,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:24,925 - INFO - Response - Page 6:
2025-06-12 09:00:25,128 - INFO - 第 6 页获取到 100 条记录
2025-06-12 09:00:25,128 - INFO - Request Parameters - Page 7:
2025-06-12 09:00:25,128 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:25,128 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:25,644 - INFO - Response - Page 7:
2025-06-12 09:00:25,847 - INFO - 第 7 页获取到 56 条记录
2025-06-12 09:00:25,847 - INFO - 查询完成，共获取到 656 条记录
2025-06-12 09:00:25,847 - INFO - 获取到 656 条表单数据
2025-06-12 09:00:25,847 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-12 09:00:25,862 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 09:00:25,862 - INFO - 开始处理日期: 2025-05
2025-06-12 09:00:25,862 - INFO - Request Parameters - Page 1:
2025-06-12 09:00:25,862 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:25,862 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:26,456 - INFO - Response - Page 1:
2025-06-12 09:00:26,659 - INFO - 第 1 页获取到 100 条记录
2025-06-12 09:00:26,659 - INFO - Request Parameters - Page 2:
2025-06-12 09:00:26,659 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:26,659 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:27,159 - INFO - Response - Page 2:
2025-06-12 09:00:27,362 - INFO - 第 2 页获取到 100 条记录
2025-06-12 09:00:27,362 - INFO - Request Parameters - Page 3:
2025-06-12 09:00:27,362 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:27,362 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:27,893 - INFO - Response - Page 3:
2025-06-12 09:00:28,097 - INFO - 第 3 页获取到 100 条记录
2025-06-12 09:00:28,097 - INFO - Request Parameters - Page 4:
2025-06-12 09:00:28,097 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:28,097 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:28,659 - INFO - Response - Page 4:
2025-06-12 09:00:28,862 - INFO - 第 4 页获取到 100 条记录
2025-06-12 09:00:28,862 - INFO - Request Parameters - Page 5:
2025-06-12 09:00:28,862 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:28,862 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:29,378 - INFO - Response - Page 5:
2025-06-12 09:00:29,581 - INFO - 第 5 页获取到 100 条记录
2025-06-12 09:00:29,581 - INFO - Request Parameters - Page 6:
2025-06-12 09:00:29,581 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:29,581 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:30,081 - INFO - Response - Page 6:
2025-06-12 09:00:30,284 - INFO - 第 6 页获取到 100 条记录
2025-06-12 09:00:30,284 - INFO - Request Parameters - Page 7:
2025-06-12 09:00:30,284 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:30,284 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:30,722 - INFO - Response - Page 7:
2025-06-12 09:00:30,925 - INFO - 第 7 页获取到 40 条记录
2025-06-12 09:00:30,925 - INFO - 查询完成，共获取到 640 条记录
2025-06-12 09:00:30,925 - INFO - 获取到 640 条表单数据
2025-06-12 09:00:30,925 - INFO - 当前日期 2025-05 有 640 条MySQL数据需要处理
2025-06-12 09:00:30,940 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 09:00:30,940 - INFO - 开始处理日期: 2025-06
2025-06-12 09:00:30,940 - INFO - Request Parameters - Page 1:
2025-06-12 09:00:30,940 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:30,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:31,534 - INFO - Response - Page 1:
2025-06-12 09:00:31,737 - INFO - 第 1 页获取到 100 条记录
2025-06-12 09:00:31,737 - INFO - Request Parameters - Page 2:
2025-06-12 09:00:31,737 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:31,737 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:32,300 - INFO - Response - Page 2:
2025-06-12 09:00:32,503 - INFO - 第 2 页获取到 100 条记录
2025-06-12 09:00:32,503 - INFO - Request Parameters - Page 3:
2025-06-12 09:00:32,503 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:32,503 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:33,065 - INFO - Response - Page 3:
2025-06-12 09:00:33,268 - INFO - 第 3 页获取到 100 条记录
2025-06-12 09:00:33,268 - INFO - Request Parameters - Page 4:
2025-06-12 09:00:33,268 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:33,268 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:33,784 - INFO - Response - Page 4:
2025-06-12 09:00:33,987 - INFO - 第 4 页获取到 100 条记录
2025-06-12 09:00:33,987 - INFO - Request Parameters - Page 5:
2025-06-12 09:00:33,987 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:33,987 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:34,518 - INFO - Response - Page 5:
2025-06-12 09:00:34,721 - INFO - 第 5 页获取到 100 条记录
2025-06-12 09:00:34,721 - INFO - Request Parameters - Page 6:
2025-06-12 09:00:34,721 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:34,721 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:35,221 - INFO - Response - Page 6:
2025-06-12 09:00:35,425 - INFO - 第 6 页获取到 100 条记录
2025-06-12 09:00:35,425 - INFO - Request Parameters - Page 7:
2025-06-12 09:00:35,425 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 09:00:35,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 09:00:35,768 - INFO - Response - Page 7:
2025-06-12 09:00:35,971 - INFO - 第 7 页获取到 20 条记录
2025-06-12 09:00:35,971 - INFO - 查询完成，共获取到 620 条记录
2025-06-12 09:00:35,971 - INFO - 获取到 620 条表单数据
2025-06-12 09:00:35,987 - INFO - 当前日期 2025-06 有 621 条MySQL数据需要处理
2025-06-12 09:00:35,987 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMIX
2025-06-12 09:00:36,456 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMIX
2025-06-12 09:00:36,456 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10200.0, 'new_value': 22440.0}, {'field': 'total_amount', 'old_value': 10200.0, 'new_value': 22440.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 6}]
2025-06-12 09:00:36,456 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Y
2025-06-12 09:00:36,924 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Y
2025-06-12 09:00:36,924 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1500.0, 'new_value': 2326.0}, {'field': 'offline_amount', 'old_value': 28413.0, 'new_value': 33420.0}, {'field': 'total_amount', 'old_value': 29913.0, 'new_value': 35746.0}, {'field': 'order_count', 'old_value': 42, 'new_value': 47}]
2025-06-12 09:00:36,924 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAY
2025-06-12 09:00:37,331 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAY
2025-06-12 09:00:37,331 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64474.0, 'new_value': 67724.0}, {'field': 'total_amount', 'old_value': 64474.0, 'new_value': 67724.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 18}]
2025-06-12 09:00:37,331 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMDY
2025-06-12 09:00:37,706 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMDY
2025-06-12 09:00:37,706 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124512.8, 'new_value': 129819.0}, {'field': 'total_amount', 'old_value': 124512.8, 'new_value': 129819.0}, {'field': 'order_count', 'old_value': 1894, 'new_value': 1975}]
2025-06-12 09:00:37,706 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGY
2025-06-12 09:00:38,128 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGY
2025-06-12 09:00:38,128 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1639.0, 'new_value': 2061.0}, {'field': 'offline_amount', 'old_value': 26901.31, 'new_value': 30025.32}, {'field': 'total_amount', 'old_value': 28540.31, 'new_value': 32086.32}, {'field': 'order_count', 'old_value': 187, 'new_value': 217}]
2025-06-12 09:00:38,128 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOY
2025-06-12 09:00:38,565 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOY
2025-06-12 09:00:38,565 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11714.0, 'new_value': 11899.0}, {'field': 'offline_amount', 'old_value': 18204.74, 'new_value': 24979.74}, {'field': 'total_amount', 'old_value': 29918.74, 'new_value': 36878.74}, {'field': 'order_count', 'old_value': 52, 'new_value': 56}]
2025-06-12 09:00:38,565 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTY
2025-06-12 09:00:39,049 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTY
2025-06-12 09:00:39,049 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3200.0, 'new_value': 3415.0}, {'field': 'total_amount', 'old_value': 5557.0, 'new_value': 5772.0}, {'field': 'order_count', 'old_value': 89, 'new_value': 96}]
2025-06-12 09:00:39,049 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM0Z
2025-06-12 09:00:39,471 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM0Z
2025-06-12 09:00:39,471 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36715.0, 'new_value': 45084.0}, {'field': 'offline_amount', 'old_value': 258685.0, 'new_value': 268823.0}, {'field': 'total_amount', 'old_value': 295400.0, 'new_value': 313907.0}, {'field': 'order_count', 'old_value': 43, 'new_value': 47}]
2025-06-12 09:00:39,471 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM2Z
2025-06-12 09:00:39,940 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM2Z
2025-06-12 09:00:39,940 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9569.03, 'new_value': 10772.75}, {'field': 'offline_amount', 'old_value': 11481.9, 'new_value': 12244.15}, {'field': 'total_amount', 'old_value': 21050.93, 'new_value': 23016.9}, {'field': 'order_count', 'old_value': 1034, 'new_value': 1109}]
2025-06-12 09:00:39,940 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM7Z
2025-06-12 09:00:40,409 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM7Z
2025-06-12 09:00:40,409 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57917.0, 'new_value': 58165.0}, {'field': 'total_amount', 'old_value': 57917.0, 'new_value': 58165.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-06-12 09:00:40,409 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMCZ
2025-06-12 09:00:40,924 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMCZ
2025-06-12 09:00:40,924 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5671.5, 'new_value': 5917.5}, {'field': 'offline_amount', 'old_value': 27659.0, 'new_value': 27737.0}, {'field': 'total_amount', 'old_value': 33330.5, 'new_value': 33654.5}, {'field': 'order_count', 'old_value': 35, 'new_value': 38}]
2025-06-12 09:00:40,924 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJZ
2025-06-12 09:00:41,331 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJZ
2025-06-12 09:00:41,331 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2891.32, 'new_value': 3154.72}, {'field': 'total_amount', 'old_value': 2891.32, 'new_value': 3154.72}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-12 09:00:41,331 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLZ
2025-06-12 09:00:41,799 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLZ
2025-06-12 09:00:41,799 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9995.0, 'new_value': 10779.0}, {'field': 'total_amount', 'old_value': 9995.0, 'new_value': 10779.0}, {'field': 'order_count', 'old_value': 50, 'new_value': 53}]
2025-06-12 09:00:41,799 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNZ
2025-06-12 09:00:42,190 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNZ
2025-06-12 09:00:42,190 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46027.42, 'new_value': 47646.62}, {'field': 'total_amount', 'old_value': 46949.26, 'new_value': 48568.46}, {'field': 'order_count', 'old_value': 1044, 'new_value': 1085}]
2025-06-12 09:00:42,190 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOZ
2025-06-12 09:00:42,612 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOZ
2025-06-12 09:00:42,612 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52338.93, 'new_value': 55366.53}, {'field': 'offline_amount', 'old_value': 435134.54, 'new_value': 473842.43}, {'field': 'total_amount', 'old_value': 487473.47, 'new_value': 529208.96}, {'field': 'order_count', 'old_value': 4276, 'new_value': 4516}]
2025-06-12 09:00:42,612 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPZ
2025-06-12 09:00:43,174 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPZ
2025-06-12 09:00:43,174 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70941.0, 'new_value': 79056.0}, {'field': 'total_amount', 'old_value': 70941.0, 'new_value': 79056.0}, {'field': 'order_count', 'old_value': 2699, 'new_value': 3013}]
2025-06-12 09:00:43,174 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMQZ
2025-06-12 09:00:43,612 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMQZ
2025-06-12 09:00:43,612 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64788.0, 'new_value': 71219.8}, {'field': 'total_amount', 'old_value': 64788.0, 'new_value': 71219.8}, {'field': 'order_count', 'old_value': 273, 'new_value': 297}]
2025-06-12 09:00:43,612 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRZ
2025-06-12 09:00:44,034 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRZ
2025-06-12 09:00:44,034 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 58270.0, 'new_value': 62320.0}, {'field': 'offline_amount', 'old_value': 23134.23, 'new_value': 24656.04}, {'field': 'total_amount', 'old_value': 81404.23, 'new_value': 86976.04}, {'field': 'order_count', 'old_value': 567, 'new_value': 602}]
2025-06-12 09:00:44,034 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTZ
2025-06-12 09:00:44,487 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTZ
2025-06-12 09:00:44,487 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54237.13, 'new_value': 60707.5}, {'field': 'total_amount', 'old_value': 54237.13, 'new_value': 60707.5}, {'field': 'order_count', 'old_value': 1863, 'new_value': 2102}]
2025-06-12 09:00:44,502 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM401
2025-06-12 09:00:44,909 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM401
2025-06-12 09:00:44,909 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26675.6, 'new_value': 31430.48}, {'field': 'total_amount', 'old_value': 67129.86, 'new_value': 71884.74}, {'field': 'order_count', 'old_value': 2216, 'new_value': 2397}]
2025-06-12 09:00:44,909 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJB
2025-06-12 09:00:45,362 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJB
2025-06-12 09:00:45,362 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34303.54, 'new_value': 36435.94}, {'field': 'total_amount', 'old_value': 34303.54, 'new_value': 36435.94}, {'field': 'order_count', 'old_value': 133, 'new_value': 143}]
2025-06-12 09:00:45,362 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMB
2025-06-12 09:00:45,799 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMB
2025-06-12 09:00:45,799 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5375.39, 'new_value': 5902.99}, {'field': 'offline_amount', 'old_value': 10005.76, 'new_value': 11216.53}, {'field': 'total_amount', 'old_value': 15381.15, 'new_value': 17119.52}, {'field': 'order_count', 'old_value': 498, 'new_value': 553}]
2025-06-12 09:00:45,799 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOB
2025-06-12 09:00:46,237 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOB
2025-06-12 09:00:46,237 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68596.91, 'new_value': 77002.58}, {'field': 'total_amount', 'old_value': 71518.49, 'new_value': 79924.16}, {'field': 'order_count', 'old_value': 473, 'new_value': 521}]
2025-06-12 09:00:46,237 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMPB
2025-06-12 09:00:46,768 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMPB
2025-06-12 09:00:46,768 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33969.0, 'new_value': 34419.0}, {'field': 'total_amount', 'old_value': 34619.0, 'new_value': 35069.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-12 09:00:46,768 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTB
2025-06-12 09:00:47,346 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTB
2025-06-12 09:00:47,346 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 717.0, 'new_value': 756.0}, {'field': 'offline_amount', 'old_value': 274182.0, 'new_value': 333332.0}, {'field': 'total_amount', 'old_value': 274899.0, 'new_value': 334088.0}, {'field': 'order_count', 'old_value': 83, 'new_value': 91}]
2025-06-12 09:00:47,346 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUB
2025-06-12 09:00:47,862 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUB
2025-06-12 09:00:47,862 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 167087.5, 'new_value': 182755.0}, {'field': 'offline_amount', 'old_value': 42095.6, 'new_value': 43351.4}, {'field': 'total_amount', 'old_value': 209183.1, 'new_value': 226106.4}, {'field': 'order_count', 'old_value': 262, 'new_value': 283}]
2025-06-12 09:00:47,862 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7C
2025-06-12 09:00:48,377 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7C
2025-06-12 09:00:48,377 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38933.53, 'new_value': 42987.35}, {'field': 'total_amount', 'old_value': 38933.53, 'new_value': 42987.35}, {'field': 'order_count', 'old_value': 1119, 'new_value': 1239}]
2025-06-12 09:00:48,377 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCC
2025-06-12 09:00:48,721 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCC
2025-06-12 09:00:48,721 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49853.0, 'new_value': 68450.0}, {'field': 'total_amount', 'old_value': 49853.0, 'new_value': 68450.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 29}]
2025-06-12 09:00:48,721 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLC
2025-06-12 09:00:49,174 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLC
2025-06-12 09:00:49,174 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 165564.0, 'new_value': 198959.0}, {'field': 'total_amount', 'old_value': 169480.0, 'new_value': 202875.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 29}]
2025-06-12 09:00:49,174 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMPC
2025-06-12 09:00:49,627 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMPC
2025-06-12 09:00:49,627 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76323.5, 'new_value': 83191.5}, {'field': 'total_amount', 'old_value': 76323.5, 'new_value': 83191.5}, {'field': 'order_count', 'old_value': 25, 'new_value': 27}]
2025-06-12 09:00:49,627 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQC
2025-06-12 09:00:50,080 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQC
2025-06-12 09:00:50,080 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 149915.39, 'new_value': 163877.13}, {'field': 'total_amount', 'old_value': 149915.39, 'new_value': 163877.13}, {'field': 'order_count', 'old_value': 463, 'new_value': 516}]
2025-06-12 09:00:50,080 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRC
2025-06-12 09:00:50,487 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRC
2025-06-12 09:00:50,487 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23246.0, 'new_value': 24115.0}, {'field': 'total_amount', 'old_value': 23246.0, 'new_value': 24115.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 37}]
2025-06-12 09:00:50,487 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWC
2025-06-12 09:00:50,893 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWC
2025-06-12 09:00:50,893 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14537.0, 'new_value': 15205.0}, {'field': 'total_amount', 'old_value': 14537.0, 'new_value': 15205.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 34}]
2025-06-12 09:00:50,893 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXC
2025-06-12 09:00:51,330 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXC
2025-06-12 09:00:51,330 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21002.5, 'new_value': 21682.5}, {'field': 'offline_amount', 'old_value': 13053.6, 'new_value': 13554.8}, {'field': 'total_amount', 'old_value': 34056.1, 'new_value': 35237.3}, {'field': 'order_count', 'old_value': 106, 'new_value': 116}]
2025-06-12 09:00:51,330 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMZC
2025-06-12 09:00:51,893 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMZC
2025-06-12 09:00:51,893 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42725.0, 'new_value': 52423.0}, {'field': 'total_amount', 'old_value': 43008.0, 'new_value': 52706.0}, {'field': 'order_count', 'old_value': 5597, 'new_value': 5598}]
2025-06-12 09:00:51,893 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0D
2025-06-12 09:00:52,252 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0D
2025-06-12 09:00:52,252 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31666.86, 'new_value': 38359.76}, {'field': 'total_amount', 'old_value': 38802.86, 'new_value': 45495.76}, {'field': 'order_count', 'old_value': 366, 'new_value': 391}]
2025-06-12 09:00:52,252 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1D
2025-06-12 09:00:52,690 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1D
2025-06-12 09:00:52,690 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1960.0, 'new_value': 2006.0}, {'field': 'offline_amount', 'old_value': 11518.3, 'new_value': 12394.7}, {'field': 'total_amount', 'old_value': 13478.3, 'new_value': 14400.7}, {'field': 'order_count', 'old_value': 492, 'new_value': 528}]
2025-06-12 09:00:52,690 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM2D
2025-06-12 09:00:53,174 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM2D
2025-06-12 09:00:53,174 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67413.0, 'new_value': 70543.0}, {'field': 'total_amount', 'old_value': 71319.2, 'new_value': 74449.2}, {'field': 'order_count', 'old_value': 37, 'new_value': 39}]
2025-06-12 09:00:53,174 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5D
2025-06-12 09:00:53,596 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5D
2025-06-12 09:00:53,596 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21303.0, 'new_value': 21981.0}, {'field': 'total_amount', 'old_value': 21303.0, 'new_value': 21981.0}, {'field': 'order_count', 'old_value': 62, 'new_value': 64}]
2025-06-12 09:00:53,596 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7D
2025-06-12 09:00:54,205 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7D
2025-06-12 09:00:54,205 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10335.0, 'new_value': 11796.0}, {'field': 'total_amount', 'old_value': 10395.0, 'new_value': 11856.0}, {'field': 'order_count', 'old_value': 41, 'new_value': 45}]
2025-06-12 09:00:54,205 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM8D
2025-06-12 09:00:54,721 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM8D
2025-06-12 09:00:54,721 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6480.0, 'new_value': 7897.0}, {'field': 'total_amount', 'old_value': 6480.0, 'new_value': 7897.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 44}]
2025-06-12 09:00:54,721 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAD
2025-06-12 09:00:55,236 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAD
2025-06-12 09:00:55,236 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35831.57, 'new_value': 39006.87}, {'field': 'offline_amount', 'old_value': 66759.29, 'new_value': 69924.16}, {'field': 'total_amount', 'old_value': 102590.86, 'new_value': 108931.03}, {'field': 'order_count', 'old_value': 580, 'new_value': 622}]
2025-06-12 09:00:55,236 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBD
2025-06-12 09:00:55,674 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBD
2025-06-12 09:00:55,674 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30866.86, 'new_value': 33380.06}, {'field': 'total_amount', 'old_value': 30866.86, 'new_value': 33380.06}, {'field': 'order_count', 'old_value': 868, 'new_value': 933}]
2025-06-12 09:00:55,674 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDD
2025-06-12 09:00:56,127 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDD
2025-06-12 09:00:56,127 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13189.0, 'new_value': 13226.68}, {'field': 'offline_amount', 'old_value': 24887.92, 'new_value': 27121.52}, {'field': 'total_amount', 'old_value': 38076.92, 'new_value': 40348.2}, {'field': 'order_count', 'old_value': 216, 'new_value': 226}]
2025-06-12 09:00:56,127 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKD
2025-06-12 09:00:56,627 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKD
2025-06-12 09:00:56,627 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18885.8, 'new_value': 19274.6}, {'field': 'total_amount', 'old_value': 18885.8, 'new_value': 19274.6}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-06-12 09:00:56,627 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLD
2025-06-12 09:00:57,080 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLD
2025-06-12 09:00:57,080 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20825.0, 'new_value': 21623.0}, {'field': 'total_amount', 'old_value': 20825.0, 'new_value': 21623.0}, {'field': 'order_count', 'old_value': 32, 'new_value': 33}]
2025-06-12 09:00:57,080 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQD
2025-06-12 09:00:57,596 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQD
2025-06-12 09:00:57,596 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 69898.96, 'new_value': 75881.75}, {'field': 'offline_amount', 'old_value': 59900.35, 'new_value': 64436.03}, {'field': 'total_amount', 'old_value': 129799.31, 'new_value': 140317.78}, {'field': 'order_count', 'old_value': 1121, 'new_value': 1230}]
2025-06-12 09:00:57,596 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSD
2025-06-12 09:00:58,111 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSD
2025-06-12 09:00:58,111 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14368.0, 'new_value': 14768.0}, {'field': 'total_amount', 'old_value': 47677.0, 'new_value': 48077.0}, {'field': 'order_count', 'old_value': 44, 'new_value': 46}]
2025-06-12 09:00:58,111 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXD
2025-06-12 09:00:58,611 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXD
2025-06-12 09:00:58,627 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6478.0, 'new_value': 6783.0}, {'field': 'total_amount', 'old_value': 6478.0, 'new_value': 6783.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-06-12 09:00:58,627 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVQ
2025-06-12 09:00:59,080 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVQ
2025-06-12 09:00:59,080 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4724.39, 'new_value': 5127.9}, {'field': 'offline_amount', 'old_value': 118256.79, 'new_value': 127431.39}, {'field': 'total_amount', 'old_value': 122981.18, 'new_value': 132559.29}, {'field': 'order_count', 'old_value': 6280, 'new_value': 6893}]
2025-06-12 09:00:59,080 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2R
2025-06-12 09:00:59,580 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2R
2025-06-12 09:00:59,580 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58094.69, 'new_value': 61689.14}, {'field': 'total_amount', 'old_value': 58094.69, 'new_value': 61689.14}, {'field': 'order_count', 'old_value': 1576, 'new_value': 1688}]
2025-06-12 09:00:59,580 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5R
2025-06-12 09:01:00,017 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5R
2025-06-12 09:01:00,017 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 135633.8, 'new_value': 140761.7}, {'field': 'total_amount', 'old_value': 135633.8, 'new_value': 140761.7}, {'field': 'order_count', 'old_value': 1349, 'new_value': 1401}]
2025-06-12 09:01:00,017 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6R
2025-06-12 09:01:00,486 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6R
2025-06-12 09:01:00,486 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7656.0, 'new_value': 7715.0}, {'field': 'total_amount', 'old_value': 7656.0, 'new_value': 7715.0}, {'field': 'order_count', 'old_value': 132, 'new_value': 133}]
2025-06-12 09:01:00,486 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7R
2025-06-12 09:01:00,955 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7R
2025-06-12 09:01:00,955 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20427.5, 'new_value': 25112.3}, {'field': 'total_amount', 'old_value': 27052.9, 'new_value': 31737.7}, {'field': 'order_count', 'old_value': 70, 'new_value': 78}]
2025-06-12 09:01:00,955 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8R
2025-06-12 09:01:01,517 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8R
2025-06-12 09:01:01,517 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2786300.0, 'new_value': 3586300.0}, {'field': 'total_amount', 'old_value': 2786300.0, 'new_value': 3586300.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 10}]
2025-06-12 09:01:01,517 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDR
2025-06-12 09:01:01,971 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDR
2025-06-12 09:01:01,971 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16648.69, 'new_value': 17187.1}, {'field': 'offline_amount', 'old_value': 12768.0, 'new_value': 13615.0}, {'field': 'total_amount', 'old_value': 29416.69, 'new_value': 30802.1}, {'field': 'order_count', 'old_value': 395, 'new_value': 415}]
2025-06-12 09:01:01,971 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGR
2025-06-12 09:01:02,439 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGR
2025-06-12 09:01:02,439 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 418398.0, 'new_value': 449897.0}, {'field': 'total_amount', 'old_value': 418398.0, 'new_value': 449897.0}, {'field': 'order_count', 'old_value': 428, 'new_value': 474}]
2025-06-12 09:01:02,439 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMJR
2025-06-12 09:01:02,892 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMJR
2025-06-12 09:01:02,892 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31607.0, 'new_value': 40199.0}, {'field': 'total_amount', 'old_value': 31607.0, 'new_value': 40199.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 13}]
2025-06-12 09:01:02,892 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLR
2025-06-12 09:01:03,361 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLR
2025-06-12 09:01:03,361 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45235.28, 'new_value': 47874.88}, {'field': 'total_amount', 'old_value': 45235.28, 'new_value': 47874.88}, {'field': 'order_count', 'old_value': 3754, 'new_value': 3949}]
2025-06-12 09:01:03,361 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSR
2025-06-12 09:01:03,908 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSR
2025-06-12 09:01:03,908 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12861.6, 'new_value': 12916.6}, {'field': 'total_amount', 'old_value': 12861.6, 'new_value': 12916.6}, {'field': 'order_count', 'old_value': 121, 'new_value': 122}]
2025-06-12 09:01:03,908 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2S
2025-06-12 09:01:04,345 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2S
2025-06-12 09:01:04,345 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 9.0}, {'field': 'offline_amount', 'old_value': 19379.1, 'new_value': 21039.1}, {'field': 'total_amount', 'old_value': 19379.1, 'new_value': 21048.1}, {'field': 'order_count', 'old_value': 771, 'new_value': 841}]
2025-06-12 09:01:04,345 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4S
2025-06-12 09:01:04,767 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4S
2025-06-12 09:01:04,767 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 135442.84, 'new_value': 148228.84}, {'field': 'total_amount', 'old_value': 155777.84, 'new_value': 168563.84}, {'field': 'order_count', 'old_value': 810, 'new_value': 865}]
2025-06-12 09:01:04,767 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6S
2025-06-12 09:01:05,299 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6S
2025-06-12 09:01:05,299 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28746.0, 'new_value': 33107.0}, {'field': 'total_amount', 'old_value': 31561.0, 'new_value': 35922.0}, {'field': 'order_count', 'old_value': 114, 'new_value': 129}]
2025-06-12 09:01:05,299 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9S
2025-06-12 09:01:05,752 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9S
2025-06-12 09:01:05,752 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 85165.02, 'new_value': 91426.6}, {'field': 'offline_amount', 'old_value': 18766.84, 'new_value': 20310.85}, {'field': 'total_amount', 'old_value': 103931.86, 'new_value': 111737.45}, {'field': 'order_count', 'old_value': 410, 'new_value': 445}]
2025-06-12 09:01:05,752 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBS
2025-06-12 09:01:06,283 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBS
2025-06-12 09:01:06,283 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32560.2, 'new_value': 33501.0}, {'field': 'total_amount', 'old_value': 32560.2, 'new_value': 33501.0}, {'field': 'order_count', 'old_value': 174, 'new_value': 180}]
2025-06-12 09:01:06,283 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCS
2025-06-12 09:01:06,783 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCS
2025-06-12 09:01:06,783 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 171973.0, 'new_value': 173389.0}, {'field': 'total_amount', 'old_value': 171973.0, 'new_value': 173389.0}, {'field': 'order_count', 'old_value': 46, 'new_value': 47}]
2025-06-12 09:01:06,783 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDS
2025-06-12 09:01:07,220 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDS
2025-06-12 09:01:07,220 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9355.03, 'new_value': 10309.37}, {'field': 'offline_amount', 'old_value': 114905.62, 'new_value': 119511.9}, {'field': 'total_amount', 'old_value': 124260.65, 'new_value': 129821.27}, {'field': 'order_count', 'old_value': 606, 'new_value': 631}]
2025-06-12 09:01:07,220 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMES
2025-06-12 09:01:07,720 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMES
2025-06-12 09:01:07,720 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65854.0, 'new_value': 68346.9}, {'field': 'total_amount', 'old_value': 65854.0, 'new_value': 68346.9}, {'field': 'order_count', 'old_value': 148, 'new_value': 155}]
2025-06-12 09:01:07,720 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGS
2025-06-12 09:01:08,142 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGS
2025-06-12 09:01:08,142 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 105441.25, 'new_value': 112706.38}, {'field': 'offline_amount', 'old_value': 35726.45, 'new_value': 37172.55}, {'field': 'total_amount', 'old_value': 141167.7, 'new_value': 149878.93}, {'field': 'order_count', 'old_value': 848, 'new_value': 913}]
2025-06-12 09:01:08,142 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHS
2025-06-12 09:01:08,673 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHS
2025-06-12 09:01:08,673 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57604.0, 'new_value': 63268.0}, {'field': 'total_amount', 'old_value': 62171.0, 'new_value': 67835.0}, {'field': 'order_count', 'old_value': 98, 'new_value': 110}]
2025-06-12 09:01:08,673 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKS
2025-06-12 09:01:09,142 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKS
2025-06-12 09:01:09,142 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68723.5, 'new_value': 74259.8}, {'field': 'total_amount', 'old_value': 68723.5, 'new_value': 74259.8}, {'field': 'order_count', 'old_value': 905, 'new_value': 989}]
2025-06-12 09:01:09,142 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLS
2025-06-12 09:01:09,611 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLS
2025-06-12 09:01:09,611 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14268.1, 'new_value': 14745.1}, {'field': 'total_amount', 'old_value': 14268.1, 'new_value': 14745.1}, {'field': 'order_count', 'old_value': 36, 'new_value': 37}]
2025-06-12 09:01:09,611 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPS
2025-06-12 09:01:10,033 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPS
2025-06-12 09:01:10,033 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45502.0, 'new_value': 48192.0}, {'field': 'offline_amount', 'old_value': 174248.0, 'new_value': 205585.0}, {'field': 'total_amount', 'old_value': 219750.0, 'new_value': 253777.0}, {'field': 'order_count', 'old_value': 289, 'new_value': 317}]
2025-06-12 09:01:10,033 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSS
2025-06-12 09:01:10,517 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSS
2025-06-12 09:01:10,517 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22661.6, 'new_value': 25072.1}, {'field': 'offline_amount', 'old_value': 148035.17, 'new_value': 156452.72}, {'field': 'total_amount', 'old_value': 170696.77, 'new_value': 181524.82}, {'field': 'order_count', 'old_value': 1055, 'new_value': 1127}]
2025-06-12 09:01:10,517 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVS
2025-06-12 09:01:10,970 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVS
2025-06-12 09:01:10,970 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 99357.5, 'new_value': 100664.0}, {'field': 'total_amount', 'old_value': 99357.5, 'new_value': 100664.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 22}]
2025-06-12 09:01:10,970 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0T
2025-06-12 09:01:11,423 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0T
2025-06-12 09:01:11,423 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 652766.0, 'new_value': 706393.4}, {'field': 'total_amount', 'old_value': 711244.7, 'new_value': 764872.1}, {'field': 'order_count', 'old_value': 1304, 'new_value': 1403}]
2025-06-12 09:01:11,423 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1T
2025-06-12 09:01:11,939 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1T
2025-06-12 09:01:11,939 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11050.93, 'new_value': 12833.53}, {'field': 'total_amount', 'old_value': 11050.93, 'new_value': 12833.53}, {'field': 'order_count', 'old_value': 21, 'new_value': 23}]
2025-06-12 09:01:11,939 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7T
2025-06-12 09:01:12,486 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7T
2025-06-12 09:01:12,486 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 222486.0, 'new_value': 246982.0}, {'field': 'total_amount', 'old_value': 222486.0, 'new_value': 246982.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 28}]
2025-06-12 09:01:12,486 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMET
2025-06-12 09:01:12,955 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMET
2025-06-12 09:01:12,955 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55090.0, 'new_value': 66136.0}, {'field': 'total_amount', 'old_value': 55090.0, 'new_value': 66136.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 14}]
2025-06-12 09:01:12,955 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFT
2025-06-12 09:01:13,439 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFT
2025-06-12 09:01:13,439 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 270993.0, 'new_value': 296789.0}, {'field': 'total_amount', 'old_value': 270993.0, 'new_value': 296789.0}, {'field': 'order_count', 'old_value': 1295, 'new_value': 1424}]
2025-06-12 09:01:13,439 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIT
2025-06-12 09:01:13,892 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIT
2025-06-12 09:01:13,892 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2516.61, 'new_value': 2730.41}, {'field': 'total_amount', 'old_value': 15256.61, 'new_value': 15470.41}, {'field': 'order_count', 'old_value': 47, 'new_value': 49}]
2025-06-12 09:01:13,892 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBK
2025-06-12 09:01:14,298 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBK
2025-06-12 09:01:14,298 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 169816.0, 'new_value': 188449.0}, {'field': 'total_amount', 'old_value': 169816.0, 'new_value': 188449.0}, {'field': 'order_count', 'old_value': 168, 'new_value': 188}]
2025-06-12 09:01:14,298 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCK
2025-06-12 09:01:14,720 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCK
2025-06-12 09:01:14,720 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 93964.0, 'new_value': 99060.0}, {'field': 'total_amount', 'old_value': 93964.0, 'new_value': 99060.0}, {'field': 'order_count', 'old_value': 95, 'new_value': 103}]
2025-06-12 09:01:14,720 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGK
2025-06-12 09:01:15,220 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGK
2025-06-12 09:01:15,220 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11441.22, 'new_value': 12552.44}, {'field': 'offline_amount', 'old_value': 11244.34, 'new_value': 12159.14}, {'field': 'total_amount', 'old_value': 22685.56, 'new_value': 24711.58}, {'field': 'order_count', 'old_value': 1017, 'new_value': 1119}]
2025-06-12 09:01:15,220 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOK
2025-06-12 09:01:15,704 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOK
2025-06-12 09:01:15,704 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 159699.0, 'new_value': 171360.0}, {'field': 'total_amount', 'old_value': 159699.0, 'new_value': 171360.0}, {'field': 'order_count', 'old_value': 32, 'new_value': 35}]
2025-06-12 09:01:15,704 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQK
2025-06-12 09:01:16,251 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQK
2025-06-12 09:01:16,251 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19889.78, 'new_value': 21783.58}, {'field': 'offline_amount', 'old_value': 13665.21, 'new_value': 14987.81}, {'field': 'total_amount', 'old_value': 33554.99, 'new_value': 36771.39}, {'field': 'order_count', 'old_value': 1941, 'new_value': 2110}]
2025-06-12 09:01:16,251 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRK
2025-06-12 09:01:16,658 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRK
2025-06-12 09:01:16,658 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46324.0, 'new_value': 48673.0}, {'field': 'offline_amount', 'old_value': 30396.8, 'new_value': 31079.6}, {'field': 'total_amount', 'old_value': 76720.8, 'new_value': 79752.6}, {'field': 'order_count', 'old_value': 486, 'new_value': 510}]
2025-06-12 09:01:16,658 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVK
2025-06-12 09:01:17,142 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVK
2025-06-12 09:01:17,142 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36270.09, 'new_value': 38750.44}, {'field': 'offline_amount', 'old_value': 35361.42, 'new_value': 38755.23}, {'field': 'total_amount', 'old_value': 71631.51, 'new_value': 77505.67}, {'field': 'order_count', 'old_value': 2973, 'new_value': 3249}]
2025-06-12 09:01:17,142 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0L
2025-06-12 09:01:17,595 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0L
2025-06-12 09:01:17,595 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18206.49, 'new_value': 18305.29}, {'field': 'total_amount', 'old_value': 18206.49, 'new_value': 18305.29}, {'field': 'order_count', 'old_value': 83, 'new_value': 84}]
2025-06-12 09:01:17,595 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1L
2025-06-12 09:01:18,064 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1L
2025-06-12 09:01:18,064 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 130336.51, 'new_value': 140282.61}, {'field': 'offline_amount', 'old_value': 49262.8, 'new_value': 52959.4}, {'field': 'total_amount', 'old_value': 179599.31, 'new_value': 193242.01}, {'field': 'order_count', 'old_value': 558, 'new_value': 598}]
2025-06-12 09:01:18,064 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM5L
2025-06-12 09:01:18,501 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM5L
2025-06-12 09:01:18,501 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 92964.0, 'new_value': 103116.0}, {'field': 'total_amount', 'old_value': 92964.0, 'new_value': 103116.0}, {'field': 'order_count', 'old_value': 7747, 'new_value': 8593}]
2025-06-12 09:01:18,501 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8L
2025-06-12 09:01:18,970 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8L
2025-06-12 09:01:18,970 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 97650.47, 'new_value': 106709.86}, {'field': 'offline_amount', 'old_value': 285800.73, 'new_value': 305865.06}, {'field': 'total_amount', 'old_value': 383451.2, 'new_value': 412574.92}, {'field': 'order_count', 'old_value': 2294, 'new_value': 2503}]
2025-06-12 09:01:18,970 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEL
2025-06-12 09:01:19,407 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEL
2025-06-12 09:01:19,407 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 99598.37, 'new_value': 104060.37}, {'field': 'total_amount', 'old_value': 99598.37, 'new_value': 104060.37}, {'field': 'order_count', 'old_value': 580, 'new_value': 611}]
2025-06-12 09:01:19,407 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMML
2025-06-12 09:01:19,923 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMML
2025-06-12 09:01:19,923 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98904.25, 'new_value': 105171.2}, {'field': 'total_amount', 'old_value': 98904.25, 'new_value': 105171.2}, {'field': 'order_count', 'old_value': 346, 'new_value': 370}]
2025-06-12 09:01:19,923 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNL
2025-06-12 09:01:20,376 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNL
2025-06-12 09:01:20,376 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15057.89, 'new_value': 16959.85}, {'field': 'offline_amount', 'old_value': 19720.47, 'new_value': 20638.28}, {'field': 'total_amount', 'old_value': 34778.36, 'new_value': 37598.13}, {'field': 'order_count', 'old_value': 2983, 'new_value': 3239}]
2025-06-12 09:01:20,376 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOL
2025-06-12 09:01:20,798 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOL
2025-06-12 09:01:20,798 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15370.72, 'new_value': 16836.02}, {'field': 'offline_amount', 'old_value': 436582.11, 'new_value': 476450.31}, {'field': 'total_amount', 'old_value': 451952.83, 'new_value': 493286.33}, {'field': 'order_count', 'old_value': 2030, 'new_value': 2201}]
2025-06-12 09:01:20,798 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSL
2025-06-12 09:01:21,236 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSL
2025-06-12 09:01:21,236 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38368.5, 'new_value': 41675.9}, {'field': 'total_amount', 'old_value': 38368.5, 'new_value': 41675.9}, {'field': 'order_count', 'old_value': 85, 'new_value': 92}]
2025-06-12 09:01:21,236 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUL
2025-06-12 09:01:21,689 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUL
2025-06-12 09:01:21,689 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43422.0, 'new_value': 45002.0}, {'field': 'total_amount', 'old_value': 43422.0, 'new_value': 45002.0}, {'field': 'order_count', 'old_value': 1301, 'new_value': 1349}]
2025-06-12 09:01:21,689 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVL
2025-06-12 09:01:22,189 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVL
2025-06-12 09:01:22,189 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 141085.18, 'new_value': 151443.55}, {'field': 'total_amount', 'old_value': 141085.18, 'new_value': 151443.55}, {'field': 'order_count', 'old_value': 490, 'new_value': 529}]
2025-06-12 09:01:22,189 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0M
2025-06-12 09:01:22,689 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0M
2025-06-12 09:01:22,689 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 8619.0}, {'field': 'offline_amount', 'old_value': 61814.0, 'new_value': 62437.0}, {'field': 'total_amount', 'old_value': 61814.0, 'new_value': 71056.0}]
2025-06-12 09:01:22,689 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM3M
2025-06-12 09:01:23,157 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM3M
2025-06-12 09:01:23,157 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32413.0, 'new_value': 36290.0}, {'field': 'total_amount', 'old_value': 32413.0, 'new_value': 36290.0}, {'field': 'order_count', 'old_value': 275, 'new_value': 304}]
2025-06-12 09:01:23,157 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM7M
2025-06-12 09:01:23,626 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM7M
2025-06-12 09:01:23,626 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8657.8, 'new_value': 9115.7}, {'field': 'total_amount', 'old_value': 8657.8, 'new_value': 9115.7}, {'field': 'order_count', 'old_value': 21, 'new_value': 24}]
2025-06-12 09:01:23,626 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMAM
2025-06-12 09:01:24,079 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMAM
2025-06-12 09:01:24,079 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 800000.0, 'new_value': 850000.0}, {'field': 'total_amount', 'old_value': 900000.0, 'new_value': 950000.0}, {'field': 'order_count', 'old_value': 362, 'new_value': 363}]
2025-06-12 09:01:24,079 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDM
2025-06-12 09:01:24,579 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDM
2025-06-12 09:01:24,579 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 62993.0, 'new_value': 67202.0}, {'field': 'offline_amount', 'old_value': 34147.0, 'new_value': 37688.0}, {'field': 'total_amount', 'old_value': 97140.0, 'new_value': 104890.0}, {'field': 'order_count', 'old_value': 1360, 'new_value': 1499}]
2025-06-12 09:01:24,579 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFM
2025-06-12 09:01:25,048 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFM
2025-06-12 09:01:25,048 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 129759.2, 'new_value': 131669.88}, {'field': 'offline_amount', 'old_value': 589908.75, 'new_value': 627132.37}, {'field': 'total_amount', 'old_value': 719667.95, 'new_value': 758802.25}, {'field': 'order_count', 'old_value': 3400, 'new_value': 3630}]
2025-06-12 09:01:25,048 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGM
2025-06-12 09:01:25,517 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGM
2025-06-12 09:01:25,517 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11462.94, 'new_value': 12273.94}, {'field': 'offline_amount', 'old_value': 14460.8, 'new_value': 14629.8}, {'field': 'total_amount', 'old_value': 25923.74, 'new_value': 26903.74}, {'field': 'order_count', 'old_value': 6564, 'new_value': 6575}]
2025-06-12 09:01:25,517 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIM
2025-06-12 09:01:25,954 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIM
2025-06-12 09:01:25,954 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 486639.0, 'new_value': 497016.0}, {'field': 'total_amount', 'old_value': 486639.0, 'new_value': 497016.0}, {'field': 'order_count', 'old_value': 266, 'new_value': 276}]
2025-06-12 09:01:25,954 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJM
2025-06-12 09:01:26,423 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJM
2025-06-12 09:01:26,423 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55286.6, 'new_value': 64166.8}, {'field': 'total_amount', 'old_value': 130905.5, 'new_value': 139785.7}, {'field': 'order_count', 'old_value': 3195, 'new_value': 3495}]
2025-06-12 09:01:26,423 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNM
2025-06-12 09:01:26,845 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNM
2025-06-12 09:01:26,845 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11261.0, 'new_value': 15692.0}, {'field': 'total_amount', 'old_value': 11261.0, 'new_value': 15692.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 25}]
2025-06-12 09:01:26,845 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOM
2025-06-12 09:01:27,313 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOM
2025-06-12 09:01:27,313 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96164.2, 'new_value': 102117.8}, {'field': 'total_amount', 'old_value': 96164.2, 'new_value': 102117.8}, {'field': 'order_count', 'old_value': 126, 'new_value': 136}]
2025-06-12 09:01:27,313 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRM
2025-06-12 09:01:27,767 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRM
2025-06-12 09:01:27,767 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 659966.0, 'new_value': 701720.0}, {'field': 'total_amount', 'old_value': 659966.0, 'new_value': 701720.0}, {'field': 'order_count', 'old_value': 3000, 'new_value': 3271}]
2025-06-12 09:01:27,767 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWM
2025-06-12 09:01:28,235 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWM
2025-06-12 09:01:28,235 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 110060.79, 'new_value': 143053.79}, {'field': 'total_amount', 'old_value': 124055.79, 'new_value': 157048.79}, {'field': 'order_count', 'old_value': 24, 'new_value': 30}]
2025-06-12 09:01:28,235 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZM
2025-06-12 09:01:28,720 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZM
2025-06-12 09:01:28,720 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45092.0, 'new_value': 48878.0}, {'field': 'total_amount', 'old_value': 45092.0, 'new_value': 48878.0}, {'field': 'order_count', 'old_value': 98, 'new_value': 110}]
2025-06-12 09:01:28,720 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0N
2025-06-12 09:01:29,173 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0N
2025-06-12 09:01:29,173 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21280.0, 'new_value': 23215.0}, {'field': 'total_amount', 'old_value': 21280.0, 'new_value': 23215.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-06-12 09:01:29,173 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP01
2025-06-12 09:01:29,626 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP01
2025-06-12 09:01:29,626 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53411.7, 'new_value': 58511.8}, {'field': 'offline_amount', 'old_value': 43526.1, 'new_value': 46628.7}, {'field': 'total_amount', 'old_value': 96937.8, 'new_value': 105140.5}, {'field': 'order_count', 'old_value': 2289, 'new_value': 2496}]
2025-06-12 09:01:29,626 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ01
2025-06-12 09:01:30,079 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ01
2025-06-12 09:01:30,079 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4600000.0, 'new_value': 5000000.0}, {'field': 'total_amount', 'old_value': 4600000.0, 'new_value': 5000000.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 26}]
2025-06-12 09:01:30,079 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT01
2025-06-12 09:01:30,641 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT01
2025-06-12 09:01:30,641 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 322593.0, 'new_value': 347373.0}, {'field': 'total_amount', 'old_value': 322593.0, 'new_value': 347373.0}, {'field': 'order_count', 'old_value': 400, 'new_value': 440}]
2025-06-12 09:01:30,641 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU01
2025-06-12 09:01:31,048 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU01
2025-06-12 09:01:31,048 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37314.2, 'new_value': 41012.2}, {'field': 'total_amount', 'old_value': 37314.2, 'new_value': 41012.2}, {'field': 'order_count', 'old_value': 170, 'new_value': 186}]
2025-06-12 09:01:31,048 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW01
2025-06-12 09:01:31,516 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW01
2025-06-12 09:01:31,516 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13714.68, 'new_value': 15669.27}, {'field': 'offline_amount', 'old_value': 17811.03, 'new_value': 19873.74}, {'field': 'total_amount', 'old_value': 31525.71, 'new_value': 35543.01}, {'field': 'order_count', 'old_value': 1636, 'new_value': 1847}]
2025-06-12 09:01:31,516 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM211
2025-06-12 09:01:31,970 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM211
2025-06-12 09:01:31,970 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59400.2, 'new_value': 63006.1}, {'field': 'total_amount', 'old_value': 59400.2, 'new_value': 63006.1}, {'field': 'order_count', 'old_value': 273, 'new_value': 292}]
2025-06-12 09:01:31,970 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM911
2025-06-12 09:01:32,485 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM911
2025-06-12 09:01:32,485 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6745.84, 'new_value': 7283.49}, {'field': 'offline_amount', 'old_value': 44662.0, 'new_value': 55175.0}, {'field': 'total_amount', 'old_value': 51407.84, 'new_value': 62458.49}, {'field': 'order_count', 'old_value': 24, 'new_value': 29}]
2025-06-12 09:01:32,485 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA11
2025-06-12 09:01:32,970 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA11
2025-06-12 09:01:32,970 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10104.0, 'new_value': 15084.0}, {'field': 'total_amount', 'old_value': 10104.0, 'new_value': 15084.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 19}]
2025-06-12 09:01:32,970 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD11
2025-06-12 09:01:33,391 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD11
2025-06-12 09:01:33,391 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28343.72, 'new_value': 30392.93}, {'field': 'total_amount', 'old_value': 28343.72, 'new_value': 30392.93}, {'field': 'order_count', 'old_value': 602, 'new_value': 623}]
2025-06-12 09:01:33,391 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME11
2025-06-12 09:01:33,845 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME11
2025-06-12 09:01:33,845 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65829.6, 'new_value': 66887.3}, {'field': 'total_amount', 'old_value': 65829.6, 'new_value': 66887.3}, {'field': 'order_count', 'old_value': 1827, 'new_value': 1859}]
2025-06-12 09:01:33,845 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMG11
2025-06-12 09:01:34,298 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMG11
2025-06-12 09:01:34,298 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88940.9, 'new_value': 95814.9}, {'field': 'total_amount', 'old_value': 88940.9, 'new_value': 95814.9}, {'field': 'order_count', 'old_value': 395, 'new_value': 424}]
2025-06-12 09:01:34,298 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMI11
2025-06-12 09:01:34,751 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMI11
2025-06-12 09:01:34,751 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2511.5, 'new_value': 2895.5}, {'field': 'offline_amount', 'old_value': 5766.86, 'new_value': 6340.34}, {'field': 'total_amount', 'old_value': 8278.36, 'new_value': 9235.84}, {'field': 'order_count', 'old_value': 92, 'new_value': 99}]
2025-06-12 09:01:34,751 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML11
2025-06-12 09:01:35,204 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML11
2025-06-12 09:01:35,204 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 127860.24, 'new_value': 131499.24}, {'field': 'offline_amount', 'old_value': 1935.0, 'new_value': 1943.0}, {'field': 'total_amount', 'old_value': 129795.24, 'new_value': 133442.24}, {'field': 'order_count', 'old_value': 10291, 'new_value': 10470}]
2025-06-12 09:01:35,204 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMM11
2025-06-12 09:01:35,626 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMM11
2025-06-12 09:01:35,626 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28217.79, 'new_value': 34181.75}, {'field': 'offline_amount', 'old_value': 124456.97, 'new_value': 131935.62}, {'field': 'total_amount', 'old_value': 152674.76, 'new_value': 166117.37}, {'field': 'order_count', 'old_value': 2673, 'new_value': 2824}]
2025-06-12 09:01:35,626 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN11
2025-06-12 09:01:36,110 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN11
2025-06-12 09:01:36,110 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 140383.0, 'new_value': 158238.0}, {'field': 'total_amount', 'old_value': 140383.0, 'new_value': 158238.0}, {'field': 'order_count', 'old_value': 117, 'new_value': 132}]
2025-06-12 09:01:36,110 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP11
2025-06-12 09:01:36,594 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP11
2025-06-12 09:01:36,594 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32959.0, 'new_value': 34147.0}, {'field': 'total_amount', 'old_value': 45546.0, 'new_value': 46734.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 33}]
2025-06-12 09:01:36,594 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ11
2025-06-12 09:01:37,063 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ11
2025-06-12 09:01:37,063 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 159.0, 'new_value': 471.0}, {'field': 'offline_amount', 'old_value': 23439.01, 'new_value': 26125.01}, {'field': 'total_amount', 'old_value': 23598.01, 'new_value': 26596.01}, {'field': 'order_count', 'old_value': 144, 'new_value': 161}]
2025-06-12 09:01:37,063 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR11
2025-06-12 09:01:37,532 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR11
2025-06-12 09:01:37,532 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7518.0, 'new_value': 7760.0}, {'field': 'offline_amount', 'old_value': 12132.0, 'new_value': 13122.0}, {'field': 'total_amount', 'old_value': 19650.0, 'new_value': 20882.0}, {'field': 'order_count', 'old_value': 152, 'new_value': 168}]
2025-06-12 09:01:37,532 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW11
2025-06-12 09:01:38,047 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW11
2025-06-12 09:01:38,047 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30327.0, 'new_value': 32806.0}, {'field': 'total_amount', 'old_value': 30327.0, 'new_value': 32806.0}, {'field': 'order_count', 'old_value': 1445, 'new_value': 1532}]
2025-06-12 09:01:38,047 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMY11
2025-06-12 09:01:38,610 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMY11
2025-06-12 09:01:38,610 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122195.53, 'new_value': 126592.65}, {'field': 'total_amount', 'old_value': 122195.53, 'new_value': 126592.65}, {'field': 'order_count', 'old_value': 630, 'new_value': 656}]
2025-06-12 09:01:38,610 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ11
2025-06-12 09:01:39,079 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ11
2025-06-12 09:01:39,079 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 123803.0, 'new_value': 130662.0}, {'field': 'total_amount', 'old_value': 123803.0, 'new_value': 130662.0}, {'field': 'order_count', 'old_value': 149, 'new_value': 160}]
2025-06-12 09:01:39,079 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM121
2025-06-12 09:01:39,547 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM121
2025-06-12 09:01:39,547 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39875.26, 'new_value': 43973.81}, {'field': 'offline_amount', 'old_value': 48364.07, 'new_value': 53421.97}, {'field': 'total_amount', 'old_value': 88239.33, 'new_value': 97395.78}, {'field': 'order_count', 'old_value': 2278, 'new_value': 2519}]
2025-06-12 09:01:39,547 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM221
2025-06-12 09:01:40,016 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM221
2025-06-12 09:01:40,016 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78757.58, 'new_value': 85871.31}, {'field': 'total_amount', 'old_value': 78757.58, 'new_value': 85871.31}, {'field': 'order_count', 'old_value': 329, 'new_value': 369}]
2025-06-12 09:01:40,016 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM421
2025-06-12 09:01:40,454 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM421
2025-06-12 09:01:40,454 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8516.0, 'new_value': 11514.0}, {'field': 'total_amount', 'old_value': 8516.0, 'new_value': 11514.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 5}]
2025-06-12 09:01:40,454 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM521
2025-06-12 09:01:41,001 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM521
2025-06-12 09:01:41,001 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46084.3, 'new_value': 55762.2}, {'field': 'total_amount', 'old_value': 120671.55, 'new_value': 130349.45}, {'field': 'order_count', 'old_value': 201, 'new_value': 220}]
2025-06-12 09:01:41,001 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM721
2025-06-12 09:01:41,469 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM721
2025-06-12 09:01:41,469 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1225.0, 'new_value': 1325.0}, {'field': 'offline_amount', 'old_value': 16890.0, 'new_value': 17175.0}, {'field': 'total_amount', 'old_value': 18115.0, 'new_value': 18500.0}, {'field': 'order_count', 'old_value': 233, 'new_value': 238}]
2025-06-12 09:01:41,469 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM821
2025-06-12 09:01:42,016 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM821
2025-06-12 09:01:42,016 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 61863.0, 'new_value': 68516.0}, {'field': 'offline_amount', 'old_value': 63892.0, 'new_value': 68994.0}, {'field': 'total_amount', 'old_value': 125755.0, 'new_value': 137510.0}, {'field': 'order_count', 'old_value': 63281, 'new_value': 75036}]
2025-06-12 09:01:42,016 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM921
2025-06-12 09:01:42,532 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM921
2025-06-12 09:01:42,532 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19138.53, 'new_value': 21293.64}, {'field': 'offline_amount', 'old_value': 28085.47, 'new_value': 31495.03}, {'field': 'total_amount', 'old_value': 47224.0, 'new_value': 52788.67}, {'field': 'order_count', 'old_value': 2326, 'new_value': 2587}]
2025-06-12 09:01:42,532 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA21
2025-06-12 09:01:42,969 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA21
2025-06-12 09:01:42,969 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2152.32, 'new_value': 2435.12}, {'field': 'offline_amount', 'old_value': 7871.8, 'new_value': 9266.8}, {'field': 'total_amount', 'old_value': 10024.12, 'new_value': 11701.92}, {'field': 'order_count', 'old_value': 39, 'new_value': 46}]
2025-06-12 09:01:42,969 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMH21
2025-06-12 09:01:43,469 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMH21
2025-06-12 09:01:43,469 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10253.0, 'new_value': 11449.0}, {'field': 'total_amount', 'old_value': 10253.0, 'new_value': 11449.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 18}]
2025-06-12 09:01:43,469 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ21
2025-06-12 09:01:43,969 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ21
2025-06-12 09:01:43,969 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39660.0, 'new_value': 42348.0}, {'field': 'total_amount', 'old_value': 39660.0, 'new_value': 42348.0}, {'field': 'order_count', 'old_value': 5105, 'new_value': 5280}]
2025-06-12 09:01:43,969 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK21
2025-06-12 09:01:44,391 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK21
2025-06-12 09:01:44,391 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46128.0, 'new_value': 90301.0}, {'field': 'offline_amount', 'old_value': 499059.0, 'new_value': 504286.0}, {'field': 'total_amount', 'old_value': 545187.0, 'new_value': 594587.0}, {'field': 'order_count', 'old_value': 13106, 'new_value': 14493}]
2025-06-12 09:01:44,391 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML21
2025-06-12 09:01:44,860 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML21
2025-06-12 09:01:44,860 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 113826.0, 'new_value': 128959.0}, {'field': 'total_amount', 'old_value': 113826.0, 'new_value': 128959.0}, {'field': 'order_count', 'old_value': 2649, 'new_value': 2984}]
2025-06-12 09:01:44,860 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP21
2025-06-12 09:01:45,329 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP21
2025-06-12 09:01:45,329 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16796.02, 'new_value': 18161.02}, {'field': 'total_amount', 'old_value': 16796.02, 'new_value': 18161.02}, {'field': 'order_count', 'old_value': 528, 'new_value': 579}]
2025-06-12 09:01:45,329 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW21
2025-06-12 09:01:45,797 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW21
2025-06-12 09:01:45,797 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 103320.0, 'new_value': 108986.0}, {'field': 'offline_amount', 'old_value': 6681.5, 'new_value': 6961.5}, {'field': 'total_amount', 'old_value': 110001.5, 'new_value': 115947.5}, {'field': 'order_count', 'old_value': 1032, 'new_value': 1105}]
2025-06-12 09:01:45,797 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ21
2025-06-12 09:01:46,282 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ21
2025-06-12 09:01:46,282 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12021.5, 'new_value': 13462.5}, {'field': 'offline_amount', 'old_value': 21003.7, 'new_value': 22994.8}, {'field': 'total_amount', 'old_value': 33025.2, 'new_value': 36457.3}, {'field': 'order_count', 'old_value': 1397, 'new_value': 1539}]
2025-06-12 09:01:46,282 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM331
2025-06-12 09:01:46,688 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM331
2025-06-12 09:01:46,688 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14986.8, 'new_value': 15746.93}, {'field': 'total_amount', 'old_value': 15356.4, 'new_value': 16116.53}, {'field': 'order_count', 'old_value': 143, 'new_value': 153}]
2025-06-12 09:01:46,688 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM431
2025-06-12 09:01:47,063 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM431
2025-06-12 09:01:47,063 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12676.99, 'new_value': 14477.99}, {'field': 'total_amount', 'old_value': 12676.99, 'new_value': 14477.99}, {'field': 'order_count', 'old_value': 40, 'new_value': 44}]
2025-06-12 09:01:47,063 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM31
2025-06-12 09:01:47,516 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM31
2025-06-12 09:01:47,516 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1129588.0, 'new_value': 1179588.0}, {'field': 'total_amount', 'old_value': 1129588.0, 'new_value': 1179588.0}, {'field': 'order_count', 'old_value': 1450, 'new_value': 1451}]
2025-06-12 09:01:47,516 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM61
2025-06-12 09:01:47,953 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM61
2025-06-12 09:01:47,953 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90000.0, 'new_value': 95000.0}, {'field': 'total_amount', 'old_value': 90000.0, 'new_value': 95000.0}, {'field': 'order_count', 'old_value': 129, 'new_value': 130}]
2025-06-12 09:01:47,953 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM71
2025-06-12 09:01:48,532 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM71
2025-06-12 09:01:48,532 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90000.0, 'new_value': 95000.0}, {'field': 'total_amount', 'old_value': 100000.0, 'new_value': 105000.0}, {'field': 'order_count', 'old_value': 219, 'new_value': 220}]
2025-06-12 09:01:48,532 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC1
2025-06-12 09:01:49,000 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC1
2025-06-12 09:01:49,000 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 293893.02, 'new_value': 306653.26}, {'field': 'total_amount', 'old_value': 293893.02, 'new_value': 306653.26}, {'field': 'order_count', 'old_value': 2055, 'new_value': 2164}]
2025-06-12 09:01:49,000 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ1
2025-06-12 09:01:49,469 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ1
2025-06-12 09:01:49,469 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62429.39, 'new_value': 66599.65}, {'field': 'total_amount', 'old_value': 62429.39, 'new_value': 66599.65}, {'field': 'order_count', 'old_value': 4481, 'new_value': 4768}]
2025-06-12 09:01:49,469 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML1
2025-06-12 09:01:49,938 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML1
2025-06-12 09:01:49,938 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 65114.84, 'new_value': 69689.1}, {'field': 'offline_amount', 'old_value': 18546.6, 'new_value': 19974.44}, {'field': 'total_amount', 'old_value': 83661.44, 'new_value': 89663.54}, {'field': 'order_count', 'old_value': 350, 'new_value': 382}]
2025-06-12 09:01:49,938 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMN1
2025-06-12 09:01:50,422 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMN1
2025-06-12 09:01:50,422 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90000.0, 'new_value': 95000.0}, {'field': 'total_amount', 'old_value': 90000.0, 'new_value': 95000.0}, {'field': 'order_count', 'old_value': 159, 'new_value': 160}]
2025-06-12 09:01:50,422 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS1
2025-06-12 09:01:50,875 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS1
2025-06-12 09:01:50,875 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 228469.0, 'new_value': 234166.0}, {'field': 'total_amount', 'old_value': 228469.0, 'new_value': 234166.0}, {'field': 'order_count', 'old_value': 5245, 'new_value': 5376}]
2025-06-12 09:01:50,875 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM12
2025-06-12 09:01:51,375 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM12
2025-06-12 09:01:51,375 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9703.0, 'new_value': 12429.0}, {'field': 'total_amount', 'old_value': 9703.0, 'new_value': 12429.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-06-12 09:01:51,375 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM32
2025-06-12 09:01:51,828 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM32
2025-06-12 09:01:51,828 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41799.0, 'new_value': 46429.0}, {'field': 'total_amount', 'old_value': 41799.0, 'new_value': 46429.0}, {'field': 'order_count', 'old_value': 192, 'new_value': 221}]
2025-06-12 09:01:51,828 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM92
2025-06-12 09:01:52,203 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM92
2025-06-12 09:01:52,203 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18815.0, 'new_value': 25175.0}, {'field': 'total_amount', 'old_value': 22260.0, 'new_value': 28620.0}, {'field': 'order_count', 'old_value': 84, 'new_value': 108}]
2025-06-12 09:01:52,203 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMB2
2025-06-12 09:01:52,672 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMB2
2025-06-12 09:01:52,672 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17849.01, 'new_value': 19571.33}, {'field': 'total_amount', 'old_value': 17849.01, 'new_value': 19571.33}, {'field': 'order_count', 'old_value': 2268, 'new_value': 2487}]
2025-06-12 09:01:52,672 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMD2
2025-06-12 09:01:53,125 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMD2
2025-06-12 09:01:53,125 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 125148.72, 'new_value': 133528.08}, {'field': 'total_amount', 'old_value': 125148.72, 'new_value': 133528.08}, {'field': 'order_count', 'old_value': 354, 'new_value': 374}]
2025-06-12 09:01:53,125 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMN2
2025-06-12 09:01:53,610 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMN2
2025-06-12 09:01:53,610 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59285.0, 'new_value': 65485.0}, {'field': 'total_amount', 'old_value': 59285.0, 'new_value': 65485.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-06-12 09:01:53,610 - INFO - 开始批量插入 1 条新记录
2025-06-12 09:01:53,781 - INFO - 批量插入响应状态码: 200
2025-06-12 09:01:53,781 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 12 Jun 2025 01:01:54 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '729183BD-A19D-748B-9D39-9612FF13B315', 'x-acs-trace-id': 'def39540e6f563dce8ed753e7fc28c8f', 'etag': '6nDd3V3DQXGkkn/h5ZHisfw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-12 09:01:53,781 - INFO - 批量插入响应体: {'result': ['FINST-3PF66271XO5WRCRF89PMI7Q6G2YE3F4HBOSBM0D']}
2025-06-12 09:01:53,781 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-12 09:01:53,781 - INFO - 成功插入的数据ID: ['FINST-3PF66271XO5WRCRF89PMI7Q6G2YE3F4HBOSBM0D']
2025-06-12 09:01:56,797 - INFO - 批量插入完成，共 1 条记录
2025-06-12 09:01:56,797 - INFO - 日期 2025-06 处理完成 - 更新: 166 条，插入: 1 条，错误: 0 条
2025-06-12 09:01:56,797 - INFO - 数据同步完成！更新: 166 条，插入: 1 条，错误: 0 条
2025-06-12 09:01:56,797 - INFO - =================同步完成====================
2025-06-12 12:00:02,635 - INFO - =================使用默认全量同步=============
2025-06-12 12:00:04,307 - INFO - MySQL查询成功，共获取 3930 条记录
2025-06-12 12:00:04,307 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-12 12:00:04,338 - INFO - 开始处理日期: 2025-01
2025-06-12 12:00:04,338 - INFO - Request Parameters - Page 1:
2025-06-12 12:00:04,338 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:04,338 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:05,431 - INFO - Response - Page 1:
2025-06-12 12:00:05,635 - INFO - 第 1 页获取到 100 条记录
2025-06-12 12:00:05,635 - INFO - Request Parameters - Page 2:
2025-06-12 12:00:05,635 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:05,635 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:06,400 - INFO - Response - Page 2:
2025-06-12 12:00:06,603 - INFO - 第 2 页获取到 100 条记录
2025-06-12 12:00:06,603 - INFO - Request Parameters - Page 3:
2025-06-12 12:00:06,603 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:06,603 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:07,135 - INFO - Response - Page 3:
2025-06-12 12:00:07,338 - INFO - 第 3 页获取到 100 条记录
2025-06-12 12:00:07,338 - INFO - Request Parameters - Page 4:
2025-06-12 12:00:07,338 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:07,338 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:07,885 - INFO - Response - Page 4:
2025-06-12 12:00:08,088 - INFO - 第 4 页获取到 100 条记录
2025-06-12 12:00:08,088 - INFO - Request Parameters - Page 5:
2025-06-12 12:00:08,088 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:08,088 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:08,588 - INFO - Response - Page 5:
2025-06-12 12:00:08,791 - INFO - 第 5 页获取到 100 条记录
2025-06-12 12:00:08,791 - INFO - Request Parameters - Page 6:
2025-06-12 12:00:08,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:08,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:09,306 - INFO - Response - Page 6:
2025-06-12 12:00:09,509 - INFO - 第 6 页获取到 100 条记录
2025-06-12 12:00:09,509 - INFO - Request Parameters - Page 7:
2025-06-12 12:00:09,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:09,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:09,963 - INFO - Response - Page 7:
2025-06-12 12:00:10,166 - INFO - 第 7 页获取到 82 条记录
2025-06-12 12:00:10,166 - INFO - 查询完成，共获取到 682 条记录
2025-06-12 12:00:10,166 - INFO - 获取到 682 条表单数据
2025-06-12 12:00:10,166 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-12 12:00:10,181 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 12:00:10,181 - INFO - 开始处理日期: 2025-02
2025-06-12 12:00:10,181 - INFO - Request Parameters - Page 1:
2025-06-12 12:00:10,181 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:10,181 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:10,900 - INFO - Response - Page 1:
2025-06-12 12:00:11,103 - INFO - 第 1 页获取到 100 条记录
2025-06-12 12:00:11,103 - INFO - Request Parameters - Page 2:
2025-06-12 12:00:11,103 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:11,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:11,666 - INFO - Response - Page 2:
2025-06-12 12:00:11,869 - INFO - 第 2 页获取到 100 条记录
2025-06-12 12:00:11,869 - INFO - Request Parameters - Page 3:
2025-06-12 12:00:11,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:11,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:12,494 - INFO - Response - Page 3:
2025-06-12 12:00:12,697 - INFO - 第 3 页获取到 100 条记录
2025-06-12 12:00:12,697 - INFO - Request Parameters - Page 4:
2025-06-12 12:00:12,697 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:12,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:13,166 - INFO - Response - Page 4:
2025-06-12 12:00:13,369 - INFO - 第 4 页获取到 100 条记录
2025-06-12 12:00:13,369 - INFO - Request Parameters - Page 5:
2025-06-12 12:00:13,369 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:13,369 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:13,962 - INFO - Response - Page 5:
2025-06-12 12:00:14,166 - INFO - 第 5 页获取到 100 条记录
2025-06-12 12:00:14,166 - INFO - Request Parameters - Page 6:
2025-06-12 12:00:14,166 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:14,166 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:14,666 - INFO - Response - Page 6:
2025-06-12 12:00:14,869 - INFO - 第 6 页获取到 100 条记录
2025-06-12 12:00:14,869 - INFO - Request Parameters - Page 7:
2025-06-12 12:00:14,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:14,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:15,416 - INFO - Response - Page 7:
2025-06-12 12:00:15,619 - INFO - 第 7 页获取到 70 条记录
2025-06-12 12:00:15,619 - INFO - 查询完成，共获取到 670 条记录
2025-06-12 12:00:15,619 - INFO - 获取到 670 条表单数据
2025-06-12 12:00:15,619 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-12 12:00:15,634 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 12:00:15,634 - INFO - 开始处理日期: 2025-03
2025-06-12 12:00:15,634 - INFO - Request Parameters - Page 1:
2025-06-12 12:00:15,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:15,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:16,150 - INFO - Response - Page 1:
2025-06-12 12:00:16,353 - INFO - 第 1 页获取到 100 条记录
2025-06-12 12:00:16,353 - INFO - Request Parameters - Page 2:
2025-06-12 12:00:16,353 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:16,353 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:16,837 - INFO - Response - Page 2:
2025-06-12 12:00:17,040 - INFO - 第 2 页获取到 100 条记录
2025-06-12 12:00:17,040 - INFO - Request Parameters - Page 3:
2025-06-12 12:00:17,040 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:17,040 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:17,634 - INFO - Response - Page 3:
2025-06-12 12:00:17,837 - INFO - 第 3 页获取到 100 条记录
2025-06-12 12:00:17,837 - INFO - Request Parameters - Page 4:
2025-06-12 12:00:17,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:17,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:18,337 - INFO - Response - Page 4:
2025-06-12 12:00:18,540 - INFO - 第 4 页获取到 100 条记录
2025-06-12 12:00:18,540 - INFO - Request Parameters - Page 5:
2025-06-12 12:00:18,540 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:18,540 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:19,040 - INFO - Response - Page 5:
2025-06-12 12:00:19,244 - INFO - 第 5 页获取到 100 条记录
2025-06-12 12:00:19,244 - INFO - Request Parameters - Page 6:
2025-06-12 12:00:19,244 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:19,244 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:19,790 - INFO - Response - Page 6:
2025-06-12 12:00:19,994 - INFO - 第 6 页获取到 100 条记录
2025-06-12 12:00:19,994 - INFO - Request Parameters - Page 7:
2025-06-12 12:00:19,994 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:19,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:20,447 - INFO - Response - Page 7:
2025-06-12 12:00:20,650 - INFO - 第 7 页获取到 61 条记录
2025-06-12 12:00:20,650 - INFO - 查询完成，共获取到 661 条记录
2025-06-12 12:00:20,650 - INFO - 获取到 661 条表单数据
2025-06-12 12:00:20,650 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-12 12:00:20,665 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 12:00:20,665 - INFO - 开始处理日期: 2025-04
2025-06-12 12:00:20,665 - INFO - Request Parameters - Page 1:
2025-06-12 12:00:20,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:20,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:21,165 - INFO - Response - Page 1:
2025-06-12 12:00:21,368 - INFO - 第 1 页获取到 100 条记录
2025-06-12 12:00:21,368 - INFO - Request Parameters - Page 2:
2025-06-12 12:00:21,368 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:21,368 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:21,884 - INFO - Response - Page 2:
2025-06-12 12:00:22,087 - INFO - 第 2 页获取到 100 条记录
2025-06-12 12:00:22,087 - INFO - Request Parameters - Page 3:
2025-06-12 12:00:22,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:22,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:22,618 - INFO - Response - Page 3:
2025-06-12 12:00:22,822 - INFO - 第 3 页获取到 100 条记录
2025-06-12 12:00:22,822 - INFO - Request Parameters - Page 4:
2025-06-12 12:00:22,822 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:22,822 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:23,322 - INFO - Response - Page 4:
2025-06-12 12:00:23,525 - INFO - 第 4 页获取到 100 条记录
2025-06-12 12:00:23,525 - INFO - Request Parameters - Page 5:
2025-06-12 12:00:23,525 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:23,525 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:24,025 - INFO - Response - Page 5:
2025-06-12 12:00:24,228 - INFO - 第 5 页获取到 100 条记录
2025-06-12 12:00:24,228 - INFO - Request Parameters - Page 6:
2025-06-12 12:00:24,228 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:24,228 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:24,696 - INFO - Response - Page 6:
2025-06-12 12:00:24,900 - INFO - 第 6 页获取到 100 条记录
2025-06-12 12:00:24,900 - INFO - Request Parameters - Page 7:
2025-06-12 12:00:24,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:24,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:25,306 - INFO - Response - Page 7:
2025-06-12 12:00:25,509 - INFO - 第 7 页获取到 56 条记录
2025-06-12 12:00:25,509 - INFO - 查询完成，共获取到 656 条记录
2025-06-12 12:00:25,509 - INFO - 获取到 656 条表单数据
2025-06-12 12:00:25,509 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-12 12:00:25,525 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 12:00:25,525 - INFO - 开始处理日期: 2025-05
2025-06-12 12:00:25,525 - INFO - Request Parameters - Page 1:
2025-06-12 12:00:25,525 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:25,525 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:26,025 - INFO - Response - Page 1:
2025-06-12 12:00:26,228 - INFO - 第 1 页获取到 100 条记录
2025-06-12 12:00:26,228 - INFO - Request Parameters - Page 2:
2025-06-12 12:00:26,228 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:26,228 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:26,790 - INFO - Response - Page 2:
2025-06-12 12:00:26,993 - INFO - 第 2 页获取到 100 条记录
2025-06-12 12:00:26,993 - INFO - Request Parameters - Page 3:
2025-06-12 12:00:26,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:26,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:27,556 - INFO - Response - Page 3:
2025-06-12 12:00:27,759 - INFO - 第 3 页获取到 100 条记录
2025-06-12 12:00:27,759 - INFO - Request Parameters - Page 4:
2025-06-12 12:00:27,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:27,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:28,259 - INFO - Response - Page 4:
2025-06-12 12:00:28,462 - INFO - 第 4 页获取到 100 条记录
2025-06-12 12:00:28,462 - INFO - Request Parameters - Page 5:
2025-06-12 12:00:28,462 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:28,462 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:28,978 - INFO - Response - Page 5:
2025-06-12 12:00:29,181 - INFO - 第 5 页获取到 100 条记录
2025-06-12 12:00:29,181 - INFO - Request Parameters - Page 6:
2025-06-12 12:00:29,181 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:29,181 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:29,649 - INFO - Response - Page 6:
2025-06-12 12:00:29,853 - INFO - 第 6 页获取到 100 条记录
2025-06-12 12:00:29,853 - INFO - Request Parameters - Page 7:
2025-06-12 12:00:29,853 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:29,853 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:30,228 - INFO - Response - Page 7:
2025-06-12 12:00:30,431 - INFO - 第 7 页获取到 40 条记录
2025-06-12 12:00:30,431 - INFO - 查询完成，共获取到 640 条记录
2025-06-12 12:00:30,431 - INFO - 获取到 640 条表单数据
2025-06-12 12:00:30,431 - INFO - 当前日期 2025-05 有 640 条MySQL数据需要处理
2025-06-12 12:00:30,446 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 12:00:30,446 - INFO - 开始处理日期: 2025-06
2025-06-12 12:00:30,446 - INFO - Request Parameters - Page 1:
2025-06-12 12:00:30,446 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:30,446 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:30,962 - INFO - Response - Page 1:
2025-06-12 12:00:31,165 - INFO - 第 1 页获取到 100 条记录
2025-06-12 12:00:31,165 - INFO - Request Parameters - Page 2:
2025-06-12 12:00:31,165 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:31,165 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:31,712 - INFO - Response - Page 2:
2025-06-12 12:00:31,915 - INFO - 第 2 页获取到 100 条记录
2025-06-12 12:00:31,915 - INFO - Request Parameters - Page 3:
2025-06-12 12:00:31,915 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:31,915 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:32,431 - INFO - Response - Page 3:
2025-06-12 12:00:32,634 - INFO - 第 3 页获取到 100 条记录
2025-06-12 12:00:32,634 - INFO - Request Parameters - Page 4:
2025-06-12 12:00:32,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:32,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:33,134 - INFO - Response - Page 4:
2025-06-12 12:00:33,337 - INFO - 第 4 页获取到 100 条记录
2025-06-12 12:00:33,337 - INFO - Request Parameters - Page 5:
2025-06-12 12:00:33,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:33,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:33,884 - INFO - Response - Page 5:
2025-06-12 12:00:34,087 - INFO - 第 5 页获取到 100 条记录
2025-06-12 12:00:34,087 - INFO - Request Parameters - Page 6:
2025-06-12 12:00:34,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:34,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:34,602 - INFO - Response - Page 6:
2025-06-12 12:00:34,806 - INFO - 第 6 页获取到 100 条记录
2025-06-12 12:00:34,806 - INFO - Request Parameters - Page 7:
2025-06-12 12:00:34,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 12:00:34,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 12:00:35,118 - INFO - Response - Page 7:
2025-06-12 12:00:35,321 - INFO - 第 7 页获取到 21 条记录
2025-06-12 12:00:35,321 - INFO - 查询完成，共获取到 621 条记录
2025-06-12 12:00:35,321 - INFO - 获取到 621 条表单数据
2025-06-12 12:00:35,321 - INFO - 当前日期 2025-06 有 621 条MySQL数据需要处理
2025-06-12 12:00:35,321 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMGX
2025-06-12 12:00:35,837 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMGX
2025-06-12 12:00:35,837 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 99518.0, 'new_value': 101529.0}, {'field': 'total_amount', 'old_value': 99518.0, 'new_value': 101529.0}, {'field': 'order_count', 'old_value': 1488, 'new_value': 1523}]
2025-06-12 12:00:35,837 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMHX
2025-06-12 12:00:36,305 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMHX
2025-06-12 12:00:36,305 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 303114.0, 'new_value': 324086.0}, {'field': 'total_amount', 'old_value': 303114.0, 'new_value': 324086.0}, {'field': 'order_count', 'old_value': 2120, 'new_value': 2264}]
2025-06-12 12:00:36,305 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMJX
2025-06-12 12:00:36,805 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMJX
2025-06-12 12:00:36,805 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65333.01, 'new_value': 68186.68}, {'field': 'total_amount', 'old_value': 65333.01, 'new_value': 68186.68}, {'field': 'order_count', 'old_value': 1660, 'new_value': 1744}]
2025-06-12 12:00:36,805 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMLX
2025-06-12 12:00:37,321 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMLX
2025-06-12 12:00:37,321 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34246.6, 'new_value': 37860.44}, {'field': 'offline_amount', 'old_value': 347913.26, 'new_value': 390370.14}, {'field': 'total_amount', 'old_value': 378167.37, 'new_value': 424238.09}, {'field': 'order_count', 'old_value': 1833, 'new_value': 2042}]
2025-06-12 12:00:37,321 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMQX
2025-06-12 12:00:37,790 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMQX
2025-06-12 12:00:37,790 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 139880.0, 'new_value': 148560.0}, {'field': 'total_amount', 'old_value': 156380.0, 'new_value': 165060.0}, {'field': 'order_count', 'old_value': 101, 'new_value': 107}]
2025-06-12 12:00:37,790 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMSX
2025-06-12 12:00:38,321 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMSX
2025-06-12 12:00:38,321 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10663.85, 'new_value': 12017.85}, {'field': 'offline_amount', 'old_value': 5852.66, 'new_value': 6577.66}, {'field': 'total_amount', 'old_value': 16516.51, 'new_value': 18595.51}, {'field': 'order_count', 'old_value': 674, 'new_value': 757}]
2025-06-12 12:00:38,321 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMTX
2025-06-12 12:00:38,805 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMTX
2025-06-12 12:00:38,805 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 350075.0, 'new_value': 386661.0}, {'field': 'total_amount', 'old_value': 350075.0, 'new_value': 386661.0}, {'field': 'order_count', 'old_value': 726, 'new_value': 802}]
2025-06-12 12:00:38,805 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMUX
2025-06-12 12:00:39,259 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMUX
2025-06-12 12:00:39,259 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 184055.0, 'new_value': 202392.0}, {'field': 'offline_amount', 'old_value': 88575.0, 'new_value': 95178.0}, {'field': 'total_amount', 'old_value': 272630.0, 'new_value': 297570.0}, {'field': 'order_count', 'old_value': 319, 'new_value': 349}]
2025-06-12 12:00:39,259 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMWX
2025-06-12 12:00:39,727 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMWX
2025-06-12 12:00:39,727 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74900.0, 'new_value': 82480.0}, {'field': 'total_amount', 'old_value': 74900.0, 'new_value': 82480.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-06-12 12:00:39,727 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM5Y
2025-06-12 12:00:40,227 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM5Y
2025-06-12 12:00:40,227 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19432.99, 'new_value': 24206.76}, {'field': 'total_amount', 'old_value': 34560.51, 'new_value': 39334.28}, {'field': 'order_count', 'old_value': 1170, 'new_value': 1333}]
2025-06-12 12:00:40,227 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMCY
2025-06-12 12:00:40,633 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMCY
2025-06-12 12:00:40,633 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40176.48, 'new_value': 44357.14}, {'field': 'total_amount', 'old_value': 49273.96, 'new_value': 53454.62}, {'field': 'order_count', 'old_value': 1247, 'new_value': 1356}]
2025-06-12 12:00:40,633 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMEY
2025-06-12 12:00:41,118 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMEY
2025-06-12 12:00:41,118 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10111.0, 'new_value': 11205.0}, {'field': 'total_amount', 'old_value': 20711.0, 'new_value': 21805.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 6}]
2025-06-12 12:00:41,118 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJY
2025-06-12 12:00:41,587 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJY
2025-06-12 12:00:41,587 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18539.98, 'new_value': 19775.04}, {'field': 'offline_amount', 'old_value': 42058.12, 'new_value': 47214.12}, {'field': 'total_amount', 'old_value': 60598.1, 'new_value': 66989.16}, {'field': 'order_count', 'old_value': 690, 'new_value': 764}]
2025-06-12 12:00:41,587 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMKY
2025-06-12 12:00:42,055 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMKY
2025-06-12 12:00:42,055 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1348.38, 'new_value': 1475.87}, {'field': 'offline_amount', 'old_value': 31510.56, 'new_value': 34612.28}, {'field': 'total_amount', 'old_value': 32858.94, 'new_value': 36088.15}, {'field': 'order_count', 'old_value': 1298, 'new_value': 1433}]
2025-06-12 12:00:42,055 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLY
2025-06-12 12:00:42,602 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLY
2025-06-12 12:00:42,602 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24999.7, 'new_value': 28576.0}, {'field': 'total_amount', 'old_value': 24999.7, 'new_value': 28576.0}, {'field': 'order_count', 'old_value': 266, 'new_value': 281}]
2025-06-12 12:00:42,602 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPY
2025-06-12 12:00:43,055 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPY
2025-06-12 12:00:43,055 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22742.0, 'new_value': 25245.0}, {'field': 'total_amount', 'old_value': 22742.0, 'new_value': 25245.0}, {'field': 'order_count', 'old_value': 434, 'new_value': 485}]
2025-06-12 12:00:43,055 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRY
2025-06-12 12:00:43,446 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRY
2025-06-12 12:00:43,446 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 61642.0, 'new_value': 66083.0}, {'field': 'offline_amount', 'old_value': 21463.76, 'new_value': 24013.1}, {'field': 'total_amount', 'old_value': 83105.76, 'new_value': 90096.1}, {'field': 'order_count', 'old_value': 559, 'new_value': 609}]
2025-06-12 12:00:43,446 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMSY
2025-06-12 12:00:43,961 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMSY
2025-06-12 12:00:43,961 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3993.0, 'new_value': 4993.0}, {'field': 'total_amount', 'old_value': 4561.2, 'new_value': 5561.2}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-06-12 12:00:43,961 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMUY
2025-06-12 12:00:44,383 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMUY
2025-06-12 12:00:44,383 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24605.52, 'new_value': 27008.98}, {'field': 'total_amount', 'old_value': 24605.52, 'new_value': 27008.98}, {'field': 'order_count', 'old_value': 1103, 'new_value': 1210}]
2025-06-12 12:00:44,383 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMVY
2025-06-12 12:00:44,805 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMVY
2025-06-12 12:00:44,805 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 102874.0, 'new_value': 110135.0}, {'field': 'total_amount', 'old_value': 102874.0, 'new_value': 110135.0}, {'field': 'order_count', 'old_value': 509, 'new_value': 562}]
2025-06-12 12:00:44,805 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYY
2025-06-12 12:00:45,290 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYY
2025-06-12 12:00:45,290 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39263.84, 'new_value': 43286.77}, {'field': 'total_amount', 'old_value': 39263.84, 'new_value': 43286.77}, {'field': 'order_count', 'old_value': 79, 'new_value': 84}]
2025-06-12 12:00:45,290 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZY
2025-06-12 12:00:45,758 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZY
2025-06-12 12:00:45,758 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79656.53, 'new_value': 86346.82}, {'field': 'total_amount', 'old_value': 79656.53, 'new_value': 86346.82}, {'field': 'order_count', 'old_value': 465, 'new_value': 510}]
2025-06-12 12:00:45,758 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Z
2025-06-12 12:00:46,305 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Z
2025-06-12 12:00:46,305 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 272490.39, 'new_value': 301096.22}, {'field': 'total_amount', 'old_value': 272490.39, 'new_value': 301096.22}, {'field': 'order_count', 'old_value': 2036, 'new_value': 2236}]
2025-06-12 12:00:46,305 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM6Z
2025-06-12 12:00:46,774 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM6Z
2025-06-12 12:00:46,774 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 330002.0, 'new_value': 343054.0}, {'field': 'total_amount', 'old_value': 330002.0, 'new_value': 343054.0}, {'field': 'order_count', 'old_value': 40, 'new_value': 43}]
2025-06-12 12:00:46,774 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM8Z
2025-06-12 12:00:47,290 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM8Z
2025-06-12 12:00:47,290 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3729.0, 'new_value': 4153.0}, {'field': 'offline_amount', 'old_value': 6928.0, 'new_value': 7996.0}, {'field': 'total_amount', 'old_value': 10657.0, 'new_value': 12149.0}, {'field': 'order_count', 'old_value': 36, 'new_value': 41}]
2025-06-12 12:00:47,290 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAZ
2025-06-12 12:00:47,743 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAZ
2025-06-12 12:00:47,743 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70906.0, 'new_value': 77998.0}, {'field': 'total_amount', 'old_value': 70906.0, 'new_value': 77998.0}, {'field': 'order_count', 'old_value': 117, 'new_value': 141}]
2025-06-12 12:00:47,743 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMBZ
2025-06-12 12:00:48,196 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMBZ
2025-06-12 12:00:48,196 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4181.54, 'new_value': 4948.05}, {'field': 'offline_amount', 'old_value': 63177.27, 'new_value': 69358.4}, {'field': 'total_amount', 'old_value': 67358.81, 'new_value': 74306.45}, {'field': 'order_count', 'old_value': 740, 'new_value': 821}]
2025-06-12 12:00:48,196 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMEZ
2025-06-12 12:00:48,664 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMEZ
2025-06-12 12:00:48,664 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87175.51, 'new_value': 88357.51}, {'field': 'total_amount', 'old_value': 98350.22, 'new_value': 99532.22}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-06-12 12:00:48,664 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMFZ
2025-06-12 12:00:49,164 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMFZ
2025-06-12 12:00:49,164 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5347.57, 'new_value': 5892.15}, {'field': 'offline_amount', 'old_value': 38032.85, 'new_value': 41651.43}, {'field': 'total_amount', 'old_value': 43380.42, 'new_value': 47543.58}, {'field': 'order_count', 'old_value': 774, 'new_value': 863}]
2025-06-12 12:00:49,164 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMHZ
2025-06-12 12:00:49,602 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMHZ
2025-06-12 12:00:49,602 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3597.68, 'new_value': 4088.09}, {'field': 'offline_amount', 'old_value': 12711.99, 'new_value': 13782.19}, {'field': 'total_amount', 'old_value': 16309.67, 'new_value': 17870.28}, {'field': 'order_count', 'old_value': 354, 'new_value': 391}]
2025-06-12 12:00:49,602 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMKZ
2025-06-12 12:00:50,039 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMKZ
2025-06-12 12:00:50,039 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55257.02, 'new_value': 57872.02}, {'field': 'total_amount', 'old_value': 55257.02, 'new_value': 57872.02}, {'field': 'order_count', 'old_value': 259, 'new_value': 273}]
2025-06-12 12:00:50,039 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMMZ
2025-06-12 12:00:50,430 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMMZ
2025-06-12 12:00:50,430 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 63962.01, 'new_value': 70539.82}, {'field': 'offline_amount', 'old_value': 10260.15, 'new_value': 10929.15}, {'field': 'total_amount', 'old_value': 74222.16, 'new_value': 81468.97}, {'field': 'order_count', 'old_value': 2296, 'new_value': 2480}]
2025-06-12 12:00:50,430 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMSZ
2025-06-12 12:00:50,977 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMSZ
2025-06-12 12:00:50,977 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3404.0, 'new_value': 8604.0}, {'field': 'total_amount', 'old_value': 46587.0, 'new_value': 51787.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-06-12 12:00:50,977 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMUZ
2025-06-12 12:00:51,446 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMUZ
2025-06-12 12:00:51,446 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1000.0, 'new_value': 1039.0}, {'field': 'total_amount', 'old_value': 1493.0, 'new_value': 1532.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-06-12 12:00:51,446 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMVZ
2025-06-12 12:00:51,992 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMVZ
2025-06-12 12:00:51,992 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14664.83, 'new_value': 15750.39}, {'field': 'offline_amount', 'old_value': 21977.76, 'new_value': 23471.48}, {'field': 'total_amount', 'old_value': 36642.59, 'new_value': 39221.87}, {'field': 'order_count', 'old_value': 1843, 'new_value': 1986}]
2025-06-12 12:00:51,992 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMWZ
2025-06-12 12:00:52,586 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMWZ
2025-06-12 12:00:52,586 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3750.0, 'new_value': 4750.0}, {'field': 'offline_amount', 'old_value': 2961.0, 'new_value': 3061.0}, {'field': 'total_amount', 'old_value': 6711.0, 'new_value': 7811.0}, {'field': 'order_count', 'old_value': 43, 'new_value': 49}]
2025-06-12 12:00:52,586 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYZ
2025-06-12 12:00:53,086 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYZ
2025-06-12 12:00:53,086 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22044.0, 'new_value': 26112.0}, {'field': 'offline_amount', 'old_value': 80684.0, 'new_value': 81262.0}, {'field': 'total_amount', 'old_value': 102728.0, 'new_value': 107374.0}, {'field': 'order_count', 'old_value': 73, 'new_value': 80}]
2025-06-12 12:00:53,086 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZZ
2025-06-12 12:00:53,555 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZZ
2025-06-12 12:00:53,555 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6249.69, 'new_value': 6739.05}, {'field': 'offline_amount', 'old_value': 140637.04, 'new_value': 152835.79}, {'field': 'total_amount', 'old_value': 146886.73, 'new_value': 159574.84}, {'field': 'order_count', 'old_value': 714, 'new_value': 795}]
2025-06-12 12:00:53,555 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM101
2025-06-12 12:00:54,039 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM101
2025-06-12 12:00:54,039 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63665.51, 'new_value': 67519.51}, {'field': 'total_amount', 'old_value': 63665.51, 'new_value': 67519.51}, {'field': 'order_count', 'old_value': 841, 'new_value': 905}]
2025-06-12 12:00:54,039 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM201
2025-06-12 12:00:54,524 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM201
2025-06-12 12:00:54,524 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8964.3, 'new_value': 10306.13}, {'field': 'offline_amount', 'old_value': 14617.12, 'new_value': 16187.64}, {'field': 'total_amount', 'old_value': 23581.42, 'new_value': 26493.77}, {'field': 'order_count', 'old_value': 1091, 'new_value': 1232}]
2025-06-12 12:00:54,524 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM601
2025-06-12 12:00:54,946 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM601
2025-06-12 12:00:54,946 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47944.0, 'new_value': 54544.0}, {'field': 'total_amount', 'old_value': 47944.0, 'new_value': 54544.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 9}]
2025-06-12 12:00:54,946 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBB
2025-06-12 12:00:55,430 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBB
2025-06-12 12:00:55,430 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63420.0, 'new_value': 70374.0}, {'field': 'total_amount', 'old_value': 63420.0, 'new_value': 70374.0}, {'field': 'order_count', 'old_value': 1618, 'new_value': 1810}]
2025-06-12 12:00:55,430 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCB
2025-06-12 12:00:55,852 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCB
2025-06-12 12:00:55,852 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32413.0, 'new_value': 34373.0}, {'field': 'total_amount', 'old_value': 32413.0, 'new_value': 34373.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 20}]
2025-06-12 12:00:55,852 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMEB
2025-06-12 12:00:56,274 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMEB
2025-06-12 12:00:56,274 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 126142.05, 'new_value': 142986.28}, {'field': 'total_amount', 'old_value': 126865.05, 'new_value': 143709.28}, {'field': 'order_count', 'old_value': 1527, 'new_value': 1736}]
2025-06-12 12:00:56,274 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMFB
2025-06-12 12:00:56,680 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMFB
2025-06-12 12:00:56,680 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20640.92, 'new_value': 24076.54}, {'field': 'offline_amount', 'old_value': 186939.25, 'new_value': 202344.71}, {'field': 'total_amount', 'old_value': 207580.17, 'new_value': 226421.25}, {'field': 'order_count', 'old_value': 648, 'new_value': 711}]
2025-06-12 12:00:56,680 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGB
2025-06-12 12:00:57,133 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGB
2025-06-12 12:00:57,133 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52569.31, 'new_value': 58296.14}, {'field': 'total_amount', 'old_value': 52569.31, 'new_value': 58296.14}, {'field': 'order_count', 'old_value': 664, 'new_value': 737}]
2025-06-12 12:00:57,133 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHB
2025-06-12 12:00:57,602 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHB
2025-06-12 12:00:57,602 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1832.0, 'new_value': 2030.0}, {'field': 'total_amount', 'old_value': 1832.0, 'new_value': 2030.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 14}]
2025-06-12 12:00:57,602 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLB
2025-06-12 12:00:58,039 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLB
2025-06-12 12:00:58,039 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22039.0, 'new_value': 24499.0}, {'field': 'total_amount', 'old_value': 22039.0, 'new_value': 24499.0}, {'field': 'order_count', 'old_value': 1074, 'new_value': 1191}]
2025-06-12 12:00:58,039 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSB
2025-06-12 12:00:58,508 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSB
2025-06-12 12:00:58,508 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12458.0, 'new_value': 17486.0}, {'field': 'total_amount', 'old_value': 12458.0, 'new_value': 17486.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-12 12:00:58,508 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWB
2025-06-12 12:00:58,961 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWB
2025-06-12 12:00:58,961 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3776.0, 'new_value': 4075.0}, {'field': 'total_amount', 'old_value': 3776.0, 'new_value': 4075.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-06-12 12:00:58,961 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXB
2025-06-12 12:00:59,414 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXB
2025-06-12 12:00:59,414 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8315.76, 'new_value': 9102.76}, {'field': 'total_amount', 'old_value': 8315.76, 'new_value': 9102.76}, {'field': 'order_count', 'old_value': 383, 'new_value': 420}]
2025-06-12 12:00:59,414 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYB
2025-06-12 12:00:59,898 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYB
2025-06-12 12:00:59,898 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9945.4, 'new_value': 10760.4}, {'field': 'total_amount', 'old_value': 9945.4, 'new_value': 10760.4}, {'field': 'order_count', 'old_value': 52, 'new_value': 57}]
2025-06-12 12:00:59,898 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMZB
2025-06-12 12:01:00,367 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMZB
2025-06-12 12:01:00,367 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23195.0, 'new_value': 27194.0}, {'field': 'total_amount', 'old_value': 23195.0, 'new_value': 27194.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-06-12 12:01:00,367 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1C
2025-06-12 12:01:00,836 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1C
2025-06-12 12:01:00,836 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 257446.39, 'new_value': 278339.35}, {'field': 'total_amount', 'old_value': 257446.39, 'new_value': 278339.35}, {'field': 'order_count', 'old_value': 1893, 'new_value': 2090}]
2025-06-12 12:01:00,836 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM4C
2025-06-12 12:01:01,289 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM4C
2025-06-12 12:01:01,289 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10200.0, 'new_value': 22200.0}, {'field': 'total_amount', 'old_value': 10200.0, 'new_value': 22200.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-06-12 12:01:01,289 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5C
2025-06-12 12:01:01,711 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5C
2025-06-12 12:01:01,711 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39673.34, 'new_value': 43992.94}, {'field': 'offline_amount', 'old_value': 229759.45, 'new_value': 257880.93}, {'field': 'total_amount', 'old_value': 269432.79, 'new_value': 301873.87}, {'field': 'order_count', 'old_value': 677, 'new_value': 761}]
2025-06-12 12:01:01,711 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6C
2025-06-12 12:01:02,289 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6C
2025-06-12 12:01:02,289 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14489.0, 'new_value': 18989.0}, {'field': 'total_amount', 'old_value': 14489.0, 'new_value': 18989.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 29}]
2025-06-12 12:01:02,289 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM8C
2025-06-12 12:01:02,805 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM8C
2025-06-12 12:01:02,805 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51849.99, 'new_value': 58016.19}, {'field': 'total_amount', 'old_value': 51849.99, 'new_value': 58016.19}, {'field': 'order_count', 'old_value': 71, 'new_value': 81}]
2025-06-12 12:01:02,805 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAC
2025-06-12 12:01:03,461 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAC
2025-06-12 12:01:03,461 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52752.54, 'new_value': 60134.78}, {'field': 'offline_amount', 'old_value': 37287.71, 'new_value': 42239.04}, {'field': 'total_amount', 'old_value': 90040.25, 'new_value': 102373.82}, {'field': 'order_count', 'old_value': 3618, 'new_value': 4134}]
2025-06-12 12:01:03,461 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDC
2025-06-12 12:01:03,898 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDC
2025-06-12 12:01:03,898 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6313.5, 'new_value': 6677.2}, {'field': 'offline_amount', 'old_value': 16672.9, 'new_value': 16677.9}, {'field': 'total_amount', 'old_value': 22986.4, 'new_value': 23355.1}, {'field': 'order_count', 'old_value': 73, 'new_value': 78}]
2025-06-12 12:01:03,898 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHC
2025-06-12 12:01:04,351 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHC
2025-06-12 12:01:04,351 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8562.0, 'new_value': 10900.0}, {'field': 'total_amount', 'old_value': 8562.0, 'new_value': 10900.0}, {'field': 'order_count', 'old_value': 37, 'new_value': 42}]
2025-06-12 12:01:04,351 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJC
2025-06-12 12:01:04,805 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJC
2025-06-12 12:01:04,805 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 93671.4, 'new_value': 97262.3}, {'field': 'offline_amount', 'old_value': 46552.0, 'new_value': 49875.0}, {'field': 'total_amount', 'old_value': 140223.4, 'new_value': 147137.3}, {'field': 'order_count', 'old_value': 582, 'new_value': 629}]
2025-06-12 12:01:04,805 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKC
2025-06-12 12:01:05,258 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKC
2025-06-12 12:01:05,258 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26013.0, 'new_value': 33974.0}, {'field': 'offline_amount', 'old_value': 175610.0, 'new_value': 181143.0}, {'field': 'total_amount', 'old_value': 201623.0, 'new_value': 215117.0}, {'field': 'order_count', 'old_value': 1564, 'new_value': 1636}]
2025-06-12 12:01:05,258 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSC
2025-06-12 12:01:05,773 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSC
2025-06-12 12:01:05,773 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5536.74, 'new_value': 5680.04}, {'field': 'total_amount', 'old_value': 9691.64, 'new_value': 9834.94}, {'field': 'order_count', 'old_value': 43, 'new_value': 45}]
2025-06-12 12:01:05,773 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTC
2025-06-12 12:01:06,211 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTC
2025-06-12 12:01:06,211 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 210311.99, 'new_value': 228034.99}, {'field': 'total_amount', 'old_value': 210311.99, 'new_value': 228034.99}, {'field': 'order_count', 'old_value': 2407, 'new_value': 2649}]
2025-06-12 12:01:06,211 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM3D
2025-06-12 12:01:06,664 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM3D
2025-06-12 12:01:06,664 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8620.27, 'new_value': 9480.83}, {'field': 'offline_amount', 'old_value': 18609.28, 'new_value': 20387.18}, {'field': 'total_amount', 'old_value': 27229.55, 'new_value': 29868.01}, {'field': 'order_count', 'old_value': 993, 'new_value': 1094}]
2025-06-12 12:01:06,664 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6D
2025-06-12 12:01:07,101 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6D
2025-06-12 12:01:07,101 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46289.9, 'new_value': 51135.9}, {'field': 'total_amount', 'old_value': 56204.2, 'new_value': 61050.2}, {'field': 'order_count', 'old_value': 64, 'new_value': 68}]
2025-06-12 12:01:07,101 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9D
2025-06-12 12:01:07,664 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9D
2025-06-12 12:01:07,664 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13336.9, 'new_value': 13815.9}, {'field': 'total_amount', 'old_value': 13336.9, 'new_value': 13815.9}, {'field': 'order_count', 'old_value': 28, 'new_value': 29}]
2025-06-12 12:01:07,664 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMID
2025-06-12 12:01:08,164 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMID
2025-06-12 12:01:08,164 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7987.84, 'new_value': 8487.4}, {'field': 'offline_amount', 'old_value': 110031.13, 'new_value': 118336.53}, {'field': 'total_amount', 'old_value': 118018.97, 'new_value': 126823.93}, {'field': 'order_count', 'old_value': 775, 'new_value': 832}]
2025-06-12 12:01:08,164 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMD
2025-06-12 12:01:08,726 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMD
2025-06-12 12:01:08,726 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12930.0, 'new_value': 17133.0}, {'field': 'total_amount', 'old_value': 12930.0, 'new_value': 17133.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-06-12 12:01:08,726 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMND
2025-06-12 12:01:09,164 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMND
2025-06-12 12:01:09,164 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 116035.56, 'new_value': 128821.04}, {'field': 'total_amount', 'old_value': 116035.56, 'new_value': 128821.04}, {'field': 'order_count', 'old_value': 3462, 'new_value': 3856}]
2025-06-12 12:01:09,164 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOD
2025-06-12 12:01:09,601 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOD
2025-06-12 12:01:09,601 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33035.7, 'new_value': 36798.0}, {'field': 'total_amount', 'old_value': 33035.7, 'new_value': 36798.0}, {'field': 'order_count', 'old_value': 124, 'new_value': 136}]
2025-06-12 12:01:09,601 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRD
2025-06-12 12:01:10,070 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRD
2025-06-12 12:01:10,070 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5080.0, 'new_value': 5290.0}, {'field': 'total_amount', 'old_value': 5080.0, 'new_value': 5290.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 24}]
2025-06-12 12:01:10,070 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMVD
2025-06-12 12:01:10,554 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMVD
2025-06-12 12:01:10,554 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 92540.0, 'new_value': 111950.0}, {'field': 'offline_amount', 'old_value': 79675.0, 'new_value': 114675.0}, {'field': 'total_amount', 'old_value': 172215.0, 'new_value': 226625.0}, {'field': 'order_count', 'old_value': 94, 'new_value': 117}]
2025-06-12 12:01:10,554 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWD
2025-06-12 12:01:11,008 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWD
2025-06-12 12:01:11,023 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 756.84, 'new_value': 838.13}, {'field': 'offline_amount', 'old_value': 10801.74, 'new_value': 11645.94}, {'field': 'total_amount', 'old_value': 11558.58, 'new_value': 12484.07}, {'field': 'order_count', 'old_value': 546, 'new_value': 588}]
2025-06-12 12:01:11,023 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMZD
2025-06-12 12:01:11,476 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMZD
2025-06-12 12:01:11,476 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27585.0, 'new_value': 31095.0}, {'field': 'total_amount', 'old_value': 27585.0, 'new_value': 31095.0}, {'field': 'order_count', 'old_value': 187, 'new_value': 211}]
2025-06-12 12:01:11,476 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTQ
2025-06-12 12:01:11,945 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTQ
2025-06-12 12:01:11,945 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33130.98, 'new_value': 34925.81}, {'field': 'total_amount', 'old_value': 33130.98, 'new_value': 34925.81}, {'field': 'order_count', 'old_value': 1060, 'new_value': 1118}]
2025-06-12 12:01:11,945 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUQ
2025-06-12 12:01:12,429 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUQ
2025-06-12 12:01:12,429 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21132.0, 'new_value': 22548.2}, {'field': 'total_amount', 'old_value': 21132.0, 'new_value': 22548.2}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-06-12 12:01:12,429 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZQ
2025-06-12 12:01:12,851 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZQ
2025-06-12 12:01:12,851 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19948.0, 'new_value': 22218.0}, {'field': 'total_amount', 'old_value': 19948.0, 'new_value': 22218.0}, {'field': 'order_count', 'old_value': 50, 'new_value': 55}]
2025-06-12 12:01:12,851 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1R
2025-06-12 12:01:13,382 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1R
2025-06-12 12:01:13,382 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14896.4, 'new_value': 16006.2}, {'field': 'offline_amount', 'old_value': 87800.2, 'new_value': 92006.8}, {'field': 'total_amount', 'old_value': 102696.6, 'new_value': 108013.0}, {'field': 'order_count', 'old_value': 844, 'new_value': 903}]
2025-06-12 12:01:13,382 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4R
2025-06-12 12:01:13,789 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4R
2025-06-12 12:01:13,789 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12794.7, 'new_value': 13548.9}, {'field': 'total_amount', 'old_value': 12794.7, 'new_value': 13548.9}, {'field': 'order_count', 'old_value': 77, 'new_value': 83}]
2025-06-12 12:01:13,789 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9R
2025-06-12 12:01:14,257 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9R
2025-06-12 12:01:14,257 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18518.85, 'new_value': 20335.29}, {'field': 'offline_amount', 'old_value': 96234.39, 'new_value': 106519.93}, {'field': 'total_amount', 'old_value': 114753.24, 'new_value': 126855.22}, {'field': 'order_count', 'old_value': 2058, 'new_value': 2297}]
2025-06-12 12:01:14,257 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBR
2025-06-12 12:01:14,711 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBR
2025-06-12 12:01:14,711 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66338.0, 'new_value': 72414.0}, {'field': 'total_amount', 'old_value': 66338.0, 'new_value': 72414.0}, {'field': 'order_count', 'old_value': 255, 'new_value': 275}]
2025-06-12 12:01:14,711 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCR
2025-06-12 12:01:15,304 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCR
2025-06-12 12:01:15,304 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17420.9, 'new_value': 18484.9}, {'field': 'offline_amount', 'old_value': 14188.7, 'new_value': 15155.7}, {'field': 'total_amount', 'old_value': 31609.6, 'new_value': 33640.6}, {'field': 'order_count', 'old_value': 2233, 'new_value': 2441}]
2025-06-12 12:01:15,304 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMER
2025-06-12 12:01:15,914 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMER
2025-06-12 12:01:15,914 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36141.86, 'new_value': 36596.02}, {'field': 'total_amount', 'old_value': 36141.86, 'new_value': 36596.02}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-06-12 12:01:15,914 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHR
2025-06-12 12:01:16,429 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHR
2025-06-12 12:01:16,429 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8927.6, 'new_value': 12144.7}, {'field': 'offline_amount', 'old_value': 20385.27, 'new_value': 24221.75}, {'field': 'total_amount', 'old_value': 29312.87, 'new_value': 36366.45}, {'field': 'order_count', 'old_value': 291, 'new_value': 351}]
2025-06-12 12:01:16,429 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKR
2025-06-12 12:01:16,929 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKR
2025-06-12 12:01:16,929 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90399.72, 'new_value': 98934.72}, {'field': 'total_amount', 'old_value': 90399.72, 'new_value': 98934.72}, {'field': 'order_count', 'old_value': 508, 'new_value': 554}]
2025-06-12 12:01:16,929 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMMR
2025-06-12 12:01:17,429 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMMR
2025-06-12 12:01:17,429 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 118210.0, 'new_value': 134090.0}, {'field': 'total_amount', 'old_value': 118210.0, 'new_value': 134090.0}, {'field': 'order_count', 'old_value': 50, 'new_value': 53}]
2025-06-12 12:01:17,429 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNR
2025-06-12 12:01:17,992 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNR
2025-06-12 12:01:17,992 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42978.66, 'new_value': 46244.88}, {'field': 'offline_amount', 'old_value': 22793.13, 'new_value': 23867.94}, {'field': 'total_amount', 'old_value': 65771.79, 'new_value': 70112.82}, {'field': 'order_count', 'old_value': 3936, 'new_value': 4196}]
2025-06-12 12:01:17,992 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMOR
2025-06-12 12:01:18,460 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMOR
2025-06-12 12:01:18,460 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51323.3, 'new_value': 52649.2}, {'field': 'total_amount', 'old_value': 51323.3, 'new_value': 52649.2}, {'field': 'order_count', 'old_value': 125, 'new_value': 130}]
2025-06-12 12:01:18,460 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQR
2025-06-12 12:01:18,976 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQR
2025-06-12 12:01:18,976 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24200.4, 'new_value': 24288.4}, {'field': 'total_amount', 'old_value': 24200.4, 'new_value': 24288.4}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-06-12 12:01:18,976 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUR
2025-06-12 12:01:19,476 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUR
2025-06-12 12:01:19,476 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22356.92, 'new_value': 24661.73}, {'field': 'offline_amount', 'old_value': 36875.94, 'new_value': 38911.47}, {'field': 'total_amount', 'old_value': 59232.86, 'new_value': 63573.2}, {'field': 'order_count', 'old_value': 2194, 'new_value': 2384}]
2025-06-12 12:01:19,476 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVR
2025-06-12 12:01:19,960 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVR
2025-06-12 12:01:19,960 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31364.42, 'new_value': 35967.72}, {'field': 'offline_amount', 'old_value': 72088.46, 'new_value': 79297.35}, {'field': 'total_amount', 'old_value': 103452.88, 'new_value': 115265.07}, {'field': 'order_count', 'old_value': 2542, 'new_value': 2885}]
2025-06-12 12:01:19,960 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMXR
2025-06-12 12:01:20,523 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMXR
2025-06-12 12:01:20,523 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15695.89, 'new_value': 19321.77}, {'field': 'offline_amount', 'old_value': 52134.84, 'new_value': 54447.24}, {'field': 'total_amount', 'old_value': 67830.73, 'new_value': 73769.01}, {'field': 'order_count', 'old_value': 3519, 'new_value': 3809}]
2025-06-12 12:01:20,523 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYR
2025-06-12 12:01:21,007 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYR
2025-06-12 12:01:21,007 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41548.49, 'new_value': 43489.54}, {'field': 'total_amount', 'old_value': 41548.49, 'new_value': 43489.54}, {'field': 'order_count', 'old_value': 1507, 'new_value': 1585}]
2025-06-12 12:01:21,007 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZR
2025-06-12 12:01:21,523 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZR
2025-06-12 12:01:21,523 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 161709.59, 'new_value': 181542.09}, {'field': 'total_amount', 'old_value': 161709.59, 'new_value': 181542.09}, {'field': 'order_count', 'old_value': 1071, 'new_value': 1126}]
2025-06-12 12:01:21,523 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0S
2025-06-12 12:01:22,007 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0S
2025-06-12 12:01:22,007 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8440.6, 'new_value': 8558.6}, {'field': 'total_amount', 'old_value': 8440.6, 'new_value': 8558.6}, {'field': 'order_count', 'old_value': 43, 'new_value': 44}]
2025-06-12 12:01:22,007 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1S
2025-06-12 12:01:22,492 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1S
2025-06-12 12:01:22,492 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16451.46, 'new_value': 17968.48}, {'field': 'offline_amount', 'old_value': 10083.54, 'new_value': 10790.61}, {'field': 'total_amount', 'old_value': 26535.0, 'new_value': 28759.09}, {'field': 'order_count', 'old_value': 1095, 'new_value': 1199}]
2025-06-12 12:01:22,492 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3S
2025-06-12 12:01:22,866 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3S
2025-06-12 12:01:22,866 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7541.69, 'new_value': 7971.14}, {'field': 'offline_amount', 'old_value': 5483.89, 'new_value': 5622.69}, {'field': 'total_amount', 'old_value': 13025.58, 'new_value': 13593.83}, {'field': 'order_count', 'old_value': 1010, 'new_value': 1058}]
2025-06-12 12:01:22,866 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5S
2025-06-12 12:01:23,398 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5S
2025-06-12 12:01:23,398 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 121924.0, 'new_value': 131802.0}, {'field': 'total_amount', 'old_value': 156924.0, 'new_value': 166802.0}, {'field': 'order_count', 'old_value': 5275, 'new_value': 5380}]
2025-06-12 12:01:23,398 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7S
2025-06-12 12:01:23,882 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7S
2025-06-12 12:01:23,882 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 301492.24, 'new_value': 312676.47}, {'field': 'total_amount', 'old_value': 301492.24, 'new_value': 312676.47}, {'field': 'order_count', 'old_value': 5022, 'new_value': 5230}]
2025-06-12 12:01:23,882 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8S
2025-06-12 12:01:24,304 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8S
2025-06-12 12:01:24,304 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18383.13, 'new_value': 19683.64}, {'field': 'offline_amount', 'old_value': 127189.54, 'new_value': 133832.31}, {'field': 'total_amount', 'old_value': 145572.67, 'new_value': 153515.95}, {'field': 'order_count', 'old_value': 3216, 'new_value': 3372}]
2025-06-12 12:01:24,304 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIS
2025-06-12 12:01:24,773 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIS
2025-06-12 12:01:24,773 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 283510.0, 'new_value': 309696.0}, {'field': 'total_amount', 'old_value': 283510.0, 'new_value': 309696.0}, {'field': 'order_count', 'old_value': 897, 'new_value': 977}]
2025-06-12 12:01:24,773 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMJS
2025-06-12 12:01:25,273 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMJS
2025-06-12 12:01:25,273 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80121.0, 'new_value': 81121.0}, {'field': 'total_amount', 'old_value': 105191.0, 'new_value': 106191.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 22}]
2025-06-12 12:01:25,273 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMOS
2025-06-12 12:01:25,726 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMOS
2025-06-12 12:01:25,726 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17226.84, 'new_value': 18882.78}, {'field': 'offline_amount', 'old_value': 122327.97, 'new_value': 132833.74}, {'field': 'total_amount', 'old_value': 139554.81, 'new_value': 151716.52}, {'field': 'order_count', 'old_value': 4430, 'new_value': 4841}]
2025-06-12 12:01:25,726 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQS
2025-06-12 12:01:26,320 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQS
2025-06-12 12:01:26,320 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37705.9, 'new_value': 38830.9}, {'field': 'offline_amount', 'old_value': 134232.79, 'new_value': 144439.79}, {'field': 'total_amount', 'old_value': 171938.69, 'new_value': 183270.69}, {'field': 'order_count', 'old_value': 1835, 'new_value': 2022}]
2025-06-12 12:01:26,320 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMRS
2025-06-12 12:01:26,788 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMRS
2025-06-12 12:01:26,788 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4252.94, 'new_value': 4719.9}, {'field': 'offline_amount', 'old_value': 12995.55, 'new_value': 13873.75}, {'field': 'total_amount', 'old_value': 17248.49, 'new_value': 18593.65}, {'field': 'order_count', 'old_value': 604, 'new_value': 645}]
2025-06-12 12:01:26,788 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUS
2025-06-12 12:01:27,241 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUS
2025-06-12 12:01:27,241 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14448.74, 'new_value': 15448.74}, {'field': 'offline_amount', 'old_value': 15892.05, 'new_value': 17312.23}, {'field': 'total_amount', 'old_value': 30340.79, 'new_value': 32760.97}, {'field': 'order_count', 'old_value': 1392, 'new_value': 1498}]
2025-06-12 12:01:27,241 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWS
2025-06-12 12:01:27,726 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWS
2025-06-12 12:01:27,726 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32906.4, 'new_value': 36195.53}, {'field': 'offline_amount', 'old_value': 13913.97, 'new_value': 14322.97}, {'field': 'total_amount', 'old_value': 46820.37, 'new_value': 50518.5}, {'field': 'order_count', 'old_value': 2987, 'new_value': 3206}]
2025-06-12 12:01:27,726 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZS
2025-06-12 12:01:28,273 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZS
2025-06-12 12:01:28,273 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 201906.16, 'new_value': 217885.73}, {'field': 'total_amount', 'old_value': 201906.16, 'new_value': 217885.73}, {'field': 'order_count', 'old_value': 2858, 'new_value': 3109}]
2025-06-12 12:01:28,273 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4T
2025-06-12 12:01:28,788 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4T
2025-06-12 12:01:28,788 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8626.0, 'new_value': 8671.0}, {'field': 'total_amount', 'old_value': 8626.0, 'new_value': 8671.0}, {'field': 'order_count', 'old_value': 46, 'new_value': 47}]
2025-06-12 12:01:28,788 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5T
2025-06-12 12:01:29,366 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5T
2025-06-12 12:01:29,366 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59736.07, 'new_value': 63644.7}, {'field': 'offline_amount', 'old_value': 40233.0, 'new_value': 42945.0}, {'field': 'total_amount', 'old_value': 99969.07, 'new_value': 106589.7}, {'field': 'order_count', 'old_value': 1002, 'new_value': 1089}]
2025-06-12 12:01:29,366 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6T
2025-06-12 12:01:29,976 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6T
2025-06-12 12:01:29,976 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 220659.92, 'new_value': 243026.27}, {'field': 'total_amount', 'old_value': 220914.08, 'new_value': 243280.43}, {'field': 'order_count', 'old_value': 555, 'new_value': 624}]
2025-06-12 12:01:29,976 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8T
2025-06-12 12:01:30,444 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8T
2025-06-12 12:01:30,444 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 93535.72, 'new_value': 101667.24}, {'field': 'total_amount', 'old_value': 110522.74, 'new_value': 118654.26}, {'field': 'order_count', 'old_value': 4847, 'new_value': 5223}]
2025-06-12 12:01:30,444 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9T
2025-06-12 12:01:30,929 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9T
2025-06-12 12:01:30,929 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32264.0, 'new_value': 35360.13}, {'field': 'offline_amount', 'old_value': 32783.61, 'new_value': 34572.48}, {'field': 'total_amount', 'old_value': 65047.61, 'new_value': 69932.61}, {'field': 'order_count', 'old_value': 2923, 'new_value': 3234}]
2025-06-12 12:01:30,929 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMAT
2025-06-12 12:01:31,444 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMAT
2025-06-12 12:01:31,444 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 110024.5, 'new_value': 121327.5}, {'field': 'total_amount', 'old_value': 110024.5, 'new_value': 121327.5}, {'field': 'order_count', 'old_value': 3244, 'new_value': 3606}]
2025-06-12 12:01:31,444 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBT
2025-06-12 12:01:31,866 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBT
2025-06-12 12:01:31,866 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13971.0, 'new_value': 15013.0}, {'field': 'total_amount', 'old_value': 13971.0, 'new_value': 15013.0}, {'field': 'order_count', 'old_value': 79, 'new_value': 88}]
2025-06-12 12:01:31,866 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDT
2025-06-12 12:01:32,382 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDT
2025-06-12 12:01:32,382 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 93826.0, 'new_value': 96826.0}, {'field': 'total_amount', 'old_value': 93826.0, 'new_value': 96826.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-06-12 12:01:32,382 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGT
2025-06-12 12:01:32,835 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGT
2025-06-12 12:01:32,835 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32928.62, 'new_value': 37286.8}, {'field': 'offline_amount', 'old_value': 112983.35, 'new_value': 118695.49}, {'field': 'total_amount', 'old_value': 145911.97, 'new_value': 155982.29}, {'field': 'order_count', 'old_value': 1977, 'new_value': 2046}]
2025-06-12 12:01:32,835 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9K
2025-06-12 12:01:33,351 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9K
2025-06-12 12:01:33,351 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4526.27, 'new_value': 5190.1}, {'field': 'offline_amount', 'old_value': 13755.51, 'new_value': 14371.61}, {'field': 'total_amount', 'old_value': 18281.78, 'new_value': 19561.71}, {'field': 'order_count', 'old_value': 827, 'new_value': 901}]
2025-06-12 12:01:33,351 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEK
2025-06-12 12:01:33,819 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEK
2025-06-12 12:01:33,819 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7771.0, 'new_value': 8107.0}, {'field': 'total_amount', 'old_value': 7771.0, 'new_value': 8107.0}, {'field': 'order_count', 'old_value': 94, 'new_value': 98}]
2025-06-12 12:01:33,819 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFK
2025-06-12 12:01:34,304 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFK
2025-06-12 12:01:34,304 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35132.0, 'new_value': 38413.0}, {'field': 'offline_amount', 'old_value': 60374.0, 'new_value': 65844.0}, {'field': 'total_amount', 'old_value': 95506.0, 'new_value': 104257.0}, {'field': 'order_count', 'old_value': 2187, 'new_value': 2372}]
2025-06-12 12:01:34,304 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIK
2025-06-12 12:01:34,757 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIK
2025-06-12 12:01:34,757 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47559.36, 'new_value': 54751.53}, {'field': 'total_amount', 'old_value': 52874.55, 'new_value': 60066.72}, {'field': 'order_count', 'old_value': 2813, 'new_value': 3113}]
2025-06-12 12:01:34,757 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJK
2025-06-12 12:01:35,194 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJK
2025-06-12 12:01:35,194 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26306.3, 'new_value': 27847.9}, {'field': 'offline_amount', 'old_value': 77951.6, 'new_value': 79584.8}, {'field': 'total_amount', 'old_value': 104257.9, 'new_value': 107432.7}, {'field': 'order_count', 'old_value': 2097, 'new_value': 2160}]
2025-06-12 12:01:35,194 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKK
2025-06-12 12:01:35,647 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKK
2025-06-12 12:01:35,647 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25251.71, 'new_value': 27909.16}, {'field': 'offline_amount', 'old_value': 99247.48, 'new_value': 104902.63}, {'field': 'total_amount', 'old_value': 124499.19, 'new_value': 132811.79}, {'field': 'order_count', 'old_value': 2662, 'new_value': 2882}]
2025-06-12 12:01:35,647 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNK
2025-06-12 12:01:36,116 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNK
2025-06-12 12:01:36,116 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1579.34, 'new_value': 1763.84}, {'field': 'offline_amount', 'old_value': 154050.46, 'new_value': 162721.86}, {'field': 'total_amount', 'old_value': 155629.8, 'new_value': 164485.7}, {'field': 'order_count', 'old_value': 7696, 'new_value': 8166}]
2025-06-12 12:01:36,116 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPK
2025-06-12 12:01:36,710 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPK
2025-06-12 12:01:36,710 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24257.97, 'new_value': 26517.82}, {'field': 'offline_amount', 'old_value': 129186.97, 'new_value': 135894.3}, {'field': 'total_amount', 'old_value': 153444.94, 'new_value': 162412.12}, {'field': 'order_count', 'old_value': 1892, 'new_value': 2074}]
2025-06-12 12:01:36,710 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSK
2025-06-12 12:01:37,194 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSK
2025-06-12 12:01:37,194 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11379.0, 'new_value': 12132.0}, {'field': 'total_amount', 'old_value': 11379.0, 'new_value': 12132.0}, {'field': 'order_count', 'old_value': 46, 'new_value': 49}]
2025-06-12 12:01:37,194 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTK
2025-06-12 12:01:37,632 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTK
2025-06-12 12:01:37,632 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11223.0, 'new_value': 12473.0}, {'field': 'total_amount', 'old_value': 11223.0, 'new_value': 12473.0}, {'field': 'order_count', 'old_value': 107, 'new_value': 119}]
2025-06-12 12:01:37,632 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUK
2025-06-12 12:01:38,116 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUK
2025-06-12 12:01:38,116 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36031.13, 'new_value': 39548.45}, {'field': 'total_amount', 'old_value': 36031.13, 'new_value': 39548.45}, {'field': 'order_count', 'old_value': 1015, 'new_value': 1123}]
2025-06-12 12:01:38,116 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWK
2025-06-12 12:01:38,585 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWK
2025-06-12 12:01:38,585 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 272310.85, 'new_value': 289589.59}, {'field': 'total_amount', 'old_value': 272310.85, 'new_value': 289589.59}, {'field': 'order_count', 'old_value': 2913, 'new_value': 3158}]
2025-06-12 12:01:38,585 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXK
2025-06-12 12:01:39,022 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXK
2025-06-12 12:01:39,022 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11296.92, 'new_value': 12880.61}, {'field': 'offline_amount', 'old_value': 89072.1, 'new_value': 93482.9}, {'field': 'total_amount', 'old_value': 100369.02, 'new_value': 106363.51}, {'field': 'order_count', 'old_value': 3089, 'new_value': 3292}]
2025-06-12 12:01:39,022 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYK
2025-06-12 12:01:39,553 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYK
2025-06-12 12:01:39,553 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38566.0, 'new_value': 42081.0}, {'field': 'total_amount', 'old_value': 38566.0, 'new_value': 42081.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 28}]
2025-06-12 12:01:39,553 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9L
2025-06-12 12:01:39,991 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9L
2025-06-12 12:01:39,991 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45172.67, 'new_value': 48695.43}, {'field': 'offline_amount', 'old_value': 103062.5, 'new_value': 110042.05}, {'field': 'total_amount', 'old_value': 148235.17, 'new_value': 158737.48}, {'field': 'order_count', 'old_value': 5163, 'new_value': 5577}]
2025-06-12 12:01:39,991 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMAL
2025-06-12 12:01:40,460 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMAL
2025-06-12 12:01:40,460 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6228.23, 'new_value': 6689.73}, {'field': 'offline_amount', 'old_value': 52575.62, 'new_value': 55033.37}, {'field': 'total_amount', 'old_value': 58803.85, 'new_value': 61723.1}, {'field': 'order_count', 'old_value': 1702, 'new_value': 1802}]
2025-06-12 12:01:40,460 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBL
2025-06-12 12:01:40,882 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBL
2025-06-12 12:01:40,882 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15501.9, 'new_value': 17002.9}, {'field': 'total_amount', 'old_value': 15703.0, 'new_value': 17204.0}, {'field': 'order_count', 'old_value': 114, 'new_value': 123}]
2025-06-12 12:01:40,882 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCL
2025-06-12 12:01:41,444 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCL
2025-06-12 12:01:41,444 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 62316.14, 'new_value': 69330.44}, {'field': 'offline_amount', 'old_value': 163069.06, 'new_value': 178386.82}, {'field': 'total_amount', 'old_value': 225385.2, 'new_value': 247717.26}, {'field': 'order_count', 'old_value': 2002, 'new_value': 2215}]
2025-06-12 12:01:41,444 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFL
2025-06-12 12:01:41,960 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFL
2025-06-12 12:01:41,960 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61401.0, 'new_value': 66955.0}, {'field': 'total_amount', 'old_value': 61401.0, 'new_value': 66955.0}, {'field': 'order_count', 'old_value': 56, 'new_value': 57}]
2025-06-12 12:01:41,960 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGL
2025-06-12 12:01:42,381 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGL
2025-06-12 12:01:42,381 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46312.19, 'new_value': 46319.19}, {'field': 'offline_amount', 'old_value': 141119.68, 'new_value': 149421.68}, {'field': 'total_amount', 'old_value': 187431.87, 'new_value': 195740.87}, {'field': 'order_count', 'old_value': 1596, 'new_value': 1614}]
2025-06-12 12:01:42,381 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLL
2025-06-12 12:01:42,819 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLL
2025-06-12 12:01:42,819 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41140.0, 'new_value': 49570.0}, {'field': 'total_amount', 'old_value': 41140.0, 'new_value': 49570.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-06-12 12:01:42,819 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPL
2025-06-12 12:01:43,303 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPL
2025-06-12 12:01:43,303 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23798.0, 'new_value': 24086.0}, {'field': 'total_amount', 'old_value': 23798.0, 'new_value': 24086.0}, {'field': 'order_count', 'old_value': 58, 'new_value': 59}]
2025-06-12 12:01:43,303 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRL
2025-06-12 12:01:43,819 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRL
2025-06-12 12:01:43,819 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63252.74, 'new_value': 66846.98}, {'field': 'total_amount', 'old_value': 63252.74, 'new_value': 66846.98}, {'field': 'order_count', 'old_value': 2886, 'new_value': 3062}]
2025-06-12 12:01:43,819 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWL
2025-06-12 12:01:44,288 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWL
2025-06-12 12:01:44,303 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 467.9, 'new_value': 567.9}, {'field': 'offline_amount', 'old_value': 28884.6, 'new_value': 30386.5}, {'field': 'total_amount', 'old_value': 29352.5, 'new_value': 30954.4}, {'field': 'order_count', 'old_value': 203, 'new_value': 215}]
2025-06-12 12:01:44,303 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXL
2025-06-12 12:01:44,725 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXL
2025-06-12 12:01:44,725 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90205.08, 'new_value': 96264.79}, {'field': 'total_amount', 'old_value': 90205.08, 'new_value': 96264.79}, {'field': 'order_count', 'old_value': 665, 'new_value': 724}]
2025-06-12 12:01:44,725 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYL
2025-06-12 12:01:45,163 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYL
2025-06-12 12:01:45,163 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11415.0, 'new_value': 12530.0}, {'field': 'total_amount', 'old_value': 11415.0, 'new_value': 12530.0}, {'field': 'order_count', 'old_value': 95, 'new_value': 104}]
2025-06-12 12:01:45,163 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1M
2025-06-12 12:01:45,647 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1M
2025-06-12 12:01:45,647 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36134.51, 'new_value': 39319.0}, {'field': 'offline_amount', 'old_value': 356857.27, 'new_value': 389730.67}, {'field': 'total_amount', 'old_value': 392991.78, 'new_value': 429049.67}, {'field': 'order_count', 'old_value': 1625, 'new_value': 1736}]
2025-06-12 12:01:45,647 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM2M
2025-06-12 12:01:46,131 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM2M
2025-06-12 12:01:46,131 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 170295.02, 'new_value': 171838.52}, {'field': 'offline_amount', 'old_value': 131689.24, 'new_value': 133503.24}, {'field': 'total_amount', 'old_value': 301984.26, 'new_value': 305341.76}, {'field': 'order_count', 'old_value': 2059, 'new_value': 2075}]
2025-06-12 12:01:46,131 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM4M
2025-06-12 12:01:46,647 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM4M
2025-06-12 12:01:46,647 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 125346.69, 'new_value': 130672.89}, {'field': 'total_amount', 'old_value': 125346.69, 'new_value': 130672.89}, {'field': 'order_count', 'old_value': 2602, 'new_value': 2718}]
2025-06-12 12:01:46,647 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM6M
2025-06-12 12:01:47,131 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM6M
2025-06-12 12:01:47,131 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 558.0}, {'field': 'offline_amount', 'old_value': 9998.0, 'new_value': 10798.0}, {'field': 'total_amount', 'old_value': 9998.0, 'new_value': 11356.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 26}]
2025-06-12 12:01:47,131 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8M
2025-06-12 12:01:47,647 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8M
2025-06-12 12:01:47,647 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5435.0, 'new_value': 5630.0}, {'field': 'offline_amount', 'old_value': 1275.0, 'new_value': 1415.0}, {'field': 'total_amount', 'old_value': 6710.0, 'new_value': 7045.0}, {'field': 'order_count', 'old_value': 65, 'new_value': 67}]
2025-06-12 12:01:47,647 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9M
2025-06-12 12:01:48,100 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9M
2025-06-12 12:01:48,100 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 176567.94, 'new_value': 187142.16}, {'field': 'total_amount', 'old_value': 176567.94, 'new_value': 187142.16}, {'field': 'order_count', 'old_value': 7158, 'new_value': 7632}]
2025-06-12 12:01:48,100 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBM
2025-06-12 12:01:48,647 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBM
2025-06-12 12:01:48,647 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91184.0, 'new_value': 92745.0}, {'field': 'total_amount', 'old_value': 91184.0, 'new_value': 92745.0}, {'field': 'order_count', 'old_value': 2911, 'new_value': 2961}]
2025-06-12 12:01:48,647 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCM
2025-06-12 12:01:49,069 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCM
2025-06-12 12:01:49,069 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112737.92, 'new_value': 117055.42}, {'field': 'total_amount', 'old_value': 112737.92, 'new_value': 117055.42}, {'field': 'order_count', 'old_value': 636, 'new_value': 677}]
2025-06-12 12:01:49,069 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEM
2025-06-12 12:01:49,506 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEM
2025-06-12 12:01:49,506 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 640603.13, 'new_value': 707170.83}, {'field': 'offline_amount', 'old_value': 68854.0, 'new_value': 78706.0}, {'field': 'total_amount', 'old_value': 709457.13, 'new_value': 785876.83}, {'field': 'order_count', 'old_value': 2649, 'new_value': 2958}]
2025-06-12 12:01:49,506 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLM
2025-06-12 12:01:49,944 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLM
2025-06-12 12:01:49,944 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 154946.95, 'new_value': 166362.15}, {'field': 'total_amount', 'old_value': 154946.95, 'new_value': 166362.15}, {'field': 'order_count', 'old_value': 574, 'new_value': 606}]
2025-06-12 12:01:49,944 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTM
2025-06-12 12:01:50,319 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTM
2025-06-12 12:01:50,319 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21285.62, 'new_value': 23020.2}, {'field': 'offline_amount', 'old_value': 169373.2, 'new_value': 175351.36}, {'field': 'total_amount', 'old_value': 190658.82, 'new_value': 198371.56}, {'field': 'order_count', 'old_value': 1539, 'new_value': 1623}]
2025-06-12 12:01:50,319 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUM
2025-06-12 12:01:50,772 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUM
2025-06-12 12:01:50,772 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67179.31, 'new_value': 75372.16}, {'field': 'offline_amount', 'old_value': 125726.16, 'new_value': 134394.96}, {'field': 'total_amount', 'old_value': 192905.47, 'new_value': 209767.12}, {'field': 'order_count', 'old_value': 1623, 'new_value': 1796}]
2025-06-12 12:01:50,772 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVM
2025-06-12 12:01:51,272 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVM
2025-06-12 12:01:51,272 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2652.18, 'new_value': 2860.27}, {'field': 'offline_amount', 'old_value': 46638.3, 'new_value': 51658.0}, {'field': 'total_amount', 'old_value': 49290.48, 'new_value': 54518.27}, {'field': 'order_count', 'old_value': 1958, 'new_value': 2166}]
2025-06-12 12:01:51,272 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYM
2025-06-12 12:01:51,678 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYM
2025-06-12 12:01:51,678 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20751.95, 'new_value': 22726.28}, {'field': 'offline_amount', 'old_value': 22810.19, 'new_value': 23992.67}, {'field': 'total_amount', 'old_value': 43562.14, 'new_value': 46718.95}, {'field': 'order_count', 'old_value': 1123, 'new_value': 1212}]
2025-06-12 12:01:51,678 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO01
2025-06-12 12:01:52,116 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO01
2025-06-12 12:01:52,116 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 133666.17, 'new_value': 143304.15}, {'field': 'offline_amount', 'old_value': 13683.04, 'new_value': 13914.14}, {'field': 'total_amount', 'old_value': 147349.21, 'new_value': 157218.29}, {'field': 'order_count', 'old_value': 5801, 'new_value': 6156}]
2025-06-12 12:01:52,116 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR01
2025-06-12 12:01:52,584 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR01
2025-06-12 12:01:52,584 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42930.0, 'new_value': 49181.0}, {'field': 'offline_amount', 'old_value': 41146.0, 'new_value': 49505.0}, {'field': 'total_amount', 'old_value': 84076.0, 'new_value': 98686.0}, {'field': 'order_count', 'old_value': 3387, 'new_value': 3950}]
2025-06-12 12:01:52,584 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS01
2025-06-12 12:01:53,037 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS01
2025-06-12 12:01:53,037 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102981.11, 'new_value': 112068.81}, {'field': 'total_amount', 'old_value': 112799.38, 'new_value': 121887.08}, {'field': 'order_count', 'old_value': 4987, 'new_value': 5386}]
2025-06-12 12:01:53,037 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV01
2025-06-12 12:01:53,522 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV01
2025-06-12 12:01:53,522 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80960.07, 'new_value': 91316.07}, {'field': 'total_amount', 'old_value': 80960.07, 'new_value': 91316.07}, {'field': 'order_count', 'old_value': 3604, 'new_value': 4056}]
2025-06-12 12:01:53,522 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX01
2025-06-12 12:01:53,990 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX01
2025-06-12 12:01:53,990 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54819.0, 'new_value': 61524.0}, {'field': 'total_amount', 'old_value': 54819.0, 'new_value': 61524.0}, {'field': 'order_count', 'old_value': 91, 'new_value': 101}]
2025-06-12 12:01:53,990 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM011
2025-06-12 12:01:54,490 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM011
2025-06-12 12:01:54,490 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1692.0, 'new_value': 1798.0}, {'field': 'offline_amount', 'old_value': 13261.6, 'new_value': 13678.6}, {'field': 'total_amount', 'old_value': 14953.6, 'new_value': 15476.6}, {'field': 'order_count', 'old_value': 501, 'new_value': 517}]
2025-06-12 12:01:54,506 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM411
2025-06-12 12:01:54,975 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM411
2025-06-12 12:01:54,975 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17861.5, 'new_value': 18571.5}, {'field': 'total_amount', 'old_value': 17861.5, 'new_value': 18571.5}, {'field': 'order_count', 'old_value': 33, 'new_value': 34}]
2025-06-12 12:01:54,975 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM611
2025-06-12 12:01:55,444 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM611
2025-06-12 12:01:55,444 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6189.21, 'new_value': 6615.21}, {'field': 'offline_amount', 'old_value': 103308.8, 'new_value': 113636.8}, {'field': 'total_amount', 'old_value': 109498.01, 'new_value': 120252.01}, {'field': 'order_count', 'old_value': 568, 'new_value': 626}]
2025-06-12 12:01:55,444 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM711
2025-06-12 12:01:55,944 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM711
2025-06-12 12:01:55,944 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13293.0, 'new_value': 15338.0}, {'field': 'total_amount', 'old_value': 13293.0, 'new_value': 15338.0}, {'field': 'order_count', 'old_value': 36, 'new_value': 44}]
2025-06-12 12:01:55,944 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM811
2025-06-12 12:01:56,444 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM811
2025-06-12 12:01:56,444 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76100.19, 'new_value': 85904.29}, {'field': 'total_amount', 'old_value': 76100.19, 'new_value': 85904.29}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-06-12 12:01:56,444 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB11
2025-06-12 12:01:56,912 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB11
2025-06-12 12:01:56,912 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48977.7, 'new_value': 55184.9}, {'field': 'offline_amount', 'old_value': 87498.46, 'new_value': 94661.5}, {'field': 'total_amount', 'old_value': 136476.16, 'new_value': 149846.4}, {'field': 'order_count', 'old_value': 4373, 'new_value': 4826}]
2025-06-12 12:01:56,912 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC11
2025-06-12 12:01:57,397 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC11
2025-06-12 12:01:57,397 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 85908.54, 'new_value': 96987.32}, {'field': 'offline_amount', 'old_value': 135071.46, 'new_value': 142595.33}, {'field': 'total_amount', 'old_value': 220980.0, 'new_value': 239582.65}, {'field': 'order_count', 'old_value': 6721, 'new_value': 7385}]
2025-06-12 12:01:57,397 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ11
2025-06-12 12:01:57,897 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ11
2025-06-12 12:01:57,897 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15865.21, 'new_value': 22070.23}, {'field': 'total_amount', 'old_value': 81936.74, 'new_value': 88141.76}, {'field': 'order_count', 'old_value': 4463, 'new_value': 4884}]
2025-06-12 12:01:57,897 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK11
2025-06-12 12:01:58,350 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK11
2025-06-12 12:01:58,350 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13352.05, 'new_value': 16203.05}, {'field': 'total_amount', 'old_value': 13352.05, 'new_value': 16203.05}, {'field': 'order_count', 'old_value': 288, 'new_value': 344}]
2025-06-12 12:01:58,350 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO11
2025-06-12 12:01:58,850 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO11
2025-06-12 12:01:58,850 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49110.0, 'new_value': 54199.0}, {'field': 'total_amount', 'old_value': 49278.0, 'new_value': 54367.0}, {'field': 'order_count', 'old_value': 157, 'new_value': 173}]
2025-06-12 12:01:58,850 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS11
2025-06-12 12:01:59,318 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS11
2025-06-12 12:01:59,318 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57837.86, 'new_value': 62791.92}, {'field': 'offline_amount', 'old_value': 19029.59, 'new_value': 20141.13}, {'field': 'total_amount', 'old_value': 76867.45, 'new_value': 82933.05}, {'field': 'order_count', 'old_value': 4382, 'new_value': 4717}]
2025-06-12 12:01:59,318 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT11
2025-06-12 12:01:59,787 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT11
2025-06-12 12:01:59,787 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30235.57, 'new_value': 32313.31}, {'field': 'offline_amount', 'old_value': 49947.0, 'new_value': 50927.9}, {'field': 'total_amount', 'old_value': 80182.57, 'new_value': 83241.21}, {'field': 'order_count', 'old_value': 864, 'new_value': 908}]
2025-06-12 12:01:59,787 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU11
2025-06-12 12:02:00,318 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU11
2025-06-12 12:02:00,318 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55486.65, 'new_value': 62350.96}, {'field': 'total_amount', 'old_value': 55486.65, 'new_value': 62350.96}, {'field': 'order_count', 'old_value': 248, 'new_value': 282}]
2025-06-12 12:02:00,318 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV11
2025-06-12 12:02:00,787 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV11
2025-06-12 12:02:00,787 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122656.4, 'new_value': 137195.4}, {'field': 'total_amount', 'old_value': 122656.4, 'new_value': 137195.4}, {'field': 'order_count', 'old_value': 176, 'new_value': 191}]
2025-06-12 12:02:00,787 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX11
2025-06-12 12:02:01,225 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX11
2025-06-12 12:02:01,225 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38619.0, 'new_value': 39373.0}, {'field': 'offline_amount', 'old_value': 94526.0, 'new_value': 104370.0}, {'field': 'total_amount', 'old_value': 133145.0, 'new_value': 143743.0}, {'field': 'order_count', 'old_value': 598, 'new_value': 643}]
2025-06-12 12:02:01,225 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM021
2025-06-12 12:02:01,678 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM021
2025-06-12 12:02:01,678 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 201717.9, 'new_value': 210101.7}, {'field': 'total_amount', 'old_value': 201717.9, 'new_value': 210101.7}, {'field': 'order_count', 'old_value': 4371, 'new_value': 4568}]
2025-06-12 12:02:01,678 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM621
2025-06-12 12:02:02,334 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM621
2025-06-12 12:02:02,334 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40441.36, 'new_value': 42600.99}, {'field': 'total_amount', 'old_value': 40441.36, 'new_value': 42600.99}, {'field': 'order_count', 'old_value': 2564, 'new_value': 2732}]
2025-06-12 12:02:02,334 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD21
2025-06-12 12:02:02,787 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD21
2025-06-12 12:02:02,787 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9290.5, 'new_value': 10253.75}, {'field': 'offline_amount', 'old_value': 5878.0, 'new_value': 6114.69}, {'field': 'total_amount', 'old_value': 15168.5, 'new_value': 16368.44}, {'field': 'order_count', 'old_value': 568, 'new_value': 604}]
2025-06-12 12:02:02,787 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME21
2025-06-12 12:02:03,240 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME21
2025-06-12 12:02:03,240 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79855.0, 'new_value': 92174.0}, {'field': 'total_amount', 'old_value': 79855.0, 'new_value': 92174.0}, {'field': 'order_count', 'old_value': 8405, 'new_value': 9731}]
2025-06-12 12:02:03,240 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS21
2025-06-12 12:02:03,725 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS21
2025-06-12 12:02:03,725 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 415533.6, 'new_value': 453132.5}, {'field': 'total_amount', 'old_value': 415533.6, 'new_value': 453132.5}, {'field': 'order_count', 'old_value': 1429, 'new_value': 1565}]
2025-06-12 12:02:03,725 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT21
2025-06-12 12:02:04,146 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT21
2025-06-12 12:02:04,146 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18984.5, 'new_value': 21277.5}, {'field': 'total_amount', 'old_value': 18984.5, 'new_value': 21277.5}, {'field': 'order_count', 'old_value': 107, 'new_value': 117}]
2025-06-12 12:02:04,146 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU21
2025-06-12 12:02:04,584 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU21
2025-06-12 12:02:04,584 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44258.8, 'new_value': 46909.7}, {'field': 'total_amount', 'old_value': 44258.8, 'new_value': 46909.7}, {'field': 'order_count', 'old_value': 2497, 'new_value': 2630}]
2025-06-12 12:02:04,584 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV21
2025-06-12 12:02:05,053 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV21
2025-06-12 12:02:05,053 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 320737.52, 'new_value': 337737.46}, {'field': 'total_amount', 'old_value': 320737.52, 'new_value': 337737.46}, {'field': 'order_count', 'old_value': 2536, 'new_value': 2741}]
2025-06-12 12:02:05,053 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX21
2025-06-12 12:02:05,521 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX21
2025-06-12 12:02:05,521 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46835.0, 'new_value': 52586.0}, {'field': 'total_amount', 'old_value': 46835.0, 'new_value': 52586.0}, {'field': 'order_count', 'old_value': 1384, 'new_value': 1567}]
2025-06-12 12:02:05,521 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMY21
2025-06-12 12:02:05,974 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMY21
2025-06-12 12:02:05,974 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 319520.0, 'new_value': 367308.0}, {'field': 'total_amount', 'old_value': 319520.0, 'new_value': 367308.0}, {'field': 'order_count', 'old_value': 1165, 'new_value': 1304}]
2025-06-12 12:02:05,974 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM231
2025-06-12 12:02:06,365 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM231
2025-06-12 12:02:06,365 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42315.65, 'new_value': 47676.67}, {'field': 'offline_amount', 'old_value': 12666.92, 'new_value': 13646.38}, {'field': 'total_amount', 'old_value': 54982.57, 'new_value': 61323.05}, {'field': 'order_count', 'old_value': 3023, 'new_value': 3395}]
2025-06-12 12:02:06,365 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM531
2025-06-12 12:02:06,849 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM531
2025-06-12 12:02:06,849 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 174661.03, 'new_value': 213765.77}, {'field': 'total_amount', 'old_value': 183743.76, 'new_value': 222848.5}, {'field': 'order_count', 'old_value': 233, 'new_value': 268}]
2025-06-12 12:02:06,849 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM831
2025-06-12 12:02:07,303 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM831
2025-06-12 12:02:07,303 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72673.48, 'new_value': 82772.48}, {'field': 'total_amount', 'old_value': 72673.48, 'new_value': 82772.48}, {'field': 'order_count', 'old_value': 23, 'new_value': 27}]
2025-06-12 12:02:07,303 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM931
2025-06-12 12:02:07,787 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM931
2025-06-12 12:02:07,787 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2047812.18, 'new_value': 2215776.18}, {'field': 'total_amount', 'old_value': 2047812.18, 'new_value': 2215776.18}, {'field': 'order_count', 'old_value': 43952, 'new_value': 47470}]
2025-06-12 12:02:07,787 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA31
2025-06-12 12:02:08,287 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA31
2025-06-12 12:02:08,287 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 418613.84, 'new_value': 459872.84}, {'field': 'total_amount', 'old_value': 418613.84, 'new_value': 459872.84}, {'field': 'order_count', 'old_value': 1265, 'new_value': 1384}]
2025-06-12 12:02:08,287 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB31
2025-06-12 12:02:08,740 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB31
2025-06-12 12:02:08,740 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 291669.25, 'new_value': 315440.45}, {'field': 'total_amount', 'old_value': 291669.25, 'new_value': 315440.45}, {'field': 'order_count', 'old_value': 790, 'new_value': 851}]
2025-06-12 12:02:08,740 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMF31
2025-06-12 12:02:09,224 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMF31
2025-06-12 12:02:09,240 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22412.42, 'new_value': 26245.96}, {'field': 'total_amount', 'old_value': 38364.5, 'new_value': 42198.04}, {'field': 'order_count', 'old_value': 2547, 'new_value': 2809}]
2025-06-12 12:02:09,240 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM01
2025-06-12 12:02:09,693 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM01
2025-06-12 12:02:09,693 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40044.51, 'new_value': 46943.0}, {'field': 'total_amount', 'old_value': 68616.77, 'new_value': 75515.26}, {'field': 'order_count', 'old_value': 4569, 'new_value': 5042}]
2025-06-12 12:02:09,693 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM21
2025-06-12 12:02:10,162 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM21
2025-06-12 12:02:10,162 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7210.0, 'new_value': 15190.0}, {'field': 'total_amount', 'old_value': 10110.0, 'new_value': 18090.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 6}]
2025-06-12 12:02:10,162 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM81
2025-06-12 12:02:10,599 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM81
2025-06-12 12:02:10,599 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27788.0, 'new_value': 31078.0}, {'field': 'total_amount', 'old_value': 27788.0, 'new_value': 31078.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-06-12 12:02:10,599 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM91
2025-06-12 12:02:11,052 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM91
2025-06-12 12:02:11,052 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4084.58, 'new_value': 4506.58}, {'field': 'offline_amount', 'old_value': 35650.79, 'new_value': 36754.19}, {'field': 'total_amount', 'old_value': 39735.37, 'new_value': 41260.77}, {'field': 'order_count', 'old_value': 1249, 'new_value': 1315}]
2025-06-12 12:02:11,052 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME1
2025-06-12 12:02:11,490 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME1
2025-06-12 12:02:11,490 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 198452.0, 'new_value': 210338.0}, {'field': 'total_amount', 'old_value': 198452.0, 'new_value': 210338.0}, {'field': 'order_count', 'old_value': 1275, 'new_value': 1400}]
2025-06-12 12:02:11,490 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF1
2025-06-12 12:02:12,052 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF1
2025-06-12 12:02:12,052 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3993.95, 'new_value': 4071.95}, {'field': 'offline_amount', 'old_value': 25593.8, 'new_value': 26093.8}, {'field': 'total_amount', 'old_value': 29587.75, 'new_value': 30165.75}, {'field': 'order_count', 'old_value': 201, 'new_value': 203}]
2025-06-12 12:02:12,052 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH1
2025-06-12 12:02:12,490 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH1
2025-06-12 12:02:12,490 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87964.04, 'new_value': 97439.2}, {'field': 'total_amount', 'old_value': 87964.04, 'new_value': 97439.2}, {'field': 'order_count', 'old_value': 2962, 'new_value': 3234}]
2025-06-12 12:02:12,490 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK1
2025-06-12 12:02:12,927 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK1
2025-06-12 12:02:12,927 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102600.0, 'new_value': 113800.0}, {'field': 'total_amount', 'old_value': 102600.0, 'new_value': 113800.0}, {'field': 'order_count', 'old_value': 244, 'new_value': 270}]
2025-06-12 12:02:12,927 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM1
2025-06-12 12:02:13,490 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM1
2025-06-12 12:02:13,490 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14122.4, 'new_value': 15783.8}, {'field': 'offline_amount', 'old_value': 51789.0, 'new_value': 53787.0}, {'field': 'total_amount', 'old_value': 65911.4, 'new_value': 69570.8}, {'field': 'order_count', 'old_value': 869, 'new_value': 917}]
2025-06-12 12:02:13,490 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO1
2025-06-12 12:02:13,927 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO1
2025-06-12 12:02:13,927 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78094.71, 'new_value': 86462.02}, {'field': 'total_amount', 'old_value': 78094.71, 'new_value': 86462.02}, {'field': 'order_count', 'old_value': 1316, 'new_value': 1446}]
2025-06-12 12:02:13,927 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ1
2025-06-12 12:02:14,365 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ1
2025-06-12 12:02:14,365 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 210926.0, 'new_value': 243167.0}, {'field': 'total_amount', 'old_value': 210926.0, 'new_value': 243167.0}, {'field': 'order_count', 'old_value': 219, 'new_value': 244}]
2025-06-12 12:02:14,365 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMR1
2025-06-12 12:02:14,755 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMR1
2025-06-12 12:02:14,755 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1381.8, 'new_value': 1659.8}, {'field': 'total_amount', 'old_value': 1381.8, 'new_value': 1659.8}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-06-12 12:02:14,755 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMT1
2025-06-12 12:02:15,209 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMT1
2025-06-12 12:02:15,209 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20780.1, 'new_value': 22082.9}, {'field': 'total_amount', 'old_value': 20780.1, 'new_value': 22082.9}, {'field': 'order_count', 'old_value': 169, 'new_value': 178}]
2025-06-12 12:02:15,209 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMU1
2025-06-12 12:02:15,677 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMU1
2025-06-12 12:02:15,677 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16140.82, 'new_value': 24324.12}, {'field': 'total_amount', 'old_value': 95989.28, 'new_value': 104172.58}, {'field': 'order_count', 'old_value': 4611, 'new_value': 5064}]
2025-06-12 12:02:15,677 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMV1
2025-06-12 12:02:16,146 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMV1
2025-06-12 12:02:16,146 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32252.64, 'new_value': 34548.21}, {'field': 'total_amount', 'old_value': 32252.64, 'new_value': 34548.21}, {'field': 'order_count', 'old_value': 1225, 'new_value': 1331}]
2025-06-12 12:02:16,146 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW1
2025-06-12 12:02:16,709 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW1
2025-06-12 12:02:16,709 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55244.15, 'new_value': 60424.15}, {'field': 'total_amount', 'old_value': 55244.15, 'new_value': 60424.15}, {'field': 'order_count', 'old_value': 1398, 'new_value': 1531}]
2025-06-12 12:02:16,709 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY1
2025-06-12 12:02:17,162 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY1
2025-06-12 12:02:17,162 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37888.5, 'new_value': 40988.0}, {'field': 'total_amount', 'old_value': 37888.5, 'new_value': 40988.0}, {'field': 'order_count', 'old_value': 345, 'new_value': 369}]
2025-06-12 12:02:17,162 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMZ1
2025-06-12 12:02:17,583 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMZ1
2025-06-12 12:02:17,583 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62290.0, 'new_value': 69559.0}, {'field': 'total_amount', 'old_value': 62290.0, 'new_value': 69559.0}, {'field': 'order_count', 'old_value': 5725, 'new_value': 6300}]
2025-06-12 12:02:17,583 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM02
2025-06-12 12:02:17,990 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM02
2025-06-12 12:02:17,990 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36365.0, 'new_value': 39450.0}, {'field': 'total_amount', 'old_value': 36365.0, 'new_value': 39450.0}, {'field': 'order_count', 'old_value': 503, 'new_value': 574}]
2025-06-12 12:02:17,990 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM22
2025-06-12 12:02:18,365 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM22
2025-06-12 12:02:18,365 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21557.7, 'new_value': 25557.7}, {'field': 'offline_amount', 'old_value': 50687.09, 'new_value': 51172.11}, {'field': 'total_amount', 'old_value': 72244.79, 'new_value': 76729.81}, {'field': 'order_count', 'old_value': 1795, 'new_value': 1906}]
2025-06-12 12:02:18,365 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM42
2025-06-12 12:02:18,865 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM42
2025-06-12 12:02:18,865 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5780.0, 'new_value': 6160.0}, {'field': 'total_amount', 'old_value': 5780.0, 'new_value': 6160.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-06-12 12:02:18,865 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC2
2025-06-12 12:02:19,365 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC2
2025-06-12 12:02:19,365 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 280350.0, 'new_value': 1333189.0}, {'field': 'total_amount', 'old_value': 1189250.0, 'new_value': 2242089.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 7}]
2025-06-12 12:02:19,365 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME2
2025-06-12 12:02:19,818 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME2
2025-06-12 12:02:19,818 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 121597.0, 'new_value': 139089.0}, {'field': 'total_amount', 'old_value': 121597.0, 'new_value': 139089.0}, {'field': 'order_count', 'old_value': 2796, 'new_value': 3216}]
2025-06-12 12:02:19,818 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF2
2025-06-12 12:02:20,271 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF2
2025-06-12 12:02:20,271 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44217.0, 'new_value': 48539.0}, {'field': 'total_amount', 'old_value': 44217.0, 'new_value': 48539.0}, {'field': 'order_count', 'old_value': 2414, 'new_value': 2672}]
2025-06-12 12:02:20,271 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG2
2025-06-12 12:02:20,724 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG2
2025-06-12 12:02:20,724 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 161218.0, 'new_value': 179378.0}, {'field': 'total_amount', 'old_value': 161218.0, 'new_value': 179378.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 42}]
2025-06-12 12:02:20,724 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH2
2025-06-12 12:02:21,161 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH2
2025-06-12 12:02:21,161 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1246967.0, 'new_value': 1363616.9}, {'field': 'total_amount', 'old_value': 1246967.0, 'new_value': 1363616.9}, {'field': 'order_count', 'old_value': 2359, 'new_value': 2567}]
2025-06-12 12:02:21,161 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMI2
2025-06-12 12:02:21,568 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMI2
2025-06-12 12:02:21,568 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2687.0, 'new_value': 2815.0}, {'field': 'offline_amount', 'old_value': 9123.0, 'new_value': 9811.0}, {'field': 'total_amount', 'old_value': 11810.0, 'new_value': 12626.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 50}]
2025-06-12 12:02:21,568 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ2
2025-06-12 12:02:22,005 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ2
2025-06-12 12:02:22,005 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 359764.0, 'new_value': 382299.0}, {'field': 'total_amount', 'old_value': 359764.0, 'new_value': 382299.0}, {'field': 'order_count', 'old_value': 1729, 'new_value': 1841}]
2025-06-12 12:02:22,005 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK2
2025-06-12 12:02:22,427 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK2
2025-06-12 12:02:22,427 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4049692.73, 'new_value': 4340080.73}, {'field': 'total_amount', 'old_value': 4049692.73, 'new_value': 4340080.73}, {'field': 'order_count', 'old_value': 15123, 'new_value': 16076}]
2025-06-12 12:02:22,427 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM2
2025-06-12 12:02:22,927 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM2
2025-06-12 12:02:22,927 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 85569.59, 'new_value': 93637.52}, {'field': 'offline_amount', 'old_value': 76471.67, 'new_value': 81755.26}, {'field': 'total_amount', 'old_value': 162041.26, 'new_value': 175392.78}, {'field': 'order_count', 'old_value': 6910, 'new_value': 7480}]
2025-06-12 12:02:22,927 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO2
2025-06-12 12:02:23,458 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO2
2025-06-12 12:02:23,458 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84063.9, 'new_value': 93635.9}, {'field': 'total_amount', 'old_value': 84063.9, 'new_value': 93635.9}, {'field': 'order_count', 'old_value': 2903, 'new_value': 3237}]
2025-06-12 12:02:23,458 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMP2
2025-06-12 12:02:23,927 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMP2
2025-06-12 12:02:23,927 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1390.0, 'new_value': 1890.0}, {'field': 'total_amount', 'old_value': 1390.0, 'new_value': 1890.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-06-12 12:02:23,927 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ2
2025-06-12 12:02:24,380 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ2
2025-06-12 12:02:24,380 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 81270.8, 'new_value': 90407.8}, {'field': 'total_amount', 'old_value': 115472.9, 'new_value': 124609.9}, {'field': 'order_count', 'old_value': 303, 'new_value': 344}]
2025-06-12 12:02:24,380 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMU2
2025-06-12 12:02:24,833 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMU2
2025-06-12 12:02:24,833 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57277.0, 'new_value': 65242.0}, {'field': 'total_amount', 'old_value': 57377.0, 'new_value': 65342.0}, {'field': 'order_count', 'old_value': 5894, 'new_value': 6781}]
2025-06-12 12:02:24,833 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMV2
2025-06-12 12:02:25,349 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMV2
2025-06-12 12:02:25,349 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19104.0, 'new_value': 20233.0}, {'field': 'offline_amount', 'old_value': 123778.0, 'new_value': 137578.0}, {'field': 'total_amount', 'old_value': 142882.0, 'new_value': 157811.0}, {'field': 'order_count', 'old_value': 125, 'new_value': 143}]
2025-06-12 12:02:25,349 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW2
2025-06-12 12:02:25,771 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW2
2025-06-12 12:02:25,771 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15202.0, 'new_value': 16902.0}, {'field': 'total_amount', 'old_value': 15202.0, 'new_value': 16902.0}, {'field': 'order_count', 'old_value': 1498, 'new_value': 1499}]
2025-06-12 12:02:25,771 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX2
2025-06-12 12:02:26,239 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX2
2025-06-12 12:02:26,239 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 495000.0, 'new_value': 535000.0}, {'field': 'total_amount', 'old_value': 495000.0, 'new_value': 535000.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-06-12 12:02:26,239 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY2
2025-06-12 12:02:26,739 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY2
2025-06-12 12:02:26,739 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 136899.0, 'new_value': 149519.0}, {'field': 'total_amount', 'old_value': 136899.0, 'new_value': 149519.0}, {'field': 'order_count', 'old_value': 3813, 'new_value': 4273}]
2025-06-12 12:02:26,739 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM43
2025-06-12 12:02:27,177 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM43
2025-06-12 12:02:27,177 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68925.0, 'new_value': 70207.0}, {'field': 'total_amount', 'old_value': 68925.0, 'new_value': 70207.0}, {'field': 'order_count', 'old_value': 1543, 'new_value': 1573}]
2025-06-12 12:02:27,177 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM63
2025-06-12 12:02:27,630 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM63
2025-06-12 12:02:27,630 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 110048.72, 'new_value': 118765.42}, {'field': 'total_amount', 'old_value': 110048.72, 'new_value': 118765.42}, {'field': 'order_count', 'old_value': 34, 'new_value': 38}]
2025-06-12 12:02:27,630 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM73
2025-06-12 12:02:28,114 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM73
2025-06-12 12:02:28,114 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9768.0, 'new_value': 13004.0}, {'field': 'total_amount', 'old_value': 9768.0, 'new_value': 13004.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 12}]
2025-06-12 12:02:28,114 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM83
2025-06-12 12:02:28,614 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM83
2025-06-12 12:02:28,630 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2849.9, 'new_value': 2993.4}, {'field': 'total_amount', 'old_value': 2849.9, 'new_value': 2993.4}, {'field': 'order_count', 'old_value': 252, 'new_value': 269}]
2025-06-12 12:02:28,630 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME3
2025-06-12 12:02:29,067 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME3
2025-06-12 12:02:29,067 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51678.0, 'new_value': 56278.0}, {'field': 'total_amount', 'old_value': 51678.0, 'new_value': 56278.0}, {'field': 'order_count', 'old_value': 2327, 'new_value': 2328}]
2025-06-12 12:02:29,067 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF3
2025-06-12 12:02:29,489 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF3
2025-06-12 12:02:29,489 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64253.88, 'new_value': 66853.88}, {'field': 'total_amount', 'old_value': 64253.88, 'new_value': 66853.88}, {'field': 'order_count', 'old_value': 379, 'new_value': 380}]
2025-06-12 12:02:29,489 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG3
2025-06-12 12:02:29,958 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG3
2025-06-12 12:02:29,958 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38384.0, 'new_value': 41584.0}, {'field': 'total_amount', 'old_value': 38384.0, 'new_value': 41584.0}, {'field': 'order_count', 'old_value': 300, 'new_value': 301}]
2025-06-12 12:02:29,958 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMI3
2025-06-12 12:02:30,427 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMI3
2025-06-12 12:02:30,427 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23299.37, 'new_value': 25967.26}, {'field': 'total_amount', 'old_value': 23299.37, 'new_value': 25967.26}, {'field': 'order_count', 'old_value': 199, 'new_value': 217}]
2025-06-12 12:02:30,427 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK3
2025-06-12 12:02:30,927 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK3
2025-06-12 12:02:30,927 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14065.0, 'new_value': 15265.0}, {'field': 'total_amount', 'old_value': 14065.0, 'new_value': 15265.0}, {'field': 'order_count', 'old_value': 1332, 'new_value': 1333}]
2025-06-12 12:02:30,927 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMO3
2025-06-12 12:02:31,396 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMO3
2025-06-12 12:02:31,396 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 133509.68, 'new_value': 141852.38}, {'field': 'total_amount', 'old_value': 133509.68, 'new_value': 141852.38}, {'field': 'order_count', 'old_value': 959, 'new_value': 1051}]
2025-06-12 12:02:31,396 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMP3
2025-06-12 12:02:31,880 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMP3
2025-06-12 12:02:31,880 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1973.0, 'new_value': 2571.0}, {'field': 'total_amount', 'old_value': 1973.0, 'new_value': 2571.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 9}]
2025-06-12 12:02:31,880 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMLP
2025-06-12 12:02:32,317 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMLP
2025-06-12 12:02:32,317 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2290126.0, 'new_value': 2500126.0}, {'field': 'total_amount', 'old_value': 2290126.0, 'new_value': 2500126.0}, {'field': 'order_count', 'old_value': 41638, 'new_value': 41639}]
2025-06-12 12:02:32,317 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMMP
2025-06-12 12:02:32,802 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMMP
2025-06-12 12:02:32,802 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27542.0, 'new_value': 27842.0}, {'field': 'total_amount', 'old_value': 27542.0, 'new_value': 27842.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-06-12 12:02:32,802 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMOP
2025-06-12 12:02:33,302 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMOP
2025-06-12 12:02:33,302 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97633.49, 'new_value': 112633.49}, {'field': 'total_amount', 'old_value': 97633.49, 'new_value': 112633.49}, {'field': 'order_count', 'old_value': 209, 'new_value': 210}]
2025-06-12 12:02:33,302 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMQP
2025-06-12 12:02:33,786 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMQP
2025-06-12 12:02:33,786 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2844.0, 'new_value': 3181.0}, {'field': 'total_amount', 'old_value': 2844.0, 'new_value': 3181.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 20}]
2025-06-12 12:02:33,786 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMRP
2025-06-12 12:02:34,286 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMRP
2025-06-12 12:02:34,302 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2864700.0, 'new_value': 2929700.0}, {'field': 'total_amount', 'old_value': 2864700.0, 'new_value': 2929700.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-06-12 12:02:34,302 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMSP
2025-06-12 12:02:34,817 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMSP
2025-06-12 12:02:34,817 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62097.0, 'new_value': 69249.0}, {'field': 'total_amount', 'old_value': 62097.0, 'new_value': 69249.0}, {'field': 'order_count', 'old_value': 63, 'new_value': 72}]
2025-06-12 12:02:34,817 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMTP
2025-06-12 12:02:35,255 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMTP
2025-06-12 12:02:35,255 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14586.13, 'new_value': 15686.13}, {'field': 'total_amount', 'old_value': 14586.13, 'new_value': 15686.13}, {'field': 'order_count', 'old_value': 551, 'new_value': 552}]
2025-06-12 12:02:35,255 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMUP
2025-06-12 12:02:35,708 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMUP
2025-06-12 12:02:35,708 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65232.68, 'new_value': 72480.99}, {'field': 'total_amount', 'old_value': 65232.68, 'new_value': 72480.99}, {'field': 'order_count', 'old_value': 4939, 'new_value': 5527}]
2025-06-12 12:02:35,708 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMWP
2025-06-12 12:02:36,177 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMWP
2025-06-12 12:02:36,177 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90661.28, 'new_value': 102518.28}, {'field': 'total_amount', 'old_value': 90661.28, 'new_value': 102518.28}, {'field': 'order_count', 'old_value': 1920, 'new_value': 2157}]
2025-06-12 12:02:36,177 - INFO - 日期 2025-06 处理完成 - 更新: 254 条，插入: 0 条，错误: 0 条
2025-06-12 12:02:36,177 - INFO - 数据同步完成！更新: 254 条，插入: 0 条，错误: 0 条
2025-06-12 12:02:36,177 - INFO - =================同步完成====================
2025-06-12 15:00:02,579 - INFO - =================使用默认全量同步=============
2025-06-12 15:00:04,204 - INFO - MySQL查询成功，共获取 3930 条记录
2025-06-12 15:00:04,204 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-12 15:00:04,235 - INFO - 开始处理日期: 2025-01
2025-06-12 15:00:04,235 - INFO - Request Parameters - Page 1:
2025-06-12 15:00:04,251 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:04,251 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:05,673 - INFO - Response - Page 1:
2025-06-12 15:00:05,876 - INFO - 第 1 页获取到 100 条记录
2025-06-12 15:00:05,876 - INFO - Request Parameters - Page 2:
2025-06-12 15:00:05,876 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:05,876 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:06,360 - INFO - Response - Page 2:
2025-06-12 15:00:06,563 - INFO - 第 2 页获取到 100 条记录
2025-06-12 15:00:06,563 - INFO - Request Parameters - Page 3:
2025-06-12 15:00:06,563 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:06,563 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:07,094 - INFO - Response - Page 3:
2025-06-12 15:00:07,297 - INFO - 第 3 页获取到 100 条记录
2025-06-12 15:00:07,297 - INFO - Request Parameters - Page 4:
2025-06-12 15:00:07,297 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:07,297 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:07,844 - INFO - Response - Page 4:
2025-06-12 15:00:08,047 - INFO - 第 4 页获取到 100 条记录
2025-06-12 15:00:08,047 - INFO - Request Parameters - Page 5:
2025-06-12 15:00:08,047 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:08,047 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:08,547 - INFO - Response - Page 5:
2025-06-12 15:00:08,751 - INFO - 第 5 页获取到 100 条记录
2025-06-12 15:00:08,751 - INFO - Request Parameters - Page 6:
2025-06-12 15:00:08,751 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:08,751 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:09,219 - INFO - Response - Page 6:
2025-06-12 15:00:09,422 - INFO - 第 6 页获取到 100 条记录
2025-06-12 15:00:09,422 - INFO - Request Parameters - Page 7:
2025-06-12 15:00:09,422 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:09,422 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:09,891 - INFO - Response - Page 7:
2025-06-12 15:00:10,094 - INFO - 第 7 页获取到 82 条记录
2025-06-12 15:00:10,094 - INFO - 查询完成，共获取到 682 条记录
2025-06-12 15:00:10,094 - INFO - 获取到 682 条表单数据
2025-06-12 15:00:10,094 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-12 15:00:10,110 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 15:00:10,110 - INFO - 开始处理日期: 2025-02
2025-06-12 15:00:10,110 - INFO - Request Parameters - Page 1:
2025-06-12 15:00:10,110 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:10,110 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:10,626 - INFO - Response - Page 1:
2025-06-12 15:00:10,829 - INFO - 第 1 页获取到 100 条记录
2025-06-12 15:00:10,829 - INFO - Request Parameters - Page 2:
2025-06-12 15:00:10,829 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:10,829 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:11,329 - INFO - Response - Page 2:
2025-06-12 15:00:11,532 - INFO - 第 2 页获取到 100 条记录
2025-06-12 15:00:11,532 - INFO - Request Parameters - Page 3:
2025-06-12 15:00:11,532 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:11,532 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:12,266 - INFO - Response - Page 3:
2025-06-12 15:00:12,469 - INFO - 第 3 页获取到 100 条记录
2025-06-12 15:00:12,469 - INFO - Request Parameters - Page 4:
2025-06-12 15:00:12,469 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:12,469 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:12,969 - INFO - Response - Page 4:
2025-06-12 15:00:13,172 - INFO - 第 4 页获取到 100 条记录
2025-06-12 15:00:13,172 - INFO - Request Parameters - Page 5:
2025-06-12 15:00:13,172 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:13,172 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:13,704 - INFO - Response - Page 5:
2025-06-12 15:00:13,907 - INFO - 第 5 页获取到 100 条记录
2025-06-12 15:00:13,907 - INFO - Request Parameters - Page 6:
2025-06-12 15:00:13,907 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:13,907 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:14,360 - INFO - Response - Page 6:
2025-06-12 15:00:14,563 - INFO - 第 6 页获取到 100 条记录
2025-06-12 15:00:14,563 - INFO - Request Parameters - Page 7:
2025-06-12 15:00:14,563 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:14,563 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:15,063 - INFO - Response - Page 7:
2025-06-12 15:00:15,266 - INFO - 第 7 页获取到 70 条记录
2025-06-12 15:00:15,266 - INFO - 查询完成，共获取到 670 条记录
2025-06-12 15:00:15,266 - INFO - 获取到 670 条表单数据
2025-06-12 15:00:15,266 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-12 15:00:15,282 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 15:00:15,282 - INFO - 开始处理日期: 2025-03
2025-06-12 15:00:15,282 - INFO - Request Parameters - Page 1:
2025-06-12 15:00:15,282 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:15,282 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:15,797 - INFO - Response - Page 1:
2025-06-12 15:00:16,000 - INFO - 第 1 页获取到 100 条记录
2025-06-12 15:00:16,000 - INFO - Request Parameters - Page 2:
2025-06-12 15:00:16,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:16,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:16,532 - INFO - Response - Page 2:
2025-06-12 15:00:16,735 - INFO - 第 2 页获取到 100 条记录
2025-06-12 15:00:16,735 - INFO - Request Parameters - Page 3:
2025-06-12 15:00:16,735 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:16,735 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:17,282 - INFO - Response - Page 3:
2025-06-12 15:00:17,485 - INFO - 第 3 页获取到 100 条记录
2025-06-12 15:00:17,485 - INFO - Request Parameters - Page 4:
2025-06-12 15:00:17,485 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:17,485 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:17,969 - INFO - Response - Page 4:
2025-06-12 15:00:18,172 - INFO - 第 4 页获取到 100 条记录
2025-06-12 15:00:18,172 - INFO - Request Parameters - Page 5:
2025-06-12 15:00:18,172 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:18,172 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:18,766 - INFO - Response - Page 5:
2025-06-12 15:00:18,969 - INFO - 第 5 页获取到 100 条记录
2025-06-12 15:00:18,969 - INFO - Request Parameters - Page 6:
2025-06-12 15:00:18,969 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:18,969 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:19,563 - INFO - Response - Page 6:
2025-06-12 15:00:19,766 - INFO - 第 6 页获取到 100 条记录
2025-06-12 15:00:19,766 - INFO - Request Parameters - Page 7:
2025-06-12 15:00:19,766 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:19,766 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:20,219 - INFO - Response - Page 7:
2025-06-12 15:00:20,422 - INFO - 第 7 页获取到 61 条记录
2025-06-12 15:00:20,422 - INFO - 查询完成，共获取到 661 条记录
2025-06-12 15:00:20,422 - INFO - 获取到 661 条表单数据
2025-06-12 15:00:20,422 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-12 15:00:20,438 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 15:00:20,438 - INFO - 开始处理日期: 2025-04
2025-06-12 15:00:20,438 - INFO - Request Parameters - Page 1:
2025-06-12 15:00:20,438 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:20,438 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:20,938 - INFO - Response - Page 1:
2025-06-12 15:00:21,141 - INFO - 第 1 页获取到 100 条记录
2025-06-12 15:00:21,141 - INFO - Request Parameters - Page 2:
2025-06-12 15:00:21,141 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:21,141 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:21,641 - INFO - Response - Page 2:
2025-06-12 15:00:21,844 - INFO - 第 2 页获取到 100 条记录
2025-06-12 15:00:21,844 - INFO - Request Parameters - Page 3:
2025-06-12 15:00:21,844 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:21,844 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:22,344 - INFO - Response - Page 3:
2025-06-12 15:00:22,547 - INFO - 第 3 页获取到 100 条记录
2025-06-12 15:00:22,547 - INFO - Request Parameters - Page 4:
2025-06-12 15:00:22,547 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:22,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:23,031 - INFO - Response - Page 4:
2025-06-12 15:00:23,234 - INFO - 第 4 页获取到 100 条记录
2025-06-12 15:00:23,234 - INFO - Request Parameters - Page 5:
2025-06-12 15:00:23,234 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:23,234 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:23,766 - INFO - Response - Page 5:
2025-06-12 15:00:23,969 - INFO - 第 5 页获取到 100 条记录
2025-06-12 15:00:23,969 - INFO - Request Parameters - Page 6:
2025-06-12 15:00:23,969 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:23,969 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:24,578 - INFO - Response - Page 6:
2025-06-12 15:00:24,781 - INFO - 第 6 页获取到 100 条记录
2025-06-12 15:00:24,781 - INFO - Request Parameters - Page 7:
2025-06-12 15:00:24,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:24,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:25,313 - INFO - Response - Page 7:
2025-06-12 15:00:25,516 - INFO - 第 7 页获取到 56 条记录
2025-06-12 15:00:25,516 - INFO - 查询完成，共获取到 656 条记录
2025-06-12 15:00:25,516 - INFO - 获取到 656 条表单数据
2025-06-12 15:00:25,516 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-12 15:00:25,531 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 15:00:25,531 - INFO - 开始处理日期: 2025-05
2025-06-12 15:00:25,531 - INFO - Request Parameters - Page 1:
2025-06-12 15:00:25,531 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:25,531 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:26,078 - INFO - Response - Page 1:
2025-06-12 15:00:26,281 - INFO - 第 1 页获取到 100 条记录
2025-06-12 15:00:26,281 - INFO - Request Parameters - Page 2:
2025-06-12 15:00:26,281 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:26,281 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:26,766 - INFO - Response - Page 2:
2025-06-12 15:00:26,969 - INFO - 第 2 页获取到 100 条记录
2025-06-12 15:00:26,969 - INFO - Request Parameters - Page 3:
2025-06-12 15:00:26,969 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:26,969 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:27,422 - INFO - Response - Page 3:
2025-06-12 15:00:27,625 - INFO - 第 3 页获取到 100 条记录
2025-06-12 15:00:27,625 - INFO - Request Parameters - Page 4:
2025-06-12 15:00:27,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:27,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:28,172 - INFO - Response - Page 4:
2025-06-12 15:00:28,375 - INFO - 第 4 页获取到 100 条记录
2025-06-12 15:00:28,375 - INFO - Request Parameters - Page 5:
2025-06-12 15:00:28,375 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:28,375 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:28,937 - INFO - Response - Page 5:
2025-06-12 15:00:29,141 - INFO - 第 5 页获取到 100 条记录
2025-06-12 15:00:29,141 - INFO - Request Parameters - Page 6:
2025-06-12 15:00:29,141 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:29,141 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:29,609 - INFO - Response - Page 6:
2025-06-12 15:00:29,812 - INFO - 第 6 页获取到 100 条记录
2025-06-12 15:00:29,812 - INFO - Request Parameters - Page 7:
2025-06-12 15:00:29,812 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:29,812 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:30,187 - INFO - Response - Page 7:
2025-06-12 15:00:30,391 - INFO - 第 7 页获取到 40 条记录
2025-06-12 15:00:30,391 - INFO - 查询完成，共获取到 640 条记录
2025-06-12 15:00:30,391 - INFO - 获取到 640 条表单数据
2025-06-12 15:00:30,391 - INFO - 当前日期 2025-05 有 640 条MySQL数据需要处理
2025-06-12 15:00:30,406 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 15:00:30,406 - INFO - 开始处理日期: 2025-06
2025-06-12 15:00:30,406 - INFO - Request Parameters - Page 1:
2025-06-12 15:00:30,406 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:30,406 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:30,937 - INFO - Response - Page 1:
2025-06-12 15:00:31,140 - INFO - 第 1 页获取到 100 条记录
2025-06-12 15:00:31,140 - INFO - Request Parameters - Page 2:
2025-06-12 15:00:31,140 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:31,140 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:31,781 - INFO - Response - Page 2:
2025-06-12 15:00:31,984 - INFO - 第 2 页获取到 100 条记录
2025-06-12 15:00:31,984 - INFO - Request Parameters - Page 3:
2025-06-12 15:00:31,984 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:31,984 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:32,547 - INFO - Response - Page 3:
2025-06-12 15:00:32,750 - INFO - 第 3 页获取到 100 条记录
2025-06-12 15:00:32,750 - INFO - Request Parameters - Page 4:
2025-06-12 15:00:32,750 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:32,750 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:33,250 - INFO - Response - Page 4:
2025-06-12 15:00:33,453 - INFO - 第 4 页获取到 100 条记录
2025-06-12 15:00:33,453 - INFO - Request Parameters - Page 5:
2025-06-12 15:00:33,453 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:33,453 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:33,922 - INFO - Response - Page 5:
2025-06-12 15:00:34,125 - INFO - 第 5 页获取到 100 条记录
2025-06-12 15:00:34,125 - INFO - Request Parameters - Page 6:
2025-06-12 15:00:34,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:34,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:34,593 - INFO - Response - Page 6:
2025-06-12 15:00:34,797 - INFO - 第 6 页获取到 100 条记录
2025-06-12 15:00:34,797 - INFO - Request Parameters - Page 7:
2025-06-12 15:00:34,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 15:00:34,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 15:00:35,109 - INFO - Response - Page 7:
2025-06-12 15:00:35,312 - INFO - 第 7 页获取到 21 条记录
2025-06-12 15:00:35,312 - INFO - 查询完成，共获取到 621 条记录
2025-06-12 15:00:35,312 - INFO - 获取到 621 条表单数据
2025-06-12 15:00:35,312 - INFO - 当前日期 2025-06 有 621 条MySQL数据需要处理
2025-06-12 15:00:35,312 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMOX
2025-06-12 15:00:35,781 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMOX
2025-06-12 15:00:35,781 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11471.2, 'new_value': 13065.2}, {'field': 'total_amount', 'old_value': 11471.2, 'new_value': 13065.2}, {'field': 'order_count', 'old_value': 62, 'new_value': 73}]
2025-06-12 15:00:35,781 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMPX
2025-06-12 15:00:36,234 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMPX
2025-06-12 15:00:36,234 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14964.04, 'new_value': 16754.26}, {'field': 'total_amount', 'old_value': 14964.04, 'new_value': 16754.26}, {'field': 'order_count', 'old_value': 2969, 'new_value': 3328}]
2025-06-12 15:00:36,234 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMIY
2025-06-12 15:00:36,922 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMIY
2025-06-12 15:00:36,922 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25710.77, 'new_value': 28042.26}, {'field': 'offline_amount', 'old_value': 134512.48, 'new_value': 149348.8}, {'field': 'total_amount', 'old_value': 160223.25, 'new_value': 177391.06}, {'field': 'order_count', 'old_value': 6512, 'new_value': 7308}]
2025-06-12 15:00:36,922 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMWY
2025-06-12 15:00:37,390 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMWY
2025-06-12 15:00:37,406 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54932.0, 'new_value': 59532.0}, {'field': 'total_amount', 'old_value': 64007.08, 'new_value': 68607.08}, {'field': 'order_count', 'old_value': 976, 'new_value': 1064}]
2025-06-12 15:00:37,406 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM9Z
2025-06-12 15:00:37,906 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM9Z
2025-06-12 15:00:37,906 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7718.0, 'new_value': 7958.0}, {'field': 'offline_amount', 'old_value': 7718.0, 'new_value': 7958.0}, {'field': 'total_amount', 'old_value': 15436.0, 'new_value': 15916.0}, {'field': 'order_count', 'old_value': 7445, 'new_value': 7685}]
2025-06-12 15:00:37,906 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMVB
2025-06-12 15:00:38,343 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMVB
2025-06-12 15:00:38,343 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 163503.0, 'new_value': 173539.0}, {'field': 'total_amount', 'old_value': 163503.0, 'new_value': 173539.0}, {'field': 'order_count', 'old_value': 4330, 'new_value': 4695}]
2025-06-12 15:00:38,343 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBC
2025-06-12 15:00:38,796 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBC
2025-06-12 15:00:38,796 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 976.0, 'new_value': 1430.0}, {'field': 'offline_amount', 'old_value': 8562.0, 'new_value': 9166.0}, {'field': 'total_amount', 'old_value': 9538.0, 'new_value': 10596.0}, {'field': 'order_count', 'old_value': 61, 'new_value': 70}]
2025-06-12 15:00:38,796 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMFC
2025-06-12 15:00:39,218 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMFC
2025-06-12 15:00:39,218 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23936.43, 'new_value': 25996.93}, {'field': 'total_amount', 'old_value': 23936.43, 'new_value': 25996.93}, {'field': 'order_count', 'old_value': 1537, 'new_value': 1662}]
2025-06-12 15:00:39,218 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGC
2025-06-12 15:00:39,812 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGC
2025-06-12 15:00:39,812 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21803.0, 'new_value': 25223.2}, {'field': 'total_amount', 'old_value': 21804.0, 'new_value': 25224.2}, {'field': 'order_count', 'old_value': 76, 'new_value': 87}]
2025-06-12 15:00:39,812 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2T
2025-06-12 15:00:40,218 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2T
2025-06-12 15:00:40,218 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11326.0, 'new_value': 12336.0}, {'field': 'total_amount', 'old_value': 11326.0, 'new_value': 12336.0}, {'field': 'order_count', 'old_value': 68, 'new_value': 74}]
2025-06-12 15:00:40,218 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZL
2025-06-12 15:00:40,671 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZL
2025-06-12 15:00:40,671 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73491.18, 'new_value': 80058.18}, {'field': 'total_amount', 'old_value': 73491.18, 'new_value': 80058.18}, {'field': 'order_count', 'old_value': 1541, 'new_value': 1692}]
2025-06-12 15:00:40,671 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM5M
2025-06-12 15:00:41,125 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM5M
2025-06-12 15:00:41,125 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29536.0, 'new_value': 32088.0}, {'field': 'offline_amount', 'old_value': 7994.0, 'new_value': 9284.0}, {'field': 'total_amount', 'old_value': 37530.0, 'new_value': 41372.0}, {'field': 'order_count', 'old_value': 2320, 'new_value': 2525}]
2025-06-12 15:00:41,140 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM111
2025-06-12 15:00:41,687 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM111
2025-06-12 15:00:41,687 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31066.09, 'new_value': 34156.46}, {'field': 'total_amount', 'old_value': 31066.09, 'new_value': 34156.46}, {'field': 'order_count', 'old_value': 3467, 'new_value': 3836}]
2025-06-12 15:00:41,687 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMH11
2025-06-12 15:00:42,171 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMH11
2025-06-12 15:00:42,171 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11216.26, 'new_value': 11820.5}, {'field': 'offline_amount', 'old_value': 12107.93, 'new_value': 13203.93}, {'field': 'total_amount', 'old_value': 23324.19, 'new_value': 25024.43}, {'field': 'order_count', 'old_value': 1096, 'new_value': 1180}]
2025-06-12 15:00:42,171 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMF21
2025-06-12 15:00:42,624 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMF21
2025-06-12 15:00:42,624 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71641.22, 'new_value': 78615.24}, {'field': 'total_amount', 'old_value': 71641.22, 'new_value': 78615.24}, {'field': 'order_count', 'old_value': 5036, 'new_value': 5574}]
2025-06-12 15:00:42,624 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ21
2025-06-12 15:00:43,078 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ21
2025-06-12 15:00:43,078 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52596.7, 'new_value': 57111.3}, {'field': 'offline_amount', 'old_value': 7188.9, 'new_value': 7843.7}, {'field': 'total_amount', 'old_value': 59785.6, 'new_value': 64955.0}, {'field': 'order_count', 'old_value': 4678, 'new_value': 5082}]
2025-06-12 15:00:43,078 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM031
2025-06-12 15:00:43,531 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM031
2025-06-12 15:00:43,531 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7150.51, 'new_value': 7766.15}, {'field': 'offline_amount', 'old_value': 47870.61, 'new_value': 52160.69}, {'field': 'total_amount', 'old_value': 55021.12, 'new_value': 59926.84}, {'field': 'order_count', 'old_value': 1521, 'new_value': 1707}]
2025-06-12 15:00:43,531 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM131
2025-06-12 15:00:43,999 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM131
2025-06-12 15:00:43,999 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 294007.04, 'new_value': 321695.95}, {'field': 'total_amount', 'old_value': 294007.04, 'new_value': 321695.95}, {'field': 'order_count', 'old_value': 1794, 'new_value': 1921}]
2025-06-12 15:00:43,999 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM11
2025-06-12 15:00:44,437 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM11
2025-06-12 15:00:44,437 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23559.0, 'new_value': 26559.0}, {'field': 'total_amount', 'old_value': 29719.0, 'new_value': 32719.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-06-12 15:00:44,437 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMB1
2025-06-12 15:00:44,921 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMB1
2025-06-12 15:00:44,921 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18380.6, 'new_value': 19679.77}, {'field': 'total_amount', 'old_value': 18380.6, 'new_value': 19679.77}, {'field': 'order_count', 'old_value': 744, 'new_value': 807}]
2025-06-12 15:00:44,921 - INFO - 日期 2025-06 处理完成 - 更新: 20 条，插入: 0 条，错误: 0 条
2025-06-12 15:00:44,921 - INFO - 数据同步完成！更新: 20 条，插入: 0 条，错误: 0 条
2025-06-12 15:00:44,921 - INFO - =================同步完成====================
2025-06-12 18:00:02,307 - INFO - =================使用默认全量同步=============
2025-06-12 18:00:03,963 - INFO - MySQL查询成功，共获取 3930 条记录
2025-06-12 18:00:03,963 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-12 18:00:04,010 - INFO - 开始处理日期: 2025-01
2025-06-12 18:00:04,010 - INFO - Request Parameters - Page 1:
2025-06-12 18:00:04,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:04,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:05,260 - INFO - Response - Page 1:
2025-06-12 18:00:05,463 - INFO - 第 1 页获取到 100 条记录
2025-06-12 18:00:05,463 - INFO - Request Parameters - Page 2:
2025-06-12 18:00:05,463 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:05,463 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:06,041 - INFO - Response - Page 2:
2025-06-12 18:00:06,244 - INFO - 第 2 页获取到 100 条记录
2025-06-12 18:00:06,244 - INFO - Request Parameters - Page 3:
2025-06-12 18:00:06,244 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:06,244 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:06,775 - INFO - Response - Page 3:
2025-06-12 18:00:06,978 - INFO - 第 3 页获取到 100 条记录
2025-06-12 18:00:06,978 - INFO - Request Parameters - Page 4:
2025-06-12 18:00:06,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:06,978 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:07,869 - INFO - Response - Page 4:
2025-06-12 18:00:08,072 - INFO - 第 4 页获取到 100 条记录
2025-06-12 18:00:08,072 - INFO - Request Parameters - Page 5:
2025-06-12 18:00:08,072 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:08,072 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:08,588 - INFO - Response - Page 5:
2025-06-12 18:00:08,791 - INFO - 第 5 页获取到 100 条记录
2025-06-12 18:00:08,791 - INFO - Request Parameters - Page 6:
2025-06-12 18:00:08,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:08,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:09,291 - INFO - Response - Page 6:
2025-06-12 18:00:09,494 - INFO - 第 6 页获取到 100 条记录
2025-06-12 18:00:09,494 - INFO - Request Parameters - Page 7:
2025-06-12 18:00:09,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:09,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:10,072 - INFO - Response - Page 7:
2025-06-12 18:00:10,275 - INFO - 第 7 页获取到 82 条记录
2025-06-12 18:00:10,275 - INFO - 查询完成，共获取到 682 条记录
2025-06-12 18:00:10,275 - INFO - 获取到 682 条表单数据
2025-06-12 18:00:10,275 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-12 18:00:10,291 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 18:00:10,291 - INFO - 开始处理日期: 2025-02
2025-06-12 18:00:10,291 - INFO - Request Parameters - Page 1:
2025-06-12 18:00:10,291 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:10,291 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:10,838 - INFO - Response - Page 1:
2025-06-12 18:00:11,041 - INFO - 第 1 页获取到 100 条记录
2025-06-12 18:00:11,041 - INFO - Request Parameters - Page 2:
2025-06-12 18:00:11,041 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:11,041 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:11,509 - INFO - Response - Page 2:
2025-06-12 18:00:11,713 - INFO - 第 2 页获取到 100 条记录
2025-06-12 18:00:11,713 - INFO - Request Parameters - Page 3:
2025-06-12 18:00:11,713 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:11,713 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:12,259 - INFO - Response - Page 3:
2025-06-12 18:00:12,463 - INFO - 第 3 页获取到 100 条记录
2025-06-12 18:00:12,463 - INFO - Request Parameters - Page 4:
2025-06-12 18:00:12,463 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:12,463 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:13,072 - INFO - Response - Page 4:
2025-06-12 18:00:13,275 - INFO - 第 4 页获取到 100 条记录
2025-06-12 18:00:13,275 - INFO - Request Parameters - Page 5:
2025-06-12 18:00:13,275 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:13,275 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:13,838 - INFO - Response - Page 5:
2025-06-12 18:00:14,041 - INFO - 第 5 页获取到 100 条记录
2025-06-12 18:00:14,041 - INFO - Request Parameters - Page 6:
2025-06-12 18:00:14,041 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:14,041 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:14,556 - INFO - Response - Page 6:
2025-06-12 18:00:14,759 - INFO - 第 6 页获取到 100 条记录
2025-06-12 18:00:14,759 - INFO - Request Parameters - Page 7:
2025-06-12 18:00:14,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:14,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:15,197 - INFO - Response - Page 7:
2025-06-12 18:00:15,400 - INFO - 第 7 页获取到 70 条记录
2025-06-12 18:00:15,400 - INFO - 查询完成，共获取到 670 条记录
2025-06-12 18:00:15,400 - INFO - 获取到 670 条表单数据
2025-06-12 18:00:15,400 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-12 18:00:15,416 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 18:00:15,416 - INFO - 开始处理日期: 2025-03
2025-06-12 18:00:15,416 - INFO - Request Parameters - Page 1:
2025-06-12 18:00:15,416 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:15,416 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:15,916 - INFO - Response - Page 1:
2025-06-12 18:00:16,119 - INFO - 第 1 页获取到 100 条记录
2025-06-12 18:00:16,119 - INFO - Request Parameters - Page 2:
2025-06-12 18:00:16,119 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:16,119 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:16,650 - INFO - Response - Page 2:
2025-06-12 18:00:16,853 - INFO - 第 2 页获取到 100 条记录
2025-06-12 18:00:16,853 - INFO - Request Parameters - Page 3:
2025-06-12 18:00:16,853 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:16,853 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:17,384 - INFO - Response - Page 3:
2025-06-12 18:00:17,587 - INFO - 第 3 页获取到 100 条记录
2025-06-12 18:00:17,587 - INFO - Request Parameters - Page 4:
2025-06-12 18:00:17,587 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:17,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:18,087 - INFO - Response - Page 4:
2025-06-12 18:00:18,291 - INFO - 第 4 页获取到 100 条记录
2025-06-12 18:00:18,291 - INFO - Request Parameters - Page 5:
2025-06-12 18:00:18,291 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:18,291 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:18,900 - INFO - Response - Page 5:
2025-06-12 18:00:19,103 - INFO - 第 5 页获取到 100 条记录
2025-06-12 18:00:19,103 - INFO - Request Parameters - Page 6:
2025-06-12 18:00:19,103 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:19,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:19,634 - INFO - Response - Page 6:
2025-06-12 18:00:19,837 - INFO - 第 6 页获取到 100 条记录
2025-06-12 18:00:19,837 - INFO - Request Parameters - Page 7:
2025-06-12 18:00:19,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:19,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:20,322 - INFO - Response - Page 7:
2025-06-12 18:00:20,525 - INFO - 第 7 页获取到 61 条记录
2025-06-12 18:00:20,525 - INFO - 查询完成，共获取到 661 条记录
2025-06-12 18:00:20,525 - INFO - 获取到 661 条表单数据
2025-06-12 18:00:20,525 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-12 18:00:20,540 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 18:00:20,540 - INFO - 开始处理日期: 2025-04
2025-06-12 18:00:20,540 - INFO - Request Parameters - Page 1:
2025-06-12 18:00:20,540 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:20,540 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:21,103 - INFO - Response - Page 1:
2025-06-12 18:00:21,322 - INFO - 第 1 页获取到 100 条记录
2025-06-12 18:00:21,322 - INFO - Request Parameters - Page 2:
2025-06-12 18:00:21,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:21,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:21,869 - INFO - Response - Page 2:
2025-06-12 18:00:22,072 - INFO - 第 2 页获取到 100 条记录
2025-06-12 18:00:22,072 - INFO - Request Parameters - Page 3:
2025-06-12 18:00:22,072 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:22,072 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:22,572 - INFO - Response - Page 3:
2025-06-12 18:00:22,775 - INFO - 第 3 页获取到 100 条记录
2025-06-12 18:00:22,775 - INFO - Request Parameters - Page 4:
2025-06-12 18:00:22,775 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:22,775 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:23,259 - INFO - Response - Page 4:
2025-06-12 18:00:23,462 - INFO - 第 4 页获取到 100 条记录
2025-06-12 18:00:23,462 - INFO - Request Parameters - Page 5:
2025-06-12 18:00:23,462 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:23,462 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:23,993 - INFO - Response - Page 5:
2025-06-12 18:00:24,197 - INFO - 第 5 页获取到 100 条记录
2025-06-12 18:00:24,197 - INFO - Request Parameters - Page 6:
2025-06-12 18:00:24,197 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:24,197 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:24,728 - INFO - Response - Page 6:
2025-06-12 18:00:24,931 - INFO - 第 6 页获取到 100 条记录
2025-06-12 18:00:24,931 - INFO - Request Parameters - Page 7:
2025-06-12 18:00:24,931 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:24,931 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:25,447 - INFO - Response - Page 7:
2025-06-12 18:00:25,650 - INFO - 第 7 页获取到 56 条记录
2025-06-12 18:00:25,650 - INFO - 查询完成，共获取到 656 条记录
2025-06-12 18:00:25,650 - INFO - 获取到 656 条表单数据
2025-06-12 18:00:25,650 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-12 18:00:25,665 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 18:00:25,665 - INFO - 开始处理日期: 2025-05
2025-06-12 18:00:25,665 - INFO - Request Parameters - Page 1:
2025-06-12 18:00:25,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:25,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:26,197 - INFO - Response - Page 1:
2025-06-12 18:00:26,400 - INFO - 第 1 页获取到 100 条记录
2025-06-12 18:00:26,400 - INFO - Request Parameters - Page 2:
2025-06-12 18:00:26,400 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:26,400 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:26,915 - INFO - Response - Page 2:
2025-06-12 18:00:27,118 - INFO - 第 2 页获取到 100 条记录
2025-06-12 18:00:27,118 - INFO - Request Parameters - Page 3:
2025-06-12 18:00:27,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:27,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:27,571 - INFO - Response - Page 3:
2025-06-12 18:00:27,775 - INFO - 第 3 页获取到 100 条记录
2025-06-12 18:00:27,775 - INFO - Request Parameters - Page 4:
2025-06-12 18:00:27,775 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:27,775 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:28,306 - INFO - Response - Page 4:
2025-06-12 18:00:28,509 - INFO - 第 4 页获取到 100 条记录
2025-06-12 18:00:28,509 - INFO - Request Parameters - Page 5:
2025-06-12 18:00:28,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:28,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:29,118 - INFO - Response - Page 5:
2025-06-12 18:00:29,321 - INFO - 第 5 页获取到 100 条记录
2025-06-12 18:00:29,321 - INFO - Request Parameters - Page 6:
2025-06-12 18:00:29,321 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:29,321 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:29,900 - INFO - Response - Page 6:
2025-06-12 18:00:30,103 - INFO - 第 6 页获取到 100 条记录
2025-06-12 18:00:30,103 - INFO - Request Parameters - Page 7:
2025-06-12 18:00:30,103 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:30,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:30,509 - INFO - Response - Page 7:
2025-06-12 18:00:30,712 - INFO - 第 7 页获取到 40 条记录
2025-06-12 18:00:30,712 - INFO - 查询完成，共获取到 640 条记录
2025-06-12 18:00:30,712 - INFO - 获取到 640 条表单数据
2025-06-12 18:00:30,712 - INFO - 当前日期 2025-05 有 640 条MySQL数据需要处理
2025-06-12 18:00:30,728 - INFO - 开始更新记录 - 表单实例ID: FINST-TKF66981MO5WD3YX5FMY56GSE1OI37QRMTOBMG1
2025-06-12 18:00:31,196 - INFO - 更新表单数据成功: FINST-TKF66981MO5WD3YX5FMY56GSE1OI37QRMTOBMG1
2025-06-12 18:00:31,196 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 585207.58, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 283832.2, 'new_value': 845561.3}, {'field': 'total_amount', 'old_value': 869039.78, 'new_value': 845561.3}]
2025-06-12 18:00:31,212 - INFO - 开始更新记录 - 表单实例ID: FINST-VFF66XA1KQ2W2WLG87XYYBGH1GHN2ESWMTOBM5Z
2025-06-12 18:00:31,649 - INFO - 更新表单数据成功: FINST-VFF66XA1KQ2W2WLG87XYYBGH1GHN2ESWMTOBM5Z
2025-06-12 18:00:31,649 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14734.0, 'new_value': 15160.0}, {'field': 'total_amount', 'old_value': 14734.0, 'new_value': 15160.0}, {'field': 'order_count', 'old_value': 107, 'new_value': 110}]
2025-06-12 18:00:31,649 - INFO - 开始更新记录 - 表单实例ID: FINST-WBF66B81QA2WH8JHA7ECY85SHGL32MBZMTOBMT11
2025-06-12 18:00:32,165 - INFO - 更新表单数据成功: FINST-WBF66B81QA2WH8JHA7ECY85SHGL32MBZMTOBMT11
2025-06-12 18:00:32,165 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52791.75, 'new_value': 1479.5}, {'field': 'offline_amount', 'old_value': 1380661.18, 'new_value': 1433288.44}, {'field': 'total_amount', 'old_value': 1433452.93, 'new_value': 1434767.94}]
2025-06-12 18:00:32,165 - INFO - 开始更新记录 - 表单实例ID: FINST-WBF66B81QA2WH8JHA7ECY85SHGL32NBZMTOBMN31
2025-06-12 18:00:32,618 - INFO - 更新表单数据成功: FINST-WBF66B81QA2WH8JHA7ECY85SHGL32NBZMTOBMN31
2025-06-12 18:00:32,618 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 583747.0, 'new_value': 470213.67}, {'field': 'total_amount', 'old_value': 583747.0, 'new_value': 470213.67}]
2025-06-12 18:00:32,634 - INFO - 开始更新记录 - 表单实例ID: FINST-RTA66X61LO5W721S827476KZ4D8Z1GU1NTOBMM1
2025-06-12 18:00:33,118 - INFO - 更新表单数据成功: FINST-RTA66X61LO5W721S827476KZ4D8Z1GU1NTOBMM1
2025-06-12 18:00:33,118 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48136.0, 'new_value': 47137.0}, {'field': 'total_amount', 'old_value': 48136.0, 'new_value': 47137.0}]
2025-06-12 18:00:33,118 - INFO - 日期 2025-05 处理完成 - 更新: 5 条，插入: 0 条，错误: 0 条
2025-06-12 18:00:33,118 - INFO - 开始处理日期: 2025-06
2025-06-12 18:00:33,118 - INFO - Request Parameters - Page 1:
2025-06-12 18:00:33,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:33,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:33,759 - INFO - Response - Page 1:
2025-06-12 18:00:33,962 - INFO - 第 1 页获取到 100 条记录
2025-06-12 18:00:33,962 - INFO - Request Parameters - Page 2:
2025-06-12 18:00:33,962 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:33,962 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:34,462 - INFO - Response - Page 2:
2025-06-12 18:00:34,665 - INFO - 第 2 页获取到 100 条记录
2025-06-12 18:00:34,665 - INFO - Request Parameters - Page 3:
2025-06-12 18:00:34,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:34,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:35,196 - INFO - Response - Page 3:
2025-06-12 18:00:35,399 - INFO - 第 3 页获取到 100 条记录
2025-06-12 18:00:35,399 - INFO - Request Parameters - Page 4:
2025-06-12 18:00:35,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:35,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:35,946 - INFO - Response - Page 4:
2025-06-12 18:00:36,149 - INFO - 第 4 页获取到 100 条记录
2025-06-12 18:00:36,149 - INFO - Request Parameters - Page 5:
2025-06-12 18:00:36,149 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:36,149 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:36,634 - INFO - Response - Page 5:
2025-06-12 18:00:36,837 - INFO - 第 5 页获取到 100 条记录
2025-06-12 18:00:36,837 - INFO - Request Parameters - Page 6:
2025-06-12 18:00:36,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:36,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:37,368 - INFO - Response - Page 6:
2025-06-12 18:00:37,571 - INFO - 第 6 页获取到 100 条记录
2025-06-12 18:00:37,571 - INFO - Request Parameters - Page 7:
2025-06-12 18:00:37,571 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 18:00:37,571 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 18:00:37,915 - INFO - Response - Page 7:
2025-06-12 18:00:38,118 - INFO - 第 7 页获取到 21 条记录
2025-06-12 18:00:38,118 - INFO - 查询完成，共获取到 621 条记录
2025-06-12 18:00:38,118 - INFO - 获取到 621 条表单数据
2025-06-12 18:00:38,118 - INFO - 当前日期 2025-06 有 621 条MySQL数据需要处理
2025-06-12 18:00:38,118 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM001
2025-06-12 18:00:38,602 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM001
2025-06-12 18:00:38,602 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 581509.05, 'new_value': 637348.06}, {'field': 'total_amount', 'old_value': 581509.05, 'new_value': 637348.06}, {'field': 'order_count', 'old_value': 6698, 'new_value': 7169}]
2025-06-12 18:00:38,618 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJD
2025-06-12 18:00:39,024 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJD
2025-06-12 18:00:39,024 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 516.37, 'new_value': 866.37}, {'field': 'offline_amount', 'old_value': 7960.3, 'new_value': 8223.52}, {'field': 'total_amount', 'old_value': 8476.67, 'new_value': 9089.89}, {'field': 'order_count', 'old_value': 170, 'new_value': 183}]
2025-06-12 18:00:39,024 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWR
2025-06-12 18:00:39,430 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWR
2025-06-12 18:00:39,430 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25943.8, 'new_value': 29092.05}, {'field': 'offline_amount', 'old_value': 34986.43, 'new_value': 39951.69}, {'field': 'total_amount', 'old_value': 60930.23, 'new_value': 69043.74}, {'field': 'order_count', 'old_value': 3010, 'new_value': 3401}]
2025-06-12 18:00:39,446 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMR2
2025-06-12 18:00:39,837 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMR2
2025-06-12 18:00:39,837 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6288672.5, 'new_value': 6917539.75}, {'field': 'total_amount', 'old_value': 6288672.5, 'new_value': 6917539.75}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-06-12 18:00:39,837 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS2
2025-06-12 18:00:40,337 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS2
2025-06-12 18:00:40,337 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67331.0, 'new_value': 79491.0}, {'field': 'total_amount', 'old_value': 67331.0, 'new_value': 79491.0}, {'field': 'order_count', 'old_value': 312, 'new_value': 358}]
2025-06-12 18:00:40,337 - INFO - 日期 2025-06 处理完成 - 更新: 5 条，插入: 0 条，错误: 0 条
2025-06-12 18:00:40,337 - INFO - 数据同步完成！更新: 10 条，插入: 0 条，错误: 0 条
2025-06-12 18:00:40,337 - INFO - =================同步完成====================
2025-06-12 21:00:02,712 - INFO - =================使用默认全量同步=============
2025-06-12 21:00:04,383 - INFO - MySQL查询成功，共获取 3930 条记录
2025-06-12 21:00:04,383 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-12 21:00:04,415 - INFO - 开始处理日期: 2025-01
2025-06-12 21:00:04,415 - INFO - Request Parameters - Page 1:
2025-06-12 21:00:04,415 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:04,415 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:05,962 - INFO - Response - Page 1:
2025-06-12 21:00:06,165 - INFO - 第 1 页获取到 100 条记录
2025-06-12 21:00:06,165 - INFO - Request Parameters - Page 2:
2025-06-12 21:00:06,165 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:06,165 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:06,774 - INFO - Response - Page 2:
2025-06-12 21:00:06,977 - INFO - 第 2 页获取到 100 条记录
2025-06-12 21:00:06,977 - INFO - Request Parameters - Page 3:
2025-06-12 21:00:06,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:06,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:07,493 - INFO - Response - Page 3:
2025-06-12 21:00:07,696 - INFO - 第 3 页获取到 100 条记录
2025-06-12 21:00:07,696 - INFO - Request Parameters - Page 4:
2025-06-12 21:00:07,696 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:07,696 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:08,196 - INFO - Response - Page 4:
2025-06-12 21:00:08,399 - INFO - 第 4 页获取到 100 条记录
2025-06-12 21:00:08,399 - INFO - Request Parameters - Page 5:
2025-06-12 21:00:08,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:08,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:09,008 - INFO - Response - Page 5:
2025-06-12 21:00:09,212 - INFO - 第 5 页获取到 100 条记录
2025-06-12 21:00:09,212 - INFO - Request Parameters - Page 6:
2025-06-12 21:00:09,212 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:09,212 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:09,758 - INFO - Response - Page 6:
2025-06-12 21:00:09,962 - INFO - 第 6 页获取到 100 条记录
2025-06-12 21:00:09,962 - INFO - Request Parameters - Page 7:
2025-06-12 21:00:09,962 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:09,962 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:10,493 - INFO - Response - Page 7:
2025-06-12 21:00:10,696 - INFO - 第 7 页获取到 82 条记录
2025-06-12 21:00:10,696 - INFO - 查询完成，共获取到 682 条记录
2025-06-12 21:00:10,696 - INFO - 获取到 682 条表单数据
2025-06-12 21:00:10,696 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-12 21:00:10,712 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 21:00:10,712 - INFO - 开始处理日期: 2025-02
2025-06-12 21:00:10,712 - INFO - Request Parameters - Page 1:
2025-06-12 21:00:10,712 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:10,712 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:11,227 - INFO - Response - Page 1:
2025-06-12 21:00:11,430 - INFO - 第 1 页获取到 100 条记录
2025-06-12 21:00:11,430 - INFO - Request Parameters - Page 2:
2025-06-12 21:00:11,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:11,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:12,024 - INFO - Response - Page 2:
2025-06-12 21:00:12,227 - INFO - 第 2 页获取到 100 条记录
2025-06-12 21:00:12,227 - INFO - Request Parameters - Page 3:
2025-06-12 21:00:12,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:12,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:12,712 - INFO - Response - Page 3:
2025-06-12 21:00:12,915 - INFO - 第 3 页获取到 100 条记录
2025-06-12 21:00:12,915 - INFO - Request Parameters - Page 4:
2025-06-12 21:00:12,915 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:12,915 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:13,384 - INFO - Response - Page 4:
2025-06-12 21:00:13,587 - INFO - 第 4 页获取到 100 条记录
2025-06-12 21:00:13,587 - INFO - Request Parameters - Page 5:
2025-06-12 21:00:13,587 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:13,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:14,165 - INFO - Response - Page 5:
2025-06-12 21:00:14,368 - INFO - 第 5 页获取到 100 条记录
2025-06-12 21:00:14,368 - INFO - Request Parameters - Page 6:
2025-06-12 21:00:14,368 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:14,368 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:14,884 - INFO - Response - Page 6:
2025-06-12 21:00:15,087 - INFO - 第 6 页获取到 100 条记录
2025-06-12 21:00:15,087 - INFO - Request Parameters - Page 7:
2025-06-12 21:00:15,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:15,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:15,555 - INFO - Response - Page 7:
2025-06-12 21:00:15,759 - INFO - 第 7 页获取到 70 条记录
2025-06-12 21:00:15,759 - INFO - 查询完成，共获取到 670 条记录
2025-06-12 21:00:15,759 - INFO - 获取到 670 条表单数据
2025-06-12 21:00:15,759 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-12 21:00:15,774 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 21:00:15,774 - INFO - 开始处理日期: 2025-03
2025-06-12 21:00:15,774 - INFO - Request Parameters - Page 1:
2025-06-12 21:00:15,774 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:15,774 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:16,384 - INFO - Response - Page 1:
2025-06-12 21:00:16,602 - INFO - 第 1 页获取到 100 条记录
2025-06-12 21:00:16,602 - INFO - Request Parameters - Page 2:
2025-06-12 21:00:16,602 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:16,602 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:17,118 - INFO - Response - Page 2:
2025-06-12 21:00:17,321 - INFO - 第 2 页获取到 100 条记录
2025-06-12 21:00:17,321 - INFO - Request Parameters - Page 3:
2025-06-12 21:00:17,321 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:17,321 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:17,821 - INFO - Response - Page 3:
2025-06-12 21:00:18,024 - INFO - 第 3 页获取到 100 条记录
2025-06-12 21:00:18,024 - INFO - Request Parameters - Page 4:
2025-06-12 21:00:18,024 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:18,024 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:18,524 - INFO - Response - Page 4:
2025-06-12 21:00:18,727 - INFO - 第 4 页获取到 100 条记录
2025-06-12 21:00:18,727 - INFO - Request Parameters - Page 5:
2025-06-12 21:00:18,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:18,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:19,259 - INFO - Response - Page 5:
2025-06-12 21:00:19,462 - INFO - 第 5 页获取到 100 条记录
2025-06-12 21:00:19,462 - INFO - Request Parameters - Page 6:
2025-06-12 21:00:19,462 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:19,462 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:19,930 - INFO - Response - Page 6:
2025-06-12 21:00:20,134 - INFO - 第 6 页获取到 100 条记录
2025-06-12 21:00:20,134 - INFO - Request Parameters - Page 7:
2025-06-12 21:00:20,134 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:20,134 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:20,602 - INFO - Response - Page 7:
2025-06-12 21:00:20,806 - INFO - 第 7 页获取到 61 条记录
2025-06-12 21:00:20,806 - INFO - 查询完成，共获取到 661 条记录
2025-06-12 21:00:20,806 - INFO - 获取到 661 条表单数据
2025-06-12 21:00:20,806 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-12 21:00:20,821 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 21:00:20,821 - INFO - 开始处理日期: 2025-04
2025-06-12 21:00:20,821 - INFO - Request Parameters - Page 1:
2025-06-12 21:00:20,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:20,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:21,306 - INFO - Response - Page 1:
2025-06-12 21:00:21,509 - INFO - 第 1 页获取到 100 条记录
2025-06-12 21:00:21,509 - INFO - Request Parameters - Page 2:
2025-06-12 21:00:21,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:21,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:22,087 - INFO - Response - Page 2:
2025-06-12 21:00:22,290 - INFO - 第 2 页获取到 100 条记录
2025-06-12 21:00:22,290 - INFO - Request Parameters - Page 3:
2025-06-12 21:00:22,290 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:22,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:22,790 - INFO - Response - Page 3:
2025-06-12 21:00:22,993 - INFO - 第 3 页获取到 100 条记录
2025-06-12 21:00:22,993 - INFO - Request Parameters - Page 4:
2025-06-12 21:00:22,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:22,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:23,431 - INFO - Response - Page 4:
2025-06-12 21:00:23,634 - INFO - 第 4 页获取到 100 条记录
2025-06-12 21:00:23,634 - INFO - Request Parameters - Page 5:
2025-06-12 21:00:23,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:23,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:24,196 - INFO - Response - Page 5:
2025-06-12 21:00:24,399 - INFO - 第 5 页获取到 100 条记录
2025-06-12 21:00:24,399 - INFO - Request Parameters - Page 6:
2025-06-12 21:00:24,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:24,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:24,915 - INFO - Response - Page 6:
2025-06-12 21:00:25,118 - INFO - 第 6 页获取到 100 条记录
2025-06-12 21:00:25,118 - INFO - Request Parameters - Page 7:
2025-06-12 21:00:25,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:25,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:25,540 - INFO - Response - Page 7:
2025-06-12 21:00:25,743 - INFO - 第 7 页获取到 56 条记录
2025-06-12 21:00:25,743 - INFO - 查询完成，共获取到 656 条记录
2025-06-12 21:00:25,743 - INFO - 获取到 656 条表单数据
2025-06-12 21:00:25,743 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-12 21:00:25,759 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 21:00:25,759 - INFO - 开始处理日期: 2025-05
2025-06-12 21:00:25,759 - INFO - Request Parameters - Page 1:
2025-06-12 21:00:25,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:25,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:26,243 - INFO - Response - Page 1:
2025-06-12 21:00:26,446 - INFO - 第 1 页获取到 100 条记录
2025-06-12 21:00:26,446 - INFO - Request Parameters - Page 2:
2025-06-12 21:00:26,446 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:26,446 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:26,977 - INFO - Response - Page 2:
2025-06-12 21:00:27,181 - INFO - 第 2 页获取到 100 条记录
2025-06-12 21:00:27,181 - INFO - Request Parameters - Page 3:
2025-06-12 21:00:27,181 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:27,181 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:27,681 - INFO - Response - Page 3:
2025-06-12 21:00:27,884 - INFO - 第 3 页获取到 100 条记录
2025-06-12 21:00:27,884 - INFO - Request Parameters - Page 4:
2025-06-12 21:00:27,884 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:27,884 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:28,352 - INFO - Response - Page 4:
2025-06-12 21:00:28,556 - INFO - 第 4 页获取到 100 条记录
2025-06-12 21:00:28,556 - INFO - Request Parameters - Page 5:
2025-06-12 21:00:28,556 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:28,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:29,118 - INFO - Response - Page 5:
2025-06-12 21:00:29,321 - INFO - 第 5 页获取到 100 条记录
2025-06-12 21:00:29,321 - INFO - Request Parameters - Page 6:
2025-06-12 21:00:29,321 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:29,321 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:29,915 - INFO - Response - Page 6:
2025-06-12 21:00:30,118 - INFO - 第 6 页获取到 100 条记录
2025-06-12 21:00:30,118 - INFO - Request Parameters - Page 7:
2025-06-12 21:00:30,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:30,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:30,556 - INFO - Response - Page 7:
2025-06-12 21:00:30,759 - INFO - 第 7 页获取到 40 条记录
2025-06-12 21:00:30,759 - INFO - 查询完成，共获取到 640 条记录
2025-06-12 21:00:30,759 - INFO - 获取到 640 条表单数据
2025-06-12 21:00:30,759 - INFO - 当前日期 2025-05 有 640 条MySQL数据需要处理
2025-06-12 21:00:30,774 - INFO - 开始更新记录 - 表单实例ID: FINST-WBF66B81QA2WH8JHA7ECY85SHGL32MBZMTOBMY11
2025-06-12 21:00:31,306 - INFO - 更新表单数据成功: FINST-WBF66B81QA2WH8JHA7ECY85SHGL32MBZMTOBMY11
2025-06-12 21:00:31,306 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66428.35, 'new_value': 68377.5}, {'field': 'total_amount', 'old_value': 69869.15, 'new_value': 71818.3}, {'field': 'order_count', 'old_value': 263, 'new_value': 271}]
2025-06-12 21:00:31,306 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-06-12 21:00:31,306 - INFO - 开始处理日期: 2025-06
2025-06-12 21:00:31,306 - INFO - Request Parameters - Page 1:
2025-06-12 21:00:31,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:31,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:31,837 - INFO - Response - Page 1:
2025-06-12 21:00:32,040 - INFO - 第 1 页获取到 100 条记录
2025-06-12 21:00:32,040 - INFO - Request Parameters - Page 2:
2025-06-12 21:00:32,040 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:32,040 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:32,509 - INFO - Response - Page 2:
2025-06-12 21:00:32,712 - INFO - 第 2 页获取到 100 条记录
2025-06-12 21:00:32,712 - INFO - Request Parameters - Page 3:
2025-06-12 21:00:32,712 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:32,712 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:33,228 - INFO - Response - Page 3:
2025-06-12 21:00:33,431 - INFO - 第 3 页获取到 100 条记录
2025-06-12 21:00:33,431 - INFO - Request Parameters - Page 4:
2025-06-12 21:00:33,431 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:33,431 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:33,915 - INFO - Response - Page 4:
2025-06-12 21:00:34,118 - INFO - 第 4 页获取到 100 条记录
2025-06-12 21:00:34,118 - INFO - Request Parameters - Page 5:
2025-06-12 21:00:34,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:34,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:34,618 - INFO - Response - Page 5:
2025-06-12 21:00:34,821 - INFO - 第 5 页获取到 100 条记录
2025-06-12 21:00:34,821 - INFO - Request Parameters - Page 6:
2025-06-12 21:00:34,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:34,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:35,353 - INFO - Response - Page 6:
2025-06-12 21:00:35,556 - INFO - 第 6 页获取到 100 条记录
2025-06-12 21:00:35,556 - INFO - Request Parameters - Page 7:
2025-06-12 21:00:35,556 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-12 21:00:35,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-12 21:00:35,899 - INFO - Response - Page 7:
2025-06-12 21:00:36,103 - INFO - 第 7 页获取到 21 条记录
2025-06-12 21:00:36,103 - INFO - 查询完成，共获取到 621 条记录
2025-06-12 21:00:36,103 - INFO - 获取到 621 条表单数据
2025-06-12 21:00:36,103 - INFO - 当前日期 2025-06 有 621 条MySQL数据需要处理
2025-06-12 21:00:36,118 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-12 21:00:36,118 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-06-12 21:00:36,118 - INFO - =================同步完成====================
