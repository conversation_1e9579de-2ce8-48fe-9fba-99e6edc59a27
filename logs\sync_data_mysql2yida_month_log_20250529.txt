2025-05-29 00:00:02,006 - INFO - =================使用默认全量同步=============
2025-05-29 00:00:03,568 - INFO - MySQL查询成功，共获取 3303 条记录
2025-05-29 00:00:03,568 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-29 00:00:03,599 - INFO - 开始处理日期: 2025-01
2025-05-29 00:00:03,599 - INFO - Request Parameters - Page 1:
2025-05-29 00:00:03,599 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:00:03,599 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:00:04,552 - INFO - Response - Page 1:
2025-05-29 00:00:04,756 - INFO - 第 1 页获取到 100 条记录
2025-05-29 00:00:04,756 - INFO - Request Parameters - Page 2:
2025-05-29 00:00:04,756 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:00:04,756 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:00:05,677 - INFO - Response - Page 2:
2025-05-29 00:00:05,881 - INFO - 第 2 页获取到 100 条记录
2025-05-29 00:00:05,881 - INFO - Request Parameters - Page 3:
2025-05-29 00:00:05,881 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:00:05,881 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:00:06,599 - INFO - Response - Page 3:
2025-05-29 00:00:06,802 - INFO - 第 3 页获取到 100 条记录
2025-05-29 00:00:06,802 - INFO - Request Parameters - Page 4:
2025-05-29 00:00:06,802 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:00:06,802 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:00:07,318 - INFO - Response - Page 4:
2025-05-29 00:00:07,521 - INFO - 第 4 页获取到 100 条记录
2025-05-29 00:00:07,521 - INFO - Request Parameters - Page 5:
2025-05-29 00:00:07,521 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:00:07,521 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:00:08,146 - INFO - Response - Page 5:
2025-05-29 00:00:08,349 - INFO - 第 5 页获取到 100 条记录
2025-05-29 00:00:08,349 - INFO - Request Parameters - Page 6:
2025-05-29 00:00:08,349 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:00:08,349 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:00:09,240 - INFO - Response - Page 6:
2025-05-29 00:00:09,443 - INFO - 第 6 页获取到 100 条记录
2025-05-29 00:00:09,443 - INFO - Request Parameters - Page 7:
2025-05-29 00:00:09,443 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:00:09,443 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:00:09,974 - INFO - Response - Page 7:
2025-05-29 00:00:10,177 - INFO - 第 7 页获取到 82 条记录
2025-05-29 00:00:10,177 - INFO - 查询完成，共获取到 682 条记录
2025-05-29 00:00:10,177 - INFO - 获取到 682 条表单数据
2025-05-29 00:00:10,177 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-29 00:00:10,193 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 00:00:10,193 - INFO - 开始处理日期: 2025-02
2025-05-29 00:00:10,193 - INFO - Request Parameters - Page 1:
2025-05-29 00:00:10,193 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:00:10,193 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:00:10,709 - INFO - Response - Page 1:
2025-05-29 00:00:10,912 - INFO - 第 1 页获取到 100 条记录
2025-05-29 00:00:10,912 - INFO - Request Parameters - Page 2:
2025-05-29 00:00:10,912 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:00:10,912 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:00:11,380 - INFO - Response - Page 2:
2025-05-29 00:00:11,584 - INFO - 第 2 页获取到 100 条记录
2025-05-29 00:00:11,584 - INFO - Request Parameters - Page 3:
2025-05-29 00:00:11,584 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:00:11,584 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:00:12,146 - INFO - Response - Page 3:
2025-05-29 00:00:12,349 - INFO - 第 3 页获取到 100 条记录
2025-05-29 00:00:12,349 - INFO - Request Parameters - Page 4:
2025-05-29 00:00:12,349 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:00:12,349 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:00:12,818 - INFO - Response - Page 4:
2025-05-29 00:00:13,021 - INFO - 第 4 页获取到 100 条记录
2025-05-29 00:00:13,021 - INFO - Request Parameters - Page 5:
2025-05-29 00:00:13,021 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:00:13,021 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:00:13,740 - INFO - Response - Page 5:
2025-05-29 00:00:13,943 - INFO - 第 5 页获取到 100 条记录
2025-05-29 00:00:13,943 - INFO - Request Parameters - Page 6:
2025-05-29 00:00:13,943 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:00:13,943 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:00:14,537 - INFO - Response - Page 6:
2025-05-29 00:00:14,740 - INFO - 第 6 页获取到 100 条记录
2025-05-29 00:00:14,740 - INFO - Request Parameters - Page 7:
2025-05-29 00:00:14,740 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:00:14,740 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:00:15,177 - INFO - Response - Page 7:
2025-05-29 00:00:15,380 - INFO - 第 7 页获取到 70 条记录
2025-05-29 00:00:15,380 - INFO - 查询完成，共获取到 670 条记录
2025-05-29 00:00:15,380 - INFO - 获取到 670 条表单数据
2025-05-29 00:00:15,380 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-29 00:00:15,396 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 00:00:15,396 - INFO - 开始处理日期: 2025-03
2025-05-29 00:00:15,396 - INFO - Request Parameters - Page 1:
2025-05-29 00:00:15,396 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:00:15,396 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:00:15,974 - INFO - Response - Page 1:
2025-05-29 00:00:16,177 - INFO - 第 1 页获取到 100 条记录
2025-05-29 00:00:16,177 - INFO - Request Parameters - Page 2:
2025-05-29 00:00:16,177 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:00:16,177 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:00:16,724 - INFO - Response - Page 2:
2025-05-29 00:00:16,927 - INFO - 第 2 页获取到 100 条记录
2025-05-29 00:00:16,927 - INFO - Request Parameters - Page 3:
2025-05-29 00:00:16,927 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:00:16,927 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:00:17,474 - INFO - Response - Page 3:
2025-05-29 00:00:17,677 - INFO - 第 3 页获取到 100 条记录
2025-05-29 00:00:17,677 - INFO - Request Parameters - Page 4:
2025-05-29 00:00:17,677 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:00:17,677 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:00:18,162 - INFO - Response - Page 4:
2025-05-29 00:00:18,365 - INFO - 第 4 页获取到 100 条记录
2025-05-29 00:00:18,365 - INFO - Request Parameters - Page 5:
2025-05-29 00:00:18,365 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:00:18,365 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:00:18,865 - INFO - Response - Page 5:
2025-05-29 00:00:19,068 - INFO - 第 5 页获取到 100 条记录
2025-05-29 00:00:19,068 - INFO - Request Parameters - Page 6:
2025-05-29 00:00:19,068 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:00:19,068 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:00:19,568 - INFO - Response - Page 6:
2025-05-29 00:00:19,771 - INFO - 第 6 页获取到 100 条记录
2025-05-29 00:00:19,771 - INFO - Request Parameters - Page 7:
2025-05-29 00:00:19,771 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:00:19,771 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:00:20,224 - INFO - Response - Page 7:
2025-05-29 00:00:20,427 - INFO - 第 7 页获取到 61 条记录
2025-05-29 00:00:20,427 - INFO - 查询完成，共获取到 661 条记录
2025-05-29 00:00:20,427 - INFO - 获取到 661 条表单数据
2025-05-29 00:00:20,427 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-29 00:00:20,443 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 00:00:20,443 - INFO - 开始处理日期: 2025-04
2025-05-29 00:00:20,443 - INFO - Request Parameters - Page 1:
2025-05-29 00:00:20,443 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:00:20,443 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:00:20,959 - INFO - Response - Page 1:
2025-05-29 00:00:21,162 - INFO - 第 1 页获取到 100 条记录
2025-05-29 00:00:21,162 - INFO - Request Parameters - Page 2:
2025-05-29 00:00:21,162 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:00:21,162 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:00:21,693 - INFO - Response - Page 2:
2025-05-29 00:00:21,896 - INFO - 第 2 页获取到 100 条记录
2025-05-29 00:00:21,896 - INFO - Request Parameters - Page 3:
2025-05-29 00:00:21,896 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:00:21,896 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:00:22,365 - INFO - Response - Page 3:
2025-05-29 00:00:22,568 - INFO - 第 3 页获取到 100 条记录
2025-05-29 00:00:22,568 - INFO - Request Parameters - Page 4:
2025-05-29 00:00:22,568 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:00:22,568 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:00:23,115 - INFO - Response - Page 4:
2025-05-29 00:00:23,318 - INFO - 第 4 页获取到 100 条记录
2025-05-29 00:00:23,318 - INFO - Request Parameters - Page 5:
2025-05-29 00:00:23,318 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:00:23,318 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:00:23,802 - INFO - Response - Page 5:
2025-05-29 00:00:24,005 - INFO - 第 5 页获取到 100 条记录
2025-05-29 00:00:24,005 - INFO - Request Parameters - Page 6:
2025-05-29 00:00:24,005 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:00:24,005 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:00:24,443 - INFO - Response - Page 6:
2025-05-29 00:00:24,646 - INFO - 第 6 页获取到 100 条记录
2025-05-29 00:00:24,646 - INFO - Request Parameters - Page 7:
2025-05-29 00:00:24,646 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:00:24,646 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:00:25,068 - INFO - Response - Page 7:
2025-05-29 00:00:25,271 - INFO - 第 7 页获取到 56 条记录
2025-05-29 00:00:25,271 - INFO - 查询完成，共获取到 656 条记录
2025-05-29 00:00:25,271 - INFO - 获取到 656 条表单数据
2025-05-29 00:00:25,271 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-29 00:00:25,287 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 00:00:25,287 - INFO - 开始处理日期: 2025-05
2025-05-29 00:00:25,287 - INFO - Request Parameters - Page 1:
2025-05-29 00:00:25,287 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:00:25,287 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:00:25,802 - INFO - Response - Page 1:
2025-05-29 00:00:26,005 - INFO - 第 1 页获取到 100 条记录
2025-05-29 00:00:26,005 - INFO - Request Parameters - Page 2:
2025-05-29 00:00:26,005 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:00:26,005 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:00:26,521 - INFO - Response - Page 2:
2025-05-29 00:00:26,724 - INFO - 第 2 页获取到 100 条记录
2025-05-29 00:00:26,724 - INFO - Request Parameters - Page 3:
2025-05-29 00:00:26,724 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:00:26,724 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:00:27,302 - INFO - Response - Page 3:
2025-05-29 00:00:27,505 - INFO - 第 3 页获取到 100 条记录
2025-05-29 00:00:27,505 - INFO - Request Parameters - Page 4:
2025-05-29 00:00:27,505 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:00:27,505 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:00:27,974 - INFO - Response - Page 4:
2025-05-29 00:00:28,177 - INFO - 第 4 页获取到 100 条记录
2025-05-29 00:00:28,177 - INFO - Request Parameters - Page 5:
2025-05-29 00:00:28,177 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:00:28,177 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:00:28,755 - INFO - Response - Page 5:
2025-05-29 00:00:28,958 - INFO - 第 5 页获取到 100 条记录
2025-05-29 00:00:28,958 - INFO - Request Parameters - Page 6:
2025-05-29 00:00:28,958 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:00:28,958 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:00:29,443 - INFO - Response - Page 6:
2025-05-29 00:00:29,646 - INFO - 第 6 页获取到 100 条记录
2025-05-29 00:00:29,646 - INFO - Request Parameters - Page 7:
2025-05-29 00:00:29,646 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 00:00:29,646 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 00:00:30,068 - INFO - Response - Page 7:
2025-05-29 00:00:30,271 - INFO - 第 7 页获取到 34 条记录
2025-05-29 00:00:30,271 - INFO - 查询完成，共获取到 634 条记录
2025-05-29 00:00:30,271 - INFO - 获取到 634 条表单数据
2025-05-29 00:00:30,271 - INFO - 当前日期 2025-05 有 634 条MySQL数据需要处理
2025-05-29 00:00:30,271 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-29 00:00:30,755 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-29 00:00:30,755 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10900080.0, 'new_value': 11300080.0}, {'field': 'total_amount', 'old_value': 11000080.0, 'new_value': 11400080.0}, {'field': 'order_count', 'old_value': 54, 'new_value': 56}]
2025-05-29 00:00:30,755 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-29 00:00:31,177 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-29 00:00:31,177 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 124138.0, 'new_value': 127204.0}, {'field': 'offline_amount', 'old_value': 147297.28, 'new_value': 153740.28}, {'field': 'total_amount', 'old_value': 271435.28, 'new_value': 280944.28}, {'field': 'order_count', 'old_value': 5823, 'new_value': 6029}]
2025-05-29 00:00:31,177 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-29 00:00:31,646 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-29 00:00:31,646 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 88284.0, 'new_value': 91726.0}, {'field': 'offline_amount', 'old_value': 114125.0, 'new_value': 118258.0}, {'field': 'total_amount', 'old_value': 202409.0, 'new_value': 209984.0}, {'field': 'order_count', 'old_value': 4599, 'new_value': 4748}]
2025-05-29 00:00:31,646 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-29 00:00:32,130 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-29 00:00:32,130 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32710.2, 'new_value': 33026.2}, {'field': 'offline_amount', 'old_value': 176559.02, 'new_value': 189909.46}, {'field': 'total_amount', 'old_value': 209269.22, 'new_value': 222935.66}, {'field': 'order_count', 'old_value': 277, 'new_value': 292}]
2025-05-29 00:00:32,130 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-29 00:00:32,537 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-29 00:00:32,537 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55973.59, 'new_value': 57826.86}, {'field': 'offline_amount', 'old_value': 738963.27, 'new_value': 771524.53}, {'field': 'total_amount', 'old_value': 794936.86, 'new_value': 829351.39}, {'field': 'order_count', 'old_value': 3315, 'new_value': 3444}]
2025-05-29 00:00:32,537 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-29 00:00:33,052 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-29 00:00:33,052 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66538.0, 'new_value': 67537.0}, {'field': 'total_amount', 'old_value': 73352.16, 'new_value': 74351.16}, {'field': 'order_count', 'old_value': 466, 'new_value': 467}]
2025-05-29 00:00:33,052 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-29 00:00:33,505 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-29 00:00:33,505 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 65081.73, 'new_value': 67109.62}, {'field': 'offline_amount', 'old_value': 108715.39, 'new_value': 111933.64}, {'field': 'total_amount', 'old_value': 173797.12, 'new_value': 179043.26}, {'field': 'order_count', 'old_value': 6017, 'new_value': 6214}]
2025-05-29 00:00:33,505 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-29 00:00:34,068 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-29 00:00:34,068 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 348831.0, 'new_value': 364923.0}, {'field': 'total_amount', 'old_value': 348831.0, 'new_value': 364923.0}, {'field': 'order_count', 'old_value': 212, 'new_value': 227}]
2025-05-29 00:00:34,068 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-29 00:00:34,490 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-29 00:00:34,490 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24745.65, 'new_value': 25065.79}, {'field': 'total_amount', 'old_value': 24811.2, 'new_value': 25131.34}, {'field': 'order_count', 'old_value': 228, 'new_value': 231}]
2025-05-29 00:00:34,490 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-29 00:00:34,927 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-29 00:00:34,927 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 164490.0, 'new_value': 167459.0}, {'field': 'total_amount', 'old_value': 198236.15, 'new_value': 201205.15}, {'field': 'order_count', 'old_value': 37, 'new_value': 38}]
2025-05-29 00:00:34,927 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-29 00:00:35,349 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-29 00:00:35,349 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58642.0, 'new_value': 61727.0}, {'field': 'total_amount', 'old_value': 58990.0, 'new_value': 62075.0}, {'field': 'order_count', 'old_value': 121, 'new_value': 129}]
2025-05-29 00:00:35,349 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-29 00:00:35,787 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-29 00:00:35,787 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 913614.0, 'new_value': 944417.0}, {'field': 'total_amount', 'old_value': 913614.0, 'new_value': 944417.0}, {'field': 'order_count', 'old_value': 168, 'new_value': 173}]
2025-05-29 00:00:35,787 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-29 00:00:36,177 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-29 00:00:36,193 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 257966.3, 'new_value': 263046.93}, {'field': 'offline_amount', 'old_value': 112200.86, 'new_value': 112766.86}, {'field': 'total_amount', 'old_value': 370167.16, 'new_value': 375813.79}, {'field': 'order_count', 'old_value': 1491, 'new_value': 1519}]
2025-05-29 00:00:36,193 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-29 00:00:37,021 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-29 00:00:37,021 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24200.77, 'new_value': 24798.64}, {'field': 'offline_amount', 'old_value': 315043.51, 'new_value': 319783.23}, {'field': 'total_amount', 'old_value': 339244.28, 'new_value': 344581.87}, {'field': 'order_count', 'old_value': 1599, 'new_value': 1626}]
2025-05-29 00:00:37,021 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-29 00:00:37,458 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-29 00:00:37,458 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41141.02, 'new_value': 42761.96}, {'field': 'offline_amount', 'old_value': 586678.09, 'new_value': 596500.09}, {'field': 'total_amount', 'old_value': 627819.11, 'new_value': 639262.05}, {'field': 'order_count', 'old_value': 3338, 'new_value': 3382}]
2025-05-29 00:00:37,458 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-29 00:00:37,912 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-29 00:00:37,912 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 315410.0, 'new_value': 317741.0}, {'field': 'total_amount', 'old_value': 315410.0, 'new_value': 317741.0}, {'field': 'order_count', 'old_value': 264, 'new_value': 267}]
2025-05-29 00:00:37,912 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-29 00:00:38,349 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-29 00:00:38,349 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 338162.8, 'new_value': 342330.0}, {'field': 'total_amount', 'old_value': 338162.8, 'new_value': 342330.0}, {'field': 'order_count', 'old_value': 3488, 'new_value': 3521}]
2025-05-29 00:00:38,349 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-29 00:00:38,787 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-29 00:00:38,787 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1862931.08, 'new_value': 1913885.08}, {'field': 'total_amount', 'old_value': 1862931.08, 'new_value': 1913885.08}, {'field': 'order_count', 'old_value': 16196, 'new_value': 17032}]
2025-05-29 00:00:38,787 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-29 00:00:39,177 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-29 00:00:39,177 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 240176.16, 'new_value': 243025.76}, {'field': 'total_amount', 'old_value': 240182.16, 'new_value': 243031.76}, {'field': 'order_count', 'old_value': 444, 'new_value': 450}]
2025-05-29 00:00:39,177 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-29 00:00:39,568 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-29 00:00:39,568 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 134630.77, 'new_value': 147770.24}, {'field': 'total_amount', 'old_value': 649769.61, 'new_value': 662909.08}, {'field': 'order_count', 'old_value': 2588, 'new_value': 2620}]
2025-05-29 00:00:39,568 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-29 00:00:39,990 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-29 00:00:39,990 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 196054.0, 'new_value': 208382.0}, {'field': 'total_amount', 'old_value': 196187.0, 'new_value': 208515.0}, {'field': 'order_count', 'old_value': 144, 'new_value': 149}]
2025-05-29 00:00:39,990 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-29 00:00:40,396 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-29 00:00:40,396 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 111099.58, 'new_value': 112917.58}, {'field': 'total_amount', 'old_value': 116711.1, 'new_value': 118529.1}, {'field': 'order_count', 'old_value': 10766, 'new_value': 10929}]
2025-05-29 00:00:40,396 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-29 00:00:40,787 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-29 00:00:40,787 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79191.0, 'new_value': 81739.0}, {'field': 'total_amount', 'old_value': 79191.0, 'new_value': 81739.0}, {'field': 'order_count', 'old_value': 120, 'new_value': 127}]
2025-05-29 00:00:40,787 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYU
2025-05-29 00:00:41,224 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYU
2025-05-29 00:00:41,224 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32152.0, 'new_value': 34713.0}, {'field': 'total_amount', 'old_value': 32152.0, 'new_value': 34713.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-05-29 00:00:41,224 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-29 00:00:41,615 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-29 00:00:41,615 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 400619.72, 'new_value': 405228.01}, {'field': 'total_amount', 'old_value': 400619.72, 'new_value': 405228.01}, {'field': 'order_count', 'old_value': 1997, 'new_value': 2026}]
2025-05-29 00:00:41,615 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-29 00:00:42,021 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-29 00:00:42,021 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 84335.62, 'new_value': 87731.48}, {'field': 'offline_amount', 'old_value': 267158.15, 'new_value': 271726.85}, {'field': 'total_amount', 'old_value': 351493.77, 'new_value': 359458.33}, {'field': 'order_count', 'old_value': 4168, 'new_value': 4230}]
2025-05-29 00:00:42,021 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-29 00:00:42,458 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-29 00:00:42,458 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71605.3, 'new_value': 73038.1}, {'field': 'total_amount', 'old_value': 71605.3, 'new_value': 73038.1}, {'field': 'order_count', 'old_value': 1645, 'new_value': 1647}]
2025-05-29 00:00:42,458 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-29 00:00:42,912 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-29 00:00:42,912 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 893515.0, 'new_value': 902999.0}, {'field': 'total_amount', 'old_value': 893515.0, 'new_value': 902999.0}, {'field': 'order_count', 'old_value': 126, 'new_value': 128}]
2025-05-29 00:00:42,912 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBV
2025-05-29 00:00:43,365 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBV
2025-05-29 00:00:43,365 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26758.0, 'new_value': 28894.0}, {'field': 'total_amount', 'old_value': 26758.0, 'new_value': 28894.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 24}]
2025-05-29 00:00:43,365 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-29 00:00:43,849 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-29 00:00:43,849 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2296.0, 'new_value': 2472.0}, {'field': 'offline_amount', 'old_value': 623573.0, 'new_value': 627915.0}, {'field': 'total_amount', 'old_value': 625869.0, 'new_value': 630387.0}, {'field': 'order_count', 'old_value': 284, 'new_value': 287}]
2025-05-29 00:00:43,849 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-29 00:00:44,271 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-29 00:00:44,271 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 212222.0, 'new_value': 214395.0}, {'field': 'total_amount', 'old_value': 212222.0, 'new_value': 214395.0}, {'field': 'order_count', 'old_value': 3380, 'new_value': 3384}]
2025-05-29 00:00:44,271 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-29 00:00:44,661 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-29 00:00:44,661 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 320519.0, 'new_value': 323561.0}, {'field': 'total_amount', 'old_value': 320519.0, 'new_value': 323561.0}, {'field': 'order_count', 'old_value': 85, 'new_value': 87}]
2025-05-29 00:00:44,661 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-29 00:00:45,099 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-29 00:00:45,099 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 782409.03, 'new_value': 802683.23}, {'field': 'total_amount', 'old_value': 782409.03, 'new_value': 802683.23}, {'field': 'order_count', 'old_value': 5496, 'new_value': 5645}]
2025-05-29 00:00:45,099 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-29 00:00:45,490 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-29 00:00:45,490 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82641.0, 'new_value': 84396.0}, {'field': 'total_amount', 'old_value': 82641.0, 'new_value': 84396.0}, {'field': 'order_count', 'old_value': 221, 'new_value': 226}]
2025-05-29 00:00:45,490 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-29 00:00:45,912 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-29 00:00:45,912 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10309.91, 'new_value': 10376.81}, {'field': 'offline_amount', 'old_value': 17300.0, 'new_value': 21300.0}, {'field': 'total_amount', 'old_value': 27609.91, 'new_value': 31676.81}, {'field': 'order_count', 'old_value': 159, 'new_value': 161}]
2025-05-29 00:00:45,912 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-29 00:00:46,349 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-29 00:00:46,349 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 138277.6, 'new_value': 139560.7}, {'field': 'total_amount', 'old_value': 138277.6, 'new_value': 139560.7}, {'field': 'order_count', 'old_value': 272, 'new_value': 277}]
2025-05-29 00:00:46,349 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-29 00:00:46,865 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-29 00:00:46,865 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 123866.7, 'new_value': 125253.6}, {'field': 'offline_amount', 'old_value': 79784.78, 'new_value': 80031.78}, {'field': 'total_amount', 'old_value': 203651.48, 'new_value': 205285.38}, {'field': 'order_count', 'old_value': 1372, 'new_value': 1385}]
2025-05-29 00:00:46,865 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-29 00:00:47,302 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-29 00:00:47,302 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 143443.8, 'new_value': 147284.1}, {'field': 'offline_amount', 'old_value': 119789.8, 'new_value': 122308.7}, {'field': 'total_amount', 'old_value': 263233.6, 'new_value': 269592.8}, {'field': 'order_count', 'old_value': 6190, 'new_value': 6352}]
2025-05-29 00:00:47,302 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-29 00:00:47,708 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-29 00:00:47,708 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 264084.24, 'new_value': 267303.74}, {'field': 'total_amount', 'old_value': 264084.24, 'new_value': 267303.74}, {'field': 'order_count', 'old_value': 1622, 'new_value': 1646}]
2025-05-29 00:00:47,708 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-29 00:00:48,083 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-29 00:00:48,083 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 254268.93, 'new_value': 265363.98}, {'field': 'offline_amount', 'old_value': 779993.09, 'new_value': 794733.47}, {'field': 'total_amount', 'old_value': 1034262.02, 'new_value': 1060097.45}, {'field': 'order_count', 'old_value': 6225, 'new_value': 6385}]
2025-05-29 00:00:48,083 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-29 00:00:48,552 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-29 00:00:48,552 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35541.39, 'new_value': 37707.09}, {'field': 'offline_amount', 'old_value': 375245.25, 'new_value': 381831.65}, {'field': 'total_amount', 'old_value': 410786.64, 'new_value': 419538.74}, {'field': 'order_count', 'old_value': 10035, 'new_value': 10139}]
2025-05-29 00:00:48,552 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-29 00:00:48,943 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-29 00:00:48,943 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37575.0, 'new_value': 38024.0}, {'field': 'total_amount', 'old_value': 37934.0, 'new_value': 38383.0}, {'field': 'order_count', 'old_value': 55, 'new_value': 56}]
2025-05-29 00:00:48,943 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-29 00:00:49,349 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-29 00:00:49,349 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 220379.0, 'new_value': 227808.09}, {'field': 'offline_amount', 'old_value': 383946.92, 'new_value': 392946.92}, {'field': 'total_amount', 'old_value': 604325.92, 'new_value': 620755.01}, {'field': 'order_count', 'old_value': 1543, 'new_value': 1593}]
2025-05-29 00:00:49,349 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-29 00:00:49,865 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-29 00:00:49,865 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1979322.0, 'new_value': 2008431.0}, {'field': 'total_amount', 'old_value': 1979322.0, 'new_value': 2008431.0}, {'field': 'order_count', 'old_value': 7931, 'new_value': 8059}]
2025-05-29 00:00:49,865 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-29 00:00:50,318 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-29 00:00:50,318 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 285078.56, 'new_value': 286121.56}, {'field': 'total_amount', 'old_value': 285078.56, 'new_value': 286121.56}, {'field': 'order_count', 'old_value': 1626, 'new_value': 1636}]
2025-05-29 00:00:50,318 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-29 00:00:50,786 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-29 00:00:50,786 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 61084.76, 'new_value': 62865.86}, {'field': 'offline_amount', 'old_value': 47447.81, 'new_value': 48107.85}, {'field': 'total_amount', 'old_value': 108532.57, 'new_value': 110973.71}, {'field': 'order_count', 'old_value': 9149, 'new_value': 9382}]
2025-05-29 00:00:50,786 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-29 00:00:51,255 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-29 00:00:51,255 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 383333.9, 'new_value': 392579.9}, {'field': 'total_amount', 'old_value': 383333.9, 'new_value': 392579.9}, {'field': 'order_count', 'old_value': 8, 'new_value': 14}]
2025-05-29 00:00:51,255 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-29 00:00:51,661 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-29 00:00:51,661 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 636749.15, 'new_value': 646150.15}, {'field': 'total_amount', 'old_value': 636749.15, 'new_value': 646150.15}, {'field': 'order_count', 'old_value': 509, 'new_value': 563}]
2025-05-29 00:00:51,661 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-29 00:00:52,099 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-29 00:00:52,099 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5023.0, 'new_value': 5957.0}, {'field': 'offline_amount', 'old_value': 484011.0, 'new_value': 488051.0}, {'field': 'total_amount', 'old_value': 489034.0, 'new_value': 494008.0}, {'field': 'order_count', 'old_value': 98, 'new_value': 102}]
2025-05-29 00:00:52,099 - INFO - 日期 2025-05 处理完成 - 更新: 49 条，插入: 0 条，错误: 0 条
2025-05-29 00:00:52,099 - INFO - 数据同步完成！更新: 49 条，插入: 0 条，错误: 0 条
2025-05-29 00:00:52,099 - INFO - =================同步完成====================
2025-05-29 03:00:02,012 - INFO - =================使用默认全量同步=============
2025-05-29 03:00:03,512 - INFO - MySQL查询成功，共获取 3303 条记录
2025-05-29 03:00:03,512 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-29 03:00:03,528 - INFO - 开始处理日期: 2025-01
2025-05-29 03:00:03,543 - INFO - Request Parameters - Page 1:
2025-05-29 03:00:03,543 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:00:03,543 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:00:04,778 - INFO - Response - Page 1:
2025-05-29 03:00:04,981 - INFO - 第 1 页获取到 100 条记录
2025-05-29 03:00:04,981 - INFO - Request Parameters - Page 2:
2025-05-29 03:00:04,981 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:00:04,981 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:00:05,575 - INFO - Response - Page 2:
2025-05-29 03:00:05,778 - INFO - 第 2 页获取到 100 条记录
2025-05-29 03:00:05,778 - INFO - Request Parameters - Page 3:
2025-05-29 03:00:05,778 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:00:05,778 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:00:06,278 - INFO - Response - Page 3:
2025-05-29 03:00:06,481 - INFO - 第 3 页获取到 100 条记录
2025-05-29 03:00:06,481 - INFO - Request Parameters - Page 4:
2025-05-29 03:00:06,481 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:00:06,481 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:00:07,075 - INFO - Response - Page 4:
2025-05-29 03:00:07,278 - INFO - 第 4 页获取到 100 条记录
2025-05-29 03:00:07,278 - INFO - Request Parameters - Page 5:
2025-05-29 03:00:07,278 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:00:07,278 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:00:07,778 - INFO - Response - Page 5:
2025-05-29 03:00:07,981 - INFO - 第 5 页获取到 100 条记录
2025-05-29 03:00:07,981 - INFO - Request Parameters - Page 6:
2025-05-29 03:00:07,981 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:00:07,981 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:00:08,465 - INFO - Response - Page 6:
2025-05-29 03:00:08,668 - INFO - 第 6 页获取到 100 条记录
2025-05-29 03:00:08,668 - INFO - Request Parameters - Page 7:
2025-05-29 03:00:08,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:00:08,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:00:09,137 - INFO - Response - Page 7:
2025-05-29 03:00:09,340 - INFO - 第 7 页获取到 82 条记录
2025-05-29 03:00:09,340 - INFO - 查询完成，共获取到 682 条记录
2025-05-29 03:00:09,340 - INFO - 获取到 682 条表单数据
2025-05-29 03:00:09,340 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-29 03:00:09,356 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 03:00:09,356 - INFO - 开始处理日期: 2025-02
2025-05-29 03:00:09,356 - INFO - Request Parameters - Page 1:
2025-05-29 03:00:09,356 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:00:09,356 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:00:09,856 - INFO - Response - Page 1:
2025-05-29 03:00:10,059 - INFO - 第 1 页获取到 100 条记录
2025-05-29 03:00:10,059 - INFO - Request Parameters - Page 2:
2025-05-29 03:00:10,059 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:00:10,059 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:00:10,528 - INFO - Response - Page 2:
2025-05-29 03:00:10,731 - INFO - 第 2 页获取到 100 条记录
2025-05-29 03:00:10,731 - INFO - Request Parameters - Page 3:
2025-05-29 03:00:10,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:00:10,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:00:11,262 - INFO - Response - Page 3:
2025-05-29 03:00:11,465 - INFO - 第 3 页获取到 100 条记录
2025-05-29 03:00:11,465 - INFO - Request Parameters - Page 4:
2025-05-29 03:00:11,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:00:11,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:00:11,950 - INFO - Response - Page 4:
2025-05-29 03:00:12,153 - INFO - 第 4 页获取到 100 条记录
2025-05-29 03:00:12,153 - INFO - Request Parameters - Page 5:
2025-05-29 03:00:12,153 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:00:12,153 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:00:12,793 - INFO - Response - Page 5:
2025-05-29 03:00:12,996 - INFO - 第 5 页获取到 100 条记录
2025-05-29 03:00:12,996 - INFO - Request Parameters - Page 6:
2025-05-29 03:00:12,996 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:00:12,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:00:13,465 - INFO - Response - Page 6:
2025-05-29 03:00:13,668 - INFO - 第 6 页获取到 100 条记录
2025-05-29 03:00:13,668 - INFO - Request Parameters - Page 7:
2025-05-29 03:00:13,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:00:13,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:00:14,215 - INFO - Response - Page 7:
2025-05-29 03:00:14,418 - INFO - 第 7 页获取到 70 条记录
2025-05-29 03:00:14,418 - INFO - 查询完成，共获取到 670 条记录
2025-05-29 03:00:14,418 - INFO - 获取到 670 条表单数据
2025-05-29 03:00:14,418 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-29 03:00:14,434 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 03:00:14,434 - INFO - 开始处理日期: 2025-03
2025-05-29 03:00:14,434 - INFO - Request Parameters - Page 1:
2025-05-29 03:00:14,434 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:00:14,434 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:00:14,934 - INFO - Response - Page 1:
2025-05-29 03:00:15,137 - INFO - 第 1 页获取到 100 条记录
2025-05-29 03:00:15,137 - INFO - Request Parameters - Page 2:
2025-05-29 03:00:15,137 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:00:15,137 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:00:15,715 - INFO - Response - Page 2:
2025-05-29 03:00:15,918 - INFO - 第 2 页获取到 100 条记录
2025-05-29 03:00:15,918 - INFO - Request Parameters - Page 3:
2025-05-29 03:00:15,918 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:00:15,918 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:00:16,465 - INFO - Response - Page 3:
2025-05-29 03:00:16,668 - INFO - 第 3 页获取到 100 条记录
2025-05-29 03:00:16,668 - INFO - Request Parameters - Page 4:
2025-05-29 03:00:16,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:00:16,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:00:17,153 - INFO - Response - Page 4:
2025-05-29 03:00:17,356 - INFO - 第 4 页获取到 100 条记录
2025-05-29 03:00:17,356 - INFO - Request Parameters - Page 5:
2025-05-29 03:00:17,356 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:00:17,356 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:00:17,965 - INFO - Response - Page 5:
2025-05-29 03:00:18,168 - INFO - 第 5 页获取到 100 条记录
2025-05-29 03:00:18,168 - INFO - Request Parameters - Page 6:
2025-05-29 03:00:18,168 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:00:18,168 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:00:18,731 - INFO - Response - Page 6:
2025-05-29 03:00:18,934 - INFO - 第 6 页获取到 100 条记录
2025-05-29 03:00:18,934 - INFO - Request Parameters - Page 7:
2025-05-29 03:00:18,934 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:00:18,934 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:00:19,340 - INFO - Response - Page 7:
2025-05-29 03:00:19,543 - INFO - 第 7 页获取到 61 条记录
2025-05-29 03:00:19,543 - INFO - 查询完成，共获取到 661 条记录
2025-05-29 03:00:19,543 - INFO - 获取到 661 条表单数据
2025-05-29 03:00:19,543 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-29 03:00:19,559 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 03:00:19,559 - INFO - 开始处理日期: 2025-04
2025-05-29 03:00:19,559 - INFO - Request Parameters - Page 1:
2025-05-29 03:00:19,559 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:00:19,559 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:00:20,137 - INFO - Response - Page 1:
2025-05-29 03:00:20,340 - INFO - 第 1 页获取到 100 条记录
2025-05-29 03:00:20,340 - INFO - Request Parameters - Page 2:
2025-05-29 03:00:20,340 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:00:20,340 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:00:20,809 - INFO - Response - Page 2:
2025-05-29 03:00:21,028 - INFO - 第 2 页获取到 100 条记录
2025-05-29 03:00:21,028 - INFO - Request Parameters - Page 3:
2025-05-29 03:00:21,028 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:00:21,028 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:00:21,528 - INFO - Response - Page 3:
2025-05-29 03:00:21,731 - INFO - 第 3 页获取到 100 条记录
2025-05-29 03:00:21,731 - INFO - Request Parameters - Page 4:
2025-05-29 03:00:21,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:00:21,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:00:22,231 - INFO - Response - Page 4:
2025-05-29 03:00:22,434 - INFO - 第 4 页获取到 100 条记录
2025-05-29 03:00:22,434 - INFO - Request Parameters - Page 5:
2025-05-29 03:00:22,434 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:00:22,434 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:00:22,887 - INFO - Response - Page 5:
2025-05-29 03:00:23,090 - INFO - 第 5 页获取到 100 条记录
2025-05-29 03:00:23,090 - INFO - Request Parameters - Page 6:
2025-05-29 03:00:23,090 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:00:23,090 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:00:23,528 - INFO - Response - Page 6:
2025-05-29 03:00:23,731 - INFO - 第 6 页获取到 100 条记录
2025-05-29 03:00:23,731 - INFO - Request Parameters - Page 7:
2025-05-29 03:00:23,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:00:23,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:00:24,121 - INFO - Response - Page 7:
2025-05-29 03:00:24,324 - INFO - 第 7 页获取到 56 条记录
2025-05-29 03:00:24,324 - INFO - 查询完成，共获取到 656 条记录
2025-05-29 03:00:24,324 - INFO - 获取到 656 条表单数据
2025-05-29 03:00:24,324 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-29 03:00:24,340 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 03:00:24,340 - INFO - 开始处理日期: 2025-05
2025-05-29 03:00:24,340 - INFO - Request Parameters - Page 1:
2025-05-29 03:00:24,340 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:00:24,340 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:00:24,871 - INFO - Response - Page 1:
2025-05-29 03:00:25,074 - INFO - 第 1 页获取到 100 条记录
2025-05-29 03:00:25,074 - INFO - Request Parameters - Page 2:
2025-05-29 03:00:25,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:00:25,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:00:25,481 - INFO - Response - Page 2:
2025-05-29 03:00:25,684 - INFO - 第 2 页获取到 100 条记录
2025-05-29 03:00:25,684 - INFO - Request Parameters - Page 3:
2025-05-29 03:00:25,684 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:00:25,684 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:00:26,184 - INFO - Response - Page 3:
2025-05-29 03:00:26,387 - INFO - 第 3 页获取到 100 条记录
2025-05-29 03:00:26,387 - INFO - Request Parameters - Page 4:
2025-05-29 03:00:26,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:00:26,387 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:00:26,965 - INFO - Response - Page 4:
2025-05-29 03:00:27,168 - INFO - 第 4 页获取到 100 条记录
2025-05-29 03:00:27,168 - INFO - Request Parameters - Page 5:
2025-05-29 03:00:27,168 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:00:27,168 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:00:27,684 - INFO - Response - Page 5:
2025-05-29 03:00:27,887 - INFO - 第 5 页获取到 100 条记录
2025-05-29 03:00:27,887 - INFO - Request Parameters - Page 6:
2025-05-29 03:00:27,887 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:00:27,887 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:00:28,387 - INFO - Response - Page 6:
2025-05-29 03:00:28,590 - INFO - 第 6 页获取到 100 条记录
2025-05-29 03:00:28,590 - INFO - Request Parameters - Page 7:
2025-05-29 03:00:28,590 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 03:00:28,590 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 03:00:28,996 - INFO - Response - Page 7:
2025-05-29 03:00:29,199 - INFO - 第 7 页获取到 34 条记录
2025-05-29 03:00:29,199 - INFO - 查询完成，共获取到 634 条记录
2025-05-29 03:00:29,199 - INFO - 获取到 634 条表单数据
2025-05-29 03:00:29,199 - INFO - 当前日期 2025-05 有 634 条MySQL数据需要处理
2025-05-29 03:00:29,215 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 03:00:29,215 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 03:00:29,215 - INFO - =================同步完成====================
2025-05-29 06:00:01,972 - INFO - =================使用默认全量同步=============
2025-05-29 06:00:03,440 - INFO - MySQL查询成功，共获取 3303 条记录
2025-05-29 06:00:03,440 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-29 06:00:03,472 - INFO - 开始处理日期: 2025-01
2025-05-29 06:00:03,472 - INFO - Request Parameters - Page 1:
2025-05-29 06:00:03,472 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:00:03,472 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:00:04,690 - INFO - Response - Page 1:
2025-05-29 06:00:04,893 - INFO - 第 1 页获取到 100 条记录
2025-05-29 06:00:04,893 - INFO - Request Parameters - Page 2:
2025-05-29 06:00:04,893 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:00:04,893 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:00:05,393 - INFO - Response - Page 2:
2025-05-29 06:00:05,597 - INFO - 第 2 页获取到 100 条记录
2025-05-29 06:00:05,597 - INFO - Request Parameters - Page 3:
2025-05-29 06:00:05,597 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:00:05,597 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:00:06,143 - INFO - Response - Page 3:
2025-05-29 06:00:06,347 - INFO - 第 3 页获取到 100 条记录
2025-05-29 06:00:06,347 - INFO - Request Parameters - Page 4:
2025-05-29 06:00:06,347 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:00:06,347 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:00:07,081 - INFO - Response - Page 4:
2025-05-29 06:00:07,284 - INFO - 第 4 页获取到 100 条记录
2025-05-29 06:00:07,284 - INFO - Request Parameters - Page 5:
2025-05-29 06:00:07,284 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:00:07,284 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:00:07,800 - INFO - Response - Page 5:
2025-05-29 06:00:08,003 - INFO - 第 5 页获取到 100 条记录
2025-05-29 06:00:08,003 - INFO - Request Parameters - Page 6:
2025-05-29 06:00:08,003 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:00:08,003 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:00:08,487 - INFO - Response - Page 6:
2025-05-29 06:00:08,690 - INFO - 第 6 页获取到 100 条记录
2025-05-29 06:00:08,690 - INFO - Request Parameters - Page 7:
2025-05-29 06:00:08,690 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:00:08,690 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:00:09,175 - INFO - Response - Page 7:
2025-05-29 06:00:09,378 - INFO - 第 7 页获取到 82 条记录
2025-05-29 06:00:09,378 - INFO - 查询完成，共获取到 682 条记录
2025-05-29 06:00:09,378 - INFO - 获取到 682 条表单数据
2025-05-29 06:00:09,378 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-29 06:00:09,393 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 06:00:09,393 - INFO - 开始处理日期: 2025-02
2025-05-29 06:00:09,393 - INFO - Request Parameters - Page 1:
2025-05-29 06:00:09,393 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:00:09,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:00:09,956 - INFO - Response - Page 1:
2025-05-29 06:00:10,159 - INFO - 第 1 页获取到 100 条记录
2025-05-29 06:00:10,159 - INFO - Request Parameters - Page 2:
2025-05-29 06:00:10,159 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:00:10,159 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:00:10,643 - INFO - Response - Page 2:
2025-05-29 06:00:10,847 - INFO - 第 2 页获取到 100 条记录
2025-05-29 06:00:10,847 - INFO - Request Parameters - Page 3:
2025-05-29 06:00:10,847 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:00:10,847 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:00:11,456 - INFO - Response - Page 3:
2025-05-29 06:00:11,659 - INFO - 第 3 页获取到 100 条记录
2025-05-29 06:00:11,659 - INFO - Request Parameters - Page 4:
2025-05-29 06:00:11,659 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:00:11,659 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:00:12,159 - INFO - Response - Page 4:
2025-05-29 06:00:12,362 - INFO - 第 4 页获取到 100 条记录
2025-05-29 06:00:12,362 - INFO - Request Parameters - Page 5:
2025-05-29 06:00:12,362 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:00:12,362 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:00:12,909 - INFO - Response - Page 5:
2025-05-29 06:00:13,112 - INFO - 第 5 页获取到 100 条记录
2025-05-29 06:00:13,112 - INFO - Request Parameters - Page 6:
2025-05-29 06:00:13,112 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:00:13,112 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:00:13,643 - INFO - Response - Page 6:
2025-05-29 06:00:13,847 - INFO - 第 6 页获取到 100 条记录
2025-05-29 06:00:13,847 - INFO - Request Parameters - Page 7:
2025-05-29 06:00:13,847 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:00:13,847 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:00:14,268 - INFO - Response - Page 7:
2025-05-29 06:00:14,472 - INFO - 第 7 页获取到 70 条记录
2025-05-29 06:00:14,472 - INFO - 查询完成，共获取到 670 条记录
2025-05-29 06:00:14,472 - INFO - 获取到 670 条表单数据
2025-05-29 06:00:14,472 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-29 06:00:14,487 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 06:00:14,487 - INFO - 开始处理日期: 2025-03
2025-05-29 06:00:14,487 - INFO - Request Parameters - Page 1:
2025-05-29 06:00:14,487 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:00:14,487 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:00:14,956 - INFO - Response - Page 1:
2025-05-29 06:00:15,159 - INFO - 第 1 页获取到 100 条记录
2025-05-29 06:00:15,159 - INFO - Request Parameters - Page 2:
2025-05-29 06:00:15,159 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:00:15,159 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:00:15,675 - INFO - Response - Page 2:
2025-05-29 06:00:15,878 - INFO - 第 2 页获取到 100 条记录
2025-05-29 06:00:15,878 - INFO - Request Parameters - Page 3:
2025-05-29 06:00:15,878 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:00:15,878 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:00:16,378 - INFO - Response - Page 3:
2025-05-29 06:00:16,581 - INFO - 第 3 页获取到 100 条记录
2025-05-29 06:00:16,581 - INFO - Request Parameters - Page 4:
2025-05-29 06:00:16,581 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:00:16,581 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:00:17,097 - INFO - Response - Page 4:
2025-05-29 06:00:17,300 - INFO - 第 4 页获取到 100 条记录
2025-05-29 06:00:17,300 - INFO - Request Parameters - Page 5:
2025-05-29 06:00:17,300 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:00:17,300 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:00:17,862 - INFO - Response - Page 5:
2025-05-29 06:00:18,065 - INFO - 第 5 页获取到 100 条记录
2025-05-29 06:00:18,065 - INFO - Request Parameters - Page 6:
2025-05-29 06:00:18,065 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:00:18,065 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:00:18,565 - INFO - Response - Page 6:
2025-05-29 06:00:18,768 - INFO - 第 6 页获取到 100 条记录
2025-05-29 06:00:18,768 - INFO - Request Parameters - Page 7:
2025-05-29 06:00:18,768 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:00:18,768 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:00:19,175 - INFO - Response - Page 7:
2025-05-29 06:00:19,378 - INFO - 第 7 页获取到 61 条记录
2025-05-29 06:00:19,378 - INFO - 查询完成，共获取到 661 条记录
2025-05-29 06:00:19,378 - INFO - 获取到 661 条表单数据
2025-05-29 06:00:19,378 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-29 06:00:19,393 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 06:00:19,393 - INFO - 开始处理日期: 2025-04
2025-05-29 06:00:19,393 - INFO - Request Parameters - Page 1:
2025-05-29 06:00:19,393 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:00:19,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:00:19,909 - INFO - Response - Page 1:
2025-05-29 06:00:20,112 - INFO - 第 1 页获取到 100 条记录
2025-05-29 06:00:20,112 - INFO - Request Parameters - Page 2:
2025-05-29 06:00:20,112 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:00:20,112 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:00:20,596 - INFO - Response - Page 2:
2025-05-29 06:00:20,800 - INFO - 第 2 页获取到 100 条记录
2025-05-29 06:00:20,800 - INFO - Request Parameters - Page 3:
2025-05-29 06:00:20,800 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:00:20,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:00:21,284 - INFO - Response - Page 3:
2025-05-29 06:00:21,487 - INFO - 第 3 页获取到 100 条记录
2025-05-29 06:00:21,487 - INFO - Request Parameters - Page 4:
2025-05-29 06:00:21,487 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:00:21,487 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:00:21,971 - INFO - Response - Page 4:
2025-05-29 06:00:22,175 - INFO - 第 4 页获取到 100 条记录
2025-05-29 06:00:22,175 - INFO - Request Parameters - Page 5:
2025-05-29 06:00:22,175 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:00:22,175 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:00:22,596 - INFO - Response - Page 5:
2025-05-29 06:00:22,800 - INFO - 第 5 页获取到 100 条记录
2025-05-29 06:00:22,800 - INFO - Request Parameters - Page 6:
2025-05-29 06:00:22,800 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:00:22,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:00:23,315 - INFO - Response - Page 6:
2025-05-29 06:00:23,518 - INFO - 第 6 页获取到 100 条记录
2025-05-29 06:00:23,518 - INFO - Request Parameters - Page 7:
2025-05-29 06:00:23,518 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:00:23,518 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:00:23,925 - INFO - Response - Page 7:
2025-05-29 06:00:24,128 - INFO - 第 7 页获取到 56 条记录
2025-05-29 06:00:24,128 - INFO - 查询完成，共获取到 656 条记录
2025-05-29 06:00:24,128 - INFO - 获取到 656 条表单数据
2025-05-29 06:00:24,128 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-29 06:00:24,143 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 06:00:24,143 - INFO - 开始处理日期: 2025-05
2025-05-29 06:00:24,143 - INFO - Request Parameters - Page 1:
2025-05-29 06:00:24,143 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:00:24,143 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:00:24,643 - INFO - Response - Page 1:
2025-05-29 06:00:24,846 - INFO - 第 1 页获取到 100 条记录
2025-05-29 06:00:24,846 - INFO - Request Parameters - Page 2:
2025-05-29 06:00:24,846 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:00:24,846 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:00:25,300 - INFO - Response - Page 2:
2025-05-29 06:00:25,503 - INFO - 第 2 页获取到 100 条记录
2025-05-29 06:00:25,503 - INFO - Request Parameters - Page 3:
2025-05-29 06:00:25,503 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:00:25,503 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:00:26,018 - INFO - Response - Page 3:
2025-05-29 06:00:26,221 - INFO - 第 3 页获取到 100 条记录
2025-05-29 06:00:26,221 - INFO - Request Parameters - Page 4:
2025-05-29 06:00:26,221 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:00:26,221 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:00:26,690 - INFO - Response - Page 4:
2025-05-29 06:00:26,893 - INFO - 第 4 页获取到 100 条记录
2025-05-29 06:00:26,893 - INFO - Request Parameters - Page 5:
2025-05-29 06:00:26,893 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:00:26,893 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:00:27,362 - INFO - Response - Page 5:
2025-05-29 06:00:27,565 - INFO - 第 5 页获取到 100 条记录
2025-05-29 06:00:27,565 - INFO - Request Parameters - Page 6:
2025-05-29 06:00:27,565 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:00:27,565 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:00:28,065 - INFO - Response - Page 6:
2025-05-29 06:00:28,268 - INFO - 第 6 页获取到 100 条记录
2025-05-29 06:00:28,268 - INFO - Request Parameters - Page 7:
2025-05-29 06:00:28,268 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 06:00:28,268 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 06:00:28,628 - INFO - Response - Page 7:
2025-05-29 06:00:28,831 - INFO - 第 7 页获取到 34 条记录
2025-05-29 06:00:28,831 - INFO - 查询完成，共获取到 634 条记录
2025-05-29 06:00:28,831 - INFO - 获取到 634 条表单数据
2025-05-29 06:00:28,831 - INFO - 当前日期 2025-05 有 634 条MySQL数据需要处理
2025-05-29 06:00:28,846 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 06:00:28,846 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 06:00:28,846 - INFO - =================同步完成====================
2025-05-29 09:00:02,040 - INFO - =================使用默认全量同步=============
2025-05-29 09:00:03,556 - INFO - MySQL查询成功，共获取 3304 条记录
2025-05-29 09:00:03,556 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-29 09:00:03,587 - INFO - 开始处理日期: 2025-01
2025-05-29 09:00:03,587 - INFO - Request Parameters - Page 1:
2025-05-29 09:00:03,587 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:00:03,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:00:04,822 - INFO - Response - Page 1:
2025-05-29 09:00:05,025 - INFO - 第 1 页获取到 100 条记录
2025-05-29 09:00:05,025 - INFO - Request Parameters - Page 2:
2025-05-29 09:00:05,025 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:00:05,025 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:00:05,540 - INFO - Response - Page 2:
2025-05-29 09:00:05,743 - INFO - 第 2 页获取到 100 条记录
2025-05-29 09:00:05,743 - INFO - Request Parameters - Page 3:
2025-05-29 09:00:05,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:00:05,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:00:06,259 - INFO - Response - Page 3:
2025-05-29 09:00:06,462 - INFO - 第 3 页获取到 100 条记录
2025-05-29 09:00:06,462 - INFO - Request Parameters - Page 4:
2025-05-29 09:00:06,462 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:00:06,462 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:00:07,025 - INFO - Response - Page 4:
2025-05-29 09:00:07,228 - INFO - 第 4 页获取到 100 条记录
2025-05-29 09:00:07,228 - INFO - Request Parameters - Page 5:
2025-05-29 09:00:07,228 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:00:07,228 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:00:07,978 - INFO - Response - Page 5:
2025-05-29 09:00:08,181 - INFO - 第 5 页获取到 100 条记录
2025-05-29 09:00:08,181 - INFO - Request Parameters - Page 6:
2025-05-29 09:00:08,181 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:00:08,181 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:00:08,665 - INFO - Response - Page 6:
2025-05-29 09:00:08,868 - INFO - 第 6 页获取到 100 条记录
2025-05-29 09:00:08,868 - INFO - Request Parameters - Page 7:
2025-05-29 09:00:08,868 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:00:08,868 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:00:09,368 - INFO - Response - Page 7:
2025-05-29 09:00:09,571 - INFO - 第 7 页获取到 82 条记录
2025-05-29 09:00:09,571 - INFO - 查询完成，共获取到 682 条记录
2025-05-29 09:00:09,571 - INFO - 获取到 682 条表单数据
2025-05-29 09:00:09,571 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-29 09:00:09,587 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 09:00:09,587 - INFO - 开始处理日期: 2025-02
2025-05-29 09:00:09,587 - INFO - Request Parameters - Page 1:
2025-05-29 09:00:09,587 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:00:09,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:00:10,118 - INFO - Response - Page 1:
2025-05-29 09:00:10,321 - INFO - 第 1 页获取到 100 条记录
2025-05-29 09:00:10,321 - INFO - Request Parameters - Page 2:
2025-05-29 09:00:10,321 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:00:10,321 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:00:10,790 - INFO - Response - Page 2:
2025-05-29 09:00:10,993 - INFO - 第 2 页获取到 100 条记录
2025-05-29 09:00:10,993 - INFO - Request Parameters - Page 3:
2025-05-29 09:00:10,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:00:10,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:00:11,509 - INFO - Response - Page 3:
2025-05-29 09:00:11,712 - INFO - 第 3 页获取到 100 条记录
2025-05-29 09:00:11,712 - INFO - Request Parameters - Page 4:
2025-05-29 09:00:11,712 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:00:11,712 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:00:12,181 - INFO - Response - Page 4:
2025-05-29 09:00:12,384 - INFO - 第 4 页获取到 100 条记录
2025-05-29 09:00:12,384 - INFO - Request Parameters - Page 5:
2025-05-29 09:00:12,384 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:00:12,384 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:00:12,868 - INFO - Response - Page 5:
2025-05-29 09:00:13,071 - INFO - 第 5 页获取到 100 条记录
2025-05-29 09:00:13,071 - INFO - Request Parameters - Page 6:
2025-05-29 09:00:13,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:00:13,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:00:13,618 - INFO - Response - Page 6:
2025-05-29 09:00:13,821 - INFO - 第 6 页获取到 100 条记录
2025-05-29 09:00:13,821 - INFO - Request Parameters - Page 7:
2025-05-29 09:00:13,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:00:13,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:00:14,384 - INFO - Response - Page 7:
2025-05-29 09:00:14,587 - INFO - 第 7 页获取到 70 条记录
2025-05-29 09:00:14,587 - INFO - 查询完成，共获取到 670 条记录
2025-05-29 09:00:14,587 - INFO - 获取到 670 条表单数据
2025-05-29 09:00:14,587 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-29 09:00:14,603 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 09:00:14,603 - INFO - 开始处理日期: 2025-03
2025-05-29 09:00:14,603 - INFO - Request Parameters - Page 1:
2025-05-29 09:00:14,603 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:00:14,603 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:00:15,118 - INFO - Response - Page 1:
2025-05-29 09:00:15,321 - INFO - 第 1 页获取到 100 条记录
2025-05-29 09:00:15,321 - INFO - Request Parameters - Page 2:
2025-05-29 09:00:15,321 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:00:15,321 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:00:15,821 - INFO - Response - Page 2:
2025-05-29 09:00:16,025 - INFO - 第 2 页获取到 100 条记录
2025-05-29 09:00:16,025 - INFO - Request Parameters - Page 3:
2025-05-29 09:00:16,025 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:00:16,025 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:00:16,665 - INFO - Response - Page 3:
2025-05-29 09:00:16,868 - INFO - 第 3 页获取到 100 条记录
2025-05-29 09:00:16,868 - INFO - Request Parameters - Page 4:
2025-05-29 09:00:16,868 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:00:16,868 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:00:17,431 - INFO - Response - Page 4:
2025-05-29 09:00:17,634 - INFO - 第 4 页获取到 100 条记录
2025-05-29 09:00:17,634 - INFO - Request Parameters - Page 5:
2025-05-29 09:00:17,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:00:17,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:00:18,134 - INFO - Response - Page 5:
2025-05-29 09:00:18,337 - INFO - 第 5 页获取到 100 条记录
2025-05-29 09:00:18,337 - INFO - Request Parameters - Page 6:
2025-05-29 09:00:18,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:00:18,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:00:18,821 - INFO - Response - Page 6:
2025-05-29 09:00:19,025 - INFO - 第 6 页获取到 100 条记录
2025-05-29 09:00:19,025 - INFO - Request Parameters - Page 7:
2025-05-29 09:00:19,025 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:00:19,025 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:00:19,478 - INFO - Response - Page 7:
2025-05-29 09:00:19,681 - INFO - 第 7 页获取到 61 条记录
2025-05-29 09:00:19,681 - INFO - 查询完成，共获取到 661 条记录
2025-05-29 09:00:19,681 - INFO - 获取到 661 条表单数据
2025-05-29 09:00:19,681 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-29 09:00:19,696 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 09:00:19,696 - INFO - 开始处理日期: 2025-04
2025-05-29 09:00:19,696 - INFO - Request Parameters - Page 1:
2025-05-29 09:00:19,696 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:00:19,696 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:00:20,290 - INFO - Response - Page 1:
2025-05-29 09:00:20,493 - INFO - 第 1 页获取到 100 条记录
2025-05-29 09:00:20,493 - INFO - Request Parameters - Page 2:
2025-05-29 09:00:20,493 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:00:20,493 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:00:21,134 - INFO - Response - Page 2:
2025-05-29 09:00:21,337 - INFO - 第 2 页获取到 100 条记录
2025-05-29 09:00:21,337 - INFO - Request Parameters - Page 3:
2025-05-29 09:00:21,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:00:21,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:00:21,806 - INFO - Response - Page 3:
2025-05-29 09:00:22,009 - INFO - 第 3 页获取到 100 条记录
2025-05-29 09:00:22,009 - INFO - Request Parameters - Page 4:
2025-05-29 09:00:22,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:00:22,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:00:22,525 - INFO - Response - Page 4:
2025-05-29 09:00:22,728 - INFO - 第 4 页获取到 100 条记录
2025-05-29 09:00:22,728 - INFO - Request Parameters - Page 5:
2025-05-29 09:00:22,728 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:00:22,728 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:00:23,196 - INFO - Response - Page 5:
2025-05-29 09:00:23,399 - INFO - 第 5 页获取到 100 条记录
2025-05-29 09:00:23,399 - INFO - Request Parameters - Page 6:
2025-05-29 09:00:23,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:00:23,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:00:23,946 - INFO - Response - Page 6:
2025-05-29 09:00:24,150 - INFO - 第 6 页获取到 100 条记录
2025-05-29 09:00:24,150 - INFO - Request Parameters - Page 7:
2025-05-29 09:00:24,150 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:00:24,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:00:24,587 - INFO - Response - Page 7:
2025-05-29 09:00:24,790 - INFO - 第 7 页获取到 56 条记录
2025-05-29 09:00:24,790 - INFO - 查询完成，共获取到 656 条记录
2025-05-29 09:00:24,790 - INFO - 获取到 656 条表单数据
2025-05-29 09:00:24,790 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-29 09:00:24,806 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 09:00:24,806 - INFO - 开始处理日期: 2025-05
2025-05-29 09:00:24,806 - INFO - Request Parameters - Page 1:
2025-05-29 09:00:24,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:00:24,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:00:25,274 - INFO - Response - Page 1:
2025-05-29 09:00:25,478 - INFO - 第 1 页获取到 100 条记录
2025-05-29 09:00:25,478 - INFO - Request Parameters - Page 2:
2025-05-29 09:00:25,478 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:00:25,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:00:25,915 - INFO - Response - Page 2:
2025-05-29 09:00:26,118 - INFO - 第 2 页获取到 100 条记录
2025-05-29 09:00:26,118 - INFO - Request Parameters - Page 3:
2025-05-29 09:00:26,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:00:26,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:00:26,649 - INFO - Response - Page 3:
2025-05-29 09:00:26,853 - INFO - 第 3 页获取到 100 条记录
2025-05-29 09:00:26,853 - INFO - Request Parameters - Page 4:
2025-05-29 09:00:26,853 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:00:26,853 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:00:27,415 - INFO - Response - Page 4:
2025-05-29 09:00:27,618 - INFO - 第 4 页获取到 100 条记录
2025-05-29 09:00:27,618 - INFO - Request Parameters - Page 5:
2025-05-29 09:00:27,618 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:00:27,618 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:00:28,103 - INFO - Response - Page 5:
2025-05-29 09:00:28,306 - INFO - 第 5 页获取到 100 条记录
2025-05-29 09:00:28,306 - INFO - Request Parameters - Page 6:
2025-05-29 09:00:28,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:00:28,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:00:28,946 - INFO - Response - Page 6:
2025-05-29 09:00:29,149 - INFO - 第 6 页获取到 100 条记录
2025-05-29 09:00:29,149 - INFO - Request Parameters - Page 7:
2025-05-29 09:00:29,149 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 09:00:29,149 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 09:00:29,603 - INFO - Response - Page 7:
2025-05-29 09:00:29,806 - INFO - 第 7 页获取到 34 条记录
2025-05-29 09:00:29,806 - INFO - 查询完成，共获取到 634 条记录
2025-05-29 09:00:29,806 - INFO - 获取到 634 条表单数据
2025-05-29 09:00:29,806 - INFO - 当前日期 2025-05 有 635 条MySQL数据需要处理
2025-05-29 09:00:29,806 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-29 09:00:30,306 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-29 09:00:30,306 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 400685.0, 'new_value': 411885.0}, {'field': 'total_amount', 'old_value': 400685.0, 'new_value': 411885.0}, {'field': 'order_count', 'old_value': 300, 'new_value': 307}]
2025-05-29 09:00:30,306 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-29 09:00:30,681 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-29 09:00:30,681 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 785530.98, 'new_value': 815759.98}, {'field': 'total_amount', 'old_value': 785530.98, 'new_value': 815759.98}, {'field': 'order_count', 'old_value': 2479, 'new_value': 2578}]
2025-05-29 09:00:30,681 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-29 09:00:31,134 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-29 09:00:31,134 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56303.78, 'new_value': 56392.78}, {'field': 'total_amount', 'old_value': 56303.78, 'new_value': 56392.78}, {'field': 'order_count', 'old_value': 132, 'new_value': 134}]
2025-05-29 09:00:31,134 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-29 09:00:31,556 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-29 09:00:31,556 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 422860.0, 'new_value': 433040.0}, {'field': 'total_amount', 'old_value': 422860.0, 'new_value': 433040.0}, {'field': 'order_count', 'old_value': 259, 'new_value': 266}]
2025-05-29 09:00:31,556 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-29 09:00:32,103 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-29 09:00:32,103 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 123329.0, 'new_value': 126330.0}, {'field': 'total_amount', 'old_value': 123329.0, 'new_value': 126330.0}, {'field': 'order_count', 'old_value': 139, 'new_value': 141}]
2025-05-29 09:00:32,103 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-29 09:00:32,681 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-29 09:00:32,681 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55480.0, 'new_value': 58440.0}, {'field': 'total_amount', 'old_value': 59600.0, 'new_value': 62560.0}, {'field': 'order_count', 'old_value': 582, 'new_value': 608}]
2025-05-29 09:00:32,681 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMKC
2025-05-29 09:00:33,196 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMKC
2025-05-29 09:00:33,196 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48122.0, 'new_value': 51102.0}, {'field': 'total_amount', 'old_value': 48122.0, 'new_value': 51102.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-05-29 09:00:33,196 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-29 09:00:33,712 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-29 09:00:33,712 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 293050.0, 'new_value': 295049.0}, {'field': 'total_amount', 'old_value': 293050.0, 'new_value': 295049.0}, {'field': 'order_count', 'old_value': 57, 'new_value': 58}]
2025-05-29 09:00:33,712 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-29 09:00:34,149 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-29 09:00:34,149 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 61870.69, 'new_value': 64327.68}, {'field': 'offline_amount', 'old_value': 121741.85, 'new_value': 127063.35}, {'field': 'total_amount', 'old_value': 183612.54, 'new_value': 191391.03}, {'field': 'order_count', 'old_value': 2102, 'new_value': 2197}]
2025-05-29 09:00:34,149 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-29 09:00:34,696 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-29 09:00:34,696 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22674.41, 'new_value': 23288.62}, {'field': 'offline_amount', 'old_value': 27945.97, 'new_value': 28465.47}, {'field': 'total_amount', 'old_value': 50620.38, 'new_value': 51754.09}, {'field': 'order_count', 'old_value': 2497, 'new_value': 2562}]
2025-05-29 09:00:34,696 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-29 09:00:35,212 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-29 09:00:35,212 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 314569.4, 'new_value': 322205.1}, {'field': 'total_amount', 'old_value': 429589.1, 'new_value': 437224.8}, {'field': 'order_count', 'old_value': 3584, 'new_value': 3676}]
2025-05-29 09:00:35,212 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-29 09:00:35,712 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-29 09:00:35,712 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112712.0, 'new_value': 117172.3}, {'field': 'total_amount', 'old_value': 112712.0, 'new_value': 117172.3}, {'field': 'order_count', 'old_value': 6191, 'new_value': 6460}]
2025-05-29 09:00:35,712 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-29 09:00:36,196 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-29 09:00:36,196 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86022.7, 'new_value': 86081.7}, {'field': 'total_amount', 'old_value': 93647.5, 'new_value': 93706.5}, {'field': 'order_count', 'old_value': 2123, 'new_value': 2124}]
2025-05-29 09:00:36,196 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-29 09:00:36,665 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-29 09:00:36,665 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42636.2, 'new_value': 59696.0}, {'field': 'total_amount', 'old_value': 1032715.5, 'new_value': 1049775.3}, {'field': 'order_count', 'old_value': 97, 'new_value': 102}]
2025-05-29 09:00:36,665 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-29 09:00:37,134 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-29 09:00:37,134 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31862.0, 'new_value': 37750.0}, {'field': 'total_amount', 'old_value': 38434.0, 'new_value': 44322.0}, {'field': 'order_count', 'old_value': 80, 'new_value': 82}]
2025-05-29 09:00:37,134 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-29 09:00:37,712 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-29 09:00:37,712 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 186776.0, 'new_value': 191895.0}, {'field': 'total_amount', 'old_value': 186776.0, 'new_value': 191895.0}, {'field': 'order_count', 'old_value': 386, 'new_value': 399}]
2025-05-29 09:00:37,712 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-29 09:00:38,165 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-29 09:00:38,165 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 188183.19, 'new_value': 192911.75}, {'field': 'offline_amount', 'old_value': 31436.81, 'new_value': 32250.41}, {'field': 'total_amount', 'old_value': 219620.0, 'new_value': 225162.16}, {'field': 'order_count', 'old_value': 812, 'new_value': 833}]
2025-05-29 09:00:38,165 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-29 09:00:38,634 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-29 09:00:38,634 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 178192.0, 'new_value': 181142.0}, {'field': 'offline_amount', 'old_value': 66934.91, 'new_value': 68110.91}, {'field': 'total_amount', 'old_value': 245126.91, 'new_value': 249252.91}, {'field': 'order_count', 'old_value': 1599, 'new_value': 1632}]
2025-05-29 09:00:38,634 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-29 09:00:39,056 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-29 09:00:39,056 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 231, 'new_value': 235}]
2025-05-29 09:00:39,056 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-29 09:00:39,571 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-29 09:00:39,571 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98863.42, 'new_value': 102745.19}, {'field': 'total_amount', 'old_value': 98863.42, 'new_value': 102745.19}, {'field': 'order_count', 'old_value': 2671, 'new_value': 2787}]
2025-05-29 09:00:39,571 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-29 09:00:40,087 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-29 09:00:40,087 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 171164.0, 'new_value': 174027.0}, {'field': 'offline_amount', 'old_value': 75666.28, 'new_value': 77219.18}, {'field': 'total_amount', 'old_value': 246830.28, 'new_value': 251246.18}, {'field': 'order_count', 'old_value': 1732, 'new_value': 1769}]
2025-05-29 09:00:40,087 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-29 09:00:40,540 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-29 09:00:40,540 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11989.65, 'new_value': 12267.62}, {'field': 'offline_amount', 'old_value': 187606.46, 'new_value': 194299.48}, {'field': 'total_amount', 'old_value': 199596.11, 'new_value': 206567.1}, {'field': 'order_count', 'old_value': 2196, 'new_value': 2274}]
2025-05-29 09:00:40,540 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-29 09:00:40,993 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-29 09:00:40,993 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 227343.43, 'new_value': 232176.58}, {'field': 'total_amount', 'old_value': 227343.43, 'new_value': 232176.58}, {'field': 'order_count', 'old_value': 774, 'new_value': 791}]
2025-05-29 09:00:40,993 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-29 09:00:41,431 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-29 09:00:41,431 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 157449.0, 'new_value': 162833.0}, {'field': 'total_amount', 'old_value': 157449.0, 'new_value': 162833.0}, {'field': 'order_count', 'old_value': 3964, 'new_value': 4118}]
2025-05-29 09:00:41,431 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-29 09:00:41,931 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-29 09:00:41,931 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24790.47, 'new_value': 26123.47}, {'field': 'total_amount', 'old_value': 24790.47, 'new_value': 26123.47}, {'field': 'order_count', 'old_value': 151, 'new_value': 157}]
2025-05-29 09:00:41,931 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-29 09:00:42,446 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-29 09:00:42,446 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 182637.0, 'new_value': 190825.0}, {'field': 'total_amount', 'old_value': 182637.0, 'new_value': 190825.0}, {'field': 'order_count', 'old_value': 6876, 'new_value': 7194}]
2025-05-29 09:00:42,446 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-29 09:00:42,915 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-29 09:00:42,915 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 753238.37, 'new_value': 777811.74}, {'field': 'total_amount', 'old_value': 753238.37, 'new_value': 777811.74}, {'field': 'order_count', 'old_value': 4331, 'new_value': 4561}]
2025-05-29 09:00:42,915 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-29 09:00:43,462 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-29 09:00:43,462 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 548135.2, 'new_value': 549993.2}, {'field': 'total_amount', 'old_value': 582481.2, 'new_value': 584339.2}, {'field': 'order_count', 'old_value': 98, 'new_value': 99}]
2025-05-29 09:00:43,462 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-29 09:00:43,962 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-29 09:00:43,962 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 150997.15, 'new_value': 156005.23}, {'field': 'total_amount', 'old_value': 150997.15, 'new_value': 156005.23}, {'field': 'order_count', 'old_value': 5482, 'new_value': 5673}]
2025-05-29 09:00:43,962 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-29 09:00:44,477 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-29 09:00:44,493 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52449.0, 'new_value': 52908.0}, {'field': 'total_amount', 'old_value': 52449.0, 'new_value': 52908.0}, {'field': 'order_count', 'old_value': 111, 'new_value': 114}]
2025-05-29 09:00:44,493 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-29 09:00:44,977 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-29 09:00:44,977 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 150026.52, 'new_value': 150945.83}, {'field': 'offline_amount', 'old_value': 544760.17, 'new_value': 581378.17}, {'field': 'total_amount', 'old_value': 694786.69, 'new_value': 732324.0}, {'field': 'order_count', 'old_value': 916, 'new_value': 943}]
2025-05-29 09:00:44,977 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-29 09:00:45,384 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-29 09:00:45,384 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 196656.15, 'new_value': 202498.54}, {'field': 'total_amount', 'old_value': 196656.15, 'new_value': 202498.54}, {'field': 'order_count', 'old_value': 1130, 'new_value': 1165}]
2025-05-29 09:00:45,384 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-29 09:00:45,821 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-29 09:00:45,821 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 211868.9, 'new_value': 223091.82}, {'field': 'offline_amount', 'old_value': 454238.45, 'new_value': 459706.17}, {'field': 'total_amount', 'old_value': 666107.35, 'new_value': 682797.99}, {'field': 'order_count', 'old_value': 4949, 'new_value': 5119}]
2025-05-29 09:00:45,821 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-05-29 09:00:46,306 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-05-29 09:00:46,306 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 207465.0, 'new_value': 208564.0}, {'field': 'total_amount', 'old_value': 207465.0, 'new_value': 208564.0}, {'field': 'order_count', 'old_value': 51, 'new_value': 52}]
2025-05-29 09:00:46,306 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-29 09:00:46,774 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-29 09:00:46,774 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1825535.58, 'new_value': 1868559.78}, {'field': 'total_amount', 'old_value': 1878980.68, 'new_value': 1922004.88}, {'field': 'order_count', 'old_value': 3431, 'new_value': 3513}]
2025-05-29 09:00:46,774 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-29 09:00:47,165 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-29 09:00:47,165 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 274227.65, 'new_value': 280463.65}, {'field': 'offline_amount', 'old_value': 133838.0, 'new_value': 137331.0}, {'field': 'total_amount', 'old_value': 408065.65, 'new_value': 417794.65}, {'field': 'order_count', 'old_value': 2078, 'new_value': 2128}]
2025-05-29 09:00:47,165 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-29 09:00:47,649 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-29 09:00:47,649 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40059.9, 'new_value': 40976.9}, {'field': 'total_amount', 'old_value': 40059.9, 'new_value': 40976.9}, {'field': 'order_count', 'old_value': 179, 'new_value': 182}]
2025-05-29 09:00:47,649 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-29 09:00:48,118 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-29 09:00:48,118 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70297.0, 'new_value': 71395.0}, {'field': 'total_amount', 'old_value': 98943.0, 'new_value': 100041.0}, {'field': 'order_count', 'old_value': 44, 'new_value': 45}]
2025-05-29 09:00:48,118 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-29 09:00:48,602 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-29 09:00:48,602 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21179.0, 'new_value': 21469.0}, {'field': 'total_amount', 'old_value': 21179.0, 'new_value': 21469.0}, {'field': 'order_count', 'old_value': 365, 'new_value': 370}]
2025-05-29 09:00:48,602 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-29 09:00:49,149 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-29 09:00:49,149 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122731.0, 'new_value': 123886.0}, {'field': 'total_amount', 'old_value': 122734.0, 'new_value': 123889.0}, {'field': 'order_count', 'old_value': 51, 'new_value': 52}]
2025-05-29 09:00:49,149 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-29 09:00:49,681 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-29 09:00:49,681 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8544.35, 'new_value': 9602.75}, {'field': 'offline_amount', 'old_value': 18355.96, 'new_value': 19182.24}, {'field': 'total_amount', 'old_value': 26900.31, 'new_value': 28784.99}, {'field': 'order_count', 'old_value': 914, 'new_value': 964}]
2025-05-29 09:00:49,681 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-29 09:00:50,149 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-29 09:00:50,149 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 195829.72, 'new_value': 201663.79}, {'field': 'offline_amount', 'old_value': 158104.31, 'new_value': 163691.31}, {'field': 'total_amount', 'old_value': 353934.03, 'new_value': 365355.1}, {'field': 'order_count', 'old_value': 3217, 'new_value': 3323}]
2025-05-29 09:00:50,149 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-29 09:00:50,681 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-29 09:00:50,681 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89748.31, 'new_value': 293277.47}, {'field': 'total_amount', 'old_value': 89771.41, 'new_value': 293300.57}, {'field': 'order_count', 'old_value': 49, 'new_value': 59}]
2025-05-29 09:00:50,681 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-29 09:00:51,118 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-29 09:00:51,118 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 400545.9, 'new_value': 411281.9}, {'field': 'total_amount', 'old_value': 516154.0, 'new_value': 526890.0}, {'field': 'order_count', 'old_value': 651, 'new_value': 664}]
2025-05-29 09:00:51,118 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-29 09:00:51,571 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-29 09:00:51,571 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80321.49, 'new_value': 80521.49}, {'field': 'total_amount', 'old_value': 84090.59, 'new_value': 84290.59}, {'field': 'order_count', 'old_value': 418, 'new_value': 419}]
2025-05-29 09:00:51,571 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-29 09:00:52,009 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-29 09:00:52,024 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 68408.0, 'new_value': 71819.0}, {'field': 'offline_amount', 'old_value': 178435.0, 'new_value': 182106.0}, {'field': 'total_amount', 'old_value': 246843.0, 'new_value': 253925.0}, {'field': 'order_count', 'old_value': 5496, 'new_value': 5678}]
2025-05-29 09:00:52,024 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-29 09:00:52,587 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-29 09:00:52,587 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 121116.64, 'new_value': 121385.64}, {'field': 'total_amount', 'old_value': 126456.64, 'new_value': 126725.64}, {'field': 'order_count', 'old_value': 33, 'new_value': 34}]
2025-05-29 09:00:52,587 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-29 09:00:53,056 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-29 09:00:53,056 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 534427.04, 'new_value': 534871.04}, {'field': 'offline_amount', 'old_value': 246774.9, 'new_value': 246784.9}, {'field': 'total_amount', 'old_value': 781201.94, 'new_value': 781655.94}, {'field': 'order_count', 'old_value': 6819, 'new_value': 6828}]
2025-05-29 09:00:53,056 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-29 09:00:53,587 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-29 09:00:53,587 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42609.6, 'new_value': 43038.5}, {'field': 'offline_amount', 'old_value': 577.0, 'new_value': 578.0}, {'field': 'total_amount', 'old_value': 43186.6, 'new_value': 43616.5}, {'field': 'order_count', 'old_value': 178, 'new_value': 179}]
2025-05-29 09:00:53,587 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-29 09:00:54,056 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-29 09:00:54,056 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 88081.2, 'new_value': 91587.7}, {'field': 'offline_amount', 'old_value': 9298.75, 'new_value': 11066.55}, {'field': 'total_amount', 'old_value': 97379.95, 'new_value': 102654.25}, {'field': 'order_count', 'old_value': 301, 'new_value': 316}]
2025-05-29 09:00:54,056 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-29 09:00:54,540 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-29 09:00:54,540 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75857.41, 'new_value': 77349.51}, {'field': 'total_amount', 'old_value': 75857.41, 'new_value': 77349.51}, {'field': 'order_count', 'old_value': 2171, 'new_value': 2228}]
2025-05-29 09:00:54,540 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-29 09:00:55,040 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-29 09:00:55,040 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10161.3, 'new_value': 10656.3}, {'field': 'offline_amount', 'old_value': 56143.1, 'new_value': 57512.1}, {'field': 'total_amount', 'old_value': 66304.4, 'new_value': 68168.4}, {'field': 'order_count', 'old_value': 81, 'new_value': 84}]
2025-05-29 09:00:55,040 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-29 09:00:55,462 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-29 09:00:55,462 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 147379.84, 'new_value': 154596.28}, {'field': 'offline_amount', 'old_value': 466993.13, 'new_value': 481035.53}, {'field': 'total_amount', 'old_value': 614372.97, 'new_value': 635631.81}, {'field': 'order_count', 'old_value': 2994, 'new_value': 3057}]
2025-05-29 09:00:55,462 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-29 09:00:55,899 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-29 09:00:55,899 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 442332.34, 'new_value': 474202.25}, {'field': 'total_amount', 'old_value': 442332.34, 'new_value': 474202.25}, {'field': 'order_count', 'old_value': 570, 'new_value': 589}]
2025-05-29 09:00:55,899 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-29 09:00:56,368 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-29 09:00:56,368 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18544.74, 'new_value': 19129.71}, {'field': 'offline_amount', 'old_value': 476104.94, 'new_value': 491958.98}, {'field': 'total_amount', 'old_value': 494649.68, 'new_value': 511088.69}, {'field': 'order_count', 'old_value': 1979, 'new_value': 2053}]
2025-05-29 09:00:56,368 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-29 09:00:56,852 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-29 09:00:56,852 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 91386.0, 'new_value': 94644.0}, {'field': 'offline_amount', 'old_value': 91519.76, 'new_value': 95668.23}, {'field': 'total_amount', 'old_value': 182905.76, 'new_value': 190312.23}, {'field': 'order_count', 'old_value': 213, 'new_value': 222}]
2025-05-29 09:00:56,852 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-29 09:00:57,306 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-29 09:00:57,306 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 176568.5, 'new_value': 181419.7}, {'field': 'total_amount', 'old_value': 176568.5, 'new_value': 181419.7}, {'field': 'order_count', 'old_value': 416, 'new_value': 424}]
2025-05-29 09:00:57,306 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-29 09:00:57,743 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-29 09:00:57,743 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 203688.28, 'new_value': 209079.79}, {'field': 'total_amount', 'old_value': 324123.12, 'new_value': 329514.63}, {'field': 'order_count', 'old_value': 3372, 'new_value': 3441}]
2025-05-29 09:00:57,743 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-29 09:00:58,180 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-29 09:00:58,180 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15440.0, 'new_value': 16904.0}, {'field': 'total_amount', 'old_value': 15440.0, 'new_value': 16904.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 37}]
2025-05-29 09:00:58,180 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-29 09:00:58,774 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-29 09:00:58,774 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45171.0, 'new_value': 48524.0}, {'field': 'total_amount', 'old_value': 45171.0, 'new_value': 48524.0}, {'field': 'order_count', 'old_value': 93, 'new_value': 96}]
2025-05-29 09:00:58,774 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-29 09:00:59,259 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-29 09:00:59,259 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8756.85, 'new_value': 9045.48}, {'field': 'offline_amount', 'old_value': 32868.0, 'new_value': 33027.0}, {'field': 'total_amount', 'old_value': 41624.85, 'new_value': 42072.48}, {'field': 'order_count', 'old_value': 221, 'new_value': 228}]
2025-05-29 09:00:59,259 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-29 09:00:59,681 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-29 09:00:59,681 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48009.66, 'new_value': 48361.86}, {'field': 'total_amount', 'old_value': 48531.26, 'new_value': 48883.46}, {'field': 'order_count', 'old_value': 424, 'new_value': 429}]
2025-05-29 09:00:59,681 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-29 09:01:00,118 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-29 09:01:00,118 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6348.0, 'new_value': 6545.0}, {'field': 'offline_amount', 'old_value': 28241.2, 'new_value': 29444.8}, {'field': 'total_amount', 'old_value': 34589.2, 'new_value': 35989.8}, {'field': 'order_count', 'old_value': 1328, 'new_value': 1382}]
2025-05-29 09:01:00,118 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-29 09:01:00,680 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-29 09:01:00,680 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 93641.66, 'new_value': 94714.26}, {'field': 'total_amount', 'old_value': 93641.66, 'new_value': 94714.26}, {'field': 'order_count', 'old_value': 349, 'new_value': 358}]
2025-05-29 09:01:00,680 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-29 09:01:01,165 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-29 09:01:01,165 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 201758.3, 'new_value': 206942.3}, {'field': 'total_amount', 'old_value': 201758.3, 'new_value': 206942.3}, {'field': 'order_count', 'old_value': 759, 'new_value': 774}]
2025-05-29 09:01:01,165 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-29 09:01:01,634 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-29 09:01:01,634 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57296.0, 'new_value': 57679.0}, {'field': 'total_amount', 'old_value': 60450.0, 'new_value': 60833.0}, {'field': 'order_count', 'old_value': 227, 'new_value': 230}]
2025-05-29 09:01:01,634 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-29 09:01:02,180 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-29 09:01:02,180 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 161273.32, 'new_value': 165948.42}, {'field': 'total_amount', 'old_value': 161273.32, 'new_value': 165948.42}, {'field': 'order_count', 'old_value': 802, 'new_value': 827}]
2025-05-29 09:01:02,180 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-29 09:01:02,759 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-29 09:01:02,759 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25032.8, 'new_value': 26003.92}, {'field': 'offline_amount', 'old_value': 46583.18, 'new_value': 48382.08}, {'field': 'total_amount', 'old_value': 71615.98, 'new_value': 74386.0}, {'field': 'order_count', 'old_value': 2582, 'new_value': 2685}]
2025-05-29 09:01:02,759 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-29 09:01:03,196 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-29 09:01:03,196 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80105.0, 'new_value': 83777.0}, {'field': 'total_amount', 'old_value': 82513.0, 'new_value': 86185.0}, {'field': 'order_count', 'old_value': 342, 'new_value': 356}]
2025-05-29 09:01:03,196 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQL
2025-05-29 09:01:03,696 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQL
2025-05-29 09:01:03,712 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64752.0, 'new_value': 75810.0}, {'field': 'total_amount', 'old_value': 64752.0, 'new_value': 75810.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 23}]
2025-05-29 09:01:03,712 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-29 09:01:04,071 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-29 09:01:04,071 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66107.04, 'new_value': 67469.04}, {'field': 'total_amount', 'old_value': 87156.24, 'new_value': 88518.24}, {'field': 'order_count', 'old_value': 971, 'new_value': 993}]
2025-05-29 09:01:04,071 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-29 09:01:04,493 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-29 09:01:04,509 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 105020.4, 'new_value': 108444.4}, {'field': 'offline_amount', 'old_value': 149868.3, 'new_value': 154547.3}, {'field': 'total_amount', 'old_value': 254888.7, 'new_value': 262991.7}, {'field': 'order_count', 'old_value': 1608, 'new_value': 1669}]
2025-05-29 09:01:04,509 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-29 09:01:04,946 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-29 09:01:04,946 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24940.29, 'new_value': 26587.7}, {'field': 'offline_amount', 'old_value': 46035.03, 'new_value': 48366.69}, {'field': 'total_amount', 'old_value': 70975.32, 'new_value': 74954.39}, {'field': 'order_count', 'old_value': 3730, 'new_value': 3936}]
2025-05-29 09:01:04,946 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-29 09:01:05,477 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-29 09:01:05,477 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33313.47, 'new_value': 84214.37}, {'field': 'total_amount', 'old_value': 65188.37, 'new_value': 116089.27}, {'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-05-29 09:01:05,477 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-29 09:01:05,930 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-29 09:01:05,930 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55862.0, 'new_value': 58161.0}, {'field': 'total_amount', 'old_value': 56211.0, 'new_value': 58510.0}, {'field': 'order_count', 'old_value': 105, 'new_value': 107}]
2025-05-29 09:01:05,930 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-29 09:01:06,368 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-29 09:01:06,368 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47184.98, 'new_value': 48025.98}, {'field': 'offline_amount', 'old_value': 509267.5, 'new_value': 519267.5}, {'field': 'total_amount', 'old_value': 556452.48, 'new_value': 567293.48}, {'field': 'order_count', 'old_value': 4371, 'new_value': 4467}]
2025-05-29 09:01:06,384 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-29 09:01:06,868 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-29 09:01:06,868 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 127325.23, 'new_value': 131182.33}, {'field': 'total_amount', 'old_value': 127325.23, 'new_value': 131182.33}, {'field': 'order_count', 'old_value': 3712, 'new_value': 3831}]
2025-05-29 09:01:06,868 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-29 09:01:07,399 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-29 09:01:07,399 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 133709.79, 'new_value': 138468.34}, {'field': 'offline_amount', 'old_value': 287219.34, 'new_value': 294453.39}, {'field': 'total_amount', 'old_value': 420929.13, 'new_value': 432921.73}, {'field': 'order_count', 'old_value': 4942, 'new_value': 5087}]
2025-05-29 09:01:07,399 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO
2025-05-29 09:01:07,852 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO
2025-05-29 09:01:07,852 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35993.08, 'new_value': 37333.48}, {'field': 'total_amount', 'old_value': 35993.08, 'new_value': 37333.48}, {'field': 'order_count', 'old_value': 35, 'new_value': 37}]
2025-05-29 09:01:07,868 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-29 09:01:08,321 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-29 09:01:08,321 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 243324.0, 'new_value': 251532.0}, {'field': 'total_amount', 'old_value': 243324.0, 'new_value': 251532.0}, {'field': 'order_count', 'old_value': 20277, 'new_value': 20961}]
2025-05-29 09:01:08,321 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCO
2025-05-29 09:01:08,805 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCO
2025-05-29 09:01:08,805 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14564.2, 'new_value': 15760.2}, {'field': 'total_amount', 'old_value': 14564.2, 'new_value': 15760.2}, {'field': 'order_count', 'old_value': 13, 'new_value': 15}]
2025-05-29 09:01:08,805 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-29 09:01:09,243 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-29 09:01:09,243 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47360.0, 'new_value': 49661.0}, {'field': 'total_amount', 'old_value': 47360.0, 'new_value': 49661.0}, {'field': 'order_count', 'old_value': 69, 'new_value': 73}]
2025-05-29 09:01:09,243 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-29 09:01:09,712 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-29 09:01:09,712 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53149.5, 'new_value': 54730.7}, {'field': 'total_amount', 'old_value': 55449.8, 'new_value': 57031.0}, {'field': 'order_count', 'old_value': 171, 'new_value': 176}]
2025-05-29 09:01:09,712 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-29 09:01:10,212 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-29 09:01:10,212 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48508.1, 'new_value': 50071.7}, {'field': 'total_amount', 'old_value': 48508.1, 'new_value': 50071.7}, {'field': 'order_count', 'old_value': 2160, 'new_value': 2247}]
2025-05-29 09:01:10,212 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-29 09:01:10,665 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-29 09:01:10,665 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 134537.6, 'new_value': 143085.2}, {'field': 'total_amount', 'old_value': 266001.95, 'new_value': 274549.55}, {'field': 'order_count', 'old_value': 7075, 'new_value': 7333}]
2025-05-29 09:01:10,665 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-29 09:01:11,149 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-29 09:01:11,149 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 334460.8, 'new_value': 340864.4}, {'field': 'total_amount', 'old_value': 334460.8, 'new_value': 340864.4}, {'field': 'order_count', 'old_value': 149, 'new_value': 152}]
2025-05-29 09:01:11,149 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-29 09:01:11,696 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-29 09:01:11,696 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47620.31, 'new_value': 49654.83}, {'field': 'total_amount', 'old_value': 47620.31, 'new_value': 49654.83}, {'field': 'order_count', 'old_value': 193, 'new_value': 199}]
2025-05-29 09:01:11,696 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-29 09:01:12,165 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-29 09:01:12,165 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 61820.47, 'new_value': 63886.68}, {'field': 'offline_amount', 'old_value': 36210.3, 'new_value': 37251.34}, {'field': 'total_amount', 'old_value': 98030.77, 'new_value': 101138.02}, {'field': 'order_count', 'old_value': 5328, 'new_value': 5498}]
2025-05-29 09:01:12,165 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-29 09:01:12,524 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-29 09:01:12,524 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31707.3, 'new_value': 33052.54}, {'field': 'offline_amount', 'old_value': 112213.48, 'new_value': 116011.48}, {'field': 'total_amount', 'old_value': 143920.78, 'new_value': 149064.02}, {'field': 'order_count', 'old_value': 115, 'new_value': 118}]
2025-05-29 09:01:12,524 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-29 09:01:13,009 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-29 09:01:13,009 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22056.08, 'new_value': 23060.08}, {'field': 'total_amount', 'old_value': 22056.08, 'new_value': 23060.08}, {'field': 'order_count', 'old_value': 187, 'new_value': 194}]
2025-05-29 09:01:13,009 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-29 09:01:13,540 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-29 09:01:13,540 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 712591.0, 'new_value': 722133.0}, {'field': 'total_amount', 'old_value': 712591.0, 'new_value': 722133.0}, {'field': 'order_count', 'old_value': 2042, 'new_value': 2091}]
2025-05-29 09:01:13,540 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-29 09:01:13,977 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-29 09:01:13,977 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 447221.48, 'new_value': 458020.59}, {'field': 'total_amount', 'old_value': 447221.48, 'new_value': 458020.59}, {'field': 'order_count', 'old_value': 1593, 'new_value': 1630}]
2025-05-29 09:01:13,977 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-29 09:01:14,634 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-29 09:01:14,634 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 332482.3, 'new_value': 348109.5}, {'field': 'total_amount', 'old_value': 343758.5, 'new_value': 359385.7}, {'field': 'order_count', 'old_value': 8535, 'new_value': 8839}]
2025-05-29 09:01:14,634 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-29 09:01:15,071 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-29 09:01:15,071 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42456.51, 'new_value': 44369.68}, {'field': 'total_amount', 'old_value': 42456.51, 'new_value': 44369.68}, {'field': 'order_count', 'old_value': 5477, 'new_value': 5731}]
2025-05-29 09:01:15,071 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-29 09:01:15,509 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-29 09:01:15,509 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27963.43, 'new_value': 28866.61}, {'field': 'offline_amount', 'old_value': 35604.18, 'new_value': 36361.64}, {'field': 'total_amount', 'old_value': 63567.61, 'new_value': 65228.25}, {'field': 'order_count', 'old_value': 2882, 'new_value': 2957}]
2025-05-29 09:01:15,509 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-29 09:01:16,102 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-29 09:01:16,102 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96072.0, 'new_value': 102156.0}, {'field': 'total_amount', 'old_value': 101273.0, 'new_value': 107357.0}, {'field': 'order_count', 'old_value': 293, 'new_value': 311}]
2025-05-29 09:01:16,102 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZM
2025-05-29 09:01:16,587 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZM
2025-05-29 09:01:16,587 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 129994.0, 'new_value': 152992.0}, {'field': 'total_amount', 'old_value': 129994.0, 'new_value': 152992.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 26}]
2025-05-29 09:01:16,587 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-29 09:01:17,102 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-29 09:01:17,102 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18525.9, 'new_value': 18714.2}, {'field': 'offline_amount', 'old_value': 46677.9, 'new_value': 46830.9}, {'field': 'total_amount', 'old_value': 65203.8, 'new_value': 65545.1}, {'field': 'order_count', 'old_value': 204, 'new_value': 227}]
2025-05-29 09:01:17,102 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-29 09:01:17,727 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-29 09:01:17,727 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 486817.32, 'new_value': 504551.48}, {'field': 'total_amount', 'old_value': 486817.32, 'new_value': 504551.48}, {'field': 'order_count', 'old_value': 2487, 'new_value': 2583}]
2025-05-29 09:01:17,727 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-29 09:01:18,258 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-29 09:01:18,258 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10873.1, 'new_value': 12000.0}, {'field': 'offline_amount', 'old_value': 28252.9, 'new_value': 28428.9}, {'field': 'total_amount', 'old_value': 39126.0, 'new_value': 40428.9}, {'field': 'order_count', 'old_value': 105, 'new_value': 108}]
2025-05-29 09:01:18,258 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-29 09:01:18,759 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-29 09:01:18,759 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47494.36, 'new_value': 47684.36}, {'field': 'offline_amount', 'old_value': 55876.69, 'new_value': 59481.41}, {'field': 'total_amount', 'old_value': 103371.05, 'new_value': 107165.77}, {'field': 'order_count', 'old_value': 366, 'new_value': 374}]
2025-05-29 09:01:18,759 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-29 09:01:19,337 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-29 09:01:19,337 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 349654.12, 'new_value': 358134.48}, {'field': 'total_amount', 'old_value': 349654.12, 'new_value': 358134.48}, {'field': 'order_count', 'old_value': 3372, 'new_value': 3469}]
2025-05-29 09:01:19,337 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-29 09:01:19,852 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-29 09:01:19,852 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78972.0, 'new_value': 79610.0}, {'field': 'total_amount', 'old_value': 78972.0, 'new_value': 79610.0}, {'field': 'order_count', 'old_value': 364, 'new_value': 366}]
2025-05-29 09:01:19,852 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-29 09:01:20,290 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-29 09:01:20,290 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 336711.92, 'new_value': 342049.92}, {'field': 'offline_amount', 'old_value': 11596.5, 'new_value': 12720.5}, {'field': 'total_amount', 'old_value': 348308.42, 'new_value': 354770.42}, {'field': 'order_count', 'old_value': 3025, 'new_value': 3077}]
2025-05-29 09:01:20,290 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-29 09:01:20,727 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-29 09:01:20,727 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23147.0, 'new_value': 23505.0}, {'field': 'total_amount', 'old_value': 23147.0, 'new_value': 23505.0}, {'field': 'order_count', 'old_value': 128, 'new_value': 130}]
2025-05-29 09:01:20,727 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-29 09:01:21,165 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-29 09:01:21,165 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29546.0, 'new_value': 30500.4}, {'field': 'total_amount', 'old_value': 29546.0, 'new_value': 30500.4}, {'field': 'order_count', 'old_value': 818, 'new_value': 842}]
2025-05-29 09:01:21,165 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-29 09:01:21,618 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-29 09:01:21,618 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52897.79, 'new_value': 54766.89}, {'field': 'total_amount', 'old_value': 60202.79, 'new_value': 62071.89}, {'field': 'order_count', 'old_value': 558, 'new_value': 576}]
2025-05-29 09:01:21,618 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-29 09:01:22,118 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-29 09:01:22,118 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35977.0, 'new_value': 36519.0}, {'field': 'total_amount', 'old_value': 43611.0, 'new_value': 44153.0}, {'field': 'order_count', 'old_value': 329, 'new_value': 333}]
2025-05-29 09:01:22,118 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-29 09:01:22,680 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-29 09:01:22,680 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46631.8, 'new_value': 49491.8}, {'field': 'total_amount', 'old_value': 63373.4, 'new_value': 66233.4}, {'field': 'order_count', 'old_value': 108, 'new_value': 109}]
2025-05-29 09:01:22,680 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-29 09:01:23,165 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-29 09:01:23,165 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35230.72, 'new_value': 36458.74}, {'field': 'total_amount', 'old_value': 35230.72, 'new_value': 36458.74}, {'field': 'order_count', 'old_value': 1313, 'new_value': 1359}]
2025-05-29 09:01:23,165 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-29 09:01:23,665 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-29 09:01:23,665 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 779219.03, 'new_value': 801157.04}, {'field': 'total_amount', 'old_value': 779219.03, 'new_value': 801157.04}, {'field': 'order_count', 'old_value': 5857, 'new_value': 6047}]
2025-05-29 09:01:23,665 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-29 09:01:24,149 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-29 09:01:24,149 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 66088.21, 'new_value': 68928.64}, {'field': 'offline_amount', 'old_value': 481757.98, 'new_value': 494372.78}, {'field': 'total_amount', 'old_value': 547846.19, 'new_value': 563301.42}, {'field': 'order_count', 'old_value': 2676, 'new_value': 2755}]
2025-05-29 09:01:24,149 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-29 09:01:24,602 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-29 09:01:24,602 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 751343.0, 'new_value': 759775.0}, {'field': 'total_amount', 'old_value': 751343.0, 'new_value': 759775.0}, {'field': 'order_count', 'old_value': 182, 'new_value': 184}]
2025-05-29 09:01:24,602 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-29 09:01:25,008 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-29 09:01:25,008 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 883010.0, 'new_value': 901910.0}, {'field': 'total_amount', 'old_value': 883010.0, 'new_value': 901910.0}, {'field': 'order_count', 'old_value': 3988, 'new_value': 4072}]
2025-05-29 09:01:25,008 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-29 09:01:25,462 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-29 09:01:25,462 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38102.0, 'new_value': 43082.0}, {'field': 'total_amount', 'old_value': 38102.0, 'new_value': 43082.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 22}]
2025-05-29 09:01:25,462 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-29 09:01:25,899 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-29 09:01:25,899 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 152531.0, 'new_value': 152969.0}, {'field': 'total_amount', 'old_value': 152531.0, 'new_value': 152969.0}, {'field': 'order_count', 'old_value': 4879, 'new_value': 4891}]
2025-05-29 09:01:25,899 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-29 09:01:26,352 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-29 09:01:26,352 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 134806.43, 'new_value': 141092.82}, {'field': 'offline_amount', 'old_value': 376733.08, 'new_value': 379067.28}, {'field': 'total_amount', 'old_value': 511539.51, 'new_value': 520160.1}, {'field': 'order_count', 'old_value': 4476, 'new_value': 4631}]
2025-05-29 09:01:26,352 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-29 09:01:26,805 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-29 09:01:26,805 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 102019.74, 'new_value': 105755.77}, {'field': 'offline_amount', 'old_value': 114479.73, 'new_value': 117076.77}, {'field': 'total_amount', 'old_value': 216499.47, 'new_value': 222832.54}, {'field': 'order_count', 'old_value': 8866, 'new_value': 9134}]
2025-05-29 09:01:26,805 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-29 09:01:27,274 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-29 09:01:27,274 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 225424.92, 'new_value': 228213.42}, {'field': 'offline_amount', 'old_value': 116290.9, 'new_value': 116989.9}, {'field': 'total_amount', 'old_value': 341715.82, 'new_value': 345203.32}, {'field': 'order_count', 'old_value': 629, 'new_value': 636}]
2025-05-29 09:01:27,274 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-29 09:01:27,868 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-29 09:01:27,868 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56577.0, 'new_value': 57375.0}, {'field': 'total_amount', 'old_value': 56577.0, 'new_value': 57375.0}, {'field': 'order_count', 'old_value': 128, 'new_value': 129}]
2025-05-29 09:01:27,868 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-29 09:01:28,290 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-29 09:01:28,290 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 376315.5, 'new_value': 376764.5}, {'field': 'total_amount', 'old_value': 376315.5, 'new_value': 376764.5}, {'field': 'order_count', 'old_value': 463, 'new_value': 464}]
2025-05-29 09:01:28,290 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-29 09:01:28,712 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-29 09:01:28,712 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 969118.0, 'new_value': 993171.0}, {'field': 'total_amount', 'old_value': 969118.0, 'new_value': 993171.0}, {'field': 'order_count', 'old_value': 51061, 'new_value': 51110}]
2025-05-29 09:01:28,712 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-29 09:01:29,149 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-29 09:01:29,149 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 745919.72, 'new_value': 767490.72}, {'field': 'total_amount', 'old_value': 745919.72, 'new_value': 767490.72}, {'field': 'order_count', 'old_value': 5727, 'new_value': 5925}]
2025-05-29 09:01:29,149 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-29 09:01:29,633 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-29 09:01:29,633 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 336058.94, 'new_value': 340588.52}, {'field': 'offline_amount', 'old_value': 1355591.84, 'new_value': 1375633.57}, {'field': 'total_amount', 'old_value': 1691650.78, 'new_value': 1716222.09}, {'field': 'order_count', 'old_value': 8435, 'new_value': 8574}]
2025-05-29 09:01:29,633 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-29 09:01:30,087 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-29 09:01:30,087 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 61783.44, 'new_value': 63442.44}, {'field': 'offline_amount', 'old_value': 48448.02, 'new_value': 49739.02}, {'field': 'total_amount', 'old_value': 110231.46, 'new_value': 113181.46}, {'field': 'order_count', 'old_value': 2211, 'new_value': 2273}]
2025-05-29 09:01:30,087 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBP
2025-05-29 09:01:30,508 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBP
2025-05-29 09:01:30,508 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 103800.0, 'new_value': 106800.0}, {'field': 'total_amount', 'old_value': 103800.0, 'new_value': 106800.0}]
2025-05-29 09:01:30,508 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCP
2025-05-29 09:01:31,040 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCP
2025-05-29 09:01:31,040 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 196400.0, 'new_value': 207400.0}, {'field': 'total_amount', 'old_value': 196400.0, 'new_value': 207400.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 28}]
2025-05-29 09:01:31,040 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-29 09:01:31,477 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-29 09:01:31,477 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2330000.0, 'new_value': 2380000.0}, {'field': 'total_amount', 'old_value': 2330000.0, 'new_value': 2380000.0}, {'field': 'order_count', 'old_value': 284, 'new_value': 285}]
2025-05-29 09:01:31,477 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-29 09:01:32,071 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-29 09:01:32,071 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96841.53, 'new_value': 97758.53}, {'field': 'total_amount', 'old_value': 96841.53, 'new_value': 97758.53}, {'field': 'order_count', 'old_value': 128, 'new_value': 129}]
2025-05-29 09:01:32,071 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-29 09:01:32,587 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-29 09:01:32,587 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 745289.95, 'new_value': 763148.24}, {'field': 'total_amount', 'old_value': 745289.95, 'new_value': 763148.24}, {'field': 'order_count', 'old_value': 8689, 'new_value': 8935}]
2025-05-29 09:01:32,587 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-29 09:01:33,055 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-29 09:01:33,055 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 206336.7, 'new_value': 213605.8}, {'field': 'offline_amount', 'old_value': 475527.2, 'new_value': 476527.2}, {'field': 'total_amount', 'old_value': 681863.9, 'new_value': 690133.0}, {'field': 'order_count', 'old_value': 4796, 'new_value': 4884}]
2025-05-29 09:01:33,055 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-29 09:01:33,493 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-29 09:01:33,493 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 617571.0, 'new_value': 637161.0}, {'field': 'total_amount', 'old_value': 617571.0, 'new_value': 637161.0}, {'field': 'order_count', 'old_value': 557, 'new_value': 576}]
2025-05-29 09:01:33,493 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-29 09:01:33,946 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-29 09:01:33,946 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 284866.86, 'new_value': 289100.66}, {'field': 'offline_amount', 'old_value': 155614.82, 'new_value': 158832.26}, {'field': 'total_amount', 'old_value': 440481.68, 'new_value': 447932.92}, {'field': 'order_count', 'old_value': 3307, 'new_value': 3335}]
2025-05-29 09:01:33,946 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-29 09:01:34,415 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-29 09:01:34,415 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89415.0, 'new_value': 90532.7}, {'field': 'total_amount', 'old_value': 91570.7, 'new_value': 92688.4}, {'field': 'order_count', 'old_value': 579, 'new_value': 586}]
2025-05-29 09:01:34,415 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-29 09:01:34,868 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-29 09:01:34,868 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52160.0, 'new_value': 53028.0}, {'field': 'total_amount', 'old_value': 52160.0, 'new_value': 53028.0}, {'field': 'order_count', 'old_value': 125, 'new_value': 127}]
2025-05-29 09:01:34,868 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-29 09:01:35,305 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-29 09:01:35,305 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 530000.0, 'new_value': 535000.0}, {'field': 'total_amount', 'old_value': 530000.0, 'new_value': 535000.0}, {'field': 'order_count', 'old_value': 151, 'new_value': 152}]
2025-05-29 09:01:35,305 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-29 09:01:35,821 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-29 09:01:35,821 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 510000.0, 'new_value': 515000.0}, {'field': 'total_amount', 'old_value': 510000.0, 'new_value': 515000.0}, {'field': 'order_count', 'old_value': 150, 'new_value': 151}]
2025-05-29 09:01:35,821 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-29 09:01:36,368 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-29 09:01:36,368 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3448674.0, 'new_value': 3498674.0}, {'field': 'total_amount', 'old_value': 3448674.0, 'new_value': 3498674.0}, {'field': 'order_count', 'old_value': 304, 'new_value': 305}]
2025-05-29 09:01:36,368 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-29 09:01:36,821 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-29 09:01:36,821 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 407781.92, 'new_value': 423285.19}, {'field': 'total_amount', 'old_value': 421292.4, 'new_value': 436795.67}, {'field': 'order_count', 'old_value': 1351, 'new_value': 1402}]
2025-05-29 09:01:36,821 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-29 09:01:37,383 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-29 09:01:37,383 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 54397.0, 'new_value': 59884.0}, {'field': 'offline_amount', 'old_value': 268427.0, 'new_value': 276863.0}, {'field': 'total_amount', 'old_value': 322824.0, 'new_value': 336747.0}, {'field': 'order_count', 'old_value': 289, 'new_value': 300}]
2025-05-29 09:01:37,383 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-29 09:01:37,852 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-29 09:01:37,852 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18511.15, 'new_value': 21264.25}, {'field': 'offline_amount', 'old_value': 53552.8, 'new_value': 54552.8}, {'field': 'total_amount', 'old_value': 72063.95, 'new_value': 75817.05}, {'field': 'order_count', 'old_value': 654, 'new_value': 661}]
2025-05-29 09:01:37,852 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-29 09:01:38,336 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-29 09:01:38,336 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 115770.0, 'new_value': 119155.0}, {'field': 'total_amount', 'old_value': 115770.0, 'new_value': 119155.0}, {'field': 'order_count', 'old_value': 491, 'new_value': 506}]
2025-05-29 09:01:38,336 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-29 09:01:38,774 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-29 09:01:38,774 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 110945.1, 'new_value': 113533.1}, {'field': 'total_amount', 'old_value': 111750.1, 'new_value': 114338.1}, {'field': 'order_count', 'old_value': 16315, 'new_value': 16317}]
2025-05-29 09:01:38,774 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-29 09:01:39,165 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-29 09:01:39,165 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 285365.66, 'new_value': 291294.66}, {'field': 'total_amount', 'old_value': 307853.06, 'new_value': 313782.06}, {'field': 'order_count', 'old_value': 1722, 'new_value': 1762}]
2025-05-29 09:01:39,165 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-29 09:01:39,680 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-29 09:01:39,680 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 182102.83, 'new_value': 186534.73}, {'field': 'total_amount', 'old_value': 182102.83, 'new_value': 186534.73}, {'field': 'order_count', 'old_value': 9249, 'new_value': 9449}]
2025-05-29 09:01:39,680 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-29 09:01:40,118 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-29 09:01:40,118 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 171200.1, 'new_value': 173692.0}, {'field': 'total_amount', 'old_value': 171200.1, 'new_value': 173692.0}, {'field': 'order_count', 'old_value': 774, 'new_value': 787}]
2025-05-29 09:01:40,118 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-29 09:01:40,555 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-29 09:01:40,555 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 140275.7, 'new_value': 141101.8}, {'field': 'total_amount', 'old_value': 140275.7, 'new_value': 141101.8}, {'field': 'order_count', 'old_value': 3875, 'new_value': 3897}]
2025-05-29 09:01:40,555 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-29 09:01:41,040 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-29 09:01:41,040 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 112242.5, 'new_value': 117715.8}, {'field': 'total_amount', 'old_value': 112242.5, 'new_value': 117715.8}, {'field': 'order_count', 'old_value': 537, 'new_value': 559}]
2025-05-29 09:01:41,040 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-29 09:01:41,586 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-29 09:01:41,586 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 111761.0, 'new_value': 112155.0}, {'field': 'total_amount', 'old_value': 111761.0, 'new_value': 112155.0}, {'field': 'order_count', 'old_value': 1697, 'new_value': 1698}]
2025-05-29 09:01:41,586 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-29 09:01:42,071 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-29 09:01:42,071 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98624.56, 'new_value': 100145.56}, {'field': 'total_amount', 'old_value': 98624.56, 'new_value': 100145.56}, {'field': 'order_count', 'old_value': 5076, 'new_value': 5163}]
2025-05-29 09:01:42,071 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-29 09:01:42,508 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-29 09:01:42,508 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87495.78, 'new_value': 89204.14}, {'field': 'total_amount', 'old_value': 93069.55, 'new_value': 94777.91}, {'field': 'order_count', 'old_value': 444, 'new_value': 451}]
2025-05-29 09:01:42,508 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-29 09:01:42,961 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-29 09:01:42,961 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 58388.87, 'new_value': 60565.35}, {'field': 'offline_amount', 'old_value': 59629.58, 'new_value': 62667.11}, {'field': 'total_amount', 'old_value': 118018.45, 'new_value': 123232.46}, {'field': 'order_count', 'old_value': 5879, 'new_value': 6134}]
2025-05-29 09:01:42,961 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-29 09:01:43,446 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-29 09:01:43,446 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 113604.82, 'new_value': 118644.43}, {'field': 'offline_amount', 'old_value': 126074.15, 'new_value': 129586.91}, {'field': 'total_amount', 'old_value': 239678.97, 'new_value': 248231.34}, {'field': 'order_count', 'old_value': 6063, 'new_value': 6291}]
2025-05-29 09:01:43,446 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2Q
2025-05-29 09:01:43,883 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2Q
2025-05-29 09:01:43,883 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88168.0, 'new_value': 96668.0}, {'field': 'total_amount', 'old_value': 88168.0, 'new_value': 96668.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-05-29 09:01:43,883 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3Q
2025-05-29 09:01:44,368 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3Q
2025-05-29 09:01:44,368 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107020.0, 'new_value': 107620.0}, {'field': 'total_amount', 'old_value': 107020.0, 'new_value': 107620.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-05-29 09:01:44,368 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-29 09:01:44,836 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-29 09:01:44,836 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1049464.0, 'new_value': 1095459.0}, {'field': 'total_amount', 'old_value': 1049464.0, 'new_value': 1095459.0}, {'field': 'order_count', 'old_value': 1209, 'new_value': 1259}]
2025-05-29 09:01:44,836 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-29 09:01:45,321 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-29 09:01:45,321 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 221638.4, 'new_value': 233826.4}, {'field': 'total_amount', 'old_value': 227588.7, 'new_value': 239776.7}, {'field': 'order_count', 'old_value': 443, 'new_value': 457}]
2025-05-29 09:01:45,321 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-29 09:01:45,790 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-29 09:01:45,790 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46479.15, 'new_value': 47610.65}, {'field': 'offline_amount', 'old_value': 137192.0, 'new_value': 139257.0}, {'field': 'total_amount', 'old_value': 183671.15, 'new_value': 186867.65}, {'field': 'order_count', 'old_value': 1996, 'new_value': 2053}]
2025-05-29 09:01:45,790 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-29 09:01:46,258 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-29 09:01:46,258 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7971.3, 'new_value': 8188.7}, {'field': 'offline_amount', 'old_value': 25243.93, 'new_value': 25546.1}, {'field': 'total_amount', 'old_value': 33215.23, 'new_value': 33734.8}, {'field': 'order_count', 'old_value': 324, 'new_value': 335}]
2025-05-29 09:01:46,258 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-29 09:01:46,696 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-29 09:01:46,696 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11916.3, 'new_value': 12174.32}, {'field': 'offline_amount', 'old_value': 181478.0, 'new_value': 191478.0}, {'field': 'total_amount', 'old_value': 193394.3, 'new_value': 203652.32}, {'field': 'order_count', 'old_value': 84, 'new_value': 86}]
2025-05-29 09:01:46,696 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-29 09:01:47,243 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-29 09:01:47,243 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34984.66, 'new_value': 35982.66}, {'field': 'offline_amount', 'old_value': 34140.82, 'new_value': 34935.82}, {'field': 'total_amount', 'old_value': 69125.48, 'new_value': 70918.48}, {'field': 'order_count', 'old_value': 309, 'new_value': 318}]
2025-05-29 09:01:47,243 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-29 09:01:47,711 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-29 09:01:47,711 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 238124.5, 'new_value': 247228.5}, {'field': 'total_amount', 'old_value': 238124.5, 'new_value': 247228.5}, {'field': 'order_count', 'old_value': 1165, 'new_value': 1203}]
2025-05-29 09:01:47,711 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-29 09:01:48,149 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-29 09:01:48,149 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58082.78, 'new_value': 59935.78}, {'field': 'total_amount', 'old_value': 64748.82, 'new_value': 66601.82}, {'field': 'order_count', 'old_value': 602, 'new_value': 624}]
2025-05-29 09:01:48,149 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-29 09:01:48,743 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-29 09:01:48,743 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 214690.34, 'new_value': 219396.24}, {'field': 'total_amount', 'old_value': 214690.34, 'new_value': 219396.24}, {'field': 'order_count', 'old_value': 821, 'new_value': 844}]
2025-05-29 09:01:48,743 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-29 09:01:49,446 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-29 09:01:49,446 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 70274.0, 'new_value': 71598.0}, {'field': 'offline_amount', 'old_value': 339056.0, 'new_value': 354384.0}, {'field': 'total_amount', 'old_value': 409330.0, 'new_value': 425982.0}, {'field': 'order_count', 'old_value': 1611, 'new_value': 1666}]
2025-05-29 09:01:49,446 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-29 09:01:49,899 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-29 09:01:49,899 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 217968.0, 'new_value': 218536.0}, {'field': 'total_amount', 'old_value': 217968.0, 'new_value': 218536.0}, {'field': 'order_count', 'old_value': 469, 'new_value': 471}]
2025-05-29 09:01:49,899 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-29 09:01:50,290 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-29 09:01:50,290 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 306720.56, 'new_value': 314802.06}, {'field': 'offline_amount', 'old_value': 218651.56, 'new_value': 223361.65}, {'field': 'total_amount', 'old_value': 525372.12, 'new_value': 538163.71}, {'field': 'order_count', 'old_value': 21322, 'new_value': 21873}]
2025-05-29 09:01:50,290 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJQ
2025-05-29 09:01:50,743 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJQ
2025-05-29 09:01:50,743 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77334.0, 'new_value': 81414.0}, {'field': 'total_amount', 'old_value': 85314.0, 'new_value': 89394.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 23}]
2025-05-29 09:01:50,743 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-29 09:01:51,211 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-29 09:01:51,211 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 184076.6, 'new_value': 191849.6}, {'field': 'total_amount', 'old_value': 344292.38, 'new_value': 352065.38}]
2025-05-29 09:01:51,211 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-29 09:01:51,665 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-29 09:01:51,665 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 209881.0, 'new_value': 215087.0}, {'field': 'offline_amount', 'old_value': 190756.0, 'new_value': 191764.0}, {'field': 'total_amount', 'old_value': 400637.0, 'new_value': 406851.0}, {'field': 'order_count', 'old_value': 1117, 'new_value': 1150}]
2025-05-29 09:01:51,665 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-29 09:01:52,118 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-29 09:01:52,118 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 838028.42, 'new_value': 846257.01}, {'field': 'total_amount', 'old_value': 838028.42, 'new_value': 846257.01}, {'field': 'order_count', 'old_value': 4425, 'new_value': 4501}]
2025-05-29 09:01:52,118 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-29 09:01:52,508 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-29 09:01:52,508 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 148440.95, 'new_value': 151070.33}, {'field': 'total_amount', 'old_value': 148440.95, 'new_value': 151070.33}, {'field': 'order_count', 'old_value': 10338, 'new_value': 10531}]
2025-05-29 09:01:52,508 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-29 09:01:52,946 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-29 09:01:52,946 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 460985.0, 'new_value': 463967.0}, {'field': 'total_amount', 'old_value': 460985.0, 'new_value': 463967.0}, {'field': 'order_count', 'old_value': 10476, 'new_value': 10536}]
2025-05-29 09:01:52,946 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-29 09:01:53,415 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-29 09:01:53,415 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106778.0, 'new_value': 108887.0}, {'field': 'total_amount', 'old_value': 106778.0, 'new_value': 108887.0}, {'field': 'order_count', 'old_value': 7207, 'new_value': 7351}]
2025-05-29 09:01:53,415 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-29 09:01:53,868 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-29 09:01:53,868 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 155895.0, 'new_value': 162826.0}, {'field': 'total_amount', 'old_value': 160634.0, 'new_value': 167565.0}, {'field': 'order_count', 'old_value': 11919, 'new_value': 12458}]
2025-05-29 09:01:53,868 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-29 09:01:54,336 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-29 09:01:54,336 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29517.6, 'new_value': 30750.8}, {'field': 'offline_amount', 'old_value': 67812.9, 'new_value': 69606.5}, {'field': 'total_amount', 'old_value': 97330.5, 'new_value': 100357.3}, {'field': 'order_count', 'old_value': 3658, 'new_value': 3783}]
2025-05-29 09:01:54,336 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM6V
2025-05-29 09:01:54,758 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM6V
2025-05-29 09:01:54,758 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71380.0, 'new_value': 81280.0}, {'field': 'total_amount', 'old_value': 71380.0, 'new_value': 81280.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 14}]
2025-05-29 09:01:54,758 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-29 09:01:55,227 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-29 09:01:55,227 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26595.88, 'new_value': 27326.88}, {'field': 'total_amount', 'old_value': 26595.88, 'new_value': 27326.88}, {'field': 'order_count', 'old_value': 120, 'new_value': 126}]
2025-05-29 09:01:55,227 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-29 09:01:55,649 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-29 09:01:55,649 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 567937.39, 'new_value': 581245.39}, {'field': 'total_amount', 'old_value': 573483.75, 'new_value': 586791.75}, {'field': 'order_count', 'old_value': 5864, 'new_value': 5989}]
2025-05-29 09:01:55,649 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-29 09:01:56,086 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-29 09:01:56,102 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 360764.0, 'new_value': 375114.0}, {'field': 'total_amount', 'old_value': 360764.0, 'new_value': 375114.0}, {'field': 'order_count', 'old_value': 7835, 'new_value': 8120}]
2025-05-29 09:01:56,102 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-29 09:01:56,539 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-29 09:01:56,539 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38043.0, 'new_value': 39668.0}, {'field': 'total_amount', 'old_value': 38043.0, 'new_value': 39668.0}, {'field': 'order_count', 'old_value': 119, 'new_value': 125}]
2025-05-29 09:01:56,539 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-29 09:01:56,930 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-29 09:01:56,930 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 203327.8, 'new_value': 213575.8}, {'field': 'total_amount', 'old_value': 203327.8, 'new_value': 213575.8}, {'field': 'order_count', 'old_value': 7100, 'new_value': 7486}]
2025-05-29 09:01:56,930 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G
2025-05-29 09:01:57,383 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G
2025-05-29 09:01:57,383 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 25, 'new_value': 27}]
2025-05-29 09:01:57,383 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-29 09:01:57,836 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-29 09:01:57,836 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 154436.0, 'new_value': 157637.0}, {'field': 'total_amount', 'old_value': 154436.0, 'new_value': 157637.0}, {'field': 'order_count', 'old_value': 620, 'new_value': 637}]
2025-05-29 09:01:57,836 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-29 09:01:58,305 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-29 09:01:58,305 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57035.0, 'new_value': 60056.0}, {'field': 'total_amount', 'old_value': 57035.0, 'new_value': 60056.0}, {'field': 'order_count', 'old_value': 1094, 'new_value': 1150}]
2025-05-29 09:01:58,305 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-29 09:01:58,743 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-29 09:01:58,743 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40049.0, 'new_value': 42019.0}, {'field': 'total_amount', 'old_value': 80174.4, 'new_value': 82144.4}, {'field': 'order_count', 'old_value': 2804, 'new_value': 2807}]
2025-05-29 09:01:58,743 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-29 09:01:59,227 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-29 09:01:59,227 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50365.48, 'new_value': 57524.81}, {'field': 'offline_amount', 'old_value': 61603.77, 'new_value': 62774.77}, {'field': 'total_amount', 'old_value': 111969.25, 'new_value': 120299.58}, {'field': 'order_count', 'old_value': 511, 'new_value': 543}]
2025-05-29 09:01:59,227 - INFO - 开始更新记录 - 表单实例ID: FINST-1PF66VA1DOOVZDTTANZS29HIONIP3LXHFP1BM44
2025-05-29 09:01:59,649 - INFO - 更新表单数据成功: FINST-1PF66VA1DOOVZDTTANZS29HIONIP3LXHFP1BM44
2025-05-29 09:01:59,649 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3816.75, 'new_value': 4691.55}, {'field': 'total_amount', 'old_value': 3816.75, 'new_value': 4691.55}, {'field': 'order_count', 'old_value': 190, 'new_value': 264}]
2025-05-29 09:01:59,649 - INFO - 开始更新记录 - 表单实例ID: FINST-DO566BD1K3PVOT69FG8V77RAX3DA3W8XU43BMOC
2025-05-29 09:02:00,133 - INFO - 更新表单数据成功: FINST-DO566BD1K3PVOT69FG8V77RAX3DA3W8XU43BMOC
2025-05-29 09:02:00,133 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30000.0, 'new_value': 35000.0}, {'field': 'total_amount', 'old_value': 30000.0, 'new_value': 35000.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-05-29 09:02:00,133 - INFO - 开始批量插入 1 条新记录
2025-05-29 09:02:00,305 - INFO - 批量插入响应状态码: 200
2025-05-29 09:02:00,305 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 29 May 2025 01:02:00 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5A886DD6-5EB0-7772-BD65-D0F075998404', 'x-acs-trace-id': 'f1c877e40cdb3b55672207ee987ebd04', 'etag': '6Rg8YHNDO8MPKKbMVI7RYsA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-29 09:02:00,305 - INFO - 批量插入响应体: {'result': ['FINST-ORA66F81M7SV63GRAIEPN5723LJD2F3O5O8BM9B']}
2025-05-29 09:02:00,305 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-29 09:02:00,305 - INFO - 成功插入的数据ID: ['FINST-ORA66F81M7SV63GRAIEPN5723LJD2F3O5O8BM9B']
2025-05-29 09:02:03,321 - INFO - 批量插入完成，共 1 条记录
2025-05-29 09:02:03,321 - INFO - 日期 2025-05 处理完成 - 更新: 189 条，插入: 1 条，错误: 0 条
2025-05-29 09:02:03,321 - INFO - 数据同步完成！更新: 189 条，插入: 1 条，错误: 0 条
2025-05-29 09:02:03,321 - INFO - =================同步完成====================
2025-05-29 12:00:02,031 - INFO - =================使用默认全量同步=============
2025-05-29 12:00:03,578 - INFO - MySQL查询成功，共获取 3304 条记录
2025-05-29 12:00:03,578 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-29 12:00:03,609 - INFO - 开始处理日期: 2025-01
2025-05-29 12:00:03,609 - INFO - Request Parameters - Page 1:
2025-05-29 12:00:03,609 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:00:03,609 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:00:04,593 - INFO - Response - Page 1:
2025-05-29 12:00:04,797 - INFO - 第 1 页获取到 100 条记录
2025-05-29 12:00:04,797 - INFO - Request Parameters - Page 2:
2025-05-29 12:00:04,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:00:04,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:00:05,843 - INFO - Response - Page 2:
2025-05-29 12:00:06,047 - INFO - 第 2 页获取到 100 条记录
2025-05-29 12:00:06,047 - INFO - Request Parameters - Page 3:
2025-05-29 12:00:06,047 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:00:06,047 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:00:06,593 - INFO - Response - Page 3:
2025-05-29 12:00:06,797 - INFO - 第 3 页获取到 100 条记录
2025-05-29 12:00:06,797 - INFO - Request Parameters - Page 4:
2025-05-29 12:00:06,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:00:06,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:00:07,406 - INFO - Response - Page 4:
2025-05-29 12:00:07,609 - INFO - 第 4 页获取到 100 条记录
2025-05-29 12:00:07,609 - INFO - Request Parameters - Page 5:
2025-05-29 12:00:07,609 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:00:07,609 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:00:08,047 - INFO - Response - Page 5:
2025-05-29 12:00:08,250 - INFO - 第 5 页获取到 100 条记录
2025-05-29 12:00:08,250 - INFO - Request Parameters - Page 6:
2025-05-29 12:00:08,250 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:00:08,250 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:00:08,922 - INFO - Response - Page 6:
2025-05-29 12:00:09,125 - INFO - 第 6 页获取到 100 条记录
2025-05-29 12:00:09,125 - INFO - Request Parameters - Page 7:
2025-05-29 12:00:09,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:00:09,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:00:09,718 - INFO - Response - Page 7:
2025-05-29 12:00:09,922 - INFO - 第 7 页获取到 82 条记录
2025-05-29 12:00:09,922 - INFO - 查询完成，共获取到 682 条记录
2025-05-29 12:00:09,922 - INFO - 获取到 682 条表单数据
2025-05-29 12:00:09,922 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-29 12:00:09,937 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 12:00:09,937 - INFO - 开始处理日期: 2025-02
2025-05-29 12:00:09,937 - INFO - Request Parameters - Page 1:
2025-05-29 12:00:09,937 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:00:09,937 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:00:10,468 - INFO - Response - Page 1:
2025-05-29 12:00:10,672 - INFO - 第 1 页获取到 100 条记录
2025-05-29 12:00:10,672 - INFO - Request Parameters - Page 2:
2025-05-29 12:00:10,672 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:00:10,672 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:00:11,203 - INFO - Response - Page 2:
2025-05-29 12:00:11,406 - INFO - 第 2 页获取到 100 条记录
2025-05-29 12:00:11,406 - INFO - Request Parameters - Page 3:
2025-05-29 12:00:11,406 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:00:11,406 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:00:11,937 - INFO - Response - Page 3:
2025-05-29 12:00:12,140 - INFO - 第 3 页获取到 100 条记录
2025-05-29 12:00:12,140 - INFO - Request Parameters - Page 4:
2025-05-29 12:00:12,140 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:00:12,140 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:00:12,734 - INFO - Response - Page 4:
2025-05-29 12:00:12,937 - INFO - 第 4 页获取到 100 条记录
2025-05-29 12:00:12,937 - INFO - Request Parameters - Page 5:
2025-05-29 12:00:12,937 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:00:12,937 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:00:13,422 - INFO - Response - Page 5:
2025-05-29 12:00:13,625 - INFO - 第 5 页获取到 100 条记录
2025-05-29 12:00:13,625 - INFO - Request Parameters - Page 6:
2025-05-29 12:00:13,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:00:13,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:00:14,093 - INFO - Response - Page 6:
2025-05-29 12:00:14,297 - INFO - 第 6 页获取到 100 条记录
2025-05-29 12:00:14,297 - INFO - Request Parameters - Page 7:
2025-05-29 12:00:14,297 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:00:14,297 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:00:14,718 - INFO - Response - Page 7:
2025-05-29 12:00:14,922 - INFO - 第 7 页获取到 70 条记录
2025-05-29 12:00:14,922 - INFO - 查询完成，共获取到 670 条记录
2025-05-29 12:00:14,922 - INFO - 获取到 670 条表单数据
2025-05-29 12:00:14,922 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-29 12:00:14,937 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 12:00:14,937 - INFO - 开始处理日期: 2025-03
2025-05-29 12:00:14,937 - INFO - Request Parameters - Page 1:
2025-05-29 12:00:14,937 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:00:14,937 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:00:15,468 - INFO - Response - Page 1:
2025-05-29 12:00:15,672 - INFO - 第 1 页获取到 100 条记录
2025-05-29 12:00:15,672 - INFO - Request Parameters - Page 2:
2025-05-29 12:00:15,672 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:00:15,672 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:00:16,172 - INFO - Response - Page 2:
2025-05-29 12:00:16,375 - INFO - 第 2 页获取到 100 条记录
2025-05-29 12:00:16,375 - INFO - Request Parameters - Page 3:
2025-05-29 12:00:16,375 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:00:16,375 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:00:16,906 - INFO - Response - Page 3:
2025-05-29 12:00:17,109 - INFO - 第 3 页获取到 100 条记录
2025-05-29 12:00:17,109 - INFO - Request Parameters - Page 4:
2025-05-29 12:00:17,109 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:00:17,109 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:00:17,625 - INFO - Response - Page 4:
2025-05-29 12:00:17,828 - INFO - 第 4 页获取到 100 条记录
2025-05-29 12:00:17,828 - INFO - Request Parameters - Page 5:
2025-05-29 12:00:17,828 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:00:17,828 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:00:18,375 - INFO - Response - Page 5:
2025-05-29 12:00:18,578 - INFO - 第 5 页获取到 100 条记录
2025-05-29 12:00:18,578 - INFO - Request Parameters - Page 6:
2025-05-29 12:00:18,578 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:00:18,578 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:00:19,062 - INFO - Response - Page 6:
2025-05-29 12:00:19,265 - INFO - 第 6 页获取到 100 条记录
2025-05-29 12:00:19,265 - INFO - Request Parameters - Page 7:
2025-05-29 12:00:19,265 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:00:19,265 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:00:19,718 - INFO - Response - Page 7:
2025-05-29 12:00:19,921 - INFO - 第 7 页获取到 61 条记录
2025-05-29 12:00:19,921 - INFO - 查询完成，共获取到 661 条记录
2025-05-29 12:00:19,921 - INFO - 获取到 661 条表单数据
2025-05-29 12:00:19,921 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-29 12:00:19,937 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 12:00:19,937 - INFO - 开始处理日期: 2025-04
2025-05-29 12:00:19,937 - INFO - Request Parameters - Page 1:
2025-05-29 12:00:19,937 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:00:19,937 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:00:20,500 - INFO - Response - Page 1:
2025-05-29 12:00:20,703 - INFO - 第 1 页获取到 100 条记录
2025-05-29 12:00:20,703 - INFO - Request Parameters - Page 2:
2025-05-29 12:00:20,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:00:20,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:00:21,187 - INFO - Response - Page 2:
2025-05-29 12:00:21,390 - INFO - 第 2 页获取到 100 条记录
2025-05-29 12:00:21,390 - INFO - Request Parameters - Page 3:
2025-05-29 12:00:21,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:00:21,390 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:00:21,875 - INFO - Response - Page 3:
2025-05-29 12:00:22,078 - INFO - 第 3 页获取到 100 条记录
2025-05-29 12:00:22,078 - INFO - Request Parameters - Page 4:
2025-05-29 12:00:22,078 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:00:22,078 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:00:22,671 - INFO - Response - Page 4:
2025-05-29 12:00:22,875 - INFO - 第 4 页获取到 100 条记录
2025-05-29 12:00:22,875 - INFO - Request Parameters - Page 5:
2025-05-29 12:00:22,875 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:00:22,875 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:00:23,390 - INFO - Response - Page 5:
2025-05-29 12:00:23,593 - INFO - 第 5 页获取到 100 条记录
2025-05-29 12:00:23,593 - INFO - Request Parameters - Page 6:
2025-05-29 12:00:23,593 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:00:23,593 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:00:24,093 - INFO - Response - Page 6:
2025-05-29 12:00:24,296 - INFO - 第 6 页获取到 100 条记录
2025-05-29 12:00:24,296 - INFO - Request Parameters - Page 7:
2025-05-29 12:00:24,296 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:00:24,296 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:00:24,750 - INFO - Response - Page 7:
2025-05-29 12:00:24,953 - INFO - 第 7 页获取到 56 条记录
2025-05-29 12:00:24,953 - INFO - 查询完成，共获取到 656 条记录
2025-05-29 12:00:24,953 - INFO - 获取到 656 条表单数据
2025-05-29 12:00:24,953 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-29 12:00:24,968 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 12:00:24,968 - INFO - 开始处理日期: 2025-05
2025-05-29 12:00:24,968 - INFO - Request Parameters - Page 1:
2025-05-29 12:00:24,968 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:00:24,968 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:00:25,453 - INFO - Response - Page 1:
2025-05-29 12:00:25,656 - INFO - 第 1 页获取到 100 条记录
2025-05-29 12:00:25,656 - INFO - Request Parameters - Page 2:
2025-05-29 12:00:25,656 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:00:25,656 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:00:26,140 - INFO - Response - Page 2:
2025-05-29 12:00:26,343 - INFO - 第 2 页获取到 100 条记录
2025-05-29 12:00:26,343 - INFO - Request Parameters - Page 3:
2025-05-29 12:00:26,343 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:00:26,343 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:00:26,890 - INFO - Response - Page 3:
2025-05-29 12:00:27,093 - INFO - 第 3 页获取到 100 条记录
2025-05-29 12:00:27,093 - INFO - Request Parameters - Page 4:
2025-05-29 12:00:27,093 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:00:27,093 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:00:27,703 - INFO - Response - Page 4:
2025-05-29 12:00:27,906 - INFO - 第 4 页获取到 100 条记录
2025-05-29 12:00:27,906 - INFO - Request Parameters - Page 5:
2025-05-29 12:00:27,906 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:00:27,906 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:00:28,421 - INFO - Response - Page 5:
2025-05-29 12:00:28,625 - INFO - 第 5 页获取到 100 条记录
2025-05-29 12:00:28,625 - INFO - Request Parameters - Page 6:
2025-05-29 12:00:28,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:00:28,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:00:29,109 - INFO - Response - Page 6:
2025-05-29 12:00:29,328 - INFO - 第 6 页获取到 100 条记录
2025-05-29 12:00:29,328 - INFO - Request Parameters - Page 7:
2025-05-29 12:00:29,328 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 12:00:29,328 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 12:00:29,734 - INFO - Response - Page 7:
2025-05-29 12:00:29,937 - INFO - 第 7 页获取到 35 条记录
2025-05-29 12:00:29,937 - INFO - 查询完成，共获取到 635 条记录
2025-05-29 12:00:29,937 - INFO - 获取到 635 条表单数据
2025-05-29 12:00:29,937 - INFO - 当前日期 2025-05 有 635 条MySQL数据需要处理
2025-05-29 12:00:29,937 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-29 12:00:30,375 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-29 12:00:30,375 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2132.0, 'new_value': 2277.0}, {'field': 'offline_amount', 'old_value': 45318.0, 'new_value': 46303.0}, {'field': 'total_amount', 'old_value': 47450.0, 'new_value': 48580.0}, {'field': 'order_count', 'old_value': 652, 'new_value': 670}]
2025-05-29 12:00:30,375 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-29 12:00:30,890 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-29 12:00:30,890 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56445.0, 'new_value': 57505.0}, {'field': 'total_amount', 'old_value': 58035.0, 'new_value': 59095.0}, {'field': 'order_count', 'old_value': 219, 'new_value': 223}]
2025-05-29 12:00:30,890 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-29 12:00:31,421 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-29 12:00:31,421 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 199039.74, 'new_value': 209283.39}, {'field': 'total_amount', 'old_value': 199039.74, 'new_value': 209283.39}, {'field': 'order_count', 'old_value': 7379, 'new_value': 7730}]
2025-05-29 12:00:31,421 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-29 12:00:31,875 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-29 12:00:31,875 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94514.0, 'new_value': 94778.0}, {'field': 'total_amount', 'old_value': 97411.0, 'new_value': 97675.0}, {'field': 'order_count', 'old_value': 44, 'new_value': 45}]
2025-05-29 12:00:31,875 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-29 12:00:32,234 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-29 12:00:32,234 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 463208.0, 'new_value': 473306.0}, {'field': 'offline_amount', 'old_value': 351335.0, 'new_value': 358245.0}, {'field': 'total_amount', 'old_value': 814543.0, 'new_value': 831551.0}, {'field': 'order_count', 'old_value': 895, 'new_value': 919}]
2025-05-29 12:00:32,234 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4F
2025-05-29 12:00:32,734 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4F
2025-05-29 12:00:32,734 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27127.0, 'new_value': 28155.0}, {'field': 'total_amount', 'old_value': 28119.0, 'new_value': 29147.0}, {'field': 'order_count', 'old_value': 2938, 'new_value': 3018}]
2025-05-29 12:00:32,734 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-29 12:00:33,140 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-29 12:00:33,140 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52804.5, 'new_value': 53061.3}, {'field': 'total_amount', 'old_value': 56764.5, 'new_value': 57021.3}, {'field': 'order_count', 'old_value': 438, 'new_value': 452}]
2025-05-29 12:00:33,140 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-29 12:00:33,640 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-29 12:00:33,640 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70008.0, 'new_value': 70604.0}, {'field': 'total_amount', 'old_value': 70307.92, 'new_value': 70903.92}, {'field': 'order_count', 'old_value': 114, 'new_value': 116}]
2025-05-29 12:00:33,640 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-29 12:00:34,093 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-29 12:00:34,093 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 460711.71, 'new_value': 471401.71}, {'field': 'total_amount', 'old_value': 493894.71, 'new_value': 504584.71}, {'field': 'order_count', 'old_value': 468, 'new_value': 478}]
2025-05-29 12:00:34,093 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMYC
2025-05-29 12:00:34,546 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMYC
2025-05-29 12:00:34,546 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21487.0, 'new_value': 23506.5}, {'field': 'total_amount', 'old_value': 21487.0, 'new_value': 23506.5}, {'field': 'order_count', 'old_value': 13, 'new_value': 15}]
2025-05-29 12:00:34,546 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5F
2025-05-29 12:00:35,015 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5F
2025-05-29 12:00:35,015 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55747.0, 'new_value': 58915.0}, {'field': 'offline_amount', 'old_value': 193392.98, 'new_value': 199462.98}, {'field': 'total_amount', 'old_value': 249139.98, 'new_value': 258377.98}, {'field': 'order_count', 'old_value': 1698, 'new_value': 1768}]
2025-05-29 12:00:35,015 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-29 12:00:35,468 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-29 12:00:35,468 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 153150.01, 'new_value': 157508.24}, {'field': 'total_amount', 'old_value': 153150.01, 'new_value': 157508.24}, {'field': 'order_count', 'old_value': 1806, 'new_value': 1865}]
2025-05-29 12:00:35,468 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-29 12:00:35,890 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-29 12:00:35,890 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1143368.0, 'new_value': 1202046.0}, {'field': 'offline_amount', 'old_value': 325885.0, 'new_value': 344657.0}, {'field': 'total_amount', 'old_value': 1469253.0, 'new_value': 1546703.0}, {'field': 'order_count', 'old_value': 1733, 'new_value': 1799}]
2025-05-29 12:00:35,890 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6F
2025-05-29 12:00:36,343 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6F
2025-05-29 12:00:36,343 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13596.43, 'new_value': 14267.96}, {'field': 'offline_amount', 'old_value': 38594.61, 'new_value': 41178.11}, {'field': 'total_amount', 'old_value': 52191.04, 'new_value': 55446.07}, {'field': 'order_count', 'old_value': 952, 'new_value': 1027}]
2025-05-29 12:00:36,343 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-29 12:00:36,843 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-29 12:00:36,843 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 267205.0, 'new_value': 277005.0}, {'field': 'total_amount', 'old_value': 297205.0, 'new_value': 307005.0}, {'field': 'order_count', 'old_value': 47, 'new_value': 48}]
2025-05-29 12:00:36,843 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-29 12:00:37,281 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-29 12:00:37,281 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 327021.89, 'new_value': 334621.89}, {'field': 'total_amount', 'old_value': 367021.89, 'new_value': 374621.89}, {'field': 'order_count', 'old_value': 62, 'new_value': 63}]
2025-05-29 12:00:37,281 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-29 12:00:37,796 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-29 12:00:37,796 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 289725.13, 'new_value': 298425.13}, {'field': 'total_amount', 'old_value': 359085.13, 'new_value': 367785.13}, {'field': 'order_count', 'old_value': 53, 'new_value': 54}]
2025-05-29 12:00:37,796 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7F
2025-05-29 12:00:38,265 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7F
2025-05-29 12:00:38,265 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27915.11, 'new_value': 29145.55}, {'field': 'offline_amount', 'old_value': 115398.37, 'new_value': 120782.18}, {'field': 'total_amount', 'old_value': 143313.48, 'new_value': 149927.73}, {'field': 'order_count', 'old_value': 1971, 'new_value': 2055}]
2025-05-29 12:00:38,265 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-29 12:00:38,781 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-29 12:00:38,781 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 176910.57, 'new_value': 196344.62}, {'field': 'total_amount', 'old_value': 176910.57, 'new_value': 196344.62}, {'field': 'order_count', 'old_value': 285, 'new_value': 302}]
2025-05-29 12:00:38,781 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-05-29 12:00:39,234 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-05-29 12:00:39,234 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15813.0, 'new_value': 16031.0}, {'field': 'total_amount', 'old_value': 15813.0, 'new_value': 16031.0}, {'field': 'order_count', 'old_value': 41, 'new_value': 42}]
2025-05-29 12:00:39,234 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-29 12:00:39,749 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-29 12:00:39,749 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86443.0, 'new_value': 88223.0}, {'field': 'total_amount', 'old_value': 86443.0, 'new_value': 88223.0}, {'field': 'order_count', 'old_value': 737, 'new_value': 752}]
2025-05-29 12:00:39,749 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-29 12:00:40,203 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-29 12:00:40,203 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41539.27, 'new_value': 44427.53}, {'field': 'total_amount', 'old_value': 41539.27, 'new_value': 44427.53}, {'field': 'order_count', 'old_value': 3044, 'new_value': 3241}]
2025-05-29 12:00:40,203 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-29 12:00:40,656 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-29 12:00:40,656 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42835.0, 'new_value': 43104.0}, {'field': 'total_amount', 'old_value': 48211.0, 'new_value': 48480.0}, {'field': 'order_count', 'old_value': 221, 'new_value': 223}]
2025-05-29 12:00:40,656 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-29 12:00:40,999 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-29 12:00:40,999 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71686.86, 'new_value': 73381.56}, {'field': 'total_amount', 'old_value': 71686.86, 'new_value': 73381.56}, {'field': 'order_count', 'old_value': 126, 'new_value': 132}]
2025-05-29 12:00:40,999 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8F
2025-05-29 12:00:41,531 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8F
2025-05-29 12:00:41,531 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55518.16, 'new_value': 60825.67}, {'field': 'offline_amount', 'old_value': 550436.38, 'new_value': 589416.1}, {'field': 'total_amount', 'old_value': 605954.54, 'new_value': 650241.77}, {'field': 'order_count', 'old_value': 1969, 'new_value': 2099}]
2025-05-29 12:00:41,531 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-29 12:00:41,984 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-29 12:00:41,984 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11.9, 'new_value': 70.9}, {'field': 'offline_amount', 'old_value': 52008.8, 'new_value': 53783.0}, {'field': 'total_amount', 'old_value': 52020.7, 'new_value': 53853.9}, {'field': 'order_count', 'old_value': 311, 'new_value': 321}]
2025-05-29 12:00:41,984 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9F
2025-05-29 12:00:42,468 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9F
2025-05-29 12:00:42,468 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30312.72, 'new_value': 32172.89}, {'field': 'offline_amount', 'old_value': 23123.61, 'new_value': 24080.33}, {'field': 'total_amount', 'old_value': 53436.33, 'new_value': 56253.22}, {'field': 'order_count', 'old_value': 3076, 'new_value': 3244}]
2025-05-29 12:00:42,468 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-29 12:00:42,890 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-29 12:00:42,890 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 149569.0, 'new_value': 191358.0}, {'field': 'offline_amount', 'old_value': 92419.0, 'new_value': 99877.0}, {'field': 'total_amount', 'old_value': 241988.0, 'new_value': 291235.0}, {'field': 'order_count', 'old_value': 76, 'new_value': 89}]
2025-05-29 12:00:42,890 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAF
2025-05-29 12:00:43,343 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAF
2025-05-29 12:00:43,343 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5879.36, 'new_value': 6099.74}, {'field': 'offline_amount', 'old_value': 132243.52, 'new_value': 137149.58}, {'field': 'total_amount', 'old_value': 138122.88, 'new_value': 143249.32}, {'field': 'order_count', 'old_value': 2215, 'new_value': 2298}]
2025-05-29 12:00:43,343 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-29 12:00:43,843 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-29 12:00:43,843 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33984.0, 'new_value': 35211.0}, {'field': 'total_amount', 'old_value': 35684.0, 'new_value': 36911.0}, {'field': 'order_count', 'old_value': 130, 'new_value': 138}]
2025-05-29 12:00:43,843 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-29 12:00:44,328 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-29 12:00:44,328 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7866.15, 'new_value': 7879.7}, {'field': 'offline_amount', 'old_value': 113726.29, 'new_value': 115934.24}, {'field': 'total_amount', 'old_value': 121592.44, 'new_value': 123813.94}, {'field': 'order_count', 'old_value': 2944, 'new_value': 3004}]
2025-05-29 12:00:44,328 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-29 12:00:44,906 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-29 12:00:44,906 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 238402.0, 'new_value': 245369.0}, {'field': 'total_amount', 'old_value': 238402.0, 'new_value': 245369.0}, {'field': 'order_count', 'old_value': 1246, 'new_value': 1293}]
2025-05-29 12:00:44,906 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMBF
2025-05-29 12:00:45,296 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMBF
2025-05-29 12:00:45,296 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82865.89, 'new_value': 85847.32}, {'field': 'total_amount', 'old_value': 82865.89, 'new_value': 85847.32}, {'field': 'order_count', 'old_value': 4349, 'new_value': 4509}]
2025-05-29 12:00:45,296 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-29 12:00:45,765 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-29 12:00:45,765 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 86988.23, 'new_value': 91047.21}, {'field': 'offline_amount', 'old_value': 42694.53, 'new_value': 44328.96}, {'field': 'total_amount', 'old_value': 129682.76, 'new_value': 135376.17}, {'field': 'order_count', 'old_value': 4481, 'new_value': 4701}]
2025-05-29 12:00:45,765 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-29 12:00:46,171 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-29 12:00:46,171 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 93069.99, 'new_value': 96205.38}, {'field': 'offline_amount', 'old_value': 934041.36, 'new_value': 966944.26}, {'field': 'total_amount', 'old_value': 1027111.35, 'new_value': 1063149.64}, {'field': 'order_count', 'old_value': 3243, 'new_value': 3355}]
2025-05-29 12:00:46,171 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E
2025-05-29 12:00:46,640 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E
2025-05-29 12:00:46,640 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53374.0, 'new_value': 57054.0}, {'field': 'total_amount', 'old_value': 53374.0, 'new_value': 57054.0}, {'field': 'order_count', 'old_value': 65, 'new_value': 69}]
2025-05-29 12:00:46,640 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-29 12:00:47,124 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-29 12:00:47,124 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 125856.0, 'new_value': 145854.0}, {'field': 'offline_amount', 'old_value': 76171.0, 'new_value': 76970.0}, {'field': 'total_amount', 'old_value': 202027.0, 'new_value': 222824.0}, {'field': 'order_count', 'old_value': 76, 'new_value': 79}]
2025-05-29 12:00:47,124 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCF
2025-05-29 12:00:47,578 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCF
2025-05-29 12:00:47,578 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 320109.58, 'new_value': 344372.68}, {'field': 'total_amount', 'old_value': 320918.58, 'new_value': 345181.68}, {'field': 'order_count', 'old_value': 3657, 'new_value': 3980}]
2025-05-29 12:00:47,578 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-29 12:00:48,031 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-29 12:00:48,031 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4389.18, 'new_value': 4518.7}, {'field': 'offline_amount', 'old_value': 137212.99, 'new_value': 141588.59}, {'field': 'total_amount', 'old_value': 141602.17, 'new_value': 146107.29}, {'field': 'order_count', 'old_value': 651, 'new_value': 675}]
2025-05-29 12:00:48,031 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-29 12:00:48,468 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-29 12:00:48,468 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30280.47, 'new_value': 30809.47}, {'field': 'total_amount', 'old_value': 30280.47, 'new_value': 30809.47}, {'field': 'order_count', 'old_value': 176, 'new_value': 181}]
2025-05-29 12:00:48,468 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-29 12:00:48,921 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-29 12:00:48,921 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7069.58, 'new_value': 7295.85}, {'field': 'offline_amount', 'old_value': 216335.52, 'new_value': 225822.35}, {'field': 'total_amount', 'old_value': 223405.1, 'new_value': 233118.2}, {'field': 'order_count', 'old_value': 1406, 'new_value': 1462}]
2025-05-29 12:00:48,921 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDF
2025-05-29 12:00:49,374 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDF
2025-05-29 12:00:49,374 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59498.6, 'new_value': 60205.6}, {'field': 'total_amount', 'old_value': 59498.6, 'new_value': 60205.6}, {'field': 'order_count', 'old_value': 251, 'new_value': 257}]
2025-05-29 12:00:49,374 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-29 12:00:49,890 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-29 12:00:49,890 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 177759.8, 'new_value': 192742.8}, {'field': 'total_amount', 'old_value': 185335.6, 'new_value': 200318.6}, {'field': 'order_count', 'old_value': 29, 'new_value': 30}]
2025-05-29 12:00:49,890 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-29 12:00:50,312 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-29 12:00:50,312 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50470.4, 'new_value': 52194.02}, {'field': 'offline_amount', 'old_value': 98790.66, 'new_value': 100142.7}, {'field': 'total_amount', 'old_value': 149261.06, 'new_value': 152336.72}, {'field': 'order_count', 'old_value': 5475, 'new_value': 5610}]
2025-05-29 12:00:50,312 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-29 12:00:50,906 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-29 12:00:50,906 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15423.87, 'new_value': 15509.7}, {'field': 'offline_amount', 'old_value': 321623.68, 'new_value': 321793.98}, {'field': 'total_amount', 'old_value': 337047.55, 'new_value': 337303.68}, {'field': 'order_count', 'old_value': 2307, 'new_value': 2319}]
2025-05-29 12:00:50,906 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-29 12:00:51,406 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-29 12:00:51,406 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 297469.8, 'new_value': 383802.8}, {'field': 'total_amount', 'old_value': 324011.8, 'new_value': 410344.8}, {'field': 'order_count', 'old_value': 90, 'new_value': 96}]
2025-05-29 12:00:51,406 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-29 12:00:51,937 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-29 12:00:51,937 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 163988.81, 'new_value': 169019.43}, {'field': 'offline_amount', 'old_value': 122947.45, 'new_value': 125010.45}, {'field': 'total_amount', 'old_value': 286936.26, 'new_value': 294029.88}, {'field': 'order_count', 'old_value': 2835, 'new_value': 2909}]
2025-05-29 12:00:51,937 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-29 12:00:52,484 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-29 12:00:52,484 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 680147.0, 'new_value': 746147.0}, {'field': 'total_amount', 'old_value': 680147.0, 'new_value': 746147.0}, {'field': 'order_count', 'old_value': 82, 'new_value': 85}]
2025-05-29 12:00:52,484 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-29 12:00:52,890 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-29 12:00:52,890 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102407.87, 'new_value': 104076.01}, {'field': 'total_amount', 'old_value': 102407.87, 'new_value': 104076.01}, {'field': 'order_count', 'old_value': 3910, 'new_value': 3974}]
2025-05-29 12:00:52,890 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-29 12:00:53,328 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-29 12:00:53,328 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 100073.12, 'new_value': 105094.12}, {'field': 'offline_amount', 'old_value': 1249918.16, 'new_value': 1306896.37}, {'field': 'total_amount', 'old_value': 1349991.28, 'new_value': 1411990.49}, {'field': 'order_count', 'old_value': 10962, 'new_value': 11398}]
2025-05-29 12:00:53,328 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-29 12:00:53,890 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-29 12:00:53,890 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15576.6, 'new_value': 16727.33}, {'field': 'total_amount', 'old_value': 26717.53, 'new_value': 27868.26}, {'field': 'order_count', 'old_value': 112, 'new_value': 119}]
2025-05-29 12:00:53,890 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-29 12:00:54,296 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-29 12:00:54,296 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 113851.44, 'new_value': 115293.34}, {'field': 'total_amount', 'old_value': 113851.44, 'new_value': 115293.34}, {'field': 'order_count', 'old_value': 829, 'new_value': 841}]
2025-05-29 12:00:54,296 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-29 12:00:54,702 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-29 12:00:54,702 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 85730.59, 'new_value': 88817.59}, {'field': 'total_amount', 'old_value': 85730.59, 'new_value': 88817.59}, {'field': 'order_count', 'old_value': 491, 'new_value': 506}]
2025-05-29 12:00:54,702 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-29 12:00:55,327 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-29 12:00:55,327 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7879.0, 'new_value': 8179.0}, {'field': 'total_amount', 'old_value': 7879.0, 'new_value': 8179.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 46}]
2025-05-29 12:00:55,327 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-29 12:00:55,827 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-29 12:00:55,827 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109399.2, 'new_value': 111645.1}, {'field': 'total_amount', 'old_value': 109399.2, 'new_value': 111645.1}, {'field': 'order_count', 'old_value': 340, 'new_value': 349}]
2025-05-29 12:00:55,827 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-29 12:00:56,281 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-29 12:00:56,281 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 222747.25, 'new_value': 234336.69}, {'field': 'offline_amount', 'old_value': 380429.5, 'new_value': 385640.5}, {'field': 'total_amount', 'old_value': 603176.75, 'new_value': 619977.19}, {'field': 'order_count', 'old_value': 17058, 'new_value': 17687}]
2025-05-29 12:00:56,281 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-29 12:00:56,734 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-29 12:00:56,734 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46532.0, 'new_value': 47245.0}, {'field': 'total_amount', 'old_value': 46532.0, 'new_value': 47245.0}, {'field': 'order_count', 'old_value': 130, 'new_value': 133}]
2025-05-29 12:00:56,734 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-29 12:00:57,202 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-29 12:00:57,202 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38887.0, 'new_value': 39557.0}, {'field': 'total_amount', 'old_value': 38887.0, 'new_value': 39557.0}, {'field': 'order_count', 'old_value': 117, 'new_value': 120}]
2025-05-29 12:00:57,202 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-29 12:00:57,827 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-29 12:00:57,827 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1369000.0, 'new_value': 1409000.0}, {'field': 'total_amount', 'old_value': 1369000.0, 'new_value': 1409000.0}, {'field': 'order_count', 'old_value': 346, 'new_value': 347}]
2025-05-29 12:00:57,827 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-29 12:00:58,312 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-29 12:00:58,312 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30123.6, 'new_value': 31086.0}, {'field': 'offline_amount', 'old_value': 22926.5, 'new_value': 23601.5}, {'field': 'total_amount', 'old_value': 53050.1, 'new_value': 54687.5}, {'field': 'order_count', 'old_value': 289, 'new_value': 297}]
2025-05-29 12:00:58,312 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEF
2025-05-29 12:00:58,781 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEF
2025-05-29 12:00:58,781 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20188.01, 'new_value': 22098.01}, {'field': 'offline_amount', 'old_value': 32670.38, 'new_value': 35693.39}, {'field': 'total_amount', 'old_value': 52858.39, 'new_value': 57791.4}, {'field': 'order_count', 'old_value': 2446, 'new_value': 2703}]
2025-05-29 12:00:58,781 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-29 12:00:59,265 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-29 12:00:59,265 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18568.09, 'new_value': 18740.79}, {'field': 'offline_amount', 'old_value': 293509.14, 'new_value': 302773.24}, {'field': 'total_amount', 'old_value': 312077.23, 'new_value': 321514.03}, {'field': 'order_count', 'old_value': 17317, 'new_value': 17924}]
2025-05-29 12:00:59,265 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-05-29 12:00:59,702 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-05-29 12:00:59,702 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7399.0, 'new_value': 7498.0}, {'field': 'total_amount', 'old_value': 7399.0, 'new_value': 7498.0}, {'field': 'order_count', 'old_value': 36, 'new_value': 37}]
2025-05-29 12:00:59,702 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-29 12:01:00,281 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-29 12:01:00,281 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 314873.6, 'new_value': 327128.43}, {'field': 'total_amount', 'old_value': 314873.6, 'new_value': 327128.43}, {'field': 'order_count', 'old_value': 8756, 'new_value': 9113}]
2025-05-29 12:01:00,281 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-29 12:01:00,718 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-29 12:01:00,718 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53465.81, 'new_value': 55253.34}, {'field': 'offline_amount', 'old_value': 37198.0, 'new_value': 38151.0}, {'field': 'total_amount', 'old_value': 90663.81, 'new_value': 93404.34}, {'field': 'order_count', 'old_value': 1128, 'new_value': 1163}]
2025-05-29 12:01:00,734 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-29 12:01:01,296 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-29 12:01:01,296 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 98760.47, 'new_value': 102356.84}, {'field': 'offline_amount', 'old_value': 72548.66, 'new_value': 74633.98}, {'field': 'total_amount', 'old_value': 171309.13, 'new_value': 176990.82}, {'field': 'order_count', 'old_value': 7282, 'new_value': 7539}]
2025-05-29 12:01:01,296 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-29 12:01:01,827 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-29 12:01:01,827 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14967.26, 'new_value': 15283.96}, {'field': 'offline_amount', 'old_value': 98071.25, 'new_value': 100606.68}, {'field': 'total_amount', 'old_value': 113038.51, 'new_value': 115890.64}, {'field': 'order_count', 'old_value': 3004, 'new_value': 3069}]
2025-05-29 12:01:01,827 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-29 12:01:02,296 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-29 12:01:02,296 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 111210.0, 'new_value': 116210.0}, {'field': 'total_amount', 'old_value': 111210.0, 'new_value': 116210.0}, {'field': 'order_count', 'old_value': 5397, 'new_value': 5633}]
2025-05-29 12:01:02,296 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-29 12:01:02,812 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-29 12:01:02,812 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12274.17, 'new_value': 12760.9}, {'field': 'offline_amount', 'old_value': 13059.41, 'new_value': 13296.61}, {'field': 'total_amount', 'old_value': 25333.58, 'new_value': 26057.51}, {'field': 'order_count', 'old_value': 2073, 'new_value': 2145}]
2025-05-29 12:01:02,812 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-29 12:01:03,374 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-29 12:01:03,374 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6870171.0, 'new_value': 7060275.0}, {'field': 'total_amount', 'old_value': 6870171.0, 'new_value': 7060275.0}, {'field': 'order_count', 'old_value': 116502, 'new_value': 119908}]
2025-05-29 12:01:03,374 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-29 12:01:03,874 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-29 12:01:03,874 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53974.0, 'new_value': 54384.0}, {'field': 'total_amount', 'old_value': 53974.0, 'new_value': 54384.0}, {'field': 'order_count', 'old_value': 389, 'new_value': 393}]
2025-05-29 12:01:03,874 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-29 12:01:04,374 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-29 12:01:04,374 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 61659.52, 'new_value': 63714.57}, {'field': 'offline_amount', 'old_value': 417669.84, 'new_value': 422208.17}, {'field': 'total_amount', 'old_value': 479329.36, 'new_value': 485922.74}, {'field': 'order_count', 'old_value': 3995, 'new_value': 4058}]
2025-05-29 12:01:04,374 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-29 12:01:04,796 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-29 12:01:04,796 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 131298.0, 'new_value': 135798.0}, {'field': 'total_amount', 'old_value': 131298.0, 'new_value': 135798.0}, {'field': 'order_count', 'old_value': 4761, 'new_value': 4762}]
2025-05-29 12:01:04,796 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-29 12:01:05,327 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-29 12:01:05,327 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 93046.17, 'new_value': 95653.37}, {'field': 'offline_amount', 'old_value': 40632.21, 'new_value': 41171.31}, {'field': 'total_amount', 'old_value': 133678.38, 'new_value': 136824.68}, {'field': 'order_count', 'old_value': 8222, 'new_value': 8410}]
2025-05-29 12:01:05,327 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-29 12:01:05,906 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-29 12:01:05,906 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33744.07, 'new_value': 34944.07}, {'field': 'total_amount', 'old_value': 33744.07, 'new_value': 34944.07}, {'field': 'order_count', 'old_value': 3228, 'new_value': 3229}]
2025-05-29 12:01:05,906 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFF
2025-05-29 12:01:06,359 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFF
2025-05-29 12:01:06,359 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 173957.03, 'new_value': 180137.43}, {'field': 'total_amount', 'old_value': 173957.03, 'new_value': 180137.43}, {'field': 'order_count', 'old_value': 293, 'new_value': 308}]
2025-05-29 12:01:06,359 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-29 12:01:06,812 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-29 12:01:06,812 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 115102.98, 'new_value': 117678.98}, {'field': 'total_amount', 'old_value': 115102.98, 'new_value': 117678.98}, {'field': 'order_count', 'old_value': 1051, 'new_value': 1076}]
2025-05-29 12:01:06,812 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-29 12:01:07,359 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-29 12:01:07,359 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 105293.51, 'new_value': 109803.78}, {'field': 'offline_amount', 'old_value': 269513.33, 'new_value': 274352.01}, {'field': 'total_amount', 'old_value': 374806.84, 'new_value': 384155.79}, {'field': 'order_count', 'old_value': 12653, 'new_value': 13012}]
2025-05-29 12:01:07,359 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-29 12:01:07,796 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-29 12:01:07,796 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7640000.0, 'new_value': 7680000.0}, {'field': 'total_amount', 'old_value': 7640000.0, 'new_value': 7680000.0}, {'field': 'order_count', 'old_value': 70, 'new_value': 71}]
2025-05-29 12:01:07,796 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGF
2025-05-29 12:01:08,265 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGF
2025-05-29 12:01:08,265 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81780.0, 'new_value': 84595.0}, {'field': 'total_amount', 'old_value': 81780.0, 'new_value': 84595.0}, {'field': 'order_count', 'old_value': 4629, 'new_value': 4794}]
2025-05-29 12:01:08,265 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-29 12:01:08,687 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-29 12:01:08,687 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 332976.53, 'new_value': 343076.85}, {'field': 'offline_amount', 'old_value': 19644.14, 'new_value': 19823.74}, {'field': 'total_amount', 'old_value': 352620.67, 'new_value': 362900.59}, {'field': 'order_count', 'old_value': 14023, 'new_value': 14476}]
2025-05-29 12:01:08,687 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-29 12:01:09,124 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-29 12:01:09,124 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122767.9, 'new_value': 125066.9}, {'field': 'total_amount', 'old_value': 144421.8, 'new_value': 146720.8}, {'field': 'order_count', 'old_value': 198, 'new_value': 200}]
2025-05-29 12:01:09,124 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-29 12:01:09,546 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-29 12:01:09,546 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 447719.0, 'new_value': 449719.0}, {'field': 'total_amount', 'old_value': 456537.99, 'new_value': 458537.99}, {'field': 'order_count', 'old_value': 81, 'new_value': 82}]
2025-05-29 12:01:09,546 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-29 12:01:10,031 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-29 12:01:10,031 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 143651.06, 'new_value': 145288.23}, {'field': 'offline_amount', 'old_value': 286766.09, 'new_value': 295375.92}, {'field': 'total_amount', 'old_value': 430417.15, 'new_value': 440664.15}, {'field': 'order_count', 'old_value': 5322, 'new_value': 5502}]
2025-05-29 12:01:10,031 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-29 12:01:10,468 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-29 12:01:10,468 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82335.65, 'new_value': 83031.65}, {'field': 'total_amount', 'old_value': 82335.65, 'new_value': 83031.65}, {'field': 'order_count', 'old_value': 599, 'new_value': 610}]
2025-05-29 12:01:10,468 - INFO - 开始更新记录 - 表单实例ID: FINST-8LC66GC10PZUU9MTETB405L5IF663D3CEZ6AMYB
2025-05-29 12:01:10,937 - INFO - 更新表单数据成功: FINST-8LC66GC10PZUU9MTETB405L5IF663D3CEZ6AMYB
2025-05-29 12:01:10,937 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 335394.38, 'new_value': 350394.38}, {'field': 'total_amount', 'old_value': 368474.59, 'new_value': 383474.59}, {'field': 'order_count', 'old_value': 15553, 'new_value': 16003}]
2025-05-29 12:01:10,937 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-29 12:01:11,421 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-29 12:01:11,421 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 479892.17, 'new_value': 499892.17}, {'field': 'total_amount', 'old_value': 479892.17, 'new_value': 499892.17}, {'field': 'order_count', 'old_value': 861, 'new_value': 862}]
2025-05-29 12:01:11,421 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-29 12:01:11,906 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-29 12:01:11,906 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1750.0, 'new_value': 2300.0}, {'field': 'offline_amount', 'old_value': 23594.14, 'new_value': 23709.29}, {'field': 'total_amount', 'old_value': 25344.14, 'new_value': 26009.29}, {'field': 'order_count', 'old_value': 491, 'new_value': 502}]
2025-05-29 12:01:11,906 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-29 12:01:12,312 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-29 12:01:12,312 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31518.0, 'new_value': 31938.0}, {'field': 'total_amount', 'old_value': 31518.0, 'new_value': 31938.0}, {'field': 'order_count', 'old_value': 304, 'new_value': 307}]
2025-05-29 12:01:12,312 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-29 12:01:12,702 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-29 12:01:12,702 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92498.0, 'new_value': 94489.0}, {'field': 'total_amount', 'old_value': 102409.0, 'new_value': 104400.0}, {'field': 'order_count', 'old_value': 57, 'new_value': 59}]
2025-05-29 12:01:12,702 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-29 12:01:13,265 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-29 12:01:13,265 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95697.0, 'new_value': 95897.0}, {'field': 'total_amount', 'old_value': 95697.0, 'new_value': 95897.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 39}]
2025-05-29 12:01:13,265 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHF
2025-05-29 12:01:13,734 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHF
2025-05-29 12:01:13,734 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 113400.15, 'new_value': 117121.15}, {'field': 'total_amount', 'old_value': 113400.15, 'new_value': 117121.15}, {'field': 'order_count', 'old_value': 561, 'new_value': 580}]
2025-05-29 12:01:13,734 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-29 12:01:14,187 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-29 12:01:14,187 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29712.0, 'new_value': 30404.0}, {'field': 'total_amount', 'old_value': 29712.0, 'new_value': 30404.0}, {'field': 'order_count', 'old_value': 314, 'new_value': 324}]
2025-05-29 12:01:14,187 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-29 12:01:14,593 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-29 12:01:14,593 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 85005.04, 'new_value': 87442.7}, {'field': 'offline_amount', 'old_value': 123263.59, 'new_value': 124903.09}, {'field': 'total_amount', 'old_value': 208268.63, 'new_value': 212345.79}, {'field': 'order_count', 'old_value': 2180, 'new_value': 2223}]
2025-05-29 12:01:14,593 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-29 12:01:15,093 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-29 12:01:15,093 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1946866.0, 'new_value': 1867138.51}, {'field': 'offline_amount', 'old_value': 162440.3, 'new_value': 230785.59}, {'field': 'total_amount', 'old_value': 2109306.3, 'new_value': 2097924.1}, {'field': 'order_count', 'old_value': 7276, 'new_value': 7284}]
2025-05-29 12:01:15,093 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-29 12:01:15,593 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-29 12:01:15,593 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36111.0, 'new_value': 37711.0}, {'field': 'total_amount', 'old_value': 37487.0, 'new_value': 39087.0}, {'field': 'order_count', 'old_value': 3771, 'new_value': 3772}]
2025-05-29 12:01:15,593 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-05-29 12:01:16,062 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-05-29 12:01:16,062 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12083.0, 'new_value': 12216.0}, {'field': 'total_amount', 'old_value': 12083.0, 'new_value': 12216.0}, {'field': 'order_count', 'old_value': 87, 'new_value': 89}]
2025-05-29 12:01:16,062 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-29 12:01:16,546 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-29 12:01:16,546 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11578.0, 'new_value': 11739.0}, {'field': 'offline_amount', 'old_value': 2006.0, 'new_value': 2008.0}, {'field': 'total_amount', 'old_value': 13584.0, 'new_value': 13747.0}]
2025-05-29 12:01:16,546 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-29 12:01:17,015 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-29 12:01:17,015 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30101.0, 'new_value': 30550.0}, {'field': 'total_amount', 'old_value': 30101.0, 'new_value': 30550.0}, {'field': 'order_count', 'old_value': 139, 'new_value': 140}]
2025-05-29 12:01:17,015 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-29 12:01:17,468 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-29 12:01:17,468 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 142745.0, 'new_value': 144745.0}, {'field': 'total_amount', 'old_value': 142745.0, 'new_value': 144745.0}, {'field': 'order_count', 'old_value': 83, 'new_value': 84}]
2025-05-29 12:01:17,468 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-29 12:01:18,030 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-29 12:01:18,030 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 113701.7, 'new_value': 119023.3}, {'field': 'offline_amount', 'old_value': 153708.5, 'new_value': 153918.4}, {'field': 'total_amount', 'old_value': 267410.2, 'new_value': 272941.7}, {'field': 'order_count', 'old_value': 5396, 'new_value': 5507}]
2025-05-29 12:01:18,030 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-29 12:01:18,687 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-29 12:01:18,687 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 502801.71, 'new_value': 512928.86}, {'field': 'total_amount', 'old_value': 502801.71, 'new_value': 512928.86}, {'field': 'order_count', 'old_value': 6925, 'new_value': 7092}]
2025-05-29 12:01:18,687 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-29 12:01:19,202 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-29 12:01:19,202 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 655376.52, 'new_value': 677848.55}, {'field': 'total_amount', 'old_value': 657289.57, 'new_value': 679761.6}, {'field': 'order_count', 'old_value': 1569, 'new_value': 1628}]
2025-05-29 12:01:19,202 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-29 12:01:19,671 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-29 12:01:19,671 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 191590.0, 'new_value': 197850.0}, {'field': 'total_amount', 'old_value': 191591.0, 'new_value': 197851.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 30}]
2025-05-29 12:01:19,671 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-29 12:01:20,202 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-29 12:01:20,202 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94582.91, 'new_value': 96425.28}, {'field': 'total_amount', 'old_value': 94582.91, 'new_value': 96425.28}, {'field': 'order_count', 'old_value': 2959, 'new_value': 3017}]
2025-05-29 12:01:20,202 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-29 12:01:20,765 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-29 12:01:20,765 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10372.75, 'new_value': 10677.3}, {'field': 'offline_amount', 'old_value': 34047.86, 'new_value': 34629.66}, {'field': 'total_amount', 'old_value': 44420.61, 'new_value': 45306.96}, {'field': 'order_count', 'old_value': 1559, 'new_value': 1592}]
2025-05-29 12:01:20,765 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-29 12:01:21,249 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-29 12:01:21,249 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 155295.03, 'new_value': 157859.26}, {'field': 'total_amount', 'old_value': 155295.03, 'new_value': 157859.26}, {'field': 'order_count', 'old_value': 4028, 'new_value': 4107}]
2025-05-29 12:01:21,249 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-29 12:01:21,609 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-29 12:01:21,609 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37858.92, 'new_value': 39898.91}, {'field': 'offline_amount', 'old_value': 333392.87, 'new_value': 338170.32}, {'field': 'total_amount', 'old_value': 371251.79, 'new_value': 378069.23}, {'field': 'order_count', 'old_value': 8616, 'new_value': 8738}]
2025-05-29 12:01:21,609 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-29 12:01:22,124 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-29 12:01:22,124 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 439664.8, 'new_value': 447588.9}, {'field': 'total_amount', 'old_value': 439664.8, 'new_value': 447588.9}, {'field': 'order_count', 'old_value': 2161, 'new_value': 2196}]
2025-05-29 12:01:22,124 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-29 12:01:22,562 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-29 12:01:22,562 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 141547.32, 'new_value': 144418.83}, {'field': 'offline_amount', 'old_value': 57781.13, 'new_value': 58893.22}, {'field': 'total_amount', 'old_value': 199328.45, 'new_value': 203312.05}, {'field': 'order_count', 'old_value': 12173, 'new_value': 12422}]
2025-05-29 12:01:22,562 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-29 12:01:23,030 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-29 12:01:23,030 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 139053.5, 'new_value': 142453.5}, {'field': 'total_amount', 'old_value': 234706.05, 'new_value': 238106.05}, {'field': 'order_count', 'old_value': 10299, 'new_value': 10300}]
2025-05-29 12:01:23,030 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-29 12:01:23,484 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-29 12:01:23,484 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 221364.0, 'new_value': 224332.0}, {'field': 'total_amount', 'old_value': 221364.0, 'new_value': 224332.0}, {'field': 'order_count', 'old_value': 262, 'new_value': 265}]
2025-05-29 12:01:23,484 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-29 12:01:23,905 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-29 12:01:23,905 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 744311.02, 'new_value': 753256.13}, {'field': 'total_amount', 'old_value': 744311.02, 'new_value': 753256.13}, {'field': 'order_count', 'old_value': 14133, 'new_value': 14290}]
2025-05-29 12:01:23,905 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-29 12:01:24,390 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-29 12:01:24,390 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 90339.38, 'new_value': 95082.07}, {'field': 'offline_amount', 'old_value': 229666.77, 'new_value': 238515.5}, {'field': 'total_amount', 'old_value': 320006.15, 'new_value': 333597.57}, {'field': 'order_count', 'old_value': 15804, 'new_value': 16481}]
2025-05-29 12:01:24,390 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-29 12:01:24,890 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-29 12:01:24,890 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 285675.25, 'new_value': 293756.67}, {'field': 'total_amount', 'old_value': 297726.22, 'new_value': 305807.64}, {'field': 'order_count', 'old_value': 12866, 'new_value': 13259}]
2025-05-29 12:01:24,890 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-29 12:01:25,468 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-29 12:01:25,468 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 223626.0, 'new_value': 226911.0}, {'field': 'total_amount', 'old_value': 223626.0, 'new_value': 226911.0}, {'field': 'order_count', 'old_value': 705, 'new_value': 719}]
2025-05-29 12:01:25,468 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-29 12:01:25,905 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-29 12:01:25,905 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1898.63, 'new_value': 1918.24}, {'field': 'offline_amount', 'old_value': 23333.82, 'new_value': 23397.12}, {'field': 'total_amount', 'old_value': 25232.45, 'new_value': 25315.36}, {'field': 'order_count', 'old_value': 905, 'new_value': 909}]
2025-05-29 12:01:25,921 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-29 12:01:26,390 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-29 12:01:26,390 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7591.13, 'new_value': 7731.53}, {'field': 'offline_amount', 'old_value': 396389.04, 'new_value': 403007.54}, {'field': 'total_amount', 'old_value': 403980.17, 'new_value': 410739.07}, {'field': 'order_count', 'old_value': 19413, 'new_value': 19773}]
2025-05-29 12:01:26,390 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-29 12:01:26,890 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-29 12:01:26,890 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55160.43, 'new_value': 56346.53}, {'field': 'offline_amount', 'old_value': 370659.66, 'new_value': 379094.71}, {'field': 'total_amount', 'old_value': 425820.09, 'new_value': 435441.24}, {'field': 'order_count', 'old_value': 2706, 'new_value': 2787}]
2025-05-29 12:01:26,890 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-29 12:01:27,343 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-29 12:01:27,343 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 231127.74, 'new_value': 237727.74}, {'field': 'total_amount', 'old_value': 231127.74, 'new_value': 237727.74}, {'field': 'order_count', 'old_value': 1283, 'new_value': 1321}]
2025-05-29 12:01:27,343 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-29 12:01:27,859 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-29 12:01:27,859 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 76409.0, 'new_value': 77275.0}, {'field': 'total_amount', 'old_value': 76409.0, 'new_value': 77275.0}, {'field': 'order_count', 'old_value': 2248, 'new_value': 2273}]
2025-05-29 12:01:27,859 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-29 12:01:28,312 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-29 12:01:28,312 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 70124.24, 'new_value': 71893.24}, {'field': 'offline_amount', 'old_value': 88331.33, 'new_value': 89561.22}, {'field': 'total_amount', 'old_value': 158455.57, 'new_value': 161454.46}, {'field': 'order_count', 'old_value': 7323, 'new_value': 7456}]
2025-05-29 12:01:28,312 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-29 12:01:28,749 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-29 12:01:28,749 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 398819.08, 'new_value': 407758.79}, {'field': 'total_amount', 'old_value': 420982.2, 'new_value': 429921.91}, {'field': 'order_count', 'old_value': 17964, 'new_value': 18374}]
2025-05-29 12:01:28,749 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-29 12:01:29,140 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-29 12:01:29,140 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 250531.74, 'new_value': 255531.74}, {'field': 'total_amount', 'old_value': 282378.54, 'new_value': 287378.54}, {'field': 'order_count', 'old_value': 9021, 'new_value': 9022}]
2025-05-29 12:01:29,140 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-29 12:01:29,593 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-29 12:01:29,593 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95985.0, 'new_value': 96925.0}, {'field': 'total_amount', 'old_value': 111189.0, 'new_value': 112129.0}, {'field': 'order_count', 'old_value': 2578, 'new_value': 2603}]
2025-05-29 12:01:29,593 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-29 12:01:30,015 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-29 12:01:30,015 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107694.0, 'new_value': 111250.0}, {'field': 'total_amount', 'old_value': 107694.0, 'new_value': 111250.0}, {'field': 'order_count', 'old_value': 727, 'new_value': 756}]
2025-05-29 12:01:30,015 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMEE
2025-05-29 12:01:30,483 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMEE
2025-05-29 12:01:30,483 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24515.0, 'new_value': 24814.0}, {'field': 'total_amount', 'old_value': 24515.0, 'new_value': 24814.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 30}]
2025-05-29 12:01:30,483 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-29 12:01:30,952 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-29 12:01:30,952 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 251070.82, 'new_value': 257089.76}, {'field': 'total_amount', 'old_value': 270244.25, 'new_value': 276263.19}, {'field': 'order_count', 'old_value': 5599, 'new_value': 5706}]
2025-05-29 12:01:30,952 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-29 12:01:31,421 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-29 12:01:31,421 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29473.0, 'new_value': 30861.0}, {'field': 'total_amount', 'old_value': 29473.0, 'new_value': 30861.0}, {'field': 'order_count', 'old_value': 172, 'new_value': 182}]
2025-05-29 12:01:31,421 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-29 12:01:31,874 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-29 12:01:31,874 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 716772.0, 'new_value': 735069.0}, {'field': 'total_amount', 'old_value': 716772.0, 'new_value': 735069.0}, {'field': 'order_count', 'old_value': 83, 'new_value': 84}]
2025-05-29 12:01:31,874 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-29 12:01:32,312 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-29 12:01:32,312 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83123.15, 'new_value': 84542.3}, {'field': 'total_amount', 'old_value': 83123.15, 'new_value': 84542.3}, {'field': 'order_count', 'old_value': 4834, 'new_value': 4938}]
2025-05-29 12:01:32,312 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIF
2025-05-29 12:01:32,749 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIF
2025-05-29 12:01:32,749 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60417.0, 'new_value': 61417.0}, {'field': 'total_amount', 'old_value': 60417.0, 'new_value': 61417.0}, {'field': 'order_count', 'old_value': 436, 'new_value': 446}]
2025-05-29 12:01:32,749 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-29 12:01:33,202 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-29 12:01:33,202 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16372.45, 'new_value': 16800.45}, {'field': 'offline_amount', 'old_value': 303742.0, 'new_value': 316037.0}, {'field': 'total_amount', 'old_value': 320114.45, 'new_value': 332837.45}, {'field': 'order_count', 'old_value': 1705, 'new_value': 1775}]
2025-05-29 12:01:33,202 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-29 12:01:33,687 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-29 12:01:33,687 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-05-29 12:01:33,687 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-29 12:01:34,124 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-29 12:01:34,124 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 198368.7, 'new_value': 207420.4}, {'field': 'total_amount', 'old_value': 198368.7, 'new_value': 207420.4}, {'field': 'order_count', 'old_value': 2558, 'new_value': 2690}]
2025-05-29 12:01:34,124 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-29 12:01:34,546 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-29 12:01:34,546 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35602.7, 'new_value': 36351.7}, {'field': 'total_amount', 'old_value': 35602.7, 'new_value': 36351.7}, {'field': 'order_count', 'old_value': 208, 'new_value': 215}]
2025-05-29 12:01:34,546 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLF
2025-05-29 12:01:34,890 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLF
2025-05-29 12:01:34,890 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41960.0, 'new_value': 42058.0}, {'field': 'total_amount', 'old_value': 54658.4, 'new_value': 54756.4}, {'field': 'order_count', 'old_value': 64, 'new_value': 65}]
2025-05-29 12:01:34,890 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-29 12:01:35,374 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-29 12:01:35,374 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45593.55, 'new_value': 48035.95}, {'field': 'offline_amount', 'old_value': 1182229.52, 'new_value': 1209517.01}, {'field': 'total_amount', 'old_value': 1227823.07, 'new_value': 1257552.96}, {'field': 'order_count', 'old_value': 6047, 'new_value': 6245}]
2025-05-29 12:01:35,374 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-29 12:01:35,843 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-29 12:01:35,843 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 341035.0, 'new_value': 353555.0}, {'field': 'total_amount', 'old_value': 368125.0, 'new_value': 380645.0}, {'field': 'order_count', 'old_value': 7872, 'new_value': 8222}]
2025-05-29 12:01:35,843 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-29 12:01:36,280 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-29 12:01:36,280 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 82212.37, 'new_value': 83735.52}, {'field': 'offline_amount', 'old_value': 210718.5, 'new_value': 214459.65}, {'field': 'total_amount', 'old_value': 292930.87, 'new_value': 298195.17}, {'field': 'order_count', 'old_value': 5633, 'new_value': 5774}]
2025-05-29 12:01:36,280 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-29 12:01:36,718 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-29 12:01:36,718 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 299259.0, 'new_value': 312836.0}, {'field': 'total_amount', 'old_value': 303559.0, 'new_value': 317136.0}, {'field': 'order_count', 'old_value': 214, 'new_value': 226}]
2025-05-29 12:01:36,718 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-29 12:01:37,233 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-29 12:01:37,233 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55178.2, 'new_value': 59214.95}, {'field': 'total_amount', 'old_value': 58619.0, 'new_value': 62655.75}, {'field': 'order_count', 'old_value': 222, 'new_value': 232}]
2025-05-29 12:01:37,233 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-29 12:01:37,718 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-29 12:01:37,718 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 189722.0, 'new_value': 191650.0}, {'field': 'total_amount', 'old_value': 189722.0, 'new_value': 191650.0}, {'field': 'order_count', 'old_value': 3191, 'new_value': 3222}]
2025-05-29 12:01:37,718 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-29 12:01:38,155 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-29 12:01:38,155 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 193250.74, 'new_value': 196322.74}, {'field': 'total_amount', 'old_value': 193250.74, 'new_value': 196322.74}, {'field': 'order_count', 'old_value': 8249, 'new_value': 8394}]
2025-05-29 12:01:38,155 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-29 12:01:38,655 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-29 12:01:38,655 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 250732.73, 'new_value': 256041.77}, {'field': 'total_amount', 'old_value': 250732.73, 'new_value': 256041.77}, {'field': 'order_count', 'old_value': 1946, 'new_value': 1990}]
2025-05-29 12:01:38,655 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-29 12:01:39,077 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-29 12:01:39,077 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 69501.6, 'new_value': 69942.93}, {'field': 'offline_amount', 'old_value': 98110.73, 'new_value': 101110.73}, {'field': 'total_amount', 'old_value': 167612.33, 'new_value': 171053.66}, {'field': 'order_count', 'old_value': 4680, 'new_value': 4756}]
2025-05-29 12:01:39,077 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-29 12:01:39,562 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-29 12:01:39,562 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 111666.0, 'new_value': 107567.0}, {'field': 'offline_amount', 'old_value': 1249631.0, 'new_value': 1270330.0}, {'field': 'total_amount', 'old_value': 1361297.0, 'new_value': 1377897.0}, {'field': 'order_count', 'old_value': 34319, 'new_value': 34250}]
2025-05-29 12:01:39,562 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-29 12:01:40,077 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-29 12:01:40,077 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39310.07, 'new_value': 40310.07}, {'field': 'offline_amount', 'old_value': 50116.44, 'new_value': 51529.0}, {'field': 'total_amount', 'old_value': 89426.51, 'new_value': 91839.07}, {'field': 'order_count', 'old_value': 4375, 'new_value': 4495}]
2025-05-29 12:01:40,077 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-29 12:01:40,452 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-29 12:01:40,452 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 315966.0, 'new_value': 326275.0}, {'field': 'total_amount', 'old_value': 315966.0, 'new_value': 326275.0}, {'field': 'order_count', 'old_value': 482, 'new_value': 496}]
2025-05-29 12:01:40,452 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOF
2025-05-29 12:01:40,968 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOF
2025-05-29 12:01:40,968 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12463.78, 'new_value': 12858.88}, {'field': 'offline_amount', 'old_value': 67250.15, 'new_value': 70023.14}, {'field': 'total_amount', 'old_value': 79713.93, 'new_value': 82882.02}, {'field': 'order_count', 'old_value': 1818, 'new_value': 1904}]
2025-05-29 12:01:40,968 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-29 12:01:41,421 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-29 12:01:41,437 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36853.5, 'new_value': 37053.5}, {'field': 'total_amount', 'old_value': 36853.5, 'new_value': 37053.5}, {'field': 'order_count', 'old_value': 174, 'new_value': 175}]
2025-05-29 12:01:41,437 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-29 12:01:41,890 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-29 12:01:41,890 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 130052.9, 'new_value': 134943.26}, {'field': 'offline_amount', 'old_value': 227561.3, 'new_value': 232414.23}, {'field': 'total_amount', 'old_value': 357614.2, 'new_value': 367357.49}, {'field': 'order_count', 'old_value': 11053, 'new_value': 11404}]
2025-05-29 12:01:41,890 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPF
2025-05-29 12:01:42,327 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPF
2025-05-29 12:01:42,327 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 148559.0, 'new_value': 152099.0}, {'field': 'total_amount', 'old_value': 148559.0, 'new_value': 152099.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 30}]
2025-05-29 12:01:42,327 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-29 12:01:42,780 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-29 12:01:42,780 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 182436.49, 'new_value': 188181.71}, {'field': 'offline_amount', 'old_value': 55614.77, 'new_value': 56366.28}, {'field': 'total_amount', 'old_value': 238051.26, 'new_value': 244547.99}, {'field': 'order_count', 'old_value': 13606, 'new_value': 14005}]
2025-05-29 12:01:42,780 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-29 12:01:43,233 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-29 12:01:43,233 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 159756.27, 'new_value': 166126.78}, {'field': 'offline_amount', 'old_value': 305331.15, 'new_value': 311870.99}, {'field': 'total_amount', 'old_value': 465087.42, 'new_value': 477997.77}, {'field': 'order_count', 'old_value': 3908, 'new_value': 4028}]
2025-05-29 12:01:43,233 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-29 12:01:43,655 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-29 12:01:43,655 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45020.6, 'new_value': 46027.02}, {'field': 'offline_amount', 'old_value': 26239.32, 'new_value': 26624.12}, {'field': 'total_amount', 'old_value': 71259.92, 'new_value': 72651.14}, {'field': 'order_count', 'old_value': 3152, 'new_value': 3225}]
2025-05-29 12:01:43,655 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-29 12:01:44,140 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-29 12:01:44,140 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15637.6, 'new_value': 16025.11}, {'field': 'offline_amount', 'old_value': 36697.1, 'new_value': 37350.8}, {'field': 'total_amount', 'old_value': 52334.7, 'new_value': 53375.91}, {'field': 'order_count', 'old_value': 2074, 'new_value': 2121}]
2025-05-29 12:01:44,140 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-29 12:01:44,608 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-29 12:01:44,608 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 828024.74, 'new_value': 841661.29}, {'field': 'total_amount', 'old_value': 828024.74, 'new_value': 841661.29}, {'field': 'order_count', 'old_value': 6706, 'new_value': 6869}]
2025-05-29 12:01:44,608 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-29 12:01:45,062 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-29 12:01:45,062 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23374.5, 'new_value': 24537.2}, {'field': 'offline_amount', 'old_value': 188227.1, 'new_value': 195257.7}, {'field': 'total_amount', 'old_value': 211601.6, 'new_value': 219794.9}, {'field': 'order_count', 'old_value': 6672, 'new_value': 6970}]
2025-05-29 12:01:45,062 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-29 12:01:45,530 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-29 12:01:45,530 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 537874.0, 'new_value': 548378.0}, {'field': 'total_amount', 'old_value': 537874.0, 'new_value': 548378.0}, {'field': 'order_count', 'old_value': 3900, 'new_value': 3970}]
2025-05-29 12:01:45,530 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-29 12:01:46,015 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-29 12:01:46,015 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3945.0, 'new_value': 4051.0}, {'field': 'offline_amount', 'old_value': 30203.8, 'new_value': 30434.8}, {'field': 'total_amount', 'old_value': 34148.8, 'new_value': 34485.8}, {'field': 'order_count', 'old_value': 1239, 'new_value': 1252}]
2025-05-29 12:01:46,015 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-29 12:01:46,468 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-29 12:01:46,468 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 147823.0, 'new_value': 153975.0}, {'field': 'offline_amount', 'old_value': 101860.0, 'new_value': 103851.0}, {'field': 'total_amount', 'old_value': 249683.0, 'new_value': 257826.0}, {'field': 'order_count', 'old_value': 3330, 'new_value': 3471}]
2025-05-29 12:01:46,468 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-29 12:01:46,890 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-29 12:01:46,890 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 127173.91, 'new_value': 133181.1}, {'field': 'total_amount', 'old_value': 134402.98, 'new_value': 140410.17}, {'field': 'order_count', 'old_value': 724, 'new_value': 754}]
2025-05-29 12:01:46,890 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-29 12:01:47,343 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-29 12:01:47,343 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7606.0, 'new_value': 8238.0}, {'field': 'offline_amount', 'old_value': 57885.0, 'new_value': 58980.0}, {'field': 'total_amount', 'old_value': 65491.0, 'new_value': 67218.0}, {'field': 'order_count', 'old_value': 509, 'new_value': 524}]
2025-05-29 12:01:47,343 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-29 12:01:47,843 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-29 12:01:47,843 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5300.0, 'new_value': 5580.0}, {'field': 'offline_amount', 'old_value': 22349.0, 'new_value': 23017.0}, {'field': 'total_amount', 'old_value': 27649.0, 'new_value': 28597.0}, {'field': 'order_count', 'old_value': 214, 'new_value': 219}]
2025-05-29 12:01:47,843 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-29 12:01:48,296 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-29 12:01:48,296 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1100871.0, 'new_value': 1113259.0}, {'field': 'total_amount', 'old_value': 1100871.0, 'new_value': 1113259.0}, {'field': 'order_count', 'old_value': 4854, 'new_value': 4923}]
2025-05-29 12:01:48,296 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-29 12:01:48,718 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-29 12:01:48,718 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13418416.0, 'new_value': 13602287.0}, {'field': 'total_amount', 'old_value': 13418416.0, 'new_value': 13602287.0}, {'field': 'order_count', 'old_value': 42898, 'new_value': 43656}]
2025-05-29 12:01:48,718 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-29 12:01:49,374 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-29 12:01:49,374 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3926339.47, 'new_value': 4020288.73}, {'field': 'total_amount', 'old_value': 3926339.47, 'new_value': 4020288.73}, {'field': 'order_count', 'old_value': 6767, 'new_value': 6921}]
2025-05-29 12:01:49,374 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-29 12:01:49,812 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-29 12:01:49,812 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 175987.8, 'new_value': 181814.69}, {'field': 'total_amount', 'old_value': 183427.44, 'new_value': 189254.33}, {'field': 'order_count', 'old_value': 12836, 'new_value': 13261}]
2025-05-29 12:01:49,812 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-29 12:01:50,265 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-29 12:01:50,265 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49237.59, 'new_value': 51133.67}, {'field': 'offline_amount', 'old_value': 73383.96, 'new_value': 74438.26}, {'field': 'total_amount', 'old_value': 122621.55, 'new_value': 125571.93}, {'field': 'order_count', 'old_value': 2583, 'new_value': 2662}]
2025-05-29 12:01:50,265 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-29 12:01:50,811 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-29 12:01:50,811 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 306293.0, 'new_value': 314142.0}, {'field': 'total_amount', 'old_value': 306293.0, 'new_value': 314142.0}, {'field': 'order_count', 'old_value': 379, 'new_value': 392}]
2025-05-29 12:01:50,811 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-29 12:01:51,265 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-29 12:01:51,265 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 329912.5, 'new_value': 333022.0}, {'field': 'total_amount', 'old_value': 329912.5, 'new_value': 333022.0}, {'field': 'order_count', 'old_value': 7202, 'new_value': 7273}]
2025-05-29 12:01:51,265 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-29 12:01:51,686 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-29 12:01:51,686 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56902.2, 'new_value': 58798.9}, {'field': 'total_amount', 'old_value': 56902.2, 'new_value': 58798.9}, {'field': 'order_count', 'old_value': 312, 'new_value': 322}]
2025-05-29 12:01:51,686 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSF
2025-05-29 12:01:52,171 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSF
2025-05-29 12:01:52,171 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70786.0, 'new_value': 73620.0}, {'field': 'total_amount', 'old_value': 70786.0, 'new_value': 73620.0}, {'field': 'order_count', 'old_value': 13827, 'new_value': 14396}]
2025-05-29 12:01:52,171 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTF
2025-05-29 12:01:52,655 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTF
2025-05-29 12:01:52,655 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104826.0, 'new_value': 109077.0}, {'field': 'total_amount', 'old_value': 104826.0, 'new_value': 109077.0}, {'field': 'order_count', 'old_value': 13827, 'new_value': 14396}]
2025-05-29 12:01:52,655 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-29 12:01:53,124 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-29 12:01:53,124 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78744.2, 'new_value': 79202.2}, {'field': 'total_amount', 'old_value': 79024.0, 'new_value': 79482.0}, {'field': 'order_count', 'old_value': 1166, 'new_value': 1170}]
2025-05-29 12:01:53,124 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-29 12:01:53,546 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-29 12:01:53,546 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71603.05, 'new_value': 73919.08}, {'field': 'total_amount', 'old_value': 76963.02, 'new_value': 79279.05}, {'field': 'order_count', 'old_value': 1380, 'new_value': 1405}]
2025-05-29 12:01:53,546 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-29 12:01:53,999 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-29 12:01:53,999 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5469707.67, 'new_value': 5611407.67}, {'field': 'total_amount', 'old_value': 5469707.67, 'new_value': 5611407.67}, {'field': 'order_count', 'old_value': 113040, 'new_value': 116133}]
2025-05-29 12:01:53,999 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-29 12:01:54,468 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-29 12:01:54,468 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 219193.66, 'new_value': 228007.98}, {'field': 'total_amount', 'old_value': 219193.66, 'new_value': 228007.98}, {'field': 'order_count', 'old_value': 4013, 'new_value': 4147}]
2025-05-29 12:01:54,468 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-29 12:01:54,952 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-29 12:01:54,952 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 493686.0, 'new_value': 546384.0}, {'field': 'total_amount', 'old_value': 493686.0, 'new_value': 546384.0}, {'field': 'order_count', 'old_value': 88, 'new_value': 99}]
2025-05-29 12:01:54,952 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-29 12:01:55,468 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-29 12:01:55,468 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 137588.43, 'new_value': 142163.44}, {'field': 'total_amount', 'old_value': 142556.63, 'new_value': 147131.64}, {'field': 'order_count', 'old_value': 3624, 'new_value': 3737}]
2025-05-29 12:01:55,468 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVF
2025-05-29 12:01:55,905 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVF
2025-05-29 12:01:55,905 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107754.0, 'new_value': 112131.0}, {'field': 'total_amount', 'old_value': 107754.0, 'new_value': 112131.0}, {'field': 'order_count', 'old_value': 267, 'new_value': 275}]
2025-05-29 12:01:55,905 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-29 12:01:56,452 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-29 12:01:56,452 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 110076.04, 'new_value': 113569.94}, {'field': 'offline_amount', 'old_value': 425598.0, 'new_value': 432732.4}, {'field': 'total_amount', 'old_value': 535674.04, 'new_value': 546302.34}, {'field': 'order_count', 'old_value': 3927, 'new_value': 4040}]
2025-05-29 12:01:56,452 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-29 12:01:57,015 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-29 12:01:57,015 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89982.0, 'new_value': 93958.0}, {'field': 'total_amount', 'old_value': 91832.0, 'new_value': 95808.0}, {'field': 'order_count', 'old_value': 510, 'new_value': 530}]
2025-05-29 12:01:57,015 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYF
2025-05-29 12:01:57,421 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYF
2025-05-29 12:01:57,421 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1906.5, 'new_value': 2038.5}, {'field': 'offline_amount', 'old_value': 49465.6, 'new_value': 54030.6}, {'field': 'total_amount', 'old_value': 51372.1, 'new_value': 56069.1}, {'field': 'order_count', 'old_value': 345, 'new_value': 362}]
2025-05-29 12:01:57,421 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-29 12:01:57,905 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-29 12:01:57,905 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34352.5, 'new_value': 37137.08}, {'field': 'total_amount', 'old_value': 99347.91, 'new_value': 102132.49}, {'field': 'order_count', 'old_value': 6514, 'new_value': 6703}]
2025-05-29 12:01:57,905 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-29 12:01:58,452 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-29 12:01:58,452 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59854.39, 'new_value': 66631.0}, {'field': 'total_amount', 'old_value': 173183.67, 'new_value': 179960.28}, {'field': 'order_count', 'old_value': 11406, 'new_value': 11840}]
2025-05-29 12:01:58,452 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-29 12:01:58,858 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-29 12:01:58,858 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1264249.99, 'new_value': 1300841.33}, {'field': 'total_amount', 'old_value': 1264249.99, 'new_value': 1300841.33}, {'field': 'order_count', 'old_value': 3717, 'new_value': 3836}]
2025-05-29 12:01:58,858 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-29 12:01:59,280 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-29 12:01:59,280 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 781794.52, 'new_value': 811972.09}, {'field': 'total_amount', 'old_value': 781794.52, 'new_value': 811972.09}, {'field': 'order_count', 'old_value': 4225, 'new_value': 4419}]
2025-05-29 12:01:59,280 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6G
2025-05-29 12:01:59,765 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6G
2025-05-29 12:01:59,765 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1111690.82, 'new_value': 1192808.32}, {'field': 'total_amount', 'old_value': 1111690.82, 'new_value': 1192808.32}, {'field': 'order_count', 'old_value': 3939, 'new_value': 4195}]
2025-05-29 12:01:59,765 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-29 12:02:00,265 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-29 12:02:00,265 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 886053.01, 'new_value': 908401.31}, {'field': 'total_amount', 'old_value': 886053.01, 'new_value': 908401.31}, {'field': 'order_count', 'old_value': 2494, 'new_value': 2562}]
2025-05-29 12:02:00,265 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAG
2025-05-29 12:02:00,905 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAG
2025-05-29 12:02:00,905 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48601.4, 'new_value': 50973.4}, {'field': 'total_amount', 'old_value': 51445.4, 'new_value': 53817.4}, {'field': 'order_count', 'old_value': 380, 'new_value': 403}]
2025-05-29 12:02:00,905 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-29 12:02:01,421 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-29 12:02:01,421 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 320390.93, 'new_value': 332644.01}, {'field': 'total_amount', 'old_value': 320390.93, 'new_value': 332644.01}, {'field': 'order_count', 'old_value': 885, 'new_value': 911}]
2025-05-29 12:02:01,421 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-29 12:02:01,827 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-29 12:02:01,827 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68339.0, 'new_value': 70984.0}, {'field': 'total_amount', 'old_value': 83844.0, 'new_value': 86489.0}, {'field': 'order_count', 'old_value': 130, 'new_value': 135}]
2025-05-29 12:02:01,827 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGG
2025-05-29 12:02:02,265 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGG
2025-05-29 12:02:02,265 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 141432.0, 'new_value': 153344.0}, {'field': 'offline_amount', 'old_value': 96666.0, 'new_value': 106850.0}, {'field': 'total_amount', 'old_value': 238098.0, 'new_value': 260194.0}, {'field': 'order_count', 'old_value': 9746, 'new_value': 10664}]
2025-05-29 12:02:02,265 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-29 12:02:02,733 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-29 12:02:02,733 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 134350.0, 'new_value': 141766.0}, {'field': 'total_amount', 'old_value': 134350.0, 'new_value': 141766.0}, {'field': 'order_count', 'old_value': 658, 'new_value': 659}]
2025-05-29 12:02:02,733 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-29 12:02:03,233 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-29 12:02:03,233 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 303985.0, 'new_value': 317285.0}, {'field': 'total_amount', 'old_value': 303985.0, 'new_value': 317285.0}, {'field': 'order_count', 'old_value': 715, 'new_value': 747}]
2025-05-29 12:02:03,233 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-29 12:02:03,733 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-29 12:02:03,733 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 213714.0, 'new_value': 227141.0}, {'field': 'total_amount', 'old_value': 213714.0, 'new_value': 227141.0}, {'field': 'order_count', 'old_value': 22640, 'new_value': 23925}]
2025-05-29 12:02:03,733 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-29 12:02:04,171 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-29 12:02:04,171 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 123102.0, 'new_value': 125911.0}, {'field': 'total_amount', 'old_value': 123102.0, 'new_value': 125911.0}, {'field': 'order_count', 'old_value': 1266, 'new_value': 1313}]
2025-05-29 12:02:04,171 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-29 12:02:04,624 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-29 12:02:04,624 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 164909.82, 'new_value': 167109.82}, {'field': 'total_amount', 'old_value': 164909.82, 'new_value': 167109.82}, {'field': 'order_count', 'old_value': 1406, 'new_value': 1407}]
2025-05-29 12:02:04,624 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AM9C
2025-05-29 12:02:05,108 - INFO - 更新表单数据成功: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AM9C
2025-05-29 12:02:05,108 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5079.6, 'new_value': 5092.6}, {'field': 'total_amount', 'old_value': 5079.6, 'new_value': 5092.6}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-05-29 12:02:05,108 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-29 12:02:05,561 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-29 12:02:05,561 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52205.43, 'new_value': 55032.66}, {'field': 'total_amount', 'old_value': 52205.43, 'new_value': 55032.66}, {'field': 'order_count', 'old_value': 898, 'new_value': 935}]
2025-05-29 12:02:05,561 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-29 12:02:06,046 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-29 12:02:06,046 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 132179.17, 'new_value': 137327.87}, {'field': 'offline_amount', 'old_value': 729665.54, 'new_value': 746853.01}, {'field': 'total_amount', 'old_value': 861844.71, 'new_value': 884180.88}, {'field': 'order_count', 'old_value': 2001, 'new_value': 2077}]
2025-05-29 12:02:06,046 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-29 12:02:06,468 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-29 12:02:06,468 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 95349.75, 'new_value': 100454.75}, {'field': 'offline_amount', 'old_value': 963226.44, 'new_value': 1008810.69}, {'field': 'total_amount', 'old_value': 1056701.86, 'new_value': 1107391.11}, {'field': 'order_count', 'old_value': 4974, 'new_value': 5202}]
2025-05-29 12:02:06,468 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-29 12:02:06,890 - INFO - 更新表单数据成功: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-29 12:02:06,890 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 128390.0, 'new_value': 130524.0}, {'field': 'total_amount', 'old_value': 128390.0, 'new_value': 130524.0}, {'field': 'order_count', 'old_value': 390, 'new_value': 401}]
2025-05-29 12:02:06,890 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-05-29 12:02:07,358 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-05-29 12:02:07,358 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79257.0, 'new_value': 83557.0}, {'field': 'total_amount', 'old_value': 84575.0, 'new_value': 88875.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 46}]
2025-05-29 12:02:07,358 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-29 12:02:07,905 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-29 12:02:07,905 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18749253.78, 'new_value': 19414401.64}, {'field': 'total_amount', 'old_value': 18749253.78, 'new_value': 19414401.64}, {'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-05-29 12:02:07,905 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-29 12:02:08,374 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-29 12:02:08,374 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 194426.77, 'new_value': 201901.22}, {'field': 'total_amount', 'old_value': 194426.77, 'new_value': 201901.22}, {'field': 'order_count', 'old_value': 20658, 'new_value': 21369}]
2025-05-29 12:02:08,374 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-29 12:02:09,014 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-29 12:02:09,014 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19104.0, 'new_value': 19798.0}, {'field': 'total_amount', 'old_value': 19104.0, 'new_value': 19798.0}, {'field': 'order_count', 'old_value': 125, 'new_value': 128}]
2025-05-29 12:02:09,014 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-29 12:02:09,421 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-29 12:02:09,421 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58018.09, 'new_value': 63314.21}, {'field': 'total_amount', 'old_value': 68874.53, 'new_value': 74170.65}, {'field': 'order_count', 'old_value': 4349, 'new_value': 4617}]
2025-05-29 12:02:09,421 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-05-29 12:02:09,890 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-05-29 12:02:09,890 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7079.75, 'new_value': 9043.17}, {'field': 'offline_amount', 'old_value': 54690.7, 'new_value': 57987.1}, {'field': 'total_amount', 'old_value': 61770.45, 'new_value': 67030.27}, {'field': 'order_count', 'old_value': 2547, 'new_value': 2820}]
2025-05-29 12:02:09,905 - INFO - 开始更新记录 - 表单实例ID: FINST-F7D66UA12CKVOE62735DBBS3ITMX3ODLPMVAMO5
2025-05-29 12:02:10,343 - INFO - 更新表单数据成功: FINST-F7D66UA12CKVOE62735DBBS3ITMX3ODLPMVAMO5
2025-05-29 12:02:10,343 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1581.38}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1581.38}, {'field': 'order_count', 'old_value': 0, 'new_value': 310}]
2025-05-29 12:02:10,343 - INFO - 开始更新记录 - 表单实例ID: FINST-2K666OB1LLRV68JUA6FHUBVSP1MR3MJGI35BM2
2025-05-29 12:02:10,796 - INFO - 更新表单数据成功: FINST-2K666OB1LLRV68JUA6FHUBVSP1MR3MJGI35BM2
2025-05-29 12:02:10,796 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20108.3, 'new_value': 21678.98}, {'field': 'total_amount', 'old_value': 20108.3, 'new_value': 21678.98}, {'field': 'order_count', 'old_value': 859, 'new_value': 915}]
2025-05-29 12:02:10,796 - INFO - 日期 2025-05 处理完成 - 更新: 213 条，插入: 0 条，错误: 0 条
2025-05-29 12:02:10,796 - INFO - 数据同步完成！更新: 213 条，插入: 0 条，错误: 0 条
2025-05-29 12:02:10,796 - INFO - =================同步完成====================
2025-05-29 15:00:01,990 - INFO - =================使用默认全量同步=============
2025-05-29 15:00:03,506 - INFO - MySQL查询成功，共获取 3304 条记录
2025-05-29 15:00:03,506 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-29 15:00:03,537 - INFO - 开始处理日期: 2025-01
2025-05-29 15:00:03,537 - INFO - Request Parameters - Page 1:
2025-05-29 15:00:03,537 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:00:03,537 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:00:04,975 - INFO - Response - Page 1:
2025-05-29 15:00:05,178 - INFO - 第 1 页获取到 100 条记录
2025-05-29 15:00:05,178 - INFO - Request Parameters - Page 2:
2025-05-29 15:00:05,178 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:00:05,178 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:00:05,678 - INFO - Response - Page 2:
2025-05-29 15:00:05,881 - INFO - 第 2 页获取到 100 条记录
2025-05-29 15:00:05,881 - INFO - Request Parameters - Page 3:
2025-05-29 15:00:05,881 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:00:05,881 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:00:06,443 - INFO - Response - Page 3:
2025-05-29 15:00:06,647 - INFO - 第 3 页获取到 100 条记录
2025-05-29 15:00:06,647 - INFO - Request Parameters - Page 4:
2025-05-29 15:00:06,647 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:00:06,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:00:07,178 - INFO - Response - Page 4:
2025-05-29 15:00:07,381 - INFO - 第 4 页获取到 100 条记录
2025-05-29 15:00:07,381 - INFO - Request Parameters - Page 5:
2025-05-29 15:00:07,381 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:00:07,381 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:00:07,990 - INFO - Response - Page 5:
2025-05-29 15:00:08,193 - INFO - 第 5 页获取到 100 条记录
2025-05-29 15:00:08,193 - INFO - Request Parameters - Page 6:
2025-05-29 15:00:08,193 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:00:08,193 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:00:08,662 - INFO - Response - Page 6:
2025-05-29 15:00:08,865 - INFO - 第 6 页获取到 100 条记录
2025-05-29 15:00:08,865 - INFO - Request Parameters - Page 7:
2025-05-29 15:00:08,865 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:00:08,865 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:00:09,334 - INFO - Response - Page 7:
2025-05-29 15:00:09,537 - INFO - 第 7 页获取到 82 条记录
2025-05-29 15:00:09,537 - INFO - 查询完成，共获取到 682 条记录
2025-05-29 15:00:09,537 - INFO - 获取到 682 条表单数据
2025-05-29 15:00:09,537 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-29 15:00:09,553 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 15:00:09,553 - INFO - 开始处理日期: 2025-02
2025-05-29 15:00:09,553 - INFO - Request Parameters - Page 1:
2025-05-29 15:00:09,553 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:00:09,553 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:00:10,146 - INFO - Response - Page 1:
2025-05-29 15:00:10,350 - INFO - 第 1 页获取到 100 条记录
2025-05-29 15:00:10,350 - INFO - Request Parameters - Page 2:
2025-05-29 15:00:10,350 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:00:10,350 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:00:10,834 - INFO - Response - Page 2:
2025-05-29 15:00:11,037 - INFO - 第 2 页获取到 100 条记录
2025-05-29 15:00:11,037 - INFO - Request Parameters - Page 3:
2025-05-29 15:00:11,037 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:00:11,037 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:00:11,568 - INFO - Response - Page 3:
2025-05-29 15:00:11,771 - INFO - 第 3 页获取到 100 条记录
2025-05-29 15:00:11,771 - INFO - Request Parameters - Page 4:
2025-05-29 15:00:11,771 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:00:11,771 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:00:12,303 - INFO - Response - Page 4:
2025-05-29 15:00:12,506 - INFO - 第 4 页获取到 100 条记录
2025-05-29 15:00:12,506 - INFO - Request Parameters - Page 5:
2025-05-29 15:00:12,506 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:00:12,506 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:00:13,068 - INFO - Response - Page 5:
2025-05-29 15:00:13,271 - INFO - 第 5 页获取到 100 条记录
2025-05-29 15:00:13,271 - INFO - Request Parameters - Page 6:
2025-05-29 15:00:13,271 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:00:13,271 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:00:13,740 - INFO - Response - Page 6:
2025-05-29 15:00:13,943 - INFO - 第 6 页获取到 100 条记录
2025-05-29 15:00:13,943 - INFO - Request Parameters - Page 7:
2025-05-29 15:00:13,943 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:00:13,943 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:00:14,396 - INFO - Response - Page 7:
2025-05-29 15:00:14,600 - INFO - 第 7 页获取到 70 条记录
2025-05-29 15:00:14,600 - INFO - 查询完成，共获取到 670 条记录
2025-05-29 15:00:14,600 - INFO - 获取到 670 条表单数据
2025-05-29 15:00:14,600 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-29 15:00:14,631 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 15:00:14,631 - INFO - 开始处理日期: 2025-03
2025-05-29 15:00:14,631 - INFO - Request Parameters - Page 1:
2025-05-29 15:00:14,631 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:00:14,631 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:00:15,146 - INFO - Response - Page 1:
2025-05-29 15:00:15,350 - INFO - 第 1 页获取到 100 条记录
2025-05-29 15:00:15,350 - INFO - Request Parameters - Page 2:
2025-05-29 15:00:15,350 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:00:15,350 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:00:15,912 - INFO - Response - Page 2:
2025-05-29 15:00:16,115 - INFO - 第 2 页获取到 100 条记录
2025-05-29 15:00:16,115 - INFO - Request Parameters - Page 3:
2025-05-29 15:00:16,115 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:00:16,115 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:00:16,600 - INFO - Response - Page 3:
2025-05-29 15:00:16,803 - INFO - 第 3 页获取到 100 条记录
2025-05-29 15:00:16,803 - INFO - Request Parameters - Page 4:
2025-05-29 15:00:16,803 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:00:16,803 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:00:17,381 - INFO - Response - Page 4:
2025-05-29 15:00:17,584 - INFO - 第 4 页获取到 100 条记录
2025-05-29 15:00:17,584 - INFO - Request Parameters - Page 5:
2025-05-29 15:00:17,584 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:00:17,584 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:00:18,162 - INFO - Response - Page 5:
2025-05-29 15:00:18,365 - INFO - 第 5 页获取到 100 条记录
2025-05-29 15:00:18,365 - INFO - Request Parameters - Page 6:
2025-05-29 15:00:18,365 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:00:18,365 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:00:18,818 - INFO - Response - Page 6:
2025-05-29 15:00:19,021 - INFO - 第 6 页获取到 100 条记录
2025-05-29 15:00:19,021 - INFO - Request Parameters - Page 7:
2025-05-29 15:00:19,021 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:00:19,021 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:00:19,428 - INFO - Response - Page 7:
2025-05-29 15:00:19,631 - INFO - 第 7 页获取到 61 条记录
2025-05-29 15:00:19,631 - INFO - 查询完成，共获取到 661 条记录
2025-05-29 15:00:19,631 - INFO - 获取到 661 条表单数据
2025-05-29 15:00:19,631 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-29 15:00:19,646 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 15:00:19,646 - INFO - 开始处理日期: 2025-04
2025-05-29 15:00:19,646 - INFO - Request Parameters - Page 1:
2025-05-29 15:00:19,646 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:00:19,646 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:00:20,225 - INFO - Response - Page 1:
2025-05-29 15:00:20,428 - INFO - 第 1 页获取到 100 条记录
2025-05-29 15:00:20,428 - INFO - Request Parameters - Page 2:
2025-05-29 15:00:20,428 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:00:20,428 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:00:20,881 - INFO - Response - Page 2:
2025-05-29 15:00:21,084 - INFO - 第 2 页获取到 100 条记录
2025-05-29 15:00:21,084 - INFO - Request Parameters - Page 3:
2025-05-29 15:00:21,084 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:00:21,084 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:00:21,568 - INFO - Response - Page 3:
2025-05-29 15:00:21,771 - INFO - 第 3 页获取到 100 条记录
2025-05-29 15:00:21,771 - INFO - Request Parameters - Page 4:
2025-05-29 15:00:21,771 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:00:21,771 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:00:22,443 - INFO - Response - Page 4:
2025-05-29 15:00:22,646 - INFO - 第 4 页获取到 100 条记录
2025-05-29 15:00:22,646 - INFO - Request Parameters - Page 5:
2025-05-29 15:00:22,646 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:00:22,646 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:00:23,131 - INFO - Response - Page 5:
2025-05-29 15:00:23,334 - INFO - 第 5 页获取到 100 条记录
2025-05-29 15:00:23,334 - INFO - Request Parameters - Page 6:
2025-05-29 15:00:23,334 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:00:23,334 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:00:23,865 - INFO - Response - Page 6:
2025-05-29 15:00:24,068 - INFO - 第 6 页获取到 100 条记录
2025-05-29 15:00:24,068 - INFO - Request Parameters - Page 7:
2025-05-29 15:00:24,068 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:00:24,068 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:00:24,490 - INFO - Response - Page 7:
2025-05-29 15:00:24,693 - INFO - 第 7 页获取到 56 条记录
2025-05-29 15:00:24,693 - INFO - 查询完成，共获取到 656 条记录
2025-05-29 15:00:24,693 - INFO - 获取到 656 条表单数据
2025-05-29 15:00:24,693 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-29 15:00:24,709 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 15:00:24,709 - INFO - 开始处理日期: 2025-05
2025-05-29 15:00:24,709 - INFO - Request Parameters - Page 1:
2025-05-29 15:00:24,709 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:00:24,709 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:00:25,224 - INFO - Response - Page 1:
2025-05-29 15:00:25,428 - INFO - 第 1 页获取到 100 条记录
2025-05-29 15:00:25,428 - INFO - Request Parameters - Page 2:
2025-05-29 15:00:25,428 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:00:25,428 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:00:25,912 - INFO - Response - Page 2:
2025-05-29 15:00:26,115 - INFO - 第 2 页获取到 100 条记录
2025-05-29 15:00:26,115 - INFO - Request Parameters - Page 3:
2025-05-29 15:00:26,115 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:00:26,115 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:00:26,584 - INFO - Response - Page 3:
2025-05-29 15:00:26,787 - INFO - 第 3 页获取到 100 条记录
2025-05-29 15:00:26,787 - INFO - Request Parameters - Page 4:
2025-05-29 15:00:26,787 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:00:26,787 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:00:27,271 - INFO - Response - Page 4:
2025-05-29 15:00:27,474 - INFO - 第 4 页获取到 100 条记录
2025-05-29 15:00:27,474 - INFO - Request Parameters - Page 5:
2025-05-29 15:00:27,474 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:00:27,474 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:00:27,990 - INFO - Response - Page 5:
2025-05-29 15:00:28,193 - INFO - 第 5 页获取到 100 条记录
2025-05-29 15:00:28,193 - INFO - Request Parameters - Page 6:
2025-05-29 15:00:28,193 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:00:28,193 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:00:28,709 - INFO - Response - Page 6:
2025-05-29 15:00:28,912 - INFO - 第 6 页获取到 100 条记录
2025-05-29 15:00:28,912 - INFO - Request Parameters - Page 7:
2025-05-29 15:00:28,912 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 15:00:28,912 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 15:00:29,349 - INFO - Response - Page 7:
2025-05-29 15:00:29,553 - INFO - 第 7 页获取到 35 条记录
2025-05-29 15:00:29,553 - INFO - 查询完成，共获取到 635 条记录
2025-05-29 15:00:29,553 - INFO - 获取到 635 条表单数据
2025-05-29 15:00:29,553 - INFO - 当前日期 2025-05 有 635 条MySQL数据需要处理
2025-05-29 15:00:29,568 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-29 15:00:30,068 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-29 15:00:30,068 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 334398.0, 'new_value': 356134.0}, {'field': 'total_amount', 'old_value': 334398.0, 'new_value': 356134.0}, {'field': 'order_count', 'old_value': 6572, 'new_value': 7022}]
2025-05-29 15:00:30,068 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSQ
2025-05-29 15:00:30,521 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSQ
2025-05-29 15:00:30,521 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2086405.75, 'new_value': 2372405.75}, {'field': 'total_amount', 'old_value': 3651655.75, 'new_value': 3937655.75}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-29 15:00:30,521 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSG
2025-05-29 15:00:30,943 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSG
2025-05-29 15:00:30,943 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5257.0, 'new_value': 5757.0}, {'field': 'total_amount', 'old_value': 5257.0, 'new_value': 5757.0}, {'field': 'order_count', 'old_value': 589, 'new_value': 590}]
2025-05-29 15:00:30,943 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-29 15:00:31,553 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-29 15:00:31,553 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79124.6, 'new_value': 84109.6}, {'field': 'total_amount', 'old_value': 79124.6, 'new_value': 84109.6}, {'field': 'order_count', 'old_value': 47, 'new_value': 49}]
2025-05-29 15:00:31,553 - INFO - 日期 2025-05 处理完成 - 更新: 4 条，插入: 0 条，错误: 0 条
2025-05-29 15:00:31,553 - INFO - 数据同步完成！更新: 4 条，插入: 0 条，错误: 0 条
2025-05-29 15:00:31,553 - INFO - =================同步完成====================
2025-05-29 18:00:01,997 - INFO - =================使用默认全量同步=============
2025-05-29 18:00:03,497 - INFO - MySQL查询成功，共获取 3304 条记录
2025-05-29 18:00:03,497 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-29 18:00:03,512 - INFO - 开始处理日期: 2025-01
2025-05-29 18:00:03,528 - INFO - Request Parameters - Page 1:
2025-05-29 18:00:03,528 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:00:03,528 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:00:04,450 - INFO - Response - Page 1:
2025-05-29 18:00:04,653 - INFO - 第 1 页获取到 100 条记录
2025-05-29 18:00:04,653 - INFO - Request Parameters - Page 2:
2025-05-29 18:00:04,653 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:00:04,653 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:00:05,669 - INFO - Response - Page 2:
2025-05-29 18:00:05,872 - INFO - 第 2 页获取到 100 条记录
2025-05-29 18:00:05,872 - INFO - Request Parameters - Page 3:
2025-05-29 18:00:05,872 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:00:05,872 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:00:06,372 - INFO - Response - Page 3:
2025-05-29 18:00:06,575 - INFO - 第 3 页获取到 100 条记录
2025-05-29 18:00:06,575 - INFO - Request Parameters - Page 4:
2025-05-29 18:00:06,575 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:00:06,575 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:00:07,090 - INFO - Response - Page 4:
2025-05-29 18:00:07,294 - INFO - 第 4 页获取到 100 条记录
2025-05-29 18:00:07,294 - INFO - Request Parameters - Page 5:
2025-05-29 18:00:07,294 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:00:07,294 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:00:07,778 - INFO - Response - Page 5:
2025-05-29 18:00:07,981 - INFO - 第 5 页获取到 100 条记录
2025-05-29 18:00:07,981 - INFO - Request Parameters - Page 6:
2025-05-29 18:00:07,981 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:00:07,981 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:00:08,528 - INFO - Response - Page 6:
2025-05-29 18:00:08,731 - INFO - 第 6 页获取到 100 条记录
2025-05-29 18:00:08,731 - INFO - Request Parameters - Page 7:
2025-05-29 18:00:08,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:00:08,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:00:09,200 - INFO - Response - Page 7:
2025-05-29 18:00:09,403 - INFO - 第 7 页获取到 82 条记录
2025-05-29 18:00:09,403 - INFO - 查询完成，共获取到 682 条记录
2025-05-29 18:00:09,403 - INFO - 获取到 682 条表单数据
2025-05-29 18:00:09,403 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-29 18:00:09,419 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 18:00:09,419 - INFO - 开始处理日期: 2025-02
2025-05-29 18:00:09,419 - INFO - Request Parameters - Page 1:
2025-05-29 18:00:09,419 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:00:09,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:00:09,934 - INFO - Response - Page 1:
2025-05-29 18:00:10,137 - INFO - 第 1 页获取到 100 条记录
2025-05-29 18:00:10,137 - INFO - Request Parameters - Page 2:
2025-05-29 18:00:10,137 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:00:10,137 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:00:10,637 - INFO - Response - Page 2:
2025-05-29 18:00:10,840 - INFO - 第 2 页获取到 100 条记录
2025-05-29 18:00:10,840 - INFO - Request Parameters - Page 3:
2025-05-29 18:00:10,840 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:00:10,840 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:00:11,372 - INFO - Response - Page 3:
2025-05-29 18:00:11,575 - INFO - 第 3 页获取到 100 条记录
2025-05-29 18:00:11,575 - INFO - Request Parameters - Page 4:
2025-05-29 18:00:11,575 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:00:11,575 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:00:12,075 - INFO - Response - Page 4:
2025-05-29 18:00:12,278 - INFO - 第 4 页获取到 100 条记录
2025-05-29 18:00:12,278 - INFO - Request Parameters - Page 5:
2025-05-29 18:00:12,278 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:00:12,278 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:00:12,762 - INFO - Response - Page 5:
2025-05-29 18:00:12,965 - INFO - 第 5 页获取到 100 条记录
2025-05-29 18:00:12,965 - INFO - Request Parameters - Page 6:
2025-05-29 18:00:12,965 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:00:12,965 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:00:13,559 - INFO - Response - Page 6:
2025-05-29 18:00:13,762 - INFO - 第 6 页获取到 100 条记录
2025-05-29 18:00:13,762 - INFO - Request Parameters - Page 7:
2025-05-29 18:00:13,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:00:13,762 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:00:14,215 - INFO - Response - Page 7:
2025-05-29 18:00:14,419 - INFO - 第 7 页获取到 70 条记录
2025-05-29 18:00:14,419 - INFO - 查询完成，共获取到 670 条记录
2025-05-29 18:00:14,419 - INFO - 获取到 670 条表单数据
2025-05-29 18:00:14,419 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-29 18:00:14,434 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 18:00:14,434 - INFO - 开始处理日期: 2025-03
2025-05-29 18:00:14,434 - INFO - Request Parameters - Page 1:
2025-05-29 18:00:14,434 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:00:14,434 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:00:14,856 - INFO - Response - Page 1:
2025-05-29 18:00:15,059 - INFO - 第 1 页获取到 100 条记录
2025-05-29 18:00:15,059 - INFO - Request Parameters - Page 2:
2025-05-29 18:00:15,059 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:00:15,059 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:00:15,606 - INFO - Response - Page 2:
2025-05-29 18:00:15,809 - INFO - 第 2 页获取到 100 条记录
2025-05-29 18:00:15,809 - INFO - Request Parameters - Page 3:
2025-05-29 18:00:15,809 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:00:15,809 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:00:16,419 - INFO - Response - Page 3:
2025-05-29 18:00:16,622 - INFO - 第 3 页获取到 100 条记录
2025-05-29 18:00:16,622 - INFO - Request Parameters - Page 4:
2025-05-29 18:00:16,622 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:00:16,622 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:00:17,122 - INFO - Response - Page 4:
2025-05-29 18:00:17,325 - INFO - 第 4 页获取到 100 条记录
2025-05-29 18:00:17,325 - INFO - Request Parameters - Page 5:
2025-05-29 18:00:17,325 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:00:17,325 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:00:17,809 - INFO - Response - Page 5:
2025-05-29 18:00:18,012 - INFO - 第 5 页获取到 100 条记录
2025-05-29 18:00:18,012 - INFO - Request Parameters - Page 6:
2025-05-29 18:00:18,012 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:00:18,012 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:00:18,559 - INFO - Response - Page 6:
2025-05-29 18:00:18,762 - INFO - 第 6 页获取到 100 条记录
2025-05-29 18:00:18,762 - INFO - Request Parameters - Page 7:
2025-05-29 18:00:18,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:00:18,762 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:00:19,247 - INFO - Response - Page 7:
2025-05-29 18:00:19,450 - INFO - 第 7 页获取到 61 条记录
2025-05-29 18:00:19,450 - INFO - 查询完成，共获取到 661 条记录
2025-05-29 18:00:19,450 - INFO - 获取到 661 条表单数据
2025-05-29 18:00:19,450 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-29 18:00:19,465 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 18:00:19,465 - INFO - 开始处理日期: 2025-04
2025-05-29 18:00:19,465 - INFO - Request Parameters - Page 1:
2025-05-29 18:00:19,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:00:19,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:00:19,965 - INFO - Response - Page 1:
2025-05-29 18:00:20,169 - INFO - 第 1 页获取到 100 条记录
2025-05-29 18:00:20,169 - INFO - Request Parameters - Page 2:
2025-05-29 18:00:20,169 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:00:20,169 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:00:20,606 - INFO - Response - Page 2:
2025-05-29 18:00:20,809 - INFO - 第 2 页获取到 100 条记录
2025-05-29 18:00:20,809 - INFO - Request Parameters - Page 3:
2025-05-29 18:00:20,809 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:00:20,809 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:00:21,231 - INFO - Response - Page 3:
2025-05-29 18:00:21,434 - INFO - 第 3 页获取到 100 条记录
2025-05-29 18:00:21,434 - INFO - Request Parameters - Page 4:
2025-05-29 18:00:21,434 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:00:21,434 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:00:21,903 - INFO - Response - Page 4:
2025-05-29 18:00:22,106 - INFO - 第 4 页获取到 100 条记录
2025-05-29 18:00:22,106 - INFO - Request Parameters - Page 5:
2025-05-29 18:00:22,106 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:00:22,106 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:00:22,622 - INFO - Response - Page 5:
2025-05-29 18:00:22,825 - INFO - 第 5 页获取到 100 条记录
2025-05-29 18:00:22,825 - INFO - Request Parameters - Page 6:
2025-05-29 18:00:22,825 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:00:22,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:00:23,543 - INFO - Response - Page 6:
2025-05-29 18:00:23,747 - INFO - 第 6 页获取到 100 条记录
2025-05-29 18:00:23,747 - INFO - Request Parameters - Page 7:
2025-05-29 18:00:23,747 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:00:23,747 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:00:24,168 - INFO - Response - Page 7:
2025-05-29 18:00:24,372 - INFO - 第 7 页获取到 56 条记录
2025-05-29 18:00:24,372 - INFO - 查询完成，共获取到 656 条记录
2025-05-29 18:00:24,372 - INFO - 获取到 656 条表单数据
2025-05-29 18:00:24,372 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-29 18:00:24,387 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MK2
2025-05-29 18:00:24,840 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MK2
2025-05-29 18:00:24,840 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16597340.0, 'new_value': 16596340.0}, {'field': 'total_amount', 'old_value': 16597340.0, 'new_value': 16596340.0}]
2025-05-29 18:00:24,840 - INFO - 日期 2025-04 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-29 18:00:24,840 - INFO - 开始处理日期: 2025-05
2025-05-29 18:00:24,840 - INFO - Request Parameters - Page 1:
2025-05-29 18:00:24,840 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:00:24,840 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:00:25,418 - INFO - Response - Page 1:
2025-05-29 18:00:25,622 - INFO - 第 1 页获取到 100 条记录
2025-05-29 18:00:25,622 - INFO - Request Parameters - Page 2:
2025-05-29 18:00:25,622 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:00:25,622 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:00:26,090 - INFO - Response - Page 2:
2025-05-29 18:00:26,293 - INFO - 第 2 页获取到 100 条记录
2025-05-29 18:00:26,293 - INFO - Request Parameters - Page 3:
2025-05-29 18:00:26,293 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:00:26,293 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:00:26,840 - INFO - Response - Page 3:
2025-05-29 18:00:27,043 - INFO - 第 3 页获取到 100 条记录
2025-05-29 18:00:27,043 - INFO - Request Parameters - Page 4:
2025-05-29 18:00:27,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:00:27,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:00:27,528 - INFO - Response - Page 4:
2025-05-29 18:00:27,731 - INFO - 第 4 页获取到 100 条记录
2025-05-29 18:00:27,731 - INFO - Request Parameters - Page 5:
2025-05-29 18:00:27,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:00:27,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:00:28,278 - INFO - Response - Page 5:
2025-05-29 18:00:28,481 - INFO - 第 5 页获取到 100 条记录
2025-05-29 18:00:28,481 - INFO - Request Parameters - Page 6:
2025-05-29 18:00:28,481 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:00:28,481 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:00:29,012 - INFO - Response - Page 6:
2025-05-29 18:00:29,215 - INFO - 第 6 页获取到 100 条记录
2025-05-29 18:00:29,215 - INFO - Request Parameters - Page 7:
2025-05-29 18:00:29,215 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 18:00:29,215 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 18:00:29,622 - INFO - Response - Page 7:
2025-05-29 18:00:29,825 - INFO - 第 7 页获取到 35 条记录
2025-05-29 18:00:29,825 - INFO - 查询完成，共获取到 635 条记录
2025-05-29 18:00:29,825 - INFO - 获取到 635 条表单数据
2025-05-29 18:00:29,825 - INFO - 当前日期 2025-05 有 635 条MySQL数据需要处理
2025-05-29 18:00:29,825 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3QQFGZBAM8D1
2025-05-29 18:00:30,403 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3QQFGZBAM8D1
2025-05-29 18:00:30,403 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29598.9, 'new_value': 30283.3}, {'field': 'total_amount', 'old_value': 29598.9, 'new_value': 30283.3}, {'field': 'order_count', 'old_value': 202, 'new_value': 210}]
2025-05-29 18:00:30,403 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMOX
2025-05-29 18:00:30,856 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMOX
2025-05-29 18:00:30,856 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48078.1, 'new_value': 49949.4}, {'field': 'total_amount', 'old_value': 48078.1, 'new_value': 49949.4}, {'field': 'order_count', 'old_value': 9430, 'new_value': 9829}]
2025-05-29 18:00:30,856 - INFO - 开始更新记录 - 表单实例ID: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMI
2025-05-29 18:00:31,293 - INFO - 更新表单数据成功: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMI
2025-05-29 18:00:31,293 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88778.0, 'new_value': 94278.0}, {'field': 'total_amount', 'old_value': 165823.0, 'new_value': 171323.0}, {'field': 'order_count', 'old_value': 2299, 'new_value': 2408}]
2025-05-29 18:00:31,293 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAM9D1
2025-05-29 18:00:31,747 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAM9D1
2025-05-29 18:00:31,747 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83103.04, 'new_value': 84821.03}, {'field': 'total_amount', 'old_value': 87911.99, 'new_value': 89629.98}, {'field': 'order_count', 'old_value': 5227, 'new_value': 5344}]
2025-05-29 18:00:31,747 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMAD1
2025-05-29 18:00:32,168 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMAD1
2025-05-29 18:00:32,168 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4698.0, 'new_value': 4922.0}, {'field': 'offline_amount', 'old_value': 22725.0, 'new_value': 22933.0}, {'field': 'total_amount', 'old_value': 27423.0, 'new_value': 27855.0}, {'field': 'order_count', 'old_value': 159, 'new_value': 165}]
2025-05-29 18:00:32,168 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMBD1
2025-05-29 18:00:32,590 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMBD1
2025-05-29 18:00:32,590 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 208113.62, 'new_value': 218113.62}, {'field': 'total_amount', 'old_value': 208113.62, 'new_value': 218113.62}, {'field': 'order_count', 'old_value': 11794, 'new_value': 11972}]
2025-05-29 18:00:32,590 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMCD1
2025-05-29 18:00:33,090 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMCD1
2025-05-29 18:00:33,090 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14556.38, 'new_value': 14886.03}, {'field': 'offline_amount', 'old_value': 134610.77, 'new_value': 139512.77}, {'field': 'total_amount', 'old_value': 149167.15, 'new_value': 154398.8}, {'field': 'order_count', 'old_value': 3912, 'new_value': 4058}]
2025-05-29 18:00:33,090 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-29 18:00:33,559 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-29 18:00:33,559 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1409000.0, 'new_value': 1419000.0}, {'field': 'total_amount', 'old_value': 1409000.0, 'new_value': 1419000.0}]
2025-05-29 18:00:33,559 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDL
2025-05-29 18:00:34,043 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDL
2025-05-29 18:00:34,043 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 138900.0, 'new_value': 148900.0}, {'field': 'total_amount', 'old_value': 138900.0, 'new_value': 148900.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 20}]
2025-05-29 18:00:34,043 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-29 18:00:34,497 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-29 18:00:34,497 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50920.0, 'new_value': 51278.0}, {'field': 'total_amount', 'old_value': 50920.0, 'new_value': 51278.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-05-29 18:00:34,497 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-29 18:00:35,059 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-29 18:00:35,059 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70797.0, 'new_value': 74126.0}, {'field': 'total_amount', 'old_value': 70797.0, 'new_value': 74126.0}, {'field': 'order_count', 'old_value': 74, 'new_value': 77}]
2025-05-29 18:00:35,059 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-29 18:00:35,543 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-29 18:00:35,543 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 135798.0, 'new_value': 135016.0}, {'field': 'total_amount', 'old_value': 135798.0, 'new_value': 135016.0}, {'field': 'order_count', 'old_value': 4762, 'new_value': 4955}]
2025-05-29 18:00:35,543 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-29 18:00:36,028 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-29 18:00:36,028 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34944.07, 'new_value': 35028.07}, {'field': 'total_amount', 'old_value': 34944.07, 'new_value': 35028.07}, {'field': 'order_count', 'old_value': 3229, 'new_value': 3347}]
2025-05-29 18:00:36,028 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMCM
2025-05-29 18:00:36,559 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMCM
2025-05-29 18:00:36,559 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12559.0, 'new_value': 14359.0}, {'field': 'total_amount', 'old_value': 12559.0, 'new_value': 14359.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-05-29 18:00:36,559 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-29 18:00:37,043 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-29 18:00:37,043 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7680000.0, 'new_value': 7764500.0}, {'field': 'total_amount', 'old_value': 7680000.0, 'new_value': 7764500.0}]
2025-05-29 18:00:37,043 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-29 18:00:37,481 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-29 18:00:37,481 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 449719.0, 'new_value': 448715.0}, {'field': 'total_amount', 'old_value': 458537.99, 'new_value': 457533.99}]
2025-05-29 18:00:37,481 - INFO - 开始更新记录 - 表单实例ID: FINST-8LC66GC10PZUU9MTETB405L5IF663D3CEZ6AMYB
2025-05-29 18:00:37,950 - INFO - 更新表单数据成功: FINST-8LC66GC10PZUU9MTETB405L5IF663D3CEZ6AMYB
2025-05-29 18:00:37,950 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 350394.38, 'new_value': 344481.42}, {'field': 'total_amount', 'old_value': 383474.59, 'new_value': 377561.63}, {'field': 'order_count', 'old_value': 16003, 'new_value': 15962}]
2025-05-29 18:00:37,950 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMDD1
2025-05-29 18:00:38,622 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMDD1
2025-05-29 18:00:38,622 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 445142.0, 'new_value': 456709.0}, {'field': 'total_amount', 'old_value': 445142.0, 'new_value': 456709.0}, {'field': 'order_count', 'old_value': 12207, 'new_value': 12572}]
2025-05-29 18:00:38,622 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-29 18:00:39,059 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-29 18:00:39,059 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 499892.17, 'new_value': 486404.13}, {'field': 'total_amount', 'old_value': 499892.17, 'new_value': 486404.13}, {'field': 'order_count', 'old_value': 862, 'new_value': 882}]
2025-05-29 18:00:39,059 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-29 18:00:39,653 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-29 18:00:39,653 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95897.0, 'new_value': 100696.0}, {'field': 'total_amount', 'old_value': 95897.0, 'new_value': 100696.0}]
2025-05-29 18:00:39,653 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-29 18:00:40,246 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-29 18:00:40,246 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37711.0, 'new_value': 37582.0}, {'field': 'total_amount', 'old_value': 39087.0, 'new_value': 38958.0}, {'field': 'order_count', 'old_value': 3772, 'new_value': 3909}]
2025-05-29 18:00:40,246 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMC
2025-05-29 18:00:40,715 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMC
2025-05-29 18:00:40,715 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 140601.74, 'new_value': 143789.78}, {'field': 'total_amount', 'old_value': 159498.93, 'new_value': 162686.97}, {'field': 'order_count', 'old_value': 4778, 'new_value': 4877}]
2025-05-29 18:00:40,715 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-29 18:00:41,121 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-29 18:00:41,121 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 142453.5, 'new_value': 148478.61}, {'field': 'total_amount', 'old_value': 238106.05, 'new_value': 244131.16}, {'field': 'order_count', 'old_value': 10300, 'new_value': 10794}]
2025-05-29 18:00:41,121 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-29 18:00:41,621 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-29 18:00:41,621 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1465900.0, 'new_value': 1477900.0}, {'field': 'total_amount', 'old_value': 1465900.0, 'new_value': 1477900.0}, {'field': 'order_count', 'old_value': 76, 'new_value': 77}]
2025-05-29 18:00:41,621 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-29 18:00:42,137 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-29 18:00:42,137 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13494.67, 'new_value': 13963.67}, {'field': 'total_amount', 'old_value': 13494.67, 'new_value': 13963.67}, {'field': 'order_count', 'old_value': 383, 'new_value': 398}]
2025-05-29 18:00:42,137 - INFO - 开始更新记录 - 表单实例ID: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMH
2025-05-29 18:00:42,590 - INFO - 更新表单数据成功: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMH
2025-05-29 18:00:42,590 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 134495.4, 'new_value': 138682.3}, {'field': 'offline_amount', 'old_value': 33730.9, 'new_value': 34320.8}, {'field': 'total_amount', 'old_value': 168226.3, 'new_value': 173003.1}, {'field': 'order_count', 'old_value': 13743, 'new_value': 14120}]
2025-05-29 18:00:42,590 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMD
2025-05-29 18:00:43,090 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMD
2025-05-29 18:00:43,090 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4794.0, 'new_value': 5193.0}, {'field': 'total_amount', 'old_value': 14970.0, 'new_value': 15369.0}, {'field': 'order_count', 'old_value': 36, 'new_value': 37}]
2025-05-29 18:00:43,090 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMED1
2025-05-29 18:00:43,528 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMED1
2025-05-29 18:00:43,528 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27655.56, 'new_value': 28778.04}, {'field': 'offline_amount', 'old_value': 30566.78, 'new_value': 31584.18}, {'field': 'total_amount', 'old_value': 58222.34, 'new_value': 60362.22}, {'field': 'order_count', 'old_value': 2823, 'new_value': 2926}]
2025-05-29 18:00:43,528 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMFD1
2025-05-29 18:00:43,965 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMFD1
2025-05-29 18:00:43,965 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25694.0, 'new_value': 26700.0}, {'field': 'total_amount', 'old_value': 30347.6, 'new_value': 31353.6}, {'field': 'order_count', 'old_value': 757, 'new_value': 782}]
2025-05-29 18:00:43,965 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMGD1
2025-05-29 18:00:44,434 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMGD1
2025-05-29 18:00:44,434 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 207466.92, 'new_value': 214254.74}, {'field': 'total_amount', 'old_value': 207466.92, 'new_value': 214254.74}, {'field': 'order_count', 'old_value': 14857, 'new_value': 15384}]
2025-05-29 18:00:44,434 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMMX
2025-05-29 18:00:44,903 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMMX
2025-05-29 18:00:44,903 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40404.28, 'new_value': 42106.54}, {'field': 'total_amount', 'old_value': 40404.28, 'new_value': 42106.54}, {'field': 'order_count', 'old_value': 1904, 'new_value': 1991}]
2025-05-29 18:00:44,903 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMNX
2025-05-29 18:00:45,325 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMNX
2025-05-29 18:00:45,325 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85706.86, 'new_value': 88506.36}, {'field': 'total_amount', 'old_value': 85706.86, 'new_value': 88506.36}, {'field': 'order_count', 'old_value': 8975, 'new_value': 9322}]
2025-05-29 18:00:45,325 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9G
2025-05-29 18:00:45,950 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9G
2025-05-29 18:00:45,950 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25552.0, 'new_value': 26507.0}, {'field': 'offline_amount', 'old_value': 27952.0, 'new_value': 28907.0}, {'field': 'total_amount', 'old_value': 53504.0, 'new_value': 55414.0}, {'field': 'order_count', 'old_value': 25590, 'new_value': 26545}]
2025-05-29 18:00:45,950 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-29 18:00:46,528 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-29 18:00:46,528 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 141766.0, 'new_value': 139483.0}, {'field': 'total_amount', 'old_value': 141766.0, 'new_value': 139483.0}, {'field': 'order_count', 'old_value': 659, 'new_value': 683}]
2025-05-29 18:00:46,528 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-29 18:00:46,996 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-29 18:00:46,996 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 167109.82, 'new_value': 169763.82}, {'field': 'total_amount', 'old_value': 167109.82, 'new_value': 169763.82}, {'field': 'order_count', 'old_value': 1407, 'new_value': 1445}]
2025-05-29 18:00:46,996 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMOE
2025-05-29 18:00:47,450 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMOE
2025-05-29 18:00:47,450 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11534.0, 'new_value': 14680.0}, {'field': 'offline_amount', 'old_value': 41961.36, 'new_value': 42857.36}, {'field': 'total_amount', 'old_value': 53495.36, 'new_value': 57537.36}, {'field': 'order_count', 'old_value': 3285, 'new_value': 3499}]
2025-05-29 18:00:47,450 - INFO - 开始更新记录 - 表单实例ID: FINST-3RE66ZB12ZGVRENEC0W1O98NW0N12F157KUAMGP
2025-05-29 18:00:48,090 - INFO - 更新表单数据成功: FINST-3RE66ZB12ZGVRENEC0W1O98NW0N12F157KUAMGP
2025-05-29 18:00:48,090 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 128316.04, 'new_value': 143767.04}, {'field': 'total_amount', 'old_value': 128316.04, 'new_value': 143767.04}, {'field': 'order_count', 'old_value': 5889, 'new_value': 6596}]
2025-05-29 18:00:48,090 - INFO - 开始更新记录 - 表单实例ID: FINST-QZE668D186RVL112BFPNG4ULXOMU20RL9K4BM5
2025-05-29 18:00:48,543 - INFO - 更新表单数据成功: FINST-QZE668D186RVL112BFPNG4ULXOMU20RL9K4BM5
2025-05-29 18:00:48,543 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 219502.0, 'new_value': 227266.0}, {'field': 'total_amount', 'old_value': 219502.0, 'new_value': 227266.0}, {'field': 'order_count', 'old_value': 185, 'new_value': 190}]
2025-05-29 18:00:48,543 - INFO - 日期 2025-05 处理完成 - 更新: 38 条，插入: 0 条，错误: 0 条
2025-05-29 18:00:48,543 - INFO - 数据同步完成！更新: 39 条，插入: 0 条，错误: 0 条
2025-05-29 18:00:48,543 - INFO - =================同步完成====================
2025-05-29 21:00:02,153 - INFO - =================使用默认全量同步=============
2025-05-29 21:00:03,680 - INFO - MySQL查询成功，共获取 3304 条记录
2025-05-29 21:00:03,681 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-29 21:00:03,709 - INFO - 开始处理日期: 2025-01
2025-05-29 21:00:03,712 - INFO - Request Parameters - Page 1:
2025-05-29 21:00:03,712 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:00:03,712 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:00:05,189 - INFO - Response - Page 1:
2025-05-29 21:00:05,390 - INFO - 第 1 页获取到 100 条记录
2025-05-29 21:00:05,390 - INFO - Request Parameters - Page 2:
2025-05-29 21:00:05,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:00:05,390 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:00:05,955 - INFO - Response - Page 2:
2025-05-29 21:00:06,155 - INFO - 第 2 页获取到 100 条记录
2025-05-29 21:00:06,155 - INFO - Request Parameters - Page 3:
2025-05-29 21:00:06,155 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:00:06,155 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:00:06,705 - INFO - Response - Page 3:
2025-05-29 21:00:06,905 - INFO - 第 3 页获取到 100 条记录
2025-05-29 21:00:06,905 - INFO - Request Parameters - Page 4:
2025-05-29 21:00:06,905 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:00:06,905 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:00:07,465 - INFO - Response - Page 4:
2025-05-29 21:00:07,665 - INFO - 第 4 页获取到 100 条记录
2025-05-29 21:00:07,665 - INFO - Request Parameters - Page 5:
2025-05-29 21:00:07,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:00:07,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:00:08,202 - INFO - Response - Page 5:
2025-05-29 21:00:08,404 - INFO - 第 5 页获取到 100 条记录
2025-05-29 21:00:08,404 - INFO - Request Parameters - Page 6:
2025-05-29 21:00:08,404 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:00:08,404 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:00:08,938 - INFO - Response - Page 6:
2025-05-29 21:00:09,138 - INFO - 第 6 页获取到 100 条记录
2025-05-29 21:00:09,138 - INFO - Request Parameters - Page 7:
2025-05-29 21:00:09,138 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:00:09,138 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:00:09,589 - INFO - Response - Page 7:
2025-05-29 21:00:09,790 - INFO - 第 7 页获取到 82 条记录
2025-05-29 21:00:09,790 - INFO - 查询完成，共获取到 682 条记录
2025-05-29 21:00:09,790 - INFO - 获取到 682 条表单数据
2025-05-29 21:00:09,803 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-29 21:00:09,815 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 21:00:09,815 - INFO - 开始处理日期: 2025-02
2025-05-29 21:00:09,815 - INFO - Request Parameters - Page 1:
2025-05-29 21:00:09,815 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:00:09,815 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:00:10,353 - INFO - Response - Page 1:
2025-05-29 21:00:10,553 - INFO - 第 1 页获取到 100 条记录
2025-05-29 21:00:10,553 - INFO - Request Parameters - Page 2:
2025-05-29 21:00:10,553 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:00:10,553 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:00:11,094 - INFO - Response - Page 2:
2025-05-29 21:00:11,295 - INFO - 第 2 页获取到 100 条记录
2025-05-29 21:00:11,295 - INFO - Request Parameters - Page 3:
2025-05-29 21:00:11,295 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:00:11,295 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:00:12,033 - INFO - Response - Page 3:
2025-05-29 21:00:12,233 - INFO - 第 3 页获取到 100 条记录
2025-05-29 21:00:12,233 - INFO - Request Parameters - Page 4:
2025-05-29 21:00:12,233 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:00:12,233 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:00:12,745 - INFO - Response - Page 4:
2025-05-29 21:00:12,945 - INFO - 第 4 页获取到 100 条记录
2025-05-29 21:00:12,945 - INFO - Request Parameters - Page 5:
2025-05-29 21:00:12,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:00:12,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:00:13,460 - INFO - Response - Page 5:
2025-05-29 21:00:13,660 - INFO - 第 5 页获取到 100 条记录
2025-05-29 21:00:13,660 - INFO - Request Parameters - Page 6:
2025-05-29 21:00:13,660 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:00:13,660 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:00:14,262 - INFO - Response - Page 6:
2025-05-29 21:00:14,462 - INFO - 第 6 页获取到 100 条记录
2025-05-29 21:00:14,462 - INFO - Request Parameters - Page 7:
2025-05-29 21:00:14,462 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:00:14,462 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:00:14,916 - INFO - Response - Page 7:
2025-05-29 21:00:15,116 - INFO - 第 7 页获取到 70 条记录
2025-05-29 21:00:15,116 - INFO - 查询完成，共获取到 670 条记录
2025-05-29 21:00:15,116 - INFO - 获取到 670 条表单数据
2025-05-29 21:00:15,133 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-29 21:00:15,145 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 21:00:15,145 - INFO - 开始处理日期: 2025-03
2025-05-29 21:00:15,145 - INFO - Request Parameters - Page 1:
2025-05-29 21:00:15,146 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:00:15,146 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:00:16,122 - INFO - Response - Page 1:
2025-05-29 21:00:16,322 - INFO - 第 1 页获取到 100 条记录
2025-05-29 21:00:16,322 - INFO - Request Parameters - Page 2:
2025-05-29 21:00:16,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:00:16,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:00:16,853 - INFO - Response - Page 2:
2025-05-29 21:00:17,054 - INFO - 第 2 页获取到 100 条记录
2025-05-29 21:00:17,054 - INFO - Request Parameters - Page 3:
2025-05-29 21:00:17,054 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:00:17,054 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:00:17,576 - INFO - Response - Page 3:
2025-05-29 21:00:17,777 - INFO - 第 3 页获取到 100 条记录
2025-05-29 21:00:17,777 - INFO - Request Parameters - Page 4:
2025-05-29 21:00:17,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:00:17,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:00:18,275 - INFO - Response - Page 4:
2025-05-29 21:00:18,476 - INFO - 第 4 页获取到 100 条记录
2025-05-29 21:00:18,476 - INFO - Request Parameters - Page 5:
2025-05-29 21:00:18,476 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:00:18,476 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:00:18,961 - INFO - Response - Page 5:
2025-05-29 21:00:19,161 - INFO - 第 5 页获取到 100 条记录
2025-05-29 21:00:19,161 - INFO - Request Parameters - Page 6:
2025-05-29 21:00:19,161 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:00:19,161 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:00:19,643 - INFO - Response - Page 6:
2025-05-29 21:00:19,844 - INFO - 第 6 页获取到 100 条记录
2025-05-29 21:00:19,844 - INFO - Request Parameters - Page 7:
2025-05-29 21:00:19,844 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:00:19,844 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:00:20,292 - INFO - Response - Page 7:
2025-05-29 21:00:20,492 - INFO - 第 7 页获取到 61 条记录
2025-05-29 21:00:20,492 - INFO - 查询完成，共获取到 661 条记录
2025-05-29 21:00:20,492 - INFO - 获取到 661 条表单数据
2025-05-29 21:00:20,504 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-29 21:00:20,516 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 21:00:20,516 - INFO - 开始处理日期: 2025-04
2025-05-29 21:00:20,516 - INFO - Request Parameters - Page 1:
2025-05-29 21:00:20,516 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:00:20,516 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:00:21,038 - INFO - Response - Page 1:
2025-05-29 21:00:21,239 - INFO - 第 1 页获取到 100 条记录
2025-05-29 21:00:21,239 - INFO - Request Parameters - Page 2:
2025-05-29 21:00:21,239 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:00:21,239 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:00:21,751 - INFO - Response - Page 2:
2025-05-29 21:00:21,952 - INFO - 第 2 页获取到 100 条记录
2025-05-29 21:00:21,952 - INFO - Request Parameters - Page 3:
2025-05-29 21:00:21,952 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:00:21,952 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:00:22,434 - INFO - Response - Page 3:
2025-05-29 21:00:22,634 - INFO - 第 3 页获取到 100 条记录
2025-05-29 21:00:22,634 - INFO - Request Parameters - Page 4:
2025-05-29 21:00:22,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:00:22,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:00:23,107 - INFO - Response - Page 4:
2025-05-29 21:00:23,307 - INFO - 第 4 页获取到 100 条记录
2025-05-29 21:00:23,307 - INFO - Request Parameters - Page 5:
2025-05-29 21:00:23,307 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:00:23,307 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:00:23,820 - INFO - Response - Page 5:
2025-05-29 21:00:24,020 - INFO - 第 5 页获取到 100 条记录
2025-05-29 21:00:24,020 - INFO - Request Parameters - Page 6:
2025-05-29 21:00:24,020 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:00:24,020 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:00:24,564 - INFO - Response - Page 6:
2025-05-29 21:00:24,764 - INFO - 第 6 页获取到 100 条记录
2025-05-29 21:00:24,764 - INFO - Request Parameters - Page 7:
2025-05-29 21:00:24,764 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:00:24,764 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:00:25,230 - INFO - Response - Page 7:
2025-05-29 21:00:25,431 - INFO - 第 7 页获取到 56 条记录
2025-05-29 21:00:25,431 - INFO - 查询完成，共获取到 656 条记录
2025-05-29 21:00:25,431 - INFO - 获取到 656 条表单数据
2025-05-29 21:00:25,443 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-29 21:00:25,454 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-29 21:00:25,455 - INFO - 开始处理日期: 2025-05
2025-05-29 21:00:25,455 - INFO - Request Parameters - Page 1:
2025-05-29 21:00:25,455 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:00:25,455 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:00:25,937 - INFO - Response - Page 1:
2025-05-29 21:00:26,137 - INFO - 第 1 页获取到 100 条记录
2025-05-29 21:00:26,137 - INFO - Request Parameters - Page 2:
2025-05-29 21:00:26,137 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:00:26,137 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:00:26,638 - INFO - Response - Page 2:
2025-05-29 21:00:26,838 - INFO - 第 2 页获取到 100 条记录
2025-05-29 21:00:26,838 - INFO - Request Parameters - Page 3:
2025-05-29 21:00:26,838 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:00:26,838 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:00:27,292 - INFO - Response - Page 3:
2025-05-29 21:00:27,492 - INFO - 第 3 页获取到 100 条记录
2025-05-29 21:00:27,492 - INFO - Request Parameters - Page 4:
2025-05-29 21:00:27,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:00:27,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:00:27,901 - INFO - Response - Page 4:
2025-05-29 21:00:28,101 - INFO - 第 4 页获取到 100 条记录
2025-05-29 21:00:28,101 - INFO - Request Parameters - Page 5:
2025-05-29 21:00:28,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:00:28,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:00:28,786 - INFO - Response - Page 5:
2025-05-29 21:00:28,986 - INFO - 第 5 页获取到 100 条记录
2025-05-29 21:00:28,986 - INFO - Request Parameters - Page 6:
2025-05-29 21:00:28,986 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:00:28,986 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:00:29,525 - INFO - Response - Page 6:
2025-05-29 21:00:29,725 - INFO - 第 6 页获取到 100 条记录
2025-05-29 21:00:29,725 - INFO - Request Parameters - Page 7:
2025-05-29 21:00:29,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-29 21:00:29,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-29 21:00:30,195 - INFO - Response - Page 7:
2025-05-29 21:00:30,396 - INFO - 第 7 页获取到 35 条记录
2025-05-29 21:00:30,396 - INFO - 查询完成，共获取到 635 条记录
2025-05-29 21:00:30,396 - INFO - 获取到 635 条表单数据
2025-05-29 21:00:30,408 - INFO - 当前日期 2025-05 有 635 条MySQL数据需要处理
2025-05-29 21:00:30,409 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-29 21:00:30,866 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-29 21:00:30,866 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56392.78, 'new_value': 58797.78}, {'field': 'total_amount', 'old_value': 56392.78, 'new_value': 58797.78}, {'field': 'order_count', 'old_value': 134, 'new_value': 138}]
2025-05-29 21:00:30,878 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-29 21:00:30,878 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-05-29 21:00:30,879 - INFO - =================同步完成====================
