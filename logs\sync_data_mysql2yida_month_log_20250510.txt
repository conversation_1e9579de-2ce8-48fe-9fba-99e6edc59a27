2025-05-10 00:00:03,492 - INFO - =================使用默认全量同步=============
2025-05-10 00:00:04,848 - INFO - MySQL查询成功，共获取 3288 条记录
2025-05-10 00:00:04,849 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-10 00:00:04,875 - INFO - 开始处理日期: 2025-01
2025-05-10 00:00:04,878 - INFO - Request Parameters - Page 1:
2025-05-10 00:00:04,879 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 00:00:04,879 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 00:00:06,440 - INFO - Response - Page 1:
2025-05-10 00:00:06,640 - INFO - 第 1 页获取到 100 条记录
2025-05-10 00:00:06,640 - INFO - Request Parameters - Page 2:
2025-05-10 00:00:06,640 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 00:00:06,640 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 00:00:07,189 - INFO - Response - Page 2:
2025-05-10 00:00:07,389 - INFO - 第 2 页获取到 100 条记录
2025-05-10 00:00:07,389 - INFO - Request Parameters - Page 3:
2025-05-10 00:00:07,389 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 00:00:07,389 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 00:00:07,936 - INFO - Response - Page 3:
2025-05-10 00:00:08,137 - INFO - 第 3 页获取到 100 条记录
2025-05-10 00:00:08,137 - INFO - Request Parameters - Page 4:
2025-05-10 00:00:08,137 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 00:00:08,137 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 00:00:08,669 - INFO - Response - Page 4:
2025-05-10 00:00:08,870 - INFO - 第 4 页获取到 100 条记录
2025-05-10 00:00:08,870 - INFO - Request Parameters - Page 5:
2025-05-10 00:00:08,870 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 00:00:08,870 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 00:00:09,366 - INFO - Response - Page 5:
2025-05-10 00:00:09,566 - INFO - 第 5 页获取到 100 条记录
2025-05-10 00:00:09,566 - INFO - Request Parameters - Page 6:
2025-05-10 00:00:09,566 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 00:00:09,566 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 00:00:10,089 - INFO - Response - Page 6:
2025-05-10 00:00:10,290 - INFO - 第 6 页获取到 100 条记录
2025-05-10 00:00:10,290 - INFO - Request Parameters - Page 7:
2025-05-10 00:00:10,290 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 00:00:10,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 00:00:10,723 - INFO - Response - Page 7:
2025-05-10 00:00:10,924 - INFO - 第 7 页获取到 82 条记录
2025-05-10 00:00:10,924 - INFO - 查询完成，共获取到 682 条记录
2025-05-10 00:00:10,924 - INFO - 获取到 682 条表单数据
2025-05-10 00:00:10,935 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-10 00:00:10,946 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-10 00:00:10,947 - INFO - 开始处理日期: 2025-02
2025-05-10 00:00:10,947 - INFO - Request Parameters - Page 1:
2025-05-10 00:00:10,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 00:00:10,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 00:00:11,429 - INFO - Response - Page 1:
2025-05-10 00:00:11,629 - INFO - 第 1 页获取到 100 条记录
2025-05-10 00:00:11,629 - INFO - Request Parameters - Page 2:
2025-05-10 00:00:11,629 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 00:00:11,629 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 00:00:12,083 - INFO - Response - Page 2:
2025-05-10 00:00:12,283 - INFO - 第 2 页获取到 100 条记录
2025-05-10 00:00:12,283 - INFO - Request Parameters - Page 3:
2025-05-10 00:00:12,283 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 00:00:12,283 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 00:00:12,781 - INFO - Response - Page 3:
2025-05-10 00:00:12,981 - INFO - 第 3 页获取到 100 条记录
2025-05-10 00:00:12,981 - INFO - Request Parameters - Page 4:
2025-05-10 00:00:12,981 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 00:00:12,981 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 00:00:13,467 - INFO - Response - Page 4:
2025-05-10 00:00:13,668 - INFO - 第 4 页获取到 100 条记录
2025-05-10 00:00:13,668 - INFO - Request Parameters - Page 5:
2025-05-10 00:00:13,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 00:00:13,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 00:00:14,115 - INFO - Response - Page 5:
2025-05-10 00:00:14,316 - INFO - 第 5 页获取到 100 条记录
2025-05-10 00:00:14,316 - INFO - Request Parameters - Page 6:
2025-05-10 00:00:14,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 00:00:14,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 00:00:14,818 - INFO - Response - Page 6:
2025-05-10 00:00:15,018 - INFO - 第 6 页获取到 100 条记录
2025-05-10 00:00:15,018 - INFO - Request Parameters - Page 7:
2025-05-10 00:00:15,018 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 00:00:15,018 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 00:00:15,523 - INFO - Response - Page 7:
2025-05-10 00:00:15,723 - INFO - 第 7 页获取到 70 条记录
2025-05-10 00:00:15,723 - INFO - 查询完成，共获取到 670 条记录
2025-05-10 00:00:15,723 - INFO - 获取到 670 条表单数据
2025-05-10 00:00:15,736 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-10 00:00:15,749 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-10 00:00:15,749 - INFO - 开始处理日期: 2025-03
2025-05-10 00:00:15,749 - INFO - Request Parameters - Page 1:
2025-05-10 00:00:15,749 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 00:00:15,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 00:00:16,275 - INFO - Response - Page 1:
2025-05-10 00:00:16,476 - INFO - 第 1 页获取到 100 条记录
2025-05-10 00:00:16,476 - INFO - Request Parameters - Page 2:
2025-05-10 00:00:16,476 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 00:00:16,476 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 00:00:16,990 - INFO - Response - Page 2:
2025-05-10 00:00:17,190 - INFO - 第 2 页获取到 100 条记录
2025-05-10 00:00:17,190 - INFO - Request Parameters - Page 3:
2025-05-10 00:00:17,190 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 00:00:17,190 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 00:00:17,688 - INFO - Response - Page 3:
2025-05-10 00:00:17,890 - INFO - 第 3 页获取到 100 条记录
2025-05-10 00:00:17,890 - INFO - Request Parameters - Page 4:
2025-05-10 00:00:17,890 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 00:00:17,890 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 00:00:18,390 - INFO - Response - Page 4:
2025-05-10 00:00:18,590 - INFO - 第 4 页获取到 100 条记录
2025-05-10 00:00:18,590 - INFO - Request Parameters - Page 5:
2025-05-10 00:00:18,590 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 00:00:18,590 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 00:00:19,130 - INFO - Response - Page 5:
2025-05-10 00:00:19,330 - INFO - 第 5 页获取到 100 条记录
2025-05-10 00:00:19,330 - INFO - Request Parameters - Page 6:
2025-05-10 00:00:19,330 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 00:00:19,330 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 00:00:19,820 - INFO - Response - Page 6:
2025-05-10 00:00:20,020 - INFO - 第 6 页获取到 100 条记录
2025-05-10 00:00:20,020 - INFO - Request Parameters - Page 7:
2025-05-10 00:00:20,020 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 00:00:20,020 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 00:00:20,424 - INFO - Response - Page 7:
2025-05-10 00:00:20,625 - INFO - 第 7 页获取到 61 条记录
2025-05-10 00:00:20,625 - INFO - 查询完成，共获取到 661 条记录
2025-05-10 00:00:20,625 - INFO - 获取到 661 条表单数据
2025-05-10 00:00:20,637 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-10 00:00:20,649 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-10 00:00:20,649 - INFO - 开始处理日期: 2025-04
2025-05-10 00:00:20,649 - INFO - Request Parameters - Page 1:
2025-05-10 00:00:20,649 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 00:00:20,649 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 00:00:21,124 - INFO - Response - Page 1:
2025-05-10 00:00:21,325 - INFO - 第 1 页获取到 100 条记录
2025-05-10 00:00:21,325 - INFO - Request Parameters - Page 2:
2025-05-10 00:00:21,325 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 00:00:21,325 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 00:00:21,925 - INFO - Response - Page 2:
2025-05-10 00:00:22,126 - INFO - 第 2 页获取到 100 条记录
2025-05-10 00:00:22,126 - INFO - Request Parameters - Page 3:
2025-05-10 00:00:22,126 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 00:00:22,126 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 00:00:22,609 - INFO - Response - Page 3:
2025-05-10 00:00:22,810 - INFO - 第 3 页获取到 100 条记录
2025-05-10 00:00:22,810 - INFO - Request Parameters - Page 4:
2025-05-10 00:00:22,810 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 00:00:22,810 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 00:00:23,267 - INFO - Response - Page 4:
2025-05-10 00:00:23,467 - INFO - 第 4 页获取到 100 条记录
2025-05-10 00:00:23,467 - INFO - Request Parameters - Page 5:
2025-05-10 00:00:23,467 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 00:00:23,467 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 00:00:24,021 - INFO - Response - Page 5:
2025-05-10 00:00:24,221 - INFO - 第 5 页获取到 100 条记录
2025-05-10 00:00:24,221 - INFO - Request Parameters - Page 6:
2025-05-10 00:00:24,221 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 00:00:24,221 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 00:00:24,651 - INFO - Response - Page 6:
2025-05-10 00:00:24,853 - INFO - 第 6 页获取到 100 条记录
2025-05-10 00:00:24,853 - INFO - Request Parameters - Page 7:
2025-05-10 00:00:24,853 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 00:00:24,853 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 00:00:25,235 - INFO - Response - Page 7:
2025-05-10 00:00:25,435 - INFO - 第 7 页获取到 54 条记录
2025-05-10 00:00:25,435 - INFO - 查询完成，共获取到 654 条记录
2025-05-10 00:00:25,435 - INFO - 获取到 654 条表单数据
2025-05-10 00:00:25,448 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-10 00:00:25,460 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-10 00:00:25,460 - INFO - 开始处理日期: 2025-05
2025-05-10 00:00:25,461 - INFO - Request Parameters - Page 1:
2025-05-10 00:00:25,461 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 00:00:25,461 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 00:00:25,968 - INFO - Response - Page 1:
2025-05-10 00:00:26,168 - INFO - 第 1 页获取到 100 条记录
2025-05-10 00:00:26,168 - INFO - Request Parameters - Page 2:
2025-05-10 00:00:26,168 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 00:00:26,168 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 00:00:26,670 - INFO - Response - Page 2:
2025-05-10 00:00:26,872 - INFO - 第 2 页获取到 100 条记录
2025-05-10 00:00:26,872 - INFO - Request Parameters - Page 3:
2025-05-10 00:00:26,872 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 00:00:26,872 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 00:00:27,336 - INFO - Response - Page 3:
2025-05-10 00:00:27,536 - INFO - 第 3 页获取到 100 条记录
2025-05-10 00:00:27,536 - INFO - Request Parameters - Page 4:
2025-05-10 00:00:27,536 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 00:00:27,536 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 00:00:28,057 - INFO - Response - Page 4:
2025-05-10 00:00:28,258 - INFO - 第 4 页获取到 100 条记录
2025-05-10 00:00:28,258 - INFO - Request Parameters - Page 5:
2025-05-10 00:00:28,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 00:00:28,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 00:00:28,729 - INFO - Response - Page 5:
2025-05-10 00:00:28,929 - INFO - 第 5 页获取到 100 条记录
2025-05-10 00:00:28,929 - INFO - Request Parameters - Page 6:
2025-05-10 00:00:28,929 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 00:00:28,929 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 00:00:29,473 - INFO - Response - Page 6:
2025-05-10 00:00:29,674 - INFO - 第 6 页获取到 100 条记录
2025-05-10 00:00:29,674 - INFO - Request Parameters - Page 7:
2025-05-10 00:00:29,674 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 00:00:29,674 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 00:00:30,016 - INFO - Response - Page 7:
2025-05-10 00:00:30,216 - INFO - 第 7 页获取到 21 条记录
2025-05-10 00:00:30,216 - INFO - 查询完成，共获取到 621 条记录
2025-05-10 00:00:30,216 - INFO - 获取到 621 条表单数据
2025-05-10 00:00:30,229 - INFO - 当前日期 2025-05 有 621 条MySQL数据需要处理
2025-05-10 00:00:30,230 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1LOZUQDCV8H1MR4A1E45C35KQ175AMO4
2025-05-10 00:00:30,699 - INFO - 更新表单数据成功: FINST-K7666JC1LOZUQDCV8H1MR4A1E45C35KQ175AMO4
2025-05-10 00:00:30,699 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3549.0, 'new_value': 16149.0}, {'field': 'total_amount', 'old_value': 3549.0, 'new_value': 16149.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 3}]
2025-05-10 00:00:30,700 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-10 00:00:31,111 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-10 00:00:31,111 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9960.0, 'new_value': 12160.0}, {'field': 'total_amount', 'old_value': 14080.0, 'new_value': 16280.0}, {'field': 'order_count', 'old_value': 139, 'new_value': 157}]
2025-05-10 00:00:31,111 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-10 00:00:31,466 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-10 00:00:31,466 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17254.3, 'new_value': 19451.2}, {'field': 'total_amount', 'old_value': 21214.3, 'new_value': 23411.2}, {'field': 'order_count', 'old_value': 131, 'new_value': 144}]
2025-05-10 00:00:31,467 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-10 00:00:31,950 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-10 00:00:31,951 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48916.0, 'new_value': 49429.0}, {'field': 'total_amount', 'old_value': 52946.45, 'new_value': 53459.45}, {'field': 'order_count', 'old_value': 1030, 'new_value': 1037}]
2025-05-10 00:00:31,951 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-10 00:00:32,381 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-10 00:00:32,381 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26925.0, 'new_value': 30816.0}, {'field': 'offline_amount', 'old_value': 35687.0, 'new_value': 39177.0}, {'field': 'total_amount', 'old_value': 62612.0, 'new_value': 69993.0}, {'field': 'order_count', 'old_value': 1513, 'new_value': 1695}]
2025-05-10 00:00:32,382 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-10 00:00:32,977 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-10 00:00:32,977 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20451.6, 'new_value': 21197.6}, {'field': 'total_amount', 'old_value': 33284.3, 'new_value': 34030.3}, {'field': 'order_count', 'old_value': 494, 'new_value': 498}]
2025-05-10 00:00:32,977 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-10 00:00:33,375 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-10 00:00:33,375 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5081.2, 'new_value': 5271.2}, {'field': 'offline_amount', 'old_value': 33372.9, 'new_value': 38472.66}, {'field': 'total_amount', 'old_value': 38454.1, 'new_value': 43743.86}, {'field': 'order_count', 'old_value': 87, 'new_value': 95}]
2025-05-10 00:00:33,375 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-05-10 00:00:33,864 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-05-10 00:00:33,864 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3380.0, 'new_value': 3738.0}, {'field': 'total_amount', 'old_value': 3380.0, 'new_value': 3738.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-10 00:00:33,865 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-10 00:00:34,307 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-10 00:00:34,307 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24921.0, 'new_value': 28097.0}, {'field': 'total_amount', 'old_value': 24921.0, 'new_value': 28097.0}, {'field': 'order_count', 'old_value': 199, 'new_value': 228}]
2025-05-10 00:00:34,308 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-10 00:00:34,720 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-10 00:00:34,720 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 650.0, 'new_value': 800.0}, {'field': 'offline_amount', 'old_value': 9783.0, 'new_value': 11234.0}, {'field': 'total_amount', 'old_value': 10433.0, 'new_value': 12034.0}, {'field': 'order_count', 'old_value': 43, 'new_value': 49}]
2025-05-10 00:00:34,720 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-10 00:00:35,155 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-10 00:00:35,156 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2370.62, 'new_value': 2429.34}, {'field': 'offline_amount', 'old_value': 35016.85, 'new_value': 38393.45}, {'field': 'total_amount', 'old_value': 37387.47, 'new_value': 40822.79}, {'field': 'order_count', 'old_value': 849, 'new_value': 930}]
2025-05-10 00:00:35,156 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-10 00:00:35,604 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-10 00:00:35,604 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17226.72, 'new_value': 18777.97}, {'field': 'offline_amount', 'old_value': 212218.97, 'new_value': 243377.57}, {'field': 'total_amount', 'old_value': 229445.69, 'new_value': 262155.54}, {'field': 'order_count', 'old_value': 943, 'new_value': 1077}]
2025-05-10 00:00:35,604 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-10 00:00:35,969 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-10 00:00:35,969 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3672.32, 'new_value': 3840.32}, {'field': 'offline_amount', 'old_value': 25355.0, 'new_value': 31833.0}, {'field': 'total_amount', 'old_value': 29027.32, 'new_value': 35673.32}, {'field': 'order_count', 'old_value': 418, 'new_value': 423}]
2025-05-10 00:00:35,969 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXD
2025-05-10 00:00:36,399 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXD
2025-05-10 00:00:36,399 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5412.05, 'new_value': 5508.05}, {'field': 'total_amount', 'old_value': 5412.05, 'new_value': 5508.05}, {'field': 'order_count', 'old_value': 79, 'new_value': 81}]
2025-05-10 00:00:36,399 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-10 00:00:36,805 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-10 00:00:36,806 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17628.2, 'new_value': 19356.66}, {'field': 'offline_amount', 'old_value': 40288.5, 'new_value': 43294.4}, {'field': 'total_amount', 'old_value': 57916.7, 'new_value': 62651.06}, {'field': 'order_count', 'old_value': 1899, 'new_value': 2083}]
2025-05-10 00:00:36,806 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-05-10 00:00:37,242 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-05-10 00:00:37,243 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16182.66, 'new_value': 18773.06}, {'field': 'total_amount', 'old_value': 16182.66, 'new_value': 18773.06}, {'field': 'order_count', 'old_value': 9, 'new_value': 19}]
2025-05-10 00:00:37,243 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-10 00:00:37,645 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-10 00:00:37,645 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 63130.67, 'new_value': 71440.54}, {'field': 'offline_amount', 'old_value': 174367.47, 'new_value': 186857.3}, {'field': 'total_amount', 'old_value': 237498.14, 'new_value': 258297.84}, {'field': 'order_count', 'old_value': 1638, 'new_value': 1796}]
2025-05-10 00:00:37,645 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-10 00:00:38,048 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-10 00:00:38,048 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11205.0, 'new_value': 12210.7}, {'field': 'total_amount', 'old_value': 11205.0, 'new_value': 12210.7}, {'field': 'order_count', 'old_value': 46, 'new_value': 53}]
2025-05-10 00:00:38,048 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-10 00:00:38,534 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-10 00:00:38,534 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2045.54, 'new_value': 2357.72}, {'field': 'offline_amount', 'old_value': 61626.02, 'new_value': 70408.02}, {'field': 'total_amount', 'old_value': 63671.56, 'new_value': 72765.74}, {'field': 'order_count', 'old_value': 439, 'new_value': 484}]
2025-05-10 00:00:38,534 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-10 00:00:39,015 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-10 00:00:39,015 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15650.95, 'new_value': 15848.95}, {'field': 'total_amount', 'old_value': 15716.5, 'new_value': 15914.5}, {'field': 'order_count', 'old_value': 147, 'new_value': 149}]
2025-05-10 00:00:39,015 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMFE
2025-05-10 00:00:39,494 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMFE
2025-05-10 00:00:39,495 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2021.0, 'new_value': 2260.0}, {'field': 'total_amount', 'old_value': 2021.0, 'new_value': 2260.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-05-10 00:00:39,495 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-10 00:00:40,005 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-10 00:00:40,006 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16915.9, 'new_value': 18072.9}, {'field': 'total_amount', 'old_value': 16915.9, 'new_value': 18072.9}, {'field': 'order_count', 'old_value': 77, 'new_value': 83}]
2025-05-10 00:00:40,006 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-10 00:00:40,458 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-10 00:00:40,458 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53566.0, 'new_value': 59570.0}, {'field': 'total_amount', 'old_value': 53567.0, 'new_value': 59571.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-05-10 00:00:40,458 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-10 00:00:40,895 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-10 00:00:40,896 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37602.2, 'new_value': 41733.2}, {'field': 'total_amount', 'old_value': 45178.0, 'new_value': 49309.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-10 00:00:40,896 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-10 00:00:41,311 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-10 00:00:41,311 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51484.09, 'new_value': 51513.99}, {'field': 'total_amount', 'old_value': 51484.09, 'new_value': 51513.99}, {'field': 'order_count', 'old_value': 250, 'new_value': 251}]
2025-05-10 00:00:41,311 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-10 00:00:41,724 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-10 00:00:41,724 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 129.0, 'new_value': 258.0}, {'field': 'offline_amount', 'old_value': 26731.0, 'new_value': 28729.0}, {'field': 'total_amount', 'old_value': 26860.0, 'new_value': 28987.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 11}]
2025-05-10 00:00:41,724 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-10 00:00:42,098 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-10 00:00:42,098 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 376384.02, 'new_value': 377890.02}, {'field': 'offline_amount', 'old_value': 91833.91, 'new_value': 96098.91}, {'field': 'total_amount', 'old_value': 468217.93, 'new_value': 473988.93}, {'field': 'order_count', 'old_value': 4691, 'new_value': 4712}]
2025-05-10 00:00:42,099 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-10 00:00:42,525 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-10 00:00:42,525 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20067.6, 'new_value': 20513.6}, {'field': 'offline_amount', 'old_value': 32.0, 'new_value': 35.0}, {'field': 'total_amount', 'old_value': 20099.6, 'new_value': 20548.6}, {'field': 'order_count', 'old_value': 92, 'new_value': 93}]
2025-05-10 00:00:42,525 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-10 00:00:42,951 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-10 00:00:42,952 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50110.0, 'new_value': 51409.0}, {'field': 'total_amount', 'old_value': 73732.48, 'new_value': 75031.48}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-10 00:00:42,952 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-10 00:00:43,354 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-10 00:00:43,354 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3740.9, 'new_value': 3928.9}, {'field': 'offline_amount', 'old_value': 11321.0, 'new_value': 17341.0}, {'field': 'total_amount', 'old_value': 15061.9, 'new_value': 21269.9}, {'field': 'order_count', 'old_value': 22, 'new_value': 25}]
2025-05-10 00:00:43,354 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-10 00:00:43,785 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-10 00:00:43,785 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25058.0, 'new_value': 28364.0}, {'field': 'total_amount', 'old_value': 25058.0, 'new_value': 28364.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 49}]
2025-05-10 00:00:43,785 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-10 00:00:44,274 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-10 00:00:44,274 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 337204.0, 'new_value': 367643.0}, {'field': 'total_amount', 'old_value': 337204.0, 'new_value': 367643.0}, {'field': 'order_count', 'old_value': 50, 'new_value': 56}]
2025-05-10 00:00:44,274 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-10 00:00:44,705 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-10 00:00:44,705 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60929.9, 'new_value': 63284.7}, {'field': 'total_amount', 'old_value': 60929.9, 'new_value': 63284.7}, {'field': 'order_count', 'old_value': 123, 'new_value': 130}]
2025-05-10 00:00:44,705 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-10 00:00:45,154 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-10 00:00:45,155 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 89530.68, 'new_value': 91040.79}, {'field': 'offline_amount', 'old_value': 37564.34, 'new_value': 46566.4}, {'field': 'total_amount', 'old_value': 127095.02, 'new_value': 137607.19}, {'field': 'order_count', 'old_value': 496, 'new_value': 526}]
2025-05-10 00:00:45,155 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-10 00:00:45,609 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-10 00:00:45,610 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 113977.56, 'new_value': 115281.82}, {'field': 'offline_amount', 'old_value': 6690.2, 'new_value': 13063.12}, {'field': 'total_amount', 'old_value': 120667.76, 'new_value': 128344.94}, {'field': 'order_count', 'old_value': 788, 'new_value': 869}]
2025-05-10 00:00:45,610 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-10 00:00:46,097 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-10 00:00:46,097 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39284.39, 'new_value': 41755.17}, {'field': 'total_amount', 'old_value': 39284.39, 'new_value': 41755.17}, {'field': 'order_count', 'old_value': 1490, 'new_value': 1585}]
2025-05-10 00:00:46,097 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-10 00:00:46,558 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-10 00:00:46,559 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6609.05, 'new_value': 8058.94}, {'field': 'offline_amount', 'old_value': 97906.98, 'new_value': 104466.16}, {'field': 'total_amount', 'old_value': 104516.03, 'new_value': 112525.1}, {'field': 'order_count', 'old_value': 503, 'new_value': 537}]
2025-05-10 00:00:46,559 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-10 00:00:46,960 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-10 00:00:46,960 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10896.86, 'new_value': 12773.59}, {'field': 'offline_amount', 'old_value': 198634.9, 'new_value': 215275.5}, {'field': 'total_amount', 'old_value': 209531.76, 'new_value': 228049.09}, {'field': 'order_count', 'old_value': 1340, 'new_value': 1420}]
2025-05-10 00:00:46,960 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-10 00:00:47,382 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-10 00:00:47,383 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24566.84, 'new_value': 27590.46}, {'field': 'offline_amount', 'old_value': 388872.37, 'new_value': 431335.48}, {'field': 'total_amount', 'old_value': 413439.21, 'new_value': 458925.94}, {'field': 'order_count', 'old_value': 3047, 'new_value': 3450}]
2025-05-10 00:00:47,383 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-10 00:00:47,793 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-10 00:00:47,793 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5061.8, 'new_value': 5514.0}, {'field': 'total_amount', 'old_value': 9114.64, 'new_value': 9566.84}, {'field': 'order_count', 'old_value': 40, 'new_value': 43}]
2025-05-10 00:00:47,794 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-10 00:00:48,254 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-10 00:00:48,254 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16315.0, 'new_value': 18386.0}, {'field': 'total_amount', 'old_value': 16315.0, 'new_value': 18386.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 38}]
2025-05-10 00:00:48,255 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-10 00:00:48,647 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-10 00:00:48,647 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31633.18, 'new_value': 33033.18}, {'field': 'total_amount', 'old_value': 31633.18, 'new_value': 33033.18}, {'field': 'order_count', 'old_value': 170, 'new_value': 180}]
2025-05-10 00:00:48,647 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-10 00:00:49,099 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-10 00:00:49,099 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44349.6, 'new_value': 46293.0}, {'field': 'total_amount', 'old_value': 44349.6, 'new_value': 46293.0}, {'field': 'order_count', 'old_value': 126, 'new_value': 133}]
2025-05-10 00:00:49,100 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-10 00:00:49,524 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-10 00:00:49,524 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47434.16, 'new_value': 55238.36}, {'field': 'offline_amount', 'old_value': 145617.93, 'new_value': 154753.56}, {'field': 'total_amount', 'old_value': 193052.09, 'new_value': 209991.92}, {'field': 'order_count', 'old_value': 4646, 'new_value': 5171}]
2025-05-10 00:00:49,524 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-10 00:00:49,978 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-10 00:00:49,978 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19506.0, 'new_value': 20250.0}, {'field': 'total_amount', 'old_value': 19506.0, 'new_value': 20250.0}, {'field': 'order_count', 'old_value': 50, 'new_value': 53}]
2025-05-10 00:00:49,978 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-10 00:00:50,422 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-10 00:00:50,422 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16463.0, 'new_value': 17786.0}, {'field': 'total_amount', 'old_value': 16463.0, 'new_value': 17786.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 50}]
2025-05-10 00:00:50,422 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-10 00:00:50,857 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-10 00:00:50,857 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10541.2, 'new_value': 11646.0}, {'field': 'offline_amount', 'old_value': 8463.7, 'new_value': 9605.7}, {'field': 'total_amount', 'old_value': 19004.9, 'new_value': 21251.7}, {'field': 'order_count', 'old_value': 99, 'new_value': 111}]
2025-05-10 00:00:50,857 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-10 00:00:51,260 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-10 00:00:51,260 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21513.0, 'new_value': 23513.0}, {'field': 'total_amount', 'old_value': 21748.0, 'new_value': 23748.0}, {'field': 'order_count', 'old_value': 86, 'new_value': 94}]
2025-05-10 00:00:51,260 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-10 00:00:51,711 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-10 00:00:51,711 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 136372.0, 'new_value': 140519.0}, {'field': 'total_amount', 'old_value': 136372.0, 'new_value': 140519.0}, {'field': 'order_count', 'old_value': 105, 'new_value': 110}]
2025-05-10 00:00:51,711 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-10 00:00:52,154 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-10 00:00:52,154 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4863.88, 'new_value': 5549.43}, {'field': 'offline_amount', 'old_value': 79510.94, 'new_value': 92699.14}, {'field': 'total_amount', 'old_value': 84374.82, 'new_value': 98248.57}, {'field': 'order_count', 'old_value': 4481, 'new_value': 5183}]
2025-05-10 00:00:52,155 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-10 00:00:52,548 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-10 00:00:52,549 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 138780.0, 'new_value': 145783.1}, {'field': 'total_amount', 'old_value': 138780.0, 'new_value': 145783.1}, {'field': 'order_count', 'old_value': 1544, 'new_value': 1622}]
2025-05-10 00:00:52,549 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-10 00:00:52,979 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-10 00:00:52,979 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17390.02, 'new_value': 19215.3}, {'field': 'offline_amount', 'old_value': 10030.0, 'new_value': 11588.0}, {'field': 'total_amount', 'old_value': 27420.02, 'new_value': 30803.3}, {'field': 'order_count', 'old_value': 343, 'new_value': 392}]
2025-05-10 00:00:52,980 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-10 00:00:53,471 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-10 00:00:53,471 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31252.36, 'new_value': 35900.72}, {'field': 'offline_amount', 'old_value': 199177.53, 'new_value': 214177.53}, {'field': 'total_amount', 'old_value': 230429.89, 'new_value': 250078.25}, {'field': 'order_count', 'old_value': 846, 'new_value': 996}]
2025-05-10 00:00:53,471 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-10 00:00:53,919 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-10 00:00:53,919 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49996.0, 'new_value': 54687.0}, {'field': 'total_amount', 'old_value': 49996.0, 'new_value': 54687.0}, {'field': 'order_count', 'old_value': 42, 'new_value': 49}]
2025-05-10 00:00:53,919 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-10 00:00:54,332 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-10 00:00:54,332 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46075.96, 'new_value': 49095.28}, {'field': 'total_amount', 'old_value': 49597.48, 'new_value': 52616.8}, {'field': 'order_count', 'old_value': 4540, 'new_value': 4742}]
2025-05-10 00:00:54,332 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-10 00:00:54,835 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-10 00:00:54,835 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2927.48, 'new_value': 3131.68}, {'field': 'offline_amount', 'old_value': 29583.36, 'new_value': 32663.47}, {'field': 'total_amount', 'old_value': 32510.84, 'new_value': 35795.15}, {'field': 'order_count', 'old_value': 1047, 'new_value': 1116}]
2025-05-10 00:00:54,836 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-10 00:00:55,351 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-10 00:00:55,351 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11597.2, 'new_value': 11773.2}, {'field': 'total_amount', 'old_value': 11597.2, 'new_value': 11773.2}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-05-10 00:00:55,351 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-10 00:00:55,758 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-10 00:00:55,758 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40729.0, 'new_value': 42156.0}, {'field': 'total_amount', 'old_value': 40729.0, 'new_value': 42156.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 51}]
2025-05-10 00:00:55,759 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYU
2025-05-10 00:00:56,201 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYU
2025-05-10 00:00:56,201 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12322.0, 'new_value': 15902.0}, {'field': 'total_amount', 'old_value': 12322.0, 'new_value': 15902.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-05-10 00:00:56,202 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-10 00:00:56,597 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-10 00:00:56,597 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17646.82, 'new_value': 19572.33}, {'field': 'offline_amount', 'old_value': 161395.17, 'new_value': 172007.07}, {'field': 'total_amount', 'old_value': 179041.99, 'new_value': 191579.4}, {'field': 'order_count', 'old_value': 1397, 'new_value': 1510}]
2025-05-10 00:00:56,597 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-10 00:00:57,086 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-10 00:00:57,086 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32825.78, 'new_value': 35856.59}, {'field': 'offline_amount', 'old_value': 18584.5, 'new_value': 19348.3}, {'field': 'total_amount', 'old_value': 51410.28, 'new_value': 55204.89}, {'field': 'order_count', 'old_value': 3410, 'new_value': 3665}]
2025-05-10 00:00:57,086 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-10 00:00:57,492 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-10 00:00:57,492 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 148116.48, 'new_value': 158055.02}, {'field': 'total_amount', 'old_value': 148116.48, 'new_value': 158055.02}, {'field': 'order_count', 'old_value': 677, 'new_value': 729}]
2025-05-10 00:00:57,492 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-10 00:00:57,944 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-10 00:00:57,944 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44858.3, 'new_value': 47595.7}, {'field': 'total_amount', 'old_value': 44858.3, 'new_value': 47595.7}, {'field': 'order_count', 'old_value': 382, 'new_value': 401}]
2025-05-10 00:00:57,944 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-10 00:00:58,344 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-10 00:00:58,344 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34017.38, 'new_value': 38166.69}, {'field': 'offline_amount', 'old_value': 93458.74, 'new_value': 101065.62}, {'field': 'total_amount', 'old_value': 127476.12, 'new_value': 139232.31}, {'field': 'order_count', 'old_value': 4002, 'new_value': 4413}]
2025-05-10 00:00:58,345 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDM
2025-05-10 00:00:58,764 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDM
2025-05-10 00:00:58,764 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100859.0, 'new_value': 139334.0}, {'field': 'total_amount', 'old_value': 100859.0, 'new_value': 139334.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-05-10 00:00:58,764 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-10 00:00:59,164 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-10 00:00:59,164 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14520.0, 'new_value': 18638.0}, {'field': 'total_amount', 'old_value': 14520.0, 'new_value': 18638.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 16}]
2025-05-10 00:00:59,164 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-10 00:00:59,568 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-10 00:00:59,569 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20290.0, 'new_value': 21129.0}, {'field': 'total_amount', 'old_value': 20811.2, 'new_value': 21650.2}, {'field': 'order_count', 'old_value': 64, 'new_value': 68}]
2025-05-10 00:00:59,569 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-10 00:00:59,998 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-10 00:00:59,998 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 107621.42, 'new_value': 117363.58}, {'field': 'offline_amount', 'old_value': 6087.5, 'new_value': 6762.5}, {'field': 'total_amount', 'old_value': 113708.92, 'new_value': 124126.08}, {'field': 'order_count', 'old_value': 4240, 'new_value': 4627}]
2025-05-10 00:00:59,998 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-10 00:01:00,516 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-10 00:01:00,516 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12154.2, 'new_value': 13104.2}, {'field': 'offline_amount', 'old_value': 42039.8, 'new_value': 42339.8}, {'field': 'total_amount', 'old_value': 54194.0, 'new_value': 55444.0}, {'field': 'order_count', 'old_value': 76, 'new_value': 77}]
2025-05-10 00:01:00,517 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-10 00:01:00,864 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-10 00:01:00,865 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39878.1, 'new_value': 45511.92}, {'field': 'offline_amount', 'old_value': 106065.33, 'new_value': 112564.03}, {'field': 'total_amount', 'old_value': 145943.43, 'new_value': 158075.95}, {'field': 'order_count', 'old_value': 1653, 'new_value': 1821}]
2025-05-10 00:01:00,865 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-10 00:01:01,376 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-10 00:01:01,376 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32015.4, 'new_value': 34872.3}, {'field': 'total_amount', 'old_value': 32015.4, 'new_value': 34872.3}, {'field': 'order_count', 'old_value': 255, 'new_value': 273}]
2025-05-10 00:01:01,377 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-10 00:01:01,809 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-10 00:01:01,809 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24331.75, 'new_value': 27083.11}, {'field': 'offline_amount', 'old_value': 91633.59, 'new_value': 98811.68}, {'field': 'total_amount', 'old_value': 115965.34, 'new_value': 125894.79}, {'field': 'order_count', 'old_value': 1654, 'new_value': 1744}]
2025-05-10 00:01:01,809 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-10 00:01:02,218 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-10 00:01:02,218 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32095.2, 'new_value': 35278.2}, {'field': 'total_amount', 'old_value': 32095.2, 'new_value': 35278.2}, {'field': 'order_count', 'old_value': 84, 'new_value': 95}]
2025-05-10 00:01:02,218 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-10 00:01:02,666 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-10 00:01:02,666 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9087.39, 'new_value': 9983.88}, {'field': 'total_amount', 'old_value': 9737.39, 'new_value': 10633.88}, {'field': 'order_count', 'old_value': 184, 'new_value': 201}]
2025-05-10 00:01:02,666 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-10 00:01:03,074 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-10 00:01:03,075 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13899.08, 'new_value': 14145.23}, {'field': 'total_amount', 'old_value': 13899.08, 'new_value': 14145.23}, {'field': 'order_count', 'old_value': 55, 'new_value': 56}]
2025-05-10 00:01:03,075 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-10 00:01:03,474 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-10 00:01:03,475 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44588.0, 'new_value': 44687.0}, {'field': 'total_amount', 'old_value': 53512.6, 'new_value': 53611.6}, {'field': 'order_count', 'old_value': 47, 'new_value': 48}]
2025-05-10 00:01:03,475 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-10 00:01:04,212 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-10 00:01:04,212 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14699.0, 'new_value': 15728.0}, {'field': 'total_amount', 'old_value': 14699.0, 'new_value': 15728.0}, {'field': 'order_count', 'old_value': 134, 'new_value': 140}]
2025-05-10 00:01:04,213 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBV
2025-05-10 00:01:04,687 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBV
2025-05-10 00:01:04,687 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10145.0, 'new_value': 10273.0}, {'field': 'total_amount', 'old_value': 10145.0, 'new_value': 10273.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-05-10 00:01:04,687 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-10 00:01:05,185 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-10 00:01:05,185 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3811.6, 'new_value': 4316.6}, {'field': 'total_amount', 'old_value': 3909.6, 'new_value': 4414.6}, {'field': 'order_count', 'old_value': 40, 'new_value': 45}]
2025-05-10 00:01:05,186 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-10 00:01:05,786 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-10 00:01:05,786 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 143730.0, 'new_value': 163803.0}, {'field': 'total_amount', 'old_value': 144170.0, 'new_value': 164243.0}, {'field': 'order_count', 'old_value': 86, 'new_value': 91}]
2025-05-10 00:01:05,786 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-10 00:01:06,257 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-10 00:01:06,257 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37267.9, 'new_value': 40723.7}, {'field': 'offline_amount', 'old_value': 55440.8, 'new_value': 57440.5}, {'field': 'total_amount', 'old_value': 92708.7, 'new_value': 98164.2}, {'field': 'order_count', 'old_value': 1855, 'new_value': 1964}]
2025-05-10 00:01:06,257 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-10 00:01:06,711 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-10 00:01:06,711 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 180099.5, 'new_value': 195517.74}, {'field': 'total_amount', 'old_value': 180099.5, 'new_value': 195517.74}, {'field': 'order_count', 'old_value': 2379, 'new_value': 2607}]
2025-05-10 00:01:06,711 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-10 00:01:07,114 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-10 00:01:07,114 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5452.4, 'new_value': 5611.4}, {'field': 'total_amount', 'old_value': 6792.4, 'new_value': 6951.4}, {'field': 'order_count', 'old_value': 41, 'new_value': 42}]
2025-05-10 00:01:07,114 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-10 00:01:07,577 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-10 00:01:07,577 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31553.35, 'new_value': 35111.82}, {'field': 'total_amount', 'old_value': 31553.35, 'new_value': 35111.82}, {'field': 'order_count', 'old_value': 995, 'new_value': 1090}]
2025-05-10 00:01:07,577 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-10 00:01:08,020 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-10 00:01:08,020 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 121787.0, 'new_value': 127902.0}, {'field': 'total_amount', 'old_value': 121787.0, 'new_value': 127902.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 26}]
2025-05-10 00:01:08,021 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-10 00:01:08,427 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-10 00:01:08,427 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13874.46, 'new_value': 15678.77}, {'field': 'offline_amount', 'old_value': 134519.38, 'new_value': 141019.18}, {'field': 'total_amount', 'old_value': 148393.84, 'new_value': 156697.95}, {'field': 'order_count', 'old_value': 3318, 'new_value': 3526}]
2025-05-10 00:01:08,428 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-10 00:01:08,886 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-10 00:01:08,886 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41185.0, 'new_value': 43394.0}, {'field': 'total_amount', 'old_value': 41185.0, 'new_value': 43394.0}, {'field': 'order_count', 'old_value': 187, 'new_value': 197}]
2025-05-10 00:01:08,887 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-10 00:01:09,291 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-10 00:01:09,291 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 188982.4, 'new_value': 196957.7}, {'field': 'total_amount', 'old_value': 188982.4, 'new_value': 196957.7}, {'field': 'order_count', 'old_value': 799, 'new_value': 846}]
2025-05-10 00:01:09,291 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-10 00:01:09,823 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-10 00:01:09,823 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 64479.53, 'new_value': 69068.11}, {'field': 'offline_amount', 'old_value': 20729.93, 'new_value': 22083.59}, {'field': 'total_amount', 'old_value': 85209.46, 'new_value': 91151.7}, {'field': 'order_count', 'old_value': 5479, 'new_value': 5808}]
2025-05-10 00:01:09,823 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-10 00:01:10,327 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-10 00:01:10,328 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5708.6, 'new_value': 5738.5}, {'field': 'total_amount', 'old_value': 6137.6, 'new_value': 6167.5}, {'field': 'order_count', 'old_value': 87, 'new_value': 88}]
2025-05-10 00:01:10,328 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-10 00:01:10,769 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-10 00:01:10,769 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 959.0, 'new_value': 1498.0}, {'field': 'offline_amount', 'old_value': 10663.0, 'new_value': 11770.0}, {'field': 'total_amount', 'old_value': 11622.0, 'new_value': 13268.0}, {'field': 'order_count', 'old_value': 103, 'new_value': 114}]
2025-05-10 00:01:10,769 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-10 00:01:11,179 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-10 00:01:11,179 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86717.0, 'new_value': 91668.0}, {'field': 'total_amount', 'old_value': 86717.0, 'new_value': 91668.0}, {'field': 'order_count', 'old_value': 84, 'new_value': 85}]
2025-05-10 00:01:11,180 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-10 00:01:11,611 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-10 00:01:11,611 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97149.03, 'new_value': 107629.54}, {'field': 'total_amount', 'old_value': 97149.03, 'new_value': 107629.54}, {'field': 'order_count', 'old_value': 4060, 'new_value': 4482}]
2025-05-10 00:01:11,611 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-10 00:01:12,057 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-10 00:01:12,057 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96976.0, 'new_value': 100899.0}, {'field': 'total_amount', 'old_value': 96976.0, 'new_value': 100899.0}, {'field': 'order_count', 'old_value': 278, 'new_value': 291}]
2025-05-10 00:01:12,057 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-10 00:01:12,490 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-10 00:01:12,490 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19800.83, 'new_value': 22874.06}, {'field': 'offline_amount', 'old_value': 165477.38, 'new_value': 178046.56}, {'field': 'total_amount', 'old_value': 185278.21, 'new_value': 200920.62}, {'field': 'order_count', 'old_value': 743, 'new_value': 818}]
2025-05-10 00:01:12,490 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-10 00:01:12,929 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-10 00:01:12,929 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2585.29, 'new_value': 2784.39}, {'field': 'offline_amount', 'old_value': 146701.7, 'new_value': 159334.74}, {'field': 'total_amount', 'old_value': 149286.99, 'new_value': 162119.13}, {'field': 'order_count', 'old_value': 6716, 'new_value': 7298}]
2025-05-10 00:01:12,929 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-10 00:01:13,343 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-10 00:01:13,343 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30881.0, 'new_value': 33682.0}, {'field': 'total_amount', 'old_value': 30881.0, 'new_value': 33682.0}, {'field': 'order_count', 'old_value': 77, 'new_value': 82}]
2025-05-10 00:01:13,344 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-10 00:01:13,823 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-10 00:01:13,823 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15991.7, 'new_value': 17602.0}, {'field': 'offline_amount', 'old_value': 103842.8, 'new_value': 115219.34}, {'field': 'total_amount', 'old_value': 119834.5, 'new_value': 132821.34}, {'field': 'order_count', 'old_value': 724, 'new_value': 820}]
2025-05-10 00:01:13,823 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-10 00:01:14,218 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-10 00:01:14,218 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43015.0, 'new_value': 44154.0}, {'field': 'total_amount', 'old_value': 43015.0, 'new_value': 44154.0}, {'field': 'order_count', 'old_value': 1290, 'new_value': 1323}]
2025-05-10 00:01:14,218 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-10 00:01:14,644 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-10 00:01:14,645 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23078.62, 'new_value': 25005.22}, {'field': 'offline_amount', 'old_value': 35035.21, 'new_value': 37994.05}, {'field': 'total_amount', 'old_value': 58113.83, 'new_value': 62999.27}, {'field': 'order_count', 'old_value': 2658, 'new_value': 2885}]
2025-05-10 00:01:14,645 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-10 00:01:15,190 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-10 00:01:15,190 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9696.0, 'new_value': 22162.38}, {'field': 'total_amount', 'old_value': 144712.45, 'new_value': 157178.83}, {'field': 'order_count', 'old_value': 5914, 'new_value': 6465}]
2025-05-10 00:01:15,190 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-10 00:01:15,624 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-10 00:01:15,624 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6642.1, 'new_value': 6745.9}, {'field': 'total_amount', 'old_value': 6642.1, 'new_value': 6745.9}, {'field': 'order_count', 'old_value': 99, 'new_value': 101}]
2025-05-10 00:01:15,624 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-10 00:01:16,030 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-10 00:01:16,030 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66837.0, 'new_value': 69207.0}, {'field': 'total_amount', 'old_value': 66837.0, 'new_value': 69207.0}, {'field': 'order_count', 'old_value': 2150, 'new_value': 2218}]
2025-05-10 00:01:16,031 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-10 00:01:16,473 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-10 00:01:16,474 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10689.04, 'new_value': 19659.14}, {'field': 'total_amount', 'old_value': 137243.98, 'new_value': 146214.08}, {'field': 'order_count', 'old_value': 238, 'new_value': 255}]
2025-05-10 00:01:16,474 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-10 00:01:16,871 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-10 00:01:16,871 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39556.0, 'new_value': 42067.0}, {'field': 'offline_amount', 'old_value': 35213.32, 'new_value': 36339.22}, {'field': 'total_amount', 'old_value': 74769.32, 'new_value': 78406.22}, {'field': 'order_count', 'old_value': 494, 'new_value': 518}]
2025-05-10 00:01:16,871 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-10 00:01:17,302 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-10 00:01:17,302 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 244865.0, 'new_value': 257563.0}, {'field': 'total_amount', 'old_value': 244865.0, 'new_value': 257563.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 30}]
2025-05-10 00:01:17,302 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-10 00:01:17,832 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-10 00:01:17,833 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 92118.42, 'new_value': 101399.77}, {'field': 'offline_amount', 'old_value': 498556.55, 'new_value': 544823.81}, {'field': 'total_amount', 'old_value': 590674.97, 'new_value': 646223.58}, {'field': 'order_count', 'old_value': 2842, 'new_value': 3109}]
2025-05-10 00:01:17,833 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCP
2025-05-10 00:01:18,353 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCP
2025-05-10 00:01:18,354 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27900.0, 'new_value': 39500.0}, {'field': 'total_amount', 'old_value': 27900.0, 'new_value': 39500.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-10 00:01:18,354 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-10 00:01:18,810 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-10 00:01:18,811 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69672.5, 'new_value': 77065.8}, {'field': 'total_amount', 'old_value': 69672.5, 'new_value': 77065.8}, {'field': 'order_count', 'old_value': 873, 'new_value': 959}]
2025-05-10 00:01:18,811 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-10 00:01:19,209 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-10 00:01:19,210 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13038.2, 'new_value': 13926.2}, {'field': 'total_amount', 'old_value': 13038.2, 'new_value': 13926.2}, {'field': 'order_count', 'old_value': 70, 'new_value': 76}]
2025-05-10 00:01:19,210 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMKF
2025-05-10 00:01:19,667 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMKF
2025-05-10 00:01:19,667 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 17500.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 17500.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-10 00:01:19,667 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-10 00:01:20,090 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-10 00:01:20,091 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100583.2, 'new_value': 106747.0}, {'field': 'total_amount', 'old_value': 100583.2, 'new_value': 106747.0}, {'field': 'order_count', 'old_value': 551, 'new_value': 589}]
2025-05-10 00:01:20,091 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-10 00:01:20,701 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-10 00:01:20,702 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16512.5, 'new_value': 18459.5}, {'field': 'total_amount', 'old_value': 16512.5, 'new_value': 18459.5}, {'field': 'order_count', 'old_value': 65, 'new_value': 73}]
2025-05-10 00:01:20,702 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-10 00:01:21,138 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-10 00:01:21,138 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 139360.0, 'new_value': 143733.4}, {'field': 'offline_amount', 'old_value': 213292.4, 'new_value': 216140.4}, {'field': 'total_amount', 'old_value': 352652.4, 'new_value': 359873.8}, {'field': 'order_count', 'old_value': 2847, 'new_value': 2947}]
2025-05-10 00:01:21,138 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-10 00:01:21,535 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-10 00:01:21,535 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 74746.05, 'new_value': 83382.78}, {'field': 'offline_amount', 'old_value': 281051.49, 'new_value': 300936.32}, {'field': 'total_amount', 'old_value': 355797.54, 'new_value': 384319.1}, {'field': 'order_count', 'old_value': 1810, 'new_value': 2017}]
2025-05-10 00:01:21,535 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-10 00:01:21,947 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-10 00:01:21,947 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11278.7, 'new_value': 12559.88}, {'field': 'offline_amount', 'old_value': 136481.82, 'new_value': 143486.52}, {'field': 'total_amount', 'old_value': 147760.52, 'new_value': 156046.4}, {'field': 'order_count', 'old_value': 853, 'new_value': 914}]
2025-05-10 00:01:21,948 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-10 00:01:22,477 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-10 00:01:22,477 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32542.0, 'new_value': 35392.0}, {'field': 'total_amount', 'old_value': 32542.0, 'new_value': 35392.0}, {'field': 'order_count', 'old_value': 139, 'new_value': 151}]
2025-05-10 00:01:22,477 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-10 00:01:22,902 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-10 00:01:22,902 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 209.0}, {'field': 'offline_amount', 'old_value': 10120.1, 'new_value': 19017.1}, {'field': 'total_amount', 'old_value': 10120.1, 'new_value': 19226.1}, {'field': 'order_count', 'old_value': 121, 'new_value': 9227}]
2025-05-10 00:01:22,902 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPF
2025-05-10 00:01:23,366 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPF
2025-05-10 00:01:23,367 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27105.0, 'new_value': 41505.0}, {'field': 'total_amount', 'old_value': 27105.0, 'new_value': 41505.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-05-10 00:01:23,367 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-10 00:01:23,771 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-10 00:01:23,772 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 174501.71, 'new_value': 190346.7}, {'field': 'total_amount', 'old_value': 174501.71, 'new_value': 190346.7}]
2025-05-10 00:01:23,772 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-10 00:01:24,228 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-10 00:01:24,228 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 166980.0, 'new_value': 212883.0}, {'field': 'total_amount', 'old_value': 166980.0, 'new_value': 212883.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 29}]
2025-05-10 00:01:24,230 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-10 00:01:24,676 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-10 00:01:24,676 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 93.2, 'new_value': 113.0}, {'field': 'offline_amount', 'old_value': 34157.2, 'new_value': 35159.2}, {'field': 'total_amount', 'old_value': 34250.4, 'new_value': 35272.2}, {'field': 'order_count', 'old_value': 551, 'new_value': 565}]
2025-05-10 00:01:24,678 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AM9C
2025-05-10 00:01:25,117 - INFO - 更新表单数据成功: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AM9C
2025-05-10 00:01:25,117 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3868.6, 'new_value': 3956.6}, {'field': 'total_amount', 'old_value': 3868.6, 'new_value': 3956.6}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-10 00:01:25,117 - INFO - 日期 2025-05 处理完成 - 更新: 123 条，插入: 0 条，错误: 0 条
2025-05-10 00:01:25,118 - INFO - 数据同步完成！更新: 123 条，插入: 0 条，错误: 0 条
2025-05-10 00:01:25,119 - INFO - =================同步完成====================
2025-05-10 03:00:03,469 - INFO - =================使用默认全量同步=============
2025-05-10 03:00:04,789 - INFO - MySQL查询成功，共获取 3288 条记录
2025-05-10 03:00:04,790 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-10 03:00:04,817 - INFO - 开始处理日期: 2025-01
2025-05-10 03:00:04,820 - INFO - Request Parameters - Page 1:
2025-05-10 03:00:04,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 03:00:04,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 03:00:06,022 - INFO - Response - Page 1:
2025-05-10 03:00:06,223 - INFO - 第 1 页获取到 100 条记录
2025-05-10 03:00:06,223 - INFO - Request Parameters - Page 2:
2025-05-10 03:00:06,223 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 03:00:06,223 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 03:00:06,679 - INFO - Response - Page 2:
2025-05-10 03:00:06,879 - INFO - 第 2 页获取到 100 条记录
2025-05-10 03:00:06,879 - INFO - Request Parameters - Page 3:
2025-05-10 03:00:06,879 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 03:00:06,879 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 03:00:07,363 - INFO - Response - Page 3:
2025-05-10 03:00:07,563 - INFO - 第 3 页获取到 100 条记录
2025-05-10 03:00:07,563 - INFO - Request Parameters - Page 4:
2025-05-10 03:00:07,563 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 03:00:07,563 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 03:00:08,050 - INFO - Response - Page 4:
2025-05-10 03:00:08,250 - INFO - 第 4 页获取到 100 条记录
2025-05-10 03:00:08,250 - INFO - Request Parameters - Page 5:
2025-05-10 03:00:08,250 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 03:00:08,250 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 03:00:08,762 - INFO - Response - Page 5:
2025-05-10 03:00:08,963 - INFO - 第 5 页获取到 100 条记录
2025-05-10 03:00:08,963 - INFO - Request Parameters - Page 6:
2025-05-10 03:00:08,963 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 03:00:08,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 03:00:09,439 - INFO - Response - Page 6:
2025-05-10 03:00:09,641 - INFO - 第 6 页获取到 100 条记录
2025-05-10 03:00:09,641 - INFO - Request Parameters - Page 7:
2025-05-10 03:00:09,641 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 03:00:09,641 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 03:00:10,115 - INFO - Response - Page 7:
2025-05-10 03:00:10,315 - INFO - 第 7 页获取到 82 条记录
2025-05-10 03:00:10,315 - INFO - 查询完成，共获取到 682 条记录
2025-05-10 03:00:10,315 - INFO - 获取到 682 条表单数据
2025-05-10 03:00:10,327 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-10 03:00:10,338 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-10 03:00:10,339 - INFO - 开始处理日期: 2025-02
2025-05-10 03:00:10,339 - INFO - Request Parameters - Page 1:
2025-05-10 03:00:10,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 03:00:10,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 03:00:10,766 - INFO - Response - Page 1:
2025-05-10 03:00:10,966 - INFO - 第 1 页获取到 100 条记录
2025-05-10 03:00:10,966 - INFO - Request Parameters - Page 2:
2025-05-10 03:00:10,966 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 03:00:10,966 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 03:00:11,434 - INFO - Response - Page 2:
2025-05-10 03:00:11,634 - INFO - 第 2 页获取到 100 条记录
2025-05-10 03:00:11,634 - INFO - Request Parameters - Page 3:
2025-05-10 03:00:11,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 03:00:11,635 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 03:00:12,123 - INFO - Response - Page 3:
2025-05-10 03:00:12,324 - INFO - 第 3 页获取到 100 条记录
2025-05-10 03:00:12,324 - INFO - Request Parameters - Page 4:
2025-05-10 03:00:12,324 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 03:00:12,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 03:00:12,882 - INFO - Response - Page 4:
2025-05-10 03:00:13,083 - INFO - 第 4 页获取到 100 条记录
2025-05-10 03:00:13,083 - INFO - Request Parameters - Page 5:
2025-05-10 03:00:13,083 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 03:00:13,083 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 03:00:13,565 - INFO - Response - Page 5:
2025-05-10 03:00:13,766 - INFO - 第 5 页获取到 100 条记录
2025-05-10 03:00:13,766 - INFO - Request Parameters - Page 6:
2025-05-10 03:00:13,766 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 03:00:13,766 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 03:00:14,234 - INFO - Response - Page 6:
2025-05-10 03:00:14,435 - INFO - 第 6 页获取到 100 条记录
2025-05-10 03:00:14,435 - INFO - Request Parameters - Page 7:
2025-05-10 03:00:14,435 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 03:00:14,435 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 03:00:14,929 - INFO - Response - Page 7:
2025-05-10 03:00:15,129 - INFO - 第 7 页获取到 70 条记录
2025-05-10 03:00:15,129 - INFO - 查询完成，共获取到 670 条记录
2025-05-10 03:00:15,129 - INFO - 获取到 670 条表单数据
2025-05-10 03:00:15,142 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-10 03:00:15,153 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-10 03:00:15,154 - INFO - 开始处理日期: 2025-03
2025-05-10 03:00:15,154 - INFO - Request Parameters - Page 1:
2025-05-10 03:00:15,154 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 03:00:15,154 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 03:00:15,743 - INFO - Response - Page 1:
2025-05-10 03:00:15,944 - INFO - 第 1 页获取到 100 条记录
2025-05-10 03:00:15,944 - INFO - Request Parameters - Page 2:
2025-05-10 03:00:15,944 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 03:00:15,944 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 03:00:16,388 - INFO - Response - Page 2:
2025-05-10 03:00:16,588 - INFO - 第 2 页获取到 100 条记录
2025-05-10 03:00:16,588 - INFO - Request Parameters - Page 3:
2025-05-10 03:00:16,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 03:00:16,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 03:00:17,244 - INFO - Response - Page 3:
2025-05-10 03:00:17,444 - INFO - 第 3 页获取到 100 条记录
2025-05-10 03:00:17,444 - INFO - Request Parameters - Page 4:
2025-05-10 03:00:17,444 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 03:00:17,444 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 03:00:17,920 - INFO - Response - Page 4:
2025-05-10 03:00:18,120 - INFO - 第 4 页获取到 100 条记录
2025-05-10 03:00:18,120 - INFO - Request Parameters - Page 5:
2025-05-10 03:00:18,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 03:00:18,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 03:00:18,619 - INFO - Response - Page 5:
2025-05-10 03:00:18,819 - INFO - 第 5 页获取到 100 条记录
2025-05-10 03:00:18,819 - INFO - Request Parameters - Page 6:
2025-05-10 03:00:18,819 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 03:00:18,819 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 03:00:19,340 - INFO - Response - Page 6:
2025-05-10 03:00:19,540 - INFO - 第 6 页获取到 100 条记录
2025-05-10 03:00:19,540 - INFO - Request Parameters - Page 7:
2025-05-10 03:00:19,540 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 03:00:19,540 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 03:00:19,956 - INFO - Response - Page 7:
2025-05-10 03:00:20,157 - INFO - 第 7 页获取到 61 条记录
2025-05-10 03:00:20,157 - INFO - 查询完成，共获取到 661 条记录
2025-05-10 03:00:20,157 - INFO - 获取到 661 条表单数据
2025-05-10 03:00:20,172 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-10 03:00:20,185 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-10 03:00:20,185 - INFO - 开始处理日期: 2025-04
2025-05-10 03:00:20,185 - INFO - Request Parameters - Page 1:
2025-05-10 03:00:20,185 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 03:00:20,185 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 03:00:20,699 - INFO - Response - Page 1:
2025-05-10 03:00:20,899 - INFO - 第 1 页获取到 100 条记录
2025-05-10 03:00:20,899 - INFO - Request Parameters - Page 2:
2025-05-10 03:00:20,899 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 03:00:20,899 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 03:00:21,421 - INFO - Response - Page 2:
2025-05-10 03:00:21,621 - INFO - 第 2 页获取到 100 条记录
2025-05-10 03:00:21,621 - INFO - Request Parameters - Page 3:
2025-05-10 03:00:21,621 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 03:00:21,621 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 03:00:22,103 - INFO - Response - Page 3:
2025-05-10 03:00:22,303 - INFO - 第 3 页获取到 100 条记录
2025-05-10 03:00:22,303 - INFO - Request Parameters - Page 4:
2025-05-10 03:00:22,303 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 03:00:22,303 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 03:00:22,764 - INFO - Response - Page 4:
2025-05-10 03:00:22,965 - INFO - 第 4 页获取到 100 条记录
2025-05-10 03:00:22,965 - INFO - Request Parameters - Page 5:
2025-05-10 03:00:22,965 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 03:00:22,965 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 03:00:23,507 - INFO - Response - Page 5:
2025-05-10 03:00:23,708 - INFO - 第 5 页获取到 100 条记录
2025-05-10 03:00:23,708 - INFO - Request Parameters - Page 6:
2025-05-10 03:00:23,708 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 03:00:23,708 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 03:00:24,183 - INFO - Response - Page 6:
2025-05-10 03:00:24,385 - INFO - 第 6 页获取到 100 条记录
2025-05-10 03:00:24,385 - INFO - Request Parameters - Page 7:
2025-05-10 03:00:24,385 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 03:00:24,385 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 03:00:24,775 - INFO - Response - Page 7:
2025-05-10 03:00:24,976 - INFO - 第 7 页获取到 54 条记录
2025-05-10 03:00:24,976 - INFO - 查询完成，共获取到 654 条记录
2025-05-10 03:00:24,976 - INFO - 获取到 654 条表单数据
2025-05-10 03:00:24,988 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-10 03:00:25,000 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-10 03:00:25,000 - INFO - 开始处理日期: 2025-05
2025-05-10 03:00:25,000 - INFO - Request Parameters - Page 1:
2025-05-10 03:00:25,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 03:00:25,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 03:00:25,499 - INFO - Response - Page 1:
2025-05-10 03:00:25,700 - INFO - 第 1 页获取到 100 条记录
2025-05-10 03:00:25,700 - INFO - Request Parameters - Page 2:
2025-05-10 03:00:25,700 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 03:00:25,700 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 03:00:26,200 - INFO - Response - Page 2:
2025-05-10 03:00:26,400 - INFO - 第 2 页获取到 100 条记录
2025-05-10 03:00:26,400 - INFO - Request Parameters - Page 3:
2025-05-10 03:00:26,400 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 03:00:26,400 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 03:00:26,896 - INFO - Response - Page 3:
2025-05-10 03:00:27,097 - INFO - 第 3 页获取到 100 条记录
2025-05-10 03:00:27,097 - INFO - Request Parameters - Page 4:
2025-05-10 03:00:27,097 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 03:00:27,097 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 03:00:27,569 - INFO - Response - Page 4:
2025-05-10 03:00:27,769 - INFO - 第 4 页获取到 100 条记录
2025-05-10 03:00:27,769 - INFO - Request Parameters - Page 5:
2025-05-10 03:00:27,769 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 03:00:27,769 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 03:00:28,286 - INFO - Response - Page 5:
2025-05-10 03:00:28,486 - INFO - 第 5 页获取到 100 条记录
2025-05-10 03:00:28,486 - INFO - Request Parameters - Page 6:
2025-05-10 03:00:28,486 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 03:00:28,486 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 03:00:29,000 - INFO - Response - Page 6:
2025-05-10 03:00:29,201 - INFO - 第 6 页获取到 100 条记录
2025-05-10 03:00:29,201 - INFO - Request Parameters - Page 7:
2025-05-10 03:00:29,201 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 03:00:29,201 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 03:00:29,537 - INFO - Response - Page 7:
2025-05-10 03:00:29,737 - INFO - 第 7 页获取到 21 条记录
2025-05-10 03:00:29,737 - INFO - 查询完成，共获取到 621 条记录
2025-05-10 03:00:29,737 - INFO - 获取到 621 条表单数据
2025-05-10 03:00:29,749 - INFO - 当前日期 2025-05 有 621 条MySQL数据需要处理
2025-05-10 03:00:29,753 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-10 03:00:30,208 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-10 03:00:30,209 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 683441.38, 'new_value': 736727.12}, {'field': 'total_amount', 'old_value': 683441.38, 'new_value': 736727.12}, {'field': 'order_count', 'old_value': 4880, 'new_value': 5386}]
2025-05-10 03:00:30,212 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-10 03:00:30,709 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-10 03:00:30,709 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 275158.52, 'new_value': 297468.7}, {'field': 'total_amount', 'old_value': 275158.52, 'new_value': 297468.7}, {'field': 'order_count', 'old_value': 1766, 'new_value': 1927}]
2025-05-10 03:00:30,712 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-10 03:00:31,091 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-10 03:00:31,092 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 80533.67, 'new_value': 86237.7}, {'field': 'offline_amount', 'old_value': 191000.0, 'new_value': 201000.0}, {'field': 'total_amount', 'old_value': 271533.67, 'new_value': 287237.7}, {'field': 'order_count', 'old_value': 511, 'new_value': 559}]
2025-05-10 03:00:31,095 - INFO - 日期 2025-05 处理完成 - 更新: 3 条，插入: 0 条，错误: 0 条
2025-05-10 03:00:31,095 - INFO - 数据同步完成！更新: 3 条，插入: 0 条，错误: 0 条
2025-05-10 03:00:31,097 - INFO - =================同步完成====================
2025-05-10 06:00:03,481 - INFO - =================使用默认全量同步=============
2025-05-10 06:00:04,799 - INFO - MySQL查询成功，共获取 3288 条记录
2025-05-10 06:00:04,800 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-10 06:00:04,829 - INFO - 开始处理日期: 2025-01
2025-05-10 06:00:04,832 - INFO - Request Parameters - Page 1:
2025-05-10 06:00:04,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 06:00:04,832 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 06:00:05,902 - INFO - Response - Page 1:
2025-05-10 06:00:06,102 - INFO - 第 1 页获取到 100 条记录
2025-05-10 06:00:06,102 - INFO - Request Parameters - Page 2:
2025-05-10 06:00:06,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 06:00:06,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 06:00:06,887 - INFO - Response - Page 2:
2025-05-10 06:00:07,087 - INFO - 第 2 页获取到 100 条记录
2025-05-10 06:00:07,087 - INFO - Request Parameters - Page 3:
2025-05-10 06:00:07,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 06:00:07,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 06:00:07,614 - INFO - Response - Page 3:
2025-05-10 06:00:07,814 - INFO - 第 3 页获取到 100 条记录
2025-05-10 06:00:07,814 - INFO - Request Parameters - Page 4:
2025-05-10 06:00:07,814 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 06:00:07,814 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 06:00:08,279 - INFO - Response - Page 4:
2025-05-10 06:00:08,481 - INFO - 第 4 页获取到 100 条记录
2025-05-10 06:00:08,481 - INFO - Request Parameters - Page 5:
2025-05-10 06:00:08,481 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 06:00:08,481 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 06:00:08,973 - INFO - Response - Page 5:
2025-05-10 06:00:09,174 - INFO - 第 5 页获取到 100 条记录
2025-05-10 06:00:09,174 - INFO - Request Parameters - Page 6:
2025-05-10 06:00:09,174 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 06:00:09,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 06:00:09,652 - INFO - Response - Page 6:
2025-05-10 06:00:09,854 - INFO - 第 6 页获取到 100 条记录
2025-05-10 06:00:09,854 - INFO - Request Parameters - Page 7:
2025-05-10 06:00:09,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 06:00:09,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 06:00:10,341 - INFO - Response - Page 7:
2025-05-10 06:00:10,542 - INFO - 第 7 页获取到 82 条记录
2025-05-10 06:00:10,542 - INFO - 查询完成，共获取到 682 条记录
2025-05-10 06:00:10,542 - INFO - 获取到 682 条表单数据
2025-05-10 06:00:10,553 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-10 06:00:10,565 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-10 06:00:10,565 - INFO - 开始处理日期: 2025-02
2025-05-10 06:00:10,565 - INFO - Request Parameters - Page 1:
2025-05-10 06:00:10,566 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 06:00:10,566 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 06:00:11,058 - INFO - Response - Page 1:
2025-05-10 06:00:11,258 - INFO - 第 1 页获取到 100 条记录
2025-05-10 06:00:11,258 - INFO - Request Parameters - Page 2:
2025-05-10 06:00:11,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 06:00:11,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 06:00:11,742 - INFO - Response - Page 2:
2025-05-10 06:00:11,942 - INFO - 第 2 页获取到 100 条记录
2025-05-10 06:00:11,942 - INFO - Request Parameters - Page 3:
2025-05-10 06:00:11,942 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 06:00:11,942 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 06:00:12,513 - INFO - Response - Page 3:
2025-05-10 06:00:12,713 - INFO - 第 3 页获取到 100 条记录
2025-05-10 06:00:12,713 - INFO - Request Parameters - Page 4:
2025-05-10 06:00:12,713 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 06:00:12,713 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 06:00:13,219 - INFO - Response - Page 4:
2025-05-10 06:00:13,420 - INFO - 第 4 页获取到 100 条记录
2025-05-10 06:00:13,420 - INFO - Request Parameters - Page 5:
2025-05-10 06:00:13,420 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 06:00:13,420 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 06:00:13,921 - INFO - Response - Page 5:
2025-05-10 06:00:14,121 - INFO - 第 5 页获取到 100 条记录
2025-05-10 06:00:14,121 - INFO - Request Parameters - Page 6:
2025-05-10 06:00:14,121 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 06:00:14,121 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 06:00:14,613 - INFO - Response - Page 6:
2025-05-10 06:00:14,813 - INFO - 第 6 页获取到 100 条记录
2025-05-10 06:00:14,813 - INFO - Request Parameters - Page 7:
2025-05-10 06:00:14,813 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 06:00:14,813 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 06:00:15,308 - INFO - Response - Page 7:
2025-05-10 06:00:15,508 - INFO - 第 7 页获取到 70 条记录
2025-05-10 06:00:15,508 - INFO - 查询完成，共获取到 670 条记录
2025-05-10 06:00:15,508 - INFO - 获取到 670 条表单数据
2025-05-10 06:00:15,522 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-10 06:00:15,534 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-10 06:00:15,534 - INFO - 开始处理日期: 2025-03
2025-05-10 06:00:15,534 - INFO - Request Parameters - Page 1:
2025-05-10 06:00:15,534 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 06:00:15,535 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 06:00:16,005 - INFO - Response - Page 1:
2025-05-10 06:00:16,205 - INFO - 第 1 页获取到 100 条记录
2025-05-10 06:00:16,205 - INFO - Request Parameters - Page 2:
2025-05-10 06:00:16,205 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 06:00:16,205 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 06:00:16,758 - INFO - Response - Page 2:
2025-05-10 06:00:16,958 - INFO - 第 2 页获取到 100 条记录
2025-05-10 06:00:16,958 - INFO - Request Parameters - Page 3:
2025-05-10 06:00:16,958 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 06:00:16,958 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 06:00:17,487 - INFO - Response - Page 3:
2025-05-10 06:00:17,687 - INFO - 第 3 页获取到 100 条记录
2025-05-10 06:00:17,687 - INFO - Request Parameters - Page 4:
2025-05-10 06:00:17,687 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 06:00:17,687 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 06:00:18,194 - INFO - Response - Page 4:
2025-05-10 06:00:18,394 - INFO - 第 4 页获取到 100 条记录
2025-05-10 06:00:18,394 - INFO - Request Parameters - Page 5:
2025-05-10 06:00:18,394 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 06:00:18,394 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 06:00:18,938 - INFO - Response - Page 5:
2025-05-10 06:00:19,139 - INFO - 第 5 页获取到 100 条记录
2025-05-10 06:00:19,139 - INFO - Request Parameters - Page 6:
2025-05-10 06:00:19,139 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 06:00:19,139 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 06:00:19,638 - INFO - Response - Page 6:
2025-05-10 06:00:19,839 - INFO - 第 6 页获取到 100 条记录
2025-05-10 06:00:19,839 - INFO - Request Parameters - Page 7:
2025-05-10 06:00:19,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 06:00:19,840 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 06:00:20,265 - INFO - Response - Page 7:
2025-05-10 06:00:20,465 - INFO - 第 7 页获取到 61 条记录
2025-05-10 06:00:20,465 - INFO - 查询完成，共获取到 661 条记录
2025-05-10 06:00:20,465 - INFO - 获取到 661 条表单数据
2025-05-10 06:00:20,478 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-10 06:00:20,489 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-10 06:00:20,489 - INFO - 开始处理日期: 2025-04
2025-05-10 06:00:20,489 - INFO - Request Parameters - Page 1:
2025-05-10 06:00:20,489 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 06:00:20,489 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 06:00:21,012 - INFO - Response - Page 1:
2025-05-10 06:00:21,212 - INFO - 第 1 页获取到 100 条记录
2025-05-10 06:00:21,212 - INFO - Request Parameters - Page 2:
2025-05-10 06:00:21,212 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 06:00:21,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 06:00:21,716 - INFO - Response - Page 2:
2025-05-10 06:00:21,917 - INFO - 第 2 页获取到 100 条记录
2025-05-10 06:00:21,917 - INFO - Request Parameters - Page 3:
2025-05-10 06:00:21,917 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 06:00:21,917 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 06:00:22,468 - INFO - Response - Page 3:
2025-05-10 06:00:22,669 - INFO - 第 3 页获取到 100 条记录
2025-05-10 06:00:22,669 - INFO - Request Parameters - Page 4:
2025-05-10 06:00:22,669 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 06:00:22,669 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 06:00:23,131 - INFO - Response - Page 4:
2025-05-10 06:00:23,331 - INFO - 第 4 页获取到 100 条记录
2025-05-10 06:00:23,331 - INFO - Request Parameters - Page 5:
2025-05-10 06:00:23,331 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 06:00:23,331 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 06:00:23,845 - INFO - Response - Page 5:
2025-05-10 06:00:24,046 - INFO - 第 5 页获取到 100 条记录
2025-05-10 06:00:24,046 - INFO - Request Parameters - Page 6:
2025-05-10 06:00:24,046 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 06:00:24,046 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 06:00:24,573 - INFO - Response - Page 6:
2025-05-10 06:00:24,774 - INFO - 第 6 页获取到 100 条记录
2025-05-10 06:00:24,774 - INFO - Request Parameters - Page 7:
2025-05-10 06:00:24,774 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 06:00:24,774 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 06:00:25,214 - INFO - Response - Page 7:
2025-05-10 06:00:25,416 - INFO - 第 7 页获取到 54 条记录
2025-05-10 06:00:25,416 - INFO - 查询完成，共获取到 654 条记录
2025-05-10 06:00:25,416 - INFO - 获取到 654 条表单数据
2025-05-10 06:00:25,428 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-10 06:00:25,440 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-10 06:00:25,440 - INFO - 开始处理日期: 2025-05
2025-05-10 06:00:25,440 - INFO - Request Parameters - Page 1:
2025-05-10 06:00:25,440 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 06:00:25,441 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 06:00:25,872 - INFO - Response - Page 1:
2025-05-10 06:00:26,072 - INFO - 第 1 页获取到 100 条记录
2025-05-10 06:00:26,072 - INFO - Request Parameters - Page 2:
2025-05-10 06:00:26,072 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 06:00:26,072 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 06:00:26,553 - INFO - Response - Page 2:
2025-05-10 06:00:26,755 - INFO - 第 2 页获取到 100 条记录
2025-05-10 06:00:26,755 - INFO - Request Parameters - Page 3:
2025-05-10 06:00:26,755 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 06:00:26,755 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 06:00:27,236 - INFO - Response - Page 3:
2025-05-10 06:00:27,437 - INFO - 第 3 页获取到 100 条记录
2025-05-10 06:00:27,437 - INFO - Request Parameters - Page 4:
2025-05-10 06:00:27,437 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 06:00:27,438 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 06:00:27,849 - INFO - Response - Page 4:
2025-05-10 06:00:28,049 - INFO - 第 4 页获取到 100 条记录
2025-05-10 06:00:28,049 - INFO - Request Parameters - Page 5:
2025-05-10 06:00:28,049 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 06:00:28,049 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 06:00:28,471 - INFO - Response - Page 5:
2025-05-10 06:00:28,671 - INFO - 第 5 页获取到 100 条记录
2025-05-10 06:00:28,671 - INFO - Request Parameters - Page 6:
2025-05-10 06:00:28,671 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 06:00:28,671 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 06:00:29,237 - INFO - Response - Page 6:
2025-05-10 06:00:29,437 - INFO - 第 6 页获取到 100 条记录
2025-05-10 06:00:29,437 - INFO - Request Parameters - Page 7:
2025-05-10 06:00:29,437 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 06:00:29,437 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 06:00:29,772 - INFO - Response - Page 7:
2025-05-10 06:00:29,973 - INFO - 第 7 页获取到 21 条记录
2025-05-10 06:00:29,973 - INFO - 查询完成，共获取到 621 条记录
2025-05-10 06:00:29,973 - INFO - 获取到 621 条表单数据
2025-05-10 06:00:29,983 - INFO - 当前日期 2025-05 有 621 条MySQL数据需要处理
2025-05-10 06:00:29,994 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-10 06:00:29,994 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-10 06:00:29,996 - INFO - =================同步完成====================
2025-05-10 09:00:01,554 - INFO - =================使用默认全量同步=============
2025-05-10 09:00:02,881 - INFO - MySQL查询成功，共获取 3288 条记录
2025-05-10 09:00:02,882 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-10 09:00:02,913 - INFO - 开始处理日期: 2025-01
2025-05-10 09:00:02,916 - INFO - Request Parameters - Page 1:
2025-05-10 09:00:02,916 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 09:00:02,916 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 09:00:04,151 - INFO - Response - Page 1:
2025-05-10 09:00:04,351 - INFO - 第 1 页获取到 100 条记录
2025-05-10 09:00:04,351 - INFO - Request Parameters - Page 2:
2025-05-10 09:00:04,351 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 09:00:04,351 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 09:00:04,886 - INFO - Response - Page 2:
2025-05-10 09:00:05,087 - INFO - 第 2 页获取到 100 条记录
2025-05-10 09:00:05,087 - INFO - Request Parameters - Page 3:
2025-05-10 09:00:05,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 09:00:05,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 09:00:05,625 - INFO - Response - Page 3:
2025-05-10 09:00:05,825 - INFO - 第 3 页获取到 100 条记录
2025-05-10 09:00:05,825 - INFO - Request Parameters - Page 4:
2025-05-10 09:00:05,825 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 09:00:05,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 09:00:06,443 - INFO - Response - Page 4:
2025-05-10 09:00:06,643 - INFO - 第 4 页获取到 100 条记录
2025-05-10 09:00:06,643 - INFO - Request Parameters - Page 5:
2025-05-10 09:00:06,643 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 09:00:06,643 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 09:00:07,164 - INFO - Response - Page 5:
2025-05-10 09:00:07,364 - INFO - 第 5 页获取到 100 条记录
2025-05-10 09:00:07,364 - INFO - Request Parameters - Page 6:
2025-05-10 09:00:07,364 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 09:00:07,364 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 09:00:07,949 - INFO - Response - Page 6:
2025-05-10 09:00:08,149 - INFO - 第 6 页获取到 100 条记录
2025-05-10 09:00:08,149 - INFO - Request Parameters - Page 7:
2025-05-10 09:00:08,149 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 09:00:08,149 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 09:00:08,656 - INFO - Response - Page 7:
2025-05-10 09:00:08,856 - INFO - 第 7 页获取到 82 条记录
2025-05-10 09:00:08,856 - INFO - 查询完成，共获取到 682 条记录
2025-05-10 09:00:08,856 - INFO - 获取到 682 条表单数据
2025-05-10 09:00:08,868 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-10 09:00:08,879 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-10 09:00:08,879 - INFO - 开始处理日期: 2025-02
2025-05-10 09:00:08,880 - INFO - Request Parameters - Page 1:
2025-05-10 09:00:08,880 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 09:00:08,880 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 09:00:09,364 - INFO - Response - Page 1:
2025-05-10 09:00:09,564 - INFO - 第 1 页获取到 100 条记录
2025-05-10 09:00:09,564 - INFO - Request Parameters - Page 2:
2025-05-10 09:00:09,564 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 09:00:09,564 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 09:00:10,142 - INFO - Response - Page 2:
2025-05-10 09:00:10,343 - INFO - 第 2 页获取到 100 条记录
2025-05-10 09:00:10,343 - INFO - Request Parameters - Page 3:
2025-05-10 09:00:10,343 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 09:00:10,343 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 09:00:10,844 - INFO - Response - Page 3:
2025-05-10 09:00:11,046 - INFO - 第 3 页获取到 100 条记录
2025-05-10 09:00:11,046 - INFO - Request Parameters - Page 4:
2025-05-10 09:00:11,046 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 09:00:11,046 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 09:00:11,507 - INFO - Response - Page 4:
2025-05-10 09:00:11,708 - INFO - 第 4 页获取到 100 条记录
2025-05-10 09:00:11,708 - INFO - Request Parameters - Page 5:
2025-05-10 09:00:11,708 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 09:00:11,708 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 09:00:12,216 - INFO - Response - Page 5:
2025-05-10 09:00:12,417 - INFO - 第 5 页获取到 100 条记录
2025-05-10 09:00:12,417 - INFO - Request Parameters - Page 6:
2025-05-10 09:00:12,417 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 09:00:12,417 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 09:00:12,951 - INFO - Response - Page 6:
2025-05-10 09:00:13,152 - INFO - 第 6 页获取到 100 条记录
2025-05-10 09:00:13,152 - INFO - Request Parameters - Page 7:
2025-05-10 09:00:13,152 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 09:00:13,152 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 09:00:13,646 - INFO - Response - Page 7:
2025-05-10 09:00:13,846 - INFO - 第 7 页获取到 70 条记录
2025-05-10 09:00:13,846 - INFO - 查询完成，共获取到 670 条记录
2025-05-10 09:00:13,846 - INFO - 获取到 670 条表单数据
2025-05-10 09:00:13,860 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-10 09:00:13,872 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-10 09:00:13,872 - INFO - 开始处理日期: 2025-03
2025-05-10 09:00:13,873 - INFO - Request Parameters - Page 1:
2025-05-10 09:00:13,873 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 09:00:13,873 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 09:00:14,356 - INFO - Response - Page 1:
2025-05-10 09:00:14,556 - INFO - 第 1 页获取到 100 条记录
2025-05-10 09:00:14,556 - INFO - Request Parameters - Page 2:
2025-05-10 09:00:14,556 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 09:00:14,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 09:00:15,091 - INFO - Response - Page 2:
2025-05-10 09:00:15,292 - INFO - 第 2 页获取到 100 条记录
2025-05-10 09:00:15,292 - INFO - Request Parameters - Page 3:
2025-05-10 09:00:15,292 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 09:00:15,292 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 09:00:15,814 - INFO - Response - Page 3:
2025-05-10 09:00:16,015 - INFO - 第 3 页获取到 100 条记录
2025-05-10 09:00:16,015 - INFO - Request Parameters - Page 4:
2025-05-10 09:00:16,015 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 09:00:16,015 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 09:00:16,624 - INFO - Response - Page 4:
2025-05-10 09:00:16,824 - INFO - 第 4 页获取到 100 条记录
2025-05-10 09:00:16,824 - INFO - Request Parameters - Page 5:
2025-05-10 09:00:16,824 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 09:00:16,824 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 09:00:17,351 - INFO - Response - Page 5:
2025-05-10 09:00:17,551 - INFO - 第 5 页获取到 100 条记录
2025-05-10 09:00:17,551 - INFO - Request Parameters - Page 6:
2025-05-10 09:00:17,551 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 09:00:17,551 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 09:00:18,074 - INFO - Response - Page 6:
2025-05-10 09:00:18,274 - INFO - 第 6 页获取到 100 条记录
2025-05-10 09:00:18,274 - INFO - Request Parameters - Page 7:
2025-05-10 09:00:18,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 09:00:18,274 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 09:00:18,707 - INFO - Response - Page 7:
2025-05-10 09:00:18,907 - INFO - 第 7 页获取到 61 条记录
2025-05-10 09:00:18,907 - INFO - 查询完成，共获取到 661 条记录
2025-05-10 09:00:18,907 - INFO - 获取到 661 条表单数据
2025-05-10 09:00:18,919 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-10 09:00:18,930 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-10 09:00:18,930 - INFO - 开始处理日期: 2025-04
2025-05-10 09:00:18,930 - INFO - Request Parameters - Page 1:
2025-05-10 09:00:18,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 09:00:18,931 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 09:00:19,461 - INFO - Response - Page 1:
2025-05-10 09:00:19,661 - INFO - 第 1 页获取到 100 条记录
2025-05-10 09:00:19,661 - INFO - Request Parameters - Page 2:
2025-05-10 09:00:19,661 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 09:00:19,661 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 09:00:20,146 - INFO - Response - Page 2:
2025-05-10 09:00:20,347 - INFO - 第 2 页获取到 100 条记录
2025-05-10 09:00:20,347 - INFO - Request Parameters - Page 3:
2025-05-10 09:00:20,347 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 09:00:20,347 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 09:00:20,832 - INFO - Response - Page 3:
2025-05-10 09:00:21,032 - INFO - 第 3 页获取到 100 条记录
2025-05-10 09:00:21,032 - INFO - Request Parameters - Page 4:
2025-05-10 09:00:21,032 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 09:00:21,032 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 09:00:21,562 - INFO - Response - Page 4:
2025-05-10 09:00:21,762 - INFO - 第 4 页获取到 100 条记录
2025-05-10 09:00:21,762 - INFO - Request Parameters - Page 5:
2025-05-10 09:00:21,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 09:00:21,762 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 09:00:22,275 - INFO - Response - Page 5:
2025-05-10 09:00:22,475 - INFO - 第 5 页获取到 100 条记录
2025-05-10 09:00:22,475 - INFO - Request Parameters - Page 6:
2025-05-10 09:00:22,475 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 09:00:22,475 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 09:00:22,967 - INFO - Response - Page 6:
2025-05-10 09:00:23,168 - INFO - 第 6 页获取到 100 条记录
2025-05-10 09:00:23,168 - INFO - Request Parameters - Page 7:
2025-05-10 09:00:23,168 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 09:00:23,168 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 09:00:23,642 - INFO - Response - Page 7:
2025-05-10 09:00:23,842 - INFO - 第 7 页获取到 54 条记录
2025-05-10 09:00:23,842 - INFO - 查询完成，共获取到 654 条记录
2025-05-10 09:00:23,843 - INFO - 获取到 654 条表单数据
2025-05-10 09:00:23,855 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-10 09:00:23,867 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-10 09:00:23,867 - INFO - 开始处理日期: 2025-05
2025-05-10 09:00:23,867 - INFO - Request Parameters - Page 1:
2025-05-10 09:00:23,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 09:00:23,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 09:00:24,388 - INFO - Response - Page 1:
2025-05-10 09:00:24,588 - INFO - 第 1 页获取到 100 条记录
2025-05-10 09:00:24,588 - INFO - Request Parameters - Page 2:
2025-05-10 09:00:24,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 09:00:24,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 09:00:25,095 - INFO - Response - Page 2:
2025-05-10 09:00:25,296 - INFO - 第 2 页获取到 100 条记录
2025-05-10 09:00:25,296 - INFO - Request Parameters - Page 3:
2025-05-10 09:00:25,296 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 09:00:25,296 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 09:00:25,871 - INFO - Response - Page 3:
2025-05-10 09:00:26,071 - INFO - 第 3 页获取到 100 条记录
2025-05-10 09:00:26,071 - INFO - Request Parameters - Page 4:
2025-05-10 09:00:26,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 09:00:26,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 09:00:26,570 - INFO - Response - Page 4:
2025-05-10 09:00:26,770 - INFO - 第 4 页获取到 100 条记录
2025-05-10 09:00:26,770 - INFO - Request Parameters - Page 5:
2025-05-10 09:00:26,770 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 09:00:26,770 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 09:00:27,304 - INFO - Response - Page 5:
2025-05-10 09:00:27,504 - INFO - 第 5 页获取到 100 条记录
2025-05-10 09:00:27,504 - INFO - Request Parameters - Page 6:
2025-05-10 09:00:27,504 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 09:00:27,504 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 09:00:27,953 - INFO - Response - Page 6:
2025-05-10 09:00:28,154 - INFO - 第 6 页获取到 100 条记录
2025-05-10 09:00:28,154 - INFO - Request Parameters - Page 7:
2025-05-10 09:00:28,154 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 09:00:28,154 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 09:00:28,462 - INFO - Response - Page 7:
2025-05-10 09:00:28,663 - INFO - 第 7 页获取到 21 条记录
2025-05-10 09:00:28,663 - INFO - 查询完成，共获取到 621 条记录
2025-05-10 09:00:28,663 - INFO - 获取到 621 条表单数据
2025-05-10 09:00:28,674 - INFO - 当前日期 2025-05 有 621 条MySQL数据需要处理
2025-05-10 09:00:28,674 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-10 09:00:29,225 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-10 09:00:29,226 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3800000.0, 'new_value': 4400000.0}, {'field': 'total_amount', 'old_value': 3900000.0, 'new_value': 4500000.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 21}]
2025-05-10 09:00:29,228 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-10 09:00:29,651 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-10 09:00:29,651 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 158390.0, 'new_value': 168888.0}, {'field': 'total_amount', 'old_value': 158390.0, 'new_value': 168888.0}, {'field': 'order_count', 'old_value': 60, 'new_value': 70}]
2025-05-10 09:00:29,655 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-10 09:00:30,043 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-10 09:00:30,043 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 356253.6, 'new_value': 365031.6}, {'field': 'total_amount', 'old_value': 356253.6, 'new_value': 365031.6}, {'field': 'order_count', 'old_value': 861, 'new_value': 884}]
2025-05-10 09:00:30,044 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-10 09:00:30,597 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-10 09:00:30,597 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 128474.0, 'new_value': 224065.0}, {'field': 'total_amount', 'old_value': 128474.0, 'new_value': 224065.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 30}]
2025-05-10 09:00:30,600 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-10 09:00:31,001 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-10 09:00:31,001 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66959.8, 'new_value': 72993.1}, {'field': 'total_amount', 'old_value': 66959.8, 'new_value': 72993.1}, {'field': 'order_count', 'old_value': 116, 'new_value': 129}]
2025-05-10 09:00:31,002 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-10 09:00:31,413 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-10 09:00:31,413 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 167605.2, 'new_value': 180047.1}, {'field': 'total_amount', 'old_value': 167605.2, 'new_value': 180047.1}, {'field': 'order_count', 'old_value': 195, 'new_value': 209}]
2025-05-10 09:00:31,414 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-10 09:00:31,970 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-10 09:00:31,970 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44668.0, 'new_value': 49888.4}, {'field': 'offline_amount', 'old_value': 36821.4, 'new_value': 40775.8}, {'field': 'total_amount', 'old_value': 81489.4, 'new_value': 90664.2}, {'field': 'order_count', 'old_value': 1951, 'new_value': 2162}]
2025-05-10 09:00:31,971 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-10 09:00:32,399 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-10 09:00:32,399 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1080000.0, 'new_value': 1130000.0}, {'field': 'total_amount', 'old_value': 1080000.0, 'new_value': 1130000.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-05-10 09:00:32,400 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-10 09:00:32,847 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-10 09:00:32,847 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40295.1, 'new_value': 43023.8}, {'field': 'total_amount', 'old_value': 41216.9, 'new_value': 43945.6}, {'field': 'order_count', 'old_value': 245, 'new_value': 259}]
2025-05-10 09:00:32,847 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-10 09:00:33,283 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-10 09:00:33,283 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23148.0, 'new_value': 26012.0}, {'field': 'total_amount', 'old_value': 23148.0, 'new_value': 26012.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 52}]
2025-05-10 09:00:33,283 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-10 09:00:33,798 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-10 09:00:33,798 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 405000.0, 'new_value': 410000.0}, {'field': 'total_amount', 'old_value': 405000.0, 'new_value': 410000.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-05-10 09:00:33,798 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-10 09:00:34,192 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-10 09:00:34,192 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 385000.0, 'new_value': 390000.0}, {'field': 'total_amount', 'old_value': 385000.0, 'new_value': 390000.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-05-10 09:00:34,192 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-10 09:00:34,709 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-10 09:00:34,709 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2198674.0, 'new_value': 2248674.0}, {'field': 'total_amount', 'old_value': 2198674.0, 'new_value': 2248674.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-05-10 09:00:34,710 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-10 09:00:35,137 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-10 09:00:35,137 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 887212.0, 'new_value': 941696.0}, {'field': 'total_amount', 'old_value': 887212.0, 'new_value': 941696.0}, {'field': 'order_count', 'old_value': 3323, 'new_value': 3526}]
2025-05-10 09:00:35,137 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-10 09:00:35,600 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-10 09:00:35,601 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 123998.5, 'new_value': 132749.25}, {'field': 'total_amount', 'old_value': 123998.5, 'new_value': 132749.25}, {'field': 'order_count', 'old_value': 649, 'new_value': 700}]
2025-05-10 09:00:35,601 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-10 09:00:36,030 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-10 09:00:36,030 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13781.74, 'new_value': 16152.94}, {'field': 'offline_amount', 'old_value': 20490.2, 'new_value': 21889.5}, {'field': 'total_amount', 'old_value': 34271.94, 'new_value': 38042.44}, {'field': 'order_count', 'old_value': 2532, 'new_value': 2839}]
2025-05-10 09:00:36,031 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-10 09:00:36,541 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-10 09:00:36,541 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 240911.0, 'new_value': 262000.0}, {'field': 'total_amount', 'old_value': 240911.0, 'new_value': 262000.0}, {'field': 'order_count', 'old_value': 155, 'new_value': 164}]
2025-05-10 09:00:36,542 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-10 09:00:37,012 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-10 09:00:37,013 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 356144.37, 'new_value': 368012.9}, {'field': 'total_amount', 'old_value': 356144.37, 'new_value': 368012.9}, {'field': 'order_count', 'old_value': 1453, 'new_value': 1552}]
2025-05-10 09:00:37,013 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-10 09:00:37,459 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-10 09:00:37,459 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52623.5, 'new_value': 56549.08}, {'field': 'total_amount', 'old_value': 52623.5, 'new_value': 56549.08}, {'field': 'order_count', 'old_value': 3560, 'new_value': 3846}]
2025-05-10 09:00:37,460 - INFO - 日期 2025-05 处理完成 - 更新: 19 条，插入: 0 条，错误: 0 条
2025-05-10 09:00:37,461 - INFO - 数据同步完成！更新: 19 条，插入: 0 条，错误: 0 条
2025-05-10 09:00:37,462 - INFO - =================同步完成====================
2025-05-10 12:00:01,600 - INFO - =================使用默认全量同步=============
2025-05-10 12:00:02,932 - INFO - MySQL查询成功，共获取 3288 条记录
2025-05-10 12:00:02,933 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-10 12:00:02,961 - INFO - 开始处理日期: 2025-01
2025-05-10 12:00:02,964 - INFO - Request Parameters - Page 1:
2025-05-10 12:00:02,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 12:00:02,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 12:00:04,161 - INFO - Response - Page 1:
2025-05-10 12:00:04,362 - INFO - 第 1 页获取到 100 条记录
2025-05-10 12:00:04,362 - INFO - Request Parameters - Page 2:
2025-05-10 12:00:04,362 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 12:00:04,362 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 12:00:04,883 - INFO - Response - Page 2:
2025-05-10 12:00:05,083 - INFO - 第 2 页获取到 100 条记录
2025-05-10 12:00:05,083 - INFO - Request Parameters - Page 3:
2025-05-10 12:00:05,083 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 12:00:05,083 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 12:00:05,557 - INFO - Response - Page 3:
2025-05-10 12:00:05,757 - INFO - 第 3 页获取到 100 条记录
2025-05-10 12:00:05,757 - INFO - Request Parameters - Page 4:
2025-05-10 12:00:05,757 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 12:00:05,757 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 12:00:06,270 - INFO - Response - Page 4:
2025-05-10 12:00:06,470 - INFO - 第 4 页获取到 100 条记录
2025-05-10 12:00:06,470 - INFO - Request Parameters - Page 5:
2025-05-10 12:00:06,470 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 12:00:06,470 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 12:00:06,990 - INFO - Response - Page 5:
2025-05-10 12:00:07,192 - INFO - 第 5 页获取到 100 条记录
2025-05-10 12:00:07,192 - INFO - Request Parameters - Page 6:
2025-05-10 12:00:07,192 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 12:00:07,192 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 12:00:07,679 - INFO - Response - Page 6:
2025-05-10 12:00:07,879 - INFO - 第 6 页获取到 100 条记录
2025-05-10 12:00:07,879 - INFO - Request Parameters - Page 7:
2025-05-10 12:00:07,879 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 12:00:07,879 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 12:00:08,370 - INFO - Response - Page 7:
2025-05-10 12:00:08,570 - INFO - 第 7 页获取到 82 条记录
2025-05-10 12:00:08,570 - INFO - 查询完成，共获取到 682 条记录
2025-05-10 12:00:08,570 - INFO - 获取到 682 条表单数据
2025-05-10 12:00:08,582 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-10 12:00:08,593 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-10 12:00:08,593 - INFO - 开始处理日期: 2025-02
2025-05-10 12:00:08,593 - INFO - Request Parameters - Page 1:
2025-05-10 12:00:08,594 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 12:00:08,594 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 12:00:09,059 - INFO - Response - Page 1:
2025-05-10 12:00:09,259 - INFO - 第 1 页获取到 100 条记录
2025-05-10 12:00:09,259 - INFO - Request Parameters - Page 2:
2025-05-10 12:00:09,259 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 12:00:09,259 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 12:00:09,780 - INFO - Response - Page 2:
2025-05-10 12:00:09,981 - INFO - 第 2 页获取到 100 条记录
2025-05-10 12:00:09,981 - INFO - Request Parameters - Page 3:
2025-05-10 12:00:09,981 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 12:00:09,981 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 12:00:10,453 - INFO - Response - Page 3:
2025-05-10 12:00:10,653 - INFO - 第 3 页获取到 100 条记录
2025-05-10 12:00:10,653 - INFO - Request Parameters - Page 4:
2025-05-10 12:00:10,653 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 12:00:10,653 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 12:00:11,219 - INFO - Response - Page 4:
2025-05-10 12:00:11,419 - INFO - 第 4 页获取到 100 条记录
2025-05-10 12:00:11,419 - INFO - Request Parameters - Page 5:
2025-05-10 12:00:11,419 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 12:00:11,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 12:00:11,968 - INFO - Response - Page 5:
2025-05-10 12:00:12,168 - INFO - 第 5 页获取到 100 条记录
2025-05-10 12:00:12,168 - INFO - Request Parameters - Page 6:
2025-05-10 12:00:12,168 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 12:00:12,168 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 12:00:12,647 - INFO - Response - Page 6:
2025-05-10 12:00:12,847 - INFO - 第 6 页获取到 100 条记录
2025-05-10 12:00:12,847 - INFO - Request Parameters - Page 7:
2025-05-10 12:00:12,847 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 12:00:12,847 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 12:00:13,359 - INFO - Response - Page 7:
2025-05-10 12:00:13,559 - INFO - 第 7 页获取到 70 条记录
2025-05-10 12:00:13,559 - INFO - 查询完成，共获取到 670 条记录
2025-05-10 12:00:13,559 - INFO - 获取到 670 条表单数据
2025-05-10 12:00:13,571 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-10 12:00:13,583 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-10 12:00:13,583 - INFO - 开始处理日期: 2025-03
2025-05-10 12:00:13,583 - INFO - Request Parameters - Page 1:
2025-05-10 12:00:13,583 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 12:00:13,584 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 12:00:14,135 - INFO - Response - Page 1:
2025-05-10 12:00:14,335 - INFO - 第 1 页获取到 100 条记录
2025-05-10 12:00:14,335 - INFO - Request Parameters - Page 2:
2025-05-10 12:00:14,335 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 12:00:14,335 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 12:00:14,854 - INFO - Response - Page 2:
2025-05-10 12:00:15,054 - INFO - 第 2 页获取到 100 条记录
2025-05-10 12:00:15,054 - INFO - Request Parameters - Page 3:
2025-05-10 12:00:15,054 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 12:00:15,054 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 12:00:15,569 - INFO - Response - Page 3:
2025-05-10 12:00:15,771 - INFO - 第 3 页获取到 100 条记录
2025-05-10 12:00:15,771 - INFO - Request Parameters - Page 4:
2025-05-10 12:00:15,771 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 12:00:15,771 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 12:00:16,258 - INFO - Response - Page 4:
2025-05-10 12:00:16,458 - INFO - 第 4 页获取到 100 条记录
2025-05-10 12:00:16,458 - INFO - Request Parameters - Page 5:
2025-05-10 12:00:16,458 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 12:00:16,458 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 12:00:16,995 - INFO - Response - Page 5:
2025-05-10 12:00:17,195 - INFO - 第 5 页获取到 100 条记录
2025-05-10 12:00:17,195 - INFO - Request Parameters - Page 6:
2025-05-10 12:00:17,195 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 12:00:17,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 12:00:17,765 - INFO - Response - Page 6:
2025-05-10 12:00:17,965 - INFO - 第 6 页获取到 100 条记录
2025-05-10 12:00:17,965 - INFO - Request Parameters - Page 7:
2025-05-10 12:00:17,965 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 12:00:17,965 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 12:00:18,498 - INFO - Response - Page 7:
2025-05-10 12:00:18,698 - INFO - 第 7 页获取到 61 条记录
2025-05-10 12:00:18,698 - INFO - 查询完成，共获取到 661 条记录
2025-05-10 12:00:18,698 - INFO - 获取到 661 条表单数据
2025-05-10 12:00:18,710 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-10 12:00:18,722 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-10 12:00:18,722 - INFO - 开始处理日期: 2025-04
2025-05-10 12:00:18,722 - INFO - Request Parameters - Page 1:
2025-05-10 12:00:18,723 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 12:00:18,723 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 12:00:19,275 - INFO - Response - Page 1:
2025-05-10 12:00:19,476 - INFO - 第 1 页获取到 100 条记录
2025-05-10 12:00:19,476 - INFO - Request Parameters - Page 2:
2025-05-10 12:00:19,476 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 12:00:19,476 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 12:00:19,897 - INFO - Response - Page 2:
2025-05-10 12:00:20,097 - INFO - 第 2 页获取到 100 条记录
2025-05-10 12:00:20,097 - INFO - Request Parameters - Page 3:
2025-05-10 12:00:20,097 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 12:00:20,097 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 12:00:20,625 - INFO - Response - Page 3:
2025-05-10 12:00:20,826 - INFO - 第 3 页获取到 100 条记录
2025-05-10 12:00:20,826 - INFO - Request Parameters - Page 4:
2025-05-10 12:00:20,826 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 12:00:20,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 12:00:21,292 - INFO - Response - Page 4:
2025-05-10 12:00:21,492 - INFO - 第 4 页获取到 100 条记录
2025-05-10 12:00:21,492 - INFO - Request Parameters - Page 5:
2025-05-10 12:00:21,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 12:00:21,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 12:00:22,008 - INFO - Response - Page 5:
2025-05-10 12:00:22,208 - INFO - 第 5 页获取到 100 条记录
2025-05-10 12:00:22,208 - INFO - Request Parameters - Page 6:
2025-05-10 12:00:22,208 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 12:00:22,208 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 12:00:22,733 - INFO - Response - Page 6:
2025-05-10 12:00:22,934 - INFO - 第 6 页获取到 100 条记录
2025-05-10 12:00:22,934 - INFO - Request Parameters - Page 7:
2025-05-10 12:00:22,934 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 12:00:22,934 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 12:00:23,415 - INFO - Response - Page 7:
2025-05-10 12:00:23,615 - INFO - 第 7 页获取到 54 条记录
2025-05-10 12:00:23,615 - INFO - 查询完成，共获取到 654 条记录
2025-05-10 12:00:23,615 - INFO - 获取到 654 条表单数据
2025-05-10 12:00:23,628 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-10 12:00:23,629 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M04
2025-05-10 12:00:24,117 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M04
2025-05-10 12:00:24,117 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 66175.0, 'new_value': 66513.87}, {'field': 'offline_amount', 'old_value': 91133.0, 'new_value': 94133.0}, {'field': 'total_amount', 'old_value': 157308.0, 'new_value': 160646.87}, {'field': 'order_count', 'old_value': 2202, 'new_value': 2217}]
2025-05-10 12:00:24,118 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M65
2025-05-10 12:00:24,595 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M65
2025-05-10 12:00:24,595 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2171.3, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 115811.13, 'new_value': 117577.2}, {'field': 'total_amount', 'old_value': 117982.43, 'new_value': 117577.2}]
2025-05-10 12:00:24,595 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MF5
2025-05-10 12:00:25,168 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MF5
2025-05-10 12:00:25,168 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50203.0, 'new_value': 50303.0}, {'field': 'total_amount', 'old_value': 54210.0, 'new_value': 54310.0}]
2025-05-10 12:00:25,172 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MX6
2025-05-10 12:00:25,651 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MX6
2025-05-10 12:00:25,651 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 458566.0, 'new_value': 423032.13}, {'field': 'total_amount', 'old_value': 458566.0, 'new_value': 423032.13}]
2025-05-10 12:00:25,654 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MXI
2025-05-10 12:00:26,127 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MXI
2025-05-10 12:00:26,127 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45895.09, 'new_value': 2020.52}, {'field': 'offline_amount', 'old_value': 1189953.2, 'new_value': 1233837.77}, {'field': 'total_amount', 'old_value': 1235848.29, 'new_value': 1235858.29}]
2025-05-10 12:00:26,128 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MB
2025-05-10 12:00:26,607 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MB
2025-05-10 12:00:26,608 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18789.68, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 358118.52, 'new_value': 377149.65}, {'field': 'total_amount', 'old_value': 376908.2, 'new_value': 377149.65}]
2025-05-10 12:00:26,612 - INFO - 日期 2025-04 处理完成 - 更新: 6 条，插入: 0 条，错误: 0 条
2025-05-10 12:00:26,612 - INFO - 开始处理日期: 2025-05
2025-05-10 12:00:26,612 - INFO - Request Parameters - Page 1:
2025-05-10 12:00:26,612 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 12:00:26,613 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 12:00:27,099 - INFO - Response - Page 1:
2025-05-10 12:00:27,299 - INFO - 第 1 页获取到 100 条记录
2025-05-10 12:00:27,299 - INFO - Request Parameters - Page 2:
2025-05-10 12:00:27,299 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 12:00:27,299 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 12:00:27,776 - INFO - Response - Page 2:
2025-05-10 12:00:27,977 - INFO - 第 2 页获取到 100 条记录
2025-05-10 12:00:27,977 - INFO - Request Parameters - Page 3:
2025-05-10 12:00:27,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 12:00:27,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 12:00:28,514 - INFO - Response - Page 3:
2025-05-10 12:00:28,714 - INFO - 第 3 页获取到 100 条记录
2025-05-10 12:00:28,714 - INFO - Request Parameters - Page 4:
2025-05-10 12:00:28,714 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 12:00:28,714 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 12:00:29,190 - INFO - Response - Page 4:
2025-05-10 12:00:29,391 - INFO - 第 4 页获取到 100 条记录
2025-05-10 12:00:29,391 - INFO - Request Parameters - Page 5:
2025-05-10 12:00:29,391 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 12:00:29,391 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 12:00:29,885 - INFO - Response - Page 5:
2025-05-10 12:00:30,085 - INFO - 第 5 页获取到 100 条记录
2025-05-10 12:00:30,085 - INFO - Request Parameters - Page 6:
2025-05-10 12:00:30,085 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 12:00:30,085 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 12:00:30,602 - INFO - Response - Page 6:
2025-05-10 12:00:30,802 - INFO - 第 6 页获取到 100 条记录
2025-05-10 12:00:30,802 - INFO - Request Parameters - Page 7:
2025-05-10 12:00:30,802 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 12:00:30,802 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 12:00:31,177 - INFO - Response - Page 7:
2025-05-10 12:00:31,377 - INFO - 第 7 页获取到 21 条记录
2025-05-10 12:00:31,377 - INFO - 查询完成，共获取到 621 条记录
2025-05-10 12:00:31,377 - INFO - 获取到 621 条表单数据
2025-05-10 12:00:31,389 - INFO - 当前日期 2025-05 有 621 条MySQL数据需要处理
2025-05-10 12:00:31,390 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2O
2025-05-10 12:00:31,894 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2O
2025-05-10 12:00:31,894 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73600.0, 'new_value': 86600.0}, {'field': 'total_amount', 'old_value': 73600.0, 'new_value': 86600.0}]
2025-05-10 12:00:31,895 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-10 12:00:32,366 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-10 12:00:32,367 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 665.0, 'new_value': 705.0}, {'field': 'offline_amount', 'old_value': 19225.0, 'new_value': 20305.0}, {'field': 'total_amount', 'old_value': 19890.0, 'new_value': 21010.0}, {'field': 'order_count', 'old_value': 229, 'new_value': 245}]
2025-05-10 12:00:32,367 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-10 12:00:32,905 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-10 12:00:32,905 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 127249.0, 'new_value': 138229.0}, {'field': 'total_amount', 'old_value': 127249.0, 'new_value': 138229.0}, {'field': 'order_count', 'old_value': 90, 'new_value': 100}]
2025-05-10 12:00:32,905 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-10 12:00:33,352 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-10 12:00:33,352 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10070.0, 'new_value': 12720.0}, {'field': 'total_amount', 'old_value': 10070.0, 'new_value': 12720.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 48}]
2025-05-10 12:00:33,352 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-10 12:00:33,810 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-10 12:00:33,811 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48679.01, 'new_value': 56607.73}, {'field': 'total_amount', 'old_value': 48679.01, 'new_value': 56607.73}, {'field': 'order_count', 'old_value': 2274, 'new_value': 2559}]
2025-05-10 12:00:33,811 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-10 12:00:34,258 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-10 12:00:34,258 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 242012.08, 'new_value': 275663.08}, {'field': 'total_amount', 'old_value': 242012.08, 'new_value': 275663.08}, {'field': 'order_count', 'old_value': 739, 'new_value': 832}]
2025-05-10 12:00:34,258 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-10 12:00:34,697 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-10 12:00:34,697 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7163.0, 'new_value': 7759.0}, {'field': 'total_amount', 'old_value': 10060.0, 'new_value': 10656.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 11}]
2025-05-10 12:00:34,697 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-10 12:00:35,188 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-10 12:00:35,188 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8578.2, 'new_value': 9421.3}, {'field': 'offline_amount', 'old_value': 5173.15, 'new_value': 5524.49}, {'field': 'total_amount', 'old_value': 13751.35, 'new_value': 14945.79}, {'field': 'order_count', 'old_value': 653, 'new_value': 732}]
2025-05-10 12:00:35,188 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-10 12:00:35,699 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-10 12:00:35,700 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80840.0, 'new_value': 97040.0}, {'field': 'total_amount', 'old_value': 80840.0, 'new_value': 97040.0}, {'field': 'order_count', 'old_value': 41, 'new_value': 50}]
2025-05-10 12:00:35,700 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-10 12:00:36,204 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-10 12:00:36,204 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38471.0, 'new_value': 45992.0}, {'field': 'total_amount', 'old_value': 38471.0, 'new_value': 45992.0}, {'field': 'order_count', 'old_value': 41, 'new_value': 46}]
2025-05-10 12:00:36,204 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMKC
2025-05-10 12:00:36,664 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMKC
2025-05-10 12:00:36,665 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3178.0, 'new_value': 10078.0}, {'field': 'total_amount', 'old_value': 3178.0, 'new_value': 10078.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-10 12:00:36,665 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-10 12:00:37,149 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-10 12:00:37,149 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64589.0, 'new_value': 65569.0}, {'field': 'total_amount', 'old_value': 64589.0, 'new_value': 65569.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-05-10 12:00:37,149 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-10 12:00:37,778 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-10 12:00:37,779 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19814.42, 'new_value': 21374.48}, {'field': 'offline_amount', 'old_value': 37882.2, 'new_value': 42748.2}, {'field': 'total_amount', 'old_value': 57696.62, 'new_value': 64122.68}, {'field': 'order_count', 'old_value': 746, 'new_value': 826}]
2025-05-10 12:00:37,779 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-10 12:00:38,318 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-10 12:00:38,318 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8886.0, 'new_value': 9734.37}, {'field': 'offline_amount', 'old_value': 10119.0, 'new_value': 10647.09}, {'field': 'total_amount', 'old_value': 19005.0, 'new_value': 20381.46}, {'field': 'order_count', 'old_value': 914, 'new_value': 977}]
2025-05-10 12:00:38,318 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-10 12:00:38,818 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-10 12:00:38,818 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 137202.5, 'new_value': 140119.3}, {'field': 'total_amount', 'old_value': 252222.2, 'new_value': 255139.0}, {'field': 'order_count', 'old_value': 1426, 'new_value': 1515}]
2025-05-10 12:00:38,818 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-10 12:00:39,250 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-10 12:00:39,250 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33231.0, 'new_value': 37164.0}, {'field': 'total_amount', 'old_value': 33231.0, 'new_value': 37164.0}, {'field': 'order_count', 'old_value': 1790, 'new_value': 2028}]
2025-05-10 12:00:39,251 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-10 12:00:39,669 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-10 12:00:39,669 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21465.0, 'new_value': 26030.0}, {'field': 'total_amount', 'old_value': 21465.0, 'new_value': 26030.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 49}]
2025-05-10 12:00:39,670 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-10 12:00:40,158 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-10 12:00:40,158 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 126355.0, 'new_value': 139190.0}, {'field': 'total_amount', 'old_value': 126355.0, 'new_value': 139190.0}, {'field': 'order_count', 'old_value': 145, 'new_value': 160}]
2025-05-10 12:00:40,159 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM4A
2025-05-10 12:00:40,581 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM4A
2025-05-10 12:00:40,581 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1655.16, 'new_value': 1822.73}, {'field': 'offline_amount', 'old_value': 30264.33, 'new_value': 33181.01}, {'field': 'total_amount', 'old_value': 31919.49, 'new_value': 35003.74}, {'field': 'order_count', 'old_value': 1170, 'new_value': 1307}]
2025-05-10 12:00:40,582 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-10 12:00:41,075 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-10 12:00:41,075 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41254.74, 'new_value': 47987.08}, {'field': 'total_amount', 'old_value': 41254.74, 'new_value': 47987.08}, {'field': 'order_count', 'old_value': 462, 'new_value': 541}]
2025-05-10 12:00:41,075 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-10 12:00:41,536 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-10 12:00:41,536 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 343269.0, 'new_value': 382931.0}, {'field': 'offline_amount', 'old_value': 121833.0, 'new_value': 132762.0}, {'field': 'total_amount', 'old_value': 465102.0, 'new_value': 515693.0}, {'field': 'order_count', 'old_value': 519, 'new_value': 588}]
2025-05-10 12:00:41,536 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM1D
2025-05-10 12:00:42,026 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM1D
2025-05-10 12:00:42,027 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3898.0, 'new_value': 6197.0}, {'field': 'total_amount', 'old_value': 3898.0, 'new_value': 6197.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-10 12:00:42,027 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-10 12:00:42,504 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-10 12:00:42,504 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91615.0, 'new_value': 126150.0}, {'field': 'total_amount', 'old_value': 91615.0, 'new_value': 126150.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 19}]
2025-05-10 12:00:42,504 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-10 12:00:43,003 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-10 12:00:43,003 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 108595.89, 'new_value': 143245.89}, {'field': 'total_amount', 'old_value': 108595.89, 'new_value': 143245.89}, {'field': 'order_count', 'old_value': 21, 'new_value': 25}]
2025-05-10 12:00:43,003 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-10 12:00:43,451 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-10 12:00:43,452 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104090.13, 'new_value': 109890.13}, {'field': 'total_amount', 'old_value': 143450.13, 'new_value': 149250.13}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-05-10 12:00:43,452 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-10 12:00:43,928 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-10 12:00:43,928 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 170941.2, 'new_value': 248831.7}, {'field': 'total_amount', 'old_value': 172198.6, 'new_value': 250089.1}, {'field': 'order_count', 'old_value': 23, 'new_value': 33}]
2025-05-10 12:00:43,929 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-10 12:00:44,512 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-10 12:00:44,513 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2489.0, 'new_value': 2680.0}, {'field': 'total_amount', 'old_value': 8253.0, 'new_value': 8444.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 27}]
2025-05-10 12:00:44,513 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-10 12:00:45,000 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-10 12:00:45,000 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64354.0, 'new_value': 71629.0}, {'field': 'total_amount', 'old_value': 64354.0, 'new_value': 71629.0}, {'field': 'order_count', 'old_value': 124, 'new_value': 133}]
2025-05-10 12:00:45,000 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-10 12:00:45,466 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-10 12:00:45,466 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45569.99, 'new_value': 75653.49}, {'field': 'total_amount', 'old_value': 45569.99, 'new_value': 75653.49}, {'field': 'order_count', 'old_value': 65, 'new_value': 81}]
2025-05-10 12:00:45,466 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-10 12:00:45,988 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-10 12:00:45,989 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31080.41, 'new_value': 37072.29}, {'field': 'offline_amount', 'old_value': 17288.14, 'new_value': 18099.72}, {'field': 'total_amount', 'old_value': 48368.55, 'new_value': 55172.01}, {'field': 'order_count', 'old_value': 2805, 'new_value': 3193}]
2025-05-10 12:00:45,989 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-10 12:00:46,516 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-10 12:00:46,516 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9351.34, 'new_value': 11573.53}, {'field': 'total_amount', 'old_value': 9351.34, 'new_value': 11573.53}, {'field': 'order_count', 'old_value': 753, 'new_value': 925}]
2025-05-10 12:00:46,517 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-10 12:00:47,050 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-10 12:00:47,050 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 71588.92, 'new_value': 74913.47}, {'field': 'offline_amount', 'old_value': 12842.64, 'new_value': 13226.84}, {'field': 'total_amount', 'old_value': 84431.56, 'new_value': 88140.31}, {'field': 'order_count', 'old_value': 308, 'new_value': 322}]
2025-05-10 12:00:47,050 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-10 12:00:47,508 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-10 12:00:47,509 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 75176.0, 'new_value': 81607.0}, {'field': 'offline_amount', 'old_value': 24554.45, 'new_value': 27744.25}, {'field': 'total_amount', 'old_value': 99730.45, 'new_value': 109351.25}, {'field': 'order_count', 'old_value': 587, 'new_value': 639}]
2025-05-10 12:00:47,509 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-10 12:00:47,904 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-10 12:00:47,905 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14835.0, 'new_value': 17231.0}, {'field': 'total_amount', 'old_value': 17179.0, 'new_value': 19575.0}, {'field': 'order_count', 'old_value': 66, 'new_value': 75}]
2025-05-10 12:00:47,905 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM7A
2025-05-10 12:00:48,350 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM7A
2025-05-10 12:00:48,350 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15211.11, 'new_value': 17480.09}, {'field': 'offline_amount', 'old_value': 18151.17, 'new_value': 19749.74}, {'field': 'total_amount', 'old_value': 33362.28, 'new_value': 37229.83}, {'field': 'order_count', 'old_value': 1663, 'new_value': 1879}]
2025-05-10 12:00:48,350 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-10 12:00:48,823 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-10 12:00:48,823 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2629.0, 'new_value': 2924.0}, {'field': 'total_amount', 'old_value': 3629.0, 'new_value': 3924.0}, {'field': 'order_count', 'old_value': 64, 'new_value': 69}]
2025-05-10 12:00:48,824 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-10 12:00:49,251 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-10 12:00:49,251 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28606.77, 'new_value': 33003.13}, {'field': 'total_amount', 'old_value': 28606.77, 'new_value': 33003.13}, {'field': 'order_count', 'old_value': 730, 'new_value': 847}]
2025-05-10 12:00:49,251 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-10 12:00:49,696 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-10 12:00:49,696 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31206.88, 'new_value': 32213.88}, {'field': 'total_amount', 'old_value': 31206.88, 'new_value': 32213.88}, {'field': 'order_count', 'old_value': 52, 'new_value': 54}]
2025-05-10 12:00:49,696 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMMD
2025-05-10 12:00:50,112 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMMD
2025-05-10 12:00:50,112 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1257.0, 'new_value': 1757.0}, {'field': 'total_amount', 'old_value': 1257.0, 'new_value': 1757.0}, {'field': 'order_count', 'old_value': 292, 'new_value': 293}]
2025-05-10 12:00:50,113 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-10 12:00:50,708 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-10 12:00:50,708 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46338.0, 'new_value': 58413.0}, {'field': 'total_amount', 'old_value': 46338.0, 'new_value': 58413.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 21}]
2025-05-10 12:00:50,709 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-10 12:00:51,148 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-10 12:00:51,148 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50989.0, 'new_value': 58100.0}, {'field': 'offline_amount', 'old_value': 23153.26, 'new_value': 26304.26}, {'field': 'total_amount', 'old_value': 74142.26, 'new_value': 84404.26}, {'field': 'order_count', 'old_value': 523, 'new_value': 588}]
2025-05-10 12:00:51,148 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM8A
2025-05-10 12:00:51,572 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM8A
2025-05-10 12:00:51,572 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3499.35, 'new_value': 3865.3}, {'field': 'offline_amount', 'old_value': 44262.15, 'new_value': 49017.59}, {'field': 'total_amount', 'old_value': 47761.5, 'new_value': 52882.89}, {'field': 'order_count', 'old_value': 725, 'new_value': 810}]
2025-05-10 12:00:51,572 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-10 12:00:52,060 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-10 12:00:52,060 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4311.83, 'new_value': 4547.86}, {'field': 'offline_amount', 'old_value': 60709.39, 'new_value': 68212.47}, {'field': 'total_amount', 'old_value': 65021.22, 'new_value': 72760.33}, {'field': 'order_count', 'old_value': 703, 'new_value': 798}]
2025-05-10 12:00:52,061 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-10 12:00:52,541 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-10 12:00:52,541 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17512.2, 'new_value': 18659.5}, {'field': 'total_amount', 'old_value': 17513.2, 'new_value': 18660.5}, {'field': 'order_count', 'old_value': 96, 'new_value': 103}]
2025-05-10 12:00:52,541 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-10 12:00:53,020 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-10 12:00:53,020 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24048.0, 'new_value': 25938.0}, {'field': 'total_amount', 'old_value': 50958.0, 'new_value': 52848.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-05-10 12:00:53,021 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-10 12:00:53,456 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-10 12:00:53,456 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67972.0, 'new_value': 89040.0}, {'field': 'total_amount', 'old_value': 67972.0, 'new_value': 89040.0}, {'field': 'order_count', 'old_value': 361, 'new_value': 425}]
2025-05-10 12:00:53,457 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-10 12:00:53,913 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-10 12:00:53,914 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67500.84, 'new_value': 75999.63}, {'field': 'total_amount', 'old_value': 67500.84, 'new_value': 75999.63}, {'field': 'order_count', 'old_value': 210, 'new_value': 234}]
2025-05-10 12:00:53,914 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-10 12:00:54,356 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-10 12:00:54,356 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25083.17, 'new_value': 33091.47}, {'field': 'offline_amount', 'old_value': 11980.66, 'new_value': 15645.46}, {'field': 'total_amount', 'old_value': 37063.83, 'new_value': 48736.93}, {'field': 'order_count', 'old_value': 1197, 'new_value': 1605}]
2025-05-10 12:00:54,356 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-10 12:00:54,769 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-10 12:00:54,769 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45406.0, 'new_value': 51222.0}, {'field': 'total_amount', 'old_value': 45406.0, 'new_value': 51222.0}, {'field': 'order_count', 'old_value': 1067, 'new_value': 1223}]
2025-05-10 12:00:54,770 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-10 12:00:55,384 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-10 12:00:55,384 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27228.8, 'new_value': 31100.23}, {'field': 'offline_amount', 'old_value': 277951.37, 'new_value': 312032.57}, {'field': 'total_amount', 'old_value': 305180.17, 'new_value': 343132.8}, {'field': 'order_count', 'old_value': 947, 'new_value': 1069}]
2025-05-10 12:00:55,384 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-10 12:00:55,856 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-10 12:00:55,857 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7046.0, 'new_value': 7096.0}, {'field': 'total_amount', 'old_value': 7046.0, 'new_value': 7096.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-05-10 12:00:55,857 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-10 12:00:56,317 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-10 12:00:56,318 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9865.17, 'new_value': 10527.37}, {'field': 'total_amount', 'old_value': 9865.17, 'new_value': 10527.37}, {'field': 'order_count', 'old_value': 38, 'new_value': 44}]
2025-05-10 12:00:56,318 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-10 12:00:56,737 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-10 12:00:56,737 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49527.0, 'new_value': 56287.0}, {'field': 'total_amount', 'old_value': 49527.0, 'new_value': 56287.0}, {'field': 'order_count', 'old_value': 1739, 'new_value': 2026}]
2025-05-10 12:00:56,737 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-10 12:00:57,168 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-10 12:00:57,168 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 238819.47, 'new_value': 263855.79}, {'field': 'total_amount', 'old_value': 238819.47, 'new_value': 263855.79}, {'field': 'order_count', 'old_value': 1642, 'new_value': 1895}]
2025-05-10 12:00:57,168 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E
2025-05-10 12:00:57,624 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E
2025-05-10 12:00:57,624 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15607.0, 'new_value': 17106.0}, {'field': 'total_amount', 'old_value': 15607.0, 'new_value': 17106.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 19}]
2025-05-10 12:00:57,625 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-10 12:00:58,074 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-10 12:00:58,074 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 144985.0, 'new_value': 203647.0}, {'field': 'total_amount', 'old_value': 158228.0, 'new_value': 216890.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 36}]
2025-05-10 12:00:58,074 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1U
2025-05-10 12:00:58,507 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1U
2025-05-10 12:00:58,507 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20387.0, 'new_value': 22486.0}, {'field': 'total_amount', 'old_value': 20387.0, 'new_value': 22486.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-10 12:00:58,507 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-10 12:00:58,913 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-10 12:00:58,914 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38601.26, 'new_value': 44976.31}, {'field': 'total_amount', 'old_value': 38601.26, 'new_value': 44976.31}, {'field': 'order_count', 'old_value': 1449, 'new_value': 1684}]
2025-05-10 12:00:58,914 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-10 12:00:59,367 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-10 12:00:59,367 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35882.0, 'new_value': 39881.0}, {'field': 'offline_amount', 'old_value': 27253.0, 'new_value': 28270.0}, {'field': 'total_amount', 'old_value': 63135.0, 'new_value': 68151.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 26}]
2025-05-10 12:00:59,367 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-05-10 12:00:59,871 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-05-10 12:00:59,871 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7483.02, 'new_value': 9375.54}, {'field': 'total_amount', 'old_value': 7483.02, 'new_value': 9375.54}, {'field': 'order_count', 'old_value': 13, 'new_value': 15}]
2025-05-10 12:00:59,871 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-10 12:01:00,300 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-10 12:01:00,300 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55563.9, 'new_value': 59371.78}, {'field': 'total_amount', 'old_value': 56787.12, 'new_value': 60595.0}, {'field': 'order_count', 'old_value': 238, 'new_value': 258}]
2025-05-10 12:01:00,300 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-10 12:01:00,774 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-10 12:01:00,774 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44420.0, 'new_value': 44976.0}, {'field': 'total_amount', 'old_value': 44420.0, 'new_value': 44976.0}, {'field': 'order_count', 'old_value': 68, 'new_value': 70}]
2025-05-10 12:01:00,774 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-10 12:01:01,219 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-10 12:01:01,219 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 145300.0, 'new_value': 286073.0}, {'field': 'total_amount', 'old_value': 145300.0, 'new_value': 286073.0}, {'field': 'order_count', 'old_value': 234, 'new_value': 264}]
2025-05-10 12:01:01,219 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-10 12:01:01,635 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-10 12:01:01,635 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58058.31, 'new_value': 64379.03}, {'field': 'total_amount', 'old_value': 58058.31, 'new_value': 64379.03}, {'field': 'order_count', 'old_value': 304, 'new_value': 334}]
2025-05-10 12:01:01,636 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-05-10 12:01:02,021 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-05-10 12:01:02,022 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48109.0, 'new_value': 58296.0}, {'field': 'total_amount', 'old_value': 48109.0, 'new_value': 58296.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 10}]
2025-05-10 12:01:02,022 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM9A
2025-05-10 12:01:02,434 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM9A
2025-05-10 12:01:02,434 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24304.84, 'new_value': 27008.44}, {'field': 'total_amount', 'old_value': 24304.84, 'new_value': 27008.44}, {'field': 'order_count', 'old_value': 1083, 'new_value': 1222}]
2025-05-10 12:01:02,435 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-10 12:01:02,922 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-10 12:01:02,923 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 676475.9, 'new_value': 725535.9}, {'field': 'total_amount', 'old_value': 676475.9, 'new_value': 725535.9}, {'field': 'order_count', 'old_value': 1164, 'new_value': 1254}]
2025-05-10 12:01:02,923 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-10 12:01:03,446 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-10 12:01:03,446 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42748.25, 'new_value': 48611.15}, {'field': 'offline_amount', 'old_value': 24173.0, 'new_value': 28380.0}, {'field': 'total_amount', 'old_value': 66921.25, 'new_value': 76991.15}, {'field': 'order_count', 'old_value': 375, 'new_value': 428}]
2025-05-10 12:01:03,447 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-10 12:01:03,932 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-10 12:01:03,932 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22102.0, 'new_value': 27063.0}, {'field': 'total_amount', 'old_value': 37249.0, 'new_value': 42210.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 22}]
2025-05-10 12:01:03,933 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-10 12:01:04,443 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-10 12:01:04,443 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10150.0, 'new_value': 10788.0}, {'field': 'total_amount', 'old_value': 10150.0, 'new_value': 10788.0}, {'field': 'order_count', 'old_value': 175, 'new_value': 186}]
2025-05-10 12:01:04,443 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-10 12:01:04,862 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-10 12:01:04,863 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1761.06, 'new_value': 2165.66}, {'field': 'offline_amount', 'old_value': 4863.02, 'new_value': 5523.74}, {'field': 'total_amount', 'old_value': 6624.08, 'new_value': 7689.4}, {'field': 'order_count', 'old_value': 225, 'new_value': 270}]
2025-05-10 12:01:04,863 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-10 12:01:05,324 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-10 12:01:05,325 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55942.45, 'new_value': 62382.51}, {'field': 'offline_amount', 'old_value': 42969.97, 'new_value': 48026.0}, {'field': 'total_amount', 'old_value': 98912.42, 'new_value': 110408.51}, {'field': 'order_count', 'old_value': 829, 'new_value': 945}]
2025-05-10 12:01:05,325 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-10 12:01:05,752 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-10 12:01:05,752 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24050.65, 'new_value': 27610.66}, {'field': 'total_amount', 'old_value': 24050.65, 'new_value': 27610.66}, {'field': 'order_count', 'old_value': 15, 'new_value': 17}]
2025-05-10 12:01:05,753 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-10 12:01:06,229 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-10 12:01:06,229 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 130048.9, 'new_value': 141528.9}, {'field': 'offline_amount', 'old_value': 24405.0, 'new_value': 25597.0}, {'field': 'total_amount', 'old_value': 154453.9, 'new_value': 167125.9}, {'field': 'order_count', 'old_value': 193, 'new_value': 211}]
2025-05-10 12:01:06,230 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-10 12:01:06,723 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-10 12:01:06,723 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9595.0, 'new_value': 14328.0}, {'field': 'total_amount', 'old_value': 9595.0, 'new_value': 14328.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 33}]
2025-05-10 12:01:06,723 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-10 12:01:07,172 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-10 12:01:07,172 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23368.0, 'new_value': 24302.0}, {'field': 'total_amount', 'old_value': 23368.0, 'new_value': 24302.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 18}]
2025-05-10 12:01:07,172 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-10 12:01:07,587 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-10 12:01:07,587 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13418.44, 'new_value': 17579.81}, {'field': 'total_amount', 'old_value': 50438.67, 'new_value': 54600.04}, {'field': 'order_count', 'old_value': 1838, 'new_value': 1946}]
2025-05-10 12:01:07,587 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-10 12:01:08,052 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-10 12:01:08,053 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57819.0, 'new_value': 74557.0}, {'field': 'total_amount', 'old_value': 70409.0, 'new_value': 87147.0}, {'field': 'order_count', 'old_value': 1317, 'new_value': 1723}]
2025-05-10 12:01:08,053 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-10 12:01:08,514 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-10 12:01:08,514 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4467.79, 'new_value': 5092.66}, {'field': 'offline_amount', 'old_value': 93909.6, 'new_value': 104076.8}, {'field': 'total_amount', 'old_value': 98377.39, 'new_value': 109169.46}, {'field': 'order_count', 'old_value': 710, 'new_value': 791}]
2025-05-10 12:01:08,514 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-10 12:01:08,963 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-10 12:01:08,963 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 13000.0}, {'field': 'total_amount', 'old_value': 62847.0, 'new_value': 75847.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 16}]
2025-05-10 12:01:08,963 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-10 12:01:09,559 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-10 12:01:09,559 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49588.48, 'new_value': 54726.19}, {'field': 'offline_amount', 'old_value': 46268.45, 'new_value': 49959.45}, {'field': 'total_amount', 'old_value': 95856.93, 'new_value': 104685.64}, {'field': 'order_count', 'old_value': 905, 'new_value': 1018}]
2025-05-10 12:01:09,560 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-10 12:01:10,013 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-10 12:01:10,013 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26095.7, 'new_value': 28006.1}, {'field': 'total_amount', 'old_value': 26984.05, 'new_value': 28894.45}, {'field': 'order_count', 'old_value': 67, 'new_value': 73}]
2025-05-10 12:01:10,013 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-10 12:01:10,491 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-10 12:01:10,491 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24615.41, 'new_value': 27468.07}, {'field': 'total_amount', 'old_value': 24615.41, 'new_value': 27468.07}, {'field': 'order_count', 'old_value': 693, 'new_value': 761}]
2025-05-10 12:01:10,491 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-10 12:01:11,020 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-10 12:01:11,020 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 115962.0, 'new_value': 121962.0}, {'field': 'total_amount', 'old_value': 115962.0, 'new_value': 121962.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-05-10 12:01:11,020 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-10 12:01:11,501 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-10 12:01:11,501 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35739.76, 'new_value': 41094.55}, {'field': 'offline_amount', 'old_value': 146341.13, 'new_value': 165515.7}, {'field': 'total_amount', 'old_value': 182080.89, 'new_value': 206610.25}, {'field': 'order_count', 'old_value': 947, 'new_value': 1021}]
2025-05-10 12:01:11,501 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-10 12:01:11,947 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-10 12:01:11,947 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98136.38, 'new_value': 115035.13}, {'field': 'total_amount', 'old_value': 98136.38, 'new_value': 115035.13}, {'field': 'order_count', 'old_value': 211, 'new_value': 235}]
2025-05-10 12:01:11,947 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-10 12:01:12,405 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-10 12:01:12,405 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5997.2, 'new_value': 6438.27}, {'field': 'offline_amount', 'old_value': 163678.96, 'new_value': 173026.26}, {'field': 'total_amount', 'old_value': 169676.16, 'new_value': 179464.53}, {'field': 'order_count', 'old_value': 647, 'new_value': 703}]
2025-05-10 12:01:12,405 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-10 12:01:12,838 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-10 12:01:12,839 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33307.0, 'new_value': 39092.0}, {'field': 'offline_amount', 'old_value': 20477.25, 'new_value': 22463.25}, {'field': 'total_amount', 'old_value': 53784.25, 'new_value': 61555.25}, {'field': 'order_count', 'old_value': 66, 'new_value': 73}]
2025-05-10 12:01:12,839 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-10 12:01:13,340 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-10 12:01:13,341 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2608.0, 'new_value': 4612.0}, {'field': 'total_amount', 'old_value': 2608.0, 'new_value': 4612.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-05-10 12:01:13,341 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPK
2025-05-10 12:01:13,761 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPK
2025-05-10 12:01:13,762 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24575.0, 'new_value': 25679.0}, {'field': 'total_amount', 'old_value': 24575.0, 'new_value': 25679.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-10 12:01:13,762 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-10 12:01:14,232 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-10 12:01:14,232 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19403.44, 'new_value': 25733.44}, {'field': 'total_amount', 'old_value': 19403.44, 'new_value': 25733.44}, {'field': 'order_count', 'old_value': 25, 'new_value': 29}]
2025-05-10 12:01:14,232 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-10 12:01:14,718 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-10 12:01:14,718 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47128.98, 'new_value': 52127.78}, {'field': 'total_amount', 'old_value': 47128.98, 'new_value': 52127.78}, {'field': 'order_count', 'old_value': 286, 'new_value': 318}]
2025-05-10 12:01:14,718 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-10 12:01:15,215 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-10 12:01:15,216 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23493.0, 'new_value': 31671.0}, {'field': 'total_amount', 'old_value': 23493.0, 'new_value': 31671.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 17}]
2025-05-10 12:01:15,216 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-10 12:01:15,682 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-10 12:01:15,682 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2068.0, 'new_value': 2438.0}, {'field': 'total_amount', 'old_value': 2068.0, 'new_value': 2438.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 12}]
2025-05-10 12:01:15,683 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-10 12:01:16,139 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-10 12:01:16,139 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2740.29, 'new_value': 3184.82}, {'field': 'offline_amount', 'old_value': 11605.0, 'new_value': 13151.0}, {'field': 'total_amount', 'old_value': 14345.29, 'new_value': 16335.82}, {'field': 'order_count', 'old_value': 69, 'new_value': 79}]
2025-05-10 12:01:16,139 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-10 12:01:16,651 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-10 12:01:16,651 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 196.8}, {'field': 'offline_amount', 'old_value': 19763.62, 'new_value': 22127.1}, {'field': 'total_amount', 'old_value': 19763.62, 'new_value': 22323.9}, {'field': 'order_count', 'old_value': 159, 'new_value': 181}]
2025-05-10 12:01:16,651 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-10 12:01:17,120 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-10 12:01:17,120 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1806.0, 'new_value': 2072.0}, {'field': 'offline_amount', 'old_value': 8822.7, 'new_value': 9849.8}, {'field': 'total_amount', 'old_value': 10628.7, 'new_value': 11921.8}, {'field': 'order_count', 'old_value': 416, 'new_value': 469}]
2025-05-10 12:01:17,120 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-10 12:01:17,715 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-10 12:01:17,715 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43448.32, 'new_value': 47440.42}, {'field': 'total_amount', 'old_value': 43448.32, 'new_value': 47440.42}, {'field': 'order_count', 'old_value': 126, 'new_value': 147}]
2025-05-10 12:01:17,716 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-10 12:01:18,202 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-10 12:01:18,203 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71108.7, 'new_value': 77516.6}, {'field': 'total_amount', 'old_value': 71108.7, 'new_value': 77516.6}, {'field': 'order_count', 'old_value': 256, 'new_value': 281}]
2025-05-10 12:01:18,203 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-10 12:01:18,736 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-10 12:01:18,736 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13812.7, 'new_value': 14872.0}, {'field': 'offline_amount', 'old_value': 98326.3, 'new_value': 104860.0}, {'field': 'total_amount', 'old_value': 112139.0, 'new_value': 119732.0}, {'field': 'order_count', 'old_value': 839, 'new_value': 912}]
2025-05-10 12:01:18,736 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-10 12:01:19,264 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-10 12:01:19,264 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89692.6, 'new_value': 102259.47}, {'field': 'total_amount', 'old_value': 89692.6, 'new_value': 102259.47}, {'field': 'order_count', 'old_value': 2366, 'new_value': 2703}]
2025-05-10 12:01:19,264 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-10 12:01:19,790 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-10 12:01:19,790 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51016.32, 'new_value': 58853.73}, {'field': 'total_amount', 'old_value': 51016.32, 'new_value': 58853.73}, {'field': 'order_count', 'old_value': 239, 'new_value': 273}]
2025-05-10 12:01:19,790 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-10 12:01:20,248 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-10 12:01:20,248 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7602.19, 'new_value': 8562.33}, {'field': 'offline_amount', 'old_value': 13714.46, 'new_value': 15483.46}, {'field': 'total_amount', 'old_value': 21316.65, 'new_value': 24045.79}, {'field': 'order_count', 'old_value': 774, 'new_value': 871}]
2025-05-10 12:01:20,249 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-10 12:01:20,720 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-10 12:01:20,720 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18986.0, 'new_value': 22054.0}, {'field': 'total_amount', 'old_value': 21394.0, 'new_value': 24462.0}, {'field': 'order_count', 'old_value': 84, 'new_value': 97}]
2025-05-10 12:01:20,721 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-10 12:01:21,162 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-10 12:01:21,162 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10371.7, 'new_value': 10463.7}, {'field': 'offline_amount', 'old_value': 21982.56, 'new_value': 22929.56}, {'field': 'total_amount', 'old_value': 32354.26, 'new_value': 33393.26}, {'field': 'order_count', 'old_value': 345, 'new_value': 359}]
2025-05-10 12:01:21,163 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-10 12:01:21,620 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-10 12:01:21,620 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 116294.6, 'new_value': 121909.2}, {'field': 'total_amount', 'old_value': 116294.6, 'new_value': 121909.2}, {'field': 'order_count', 'old_value': 202, 'new_value': 211}]
2025-05-10 12:01:21,621 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-10 12:01:22,100 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-10 12:01:22,100 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36785.9, 'new_value': 40574.0}, {'field': 'offline_amount', 'old_value': 35936.54, 'new_value': 40592.79}, {'field': 'total_amount', 'old_value': 72722.44, 'new_value': 81166.79}, {'field': 'order_count', 'old_value': 499, 'new_value': 556}]
2025-05-10 12:01:22,100 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-10 12:01:22,510 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-10 12:01:22,510 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30556.23, 'new_value': 34496.55}, {'field': 'offline_amount', 'old_value': 22546.59, 'new_value': 25565.54}, {'field': 'total_amount', 'old_value': 53102.82, 'new_value': 60062.09}, {'field': 'order_count', 'old_value': 2135, 'new_value': 2425}]
2025-05-10 12:01:22,510 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-10 12:01:22,893 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-10 12:01:22,893 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5150.89, 'new_value': 6216.34}, {'field': 'offline_amount', 'old_value': 11683.94, 'new_value': 13809.11}, {'field': 'total_amount', 'old_value': 16834.83, 'new_value': 20025.45}, {'field': 'order_count', 'old_value': 890, 'new_value': 1055}]
2025-05-10 12:01:22,893 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-10 12:01:23,345 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-10 12:01:23,345 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30740.0, 'new_value': 42810.0}, {'field': 'total_amount', 'old_value': 30740.0, 'new_value': 42810.0}, {'field': 'order_count', 'old_value': 1417, 'new_value': 2178}]
2025-05-10 12:01:23,345 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-10 12:01:23,806 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-10 12:01:23,806 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2390.9, 'new_value': 2675.28}, {'field': 'offline_amount', 'old_value': 5770.24, 'new_value': 5905.64}, {'field': 'total_amount', 'old_value': 8161.14, 'new_value': 8580.92}, {'field': 'order_count', 'old_value': 567, 'new_value': 619}]
2025-05-10 12:01:23,807 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-10 12:01:24,240 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-10 12:01:24,240 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21005.0, 'new_value': 22375.0}, {'field': 'total_amount', 'old_value': 21005.0, 'new_value': 22375.0}, {'field': 'order_count', 'old_value': 35, 'new_value': 39}]
2025-05-10 12:01:24,240 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-10 12:01:24,672 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-10 12:01:24,672 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24067.0, 'new_value': 25142.0}, {'field': 'total_amount', 'old_value': 24067.0, 'new_value': 25142.0}, {'field': 'order_count', 'old_value': 162, 'new_value': 168}]
2025-05-10 12:01:24,672 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-10 12:01:25,128 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-10 12:01:25,129 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8182.0, 'new_value': 11277.0}, {'field': 'offline_amount', 'old_value': 176419.0, 'new_value': 186264.0}, {'field': 'total_amount', 'old_value': 184601.0, 'new_value': 197541.0}, {'field': 'order_count', 'old_value': 1442, 'new_value': 1554}]
2025-05-10 12:01:25,129 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-10 12:01:25,589 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-10 12:01:25,589 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43910.67, 'new_value': 47374.07}, {'field': 'total_amount', 'old_value': 43910.67, 'new_value': 47374.07}, {'field': 'order_count', 'old_value': 1259, 'new_value': 1368}]
2025-05-10 12:01:25,589 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0V
2025-05-10 12:01:26,086 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0V
2025-05-10 12:01:26,086 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13837.0, 'new_value': 16617.0}, {'field': 'total_amount', 'old_value': 13837.0, 'new_value': 16617.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 9}]
2025-05-10 12:01:26,086 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-10 12:01:26,555 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-10 12:01:26,556 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43590.69, 'new_value': 50109.89}, {'field': 'offline_amount', 'old_value': 87606.54, 'new_value': 94139.14}, {'field': 'total_amount', 'old_value': 131197.23, 'new_value': 144249.03}, {'field': 'order_count', 'old_value': 1344, 'new_value': 1523}]
2025-05-10 12:01:26,556 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO
2025-05-10 12:01:26,952 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO
2025-05-10 12:01:26,952 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14234.18, 'new_value': 15396.98}, {'field': 'total_amount', 'old_value': 14234.18, 'new_value': 15396.98}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-05-10 12:01:26,952 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-10 12:01:27,409 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-10 12:01:27,409 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 66564.0, 'new_value': 76512.0}, {'field': 'total_amount', 'old_value': 66564.0, 'new_value': 76512.0}, {'field': 'order_count', 'old_value': 5547, 'new_value': 6376}]
2025-05-10 12:01:27,409 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMBA
2025-05-10 12:01:27,891 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMBA
2025-05-10 12:01:27,891 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12089.2, 'new_value': 13733.2}, {'field': 'offline_amount', 'old_value': 12333.95, 'new_value': 13063.15}, {'field': 'total_amount', 'old_value': 24423.15, 'new_value': 26796.35}, {'field': 'order_count', 'old_value': 5040, 'new_value': 5211}]
2025-05-10 12:01:27,891 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCO
2025-05-10 12:01:28,362 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCO
2025-05-10 12:01:28,362 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4160.0, 'new_value': 4161.0}, {'field': 'total_amount', 'old_value': 4160.0, 'new_value': 4161.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-10 12:01:28,363 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-10 12:01:28,942 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-10 12:01:28,942 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21622.4, 'new_value': 22420.4}, {'field': 'total_amount', 'old_value': 21622.4, 'new_value': 22420.4}, {'field': 'order_count', 'old_value': 30, 'new_value': 32}]
2025-05-10 12:01:28,942 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMCA
2025-05-10 12:01:29,528 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMCA
2025-05-10 12:01:29,529 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15813.2, 'new_value': 18163.2}, {'field': 'total_amount', 'old_value': 15813.2, 'new_value': 18163.2}, {'field': 'order_count', 'old_value': 769, 'new_value': 886}]
2025-05-10 12:01:29,529 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-10 12:01:30,013 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-10 12:01:30,013 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14662.15, 'new_value': 16329.95}, {'field': 'total_amount', 'old_value': 14662.15, 'new_value': 16329.95}, {'field': 'order_count', 'old_value': 638, 'new_value': 722}]
2025-05-10 12:01:30,014 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMDA
2025-05-10 12:01:30,452 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMDA
2025-05-10 12:01:30,452 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4405.58, 'new_value': 5839.4}, {'field': 'offline_amount', 'old_value': 83920.92, 'new_value': 90971.65}, {'field': 'total_amount', 'old_value': 88326.5, 'new_value': 96811.05}, {'field': 'order_count', 'old_value': 1110, 'new_value': 1280}]
2025-05-10 12:01:30,452 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-10 12:01:30,881 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-10 12:01:30,881 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46184.3, 'new_value': 53005.5}, {'field': 'total_amount', 'old_value': 90222.15, 'new_value': 97043.35}, {'field': 'order_count', 'old_value': 2218, 'new_value': 2428}]
2025-05-10 12:01:30,881 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-10 12:01:31,360 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-10 12:01:31,360 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94899.0, 'new_value': 103565.0}, {'field': 'total_amount', 'old_value': 94899.0, 'new_value': 103565.0}, {'field': 'order_count', 'old_value': 41, 'new_value': 46}]
2025-05-10 12:01:31,361 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-10 12:01:31,844 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-10 12:01:31,844 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9049.0, 'new_value': 11727.0}, {'field': 'total_amount', 'old_value': 9049.0, 'new_value': 11727.0}, {'field': 'order_count', 'old_value': 100, 'new_value': 125}]
2025-05-10 12:01:31,844 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-10 12:01:32,288 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-10 12:01:32,289 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16779.79, 'new_value': 19416.58}, {'field': 'offline_amount', 'old_value': 11523.99, 'new_value': 12679.48}, {'field': 'total_amount', 'old_value': 28303.78, 'new_value': 32096.06}, {'field': 'order_count', 'old_value': 1586, 'new_value': 1791}]
2025-05-10 12:01:32,289 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-10 12:01:32,751 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-10 12:01:32,751 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22882.0, 'new_value': 24407.0}, {'field': 'total_amount', 'old_value': 22882.0, 'new_value': 24407.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 18}]
2025-05-10 12:01:32,752 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-10 12:01:33,178 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-10 12:01:33,178 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5995.68, 'new_value': 6841.48}, {'field': 'total_amount', 'old_value': 5995.68, 'new_value': 6841.48}, {'field': 'order_count', 'old_value': 64, 'new_value': 75}]
2025-05-10 12:01:33,178 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-10 12:01:33,727 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-10 12:01:33,727 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 141971.49, 'new_value': 157811.81}, {'field': 'total_amount', 'old_value': 141971.49, 'new_value': 157811.81}, {'field': 'order_count', 'old_value': 512, 'new_value': 569}]
2025-05-10 12:01:33,727 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-10 12:01:34,153 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-10 12:01:34,153 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91904.4, 'new_value': 104175.9}, {'field': 'total_amount', 'old_value': 91904.4, 'new_value': 104175.9}, {'field': 'order_count', 'old_value': 2459, 'new_value': 2802}]
2025-05-10 12:01:34,153 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-10 12:01:34,612 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-10 12:01:34,613 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12455.71, 'new_value': 14148.34}, {'field': 'total_amount', 'old_value': 12455.71, 'new_value': 14148.34}, {'field': 'order_count', 'old_value': 1575, 'new_value': 1783}]
2025-05-10 12:01:34,613 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-10 12:01:35,042 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-10 12:01:35,042 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7733.34, 'new_value': 8911.62}, {'field': 'offline_amount', 'old_value': 11533.4, 'new_value': 12770.6}, {'field': 'total_amount', 'old_value': 19266.74, 'new_value': 21682.22}, {'field': 'order_count', 'old_value': 821, 'new_value': 943}]
2025-05-10 12:01:35,042 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-10 12:01:35,539 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-10 12:01:35,539 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25762.06, 'new_value': 28487.22}, {'field': 'offline_amount', 'old_value': 52431.24, 'new_value': 54990.08}, {'field': 'total_amount', 'old_value': 78193.3, 'new_value': 83477.3}, {'field': 'order_count', 'old_value': 754, 'new_value': 831}]
2025-05-10 12:01:35,540 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-10 12:01:36,007 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-10 12:01:36,008 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11926.0, 'new_value': 16272.0}, {'field': 'total_amount', 'old_value': 17127.0, 'new_value': 21473.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 68}]
2025-05-10 12:01:36,008 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-10 12:01:36,485 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-10 12:01:36,485 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 481752.55, 'new_value': 555064.19}, {'field': 'offline_amount', 'old_value': 99155.3, 'new_value': 101214.3}, {'field': 'total_amount', 'old_value': 580907.85, 'new_value': 656278.49}, {'field': 'order_count', 'old_value': 2095, 'new_value': 2369}]
2025-05-10 12:01:36,485 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-10 12:01:36,914 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-10 12:01:36,914 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19863.0, 'new_value': 68551.0}, {'field': 'total_amount', 'old_value': 19863.0, 'new_value': 68551.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 18}]
2025-05-10 12:01:36,914 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-05-10 12:01:37,423 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-05-10 12:01:37,423 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3003.0, 'new_value': 3434.0}, {'field': 'total_amount', 'old_value': 3003.0, 'new_value': 3434.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 26}]
2025-05-10 12:01:37,424 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-10 12:01:37,904 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-10 12:01:37,904 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6654.8, 'new_value': 7842.0}, {'field': 'offline_amount', 'old_value': 19447.9, 'new_value': 21260.9}, {'field': 'total_amount', 'old_value': 26102.7, 'new_value': 29102.9}, {'field': 'order_count', 'old_value': 65, 'new_value': 71}]
2025-05-10 12:01:37,904 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-10 12:01:38,383 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-10 12:01:38,383 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 169460.92, 'new_value': 182807.12}, {'field': 'total_amount', 'old_value': 169460.92, 'new_value': 182807.12}, {'field': 'order_count', 'old_value': 877, 'new_value': 916}]
2025-05-10 12:01:38,384 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-10 12:01:38,852 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-10 12:01:38,852 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11738.0, 'new_value': 12761.0}, {'field': 'total_amount', 'old_value': 11738.0, 'new_value': 12761.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 56}]
2025-05-10 12:01:38,852 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-10 12:01:39,342 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-10 12:01:39,342 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72790.0, 'new_value': 82776.0}, {'field': 'total_amount', 'old_value': 72790.0, 'new_value': 82776.0}, {'field': 'order_count', 'old_value': 118, 'new_value': 132}]
2025-05-10 12:01:39,342 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-10 12:01:39,756 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-10 12:01:39,757 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 254.38, 'new_value': 406.56}, {'field': 'offline_amount', 'old_value': 208338.56, 'new_value': 233744.06}, {'field': 'total_amount', 'old_value': 208592.94, 'new_value': 234150.62}, {'field': 'order_count', 'old_value': 469, 'new_value': 537}]
2025-05-10 12:01:39,757 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-10 12:01:40,254 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-10 12:01:40,254 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50670.0, 'new_value': 60250.0}, {'field': 'total_amount', 'old_value': 50670.0, 'new_value': 60250.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-05-10 12:01:40,254 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-10 12:01:40,727 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-10 12:01:40,727 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9536.25, 'new_value': 9707.69}, {'field': 'offline_amount', 'old_value': 16398.42, 'new_value': 19061.54}, {'field': 'total_amount', 'old_value': 25934.67, 'new_value': 28769.23}, {'field': 'order_count', 'old_value': 103, 'new_value': 113}]
2025-05-10 12:01:40,727 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-10 12:01:41,216 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-10 12:01:41,216 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59936.58, 'new_value': 63527.88}, {'field': 'total_amount', 'old_value': 59936.58, 'new_value': 63527.88}, {'field': 'order_count', 'old_value': 1516, 'new_value': 1615}]
2025-05-10 12:01:41,216 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-10 12:01:41,691 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-10 12:01:41,691 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 123955.92, 'new_value': 133431.01}, {'field': 'total_amount', 'old_value': 123955.92, 'new_value': 133431.01}, {'field': 'order_count', 'old_value': 997, 'new_value': 1116}]
2025-05-10 12:01:41,691 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-10 12:01:42,089 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-10 12:01:42,089 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 134023.52, 'new_value': 141556.22}, {'field': 'offline_amount', 'old_value': 4920.0, 'new_value': 4998.0}, {'field': 'total_amount', 'old_value': 138943.52, 'new_value': 146554.22}, {'field': 'order_count', 'old_value': 993, 'new_value': 1076}]
2025-05-10 12:01:42,089 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-10 12:01:42,496 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-10 12:01:42,496 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5919.0, 'new_value': 6346.0}, {'field': 'total_amount', 'old_value': 5919.0, 'new_value': 6346.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 33}]
2025-05-10 12:01:42,496 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-10 12:01:42,906 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-10 12:01:42,906 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6034.4, 'new_value': 6803.1}, {'field': 'total_amount', 'old_value': 6034.4, 'new_value': 6803.1}, {'field': 'order_count', 'old_value': 232, 'new_value': 265}]
2025-05-10 12:01:42,907 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-10 12:01:43,364 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-10 12:01:43,364 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3325.5, 'new_value': 3483.5}, {'field': 'offline_amount', 'old_value': 14327.4, 'new_value': 15222.7}, {'field': 'total_amount', 'old_value': 17652.9, 'new_value': 18706.2}, {'field': 'order_count', 'old_value': 181, 'new_value': 201}]
2025-05-10 12:01:43,365 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-10 12:01:43,814 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-10 12:01:43,815 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3779.0, 'new_value': 4584.0}, {'field': 'offline_amount', 'old_value': 11618.0, 'new_value': 11867.0}, {'field': 'total_amount', 'old_value': 15397.0, 'new_value': 16451.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 31}]
2025-05-10 12:01:43,815 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-10 12:01:44,251 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-10 12:01:44,251 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 294251.22, 'new_value': 314243.28}, {'field': 'total_amount', 'old_value': 294251.22, 'new_value': 314243.28}, {'field': 'order_count', 'old_value': 6001, 'new_value': 6330}]
2025-05-10 12:01:44,251 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-10 12:01:44,726 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-10 12:01:44,726 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27746.39, 'new_value': 31007.19}, {'field': 'offline_amount', 'old_value': 72959.99, 'new_value': 84179.69}, {'field': 'total_amount', 'old_value': 100706.38, 'new_value': 115186.88}, {'field': 'order_count', 'old_value': 4470, 'new_value': 5177}]
2025-05-10 12:01:44,726 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-10 12:01:45,104 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-10 12:01:45,104 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9751.31, 'new_value': 10993.63}, {'field': 'total_amount', 'old_value': 9751.31, 'new_value': 10993.63}, {'field': 'order_count', 'old_value': 366, 'new_value': 405}]
2025-05-10 12:01:45,104 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-10 12:01:45,663 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-10 12:01:45,663 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 256566.48, 'new_value': 280683.58}, {'field': 'total_amount', 'old_value': 256566.48, 'new_value': 280683.58}, {'field': 'order_count', 'old_value': 1829, 'new_value': 2032}]
2025-05-10 12:01:45,663 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-10 12:01:46,233 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-10 12:01:46,233 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 252216.0, 'new_value': 279082.0}, {'field': 'total_amount', 'old_value': 252216.0, 'new_value': 279082.0}, {'field': 'order_count', 'old_value': 62, 'new_value': 67}]
2025-05-10 12:01:46,233 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-10 12:01:46,700 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-10 12:01:46,701 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 542.51, 'new_value': 561.42}, {'field': 'offline_amount', 'old_value': 12260.04, 'new_value': 12744.01}, {'field': 'total_amount', 'old_value': 12802.55, 'new_value': 13305.43}, {'field': 'order_count', 'old_value': 449, 'new_value': 465}]
2025-05-10 12:01:46,701 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-10 12:01:47,152 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-10 12:01:47,152 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 358946.0, 'new_value': 381049.0}, {'field': 'total_amount', 'old_value': 358946.0, 'new_value': 381049.0}, {'field': 'order_count', 'old_value': 1655, 'new_value': 1764}]
2025-05-10 12:01:47,152 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-10 12:01:47,601 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-10 12:01:47,601 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1368.0, 'new_value': 2367.0}, {'field': 'total_amount', 'old_value': 1368.0, 'new_value': 2367.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-10 12:01:47,601 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-10 12:01:48,083 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-10 12:01:48,083 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65981.5, 'new_value': 73459.5}, {'field': 'total_amount', 'old_value': 65981.5, 'new_value': 73459.5}, {'field': 'order_count', 'old_value': 363, 'new_value': 401}]
2025-05-10 12:01:48,083 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-10 12:01:48,636 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-10 12:01:48,636 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7864.1, 'new_value': 8903.95}, {'field': 'offline_amount', 'old_value': 88034.74, 'new_value': 94590.84}, {'field': 'total_amount', 'old_value': 95898.84, 'new_value': 103494.79}, {'field': 'order_count', 'old_value': 2920, 'new_value': 3160}]
2025-05-10 12:01:48,636 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-10 12:01:49,109 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-10 12:01:49,109 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50288.0, 'new_value': 52971.0}, {'field': 'total_amount', 'old_value': 65492.0, 'new_value': 68175.0}, {'field': 'order_count', 'old_value': 1357, 'new_value': 1358}]
2025-05-10 12:01:49,109 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-10 12:01:49,582 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-10 12:01:49,582 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40783.0, 'new_value': 44477.0}, {'field': 'total_amount', 'old_value': 40783.0, 'new_value': 44477.0}, {'field': 'order_count', 'old_value': 226, 'new_value': 249}]
2025-05-10 12:01:49,583 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-10 12:01:50,041 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-10 12:01:50,041 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11589.0, 'new_value': 11809.0}, {'field': 'total_amount', 'old_value': 11589.0, 'new_value': 11809.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-10 12:01:50,041 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMEE
2025-05-10 12:01:50,465 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMEE
2025-05-10 12:01:50,465 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2832.0, 'new_value': 3230.0}, {'field': 'total_amount', 'old_value': 2832.0, 'new_value': 3230.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-10 12:01:50,465 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-10 12:01:50,937 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-10 12:01:50,937 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24381.73, 'new_value': 30188.46}, {'field': 'offline_amount', 'old_value': 163340.22, 'new_value': 170680.62}, {'field': 'total_amount', 'old_value': 187721.95, 'new_value': 200869.08}, {'field': 'order_count', 'old_value': 1158, 'new_value': 1319}]
2025-05-10 12:01:50,937 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-10 12:01:51,391 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-10 12:01:51,391 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34612.0, 'new_value': 39592.0}, {'field': 'total_amount', 'old_value': 34612.0, 'new_value': 39592.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-05-10 12:01:51,392 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-10 12:01:51,879 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-10 12:01:51,879 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26869.39, 'new_value': 31678.21}, {'field': 'offline_amount', 'old_value': 36136.36, 'new_value': 41096.85}, {'field': 'total_amount', 'old_value': 63005.75, 'new_value': 72775.06}, {'field': 'order_count', 'old_value': 2570, 'new_value': 2958}]
2025-05-10 12:01:51,879 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-10 12:01:52,336 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-10 12:01:52,337 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84588.6, 'new_value': 90322.08}, {'field': 'total_amount', 'old_value': 103762.03, 'new_value': 109495.51}, {'field': 'order_count', 'old_value': 2197, 'new_value': 2315}]
2025-05-10 12:01:52,337 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-10 12:01:52,775 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-10 12:01:52,775 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23059.0, 'new_value': 23788.0}, {'field': 'total_amount', 'old_value': 23059.0, 'new_value': 23788.0}, {'field': 'order_count', 'old_value': 46, 'new_value': 47}]
2025-05-10 12:01:52,775 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMJE
2025-05-10 12:01:53,458 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMJE
2025-05-10 12:01:53,458 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27116.0, 'new_value': 29466.0}, {'field': 'total_amount', 'old_value': 27116.0, 'new_value': 29466.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-10 12:01:53,458 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-10 12:01:53,967 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-10 12:01:53,967 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 382063.0, 'new_value': 399264.0}, {'field': 'total_amount', 'old_value': 382063.0, 'new_value': 399264.0}, {'field': 'order_count', 'old_value': 409, 'new_value': 448}]
2025-05-10 12:01:53,967 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-10 12:01:54,383 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-10 12:01:54,383 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 245304.52, 'new_value': 265367.58}, {'field': 'total_amount', 'old_value': 245304.52, 'new_value': 265367.58}, {'field': 'order_count', 'old_value': 1807, 'new_value': 1984}]
2025-05-10 12:01:54,384 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-10 12:01:54,850 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-10 12:01:54,851 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8920.0, 'new_value': 10288.0}, {'field': 'total_amount', 'old_value': 8920.0, 'new_value': 10288.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 54}]
2025-05-10 12:01:54,851 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-10 12:01:55,255 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-10 12:01:55,256 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32110.85, 'new_value': 34063.58}, {'field': 'total_amount', 'old_value': 32110.85, 'new_value': 34063.58}, {'field': 'order_count', 'old_value': 1841, 'new_value': 1962}]
2025-05-10 12:01:55,256 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9P
2025-05-10 12:01:55,693 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9P
2025-05-10 12:01:55,694 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12944.0, 'new_value': 13714.0}, {'field': 'total_amount', 'old_value': 12944.0, 'new_value': 13714.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 17}]
2025-05-10 12:01:55,694 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-10 12:01:56,113 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-10 12:01:56,113 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19934.98, 'new_value': 21362.97}, {'field': 'offline_amount', 'old_value': 17945.17, 'new_value': 19606.29}, {'field': 'total_amount', 'old_value': 37880.15, 'new_value': 40969.26}, {'field': 'order_count', 'old_value': 733, 'new_value': 811}]
2025-05-10 12:01:56,114 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-10 12:01:56,636 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-10 12:01:56,636 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5288.0, 'new_value': 5656.0}, {'field': 'offline_amount', 'old_value': 86373.0, 'new_value': 98739.0}, {'field': 'total_amount', 'old_value': 91661.0, 'new_value': 104395.0}, {'field': 'order_count', 'old_value': 459, 'new_value': 526}]
2025-05-10 12:01:56,636 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-10 12:01:57,111 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-10 12:01:57,111 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-05-10 12:01:57,112 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-10 12:01:57,630 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-10 12:01:57,630 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19403.43, 'new_value': 19914.43}, {'field': 'total_amount', 'old_value': 19403.43, 'new_value': 19914.43}, {'field': 'order_count', 'old_value': 61, 'new_value': 62}]
2025-05-10 12:01:57,630 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-10 12:01:58,149 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-10 12:01:58,149 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13288.43, 'new_value': 14975.33}, {'field': 'offline_amount', 'old_value': 414919.52, 'new_value': 454812.58}, {'field': 'total_amount', 'old_value': 428207.95, 'new_value': 469787.91}, {'field': 'order_count', 'old_value': 1783, 'new_value': 1992}]
2025-05-10 12:01:58,150 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPE
2025-05-10 12:01:58,553 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPE
2025-05-10 12:01:58,554 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3298.0, 'new_value': 3896.0}, {'field': 'offline_amount', 'old_value': 1598.0, 'new_value': 1996.0}, {'field': 'total_amount', 'old_value': 4896.0, 'new_value': 5892.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 28}]
2025-05-10 12:01:58,554 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-10 12:01:59,022 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-10 12:01:59,022 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34016.63, 'new_value': 35833.12}, {'field': 'offline_amount', 'old_value': 68285.64, 'new_value': 74664.99}, {'field': 'total_amount', 'old_value': 102302.27, 'new_value': 110498.11}, {'field': 'order_count', 'old_value': 1674, 'new_value': 1857}]
2025-05-10 12:01:59,022 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-10 12:01:59,555 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-10 12:01:59,555 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4000.0, 'new_value': 4100.0}, {'field': 'offline_amount', 'old_value': 106837.0, 'new_value': 115670.0}, {'field': 'total_amount', 'old_value': 110837.0, 'new_value': 119770.0}, {'field': 'order_count', 'old_value': 70, 'new_value': 78}]
2025-05-10 12:01:59,555 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-10 12:02:00,032 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-10 12:02:00,032 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 255101.6, 'new_value': 280054.85}, {'field': 'total_amount', 'old_value': 255101.6, 'new_value': 280054.85}, {'field': 'order_count', 'old_value': 2714, 'new_value': 3127}]
2025-05-10 12:02:00,033 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-10 12:02:00,518 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-10 12:02:00,519 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84633.0, 'new_value': 87758.0}, {'field': 'total_amount', 'old_value': 84633.0, 'new_value': 87758.0}, {'field': 'order_count', 'old_value': 1367, 'new_value': 1442}]
2025-05-10 12:02:00,519 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-10 12:02:01,056 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-10 12:02:01,056 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73296.26, 'new_value': 78091.13}, {'field': 'total_amount', 'old_value': 73296.26, 'new_value': 78091.13}, {'field': 'order_count', 'old_value': 3025, 'new_value': 3242}]
2025-05-10 12:02:01,057 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-10 12:02:01,559 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-10 12:02:01,559 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 144602.0, 'new_value': 169744.0}, {'field': 'total_amount', 'old_value': 144602.0, 'new_value': 169744.0}, {'field': 'order_count', 'old_value': 139, 'new_value': 159}]
2025-05-10 12:02:01,560 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-10 12:02:02,048 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-10 12:02:02,048 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 95478.45, 'new_value': 106305.52}, {'field': 'offline_amount', 'old_value': 59003.94, 'new_value': 64098.55}, {'field': 'total_amount', 'old_value': 154482.39, 'new_value': 170404.07}, {'field': 'order_count', 'old_value': 1322, 'new_value': 1549}]
2025-05-10 12:02:02,049 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-10 12:02:02,470 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-10 12:02:02,470 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84169.69, 'new_value': 91742.34}, {'field': 'total_amount', 'old_value': 84169.69, 'new_value': 91742.34}, {'field': 'order_count', 'old_value': 608, 'new_value': 668}]
2025-05-10 12:02:02,470 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-10 12:02:03,026 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-10 12:02:03,027 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42447.16, 'new_value': 47301.22}, {'field': 'total_amount', 'old_value': 61486.76, 'new_value': 66340.82}, {'field': 'order_count', 'old_value': 1835, 'new_value': 1961}]
2025-05-10 12:02:03,027 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-10 12:02:03,455 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-10 12:02:03,456 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 68123.14, 'new_value': 76552.94}, {'field': 'offline_amount', 'old_value': 5504.1, 'new_value': 6011.3}, {'field': 'total_amount', 'old_value': 73627.24, 'new_value': 82564.24}, {'field': 'order_count', 'old_value': 2895, 'new_value': 3201}]
2025-05-10 12:02:03,456 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-10 12:02:04,022 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-10 12:02:04,022 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26658.0, 'new_value': 30947.0}, {'field': 'offline_amount', 'old_value': 345442.0, 'new_value': 398953.0}, {'field': 'total_amount', 'old_value': 372100.0, 'new_value': 429900.0}, {'field': 'order_count', 'old_value': 8502, 'new_value': 9787}]
2025-05-10 12:02:04,023 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-10 12:02:04,517 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-10 12:02:04,517 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 139959.26, 'new_value': 154766.51}, {'field': 'total_amount', 'old_value': 139959.26, 'new_value': 154766.51}, {'field': 'order_count', 'old_value': 474, 'new_value': 529}]
2025-05-10 12:02:04,517 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-10 12:02:04,935 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-10 12:02:04,935 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13688.0, 'new_value': 14351.0}, {'field': 'offline_amount', 'old_value': 50492.0, 'new_value': 66197.0}, {'field': 'total_amount', 'old_value': 64180.0, 'new_value': 80548.0}, {'field': 'order_count', 'old_value': 79, 'new_value': 91}]
2025-05-10 12:02:04,935 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-10 12:02:05,352 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-10 12:02:05,352 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14763.0, 'new_value': 15032.0}, {'field': 'total_amount', 'old_value': 14763.0, 'new_value': 15032.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-05-10 12:02:05,352 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-10 12:02:05,878 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-10 12:02:05,878 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15066.68, 'new_value': 16066.68}, {'field': 'offline_amount', 'old_value': 14984.49, 'new_value': 16628.96}, {'field': 'total_amount', 'old_value': 30051.17, 'new_value': 32695.64}, {'field': 'order_count', 'old_value': 1302, 'new_value': 1450}]
2025-05-10 12:02:05,878 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-10 12:02:06,335 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-10 12:02:06,335 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6953.5, 'new_value': 7805.75}, {'field': 'offline_amount', 'old_value': 6742.5, 'new_value': 7263.5}, {'field': 'total_amount', 'old_value': 13696.0, 'new_value': 15069.25}, {'field': 'order_count', 'old_value': 642, 'new_value': 695}]
2025-05-10 12:02:06,336 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-10 12:02:06,801 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-10 12:02:06,801 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107230.0, 'new_value': 122752.0}, {'field': 'total_amount', 'old_value': 107230.0, 'new_value': 122752.0}, {'field': 'order_count', 'old_value': 178, 'new_value': 180}]
2025-05-10 12:02:06,801 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-10 12:02:07,253 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-10 12:02:07,253 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12290.0, 'new_value': 15224.0}, {'field': 'total_amount', 'old_value': 12290.0, 'new_value': 15224.0}, {'field': 'order_count', 'old_value': 71, 'new_value': 81}]
2025-05-10 12:02:07,253 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-10 12:02:07,683 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-10 12:02:07,683 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7844.0, 'new_value': 8112.9}, {'field': 'offline_amount', 'old_value': 23459.1, 'new_value': 23537.1}, {'field': 'total_amount', 'old_value': 31303.1, 'new_value': 31650.0}, {'field': 'order_count', 'old_value': 356, 'new_value': 360}]
2025-05-10 12:02:07,683 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-10 12:02:08,155 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-10 12:02:08,155 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31037.31, 'new_value': 35765.35}, {'field': 'offline_amount', 'old_value': 72565.05, 'new_value': 79171.85}, {'field': 'total_amount', 'old_value': 103602.36, 'new_value': 114937.2}, {'field': 'order_count', 'old_value': 3198, 'new_value': 3563}]
2025-05-10 12:02:08,155 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-10 12:02:08,614 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-10 12:02:08,614 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52691.89, 'new_value': 58605.51}, {'field': 'offline_amount', 'old_value': 21279.77, 'new_value': 22348.27}, {'field': 'total_amount', 'old_value': 73971.66, 'new_value': 80953.78}, {'field': 'order_count', 'old_value': 4104, 'new_value': 4549}]
2025-05-10 12:02:08,615 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-10 12:02:09,141 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-10 12:02:09,142 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80210.0, 'new_value': 92012.59}, {'field': 'total_amount', 'old_value': 102697.4, 'new_value': 114499.99}, {'field': 'order_count', 'old_value': 630, 'new_value': 699}]
2025-05-10 12:02:09,142 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-10 12:02:09,574 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-10 12:02:09,574 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65213.65, 'new_value': 79178.39}, {'field': 'total_amount', 'old_value': 65213.65, 'new_value': 79178.39}, {'field': 'order_count', 'old_value': 3257, 'new_value': 3888}]
2025-05-10 12:02:09,574 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-10 12:02:10,026 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-10 12:02:10,027 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57486.6, 'new_value': 64289.0}, {'field': 'total_amount', 'old_value': 57486.6, 'new_value': 64289.0}, {'field': 'order_count', 'old_value': 264, 'new_value': 295}]
2025-05-10 12:02:10,027 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-10 12:02:10,488 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-10 12:02:10,488 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72718.7, 'new_value': 76644.6}, {'field': 'total_amount', 'old_value': 72718.7, 'new_value': 76644.6}, {'field': 'order_count', 'old_value': 1994, 'new_value': 2099}]
2025-05-10 12:02:10,489 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-10 12:02:10,943 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-10 12:02:10,943 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1847.0, 'new_value': 2488.0}, {'field': 'total_amount', 'old_value': 8319.0, 'new_value': 8960.0}, {'field': 'order_count', 'old_value': 41, 'new_value': 44}]
2025-05-10 12:02:10,943 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-10 12:02:11,379 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-10 12:02:11,380 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 51608.14, 'new_value': 58406.64}, {'field': 'offline_amount', 'old_value': 109270.32, 'new_value': 117743.24}, {'field': 'total_amount', 'old_value': 160878.46, 'new_value': 176149.88}, {'field': 'order_count', 'old_value': 1246, 'new_value': 1378}]
2025-05-10 12:02:11,380 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-10 12:02:11,833 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-10 12:02:11,834 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42210.5, 'new_value': 46131.3}, {'field': 'total_amount', 'old_value': 42210.5, 'new_value': 46131.3}, {'field': 'order_count', 'old_value': 187, 'new_value': 208}]
2025-05-10 12:02:11,834 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-10 12:02:12,296 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-10 12:02:12,296 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14858.02, 'new_value': 16736.37}, {'field': 'offline_amount', 'old_value': 10154.82, 'new_value': 10540.3}, {'field': 'total_amount', 'old_value': 25012.84, 'new_value': 27276.67}, {'field': 'order_count', 'old_value': 1066, 'new_value': 1165}]
2025-05-10 12:02:12,296 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-10 12:02:12,745 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-10 12:02:12,745 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5646.97, 'new_value': 6376.58}, {'field': 'offline_amount', 'old_value': 14507.1, 'new_value': 15397.0}, {'field': 'total_amount', 'old_value': 20154.07, 'new_value': 21773.58}, {'field': 'order_count', 'old_value': 810, 'new_value': 870}]
2025-05-10 12:02:12,745 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-10 12:02:13,200 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-10 12:02:13,200 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31440.0, 'new_value': 32134.0}, {'field': 'total_amount', 'old_value': 31440.0, 'new_value': 32134.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-05-10 12:02:13,200 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-10 12:02:13,742 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-10 12:02:13,742 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28895.0, 'new_value': 31997.0}, {'field': 'total_amount', 'old_value': 28895.0, 'new_value': 31997.0}, {'field': 'order_count', 'old_value': 1254, 'new_value': 1422}]
2025-05-10 12:02:13,742 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-10 12:02:14,355 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-10 12:02:14,356 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34609.06, 'new_value': 40260.87}, {'field': 'total_amount', 'old_value': 35772.82, 'new_value': 41424.63}, {'field': 'order_count', 'old_value': 163, 'new_value': 189}]
2025-05-10 12:02:14,356 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-10 12:02:14,821 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-10 12:02:14,821 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 294404.53, 'new_value': 317077.35}, {'field': 'total_amount', 'old_value': 294404.53, 'new_value': 317077.35}, {'field': 'order_count', 'old_value': 2178, 'new_value': 2380}]
2025-05-10 12:02:14,821 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-10 12:02:15,336 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-10 12:02:15,337 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4369.0, 'new_value': 5841.3}, {'field': 'offline_amount', 'old_value': 48824.1, 'new_value': 62995.6}, {'field': 'total_amount', 'old_value': 53193.1, 'new_value': 68836.9}, {'field': 'order_count', 'old_value': 1627, 'new_value': 2136}]
2025-05-10 12:02:15,337 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-10 12:02:15,819 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-10 12:02:15,819 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 179717.0, 'new_value': 189220.0}, {'field': 'total_amount', 'old_value': 179717.0, 'new_value': 189220.0}, {'field': 'order_count', 'old_value': 1363, 'new_value': 1459}]
2025-05-10 12:02:15,819 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-10 12:02:16,360 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-10 12:02:16,360 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1979.0, 'new_value': 2199.0}, {'field': 'total_amount', 'old_value': 5805.0, 'new_value': 6025.0}, {'field': 'order_count', 'old_value': 53, 'new_value': 54}]
2025-05-10 12:02:16,360 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-10 12:02:16,807 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-10 12:02:16,807 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19200.91, 'new_value': 21623.21}, {'field': 'offline_amount', 'old_value': 14191.17, 'new_value': 16070.19}, {'field': 'total_amount', 'old_value': 33392.08, 'new_value': 37693.4}, {'field': 'order_count', 'old_value': 1806, 'new_value': 2034}]
2025-05-10 12:02:16,808 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-10 12:02:17,314 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-10 12:02:17,314 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1330.0, 'new_value': 1408.0}, {'field': 'offline_amount', 'old_value': 13126.8, 'new_value': 13654.8}, {'field': 'total_amount', 'old_value': 14456.8, 'new_value': 15062.8}, {'field': 'order_count', 'old_value': 524, 'new_value': 548}]
2025-05-10 12:02:17,314 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-10 12:02:17,927 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-10 12:02:17,928 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30632.07, 'new_value': 35549.22}, {'field': 'offline_amount', 'old_value': 33224.45, 'new_value': 38651.19}, {'field': 'total_amount', 'old_value': 63856.52, 'new_value': 74200.41}, {'field': 'order_count', 'old_value': 1637, 'new_value': 1899}]
2025-05-10 12:02:17,928 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2Q
2025-05-10 12:02:18,444 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2Q
2025-05-10 12:02:18,444 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42992.0, 'new_value': 45991.0}, {'field': 'total_amount', 'old_value': 42992.0, 'new_value': 45991.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-05-10 12:02:18,445 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-10 12:02:18,911 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-10 12:02:18,912 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 321369.0, 'new_value': 353254.0}, {'field': 'total_amount', 'old_value': 321369.0, 'new_value': 353254.0}, {'field': 'order_count', 'old_value': 388, 'new_value': 422}]
2025-05-10 12:02:18,912 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-10 12:02:19,347 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-10 12:02:19,347 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88809.0, 'new_value': 93165.0}, {'field': 'total_amount', 'old_value': 94759.3, 'new_value': 99115.3}, {'field': 'order_count', 'old_value': 185, 'new_value': 196}]
2025-05-10 12:02:19,348 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-10 12:02:19,808 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-10 12:02:19,808 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15529.7, 'new_value': 16696.1}, {'field': 'offline_amount', 'old_value': 31679.0, 'new_value': 43647.0}, {'field': 'total_amount', 'old_value': 47208.7, 'new_value': 60343.1}, {'field': 'order_count', 'old_value': 575, 'new_value': 646}]
2025-05-10 12:02:19,808 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-10 12:02:20,222 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-10 12:02:20,222 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45491.0, 'new_value': 49744.0}, {'field': 'offline_amount', 'old_value': 31429.0, 'new_value': 36527.0}, {'field': 'total_amount', 'old_value': 76920.0, 'new_value': 86271.0}, {'field': 'order_count', 'old_value': 953, 'new_value': 1056}]
2025-05-10 12:02:20,222 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-10 12:02:20,688 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-10 12:02:20,688 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3801.4, 'new_value': 4315.4}, {'field': 'offline_amount', 'old_value': 6192.95, 'new_value': 7402.77}, {'field': 'total_amount', 'old_value': 9994.35, 'new_value': 11718.17}, {'field': 'order_count', 'old_value': 118, 'new_value': 135}]
2025-05-10 12:02:20,688 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-10 12:02:21,169 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-10 12:02:21,169 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4340.02, 'new_value': 4616.66}, {'field': 'total_amount', 'old_value': 49017.02, 'new_value': 49293.66}, {'field': 'order_count', 'old_value': 21, 'new_value': 22}]
2025-05-10 12:02:21,169 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-10 12:02:21,638 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-10 12:02:21,638 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28237.86, 'new_value': 33074.1}, {'field': 'total_amount', 'old_value': 28237.86, 'new_value': 33074.1}, {'field': 'order_count', 'old_value': 151, 'new_value': 185}]
2025-05-10 12:02:21,638 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-10 12:02:22,098 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-10 12:02:22,098 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9126.82, 'new_value': 11324.62}, {'field': 'offline_amount', 'old_value': 12511.0, 'new_value': 13493.0}, {'field': 'total_amount', 'old_value': 21637.82, 'new_value': 24817.62}, {'field': 'order_count', 'old_value': 83, 'new_value': 94}]
2025-05-10 12:02:22,098 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-10 12:02:22,547 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-10 12:02:22,548 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 323.0, 'new_value': 381.0}, {'field': 'offline_amount', 'old_value': 23876.0, 'new_value': 26029.0}, {'field': 'total_amount', 'old_value': 24199.0, 'new_value': 26410.0}, {'field': 'order_count', 'old_value': 183, 'new_value': 200}]
2025-05-10 12:02:22,548 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-10 12:02:23,103 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-10 12:02:23,104 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75191.5, 'new_value': 84847.5}, {'field': 'total_amount', 'old_value': 75191.5, 'new_value': 84847.5}, {'field': 'order_count', 'old_value': 383, 'new_value': 430}]
2025-05-10 12:02:23,104 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-10 12:02:23,587 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-10 12:02:23,587 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1921.0, 'new_value': 2271.0}, {'field': 'offline_amount', 'old_value': 6320.0, 'new_value': 6808.0}, {'field': 'total_amount', 'old_value': 8241.0, 'new_value': 9079.0}, {'field': 'order_count', 'old_value': 99, 'new_value': 104}]
2025-05-10 12:02:23,587 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-10 12:02:24,117 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-10 12:02:24,118 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2472.0, 'new_value': 3088.0}, {'field': 'offline_amount', 'old_value': 13558.57, 'new_value': 18859.57}, {'field': 'total_amount', 'old_value': 16030.57, 'new_value': 21947.57}, {'field': 'order_count', 'old_value': 150, 'new_value': 195}]
2025-05-10 12:02:24,118 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-10 12:02:24,657 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-10 12:02:24,657 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53674.98, 'new_value': 60862.14}, {'field': 'total_amount', 'old_value': 53674.98, 'new_value': 60862.14}, {'field': 'order_count', 'old_value': 193, 'new_value': 228}]
2025-05-10 12:02:24,658 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFQ
2025-05-10 12:02:25,200 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFQ
2025-05-10 12:02:25,200 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5308.0, 'new_value': 5607.0}, {'field': 'total_amount', 'old_value': 5308.0, 'new_value': 5607.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-10 12:02:25,200 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-10 12:02:25,674 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-10 12:02:25,674 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25542.0, 'new_value': 28983.0}, {'field': 'offline_amount', 'old_value': 92105.0, 'new_value': 100733.0}, {'field': 'total_amount', 'old_value': 117647.0, 'new_value': 129716.0}, {'field': 'order_count', 'old_value': 513, 'new_value': 574}]
2025-05-10 12:02:25,674 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-10 12:02:26,148 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-10 12:02:26,149 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 476102.0, 'new_value': 503139.0}, {'field': 'total_amount', 'old_value': 476102.0, 'new_value': 503139.0}, {'field': 'order_count', 'old_value': 1990, 'new_value': 2106}]
2025-05-10 12:02:26,149 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-10 12:02:26,588 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-10 12:02:26,589 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6050397.0, 'new_value': 6457026.0}, {'field': 'total_amount', 'old_value': 6050397.0, 'new_value': 6457026.0}, {'field': 'order_count', 'old_value': 17525, 'new_value': 18681}]
2025-05-10 12:02:26,589 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-10 12:02:27,014 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-10 12:02:27,015 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1366873.55, 'new_value': 1481700.59}, {'field': 'total_amount', 'old_value': 1366873.55, 'new_value': 1481700.59}, {'field': 'order_count', 'old_value': 2351, 'new_value': 2543}]
2025-05-10 12:02:27,015 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-10 12:02:27,440 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-10 12:02:27,441 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52542.53, 'new_value': 60478.17}, {'field': 'total_amount', 'old_value': 52542.53, 'new_value': 60478.17}, {'field': 'order_count', 'old_value': 3578, 'new_value': 4149}]
2025-05-10 12:02:27,441 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-10 12:02:27,900 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-10 12:02:27,900 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98388.0, 'new_value': 111327.0}, {'field': 'total_amount', 'old_value': 98388.0, 'new_value': 111327.0}, {'field': 'order_count', 'old_value': 1883, 'new_value': 2193}]
2025-05-10 12:02:27,900 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-10 12:02:28,462 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-10 12:02:28,462 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89440.0, 'new_value': 106097.0}, {'field': 'total_amount', 'old_value': 89440.0, 'new_value': 106097.0}, {'field': 'order_count', 'old_value': 171, 'new_value': 206}]
2025-05-10 12:02:28,463 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-10 12:02:28,964 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-10 12:02:28,964 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 95422.66, 'new_value': 109560.46}, {'field': 'offline_amount', 'old_value': 83126.99, 'new_value': 90097.98}, {'field': 'total_amount', 'old_value': 178549.65, 'new_value': 199658.44}, {'field': 'order_count', 'old_value': 6670, 'new_value': 7631}]
2025-05-10 12:02:28,964 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-10 12:02:29,376 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-10 12:02:29,377 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64156.96, 'new_value': 77542.41}, {'field': 'total_amount', 'old_value': 113036.66, 'new_value': 126422.11}, {'field': 'order_count', 'old_value': 116, 'new_value': 138}]
2025-05-10 12:02:29,377 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-10 12:02:29,882 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-10 12:02:29,883 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 160324.4, 'new_value': 168007.6}, {'field': 'total_amount', 'old_value': 160324.4, 'new_value': 168007.6}, {'field': 'order_count', 'old_value': 3474, 'new_value': 3640}]
2025-05-10 12:02:29,883 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-10 12:02:30,329 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-10 12:02:30,329 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16878.1, 'new_value': 18784.1}, {'field': 'total_amount', 'old_value': 16878.1, 'new_value': 18784.1}, {'field': 'order_count', 'old_value': 95, 'new_value': 105}]
2025-05-10 12:02:30,329 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-10 12:02:30,807 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-10 12:02:30,807 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 56685.0, 'new_value': 64789.0}, {'field': 'offline_amount', 'old_value': 34578.0, 'new_value': 48076.0}, {'field': 'total_amount', 'old_value': 91263.0, 'new_value': 112865.0}, {'field': 'order_count', 'old_value': 316, 'new_value': 362}]
2025-05-10 12:02:30,808 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-10 12:02:31,323 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-10 12:02:31,323 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 251961.0, 'new_value': 264068.0}, {'field': 'total_amount', 'old_value': 251961.0, 'new_value': 264068.0}, {'field': 'order_count', 'old_value': 5661, 'new_value': 5937}]
2025-05-10 12:02:31,323 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-10 12:02:31,819 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-10 12:02:31,819 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39247.0, 'new_value': 42932.0}, {'field': 'total_amount', 'old_value': 39247.0, 'new_value': 42932.0}, {'field': 'order_count', 'old_value': 2758, 'new_value': 2956}]
2025-05-10 12:02:31,819 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-10 12:02:32,259 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-10 12:02:32,259 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38965.0, 'new_value': 46490.0}, {'field': 'total_amount', 'old_value': 38965.0, 'new_value': 46490.0}, {'field': 'order_count', 'old_value': 2824, 'new_value': 3386}]
2025-05-10 12:02:32,260 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-10 12:02:32,745 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-10 12:02:32,746 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18592.0, 'new_value': 18664.0}, {'field': 'total_amount', 'old_value': 18592.0, 'new_value': 18664.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 16}]
2025-05-10 12:02:32,746 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-10 12:02:33,355 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-10 12:02:33,355 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6202.3, 'new_value': 6745.5}, {'field': 'offline_amount', 'old_value': 20420.9, 'new_value': 22296.0}, {'field': 'total_amount', 'old_value': 26623.2, 'new_value': 29041.5}, {'field': 'order_count', 'old_value': 983, 'new_value': 1086}]
2025-05-10 12:02:33,355 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-10 12:02:33,796 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-10 12:02:33,796 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23216.44, 'new_value': 25597.38}, {'field': 'total_amount', 'old_value': 23216.44, 'new_value': 25597.38}, {'field': 'order_count', 'old_value': 324, 'new_value': 341}]
2025-05-10 12:02:33,796 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-10 12:02:34,281 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-10 12:02:34,281 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1595970.39, 'new_value': 1768015.39}, {'field': 'total_amount', 'old_value': 1595970.39, 'new_value': 1768015.39}, {'field': 'order_count', 'old_value': 34316, 'new_value': 37960}]
2025-05-10 12:02:34,281 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-10 12:02:34,821 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-10 12:02:34,822 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10876.96, 'new_value': 12204.96}, {'field': 'total_amount', 'old_value': 10876.96, 'new_value': 12204.96}, {'field': 'order_count', 'old_value': 40, 'new_value': 45}]
2025-05-10 12:02:34,822 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-10 12:02:35,254 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-10 12:02:35,254 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 203576.42, 'new_value': 219494.03}, {'field': 'total_amount', 'old_value': 203576.42, 'new_value': 219494.03}, {'field': 'order_count', 'old_value': 1203, 'new_value': 1328}]
2025-05-10 12:02:35,254 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-10 12:02:35,864 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-10 12:02:35,864 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54645.38, 'new_value': 60256.66}, {'field': 'total_amount', 'old_value': 54645.38, 'new_value': 60256.66}, {'field': 'order_count', 'old_value': 1135, 'new_value': 1269}]
2025-05-10 12:02:35,865 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-10 12:02:36,363 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-10 12:02:36,363 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95877.0, 'new_value': 117444.0}, {'field': 'total_amount', 'old_value': 95877.0, 'new_value': 117444.0}, {'field': 'order_count', 'old_value': 2111, 'new_value': 2470}]
2025-05-10 12:02:36,363 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMDV
2025-05-10 12:02:36,820 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMDV
2025-05-10 12:02:36,821 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8329.0, 'new_value': 11407.0}, {'field': 'total_amount', 'old_value': 8329.0, 'new_value': 11407.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 10}]
2025-05-10 12:02:36,821 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMEV
2025-05-10 12:02:37,299 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMEV
2025-05-10 12:02:37,299 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29207.41, 'new_value': 31650.1}, {'field': 'total_amount', 'old_value': 29207.41, 'new_value': 31650.1}, {'field': 'order_count', 'old_value': 408, 'new_value': 445}]
2025-05-10 12:02:37,299 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-10 12:02:37,716 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-10 12:02:37,716 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98253.0, 'new_value': 100851.0}, {'field': 'total_amount', 'old_value': 98253.0, 'new_value': 100851.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 29}]
2025-05-10 12:02:37,716 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-10 12:02:38,261 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-10 12:02:38,261 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48148.86, 'new_value': 52726.92}, {'field': 'total_amount', 'old_value': 48148.86, 'new_value': 52726.92}, {'field': 'order_count', 'old_value': 1167, 'new_value': 1312}]
2025-05-10 12:02:38,261 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMHV
2025-05-10 12:02:38,743 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMHV
2025-05-10 12:02:38,743 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5064.0, 'new_value': 6976.0}, {'field': 'total_amount', 'old_value': 5064.0, 'new_value': 6976.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-05-10 12:02:38,744 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-10 12:02:39,200 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-10 12:02:39,201 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9693.0, 'new_value': 11072.0}, {'field': 'total_amount', 'old_value': 9693.0, 'new_value': 11072.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 42}]
2025-05-10 12:02:39,201 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-10 12:02:39,637 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-10 12:02:39,637 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27769.26, 'new_value': 32485.96}, {'field': 'offline_amount', 'old_value': 161795.2, 'new_value': 173016.6}, {'field': 'total_amount', 'old_value': 189564.46, 'new_value': 205502.56}, {'field': 'order_count', 'old_value': 1164, 'new_value': 1304}]
2025-05-10 12:02:39,637 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-10 12:02:40,094 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-10 12:02:40,095 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 1850.0}, {'field': 'offline_amount', 'old_value': 32473.0, 'new_value': 33573.0}, {'field': 'total_amount', 'old_value': 32473.0, 'new_value': 35423.0}, {'field': 'order_count', 'old_value': 158, 'new_value': 203}]
2025-05-10 12:02:40,095 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-10 12:02:40,573 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-10 12:02:40,573 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9740.9, 'new_value': 13970.31}, {'field': 'total_amount', 'old_value': 28618.37, 'new_value': 32847.78}, {'field': 'order_count', 'old_value': 1811, 'new_value': 2098}]
2025-05-10 12:02:40,573 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-10 12:02:40,974 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-10 12:02:40,975 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13140.18, 'new_value': 21004.01}, {'field': 'total_amount', 'old_value': 43548.32, 'new_value': 51412.15}, {'field': 'order_count', 'old_value': 2738, 'new_value': 3251}]
2025-05-10 12:02:40,975 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-10 12:02:41,384 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-10 12:02:41,384 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 390456.17, 'new_value': 435441.47}, {'field': 'total_amount', 'old_value': 390456.17, 'new_value': 435441.47}, {'field': 'order_count', 'old_value': 1183, 'new_value': 1324}]
2025-05-10 12:02:41,385 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-10 12:02:41,918 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-10 12:02:41,918 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48837.0, 'new_value': 57468.0}, {'field': 'total_amount', 'old_value': 48837.0, 'new_value': 57468.0}, {'field': 'order_count', 'old_value': 1742, 'new_value': 2066}]
2025-05-10 12:02:41,918 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G
2025-05-10 12:02:42,380 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G
2025-05-10 12:02:42,380 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-10 12:02:42,381 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-10 12:02:42,820 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-10 12:02:42,820 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 234272.62, 'new_value': 263733.17}, {'field': 'total_amount', 'old_value': 234272.62, 'new_value': 263733.17}, {'field': 'order_count', 'old_value': 1075, 'new_value': 1222}]
2025-05-10 12:02:42,820 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-10 12:02:43,286 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-10 12:02:43,286 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 251424.58, 'new_value': 290281.91}, {'field': 'total_amount', 'old_value': 251424.58, 'new_value': 290281.91}, {'field': 'order_count', 'old_value': 835, 'new_value': 944}]
2025-05-10 12:02:43,286 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-10 12:02:43,780 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-10 12:02:43,780 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 113248.33, 'new_value': 124304.8}, {'field': 'total_amount', 'old_value': 113248.33, 'new_value': 124304.8}, {'field': 'order_count', 'old_value': 313, 'new_value': 342}]
2025-05-10 12:02:43,780 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-10 12:02:44,222 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-10 12:02:44,222 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45799.0, 'new_value': 47759.0}, {'field': 'total_amount', 'old_value': 45799.0, 'new_value': 47759.0}, {'field': 'order_count', 'old_value': 204, 'new_value': 216}]
2025-05-10 12:02:44,223 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-10 12:02:44,682 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-10 12:02:44,682 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48085.0, 'new_value': 60585.0}, {'field': 'total_amount', 'old_value': 48085.0, 'new_value': 60585.0}, {'field': 'order_count', 'old_value': 114, 'new_value': 145}]
2025-05-10 12:02:44,683 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMKG
2025-05-10 12:02:45,123 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMKG
2025-05-10 12:02:45,123 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 3088.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 3088.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-10 12:02:45,123 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-10 12:02:45,560 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-10 12:02:45,560 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13640.0, 'new_value': 16588.0}, {'field': 'total_amount', 'old_value': 13640.0, 'new_value': 16588.0}, {'field': 'order_count', 'old_value': 264, 'new_value': 315}]
2025-05-10 12:02:45,560 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-10 12:02:46,059 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-10 12:02:46,059 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48644.0, 'new_value': 60702.0}, {'field': 'total_amount', 'old_value': 48644.0, 'new_value': 60702.0}, {'field': 'order_count', 'old_value': 5103, 'new_value': 6362}]
2025-05-10 12:02:46,059 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-10 12:02:46,550 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-10 12:02:46,550 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53179.0, 'new_value': 56244.0}, {'field': 'total_amount', 'old_value': 53179.0, 'new_value': 56244.0}, {'field': 'order_count', 'old_value': 410, 'new_value': 449}]
2025-05-10 12:02:46,550 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQG
2025-05-10 12:02:47,003 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQG
2025-05-10 12:02:47,003 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36972.0, 'new_value': 43370.0}, {'field': 'total_amount', 'old_value': 36973.0, 'new_value': 43371.0}, {'field': 'order_count', 'old_value': 32, 'new_value': 33}]
2025-05-10 12:02:47,003 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSG
2025-05-10 12:02:47,453 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSG
2025-05-10 12:02:47,453 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1370.0, 'new_value': 1760.0}, {'field': 'total_amount', 'old_value': 1370.0, 'new_value': 1760.0}, {'field': 'order_count', 'old_value': 582, 'new_value': 583}]
2025-05-10 12:02:47,453 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-10 12:02:47,939 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-10 12:02:47,939 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8343.0, 'new_value': 9963.0}, {'field': 'offline_amount', 'old_value': 21551.5, 'new_value': 22119.5}, {'field': 'total_amount', 'old_value': 29894.5, 'new_value': 32082.5}, {'field': 'order_count', 'old_value': 48, 'new_value': 51}]
2025-05-10 12:02:47,939 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-10 12:02:48,418 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-10 12:02:48,419 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19477.95, 'new_value': 21580.01}, {'field': 'total_amount', 'old_value': 19477.95, 'new_value': 21580.01}, {'field': 'order_count', 'old_value': 296, 'new_value': 332}]
2025-05-10 12:02:48,419 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-10 12:02:48,880 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-10 12:02:48,881 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44639.21, 'new_value': 52196.33}, {'field': 'offline_amount', 'old_value': 205276.84, 'new_value': 236993.96}, {'field': 'total_amount', 'old_value': 249916.05, 'new_value': 289190.29}, {'field': 'order_count', 'old_value': 596, 'new_value': 667}]
2025-05-10 12:02:48,881 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-10 12:02:49,331 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-10 12:02:49,331 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22473.6, 'new_value': 24396.6}, {'field': 'total_amount', 'old_value': 22473.6, 'new_value': 24396.6}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-05-10 12:02:49,332 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-10 12:02:49,812 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-10 12:02:49,812 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24827.96, 'new_value': 28578.59}, {'field': 'offline_amount', 'old_value': 231221.93, 'new_value': 264208.03}, {'field': 'total_amount', 'old_value': 254175.56, 'new_value': 290912.29}, {'field': 'order_count', 'old_value': 1287, 'new_value': 1477}]
2025-05-10 12:02:49,813 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-10 12:02:50,207 - INFO - 更新表单数据成功: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-10 12:02:50,207 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25848.0, 'new_value': 27578.0}, {'field': 'total_amount', 'old_value': 25848.0, 'new_value': 27578.0}, {'field': 'order_count', 'old_value': 84, 'new_value': 91}]
2025-05-10 12:02:50,207 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMLX
2025-05-10 12:02:50,657 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMLX
2025-05-10 12:02:50,657 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 652.9, 'new_value': 1389.97}, {'field': 'total_amount', 'old_value': 652.9, 'new_value': 1389.97}, {'field': 'order_count', 'old_value': 8, 'new_value': 15}]
2025-05-10 12:02:50,657 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMOE
2025-05-10 12:02:51,137 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMOE
2025-05-10 12:02:51,137 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21436.0, 'new_value': 22614.36}, {'field': 'total_amount', 'old_value': 25435.0, 'new_value': 26613.36}, {'field': 'order_count', 'old_value': 1739, 'new_value': 1807}]
2025-05-10 12:02:51,137 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-10 12:02:51,623 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-10 12:02:51,623 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58704.67, 'new_value': 65737.54}, {'field': 'total_amount', 'old_value': 58704.67, 'new_value': 65737.54}, {'field': 'order_count', 'old_value': 6088, 'new_value': 6817}]
2025-05-10 12:02:51,624 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-10 12:02:52,167 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-10 12:02:52,167 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8286.0, 'new_value': 9142.0}, {'field': 'total_amount', 'old_value': 8286.0, 'new_value': 9142.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 53}]
2025-05-10 12:02:52,167 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-10 12:02:52,677 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-10 12:02:52,678 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5431.17, 'new_value': 8373.28}, {'field': 'total_amount', 'old_value': 5431.17, 'new_value': 8373.28}, {'field': 'order_count', 'old_value': 145, 'new_value': 240}]
2025-05-10 12:02:52,678 - INFO - 日期 2025-05 处理完成 - 更新: 298 条，插入: 0 条，错误: 0 条
2025-05-10 12:02:52,678 - INFO - 数据同步完成！更新: 304 条，插入: 0 条，错误: 0 条
2025-05-10 12:02:52,680 - INFO - =================同步完成====================
2025-05-10 15:00:01,756 - INFO - =================使用默认全量同步=============
2025-05-10 15:00:03,092 - INFO - MySQL查询成功，共获取 3288 条记录
2025-05-10 15:00:03,093 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-10 15:00:03,121 - INFO - 开始处理日期: 2025-01
2025-05-10 15:00:03,123 - INFO - Request Parameters - Page 1:
2025-05-10 15:00:03,123 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 15:00:03,123 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 15:00:04,332 - INFO - Response - Page 1:
2025-05-10 15:00:04,533 - INFO - 第 1 页获取到 100 条记录
2025-05-10 15:00:04,533 - INFO - Request Parameters - Page 2:
2025-05-10 15:00:04,533 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 15:00:04,533 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 15:00:05,114 - INFO - Response - Page 2:
2025-05-10 15:00:05,314 - INFO - 第 2 页获取到 100 条记录
2025-05-10 15:00:05,314 - INFO - Request Parameters - Page 3:
2025-05-10 15:00:05,314 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 15:00:05,314 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 15:00:05,842 - INFO - Response - Page 3:
2025-05-10 15:00:06,043 - INFO - 第 3 页获取到 100 条记录
2025-05-10 15:00:06,043 - INFO - Request Parameters - Page 4:
2025-05-10 15:00:06,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 15:00:06,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 15:00:06,534 - INFO - Response - Page 4:
2025-05-10 15:00:06,735 - INFO - 第 4 页获取到 100 条记录
2025-05-10 15:00:06,735 - INFO - Request Parameters - Page 5:
2025-05-10 15:00:06,735 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 15:00:06,735 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 15:00:07,209 - INFO - Response - Page 5:
2025-05-10 15:00:07,410 - INFO - 第 5 页获取到 100 条记录
2025-05-10 15:00:07,410 - INFO - Request Parameters - Page 6:
2025-05-10 15:00:07,410 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 15:00:07,410 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 15:00:07,881 - INFO - Response - Page 6:
2025-05-10 15:00:08,081 - INFO - 第 6 页获取到 100 条记录
2025-05-10 15:00:08,081 - INFO - Request Parameters - Page 7:
2025-05-10 15:00:08,081 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 15:00:08,081 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 15:00:08,592 - INFO - Response - Page 7:
2025-05-10 15:00:08,792 - INFO - 第 7 页获取到 82 条记录
2025-05-10 15:00:08,792 - INFO - 查询完成，共获取到 682 条记录
2025-05-10 15:00:08,792 - INFO - 获取到 682 条表单数据
2025-05-10 15:00:08,806 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-10 15:00:08,823 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-10 15:00:08,823 - INFO - 开始处理日期: 2025-02
2025-05-10 15:00:08,823 - INFO - Request Parameters - Page 1:
2025-05-10 15:00:08,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 15:00:08,824 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 15:00:09,407 - INFO - Response - Page 1:
2025-05-10 15:00:09,607 - INFO - 第 1 页获取到 100 条记录
2025-05-10 15:00:09,607 - INFO - Request Parameters - Page 2:
2025-05-10 15:00:09,607 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 15:00:09,607 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 15:00:10,079 - INFO - Response - Page 2:
2025-05-10 15:00:10,280 - INFO - 第 2 页获取到 100 条记录
2025-05-10 15:00:10,280 - INFO - Request Parameters - Page 3:
2025-05-10 15:00:10,280 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 15:00:10,280 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 15:00:10,706 - INFO - Response - Page 3:
2025-05-10 15:00:10,907 - INFO - 第 3 页获取到 100 条记录
2025-05-10 15:00:10,907 - INFO - Request Parameters - Page 4:
2025-05-10 15:00:10,907 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 15:00:10,907 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 15:00:11,423 - INFO - Response - Page 4:
2025-05-10 15:00:11,623 - INFO - 第 4 页获取到 100 条记录
2025-05-10 15:00:11,623 - INFO - Request Parameters - Page 5:
2025-05-10 15:00:11,623 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 15:00:11,623 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 15:00:12,040 - INFO - Response - Page 5:
2025-05-10 15:00:12,242 - INFO - 第 5 页获取到 100 条记录
2025-05-10 15:00:12,242 - INFO - Request Parameters - Page 6:
2025-05-10 15:00:12,242 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 15:00:12,242 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 15:00:12,732 - INFO - Response - Page 6:
2025-05-10 15:00:12,933 - INFO - 第 6 页获取到 100 条记录
2025-05-10 15:00:12,933 - INFO - Request Parameters - Page 7:
2025-05-10 15:00:12,933 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 15:00:12,933 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 15:00:13,388 - INFO - Response - Page 7:
2025-05-10 15:00:13,589 - INFO - 第 7 页获取到 70 条记录
2025-05-10 15:00:13,589 - INFO - 查询完成，共获取到 670 条记录
2025-05-10 15:00:13,589 - INFO - 获取到 670 条表单数据
2025-05-10 15:00:13,602 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-10 15:00:13,615 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-10 15:00:13,615 - INFO - 开始处理日期: 2025-03
2025-05-10 15:00:13,615 - INFO - Request Parameters - Page 1:
2025-05-10 15:00:13,615 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 15:00:13,615 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 15:00:14,417 - INFO - Response - Page 1:
2025-05-10 15:00:14,618 - INFO - 第 1 页获取到 100 条记录
2025-05-10 15:00:14,618 - INFO - Request Parameters - Page 2:
2025-05-10 15:00:14,618 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 15:00:14,618 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 15:00:15,098 - INFO - Response - Page 2:
2025-05-10 15:00:15,298 - INFO - 第 2 页获取到 100 条记录
2025-05-10 15:00:15,298 - INFO - Request Parameters - Page 3:
2025-05-10 15:00:15,298 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 15:00:15,298 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 15:00:15,881 - INFO - Response - Page 3:
2025-05-10 15:00:16,081 - INFO - 第 3 页获取到 100 条记录
2025-05-10 15:00:16,081 - INFO - Request Parameters - Page 4:
2025-05-10 15:00:16,081 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 15:00:16,081 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 15:00:16,569 - INFO - Response - Page 4:
2025-05-10 15:00:16,769 - INFO - 第 4 页获取到 100 条记录
2025-05-10 15:00:16,769 - INFO - Request Parameters - Page 5:
2025-05-10 15:00:16,769 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 15:00:16,769 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 15:00:17,320 - INFO - Response - Page 5:
2025-05-10 15:00:17,520 - INFO - 第 5 页获取到 100 条记录
2025-05-10 15:00:17,520 - INFO - Request Parameters - Page 6:
2025-05-10 15:00:17,520 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 15:00:17,521 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 15:00:18,028 - INFO - Response - Page 6:
2025-05-10 15:00:18,228 - INFO - 第 6 页获取到 100 条记录
2025-05-10 15:00:18,228 - INFO - Request Parameters - Page 7:
2025-05-10 15:00:18,228 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 15:00:18,228 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 15:00:18,689 - INFO - Response - Page 7:
2025-05-10 15:00:18,889 - INFO - 第 7 页获取到 61 条记录
2025-05-10 15:00:18,889 - INFO - 查询完成，共获取到 661 条记录
2025-05-10 15:00:18,889 - INFO - 获取到 661 条表单数据
2025-05-10 15:00:18,902 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-10 15:00:18,913 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-10 15:00:18,913 - INFO - 开始处理日期: 2025-04
2025-05-10 15:00:18,913 - INFO - Request Parameters - Page 1:
2025-05-10 15:00:18,913 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 15:00:18,913 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 15:00:19,411 - INFO - Response - Page 1:
2025-05-10 15:00:19,613 - INFO - 第 1 页获取到 100 条记录
2025-05-10 15:00:19,613 - INFO - Request Parameters - Page 2:
2025-05-10 15:00:19,613 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 15:00:19,613 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 15:00:20,036 - INFO - Response - Page 2:
2025-05-10 15:00:20,237 - INFO - 第 2 页获取到 100 条记录
2025-05-10 15:00:20,237 - INFO - Request Parameters - Page 3:
2025-05-10 15:00:20,237 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 15:00:20,237 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 15:00:20,744 - INFO - Response - Page 3:
2025-05-10 15:00:20,945 - INFO - 第 3 页获取到 100 条记录
2025-05-10 15:00:20,945 - INFO - Request Parameters - Page 4:
2025-05-10 15:00:20,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 15:00:20,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 15:00:21,453 - INFO - Response - Page 4:
2025-05-10 15:00:21,654 - INFO - 第 4 页获取到 100 条记录
2025-05-10 15:00:21,654 - INFO - Request Parameters - Page 5:
2025-05-10 15:00:21,654 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 15:00:21,654 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 15:00:22,201 - INFO - Response - Page 5:
2025-05-10 15:00:22,402 - INFO - 第 5 页获取到 100 条记录
2025-05-10 15:00:22,402 - INFO - Request Parameters - Page 6:
2025-05-10 15:00:22,402 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 15:00:22,402 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 15:00:22,869 - INFO - Response - Page 6:
2025-05-10 15:00:23,070 - INFO - 第 6 页获取到 100 条记录
2025-05-10 15:00:23,070 - INFO - Request Parameters - Page 7:
2025-05-10 15:00:23,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 15:00:23,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 15:00:23,519 - INFO - Response - Page 7:
2025-05-10 15:00:23,719 - INFO - 第 7 页获取到 54 条记录
2025-05-10 15:00:23,719 - INFO - 查询完成，共获取到 654 条记录
2025-05-10 15:00:23,719 - INFO - 获取到 654 条表单数据
2025-05-10 15:00:23,732 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-10 15:00:23,738 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MHI
2025-05-10 15:00:24,195 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MHI
2025-05-10 15:00:24,196 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 20768.0, 'new_value': 22088.0}, {'field': 'total_amount', 'old_value': 20797.0, 'new_value': 22088.0}]
2025-05-10 15:00:24,197 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MV
2025-05-10 15:00:24,787 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MV
2025-05-10 15:00:24,787 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94228.0, 'new_value': 126728.0}, {'field': 'total_amount', 'old_value': 100908.0, 'new_value': 133408.0}]
2025-05-10 15:00:24,790 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MB7
2025-05-10 15:00:25,187 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MB7
2025-05-10 15:00:25,187 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47852.53, 'new_value': 47853.19}, {'field': 'total_amount', 'old_value': 47852.53, 'new_value': 47853.19}]
2025-05-10 15:00:25,189 - INFO - 日期 2025-04 处理完成 - 更新: 3 条，插入: 0 条，错误: 0 条
2025-05-10 15:00:25,189 - INFO - 开始处理日期: 2025-05
2025-05-10 15:00:25,190 - INFO - Request Parameters - Page 1:
2025-05-10 15:00:25,190 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 15:00:25,190 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 15:00:25,648 - INFO - Response - Page 1:
2025-05-10 15:00:25,848 - INFO - 第 1 页获取到 100 条记录
2025-05-10 15:00:25,848 - INFO - Request Parameters - Page 2:
2025-05-10 15:00:25,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 15:00:25,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 15:00:26,357 - INFO - Response - Page 2:
2025-05-10 15:00:26,557 - INFO - 第 2 页获取到 100 条记录
2025-05-10 15:00:26,557 - INFO - Request Parameters - Page 3:
2025-05-10 15:00:26,557 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 15:00:26,557 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 15:00:27,143 - INFO - Response - Page 3:
2025-05-10 15:00:27,343 - INFO - 第 3 页获取到 100 条记录
2025-05-10 15:00:27,343 - INFO - Request Parameters - Page 4:
2025-05-10 15:00:27,343 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 15:00:27,343 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 15:00:27,931 - INFO - Response - Page 4:
2025-05-10 15:00:28,132 - INFO - 第 4 页获取到 100 条记录
2025-05-10 15:00:28,132 - INFO - Request Parameters - Page 5:
2025-05-10 15:00:28,132 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 15:00:28,132 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 15:00:28,591 - INFO - Response - Page 5:
2025-05-10 15:00:28,791 - INFO - 第 5 页获取到 100 条记录
2025-05-10 15:00:28,791 - INFO - Request Parameters - Page 6:
2025-05-10 15:00:28,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 15:00:28,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 15:00:29,273 - INFO - Response - Page 6:
2025-05-10 15:00:29,473 - INFO - 第 6 页获取到 100 条记录
2025-05-10 15:00:29,473 - INFO - Request Parameters - Page 7:
2025-05-10 15:00:29,473 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 15:00:29,473 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 15:00:29,859 - INFO - Response - Page 7:
2025-05-10 15:00:30,060 - INFO - 第 7 页获取到 21 条记录
2025-05-10 15:00:30,060 - INFO - 查询完成，共获取到 621 条记录
2025-05-10 15:00:30,060 - INFO - 获取到 621 条表单数据
2025-05-10 15:00:30,072 - INFO - 当前日期 2025-05 有 621 条MySQL数据需要处理
2025-05-10 15:00:30,072 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-10 15:00:30,481 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-10 15:00:30,481 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 167196.0, 'new_value': 183172.0}, {'field': 'offline_amount', 'old_value': 96135.0, 'new_value': 103563.0}, {'field': 'total_amount', 'old_value': 263331.0, 'new_value': 286735.0}, {'field': 'order_count', 'old_value': 266, 'new_value': 299}]
2025-05-10 15:00:30,484 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM6H2
2025-05-10 15:00:30,936 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM6H2
2025-05-10 15:00:30,937 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4679.7, 'new_value': 4947.7}, {'field': 'total_amount', 'old_value': 5221.7, 'new_value': 5489.7}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-05-10 15:00:30,938 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-10 15:00:31,478 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-10 15:00:31,478 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 440000.0, 'new_value': 488000.0}, {'field': 'total_amount', 'old_value': 440000.0, 'new_value': 488000.0}, {'field': 'order_count', 'old_value': 327, 'new_value': 328}]
2025-05-10 15:00:31,478 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBL
2025-05-10 15:00:31,925 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBL
2025-05-10 15:00:31,925 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26469.0, 'new_value': 28349.0}, {'field': 'total_amount', 'old_value': 26469.0, 'new_value': 28349.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-05-10 15:00:31,925 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGL
2025-05-10 15:00:32,376 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGL
2025-05-10 15:00:32,376 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26186.0, 'new_value': 26215.9}, {'field': 'total_amount', 'old_value': 26186.0, 'new_value': 26215.9}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-10 15:00:32,377 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-10 15:00:32,964 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-10 15:00:32,964 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59190.5, 'new_value': 61029.5}, {'field': 'total_amount', 'old_value': 59190.5, 'new_value': 61029.5}, {'field': 'order_count', 'old_value': 553, 'new_value': 574}]
2025-05-10 15:00:32,964 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-10 15:00:33,436 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-10 15:00:33,437 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2017437.0, 'new_value': 2241000.0}, {'field': 'total_amount', 'old_value': 2017437.0, 'new_value': 2241000.0}, {'field': 'order_count', 'old_value': 33098, 'new_value': 37123}]
2025-05-10 15:00:33,437 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-10 15:00:33,886 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-10 15:00:33,887 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48228.0, 'new_value': 51404.0}, {'field': 'total_amount', 'old_value': 48228.0, 'new_value': 51404.0}, {'field': 'order_count', 'old_value': 1922, 'new_value': 2042}]
2025-05-10 15:00:33,887 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-10 15:00:34,287 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-10 15:00:34,287 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10396.07, 'new_value': 11444.07}, {'field': 'total_amount', 'old_value': 10396.07, 'new_value': 11444.07}, {'field': 'order_count', 'old_value': 1054, 'new_value': 1151}]
2025-05-10 15:00:34,287 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7M
2025-05-10 15:00:34,776 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7M
2025-05-10 15:00:34,776 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21000.0, 'new_value': 24000.0}, {'field': 'total_amount', 'old_value': 21000.0, 'new_value': 24000.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-05-10 15:00:34,777 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEM
2025-05-10 15:00:35,217 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEM
2025-05-10 15:00:35,217 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16617.4, 'new_value': 18490.2}, {'field': 'total_amount', 'old_value': 16617.4, 'new_value': 18490.2}, {'field': 'order_count', 'old_value': 169, 'new_value': 187}]
2025-05-10 15:00:35,218 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-10 15:00:35,708 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-10 15:00:35,708 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 185068.8, 'new_value': 194994.75}, {'field': 'total_amount', 'old_value': 185068.8, 'new_value': 194994.75}, {'field': 'order_count', 'old_value': 308, 'new_value': 331}]
2025-05-10 15:00:35,708 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-10 15:00:36,140 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-10 15:00:36,140 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41756.0, 'new_value': 41856.0}, {'field': 'total_amount', 'old_value': 41756.0, 'new_value': 41856.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-05-10 15:00:36,140 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-10 15:00:36,560 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-10 15:00:36,561 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10278.0, 'new_value': 11506.0}, {'field': 'total_amount', 'old_value': 11654.0, 'new_value': 12882.0}, {'field': 'order_count', 'old_value': 1282, 'new_value': 1416}]
2025-05-10 15:00:36,562 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMHA
2025-05-10 15:00:37,061 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMHA
2025-05-10 15:00:37,061 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5679.0, 'new_value': 6349.0}, {'field': 'total_amount', 'old_value': 5679.0, 'new_value': 6349.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-05-10 15:00:37,062 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-10 15:00:37,540 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-10 15:00:37,540 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 123800.0, 'new_value': 134800.0}, {'field': 'total_amount', 'old_value': 123800.0, 'new_value': 134800.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-05-10 15:00:37,540 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-10 15:00:37,968 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-10 15:00:37,968 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2306.52, 'new_value': 2993.37}, {'field': 'total_amount', 'old_value': 2306.52, 'new_value': 2993.37}, {'field': 'order_count', 'old_value': 77, 'new_value': 99}]
2025-05-10 15:00:37,969 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-10 15:00:38,432 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-10 15:00:38,432 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104752.0, 'new_value': 117470.0}, {'field': 'total_amount', 'old_value': 118527.0, 'new_value': 131245.0}, {'field': 'order_count', 'old_value': 2456, 'new_value': 2666}]
2025-05-10 15:00:38,436 - INFO - 日期 2025-05 处理完成 - 更新: 18 条，插入: 0 条，错误: 0 条
2025-05-10 15:00:38,436 - INFO - 数据同步完成！更新: 21 条，插入: 0 条，错误: 0 条
2025-05-10 15:00:38,438 - INFO - =================同步完成====================
2025-05-10 18:00:01,900 - INFO - =================使用默认全量同步=============
2025-05-10 18:00:03,231 - INFO - MySQL查询成功，共获取 3288 条记录
2025-05-10 18:00:03,232 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-10 18:00:03,259 - INFO - 开始处理日期: 2025-01
2025-05-10 18:00:03,262 - INFO - Request Parameters - Page 1:
2025-05-10 18:00:03,262 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 18:00:03,262 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 18:00:04,240 - INFO - Response - Page 1:
2025-05-10 18:00:04,440 - INFO - 第 1 页获取到 100 条记录
2025-05-10 18:00:04,440 - INFO - Request Parameters - Page 2:
2025-05-10 18:00:04,440 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 18:00:04,440 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 18:00:04,950 - INFO - Response - Page 2:
2025-05-10 18:00:05,150 - INFO - 第 2 页获取到 100 条记录
2025-05-10 18:00:05,150 - INFO - Request Parameters - Page 3:
2025-05-10 18:00:05,150 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 18:00:05,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 18:00:05,628 - INFO - Response - Page 3:
2025-05-10 18:00:05,829 - INFO - 第 3 页获取到 100 条记录
2025-05-10 18:00:05,829 - INFO - Request Parameters - Page 4:
2025-05-10 18:00:05,829 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 18:00:05,829 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 18:00:06,343 - INFO - Response - Page 4:
2025-05-10 18:00:06,543 - INFO - 第 4 页获取到 100 条记录
2025-05-10 18:00:06,543 - INFO - Request Parameters - Page 5:
2025-05-10 18:00:06,543 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 18:00:06,543 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 18:00:07,305 - INFO - Response - Page 5:
2025-05-10 18:00:07,506 - INFO - 第 5 页获取到 100 条记录
2025-05-10 18:00:07,506 - INFO - Request Parameters - Page 6:
2025-05-10 18:00:07,506 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 18:00:07,506 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 18:00:07,948 - INFO - Response - Page 6:
2025-05-10 18:00:08,148 - INFO - 第 6 页获取到 100 条记录
2025-05-10 18:00:08,148 - INFO - Request Parameters - Page 7:
2025-05-10 18:00:08,148 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 18:00:08,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 18:00:08,606 - INFO - Response - Page 7:
2025-05-10 18:00:08,806 - INFO - 第 7 页获取到 82 条记录
2025-05-10 18:00:08,806 - INFO - 查询完成，共获取到 682 条记录
2025-05-10 18:00:08,806 - INFO - 获取到 682 条表单数据
2025-05-10 18:00:08,818 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-10 18:00:08,830 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-10 18:00:08,830 - INFO - 开始处理日期: 2025-02
2025-05-10 18:00:08,830 - INFO - Request Parameters - Page 1:
2025-05-10 18:00:08,830 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 18:00:08,830 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 18:00:09,333 - INFO - Response - Page 1:
2025-05-10 18:00:09,534 - INFO - 第 1 页获取到 100 条记录
2025-05-10 18:00:09,534 - INFO - Request Parameters - Page 2:
2025-05-10 18:00:09,534 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 18:00:09,534 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 18:00:09,984 - INFO - Response - Page 2:
2025-05-10 18:00:10,185 - INFO - 第 2 页获取到 100 条记录
2025-05-10 18:00:10,185 - INFO - Request Parameters - Page 3:
2025-05-10 18:00:10,185 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 18:00:10,185 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 18:00:10,692 - INFO - Response - Page 3:
2025-05-10 18:00:10,892 - INFO - 第 3 页获取到 100 条记录
2025-05-10 18:00:10,892 - INFO - Request Parameters - Page 4:
2025-05-10 18:00:10,892 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 18:00:10,892 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 18:00:11,431 - INFO - Response - Page 4:
2025-05-10 18:00:11,631 - INFO - 第 4 页获取到 100 条记录
2025-05-10 18:00:11,631 - INFO - Request Parameters - Page 5:
2025-05-10 18:00:11,631 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 18:00:11,631 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 18:00:12,207 - INFO - Response - Page 5:
2025-05-10 18:00:12,409 - INFO - 第 5 页获取到 100 条记录
2025-05-10 18:00:12,409 - INFO - Request Parameters - Page 6:
2025-05-10 18:00:12,409 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 18:00:12,409 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 18:00:12,922 - INFO - Response - Page 6:
2025-05-10 18:00:13,122 - INFO - 第 6 页获取到 100 条记录
2025-05-10 18:00:13,122 - INFO - Request Parameters - Page 7:
2025-05-10 18:00:13,122 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 18:00:13,122 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 18:00:13,558 - INFO - Response - Page 7:
2025-05-10 18:00:13,758 - INFO - 第 7 页获取到 70 条记录
2025-05-10 18:00:13,758 - INFO - 查询完成，共获取到 670 条记录
2025-05-10 18:00:13,758 - INFO - 获取到 670 条表单数据
2025-05-10 18:00:13,771 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-10 18:00:13,784 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-10 18:00:13,784 - INFO - 开始处理日期: 2025-03
2025-05-10 18:00:13,784 - INFO - Request Parameters - Page 1:
2025-05-10 18:00:13,784 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 18:00:13,784 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 18:00:14,228 - INFO - Response - Page 1:
2025-05-10 18:00:14,428 - INFO - 第 1 页获取到 100 条记录
2025-05-10 18:00:14,428 - INFO - Request Parameters - Page 2:
2025-05-10 18:00:14,428 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 18:00:14,428 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 18:00:14,933 - INFO - Response - Page 2:
2025-05-10 18:00:15,134 - INFO - 第 2 页获取到 100 条记录
2025-05-10 18:00:15,134 - INFO - Request Parameters - Page 3:
2025-05-10 18:00:15,134 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 18:00:15,134 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 18:00:15,662 - INFO - Response - Page 3:
2025-05-10 18:00:15,864 - INFO - 第 3 页获取到 100 条记录
2025-05-10 18:00:15,864 - INFO - Request Parameters - Page 4:
2025-05-10 18:00:15,864 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 18:00:15,864 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 18:00:16,329 - INFO - Response - Page 4:
2025-05-10 18:00:16,530 - INFO - 第 4 页获取到 100 条记录
2025-05-10 18:00:16,530 - INFO - Request Parameters - Page 5:
2025-05-10 18:00:16,530 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 18:00:16,530 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 18:00:17,032 - INFO - Response - Page 5:
2025-05-10 18:00:17,232 - INFO - 第 5 页获取到 100 条记录
2025-05-10 18:00:17,232 - INFO - Request Parameters - Page 6:
2025-05-10 18:00:17,232 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 18:00:17,232 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 18:00:17,735 - INFO - Response - Page 6:
2025-05-10 18:00:17,935 - INFO - 第 6 页获取到 100 条记录
2025-05-10 18:00:17,935 - INFO - Request Parameters - Page 7:
2025-05-10 18:00:17,935 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 18:00:17,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 18:00:18,409 - INFO - Response - Page 7:
2025-05-10 18:00:18,609 - INFO - 第 7 页获取到 61 条记录
2025-05-10 18:00:18,609 - INFO - 查询完成，共获取到 661 条记录
2025-05-10 18:00:18,609 - INFO - 获取到 661 条表单数据
2025-05-10 18:00:18,621 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-10 18:00:18,633 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-10 18:00:18,633 - INFO - 开始处理日期: 2025-04
2025-05-10 18:00:18,633 - INFO - Request Parameters - Page 1:
2025-05-10 18:00:18,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 18:00:18,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 18:00:19,141 - INFO - Response - Page 1:
2025-05-10 18:00:19,342 - INFO - 第 1 页获取到 100 条记录
2025-05-10 18:00:19,342 - INFO - Request Parameters - Page 2:
2025-05-10 18:00:19,342 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 18:00:19,342 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 18:00:19,930 - INFO - Response - Page 2:
2025-05-10 18:00:20,130 - INFO - 第 2 页获取到 100 条记录
2025-05-10 18:00:20,130 - INFO - Request Parameters - Page 3:
2025-05-10 18:00:20,130 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 18:00:20,130 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 18:00:20,607 - INFO - Response - Page 3:
2025-05-10 18:00:20,807 - INFO - 第 3 页获取到 100 条记录
2025-05-10 18:00:20,807 - INFO - Request Parameters - Page 4:
2025-05-10 18:00:20,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 18:00:20,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 18:00:21,305 - INFO - Response - Page 4:
2025-05-10 18:00:21,505 - INFO - 第 4 页获取到 100 条记录
2025-05-10 18:00:21,505 - INFO - Request Parameters - Page 5:
2025-05-10 18:00:21,505 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 18:00:21,505 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 18:00:21,956 - INFO - Response - Page 5:
2025-05-10 18:00:22,157 - INFO - 第 5 页获取到 100 条记录
2025-05-10 18:00:22,157 - INFO - Request Parameters - Page 6:
2025-05-10 18:00:22,157 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 18:00:22,157 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 18:00:22,617 - INFO - Response - Page 6:
2025-05-10 18:00:22,817 - INFO - 第 6 页获取到 100 条记录
2025-05-10 18:00:22,817 - INFO - Request Parameters - Page 7:
2025-05-10 18:00:22,817 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 18:00:22,817 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 18:00:23,361 - INFO - Response - Page 7:
2025-05-10 18:00:23,561 - INFO - 第 7 页获取到 54 条记录
2025-05-10 18:00:23,561 - INFO - 查询完成，共获取到 654 条记录
2025-05-10 18:00:23,561 - INFO - 获取到 654 条表单数据
2025-05-10 18:00:23,575 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-10 18:00:23,586 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-10 18:00:23,586 - INFO - 开始处理日期: 2025-05
2025-05-10 18:00:23,587 - INFO - Request Parameters - Page 1:
2025-05-10 18:00:23,587 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 18:00:23,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 18:00:24,132 - INFO - Response - Page 1:
2025-05-10 18:00:24,332 - INFO - 第 1 页获取到 100 条记录
2025-05-10 18:00:24,332 - INFO - Request Parameters - Page 2:
2025-05-10 18:00:24,332 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 18:00:24,332 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 18:00:24,804 - INFO - Response - Page 2:
2025-05-10 18:00:25,005 - INFO - 第 2 页获取到 100 条记录
2025-05-10 18:00:25,005 - INFO - Request Parameters - Page 3:
2025-05-10 18:00:25,005 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 18:00:25,005 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 18:00:25,553 - INFO - Response - Page 3:
2025-05-10 18:00:25,753 - INFO - 第 3 页获取到 100 条记录
2025-05-10 18:00:25,753 - INFO - Request Parameters - Page 4:
2025-05-10 18:00:25,753 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 18:00:25,753 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 18:00:26,241 - INFO - Response - Page 4:
2025-05-10 18:00:26,441 - INFO - 第 4 页获取到 100 条记录
2025-05-10 18:00:26,441 - INFO - Request Parameters - Page 5:
2025-05-10 18:00:26,441 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 18:00:26,441 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 18:00:26,902 - INFO - Response - Page 5:
2025-05-10 18:00:27,102 - INFO - 第 5 页获取到 100 条记录
2025-05-10 18:00:27,102 - INFO - Request Parameters - Page 6:
2025-05-10 18:00:27,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 18:00:27,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 18:00:27,618 - INFO - Response - Page 6:
2025-05-10 18:00:27,819 - INFO - 第 6 页获取到 100 条记录
2025-05-10 18:00:27,819 - INFO - Request Parameters - Page 7:
2025-05-10 18:00:27,819 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 18:00:27,819 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 18:00:28,139 - INFO - Response - Page 7:
2025-05-10 18:00:28,340 - INFO - 第 7 页获取到 21 条记录
2025-05-10 18:00:28,340 - INFO - 查询完成，共获取到 621 条记录
2025-05-10 18:00:28,340 - INFO - 获取到 621 条表单数据
2025-05-10 18:00:28,352 - INFO - 当前日期 2025-05 有 621 条MySQL数据需要处理
2025-05-10 18:00:28,353 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-10 18:00:28,848 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-10 18:00:28,848 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48470.0, 'new_value': 52693.0}, {'field': 'offline_amount', 'old_value': 33087.28, 'new_value': 38769.28}, {'field': 'total_amount', 'old_value': 81557.28, 'new_value': 91462.28}, {'field': 'order_count', 'old_value': 1723, 'new_value': 1936}]
2025-05-10 18:00:28,852 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-10 18:00:29,381 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-10 18:00:29,381 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18050.0, 'new_value': 19530.0}, {'field': 'total_amount', 'old_value': 18050.0, 'new_value': 19530.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-10 18:00:29,382 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-10 18:00:29,813 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-10 18:00:29,813 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4230900.0, 'new_value': 4268400.0}, {'field': 'total_amount', 'old_value': 4230900.0, 'new_value': 4268400.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 52}]
2025-05-10 18:00:29,816 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4E
2025-05-10 18:00:30,288 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4E
2025-05-10 18:00:30,288 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32467.0, 'new_value': 37024.0}, {'field': 'total_amount', 'old_value': 32467.0, 'new_value': 37024.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-10 18:00:30,289 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-10 18:00:30,736 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-10 18:00:30,737 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 157495.39, 'new_value': 169082.39}, {'field': 'total_amount', 'old_value': 157495.39, 'new_value': 169082.39}, {'field': 'order_count', 'old_value': 999, 'new_value': 1066}]
2025-05-10 18:00:30,741 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-10 18:00:31,182 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-10 18:00:31,182 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60519.24, 'new_value': 65442.6}, {'field': 'total_amount', 'old_value': 60519.24, 'new_value': 65442.6}, {'field': 'order_count', 'old_value': 441, 'new_value': 488}]
2025-05-10 18:00:31,183 - INFO - 日期 2025-05 处理完成 - 更新: 6 条，插入: 0 条，错误: 0 条
2025-05-10 18:00:31,183 - INFO - 数据同步完成！更新: 6 条，插入: 0 条，错误: 0 条
2025-05-10 18:00:31,184 - INFO - =================同步完成====================
2025-05-10 21:00:02,027 - INFO - =================使用默认全量同步=============
2025-05-10 21:00:03,350 - INFO - MySQL查询成功，共获取 3288 条记录
2025-05-10 21:00:03,351 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-10 21:00:03,377 - INFO - 开始处理日期: 2025-01
2025-05-10 21:00:03,380 - INFO - Request Parameters - Page 1:
2025-05-10 21:00:03,381 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 21:00:03,381 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 21:00:04,317 - INFO - Response - Page 1:
2025-05-10 21:00:04,518 - INFO - 第 1 页获取到 100 条记录
2025-05-10 21:00:04,518 - INFO - Request Parameters - Page 2:
2025-05-10 21:00:04,518 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 21:00:04,519 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 21:00:05,017 - INFO - Response - Page 2:
2025-05-10 21:00:05,218 - INFO - 第 2 页获取到 100 条记录
2025-05-10 21:00:05,218 - INFO - Request Parameters - Page 3:
2025-05-10 21:00:05,218 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 21:00:05,218 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 21:00:05,902 - INFO - Response - Page 3:
2025-05-10 21:00:06,103 - INFO - 第 3 页获取到 100 条记录
2025-05-10 21:00:06,103 - INFO - Request Parameters - Page 4:
2025-05-10 21:00:06,103 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 21:00:06,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 21:00:06,601 - INFO - Response - Page 4:
2025-05-10 21:00:06,801 - INFO - 第 4 页获取到 100 条记录
2025-05-10 21:00:06,801 - INFO - Request Parameters - Page 5:
2025-05-10 21:00:06,801 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 21:00:06,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 21:00:07,364 - INFO - Response - Page 5:
2025-05-10 21:00:07,565 - INFO - 第 5 页获取到 100 条记录
2025-05-10 21:00:07,565 - INFO - Request Parameters - Page 6:
2025-05-10 21:00:07,565 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 21:00:07,565 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 21:00:08,106 - INFO - Response - Page 6:
2025-05-10 21:00:08,306 - INFO - 第 6 页获取到 100 条记录
2025-05-10 21:00:08,306 - INFO - Request Parameters - Page 7:
2025-05-10 21:00:08,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 21:00:08,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 21:00:08,797 - INFO - Response - Page 7:
2025-05-10 21:00:08,997 - INFO - 第 7 页获取到 82 条记录
2025-05-10 21:00:08,997 - INFO - 查询完成，共获取到 682 条记录
2025-05-10 21:00:08,997 - INFO - 获取到 682 条表单数据
2025-05-10 21:00:09,010 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-10 21:00:09,022 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-10 21:00:09,022 - INFO - 开始处理日期: 2025-02
2025-05-10 21:00:09,023 - INFO - Request Parameters - Page 1:
2025-05-10 21:00:09,023 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 21:00:09,023 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 21:00:09,518 - INFO - Response - Page 1:
2025-05-10 21:00:09,719 - INFO - 第 1 页获取到 100 条记录
2025-05-10 21:00:09,719 - INFO - Request Parameters - Page 2:
2025-05-10 21:00:09,719 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 21:00:09,719 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 21:00:10,200 - INFO - Response - Page 2:
2025-05-10 21:00:10,401 - INFO - 第 2 页获取到 100 条记录
2025-05-10 21:00:10,401 - INFO - Request Parameters - Page 3:
2025-05-10 21:00:10,401 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 21:00:10,401 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 21:00:10,887 - INFO - Response - Page 3:
2025-05-10 21:00:11,087 - INFO - 第 3 页获取到 100 条记录
2025-05-10 21:00:11,087 - INFO - Request Parameters - Page 4:
2025-05-10 21:00:11,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 21:00:11,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 21:00:11,556 - INFO - Response - Page 4:
2025-05-10 21:00:11,757 - INFO - 第 4 页获取到 100 条记录
2025-05-10 21:00:11,757 - INFO - Request Parameters - Page 5:
2025-05-10 21:00:11,757 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 21:00:11,757 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 21:00:12,205 - INFO - Response - Page 5:
2025-05-10 21:00:12,406 - INFO - 第 5 页获取到 100 条记录
2025-05-10 21:00:12,406 - INFO - Request Parameters - Page 6:
2025-05-10 21:00:12,406 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 21:00:12,406 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 21:00:12,980 - INFO - Response - Page 6:
2025-05-10 21:00:13,180 - INFO - 第 6 页获取到 100 条记录
2025-05-10 21:00:13,180 - INFO - Request Parameters - Page 7:
2025-05-10 21:00:13,180 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 21:00:13,180 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 21:00:13,708 - INFO - Response - Page 7:
2025-05-10 21:00:13,908 - INFO - 第 7 页获取到 70 条记录
2025-05-10 21:00:13,908 - INFO - 查询完成，共获取到 670 条记录
2025-05-10 21:00:13,908 - INFO - 获取到 670 条表单数据
2025-05-10 21:00:13,920 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-10 21:00:13,932 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-10 21:00:13,933 - INFO - 开始处理日期: 2025-03
2025-05-10 21:00:13,933 - INFO - Request Parameters - Page 1:
2025-05-10 21:00:13,933 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 21:00:13,933 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 21:00:14,371 - INFO - Response - Page 1:
2025-05-10 21:00:14,572 - INFO - 第 1 页获取到 100 条记录
2025-05-10 21:00:14,572 - INFO - Request Parameters - Page 2:
2025-05-10 21:00:14,572 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 21:00:14,572 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 21:00:15,036 - INFO - Response - Page 2:
2025-05-10 21:00:15,236 - INFO - 第 2 页获取到 100 条记录
2025-05-10 21:00:15,236 - INFO - Request Parameters - Page 3:
2025-05-10 21:00:15,236 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 21:00:15,236 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 21:00:15,701 - INFO - Response - Page 3:
2025-05-10 21:00:15,901 - INFO - 第 3 页获取到 100 条记录
2025-05-10 21:00:15,901 - INFO - Request Parameters - Page 4:
2025-05-10 21:00:15,901 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 21:00:15,901 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 21:00:16,396 - INFO - Response - Page 4:
2025-05-10 21:00:16,597 - INFO - 第 4 页获取到 100 条记录
2025-05-10 21:00:16,597 - INFO - Request Parameters - Page 5:
2025-05-10 21:00:16,597 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 21:00:16,597 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 21:00:17,089 - INFO - Response - Page 5:
2025-05-10 21:00:17,289 - INFO - 第 5 页获取到 100 条记录
2025-05-10 21:00:17,289 - INFO - Request Parameters - Page 6:
2025-05-10 21:00:17,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 21:00:17,289 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 21:00:17,752 - INFO - Response - Page 6:
2025-05-10 21:00:17,952 - INFO - 第 6 页获取到 100 条记录
2025-05-10 21:00:17,952 - INFO - Request Parameters - Page 7:
2025-05-10 21:00:17,952 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 21:00:17,952 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 21:00:18,437 - INFO - Response - Page 7:
2025-05-10 21:00:18,638 - INFO - 第 7 页获取到 61 条记录
2025-05-10 21:00:18,638 - INFO - 查询完成，共获取到 661 条记录
2025-05-10 21:00:18,638 - INFO - 获取到 661 条表单数据
2025-05-10 21:00:18,650 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-10 21:00:18,662 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-10 21:00:18,662 - INFO - 开始处理日期: 2025-04
2025-05-10 21:00:18,662 - INFO - Request Parameters - Page 1:
2025-05-10 21:00:18,662 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 21:00:18,662 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 21:00:19,100 - INFO - Response - Page 1:
2025-05-10 21:00:19,301 - INFO - 第 1 页获取到 100 条记录
2025-05-10 21:00:19,301 - INFO - Request Parameters - Page 2:
2025-05-10 21:00:19,301 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 21:00:19,301 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 21:00:19,826 - INFO - Response - Page 2:
2025-05-10 21:00:20,026 - INFO - 第 2 页获取到 100 条记录
2025-05-10 21:00:20,026 - INFO - Request Parameters - Page 3:
2025-05-10 21:00:20,026 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 21:00:20,026 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 21:00:20,460 - INFO - Response - Page 3:
2025-05-10 21:00:20,660 - INFO - 第 3 页获取到 100 条记录
2025-05-10 21:00:20,660 - INFO - Request Parameters - Page 4:
2025-05-10 21:00:20,660 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 21:00:20,660 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 21:00:21,128 - INFO - Response - Page 4:
2025-05-10 21:00:21,328 - INFO - 第 4 页获取到 100 条记录
2025-05-10 21:00:21,328 - INFO - Request Parameters - Page 5:
2025-05-10 21:00:21,328 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 21:00:21,328 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 21:00:21,847 - INFO - Response - Page 5:
2025-05-10 21:00:22,048 - INFO - 第 5 页获取到 100 条记录
2025-05-10 21:00:22,048 - INFO - Request Parameters - Page 6:
2025-05-10 21:00:22,048 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 21:00:22,048 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 21:00:22,513 - INFO - Response - Page 6:
2025-05-10 21:00:22,713 - INFO - 第 6 页获取到 100 条记录
2025-05-10 21:00:22,713 - INFO - Request Parameters - Page 7:
2025-05-10 21:00:22,713 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 21:00:22,713 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 21:00:23,085 - INFO - Response - Page 7:
2025-05-10 21:00:23,285 - INFO - 第 7 页获取到 54 条记录
2025-05-10 21:00:23,285 - INFO - 查询完成，共获取到 654 条记录
2025-05-10 21:00:23,285 - INFO - 获取到 654 条表单数据
2025-05-10 21:00:23,298 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-10 21:00:23,311 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-10 21:00:23,311 - INFO - 开始处理日期: 2025-05
2025-05-10 21:00:23,311 - INFO - Request Parameters - Page 1:
2025-05-10 21:00:23,311 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 21:00:23,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 21:00:23,736 - INFO - Response - Page 1:
2025-05-10 21:00:23,937 - INFO - 第 1 页获取到 100 条记录
2025-05-10 21:00:23,937 - INFO - Request Parameters - Page 2:
2025-05-10 21:00:23,937 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 21:00:23,937 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 21:00:24,408 - INFO - Response - Page 2:
2025-05-10 21:00:24,609 - INFO - 第 2 页获取到 100 条记录
2025-05-10 21:00:24,609 - INFO - Request Parameters - Page 3:
2025-05-10 21:00:24,609 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 21:00:24,609 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 21:00:25,129 - INFO - Response - Page 3:
2025-05-10 21:00:25,329 - INFO - 第 3 页获取到 100 条记录
2025-05-10 21:00:25,329 - INFO - Request Parameters - Page 4:
2025-05-10 21:00:25,329 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 21:00:25,329 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 21:00:25,786 - INFO - Response - Page 4:
2025-05-10 21:00:25,986 - INFO - 第 4 页获取到 100 条记录
2025-05-10 21:00:25,986 - INFO - Request Parameters - Page 5:
2025-05-10 21:00:25,986 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 21:00:25,986 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 21:00:26,514 - INFO - Response - Page 5:
2025-05-10 21:00:26,714 - INFO - 第 5 页获取到 100 条记录
2025-05-10 21:00:26,714 - INFO - Request Parameters - Page 6:
2025-05-10 21:00:26,714 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 21:00:26,714 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 21:00:27,189 - INFO - Response - Page 6:
2025-05-10 21:00:27,390 - INFO - 第 6 页获取到 100 条记录
2025-05-10 21:00:27,390 - INFO - Request Parameters - Page 7:
2025-05-10 21:00:27,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-10 21:00:27,390 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-10 21:00:27,724 - INFO - Response - Page 7:
2025-05-10 21:00:27,924 - INFO - 第 7 页获取到 21 条记录
2025-05-10 21:00:27,924 - INFO - 查询完成，共获取到 621 条记录
2025-05-10 21:00:27,924 - INFO - 获取到 621 条表单数据
2025-05-10 21:00:27,935 - INFO - 当前日期 2025-05 有 621 条MySQL数据需要处理
2025-05-10 21:00:27,937 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-10 21:00:28,412 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-10 21:00:28,412 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30816.0, 'new_value': 33794.0}, {'field': 'offline_amount', 'old_value': 39177.0, 'new_value': 43188.0}, {'field': 'total_amount', 'old_value': 69993.0, 'new_value': 76982.0}, {'field': 'order_count', 'old_value': 1695, 'new_value': 1862}]
2025-05-10 21:00:28,423 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-10 21:00:28,423 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-05-10 21:00:28,424 - INFO - =================同步完成====================
