2025-05-01 00:30:34,391 - INFO - 使用默认增量同步（当天更新数据）
2025-05-01 00:30:34,391 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 00:30:34,391 - INFO - 查询参数: ('2025-05-01',)
2025-05-01 00:30:34,454 - INFO - MySQL查询成功，增量数据（日期: 2025-05-01），共获取 1 条记录
2025-05-01 00:30:34,454 - INFO - 获取到 1 个日期需要处理: ['2025-04-30']
2025-05-01 00:30:34,454 - INFO - 开始处理日期: 2025-04-30
2025-05-01 00:30:34,454 - INFO - Request Parameters - Page 1:
2025-05-01 00:30:34,454 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 00:30:34,454 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 00:30:42,571 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A35BE224-E63D-71C7-9A0F-8590F424DA5E Response: {'code': 'ServiceUnavailable', 'requestid': 'A35BE224-E63D-71C7-9A0F-8590F424DA5E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A35BE224-E63D-71C7-9A0F-8590F424DA5E)
2025-05-01 00:30:42,571 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-01 00:31:42,645 - INFO - 开始同步昨天与今天的销售数据: 20250430 至 20250501
2025-05-01 00:31:42,645 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 00:31:42,645 - INFO - 查询参数: ('20250430', '20250501')
2025-05-01 00:31:42,692 - INFO - MySQL查询成功，时间段: 20250430 至 20250501，共获取 0 条记录
2025-05-01 00:31:42,692 - ERROR - 未获取到MySQL数据
2025-05-01 00:31:42,692 - INFO - 同步完成
2025-05-01 01:30:34,351 - INFO - 使用默认增量同步（当天更新数据）
2025-05-01 01:30:34,351 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 01:30:34,351 - INFO - 查询参数: ('2025-05-01',)
2025-05-01 01:30:34,413 - INFO - MySQL查询成功，增量数据（日期: 2025-05-01），共获取 9 条记录
2025-05-01 01:30:34,413 - INFO - 获取到 1 个日期需要处理: ['2025-04-30']
2025-05-01 01:30:34,413 - INFO - 开始处理日期: 2025-04-30
2025-05-01 01:30:34,413 - INFO - Request Parameters - Page 1:
2025-05-01 01:30:34,413 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 01:30:34,413 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 01:30:42,531 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E79B9EC9-6536-703F-A757-F271BC919632 Response: {'code': 'ServiceUnavailable', 'requestid': 'E79B9EC9-6536-703F-A757-F271BC919632', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E79B9EC9-6536-703F-A757-F271BC919632)
2025-05-01 01:30:42,531 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-01 01:31:42,605 - INFO - 开始同步昨天与今天的销售数据: 20250430 至 20250501
2025-05-01 01:31:42,605 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 01:31:42,605 - INFO - 查询参数: ('20250430', '20250501')
2025-05-01 01:31:42,652 - INFO - MySQL查询成功，时间段: 20250430 至 20250501，共获取 0 条记录
2025-05-01 01:31:42,652 - ERROR - 未获取到MySQL数据
2025-05-01 01:31:42,652 - INFO - 同步完成
2025-05-01 02:30:34,373 - INFO - 使用默认增量同步（当天更新数据）
2025-05-01 02:30:34,373 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 02:30:34,373 - INFO - 查询参数: ('2025-05-01',)
2025-05-01 02:30:34,420 - INFO - MySQL查询成功，增量数据（日期: 2025-05-01），共获取 9 条记录
2025-05-01 02:30:34,420 - INFO - 获取到 1 个日期需要处理: ['2025-04-30']
2025-05-01 02:30:34,420 - INFO - 开始处理日期: 2025-04-30
2025-05-01 02:30:34,420 - INFO - Request Parameters - Page 1:
2025-05-01 02:30:34,420 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 02:30:34,420 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 02:30:42,569 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: FDE75940-74BD-755B-8AE7-1B7116113EF1 Response: {'code': 'ServiceUnavailable', 'requestid': 'FDE75940-74BD-755B-8AE7-1B7116113EF1', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: FDE75940-74BD-755B-8AE7-1B7116113EF1)
2025-05-01 02:30:42,569 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-01 02:31:42,643 - INFO - 开始同步昨天与今天的销售数据: 20250430 至 20250501
2025-05-01 02:31:42,643 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 02:31:42,643 - INFO - 查询参数: ('20250430', '20250501')
2025-05-01 02:31:42,690 - INFO - MySQL查询成功，时间段: 20250430 至 20250501，共获取 0 条记录
2025-05-01 02:31:42,690 - ERROR - 未获取到MySQL数据
2025-05-01 02:31:42,690 - INFO - 同步完成
2025-05-01 03:30:34,349 - INFO - 使用默认增量同步（当天更新数据）
2025-05-01 03:30:34,349 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 03:30:34,349 - INFO - 查询参数: ('2025-05-01',)
2025-05-01 03:30:34,411 - INFO - MySQL查询成功，增量数据（日期: 2025-05-01），共获取 9 条记录
2025-05-01 03:30:34,411 - INFO - 获取到 1 个日期需要处理: ['2025-04-30']
2025-05-01 03:30:34,411 - INFO - 开始处理日期: 2025-04-30
2025-05-01 03:30:34,411 - INFO - Request Parameters - Page 1:
2025-05-01 03:30:34,411 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 03:30:34,411 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 03:30:35,475 - INFO - Response - Page 1:
2025-05-01 03:30:35,475 - INFO - 第 1 页获取到 100 条记录
2025-05-01 03:30:35,678 - INFO - Request Parameters - Page 2:
2025-05-01 03:30:35,678 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 03:30:35,678 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 03:30:36,179 - INFO - Response - Page 2:
2025-05-01 03:30:36,179 - INFO - 第 2 页获取到 11 条记录
2025-05-01 03:30:36,382 - INFO - 查询完成，共获取到 111 条记录
2025-05-01 03:30:36,382 - INFO - 获取到 111 条表单数据
2025-05-01 03:30:36,382 - INFO - 当前日期 2025-04-30 有 9 条MySQL数据需要处理
2025-05-01 03:30:36,382 - INFO - 开始更新记录 - 表单实例ID: FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM14
2025-05-01 03:30:36,820 - INFO - 更新表单数据成功: FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM14
2025-05-01 03:30:36,820 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1474.58, 'new_value': 1559.92}, {'field': 'offline_amount', 'old_value': 1589.0, 'new_value': 1571.5}, {'field': 'total_amount', 'old_value': 3063.58, 'new_value': 3131.42}]
2025-05-01 03:30:36,820 - INFO - 开始批量插入 8 条新记录
2025-05-01 03:30:36,992 - INFO - 批量插入响应状态码: 200
2025-05-01 03:30:36,992 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 30 Apr 2025 19:30:21 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '389', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '9C6A321F-7047-7BA9-9FE1-9F4ED3CCF986', 'x-acs-trace-id': '6adf78381ff9a22643bd6a4b05285e5c', 'etag': '3Oi9xyimzI7Tw7Nhx7WiuNg9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-01 03:30:36,992 - INFO - 批量插入响应体: {'result': ['FINST-XL666BD1DW0V9NKMBUAV0DPKG7FX2X3BZB4AMT', 'FINST-XL666BD1DW0V9NKMBUAV0DPKG7FX2X3BZB4AMU', 'FINST-XL666BD1DW0V9NKMBUAV0DPKG7FX2X3BZB4AMV', 'FINST-XL666BD1DW0V9NKMBUAV0DPKG7FX2X3BZB4AMW', 'FINST-XL666BD1DW0V9NKMBUAV0DPKG7FX2X3BZB4AMX', 'FINST-XL666BD1DW0V9NKMBUAV0DPKG7FX2X3BZB4AMY', 'FINST-XL666BD1DW0V9NKMBUAV0DPKG7FX2X3BZB4AMZ', 'FINST-XL666BD1DW0V9NKMBUAV0DPKG7FX2X3BZB4AM01']}
2025-05-01 03:30:36,992 - INFO - 批量插入表单数据成功，批次 1，共 8 条记录
2025-05-01 03:30:36,992 - INFO - 成功插入的数据ID: ['FINST-XL666BD1DW0V9NKMBUAV0DPKG7FX2X3BZB4AMT', 'FINST-XL666BD1DW0V9NKMBUAV0DPKG7FX2X3BZB4AMU', 'FINST-XL666BD1DW0V9NKMBUAV0DPKG7FX2X3BZB4AMV', 'FINST-XL666BD1DW0V9NKMBUAV0DPKG7FX2X3BZB4AMW', 'FINST-XL666BD1DW0V9NKMBUAV0DPKG7FX2X3BZB4AMX', 'FINST-XL666BD1DW0V9NKMBUAV0DPKG7FX2X3BZB4AMY', 'FINST-XL666BD1DW0V9NKMBUAV0DPKG7FX2X3BZB4AMZ', 'FINST-XL666BD1DW0V9NKMBUAV0DPKG7FX2X3BZB4AM01']
2025-05-01 03:30:42,012 - INFO - 批量插入完成，共 8 条记录
2025-05-01 03:30:42,012 - INFO - 日期 2025-04-30 处理完成 - 更新: 1 条，插入: 8 条，错误: 0 条
2025-05-01 03:30:42,012 - INFO - 数据同步完成！更新: 1 条，插入: 8 条，错误: 0 条
2025-05-01 03:31:42,087 - INFO - 开始同步昨天与今天的销售数据: 20250430 至 20250501
2025-05-01 03:31:42,087 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 03:31:42,087 - INFO - 查询参数: ('20250430', '20250501')
2025-05-01 03:31:42,134 - INFO - MySQL查询成功，时间段: 20250430 至 20250501，共获取 0 条记录
2025-05-01 03:31:42,134 - ERROR - 未获取到MySQL数据
2025-05-01 03:31:42,134 - INFO - 同步完成
2025-05-01 04:30:34,527 - INFO - 使用默认增量同步（当天更新数据）
2025-05-01 04:30:34,527 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 04:30:34,527 - INFO - 查询参数: ('2025-05-01',)
2025-05-01 04:30:34,574 - INFO - MySQL查询成功，增量数据（日期: 2025-05-01），共获取 9 条记录
2025-05-01 04:30:34,574 - INFO - 获取到 1 个日期需要处理: ['2025-04-30']
2025-05-01 04:30:34,574 - INFO - 开始处理日期: 2025-04-30
2025-05-01 04:30:34,590 - INFO - Request Parameters - Page 1:
2025-05-01 04:30:34,590 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 04:30:34,590 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 04:30:42,707 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 50D837EB-90F0-7144-9FB8-D46EECA9FBEC Response: {'code': 'ServiceUnavailable', 'requestid': '50D837EB-90F0-7144-9FB8-D46EECA9FBEC', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 50D837EB-90F0-7144-9FB8-D46EECA9FBEC)
2025-05-01 04:30:42,707 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-01 04:31:42,782 - INFO - 开始同步昨天与今天的销售数据: 20250430 至 20250501
2025-05-01 04:31:42,782 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 04:31:42,782 - INFO - 查询参数: ('20250430', '20250501')
2025-05-01 04:31:42,829 - INFO - MySQL查询成功，时间段: 20250430 至 20250501，共获取 0 条记录
2025-05-01 04:31:42,829 - ERROR - 未获取到MySQL数据
2025-05-01 04:31:42,829 - INFO - 同步完成
2025-05-01 05:30:34,394 - INFO - 使用默认增量同步（当天更新数据）
2025-05-01 05:30:34,394 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 05:30:34,394 - INFO - 查询参数: ('2025-05-01',)
2025-05-01 05:30:34,440 - INFO - MySQL查询成功，增量数据（日期: 2025-05-01），共获取 9 条记录
2025-05-01 05:30:34,440 - INFO - 获取到 1 个日期需要处理: ['2025-04-30']
2025-05-01 05:30:34,440 - INFO - 开始处理日期: 2025-04-30
2025-05-01 05:30:34,440 - INFO - Request Parameters - Page 1:
2025-05-01 05:30:34,440 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 05:30:34,440 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 05:30:42,573 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A0860BC4-C4A5-7F2D-A06D-80F27FF55101 Response: {'code': 'ServiceUnavailable', 'requestid': 'A0860BC4-C4A5-7F2D-A06D-80F27FF55101', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A0860BC4-C4A5-7F2D-A06D-80F27FF55101)
2025-05-01 05:30:42,573 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-01 05:31:42,648 - INFO - 开始同步昨天与今天的销售数据: 20250430 至 20250501
2025-05-01 05:31:42,648 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 05:31:42,648 - INFO - 查询参数: ('20250430', '20250501')
2025-05-01 05:31:42,695 - INFO - MySQL查询成功，时间段: 20250430 至 20250501，共获取 0 条记录
2025-05-01 05:31:42,695 - ERROR - 未获取到MySQL数据
2025-05-01 05:31:42,695 - INFO - 同步完成
2025-05-01 06:30:34,487 - INFO - 使用默认增量同步（当天更新数据）
2025-05-01 06:30:34,487 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 06:30:34,487 - INFO - 查询参数: ('2025-05-01',)
2025-05-01 06:30:34,534 - INFO - MySQL查询成功，增量数据（日期: 2025-05-01），共获取 9 条记录
2025-05-01 06:30:34,534 - INFO - 获取到 1 个日期需要处理: ['2025-04-30']
2025-05-01 06:30:34,534 - INFO - 开始处理日期: 2025-04-30
2025-05-01 06:30:34,534 - INFO - Request Parameters - Page 1:
2025-05-01 06:30:34,534 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 06:30:34,534 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 06:30:42,652 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D47805E4-6B1E-73EA-A007-E0802F4E180C Response: {'code': 'ServiceUnavailable', 'requestid': 'D47805E4-6B1E-73EA-A007-E0802F4E180C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D47805E4-6B1E-73EA-A007-E0802F4E180C)
2025-05-01 06:30:42,652 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-01 06:31:42,731 - INFO - 开始同步昨天与今天的销售数据: 20250430 至 20250501
2025-05-01 06:31:42,731 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 06:31:42,731 - INFO - 查询参数: ('20250430', '20250501')
2025-05-01 06:31:42,778 - INFO - MySQL查询成功，时间段: 20250430 至 20250501，共获取 0 条记录
2025-05-01 06:31:42,778 - ERROR - 未获取到MySQL数据
2025-05-01 06:31:42,778 - INFO - 同步完成
2025-05-01 07:30:33,777 - INFO - 使用默认增量同步（当天更新数据）
2025-05-01 07:30:33,793 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 07:30:33,793 - INFO - 查询参数: ('2025-05-01',)
2025-05-01 07:30:33,840 - INFO - MySQL查询成功，增量数据（日期: 2025-05-01），共获取 13 条记录
2025-05-01 07:30:33,840 - INFO - 获取到 1 个日期需要处理: ['2025-04-30']
2025-05-01 07:30:33,840 - INFO - 开始处理日期: 2025-04-30
2025-05-01 07:30:33,840 - INFO - Request Parameters - Page 1:
2025-05-01 07:30:33,840 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 07:30:33,840 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 07:30:41,965 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3A5CEE8A-BD29-7824-94A8-4AB67D50A2CD Response: {'code': 'ServiceUnavailable', 'requestid': '3A5CEE8A-BD29-7824-94A8-4AB67D50A2CD', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3A5CEE8A-BD29-7824-94A8-4AB67D50A2CD)
2025-05-01 07:30:41,965 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-01 07:31:41,980 - INFO - 开始同步昨天与今天的销售数据: 20250430 至 20250501
2025-05-01 07:31:41,980 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 07:31:41,980 - INFO - 查询参数: ('20250430', '20250501')
2025-05-01 07:31:42,027 - INFO - MySQL查询成功，时间段: 20250430 至 20250501，共获取 0 条记录
2025-05-01 07:31:42,027 - ERROR - 未获取到MySQL数据
2025-05-01 07:31:42,027 - INFO - 同步完成
2025-05-01 08:30:33,755 - INFO - 使用默认增量同步（当天更新数据）
2025-05-01 08:30:33,755 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 08:30:33,755 - INFO - 查询参数: ('2025-05-01',)
2025-05-01 08:30:33,802 - INFO - MySQL查询成功，增量数据（日期: 2025-05-01），共获取 19 条记录
2025-05-01 08:30:33,802 - INFO - 获取到 1 个日期需要处理: ['2025-04-30']
2025-05-01 08:30:33,802 - INFO - 开始处理日期: 2025-04-30
2025-05-01 08:30:33,817 - INFO - Request Parameters - Page 1:
2025-05-01 08:30:33,817 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 08:30:33,817 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 08:30:41,942 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 1320C170-D5C8-7C90-A1FE-45B0090BBFB3 Response: {'code': 'ServiceUnavailable', 'requestid': '1320C170-D5C8-7C90-A1FE-45B0090BBFB3', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 1320C170-D5C8-7C90-A1FE-45B0090BBFB3)
2025-05-01 08:30:41,942 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-01 08:31:41,958 - INFO - 开始同步昨天与今天的销售数据: 20250430 至 20250501
2025-05-01 08:31:41,958 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 08:31:41,958 - INFO - 查询参数: ('20250430', '20250501')
2025-05-01 08:31:42,004 - INFO - MySQL查询成功，时间段: 20250430 至 20250501，共获取 0 条记录
2025-05-01 08:31:42,004 - ERROR - 未获取到MySQL数据
2025-05-01 08:31:42,004 - INFO - 同步完成
2025-05-01 09:30:33,936 - INFO - 使用默认增量同步（当天更新数据）
2025-05-01 09:30:33,936 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 09:30:33,936 - INFO - 查询参数: ('2025-05-01',)
2025-05-01 09:30:33,998 - INFO - MySQL查询成功，增量数据（日期: 2025-05-01），共获取 89 条记录
2025-05-01 09:30:33,998 - INFO - 获取到 2 个日期需要处理: ['2025-04-30', '2025-05-01']
2025-05-01 09:30:33,998 - INFO - 开始处理日期: 2025-04-30
2025-05-01 09:30:33,998 - INFO - Request Parameters - Page 1:
2025-05-01 09:30:33,998 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 09:30:33,998 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 09:30:42,139 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 110622FB-4855-7D97-9057-C64F26A4B771 Response: {'code': 'ServiceUnavailable', 'requestid': '110622FB-4855-7D97-9057-C64F26A4B771', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 110622FB-4855-7D97-9057-C64F26A4B771)
2025-05-01 09:30:42,139 - INFO - 开始处理日期: 2025-05-01
2025-05-01 09:30:42,139 - INFO - Request Parameters - Page 1:
2025-05-01 09:30:42,139 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 09:30:42,139 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 09:30:47,357 - INFO - Response - Page 1:
2025-05-01 09:30:47,357 - INFO - 查询完成，共获取到 0 条记录
2025-05-01 09:30:47,357 - INFO - 获取到 0 条表单数据
2025-05-01 09:30:47,357 - INFO - 当前日期 2025-05-01 有 1 条MySQL数据需要处理
2025-05-01 09:30:47,357 - INFO - 开始批量插入 1 条新记录
2025-05-01 09:30:47,514 - INFO - 批量插入响应状态码: 200
2025-05-01 09:30:47,514 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 01 May 2025 01:30:14 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '741A831B-11D5-7909-9459-B91D00A22366', 'x-acs-trace-id': '6f7f8cec83950a7ffd57c7d264c360a4', 'etag': '6NAPkVRU0Zn7t1N/riV7tuQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-01 09:30:47,514 - INFO - 批量插入响应体: {'result': ['FINST-V7966QC1MPZUIYR06PNH07Q4372F29V4UO4AM79']}
2025-05-01 09:30:47,514 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-01 09:30:47,514 - INFO - 成功插入的数据ID: ['FINST-V7966QC1MPZUIYR06PNH07Q4372F29V4UO4AM79']
2025-05-01 09:30:52,529 - INFO - 批量插入完成，共 1 条记录
2025-05-01 09:30:52,529 - INFO - 日期 2025-05-01 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-05-01 09:30:52,529 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 1 条
2025-05-01 09:31:52,545 - INFO - 开始同步昨天与今天的销售数据: 20250430 至 20250501
2025-05-01 09:31:52,545 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 09:31:52,545 - INFO - 查询参数: ('20250430', '20250501')
2025-05-01 09:31:52,591 - INFO - MySQL查询成功，时间段: 20250430 至 20250501，共获取 0 条记录
2025-05-01 09:31:52,591 - ERROR - 未获取到MySQL数据
2025-05-01 09:31:52,591 - INFO - 同步完成
2025-05-01 10:30:33,788 - INFO - 使用默认增量同步（当天更新数据）
2025-05-01 10:30:33,788 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 10:30:33,788 - INFO - 查询参数: ('2025-05-01',)
2025-05-01 10:30:33,851 - INFO - MySQL查询成功，增量数据（日期: 2025-05-01），共获取 161 条记录
2025-05-01 10:30:33,851 - INFO - 获取到 3 个日期需要处理: ['2025-04-29', '2025-04-30', '2025-05-01']
2025-05-01 10:30:33,851 - INFO - 开始处理日期: 2025-04-29
2025-05-01 10:30:33,851 - INFO - Request Parameters - Page 1:
2025-05-01 10:30:33,851 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 10:30:33,851 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 10:30:41,960 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2BB60BB6-5BBE-73F7-B804-DBE789E26CC8 Response: {'code': 'ServiceUnavailable', 'requestid': '2BB60BB6-5BBE-73F7-B804-DBE789E26CC8', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2BB60BB6-5BBE-73F7-B804-DBE789E26CC8)
2025-05-01 10:30:41,960 - INFO - 开始处理日期: 2025-04-30
2025-05-01 10:30:41,960 - INFO - Request Parameters - Page 1:
2025-05-01 10:30:41,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 10:30:41,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 10:30:42,132 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 7E9C0EAA-3D25-70A7-8541-72276E4A884F Response: {'requestid': '7E9C0EAA-3D25-70A7-8541-72276E4A884F', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 7E9C0EAA-3D25-70A7-8541-72276E4A884F)
2025-05-01 10:30:42,132 - INFO - 开始处理日期: 2025-05-01
2025-05-01 10:30:42,132 - INFO - Request Parameters - Page 1:
2025-05-01 10:30:42,132 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 10:30:42,132 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 10:30:42,273 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 9FD7A189-EA5D-728C-A8E7-D57761A005C1 Response: {'requestid': '9FD7A189-EA5D-728C-A8E7-D57761A005C1', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 9FD7A189-EA5D-728C-A8E7-D57761A005C1)
2025-05-01 10:30:42,273 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 3 条
2025-05-01 10:31:42,288 - INFO - 开始同步昨天与今天的销售数据: 20250430 至 20250501
2025-05-01 10:31:42,288 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 10:31:42,288 - INFO - 查询参数: ('20250430', '20250501')
2025-05-01 10:31:42,335 - INFO - MySQL查询成功，时间段: 20250430 至 20250501，共获取 0 条记录
2025-05-01 10:31:42,335 - ERROR - 未获取到MySQL数据
2025-05-01 10:31:42,335 - INFO - 同步完成
2025-05-01 11:30:33,750 - INFO - 使用默认增量同步（当天更新数据）
2025-05-01 11:30:33,750 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 11:30:33,750 - INFO - 查询参数: ('2025-05-01',)
2025-05-01 11:30:33,829 - INFO - MySQL查询成功，增量数据（日期: 2025-05-01），共获取 189 条记录
2025-05-01 11:30:33,829 - INFO - 获取到 3 个日期需要处理: ['2025-04-29', '2025-04-30', '2025-05-01']
2025-05-01 11:30:33,829 - INFO - 开始处理日期: 2025-04-29
2025-05-01 11:30:33,829 - INFO - Request Parameters - Page 1:
2025-05-01 11:30:33,829 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 11:30:33,829 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 11:30:41,953 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: AA4E33F7-E633-7774-AB84-F4E58514C8D4 Response: {'code': 'ServiceUnavailable', 'requestid': 'AA4E33F7-E633-7774-AB84-F4E58514C8D4', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: AA4E33F7-E633-7774-AB84-F4E58514C8D4)
2025-05-01 11:30:41,953 - INFO - 开始处理日期: 2025-04-30
2025-05-01 11:30:41,953 - INFO - Request Parameters - Page 1:
2025-05-01 11:30:41,953 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 11:30:41,953 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 11:30:42,078 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: E39CC71C-CA33-7C9B-960B-6E60818319EC Response: {'requestid': 'E39CC71C-CA33-7C9B-960B-6E60818319EC', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: E39CC71C-CA33-7C9B-960B-6E60818319EC)
2025-05-01 11:30:42,078 - INFO - 开始处理日期: 2025-05-01
2025-05-01 11:30:42,078 - INFO - Request Parameters - Page 1:
2025-05-01 11:30:42,078 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 11:30:42,078 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 11:30:42,235 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 4F025C48-A8C0-781B-AF98-9C362E21F0EA Response: {'requestid': '4F025C48-A8C0-781B-AF98-9C362E21F0EA', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 4F025C48-A8C0-781B-AF98-9C362E21F0EA)
2025-05-01 11:30:42,235 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 3 条
2025-05-01 11:31:42,250 - INFO - 开始同步昨天与今天的销售数据: 20250430 至 20250501
2025-05-01 11:31:42,250 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 11:31:42,250 - INFO - 查询参数: ('20250430', '20250501')
2025-05-01 11:31:42,297 - INFO - MySQL查询成功，时间段: 20250430 至 20250501，共获取 0 条记录
2025-05-01 11:31:42,297 - ERROR - 未获取到MySQL数据
2025-05-01 11:31:42,297 - INFO - 同步完成
2025-05-01 12:30:33,931 - INFO - 使用默认增量同步（当天更新数据）
2025-05-01 12:30:33,931 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 12:30:33,931 - INFO - 查询参数: ('2025-05-01',)
2025-05-01 12:30:33,993 - INFO - MySQL查询成功，增量数据（日期: 2025-05-01），共获取 191 条记录
2025-05-01 12:30:33,993 - INFO - 获取到 3 个日期需要处理: ['2025-04-29', '2025-04-30', '2025-05-01']
2025-05-01 12:30:33,993 - INFO - 开始处理日期: 2025-04-29
2025-05-01 12:30:33,993 - INFO - Request Parameters - Page 1:
2025-05-01 12:30:33,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 12:30:33,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 12:30:42,118 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 9547EE2C-84C0-7CBC-8373-6BFFB88F463B Response: {'code': 'ServiceUnavailable', 'requestid': '9547EE2C-84C0-7CBC-8373-6BFFB88F463B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 9547EE2C-84C0-7CBC-8373-6BFFB88F463B)
2025-05-01 12:30:42,118 - INFO - 开始处理日期: 2025-04-30
2025-05-01 12:30:42,118 - INFO - Request Parameters - Page 1:
2025-05-01 12:30:42,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 12:30:42,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 12:30:42,259 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 51A1D6DC-72F0-7813-A3D4-96158837C876 Response: {'requestid': '51A1D6DC-72F0-7813-A3D4-96158837C876', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 51A1D6DC-72F0-7813-A3D4-96158837C876)
2025-05-01 12:30:42,259 - INFO - 开始处理日期: 2025-05-01
2025-05-01 12:30:42,259 - INFO - Request Parameters - Page 1:
2025-05-01 12:30:42,259 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 12:30:42,259 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 12:30:42,400 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 7512A1EE-896C-752E-AE4D-C5D3D6F20C4B Response: {'requestid': '7512A1EE-896C-752E-AE4D-C5D3D6F20C4B', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 7512A1EE-896C-752E-AE4D-C5D3D6F20C4B)
2025-05-01 12:30:42,400 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 3 条
2025-05-01 12:31:42,415 - INFO - 开始同步昨天与今天的销售数据: 20250430 至 20250501
2025-05-01 12:31:42,415 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 12:31:42,415 - INFO - 查询参数: ('20250430', '20250501')
2025-05-01 12:31:42,462 - INFO - MySQL查询成功，时间段: 20250430 至 20250501，共获取 0 条记录
2025-05-01 12:31:42,462 - ERROR - 未获取到MySQL数据
2025-05-01 12:31:42,462 - INFO - 同步完成
2025-05-01 13:30:33,783 - INFO - 使用默认增量同步（当天更新数据）
2025-05-01 13:30:33,783 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 13:30:33,783 - INFO - 查询参数: ('2025-05-01',)
2025-05-01 13:30:33,846 - INFO - MySQL查询成功，增量数据（日期: 2025-05-01），共获取 191 条记录
2025-05-01 13:30:33,846 - INFO - 获取到 3 个日期需要处理: ['2025-04-29', '2025-04-30', '2025-05-01']
2025-05-01 13:30:33,846 - INFO - 开始处理日期: 2025-04-29
2025-05-01 13:30:33,861 - INFO - Request Parameters - Page 1:
2025-05-01 13:30:33,861 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 13:30:33,861 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 13:30:41,986 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: CE7581D9-0997-7885-9CFE-530629CC5169 Response: {'code': 'ServiceUnavailable', 'requestid': 'CE7581D9-0997-7885-9CFE-530629CC5169', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: CE7581D9-0997-7885-9CFE-530629CC5169)
2025-05-01 13:30:41,986 - INFO - 开始处理日期: 2025-04-30
2025-05-01 13:30:41,986 - INFO - Request Parameters - Page 1:
2025-05-01 13:30:41,986 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 13:30:41,986 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 13:30:42,127 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 2A208854-C4DB-75EF-A48E-4A0C024C88C0 Response: {'requestid': '2A208854-C4DB-75EF-A48E-4A0C024C88C0', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 2A208854-C4DB-75EF-A48E-4A0C024C88C0)
2025-05-01 13:30:42,127 - INFO - 开始处理日期: 2025-05-01
2025-05-01 13:30:42,127 - INFO - Request Parameters - Page 1:
2025-05-01 13:30:42,127 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 13:30:42,127 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 13:30:42,283 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 46F632BF-69EE-735F-914C-AAFE0A2BD8B7 Response: {'requestid': '46F632BF-69EE-735F-914C-AAFE0A2BD8B7', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 46F632BF-69EE-735F-914C-AAFE0A2BD8B7)
2025-05-01 13:30:42,283 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 3 条
2025-05-01 13:31:42,298 - INFO - 开始同步昨天与今天的销售数据: 20250430 至 20250501
2025-05-01 13:31:42,298 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 13:31:42,298 - INFO - 查询参数: ('20250430', '20250501')
2025-05-01 13:31:42,345 - INFO - MySQL查询成功，时间段: 20250430 至 20250501，共获取 0 条记录
2025-05-01 13:31:42,345 - ERROR - 未获取到MySQL数据
2025-05-01 13:31:42,345 - INFO - 同步完成
2025-05-01 14:30:33,917 - INFO - 使用默认增量同步（当天更新数据）
2025-05-01 14:30:33,917 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 14:30:33,917 - INFO - 查询参数: ('2025-05-01',)
2025-05-01 14:30:33,979 - INFO - MySQL查询成功，增量数据（日期: 2025-05-01），共获取 192 条记录
2025-05-01 14:30:33,979 - INFO - 获取到 3 个日期需要处理: ['2025-04-29', '2025-04-30', '2025-05-01']
2025-05-01 14:30:33,979 - INFO - 开始处理日期: 2025-04-29
2025-05-01 14:30:33,979 - INFO - Request Parameters - Page 1:
2025-05-01 14:30:33,979 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 14:30:33,979 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 14:30:42,120 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F206DAFF-945C-7629-8F0C-098CB3FC794A Response: {'code': 'ServiceUnavailable', 'requestid': 'F206DAFF-945C-7629-8F0C-098CB3FC794A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F206DAFF-945C-7629-8F0C-098CB3FC794A)
2025-05-01 14:30:42,120 - INFO - 开始处理日期: 2025-04-30
2025-05-01 14:30:42,120 - INFO - Request Parameters - Page 1:
2025-05-01 14:30:42,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 14:30:42,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 14:30:42,261 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 2B3A1EB8-DB7A-70C0-98D4-1E0E385F2543 Response: {'requestid': '2B3A1EB8-DB7A-70C0-98D4-1E0E385F2543', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 2B3A1EB8-DB7A-70C0-98D4-1E0E385F2543)
2025-05-01 14:30:42,261 - INFO - 开始处理日期: 2025-05-01
2025-05-01 14:30:42,261 - INFO - Request Parameters - Page 1:
2025-05-01 14:30:42,261 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 14:30:42,261 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 14:30:42,464 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: EAB6D3EB-021A-7A10-98C5-86AD1EB951BD Response: {'requestid': 'EAB6D3EB-021A-7A10-98C5-86AD1EB951BD', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: EAB6D3EB-021A-7A10-98C5-86AD1EB951BD)
2025-05-01 14:30:42,464 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 3 条
2025-05-01 14:31:42,479 - INFO - 开始同步昨天与今天的销售数据: 20250430 至 20250501
2025-05-01 14:31:42,479 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 14:31:42,479 - INFO - 查询参数: ('20250430', '20250501')
2025-05-01 14:31:42,526 - INFO - MySQL查询成功，时间段: 20250430 至 20250501，共获取 0 条记录
2025-05-01 14:31:42,526 - ERROR - 未获取到MySQL数据
2025-05-01 14:31:42,526 - INFO - 同步完成
2025-05-01 15:30:33,957 - INFO - 使用默认增量同步（当天更新数据）
2025-05-01 15:30:33,957 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 15:30:33,957 - INFO - 查询参数: ('2025-05-01',)
2025-05-01 15:30:34,020 - INFO - MySQL查询成功，增量数据（日期: 2025-05-01），共获取 193 条记录
2025-05-01 15:30:34,020 - INFO - 获取到 3 个日期需要处理: ['2025-04-29', '2025-04-30', '2025-05-01']
2025-05-01 15:30:34,020 - INFO - 开始处理日期: 2025-04-29
2025-05-01 15:30:34,020 - INFO - Request Parameters - Page 1:
2025-05-01 15:30:34,020 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 15:30:34,020 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 15:30:42,160 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E282820C-6006-7769-BADF-EF4BDE7447AB Response: {'code': 'ServiceUnavailable', 'requestid': 'E282820C-6006-7769-BADF-EF4BDE7447AB', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E282820C-6006-7769-BADF-EF4BDE7447AB)
2025-05-01 15:30:42,160 - INFO - 开始处理日期: 2025-04-30
2025-05-01 15:30:42,160 - INFO - Request Parameters - Page 1:
2025-05-01 15:30:42,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 15:30:42,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 15:30:42,316 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 60DFBC09-4B2F-7002-B7C9-707CF794C370 Response: {'requestid': '60DFBC09-4B2F-7002-B7C9-707CF794C370', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 60DFBC09-4B2F-7002-B7C9-707CF794C370)
2025-05-01 15:30:42,316 - INFO - 开始处理日期: 2025-05-01
2025-05-01 15:30:42,316 - INFO - Request Parameters - Page 1:
2025-05-01 15:30:42,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 15:30:42,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 15:30:42,457 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: C788328F-1A95-7139-984E-DCDED8816CFE Response: {'requestid': 'C788328F-1A95-7139-984E-DCDED8816CFE', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: C788328F-1A95-7139-984E-DCDED8816CFE)
2025-05-01 15:30:42,457 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 3 条
2025-05-01 15:31:42,472 - INFO - 开始同步昨天与今天的销售数据: 20250430 至 20250501
2025-05-01 15:31:42,472 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 15:31:42,472 - INFO - 查询参数: ('20250430', '20250501')
2025-05-01 15:31:42,519 - INFO - MySQL查询成功，时间段: 20250430 至 20250501，共获取 0 条记录
2025-05-01 15:31:42,519 - ERROR - 未获取到MySQL数据
2025-05-01 15:31:42,519 - INFO - 同步完成
2025-05-01 16:30:33,950 - INFO - 使用默认增量同步（当天更新数据）
2025-05-01 16:30:33,950 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 16:30:33,950 - INFO - 查询参数: ('2025-05-01',)
2025-05-01 16:30:34,013 - INFO - MySQL查询成功，增量数据（日期: 2025-05-01），共获取 193 条记录
2025-05-01 16:30:34,013 - INFO - 获取到 3 个日期需要处理: ['2025-04-29', '2025-04-30', '2025-05-01']
2025-05-01 16:30:34,013 - INFO - 开始处理日期: 2025-04-29
2025-05-01 16:30:34,013 - INFO - Request Parameters - Page 1:
2025-05-01 16:30:34,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 16:30:34,013 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 16:30:42,153 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 0C9F43E6-3A84-7F46-8A0C-C2EE327118B6 Response: {'code': 'ServiceUnavailable', 'requestid': '0C9F43E6-3A84-7F46-8A0C-C2EE327118B6', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 0C9F43E6-3A84-7F46-8A0C-C2EE327118B6)
2025-05-01 16:30:42,153 - INFO - 开始处理日期: 2025-04-30
2025-05-01 16:30:42,153 - INFO - Request Parameters - Page 1:
2025-05-01 16:30:42,153 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 16:30:42,153 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 16:30:42,294 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 4C442D07-C83B-7B2B-894B-BD28F3E833A9 Response: {'requestid': '4C442D07-C83B-7B2B-894B-BD28F3E833A9', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 4C442D07-C83B-7B2B-894B-BD28F3E833A9)
2025-05-01 16:30:42,294 - INFO - 开始处理日期: 2025-05-01
2025-05-01 16:30:42,294 - INFO - Request Parameters - Page 1:
2025-05-01 16:30:42,294 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 16:30:42,294 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 16:30:42,435 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 545BF768-B81C-7D35-845E-AD677E823293 Response: {'requestid': '545BF768-B81C-7D35-845E-AD677E823293', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 545BF768-B81C-7D35-845E-AD677E823293)
2025-05-01 16:30:42,435 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 3 条
2025-05-01 16:31:42,450 - INFO - 开始同步昨天与今天的销售数据: 20250430 至 20250501
2025-05-01 16:31:42,450 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 16:31:42,450 - INFO - 查询参数: ('20250430', '20250501')
2025-05-01 16:31:42,497 - INFO - MySQL查询成功，时间段: 20250430 至 20250501，共获取 0 条记录
2025-05-01 16:31:42,497 - ERROR - 未获取到MySQL数据
2025-05-01 16:31:42,497 - INFO - 同步完成
2025-05-01 17:30:33,600 - INFO - 使用默认增量同步（当天更新数据）
2025-05-01 17:30:33,600 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 17:30:33,600 - INFO - 查询参数: ('2025-05-01',)
2025-05-01 17:30:33,662 - INFO - MySQL查询成功，增量数据（日期: 2025-05-01），共获取 194 条记录
2025-05-01 17:30:33,662 - INFO - 获取到 3 个日期需要处理: ['2025-04-29', '2025-04-30', '2025-05-01']
2025-05-01 17:30:33,662 - INFO - 开始处理日期: 2025-04-29
2025-05-01 17:30:33,662 - INFO - Request Parameters - Page 1:
2025-05-01 17:30:33,662 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 17:30:33,662 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 17:30:41,787 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3D52218E-5C9D-7161-90BA-1B94535BE6FE Response: {'code': 'ServiceUnavailable', 'requestid': '3D52218E-5C9D-7161-90BA-1B94535BE6FE', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3D52218E-5C9D-7161-90BA-1B94535BE6FE)
2025-05-01 17:30:41,787 - INFO - 开始处理日期: 2025-04-30
2025-05-01 17:30:41,787 - INFO - Request Parameters - Page 1:
2025-05-01 17:30:41,787 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 17:30:41,787 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 17:30:49,897 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A1A1E039-AA52-7863-AAE3-EF103FFBAFF8 Response: {'code': 'ServiceUnavailable', 'requestid': 'A1A1E039-AA52-7863-AAE3-EF103FFBAFF8', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A1A1E039-AA52-7863-AAE3-EF103FFBAFF8)
2025-05-01 17:30:49,897 - INFO - 开始处理日期: 2025-05-01
2025-05-01 17:30:49,897 - INFO - Request Parameters - Page 1:
2025-05-01 17:30:49,897 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 17:30:49,897 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 17:30:50,053 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 8F3BE005-BF95-72F9-831F-19CC2A0F4AF9 Response: {'requestid': '8F3BE005-BF95-72F9-831F-19CC2A0F4AF9', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 8F3BE005-BF95-72F9-831F-19CC2A0F4AF9)
2025-05-01 17:30:50,053 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 3 条
2025-05-01 17:31:50,068 - INFO - 开始同步昨天与今天的销售数据: 20250430 至 20250501
2025-05-01 17:31:50,068 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 17:31:50,068 - INFO - 查询参数: ('20250430', '20250501')
2025-05-01 17:31:50,115 - INFO - MySQL查询成功，时间段: 20250430 至 20250501，共获取 0 条记录
2025-05-01 17:31:50,115 - ERROR - 未获取到MySQL数据
2025-05-01 17:31:50,115 - INFO - 同步完成
2025-05-01 18:30:33,968 - INFO - 使用默认增量同步（当天更新数据）
2025-05-01 18:30:33,968 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 18:30:33,968 - INFO - 查询参数: ('2025-05-01',)
2025-05-01 18:30:34,031 - INFO - MySQL查询成功，增量数据（日期: 2025-05-01），共获取 233 条记录
2025-05-01 18:30:34,031 - INFO - 获取到 3 个日期需要处理: ['2025-04-29', '2025-04-30', '2025-05-01']
2025-05-01 18:30:34,031 - INFO - 开始处理日期: 2025-04-29
2025-05-01 18:30:34,031 - INFO - Request Parameters - Page 1:
2025-05-01 18:30:34,031 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 18:30:34,031 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 18:30:42,171 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F1D91B5C-BDAF-7EC8-B0FA-5ED01E85E97A Response: {'code': 'ServiceUnavailable', 'requestid': 'F1D91B5C-BDAF-7EC8-B0FA-5ED01E85E97A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F1D91B5C-BDAF-7EC8-B0FA-5ED01E85E97A)
2025-05-01 18:30:42,171 - INFO - 开始处理日期: 2025-04-30
2025-05-01 18:30:42,171 - INFO - Request Parameters - Page 1:
2025-05-01 18:30:42,171 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 18:30:42,171 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 18:30:42,312 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 5FB142BF-D9C5-74A4-96FE-0EDC898C243F Response: {'requestid': '5FB142BF-D9C5-74A4-96FE-0EDC898C243F', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 5FB142BF-D9C5-74A4-96FE-0EDC898C243F)
2025-05-01 18:30:42,312 - INFO - 开始处理日期: 2025-05-01
2025-05-01 18:30:42,312 - INFO - Request Parameters - Page 1:
2025-05-01 18:30:42,312 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 18:30:42,312 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 18:30:42,468 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 668D5171-33D2-701C-B2BB-A33232FDC6EE Response: {'requestid': '668D5171-33D2-701C-B2BB-A33232FDC6EE', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 668D5171-33D2-701C-B2BB-A33232FDC6EE)
2025-05-01 18:30:42,468 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 3 条
2025-05-01 18:31:42,483 - INFO - 开始同步昨天与今天的销售数据: 20250430 至 20250501
2025-05-01 18:31:42,483 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 18:31:42,483 - INFO - 查询参数: ('20250430', '20250501')
2025-05-01 18:31:42,530 - INFO - MySQL查询成功，时间段: 20250430 至 20250501，共获取 0 条记录
2025-05-01 18:31:42,530 - ERROR - 未获取到MySQL数据
2025-05-01 18:31:42,530 - INFO - 同步完成
2025-05-01 19:30:33,726 - INFO - 使用默认增量同步（当天更新数据）
2025-05-01 19:30:33,726 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 19:30:33,726 - INFO - 查询参数: ('2025-05-01',)
2025-05-01 19:30:33,788 - INFO - MySQL查询成功，增量数据（日期: 2025-05-01），共获取 234 条记录
2025-05-01 19:30:33,788 - INFO - 获取到 3 个日期需要处理: ['2025-04-29', '2025-04-30', '2025-05-01']
2025-05-01 19:30:33,804 - INFO - 开始处理日期: 2025-04-29
2025-05-01 19:30:33,804 - INFO - Request Parameters - Page 1:
2025-05-01 19:30:33,804 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 19:30:33,804 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 19:30:41,913 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 105D462B-91B6-7681-927B-692DE061513D Response: {'code': 'ServiceUnavailable', 'requestid': '105D462B-91B6-7681-927B-692DE061513D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 105D462B-91B6-7681-927B-692DE061513D)
2025-05-01 19:30:41,913 - INFO - 开始处理日期: 2025-04-30
2025-05-01 19:30:41,913 - INFO - Request Parameters - Page 1:
2025-05-01 19:30:41,913 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 19:30:41,913 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 19:30:42,132 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 74DEE551-8FF6-7B07-BA90-A4401387B0F4 Response: {'requestid': '74DEE551-8FF6-7B07-BA90-A4401387B0F4', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 74DEE551-8FF6-7B07-BA90-A4401387B0F4)
2025-05-01 19:30:42,132 - INFO - 开始处理日期: 2025-05-01
2025-05-01 19:30:42,132 - INFO - Request Parameters - Page 1:
2025-05-01 19:30:42,132 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 19:30:42,132 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 19:30:42,273 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: E9ABF8CF-3CF5-70DA-9B7C-C47382241A59 Response: {'requestid': 'E9ABF8CF-3CF5-70DA-9B7C-C47382241A59', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: E9ABF8CF-3CF5-70DA-9B7C-C47382241A59)
2025-05-01 19:30:42,273 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 3 条
2025-05-01 19:31:42,288 - INFO - 开始同步昨天与今天的销售数据: 20250430 至 20250501
2025-05-01 19:31:42,288 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 19:31:42,288 - INFO - 查询参数: ('20250430', '20250501')
2025-05-01 19:31:42,335 - INFO - MySQL查询成功，时间段: 20250430 至 20250501，共获取 0 条记录
2025-05-01 19:31:42,335 - ERROR - 未获取到MySQL数据
2025-05-01 19:31:42,335 - INFO - 同步完成
2025-05-01 20:30:33,735 - INFO - 使用默认增量同步（当天更新数据）
2025-05-01 20:30:33,735 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 20:30:33,735 - INFO - 查询参数: ('2025-05-01',)
2025-05-01 20:30:33,797 - INFO - MySQL查询成功，增量数据（日期: 2025-05-01），共获取 236 条记录
2025-05-01 20:30:33,797 - INFO - 获取到 3 个日期需要处理: ['2025-04-29', '2025-04-30', '2025-05-01']
2025-05-01 20:30:33,797 - INFO - 开始处理日期: 2025-04-29
2025-05-01 20:30:33,797 - INFO - Request Parameters - Page 1:
2025-05-01 20:30:33,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 20:30:33,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 20:30:41,922 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: EA53EAD4-4615-7D52-978B-98DB898C2915 Response: {'code': 'ServiceUnavailable', 'requestid': 'EA53EAD4-4615-7D52-978B-98DB898C2915', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: EA53EAD4-4615-7D52-978B-98DB898C2915)
2025-05-01 20:30:41,922 - INFO - 开始处理日期: 2025-04-30
2025-05-01 20:30:41,922 - INFO - Request Parameters - Page 1:
2025-05-01 20:30:41,922 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 20:30:41,922 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 20:30:42,063 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: BDA0070B-5C81-7470-9664-5B6F9BDDE79B Response: {'requestid': 'BDA0070B-5C81-7470-9664-5B6F9BDDE79B', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: BDA0070B-5C81-7470-9664-5B6F9BDDE79B)
2025-05-01 20:30:42,063 - INFO - 开始处理日期: 2025-05-01
2025-05-01 20:30:42,063 - INFO - Request Parameters - Page 1:
2025-05-01 20:30:42,063 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 20:30:42,063 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 20:30:42,219 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: E072037D-F477-7348-A926-13AD35FFDD97 Response: {'requestid': 'E072037D-F477-7348-A926-13AD35FFDD97', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: E072037D-F477-7348-A926-13AD35FFDD97)
2025-05-01 20:30:42,219 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 3 条
2025-05-01 20:31:42,234 - INFO - 开始同步昨天与今天的销售数据: 20250430 至 20250501
2025-05-01 20:31:42,234 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 20:31:42,234 - INFO - 查询参数: ('20250430', '20250501')
2025-05-01 20:31:42,281 - INFO - MySQL查询成功，时间段: 20250430 至 20250501，共获取 0 条记录
2025-05-01 20:31:42,281 - ERROR - 未获取到MySQL数据
2025-05-01 20:31:42,281 - INFO - 同步完成
2025-05-01 21:30:33,931 - INFO - 使用默认增量同步（当天更新数据）
2025-05-01 21:30:33,931 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 21:30:33,931 - INFO - 查询参数: ('2025-05-01',)
2025-05-01 21:30:33,994 - INFO - MySQL查询成功，增量数据（日期: 2025-05-01），共获取 244 条记录
2025-05-01 21:30:33,994 - INFO - 获取到 3 个日期需要处理: ['2025-04-29', '2025-04-30', '2025-05-01']
2025-05-01 21:30:34,009 - INFO - 开始处理日期: 2025-04-29
2025-05-01 21:30:34,009 - INFO - Request Parameters - Page 1:
2025-05-01 21:30:34,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 21:30:34,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 21:30:42,118 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: EE83B36D-E147-793C-9922-BE2E752B2F12 Response: {'code': 'ServiceUnavailable', 'requestid': 'EE83B36D-E147-793C-9922-BE2E752B2F12', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: EE83B36D-E147-793C-9922-BE2E752B2F12)
2025-05-01 21:30:42,118 - INFO - 开始处理日期: 2025-04-30
2025-05-01 21:30:42,118 - INFO - Request Parameters - Page 1:
2025-05-01 21:30:42,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 21:30:42,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 21:30:42,259 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: BFEDC782-D530-7760-B172-51522F3F2604 Response: {'requestid': 'BFEDC782-D530-7760-B172-51522F3F2604', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: BFEDC782-D530-7760-B172-51522F3F2604)
2025-05-01 21:30:42,259 - INFO - 开始处理日期: 2025-05-01
2025-05-01 21:30:42,259 - INFO - Request Parameters - Page 1:
2025-05-01 21:30:42,259 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 21:30:42,259 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 21:30:42,415 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 9B07343A-1284-784D-BA9E-4106968C742F Response: {'requestid': '9B07343A-1284-784D-BA9E-4106968C742F', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 9B07343A-1284-784D-BA9E-4106968C742F)
2025-05-01 21:30:42,415 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 3 条
2025-05-01 21:31:42,431 - INFO - 开始同步昨天与今天的销售数据: 20250430 至 20250501
2025-05-01 21:31:42,431 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 21:31:42,431 - INFO - 查询参数: ('20250430', '20250501')
2025-05-01 21:31:42,477 - INFO - MySQL查询成功，时间段: 20250430 至 20250501，共获取 0 条记录
2025-05-01 21:31:42,477 - ERROR - 未获取到MySQL数据
2025-05-01 21:31:42,477 - INFO - 同步完成
2025-05-01 22:30:33,862 - INFO - 使用默认增量同步（当天更新数据）
2025-05-01 22:30:33,862 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 22:30:33,862 - INFO - 查询参数: ('2025-05-01',)
2025-05-01 22:30:33,940 - INFO - MySQL查询成功，增量数据（日期: 2025-05-01），共获取 396 条记录
2025-05-01 22:30:33,940 - INFO - 获取到 3 个日期需要处理: ['2025-04-29', '2025-04-30', '2025-05-01']
2025-05-01 22:30:33,940 - INFO - 开始处理日期: 2025-04-29
2025-05-01 22:30:33,940 - INFO - Request Parameters - Page 1:
2025-05-01 22:30:33,940 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 22:30:33,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 22:30:42,049 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 1FA63294-B40C-78B5-988B-40D8F2443AB5 Response: {'code': 'ServiceUnavailable', 'requestid': '1FA63294-B40C-78B5-988B-40D8F2443AB5', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 1FA63294-B40C-78B5-988B-40D8F2443AB5)
2025-05-01 22:30:42,049 - INFO - 开始处理日期: 2025-04-30
2025-05-01 22:30:42,049 - INFO - Request Parameters - Page 1:
2025-05-01 22:30:42,049 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 22:30:42,049 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 22:30:50,315 - INFO - Response - Page 1:
2025-05-01 22:30:50,315 - INFO - 第 1 页获取到 100 条记录
2025-05-01 22:30:50,518 - INFO - Request Parameters - Page 2:
2025-05-01 22:30:50,518 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 22:30:50,518 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 22:30:51,049 - INFO - Response - Page 2:
2025-05-01 22:30:51,049 - INFO - 第 2 页获取到 19 条记录
2025-05-01 22:30:51,252 - INFO - 查询完成，共获取到 119 条记录
2025-05-01 22:30:51,252 - INFO - 获取到 119 条表单数据
2025-05-01 22:30:51,252 - INFO - 当前日期 2025-04-30 有 187 条MySQL数据需要处理
2025-05-01 22:30:51,252 - INFO - 开始更新记录 - 表单实例ID: FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMX3
2025-05-01 22:30:51,705 - INFO - 更新表单数据成功: FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMX3
2025-05-01 22:30:51,705 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2700.0, 'new_value': 3785.89}, {'field': 'offline_amount', 'old_value': 2300.0, 'new_value': 2311.03}, {'field': 'total_amount', 'old_value': 5000.0, 'new_value': 6096.92}, {'field': 'order_count', 'old_value': 234, 'new_value': 307}]
2025-05-01 22:30:51,705 - INFO - 开始批量插入 177 条新记录
2025-05-01 22:30:52,002 - INFO - 批量插入响应状态码: 200
2025-05-01 22:30:52,002 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 01 May 2025 14:30:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'AF094FC1-F0BF-7F36-A9A3-DAE6C342BC6A', 'x-acs-trace-id': '9eabbacdadd7b3763bca6804c5da0600', 'etag': '4LGXsPypoyGAnkuL2qowSpQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-01 22:30:52,002 - INFO - 批量插入响应体: {'result': ['FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMOC', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMPC', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMQC', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMRC', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMSC', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMTC', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMUC', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMVC', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMWC', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMXC', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMYC', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMZC', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM0D', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM1D', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM2D', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM3D', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM4D', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM5D', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM6D', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM7D', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM8D', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM9D', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMAD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMBD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMCD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMDD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMED', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMFD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMGD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMHD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMID', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMJD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMKD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMLD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMMD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMND', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMOD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMPD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMQD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMRD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMSD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMTD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMUD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMVD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMWD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMXD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMYD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMZD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM0E', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM1E', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM2E', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM3E', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM4E', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM5E', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM6E', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM7E', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM8E', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM9E', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMAE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMBE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMCE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMDE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMEE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMFE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMGE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMHE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMIE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMJE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMKE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMLE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMME', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMNE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMOE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMPE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMQE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMRE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMSE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMTE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMUE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMVE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMWE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMXE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMYE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMZE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM0F', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM1F', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM2F', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM3F', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM4F', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM5F', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM6F', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM7F', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM8F', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM9F', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMAF', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMBF', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMCF', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMDF', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMEF', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMFF']}
2025-05-01 22:30:52,002 - INFO - 批量插入表单数据成功，批次 1，共 100 条记录
2025-05-01 22:30:52,002 - INFO - 成功插入的数据ID: ['FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMOC', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMPC', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMQC', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMRC', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMSC', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMTC', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMUC', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMVC', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMWC', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMXC', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMYC', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMZC', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM0D', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM1D', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM2D', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM3D', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM4D', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM5D', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM6D', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM7D', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM8D', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM9D', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMAD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMBD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMCD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMDD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMED', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMFD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMGD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMHD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMID', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMJD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMKD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMLD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMMD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMND', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMOD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMPD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMQD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMRD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMSD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMTD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMUD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMVD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMWD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMXD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMYD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMZD', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM0E', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM1E', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM2E', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM3E', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM4E', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM5E', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM6E', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM7E', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM8E', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM9E', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMAE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMBE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMCE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMDE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMEE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMFE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMGE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMHE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMIE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMJE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMKE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMLE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMME', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMNE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMOE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMPE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMQE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMRE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMSE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMTE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMUE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMVE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMWE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMXE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMYE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMZE', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM0F', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM1F', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM2F', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM3F', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM4F', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM5F', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM6F', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM7F', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM8F', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AM9F', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMAF', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMBF', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMCF', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMDF', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMEF', 'FINST-OIF66BA1E20VIF4GB9DHCA22UC3O3CI0QG5AMFF']
2025-05-01 22:30:57,237 - INFO - 批量插入响应状态码: 200
2025-05-01 22:30:57,237 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 01 May 2025 14:30:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '3708', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'AD921567-3794-7BA4-AEE3-A0B5F30310C5', 'x-acs-trace-id': '272acc2817ac3e2b9eaeda46e66bf925', 'etag': '3nrlD0pdpt9AvbJO9tLfd7w8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-01 22:30:57,237 - INFO - 批量插入响应体: {'result': ['FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM1H', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM2H', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM3H', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM4H', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM5H', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM6H', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM7H', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM8H', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM9H', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMAH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMBH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMCH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMDH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMEH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMFH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMGH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMHH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMIH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMJH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMKH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMLH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMMH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMNH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMOH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMPH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMQH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMRH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMSH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMTH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMUH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMVH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMWH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMXH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMYH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMZH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM0I', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM1I', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM2I', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM3I', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM4I', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM5I', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM6I', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM7I', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM8I', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM9I', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMAI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMBI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMCI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMDI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMEI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMFI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMGI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMHI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMII', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMJI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMKI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMLI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMMI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMNI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMOI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMPI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMQI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMRI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMSI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMTI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMUI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMVI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMWI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMXI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMYI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMZI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM0J', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM1J', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM2J', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM3J', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM4J', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM5J']}
2025-05-01 22:30:57,237 - INFO - 批量插入表单数据成功，批次 2，共 77 条记录
2025-05-01 22:30:57,237 - INFO - 成功插入的数据ID: ['FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM1H', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM2H', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM3H', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM4H', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM5H', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM6H', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM7H', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM8H', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM9H', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMAH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMBH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMCH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMDH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMEH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMFH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMGH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMHH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMIH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMJH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMKH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMLH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMMH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMNH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMOH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMPH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMQH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMRH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMSH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMTH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMUH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMVH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMWH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMXH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMYH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMZH', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM0I', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM1I', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM2I', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM3I', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM4I', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM5I', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM6I', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM7I', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM8I', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM9I', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMAI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMBI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMCI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMDI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMEI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMFI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMGI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMHI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMII', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMJI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMKI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMLI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMMI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMNI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMOI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMPI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMQI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMRI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMSI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMTI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMUI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMVI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMWI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMXI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMYI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AMZI', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM0J', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM1J', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM2J', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM3J', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM4J', 'FINST-2PF662C1T4ZUH55D9UVAI716TU9O2LJ4QG5AM5J']
2025-05-01 22:31:02,252 - INFO - 批量插入完成，共 177 条记录
2025-05-01 22:31:02,252 - INFO - 日期 2025-04-30 处理完成 - 更新: 1 条，插入: 177 条，错误: 0 条
2025-05-01 22:31:02,252 - INFO - 开始处理日期: 2025-05-01
2025-05-01 22:31:02,252 - INFO - Request Parameters - Page 1:
2025-05-01 22:31:02,252 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 22:31:02,252 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 22:31:02,705 - INFO - Response - Page 1:
2025-05-01 22:31:02,705 - INFO - 第 1 页获取到 1 条记录
2025-05-01 22:31:02,908 - INFO - 查询完成，共获取到 1 条记录
2025-05-01 22:31:02,908 - INFO - 获取到 1 条表单数据
2025-05-01 22:31:02,908 - INFO - 当前日期 2025-05-01 有 165 条MySQL数据需要处理
2025-05-01 22:31:02,908 - INFO - 开始批量插入 164 条新记录
2025-05-01 22:31:03,158 - INFO - 批量插入响应状态码: 200
2025-05-01 22:31:03,158 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 01 May 2025 14:31:03 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'C428504A-EA2B-7D21-99B8-DCB2AAC11D52', 'x-acs-trace-id': 'a4cc0a84b312e2c04d0b5e8c20b72643', 'etag': '4CtPe8RjZ+LNq25gGeyx9bg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-01 22:31:03,158 - INFO - 批量插入响应体: {'result': ['FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMGJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMHJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMIJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMJJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMKJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMLJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMMJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMNJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMOJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMPJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMQJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMRJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMSJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMTJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMUJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMVJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMWJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMXJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMYJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMZJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM0K', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM1K', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM2K', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM3K', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM4K', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM5K', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM6K', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM7K', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM8K', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM9K', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMAK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMBK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMCK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMDK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMEK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMFK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMGK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMHK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMIK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMJK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMKK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMLK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMMK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMNK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMOK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMPK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMQK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMRK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMSK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMTK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMUK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMVK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMWK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMXK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMYK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMZK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM0L', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM1L', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM2L', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM3L', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM4L', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM5L', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM6L', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM7L', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM8L', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM9L', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMAL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMBL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMCL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMDL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMEL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMFL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMGL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMHL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMIL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMJL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMKL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMLL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMML', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMNL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMOL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMPL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMQL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMRL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMSL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMTL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMUL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMVL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMWL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMXL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3D49QG5AMYL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3D49QG5AMZL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3D49QG5AM0M', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3D49QG5AM1M', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3D49QG5AM2M', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3D49QG5AM3M', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3D49QG5AM4M', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3D49QG5AM5M', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3D49QG5AM6M', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3D49QG5AM7M']}
2025-05-01 22:31:03,158 - INFO - 批量插入表单数据成功，批次 1，共 100 条记录
2025-05-01 22:31:03,158 - INFO - 成功插入的数据ID: ['FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMGJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMHJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMIJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMJJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMKJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMLJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMMJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMNJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMOJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMPJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMQJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMRJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMSJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMTJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMUJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMVJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMWJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMXJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMYJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMZJ', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM0K', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM1K', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM2K', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM3K', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM4K', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM5K', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM6K', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM7K', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM8K', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM9K', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMAK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMBK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMCK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMDK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMEK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMFK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMGK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMHK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMIK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMJK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMKK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMLK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMMK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMNK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMOK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMPK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMQK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMRK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMSK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMTK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMUK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMVK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMWK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMXK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMYK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMZK', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM0L', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM1L', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM2L', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM3L', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM4L', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM5L', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM6L', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM7L', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM8L', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AM9L', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMAL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMBL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMCL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMDL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMEL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMFL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMGL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMHL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMIL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMJL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMKL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMLL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMML', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMNL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMOL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMPL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMQL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMRL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMSL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMTL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMUL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMVL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMWL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3C49QG5AMXL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3D49QG5AMYL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3D49QG5AMZL', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3D49QG5AM0M', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3D49QG5AM1M', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3D49QG5AM2M', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3D49QG5AM3M', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3D49QG5AM4M', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3D49QG5AM5M', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3D49QG5AM6M', 'FINST-W3B66L71GMZULEK8CF2UGDZU15UG3D49QG5AM7M']
2025-05-01 22:31:08,424 - INFO - 批量插入响应状态码: 200
2025-05-01 22:31:08,424 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 01 May 2025 14:31:08 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '3084', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '9F8951A8-5A46-79AB-960F-E66B0E12FFCB', 'x-acs-trace-id': 'd92d205a725ee56d32c083af946696cd', 'etag': '3HhqBsVxn4olU0tKCx7v31Q4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-01 22:31:08,424 - INFO - 批量插入响应体: {'result': ['FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AM83', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AM93', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMA3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMB3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMC3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMD3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AME3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMF3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMG3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMH3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMI3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMJ3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMK3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AML3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMM3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMN3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMO3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMP3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMQ3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMR3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMS3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMT3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMU3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMV3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMW3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMX3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMY3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMZ3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AM04', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AM14', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AM24', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AM34', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AM44', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AM54', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AM64', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AM74', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AM84', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AM94', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMA4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMB4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMC4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMD4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AME4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMF4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMG4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMH4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMI4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMJ4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMK4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AML4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMM4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMN4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMO4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMP4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMQ4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMR4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMS4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMT4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMU4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMV4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMW4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMX4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMY4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMZ4']}
2025-05-01 22:31:08,424 - INFO - 批量插入表单数据成功，批次 2，共 64 条记录
2025-05-01 22:31:08,424 - INFO - 成功插入的数据ID: ['FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AM83', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AM93', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMA3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMB3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMC3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMD3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AME3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMF3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMG3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMH3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMI3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMJ3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMK3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AML3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMM3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMN3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMO3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMP3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMQ3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMR3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMS3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMT3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMU3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMV3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMW3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMX3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMY3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMZ3', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AM04', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AM14', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AM24', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AM34', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AM44', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AM54', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AM64', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AM74', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AM84', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AM94', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMA4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMB4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMC4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMD4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AME4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMF4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMG4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMH4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMI4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMJ4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMK4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AML4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMM4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMN4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMO4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMP4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMQ4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMR4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMS4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMT4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMU4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMV4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMW4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMX4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMY4', 'FINST-VOC66Y91NOZUI2K68XREI7BWNOWD2L6DQG5AMZ4']
2025-05-01 22:31:13,440 - INFO - 批量插入完成，共 164 条记录
2025-05-01 22:31:13,440 - INFO - 日期 2025-05-01 处理完成 - 更新: 0 条，插入: 164 条，错误: 0 条
2025-05-01 22:31:13,440 - INFO - 数据同步完成！更新: 1 条，插入: 341 条，错误: 1 条
2025-05-01 22:32:13,455 - INFO - 开始同步昨天与今天的销售数据: 20250430 至 20250501
2025-05-01 22:32:13,455 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 22:32:13,455 - INFO - 查询参数: ('20250430', '20250501')
2025-05-01 22:32:13,502 - INFO - MySQL查询成功，时间段: 20250430 至 20250501，共获取 0 条记录
2025-05-01 22:32:13,502 - ERROR - 未获取到MySQL数据
2025-05-01 22:32:13,502 - INFO - 同步完成
2025-05-01 23:30:32,892 - INFO - 使用默认增量同步（当天更新数据）
2025-05-01 23:30:32,892 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 23:30:32,892 - INFO - 查询参数: ('2025-05-01',)
2025-05-01 23:30:32,970 - INFO - MySQL查询成功，增量数据（日期: 2025-05-01），共获取 493 条记录
2025-05-01 23:30:32,970 - INFO - 获取到 3 个日期需要处理: ['2025-04-29', '2025-04-30', '2025-05-01']
2025-05-01 23:30:32,970 - INFO - 开始处理日期: 2025-04-29
2025-05-01 23:30:32,970 - INFO - Request Parameters - Page 1:
2025-05-01 23:30:32,970 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 23:30:32,970 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 23:30:41,089 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 93FA5CCF-**************-6D018BCCEEEA Response: {'code': 'ServiceUnavailable', 'requestid': '93FA5CCF-**************-6D018BCCEEEA', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 93FA5CCF-**************-6D018BCCEEEA)
2025-05-01 23:30:41,089 - INFO - 开始处理日期: 2025-04-30
2025-05-01 23:30:41,089 - INFO - Request Parameters - Page 1:
2025-05-01 23:30:41,089 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 23:30:41,089 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 23:30:49,223 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 73E996FE-C280-786F-99B9-126B01A5DC75 Response: {'code': 'ServiceUnavailable', 'requestid': '73E996FE-C280-786F-99B9-126B01A5DC75', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 73E996FE-C280-786F-99B9-126B01A5DC75)
2025-05-01 23:30:49,223 - INFO - 开始处理日期: 2025-05-01
2025-05-01 23:30:49,223 - INFO - Request Parameters - Page 1:
2025-05-01 23:30:49,223 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 23:30:49,223 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 23:30:52,805 - INFO - Response - Page 1:
2025-05-01 23:30:52,805 - INFO - 第 1 页获取到 100 条记录
2025-05-01 23:30:53,009 - INFO - Request Parameters - Page 2:
2025-05-01 23:30:53,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-01 23:30:53,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-01 23:30:53,587 - INFO - Response - Page 2:
2025-05-01 23:30:53,587 - INFO - 第 2 页获取到 65 条记录
2025-05-01 23:30:53,791 - INFO - 查询完成，共获取到 165 条记录
2025-05-01 23:30:53,791 - INFO - 获取到 165 条表单数据
2025-05-01 23:30:53,791 - INFO - 当前日期 2025-05-01 有 262 条MySQL数据需要处理
2025-05-01 23:30:53,791 - INFO - 开始批量插入 97 条新记录
2025-05-01 23:30:54,104 - INFO - 批量插入响应状态码: 200
2025-05-01 23:30:54,104 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 01 May 2025 15:30:52 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4668', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D962BDDC-7769-7835-A4E6-FB09ED3B52CC', 'x-acs-trace-id': '0648f53b42d72d65b009e6f6a6df3177', 'etag': '4xkz1otK88FGKTuPZwLWVlw8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-01 23:30:54,104 - INFO - 批量插入响应体: {'result': ['FINST-00D66K71RNZUEUTB8KOY44X2XO0U2RI6VI5AM2G', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2RI6VI5AM3G', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2RI6VI5AM4G', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2RI6VI5AM5G', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2RI6VI5AM6G', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2RI6VI5AM7G', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2RI6VI5AM8G', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2RI6VI5AM9G', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2RI6VI5AMAG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2RI6VI5AMBG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2RI6VI5AMCG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2RI6VI5AMDG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2RI6VI5AMEG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMFG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMGG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMHG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMIG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMJG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMKG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMLG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMMG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMNG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMOG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMPG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMQG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMRG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMSG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMTG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMUG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMVG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMWG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMXG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMYG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMZG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM0H', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM1H', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM2H', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM3H', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM4H', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM5H', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM6H', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM7H', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM8H', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM9H', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMAH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMBH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMCH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMDH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMEH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMFH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMGH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMHH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMIH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMJH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMKH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMLH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMMH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMNH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMOH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMPH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMQH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMRH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMSH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMTH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMUH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMVH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMWH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMXH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMYH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMZH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM0I', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM1I', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM2I', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM3I', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM4I', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM5I', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM6I', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM7I', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM8I', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM9I', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMAI', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMBI', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMCI', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMDI', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMEI', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMFI', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMGI', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMHI', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMII', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMJI', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMKI', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMLI', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMMI', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMNI', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMOI', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMPI', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMQI']}
2025-05-01 23:30:54,104 - INFO - 批量插入表单数据成功，批次 1，共 97 条记录
2025-05-01 23:30:54,104 - INFO - 成功插入的数据ID: ['FINST-00D66K71RNZUEUTB8KOY44X2XO0U2RI6VI5AM2G', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2RI6VI5AM3G', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2RI6VI5AM4G', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2RI6VI5AM5G', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2RI6VI5AM6G', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2RI6VI5AM7G', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2RI6VI5AM8G', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2RI6VI5AM9G', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2RI6VI5AMAG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2RI6VI5AMBG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2RI6VI5AMCG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2RI6VI5AMDG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2RI6VI5AMEG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMFG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMGG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMHG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMIG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMJG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMKG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMLG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMMG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMNG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMOG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMPG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMQG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMRG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMSG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMTG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMUG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMVG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMWG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMXG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMYG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMZG', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM0H', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM1H', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM2H', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM3H', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM4H', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM5H', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM6H', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM7H', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM8H', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM9H', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMAH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMBH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMCH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMDH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMEH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMFH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMGH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMHH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMIH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMJH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMKH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMLH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMMH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMNH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMOH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMPH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMQH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMRH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMSH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMTH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMUH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMVH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMWH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMXH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMYH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMZH', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM0I', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM1I', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM2I', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM3I', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM4I', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM5I', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM6I', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM7I', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM8I', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AM9I', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMAI', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMBI', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMCI', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMDI', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMEI', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMFI', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMGI', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMHI', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMII', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMJI', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMKI', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMLI', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMMI', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMNI', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMOI', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMPI', 'FINST-00D66K71RNZUEUTB8KOY44X2XO0U2SI6VI5AMQI']
2025-05-01 23:30:59,125 - INFO - 批量插入完成，共 97 条记录
2025-05-01 23:30:59,125 - INFO - 日期 2025-05-01 处理完成 - 更新: 0 条，插入: 97 条，错误: 0 条
2025-05-01 23:30:59,125 - INFO - 数据同步完成！更新: 0 条，插入: 97 条，错误: 2 条
2025-05-01 23:31:59,208 - INFO - 开始同步昨天与今天的销售数据: 20250430 至 20250501
2025-05-01 23:31:59,208 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-01 23:31:59,208 - INFO - 查询参数: ('20250430', '20250501')
2025-05-01 23:31:59,255 - INFO - MySQL查询成功，时间段: 20250430 至 20250501，共获取 0 条记录
2025-05-01 23:31:59,255 - ERROR - 未获取到MySQL数据
2025-05-01 23:31:59,255 - INFO - 同步完成
