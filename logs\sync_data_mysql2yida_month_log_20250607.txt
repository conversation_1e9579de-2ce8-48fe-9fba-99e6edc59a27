2025-06-07 00:00:02,636 - INFO - =================使用默认全量同步=============
2025-06-07 00:00:04,261 - INFO - MySQL查询成功，共获取 3924 条记录
2025-06-07 00:00:04,261 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-07 00:00:04,292 - INFO - 开始处理日期: 2025-01
2025-06-07 00:00:04,292 - INFO - Request Parameters - Page 1:
2025-06-07 00:00:04,292 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:04,292 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:05,933 - INFO - Response - Page 1:
2025-06-07 00:00:06,136 - INFO - 第 1 页获取到 100 条记录
2025-06-07 00:00:06,136 - INFO - Request Parameters - Page 2:
2025-06-07 00:00:06,136 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:06,136 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:06,667 - INFO - Response - Page 2:
2025-06-07 00:00:06,870 - INFO - 第 2 页获取到 100 条记录
2025-06-07 00:00:06,870 - INFO - Request Parameters - Page 3:
2025-06-07 00:00:06,870 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:06,870 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:07,354 - INFO - Response - Page 3:
2025-06-07 00:00:07,557 - INFO - 第 3 页获取到 100 条记录
2025-06-07 00:00:07,557 - INFO - Request Parameters - Page 4:
2025-06-07 00:00:07,557 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:07,557 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:08,089 - INFO - Response - Page 4:
2025-06-07 00:00:08,292 - INFO - 第 4 页获取到 100 条记录
2025-06-07 00:00:08,292 - INFO - Request Parameters - Page 5:
2025-06-07 00:00:08,292 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:08,292 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:08,807 - INFO - Response - Page 5:
2025-06-07 00:00:09,010 - INFO - 第 5 页获取到 100 条记录
2025-06-07 00:00:09,010 - INFO - Request Parameters - Page 6:
2025-06-07 00:00:09,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:09,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:09,542 - INFO - Response - Page 6:
2025-06-07 00:00:09,745 - INFO - 第 6 页获取到 100 条记录
2025-06-07 00:00:09,745 - INFO - Request Parameters - Page 7:
2025-06-07 00:00:09,745 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:09,745 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:10,354 - INFO - Response - Page 7:
2025-06-07 00:00:10,557 - INFO - 第 7 页获取到 82 条记录
2025-06-07 00:00:10,557 - INFO - 查询完成，共获取到 682 条记录
2025-06-07 00:00:10,557 - INFO - 获取到 682 条表单数据
2025-06-07 00:00:10,557 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-07 00:00:10,573 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 00:00:10,573 - INFO - 开始处理日期: 2025-02
2025-06-07 00:00:10,573 - INFO - Request Parameters - Page 1:
2025-06-07 00:00:10,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:10,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:11,057 - INFO - Response - Page 1:
2025-06-07 00:00:11,260 - INFO - 第 1 页获取到 100 条记录
2025-06-07 00:00:11,260 - INFO - Request Parameters - Page 2:
2025-06-07 00:00:11,260 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:11,260 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:11,807 - INFO - Response - Page 2:
2025-06-07 00:00:12,010 - INFO - 第 2 页获取到 100 条记录
2025-06-07 00:00:12,010 - INFO - Request Parameters - Page 3:
2025-06-07 00:00:12,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:12,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:12,448 - INFO - Response - Page 3:
2025-06-07 00:00:12,651 - INFO - 第 3 页获取到 100 条记录
2025-06-07 00:00:12,651 - INFO - Request Parameters - Page 4:
2025-06-07 00:00:12,651 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:12,651 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:13,135 - INFO - Response - Page 4:
2025-06-07 00:00:13,338 - INFO - 第 4 页获取到 100 条记录
2025-06-07 00:00:13,338 - INFO - Request Parameters - Page 5:
2025-06-07 00:00:13,338 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:13,338 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:13,838 - INFO - Response - Page 5:
2025-06-07 00:00:14,041 - INFO - 第 5 页获取到 100 条记录
2025-06-07 00:00:14,041 - INFO - Request Parameters - Page 6:
2025-06-07 00:00:14,041 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:14,041 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:14,541 - INFO - Response - Page 6:
2025-06-07 00:00:14,744 - INFO - 第 6 页获取到 100 条记录
2025-06-07 00:00:14,744 - INFO - Request Parameters - Page 7:
2025-06-07 00:00:14,744 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:14,744 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:15,182 - INFO - Response - Page 7:
2025-06-07 00:00:15,385 - INFO - 第 7 页获取到 70 条记录
2025-06-07 00:00:15,385 - INFO - 查询完成，共获取到 670 条记录
2025-06-07 00:00:15,385 - INFO - 获取到 670 条表单数据
2025-06-07 00:00:15,385 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-07 00:00:15,401 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 00:00:15,401 - INFO - 开始处理日期: 2025-03
2025-06-07 00:00:15,401 - INFO - Request Parameters - Page 1:
2025-06-07 00:00:15,401 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:15,401 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:15,947 - INFO - Response - Page 1:
2025-06-07 00:00:16,151 - INFO - 第 1 页获取到 100 条记录
2025-06-07 00:00:16,151 - INFO - Request Parameters - Page 2:
2025-06-07 00:00:16,151 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:16,151 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:16,666 - INFO - Response - Page 2:
2025-06-07 00:00:16,869 - INFO - 第 2 页获取到 100 条记录
2025-06-07 00:00:16,869 - INFO - Request Parameters - Page 3:
2025-06-07 00:00:16,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:16,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:17,307 - INFO - Response - Page 3:
2025-06-07 00:00:17,510 - INFO - 第 3 页获取到 100 条记录
2025-06-07 00:00:17,510 - INFO - Request Parameters - Page 4:
2025-06-07 00:00:17,510 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:17,510 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:17,994 - INFO - Response - Page 4:
2025-06-07 00:00:18,197 - INFO - 第 4 页获取到 100 条记录
2025-06-07 00:00:18,197 - INFO - Request Parameters - Page 5:
2025-06-07 00:00:18,197 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:18,197 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:18,697 - INFO - Response - Page 5:
2025-06-07 00:00:18,900 - INFO - 第 5 页获取到 100 条记录
2025-06-07 00:00:18,900 - INFO - Request Parameters - Page 6:
2025-06-07 00:00:18,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:18,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:19,416 - INFO - Response - Page 6:
2025-06-07 00:00:19,619 - INFO - 第 6 页获取到 100 条记录
2025-06-07 00:00:19,619 - INFO - Request Parameters - Page 7:
2025-06-07 00:00:19,619 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:19,619 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:20,103 - INFO - Response - Page 7:
2025-06-07 00:00:20,306 - INFO - 第 7 页获取到 61 条记录
2025-06-07 00:00:20,306 - INFO - 查询完成，共获取到 661 条记录
2025-06-07 00:00:20,306 - INFO - 获取到 661 条表单数据
2025-06-07 00:00:20,306 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-07 00:00:20,322 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 00:00:20,322 - INFO - 开始处理日期: 2025-04
2025-06-07 00:00:20,322 - INFO - Request Parameters - Page 1:
2025-06-07 00:00:20,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:20,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:20,994 - INFO - Response - Page 1:
2025-06-07 00:00:21,197 - INFO - 第 1 页获取到 100 条记录
2025-06-07 00:00:21,197 - INFO - Request Parameters - Page 2:
2025-06-07 00:00:21,197 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:21,197 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:21,697 - INFO - Response - Page 2:
2025-06-07 00:00:21,900 - INFO - 第 2 页获取到 100 条记录
2025-06-07 00:00:21,900 - INFO - Request Parameters - Page 3:
2025-06-07 00:00:21,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:21,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:22,400 - INFO - Response - Page 3:
2025-06-07 00:00:22,603 - INFO - 第 3 页获取到 100 条记录
2025-06-07 00:00:22,603 - INFO - Request Parameters - Page 4:
2025-06-07 00:00:22,603 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:22,603 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:23,041 - INFO - Response - Page 4:
2025-06-07 00:00:23,244 - INFO - 第 4 页获取到 100 条记录
2025-06-07 00:00:23,244 - INFO - Request Parameters - Page 5:
2025-06-07 00:00:23,244 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:23,244 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:23,697 - INFO - Response - Page 5:
2025-06-07 00:00:23,900 - INFO - 第 5 页获取到 100 条记录
2025-06-07 00:00:23,900 - INFO - Request Parameters - Page 6:
2025-06-07 00:00:23,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:23,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:24,353 - INFO - Response - Page 6:
2025-06-07 00:00:24,556 - INFO - 第 6 页获取到 100 条记录
2025-06-07 00:00:24,556 - INFO - Request Parameters - Page 7:
2025-06-07 00:00:24,556 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:24,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:24,978 - INFO - Response - Page 7:
2025-06-07 00:00:25,181 - INFO - 第 7 页获取到 56 条记录
2025-06-07 00:00:25,181 - INFO - 查询完成，共获取到 656 条记录
2025-06-07 00:00:25,181 - INFO - 获取到 656 条表单数据
2025-06-07 00:00:25,181 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-07 00:00:25,197 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 00:00:25,197 - INFO - 开始处理日期: 2025-05
2025-06-07 00:00:25,197 - INFO - Request Parameters - Page 1:
2025-06-07 00:00:25,197 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:25,197 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:25,634 - INFO - Response - Page 1:
2025-06-07 00:00:25,837 - INFO - 第 1 页获取到 100 条记录
2025-06-07 00:00:25,837 - INFO - Request Parameters - Page 2:
2025-06-07 00:00:25,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:25,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:26,306 - INFO - Response - Page 2:
2025-06-07 00:00:26,509 - INFO - 第 2 页获取到 100 条记录
2025-06-07 00:00:26,509 - INFO - Request Parameters - Page 3:
2025-06-07 00:00:26,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:26,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:27,025 - INFO - Response - Page 3:
2025-06-07 00:00:27,228 - INFO - 第 3 页获取到 100 条记录
2025-06-07 00:00:27,228 - INFO - Request Parameters - Page 4:
2025-06-07 00:00:27,228 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:27,228 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:27,728 - INFO - Response - Page 4:
2025-06-07 00:00:27,931 - INFO - 第 4 页获取到 100 条记录
2025-06-07 00:00:27,931 - INFO - Request Parameters - Page 5:
2025-06-07 00:00:27,931 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:27,931 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:28,415 - INFO - Response - Page 5:
2025-06-07 00:00:28,618 - INFO - 第 5 页获取到 100 条记录
2025-06-07 00:00:28,618 - INFO - Request Parameters - Page 6:
2025-06-07 00:00:28,618 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:28,618 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:29,165 - INFO - Response - Page 6:
2025-06-07 00:00:29,368 - INFO - 第 6 页获取到 100 条记录
2025-06-07 00:00:29,368 - INFO - Request Parameters - Page 7:
2025-06-07 00:00:29,368 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:29,368 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:29,774 - INFO - Response - Page 7:
2025-06-07 00:00:29,978 - INFO - 第 7 页获取到 37 条记录
2025-06-07 00:00:29,978 - INFO - 查询完成，共获取到 637 条记录
2025-06-07 00:00:29,978 - INFO - 获取到 637 条表单数据
2025-06-07 00:00:29,993 - INFO - 当前日期 2025-05 有 637 条MySQL数据需要处理
2025-06-07 00:00:30,009 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 00:00:30,009 - INFO - 开始处理日期: 2025-06
2025-06-07 00:00:30,009 - INFO - Request Parameters - Page 1:
2025-06-07 00:00:30,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:30,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:30,524 - INFO - Response - Page 1:
2025-06-07 00:00:30,728 - INFO - 第 1 页获取到 100 条记录
2025-06-07 00:00:30,728 - INFO - Request Parameters - Page 2:
2025-06-07 00:00:30,728 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:30,728 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:31,259 - INFO - Response - Page 2:
2025-06-07 00:00:31,462 - INFO - 第 2 页获取到 100 条记录
2025-06-07 00:00:31,462 - INFO - Request Parameters - Page 3:
2025-06-07 00:00:31,462 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:31,462 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:32,071 - INFO - Response - Page 3:
2025-06-07 00:00:32,274 - INFO - 第 3 页获取到 100 条记录
2025-06-07 00:00:32,274 - INFO - Request Parameters - Page 4:
2025-06-07 00:00:32,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:32,274 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:32,821 - INFO - Response - Page 4:
2025-06-07 00:00:33,024 - INFO - 第 4 页获取到 100 条记录
2025-06-07 00:00:33,024 - INFO - Request Parameters - Page 5:
2025-06-07 00:00:33,024 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:33,024 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:33,540 - INFO - Response - Page 5:
2025-06-07 00:00:33,743 - INFO - 第 5 页获取到 100 条记录
2025-06-07 00:00:33,743 - INFO - Request Parameters - Page 6:
2025-06-07 00:00:33,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:33,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:34,196 - INFO - Response - Page 6:
2025-06-07 00:00:34,399 - INFO - 第 6 页获取到 100 条记录
2025-06-07 00:00:34,399 - INFO - Request Parameters - Page 7:
2025-06-07 00:00:34,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 00:00:34,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 00:00:34,758 - INFO - Response - Page 7:
2025-06-07 00:00:34,962 - INFO - 第 7 页获取到 18 条记录
2025-06-07 00:00:34,962 - INFO - 查询完成，共获取到 618 条记录
2025-06-07 00:00:34,962 - INFO - 获取到 618 条表单数据
2025-06-07 00:00:34,962 - INFO - 当前日期 2025-06 有 618 条MySQL数据需要处理
2025-06-07 00:00:34,962 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66KD1OESVDO0260ETU6OP80122L7BABDBM8I
2025-06-07 00:00:35,399 - INFO - 更新表单数据成功: FINST-2PF66KD1OESVDO0260ETU6OP80122L7BABDBM8I
2025-06-07 00:00:35,399 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17449.74, 'new_value': 20146.17}, {'field': 'offline_amount', 'old_value': 172186.64, 'new_value': 215209.72}, {'field': 'total_amount', 'old_value': 185643.89, 'new_value': 231363.4}, {'field': 'order_count', 'old_value': 908, 'new_value': 1092}]
2025-06-07 00:00:35,399 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMPB1
2025-06-07 00:00:35,836 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMPB1
2025-06-07 00:00:35,836 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5254.28, 'new_value': 6510.38}, {'field': 'offline_amount', 'old_value': 2309.84, 'new_value': 2855.44}, {'field': 'total_amount', 'old_value': 7564.12, 'new_value': 9365.82}, {'field': 'order_count', 'old_value': 319, 'new_value': 393}]
2025-06-07 00:00:35,836 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMYB1
2025-06-07 00:00:36,290 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMYB1
2025-06-07 00:00:36,290 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57969.0, 'new_value': 60968.0}, {'field': 'total_amount', 'old_value': 57969.0, 'new_value': 60968.0}, {'field': 'order_count', 'old_value': 248, 'new_value': 295}]
2025-06-07 00:00:36,290 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMZB1
2025-06-07 00:00:36,711 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMZB1
2025-06-07 00:00:36,711 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8198.46, 'new_value': 13745.49}, {'field': 'total_amount', 'old_value': 8198.46, 'new_value': 13745.49}, {'field': 'order_count', 'old_value': 31, 'new_value': 41}]
2025-06-07 00:00:36,711 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMAB
2025-06-07 00:00:37,102 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMAB
2025-06-07 00:00:37,102 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14130.85, 'new_value': 15357.85}, {'field': 'total_amount', 'old_value': 19798.56, 'new_value': 21025.56}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-07 00:00:37,102 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM4C1
2025-06-07 00:00:37,539 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM4C1
2025-06-07 00:00:37,539 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 199.0, 'new_value': 5199.0}, {'field': 'total_amount', 'old_value': 876.0, 'new_value': 5876.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-06-07 00:00:37,539 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM45
2025-06-07 00:00:37,993 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM45
2025-06-07 00:00:37,993 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 278525.05, 'new_value': 334846.33}, {'field': 'total_amount', 'old_value': 278525.05, 'new_value': 334846.33}, {'field': 'order_count', 'old_value': 3332, 'new_value': 4214}]
2025-06-07 00:00:37,993 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM9C1
2025-06-07 00:00:38,383 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM9C1
2025-06-07 00:00:38,383 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8930.0, 'new_value': 14730.0}, {'field': 'total_amount', 'old_value': 8930.0, 'new_value': 14730.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-06-07 00:00:38,383 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMCB
2025-06-07 00:00:38,789 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMCB
2025-06-07 00:00:38,789 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2592.0, 'new_value': 2880.0}, {'field': 'total_amount', 'old_value': 2592.0, 'new_value': 2880.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-07 00:00:38,789 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMDB
2025-06-07 00:00:39,242 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMDB
2025-06-07 00:00:39,242 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5717.4, 'new_value': 6566.4}, {'field': 'total_amount', 'old_value': 5717.4, 'new_value': 6566.4}, {'field': 'order_count', 'old_value': 28, 'new_value': 33}]
2025-06-07 00:00:39,242 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMEC1
2025-06-07 00:00:39,680 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMEC1
2025-06-07 00:00:39,680 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8298.0, 'new_value': 11997.0}, {'field': 'total_amount', 'old_value': 8298.0, 'new_value': 11997.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-06-07 00:00:39,680 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM95
2025-06-07 00:00:40,102 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM95
2025-06-07 00:00:40,102 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9836.11, 'new_value': 11778.62}, {'field': 'offline_amount', 'old_value': 113686.52, 'new_value': 145139.62}, {'field': 'total_amount', 'old_value': 123522.63, 'new_value': 156918.24}, {'field': 'order_count', 'old_value': 522, 'new_value': 650}]
2025-06-07 00:00:40,102 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMEB
2025-06-07 00:00:40,539 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMEB
2025-06-07 00:00:40,539 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3977.0, 'new_value': 4516.0}, {'field': 'total_amount', 'old_value': 3977.0, 'new_value': 4516.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 20}]
2025-06-07 00:00:40,539 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMFB
2025-06-07 00:00:40,992 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMFB
2025-06-07 00:00:40,992 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3500.74, 'new_value': 4240.14}, {'field': 'total_amount', 'old_value': 6068.34, 'new_value': 6807.74}, {'field': 'order_count', 'old_value': 24, 'new_value': 28}]
2025-06-07 00:00:40,992 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMI5
2025-06-07 00:00:41,414 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMI5
2025-06-07 00:00:41,414 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6986.0, 'new_value': 12090.0}, {'field': 'total_amount', 'old_value': 9295.0, 'new_value': 14399.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 13}]
2025-06-07 00:00:41,414 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMGB
2025-06-07 00:00:41,852 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMGB
2025-06-07 00:00:41,852 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19363.0, 'new_value': 20731.9}, {'field': 'total_amount', 'old_value': 22950.6, 'new_value': 24319.5}, {'field': 'order_count', 'old_value': 27, 'new_value': 30}]
2025-06-07 00:00:41,852 - INFO - 开始更新记录 - 表单实例ID: FINST-OLF66Q7175TVPOUV9A4TL9SL8U353CW11AFBMT31
2025-06-07 00:00:42,305 - INFO - 更新表单数据成功: FINST-OLF66Q7175TVPOUV9A4TL9SL8U353CW11AFBMT31
2025-06-07 00:00:42,305 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 350.0, 'new_value': 416.37}, {'field': 'offline_amount', 'old_value': 3795.79, 'new_value': 4614.86}, {'field': 'total_amount', 'old_value': 4145.79, 'new_value': 5031.23}, {'field': 'order_count', 'old_value': 73, 'new_value': 96}]
2025-06-07 00:00:42,305 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMHB
2025-06-07 00:00:42,789 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMHB
2025-06-07 00:00:42,789 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-06-07 00:00:42,789 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMRC1
2025-06-07 00:00:43,227 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMRC1
2025-06-07 00:00:43,227 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55334.82, 'new_value': 68502.85}, {'field': 'total_amount', 'old_value': 55334.82, 'new_value': 68502.85}, {'field': 'order_count', 'old_value': 1604, 'new_value': 2003}]
2025-06-07 00:00:43,227 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMIB
2025-06-07 00:00:43,633 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMIB
2025-06-07 00:00:43,633 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18017.1, 'new_value': 20516.8}, {'field': 'total_amount', 'old_value': 18017.1, 'new_value': 20516.8}, {'field': 'order_count', 'old_value': 61, 'new_value': 72}]
2025-06-07 00:00:43,633 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMSC1
2025-06-07 00:00:44,101 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMSC1
2025-06-07 00:00:44,101 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2120.0, 'new_value': 3970.0}, {'field': 'total_amount', 'old_value': 2120.0, 'new_value': 3970.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 15}]
2025-06-07 00:00:44,101 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMUC1
2025-06-07 00:00:44,539 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMUC1
2025-06-07 00:00:44,539 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8604.0, 'new_value': 43601.0}, {'field': 'offline_amount', 'old_value': 13644.0, 'new_value': 39883.0}, {'field': 'total_amount', 'old_value': 22248.0, 'new_value': 83484.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 45}]
2025-06-07 00:00:44,539 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMJB
2025-06-07 00:00:44,992 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMJB
2025-06-07 00:00:44,992 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16145.35, 'new_value': 19575.05}, {'field': 'total_amount', 'old_value': 16145.35, 'new_value': 19575.05}, {'field': 'order_count', 'old_value': 526, 'new_value': 628}]
2025-06-07 00:00:44,992 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMLB
2025-06-07 00:00:45,445 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMLB
2025-06-07 00:00:45,445 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12844.0, 'new_value': 13533.0}, {'field': 'total_amount', 'old_value': 12844.0, 'new_value': 13533.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 33}]
2025-06-07 00:00:45,445 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMMB
2025-06-07 00:00:45,867 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMMB
2025-06-07 00:00:45,867 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7676.7, 'new_value': 8429.7}, {'field': 'total_amount', 'old_value': 7676.7, 'new_value': 8429.7}, {'field': 'order_count', 'old_value': 42, 'new_value': 47}]
2025-06-07 00:00:45,867 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMNB
2025-06-07 00:00:46,258 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMNB
2025-06-07 00:00:46,258 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34755.0, 'new_value': 39644.0}, {'field': 'total_amount', 'old_value': 34755.0, 'new_value': 39644.0}, {'field': 'order_count', 'old_value': 127, 'new_value': 143}]
2025-06-07 00:00:46,258 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM2D1
2025-06-07 00:00:46,679 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM2D1
2025-06-07 00:00:46,679 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42504.8, 'new_value': 52602.8}, {'field': 'total_amount', 'old_value': 42504.8, 'new_value': 52602.8}, {'field': 'order_count', 'old_value': 233, 'new_value': 289}]
2025-06-07 00:00:46,679 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMOB
2025-06-07 00:00:47,164 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMOB
2025-06-07 00:00:47,164 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10847.81, 'new_value': 12693.28}, {'field': 'offline_amount', 'old_value': 18858.51, 'new_value': 21191.43}, {'field': 'total_amount', 'old_value': 29706.32, 'new_value': 33884.71}, {'field': 'order_count', 'old_value': 1059, 'new_value': 1226}]
2025-06-07 00:00:47,164 - INFO - 开始更新记录 - 表单实例ID: FINST-OLF66Q7175TVPOUV9A4TL9SL8U353CW11AFBMS31
2025-06-07 00:00:47,617 - INFO - 更新表单数据成功: FINST-OLF66Q7175TVPOUV9A4TL9SL8U353CW11AFBMS31
2025-06-07 00:00:47,617 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9800.26, 'new_value': 13210.94}, {'field': 'offline_amount', 'old_value': 16647.65, 'new_value': 21494.17}, {'field': 'total_amount', 'old_value': 26447.91, 'new_value': 34705.11}, {'field': 'order_count', 'old_value': 1259, 'new_value': 1650}]
2025-06-07 00:00:47,617 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMPB
2025-06-07 00:00:48,054 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMPB
2025-06-07 00:00:48,054 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23027.47, 'new_value': 25513.44}, {'field': 'total_amount', 'old_value': 23027.47, 'new_value': 25513.44}, {'field': 'order_count', 'old_value': 811, 'new_value': 910}]
2025-06-07 00:00:48,054 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM16
2025-06-07 00:00:48,632 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM16
2025-06-07 00:00:48,632 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78018.39, 'new_value': 89839.69}, {'field': 'total_amount', 'old_value': 78018.39, 'new_value': 89839.69}, {'field': 'order_count', 'old_value': 667, 'new_value': 717}]
2025-06-07 00:00:48,632 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMQB
2025-06-07 00:00:48,992 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMQB
2025-06-07 00:00:48,992 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3105.6, 'new_value': 3542.6}, {'field': 'total_amount', 'old_value': 3105.6, 'new_value': 3542.6}, {'field': 'order_count', 'old_value': 21, 'new_value': 26}]
2025-06-07 00:00:48,992 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM36
2025-06-07 00:00:49,429 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM36
2025-06-07 00:00:49,429 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3776.7, 'new_value': 4515.16}, {'field': 'offline_amount', 'old_value': 3086.6, 'new_value': 3367.9}, {'field': 'total_amount', 'old_value': 6863.3, 'new_value': 7883.06}, {'field': 'order_count', 'old_value': 472, 'new_value': 542}]
2025-06-07 00:00:49,429 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMRB
2025-06-07 00:00:49,882 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMRB
2025-06-07 00:00:49,882 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66893.0, 'new_value': 77372.0}, {'field': 'total_amount', 'old_value': 101893.0, 'new_value': 112372.0}, {'field': 'order_count', 'old_value': 915, 'new_value': 1041}]
2025-06-07 00:00:49,882 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM56
2025-06-07 00:00:50,320 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM56
2025-06-07 00:00:50,320 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7357.99, 'new_value': 11973.72}, {'field': 'offline_amount', 'old_value': 76072.91, 'new_value': 84000.51}, {'field': 'total_amount', 'old_value': 83430.9, 'new_value': 95974.23}, {'field': 'order_count', 'old_value': 1865, 'new_value': 2109}]
2025-06-07 00:00:50,320 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMC6
2025-06-07 00:00:50,679 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMC6
2025-06-07 00:00:50,679 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21124.29, 'new_value': 23923.28}, {'field': 'offline_amount', 'old_value': 66681.51, 'new_value': 75749.75}, {'field': 'total_amount', 'old_value': 87805.8, 'new_value': 99673.03}, {'field': 'order_count', 'old_value': 996, 'new_value': 1121}]
2025-06-07 00:00:50,679 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMSB
2025-06-07 00:00:51,132 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMSB
2025-06-07 00:00:51,132 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2177.29, 'new_value': 2593.9}, {'field': 'offline_amount', 'old_value': 7146.42, 'new_value': 8039.93}, {'field': 'total_amount', 'old_value': 9323.71, 'new_value': 10633.83}, {'field': 'order_count', 'old_value': 340, 'new_value': 381}]
2025-06-07 00:00:51,132 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMTB
2025-06-07 00:00:51,554 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMTB
2025-06-07 00:00:51,554 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6829.08, 'new_value': 7829.08}, {'field': 'offline_amount', 'old_value': 8797.69, 'new_value': 10425.85}, {'field': 'total_amount', 'old_value': 15626.77, 'new_value': 18254.93}, {'field': 'order_count', 'old_value': 705, 'new_value': 843}]
2025-06-07 00:00:51,554 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMUB
2025-06-07 00:00:51,976 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMUB
2025-06-07 00:00:51,976 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15347.76, 'new_value': 18785.6}, {'field': 'offline_amount', 'old_value': 7680.71, 'new_value': 8492.09}, {'field': 'total_amount', 'old_value': 23028.47, 'new_value': 27277.69}, {'field': 'order_count', 'old_value': 1419, 'new_value': 1702}]
2025-06-07 00:00:51,976 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMD6
2025-06-07 00:00:52,413 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMD6
2025-06-07 00:00:52,413 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8482.0, 'new_value': 9856.0}, {'field': 'offline_amount', 'old_value': 29320.32, 'new_value': 30388.92}, {'field': 'total_amount', 'old_value': 37802.32, 'new_value': 40244.92}, {'field': 'order_count', 'old_value': 54, 'new_value': 59}]
2025-06-07 00:00:52,413 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBME6
2025-06-07 00:00:52,835 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBME6
2025-06-07 00:00:52,835 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100542.38, 'new_value': 118751.48}, {'field': 'total_amount', 'old_value': 100542.38, 'new_value': 118751.48}, {'field': 'order_count', 'old_value': 1362, 'new_value': 1636}]
2025-06-07 00:00:52,835 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMR7
2025-06-07 00:00:53,257 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMR7
2025-06-07 00:00:53,257 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31081.69, 'new_value': 36196.13}, {'field': 'offline_amount', 'old_value': 21803.0, 'new_value': 23669.0}, {'field': 'total_amount', 'old_value': 52884.69, 'new_value': 59865.13}, {'field': 'order_count', 'old_value': 490, 'new_value': 573}]
2025-06-07 00:00:53,257 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMG6
2025-06-07 00:00:53,694 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMG6
2025-06-07 00:00:53,694 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49293.39, 'new_value': 59197.66}, {'field': 'total_amount', 'old_value': 57908.92, 'new_value': 67813.19}, {'field': 'order_count', 'old_value': 2501, 'new_value': 2966}]
2025-06-07 00:00:53,694 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMVB
2025-06-07 00:00:54,163 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMVB
2025-06-07 00:00:54,163 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12933.56, 'new_value': 16430.89}, {'field': 'offline_amount', 'old_value': 18110.41, 'new_value': 20384.96}, {'field': 'total_amount', 'old_value': 31043.97, 'new_value': 36815.85}, {'field': 'order_count', 'old_value': 1312, 'new_value': 1549}]
2025-06-07 00:00:54,163 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMJ6
2025-06-07 00:00:54,601 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMJ6
2025-06-07 00:00:54,601 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2075.75, 'new_value': 2501.25}, {'field': 'offline_amount', 'old_value': 7473.1, 'new_value': 8212.9}, {'field': 'total_amount', 'old_value': 9548.85, 'new_value': 10714.15}, {'field': 'order_count', 'old_value': 385, 'new_value': 446}]
2025-06-07 00:00:54,601 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMM6
2025-06-07 00:00:55,054 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMM6
2025-06-07 00:00:55,054 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13663.8, 'new_value': 15337.6}, {'field': 'offline_amount', 'old_value': 59562.6, 'new_value': 61813.2}, {'field': 'total_amount', 'old_value': 73226.4, 'new_value': 77150.8}, {'field': 'order_count', 'old_value': 1477, 'new_value': 1555}]
2025-06-07 00:00:55,054 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMWB
2025-06-07 00:00:55,491 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMWB
2025-06-07 00:00:55,491 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5200.0, 'new_value': 5918.0}, {'field': 'total_amount', 'old_value': 5200.0, 'new_value': 5918.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 21}]
2025-06-07 00:00:55,491 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM28
2025-06-07 00:00:55,897 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM28
2025-06-07 00:00:55,897 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10451.0, 'new_value': 16141.0}, {'field': 'total_amount', 'old_value': 10451.0, 'new_value': 16141.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 13}]
2025-06-07 00:00:55,897 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMYB
2025-06-07 00:00:56,335 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMYB
2025-06-07 00:00:56,335 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29479.0, 'new_value': 32199.0}, {'field': 'total_amount', 'old_value': 29479.0, 'new_value': 32199.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 17}]
2025-06-07 00:00:56,335 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMY6
2025-06-07 00:00:56,772 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMY6
2025-06-07 00:00:56,772 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3238.0, 'new_value': 3755.0}, {'field': 'total_amount', 'old_value': 3238.0, 'new_value': 3755.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 9}]
2025-06-07 00:00:56,788 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM17
2025-06-07 00:00:57,210 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM17
2025-06-07 00:00:57,210 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46084.06, 'new_value': 52837.02}, {'field': 'total_amount', 'old_value': 46084.06, 'new_value': 52837.02}, {'field': 'order_count', 'old_value': 316, 'new_value': 372}]
2025-06-07 00:00:57,210 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMAQ1
2025-06-07 00:00:57,663 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMAQ1
2025-06-07 00:00:57,663 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18381.59, 'new_value': 21746.21}, {'field': 'offline_amount', 'old_value': 169202.46, 'new_value': 209697.11}, {'field': 'total_amount', 'old_value': 187584.05, 'new_value': 231443.32}, {'field': 'order_count', 'old_value': 782, 'new_value': 967}]
2025-06-07 00:00:57,663 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMCQ1
2025-06-07 00:00:58,179 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMCQ1
2025-06-07 00:00:58,179 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1728.0, 'new_value': 4108.0}, {'field': 'total_amount', 'old_value': 1728.0, 'new_value': 4108.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 10}]
2025-06-07 00:00:58,179 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM27
2025-06-07 00:00:58,632 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM27
2025-06-07 00:00:58,632 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97658.53, 'new_value': 111815.51}, {'field': 'total_amount', 'old_value': 97658.53, 'new_value': 111815.51}, {'field': 'order_count', 'old_value': 3709, 'new_value': 4312}]
2025-06-07 00:00:58,632 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMGQ1
2025-06-07 00:00:59,038 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMGQ1
2025-06-07 00:00:59,038 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 318821.76, 'new_value': 388752.54}, {'field': 'offline_amount', 'old_value': 20368.0, 'new_value': 28712.0}, {'field': 'total_amount', 'old_value': 339189.76, 'new_value': 417464.54}, {'field': 'order_count', 'old_value': 1267, 'new_value': 1569}]
2025-06-07 00:00:59,038 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM1C
2025-06-07 00:00:59,428 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM1C
2025-06-07 00:00:59,428 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67806.46, 'new_value': 81046.78}, {'field': 'offline_amount', 'old_value': 10683.94, 'new_value': 11198.14}, {'field': 'total_amount', 'old_value': 78490.4, 'new_value': 92244.92}, {'field': 'order_count', 'old_value': 3104, 'new_value': 3599}]
2025-06-07 00:00:59,428 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM6C
2025-06-07 00:00:59,882 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM6C
2025-06-07 00:00:59,882 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42883.7, 'new_value': 51932.08}, {'field': 'offline_amount', 'old_value': 66480.4, 'new_value': 77151.4}, {'field': 'total_amount', 'old_value': 109364.1, 'new_value': 129083.48}, {'field': 'order_count', 'old_value': 3324, 'new_value': 3994}]
2025-06-07 00:00:59,882 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM9C
2025-06-07 00:01:00,303 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM9C
2025-06-07 00:01:00,303 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17945.92, 'new_value': 19666.66}, {'field': 'offline_amount', 'old_value': 29322.9, 'new_value': 33921.2}, {'field': 'total_amount', 'old_value': 47268.82, 'new_value': 53587.86}, {'field': 'order_count', 'old_value': 506, 'new_value': 572}]
2025-06-07 00:01:00,303 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM0R1
2025-06-07 00:01:00,803 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM0R1
2025-06-07 00:01:00,803 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26289.79, 'new_value': 33556.46}, {'field': 'total_amount', 'old_value': 26289.79, 'new_value': 33556.46}, {'field': 'order_count', 'old_value': 119, 'new_value': 152}]
2025-06-07 00:01:00,803 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMLO
2025-06-07 00:01:01,288 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMLO
2025-06-07 00:01:01,288 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16991.0, 'new_value': 21759.0}, {'field': 'offline_amount', 'old_value': 31718.0, 'new_value': 40057.0}, {'field': 'total_amount', 'old_value': 48709.0, 'new_value': 61816.0}, {'field': 'order_count', 'old_value': 1000, 'new_value': 1241}]
2025-06-07 00:01:01,288 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM1P
2025-06-07 00:01:01,709 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM1P
2025-06-07 00:01:01,709 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2248.08, 'new_value': 2422.78}, {'field': 'offline_amount', 'old_value': 19664.13, 'new_value': 21687.54}, {'field': 'total_amount', 'old_value': 21912.21, 'new_value': 24110.32}, {'field': 'order_count', 'old_value': 676, 'new_value': 763}]
2025-06-07 00:01:01,709 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMFP
2025-06-07 00:01:02,163 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMFP
2025-06-07 00:01:02,163 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 101524.0, 'new_value': 144250.0}, {'field': 'total_amount', 'old_value': 101524.0, 'new_value': 144250.0}, {'field': 'order_count', 'old_value': 113, 'new_value': 150}]
2025-06-07 00:01:02,163 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMGP
2025-06-07 00:01:02,616 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMGP
2025-06-07 00:01:02,616 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 292.0, 'new_value': 690.0}, {'field': 'total_amount', 'old_value': 292.0, 'new_value': 690.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-06-07 00:01:02,616 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMIP
2025-06-07 00:01:03,069 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMIP
2025-06-07 00:01:03,069 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13324.1, 'new_value': 14849.7}, {'field': 'total_amount', 'old_value': 13324.1, 'new_value': 14849.7}, {'field': 'order_count', 'old_value': 107, 'new_value': 114}]
2025-06-07 00:01:03,069 - INFO - 日期 2025-06 处理完成 - 更新: 64 条，插入: 0 条，错误: 0 条
2025-06-07 00:01:03,069 - INFO - 数据同步完成！更新: 64 条，插入: 0 条，错误: 0 条
2025-06-07 00:01:03,069 - INFO - =================同步完成====================
2025-06-07 03:00:02,539 - INFO - =================使用默认全量同步=============
2025-06-07 03:00:04,148 - INFO - MySQL查询成功，共获取 3924 条记录
2025-06-07 03:00:04,148 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-07 03:00:04,179 - INFO - 开始处理日期: 2025-01
2025-06-07 03:00:04,179 - INFO - Request Parameters - Page 1:
2025-06-07 03:00:04,179 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:04,179 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:05,476 - INFO - Response - Page 1:
2025-06-07 03:00:05,679 - INFO - 第 1 页获取到 100 条记录
2025-06-07 03:00:05,679 - INFO - Request Parameters - Page 2:
2025-06-07 03:00:05,679 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:05,679 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:06,164 - INFO - Response - Page 2:
2025-06-07 03:00:06,367 - INFO - 第 2 页获取到 100 条记录
2025-06-07 03:00:06,367 - INFO - Request Parameters - Page 3:
2025-06-07 03:00:06,367 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:06,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:06,851 - INFO - Response - Page 3:
2025-06-07 03:00:07,054 - INFO - 第 3 页获取到 100 条记录
2025-06-07 03:00:07,054 - INFO - Request Parameters - Page 4:
2025-06-07 03:00:07,054 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:07,054 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:07,945 - INFO - Response - Page 4:
2025-06-07 03:00:08,148 - INFO - 第 4 页获取到 100 条记录
2025-06-07 03:00:08,148 - INFO - Request Parameters - Page 5:
2025-06-07 03:00:08,148 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:08,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:08,867 - INFO - Response - Page 5:
2025-06-07 03:00:09,070 - INFO - 第 5 页获取到 100 条记录
2025-06-07 03:00:09,070 - INFO - Request Parameters - Page 6:
2025-06-07 03:00:09,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:09,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:09,554 - INFO - Response - Page 6:
2025-06-07 03:00:09,757 - INFO - 第 6 页获取到 100 条记录
2025-06-07 03:00:09,757 - INFO - Request Parameters - Page 7:
2025-06-07 03:00:09,757 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:09,757 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:10,195 - INFO - Response - Page 7:
2025-06-07 03:00:10,398 - INFO - 第 7 页获取到 82 条记录
2025-06-07 03:00:10,398 - INFO - 查询完成，共获取到 682 条记录
2025-06-07 03:00:10,398 - INFO - 获取到 682 条表单数据
2025-06-07 03:00:10,398 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-07 03:00:10,413 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 03:00:10,413 - INFO - 开始处理日期: 2025-02
2025-06-07 03:00:10,413 - INFO - Request Parameters - Page 1:
2025-06-07 03:00:10,413 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:10,413 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:10,976 - INFO - Response - Page 1:
2025-06-07 03:00:11,179 - INFO - 第 1 页获取到 100 条记录
2025-06-07 03:00:11,179 - INFO - Request Parameters - Page 2:
2025-06-07 03:00:11,179 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:11,179 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:11,663 - INFO - Response - Page 2:
2025-06-07 03:00:11,866 - INFO - 第 2 页获取到 100 条记录
2025-06-07 03:00:11,866 - INFO - Request Parameters - Page 3:
2025-06-07 03:00:11,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:11,866 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:12,398 - INFO - Response - Page 3:
2025-06-07 03:00:12,601 - INFO - 第 3 页获取到 100 条记录
2025-06-07 03:00:12,601 - INFO - Request Parameters - Page 4:
2025-06-07 03:00:12,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:12,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:13,085 - INFO - Response - Page 4:
2025-06-07 03:00:13,288 - INFO - 第 4 页获取到 100 条记录
2025-06-07 03:00:13,288 - INFO - Request Parameters - Page 5:
2025-06-07 03:00:13,288 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:13,288 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:13,741 - INFO - Response - Page 5:
2025-06-07 03:00:13,944 - INFO - 第 5 页获取到 100 条记录
2025-06-07 03:00:13,944 - INFO - Request Parameters - Page 6:
2025-06-07 03:00:13,944 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:13,944 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:14,491 - INFO - Response - Page 6:
2025-06-07 03:00:14,694 - INFO - 第 6 页获取到 100 条记录
2025-06-07 03:00:14,694 - INFO - Request Parameters - Page 7:
2025-06-07 03:00:14,694 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:14,694 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:15,179 - INFO - Response - Page 7:
2025-06-07 03:00:15,397 - INFO - 第 7 页获取到 70 条记录
2025-06-07 03:00:15,397 - INFO - 查询完成，共获取到 670 条记录
2025-06-07 03:00:15,397 - INFO - 获取到 670 条表单数据
2025-06-07 03:00:15,397 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-07 03:00:15,413 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 03:00:15,413 - INFO - 开始处理日期: 2025-03
2025-06-07 03:00:15,413 - INFO - Request Parameters - Page 1:
2025-06-07 03:00:15,413 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:15,413 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:15,944 - INFO - Response - Page 1:
2025-06-07 03:00:16,147 - INFO - 第 1 页获取到 100 条记录
2025-06-07 03:00:16,147 - INFO - Request Parameters - Page 2:
2025-06-07 03:00:16,147 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:16,147 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:16,725 - INFO - Response - Page 2:
2025-06-07 03:00:16,928 - INFO - 第 2 页获取到 100 条记录
2025-06-07 03:00:16,928 - INFO - Request Parameters - Page 3:
2025-06-07 03:00:16,928 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:16,928 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:17,428 - INFO - Response - Page 3:
2025-06-07 03:00:17,632 - INFO - 第 3 页获取到 100 条记录
2025-06-07 03:00:17,632 - INFO - Request Parameters - Page 4:
2025-06-07 03:00:17,632 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:17,632 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:18,116 - INFO - Response - Page 4:
2025-06-07 03:00:18,319 - INFO - 第 4 页获取到 100 条记录
2025-06-07 03:00:18,319 - INFO - Request Parameters - Page 5:
2025-06-07 03:00:18,319 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:18,319 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:18,881 - INFO - Response - Page 5:
2025-06-07 03:00:19,085 - INFO - 第 5 页获取到 100 条记录
2025-06-07 03:00:19,085 - INFO - Request Parameters - Page 6:
2025-06-07 03:00:19,085 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:19,085 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:19,600 - INFO - Response - Page 6:
2025-06-07 03:00:19,803 - INFO - 第 6 页获取到 100 条记录
2025-06-07 03:00:19,803 - INFO - Request Parameters - Page 7:
2025-06-07 03:00:19,803 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:19,803 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:20,303 - INFO - Response - Page 7:
2025-06-07 03:00:20,506 - INFO - 第 7 页获取到 61 条记录
2025-06-07 03:00:20,506 - INFO - 查询完成，共获取到 661 条记录
2025-06-07 03:00:20,506 - INFO - 获取到 661 条表单数据
2025-06-07 03:00:20,506 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-07 03:00:20,522 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 03:00:20,522 - INFO - 开始处理日期: 2025-04
2025-06-07 03:00:20,522 - INFO - Request Parameters - Page 1:
2025-06-07 03:00:20,522 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:20,522 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:21,038 - INFO - Response - Page 1:
2025-06-07 03:00:21,241 - INFO - 第 1 页获取到 100 条记录
2025-06-07 03:00:21,241 - INFO - Request Parameters - Page 2:
2025-06-07 03:00:21,241 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:21,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:21,756 - INFO - Response - Page 2:
2025-06-07 03:00:21,959 - INFO - 第 2 页获取到 100 条记录
2025-06-07 03:00:21,959 - INFO - Request Parameters - Page 3:
2025-06-07 03:00:21,959 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:21,959 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:22,491 - INFO - Response - Page 3:
2025-06-07 03:00:22,694 - INFO - 第 3 页获取到 100 条记录
2025-06-07 03:00:22,694 - INFO - Request Parameters - Page 4:
2025-06-07 03:00:22,694 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:22,694 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:23,319 - INFO - Response - Page 4:
2025-06-07 03:00:23,522 - INFO - 第 4 页获取到 100 条记录
2025-06-07 03:00:23,522 - INFO - Request Parameters - Page 5:
2025-06-07 03:00:23,522 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:23,522 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:24,100 - INFO - Response - Page 5:
2025-06-07 03:00:24,303 - INFO - 第 5 页获取到 100 条记录
2025-06-07 03:00:24,303 - INFO - Request Parameters - Page 6:
2025-06-07 03:00:24,303 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:24,303 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:24,787 - INFO - Response - Page 6:
2025-06-07 03:00:24,990 - INFO - 第 6 页获取到 100 条记录
2025-06-07 03:00:24,990 - INFO - Request Parameters - Page 7:
2025-06-07 03:00:24,990 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:24,990 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:25,475 - INFO - Response - Page 7:
2025-06-07 03:00:25,678 - INFO - 第 7 页获取到 56 条记录
2025-06-07 03:00:25,678 - INFO - 查询完成，共获取到 656 条记录
2025-06-07 03:00:25,678 - INFO - 获取到 656 条表单数据
2025-06-07 03:00:25,678 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-07 03:00:25,693 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 03:00:25,693 - INFO - 开始处理日期: 2025-05
2025-06-07 03:00:25,693 - INFO - Request Parameters - Page 1:
2025-06-07 03:00:25,693 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:25,693 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:26,193 - INFO - Response - Page 1:
2025-06-07 03:00:26,397 - INFO - 第 1 页获取到 100 条记录
2025-06-07 03:00:26,397 - INFO - Request Parameters - Page 2:
2025-06-07 03:00:26,397 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:26,397 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:26,865 - INFO - Response - Page 2:
2025-06-07 03:00:27,068 - INFO - 第 2 页获取到 100 条记录
2025-06-07 03:00:27,068 - INFO - Request Parameters - Page 3:
2025-06-07 03:00:27,068 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:27,068 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:27,521 - INFO - Response - Page 3:
2025-06-07 03:00:27,725 - INFO - 第 3 页获取到 100 条记录
2025-06-07 03:00:27,725 - INFO - Request Parameters - Page 4:
2025-06-07 03:00:27,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:27,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:28,240 - INFO - Response - Page 4:
2025-06-07 03:00:28,443 - INFO - 第 4 页获取到 100 条记录
2025-06-07 03:00:28,443 - INFO - Request Parameters - Page 5:
2025-06-07 03:00:28,443 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:28,443 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:28,896 - INFO - Response - Page 5:
2025-06-07 03:00:29,099 - INFO - 第 5 页获取到 100 条记录
2025-06-07 03:00:29,099 - INFO - Request Parameters - Page 6:
2025-06-07 03:00:29,099 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:29,099 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:29,537 - INFO - Response - Page 6:
2025-06-07 03:00:29,740 - INFO - 第 6 页获取到 100 条记录
2025-06-07 03:00:29,740 - INFO - Request Parameters - Page 7:
2025-06-07 03:00:29,740 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:29,740 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:30,115 - INFO - Response - Page 7:
2025-06-07 03:00:30,318 - INFO - 第 7 页获取到 37 条记录
2025-06-07 03:00:30,318 - INFO - 查询完成，共获取到 637 条记录
2025-06-07 03:00:30,318 - INFO - 获取到 637 条表单数据
2025-06-07 03:00:30,318 - INFO - 当前日期 2025-05 有 637 条MySQL数据需要处理
2025-06-07 03:00:30,334 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 03:00:30,334 - INFO - 开始处理日期: 2025-06
2025-06-07 03:00:30,334 - INFO - Request Parameters - Page 1:
2025-06-07 03:00:30,334 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:30,334 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:30,834 - INFO - Response - Page 1:
2025-06-07 03:00:31,037 - INFO - 第 1 页获取到 100 条记录
2025-06-07 03:00:31,037 - INFO - Request Parameters - Page 2:
2025-06-07 03:00:31,037 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:31,037 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:31,521 - INFO - Response - Page 2:
2025-06-07 03:00:31,724 - INFO - 第 2 页获取到 100 条记录
2025-06-07 03:00:31,724 - INFO - Request Parameters - Page 3:
2025-06-07 03:00:31,724 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:31,724 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:32,209 - INFO - Response - Page 3:
2025-06-07 03:00:32,412 - INFO - 第 3 页获取到 100 条记录
2025-06-07 03:00:32,412 - INFO - Request Parameters - Page 4:
2025-06-07 03:00:32,412 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:32,412 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:32,959 - INFO - Response - Page 4:
2025-06-07 03:00:33,162 - INFO - 第 4 页获取到 100 条记录
2025-06-07 03:00:33,162 - INFO - Request Parameters - Page 5:
2025-06-07 03:00:33,162 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:33,162 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:33,708 - INFO - Response - Page 5:
2025-06-07 03:00:33,912 - INFO - 第 5 页获取到 100 条记录
2025-06-07 03:00:33,912 - INFO - Request Parameters - Page 6:
2025-06-07 03:00:33,912 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:33,912 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:34,396 - INFO - Response - Page 6:
2025-06-07 03:00:34,599 - INFO - 第 6 页获取到 100 条记录
2025-06-07 03:00:34,599 - INFO - Request Parameters - Page 7:
2025-06-07 03:00:34,599 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 03:00:34,599 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 03:00:34,943 - INFO - Response - Page 7:
2025-06-07 03:00:35,146 - INFO - 第 7 页获取到 18 条记录
2025-06-07 03:00:35,146 - INFO - 查询完成，共获取到 618 条记录
2025-06-07 03:00:35,146 - INFO - 获取到 618 条表单数据
2025-06-07 03:00:35,146 - INFO - 当前日期 2025-06 有 618 条MySQL数据需要处理
2025-06-07 03:00:35,146 - INFO - 开始更新记录 - 表单实例ID: FINST-CJ966Q71RIWVLV7P7SGXMCGLMKQM3JJ85ODBM06
2025-06-07 03:00:35,568 - INFO - 更新表单数据成功: FINST-CJ966Q71RIWVLV7P7SGXMCGLMKQM3JJ85ODBM06
2025-06-07 03:00:35,568 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15679.0, 'new_value': 18812.0}, {'field': 'offline_amount', 'old_value': 19616.0, 'new_value': 24593.0}, {'field': 'total_amount', 'old_value': 35295.0, 'new_value': 43405.0}, {'field': 'order_count', 'old_value': 752, 'new_value': 928}]
2025-06-07 03:00:35,583 - INFO - 日期 2025-06 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-06-07 03:00:35,583 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-06-07 03:00:35,583 - INFO - =================同步完成====================
2025-06-07 06:00:02,754 - INFO - =================使用默认全量同步=============
2025-06-07 06:00:04,350 - INFO - MySQL查询成功，共获取 3924 条记录
2025-06-07 06:00:04,350 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-07 06:00:04,397 - INFO - 开始处理日期: 2025-01
2025-06-07 06:00:04,397 - INFO - Request Parameters - Page 1:
2025-06-07 06:00:04,397 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:04,397 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:05,884 - INFO - Response - Page 1:
2025-06-07 06:00:06,087 - INFO - 第 1 页获取到 100 条记录
2025-06-07 06:00:06,087 - INFO - Request Parameters - Page 2:
2025-06-07 06:00:06,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:06,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:06,557 - INFO - Response - Page 2:
2025-06-07 06:00:06,760 - INFO - 第 2 页获取到 100 条记录
2025-06-07 06:00:06,760 - INFO - Request Parameters - Page 3:
2025-06-07 06:00:06,760 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:06,760 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:07,308 - INFO - Response - Page 3:
2025-06-07 06:00:07,511 - INFO - 第 3 页获取到 100 条记录
2025-06-07 06:00:07,511 - INFO - Request Parameters - Page 4:
2025-06-07 06:00:07,511 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:07,511 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:08,059 - INFO - Response - Page 4:
2025-06-07 06:00:08,262 - INFO - 第 4 页获取到 100 条记录
2025-06-07 06:00:08,262 - INFO - Request Parameters - Page 5:
2025-06-07 06:00:08,262 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:08,262 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:08,810 - INFO - Response - Page 5:
2025-06-07 06:00:09,013 - INFO - 第 5 页获取到 100 条记录
2025-06-07 06:00:09,013 - INFO - Request Parameters - Page 6:
2025-06-07 06:00:09,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:09,013 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:09,530 - INFO - Response - Page 6:
2025-06-07 06:00:09,733 - INFO - 第 6 页获取到 100 条记录
2025-06-07 06:00:09,733 - INFO - Request Parameters - Page 7:
2025-06-07 06:00:09,733 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:09,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:10,203 - INFO - Response - Page 7:
2025-06-07 06:00:10,406 - INFO - 第 7 页获取到 82 条记录
2025-06-07 06:00:10,406 - INFO - 查询完成，共获取到 682 条记录
2025-06-07 06:00:10,406 - INFO - 获取到 682 条表单数据
2025-06-07 06:00:10,406 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-07 06:00:10,422 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 06:00:10,422 - INFO - 开始处理日期: 2025-02
2025-06-07 06:00:10,422 - INFO - Request Parameters - Page 1:
2025-06-07 06:00:10,422 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:10,422 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:10,922 - INFO - Response - Page 1:
2025-06-07 06:00:11,126 - INFO - 第 1 页获取到 100 条记录
2025-06-07 06:00:11,126 - INFO - Request Parameters - Page 2:
2025-06-07 06:00:11,126 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:11,126 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:11,580 - INFO - Response - Page 2:
2025-06-07 06:00:11,783 - INFO - 第 2 页获取到 100 条记录
2025-06-07 06:00:11,783 - INFO - Request Parameters - Page 3:
2025-06-07 06:00:11,783 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:11,783 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:12,253 - INFO - Response - Page 3:
2025-06-07 06:00:12,456 - INFO - 第 3 页获取到 100 条记录
2025-06-07 06:00:12,456 - INFO - Request Parameters - Page 4:
2025-06-07 06:00:12,456 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:12,456 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:12,988 - INFO - Response - Page 4:
2025-06-07 06:00:13,191 - INFO - 第 4 页获取到 100 条记录
2025-06-07 06:00:13,191 - INFO - Request Parameters - Page 5:
2025-06-07 06:00:13,191 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:13,191 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:13,739 - INFO - Response - Page 5:
2025-06-07 06:00:13,943 - INFO - 第 5 页获取到 100 条记录
2025-06-07 06:00:13,943 - INFO - Request Parameters - Page 6:
2025-06-07 06:00:13,943 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:13,943 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:14,412 - INFO - Response - Page 6:
2025-06-07 06:00:14,615 - INFO - 第 6 页获取到 100 条记录
2025-06-07 06:00:14,615 - INFO - Request Parameters - Page 7:
2025-06-07 06:00:14,615 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:14,615 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:15,054 - INFO - Response - Page 7:
2025-06-07 06:00:15,257 - INFO - 第 7 页获取到 70 条记录
2025-06-07 06:00:15,257 - INFO - 查询完成，共获取到 670 条记录
2025-06-07 06:00:15,257 - INFO - 获取到 670 条表单数据
2025-06-07 06:00:15,257 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-07 06:00:15,273 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 06:00:15,273 - INFO - 开始处理日期: 2025-03
2025-06-07 06:00:15,273 - INFO - Request Parameters - Page 1:
2025-06-07 06:00:15,273 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:15,273 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:15,805 - INFO - Response - Page 1:
2025-06-07 06:00:16,008 - INFO - 第 1 页获取到 100 条记录
2025-06-07 06:00:16,008 - INFO - Request Parameters - Page 2:
2025-06-07 06:00:16,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:16,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:16,571 - INFO - Response - Page 2:
2025-06-07 06:00:16,775 - INFO - 第 2 页获取到 100 条记录
2025-06-07 06:00:16,775 - INFO - Request Parameters - Page 3:
2025-06-07 06:00:16,775 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:16,775 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:17,307 - INFO - Response - Page 3:
2025-06-07 06:00:17,510 - INFO - 第 3 页获取到 100 条记录
2025-06-07 06:00:17,510 - INFO - Request Parameters - Page 4:
2025-06-07 06:00:17,510 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:17,510 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:17,995 - INFO - Response - Page 4:
2025-06-07 06:00:18,199 - INFO - 第 4 页获取到 100 条记录
2025-06-07 06:00:18,199 - INFO - Request Parameters - Page 5:
2025-06-07 06:00:18,199 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:18,199 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:18,637 - INFO - Response - Page 5:
2025-06-07 06:00:18,840 - INFO - 第 5 页获取到 100 条记录
2025-06-07 06:00:18,840 - INFO - Request Parameters - Page 6:
2025-06-07 06:00:18,840 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:18,840 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:19,278 - INFO - Response - Page 6:
2025-06-07 06:00:19,482 - INFO - 第 6 页获取到 100 条记录
2025-06-07 06:00:19,482 - INFO - Request Parameters - Page 7:
2025-06-07 06:00:19,482 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:19,482 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:19,967 - INFO - Response - Page 7:
2025-06-07 06:00:20,170 - INFO - 第 7 页获取到 61 条记录
2025-06-07 06:00:20,170 - INFO - 查询完成，共获取到 661 条记录
2025-06-07 06:00:20,170 - INFO - 获取到 661 条表单数据
2025-06-07 06:00:20,170 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-07 06:00:20,186 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 06:00:20,186 - INFO - 开始处理日期: 2025-04
2025-06-07 06:00:20,186 - INFO - Request Parameters - Page 1:
2025-06-07 06:00:20,186 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:20,186 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:20,828 - INFO - Response - Page 1:
2025-06-07 06:00:21,031 - INFO - 第 1 页获取到 100 条记录
2025-06-07 06:00:21,031 - INFO - Request Parameters - Page 2:
2025-06-07 06:00:21,031 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:21,031 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:21,485 - INFO - Response - Page 2:
2025-06-07 06:00:21,688 - INFO - 第 2 页获取到 100 条记录
2025-06-07 06:00:21,688 - INFO - Request Parameters - Page 3:
2025-06-07 06:00:21,688 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:21,688 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:22,205 - INFO - Response - Page 3:
2025-06-07 06:00:22,408 - INFO - 第 3 页获取到 100 条记录
2025-06-07 06:00:22,408 - INFO - Request Parameters - Page 4:
2025-06-07 06:00:22,408 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:22,408 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:22,909 - INFO - Response - Page 4:
2025-06-07 06:00:23,112 - INFO - 第 4 页获取到 100 条记录
2025-06-07 06:00:23,112 - INFO - Request Parameters - Page 5:
2025-06-07 06:00:23,112 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:23,112 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:23,613 - INFO - Response - Page 5:
2025-06-07 06:00:23,816 - INFO - 第 5 页获取到 100 条记录
2025-06-07 06:00:23,816 - INFO - Request Parameters - Page 6:
2025-06-07 06:00:23,816 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:23,816 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:24,317 - INFO - Response - Page 6:
2025-06-07 06:00:24,520 - INFO - 第 6 页获取到 100 条记录
2025-06-07 06:00:24,520 - INFO - Request Parameters - Page 7:
2025-06-07 06:00:24,520 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:24,520 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:24,943 - INFO - Response - Page 7:
2025-06-07 06:00:25,146 - INFO - 第 7 页获取到 56 条记录
2025-06-07 06:00:25,146 - INFO - 查询完成，共获取到 656 条记录
2025-06-07 06:00:25,146 - INFO - 获取到 656 条表单数据
2025-06-07 06:00:25,146 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-07 06:00:25,162 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 06:00:25,162 - INFO - 开始处理日期: 2025-05
2025-06-07 06:00:25,162 - INFO - Request Parameters - Page 1:
2025-06-07 06:00:25,162 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:25,162 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:25,600 - INFO - Response - Page 1:
2025-06-07 06:00:25,803 - INFO - 第 1 页获取到 100 条记录
2025-06-07 06:00:25,803 - INFO - Request Parameters - Page 2:
2025-06-07 06:00:25,803 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:25,803 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:26,335 - INFO - Response - Page 2:
2025-06-07 06:00:26,539 - INFO - 第 2 页获取到 100 条记录
2025-06-07 06:00:26,539 - INFO - Request Parameters - Page 3:
2025-06-07 06:00:26,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:26,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:26,961 - INFO - Response - Page 3:
2025-06-07 06:00:27,165 - INFO - 第 3 页获取到 100 条记录
2025-06-07 06:00:27,165 - INFO - Request Parameters - Page 4:
2025-06-07 06:00:27,165 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:27,165 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:27,618 - INFO - Response - Page 4:
2025-06-07 06:00:27,822 - INFO - 第 4 页获取到 100 条记录
2025-06-07 06:00:27,822 - INFO - Request Parameters - Page 5:
2025-06-07 06:00:27,822 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:27,822 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:28,401 - INFO - Response - Page 5:
2025-06-07 06:00:28,604 - INFO - 第 5 页获取到 100 条记录
2025-06-07 06:00:28,604 - INFO - Request Parameters - Page 6:
2025-06-07 06:00:28,604 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:28,604 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:29,152 - INFO - Response - Page 6:
2025-06-07 06:00:29,355 - INFO - 第 6 页获取到 100 条记录
2025-06-07 06:00:29,355 - INFO - Request Parameters - Page 7:
2025-06-07 06:00:29,355 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:29,355 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:29,715 - INFO - Response - Page 7:
2025-06-07 06:00:29,918 - INFO - 第 7 页获取到 37 条记录
2025-06-07 06:00:29,918 - INFO - 查询完成，共获取到 637 条记录
2025-06-07 06:00:29,918 - INFO - 获取到 637 条表单数据
2025-06-07 06:00:29,918 - INFO - 当前日期 2025-05 有 637 条MySQL数据需要处理
2025-06-07 06:00:29,934 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 06:00:29,934 - INFO - 开始处理日期: 2025-06
2025-06-07 06:00:29,934 - INFO - Request Parameters - Page 1:
2025-06-07 06:00:29,934 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:29,934 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:30,482 - INFO - Response - Page 1:
2025-06-07 06:00:30,685 - INFO - 第 1 页获取到 100 条记录
2025-06-07 06:00:30,685 - INFO - Request Parameters - Page 2:
2025-06-07 06:00:30,685 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:30,685 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:31,295 - INFO - Response - Page 2:
2025-06-07 06:00:31,499 - INFO - 第 2 页获取到 100 条记录
2025-06-07 06:00:31,499 - INFO - Request Parameters - Page 3:
2025-06-07 06:00:31,499 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:31,499 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:32,031 - INFO - Response - Page 3:
2025-06-07 06:00:32,234 - INFO - 第 3 页获取到 100 条记录
2025-06-07 06:00:32,234 - INFO - Request Parameters - Page 4:
2025-06-07 06:00:32,234 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:32,234 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:32,719 - INFO - Response - Page 4:
2025-06-07 06:00:32,923 - INFO - 第 4 页获取到 100 条记录
2025-06-07 06:00:32,923 - INFO - Request Parameters - Page 5:
2025-06-07 06:00:32,923 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:32,923 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:33,376 - INFO - Response - Page 5:
2025-06-07 06:00:33,580 - INFO - 第 5 页获取到 100 条记录
2025-06-07 06:00:33,580 - INFO - Request Parameters - Page 6:
2025-06-07 06:00:33,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:33,580 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:34,159 - INFO - Response - Page 6:
2025-06-07 06:00:34,362 - INFO - 第 6 页获取到 100 条记录
2025-06-07 06:00:34,362 - INFO - Request Parameters - Page 7:
2025-06-07 06:00:34,362 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 06:00:34,362 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 06:00:34,738 - INFO - Response - Page 7:
2025-06-07 06:00:34,941 - INFO - 第 7 页获取到 18 条记录
2025-06-07 06:00:34,941 - INFO - 查询完成，共获取到 618 条记录
2025-06-07 06:00:34,941 - INFO - 获取到 618 条表单数据
2025-06-07 06:00:34,941 - INFO - 当前日期 2025-06 有 618 条MySQL数据需要处理
2025-06-07 06:00:34,957 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 06:00:34,957 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 06:00:34,957 - INFO - =================同步完成====================
2025-06-07 09:00:02,596 - INFO - =================使用默认全量同步=============
2025-06-07 09:00:04,206 - INFO - MySQL查询成功，共获取 3925 条记录
2025-06-07 09:00:04,206 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-07 09:00:04,253 - INFO - 开始处理日期: 2025-01
2025-06-07 09:00:04,253 - INFO - Request Parameters - Page 1:
2025-06-07 09:00:04,253 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:04,253 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:05,471 - INFO - Response - Page 1:
2025-06-07 09:00:05,675 - INFO - 第 1 页获取到 100 条记录
2025-06-07 09:00:05,675 - INFO - Request Parameters - Page 2:
2025-06-07 09:00:05,675 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:05,675 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:06,565 - INFO - Response - Page 2:
2025-06-07 09:00:06,768 - INFO - 第 2 页获取到 100 条记录
2025-06-07 09:00:06,768 - INFO - Request Parameters - Page 3:
2025-06-07 09:00:06,768 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:06,768 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:07,253 - INFO - Response - Page 3:
2025-06-07 09:00:07,456 - INFO - 第 3 页获取到 100 条记录
2025-06-07 09:00:07,456 - INFO - Request Parameters - Page 4:
2025-06-07 09:00:07,456 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:07,456 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:07,956 - INFO - Response - Page 4:
2025-06-07 09:00:08,159 - INFO - 第 4 页获取到 100 条记录
2025-06-07 09:00:08,159 - INFO - Request Parameters - Page 5:
2025-06-07 09:00:08,159 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:08,159 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:08,675 - INFO - Response - Page 5:
2025-06-07 09:00:08,878 - INFO - 第 5 页获取到 100 条记录
2025-06-07 09:00:08,878 - INFO - Request Parameters - Page 6:
2025-06-07 09:00:08,878 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:08,878 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:09,346 - INFO - Response - Page 6:
2025-06-07 09:00:09,550 - INFO - 第 6 页获取到 100 条记录
2025-06-07 09:00:09,550 - INFO - Request Parameters - Page 7:
2025-06-07 09:00:09,550 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:09,550 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:10,050 - INFO - Response - Page 7:
2025-06-07 09:00:10,253 - INFO - 第 7 页获取到 82 条记录
2025-06-07 09:00:10,253 - INFO - 查询完成，共获取到 682 条记录
2025-06-07 09:00:10,253 - INFO - 获取到 682 条表单数据
2025-06-07 09:00:10,253 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-07 09:00:10,268 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 09:00:10,268 - INFO - 开始处理日期: 2025-02
2025-06-07 09:00:10,268 - INFO - Request Parameters - Page 1:
2025-06-07 09:00:10,268 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:10,268 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:10,800 - INFO - Response - Page 1:
2025-06-07 09:00:11,003 - INFO - 第 1 页获取到 100 条记录
2025-06-07 09:00:11,003 - INFO - Request Parameters - Page 2:
2025-06-07 09:00:11,003 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:11,003 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:11,518 - INFO - Response - Page 2:
2025-06-07 09:00:11,721 - INFO - 第 2 页获取到 100 条记录
2025-06-07 09:00:11,721 - INFO - Request Parameters - Page 3:
2025-06-07 09:00:11,721 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:11,721 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:12,268 - INFO - Response - Page 3:
2025-06-07 09:00:12,471 - INFO - 第 3 页获取到 100 条记录
2025-06-07 09:00:12,471 - INFO - Request Parameters - Page 4:
2025-06-07 09:00:12,471 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:12,471 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:12,971 - INFO - Response - Page 4:
2025-06-07 09:00:13,175 - INFO - 第 4 页获取到 100 条记录
2025-06-07 09:00:13,175 - INFO - Request Parameters - Page 5:
2025-06-07 09:00:13,175 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:13,175 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:13,612 - INFO - Response - Page 5:
2025-06-07 09:00:13,815 - INFO - 第 5 页获取到 100 条记录
2025-06-07 09:00:13,815 - INFO - Request Parameters - Page 6:
2025-06-07 09:00:13,815 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:13,815 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:14,362 - INFO - Response - Page 6:
2025-06-07 09:00:14,565 - INFO - 第 6 页获取到 100 条记录
2025-06-07 09:00:14,565 - INFO - Request Parameters - Page 7:
2025-06-07 09:00:14,565 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:14,565 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:15,018 - INFO - Response - Page 7:
2025-06-07 09:00:15,221 - INFO - 第 7 页获取到 70 条记录
2025-06-07 09:00:15,221 - INFO - 查询完成，共获取到 670 条记录
2025-06-07 09:00:15,221 - INFO - 获取到 670 条表单数据
2025-06-07 09:00:15,221 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-07 09:00:15,237 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 09:00:15,237 - INFO - 开始处理日期: 2025-03
2025-06-07 09:00:15,237 - INFO - Request Parameters - Page 1:
2025-06-07 09:00:15,237 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:15,237 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:15,768 - INFO - Response - Page 1:
2025-06-07 09:00:15,971 - INFO - 第 1 页获取到 100 条记录
2025-06-07 09:00:15,971 - INFO - Request Parameters - Page 2:
2025-06-07 09:00:15,971 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:15,971 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:16,440 - INFO - Response - Page 2:
2025-06-07 09:00:16,643 - INFO - 第 2 页获取到 100 条记录
2025-06-07 09:00:16,643 - INFO - Request Parameters - Page 3:
2025-06-07 09:00:16,643 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:16,643 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:17,128 - INFO - Response - Page 3:
2025-06-07 09:00:17,331 - INFO - 第 3 页获取到 100 条记录
2025-06-07 09:00:17,331 - INFO - Request Parameters - Page 4:
2025-06-07 09:00:17,331 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:17,331 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:17,909 - INFO - Response - Page 4:
2025-06-07 09:00:18,112 - INFO - 第 4 页获取到 100 条记录
2025-06-07 09:00:18,112 - INFO - Request Parameters - Page 5:
2025-06-07 09:00:18,112 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:18,112 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:18,581 - INFO - Response - Page 5:
2025-06-07 09:00:18,784 - INFO - 第 5 页获取到 100 条记录
2025-06-07 09:00:18,784 - INFO - Request Parameters - Page 6:
2025-06-07 09:00:18,784 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:18,784 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:19,346 - INFO - Response - Page 6:
2025-06-07 09:00:19,549 - INFO - 第 6 页获取到 100 条记录
2025-06-07 09:00:19,549 - INFO - Request Parameters - Page 7:
2025-06-07 09:00:19,549 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:19,549 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:20,081 - INFO - Response - Page 7:
2025-06-07 09:00:20,284 - INFO - 第 7 页获取到 61 条记录
2025-06-07 09:00:20,284 - INFO - 查询完成，共获取到 661 条记录
2025-06-07 09:00:20,284 - INFO - 获取到 661 条表单数据
2025-06-07 09:00:20,284 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-07 09:00:20,299 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 09:00:20,299 - INFO - 开始处理日期: 2025-04
2025-06-07 09:00:20,299 - INFO - Request Parameters - Page 1:
2025-06-07 09:00:20,299 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:20,299 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:20,940 - INFO - Response - Page 1:
2025-06-07 09:00:21,143 - INFO - 第 1 页获取到 100 条记录
2025-06-07 09:00:21,143 - INFO - Request Parameters - Page 2:
2025-06-07 09:00:21,143 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:21,143 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:21,643 - INFO - Response - Page 2:
2025-06-07 09:00:21,846 - INFO - 第 2 页获取到 100 条记录
2025-06-07 09:00:21,846 - INFO - Request Parameters - Page 3:
2025-06-07 09:00:21,846 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:21,846 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:22,440 - INFO - Response - Page 3:
2025-06-07 09:00:22,643 - INFO - 第 3 页获取到 100 条记录
2025-06-07 09:00:22,643 - INFO - Request Parameters - Page 4:
2025-06-07 09:00:22,643 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:22,643 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:23,143 - INFO - Response - Page 4:
2025-06-07 09:00:23,346 - INFO - 第 4 页获取到 100 条记录
2025-06-07 09:00:23,346 - INFO - Request Parameters - Page 5:
2025-06-07 09:00:23,346 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:23,346 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:23,815 - INFO - Response - Page 5:
2025-06-07 09:00:24,018 - INFO - 第 5 页获取到 100 条记录
2025-06-07 09:00:24,018 - INFO - Request Parameters - Page 6:
2025-06-07 09:00:24,018 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:24,018 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:24,565 - INFO - Response - Page 6:
2025-06-07 09:00:24,768 - INFO - 第 6 页获取到 100 条记录
2025-06-07 09:00:24,768 - INFO - Request Parameters - Page 7:
2025-06-07 09:00:24,768 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:24,768 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:25,237 - INFO - Response - Page 7:
2025-06-07 09:00:25,440 - INFO - 第 7 页获取到 56 条记录
2025-06-07 09:00:25,440 - INFO - 查询完成，共获取到 656 条记录
2025-06-07 09:00:25,440 - INFO - 获取到 656 条表单数据
2025-06-07 09:00:25,440 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-07 09:00:25,456 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 09:00:25,456 - INFO - 开始处理日期: 2025-05
2025-06-07 09:00:25,456 - INFO - Request Parameters - Page 1:
2025-06-07 09:00:25,456 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:25,456 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:26,018 - INFO - Response - Page 1:
2025-06-07 09:00:26,221 - INFO - 第 1 页获取到 100 条记录
2025-06-07 09:00:26,221 - INFO - Request Parameters - Page 2:
2025-06-07 09:00:26,221 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:26,221 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:26,706 - INFO - Response - Page 2:
2025-06-07 09:00:26,909 - INFO - 第 2 页获取到 100 条记录
2025-06-07 09:00:26,909 - INFO - Request Parameters - Page 3:
2025-06-07 09:00:26,909 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:26,909 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:27,409 - INFO - Response - Page 3:
2025-06-07 09:00:27,612 - INFO - 第 3 页获取到 100 条记录
2025-06-07 09:00:27,612 - INFO - Request Parameters - Page 4:
2025-06-07 09:00:27,612 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:27,612 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:28,206 - INFO - Response - Page 4:
2025-06-07 09:00:28,409 - INFO - 第 4 页获取到 100 条记录
2025-06-07 09:00:28,409 - INFO - Request Parameters - Page 5:
2025-06-07 09:00:28,409 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:28,409 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:29,034 - INFO - Response - Page 5:
2025-06-07 09:00:29,237 - INFO - 第 5 页获取到 100 条记录
2025-06-07 09:00:29,237 - INFO - Request Parameters - Page 6:
2025-06-07 09:00:29,237 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:29,237 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:29,753 - INFO - Response - Page 6:
2025-06-07 09:00:29,956 - INFO - 第 6 页获取到 100 条记录
2025-06-07 09:00:29,956 - INFO - Request Parameters - Page 7:
2025-06-07 09:00:29,956 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:29,956 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:30,378 - INFO - Response - Page 7:
2025-06-07 09:00:30,581 - INFO - 第 7 页获取到 37 条记录
2025-06-07 09:00:30,581 - INFO - 查询完成，共获取到 637 条记录
2025-06-07 09:00:30,581 - INFO - 获取到 637 条表单数据
2025-06-07 09:00:30,581 - INFO - 当前日期 2025-05 有 637 条MySQL数据需要处理
2025-06-07 09:00:30,596 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 09:00:30,596 - INFO - 开始处理日期: 2025-06
2025-06-07 09:00:30,596 - INFO - Request Parameters - Page 1:
2025-06-07 09:00:30,596 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:30,596 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:31,159 - INFO - Response - Page 1:
2025-06-07 09:00:31,362 - INFO - 第 1 页获取到 100 条记录
2025-06-07 09:00:31,362 - INFO - Request Parameters - Page 2:
2025-06-07 09:00:31,362 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:31,362 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:31,831 - INFO - Response - Page 2:
2025-06-07 09:00:32,049 - INFO - 第 2 页获取到 100 条记录
2025-06-07 09:00:32,049 - INFO - Request Parameters - Page 3:
2025-06-07 09:00:32,049 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:32,049 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:32,612 - INFO - Response - Page 3:
2025-06-07 09:00:32,815 - INFO - 第 3 页获取到 100 条记录
2025-06-07 09:00:32,815 - INFO - Request Parameters - Page 4:
2025-06-07 09:00:32,815 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:32,815 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:33,315 - INFO - Response - Page 4:
2025-06-07 09:00:33,518 - INFO - 第 4 页获取到 100 条记录
2025-06-07 09:00:33,518 - INFO - Request Parameters - Page 5:
2025-06-07 09:00:33,518 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:33,518 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:33,971 - INFO - Response - Page 5:
2025-06-07 09:00:34,174 - INFO - 第 5 页获取到 100 条记录
2025-06-07 09:00:34,174 - INFO - Request Parameters - Page 6:
2025-06-07 09:00:34,174 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:34,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:34,737 - INFO - Response - Page 6:
2025-06-07 09:00:34,940 - INFO - 第 6 页获取到 100 条记录
2025-06-07 09:00:34,940 - INFO - Request Parameters - Page 7:
2025-06-07 09:00:34,940 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 09:00:34,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 09:00:35,268 - INFO - Response - Page 7:
2025-06-07 09:00:35,471 - INFO - 第 7 页获取到 18 条记录
2025-06-07 09:00:35,471 - INFO - 查询完成，共获取到 618 条记录
2025-06-07 09:00:35,471 - INFO - 获取到 618 条表单数据
2025-06-07 09:00:35,471 - INFO - 当前日期 2025-06 有 619 条MySQL数据需要处理
2025-06-07 09:00:35,471 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66KD1OESVDO0260ETU6OP80122L7BABDBM7I
2025-06-07 09:00:35,940 - INFO - 更新表单数据成功: FINST-2PF66KD1OESVDO0260ETU6OP80122L7BABDBM7I
2025-06-07 09:00:35,940 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7800.0, 'new_value': 8400.0}, {'field': 'total_amount', 'old_value': 7800.0, 'new_value': 8400.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-06-07 09:00:35,940 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMJ5
2025-06-07 09:00:36,346 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMJ5
2025-06-07 09:00:36,346 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4000.0, 'new_value': 16200.0}, {'field': 'total_amount', 'old_value': 4000.0, 'new_value': 16200.0}]
2025-06-07 09:00:36,346 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMK5
2025-06-07 09:00:36,721 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMK5
2025-06-07 09:00:36,721 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 16500.0}, {'field': 'total_amount', 'old_value': 95590.0, 'new_value': 112090.0}, {'field': 'order_count', 'old_value': 58, 'new_value': 72}]
2025-06-07 09:00:36,721 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBML5
2025-06-07 09:00:37,221 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBML5
2025-06-07 09:00:37,221 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8170.0, 'new_value': 10870.0}, {'field': 'total_amount', 'old_value': 8170.0, 'new_value': 10870.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-06-07 09:00:37,221 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMRB1
2025-06-07 09:00:37,659 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMRB1
2025-06-07 09:00:37,659 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4048.0, 'new_value': 4166.82}, {'field': 'total_amount', 'old_value': 4048.0, 'new_value': 4166.82}, {'field': 'order_count', 'old_value': 21, 'new_value': 23}]
2025-06-07 09:00:37,659 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMN5
2025-06-07 09:00:38,127 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMN5
2025-06-07 09:00:38,127 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 360.0, 'new_value': 565.0}, {'field': 'offline_amount', 'old_value': 9547.0, 'new_value': 11418.0}, {'field': 'total_amount', 'old_value': 9907.0, 'new_value': 11983.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 23}]
2025-06-07 09:00:38,127 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMTB1
2025-06-07 09:00:38,674 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMTB1
2025-06-07 09:00:38,690 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 2900.0}, {'field': 'total_amount', 'old_value': 12900.0, 'new_value': 15800.0}, {'field': 'order_count', 'old_value': 126, 'new_value': 151}]
2025-06-07 09:00:38,690 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMQ5
2025-06-07 09:00:39,127 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMQ5
2025-06-07 09:00:39,127 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73474.9, 'new_value': 78817.1}, {'field': 'total_amount', 'old_value': 73474.9, 'new_value': 78817.1}, {'field': 'order_count', 'old_value': 1123, 'new_value': 1251}]
2025-06-07 09:00:39,127 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMR5
2025-06-07 09:00:39,690 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMR5
2025-06-07 09:00:39,690 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1266.0, 'new_value': 1351.0}, {'field': 'offline_amount', 'old_value': 10637.9, 'new_value': 22528.01}, {'field': 'total_amount', 'old_value': 11903.9, 'new_value': 23879.01}, {'field': 'order_count', 'old_value': 109, 'new_value': 131}]
2025-06-07 09:00:39,690 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMS5
2025-06-07 09:00:40,112 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMS5
2025-06-07 09:00:40,112 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9235.63, 'new_value': 10893.6}, {'field': 'offline_amount', 'old_value': 21724.62, 'new_value': 26434.62}, {'field': 'total_amount', 'old_value': 30960.25, 'new_value': 37328.22}, {'field': 'order_count', 'old_value': 363, 'new_value': 437}]
2025-06-07 09:00:40,112 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMT5
2025-06-07 09:00:40,502 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMT5
2025-06-07 09:00:40,502 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5767.0, 'new_value': 6398.0}, {'field': 'offline_amount', 'old_value': 7139.8, 'new_value': 12347.74}, {'field': 'total_amount', 'old_value': 12906.8, 'new_value': 18745.74}, {'field': 'order_count', 'old_value': 28, 'new_value': 34}]
2025-06-07 09:00:40,502 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMU5
2025-06-07 09:00:40,987 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMU5
2025-06-07 09:00:40,987 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11006.0, 'new_value': 13595.0}, {'field': 'total_amount', 'old_value': 11006.0, 'new_value': 13595.0}, {'field': 'order_count', 'old_value': 211, 'new_value': 259}]
2025-06-07 09:00:40,987 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMV5
2025-06-07 09:00:41,581 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMV5
2025-06-07 09:00:41,581 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35272.0, 'new_value': 41322.0}, {'field': 'offline_amount', 'old_value': 10680.5, 'new_value': 12363.16}, {'field': 'total_amount', 'old_value': 45952.5, 'new_value': 53685.16}, {'field': 'order_count', 'old_value': 283, 'new_value': 334}]
2025-06-07 09:00:41,581 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMX5
2025-06-07 09:00:42,034 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMX5
2025-06-07 09:00:42,034 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2200.0, 'new_value': 2300.0}, {'field': 'offline_amount', 'old_value': 998.0, 'new_value': 1625.0}, {'field': 'total_amount', 'old_value': 3198.0, 'new_value': 3925.0}, {'field': 'order_count', 'old_value': 43, 'new_value': 56}]
2025-06-07 09:00:42,034 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMY5
2025-06-07 09:00:42,471 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMY5
2025-06-07 09:00:42,471 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12211.0, 'new_value': 13086.0}, {'field': 'offline_amount', 'old_value': 156634.0, 'new_value': 163659.0}, {'field': 'total_amount', 'old_value': 168845.0, 'new_value': 176745.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 33}]
2025-06-07 09:00:42,471 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMZ5
2025-06-07 09:00:42,893 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMZ5
2025-06-07 09:00:42,893 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4787.5, 'new_value': 5699.02}, {'field': 'offline_amount', 'old_value': 6136.76, 'new_value': 7054.05}, {'field': 'total_amount', 'old_value': 10924.26, 'new_value': 12753.07}, {'field': 'order_count', 'old_value': 528, 'new_value': 630}]
2025-06-07 09:00:42,893 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM16
2025-06-07 09:00:43,424 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM16
2025-06-07 09:00:43,424 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23678.0, 'new_value': 27158.0}, {'field': 'total_amount', 'old_value': 23678.0, 'new_value': 27158.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 12}]
2025-06-07 09:00:43,424 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM26
2025-06-07 09:00:43,893 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM26
2025-06-07 09:00:43,893 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2706.0, 'new_value': 5994.0}, {'field': 'total_amount', 'old_value': 4525.0, 'new_value': 7813.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 26}]
2025-06-07 09:00:43,893 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM36
2025-06-07 09:00:44,346 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM36
2025-06-07 09:00:44,346 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39061.0, 'new_value': 42832.0}, {'field': 'total_amount', 'old_value': 39061.0, 'new_value': 42832.0}, {'field': 'order_count', 'old_value': 58, 'new_value': 68}]
2025-06-07 09:00:44,346 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM46
2025-06-07 09:00:44,815 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM46
2025-06-07 09:00:44,815 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2367.46, 'new_value': 2503.71}, {'field': 'offline_amount', 'old_value': 28629.99, 'new_value': 35712.09}, {'field': 'total_amount', 'old_value': 30997.45, 'new_value': 38215.8}, {'field': 'order_count', 'old_value': 366, 'new_value': 436}]
2025-06-07 09:00:44,815 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM56
2025-06-07 09:00:45,190 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM56
2025-06-07 09:00:45,190 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3381.5, 'new_value': 4116.5}, {'field': 'offline_amount', 'old_value': 711.0, 'new_value': 2607.0}, {'field': 'total_amount', 'old_value': 4092.5, 'new_value': 6723.5}, {'field': 'order_count', 'old_value': 16, 'new_value': 20}]
2025-06-07 09:00:45,190 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM15
2025-06-07 09:00:45,643 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM15
2025-06-07 09:00:45,643 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 154208.0, 'new_value': 191267.0}, {'field': 'total_amount', 'old_value': 154208.0, 'new_value': 191267.0}, {'field': 'order_count', 'old_value': 32, 'new_value': 38}]
2025-06-07 09:00:45,643 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM66
2025-06-07 09:00:46,049 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM66
2025-06-07 09:00:46,049 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1441.4, 'new_value': 2100.2}, {'field': 'total_amount', 'old_value': 1441.4, 'new_value': 2100.2}, {'field': 'order_count', 'old_value': 3, 'new_value': 5}]
2025-06-07 09:00:46,049 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM76
2025-06-07 09:00:46,534 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM76
2025-06-07 09:00:46,534 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27505.86, 'new_value': 32411.46}, {'field': 'total_amount', 'old_value': 27505.86, 'new_value': 32411.46}, {'field': 'order_count', 'old_value': 126, 'new_value': 148}]
2025-06-07 09:00:46,534 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM86
2025-06-07 09:00:47,002 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM86
2025-06-07 09:00:47,002 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4288.0, 'new_value': 5459.0}, {'field': 'total_amount', 'old_value': 4288.0, 'new_value': 5459.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 27}]
2025-06-07 09:00:47,002 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM96
2025-06-07 09:00:47,456 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM96
2025-06-07 09:00:47,456 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34653.0, 'new_value': 42836.0}, {'field': 'total_amount', 'old_value': 34653.0, 'new_value': 42836.0}, {'field': 'order_count', 'old_value': 1317, 'new_value': 1644}]
2025-06-07 09:00:47,456 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMA6
2025-06-07 09:00:48,049 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMA6
2025-06-07 09:00:48,049 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35782.8, 'new_value': 40923.7}, {'field': 'total_amount', 'old_value': 35782.8, 'new_value': 40923.7}, {'field': 'order_count', 'old_value': 150, 'new_value': 172}]
2025-06-07 09:00:48,049 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMB6
2025-06-07 09:00:48,471 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMB6
2025-06-07 09:00:48,471 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30149.0, 'new_value': 35234.0}, {'field': 'offline_amount', 'old_value': 13692.49, 'new_value': 16238.66}, {'field': 'total_amount', 'old_value': 43841.49, 'new_value': 51472.66}, {'field': 'order_count', 'old_value': 302, 'new_value': 359}]
2025-06-07 09:00:48,471 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMC6
2025-06-07 09:00:48,987 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMC6
2025-06-07 09:00:48,987 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28236.57, 'new_value': 34862.14}, {'field': 'total_amount', 'old_value': 28236.57, 'new_value': 34862.14}, {'field': 'order_count', 'old_value': 978, 'new_value': 1208}]
2025-06-07 09:00:48,987 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM5C1
2025-06-07 09:00:49,393 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM5C1
2025-06-07 09:00:49,393 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8523.0, 'new_value': 8897.0}, {'field': 'total_amount', 'old_value': 8523.0, 'new_value': 8897.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-06-07 09:00:49,393 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBME6
2025-06-07 09:00:49,862 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBME6
2025-06-07 09:00:49,862 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6518.0, 'new_value': 9878.0}, {'field': 'offline_amount', 'old_value': 57328.0, 'new_value': 61893.0}, {'field': 'total_amount', 'old_value': 63846.0, 'new_value': 71771.0}, {'field': 'order_count', 'old_value': 35, 'new_value': 45}]
2025-06-07 09:00:49,862 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMF6
2025-06-07 09:00:50,315 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMF6
2025-06-07 09:00:50,315 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2088.47, 'new_value': 3191.91}, {'field': 'offline_amount', 'old_value': 80131.72, 'new_value': 94295.25}, {'field': 'total_amount', 'old_value': 82220.19, 'new_value': 97487.16}, {'field': 'order_count', 'old_value': 373, 'new_value': 451}]
2025-06-07 09:00:50,315 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM6C1
2025-06-07 09:00:50,690 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM6C1
2025-06-07 09:00:50,690 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21056.0, 'new_value': 21174.0}, {'field': 'total_amount', 'old_value': 22822.0, 'new_value': 22940.0}, {'field': 'order_count', 'old_value': 150, 'new_value': 151}]
2025-06-07 09:00:50,690 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM55
2025-06-07 09:00:51,049 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM55
2025-06-07 09:00:51,049 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16401.42, 'new_value': 18733.82}, {'field': 'offline_amount', 'old_value': 17007.9, 'new_value': 21266.34}, {'field': 'total_amount', 'old_value': 33409.32, 'new_value': 40000.16}, {'field': 'order_count', 'old_value': 1093, 'new_value': 1322}]
2025-06-07 09:00:51,049 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMG6
2025-06-07 09:00:51,424 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMG6
2025-06-07 09:00:51,424 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31737.0, 'new_value': 37549.0}, {'field': 'total_amount', 'old_value': 31737.0, 'new_value': 37549.0}, {'field': 'order_count', 'old_value': 777, 'new_value': 938}]
2025-06-07 09:00:51,424 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMH6
2025-06-07 09:00:51,862 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMH6
2025-06-07 09:00:51,862 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26941.91, 'new_value': 32042.95}, {'field': 'total_amount', 'old_value': 26941.91, 'new_value': 32042.95}, {'field': 'order_count', 'old_value': 333, 'new_value': 402}]
2025-06-07 09:00:51,862 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMI6
2025-06-07 09:00:52,299 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMI6
2025-06-07 09:00:52,299 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16008.04, 'new_value': 18267.04}, {'field': 'total_amount', 'old_value': 16008.04, 'new_value': 18267.04}, {'field': 'order_count', 'old_value': 69, 'new_value': 84}]
2025-06-07 09:00:52,299 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMBC1
2025-06-07 09:00:52,690 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMBC1
2025-06-07 09:00:52,690 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 509.0, 'new_value': 608.0}, {'field': 'total_amount', 'old_value': 6279.0, 'new_value': 6378.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-06-07 09:00:52,690 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMJ6
2025-06-07 09:00:53,112 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMJ6
2025-06-07 09:00:53,112 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2781.48, 'new_value': 3349.08}, {'field': 'offline_amount', 'old_value': 5089.37, 'new_value': 6453.03}, {'field': 'total_amount', 'old_value': 7870.85, 'new_value': 9802.11}, {'field': 'order_count', 'old_value': 247, 'new_value': 308}]
2025-06-07 09:00:53,112 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMCC1
2025-06-07 09:00:53,706 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMCC1
2025-06-07 09:00:53,706 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33691.86, 'new_value': 41292.65}, {'field': 'offline_amount', 'old_value': 76587.22, 'new_value': 88197.14}, {'field': 'total_amount', 'old_value': 110279.08, 'new_value': 129489.79}, {'field': 'order_count', 'old_value': 794, 'new_value': 935}]
2025-06-07 09:00:53,706 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMDC1
2025-06-07 09:00:54,252 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMDC1
2025-06-07 09:00:54,252 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8441.05, 'new_value': 8836.45}, {'field': 'offline_amount', 'old_value': 14.0, 'new_value': 15.0}, {'field': 'total_amount', 'old_value': 8455.05, 'new_value': 8851.45}, {'field': 'order_count', 'old_value': 35, 'new_value': 39}]
2025-06-07 09:00:54,252 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMK6
2025-06-07 09:00:54,705 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMK6
2025-06-07 09:00:54,705 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1026.0, 'new_value': 2025.0}, {'field': 'total_amount', 'old_value': 1026.0, 'new_value': 2025.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-06-07 09:00:54,705 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM85
2025-06-07 09:00:55,174 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM85
2025-06-07 09:00:55,174 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 218863.0, 'new_value': 227796.0}, {'field': 'total_amount', 'old_value': 219442.0, 'new_value': 228375.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 54}]
2025-06-07 09:00:55,174 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBML6
2025-06-07 09:00:55,612 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBML6
2025-06-07 09:00:55,612 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 89789.5, 'new_value': 102482.7}, {'field': 'offline_amount', 'old_value': 15195.0, 'new_value': 21817.0}, {'field': 'total_amount', 'old_value': 104984.5, 'new_value': 124299.7}, {'field': 'order_count', 'old_value': 131, 'new_value': 157}]
2025-06-07 09:00:55,612 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMM6
2025-06-07 09:00:56,049 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMM6
2025-06-07 09:00:56,049 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4231.0, 'new_value': 4755.0}, {'field': 'total_amount', 'old_value': 4231.0, 'new_value': 4755.0}, {'field': 'order_count', 'old_value': 205, 'new_value': 238}]
2025-06-07 09:00:56,049 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMFC1
2025-06-07 09:00:56,518 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMFC1
2025-06-07 09:00:56,518 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29938.0, 'new_value': 30692.0}, {'field': 'total_amount', 'old_value': 29938.0, 'new_value': 30692.0}, {'field': 'order_count', 'old_value': 125, 'new_value': 128}]
2025-06-07 09:00:56,518 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMJC1
2025-06-07 09:00:56,924 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMJC1
2025-06-07 09:00:56,924 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5019.0, 'new_value': 7946.0}, {'field': 'total_amount', 'old_value': 5019.0, 'new_value': 7946.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 15}]
2025-06-07 09:00:56,924 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMN6
2025-06-07 09:00:57,471 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMN6
2025-06-07 09:00:57,471 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18384.17, 'new_value': 22574.85}, {'field': 'total_amount', 'old_value': 18384.17, 'new_value': 22574.85}, {'field': 'order_count', 'old_value': 527, 'new_value': 651}]
2025-06-07 09:00:57,471 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMO6
2025-06-07 09:00:57,971 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMO6
2025-06-07 09:00:57,971 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7034.0, 'new_value': 22830.0}, {'field': 'total_amount', 'old_value': 7034.0, 'new_value': 22830.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 12}]
2025-06-07 09:00:57,971 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMB5
2025-06-07 09:00:58,424 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMB5
2025-06-07 09:00:58,424 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29627.0, 'new_value': 59435.0}, {'field': 'total_amount', 'old_value': 29627.0, 'new_value': 59435.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 11}]
2025-06-07 09:00:58,424 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMNC1
2025-06-07 09:00:58,830 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMNC1
2025-06-07 09:00:58,830 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21445.0, 'new_value': 25026.0}, {'field': 'total_amount', 'old_value': 21445.0, 'new_value': 25026.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 24}]
2025-06-07 09:00:58,830 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMC5
2025-06-07 09:00:59,299 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMC5
2025-06-07 09:00:59,299 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8780.0, 'new_value': 17890.0}, {'field': 'total_amount', 'old_value': 8780.0, 'new_value': 17890.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 4}]
2025-06-07 09:00:59,299 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMD5
2025-06-07 09:00:59,799 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMD5
2025-06-07 09:00:59,799 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43503.5, 'new_value': 44549.5}, {'field': 'total_amount', 'old_value': 43503.5, 'new_value': 44549.5}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-06-07 09:00:59,799 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMQ6
2025-06-07 09:01:00,299 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMQ6
2025-06-07 09:01:00,299 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75629.83, 'new_value': 92338.22}, {'field': 'total_amount', 'old_value': 75629.83, 'new_value': 92338.22}, {'field': 'order_count', 'old_value': 247, 'new_value': 294}]
2025-06-07 09:01:00,299 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMR6
2025-06-07 09:01:00,862 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMR6
2025-06-07 09:01:00,862 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12570.0, 'new_value': 15570.0}, {'field': 'total_amount', 'old_value': 12570.0, 'new_value': 15570.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 19}]
2025-06-07 09:01:00,862 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMOC1
2025-06-07 09:01:01,362 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMOC1
2025-06-07 09:01:01,362 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3246.05, 'new_value': 3315.95}, {'field': 'total_amount', 'old_value': 29724.05, 'new_value': 29793.95}, {'field': 'order_count', 'old_value': 491, 'new_value': 492}]
2025-06-07 09:01:01,362 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMPC1
2025-06-07 09:01:01,830 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMPC1
2025-06-07 09:01:01,830 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8336.0, 'new_value': 9522.0}, {'field': 'total_amount', 'old_value': 8336.0, 'new_value': 9522.0}, {'field': 'order_count', 'old_value': 40, 'new_value': 44}]
2025-06-07 09:01:01,830 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBME5
2025-06-07 09:01:02,362 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBME5
2025-06-07 09:01:02,362 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6263.0, 'new_value': 7642.0}, {'field': 'total_amount', 'old_value': 6263.0, 'new_value': 7642.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 21}]
2025-06-07 09:01:02,362 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMS6
2025-06-07 09:01:02,799 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMS6
2025-06-07 09:01:02,799 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9324.8, 'new_value': 13019.5}, {'field': 'offline_amount', 'old_value': 8126.4, 'new_value': 8509.7}, {'field': 'total_amount', 'old_value': 17451.2, 'new_value': 21529.2}, {'field': 'order_count', 'old_value': 43, 'new_value': 55}]
2025-06-07 09:01:02,799 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMF5
2025-06-07 09:01:03,284 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMF5
2025-06-07 09:01:03,284 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75816.42, 'new_value': 87285.78}, {'field': 'total_amount', 'old_value': 75816.42, 'new_value': 87285.78}, {'field': 'order_count', 'old_value': 340, 'new_value': 399}]
2025-06-07 09:01:03,284 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMT6
2025-06-07 09:01:03,721 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMT6
2025-06-07 09:01:03,721 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4402.0, 'new_value': 4547.0}, {'field': 'offline_amount', 'old_value': 17141.66, 'new_value': 18075.96}, {'field': 'total_amount', 'old_value': 21543.66, 'new_value': 22622.96}, {'field': 'order_count', 'old_value': 241, 'new_value': 257}]
2025-06-07 09:01:03,721 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMU6
2025-06-07 09:01:04,237 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMU6
2025-06-07 09:01:04,237 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 858.0, 'new_value': 935.0}, {'field': 'offline_amount', 'old_value': 5425.8, 'new_value': 6293.0}, {'field': 'total_amount', 'old_value': 6283.8, 'new_value': 7228.0}, {'field': 'order_count', 'old_value': 227, 'new_value': 265}]
2025-06-07 09:01:04,237 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMH5
2025-06-07 09:01:04,705 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMH5
2025-06-07 09:01:04,705 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40686.0, 'new_value': 47474.0}, {'field': 'total_amount', 'old_value': 44592.2, 'new_value': 51380.2}, {'field': 'order_count', 'old_value': 17, 'new_value': 22}]
2025-06-07 09:01:04,705 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMV6
2025-06-07 09:01:05,221 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMV6
2025-06-07 09:01:05,221 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3949.85, 'new_value': 4581.52}, {'field': 'offline_amount', 'old_value': 9191.1, 'new_value': 11520.6}, {'field': 'total_amount', 'old_value': 13140.95, 'new_value': 16102.12}, {'field': 'order_count', 'old_value': 482, 'new_value': 590}]
2025-06-07 09:01:05,221 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMJ5
2025-06-07 09:01:05,768 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMJ5
2025-06-07 09:01:05,768 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12519.0, 'new_value': 14119.0}, {'field': 'total_amount', 'old_value': 12519.0, 'new_value': 14119.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 42}]
2025-06-07 09:01:05,768 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMW6
2025-06-07 09:01:06,252 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMW6
2025-06-07 09:01:06,252 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3347.0, 'new_value': 4396.0}, {'field': 'total_amount', 'old_value': 3347.0, 'new_value': 4396.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 27}]
2025-06-07 09:01:06,252 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMX6
2025-06-07 09:01:06,627 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMX6
2025-06-07 09:01:06,627 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17322.7, 'new_value': 19854.5}, {'field': 'offline_amount', 'old_value': 33905.6, 'new_value': 37671.96}, {'field': 'total_amount', 'old_value': 51228.3, 'new_value': 57526.46}, {'field': 'order_count', 'old_value': 220, 'new_value': 260}]
2025-06-07 09:01:06,627 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMY6
2025-06-07 09:01:07,127 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMY6
2025-06-07 09:01:07,127 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16892.73, 'new_value': 18915.68}, {'field': 'total_amount', 'old_value': 16892.73, 'new_value': 18915.68}, {'field': 'order_count', 'old_value': 444, 'new_value': 513}]
2025-06-07 09:01:07,127 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBML5
2025-06-07 09:01:07,596 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBML5
2025-06-07 09:01:07,596 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8268.93, 'new_value': 9180.98}, {'field': 'offline_amount', 'old_value': 101880.79, 'new_value': 121188.79}, {'field': 'total_amount', 'old_value': 110149.72, 'new_value': 130369.77}, {'field': 'order_count', 'old_value': 559, 'new_value': 648}]
2025-06-07 09:01:07,596 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMZ6
2025-06-07 09:01:08,112 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMZ6
2025-06-07 09:01:08,112 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5524.36, 'new_value': 9597.53}, {'field': 'offline_amount', 'old_value': 13702.19, 'new_value': 13732.45}, {'field': 'total_amount', 'old_value': 19226.55, 'new_value': 23329.98}, {'field': 'order_count', 'old_value': 101, 'new_value': 131}]
2025-06-07 09:01:08,112 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMM5
2025-06-07 09:01:08,596 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMM5
2025-06-07 09:01:08,596 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21262.0, 'new_value': 39297.0}, {'field': 'total_amount', 'old_value': 21262.0, 'new_value': 39297.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 28}]
2025-06-07 09:01:08,596 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM07
2025-06-07 09:01:09,127 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM07
2025-06-07 09:01:09,127 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28588.0, 'new_value': 32788.0}, {'field': 'total_amount', 'old_value': 28588.0, 'new_value': 32788.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-06-07 09:01:09,127 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM17
2025-06-07 09:01:09,596 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM17
2025-06-07 09:01:09,596 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5171.0, 'new_value': 5719.0}, {'field': 'total_amount', 'old_value': 5171.0, 'new_value': 5719.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 16}]
2025-06-07 09:01:09,596 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM27
2025-06-07 09:01:10,034 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM27
2025-06-07 09:01:10,034 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8000.0, 'new_value': 8480.0}, {'field': 'total_amount', 'old_value': 8000.0, 'new_value': 8480.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-06-07 09:01:10,034 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM37
2025-06-07 09:01:10,565 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM37
2025-06-07 09:01:10,565 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7009.5, 'new_value': 9458.5}, {'field': 'total_amount', 'old_value': 7009.5, 'new_value': 9458.5}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-06-07 09:01:10,565 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMN5
2025-06-07 09:01:11,018 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMN5
2025-06-07 09:01:11,018 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10507.0, 'new_value': 11574.0}, {'field': 'total_amount', 'old_value': 10507.0, 'new_value': 11574.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 14}]
2025-06-07 09:01:11,018 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM47
2025-06-07 09:01:11,440 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM47
2025-06-07 09:01:11,440 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35247.84, 'new_value': 42466.42}, {'field': 'offline_amount', 'old_value': 30213.45, 'new_value': 39920.89}, {'field': 'total_amount', 'old_value': 65461.29, 'new_value': 82387.31}, {'field': 'order_count', 'old_value': 565, 'new_value': 697}]
2025-06-07 09:01:11,440 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM57
2025-06-07 09:01:11,877 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM57
2025-06-07 09:01:11,877 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6140.0, 'new_value': 7100.0}, {'field': 'total_amount', 'old_value': 30287.0, 'new_value': 31247.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 28}]
2025-06-07 09:01:11,877 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMP5
2025-06-07 09:01:12,330 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMP5
2025-06-07 09:01:12,330 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10832.95, 'new_value': 12506.15}, {'field': 'offline_amount', 'old_value': 70866.48, 'new_value': 80133.78}, {'field': 'total_amount', 'old_value': 81699.43, 'new_value': 92639.93}, {'field': 'order_count', 'old_value': 588, 'new_value': 697}]
2025-06-07 09:01:12,330 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMVC1
2025-06-07 09:01:12,784 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMVC1
2025-06-07 09:01:12,784 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33394.0, 'new_value': 37893.0}, {'field': 'total_amount', 'old_value': 33394.0, 'new_value': 37893.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-07 09:01:12,784 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMWC1
2025-06-07 09:01:13,268 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMWC1
2025-06-07 09:01:13,268 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25396.0, 'new_value': 30617.0}, {'field': 'total_amount', 'old_value': 25396.0, 'new_value': 30617.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-07 09:01:13,268 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMYC1
2025-06-07 09:01:13,705 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMYC1
2025-06-07 09:01:13,705 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1923.1, 'new_value': 1925.1}, {'field': 'offline_amount', 'old_value': 5335.0, 'new_value': 10195.0}, {'field': 'total_amount', 'old_value': 7258.1, 'new_value': 12120.1}, {'field': 'order_count', 'old_value': 13, 'new_value': 16}]
2025-06-07 09:01:13,705 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM77
2025-06-07 09:01:14,252 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM77
2025-06-07 09:01:14,252 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30653.63, 'new_value': 36257.68}, {'field': 'total_amount', 'old_value': 30653.63, 'new_value': 36257.68}, {'field': 'order_count', 'old_value': 807, 'new_value': 938}]
2025-06-07 09:01:14,252 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMR5
2025-06-07 09:01:14,690 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMR5
2025-06-07 09:01:14,690 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84239.9, 'new_value': 91024.0}, {'field': 'total_amount', 'old_value': 84239.9, 'new_value': 91024.0}, {'field': 'order_count', 'old_value': 785, 'new_value': 856}]
2025-06-07 09:01:14,690 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM87
2025-06-07 09:01:15,158 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM87
2025-06-07 09:01:15,158 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4582.0, 'new_value': 4930.0}, {'field': 'total_amount', 'old_value': 4582.0, 'new_value': 4930.0}, {'field': 'order_count', 'old_value': 79, 'new_value': 85}]
2025-06-07 09:01:15,158 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMT5
2025-06-07 09:01:15,612 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMT5
2025-06-07 09:01:15,612 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 933700.0, 'new_value': 1333600.0}, {'field': 'total_amount', 'old_value': 933700.0, 'new_value': 1333600.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-06-07 09:01:15,612 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMA7
2025-06-07 09:01:16,065 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMA7
2025-06-07 09:01:16,065 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28576.7, 'new_value': 31043.72}, {'field': 'total_amount', 'old_value': 28576.7, 'new_value': 31043.72}, {'field': 'order_count', 'old_value': 10, 'new_value': 12}]
2025-06-07 09:01:16,065 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMV5
2025-06-07 09:01:16,502 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMV5
2025-06-07 09:01:16,502 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 203398.0, 'new_value': 223508.0}, {'field': 'total_amount', 'old_value': 203398.0, 'new_value': 223508.0}, {'field': 'order_count', 'old_value': 253, 'new_value': 287}]
2025-06-07 09:01:16,502 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMB7
2025-06-07 09:01:16,940 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMB7
2025-06-07 09:01:16,940 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2217.0, 'new_value': 3415.0}, {'field': 'total_amount', 'old_value': 2217.0, 'new_value': 3415.0}, {'field': 'order_count', 'old_value': 2375, 'new_value': 2377}]
2025-06-07 09:01:16,940 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMX5
2025-06-07 09:01:17,362 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMX5
2025-06-07 09:01:17,362 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22752.92, 'new_value': 26067.92}, {'field': 'total_amount', 'old_value': 22752.92, 'new_value': 26067.92}, {'field': 'order_count', 'old_value': 1837, 'new_value': 2144}]
2025-06-07 09:01:17,362 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM3D1
2025-06-07 09:01:17,955 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM3D1
2025-06-07 09:01:17,955 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31780.0, 'new_value': 46380.0}, {'field': 'total_amount', 'old_value': 31780.0, 'new_value': 46380.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 24}]
2025-06-07 09:01:17,955 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM4D1
2025-06-07 09:01:18,408 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM4D1
2025-06-07 09:01:18,408 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34013.5, 'new_value': 35605.5}, {'field': 'total_amount', 'old_value': 34013.5, 'new_value': 35605.5}, {'field': 'order_count', 'old_value': 84, 'new_value': 89}]
2025-06-07 09:01:18,408 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMZ5
2025-06-07 09:01:18,955 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMZ5
2025-06-07 09:01:18,955 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89276.87, 'new_value': 104679.29}, {'field': 'total_amount', 'old_value': 118357.17, 'new_value': 133759.59}, {'field': 'order_count', 'old_value': 443, 'new_value': 492}]
2025-06-07 09:01:18,955 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM5D1
2025-06-07 09:01:19,565 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM5D1
2025-06-07 09:01:19,565 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11112.9, 'new_value': 16408.8}, {'field': 'total_amount', 'old_value': 11112.9, 'new_value': 16408.8}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-06-07 09:01:19,565 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM06
2025-06-07 09:01:20,002 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM06
2025-06-07 09:01:20,002 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28390.0, 'new_value': 34489.0}, {'field': 'total_amount', 'old_value': 28391.0, 'new_value': 34490.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-06-07 09:01:20,002 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMC7
2025-06-07 09:01:20,487 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMC7
2025-06-07 09:01:20,487 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7750.6, 'new_value': 7820.6}, {'field': 'total_amount', 'old_value': 7750.6, 'new_value': 7820.6}, {'field': 'order_count', 'old_value': 66, 'new_value': 71}]
2025-06-07 09:01:20,487 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM6D1
2025-06-07 09:01:20,955 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM6D1
2025-06-07 09:01:20,955 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19204.0, 'new_value': 20293.7}, {'field': 'total_amount', 'old_value': 19204.0, 'new_value': 20293.7}, {'field': 'order_count', 'old_value': 123, 'new_value': 128}]
2025-06-07 09:01:20,955 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMD7
2025-06-07 09:01:21,408 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMD7
2025-06-07 09:01:21,408 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9657.4, 'new_value': 11774.2}, {'field': 'total_amount', 'old_value': 9657.4, 'new_value': 11774.2}, {'field': 'order_count', 'old_value': 371, 'new_value': 452}]
2025-06-07 09:01:21,408 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBME7
2025-06-07 09:01:21,862 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBME7
2025-06-07 09:01:21,862 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72346.64, 'new_value': 87811.64}, {'field': 'total_amount', 'old_value': 72346.64, 'new_value': 87811.64}, {'field': 'order_count', 'old_value': 379, 'new_value': 446}]
2025-06-07 09:01:21,862 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMF7
2025-06-07 09:01:22,315 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMF7
2025-06-07 09:01:22,315 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14528.0, 'new_value': 18187.0}, {'field': 'total_amount', 'old_value': 17343.0, 'new_value': 21002.0}, {'field': 'order_count', 'old_value': 57, 'new_value': 72}]
2025-06-07 09:01:22,315 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMG7
2025-06-07 09:01:22,768 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMG7
2025-06-07 09:01:22,768 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50839.97, 'new_value': 55601.8}, {'field': 'offline_amount', 'old_value': 9159.64, 'new_value': 11275.95}, {'field': 'total_amount', 'old_value': 59999.61, 'new_value': 66877.75}, {'field': 'order_count', 'old_value': 220, 'new_value': 247}]
2025-06-07 09:01:22,768 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM66
2025-06-07 09:01:23,190 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM66
2025-06-07 09:01:23,190 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69438.0, 'new_value': 77580.0}, {'field': 'total_amount', 'old_value': 69438.0, 'new_value': 77580.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 27}]
2025-06-07 09:01:23,190 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM76
2025-06-07 09:01:23,643 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM76
2025-06-07 09:01:23,658 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5118.12, 'new_value': 5762.38}, {'field': 'offline_amount', 'old_value': 58283.55, 'new_value': 66371.45}, {'field': 'total_amount', 'old_value': 63401.67, 'new_value': 72133.83}, {'field': 'order_count', 'old_value': 295, 'new_value': 344}]
2025-06-07 09:01:23,658 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM86
2025-06-07 09:01:24,096 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM86
2025-06-07 09:01:24,096 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38322.0, 'new_value': 40861.4}, {'field': 'total_amount', 'old_value': 38322.0, 'new_value': 40861.4}, {'field': 'order_count', 'old_value': 73, 'new_value': 83}]
2025-06-07 09:01:24,096 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMI7
2025-06-07 09:01:24,533 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMI7
2025-06-07 09:01:24,533 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12256.0, 'new_value': 16222.0}, {'field': 'total_amount', 'old_value': 12256.0, 'new_value': 16222.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 11}]
2025-06-07 09:01:24,533 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM96
2025-06-07 09:01:24,987 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM96
2025-06-07 09:01:24,987 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 58298.76, 'new_value': 67685.97}, {'field': 'offline_amount', 'old_value': 13472.32, 'new_value': 16025.23}, {'field': 'total_amount', 'old_value': 71771.08, 'new_value': 83711.2}, {'field': 'order_count', 'old_value': 389, 'new_value': 473}]
2025-06-07 09:01:24,987 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMA6
2025-06-07 09:01:25,408 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMA6
2025-06-07 09:01:25,408 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30644.0, 'new_value': 35189.0}, {'field': 'total_amount', 'old_value': 30644.0, 'new_value': 35189.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 57}]
2025-06-07 09:01:25,408 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMK7
2025-06-07 09:01:25,846 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMK7
2025-06-07 09:01:25,846 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7238.0, 'new_value': 8490.1}, {'field': 'total_amount', 'old_value': 7238.0, 'new_value': 8490.1}, {'field': 'order_count', 'old_value': 18, 'new_value': 23}]
2025-06-07 09:01:25,846 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM9D1
2025-06-07 09:01:26,330 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM9D1
2025-06-07 09:01:26,330 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10261.0, 'new_value': 10497.0}, {'field': 'total_amount', 'old_value': 10480.0, 'new_value': 10716.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 41}]
2025-06-07 09:01:26,330 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBML7
2025-06-07 09:01:26,799 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBML7
2025-06-07 09:01:26,799 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25173.0, 'new_value': 29506.0}, {'field': 'offline_amount', 'old_value': 107851.0, 'new_value': 120226.0}, {'field': 'total_amount', 'old_value': 133024.0, 'new_value': 149732.0}, {'field': 'order_count', 'old_value': 159, 'new_value': 190}]
2025-06-07 09:01:26,799 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMN7
2025-06-07 09:01:27,237 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMN7
2025-06-07 09:01:27,237 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72942.0, 'new_value': 98460.5}, {'field': 'total_amount', 'old_value': 72942.0, 'new_value': 98460.5}, {'field': 'order_count', 'old_value': 13, 'new_value': 16}]
2025-06-07 09:01:27,237 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMBD1
2025-06-07 09:01:27,690 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMBD1
2025-06-07 09:01:27,690 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18136.0, 'new_value': 21136.0}, {'field': 'total_amount', 'old_value': 18136.0, 'new_value': 21136.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-06-07 09:01:27,690 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMO7
2025-06-07 09:01:28,127 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMO7
2025-06-07 09:01:28,127 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 58478.7}, {'field': 'total_amount', 'old_value': 372489.8, 'new_value': 430968.5}, {'field': 'order_count', 'old_value': 675, 'new_value': 791}]
2025-06-07 09:01:28,127 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMP7
2025-06-07 09:01:28,580 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMP7
2025-06-07 09:01:28,580 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2810.2, 'new_value': 4114.7}, {'field': 'total_amount', 'old_value': 2810.2, 'new_value': 4114.7}, {'field': 'order_count', 'old_value': 5, 'new_value': 7}]
2025-06-07 09:01:28,580 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMF6
2025-06-07 09:01:29,033 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMF6
2025-06-07 09:01:29,033 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98807.0, 'new_value': 127302.0}, {'field': 'total_amount', 'old_value': 98807.0, 'new_value': 127302.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 16}]
2025-06-07 09:01:29,033 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMT7
2025-06-07 09:01:29,487 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMT7
2025-06-07 09:01:29,487 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56012.2, 'new_value': 67959.2}, {'field': 'total_amount', 'old_value': 56012.2, 'new_value': 67959.2}, {'field': 'order_count', 'old_value': 1685, 'new_value': 2042}]
2025-06-07 09:01:29,487 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMDD1
2025-06-07 09:01:29,940 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMDD1
2025-06-07 09:01:29,940 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17372.0, 'new_value': 18284.8}, {'field': 'total_amount', 'old_value': 17372.0, 'new_value': 18284.8}, {'field': 'order_count', 'old_value': 241, 'new_value': 255}]
2025-06-07 09:01:29,940 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMU7
2025-06-07 09:01:30,502 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMU7
2025-06-07 09:01:30,502 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33744.0, 'new_value': 41743.0}, {'field': 'total_amount', 'old_value': 33744.0, 'new_value': 41743.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-07 09:01:30,502 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMED1
2025-06-07 09:01:30,940 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMED1
2025-06-07 09:01:30,940 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 126914.82, 'new_value': 130260.82}, {'field': 'total_amount', 'old_value': 167444.06, 'new_value': 170790.06}, {'field': 'order_count', 'old_value': 327, 'new_value': 337}]
2025-06-07 09:01:30,940 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMI6
2025-06-07 09:01:31,346 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMI6
2025-06-07 09:01:31,346 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1554.3, 'new_value': 1621.2}, {'field': 'total_amount', 'old_value': 4454.3, 'new_value': 4521.2}, {'field': 'order_count', 'old_value': 28, 'new_value': 29}]
2025-06-07 09:01:31,346 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMW7
2025-06-07 09:01:31,830 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMW7
2025-06-07 09:01:31,830 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72854.0, 'new_value': 108549.0}, {'field': 'total_amount', 'old_value': 72854.0, 'new_value': 108549.0}, {'field': 'order_count', 'old_value': 79, 'new_value': 105}]
2025-06-07 09:01:31,830 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMHD1
2025-06-07 09:01:32,315 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMHD1
2025-06-07 09:01:32,315 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7329.03, 'new_value': 8636.65}, {'field': 'total_amount', 'old_value': 7329.03, 'new_value': 8636.65}, {'field': 'order_count', 'old_value': 31, 'new_value': 37}]
2025-06-07 09:01:32,315 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMX7
2025-06-07 09:01:32,846 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMX7
2025-06-07 09:01:32,846 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5699.02, 'new_value': 7211.72}, {'field': 'offline_amount', 'old_value': 6927.89, 'new_value': 7934.03}, {'field': 'total_amount', 'old_value': 12626.91, 'new_value': 15145.75}, {'field': 'order_count', 'old_value': 545, 'new_value': 664}]
2025-06-07 09:01:32,846 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMY7
2025-06-07 09:01:33,268 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMY7
2025-06-07 09:01:33,268 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25216.25, 'new_value': 32373.98}, {'field': 'total_amount', 'old_value': 25216.25, 'new_value': 32373.98}, {'field': 'order_count', 'old_value': 1299, 'new_value': 1694}]
2025-06-07 09:01:33,268 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMP6
2025-06-07 09:01:33,736 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMP6
2025-06-07 09:01:33,736 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83506.0, 'new_value': 88850.0}, {'field': 'total_amount', 'old_value': 83506.0, 'new_value': 88850.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 18}]
2025-06-07 09:01:33,736 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM08
2025-06-07 09:01:34,174 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM08
2025-06-07 09:01:34,174 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10640.09, 'new_value': 12894.31}, {'field': 'offline_amount', 'old_value': 6647.41, 'new_value': 8627.15}, {'field': 'total_amount', 'old_value': 17287.5, 'new_value': 21521.46}, {'field': 'order_count', 'old_value': 961, 'new_value': 1196}]
2025-06-07 09:01:34,174 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMQ6
2025-06-07 09:01:34,643 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMQ6
2025-06-07 09:01:34,643 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25366.0, 'new_value': 27531.0}, {'field': 'offline_amount', 'old_value': 15956.2, 'new_value': 17739.9}, {'field': 'total_amount', 'old_value': 41322.2, 'new_value': 45270.9}, {'field': 'order_count', 'old_value': 249, 'new_value': 278}]
2025-06-07 09:01:34,643 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM18
2025-06-07 09:01:35,096 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM18
2025-06-07 09:01:35,096 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18170.23, 'new_value': 22316.97}, {'field': 'offline_amount', 'old_value': 19101.38, 'new_value': 22824.57}, {'field': 'total_amount', 'old_value': 37271.61, 'new_value': 45141.54}, {'field': 'order_count', 'old_value': 1551, 'new_value': 1867}]
2025-06-07 09:01:35,096 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMMD1
2025-06-07 09:01:35,533 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMMD1
2025-06-07 09:01:35,533 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22978.0, 'new_value': 29830.0}, {'field': 'total_amount', 'old_value': 22978.0, 'new_value': 29830.0}, {'field': 'order_count', 'old_value': 84, 'new_value': 104}]
2025-06-07 09:01:35,533 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMR6
2025-06-07 09:01:36,018 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMR6
2025-06-07 09:01:36,018 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16112.34, 'new_value': 16484.59}, {'field': 'total_amount', 'old_value': 16112.34, 'new_value': 16484.59}, {'field': 'order_count', 'old_value': 65, 'new_value': 66}]
2025-06-07 09:01:36,018 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM38
2025-06-07 09:01:36,455 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM38
2025-06-07 09:01:36,455 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 60450.67, 'new_value': 77745.77}, {'field': 'offline_amount', 'old_value': 28034.6, 'new_value': 33368.7}, {'field': 'total_amount', 'old_value': 88485.27, 'new_value': 111114.47}, {'field': 'order_count', 'old_value': 275, 'new_value': 348}]
2025-06-07 09:01:36,455 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMND1
2025-06-07 09:01:36,830 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMND1
2025-06-07 09:01:36,830 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 144709.0, 'new_value': 163747.0}, {'field': 'total_amount', 'old_value': 144709.0, 'new_value': 163747.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-06-07 09:01:36,830 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM48
2025-06-07 09:01:37,299 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM48
2025-06-07 09:01:37,299 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10768.09, 'new_value': 12238.14}, {'field': 'offline_amount', 'old_value': 8081.73, 'new_value': 8670.08}, {'field': 'total_amount', 'old_value': 18849.82, 'new_value': 20908.22}, {'field': 'order_count', 'old_value': 394, 'new_value': 443}]
2025-06-07 09:01:37,299 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM58
2025-06-07 09:01:37,721 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM58
2025-06-07 09:01:37,721 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46896.0, 'new_value': 57096.0}, {'field': 'total_amount', 'old_value': 46896.0, 'new_value': 57096.0}, {'field': 'order_count', 'old_value': 3908, 'new_value': 4758}]
2025-06-07 09:01:37,721 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMS6
2025-06-07 09:01:38,174 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMS6
2025-06-07 09:01:38,174 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43093.49, 'new_value': 51513.26}, {'field': 'offline_amount', 'old_value': 147791.36, 'new_value': 169261.33}, {'field': 'total_amount', 'old_value': 190884.85, 'new_value': 220774.59}, {'field': 'order_count', 'old_value': 1151, 'new_value': 1339}]
2025-06-07 09:01:38,190 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMV6
2025-06-07 09:01:38,690 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMV6
2025-06-07 09:01:38,690 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50069.5, 'new_value': 58951.4}, {'field': 'total_amount', 'old_value': 50069.5, 'new_value': 58951.4}, {'field': 'order_count', 'old_value': 284, 'new_value': 337}]
2025-06-07 09:01:38,690 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMQD1
2025-06-07 09:01:39,127 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMQD1
2025-06-07 09:01:39,127 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24143.55, 'new_value': 28953.27}, {'field': 'offline_amount', 'old_value': 73817.28, 'new_value': 79375.89}, {'field': 'total_amount', 'old_value': 97960.83, 'new_value': 108329.16}, {'field': 'order_count', 'old_value': 838, 'new_value': 965}]
2025-06-07 09:01:39,127 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMUD1
2025-06-07 09:01:39,658 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMUD1
2025-06-07 09:01:39,658 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20160.0, 'new_value': 34960.0}, {'field': 'total_amount', 'old_value': 20160.0, 'new_value': 34960.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-06-07 09:01:39,658 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMX6
2025-06-07 09:01:40,174 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMX6
2025-06-07 09:01:40,174 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5734.9, 'new_value': 7252.48}, {'field': 'offline_amount', 'old_value': 10391.33, 'new_value': 11941.82}, {'field': 'total_amount', 'old_value': 16126.23, 'new_value': 19194.3}, {'field': 'order_count', 'old_value': 1353, 'new_value': 1641}]
2025-06-07 09:01:40,174 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMVD1
2025-06-07 09:01:40,611 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMVD1
2025-06-07 09:01:40,611 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15146.0, 'new_value': 15546.0}, {'field': 'total_amount', 'old_value': 15146.0, 'new_value': 15546.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 40}]
2025-06-07 09:01:40,611 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM07
2025-06-07 09:01:41,065 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM07
2025-06-07 09:01:41,065 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23996.4, 'new_value': 26208.9}, {'field': 'total_amount', 'old_value': 23996.4, 'new_value': 26208.9}, {'field': 'order_count', 'old_value': 48, 'new_value': 54}]
2025-06-07 09:01:41,065 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM98
2025-06-07 09:01:41,502 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM98
2025-06-07 09:01:41,502 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69851.64, 'new_value': 83509.98}, {'field': 'total_amount', 'old_value': 69851.64, 'new_value': 83509.98}, {'field': 'order_count', 'old_value': 244, 'new_value': 297}]
2025-06-07 09:01:41,502 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMXD1
2025-06-07 09:01:41,971 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMXD1
2025-06-07 09:01:41,971 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15399.7, 'new_value': 16809.6}, {'field': 'total_amount', 'old_value': 15867.6, 'new_value': 17277.5}, {'field': 'order_count', 'old_value': 111, 'new_value': 120}]
2025-06-07 09:01:41,971 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMA8
2025-06-07 09:01:42,455 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMA8
2025-06-07 09:01:42,455 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29913.0, 'new_value': 37595.0}, {'field': 'total_amount', 'old_value': 29913.0, 'new_value': 37595.0}, {'field': 'order_count', 'old_value': 2273, 'new_value': 2863}]
2025-06-07 09:01:42,455 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMZD1
2025-06-07 09:01:42,830 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMZD1
2025-06-07 09:01:42,830 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 130462.02, 'new_value': 132752.02}, {'field': 'offline_amount', 'old_value': 91834.02, 'new_value': 91944.02}, {'field': 'total_amount', 'old_value': 222296.04, 'new_value': 224696.04}, {'field': 'order_count', 'old_value': 1520, 'new_value': 1560}]
2025-06-07 09:01:42,830 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMEQ1
2025-06-07 09:01:43,252 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMEQ1
2025-06-07 09:01:43,252 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2892.0, 'new_value': 3255.0}, {'field': 'offline_amount', 'old_value': 470.0, 'new_value': 495.0}, {'field': 'total_amount', 'old_value': 3362.0, 'new_value': 3750.0}, {'field': 'order_count', 'old_value': 36, 'new_value': 40}]
2025-06-07 09:01:43,252 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM0E1
2025-06-07 09:01:43,721 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM0E1
2025-06-07 09:01:43,721 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58796.0, 'new_value': 62414.0}, {'field': 'total_amount', 'old_value': 58796.0, 'new_value': 62414.0}, {'field': 'order_count', 'old_value': 1871, 'new_value': 1986}]
2025-06-07 09:01:43,721 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM1E1
2025-06-07 09:01:44,252 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM1E1
2025-06-07 09:01:44,252 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61892.54, 'new_value': 69900.12}, {'field': 'total_amount', 'old_value': 61892.54, 'new_value': 69900.12}, {'field': 'order_count', 'old_value': 345, 'new_value': 389}]
2025-06-07 09:01:44,252 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMFQ1
2025-06-07 09:01:44,705 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMFQ1
2025-06-07 09:01:44,705 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28901.0, 'new_value': 35955.0}, {'field': 'offline_amount', 'old_value': 20162.0, 'new_value': 23640.0}, {'field': 'total_amount', 'old_value': 49063.0, 'new_value': 59595.0}, {'field': 'order_count', 'old_value': 644, 'new_value': 813}]
2025-06-07 09:01:44,705 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM2E1
2025-06-07 09:01:45,190 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM2E1
2025-06-07 09:01:45,190 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 63646.77, 'new_value': 74362.98}, {'field': 'offline_amount', 'old_value': 304845.07, 'new_value': 357765.94}, {'field': 'total_amount', 'old_value': 368491.84, 'new_value': 432128.92}, {'field': 'order_count', 'old_value': 1693, 'new_value': 2006}]
2025-06-07 09:01:45,190 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMHQ1
2025-06-07 09:01:45,674 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMHQ1
2025-06-07 09:01:45,674 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2422.0, 'new_value': 2910.0}, {'field': 'offline_amount', 'old_value': 9172.8, 'new_value': 10875.8}, {'field': 'total_amount', 'old_value': 11594.8, 'new_value': 13785.8}, {'field': 'order_count', 'old_value': 59, 'new_value': 71}]
2025-06-07 09:01:45,674 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM3E1
2025-06-07 09:01:46,190 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM3E1
2025-06-07 09:01:46,190 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11265.69, 'new_value': 12990.27}, {'field': 'offline_amount', 'old_value': 99141.89, 'new_value': 114011.18}, {'field': 'total_amount', 'old_value': 110407.58, 'new_value': 127001.45}, {'field': 'order_count', 'old_value': 501, 'new_value': 583}]
2025-06-07 09:01:46,190 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM47
2025-06-07 09:01:46,690 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM47
2025-06-07 09:01:46,690 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62104.0, 'new_value': 444636.0}, {'field': 'total_amount', 'old_value': 62104.0, 'new_value': 444636.0}, {'field': 'order_count', 'old_value': 100, 'new_value': 225}]
2025-06-07 09:01:46,690 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMIQ1
2025-06-07 09:01:47,190 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMIQ1
2025-06-07 09:01:47,190 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22354.5, 'new_value': 33422.8}, {'field': 'total_amount', 'old_value': 68183.1, 'new_value': 79251.4}, {'field': 'order_count', 'old_value': 1627, 'new_value': 1941}]
2025-06-07 09:01:47,190 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM67
2025-06-07 09:01:47,643 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM67
2025-06-07 09:01:47,643 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67595.0, 'new_value': 78416.0}, {'field': 'total_amount', 'old_value': 67595.0, 'new_value': 78416.0}, {'field': 'order_count', 'old_value': 248, 'new_value': 283}]
2025-06-07 09:01:47,643 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM4E1
2025-06-07 09:01:48,127 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM4E1
2025-06-07 09:01:48,127 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 255350.2, 'new_value': 264168.3}, {'field': 'total_amount', 'old_value': 258945.7, 'new_value': 267763.8}, {'field': 'order_count', 'old_value': 298, 'new_value': 306}]
2025-06-07 09:01:48,127 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM5E1
2025-06-07 09:01:48,596 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM5E1
2025-06-07 09:01:48,596 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10477.6, 'new_value': 11271.6}, {'field': 'total_amount', 'old_value': 12064.6, 'new_value': 12858.6}, {'field': 'order_count', 'old_value': 88, 'new_value': 92}]
2025-06-07 09:01:48,596 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMA7
2025-06-07 09:01:49,049 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMA7
2025-06-07 09:01:49,049 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 341420.0, 'new_value': 385663.0}, {'field': 'total_amount', 'old_value': 341420.0, 'new_value': 385663.0}, {'field': 'order_count', 'old_value': 1491, 'new_value': 1713}]
2025-06-07 09:01:49,049 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM6E1
2025-06-07 09:01:49,486 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM6E1
2025-06-07 09:01:49,486 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70328.19, 'new_value': 79518.38}, {'field': 'total_amount', 'old_value': 70328.19, 'new_value': 79518.38}, {'field': 'order_count', 'old_value': 605, 'new_value': 693}]
2025-06-07 09:01:49,486 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMJQ1
2025-06-07 09:01:49,893 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMJQ1
2025-06-07 09:01:49,893 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19960.0, 'new_value': 27370.0}, {'field': 'total_amount', 'old_value': 19960.0, 'new_value': 27370.0}, {'field': 'order_count', 'old_value': 42, 'new_value': 60}]
2025-06-07 09:01:49,893 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMKQ1
2025-06-07 09:01:50,361 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMKQ1
2025-06-07 09:01:50,361 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11270.0, 'new_value': 12496.0}, {'field': 'total_amount', 'old_value': 11270.0, 'new_value': 12496.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-06-07 09:01:50,361 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMC7
2025-06-07 09:01:50,814 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMC7
2025-06-07 09:01:50,814 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26639.6, 'new_value': 31871.8}, {'field': 'offline_amount', 'old_value': 20205.0, 'new_value': 25338.1}, {'field': 'total_amount', 'old_value': 46844.6, 'new_value': 57209.9}, {'field': 'order_count', 'old_value': 1112, 'new_value': 1337}]
2025-06-07 09:01:50,814 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMLQ1
2025-06-07 09:01:51,299 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMLQ1
2025-06-07 09:01:51,299 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 176503.0, 'new_value': 207354.0}, {'field': 'total_amount', 'old_value': 176503.0, 'new_value': 207354.0}, {'field': 'order_count', 'old_value': 217, 'new_value': 255}]
2025-06-07 09:01:51,299 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMMQ1
2025-06-07 09:01:51,830 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMMQ1
2025-06-07 09:01:51,830 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16946.5, 'new_value': 20349.1}, {'field': 'total_amount', 'old_value': 16946.5, 'new_value': 20349.1}, {'field': 'order_count', 'old_value': 80, 'new_value': 96}]
2025-06-07 09:01:51,830 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMNQ1
2025-06-07 09:01:52,299 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMNQ1
2025-06-07 09:01:52,299 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6230.9, 'new_value': 7504.21}, {'field': 'offline_amount', 'old_value': 9109.07, 'new_value': 10982.92}, {'field': 'total_amount', 'old_value': 15339.97, 'new_value': 18487.13}, {'field': 'order_count', 'old_value': 792, 'new_value': 955}]
2025-06-07 09:01:52,299 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMOQ1
2025-06-07 09:01:52,783 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMOQ1
2025-06-07 09:01:52,783 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31155.9, 'new_value': 35346.7}, {'field': 'total_amount', 'old_value': 31155.9, 'new_value': 35346.7}, {'field': 'order_count', 'old_value': 138, 'new_value': 154}]
2025-06-07 09:01:52,783 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMAE1
2025-06-07 09:01:53,252 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMAE1
2025-06-07 09:01:53,252 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34400.0, 'new_value': 35180.0}, {'field': 'total_amount', 'old_value': 34400.0, 'new_value': 35180.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-07 09:01:53,252 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMBE1
2025-06-07 09:01:53,705 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMBE1
2025-06-07 09:01:53,705 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4512.0, 'new_value': 4988.5}, {'field': 'total_amount', 'old_value': 4512.0, 'new_value': 4988.5}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-06-07 09:01:53,705 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMPQ1
2025-06-07 09:01:54,158 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMPQ1
2025-06-07 09:01:54,158 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24399.0, 'new_value': 34399.0}, {'field': 'total_amount', 'old_value': 29186.8, 'new_value': 39186.8}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-06-07 09:01:54,158 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMQQ1
2025-06-07 09:01:54,580 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMQQ1
2025-06-07 09:01:54,580 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9486.0, 'new_value': 9651.0}, {'field': 'total_amount', 'old_value': 9486.0, 'new_value': 9651.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-06-07 09:01:54,580 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMSQ1
2025-06-07 09:01:55,049 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMSQ1
2025-06-07 09:01:55,049 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40069.0, 'new_value': 42223.0}, {'field': 'total_amount', 'old_value': 40069.0, 'new_value': 42223.0}, {'field': 'order_count', 'old_value': 1096, 'new_value': 1160}]
2025-06-07 09:01:55,049 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMUQ1
2025-06-07 09:01:55,471 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMUQ1
2025-06-07 09:01:55,471 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44848.9, 'new_value': 54078.9}, {'field': 'total_amount', 'old_value': 44848.9, 'new_value': 54078.9}, {'field': 'order_count', 'old_value': 205, 'new_value': 245}]
2025-06-07 09:01:55,471 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM8Q
2025-06-07 09:01:55,908 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM8Q
2025-06-07 09:01:55,908 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1010.8, 'new_value': 1088.6}, {'field': 'offline_amount', 'old_value': 2349.85, 'new_value': 3750.65}, {'field': 'total_amount', 'old_value': 3360.65, 'new_value': 4839.25}, {'field': 'order_count', 'old_value': 37, 'new_value': 49}]
2025-06-07 09:01:55,908 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMEE1
2025-06-07 09:01:56,502 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMEE1
2025-06-07 09:01:56,502 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 82055.54, 'new_value': 88898.24}, {'field': 'offline_amount', 'old_value': 1141.0, 'new_value': 1266.0}, {'field': 'total_amount', 'old_value': 83196.54, 'new_value': 90164.24}, {'field': 'order_count', 'old_value': 7491, 'new_value': 7880}]
2025-06-07 09:01:56,502 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMWQ1
2025-06-07 09:01:56,971 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMWQ1
2025-06-07 09:01:56,971 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66700.51, 'new_value': 78167.24}, {'field': 'total_amount', 'old_value': 82688.71, 'new_value': 94155.44}, {'field': 'order_count', 'old_value': 818, 'new_value': 1957}]
2025-06-07 09:01:56,971 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMXQ1
2025-06-07 09:01:57,424 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMXQ1
2025-06-07 09:01:57,424 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69102.0, 'new_value': 83988.0}, {'field': 'total_amount', 'old_value': 69102.0, 'new_value': 83988.0}, {'field': 'order_count', 'old_value': 56, 'new_value': 69}]
2025-06-07 09:01:57,424 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM0O
2025-06-07 09:01:57,861 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM0O
2025-06-07 09:01:57,861 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18352.0, 'new_value': 24176.0}, {'field': 'total_amount', 'old_value': 18352.0, 'new_value': 24176.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 17}]
2025-06-07 09:01:57,861 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMYQ1
2025-06-07 09:01:58,361 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMYQ1
2025-06-07 09:01:58,361 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13612.51, 'new_value': 15329.51}, {'field': 'total_amount', 'old_value': 13612.51, 'new_value': 15329.51}, {'field': 'order_count', 'old_value': 82, 'new_value': 96}]
2025-06-07 09:01:58,361 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM1R1
2025-06-07 09:01:58,736 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM1R1
2025-06-07 09:01:58,736 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41894.6, 'new_value': 63587.5}, {'field': 'total_amount', 'old_value': 41894.6, 'new_value': 63587.5}, {'field': 'order_count', 'old_value': 84, 'new_value': 112}]
2025-06-07 09:01:58,736 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM1O
2025-06-07 09:01:59,189 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM1O
2025-06-07 09:01:59,189 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15385.0, 'new_value': 18254.0}, {'field': 'total_amount', 'old_value': 15385.0, 'new_value': 18254.0}, {'field': 'order_count', 'old_value': 730, 'new_value': 862}]
2025-06-07 09:01:59,189 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM2O
2025-06-07 09:01:59,596 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM2O
2025-06-07 09:01:59,596 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17852.0, 'new_value': 20018.0}, {'field': 'offline_amount', 'old_value': 48694.0, 'new_value': 60834.0}, {'field': 'total_amount', 'old_value': 66546.0, 'new_value': 80852.0}, {'field': 'order_count', 'old_value': 285, 'new_value': 348}]
2025-06-07 09:01:59,596 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM3O
2025-06-07 09:02:00,143 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM3O
2025-06-07 09:02:00,143 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57331.49, 'new_value': 68041.26}, {'field': 'total_amount', 'old_value': 57331.49, 'new_value': 68041.26}, {'field': 'order_count', 'old_value': 295, 'new_value': 350}]
2025-06-07 09:02:00,143 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM3R1
2025-06-07 09:02:00,643 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM3R1
2025-06-07 09:02:00,643 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19857.52, 'new_value': 23939.03}, {'field': 'offline_amount', 'old_value': 22096.03, 'new_value': 26090.03}, {'field': 'total_amount', 'old_value': 41953.55, 'new_value': 50029.06}, {'field': 'order_count', 'old_value': 1058, 'new_value': 1284}]
2025-06-07 09:02:00,643 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM4R1
2025-06-07 09:02:01,064 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM4R1
2025-06-07 09:02:01,064 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40060.79, 'new_value': 48215.05}, {'field': 'total_amount', 'old_value': 40060.79, 'new_value': 48215.05}, {'field': 'order_count', 'old_value': 162, 'new_value': 196}]
2025-06-07 09:02:01,064 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM5R1
2025-06-07 09:02:01,549 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM5R1
2025-06-07 09:02:01,549 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2498.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2498.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 2}]
2025-06-07 09:02:01,549 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM6R1
2025-06-07 09:02:01,986 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM6R1
2025-06-07 09:02:01,986 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48891.25, 'new_value': 59371.25}, {'field': 'total_amount', 'old_value': 56421.95, 'new_value': 66901.95}, {'field': 'order_count', 'old_value': 98, 'new_value': 114}]
2025-06-07 09:02:01,986 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM7R1
2025-06-07 09:02:02,518 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM7R1
2025-06-07 09:02:02,518 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 645.0, 'new_value': 825.0}, {'field': 'offline_amount', 'old_value': 7480.0, 'new_value': 11450.0}, {'field': 'total_amount', 'old_value': 8125.0, 'new_value': 12275.0}, {'field': 'order_count', 'old_value': 121, 'new_value': 169}]
2025-06-07 09:02:02,518 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM8R1
2025-06-07 09:02:02,955 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM8R1
2025-06-07 09:02:02,955 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29769.0, 'new_value': 38175.0}, {'field': 'offline_amount', 'old_value': 18785.0, 'new_value': 31514.0}, {'field': 'total_amount', 'old_value': 48554.0, 'new_value': 69689.0}, {'field': 'order_count', 'old_value': 3333, 'new_value': 24468}]
2025-06-07 09:02:02,955 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM9R1
2025-06-07 09:02:03,377 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM9R1
2025-06-07 09:02:03,377 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9226.91, 'new_value': 11441.83}, {'field': 'offline_amount', 'old_value': 13824.23, 'new_value': 16815.73}, {'field': 'total_amount', 'old_value': 23051.14, 'new_value': 28257.56}, {'field': 'order_count', 'old_value': 1130, 'new_value': 1376}]
2025-06-07 09:02:03,377 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMAR1
2025-06-07 09:02:03,861 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMAR1
2025-06-07 09:02:03,861 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4487.9, 'new_value': 5897.8}, {'field': 'total_amount', 'old_value': 5406.68, 'new_value': 6816.58}, {'field': 'order_count', 'old_value': 21, 'new_value': 25}]
2025-06-07 09:02:03,861 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMDR1
2025-06-07 09:02:04,330 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMDR1
2025-06-07 09:02:04,330 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 199.0, 'new_value': 1695.0}, {'field': 'total_amount', 'old_value': 199.0, 'new_value': 1695.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 3}]
2025-06-07 09:02:04,330 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMAO
2025-06-07 09:02:04,768 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMAO
2025-06-07 09:02:04,768 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18249.0, 'new_value': 22175.0}, {'field': 'total_amount', 'old_value': 18249.0, 'new_value': 22175.0}, {'field': 'order_count', 'old_value': 3565, 'new_value': 3826}]
2025-06-07 09:02:04,768 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMER1
2025-06-07 09:02:05,283 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMER1
2025-06-07 09:02:05,283 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9860.0, 'new_value': 18957.0}, {'field': 'offline_amount', 'old_value': 262142.0, 'new_value': 302772.0}, {'field': 'total_amount', 'old_value': 272002.0, 'new_value': 321729.0}, {'field': 'order_count', 'old_value': 6669, 'new_value': 8129}]
2025-06-07 09:02:05,283 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMHR1
2025-06-07 09:02:05,705 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMHR1
2025-06-07 09:02:05,705 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3522.0, 'new_value': 4992.0}, {'field': 'total_amount', 'old_value': 3522.0, 'new_value': 4992.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 11}]
2025-06-07 09:02:05,705 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMIR1
2025-06-07 09:02:06,174 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMIR1
2025-06-07 09:02:06,174 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9460.0, 'new_value': 11885.5}, {'field': 'total_amount', 'old_value': 9460.0, 'new_value': 11885.5}, {'field': 'order_count', 'old_value': 314, 'new_value': 365}]
2025-06-07 09:02:06,174 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMCO
2025-06-07 09:02:06,596 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMCO
2025-06-07 09:02:06,596 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8454.8, 'new_value': 10762.1}, {'field': 'total_amount', 'old_value': 8454.8, 'new_value': 10762.1}, {'field': 'order_count', 'old_value': 50, 'new_value': 60}]
2025-06-07 09:02:06,596 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMEO
2025-06-07 09:02:07,002 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMEO
2025-06-07 09:02:07,002 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 64130.0, 'new_value': 70902.0}, {'field': 'offline_amount', 'old_value': 2163.5, 'new_value': 2439.5}, {'field': 'total_amount', 'old_value': 66293.5, 'new_value': 73341.5}, {'field': 'order_count', 'old_value': 582, 'new_value': 652}]
2025-06-07 09:02:07,002 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMFO
2025-06-07 09:02:07,408 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMFO
2025-06-07 09:02:07,408 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6753.8, 'new_value': 7770.8}, {'field': 'offline_amount', 'old_value': 11401.8, 'new_value': 13937.6}, {'field': 'total_amount', 'old_value': 18155.6, 'new_value': 21708.4}, {'field': 'order_count', 'old_value': 746, 'new_value': 912}]
2025-06-07 09:02:07,408 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMHO
2025-06-07 09:02:07,877 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMHO
2025-06-07 09:02:07,877 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7834.3, 'new_value': 9604.71}, {'field': 'total_amount', 'old_value': 8203.9, 'new_value': 9974.31}, {'field': 'order_count', 'old_value': 74, 'new_value': 90}]
2025-06-07 09:02:07,877 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMIO
2025-06-07 09:02:08,346 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMIO
2025-06-07 09:02:08,346 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6309.67, 'new_value': 7087.0}, {'field': 'total_amount', 'old_value': 6309.67, 'new_value': 7087.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 24}]
2025-06-07 09:02:08,346 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMPO
2025-06-07 09:02:08,689 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMPO
2025-06-07 09:02:08,689 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15549.0, 'new_value': 19744.0}, {'field': 'total_amount', 'old_value': 15549.0, 'new_value': 19744.0}, {'field': 'order_count', 'old_value': 91, 'new_value': 111}]
2025-06-07 09:02:08,689 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM0P
2025-06-07 09:02:09,143 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM0P
2025-06-07 09:02:09,143 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17148.0, 'new_value': 17960.0}, {'field': 'total_amount', 'old_value': 17148.0, 'new_value': 17960.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-06-07 09:02:09,143 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM4P
2025-06-07 09:02:09,658 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM4P
2025-06-07 09:02:09,658 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 113031.0, 'new_value': 127339.0}, {'field': 'total_amount', 'old_value': 113031.0, 'new_value': 127339.0}, {'field': 'order_count', 'old_value': 650, 'new_value': 775}]
2025-06-07 09:02:09,658 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM5P
2025-06-07 09:02:10,111 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM5P
2025-06-07 09:02:10,111 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2869.25, 'new_value': 2908.25}, {'field': 'offline_amount', 'old_value': 16011.1, 'new_value': 17128.1}, {'field': 'total_amount', 'old_value': 18880.35, 'new_value': 20036.35}, {'field': 'order_count', 'old_value': 119, 'new_value': 125}]
2025-06-07 09:02:10,111 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMAP
2025-06-07 09:02:10,549 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMAP
2025-06-07 09:02:10,549 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30776.92, 'new_value': 38534.94}, {'field': 'offline_amount', 'old_value': 7279.72, 'new_value': 9103.72}, {'field': 'total_amount', 'old_value': 38056.64, 'new_value': 47638.66}, {'field': 'order_count', 'old_value': 161, 'new_value': 207}]
2025-06-07 09:02:10,549 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMKP
2025-06-07 09:02:10,939 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMKP
2025-06-07 09:02:10,939 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16413.51, 'new_value': 17473.54}, {'field': 'offline_amount', 'old_value': 52221.47, 'new_value': 61235.67}, {'field': 'total_amount', 'old_value': 68634.98, 'new_value': 78709.21}, {'field': 'order_count', 'old_value': 420, 'new_value': 508}]
2025-06-07 09:02:10,939 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMPP
2025-06-07 09:02:11,330 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMPP
2025-06-07 09:02:11,330 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19250.0, 'new_value': 22980.0}, {'field': 'total_amount', 'old_value': 19250.0, 'new_value': 22980.0}, {'field': 'order_count', 'old_value': 97, 'new_value': 116}]
2025-06-07 09:02:11,330 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMUP
2025-06-07 09:02:11,830 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMUP
2025-06-07 09:02:11,830 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6890.0, 'new_value': 9275.0}, {'field': 'total_amount', 'old_value': 10335.0, 'new_value': 12720.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 48}]
2025-06-07 09:02:11,830 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMVP
2025-06-07 09:02:12,346 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMVP
2025-06-07 09:02:12,346 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8978.92, 'new_value': 10568.73}, {'field': 'total_amount', 'old_value': 8978.92, 'new_value': 10568.73}, {'field': 'order_count', 'old_value': 1126, 'new_value': 1341}]
2025-06-07 09:02:12,346 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMYP
2025-06-07 09:02:12,783 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMYP
2025-06-07 09:02:12,783 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63104.61, 'new_value': 77402.34}, {'field': 'total_amount', 'old_value': 63104.61, 'new_value': 77402.34}, {'field': 'order_count', 'old_value': 186, 'new_value': 223}]
2025-06-07 09:02:12,783 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM0Q
2025-06-07 09:02:13,221 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM0Q
2025-06-07 09:02:13,221 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21331.0, 'new_value': 25362.0}, {'field': 'total_amount', 'old_value': 21331.0, 'new_value': 25362.0}, {'field': 'order_count', 'old_value': 1176, 'new_value': 1411}]
2025-06-07 09:02:13,221 - INFO - 开始批量插入 1 条新记录
2025-06-07 09:02:13,361 - INFO - 批量插入响应状态码: 200
2025-06-07 09:02:13,361 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 07 Jun 2025 01:02:19 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5FF1B6A5-7105-781B-B0A3-BF388239EC9F', 'x-acs-trace-id': 'ab9f0e870b17385fd7fc2c4e053955ad', 'etag': '5tykCOYY2zeI3k52w2JgDZw9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-07 09:02:13,361 - INFO - 批量插入响应体: {'result': ['FINST-5A966081KR2WKZ2OCIITF9X4YONC3OIQ4JLBMU']}
2025-06-07 09:02:13,361 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-07 09:02:13,361 - INFO - 成功插入的数据ID: ['FINST-5A966081KR2WKZ2OCIITF9X4YONC3OIQ4JLBMU']
2025-06-07 09:02:16,377 - INFO - 批量插入完成，共 1 条记录
2025-06-07 09:02:16,377 - INFO - 日期 2025-06 处理完成 - 更新: 211 条，插入: 1 条，错误: 0 条
2025-06-07 09:02:16,377 - INFO - 数据同步完成！更新: 211 条，插入: 1 条，错误: 0 条
2025-06-07 09:02:16,377 - INFO - =================同步完成====================
2025-06-07 12:00:02,900 - INFO - =================使用默认全量同步=============
2025-06-07 12:00:04,525 - INFO - MySQL查询成功，共获取 3926 条记录
2025-06-07 12:00:04,525 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-07 12:00:04,556 - INFO - 开始处理日期: 2025-01
2025-06-07 12:00:04,556 - INFO - Request Parameters - Page 1:
2025-06-07 12:00:04,556 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:04,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:05,978 - INFO - Response - Page 1:
2025-06-07 12:00:06,181 - INFO - 第 1 页获取到 100 条记录
2025-06-07 12:00:06,181 - INFO - Request Parameters - Page 2:
2025-06-07 12:00:06,181 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:06,181 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:06,806 - INFO - Response - Page 2:
2025-06-07 12:00:07,010 - INFO - 第 2 页获取到 100 条记录
2025-06-07 12:00:07,010 - INFO - Request Parameters - Page 3:
2025-06-07 12:00:07,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:07,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:07,588 - INFO - Response - Page 3:
2025-06-07 12:00:07,791 - INFO - 第 3 页获取到 100 条记录
2025-06-07 12:00:07,791 - INFO - Request Parameters - Page 4:
2025-06-07 12:00:07,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:07,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:08,260 - INFO - Response - Page 4:
2025-06-07 12:00:08,463 - INFO - 第 4 页获取到 100 条记录
2025-06-07 12:00:08,463 - INFO - Request Parameters - Page 5:
2025-06-07 12:00:08,463 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:08,463 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:08,947 - INFO - Response - Page 5:
2025-06-07 12:00:09,150 - INFO - 第 5 页获取到 100 条记录
2025-06-07 12:00:09,150 - INFO - Request Parameters - Page 6:
2025-06-07 12:00:09,150 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:09,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:09,728 - INFO - Response - Page 6:
2025-06-07 12:00:09,931 - INFO - 第 6 页获取到 100 条记录
2025-06-07 12:00:09,931 - INFO - Request Parameters - Page 7:
2025-06-07 12:00:09,931 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:09,931 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:10,385 - INFO - Response - Page 7:
2025-06-07 12:00:10,588 - INFO - 第 7 页获取到 82 条记录
2025-06-07 12:00:10,588 - INFO - 查询完成，共获取到 682 条记录
2025-06-07 12:00:10,588 - INFO - 获取到 682 条表单数据
2025-06-07 12:00:10,588 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-07 12:00:10,603 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 12:00:10,603 - INFO - 开始处理日期: 2025-02
2025-06-07 12:00:10,603 - INFO - Request Parameters - Page 1:
2025-06-07 12:00:10,603 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:10,603 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:11,135 - INFO - Response - Page 1:
2025-06-07 12:00:11,338 - INFO - 第 1 页获取到 100 条记录
2025-06-07 12:00:11,338 - INFO - Request Parameters - Page 2:
2025-06-07 12:00:11,338 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:11,338 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:12,353 - INFO - Response - Page 2:
2025-06-07 12:00:12,556 - INFO - 第 2 页获取到 100 条记录
2025-06-07 12:00:12,556 - INFO - Request Parameters - Page 3:
2025-06-07 12:00:12,556 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:12,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:13,041 - INFO - Response - Page 3:
2025-06-07 12:00:13,244 - INFO - 第 3 页获取到 100 条记录
2025-06-07 12:00:13,244 - INFO - Request Parameters - Page 4:
2025-06-07 12:00:13,244 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:13,244 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:13,744 - INFO - Response - Page 4:
2025-06-07 12:00:13,947 - INFO - 第 4 页获取到 100 条记录
2025-06-07 12:00:13,947 - INFO - Request Parameters - Page 5:
2025-06-07 12:00:13,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:13,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:14,463 - INFO - Response - Page 5:
2025-06-07 12:00:14,666 - INFO - 第 5 页获取到 100 条记录
2025-06-07 12:00:14,666 - INFO - Request Parameters - Page 6:
2025-06-07 12:00:14,666 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:14,666 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:15,213 - INFO - Response - Page 6:
2025-06-07 12:00:15,416 - INFO - 第 6 页获取到 100 条记录
2025-06-07 12:00:15,416 - INFO - Request Parameters - Page 7:
2025-06-07 12:00:15,416 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:15,416 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:15,885 - INFO - Response - Page 7:
2025-06-07 12:00:16,088 - INFO - 第 7 页获取到 70 条记录
2025-06-07 12:00:16,088 - INFO - 查询完成，共获取到 670 条记录
2025-06-07 12:00:16,088 - INFO - 获取到 670 条表单数据
2025-06-07 12:00:16,088 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-07 12:00:16,103 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 12:00:16,103 - INFO - 开始处理日期: 2025-03
2025-06-07 12:00:16,103 - INFO - Request Parameters - Page 1:
2025-06-07 12:00:16,103 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:16,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:16,666 - INFO - Response - Page 1:
2025-06-07 12:00:16,869 - INFO - 第 1 页获取到 100 条记录
2025-06-07 12:00:16,869 - INFO - Request Parameters - Page 2:
2025-06-07 12:00:16,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:16,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:17,400 - INFO - Response - Page 2:
2025-06-07 12:00:17,603 - INFO - 第 2 页获取到 100 条记录
2025-06-07 12:00:17,603 - INFO - Request Parameters - Page 3:
2025-06-07 12:00:17,603 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:17,603 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:18,150 - INFO - Response - Page 3:
2025-06-07 12:00:18,353 - INFO - 第 3 页获取到 100 条记录
2025-06-07 12:00:18,353 - INFO - Request Parameters - Page 4:
2025-06-07 12:00:18,353 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:18,353 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:18,853 - INFO - Response - Page 4:
2025-06-07 12:00:19,056 - INFO - 第 4 页获取到 100 条记录
2025-06-07 12:00:19,056 - INFO - Request Parameters - Page 5:
2025-06-07 12:00:19,056 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:19,056 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:19,635 - INFO - Response - Page 5:
2025-06-07 12:00:19,838 - INFO - 第 5 页获取到 100 条记录
2025-06-07 12:00:19,838 - INFO - Request Parameters - Page 6:
2025-06-07 12:00:19,838 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:19,838 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:20,322 - INFO - Response - Page 6:
2025-06-07 12:00:20,525 - INFO - 第 6 页获取到 100 条记录
2025-06-07 12:00:20,525 - INFO - Request Parameters - Page 7:
2025-06-07 12:00:20,525 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:20,525 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:20,994 - INFO - Response - Page 7:
2025-06-07 12:00:21,197 - INFO - 第 7 页获取到 61 条记录
2025-06-07 12:00:21,197 - INFO - 查询完成，共获取到 661 条记录
2025-06-07 12:00:21,197 - INFO - 获取到 661 条表单数据
2025-06-07 12:00:21,197 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-07 12:00:21,213 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 12:00:21,213 - INFO - 开始处理日期: 2025-04
2025-06-07 12:00:21,213 - INFO - Request Parameters - Page 1:
2025-06-07 12:00:21,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:21,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:21,744 - INFO - Response - Page 1:
2025-06-07 12:00:21,947 - INFO - 第 1 页获取到 100 条记录
2025-06-07 12:00:21,947 - INFO - Request Parameters - Page 2:
2025-06-07 12:00:21,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:21,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:22,431 - INFO - Response - Page 2:
2025-06-07 12:00:22,634 - INFO - 第 2 页获取到 100 条记录
2025-06-07 12:00:22,634 - INFO - Request Parameters - Page 3:
2025-06-07 12:00:22,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:22,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:23,103 - INFO - Response - Page 3:
2025-06-07 12:00:23,306 - INFO - 第 3 页获取到 100 条记录
2025-06-07 12:00:23,306 - INFO - Request Parameters - Page 4:
2025-06-07 12:00:23,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:23,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:24,447 - INFO - Response - Page 4:
2025-06-07 12:00:24,650 - INFO - 第 4 页获取到 100 条记录
2025-06-07 12:00:24,650 - INFO - Request Parameters - Page 5:
2025-06-07 12:00:24,650 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:24,650 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:25,134 - INFO - Response - Page 5:
2025-06-07 12:00:25,338 - INFO - 第 5 页获取到 100 条记录
2025-06-07 12:00:25,338 - INFO - Request Parameters - Page 6:
2025-06-07 12:00:25,338 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:25,338 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:25,838 - INFO - Response - Page 6:
2025-06-07 12:00:26,041 - INFO - 第 6 页获取到 100 条记录
2025-06-07 12:00:26,041 - INFO - Request Parameters - Page 7:
2025-06-07 12:00:26,041 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:26,041 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:26,509 - INFO - Response - Page 7:
2025-06-07 12:00:26,713 - INFO - 第 7 页获取到 56 条记录
2025-06-07 12:00:26,713 - INFO - 查询完成，共获取到 656 条记录
2025-06-07 12:00:26,713 - INFO - 获取到 656 条表单数据
2025-06-07 12:00:26,713 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-07 12:00:26,728 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 12:00:26,728 - INFO - 开始处理日期: 2025-05
2025-06-07 12:00:26,728 - INFO - Request Parameters - Page 1:
2025-06-07 12:00:26,728 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:26,728 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:27,322 - INFO - Response - Page 1:
2025-06-07 12:00:27,525 - INFO - 第 1 页获取到 100 条记录
2025-06-07 12:00:27,525 - INFO - Request Parameters - Page 2:
2025-06-07 12:00:27,525 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:27,525 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:27,994 - INFO - Response - Page 2:
2025-06-07 12:00:28,197 - INFO - 第 2 页获取到 100 条记录
2025-06-07 12:00:28,197 - INFO - Request Parameters - Page 3:
2025-06-07 12:00:28,197 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:28,197 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:28,681 - INFO - Response - Page 3:
2025-06-07 12:00:28,884 - INFO - 第 3 页获取到 100 条记录
2025-06-07 12:00:28,884 - INFO - Request Parameters - Page 4:
2025-06-07 12:00:28,884 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:28,884 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:29,400 - INFO - Response - Page 4:
2025-06-07 12:00:29,603 - INFO - 第 4 页获取到 100 条记录
2025-06-07 12:00:29,603 - INFO - Request Parameters - Page 5:
2025-06-07 12:00:29,603 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:29,603 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:30,088 - INFO - Response - Page 5:
2025-06-07 12:00:30,291 - INFO - 第 5 页获取到 100 条记录
2025-06-07 12:00:30,291 - INFO - Request Parameters - Page 6:
2025-06-07 12:00:30,291 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:30,291 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:30,806 - INFO - Response - Page 6:
2025-06-07 12:00:31,009 - INFO - 第 6 页获取到 100 条记录
2025-06-07 12:00:31,009 - INFO - Request Parameters - Page 7:
2025-06-07 12:00:31,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:31,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:31,400 - INFO - Response - Page 7:
2025-06-07 12:00:31,603 - INFO - 第 7 页获取到 37 条记录
2025-06-07 12:00:31,603 - INFO - 查询完成，共获取到 637 条记录
2025-06-07 12:00:31,603 - INFO - 获取到 637 条表单数据
2025-06-07 12:00:31,603 - INFO - 当前日期 2025-05 有 638 条MySQL数据需要处理
2025-06-07 12:00:31,619 - INFO - 开始批量插入 1 条新记录
2025-06-07 12:00:31,775 - INFO - 批量插入响应状态码: 200
2025-06-07 12:00:31,775 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 07 Jun 2025 04:00:37 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7F646A39-440B-7077-BC8D-E296F205B232', 'x-acs-trace-id': 'efd34ed612da0def9d0973852dd828ba', 'etag': '6UPM3dzgUEgFm03tgOgPBtQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-07 12:00:31,775 - INFO - 批量插入响应体: {'result': ['FINST-RTA66X610Q2WXYH1C9XWS8CD7RJ73LI1IPLBMAN']}
2025-06-07 12:00:31,775 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-07 12:00:31,775 - INFO - 成功插入的数据ID: ['FINST-RTA66X610Q2WXYH1C9XWS8CD7RJ73LI1IPLBMAN']
2025-06-07 12:00:34,791 - INFO - 批量插入完成，共 1 条记录
2025-06-07 12:00:34,791 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-07 12:00:34,791 - INFO - 开始处理日期: 2025-06
2025-06-07 12:00:34,791 - INFO - Request Parameters - Page 1:
2025-06-07 12:00:34,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:34,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:35,338 - INFO - Response - Page 1:
2025-06-07 12:00:35,541 - INFO - 第 1 页获取到 100 条记录
2025-06-07 12:00:35,541 - INFO - Request Parameters - Page 2:
2025-06-07 12:00:35,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:35,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:36,041 - INFO - Response - Page 2:
2025-06-07 12:00:36,244 - INFO - 第 2 页获取到 100 条记录
2025-06-07 12:00:36,244 - INFO - Request Parameters - Page 3:
2025-06-07 12:00:36,244 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:36,244 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:36,900 - INFO - Response - Page 3:
2025-06-07 12:00:37,103 - INFO - 第 3 页获取到 100 条记录
2025-06-07 12:00:37,103 - INFO - Request Parameters - Page 4:
2025-06-07 12:00:37,103 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:37,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:37,634 - INFO - Response - Page 4:
2025-06-07 12:00:37,838 - INFO - 第 4 页获取到 100 条记录
2025-06-07 12:00:37,838 - INFO - Request Parameters - Page 5:
2025-06-07 12:00:37,838 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:37,838 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:38,400 - INFO - Response - Page 5:
2025-06-07 12:00:38,603 - INFO - 第 5 页获取到 100 条记录
2025-06-07 12:00:38,603 - INFO - Request Parameters - Page 6:
2025-06-07 12:00:38,603 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:38,603 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:39,087 - INFO - Response - Page 6:
2025-06-07 12:00:39,291 - INFO - 第 6 页获取到 100 条记录
2025-06-07 12:00:39,291 - INFO - Request Parameters - Page 7:
2025-06-07 12:00:39,291 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 12:00:39,291 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 12:00:39,634 - INFO - Response - Page 7:
2025-06-07 12:00:39,837 - INFO - 第 7 页获取到 19 条记录
2025-06-07 12:00:39,837 - INFO - 查询完成，共获取到 619 条记录
2025-06-07 12:00:39,837 - INFO - 获取到 619 条表单数据
2025-06-07 12:00:39,837 - INFO - 当前日期 2025-06 有 619 条MySQL数据需要处理
2025-06-07 12:00:39,837 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM7B
2025-06-07 12:00:40,306 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM7B
2025-06-07 12:00:40,306 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69081.0, 'new_value': 72694.0}, {'field': 'total_amount', 'old_value': 69081.0, 'new_value': 72694.0}, {'field': 'order_count', 'old_value': 964, 'new_value': 1030}]
2025-06-07 12:00:40,306 - INFO - 开始更新记录 - 表单实例ID: FINST-RN766181UFSV54GWDU2WV8KY883V3JW0X4DBMP21
2025-06-07 12:00:40,744 - INFO - 更新表单数据成功: FINST-RN766181UFSV54GWDU2WV8KY883V3JW0X4DBMP21
2025-06-07 12:00:40,744 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 152825.0, 'new_value': 178484.0}, {'field': 'total_amount', 'old_value': 152825.0, 'new_value': 178484.0}, {'field': 'order_count', 'old_value': 1155, 'new_value': 1354}]
2025-06-07 12:00:40,744 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM8B
2025-06-07 12:00:41,244 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM8B
2025-06-07 12:00:41,244 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33989.23, 'new_value': 37974.61}, {'field': 'total_amount', 'old_value': 33989.23, 'new_value': 37974.61}, {'field': 'order_count', 'old_value': 838, 'new_value': 961}]
2025-06-07 12:00:41,244 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMX4
2025-06-07 12:00:41,728 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMX4
2025-06-07 12:00:41,728 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 8295.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 8295.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 2}]
2025-06-07 12:00:41,728 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMQB1
2025-06-07 12:00:42,181 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMQB1
2025-06-07 12:00:42,181 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 107583.0, 'new_value': 116560.0}, {'field': 'offline_amount', 'old_value': 44955.0, 'new_value': 52145.0}, {'field': 'total_amount', 'old_value': 152538.0, 'new_value': 168705.0}, {'field': 'order_count', 'old_value': 179, 'new_value': 203}]
2025-06-07 12:00:42,181 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMY4
2025-06-07 12:00:42,619 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMY4
2025-06-07 12:00:42,619 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2398.0, 'new_value': 10898.0}, {'field': 'total_amount', 'old_value': 2398.0, 'new_value': 10898.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-06-07 12:00:42,619 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBMG
2025-06-07 12:00:43,103 - INFO - 更新表单数据成功: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBMG
2025-06-07 12:00:43,103 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2479.53, 'new_value': 8338.9}, {'field': 'total_amount', 'old_value': 15532.04, 'new_value': 21391.41}, {'field': 'order_count', 'old_value': 551, 'new_value': 738}]
2025-06-07 12:00:43,103 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMSB1
2025-06-07 12:00:43,587 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMSB1
2025-06-07 12:00:43,603 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2035.5, 'new_value': 6991.5}, {'field': 'total_amount', 'old_value': 2035.5, 'new_value': 6991.5}, {'field': 'order_count', 'old_value': 405, 'new_value': 1410}]
2025-06-07 12:00:43,603 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMVB1
2025-06-07 12:00:44,166 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMVB1
2025-06-07 12:00:44,166 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19706.8, 'new_value': 25894.8}, {'field': 'total_amount', 'old_value': 19706.8, 'new_value': 25894.8}, {'field': 'order_count', 'old_value': 90, 'new_value': 119}]
2025-06-07 12:00:44,166 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMWB1
2025-06-07 12:00:44,587 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMWB1
2025-06-07 12:00:44,587 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9522.0, 'new_value': 14639.0}, {'field': 'offline_amount', 'old_value': 25731.0, 'new_value': 42518.0}, {'field': 'total_amount', 'old_value': 35253.0, 'new_value': 57157.0}, {'field': 'order_count', 'old_value': 216, 'new_value': 374}]
2025-06-07 12:00:44,587 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM9B
2025-06-07 12:00:45,087 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM9B
2025-06-07 12:00:45,087 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16498.3, 'new_value': 19927.6}, {'field': 'total_amount', 'old_value': 16498.3, 'new_value': 19927.6}, {'field': 'order_count', 'old_value': 123, 'new_value': 154}]
2025-06-07 12:00:45,087 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMXB1
2025-06-07 12:00:45,603 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMXB1
2025-06-07 12:00:45,603 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3649.82, 'new_value': 6736.15}, {'field': 'offline_amount', 'old_value': 9578.0, 'new_value': 21608.8}, {'field': 'total_amount', 'old_value': 13227.82, 'new_value': 28344.95}, {'field': 'order_count', 'old_value': 193, 'new_value': 398}]
2025-06-07 12:00:45,603 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMW5
2025-06-07 12:00:46,009 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMW5
2025-06-07 12:00:46,009 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1.0}, {'field': 'total_amount', 'old_value': 212.0, 'new_value': 213.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-06-07 12:00:46,009 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM0C1
2025-06-07 12:00:46,400 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM0C1
2025-06-07 12:00:46,400 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42210.13, 'new_value': 50714.41}, {'field': 'total_amount', 'old_value': 42210.13, 'new_value': 50714.41}, {'field': 'order_count', 'old_value': 238, 'new_value': 292}]
2025-06-07 12:00:46,400 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM2C1
2025-06-07 12:00:46,837 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM2C1
2025-06-07 12:00:46,837 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 132266.73, 'new_value': 161199.23}, {'field': 'total_amount', 'old_value': 132266.73, 'new_value': 161199.23}, {'field': 'order_count', 'old_value': 1001, 'new_value': 1216}]
2025-06-07 12:00:46,837 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM3C1
2025-06-07 12:00:47,322 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM3C1
2025-06-07 12:00:47,322 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 93548.9, 'new_value': 141697.5}, {'field': 'total_amount', 'old_value': 93548.9, 'new_value': 141697.5}, {'field': 'order_count', 'old_value': 16, 'new_value': 24}]
2025-06-07 12:00:47,322 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBMK
2025-06-07 12:00:47,822 - INFO - 更新表单数据成功: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBMK
2025-06-07 12:00:47,822 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20864.43, 'new_value': 36447.44}, {'field': 'offline_amount', 'old_value': 6443.15, 'new_value': 7744.15}, {'field': 'total_amount', 'old_value': 27307.58, 'new_value': 44191.59}, {'field': 'order_count', 'old_value': 868, 'new_value': 1390}]
2025-06-07 12:00:47,822 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM25
2025-06-07 12:00:48,322 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM25
2025-06-07 12:00:48,322 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 601.78, 'new_value': 783.07}, {'field': 'offline_amount', 'old_value': 25156.17, 'new_value': 28863.89}, {'field': 'total_amount', 'old_value': 25757.95, 'new_value': 29646.96}, {'field': 'order_count', 'old_value': 506, 'new_value': 606}]
2025-06-07 12:00:48,322 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM35
2025-06-07 12:00:48,791 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM35
2025-06-07 12:00:48,791 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26955.4, 'new_value': 30967.5}, {'field': 'offline_amount', 'old_value': 216867.17, 'new_value': 260630.94}, {'field': 'total_amount', 'old_value': 243822.57, 'new_value': 291598.44}, {'field': 'order_count', 'old_value': 2137, 'new_value': 2573}]
2025-06-07 12:00:48,791 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM8C1
2025-06-07 12:00:49,291 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM8C1
2025-06-07 12:00:49,291 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8452.9, 'new_value': 14418.15}, {'field': 'total_amount', 'old_value': 8452.9, 'new_value': 14418.15}, {'field': 'order_count', 'old_value': 416, 'new_value': 750}]
2025-06-07 12:00:49,291 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMAC1
2025-06-07 12:00:49,712 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMAC1
2025-06-07 12:00:49,712 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9019.0, 'new_value': 18086.0}, {'field': 'total_amount', 'old_value': 9019.0, 'new_value': 18086.0}, {'field': 'order_count', 'old_value': 536, 'new_value': 1062}]
2025-06-07 12:00:49,712 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM65
2025-06-07 12:00:50,212 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBM65
2025-06-07 12:00:50,212 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1050.08, 'new_value': 1332.51}, {'field': 'offline_amount', 'old_value': 36851.77, 'new_value': 43798.94}, {'field': 'total_amount', 'old_value': 37901.85, 'new_value': 45131.45}, {'field': 'order_count', 'old_value': 271, 'new_value': 308}]
2025-06-07 12:00:50,212 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMGC1
2025-06-07 12:00:50,728 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMGC1
2025-06-07 12:00:50,728 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 119292.15, 'new_value': 147511.71}, {'field': 'total_amount', 'old_value': 119292.15, 'new_value': 147511.71}, {'field': 'order_count', 'old_value': 909, 'new_value': 1102}]
2025-06-07 12:00:50,728 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMHC1
2025-06-07 12:00:51,181 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMHC1
2025-06-07 12:00:51,181 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-06-07 12:00:51,181 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMIC1
2025-06-07 12:00:51,650 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMIC1
2025-06-07 12:00:51,650 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17963.88, 'new_value': 20141.42}, {'field': 'offline_amount', 'old_value': 107641.52, 'new_value': 143380.94}, {'field': 'total_amount', 'old_value': 125605.4, 'new_value': 163522.36}, {'field': 'order_count', 'old_value': 325, 'new_value': 386}]
2025-06-07 12:00:51,650 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBMM
2025-06-07 12:00:52,103 - INFO - 更新表单数据成功: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBMM
2025-06-07 12:00:52,103 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18550.62, 'new_value': 31724.7}, {'field': 'offline_amount', 'old_value': 13254.03, 'new_value': 22573.3}, {'field': 'total_amount', 'old_value': 31804.65, 'new_value': 54298.0}, {'field': 'order_count', 'old_value': 1269, 'new_value': 2208}]
2025-06-07 12:00:52,103 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMKC1
2025-06-07 12:00:52,587 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMKC1
2025-06-07 12:00:52,587 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2001.6, 'new_value': 2966.3}, {'field': 'offline_amount', 'old_value': 7106.0, 'new_value': 7302.9}, {'field': 'total_amount', 'old_value': 9107.6, 'new_value': 10269.2}, {'field': 'order_count', 'old_value': 40, 'new_value': 46}]
2025-06-07 12:00:52,587 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMA5
2025-06-07 12:00:52,978 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMA5
2025-06-07 12:00:52,978 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 1.0}, {'field': 'offline_amount', 'old_value': 10729.75, 'new_value': 12502.6}, {'field': 'total_amount', 'old_value': 10729.75, 'new_value': 12503.6}, {'field': 'order_count', 'old_value': 37, 'new_value': 44}]
2025-06-07 12:00:52,978 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMLC1
2025-06-07 12:00:53,447 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMLC1
2025-06-07 12:00:53,447 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39580.5, 'new_value': 47669.9}, {'field': 'offline_amount', 'old_value': 21639.0, 'new_value': 27186.0}, {'field': 'total_amount', 'old_value': 61219.5, 'new_value': 74855.9}, {'field': 'order_count', 'old_value': 230, 'new_value': 296}]
2025-06-07 12:00:53,447 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMMC1
2025-06-07 12:00:53,931 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMMC1
2025-06-07 12:00:53,931 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1764.0, 'new_value': 2464.0}, {'field': 'offline_amount', 'old_value': 93565.0, 'new_value': 109565.0}, {'field': 'total_amount', 'old_value': 95329.0, 'new_value': 112029.0}, {'field': 'order_count', 'old_value': 832, 'new_value': 980}]
2025-06-07 12:00:53,931 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBMP
2025-06-07 12:00:54,494 - INFO - 更新表单数据成功: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBMP
2025-06-07 12:00:54,494 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90356.14, 'new_value': 126949.43}, {'field': 'total_amount', 'old_value': 90356.14, 'new_value': 126949.43}, {'field': 'order_count', 'old_value': 964, 'new_value': 1351}]
2025-06-07 12:00:54,494 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMK5
2025-06-07 12:00:55,025 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMK5
2025-06-07 12:00:55,025 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5134.0, 'new_value': 6460.0}, {'field': 'total_amount', 'old_value': 5194.0, 'new_value': 6520.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 21}]
2025-06-07 12:00:55,025 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMQC1
2025-06-07 12:00:55,541 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMQC1
2025-06-07 12:00:55,541 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3791.05, 'new_value': 4502.76}, {'field': 'offline_amount', 'old_value': 54201.33, 'new_value': 66820.63}, {'field': 'total_amount', 'old_value': 57992.38, 'new_value': 71323.39}, {'field': 'order_count', 'old_value': 379, 'new_value': 468}]
2025-06-07 12:00:55,541 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMQ5
2025-06-07 12:00:56,041 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMQ5
2025-06-07 12:00:56,041 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1883.13, 'new_value': 2531.02}, {'field': 'offline_amount', 'old_value': 65366.29, 'new_value': 75690.49}, {'field': 'total_amount', 'old_value': 67249.42, 'new_value': 78221.51}, {'field': 'order_count', 'old_value': 3033, 'new_value': 3738}]
2025-06-07 12:00:56,041 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMZC1
2025-06-07 12:00:56,494 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMZC1
2025-06-07 12:00:56,494 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7598.0, 'new_value': 12728.0}, {'field': 'total_amount', 'old_value': 7598.0, 'new_value': 12728.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 37}]
2025-06-07 12:00:56,494 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMS5
2025-06-07 12:00:57,009 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMS5
2025-06-07 12:00:57,009 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14971.5, 'new_value': 17642.5}, {'field': 'total_amount', 'old_value': 14971.5, 'new_value': 17642.5}, {'field': 'order_count', 'old_value': 38, 'new_value': 46}]
2025-06-07 12:00:57,009 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMU5
2025-06-07 12:00:57,494 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2PMPKUDBMU5
2025-06-07 12:00:57,494 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10605.0, 'new_value': 11722.35}, {'field': 'offline_amount', 'old_value': 6443.0, 'new_value': 7931.0}, {'field': 'total_amount', 'old_value': 17048.0, 'new_value': 19653.35}, {'field': 'order_count', 'old_value': 230, 'new_value': 265}]
2025-06-07 12:00:57,494 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBMQ
2025-06-07 12:00:57,962 - INFO - 更新表单数据成功: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBMQ
2025-06-07 12:00:57,962 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3613.94, 'new_value': 6052.03}, {'field': 'offline_amount', 'old_value': 5807.6, 'new_value': 9908.56}, {'field': 'total_amount', 'old_value': 9421.54, 'new_value': 15960.59}, {'field': 'order_count', 'old_value': 95, 'new_value': 158}]
2025-06-07 12:00:57,962 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMY5
2025-06-07 12:00:58,462 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMY5
2025-06-07 12:00:58,462 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22807.51, 'new_value': 26909.14}, {'field': 'offline_amount', 'old_value': 12070.01, 'new_value': 13926.1}, {'field': 'total_amount', 'old_value': 34877.52, 'new_value': 40835.24}, {'field': 'order_count', 'old_value': 2065, 'new_value': 2430}]
2025-06-07 12:00:58,462 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBMR
2025-06-07 12:00:58,900 - INFO - 更新表单数据成功: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBMR
2025-06-07 12:00:58,900 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12604.61, 'new_value': 20135.28}, {'field': 'offline_amount', 'old_value': 27644.66, 'new_value': 43243.04}, {'field': 'total_amount', 'old_value': 40249.27, 'new_value': 63378.32}, {'field': 'order_count', 'old_value': 936, 'new_value': 1569}]
2025-06-07 12:00:58,900 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM26
2025-06-07 12:00:59,369 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM26
2025-06-07 12:00:59,369 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8022.77, 'new_value': 9749.24}, {'field': 'offline_amount', 'old_value': 5291.0, 'new_value': 6187.26}, {'field': 'total_amount', 'old_value': 13313.77, 'new_value': 15936.5}, {'field': 'order_count', 'old_value': 524, 'new_value': 629}]
2025-06-07 12:00:59,369 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM46
2025-06-07 12:00:59,884 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM46
2025-06-07 12:00:59,884 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 174472.0, 'new_value': 192052.92}, {'field': 'total_amount', 'old_value': 174472.0, 'new_value': 192052.92}, {'field': 'order_count', 'old_value': 2966, 'new_value': 3078}]
2025-06-07 12:00:59,884 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM7D1
2025-06-07 12:01:00,306 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM7D1
2025-06-07 12:01:00,322 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3736.15, 'new_value': 7207.5}, {'field': 'offline_amount', 'old_value': 2346.68, 'new_value': 5092.62}, {'field': 'total_amount', 'old_value': 6082.83, 'new_value': 12300.12}, {'field': 'order_count', 'old_value': 373, 'new_value': 737}]
2025-06-07 12:01:00,322 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMH7
2025-06-07 12:01:00,759 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMH7
2025-06-07 12:01:00,759 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15265.0, 'new_value': 16810.0}, {'field': 'total_amount', 'old_value': 15265.0, 'new_value': 16810.0}, {'field': 'order_count', 'old_value': 85, 'new_value': 93}]
2025-06-07 12:01:00,759 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM8D1
2025-06-07 12:01:01,228 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM8D1
2025-06-07 12:01:01,228 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 132477.0, 'new_value': 164023.0}, {'field': 'total_amount', 'old_value': 132477.0, 'new_value': 164023.0}, {'field': 'order_count', 'old_value': 432, 'new_value': 534}]
2025-06-07 12:01:01,228 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMJ7
2025-06-07 12:01:01,822 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMJ7
2025-06-07 12:01:01,822 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29228.3, 'new_value': 35399.1}, {'field': 'total_amount', 'old_value': 29228.3, 'new_value': 35399.1}, {'field': 'order_count', 'old_value': 389, 'new_value': 464}]
2025-06-07 12:01:01,822 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBMS
2025-06-07 12:01:02,259 - INFO - 更新表单数据成功: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBMS
2025-06-07 12:01:02,259 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7585.37, 'new_value': 11476.25}, {'field': 'offline_amount', 'old_value': 47952.01, 'new_value': 74360.81}, {'field': 'total_amount', 'old_value': 55537.38, 'new_value': 85837.06}, {'field': 'order_count', 'old_value': 1730, 'new_value': 2684}]
2025-06-07 12:01:02,259 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMM7
2025-06-07 12:01:02,759 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMM7
2025-06-07 12:01:02,759 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11392.5, 'new_value': 13498.4}, {'field': 'offline_amount', 'old_value': 72280.75, 'new_value': 86932.92}, {'field': 'total_amount', 'old_value': 83673.25, 'new_value': 100431.32}, {'field': 'order_count', 'old_value': 499, 'new_value': 609}]
2025-06-07 12:01:02,759 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMAD1
2025-06-07 12:01:03,212 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMAD1
2025-06-07 12:01:03,212 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 405.5, 'new_value': 1081.22}, {'field': 'offline_amount', 'old_value': 20026.52, 'new_value': 38164.82}, {'field': 'total_amount', 'old_value': 20432.02, 'new_value': 39246.04}, {'field': 'order_count', 'old_value': 289, 'new_value': 563}]
2025-06-07 12:01:03,212 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMQ7
2025-06-07 12:01:03,744 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMQ7
2025-06-07 12:01:03,744 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4862.0, 'new_value': 5885.0}, {'field': 'total_amount', 'old_value': 4862.0, 'new_value': 5885.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 40}]
2025-06-07 12:01:03,744 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMCD1
2025-06-07 12:01:04,228 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMCD1
2025-06-07 12:01:04,228 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14210.7, 'new_value': 14930.3}, {'field': 'total_amount', 'old_value': 14477.1, 'new_value': 15196.7}, {'field': 'order_count', 'old_value': 48, 'new_value': 56}]
2025-06-07 12:01:04,228 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMS7
2025-06-07 12:01:04,759 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMS7
2025-06-07 12:01:04,759 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 254.16}, {'field': 'offline_amount', 'old_value': 99498.5, 'new_value': 124450.2}, {'field': 'total_amount', 'old_value': 99498.5, 'new_value': 124704.36}, {'field': 'order_count', 'old_value': 250, 'new_value': 316}]
2025-06-07 12:01:04,759 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMH6
2025-06-07 12:01:05,181 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMH6
2025-06-07 12:01:05,181 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13996.84, 'new_value': 19555.95}, {'field': 'offline_amount', 'old_value': 59197.61, 'new_value': 71530.45}, {'field': 'total_amount', 'old_value': 73194.45, 'new_value': 91086.4}, {'field': 'order_count', 'old_value': 746, 'new_value': 1300}]
2025-06-07 12:01:05,181 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMFD1
2025-06-07 12:01:05,650 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMFD1
2025-06-07 12:01:05,650 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8467.9, 'new_value': 13017.1}, {'field': 'total_amount', 'old_value': 8467.9, 'new_value': 13017.1}, {'field': 'order_count', 'old_value': 38, 'new_value': 72}]
2025-06-07 12:01:05,650 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMK6
2025-06-07 12:01:06,087 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMK6
2025-06-07 12:01:06,087 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59589.0, 'new_value': 63661.0}, {'field': 'total_amount', 'old_value': 59589.0, 'new_value': 63661.0}, {'field': 'order_count', 'old_value': 55, 'new_value': 64}]
2025-06-07 12:01:06,087 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBML6
2025-06-07 12:01:06,572 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBML6
2025-06-07 12:01:06,572 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4290.0, 'new_value': 4665.0}, {'field': 'total_amount', 'old_value': 4290.0, 'new_value': 4665.0}, {'field': 'order_count', 'old_value': 59, 'new_value': 61}]
2025-06-07 12:01:06,587 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMID1
2025-06-07 12:01:07,009 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMID1
2025-06-07 12:01:07,009 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15286.0, 'new_value': 20286.0}, {'field': 'offline_amount', 'old_value': 33750.0, 'new_value': 36918.0}, {'field': 'total_amount', 'old_value': 49036.0, 'new_value': 57204.0}, {'field': 'order_count', 'old_value': 1105, 'new_value': 1293}]
2025-06-07 12:01:07,009 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMN6
2025-06-07 12:01:07,525 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMN6
2025-06-07 12:01:07,525 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13084.87, 'new_value': 15175.78}, {'field': 'offline_amount', 'old_value': 49218.05, 'new_value': 57169.33}, {'field': 'total_amount', 'old_value': 62302.92, 'new_value': 72345.11}, {'field': 'order_count', 'old_value': 1303, 'new_value': 1549}]
2025-06-07 12:01:07,525 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMZ7
2025-06-07 12:01:08,009 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMZ7
2025-06-07 12:01:08,009 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39746.0, 'new_value': 49490.0}, {'field': 'total_amount', 'old_value': 39746.0, 'new_value': 49490.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 7}]
2025-06-07 12:01:08,009 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMO6
2025-06-07 12:01:08,447 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMO6
2025-06-07 12:01:08,447 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 738.74, 'new_value': 989.74}, {'field': 'offline_amount', 'old_value': 80305.99, 'new_value': 93366.09}, {'field': 'total_amount', 'old_value': 81044.73, 'new_value': 94355.83}, {'field': 'order_count', 'old_value': 3827, 'new_value': 4513}]
2025-06-07 12:01:08,447 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMKD1
2025-06-07 12:01:08,962 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMKD1
2025-06-07 12:01:08,962 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6184.0, 'new_value': 7640.0}, {'field': 'total_amount', 'old_value': 6184.0, 'new_value': 7640.0}, {'field': 'order_count', 'old_value': 55, 'new_value': 70}]
2025-06-07 12:01:08,962 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBMT
2025-06-07 12:01:09,619 - INFO - 更新表单数据成功: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBMT
2025-06-07 12:01:09,619 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13708.13, 'new_value': 20787.65}, {'field': 'total_amount', 'old_value': 13708.13, 'new_value': 20787.65}, {'field': 'order_count', 'old_value': 391, 'new_value': 595}]
2025-06-07 12:01:09,619 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMLD1
2025-06-07 12:01:10,165 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMLD1
2025-06-07 12:01:10,165 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 140487.24, 'new_value': 161546.14}, {'field': 'total_amount', 'old_value': 140487.24, 'new_value': 161546.14}, {'field': 'order_count', 'old_value': 1488, 'new_value': 1742}]
2025-06-07 12:01:10,165 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMXB
2025-06-07 12:01:10,697 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMXB
2025-06-07 12:01:10,697 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5533.17, 'new_value': 6522.88}, {'field': 'offline_amount', 'old_value': 49980.2, 'new_value': 55073.0}, {'field': 'total_amount', 'old_value': 55513.37, 'new_value': 61595.88}, {'field': 'order_count', 'old_value': 1806, 'new_value': 2024}]
2025-06-07 12:01:10,697 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMT6
2025-06-07 12:01:11,103 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMT6
2025-06-07 12:01:11,103 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23310.99, 'new_value': 27765.44}, {'field': 'offline_amount', 'old_value': 52362.74, 'new_value': 59889.12}, {'field': 'total_amount', 'old_value': 75673.73, 'new_value': 87654.56}, {'field': 'order_count', 'old_value': 2553, 'new_value': 3028}]
2025-06-07 12:01:11,103 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMPD1
2025-06-07 12:01:11,603 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMPD1
2025-06-07 12:01:11,603 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32499.88, 'new_value': 40063.21}, {'field': 'offline_amount', 'old_value': 87006.78, 'new_value': 106301.78}, {'field': 'total_amount', 'old_value': 119506.66, 'new_value': 146364.99}, {'field': 'order_count', 'old_value': 947, 'new_value': 1213}]
2025-06-07 12:01:11,603 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMU6
2025-06-07 12:01:12,009 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMU6
2025-06-07 12:01:12,009 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33298.73, 'new_value': 38630.39}, {'field': 'offline_amount', 'old_value': 36000.0, 'new_value': 46000.0}, {'field': 'total_amount', 'old_value': 69298.73, 'new_value': 84630.39}, {'field': 'order_count', 'old_value': 212, 'new_value': 266}]
2025-06-07 12:01:12,009 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMW6
2025-06-07 12:01:12,509 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMW6
2025-06-07 12:01:12,509 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 156281.35, 'new_value': 187677.26}, {'field': 'total_amount', 'old_value': 156281.35, 'new_value': 187677.26}, {'field': 'order_count', 'old_value': 854, 'new_value': 1089}]
2025-06-07 12:01:12,509 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMTD1
2025-06-07 12:01:12,994 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMTD1
2025-06-07 12:01:12,994 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 163.0, 'new_value': 2865.9}, {'field': 'total_amount', 'old_value': 715.0, 'new_value': 3417.9}, {'field': 'order_count', 'old_value': 8, 'new_value': 16}]
2025-06-07 12:01:12,994 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM78
2025-06-07 12:01:13,431 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM78
2025-06-07 12:01:13,431 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6281.9, 'new_value': 8196.4}, {'field': 'offline_amount', 'old_value': 258823.32, 'new_value': 290554.6}, {'field': 'total_amount', 'old_value': 265105.22, 'new_value': 298751.0}, {'field': 'order_count', 'old_value': 1030, 'new_value': 1236}]
2025-06-07 12:01:13,431 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMZ6
2025-06-07 12:01:13,931 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMZ6
2025-06-07 12:01:13,931 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34679.66, 'new_value': 39054.1}, {'field': 'total_amount', 'old_value': 34679.66, 'new_value': 39054.1}, {'field': 'order_count', 'old_value': 1555, 'new_value': 1770}]
2025-06-07 12:01:13,931 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMWD1
2025-06-07 12:01:14,353 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMWD1
2025-06-07 12:01:14,353 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 487.5, 'new_value': 786.7}, {'field': 'offline_amount', 'old_value': 9308.1, 'new_value': 20828.1}, {'field': 'total_amount', 'old_value': 9795.6, 'new_value': 21614.8}, {'field': 'order_count', 'old_value': 71, 'new_value': 138}]
2025-06-07 12:01:14,353 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM88
2025-06-07 12:01:14,837 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM88
2025-06-07 12:01:14,837 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27614.0, 'new_value': 30193.0}, {'field': 'total_amount', 'old_value': 27614.0, 'new_value': 30193.0}, {'field': 'order_count', 'old_value': 832, 'new_value': 910}]
2025-06-07 12:01:14,837 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMYD1
2025-06-07 12:01:15,322 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMYD1
2025-06-07 12:01:15,322 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4322.0, 'new_value': 5634.0}, {'field': 'total_amount', 'old_value': 4322.0, 'new_value': 5634.0}, {'field': 'order_count', 'old_value': 37, 'new_value': 50}]
2025-06-07 12:01:15,322 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBMU
2025-06-07 12:01:15,822 - INFO - 更新表单数据成功: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBMU
2025-06-07 12:01:15,822 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32303.0, 'new_value': 44253.18}, {'field': 'total_amount', 'old_value': 32303.0, 'new_value': 44253.18}, {'field': 'order_count', 'old_value': 710, 'new_value': 908}]
2025-06-07 12:01:15,822 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMBQ1
2025-06-07 12:01:16,228 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMBQ1
2025-06-07 12:01:16,228 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13673.0, 'new_value': 18041.0}, {'field': 'total_amount', 'old_value': 13673.0, 'new_value': 18041.0}, {'field': 'order_count', 'old_value': 122, 'new_value': 157}]
2025-06-07 12:01:16,228 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMZB
2025-06-07 12:01:16,712 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBMZB
2025-06-07 12:01:16,712 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79979.55, 'new_value': 87113.33}, {'field': 'total_amount', 'old_value': 79979.55, 'new_value': 87113.33}, {'field': 'order_count', 'old_value': 1530, 'new_value': 1660}]
2025-06-07 12:01:16,712 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMDQ1
2025-06-07 12:01:17,181 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMDQ1
2025-06-07 12:01:17,181 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7928.0, 'new_value': 8627.0}, {'field': 'total_amount', 'old_value': 7928.0, 'new_value': 8627.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 15}]
2025-06-07 12:01:17,181 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM37
2025-06-07 12:01:17,618 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM37
2025-06-07 12:01:17,618 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 550000.0, 'new_value': 600000.0}, {'field': 'total_amount', 'old_value': 550000.0, 'new_value': 600000.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-06-07 12:01:17,618 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM87
2025-06-07 12:01:18,009 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM87
2025-06-07 12:01:18,009 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6481.0, 'new_value': 7159.0}, {'field': 'total_amount', 'old_value': 6481.0, 'new_value': 7159.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 12}]
2025-06-07 12:01:18,009 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM97
2025-06-07 12:01:18,431 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBM97
2025-06-07 12:01:18,431 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49596.1, 'new_value': 55527.3}, {'field': 'total_amount', 'old_value': 49596.1, 'new_value': 55527.3}, {'field': 'order_count', 'old_value': 72, 'new_value': 77}]
2025-06-07 12:01:18,431 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM0C
2025-06-07 12:01:18,884 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM0C
2025-06-07 12:01:18,884 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9468.05, 'new_value': 11644.07}, {'field': 'offline_amount', 'old_value': 85830.05, 'new_value': 98618.17}, {'field': 'total_amount', 'old_value': 95298.1, 'new_value': 110262.24}, {'field': 'order_count', 'old_value': 755, 'new_value': 890}]
2025-06-07 12:01:18,884 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM7E1
2025-06-07 12:01:19,368 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM7E1
2025-06-07 12:01:19,368 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33037.53, 'new_value': 38945.93}, {'field': 'offline_amount', 'old_value': 60175.3, 'new_value': 73137.06}, {'field': 'total_amount', 'old_value': 93212.83, 'new_value': 112082.99}, {'field': 'order_count', 'old_value': 784, 'new_value': 937}]
2025-06-07 12:01:19,368 - INFO - 开始更新记录 - 表单实例ID: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMB7
2025-06-07 12:01:19,775 - INFO - 更新表单数据成功: FINST-ACB660710VWVQVI8AJZEV6YWQWFS2QMPKUDBMB7
2025-06-07 12:01:19,775 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46908.0, 'new_value': 50805.0}, {'field': 'total_amount', 'old_value': 60903.0, 'new_value': 64800.0}]
2025-06-07 12:01:19,775 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM8E1
2025-06-07 12:01:20,306 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM8E1
2025-06-07 12:01:20,306 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 35, 'new_value': 61}]
2025-06-07 12:01:20,306 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM9E1
2025-06-07 12:01:20,900 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBM9E1
2025-06-07 12:01:20,900 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2800000.0, 'new_value': 3200000.0}, {'field': 'total_amount', 'old_value': 2800000.0, 'new_value': 3200000.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 17}]
2025-06-07 12:01:20,900 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBMW
2025-06-07 12:01:21,384 - INFO - 更新表单数据成功: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBMW
2025-06-07 12:01:21,384 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30289.53, 'new_value': 49940.07}, {'field': 'total_amount', 'old_value': 30289.53, 'new_value': 49940.07}, {'field': 'order_count', 'old_value': 1263, 'new_value': 2201}]
2025-06-07 12:01:21,384 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM2C
2025-06-07 12:01:21,868 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM2C
2025-06-07 12:01:21,868 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32437.0, 'new_value': 37751.0}, {'field': 'total_amount', 'old_value': 32437.0, 'new_value': 37751.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 58}]
2025-06-07 12:01:21,868 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM4C
2025-06-07 12:01:22,322 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM4C
2025-06-07 12:01:22,322 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 703.0, 'new_value': 810.0}, {'field': 'offline_amount', 'old_value': 7937.6, 'new_value': 8748.6}, {'field': 'total_amount', 'old_value': 8640.6, 'new_value': 9558.6}, {'field': 'order_count', 'old_value': 296, 'new_value': 328}]
2025-06-07 12:01:22,322 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMWP
2025-06-07 12:01:22,837 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMWP
2025-06-07 12:01:22,837 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3418.0, 'new_value': 4126.21}, {'field': 'offline_amount', 'old_value': 52392.0, 'new_value': 65154.8}, {'field': 'total_amount', 'old_value': 55810.0, 'new_value': 69281.01}, {'field': 'order_count', 'old_value': 291, 'new_value': 352}]
2025-06-07 12:01:22,837 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMDE1
2025-06-07 12:01:23,306 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMDE1
2025-06-07 12:01:23,306 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7801.0, 'new_value': 8786.0}, {'field': 'total_amount', 'old_value': 7801.0, 'new_value': 8786.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 23}]
2025-06-07 12:01:23,306 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBMY
2025-06-07 12:01:23,853 - INFO - 更新表单数据成功: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBMY
2025-06-07 12:01:23,853 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35913.8, 'new_value': 42029.5}, {'field': 'total_amount', 'old_value': 35913.8, 'new_value': 42029.5}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-06-07 12:01:23,853 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM5C
2025-06-07 12:01:24,368 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM5C
2025-06-07 12:01:24,368 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22155.28, 'new_value': 27514.57}, {'field': 'offline_amount', 'old_value': 44626.26, 'new_value': 50970.19}, {'field': 'total_amount', 'old_value': 66781.54, 'new_value': 78484.76}, {'field': 'order_count', 'old_value': 2098, 'new_value': 2509}]
2025-06-07 12:01:24,368 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMRQ1
2025-06-07 12:01:24,790 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMRQ1
2025-06-07 12:01:24,790 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12484.15, 'new_value': 15487.92}, {'field': 'total_amount', 'old_value': 12484.15, 'new_value': 15487.92}, {'field': 'order_count', 'old_value': 268, 'new_value': 359}]
2025-06-07 12:01:24,790 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM7C
2025-06-07 12:01:25,290 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM7C
2025-06-07 12:01:25,290 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25289.08, 'new_value': 33770.39}, {'field': 'total_amount', 'old_value': 34779.64, 'new_value': 43260.95}, {'field': 'order_count', 'old_value': 1980, 'new_value': 2411}]
2025-06-07 12:01:25,290 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMVQ1
2025-06-07 12:01:25,743 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMVQ1
2025-06-07 12:01:25,743 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6318.45, 'new_value': 8715.05}, {'field': 'total_amount', 'old_value': 6318.45, 'new_value': 8715.05}, {'field': 'order_count', 'old_value': 136, 'new_value': 177}]
2025-06-07 12:01:25,743 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMFE1
2025-06-07 12:01:26,290 - INFO - 更新表单数据成功: FINST-PAB66N71T5SVM8U67NKHS71U5ZGF2GGRAKEBMFE1
2025-06-07 12:01:26,290 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23475.0, 'new_value': 27783.0}, {'field': 'total_amount', 'old_value': 23643.0, 'new_value': 27951.0}, {'field': 'order_count', 'old_value': 72, 'new_value': 89}]
2025-06-07 12:01:26,290 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMZQ1
2025-06-07 12:01:26,759 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMZQ1
2025-06-07 12:01:26,759 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3808.0, 'new_value': 4632.0}, {'field': 'offline_amount', 'old_value': 6816.0, 'new_value': 8094.0}, {'field': 'total_amount', 'old_value': 10624.0, 'new_value': 12726.0}, {'field': 'order_count', 'old_value': 76, 'new_value': 93}]
2025-06-07 12:01:26,759 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM8C
2025-06-07 12:01:27,165 - INFO - 更新表单数据成功: FINST-NU966I81P8UV7RHPAKRNK5N8N9ZL39DLF7EBM8C
2025-06-07 12:01:27,165 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28390.25, 'new_value': 34980.43}, {'field': 'offline_amount', 'old_value': 10089.61, 'new_value': 12460.96}, {'field': 'total_amount', 'old_value': 38479.86, 'new_value': 47441.39}, {'field': 'order_count', 'old_value': 2170, 'new_value': 2632}]
2025-06-07 12:01:27,165 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM4O
2025-06-07 12:01:27,681 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM4O
2025-06-07 12:01:27,681 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66615.0, 'new_value': 79156.0}, {'field': 'total_amount', 'old_value': 66615.0, 'new_value': 79156.0}, {'field': 'order_count', 'old_value': 82, 'new_value': 97}]
2025-06-07 12:01:27,681 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM2R1
2025-06-07 12:01:28,118 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBM2R1
2025-06-07 12:01:28,118 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 118341.0, 'new_value': 130949.2}, {'field': 'total_amount', 'old_value': 118341.0, 'new_value': 130949.2}, {'field': 'order_count', 'old_value': 2535, 'new_value': 2801}]
2025-06-07 12:01:28,118 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM5O
2025-06-07 12:01:28,556 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM5O
2025-06-07 12:01:28,556 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21127.98, 'new_value': 23934.64}, {'field': 'total_amount', 'old_value': 21127.98, 'new_value': 23934.64}, {'field': 'order_count', 'old_value': 1225, 'new_value': 1429}]
2025-06-07 12:01:28,556 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM6O
2025-06-07 12:01:28,993 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM6O
2025-06-07 12:01:28,993 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8985.0, 'new_value': 20368.0}, {'field': 'total_amount', 'old_value': 8985.0, 'new_value': 20368.0}, {'field': 'order_count', 'old_value': 1247, 'new_value': 2979}]
2025-06-07 12:01:28,993 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM7O
2025-06-07 12:01:29,478 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM7O
2025-06-07 12:01:29,478 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5990.0, 'new_value': 13577.0}, {'field': 'total_amount', 'old_value': 5990.0, 'new_value': 13577.0}, {'field': 'order_count', 'old_value': 1247, 'new_value': 2979}]
2025-06-07 12:01:29,478 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM8O
2025-06-07 12:01:29,915 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM8O
2025-06-07 12:01:29,915 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4617.11, 'new_value': 5703.36}, {'field': 'offline_amount', 'old_value': 2807.0, 'new_value': 3565.0}, {'field': 'total_amount', 'old_value': 7424.11, 'new_value': 9268.36}, {'field': 'order_count', 'old_value': 267, 'new_value': 342}]
2025-06-07 12:01:29,915 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMBR1
2025-06-07 12:01:30,368 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMBR1
2025-06-07 12:01:30,368 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37924.0, 'new_value': 48584.0}, {'field': 'total_amount', 'old_value': 37924.0, 'new_value': 48584.0}, {'field': 'order_count', 'old_value': 4001, 'new_value': 5175}]
2025-06-07 12:01:30,368 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM9O
2025-06-07 12:01:30,853 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM9O
2025-06-07 12:01:30,853 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2010.93, 'new_value': 3422.1}, {'field': 'offline_amount', 'old_value': 6704.59, 'new_value': 15355.49}, {'field': 'total_amount', 'old_value': 8715.52, 'new_value': 18777.59}, {'field': 'order_count', 'old_value': 178, 'new_value': 422}]
2025-06-07 12:01:30,853 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMFR1
2025-06-07 12:01:31,306 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMFR1
2025-06-07 12:01:31,306 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59570.0, 'new_value': 72990.0}, {'field': 'total_amount', 'old_value': 59570.0, 'new_value': 72990.0}, {'field': 'order_count', 'old_value': 1373, 'new_value': 1678}]
2025-06-07 12:01:31,306 - INFO - 开始更新记录 - 表单实例ID: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMGR1
2025-06-07 12:01:31,884 - INFO - 更新表单数据成功: FINST-OJ966381NIRVIE737MQ1X51T1JCP2FA4VDEBMGR1
2025-06-07 12:01:31,884 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2839.0, 'new_value': 4796.0}, {'field': 'total_amount', 'old_value': 3877.0, 'new_value': 5834.0}, {'field': 'order_count', 'old_value': 43, 'new_value': 56}]
2025-06-07 12:01:31,884 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMDO
2025-06-07 12:01:32,290 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMDO
2025-06-07 12:01:32,290 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 159883.93, 'new_value': 187297.65}, {'field': 'total_amount', 'old_value': 159883.93, 'new_value': 187297.65}, {'field': 'order_count', 'old_value': 1200, 'new_value': 1424}]
2025-06-07 12:01:32,290 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBM21
2025-06-07 12:01:32,712 - INFO - 更新表单数据成功: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBM21
2025-06-07 12:01:32,712 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15920.0, 'new_value': 28648.0}, {'field': 'total_amount', 'old_value': 15920.0, 'new_value': 28648.0}, {'field': 'order_count', 'old_value': 413, 'new_value': 799}]
2025-06-07 12:01:32,712 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMGO
2025-06-07 12:01:33,150 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMGO
2025-06-07 12:01:33,150 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 137434.53, 'new_value': 168939.03}, {'field': 'total_amount', 'old_value': 137434.53, 'new_value': 168939.03}, {'field': 'order_count', 'old_value': 844, 'new_value': 1046}]
2025-06-07 12:01:33,150 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMJO
2025-06-07 12:01:33,634 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMJO
2025-06-07 12:01:33,634 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 114646.22, 'new_value': 128590.3}, {'field': 'total_amount', 'old_value': 118344.22, 'new_value': 132288.3}, {'field': 'order_count', 'old_value': 139, 'new_value': 164}]
2025-06-07 12:01:33,634 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMMO
2025-06-07 12:01:34,072 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMMO
2025-06-07 12:01:34,072 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 979528.18, 'new_value': 1177495.18}, {'field': 'total_amount', 'old_value': 979528.18, 'new_value': 1177495.18}, {'field': 'order_count', 'old_value': 21602, 'new_value': 25492}]
2025-06-07 12:01:34,072 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMNO
2025-06-07 12:01:34,540 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMNO
2025-06-07 12:01:34,540 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 196409.43, 'new_value': 243823.23}, {'field': 'total_amount', 'old_value': 196409.43, 'new_value': 243823.23}, {'field': 'order_count', 'old_value': 620, 'new_value': 759}]
2025-06-07 12:01:34,540 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMOO
2025-06-07 12:01:35,009 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMOO
2025-06-07 12:01:35,009 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 147433.83, 'new_value': 180837.63}, {'field': 'total_amount', 'old_value': 147433.83, 'new_value': 180837.63}, {'field': 'order_count', 'old_value': 402, 'new_value': 463}]
2025-06-07 12:01:35,009 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMQO
2025-06-07 12:01:35,431 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMQO
2025-06-07 12:01:35,431 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1408.8, 'new_value': 2892.6}, {'field': 'total_amount', 'old_value': 1408.8, 'new_value': 2892.6}, {'field': 'order_count', 'old_value': 101, 'new_value': 236}]
2025-06-07 12:01:35,431 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMSO
2025-06-07 12:01:35,884 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMSO
2025-06-07 12:01:35,884 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7543.21, 'new_value': 12824.61}, {'field': 'total_amount', 'old_value': 19979.72, 'new_value': 25261.12}, {'field': 'order_count', 'old_value': 1323, 'new_value': 1665}]
2025-06-07 12:01:35,884 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMTO
2025-06-07 12:01:36,353 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMTO
2025-06-07 12:01:36,353 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13097.02, 'new_value': 21831.48}, {'field': 'total_amount', 'old_value': 35030.35, 'new_value': 43764.81}, {'field': 'order_count', 'old_value': 2296, 'new_value': 2877}]
2025-06-07 12:01:36,353 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMVO
2025-06-07 12:01:36,821 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMVO
2025-06-07 12:01:36,821 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 779588.0, 'new_value': 829588.0}, {'field': 'total_amount', 'old_value': 779588.0, 'new_value': 829588.0}, {'field': 'order_count', 'old_value': 1083, 'new_value': 1084}]
2025-06-07 12:01:36,821 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMYO
2025-06-07 12:01:37,243 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMYO
2025-06-07 12:01:37,243 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55000.0, 'new_value': 60000.0}, {'field': 'total_amount', 'old_value': 55000.0, 'new_value': 60000.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-06-07 12:01:37,243 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMZO
2025-06-07 12:01:37,681 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMZO
2025-06-07 12:01:37,681 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65000.0, 'new_value': 70000.0}, {'field': 'total_amount', 'old_value': 65000.0, 'new_value': 70000.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-06-07 12:01:37,681 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBM51
2025-06-07 12:01:38,118 - INFO - 更新表单数据成功: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBM51
2025-06-07 12:01:38,118 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8480.34, 'new_value': 10337.99}, {'field': 'total_amount', 'old_value': 8480.34, 'new_value': 10337.99}, {'field': 'order_count', 'old_value': 389, 'new_value': 439}]
2025-06-07 12:01:38,118 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM2P
2025-06-07 12:01:38,571 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM2P
2025-06-07 12:01:38,571 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 211454.59, 'new_value': 228609.64}, {'field': 'total_amount', 'old_value': 211454.59, 'new_value': 228609.64}, {'field': 'order_count', 'old_value': 1028, 'new_value': 1142}]
2025-06-07 12:01:38,571 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM6P
2025-06-07 12:01:39,103 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM6P
2025-06-07 12:01:39,103 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43672.78, 'new_value': 52911.77}, {'field': 'total_amount', 'old_value': 43672.78, 'new_value': 52911.77}, {'field': 'order_count', 'old_value': 1470, 'new_value': 1750}]
2025-06-07 12:01:39,103 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM8P
2025-06-07 12:01:39,540 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM8P
2025-06-07 12:01:39,540 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32702.36, 'new_value': 37564.16}, {'field': 'total_amount', 'old_value': 32702.36, 'new_value': 37564.16}, {'field': 'order_count', 'old_value': 2377, 'new_value': 2712}]
2025-06-07 12:01:39,540 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM9P
2025-06-07 12:01:39,993 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM9P
2025-06-07 12:01:39,993 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47100.0, 'new_value': 59400.0}, {'field': 'total_amount', 'old_value': 47100.0, 'new_value': 59400.0}, {'field': 'order_count', 'old_value': 112, 'new_value': 141}]
2025-06-07 12:01:39,993 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMCP
2025-06-07 12:01:40,618 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMCP
2025-06-07 12:01:40,618 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55000.0, 'new_value': 60000.0}, {'field': 'total_amount', 'old_value': 55000.0, 'new_value': 60000.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-06-07 12:01:40,618 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMDP
2025-06-07 12:01:41,056 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMDP
2025-06-07 12:01:41,056 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37529.67, 'new_value': 44676.55}, {'field': 'total_amount', 'old_value': 37529.67, 'new_value': 44676.55}, {'field': 'order_count', 'old_value': 666, 'new_value': 800}]
2025-06-07 12:01:41,056 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMEP
2025-06-07 12:01:41,556 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMEP
2025-06-07 12:01:41,556 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97000.0, 'new_value': 110948.0}, {'field': 'total_amount', 'old_value': 97000.0, 'new_value': 110948.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-06-07 12:01:41,556 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMHP
2025-06-07 12:01:41,978 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMHP
2025-06-07 12:01:41,978 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 143632.0, 'new_value': 154632.0}, {'field': 'total_amount', 'old_value': 143632.0, 'new_value': 154632.0}, {'field': 'order_count', 'old_value': 3274, 'new_value': 3550}]
2025-06-07 12:01:41,978 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMJP
2025-06-07 12:01:42,384 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMJP
2025-06-07 12:01:42,384 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27192.4, 'new_value': 31626.75}, {'field': 'total_amount', 'old_value': 27192.4, 'new_value': 31626.75}, {'field': 'order_count', 'old_value': 693, 'new_value': 812}]
2025-06-07 12:01:42,384 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMLP
2025-06-07 12:01:42,837 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMLP
2025-06-07 12:01:42,837 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21053.0, 'new_value': 23619.0}, {'field': 'total_amount', 'old_value': 21053.0, 'new_value': 23619.0}, {'field': 'order_count', 'old_value': 174, 'new_value': 204}]
2025-06-07 12:01:42,837 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMNP
2025-06-07 12:01:43,275 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMNP
2025-06-07 12:01:43,275 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2158.0, 'new_value': 7626.0}, {'field': 'total_amount', 'old_value': 2158.0, 'new_value': 7626.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-06-07 12:01:43,275 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMOP
2025-06-07 12:01:43,775 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMOP
2025-06-07 12:01:43,775 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6557.7, 'new_value': 11557.7}, {'field': 'offline_amount', 'old_value': 35108.74, 'new_value': 35699.74}, {'field': 'total_amount', 'old_value': 41666.44, 'new_value': 47257.44}, {'field': 'order_count', 'old_value': 973, 'new_value': 1128}]
2025-06-07 12:01:43,775 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMTP
2025-06-07 12:01:44,212 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMTP
2025-06-07 12:01:44,212 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2209.0, 'new_value': 3673.0}, {'field': 'offline_amount', 'old_value': 41611.0, 'new_value': 97862.0}, {'field': 'total_amount', 'old_value': 43820.0, 'new_value': 101535.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 29}]
2025-06-07 12:01:44,212 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMZP
2025-06-07 12:01:44,665 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMZP
2025-06-07 12:01:44,665 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57176.0, 'new_value': 72110.0}, {'field': 'total_amount', 'old_value': 57176.0, 'new_value': 72110.0}, {'field': 'order_count', 'old_value': 1419, 'new_value': 1750}]
2025-06-07 12:01:44,665 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM9Q
2025-06-07 12:01:45,071 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM9Q
2025-06-07 12:01:45,071 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69614.0, 'new_value': 110120.0}, {'field': 'total_amount', 'old_value': 69614.0, 'new_value': 110120.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 23}]
2025-06-07 12:01:45,071 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM1Q
2025-06-07 12:01:45,540 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM1Q
2025-06-07 12:01:45,540 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 577894.58, 'new_value': 726719.56}, {'field': 'total_amount', 'old_value': 577894.58, 'new_value': 726719.56}, {'field': 'order_count', 'old_value': 1143, 'new_value': 1389}]
2025-06-07 12:01:45,540 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM2Q
2025-06-07 12:01:45,993 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM2Q
2025-06-07 12:01:45,993 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3922.0, 'new_value': 4811.0}, {'field': 'total_amount', 'old_value': 5263.0, 'new_value': 6152.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 25}]
2025-06-07 12:01:45,993 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM3Q
2025-06-07 12:01:46,431 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM3Q
2025-06-07 12:01:46,431 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 187681.0, 'new_value': 215227.0}, {'field': 'total_amount', 'old_value': 187681.0, 'new_value': 215227.0}, {'field': 'order_count', 'old_value': 890, 'new_value': 1016}]
2025-06-07 12:01:46,431 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM4Q
2025-06-07 12:01:46,993 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM4Q
2025-06-07 12:01:46,993 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2021548.72, 'new_value': 2344137.73}, {'field': 'total_amount', 'old_value': 2021548.72, 'new_value': 2344137.73}, {'field': 'order_count', 'old_value': 7609, 'new_value': 8899}]
2025-06-07 12:01:46,993 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM5Q
2025-06-07 12:01:47,493 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM5Q
2025-06-07 12:01:47,493 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32128.59, 'new_value': 39585.38}, {'field': 'total_amount', 'old_value': 32128.59, 'new_value': 39585.38}, {'field': 'order_count', 'old_value': 3383, 'new_value': 4221}]
2025-06-07 12:01:47,493 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM6Q
2025-06-07 12:01:47,931 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBM6Q
2025-06-07 12:01:47,931 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40660.03, 'new_value': 50871.55}, {'field': 'offline_amount', 'old_value': 37297.58, 'new_value': 45019.43}, {'field': 'total_amount', 'old_value': 77957.61, 'new_value': 95890.98}, {'field': 'order_count', 'old_value': 3349, 'new_value': 4077}]
2025-06-07 12:01:47,931 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N7187VVULONBQJREB25Z9B92MG1QQEBMKK
2025-06-07 12:01:48,384 - INFO - 更新表单数据成功: FINST-PAB66N7187VVULONBQJREB25Z9B92MG1QQEBMKK
2025-06-07 12:01:48,384 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 890.0, 'new_value': 1390.0}, {'field': 'total_amount', 'old_value': 890.0, 'new_value': 1390.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-06-07 12:01:48,384 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N7187VVULONBQJREB25Z9B92MG1QQEBMLK
2025-06-07 12:01:48,821 - INFO - 更新表单数据成功: FINST-PAB66N7187VVULONBQJREB25Z9B92MG1QQEBMLK
2025-06-07 12:01:48,821 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27063.7, 'new_value': 40047.7}, {'field': 'offline_amount', 'old_value': 16354.1, 'new_value': 29512.1}, {'field': 'total_amount', 'old_value': 43417.8, 'new_value': 69559.8}, {'field': 'order_count', 'old_value': 133, 'new_value': 180}]
2025-06-07 12:01:48,821 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N7187VVULONBQJREB25Z9B92MG1QQEBMNK
2025-06-07 12:01:49,259 - INFO - 更新表单数据成功: FINST-PAB66N7187VVULONBQJREB25Z9B92MG1QQEBMNK
2025-06-07 12:01:49,259 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35461.0, 'new_value': 42575.0}, {'field': 'total_amount', 'old_value': 35461.0, 'new_value': 42575.0}, {'field': 'order_count', 'old_value': 179, 'new_value': 180}]
2025-06-07 12:01:49,259 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N7187VVULONBQJREB25Z9B92MG1QQEBMPK
2025-06-07 12:01:49,696 - INFO - 更新表单数据成功: FINST-PAB66N7187VVULONBQJREB25Z9B92MG1QQEBMPK
2025-06-07 12:01:49,696 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19361.0, 'new_value': 35625.0}, {'field': 'total_amount', 'old_value': 19361.0, 'new_value': 35625.0}, {'field': 'order_count', 'old_value': 1853, 'new_value': 3624}]
2025-06-07 12:01:49,696 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBM61
2025-06-07 12:01:50,150 - INFO - 更新表单数据成功: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBM61
2025-06-07 12:01:50,150 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27553.68, 'new_value': 35423.84}, {'field': 'total_amount', 'old_value': 27553.68, 'new_value': 35423.84}, {'field': 'order_count', 'old_value': 2076, 'new_value': 2666}]
2025-06-07 12:01:50,150 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBM81
2025-06-07 12:01:50,618 - INFO - 更新表单数据成功: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBM81
2025-06-07 12:01:50,618 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35987.28, 'new_value': 48525.28}, {'field': 'total_amount', 'old_value': 35987.28, 'new_value': 48525.28}, {'field': 'order_count', 'old_value': 720, 'new_value': 971}]
2025-06-07 12:01:50,618 - INFO - 日期 2025-06 处理完成 - 更新: 150 条，插入: 0 条，错误: 0 条
2025-06-07 12:01:50,618 - INFO - 数据同步完成！更新: 150 条，插入: 1 条，错误: 0 条
2025-06-07 12:01:50,618 - INFO - =================同步完成====================
2025-06-07 15:00:02,657 - INFO - =================使用默认全量同步=============
2025-06-07 15:00:04,282 - INFO - MySQL查询成功，共获取 3926 条记录
2025-06-07 15:00:04,282 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-07 15:00:04,329 - INFO - 开始处理日期: 2025-01
2025-06-07 15:00:04,329 - INFO - Request Parameters - Page 1:
2025-06-07 15:00:04,329 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:04,329 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:05,766 - INFO - Response - Page 1:
2025-06-07 15:00:05,969 - INFO - 第 1 页获取到 100 条记录
2025-06-07 15:00:05,969 - INFO - Request Parameters - Page 2:
2025-06-07 15:00:05,969 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:05,969 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:06,485 - INFO - Response - Page 2:
2025-06-07 15:00:06,688 - INFO - 第 2 页获取到 100 条记录
2025-06-07 15:00:06,688 - INFO - Request Parameters - Page 3:
2025-06-07 15:00:06,688 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:06,688 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:07,157 - INFO - Response - Page 3:
2025-06-07 15:00:07,360 - INFO - 第 3 页获取到 100 条记录
2025-06-07 15:00:07,360 - INFO - Request Parameters - Page 4:
2025-06-07 15:00:07,360 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:07,360 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:07,938 - INFO - Response - Page 4:
2025-06-07 15:00:08,141 - INFO - 第 4 页获取到 100 条记录
2025-06-07 15:00:08,141 - INFO - Request Parameters - Page 5:
2025-06-07 15:00:08,141 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:08,141 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:08,657 - INFO - Response - Page 5:
2025-06-07 15:00:08,860 - INFO - 第 5 页获取到 100 条记录
2025-06-07 15:00:08,860 - INFO - Request Parameters - Page 6:
2025-06-07 15:00:08,860 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:08,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:09,376 - INFO - Response - Page 6:
2025-06-07 15:00:09,579 - INFO - 第 6 页获取到 100 条记录
2025-06-07 15:00:09,579 - INFO - Request Parameters - Page 7:
2025-06-07 15:00:09,579 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:09,579 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:10,032 - INFO - Response - Page 7:
2025-06-07 15:00:10,235 - INFO - 第 7 页获取到 82 条记录
2025-06-07 15:00:10,235 - INFO - 查询完成，共获取到 682 条记录
2025-06-07 15:00:10,235 - INFO - 获取到 682 条表单数据
2025-06-07 15:00:10,235 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-07 15:00:10,251 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 15:00:10,251 - INFO - 开始处理日期: 2025-02
2025-06-07 15:00:10,251 - INFO - Request Parameters - Page 1:
2025-06-07 15:00:10,251 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:10,251 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:10,751 - INFO - Response - Page 1:
2025-06-07 15:00:10,954 - INFO - 第 1 页获取到 100 条记录
2025-06-07 15:00:10,954 - INFO - Request Parameters - Page 2:
2025-06-07 15:00:10,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:10,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:11,454 - INFO - Response - Page 2:
2025-06-07 15:00:11,657 - INFO - 第 2 页获取到 100 条记录
2025-06-07 15:00:11,657 - INFO - Request Parameters - Page 3:
2025-06-07 15:00:11,657 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:11,657 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:12,157 - INFO - Response - Page 3:
2025-06-07 15:00:12,360 - INFO - 第 3 页获取到 100 条记录
2025-06-07 15:00:12,360 - INFO - Request Parameters - Page 4:
2025-06-07 15:00:12,360 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:12,360 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:12,797 - INFO - Response - Page 4:
2025-06-07 15:00:13,001 - INFO - 第 4 页获取到 100 条记录
2025-06-07 15:00:13,001 - INFO - Request Parameters - Page 5:
2025-06-07 15:00:13,001 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:13,001 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:13,501 - INFO - Response - Page 5:
2025-06-07 15:00:13,704 - INFO - 第 5 页获取到 100 条记录
2025-06-07 15:00:13,704 - INFO - Request Parameters - Page 6:
2025-06-07 15:00:13,704 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:13,704 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:14,172 - INFO - Response - Page 6:
2025-06-07 15:00:14,376 - INFO - 第 6 页获取到 100 条记录
2025-06-07 15:00:14,376 - INFO - Request Parameters - Page 7:
2025-06-07 15:00:14,376 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:14,376 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:14,844 - INFO - Response - Page 7:
2025-06-07 15:00:15,047 - INFO - 第 7 页获取到 70 条记录
2025-06-07 15:00:15,047 - INFO - 查询完成，共获取到 670 条记录
2025-06-07 15:00:15,047 - INFO - 获取到 670 条表单数据
2025-06-07 15:00:15,047 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-07 15:00:15,063 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 15:00:15,063 - INFO - 开始处理日期: 2025-03
2025-06-07 15:00:15,063 - INFO - Request Parameters - Page 1:
2025-06-07 15:00:15,063 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:15,063 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:15,579 - INFO - Response - Page 1:
2025-06-07 15:00:15,782 - INFO - 第 1 页获取到 100 条记录
2025-06-07 15:00:15,782 - INFO - Request Parameters - Page 2:
2025-06-07 15:00:15,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:15,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:16,282 - INFO - Response - Page 2:
2025-06-07 15:00:16,485 - INFO - 第 2 页获取到 100 条记录
2025-06-07 15:00:16,485 - INFO - Request Parameters - Page 3:
2025-06-07 15:00:16,485 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:16,485 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:17,016 - INFO - Response - Page 3:
2025-06-07 15:00:17,219 - INFO - 第 3 页获取到 100 条记录
2025-06-07 15:00:17,219 - INFO - Request Parameters - Page 4:
2025-06-07 15:00:17,219 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:17,219 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:17,704 - INFO - Response - Page 4:
2025-06-07 15:00:17,907 - INFO - 第 4 页获取到 100 条记录
2025-06-07 15:00:17,907 - INFO - Request Parameters - Page 5:
2025-06-07 15:00:17,907 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:17,907 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:18,422 - INFO - Response - Page 5:
2025-06-07 15:00:18,626 - INFO - 第 5 页获取到 100 条记录
2025-06-07 15:00:18,626 - INFO - Request Parameters - Page 6:
2025-06-07 15:00:18,626 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:18,626 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:19,141 - INFO - Response - Page 6:
2025-06-07 15:00:19,344 - INFO - 第 6 页获取到 100 条记录
2025-06-07 15:00:19,344 - INFO - Request Parameters - Page 7:
2025-06-07 15:00:19,344 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:19,344 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:19,813 - INFO - Response - Page 7:
2025-06-07 15:00:20,016 - INFO - 第 7 页获取到 61 条记录
2025-06-07 15:00:20,016 - INFO - 查询完成，共获取到 661 条记录
2025-06-07 15:00:20,016 - INFO - 获取到 661 条表单数据
2025-06-07 15:00:20,016 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-07 15:00:20,032 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 15:00:20,032 - INFO - 开始处理日期: 2025-04
2025-06-07 15:00:20,032 - INFO - Request Parameters - Page 1:
2025-06-07 15:00:20,032 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:20,032 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:20,626 - INFO - Response - Page 1:
2025-06-07 15:00:20,829 - INFO - 第 1 页获取到 100 条记录
2025-06-07 15:00:20,829 - INFO - Request Parameters - Page 2:
2025-06-07 15:00:20,829 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:20,829 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:21,391 - INFO - Response - Page 2:
2025-06-07 15:00:21,594 - INFO - 第 2 页获取到 100 条记录
2025-06-07 15:00:21,594 - INFO - Request Parameters - Page 3:
2025-06-07 15:00:21,594 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:21,594 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:22,063 - INFO - Response - Page 3:
2025-06-07 15:00:22,266 - INFO - 第 3 页获取到 100 条记录
2025-06-07 15:00:22,266 - INFO - Request Parameters - Page 4:
2025-06-07 15:00:22,266 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:22,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:22,719 - INFO - Response - Page 4:
2025-06-07 15:00:22,922 - INFO - 第 4 页获取到 100 条记录
2025-06-07 15:00:22,922 - INFO - Request Parameters - Page 5:
2025-06-07 15:00:22,922 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:22,922 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:23,422 - INFO - Response - Page 5:
2025-06-07 15:00:23,626 - INFO - 第 5 页获取到 100 条记录
2025-06-07 15:00:23,626 - INFO - Request Parameters - Page 6:
2025-06-07 15:00:23,626 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:23,626 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:24,141 - INFO - Response - Page 6:
2025-06-07 15:00:24,344 - INFO - 第 6 页获取到 100 条记录
2025-06-07 15:00:24,344 - INFO - Request Parameters - Page 7:
2025-06-07 15:00:24,344 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:24,344 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:24,844 - INFO - Response - Page 7:
2025-06-07 15:00:25,047 - INFO - 第 7 页获取到 56 条记录
2025-06-07 15:00:25,047 - INFO - 查询完成，共获取到 656 条记录
2025-06-07 15:00:25,047 - INFO - 获取到 656 条表单数据
2025-06-07 15:00:25,047 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-07 15:00:25,063 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 15:00:25,063 - INFO - 开始处理日期: 2025-05
2025-06-07 15:00:25,063 - INFO - Request Parameters - Page 1:
2025-06-07 15:00:25,063 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:25,063 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:25,547 - INFO - Response - Page 1:
2025-06-07 15:00:25,751 - INFO - 第 1 页获取到 100 条记录
2025-06-07 15:00:25,751 - INFO - Request Parameters - Page 2:
2025-06-07 15:00:25,751 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:25,751 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:26,250 - INFO - Response - Page 2:
2025-06-07 15:00:26,454 - INFO - 第 2 页获取到 100 条记录
2025-06-07 15:00:26,454 - INFO - Request Parameters - Page 3:
2025-06-07 15:00:26,454 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:26,454 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:26,938 - INFO - Response - Page 3:
2025-06-07 15:00:27,141 - INFO - 第 3 页获取到 100 条记录
2025-06-07 15:00:27,141 - INFO - Request Parameters - Page 4:
2025-06-07 15:00:27,141 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:27,141 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:27,594 - INFO - Response - Page 4:
2025-06-07 15:00:27,797 - INFO - 第 4 页获取到 100 条记录
2025-06-07 15:00:27,797 - INFO - Request Parameters - Page 5:
2025-06-07 15:00:27,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:27,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:28,282 - INFO - Response - Page 5:
2025-06-07 15:00:28,485 - INFO - 第 5 页获取到 100 条记录
2025-06-07 15:00:28,485 - INFO - Request Parameters - Page 6:
2025-06-07 15:00:28,485 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:28,485 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:28,985 - INFO - Response - Page 6:
2025-06-07 15:00:29,188 - INFO - 第 6 页获取到 100 条记录
2025-06-07 15:00:29,188 - INFO - Request Parameters - Page 7:
2025-06-07 15:00:29,188 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:29,188 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:29,625 - INFO - Response - Page 7:
2025-06-07 15:00:29,829 - INFO - 第 7 页获取到 38 条记录
2025-06-07 15:00:29,829 - INFO - 查询完成，共获取到 638 条记录
2025-06-07 15:00:29,829 - INFO - 获取到 638 条表单数据
2025-06-07 15:00:29,829 - INFO - 当前日期 2025-05 有 638 条MySQL数据需要处理
2025-06-07 15:00:29,844 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 15:00:29,844 - INFO - 开始处理日期: 2025-06
2025-06-07 15:00:29,844 - INFO - Request Parameters - Page 1:
2025-06-07 15:00:29,844 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:29,844 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:30,344 - INFO - Response - Page 1:
2025-06-07 15:00:30,547 - INFO - 第 1 页获取到 100 条记录
2025-06-07 15:00:30,547 - INFO - Request Parameters - Page 2:
2025-06-07 15:00:30,547 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:30,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:31,563 - INFO - Response - Page 2:
2025-06-07 15:00:31,766 - INFO - 第 2 页获取到 100 条记录
2025-06-07 15:00:31,766 - INFO - Request Parameters - Page 3:
2025-06-07 15:00:31,766 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:31,766 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:32,297 - INFO - Response - Page 3:
2025-06-07 15:00:32,500 - INFO - 第 3 页获取到 100 条记录
2025-06-07 15:00:32,500 - INFO - Request Parameters - Page 4:
2025-06-07 15:00:32,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:32,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:32,969 - INFO - Response - Page 4:
2025-06-07 15:00:33,172 - INFO - 第 4 页获取到 100 条记录
2025-06-07 15:00:33,172 - INFO - Request Parameters - Page 5:
2025-06-07 15:00:33,172 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:33,172 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:33,860 - INFO - Response - Page 5:
2025-06-07 15:00:34,063 - INFO - 第 5 页获取到 100 条记录
2025-06-07 15:00:34,063 - INFO - Request Parameters - Page 6:
2025-06-07 15:00:34,063 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:34,063 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:34,563 - INFO - Response - Page 6:
2025-06-07 15:00:34,766 - INFO - 第 6 页获取到 100 条记录
2025-06-07 15:00:34,766 - INFO - Request Parameters - Page 7:
2025-06-07 15:00:34,766 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 15:00:34,766 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 15:00:35,079 - INFO - Response - Page 7:
2025-06-07 15:00:35,282 - INFO - 第 7 页获取到 19 条记录
2025-06-07 15:00:35,282 - INFO - 查询完成，共获取到 619 条记录
2025-06-07 15:00:35,282 - INFO - 获取到 619 条表单数据
2025-06-07 15:00:35,282 - INFO - 当前日期 2025-06 有 619 条MySQL数据需要处理
2025-06-07 15:00:35,282 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMM5
2025-06-07 15:00:35,750 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2YR1VDEBMM5
2025-06-07 15:00:35,750 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36260.0, 'new_value': 45120.0}, {'field': 'total_amount', 'old_value': 36260.0, 'new_value': 45120.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-06-07 15:00:35,750 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMV7
2025-06-07 15:00:36,204 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBMV7
2025-06-07 15:00:36,204 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 140461.0, 'new_value': 167069.0}, {'field': 'total_amount', 'old_value': 140461.0, 'new_value': 167069.0}, {'field': 'order_count', 'old_value': 677, 'new_value': 808}]
2025-06-07 15:00:36,204 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM68
2025-06-07 15:00:36,672 - INFO - 更新表单数据成功: FINST-AAG66KB14GXVD5ADE2YBM5ZVYK6Z2ZR1VDEBM68
2025-06-07 15:00:36,672 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50198.09, 'new_value': 59342.75}, {'field': 'total_amount', 'old_value': 50198.09, 'new_value': 59342.75}, {'field': 'order_count', 'old_value': 180, 'new_value': 210}]
2025-06-07 15:00:36,672 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMBP
2025-06-07 15:00:37,094 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMBP
2025-06-07 15:00:37,094 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4028.7, 'new_value': 6747.7}, {'field': 'total_amount', 'old_value': 35976.7, 'new_value': 38695.7}, {'field': 'order_count', 'old_value': 447, 'new_value': 526}]
2025-06-07 15:00:37,094 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMMP
2025-06-07 15:00:37,594 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMMP
2025-06-07 15:00:37,594 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18532.0, 'new_value': 22584.0}, {'field': 'total_amount', 'old_value': 18532.0, 'new_value': 22584.0}, {'field': 'order_count', 'old_value': 241, 'new_value': 296}]
2025-06-07 15:00:37,594 - INFO - 开始更新记录 - 表单实例ID: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMXP
2025-06-07 15:00:38,047 - INFO - 更新表单数据成功: FINST-B2766R81TGSV5HDRAWH96CR3A78H32YTAKEBMXP
2025-06-07 15:00:38,047 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 341000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 341000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-07 15:00:38,047 - INFO - 开始更新记录 - 表单实例ID: FINST-PAB66N7187VVULONBQJREB25Z9B92MG1QQEBMJK
2025-06-07 15:00:38,500 - INFO - 更新表单数据成功: FINST-PAB66N7187VVULONBQJREB25Z9B92MG1QQEBMJK
2025-06-07 15:00:38,500 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39850.0, 'new_value': 49680.0}, {'field': 'total_amount', 'old_value': 39850.0, 'new_value': 49680.0}, {'field': 'order_count', 'old_value': 1354, 'new_value': 1701}]
2025-06-07 15:00:38,500 - INFO - 日期 2025-06 处理完成 - 更新: 7 条，插入: 0 条，错误: 0 条
2025-06-07 15:00:38,500 - INFO - 数据同步完成！更新: 7 条，插入: 0 条，错误: 0 条
2025-06-07 15:00:38,500 - INFO - =================同步完成====================
2025-06-07 18:00:02,289 - INFO - =================使用默认全量同步=============
2025-06-07 18:00:03,898 - INFO - MySQL查询成功，共获取 3926 条记录
2025-06-07 18:00:03,898 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-07 18:00:03,930 - INFO - 开始处理日期: 2025-01
2025-06-07 18:00:03,930 - INFO - Request Parameters - Page 1:
2025-06-07 18:00:03,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:03,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:04,930 - INFO - Response - Page 1:
2025-06-07 18:00:05,133 - INFO - 第 1 页获取到 100 条记录
2025-06-07 18:00:05,133 - INFO - Request Parameters - Page 2:
2025-06-07 18:00:05,133 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:05,133 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:05,695 - INFO - Response - Page 2:
2025-06-07 18:00:05,898 - INFO - 第 2 页获取到 100 条记录
2025-06-07 18:00:05,898 - INFO - Request Parameters - Page 3:
2025-06-07 18:00:05,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:05,898 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:06,898 - INFO - Response - Page 3:
2025-06-07 18:00:07,101 - INFO - 第 3 页获取到 100 条记录
2025-06-07 18:00:07,101 - INFO - Request Parameters - Page 4:
2025-06-07 18:00:07,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:07,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:07,586 - INFO - Response - Page 4:
2025-06-07 18:00:07,789 - INFO - 第 4 页获取到 100 条记录
2025-06-07 18:00:07,789 - INFO - Request Parameters - Page 5:
2025-06-07 18:00:07,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:07,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:08,351 - INFO - Response - Page 5:
2025-06-07 18:00:08,554 - INFO - 第 5 页获取到 100 条记录
2025-06-07 18:00:08,554 - INFO - Request Parameters - Page 6:
2025-06-07 18:00:08,554 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:08,554 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:09,258 - INFO - Response - Page 6:
2025-06-07 18:00:09,461 - INFO - 第 6 页获取到 100 条记录
2025-06-07 18:00:09,461 - INFO - Request Parameters - Page 7:
2025-06-07 18:00:09,461 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:09,461 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:09,976 - INFO - Response - Page 7:
2025-06-07 18:00:10,179 - INFO - 第 7 页获取到 82 条记录
2025-06-07 18:00:10,179 - INFO - 查询完成，共获取到 682 条记录
2025-06-07 18:00:10,179 - INFO - 获取到 682 条表单数据
2025-06-07 18:00:10,179 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-07 18:00:10,195 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 18:00:10,195 - INFO - 开始处理日期: 2025-02
2025-06-07 18:00:10,195 - INFO - Request Parameters - Page 1:
2025-06-07 18:00:10,195 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:10,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:10,758 - INFO - Response - Page 1:
2025-06-07 18:00:10,961 - INFO - 第 1 页获取到 100 条记录
2025-06-07 18:00:10,961 - INFO - Request Parameters - Page 2:
2025-06-07 18:00:10,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:10,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:11,508 - INFO - Response - Page 2:
2025-06-07 18:00:11,711 - INFO - 第 2 页获取到 100 条记录
2025-06-07 18:00:11,711 - INFO - Request Parameters - Page 3:
2025-06-07 18:00:11,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:11,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:12,211 - INFO - Response - Page 3:
2025-06-07 18:00:12,414 - INFO - 第 3 页获取到 100 条记录
2025-06-07 18:00:12,414 - INFO - Request Parameters - Page 4:
2025-06-07 18:00:12,414 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:12,414 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:13,117 - INFO - Response - Page 4:
2025-06-07 18:00:13,320 - INFO - 第 4 页获取到 100 条记录
2025-06-07 18:00:13,320 - INFO - Request Parameters - Page 5:
2025-06-07 18:00:13,320 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:13,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:13,804 - INFO - Response - Page 5:
2025-06-07 18:00:14,008 - INFO - 第 5 页获取到 100 条记录
2025-06-07 18:00:14,008 - INFO - Request Parameters - Page 6:
2025-06-07 18:00:14,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:14,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:14,508 - INFO - Response - Page 6:
2025-06-07 18:00:14,711 - INFO - 第 6 页获取到 100 条记录
2025-06-07 18:00:14,711 - INFO - Request Parameters - Page 7:
2025-06-07 18:00:14,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:14,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:15,148 - INFO - Response - Page 7:
2025-06-07 18:00:15,351 - INFO - 第 7 页获取到 70 条记录
2025-06-07 18:00:15,351 - INFO - 查询完成，共获取到 670 条记录
2025-06-07 18:00:15,351 - INFO - 获取到 670 条表单数据
2025-06-07 18:00:15,351 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-07 18:00:15,367 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 18:00:15,367 - INFO - 开始处理日期: 2025-03
2025-06-07 18:00:15,367 - INFO - Request Parameters - Page 1:
2025-06-07 18:00:15,367 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:15,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:15,898 - INFO - Response - Page 1:
2025-06-07 18:00:16,101 - INFO - 第 1 页获取到 100 条记录
2025-06-07 18:00:16,101 - INFO - Request Parameters - Page 2:
2025-06-07 18:00:16,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:16,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:16,586 - INFO - Response - Page 2:
2025-06-07 18:00:16,789 - INFO - 第 2 页获取到 100 条记录
2025-06-07 18:00:16,789 - INFO - Request Parameters - Page 3:
2025-06-07 18:00:16,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:16,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:17,304 - INFO - Response - Page 3:
2025-06-07 18:00:17,508 - INFO - 第 3 页获取到 100 条记录
2025-06-07 18:00:17,508 - INFO - Request Parameters - Page 4:
2025-06-07 18:00:17,508 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:17,508 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:18,023 - INFO - Response - Page 4:
2025-06-07 18:00:18,226 - INFO - 第 4 页获取到 100 条记录
2025-06-07 18:00:18,226 - INFO - Request Parameters - Page 5:
2025-06-07 18:00:18,226 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:18,226 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:18,711 - INFO - Response - Page 5:
2025-06-07 18:00:18,914 - INFO - 第 5 页获取到 100 条记录
2025-06-07 18:00:18,914 - INFO - Request Parameters - Page 6:
2025-06-07 18:00:18,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:18,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:19,914 - INFO - Response - Page 6:
2025-06-07 18:00:20,117 - INFO - 第 6 页获取到 100 条记录
2025-06-07 18:00:20,117 - INFO - Request Parameters - Page 7:
2025-06-07 18:00:20,117 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:20,117 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:20,570 - INFO - Response - Page 7:
2025-06-07 18:00:20,773 - INFO - 第 7 页获取到 61 条记录
2025-06-07 18:00:20,773 - INFO - 查询完成，共获取到 661 条记录
2025-06-07 18:00:20,773 - INFO - 获取到 661 条表单数据
2025-06-07 18:00:20,773 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-07 18:00:20,789 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 18:00:20,789 - INFO - 开始处理日期: 2025-04
2025-06-07 18:00:20,789 - INFO - Request Parameters - Page 1:
2025-06-07 18:00:20,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:20,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:21,336 - INFO - Response - Page 1:
2025-06-07 18:00:21,539 - INFO - 第 1 页获取到 100 条记录
2025-06-07 18:00:21,539 - INFO - Request Parameters - Page 2:
2025-06-07 18:00:21,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:21,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:21,976 - INFO - Response - Page 2:
2025-06-07 18:00:22,179 - INFO - 第 2 页获取到 100 条记录
2025-06-07 18:00:22,179 - INFO - Request Parameters - Page 3:
2025-06-07 18:00:22,179 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:22,179 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:22,695 - INFO - Response - Page 3:
2025-06-07 18:00:22,898 - INFO - 第 3 页获取到 100 条记录
2025-06-07 18:00:22,898 - INFO - Request Parameters - Page 4:
2025-06-07 18:00:22,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:22,898 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:23,414 - INFO - Response - Page 4:
2025-06-07 18:00:23,617 - INFO - 第 4 页获取到 100 条记录
2025-06-07 18:00:23,617 - INFO - Request Parameters - Page 5:
2025-06-07 18:00:23,617 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:23,617 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:24,258 - INFO - Response - Page 5:
2025-06-07 18:00:24,461 - INFO - 第 5 页获取到 100 条记录
2025-06-07 18:00:24,461 - INFO - Request Parameters - Page 6:
2025-06-07 18:00:24,461 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:24,461 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:25,054 - INFO - Response - Page 6:
2025-06-07 18:00:25,257 - INFO - 第 6 页获取到 100 条记录
2025-06-07 18:00:25,257 - INFO - Request Parameters - Page 7:
2025-06-07 18:00:25,257 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:25,257 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:25,633 - INFO - Response - Page 7:
2025-06-07 18:00:25,836 - INFO - 第 7 页获取到 56 条记录
2025-06-07 18:00:25,836 - INFO - 查询完成，共获取到 656 条记录
2025-06-07 18:00:25,836 - INFO - 获取到 656 条表单数据
2025-06-07 18:00:25,836 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-07 18:00:25,851 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 18:00:25,851 - INFO - 开始处理日期: 2025-05
2025-06-07 18:00:25,851 - INFO - Request Parameters - Page 1:
2025-06-07 18:00:25,851 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:25,851 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:26,382 - INFO - Response - Page 1:
2025-06-07 18:00:26,586 - INFO - 第 1 页获取到 100 条记录
2025-06-07 18:00:26,586 - INFO - Request Parameters - Page 2:
2025-06-07 18:00:26,586 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:26,586 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:27,086 - INFO - Response - Page 2:
2025-06-07 18:00:27,289 - INFO - 第 2 页获取到 100 条记录
2025-06-07 18:00:27,289 - INFO - Request Parameters - Page 3:
2025-06-07 18:00:27,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:27,289 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:27,773 - INFO - Response - Page 3:
2025-06-07 18:00:27,976 - INFO - 第 3 页获取到 100 条记录
2025-06-07 18:00:27,976 - INFO - Request Parameters - Page 4:
2025-06-07 18:00:27,976 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:27,976 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:28,429 - INFO - Response - Page 4:
2025-06-07 18:00:28,632 - INFO - 第 4 页获取到 100 条记录
2025-06-07 18:00:28,632 - INFO - Request Parameters - Page 5:
2025-06-07 18:00:28,632 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:28,632 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:29,148 - INFO - Response - Page 5:
2025-06-07 18:00:29,351 - INFO - 第 5 页获取到 100 条记录
2025-06-07 18:00:29,351 - INFO - Request Parameters - Page 6:
2025-06-07 18:00:29,351 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:29,351 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:29,820 - INFO - Response - Page 6:
2025-06-07 18:00:30,023 - INFO - 第 6 页获取到 100 条记录
2025-06-07 18:00:30,023 - INFO - Request Parameters - Page 7:
2025-06-07 18:00:30,023 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:30,023 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:30,398 - INFO - Response - Page 7:
2025-06-07 18:00:30,601 - INFO - 第 7 页获取到 38 条记录
2025-06-07 18:00:30,601 - INFO - 查询完成，共获取到 638 条记录
2025-06-07 18:00:30,601 - INFO - 获取到 638 条表单数据
2025-06-07 18:00:30,601 - INFO - 当前日期 2025-05 有 638 条MySQL数据需要处理
2025-06-07 18:00:30,617 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 18:00:30,617 - INFO - 开始处理日期: 2025-06
2025-06-07 18:00:30,617 - INFO - Request Parameters - Page 1:
2025-06-07 18:00:30,617 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:30,617 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:31,148 - INFO - Response - Page 1:
2025-06-07 18:00:31,351 - INFO - 第 1 页获取到 100 条记录
2025-06-07 18:00:31,351 - INFO - Request Parameters - Page 2:
2025-06-07 18:00:31,351 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:31,351 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:31,804 - INFO - Response - Page 2:
2025-06-07 18:00:32,007 - INFO - 第 2 页获取到 100 条记录
2025-06-07 18:00:32,007 - INFO - Request Parameters - Page 3:
2025-06-07 18:00:32,007 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:32,007 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:32,461 - INFO - Response - Page 3:
2025-06-07 18:00:32,664 - INFO - 第 3 页获取到 100 条记录
2025-06-07 18:00:32,664 - INFO - Request Parameters - Page 4:
2025-06-07 18:00:32,664 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:32,664 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:33,414 - INFO - Response - Page 4:
2025-06-07 18:00:33,617 - INFO - 第 4 页获取到 100 条记录
2025-06-07 18:00:33,617 - INFO - Request Parameters - Page 5:
2025-06-07 18:00:33,617 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:33,617 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:34,164 - INFO - Response - Page 5:
2025-06-07 18:00:34,367 - INFO - 第 5 页获取到 100 条记录
2025-06-07 18:00:34,367 - INFO - Request Parameters - Page 6:
2025-06-07 18:00:34,367 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:34,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:34,929 - INFO - Response - Page 6:
2025-06-07 18:00:35,132 - INFO - 第 6 页获取到 100 条记录
2025-06-07 18:00:35,132 - INFO - Request Parameters - Page 7:
2025-06-07 18:00:35,132 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 18:00:35,132 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 18:00:35,445 - INFO - Response - Page 7:
2025-06-07 18:00:35,648 - INFO - 第 7 页获取到 19 条记录
2025-06-07 18:00:35,648 - INFO - 查询完成，共获取到 619 条记录
2025-06-07 18:00:35,648 - INFO - 获取到 619 条表单数据
2025-06-07 18:00:35,648 - INFO - 当前日期 2025-06 有 619 条MySQL数据需要处理
2025-06-07 18:00:35,664 - INFO - 开始更新记录 - 表单实例ID: FINST-RNA66D71CHZVM3KV7IDIS53MRL6X3I6GHPGBM06
2025-06-07 18:00:36,101 - INFO - 更新表单数据成功: FINST-RNA66D71CHZVM3KV7IDIS53MRL6X3I6GHPGBM06
2025-06-07 18:00:36,101 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7998.0, 'new_value': 37046.0}, {'field': 'total_amount', 'old_value': 12152.0, 'new_value': 41200.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 5}]
2025-06-07 18:00:36,117 - INFO - 开始更新记录 - 表单实例ID: FINST-RNA66D71CHZVM3KV7IDIS53MRL6X3I6GHPGBM86
2025-06-07 18:00:36,523 - INFO - 更新表单数据成功: FINST-RNA66D71CHZVM3KV7IDIS53MRL6X3I6GHPGBM86
2025-06-07 18:00:36,523 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83583.0, 'new_value': 87788.4}, {'field': 'total_amount', 'old_value': 83583.0, 'new_value': 87788.4}, {'field': 'order_count', 'old_value': 1099, 'new_value': 1127}]
2025-06-07 18:00:36,539 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBMH1
2025-06-07 18:00:36,992 - INFO - 更新表单数据成功: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBMH1
2025-06-07 18:00:36,992 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2398.0, 'new_value': 2797.0}, {'field': 'total_amount', 'old_value': 4597.0, 'new_value': 4996.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-06-07 18:00:36,992 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM2
2025-06-07 18:00:37,445 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM2
2025-06-07 18:00:37,445 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6886.0, 'new_value': 8484.0}, {'field': 'total_amount', 'old_value': 6886.0, 'new_value': 8484.0}, {'field': 'order_count', 'old_value': 685, 'new_value': 827}]
2025-06-07 18:00:37,445 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM3
2025-06-07 18:00:37,945 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM3
2025-06-07 18:00:37,945 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 258000.0, 'new_value': 308000.0}, {'field': 'total_amount', 'old_value': 258000.0, 'new_value': 308000.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-06-07 18:00:37,945 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM4
2025-06-07 18:00:38,429 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM4
2025-06-07 18:00:38,445 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72836.0, 'new_value': 84370.0}, {'field': 'total_amount', 'old_value': 72836.0, 'new_value': 84370.0}, {'field': 'order_count', 'old_value': 2017, 'new_value': 2245}]
2025-06-07 18:00:38,445 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMA
2025-06-07 18:00:38,898 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMA
2025-06-07 18:00:38,898 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42505.0, 'new_value': 47600.0}, {'field': 'total_amount', 'old_value': 42505.0, 'new_value': 47600.0}, {'field': 'order_count', 'old_value': 1030, 'new_value': 1031}]
2025-06-07 18:00:38,898 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMB
2025-06-07 18:00:39,476 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMB
2025-06-07 18:00:39,476 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1800.0, 'new_value': 3600.0}, {'field': 'total_amount', 'old_value': 1800.0, 'new_value': 3600.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-06-07 18:00:39,476 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMD
2025-06-07 18:00:40,007 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMD
2025-06-07 18:00:40,007 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5781.0, 'new_value': 6081.0}, {'field': 'total_amount', 'old_value': 5781.0, 'new_value': 6081.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-06-07 18:00:40,007 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMF
2025-06-07 18:00:40,476 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMF
2025-06-07 18:00:40,476 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 99.0, 'new_value': 30099.0}, {'field': 'total_amount', 'old_value': 99.0, 'new_value': 30099.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-06-07 18:00:40,476 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMG
2025-06-07 18:00:40,945 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMG
2025-06-07 18:00:40,945 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84000.0, 'new_value': 91000.0}, {'field': 'total_amount', 'old_value': 84000.0, 'new_value': 91000.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-06-07 18:00:40,945 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMK
2025-06-07 18:00:41,414 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMK
2025-06-07 18:00:41,414 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24184.0, 'new_value': 29322.0}, {'field': 'total_amount', 'old_value': 24184.0, 'new_value': 29322.0}, {'field': 'order_count', 'old_value': 1138, 'new_value': 1336}]
2025-06-07 18:00:41,414 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBML
2025-06-07 18:00:41,929 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBML
2025-06-07 18:00:41,929 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30971.89, 'new_value': 37496.89}, {'field': 'total_amount', 'old_value': 30971.89, 'new_value': 37496.89}, {'field': 'order_count', 'old_value': 169, 'new_value': 203}]
2025-06-07 18:00:41,929 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMM
2025-06-07 18:00:42,382 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMM
2025-06-07 18:00:42,382 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22524.0, 'new_value': 24698.0}, {'field': 'total_amount', 'old_value': 22524.0, 'new_value': 24698.0}, {'field': 'order_count', 'old_value': 160, 'new_value': 184}]
2025-06-07 18:00:42,382 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMQ
2025-06-07 18:00:42,867 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMQ
2025-06-07 18:00:42,867 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6204.0, 'new_value': 7784.0}, {'field': 'total_amount', 'old_value': 6204.0, 'new_value': 7784.0}, {'field': 'order_count', 'old_value': 581, 'new_value': 729}]
2025-06-07 18:00:42,867 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMR
2025-06-07 18:00:43,320 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMR
2025-06-07 18:00:43,320 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2677.0, 'new_value': 3131.0}, {'field': 'total_amount', 'old_value': 2677.0, 'new_value': 3131.0}, {'field': 'order_count', 'old_value': 76, 'new_value': 89}]
2025-06-07 18:00:43,320 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMU
2025-06-07 18:00:43,742 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMU
2025-06-07 18:00:43,757 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72809.17, 'new_value': 81774.17}, {'field': 'total_amount', 'old_value': 72809.17, 'new_value': 81774.17}, {'field': 'order_count', 'old_value': 534, 'new_value': 596}]
2025-06-07 18:00:43,757 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMV
2025-06-07 18:00:44,257 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMV
2025-06-07 18:00:44,257 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 298.0, 'new_value': 1257.0}, {'field': 'total_amount', 'old_value': 298.0, 'new_value': 1257.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 4}]
2025-06-07 18:00:44,257 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMX
2025-06-07 18:00:44,773 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMX
2025-06-07 18:00:44,773 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7824.0, 'new_value': 11801.0}, {'field': 'total_amount', 'old_value': 7824.0, 'new_value': 11801.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 12}]
2025-06-07 18:00:44,789 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMZ
2025-06-07 18:00:45,226 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBMZ
2025-06-07 18:00:45,226 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1093601.0, 'new_value': 1302063.0}, {'field': 'total_amount', 'old_value': 1093601.0, 'new_value': 1302063.0}, {'field': 'order_count', 'old_value': 19947, 'new_value': 23990}]
2025-06-07 18:00:45,226 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM01
2025-06-07 18:00:45,710 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM01
2025-06-07 18:00:45,710 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8887.0, 'new_value': 13886.0}, {'field': 'total_amount', 'old_value': 8887.0, 'new_value': 13886.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-06-07 18:00:45,710 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM21
2025-06-07 18:00:46,195 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM21
2025-06-07 18:00:46,195 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66369.2, 'new_value': 69495.55}, {'field': 'total_amount', 'old_value': 66369.2, 'new_value': 69495.55}, {'field': 'order_count', 'old_value': 114, 'new_value': 134}]
2025-06-07 18:00:46,195 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM41
2025-06-07 18:00:46,789 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM41
2025-06-07 18:00:46,789 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 634.0, 'new_value': 832.0}, {'field': 'total_amount', 'old_value': 634.0, 'new_value': 832.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-06-07 18:00:46,789 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM51
2025-06-07 18:00:47,289 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM51
2025-06-07 18:00:47,289 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2258700.0, 'new_value': 2331700.0}, {'field': 'total_amount', 'old_value': 2258700.0, 'new_value': 2331700.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-06-07 18:00:47,289 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM71
2025-06-07 18:00:47,773 - INFO - 更新表单数据成功: FINST-3PF66V71UMYVDPHH627O24JCWTM82P4U5XEBM71
2025-06-07 18:00:47,773 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8079.88, 'new_value': 8980.44}, {'field': 'total_amount', 'old_value': 8079.88, 'new_value': 8980.44}, {'field': 'order_count', 'old_value': 292, 'new_value': 326}]
2025-06-07 18:00:47,773 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBM91
2025-06-07 18:00:48,242 - INFO - 更新表单数据成功: FINST-7PF66MD1WBZV481OERNV54M27W953DDRSZFBM91
2025-06-07 18:00:48,242 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 93000.0, 'new_value': 102000.0}, {'field': 'total_amount', 'old_value': 93000.0, 'new_value': 102000.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-06-07 18:00:48,242 - INFO - 日期 2025-06 处理完成 - 更新: 26 条，插入: 0 条，错误: 0 条
2025-06-07 18:00:48,242 - INFO - 数据同步完成！更新: 26 条，插入: 0 条，错误: 0 条
2025-06-07 18:00:48,242 - INFO - =================同步完成====================
2025-06-07 21:00:02,697 - INFO - =================使用默认全量同步=============
2025-06-07 21:00:04,275 - INFO - MySQL查询成功，共获取 3926 条记录
2025-06-07 21:00:04,275 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-07 21:00:04,306 - INFO - 开始处理日期: 2025-01
2025-06-07 21:00:04,306 - INFO - Request Parameters - Page 1:
2025-06-07 21:00:04,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:04,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:05,712 - INFO - Response - Page 1:
2025-06-07 21:00:05,915 - INFO - 第 1 页获取到 100 条记录
2025-06-07 21:00:05,915 - INFO - Request Parameters - Page 2:
2025-06-07 21:00:05,915 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:05,915 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:06,478 - INFO - Response - Page 2:
2025-06-07 21:00:06,681 - INFO - 第 2 页获取到 100 条记录
2025-06-07 21:00:06,681 - INFO - Request Parameters - Page 3:
2025-06-07 21:00:06,681 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:06,681 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:07,259 - INFO - Response - Page 3:
2025-06-07 21:00:07,462 - INFO - 第 3 页获取到 100 条记录
2025-06-07 21:00:07,462 - INFO - Request Parameters - Page 4:
2025-06-07 21:00:07,462 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:07,462 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:07,962 - INFO - Response - Page 4:
2025-06-07 21:00:08,165 - INFO - 第 4 页获取到 100 条记录
2025-06-07 21:00:08,165 - INFO - Request Parameters - Page 5:
2025-06-07 21:00:08,165 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:08,165 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:08,619 - INFO - Response - Page 5:
2025-06-07 21:00:08,822 - INFO - 第 5 页获取到 100 条记录
2025-06-07 21:00:08,822 - INFO - Request Parameters - Page 6:
2025-06-07 21:00:08,822 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:08,822 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:09,290 - INFO - Response - Page 6:
2025-06-07 21:00:09,494 - INFO - 第 6 页获取到 100 条记录
2025-06-07 21:00:09,494 - INFO - Request Parameters - Page 7:
2025-06-07 21:00:09,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:09,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:09,994 - INFO - Response - Page 7:
2025-06-07 21:00:10,197 - INFO - 第 7 页获取到 82 条记录
2025-06-07 21:00:10,197 - INFO - 查询完成，共获取到 682 条记录
2025-06-07 21:00:10,197 - INFO - 获取到 682 条表单数据
2025-06-07 21:00:10,197 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-07 21:00:10,212 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 21:00:10,212 - INFO - 开始处理日期: 2025-02
2025-06-07 21:00:10,212 - INFO - Request Parameters - Page 1:
2025-06-07 21:00:10,212 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:10,212 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:10,697 - INFO - Response - Page 1:
2025-06-07 21:00:10,900 - INFO - 第 1 页获取到 100 条记录
2025-06-07 21:00:10,900 - INFO - Request Parameters - Page 2:
2025-06-07 21:00:10,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:10,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:11,447 - INFO - Response - Page 2:
2025-06-07 21:00:11,650 - INFO - 第 2 页获取到 100 条记录
2025-06-07 21:00:11,650 - INFO - Request Parameters - Page 3:
2025-06-07 21:00:11,650 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:11,650 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:12,119 - INFO - Response - Page 3:
2025-06-07 21:00:12,322 - INFO - 第 3 页获取到 100 条记录
2025-06-07 21:00:12,322 - INFO - Request Parameters - Page 4:
2025-06-07 21:00:12,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:12,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:12,822 - INFO - Response - Page 4:
2025-06-07 21:00:13,025 - INFO - 第 4 页获取到 100 条记录
2025-06-07 21:00:13,025 - INFO - Request Parameters - Page 5:
2025-06-07 21:00:13,025 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:13,025 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:13,556 - INFO - Response - Page 5:
2025-06-07 21:00:13,759 - INFO - 第 5 页获取到 100 条记录
2025-06-07 21:00:13,759 - INFO - Request Parameters - Page 6:
2025-06-07 21:00:13,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:13,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:14,228 - INFO - Response - Page 6:
2025-06-07 21:00:14,431 - INFO - 第 6 页获取到 100 条记录
2025-06-07 21:00:14,431 - INFO - Request Parameters - Page 7:
2025-06-07 21:00:14,431 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:14,431 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:15,040 - INFO - Response - Page 7:
2025-06-07 21:00:15,244 - INFO - 第 7 页获取到 70 条记录
2025-06-07 21:00:15,244 - INFO - 查询完成，共获取到 670 条记录
2025-06-07 21:00:15,244 - INFO - 获取到 670 条表单数据
2025-06-07 21:00:15,244 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-07 21:00:15,259 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 21:00:15,259 - INFO - 开始处理日期: 2025-03
2025-06-07 21:00:15,259 - INFO - Request Parameters - Page 1:
2025-06-07 21:00:15,259 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:15,259 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:15,775 - INFO - Response - Page 1:
2025-06-07 21:00:15,978 - INFO - 第 1 页获取到 100 条记录
2025-06-07 21:00:15,978 - INFO - Request Parameters - Page 2:
2025-06-07 21:00:15,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:15,978 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:16,462 - INFO - Response - Page 2:
2025-06-07 21:00:16,665 - INFO - 第 2 页获取到 100 条记录
2025-06-07 21:00:16,665 - INFO - Request Parameters - Page 3:
2025-06-07 21:00:16,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:16,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:17,134 - INFO - Response - Page 3:
2025-06-07 21:00:17,337 - INFO - 第 3 页获取到 100 条记录
2025-06-07 21:00:17,337 - INFO - Request Parameters - Page 4:
2025-06-07 21:00:17,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:17,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:17,822 - INFO - Response - Page 4:
2025-06-07 21:00:18,025 - INFO - 第 4 页获取到 100 条记录
2025-06-07 21:00:18,025 - INFO - Request Parameters - Page 5:
2025-06-07 21:00:18,025 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:18,025 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:18,556 - INFO - Response - Page 5:
2025-06-07 21:00:18,759 - INFO - 第 5 页获取到 100 条记录
2025-06-07 21:00:18,759 - INFO - Request Parameters - Page 6:
2025-06-07 21:00:18,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:18,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:19,228 - INFO - Response - Page 6:
2025-06-07 21:00:19,431 - INFO - 第 6 页获取到 100 条记录
2025-06-07 21:00:19,431 - INFO - Request Parameters - Page 7:
2025-06-07 21:00:19,431 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:19,431 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:19,853 - INFO - Response - Page 7:
2025-06-07 21:00:20,056 - INFO - 第 7 页获取到 61 条记录
2025-06-07 21:00:20,056 - INFO - 查询完成，共获取到 661 条记录
2025-06-07 21:00:20,056 - INFO - 获取到 661 条表单数据
2025-06-07 21:00:20,056 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-07 21:00:20,072 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 21:00:20,072 - INFO - 开始处理日期: 2025-04
2025-06-07 21:00:20,072 - INFO - Request Parameters - Page 1:
2025-06-07 21:00:20,072 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:20,072 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:20,603 - INFO - Response - Page 1:
2025-06-07 21:00:20,806 - INFO - 第 1 页获取到 100 条记录
2025-06-07 21:00:20,806 - INFO - Request Parameters - Page 2:
2025-06-07 21:00:20,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:20,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:21,306 - INFO - Response - Page 2:
2025-06-07 21:00:21,509 - INFO - 第 2 页获取到 100 条记录
2025-06-07 21:00:21,509 - INFO - Request Parameters - Page 3:
2025-06-07 21:00:21,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:21,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:21,962 - INFO - Response - Page 3:
2025-06-07 21:00:22,165 - INFO - 第 3 页获取到 100 条记录
2025-06-07 21:00:22,165 - INFO - Request Parameters - Page 4:
2025-06-07 21:00:22,165 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:22,165 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:22,634 - INFO - Response - Page 4:
2025-06-07 21:00:22,837 - INFO - 第 4 页获取到 100 条记录
2025-06-07 21:00:22,837 - INFO - Request Parameters - Page 5:
2025-06-07 21:00:22,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:22,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:23,368 - INFO - Response - Page 5:
2025-06-07 21:00:23,572 - INFO - 第 5 页获取到 100 条记录
2025-06-07 21:00:23,572 - INFO - Request Parameters - Page 6:
2025-06-07 21:00:23,572 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:23,572 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:24,197 - INFO - Response - Page 6:
2025-06-07 21:00:24,400 - INFO - 第 6 页获取到 100 条记录
2025-06-07 21:00:24,400 - INFO - Request Parameters - Page 7:
2025-06-07 21:00:24,400 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:24,400 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:24,806 - INFO - Response - Page 7:
2025-06-07 21:00:25,009 - INFO - 第 7 页获取到 56 条记录
2025-06-07 21:00:25,009 - INFO - 查询完成，共获取到 656 条记录
2025-06-07 21:00:25,009 - INFO - 获取到 656 条表单数据
2025-06-07 21:00:25,009 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-07 21:00:25,025 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 21:00:25,025 - INFO - 开始处理日期: 2025-05
2025-06-07 21:00:25,025 - INFO - Request Parameters - Page 1:
2025-06-07 21:00:25,025 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:25,025 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:25,572 - INFO - Response - Page 1:
2025-06-07 21:00:25,775 - INFO - 第 1 页获取到 100 条记录
2025-06-07 21:00:25,775 - INFO - Request Parameters - Page 2:
2025-06-07 21:00:25,775 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:25,775 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:26,212 - INFO - Response - Page 2:
2025-06-07 21:00:26,415 - INFO - 第 2 页获取到 100 条记录
2025-06-07 21:00:26,415 - INFO - Request Parameters - Page 3:
2025-06-07 21:00:26,415 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:26,415 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:26,900 - INFO - Response - Page 3:
2025-06-07 21:00:27,103 - INFO - 第 3 页获取到 100 条记录
2025-06-07 21:00:27,103 - INFO - Request Parameters - Page 4:
2025-06-07 21:00:27,103 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:27,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:27,790 - INFO - Response - Page 4:
2025-06-07 21:00:27,993 - INFO - 第 4 页获取到 100 条记录
2025-06-07 21:00:27,993 - INFO - Request Parameters - Page 5:
2025-06-07 21:00:27,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:27,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:28,618 - INFO - Response - Page 5:
2025-06-07 21:00:28,822 - INFO - 第 5 页获取到 100 条记录
2025-06-07 21:00:28,822 - INFO - Request Parameters - Page 6:
2025-06-07 21:00:28,822 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:28,822 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:29,275 - INFO - Response - Page 6:
2025-06-07 21:00:29,478 - INFO - 第 6 页获取到 100 条记录
2025-06-07 21:00:29,478 - INFO - Request Parameters - Page 7:
2025-06-07 21:00:29,478 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:29,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:29,853 - INFO - Response - Page 7:
2025-06-07 21:00:30,056 - INFO - 第 7 页获取到 38 条记录
2025-06-07 21:00:30,056 - INFO - 查询完成，共获取到 638 条记录
2025-06-07 21:00:30,056 - INFO - 获取到 638 条表单数据
2025-06-07 21:00:30,056 - INFO - 当前日期 2025-05 有 638 条MySQL数据需要处理
2025-06-07 21:00:30,072 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-07 21:00:30,072 - INFO - 开始处理日期: 2025-06
2025-06-07 21:00:30,072 - INFO - Request Parameters - Page 1:
2025-06-07 21:00:30,072 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:30,072 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:30,572 - INFO - Response - Page 1:
2025-06-07 21:00:30,775 - INFO - 第 1 页获取到 100 条记录
2025-06-07 21:00:30,775 - INFO - Request Parameters - Page 2:
2025-06-07 21:00:30,775 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:30,775 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:31,368 - INFO - Response - Page 2:
2025-06-07 21:00:31,572 - INFO - 第 2 页获取到 100 条记录
2025-06-07 21:00:31,572 - INFO - Request Parameters - Page 3:
2025-06-07 21:00:31,572 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:31,572 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:32,072 - INFO - Response - Page 3:
2025-06-07 21:00:32,275 - INFO - 第 3 页获取到 100 条记录
2025-06-07 21:00:32,275 - INFO - Request Parameters - Page 4:
2025-06-07 21:00:32,275 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:32,275 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:32,837 - INFO - Response - Page 4:
2025-06-07 21:00:33,040 - INFO - 第 4 页获取到 100 条记录
2025-06-07 21:00:33,040 - INFO - Request Parameters - Page 5:
2025-06-07 21:00:33,040 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:33,040 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:33,572 - INFO - Response - Page 5:
2025-06-07 21:00:33,775 - INFO - 第 5 页获取到 100 条记录
2025-06-07 21:00:33,775 - INFO - Request Parameters - Page 6:
2025-06-07 21:00:33,775 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:33,775 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:34,337 - INFO - Response - Page 6:
2025-06-07 21:00:34,540 - INFO - 第 6 页获取到 100 条记录
2025-06-07 21:00:34,540 - INFO - Request Parameters - Page 7:
2025-06-07 21:00:34,540 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 21:00:34,540 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 21:00:34,900 - INFO - Response - Page 7:
2025-06-07 21:00:35,103 - INFO - 第 7 页获取到 19 条记录
2025-06-07 21:00:35,103 - INFO - 查询完成，共获取到 619 条记录
2025-06-07 21:00:35,103 - INFO - 获取到 619 条表单数据
2025-06-07 21:00:35,103 - INFO - 当前日期 2025-06 有 619 条MySQL数据需要处理
2025-06-07 21:00:35,103 - INFO - 开始更新记录 - 表单实例ID: FINST-CJ966Q71RIWVLV7P7SGXMCGLMKQM3JJ85ODBM06
2025-06-07 21:00:35,618 - INFO - 更新表单数据成功: FINST-CJ966Q71RIWVLV7P7SGXMCGLMKQM3JJ85ODBM06
2025-06-07 21:00:35,618 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18812.0, 'new_value': 21679.0}, {'field': 'offline_amount', 'old_value': 24593.0, 'new_value': 28705.0}, {'field': 'total_amount', 'old_value': 43405.0, 'new_value': 50384.0}, {'field': 'order_count', 'old_value': 928, 'new_value': 1083}]
2025-06-07 21:00:35,634 - INFO - 日期 2025-06 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-06-07 21:00:35,634 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-06-07 21:00:35,634 - INFO - =================同步完成====================
