2025-06-14 00:00:02,674 - INFO - =================使用默认全量同步=============
2025-06-14 00:00:04,377 - INFO - MySQL查询成功，共获取 3930 条记录
2025-06-14 00:00:04,377 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-14 00:00:04,424 - INFO - 开始处理日期: 2025-01
2025-06-14 00:00:04,424 - INFO - Request Parameters - Page 1:
2025-06-14 00:00:04,424 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:04,424 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:05,877 - INFO - Response - Page 1:
2025-06-14 00:00:06,080 - INFO - 第 1 页获取到 100 条记录
2025-06-14 00:00:06,080 - INFO - Request Parameters - Page 2:
2025-06-14 00:00:06,080 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:06,080 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:06,596 - INFO - Response - Page 2:
2025-06-14 00:00:06,799 - INFO - 第 2 页获取到 100 条记录
2025-06-14 00:00:06,799 - INFO - Request Parameters - Page 3:
2025-06-14 00:00:06,799 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:06,799 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:07,314 - INFO - Response - Page 3:
2025-06-14 00:00:07,518 - INFO - 第 3 页获取到 100 条记录
2025-06-14 00:00:07,518 - INFO - Request Parameters - Page 4:
2025-06-14 00:00:07,518 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:07,518 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:08,096 - INFO - Response - Page 4:
2025-06-14 00:00:08,299 - INFO - 第 4 页获取到 100 条记录
2025-06-14 00:00:08,299 - INFO - Request Parameters - Page 5:
2025-06-14 00:00:08,299 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:08,299 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:08,908 - INFO - Response - Page 5:
2025-06-14 00:00:09,111 - INFO - 第 5 页获取到 100 条记录
2025-06-14 00:00:09,111 - INFO - Request Parameters - Page 6:
2025-06-14 00:00:09,111 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:09,111 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:09,689 - INFO - Response - Page 6:
2025-06-14 00:00:09,893 - INFO - 第 6 页获取到 100 条记录
2025-06-14 00:00:09,893 - INFO - Request Parameters - Page 7:
2025-06-14 00:00:09,893 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:09,893 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:10,377 - INFO - Response - Page 7:
2025-06-14 00:00:10,580 - INFO - 第 7 页获取到 82 条记录
2025-06-14 00:00:10,580 - INFO - 查询完成，共获取到 682 条记录
2025-06-14 00:00:10,580 - INFO - 获取到 682 条表单数据
2025-06-14 00:00:10,580 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-14 00:00:10,596 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 00:00:10,596 - INFO - 开始处理日期: 2025-02
2025-06-14 00:00:10,596 - INFO - Request Parameters - Page 1:
2025-06-14 00:00:10,596 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:10,596 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:11,111 - INFO - Response - Page 1:
2025-06-14 00:00:11,314 - INFO - 第 1 页获取到 100 条记录
2025-06-14 00:00:11,314 - INFO - Request Parameters - Page 2:
2025-06-14 00:00:11,314 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:11,314 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:12,127 - INFO - Response - Page 2:
2025-06-14 00:00:12,330 - INFO - 第 2 页获取到 100 条记录
2025-06-14 00:00:12,330 - INFO - Request Parameters - Page 3:
2025-06-14 00:00:12,330 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:12,330 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:12,861 - INFO - Response - Page 3:
2025-06-14 00:00:13,064 - INFO - 第 3 页获取到 100 条记录
2025-06-14 00:00:13,064 - INFO - Request Parameters - Page 4:
2025-06-14 00:00:13,064 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:13,064 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:13,611 - INFO - Response - Page 4:
2025-06-14 00:00:13,814 - INFO - 第 4 页获取到 100 条记录
2025-06-14 00:00:13,814 - INFO - Request Parameters - Page 5:
2025-06-14 00:00:13,814 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:13,814 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:14,377 - INFO - Response - Page 5:
2025-06-14 00:00:14,580 - INFO - 第 5 页获取到 100 条记录
2025-06-14 00:00:14,580 - INFO - Request Parameters - Page 6:
2025-06-14 00:00:14,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:14,580 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:15,143 - INFO - Response - Page 6:
2025-06-14 00:00:15,346 - INFO - 第 6 页获取到 100 条记录
2025-06-14 00:00:15,346 - INFO - Request Parameters - Page 7:
2025-06-14 00:00:15,346 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:15,346 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:15,830 - INFO - Response - Page 7:
2025-06-14 00:00:16,033 - INFO - 第 7 页获取到 70 条记录
2025-06-14 00:00:16,033 - INFO - 查询完成，共获取到 670 条记录
2025-06-14 00:00:16,033 - INFO - 获取到 670 条表单数据
2025-06-14 00:00:16,033 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-14 00:00:16,049 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 00:00:16,049 - INFO - 开始处理日期: 2025-03
2025-06-14 00:00:16,049 - INFO - Request Parameters - Page 1:
2025-06-14 00:00:16,049 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:16,049 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:16,549 - INFO - Response - Page 1:
2025-06-14 00:00:16,768 - INFO - 第 1 页获取到 100 条记录
2025-06-14 00:00:16,768 - INFO - Request Parameters - Page 2:
2025-06-14 00:00:16,768 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:16,768 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:17,314 - INFO - Response - Page 2:
2025-06-14 00:00:17,518 - INFO - 第 2 页获取到 100 条记录
2025-06-14 00:00:17,518 - INFO - Request Parameters - Page 3:
2025-06-14 00:00:17,518 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:17,518 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:18,064 - INFO - Response - Page 3:
2025-06-14 00:00:18,268 - INFO - 第 3 页获取到 100 条记录
2025-06-14 00:00:18,268 - INFO - Request Parameters - Page 4:
2025-06-14 00:00:18,268 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:18,268 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:18,783 - INFO - Response - Page 4:
2025-06-14 00:00:18,986 - INFO - 第 4 页获取到 100 条记录
2025-06-14 00:00:18,986 - INFO - Request Parameters - Page 5:
2025-06-14 00:00:18,986 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:18,986 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:19,689 - INFO - Response - Page 5:
2025-06-14 00:00:19,892 - INFO - 第 5 页获取到 100 条记录
2025-06-14 00:00:19,892 - INFO - Request Parameters - Page 6:
2025-06-14 00:00:19,892 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:19,892 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:21,111 - INFO - Response - Page 6:
2025-06-14 00:00:21,314 - INFO - 第 6 页获取到 100 条记录
2025-06-14 00:00:21,314 - INFO - Request Parameters - Page 7:
2025-06-14 00:00:21,314 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:21,314 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:21,736 - INFO - Response - Page 7:
2025-06-14 00:00:21,939 - INFO - 第 7 页获取到 61 条记录
2025-06-14 00:00:21,939 - INFO - 查询完成，共获取到 661 条记录
2025-06-14 00:00:21,939 - INFO - 获取到 661 条表单数据
2025-06-14 00:00:21,939 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-14 00:00:21,955 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 00:00:21,955 - INFO - 开始处理日期: 2025-04
2025-06-14 00:00:21,955 - INFO - Request Parameters - Page 1:
2025-06-14 00:00:21,955 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:21,955 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:22,642 - INFO - Response - Page 1:
2025-06-14 00:00:22,846 - INFO - 第 1 页获取到 100 条记录
2025-06-14 00:00:22,846 - INFO - Request Parameters - Page 2:
2025-06-14 00:00:22,846 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:22,846 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:25,408 - INFO - Response - Page 2:
2025-06-14 00:00:25,611 - INFO - 第 2 页获取到 100 条记录
2025-06-14 00:00:25,611 - INFO - Request Parameters - Page 3:
2025-06-14 00:00:25,611 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:25,611 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:27,689 - INFO - Response - Page 3:
2025-06-14 00:00:27,892 - INFO - 第 3 页获取到 100 条记录
2025-06-14 00:00:27,892 - INFO - Request Parameters - Page 4:
2025-06-14 00:00:27,892 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:27,892 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:28,455 - INFO - Response - Page 4:
2025-06-14 00:00:28,658 - INFO - 第 4 页获取到 100 条记录
2025-06-14 00:00:28,658 - INFO - Request Parameters - Page 5:
2025-06-14 00:00:28,658 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:28,658 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:29,189 - INFO - Response - Page 5:
2025-06-14 00:00:29,392 - INFO - 第 5 页获取到 100 条记录
2025-06-14 00:00:29,392 - INFO - Request Parameters - Page 6:
2025-06-14 00:00:29,392 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:29,392 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:30,049 - INFO - Response - Page 6:
2025-06-14 00:00:30,252 - INFO - 第 6 页获取到 100 条记录
2025-06-14 00:00:30,252 - INFO - Request Parameters - Page 7:
2025-06-14 00:00:30,252 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:30,252 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:30,705 - INFO - Response - Page 7:
2025-06-14 00:00:30,908 - INFO - 第 7 页获取到 56 条记录
2025-06-14 00:00:30,908 - INFO - 查询完成，共获取到 656 条记录
2025-06-14 00:00:30,908 - INFO - 获取到 656 条表单数据
2025-06-14 00:00:30,908 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-14 00:00:30,924 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 00:00:30,924 - INFO - 开始处理日期: 2025-05
2025-06-14 00:00:30,924 - INFO - Request Parameters - Page 1:
2025-06-14 00:00:30,924 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:30,924 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:31,549 - INFO - Response - Page 1:
2025-06-14 00:00:31,752 - INFO - 第 1 页获取到 100 条记录
2025-06-14 00:00:31,752 - INFO - Request Parameters - Page 2:
2025-06-14 00:00:31,752 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:31,752 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:32,283 - INFO - Response - Page 2:
2025-06-14 00:00:32,486 - INFO - 第 2 页获取到 100 条记录
2025-06-14 00:00:32,486 - INFO - Request Parameters - Page 3:
2025-06-14 00:00:32,486 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:32,486 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:32,986 - INFO - Response - Page 3:
2025-06-14 00:00:33,189 - INFO - 第 3 页获取到 100 条记录
2025-06-14 00:00:33,189 - INFO - Request Parameters - Page 4:
2025-06-14 00:00:33,189 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:33,189 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:33,658 - INFO - Response - Page 4:
2025-06-14 00:00:33,861 - INFO - 第 4 页获取到 100 条记录
2025-06-14 00:00:33,861 - INFO - Request Parameters - Page 5:
2025-06-14 00:00:33,861 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:33,861 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:34,377 - INFO - Response - Page 5:
2025-06-14 00:00:34,580 - INFO - 第 5 页获取到 100 条记录
2025-06-14 00:00:34,580 - INFO - Request Parameters - Page 6:
2025-06-14 00:00:34,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:34,580 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:35,127 - INFO - Response - Page 6:
2025-06-14 00:00:35,330 - INFO - 第 6 页获取到 100 条记录
2025-06-14 00:00:35,330 - INFO - Request Parameters - Page 7:
2025-06-14 00:00:35,330 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:35,330 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:35,721 - INFO - Response - Page 7:
2025-06-14 00:00:35,924 - INFO - 第 7 页获取到 40 条记录
2025-06-14 00:00:35,924 - INFO - 查询完成，共获取到 640 条记录
2025-06-14 00:00:35,924 - INFO - 获取到 640 条表单数据
2025-06-14 00:00:35,924 - INFO - 当前日期 2025-05 有 640 条MySQL数据需要处理
2025-06-14 00:00:35,939 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 00:00:35,939 - INFO - 开始处理日期: 2025-06
2025-06-14 00:00:35,939 - INFO - Request Parameters - Page 1:
2025-06-14 00:00:35,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:35,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:36,471 - INFO - Response - Page 1:
2025-06-14 00:00:36,674 - INFO - 第 1 页获取到 100 条记录
2025-06-14 00:00:36,674 - INFO - Request Parameters - Page 2:
2025-06-14 00:00:36,674 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:36,674 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:37,127 - INFO - Response - Page 2:
2025-06-14 00:00:37,330 - INFO - 第 2 页获取到 100 条记录
2025-06-14 00:00:37,330 - INFO - Request Parameters - Page 3:
2025-06-14 00:00:37,330 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:37,330 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:37,846 - INFO - Response - Page 3:
2025-06-14 00:00:38,049 - INFO - 第 3 页获取到 100 条记录
2025-06-14 00:00:38,049 - INFO - Request Parameters - Page 4:
2025-06-14 00:00:38,049 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:38,049 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:38,627 - INFO - Response - Page 4:
2025-06-14 00:00:38,830 - INFO - 第 4 页获取到 100 条记录
2025-06-14 00:00:38,830 - INFO - Request Parameters - Page 5:
2025-06-14 00:00:38,830 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:38,830 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:39,314 - INFO - Response - Page 5:
2025-06-14 00:00:39,517 - INFO - 第 5 页获取到 100 条记录
2025-06-14 00:00:39,517 - INFO - Request Parameters - Page 6:
2025-06-14 00:00:39,517 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:39,517 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:39,970 - INFO - Response - Page 6:
2025-06-14 00:00:40,174 - INFO - 第 6 页获取到 100 条记录
2025-06-14 00:00:40,174 - INFO - Request Parameters - Page 7:
2025-06-14 00:00:40,174 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 00:00:40,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 00:00:40,471 - INFO - Response - Page 7:
2025-06-14 00:00:40,674 - INFO - 第 7 页获取到 21 条记录
2025-06-14 00:00:40,674 - INFO - 查询完成，共获取到 621 条记录
2025-06-14 00:00:40,674 - INFO - 获取到 621 条表单数据
2025-06-14 00:00:40,674 - INFO - 当前日期 2025-06 有 621 条MySQL数据需要处理
2025-06-14 00:00:40,674 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMNX
2025-06-14 00:00:41,174 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMNX
2025-06-14 00:00:41,174 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15915.0, 'new_value': 20495.0}, {'field': 'total_amount', 'old_value': 15915.0, 'new_value': 20495.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-06-14 00:00:41,174 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMRX
2025-06-14 00:00:41,611 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMRX
2025-06-14 00:00:41,611 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20120.0, 'new_value': 27120.0}, {'field': 'total_amount', 'old_value': 20120.0, 'new_value': 27120.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-06-14 00:00:41,611 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMYX
2025-06-14 00:00:42,002 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMYX
2025-06-14 00:00:42,002 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10149.82, 'new_value': 10284.82}, {'field': 'total_amount', 'old_value': 10149.82, 'new_value': 10284.82}, {'field': 'order_count', 'old_value': 37, 'new_value': 39}]
2025-06-14 00:00:42,002 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZX
2025-06-14 00:00:42,486 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZX
2025-06-14 00:00:42,486 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45638.0, 'new_value': 46637.0}, {'field': 'total_amount', 'old_value': 45638.0, 'new_value': 46637.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-14 00:00:42,486 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM9Y
2025-06-14 00:00:42,939 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM9Y
2025-06-14 00:00:42,939 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23060.0, 'new_value': 25720.0}, {'field': 'total_amount', 'old_value': 30040.0, 'new_value': 32700.0}, {'field': 'order_count', 'old_value': 309, 'new_value': 337}]
2025-06-14 00:00:42,939 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMMY
2025-06-14 00:00:43,377 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMMY
2025-06-14 00:00:43,377 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 454041.0, 'new_value': 498698.0}, {'field': 'offline_amount', 'old_value': 130650.0, 'new_value': 141763.0}, {'field': 'total_amount', 'old_value': 584691.0, 'new_value': 640461.0}, {'field': 'order_count', 'old_value': 608, 'new_value': 665}]
2025-06-14 00:00:43,377 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM5Z
2025-06-14 00:00:43,939 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM5Z
2025-06-14 00:00:43,939 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6483.7, 'new_value': 6672.7}, {'field': 'total_amount', 'old_value': 6483.7, 'new_value': 6672.7}, {'field': 'order_count', 'old_value': 80, 'new_value': 82}]
2025-06-14 00:00:43,939 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMEZ
2025-06-14 00:00:44,470 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMEZ
2025-06-14 00:00:44,470 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88958.51, 'new_value': 91691.51}, {'field': 'total_amount', 'old_value': 100133.22, 'new_value': 102866.22}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-06-14 00:00:44,470 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNZ
2025-06-14 00:00:44,955 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMNZ
2025-06-14 00:00:44,955 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 963.72, 'new_value': 1032.3}, {'field': 'offline_amount', 'old_value': 49932.62, 'new_value': 53633.97}, {'field': 'total_amount', 'old_value': 50896.34, 'new_value': 54666.27}, {'field': 'order_count', 'old_value': 1144, 'new_value': 1235}]
2025-06-14 00:00:44,955 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOZ
2025-06-14 00:00:45,408 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOZ
2025-06-14 00:00:45,408 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 58378.63, 'new_value': 62358.89}, {'field': 'offline_amount', 'old_value': 509510.84, 'new_value': 556032.24}, {'field': 'total_amount', 'old_value': 567889.47, 'new_value': 618391.13}, {'field': 'order_count', 'old_value': 4926, 'new_value': 5356}]
2025-06-14 00:00:45,408 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMXZ
2025-06-14 00:00:45,877 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMXZ
2025-06-14 00:00:45,877 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10294.5, 'new_value': 10533.5}, {'field': 'total_amount', 'old_value': 10294.5, 'new_value': 10533.5}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-06-14 00:00:45,877 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM301
2025-06-14 00:00:46,314 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM301
2025-06-14 00:00:46,314 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3410.0, 'new_value': 3546.0}, {'field': 'offline_amount', 'old_value': 30774.0, 'new_value': 30892.0}, {'field': 'total_amount', 'old_value': 34184.0, 'new_value': 34438.0}, {'field': 'order_count', 'old_value': 231, 'new_value': 235}]
2025-06-14 00:00:46,314 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM601
2025-06-14 00:00:46,752 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM601
2025-06-14 00:00:46,752 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60544.0, 'new_value': 63544.0}, {'field': 'total_amount', 'old_value': 60544.0, 'new_value': 63544.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-06-14 00:00:46,752 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHB
2025-06-14 00:00:47,220 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHB
2025-06-14 00:00:47,220 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2030.0, 'new_value': 2438.0}, {'field': 'total_amount', 'old_value': 2030.0, 'new_value': 2438.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-06-14 00:00:47,220 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKB
2025-06-14 00:00:47,642 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKB
2025-06-14 00:00:47,642 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6664.0, 'new_value': 6843.0}, {'field': 'total_amount', 'old_value': 11640.0, 'new_value': 11819.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-06-14 00:00:47,642 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQB
2025-06-14 00:00:48,080 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQB
2025-06-14 00:00:48,080 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 88369.06, 'new_value': 95274.4}, {'field': 'offline_amount', 'old_value': 169624.22, 'new_value': 182341.48}, {'field': 'total_amount', 'old_value': 257993.28, 'new_value': 277615.88}, {'field': 'order_count', 'old_value': 1873, 'new_value': 2048}]
2025-06-14 00:00:48,080 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRB
2025-06-14 00:00:48,517 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRB
2025-06-14 00:00:48,517 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12469.45, 'new_value': 12634.45}, {'field': 'offline_amount', 'old_value': 26.0, 'new_value': 27.0}, {'field': 'total_amount', 'old_value': 12495.45, 'new_value': 12661.45}, {'field': 'order_count', 'old_value': 61, 'new_value': 62}]
2025-06-14 00:00:48,517 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYB
2025-06-14 00:00:48,955 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYB
2025-06-14 00:00:48,955 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11454.4, 'new_value': 12656.4}, {'field': 'total_amount', 'old_value': 11454.4, 'new_value': 12656.4}, {'field': 'order_count', 'old_value': 61, 'new_value': 64}]
2025-06-14 00:00:48,955 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0C
2025-06-14 00:00:49,377 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0C
2025-06-14 00:00:49,377 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51394.0, 'new_value': 51800.0}, {'field': 'total_amount', 'old_value': 51394.0, 'new_value': 51800.0}, {'field': 'order_count', 'old_value': 193, 'new_value': 198}]
2025-06-14 00:00:49,377 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6C
2025-06-14 00:00:49,830 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6C
2025-06-14 00:00:49,830 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24057.0, 'new_value': 25299.0}, {'field': 'total_amount', 'old_value': 24057.0, 'new_value': 25299.0}, {'field': 'order_count', 'old_value': 32, 'new_value': 35}]
2025-06-14 00:00:49,830 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHC
2025-06-14 00:00:50,283 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHC
2025-06-14 00:00:50,283 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12105.0, 'new_value': 12995.0}, {'field': 'total_amount', 'old_value': 12105.0, 'new_value': 12995.0}, {'field': 'order_count', 'old_value': 47, 'new_value': 51}]
2025-06-14 00:00:50,283 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMC
2025-06-14 00:00:50,736 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMC
2025-06-14 00:00:50,736 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57573.0, 'new_value': 64090.0}, {'field': 'total_amount', 'old_value': 57573.0, 'new_value': 64090.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 29}]
2025-06-14 00:00:50,736 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSC
2025-06-14 00:00:51,205 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSC
2025-06-14 00:00:51,205 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4154.9, 'new_value': 4628.5}, {'field': 'total_amount', 'old_value': 10126.44, 'new_value': 10600.04}, {'field': 'order_count', 'old_value': 48, 'new_value': 51}]
2025-06-14 00:00:51,205 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUC
2025-06-14 00:00:51,642 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUC
2025-06-14 00:00:51,642 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5145.95, 'new_value': 5262.75}, {'field': 'offline_amount', 'old_value': 40503.0, 'new_value': 42666.0}, {'field': 'total_amount', 'old_value': 45648.95, 'new_value': 47928.75}, {'field': 'order_count', 'old_value': 893, 'new_value': 916}]
2025-06-14 00:00:51,642 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMVC
2025-06-14 00:00:52,017 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMVC
2025-06-14 00:00:52,017 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14146.0, 'new_value': 14486.0}, {'field': 'total_amount', 'old_value': 14146.0, 'new_value': 14486.0}, {'field': 'order_count', 'old_value': 64, 'new_value': 65}]
2025-06-14 00:00:52,017 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6D
2025-06-14 00:00:52,470 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6D
2025-06-14 00:00:52,470 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9914.3, 'new_value': 10212.3}, {'field': 'offline_amount', 'old_value': 59794.7, 'new_value': 64501.7}, {'field': 'total_amount', 'old_value': 69709.0, 'new_value': 74714.0}, {'field': 'order_count', 'old_value': 78, 'new_value': 85}]
2025-06-14 00:00:52,470 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7D
2025-06-14 00:00:52,908 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7D
2025-06-14 00:00:52,908 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12337.0, 'new_value': 13312.0}, {'field': 'total_amount', 'old_value': 12397.0, 'new_value': 13372.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 54}]
2025-06-14 00:00:52,908 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMD
2025-06-14 00:00:53,377 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMD
2025-06-14 00:00:53,377 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17826.0, 'new_value': 25067.0}, {'field': 'total_amount', 'old_value': 17826.0, 'new_value': 25067.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 18}]
2025-06-14 00:00:53,377 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOD
2025-06-14 00:00:53,845 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOD
2025-06-14 00:00:53,845 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40954.1, 'new_value': 41433.5}, {'field': 'total_amount', 'old_value': 40954.1, 'new_value': 41433.5}, {'field': 'order_count', 'old_value': 149, 'new_value': 151}]
2025-06-14 00:00:53,845 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1E
2025-06-14 00:00:54,252 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1E
2025-06-14 00:00:54,252 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52353.6, 'new_value': 53497.6}, {'field': 'total_amount', 'old_value': 52353.6, 'new_value': 53497.6}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-06-14 00:00:54,252 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTQ
2025-06-14 00:00:54,736 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTQ
2025-06-14 00:00:54,736 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37001.2, 'new_value': 39414.01}, {'field': 'total_amount', 'old_value': 37001.2, 'new_value': 39414.01}, {'field': 'order_count', 'old_value': 1185, 'new_value': 1267}]
2025-06-14 00:00:54,736 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVQ
2025-06-14 00:00:55,142 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVQ
2025-06-14 00:00:55,158 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5578.66, 'new_value': 6002.36}, {'field': 'offline_amount', 'old_value': 136741.49, 'new_value': 147631.49}, {'field': 'total_amount', 'old_value': 142320.15, 'new_value': 153633.85}, {'field': 'order_count', 'old_value': 7479, 'new_value': 8227}]
2025-06-14 00:00:55,158 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWQ
2025-06-14 00:00:55,564 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWQ
2025-06-14 00:00:55,564 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4624.1, 'new_value': 4921.1}, {'field': 'offline_amount', 'old_value': 40853.0, 'new_value': 46653.0}, {'field': 'total_amount', 'old_value': 45477.1, 'new_value': 51574.1}, {'field': 'order_count', 'old_value': 34, 'new_value': 37}]
2025-06-14 00:00:55,564 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZQ
2025-06-14 00:00:56,033 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZQ
2025-06-14 00:00:56,033 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25937.0, 'new_value': 26474.0}, {'field': 'total_amount', 'old_value': 25937.0, 'new_value': 26474.0}, {'field': 'order_count', 'old_value': 64, 'new_value': 67}]
2025-06-14 00:00:56,033 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3R
2025-06-14 00:00:56,408 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3R
2025-06-14 00:00:56,408 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31475.7, 'new_value': 33492.91}, {'field': 'offline_amount', 'old_value': 39993.15, 'new_value': 45492.15}, {'field': 'total_amount', 'old_value': 71468.85, 'new_value': 78985.06}, {'field': 'order_count', 'old_value': 55, 'new_value': 57}]
2025-06-14 00:00:56,408 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4R
2025-06-14 00:00:56,814 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4R
2025-06-14 00:00:56,814 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14656.9, 'new_value': 15200.9}, {'field': 'total_amount', 'old_value': 14656.9, 'new_value': 15200.9}, {'field': 'order_count', 'old_value': 91, 'new_value': 96}]
2025-06-14 00:00:56,814 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBR
2025-06-14 00:00:57,267 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBR
2025-06-14 00:00:57,267 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79033.0, 'new_value': 82187.0}, {'field': 'total_amount', 'old_value': 79033.0, 'new_value': 82187.0}, {'field': 'order_count', 'old_value': 292, 'new_value': 306}]
2025-06-14 00:00:57,267 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDR
2025-06-14 00:00:57,689 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDR
2025-06-14 00:00:57,689 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18964.16, 'new_value': 20543.25}, {'field': 'offline_amount', 'old_value': 14070.0, 'new_value': 15259.0}, {'field': 'total_amount', 'old_value': 33034.16, 'new_value': 35802.25}, {'field': 'order_count', 'old_value': 443, 'new_value': 479}]
2025-06-14 00:00:57,689 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMMR
2025-06-14 00:00:58,127 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMMR
2025-06-14 00:00:58,127 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 137256.0, 'new_value': 142850.0}, {'field': 'total_amount', 'old_value': 137256.0, 'new_value': 142850.0}, {'field': 'order_count', 'old_value': 56, 'new_value': 61}]
2025-06-14 00:00:58,127 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNR
2025-06-14 00:00:58,595 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNR
2025-06-14 00:00:58,595 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49910.91, 'new_value': 53388.02}, {'field': 'offline_amount', 'old_value': 24883.94, 'new_value': 26886.87}, {'field': 'total_amount', 'old_value': 74794.85, 'new_value': 80274.89}, {'field': 'order_count', 'old_value': 4441, 'new_value': 4755}]
2025-06-14 00:00:58,611 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMOR
2025-06-14 00:00:58,986 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMOR
2025-06-14 00:00:58,986 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54566.1, 'new_value': 56320.3}, {'field': 'total_amount', 'old_value': 54566.1, 'new_value': 56320.3}, {'field': 'order_count', 'old_value': 136, 'new_value': 140}]
2025-06-14 00:00:58,986 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQR
2025-06-14 00:00:59,377 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQR
2025-06-14 00:00:59,377 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24792.2, 'new_value': 29480.2}, {'field': 'total_amount', 'old_value': 24792.2, 'new_value': 29480.2}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-06-14 00:00:59,377 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTR
2025-06-14 00:00:59,798 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTR
2025-06-14 00:00:59,798 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27543.2, 'new_value': 28073.1}, {'field': 'total_amount', 'old_value': 27543.2, 'new_value': 28073.1}, {'field': 'order_count', 'old_value': 176, 'new_value': 182}]
2025-06-14 00:00:59,798 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUR
2025-06-14 00:01:00,283 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUR
2025-06-14 00:01:00,283 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26684.0, 'new_value': 28832.59}, {'field': 'offline_amount', 'old_value': 40680.48, 'new_value': 42916.59}, {'field': 'total_amount', 'old_value': 67364.48, 'new_value': 71749.18}, {'field': 'order_count', 'old_value': 2542, 'new_value': 2740}]
2025-06-14 00:01:00,283 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYR
2025-06-14 00:01:00,736 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYR
2025-06-14 00:01:00,736 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45571.26, 'new_value': 47833.05}, {'field': 'total_amount', 'old_value': 45571.26, 'new_value': 47833.05}, {'field': 'order_count', 'old_value': 1658, 'new_value': 1759}]
2025-06-14 00:01:00,736 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0S
2025-06-14 00:01:01,252 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0S
2025-06-14 00:01:01,252 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10199.6, 'new_value': 10443.6}, {'field': 'total_amount', 'old_value': 10199.6, 'new_value': 10443.6}, {'field': 'order_count', 'old_value': 49, 'new_value': 51}]
2025-06-14 00:01:01,252 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1S
2025-06-14 00:01:01,798 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1S
2025-06-14 00:01:01,798 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19736.89, 'new_value': 21381.62}, {'field': 'offline_amount', 'old_value': 11107.07, 'new_value': 11606.45}, {'field': 'total_amount', 'old_value': 30843.96, 'new_value': 32988.07}, {'field': 'order_count', 'old_value': 1286, 'new_value': 1381}]
2025-06-14 00:01:01,798 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3S
2025-06-14 00:01:02,267 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3S
2025-06-14 00:01:02,267 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8511.29, 'new_value': 8921.15}, {'field': 'offline_amount', 'old_value': 6021.39, 'new_value': 6285.79}, {'field': 'total_amount', 'old_value': 14532.68, 'new_value': 15206.94}, {'field': 'order_count', 'old_value': 1129, 'new_value': 1198}]
2025-06-14 00:01:02,267 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5S
2025-06-14 00:01:02,720 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5S
2025-06-14 00:01:02,720 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 142870.0, 'new_value': 152173.0}, {'field': 'total_amount', 'old_value': 177870.0, 'new_value': 187173.0}, {'field': 'order_count', 'old_value': 5499, 'new_value': 5634}]
2025-06-14 00:01:02,720 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7S
2025-06-14 00:01:03,189 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7S
2025-06-14 00:01:03,189 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 326375.01, 'new_value': 341474.69}, {'field': 'total_amount', 'old_value': 326375.01, 'new_value': 341474.69}, {'field': 'order_count', 'old_value': 5458, 'new_value': 5703}]
2025-06-14 00:01:03,189 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8S
2025-06-14 00:01:03,673 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8S
2025-06-14 00:01:03,673 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20968.42, 'new_value': 22013.23}, {'field': 'offline_amount', 'old_value': 139727.36, 'new_value': 150564.59}, {'field': 'total_amount', 'old_value': 160695.78, 'new_value': 172577.82}, {'field': 'order_count', 'old_value': 3537, 'new_value': 3827}]
2025-06-14 00:01:03,673 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBS
2025-06-14 00:01:04,127 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBS
2025-06-14 00:01:04,127 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34730.0, 'new_value': 38914.0}, {'field': 'total_amount', 'old_value': 34730.0, 'new_value': 38914.0}, {'field': 'order_count', 'old_value': 189, 'new_value': 211}]
2025-06-14 00:01:04,127 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKS
2025-06-14 00:01:04,611 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKS
2025-06-14 00:01:04,611 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79265.8, 'new_value': 84108.2}, {'field': 'total_amount', 'old_value': 79265.8, 'new_value': 84108.2}, {'field': 'order_count', 'old_value': 1055, 'new_value': 1111}]
2025-06-14 00:01:04,611 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNS
2025-06-14 00:01:05,033 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNS
2025-06-14 00:01:05,033 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19047.0, 'new_value': 19781.0}, {'field': 'total_amount', 'old_value': 19266.0, 'new_value': 20000.0}, {'field': 'order_count', 'old_value': 71, 'new_value': 75}]
2025-06-14 00:01:05,033 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQS
2025-06-14 00:01:05,486 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQS
2025-06-14 00:01:05,486 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42577.5, 'new_value': 46642.16}, {'field': 'offline_amount', 'old_value': 151273.92, 'new_value': 161947.51}, {'field': 'total_amount', 'old_value': 193851.42, 'new_value': 208589.67}, {'field': 'order_count', 'old_value': 2128, 'new_value': 2256}]
2025-06-14 00:01:05,486 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSS
2025-06-14 00:01:05,908 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSS
2025-06-14 00:01:05,908 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26866.5, 'new_value': 28025.3}, {'field': 'offline_amount', 'old_value': 162904.76, 'new_value': 172516.47}, {'field': 'total_amount', 'old_value': 189771.26, 'new_value': 200541.77}, {'field': 'order_count', 'old_value': 1184, 'new_value': 1259}]
2025-06-14 00:01:05,908 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUS
2025-06-14 00:01:06,486 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUS
2025-06-14 00:01:06,486 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16568.86, 'new_value': 17568.86}, {'field': 'offline_amount', 'old_value': 18312.23, 'new_value': 20033.05}, {'field': 'total_amount', 'old_value': 34881.09, 'new_value': 37601.91}, {'field': 'order_count', 'old_value': 1606, 'new_value': 1740}]
2025-06-14 00:01:06,486 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWS
2025-06-14 00:01:06,923 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWS
2025-06-14 00:01:06,923 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39084.01, 'new_value': 43158.27}, {'field': 'offline_amount', 'old_value': 14778.27, 'new_value': 17058.42}, {'field': 'total_amount', 'old_value': 53862.28, 'new_value': 60216.69}, {'field': 'order_count', 'old_value': 3384, 'new_value': 3621}]
2025-06-14 00:01:06,923 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZS
2025-06-14 00:01:07,502 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZS
2025-06-14 00:01:07,502 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 230518.25, 'new_value': 248565.51}, {'field': 'total_amount', 'old_value': 230518.25, 'new_value': 248565.51}, {'field': 'order_count', 'old_value': 3348, 'new_value': 3609}]
2025-06-14 00:01:07,502 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2T
2025-06-14 00:01:08,002 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2T
2025-06-14 00:01:08,002 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13373.0, 'new_value': 14531.0}, {'field': 'total_amount', 'old_value': 13373.0, 'new_value': 14531.0}, {'field': 'order_count', 'old_value': 81, 'new_value': 89}]
2025-06-14 00:01:08,002 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3T
2025-06-14 00:01:08,455 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3T
2025-06-14 00:01:08,455 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21796.4, 'new_value': 22124.4}, {'field': 'total_amount', 'old_value': 22062.8, 'new_value': 22390.8}, {'field': 'order_count', 'old_value': 77, 'new_value': 79}]
2025-06-14 00:01:08,455 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9T
2025-06-14 00:01:08,908 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9T
2025-06-14 00:01:08,908 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37457.09, 'new_value': 40454.87}, {'field': 'offline_amount', 'old_value': 36450.32, 'new_value': 38189.7}, {'field': 'total_amount', 'old_value': 73907.41, 'new_value': 78644.57}, {'field': 'order_count', 'old_value': 3405, 'new_value': 3613}]
2025-06-14 00:01:08,908 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHT
2025-06-14 00:01:09,330 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHT
2025-06-14 00:01:09,330 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 168995.48, 'new_value': 171713.98}, {'field': 'offline_amount', 'old_value': 49549.48, 'new_value': 51008.5}, {'field': 'total_amount', 'old_value': 218544.96, 'new_value': 222722.48}, {'field': 'order_count', 'old_value': 438, 'new_value': 445}]
2025-06-14 00:01:09,330 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9K
2025-06-14 00:01:09,752 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9K
2025-06-14 00:01:09,752 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5612.03, 'new_value': 6180.1}, {'field': 'offline_amount', 'old_value': 14929.11, 'new_value': 15926.91}, {'field': 'total_amount', 'old_value': 20541.14, 'new_value': 22107.01}, {'field': 'order_count', 'old_value': 958, 'new_value': 1042}]
2025-06-14 00:01:09,752 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDK
2025-06-14 00:01:10,158 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDK
2025-06-14 00:01:10,158 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16433.71, 'new_value': 18321.2}, {'field': 'total_amount', 'old_value': 16433.71, 'new_value': 18321.2}, {'field': 'order_count', 'old_value': 71, 'new_value': 78}]
2025-06-14 00:01:10,158 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMMK
2025-06-14 00:01:10,564 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMMK
2025-06-14 00:01:10,564 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69124.0, 'new_value': 72260.0}, {'field': 'total_amount', 'old_value': 69124.0, 'new_value': 72260.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 14}]
2025-06-14 00:01:10,564 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSK
2025-06-14 00:01:11,002 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSK
2025-06-14 00:01:11,002 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12132.0, 'new_value': 13459.0}, {'field': 'total_amount', 'old_value': 12132.0, 'new_value': 13459.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 55}]
2025-06-14 00:01:11,002 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXK
2025-06-14 00:01:11,439 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXK
2025-06-14 00:01:11,439 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14326.72, 'new_value': 15091.62}, {'field': 'offline_amount', 'old_value': 97757.4, 'new_value': 103626.2}, {'field': 'total_amount', 'old_value': 112084.12, 'new_value': 118717.82}, {'field': 'order_count', 'old_value': 3478, 'new_value': 3731}]
2025-06-14 00:01:11,439 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGL
2025-06-14 00:01:11,877 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGL
2025-06-14 00:01:11,877 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57778.95, 'new_value': 57794.95}, {'field': 'offline_amount', 'old_value': 154943.77, 'new_value': 167806.77}, {'field': 'total_amount', 'old_value': 212722.72, 'new_value': 225601.72}, {'field': 'order_count', 'old_value': 1763, 'new_value': 1805}]
2025-06-14 00:01:11,877 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUL
2025-06-14 00:01:12,408 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUL
2025-06-14 00:01:12,408 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45629.0, 'new_value': 47702.0}, {'field': 'total_amount', 'old_value': 45629.0, 'new_value': 47702.0}, {'field': 'order_count', 'old_value': 1369, 'new_value': 1429}]
2025-06-14 00:01:12,408 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWL
2025-06-14 00:01:12,877 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWL
2025-06-14 00:01:12,877 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32315.2, 'new_value': 33060.1}, {'field': 'total_amount', 'old_value': 32883.1, 'new_value': 33628.0}, {'field': 'order_count', 'old_value': 229, 'new_value': 237}]
2025-06-14 00:01:12,877 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM3M
2025-06-14 00:01:13,345 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM3M
2025-06-14 00:01:13,345 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40721.0, 'new_value': 42975.0}, {'field': 'total_amount', 'old_value': 40721.0, 'new_value': 42975.0}, {'field': 'order_count', 'old_value': 335, 'new_value': 355}]
2025-06-14 00:01:13,345 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBM
2025-06-14 00:01:13,845 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBM
2025-06-14 00:01:13,845 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 93818.0, 'new_value': 95380.0}, {'field': 'total_amount', 'old_value': 93818.0, 'new_value': 95380.0}, {'field': 'order_count', 'old_value': 2993, 'new_value': 3043}]
2025-06-14 00:01:13,845 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPM
2025-06-14 00:01:14,298 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPM
2025-06-14 00:01:14,298 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35890.3, 'new_value': 40379.8}, {'field': 'offline_amount', 'old_value': 292446.2, 'new_value': 295446.2}, {'field': 'total_amount', 'old_value': 328336.5, 'new_value': 335826.0}, {'field': 'order_count', 'old_value': 1180, 'new_value': 1268}]
2025-06-14 00:01:14,298 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSM
2025-06-14 00:01:14,783 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSM
2025-06-14 00:01:14,783 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 152688.06, 'new_value': 162759.44}, {'field': 'total_amount', 'old_value': 152688.06, 'new_value': 162759.44}, {'field': 'order_count', 'old_value': 1364, 'new_value': 1472}]
2025-06-14 00:01:14,783 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB11
2025-06-14 00:01:15,252 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB11
2025-06-14 00:01:15,252 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 61225.6, 'new_value': 67662.4}, {'field': 'offline_amount', 'old_value': 100952.56, 'new_value': 107818.21}, {'field': 'total_amount', 'old_value': 162178.16, 'new_value': 175480.61}, {'field': 'order_count', 'old_value': 5258, 'new_value': 5749}]
2025-06-14 00:01:15,252 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ11
2025-06-14 00:01:15,720 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ11
2025-06-14 00:01:15,720 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72199.27, 'new_value': 79406.99}, {'field': 'total_amount', 'old_value': 94269.5, 'new_value': 101477.22}, {'field': 'order_count', 'old_value': 5253, 'new_value': 5691}]
2025-06-14 00:01:15,720 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT11
2025-06-14 00:01:16,173 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT11
2025-06-14 00:01:16,173 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34064.0, 'new_value': 36630.74}, {'field': 'offline_amount', 'old_value': 51556.3, 'new_value': 54675.7}, {'field': 'total_amount', 'old_value': 85620.3, 'new_value': 91306.44}, {'field': 'order_count', 'old_value': 950, 'new_value': 1016}]
2025-06-14 00:01:16,173 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA1
2025-06-14 00:01:16,627 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA1
2025-06-14 00:01:16,627 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-06-14 00:01:16,627 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME1
2025-06-14 00:01:17,033 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME1
2025-06-14 00:01:17,033 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 222919.0, 'new_value': 238595.0}, {'field': 'total_amount', 'old_value': 222919.0, 'new_value': 238595.0}, {'field': 'order_count', 'old_value': 1525, 'new_value': 1650}]
2025-06-14 00:01:17,033 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMT1
2025-06-14 00:01:17,470 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMT1
2025-06-14 00:01:17,470 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23473.3, 'new_value': 23910.4}, {'field': 'total_amount', 'old_value': 23473.3, 'new_value': 23910.4}, {'field': 'order_count', 'old_value': 187, 'new_value': 193}]
2025-06-14 00:01:17,470 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX1
2025-06-14 00:01:17,986 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX1
2025-06-14 00:01:17,986 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 51332.33, 'new_value': 60682.64}, {'field': 'total_amount', 'old_value': 149914.54, 'new_value': 159264.85}, {'field': 'order_count', 'old_value': 1083, 'new_value': 1180}]
2025-06-14 00:01:17,986 - INFO - 日期 2025-06 处理完成 - 更新: 82 条，插入: 0 条，错误: 0 条
2025-06-14 00:01:17,986 - INFO - 数据同步完成！更新: 82 条，插入: 0 条，错误: 0 条
2025-06-14 00:01:17,986 - INFO - =================同步完成====================
2025-06-14 03:00:02,665 - INFO - =================使用默认全量同步=============
2025-06-14 03:00:04,289 - INFO - MySQL查询成功，共获取 3930 条记录
2025-06-14 03:00:04,289 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-14 03:00:04,321 - INFO - 开始处理日期: 2025-01
2025-06-14 03:00:04,336 - INFO - Request Parameters - Page 1:
2025-06-14 03:00:04,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:04,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:05,821 - INFO - Response - Page 1:
2025-06-14 03:00:06,024 - INFO - 第 1 页获取到 100 条记录
2025-06-14 03:00:06,024 - INFO - Request Parameters - Page 2:
2025-06-14 03:00:06,024 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:06,024 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:06,555 - INFO - Response - Page 2:
2025-06-14 03:00:06,758 - INFO - 第 2 页获取到 100 条记录
2025-06-14 03:00:06,758 - INFO - Request Parameters - Page 3:
2025-06-14 03:00:06,758 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:06,758 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:07,274 - INFO - Response - Page 3:
2025-06-14 03:00:07,477 - INFO - 第 3 页获取到 100 条记录
2025-06-14 03:00:07,477 - INFO - Request Parameters - Page 4:
2025-06-14 03:00:07,477 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:07,477 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:08,024 - INFO - Response - Page 4:
2025-06-14 03:00:08,227 - INFO - 第 4 页获取到 100 条记录
2025-06-14 03:00:08,227 - INFO - Request Parameters - Page 5:
2025-06-14 03:00:08,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:08,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:08,774 - INFO - Response - Page 5:
2025-06-14 03:00:08,977 - INFO - 第 5 页获取到 100 条记录
2025-06-14 03:00:08,977 - INFO - Request Parameters - Page 6:
2025-06-14 03:00:08,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:08,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:09,477 - INFO - Response - Page 6:
2025-06-14 03:00:09,680 - INFO - 第 6 页获取到 100 条记录
2025-06-14 03:00:09,680 - INFO - Request Parameters - Page 7:
2025-06-14 03:00:09,680 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:09,680 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:10,164 - INFO - Response - Page 7:
2025-06-14 03:00:10,368 - INFO - 第 7 页获取到 82 条记录
2025-06-14 03:00:10,368 - INFO - 查询完成，共获取到 682 条记录
2025-06-14 03:00:10,368 - INFO - 获取到 682 条表单数据
2025-06-14 03:00:10,368 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-14 03:00:10,383 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 03:00:10,383 - INFO - 开始处理日期: 2025-02
2025-06-14 03:00:10,383 - INFO - Request Parameters - Page 1:
2025-06-14 03:00:10,383 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:10,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:10,852 - INFO - Response - Page 1:
2025-06-14 03:00:11,055 - INFO - 第 1 页获取到 100 条记录
2025-06-14 03:00:11,055 - INFO - Request Parameters - Page 2:
2025-06-14 03:00:11,055 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:11,055 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:11,539 - INFO - Response - Page 2:
2025-06-14 03:00:11,743 - INFO - 第 2 页获取到 100 条记录
2025-06-14 03:00:11,743 - INFO - Request Parameters - Page 3:
2025-06-14 03:00:11,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:11,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:12,352 - INFO - Response - Page 3:
2025-06-14 03:00:12,555 - INFO - 第 3 页获取到 100 条记录
2025-06-14 03:00:12,555 - INFO - Request Parameters - Page 4:
2025-06-14 03:00:12,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:12,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:13,133 - INFO - Response - Page 4:
2025-06-14 03:00:13,336 - INFO - 第 4 页获取到 100 条记录
2025-06-14 03:00:13,336 - INFO - Request Parameters - Page 5:
2025-06-14 03:00:13,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:13,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:13,914 - INFO - Response - Page 5:
2025-06-14 03:00:14,118 - INFO - 第 5 页获取到 100 条记录
2025-06-14 03:00:14,118 - INFO - Request Parameters - Page 6:
2025-06-14 03:00:14,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:14,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:14,586 - INFO - Response - Page 6:
2025-06-14 03:00:14,789 - INFO - 第 6 页获取到 100 条记录
2025-06-14 03:00:14,789 - INFO - Request Parameters - Page 7:
2025-06-14 03:00:14,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:14,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:15,258 - INFO - Response - Page 7:
2025-06-14 03:00:15,461 - INFO - 第 7 页获取到 70 条记录
2025-06-14 03:00:15,461 - INFO - 查询完成，共获取到 670 条记录
2025-06-14 03:00:15,461 - INFO - 获取到 670 条表单数据
2025-06-14 03:00:15,461 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-14 03:00:15,477 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 03:00:15,477 - INFO - 开始处理日期: 2025-03
2025-06-14 03:00:15,477 - INFO - Request Parameters - Page 1:
2025-06-14 03:00:15,477 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:15,477 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:16,118 - INFO - Response - Page 1:
2025-06-14 03:00:16,321 - INFO - 第 1 页获取到 100 条记录
2025-06-14 03:00:16,321 - INFO - Request Parameters - Page 2:
2025-06-14 03:00:16,321 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:16,321 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:16,836 - INFO - Response - Page 2:
2025-06-14 03:00:17,039 - INFO - 第 2 页获取到 100 条记录
2025-06-14 03:00:17,039 - INFO - Request Parameters - Page 3:
2025-06-14 03:00:17,039 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:17,039 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:17,789 - INFO - Response - Page 3:
2025-06-14 03:00:17,993 - INFO - 第 3 页获取到 100 条记录
2025-06-14 03:00:17,993 - INFO - Request Parameters - Page 4:
2025-06-14 03:00:17,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:17,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:18,508 - INFO - Response - Page 4:
2025-06-14 03:00:18,711 - INFO - 第 4 页获取到 100 条记录
2025-06-14 03:00:18,711 - INFO - Request Parameters - Page 5:
2025-06-14 03:00:18,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:18,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:19,196 - INFO - Response - Page 5:
2025-06-14 03:00:19,414 - INFO - 第 5 页获取到 100 条记录
2025-06-14 03:00:19,414 - INFO - Request Parameters - Page 6:
2025-06-14 03:00:19,414 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:19,414 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:19,883 - INFO - Response - Page 6:
2025-06-14 03:00:20,086 - INFO - 第 6 页获取到 100 条记录
2025-06-14 03:00:20,086 - INFO - Request Parameters - Page 7:
2025-06-14 03:00:20,086 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:20,086 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:20,571 - INFO - Response - Page 7:
2025-06-14 03:00:20,774 - INFO - 第 7 页获取到 61 条记录
2025-06-14 03:00:20,774 - INFO - 查询完成，共获取到 661 条记录
2025-06-14 03:00:20,774 - INFO - 获取到 661 条表单数据
2025-06-14 03:00:20,774 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-14 03:00:20,789 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 03:00:20,789 - INFO - 开始处理日期: 2025-04
2025-06-14 03:00:20,789 - INFO - Request Parameters - Page 1:
2025-06-14 03:00:20,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:20,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:21,352 - INFO - Response - Page 1:
2025-06-14 03:00:21,555 - INFO - 第 1 页获取到 100 条记录
2025-06-14 03:00:21,555 - INFO - Request Parameters - Page 2:
2025-06-14 03:00:21,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:21,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:22,024 - INFO - Response - Page 2:
2025-06-14 03:00:22,227 - INFO - 第 2 页获取到 100 条记录
2025-06-14 03:00:22,227 - INFO - Request Parameters - Page 3:
2025-06-14 03:00:22,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:22,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:22,696 - INFO - Response - Page 3:
2025-06-14 03:00:22,899 - INFO - 第 3 页获取到 100 条记录
2025-06-14 03:00:22,899 - INFO - Request Parameters - Page 4:
2025-06-14 03:00:22,899 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:22,899 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:23,446 - INFO - Response - Page 4:
2025-06-14 03:00:23,649 - INFO - 第 4 页获取到 100 条记录
2025-06-14 03:00:23,649 - INFO - Request Parameters - Page 5:
2025-06-14 03:00:23,649 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:23,649 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:24,118 - INFO - Response - Page 5:
2025-06-14 03:00:24,321 - INFO - 第 5 页获取到 100 条记录
2025-06-14 03:00:24,321 - INFO - Request Parameters - Page 6:
2025-06-14 03:00:24,321 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:24,321 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:24,789 - INFO - Response - Page 6:
2025-06-14 03:00:24,993 - INFO - 第 6 页获取到 100 条记录
2025-06-14 03:00:24,993 - INFO - Request Parameters - Page 7:
2025-06-14 03:00:24,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:24,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:25,399 - INFO - Response - Page 7:
2025-06-14 03:00:25,602 - INFO - 第 7 页获取到 56 条记录
2025-06-14 03:00:25,602 - INFO - 查询完成，共获取到 656 条记录
2025-06-14 03:00:25,602 - INFO - 获取到 656 条表单数据
2025-06-14 03:00:25,602 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-14 03:00:25,617 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 03:00:25,617 - INFO - 开始处理日期: 2025-05
2025-06-14 03:00:25,617 - INFO - Request Parameters - Page 1:
2025-06-14 03:00:25,617 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:25,617 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:26,133 - INFO - Response - Page 1:
2025-06-14 03:00:26,336 - INFO - 第 1 页获取到 100 条记录
2025-06-14 03:00:26,336 - INFO - Request Parameters - Page 2:
2025-06-14 03:00:26,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:26,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:26,852 - INFO - Response - Page 2:
2025-06-14 03:00:27,055 - INFO - 第 2 页获取到 100 条记录
2025-06-14 03:00:27,055 - INFO - Request Parameters - Page 3:
2025-06-14 03:00:27,055 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:27,055 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:27,696 - INFO - Response - Page 3:
2025-06-14 03:00:27,899 - INFO - 第 3 页获取到 100 条记录
2025-06-14 03:00:27,899 - INFO - Request Parameters - Page 4:
2025-06-14 03:00:27,899 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:27,899 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:28,399 - INFO - Response - Page 4:
2025-06-14 03:00:28,602 - INFO - 第 4 页获取到 100 条记录
2025-06-14 03:00:28,602 - INFO - Request Parameters - Page 5:
2025-06-14 03:00:28,602 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:28,602 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:29,055 - INFO - Response - Page 5:
2025-06-14 03:00:29,258 - INFO - 第 5 页获取到 100 条记录
2025-06-14 03:00:29,258 - INFO - Request Parameters - Page 6:
2025-06-14 03:00:29,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:29,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:29,742 - INFO - Response - Page 6:
2025-06-14 03:00:29,946 - INFO - 第 6 页获取到 100 条记录
2025-06-14 03:00:29,946 - INFO - Request Parameters - Page 7:
2025-06-14 03:00:29,946 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:29,946 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:30,367 - INFO - Response - Page 7:
2025-06-14 03:00:30,571 - INFO - 第 7 页获取到 40 条记录
2025-06-14 03:00:30,571 - INFO - 查询完成，共获取到 640 条记录
2025-06-14 03:00:30,571 - INFO - 获取到 640 条表单数据
2025-06-14 03:00:30,571 - INFO - 当前日期 2025-05 有 640 条MySQL数据需要处理
2025-06-14 03:00:30,586 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 03:00:30,586 - INFO - 开始处理日期: 2025-06
2025-06-14 03:00:30,586 - INFO - Request Parameters - Page 1:
2025-06-14 03:00:30,586 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:30,586 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:31,149 - INFO - Response - Page 1:
2025-06-14 03:00:31,352 - INFO - 第 1 页获取到 100 条记录
2025-06-14 03:00:31,352 - INFO - Request Parameters - Page 2:
2025-06-14 03:00:31,352 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:31,352 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:31,867 - INFO - Response - Page 2:
2025-06-14 03:00:32,071 - INFO - 第 2 页获取到 100 条记录
2025-06-14 03:00:32,071 - INFO - Request Parameters - Page 3:
2025-06-14 03:00:32,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:32,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:32,539 - INFO - Response - Page 3:
2025-06-14 03:00:32,742 - INFO - 第 3 页获取到 100 条记录
2025-06-14 03:00:32,742 - INFO - Request Parameters - Page 4:
2025-06-14 03:00:32,742 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:32,742 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:33,227 - INFO - Response - Page 4:
2025-06-14 03:00:33,430 - INFO - 第 4 页获取到 100 条记录
2025-06-14 03:00:33,430 - INFO - Request Parameters - Page 5:
2025-06-14 03:00:33,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:33,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:33,977 - INFO - Response - Page 5:
2025-06-14 03:00:34,180 - INFO - 第 5 页获取到 100 条记录
2025-06-14 03:00:34,180 - INFO - Request Parameters - Page 6:
2025-06-14 03:00:34,180 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:34,180 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:34,633 - INFO - Response - Page 6:
2025-06-14 03:00:34,836 - INFO - 第 6 页获取到 100 条记录
2025-06-14 03:00:34,836 - INFO - Request Parameters - Page 7:
2025-06-14 03:00:34,836 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 03:00:34,836 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 03:00:35,180 - INFO - Response - Page 7:
2025-06-14 03:00:35,383 - INFO - 第 7 页获取到 21 条记录
2025-06-14 03:00:35,383 - INFO - 查询完成，共获取到 621 条记录
2025-06-14 03:00:35,383 - INFO - 获取到 621 条表单数据
2025-06-14 03:00:35,383 - INFO - 当前日期 2025-06 有 621 条MySQL数据需要处理
2025-06-14 03:00:35,383 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM001
2025-06-14 03:00:35,914 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM001
2025-06-14 03:00:35,914 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 690888.1, 'new_value': 751666.43}, {'field': 'total_amount', 'old_value': 690888.1, 'new_value': 751666.43}, {'field': 'order_count', 'old_value': 7675, 'new_value': 8303}]
2025-06-14 03:00:35,914 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9C
2025-06-14 03:00:36,321 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9C
2025-06-14 03:00:36,336 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23149.51, 'new_value': 25432.39}, {'field': 'offline_amount', 'old_value': 318657.15, 'new_value': 350398.34}, {'field': 'total_amount', 'old_value': 341806.66, 'new_value': 375830.73}, {'field': 'order_count', 'old_value': 1445, 'new_value': 1579}]
2025-06-14 03:00:36,336 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYS
2025-06-14 03:00:36,805 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYS
2025-06-14 03:00:36,805 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18228.0, 'new_value': 20339.0}, {'field': 'offline_amount', 'old_value': 60199.46, 'new_value': 63570.46}, {'field': 'total_amount', 'old_value': 78427.46, 'new_value': 83909.46}, {'field': 'order_count', 'old_value': 115, 'new_value': 126}]
2025-06-14 03:00:36,805 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIL
2025-06-14 03:00:37,227 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIL
2025-06-14 03:00:37,227 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 349040.8, 'new_value': 380017.86}, {'field': 'total_amount', 'old_value': 349040.8, 'new_value': 380017.86}, {'field': 'order_count', 'old_value': 2169, 'new_value': 2394}]
2025-06-14 03:00:37,242 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM82
2025-06-14 03:00:37,696 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM82
2025-06-14 03:00:37,696 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6203.0, 'new_value': 31440.0}, {'field': 'total_amount', 'old_value': 135962.0, 'new_value': 161199.0}, {'field': 'order_count', 'old_value': 52, 'new_value': 53}]
2025-06-14 03:00:37,711 - INFO - 日期 2025-06 处理完成 - 更新: 5 条，插入: 0 条，错误: 0 条
2025-06-14 03:00:37,711 - INFO - 数据同步完成！更新: 5 条，插入: 0 条，错误: 0 条
2025-06-14 03:00:37,711 - INFO - =================同步完成====================
2025-06-14 06:00:03,233 - INFO - =================使用默认全量同步=============
2025-06-14 06:00:04,890 - INFO - MySQL查询成功，共获取 3930 条记录
2025-06-14 06:00:04,890 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-14 06:00:04,921 - INFO - 开始处理日期: 2025-01
2025-06-14 06:00:04,921 - INFO - Request Parameters - Page 1:
2025-06-14 06:00:04,921 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:04,921 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:06,452 - INFO - Response - Page 1:
2025-06-14 06:00:06,655 - INFO - 第 1 页获取到 100 条记录
2025-06-14 06:00:06,655 - INFO - Request Parameters - Page 2:
2025-06-14 06:00:06,655 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:06,655 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:07,264 - INFO - Response - Page 2:
2025-06-14 06:00:07,468 - INFO - 第 2 页获取到 100 条记录
2025-06-14 06:00:07,468 - INFO - Request Parameters - Page 3:
2025-06-14 06:00:07,468 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:07,468 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:07,983 - INFO - Response - Page 3:
2025-06-14 06:00:08,186 - INFO - 第 3 页获取到 100 条记录
2025-06-14 06:00:08,186 - INFO - Request Parameters - Page 4:
2025-06-14 06:00:08,186 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:08,186 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:08,718 - INFO - Response - Page 4:
2025-06-14 06:00:08,921 - INFO - 第 4 页获取到 100 条记录
2025-06-14 06:00:08,921 - INFO - Request Parameters - Page 5:
2025-06-14 06:00:08,921 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:08,921 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:09,436 - INFO - Response - Page 5:
2025-06-14 06:00:09,639 - INFO - 第 5 页获取到 100 条记录
2025-06-14 06:00:09,639 - INFO - Request Parameters - Page 6:
2025-06-14 06:00:09,639 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:09,639 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:10,093 - INFO - Response - Page 6:
2025-06-14 06:00:10,296 - INFO - 第 6 页获取到 100 条记录
2025-06-14 06:00:10,296 - INFO - Request Parameters - Page 7:
2025-06-14 06:00:10,296 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:10,296 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:10,827 - INFO - Response - Page 7:
2025-06-14 06:00:11,030 - INFO - 第 7 页获取到 82 条记录
2025-06-14 06:00:11,030 - INFO - 查询完成，共获取到 682 条记录
2025-06-14 06:00:11,030 - INFO - 获取到 682 条表单数据
2025-06-14 06:00:11,030 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-14 06:00:11,046 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 06:00:11,046 - INFO - 开始处理日期: 2025-02
2025-06-14 06:00:11,046 - INFO - Request Parameters - Page 1:
2025-06-14 06:00:11,046 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:11,046 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:11,608 - INFO - Response - Page 1:
2025-06-14 06:00:11,811 - INFO - 第 1 页获取到 100 条记录
2025-06-14 06:00:11,811 - INFO - Request Parameters - Page 2:
2025-06-14 06:00:11,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:11,811 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:12,311 - INFO - Response - Page 2:
2025-06-14 06:00:12,514 - INFO - 第 2 页获取到 100 条记录
2025-06-14 06:00:12,514 - INFO - Request Parameters - Page 3:
2025-06-14 06:00:12,514 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:12,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:13,014 - INFO - Response - Page 3:
2025-06-14 06:00:13,218 - INFO - 第 3 页获取到 100 条记录
2025-06-14 06:00:13,218 - INFO - Request Parameters - Page 4:
2025-06-14 06:00:13,218 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:13,218 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:13,749 - INFO - Response - Page 4:
2025-06-14 06:00:13,952 - INFO - 第 4 页获取到 100 条记录
2025-06-14 06:00:13,952 - INFO - Request Parameters - Page 5:
2025-06-14 06:00:13,952 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:13,952 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:14,421 - INFO - Response - Page 5:
2025-06-14 06:00:14,624 - INFO - 第 5 页获取到 100 条记录
2025-06-14 06:00:14,624 - INFO - Request Parameters - Page 6:
2025-06-14 06:00:14,624 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:14,624 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:15,155 - INFO - Response - Page 6:
2025-06-14 06:00:15,358 - INFO - 第 6 页获取到 100 条记录
2025-06-14 06:00:15,358 - INFO - Request Parameters - Page 7:
2025-06-14 06:00:15,358 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:15,358 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:15,764 - INFO - Response - Page 7:
2025-06-14 06:00:15,968 - INFO - 第 7 页获取到 70 条记录
2025-06-14 06:00:15,968 - INFO - 查询完成，共获取到 670 条记录
2025-06-14 06:00:15,968 - INFO - 获取到 670 条表单数据
2025-06-14 06:00:15,968 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-14 06:00:15,983 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 06:00:15,983 - INFO - 开始处理日期: 2025-03
2025-06-14 06:00:15,983 - INFO - Request Parameters - Page 1:
2025-06-14 06:00:15,983 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:15,983 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:16,530 - INFO - Response - Page 1:
2025-06-14 06:00:16,733 - INFO - 第 1 页获取到 100 条记录
2025-06-14 06:00:16,733 - INFO - Request Parameters - Page 2:
2025-06-14 06:00:16,733 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:16,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:17,233 - INFO - Response - Page 2:
2025-06-14 06:00:17,436 - INFO - 第 2 页获取到 100 条记录
2025-06-14 06:00:17,436 - INFO - Request Parameters - Page 3:
2025-06-14 06:00:17,436 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:17,436 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:17,921 - INFO - Response - Page 3:
2025-06-14 06:00:18,124 - INFO - 第 3 页获取到 100 条记录
2025-06-14 06:00:18,124 - INFO - Request Parameters - Page 4:
2025-06-14 06:00:18,124 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:18,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:18,624 - INFO - Response - Page 4:
2025-06-14 06:00:18,827 - INFO - 第 4 页获取到 100 条记录
2025-06-14 06:00:18,827 - INFO - Request Parameters - Page 5:
2025-06-14 06:00:18,827 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:18,827 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:19,296 - INFO - Response - Page 5:
2025-06-14 06:00:19,499 - INFO - 第 5 页获取到 100 条记录
2025-06-14 06:00:19,499 - INFO - Request Parameters - Page 6:
2025-06-14 06:00:19,499 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:19,499 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:19,983 - INFO - Response - Page 6:
2025-06-14 06:00:20,186 - INFO - 第 6 页获取到 100 条记录
2025-06-14 06:00:20,186 - INFO - Request Parameters - Page 7:
2025-06-14 06:00:20,186 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:20,186 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:20,671 - INFO - Response - Page 7:
2025-06-14 06:00:20,874 - INFO - 第 7 页获取到 61 条记录
2025-06-14 06:00:20,874 - INFO - 查询完成，共获取到 661 条记录
2025-06-14 06:00:20,874 - INFO - 获取到 661 条表单数据
2025-06-14 06:00:20,889 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-14 06:00:20,905 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 06:00:20,905 - INFO - 开始处理日期: 2025-04
2025-06-14 06:00:20,905 - INFO - Request Parameters - Page 1:
2025-06-14 06:00:20,905 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:20,905 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:21,436 - INFO - Response - Page 1:
2025-06-14 06:00:21,639 - INFO - 第 1 页获取到 100 条记录
2025-06-14 06:00:21,639 - INFO - Request Parameters - Page 2:
2025-06-14 06:00:21,639 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:21,639 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:22,139 - INFO - Response - Page 2:
2025-06-14 06:00:22,343 - INFO - 第 2 页获取到 100 条记录
2025-06-14 06:00:22,343 - INFO - Request Parameters - Page 3:
2025-06-14 06:00:22,343 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:22,343 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:22,843 - INFO - Response - Page 3:
2025-06-14 06:00:23,046 - INFO - 第 3 页获取到 100 条记录
2025-06-14 06:00:23,046 - INFO - Request Parameters - Page 4:
2025-06-14 06:00:23,046 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:23,046 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:23,530 - INFO - Response - Page 4:
2025-06-14 06:00:23,749 - INFO - 第 4 页获取到 100 条记录
2025-06-14 06:00:23,749 - INFO - Request Parameters - Page 5:
2025-06-14 06:00:23,749 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:23,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:24,343 - INFO - Response - Page 5:
2025-06-14 06:00:24,546 - INFO - 第 5 页获取到 100 条记录
2025-06-14 06:00:24,546 - INFO - Request Parameters - Page 6:
2025-06-14 06:00:24,546 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:24,546 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:25,061 - INFO - Response - Page 6:
2025-06-14 06:00:25,264 - INFO - 第 6 页获取到 100 条记录
2025-06-14 06:00:25,264 - INFO - Request Parameters - Page 7:
2025-06-14 06:00:25,264 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:25,264 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:25,686 - INFO - Response - Page 7:
2025-06-14 06:00:25,889 - INFO - 第 7 页获取到 56 条记录
2025-06-14 06:00:25,889 - INFO - 查询完成，共获取到 656 条记录
2025-06-14 06:00:25,889 - INFO - 获取到 656 条表单数据
2025-06-14 06:00:25,889 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-14 06:00:25,905 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 06:00:25,905 - INFO - 开始处理日期: 2025-05
2025-06-14 06:00:25,905 - INFO - Request Parameters - Page 1:
2025-06-14 06:00:25,905 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:25,905 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:26,436 - INFO - Response - Page 1:
2025-06-14 06:00:26,639 - INFO - 第 1 页获取到 100 条记录
2025-06-14 06:00:26,639 - INFO - Request Parameters - Page 2:
2025-06-14 06:00:26,639 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:26,639 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:27,186 - INFO - Response - Page 2:
2025-06-14 06:00:27,389 - INFO - 第 2 页获取到 100 条记录
2025-06-14 06:00:27,389 - INFO - Request Parameters - Page 3:
2025-06-14 06:00:27,389 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:27,389 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:27,921 - INFO - Response - Page 3:
2025-06-14 06:00:28,124 - INFO - 第 3 页获取到 100 条记录
2025-06-14 06:00:28,124 - INFO - Request Parameters - Page 4:
2025-06-14 06:00:28,124 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:28,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:28,717 - INFO - Response - Page 4:
2025-06-14 06:00:28,921 - INFO - 第 4 页获取到 100 条记录
2025-06-14 06:00:28,921 - INFO - Request Parameters - Page 5:
2025-06-14 06:00:28,921 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:28,921 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:29,467 - INFO - Response - Page 5:
2025-06-14 06:00:29,671 - INFO - 第 5 页获取到 100 条记录
2025-06-14 06:00:29,671 - INFO - Request Parameters - Page 6:
2025-06-14 06:00:29,671 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:29,671 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:30,186 - INFO - Response - Page 6:
2025-06-14 06:00:30,389 - INFO - 第 6 页获取到 100 条记录
2025-06-14 06:00:30,389 - INFO - Request Parameters - Page 7:
2025-06-14 06:00:30,389 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:30,389 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:30,749 - INFO - Response - Page 7:
2025-06-14 06:00:30,952 - INFO - 第 7 页获取到 40 条记录
2025-06-14 06:00:30,952 - INFO - 查询完成，共获取到 640 条记录
2025-06-14 06:00:30,952 - INFO - 获取到 640 条表单数据
2025-06-14 06:00:30,952 - INFO - 当前日期 2025-05 有 640 条MySQL数据需要处理
2025-06-14 06:00:30,967 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 06:00:30,967 - INFO - 开始处理日期: 2025-06
2025-06-14 06:00:30,967 - INFO - Request Parameters - Page 1:
2025-06-14 06:00:30,967 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:30,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:31,499 - INFO - Response - Page 1:
2025-06-14 06:00:31,702 - INFO - 第 1 页获取到 100 条记录
2025-06-14 06:00:31,702 - INFO - Request Parameters - Page 2:
2025-06-14 06:00:31,702 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:31,702 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:32,139 - INFO - Response - Page 2:
2025-06-14 06:00:32,342 - INFO - 第 2 页获取到 100 条记录
2025-06-14 06:00:32,342 - INFO - Request Parameters - Page 3:
2025-06-14 06:00:32,342 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:32,342 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:32,811 - INFO - Response - Page 3:
2025-06-14 06:00:33,014 - INFO - 第 3 页获取到 100 条记录
2025-06-14 06:00:33,014 - INFO - Request Parameters - Page 4:
2025-06-14 06:00:33,014 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:33,014 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:33,467 - INFO - Response - Page 4:
2025-06-14 06:00:33,671 - INFO - 第 4 页获取到 100 条记录
2025-06-14 06:00:33,671 - INFO - Request Parameters - Page 5:
2025-06-14 06:00:33,671 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:33,671 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:34,592 - INFO - Response - Page 5:
2025-06-14 06:00:34,796 - INFO - 第 5 页获取到 100 条记录
2025-06-14 06:00:34,796 - INFO - Request Parameters - Page 6:
2025-06-14 06:00:34,796 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:34,796 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:35,264 - INFO - Response - Page 6:
2025-06-14 06:00:35,467 - INFO - 第 6 页获取到 100 条记录
2025-06-14 06:00:35,467 - INFO - Request Parameters - Page 7:
2025-06-14 06:00:35,467 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 06:00:35,467 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 06:00:35,780 - INFO - Response - Page 7:
2025-06-14 06:00:35,983 - INFO - 第 7 页获取到 21 条记录
2025-06-14 06:00:35,983 - INFO - 查询完成，共获取到 621 条记录
2025-06-14 06:00:35,983 - INFO - 获取到 621 条表单数据
2025-06-14 06:00:35,983 - INFO - 当前日期 2025-06 有 621 条MySQL数据需要处理
2025-06-14 06:00:35,983 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMGX
2025-06-14 06:00:36,467 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMGX
2025-06-14 06:00:36,467 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 103275.0, 'new_value': 106202.0}, {'field': 'total_amount', 'old_value': 103275.0, 'new_value': 106202.0}, {'field': 'order_count', 'old_value': 1566, 'new_value': 1634}]
2025-06-14 06:00:36,467 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZR
2025-06-14 06:00:36,921 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZR
2025-06-14 06:00:36,921 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 193296.19, 'new_value': 201964.09}, {'field': 'total_amount', 'old_value': 193296.19, 'new_value': 201964.09}, {'field': 'order_count', 'old_value': 1186, 'new_value': 1233}]
2025-06-14 06:00:36,936 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8T
2025-06-14 06:00:37,358 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8T
2025-06-14 06:00:37,358 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25840.23, 'new_value': 37421.41}, {'field': 'total_amount', 'old_value': 127507.47, 'new_value': 139088.65}, {'field': 'order_count', 'old_value': 5606, 'new_value': 6129}]
2025-06-14 06:00:37,358 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEK
2025-06-14 06:00:37,842 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEK
2025-06-14 06:00:37,842 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8696.0, 'new_value': 9110.0}, {'field': 'total_amount', 'old_value': 8696.0, 'new_value': 9110.0}, {'field': 'order_count', 'old_value': 105, 'new_value': 109}]
2025-06-14 06:00:37,842 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJK
2025-06-14 06:00:38,296 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJK
2025-06-14 06:00:38,296 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28733.2, 'new_value': 30686.6}, {'field': 'offline_amount', 'old_value': 81484.1, 'new_value': 85443.0}, {'field': 'total_amount', 'old_value': 110217.3, 'new_value': 116129.6}, {'field': 'order_count', 'old_value': 2216, 'new_value': 2334}]
2025-06-14 06:00:38,296 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKK
2025-06-14 06:00:38,780 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKK
2025-06-14 06:00:38,780 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30189.9, 'new_value': 32650.91}, {'field': 'offline_amount', 'old_value': 110403.84, 'new_value': 117266.19}, {'field': 'total_amount', 'old_value': 140593.74, 'new_value': 149917.1}, {'field': 'order_count', 'old_value': 3089, 'new_value': 3357}]
2025-06-14 06:00:38,780 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9L
2025-06-14 06:00:39,233 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9L
2025-06-14 06:00:39,233 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53005.76, 'new_value': 57217.61}, {'field': 'offline_amount', 'old_value': 116846.23, 'new_value': 124831.21}, {'field': 'total_amount', 'old_value': 169851.99, 'new_value': 182048.82}, {'field': 'order_count', 'old_value': 5997, 'new_value': 6459}]
2025-06-14 06:00:39,233 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFL
2025-06-14 06:00:39,686 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFL
2025-06-14 06:00:39,686 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74673.0, 'new_value': 85533.0}, {'field': 'total_amount', 'old_value': 74673.0, 'new_value': 85533.0}, {'field': 'order_count', 'old_value': 74, 'new_value': 75}]
2025-06-14 06:00:39,686 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRL
2025-06-14 06:00:40,171 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRL
2025-06-14 06:00:40,171 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70570.46, 'new_value': 76075.53}, {'field': 'total_amount', 'old_value': 70570.46, 'new_value': 76075.53}, {'field': 'order_count', 'old_value': 3239, 'new_value': 3481}]
2025-06-14 06:00:40,171 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXL
2025-06-14 06:00:40,639 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXL
2025-06-14 06:00:40,639 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 101741.68, 'new_value': 110487.04}, {'field': 'total_amount', 'old_value': 101741.68, 'new_value': 110487.04}, {'field': 'order_count', 'old_value': 786, 'new_value': 874}]
2025-06-14 06:00:40,639 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM4M
2025-06-14 06:00:41,233 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM4M
2025-06-14 06:00:41,233 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 137176.75, 'new_value': 142668.69}, {'field': 'total_amount', 'old_value': 137176.75, 'new_value': 142668.69}, {'field': 'order_count', 'old_value': 2809, 'new_value': 2943}]
2025-06-14 06:00:41,233 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9M
2025-06-14 06:00:41,655 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9M
2025-06-14 06:00:41,655 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 198372.87, 'new_value': 214235.37}, {'field': 'total_amount', 'old_value': 198372.87, 'new_value': 214235.37}, {'field': 'order_count', 'old_value': 8116, 'new_value': 8759}]
2025-06-14 06:00:41,655 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTM
2025-06-14 06:00:42,092 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTM
2025-06-14 06:00:42,092 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24825.41, 'new_value': 27863.97}, {'field': 'offline_amount', 'old_value': 180684.96, 'new_value': 190948.86}, {'field': 'total_amount', 'old_value': 205510.37, 'new_value': 218812.83}, {'field': 'order_count', 'old_value': 1698, 'new_value': 1805}]
2025-06-14 06:00:42,092 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO01
2025-06-14 06:00:42,499 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO01
2025-06-14 06:00:42,499 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 153501.51, 'new_value': 165297.69}, {'field': 'offline_amount', 'old_value': 14129.94, 'new_value': 15265.59}, {'field': 'total_amount', 'old_value': 167631.45, 'new_value': 180563.28}, {'field': 'order_count', 'old_value': 6546, 'new_value': 6978}]
2025-06-14 06:00:42,499 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX01
2025-06-14 06:00:42,999 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX01
2025-06-14 06:00:42,999 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64279.0, 'new_value': 68615.0}, {'field': 'total_amount', 'old_value': 64279.0, 'new_value': 68615.0}, {'field': 'order_count', 'old_value': 106, 'new_value': 113}]
2025-06-14 06:00:42,999 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM011
2025-06-14 06:00:43,546 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM011
2025-06-14 06:00:43,546 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1848.0, 'new_value': 1976.0}, {'field': 'offline_amount', 'old_value': 14116.6, 'new_value': 14759.6}, {'field': 'total_amount', 'old_value': 15964.6, 'new_value': 16735.6}, {'field': 'order_count', 'old_value': 534, 'new_value': 559}]
2025-06-14 06:00:43,546 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC11
2025-06-14 06:00:44,171 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC11
2025-06-14 06:00:44,171 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 105113.97, 'new_value': 116226.73}, {'field': 'offline_amount', 'old_value': 149349.33, 'new_value': 160114.51}, {'field': 'total_amount', 'old_value': 254463.3, 'new_value': 276341.24}, {'field': 'order_count', 'old_value': 7910, 'new_value': 8681}]
2025-06-14 06:00:44,171 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS11
2025-06-14 06:00:44,608 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS11
2025-06-14 06:00:44,608 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67135.22, 'new_value': 72336.9}, {'field': 'offline_amount', 'old_value': 21178.81, 'new_value': 22235.39}, {'field': 'total_amount', 'old_value': 88314.03, 'new_value': 94572.29}, {'field': 'order_count', 'old_value': 5040, 'new_value': 5419}]
2025-06-14 06:00:44,608 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM91
2025-06-14 06:00:45,014 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM91
2025-06-14 06:00:45,014 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4939.79, 'new_value': 5250.49}, {'field': 'offline_amount', 'old_value': 37998.33, 'new_value': 39653.26}, {'field': 'total_amount', 'old_value': 42938.12, 'new_value': 44903.75}, {'field': 'order_count', 'old_value': 1377, 'new_value': 1434}]
2025-06-14 06:00:45,014 - INFO - 日期 2025-06 处理完成 - 更新: 19 条，插入: 0 条，错误: 0 条
2025-06-14 06:00:45,014 - INFO - 数据同步完成！更新: 19 条，插入: 0 条，错误: 0 条
2025-06-14 06:00:45,014 - INFO - =================同步完成====================
2025-06-14 09:00:02,646 - INFO - =================使用默认全量同步=============
2025-06-14 09:00:04,302 - INFO - MySQL查询成功，共获取 3930 条记录
2025-06-14 09:00:04,302 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-14 09:00:04,333 - INFO - 开始处理日期: 2025-01
2025-06-14 09:00:04,333 - INFO - Request Parameters - Page 1:
2025-06-14 09:00:04,333 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:04,333 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:05,818 - INFO - Response - Page 1:
2025-06-14 09:00:06,021 - INFO - 第 1 页获取到 100 条记录
2025-06-14 09:00:06,021 - INFO - Request Parameters - Page 2:
2025-06-14 09:00:06,021 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:06,021 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:06,583 - INFO - Response - Page 2:
2025-06-14 09:00:06,787 - INFO - 第 2 页获取到 100 条记录
2025-06-14 09:00:06,787 - INFO - Request Parameters - Page 3:
2025-06-14 09:00:06,787 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:06,787 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:07,287 - INFO - Response - Page 3:
2025-06-14 09:00:07,490 - INFO - 第 3 页获取到 100 条记录
2025-06-14 09:00:07,490 - INFO - Request Parameters - Page 4:
2025-06-14 09:00:07,490 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:07,490 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:08,068 - INFO - Response - Page 4:
2025-06-14 09:00:08,271 - INFO - 第 4 页获取到 100 条记录
2025-06-14 09:00:08,271 - INFO - Request Parameters - Page 5:
2025-06-14 09:00:08,271 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:08,271 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:08,771 - INFO - Response - Page 5:
2025-06-14 09:00:08,974 - INFO - 第 5 页获取到 100 条记录
2025-06-14 09:00:08,974 - INFO - Request Parameters - Page 6:
2025-06-14 09:00:08,974 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:08,974 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:09,552 - INFO - Response - Page 6:
2025-06-14 09:00:09,755 - INFO - 第 6 页获取到 100 条记录
2025-06-14 09:00:09,755 - INFO - Request Parameters - Page 7:
2025-06-14 09:00:09,755 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:09,755 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:10,287 - INFO - Response - Page 7:
2025-06-14 09:00:10,490 - INFO - 第 7 页获取到 82 条记录
2025-06-14 09:00:10,490 - INFO - 查询完成，共获取到 682 条记录
2025-06-14 09:00:10,490 - INFO - 获取到 682 条表单数据
2025-06-14 09:00:10,490 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-14 09:00:10,505 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 09:00:10,505 - INFO - 开始处理日期: 2025-02
2025-06-14 09:00:10,505 - INFO - Request Parameters - Page 1:
2025-06-14 09:00:10,505 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:10,505 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:11,021 - INFO - Response - Page 1:
2025-06-14 09:00:11,224 - INFO - 第 1 页获取到 100 条记录
2025-06-14 09:00:11,224 - INFO - Request Parameters - Page 2:
2025-06-14 09:00:11,224 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:11,224 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:11,771 - INFO - Response - Page 2:
2025-06-14 09:00:11,974 - INFO - 第 2 页获取到 100 条记录
2025-06-14 09:00:11,974 - INFO - Request Parameters - Page 3:
2025-06-14 09:00:11,974 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:11,974 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:12,474 - INFO - Response - Page 3:
2025-06-14 09:00:12,677 - INFO - 第 3 页获取到 100 条记录
2025-06-14 09:00:12,677 - INFO - Request Parameters - Page 4:
2025-06-14 09:00:12,677 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:12,677 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:13,193 - INFO - Response - Page 4:
2025-06-14 09:00:13,396 - INFO - 第 4 页获取到 100 条记录
2025-06-14 09:00:13,396 - INFO - Request Parameters - Page 5:
2025-06-14 09:00:13,396 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:13,396 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:14,115 - INFO - Response - Page 5:
2025-06-14 09:00:14,318 - INFO - 第 5 页获取到 100 条记录
2025-06-14 09:00:14,318 - INFO - Request Parameters - Page 6:
2025-06-14 09:00:14,318 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:14,318 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:14,880 - INFO - Response - Page 6:
2025-06-14 09:00:15,083 - INFO - 第 6 页获取到 100 条记录
2025-06-14 09:00:15,083 - INFO - Request Parameters - Page 7:
2025-06-14 09:00:15,083 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:15,083 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:15,537 - INFO - Response - Page 7:
2025-06-14 09:00:15,740 - INFO - 第 7 页获取到 70 条记录
2025-06-14 09:00:15,740 - INFO - 查询完成，共获取到 670 条记录
2025-06-14 09:00:15,740 - INFO - 获取到 670 条表单数据
2025-06-14 09:00:15,740 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-14 09:00:15,755 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 09:00:15,755 - INFO - 开始处理日期: 2025-03
2025-06-14 09:00:15,755 - INFO - Request Parameters - Page 1:
2025-06-14 09:00:15,755 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:15,755 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:16,349 - INFO - Response - Page 1:
2025-06-14 09:00:16,552 - INFO - 第 1 页获取到 100 条记录
2025-06-14 09:00:16,552 - INFO - Request Parameters - Page 2:
2025-06-14 09:00:16,552 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:16,552 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:17,115 - INFO - Response - Page 2:
2025-06-14 09:00:17,318 - INFO - 第 2 页获取到 100 条记录
2025-06-14 09:00:17,318 - INFO - Request Parameters - Page 3:
2025-06-14 09:00:17,318 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:17,318 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:17,865 - INFO - Response - Page 3:
2025-06-14 09:00:18,068 - INFO - 第 3 页获取到 100 条记录
2025-06-14 09:00:18,068 - INFO - Request Parameters - Page 4:
2025-06-14 09:00:18,068 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:18,068 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:18,583 - INFO - Response - Page 4:
2025-06-14 09:00:18,786 - INFO - 第 4 页获取到 100 条记录
2025-06-14 09:00:18,786 - INFO - Request Parameters - Page 5:
2025-06-14 09:00:18,786 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:18,786 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:19,318 - INFO - Response - Page 5:
2025-06-14 09:00:19,521 - INFO - 第 5 页获取到 100 条记录
2025-06-14 09:00:19,521 - INFO - Request Parameters - Page 6:
2025-06-14 09:00:19,521 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:19,521 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:20,021 - INFO - Response - Page 6:
2025-06-14 09:00:20,224 - INFO - 第 6 页获取到 100 条记录
2025-06-14 09:00:20,224 - INFO - Request Parameters - Page 7:
2025-06-14 09:00:20,224 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:20,224 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:20,693 - INFO - Response - Page 7:
2025-06-14 09:00:20,896 - INFO - 第 7 页获取到 61 条记录
2025-06-14 09:00:20,896 - INFO - 查询完成，共获取到 661 条记录
2025-06-14 09:00:20,896 - INFO - 获取到 661 条表单数据
2025-06-14 09:00:20,896 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-14 09:00:20,911 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 09:00:20,911 - INFO - 开始处理日期: 2025-04
2025-06-14 09:00:20,911 - INFO - Request Parameters - Page 1:
2025-06-14 09:00:20,911 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:20,911 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:21,427 - INFO - Response - Page 1:
2025-06-14 09:00:21,630 - INFO - 第 1 页获取到 100 条记录
2025-06-14 09:00:21,630 - INFO - Request Parameters - Page 2:
2025-06-14 09:00:21,630 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:21,630 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:22,271 - INFO - Response - Page 2:
2025-06-14 09:00:22,474 - INFO - 第 2 页获取到 100 条记录
2025-06-14 09:00:22,474 - INFO - Request Parameters - Page 3:
2025-06-14 09:00:22,474 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:22,474 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:22,990 - INFO - Response - Page 3:
2025-06-14 09:00:23,193 - INFO - 第 3 页获取到 100 条记录
2025-06-14 09:00:23,193 - INFO - Request Parameters - Page 4:
2025-06-14 09:00:23,193 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:23,193 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:23,646 - INFO - Response - Page 4:
2025-06-14 09:00:23,849 - INFO - 第 4 页获取到 100 条记录
2025-06-14 09:00:23,849 - INFO - Request Parameters - Page 5:
2025-06-14 09:00:23,849 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:23,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:24,411 - INFO - Response - Page 5:
2025-06-14 09:00:24,615 - INFO - 第 5 页获取到 100 条记录
2025-06-14 09:00:24,615 - INFO - Request Parameters - Page 6:
2025-06-14 09:00:24,615 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:24,615 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:25,052 - INFO - Response - Page 6:
2025-06-14 09:00:25,255 - INFO - 第 6 页获取到 100 条记录
2025-06-14 09:00:25,255 - INFO - Request Parameters - Page 7:
2025-06-14 09:00:25,255 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:25,255 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:25,693 - INFO - Response - Page 7:
2025-06-14 09:00:25,896 - INFO - 第 7 页获取到 56 条记录
2025-06-14 09:00:25,896 - INFO - 查询完成，共获取到 656 条记录
2025-06-14 09:00:25,896 - INFO - 获取到 656 条表单数据
2025-06-14 09:00:25,896 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-14 09:00:25,911 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 09:00:25,911 - INFO - 开始处理日期: 2025-05
2025-06-14 09:00:25,911 - INFO - Request Parameters - Page 1:
2025-06-14 09:00:25,911 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:25,911 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:26,474 - INFO - Response - Page 1:
2025-06-14 09:00:26,677 - INFO - 第 1 页获取到 100 条记录
2025-06-14 09:00:26,677 - INFO - Request Parameters - Page 2:
2025-06-14 09:00:26,677 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:26,677 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:27,161 - INFO - Response - Page 2:
2025-06-14 09:00:27,365 - INFO - 第 2 页获取到 100 条记录
2025-06-14 09:00:27,365 - INFO - Request Parameters - Page 3:
2025-06-14 09:00:27,365 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:27,365 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:27,833 - INFO - Response - Page 3:
2025-06-14 09:00:28,036 - INFO - 第 3 页获取到 100 条记录
2025-06-14 09:00:28,036 - INFO - Request Parameters - Page 4:
2025-06-14 09:00:28,036 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:28,036 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:28,568 - INFO - Response - Page 4:
2025-06-14 09:00:28,771 - INFO - 第 4 页获取到 100 条记录
2025-06-14 09:00:28,771 - INFO - Request Parameters - Page 5:
2025-06-14 09:00:28,771 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:28,771 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:29,224 - INFO - Response - Page 5:
2025-06-14 09:00:29,427 - INFO - 第 5 页获取到 100 条记录
2025-06-14 09:00:29,427 - INFO - Request Parameters - Page 6:
2025-06-14 09:00:29,427 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:29,427 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:30,036 - INFO - Response - Page 6:
2025-06-14 09:00:30,240 - INFO - 第 6 页获取到 100 条记录
2025-06-14 09:00:30,240 - INFO - Request Parameters - Page 7:
2025-06-14 09:00:30,240 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:30,240 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:30,599 - INFO - Response - Page 7:
2025-06-14 09:00:30,802 - INFO - 第 7 页获取到 40 条记录
2025-06-14 09:00:30,802 - INFO - 查询完成，共获取到 640 条记录
2025-06-14 09:00:30,802 - INFO - 获取到 640 条表单数据
2025-06-14 09:00:30,802 - INFO - 当前日期 2025-05 有 640 条MySQL数据需要处理
2025-06-14 09:00:30,818 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 09:00:30,818 - INFO - 开始处理日期: 2025-06
2025-06-14 09:00:30,818 - INFO - Request Parameters - Page 1:
2025-06-14 09:00:30,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:30,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:31,333 - INFO - Response - Page 1:
2025-06-14 09:00:31,536 - INFO - 第 1 页获取到 100 条记录
2025-06-14 09:00:31,536 - INFO - Request Parameters - Page 2:
2025-06-14 09:00:31,536 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:31,536 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:32,005 - INFO - Response - Page 2:
2025-06-14 09:00:32,208 - INFO - 第 2 页获取到 100 条记录
2025-06-14 09:00:32,208 - INFO - Request Parameters - Page 3:
2025-06-14 09:00:32,208 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:32,208 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:32,646 - INFO - Response - Page 3:
2025-06-14 09:00:32,849 - INFO - 第 3 页获取到 100 条记录
2025-06-14 09:00:32,849 - INFO - Request Parameters - Page 4:
2025-06-14 09:00:32,849 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:32,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:33,286 - INFO - Response - Page 4:
2025-06-14 09:00:33,489 - INFO - 第 4 页获取到 100 条记录
2025-06-14 09:00:33,489 - INFO - Request Parameters - Page 5:
2025-06-14 09:00:33,489 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:33,489 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:34,036 - INFO - Response - Page 5:
2025-06-14 09:00:34,239 - INFO - 第 5 页获取到 100 条记录
2025-06-14 09:00:34,239 - INFO - Request Parameters - Page 6:
2025-06-14 09:00:34,239 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:34,239 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:34,786 - INFO - Response - Page 6:
2025-06-14 09:00:34,990 - INFO - 第 6 页获取到 100 条记录
2025-06-14 09:00:34,990 - INFO - Request Parameters - Page 7:
2025-06-14 09:00:34,990 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 09:00:34,990 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 09:00:35,302 - INFO - Response - Page 7:
2025-06-14 09:00:35,505 - INFO - 第 7 页获取到 21 条记录
2025-06-14 09:00:35,505 - INFO - 查询完成，共获取到 621 条记录
2025-06-14 09:00:35,505 - INFO - 获取到 621 条表单数据
2025-06-14 09:00:35,505 - INFO - 当前日期 2025-06 有 621 条MySQL数据需要处理
2025-06-14 09:00:35,505 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGZ
2025-06-14 09:00:36,005 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGZ
2025-06-14 09:00:36,005 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 396706.0, 'new_value': 429680.0}, {'field': 'total_amount', 'old_value': 396706.0, 'new_value': 429680.0}, {'field': 'order_count', 'old_value': 91, 'new_value': 96}]
2025-06-14 09:00:36,005 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM401
2025-06-14 09:00:36,536 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM401
2025-06-14 09:00:36,536 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33534.48, 'new_value': 35644.29}, {'field': 'offline_amount', 'old_value': 43363.56, 'new_value': 46585.72}, {'field': 'total_amount', 'old_value': 76898.04, 'new_value': 82230.01}, {'field': 'order_count', 'old_value': 2570, 'new_value': 2763}]
2025-06-14 09:00:36,536 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMPB
2025-06-14 09:00:36,974 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMPB
2025-06-14 09:00:36,974 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34419.0, 'new_value': 36219.0}, {'field': 'total_amount', 'old_value': 35069.0, 'new_value': 36869.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-06-14 09:00:36,974 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTB
2025-06-14 09:00:37,443 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTB
2025-06-14 09:00:37,443 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 336092.0, 'new_value': 354385.0}, {'field': 'total_amount', 'old_value': 336848.0, 'new_value': 355141.0}, {'field': 'order_count', 'old_value': 98, 'new_value': 106}]
2025-06-14 09:00:37,443 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLC
2025-06-14 09:00:37,833 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLC
2025-06-14 09:00:37,833 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 213720.0, 'new_value': 227720.0}, {'field': 'total_amount', 'old_value': 217636.0, 'new_value': 231636.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 35}]
2025-06-14 09:00:37,833 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMPC
2025-06-14 09:00:38,255 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMPC
2025-06-14 09:00:38,255 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84371.5, 'new_value': 88271.5}, {'field': 'total_amount', 'old_value': 84371.5, 'new_value': 88271.5}, {'field': 'order_count', 'old_value': 28, 'new_value': 29}]
2025-06-14 09:00:38,255 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWC
2025-06-14 09:00:38,693 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWC
2025-06-14 09:00:38,693 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16591.0, 'new_value': 16779.0}, {'field': 'total_amount', 'old_value': 16591.0, 'new_value': 16779.0}, {'field': 'order_count', 'old_value': 37, 'new_value': 38}]
2025-06-14 09:00:38,693 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYC
2025-06-14 09:00:39,114 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYC
2025-06-14 09:00:39,114 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 165398.88, 'new_value': 174605.12}, {'field': 'total_amount', 'old_value': 165398.88, 'new_value': 174605.12}, {'field': 'order_count', 'old_value': 805, 'new_value': 859}]
2025-06-14 09:00:39,114 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM2D
2025-06-14 09:00:39,568 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM2D
2025-06-14 09:00:39,568 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75689.0, 'new_value': 78813.0}, {'field': 'total_amount', 'old_value': 79595.2, 'new_value': 82719.2}, {'field': 'order_count', 'old_value': 41, 'new_value': 48}]
2025-06-14 09:00:39,568 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM4D
2025-06-14 09:00:40,005 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM4D
2025-06-14 09:00:40,005 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3463.0, 'new_value': 4631.0}, {'field': 'offline_amount', 'old_value': 26847.0, 'new_value': 29938.0}, {'field': 'total_amount', 'old_value': 30310.0, 'new_value': 34569.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 29}]
2025-06-14 09:00:40,005 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5D
2025-06-14 09:00:40,505 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5D
2025-06-14 09:00:40,505 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23037.0, 'new_value': 25583.0}, {'field': 'total_amount', 'old_value': 23037.0, 'new_value': 25583.0}, {'field': 'order_count', 'old_value': 67, 'new_value': 71}]
2025-06-14 09:00:40,505 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCD
2025-06-14 09:00:41,146 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCD
2025-06-14 09:00:41,146 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22232.85, 'new_value': 24141.2}, {'field': 'offline_amount', 'old_value': 229904.46, 'new_value': 244387.39}, {'field': 'total_amount', 'old_value': 252137.31, 'new_value': 268528.59}, {'field': 'order_count', 'old_value': 1587, 'new_value': 1802}]
2025-06-14 09:00:41,146 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMED
2025-06-14 09:00:41,646 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMED
2025-06-14 09:00:41,646 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 114567.0, 'new_value': 118894.53}, {'field': 'total_amount', 'old_value': 114567.0, 'new_value': 118894.53}, {'field': 'order_count', 'old_value': 61, 'new_value': 63}]
2025-06-14 09:00:41,646 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLD
2025-06-14 09:00:42,099 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLD
2025-06-14 09:00:42,099 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22655.0, 'new_value': 24537.0}, {'field': 'total_amount', 'old_value': 22655.0, 'new_value': 24537.0}, {'field': 'order_count', 'old_value': 35, 'new_value': 38}]
2025-06-14 09:00:42,099 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXD
2025-06-14 09:00:42,552 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXD
2025-06-14 09:00:42,552 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13183.0, 'new_value': 18583.0}, {'field': 'total_amount', 'old_value': 13183.0, 'new_value': 18583.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 11}]
2025-06-14 09:00:42,552 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYD
2025-06-14 09:00:43,052 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYD
2025-06-14 09:00:43,052 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25805.7, 'new_value': 27695.83}, {'field': 'offline_amount', 'old_value': 148184.41, 'new_value': 159136.81}, {'field': 'total_amount', 'old_value': 173990.11, 'new_value': 186832.64}, {'field': 'order_count', 'old_value': 1235, 'new_value': 1281}]
2025-06-14 09:00:43,052 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5R
2025-06-14 09:00:43,536 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5R
2025-06-14 09:00:43,536 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 153102.8, 'new_value': 161797.9}, {'field': 'total_amount', 'old_value': 153102.8, 'new_value': 161797.9}, {'field': 'order_count', 'old_value': 1508, 'new_value': 1603}]
2025-06-14 09:00:43,536 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGR
2025-06-14 09:00:43,989 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGR
2025-06-14 09:00:43,989 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 468681.0, 'new_value': 513544.0}, {'field': 'total_amount', 'old_value': 468681.0, 'new_value': 513544.0}, {'field': 'order_count', 'old_value': 499, 'new_value': 529}]
2025-06-14 09:00:43,989 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMJR
2025-06-14 09:00:44,458 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMJR
2025-06-14 09:00:44,458 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40199.0, 'new_value': 41033.0}, {'field': 'total_amount', 'old_value': 40199.0, 'new_value': 41033.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-06-14 09:00:44,458 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLR
2025-06-14 09:00:44,911 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLR
2025-06-14 09:00:44,911 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50538.79, 'new_value': 54122.79}, {'field': 'total_amount', 'old_value': 50538.79, 'new_value': 54122.79}, {'field': 'order_count', 'old_value': 4147, 'new_value': 4484}]
2025-06-14 09:00:44,911 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPR
2025-06-14 09:00:45,411 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPR
2025-06-14 09:00:45,411 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 180620.54, 'new_value': 201103.55}, {'field': 'total_amount', 'old_value': 256415.02, 'new_value': 276898.03}, {'field': 'order_count', 'old_value': 862, 'new_value': 914}]
2025-06-14 09:00:45,411 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMRR
2025-06-14 09:00:45,833 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMRR
2025-06-14 09:00:45,833 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56287.0, 'new_value': 63187.0}, {'field': 'total_amount', 'old_value': 56288.0, 'new_value': 63188.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-06-14 09:00:45,833 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCS
2025-06-14 09:00:46,349 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCS
2025-06-14 09:00:46,349 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 175127.0, 'new_value': 199275.0}, {'field': 'total_amount', 'old_value': 175127.0, 'new_value': 199275.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 52}]
2025-06-14 09:00:46,349 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDS
2025-06-14 09:00:46,786 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDS
2025-06-14 09:00:46,786 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11502.79, 'new_value': 12398.58}, {'field': 'offline_amount', 'old_value': 123316.75, 'new_value': 130551.02}, {'field': 'total_amount', 'old_value': 134819.54, 'new_value': 142949.6}, {'field': 'order_count', 'old_value': 647, 'new_value': 682}]
2025-06-14 09:00:46,802 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMES
2025-06-14 09:00:47,333 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMES
2025-06-14 09:00:47,333 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69035.8, 'new_value': 71469.6}, {'field': 'total_amount', 'old_value': 69035.8, 'new_value': 71469.6}, {'field': 'order_count', 'old_value': 159, 'new_value': 166}]
2025-06-14 09:00:47,333 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGS
2025-06-14 09:00:47,818 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGS
2025-06-14 09:00:47,818 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 121706.26, 'new_value': 131279.67}, {'field': 'offline_amount', 'old_value': 38814.75, 'new_value': 40768.39}, {'field': 'total_amount', 'old_value': 160521.01, 'new_value': 172048.06}, {'field': 'order_count', 'old_value': 988, 'new_value': 1074}]
2025-06-14 09:00:47,818 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHS
2025-06-14 09:00:48,349 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHS
2025-06-14 09:00:48,349 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64300.0, 'new_value': 68214.0}, {'field': 'total_amount', 'old_value': 68867.0, 'new_value': 72781.0}, {'field': 'order_count', 'old_value': 115, 'new_value': 121}]
2025-06-14 09:00:48,349 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGT
2025-06-14 09:00:48,786 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGT
2025-06-14 09:00:48,786 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42236.6, 'new_value': 47924.4}, {'field': 'offline_amount', 'old_value': 123536.62, 'new_value': 129357.76}, {'field': 'total_amount', 'old_value': 165773.22, 'new_value': 177282.16}, {'field': 'order_count', 'old_value': 2133, 'new_value': 2249}]
2025-06-14 09:00:48,786 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIT
2025-06-14 09:00:49,255 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIT
2025-06-14 09:00:49,255 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2797.31, 'new_value': 3008.01}, {'field': 'total_amount', 'old_value': 15537.31, 'new_value': 15748.01}, {'field': 'order_count', 'old_value': 50, 'new_value': 53}]
2025-06-14 09:00:49,255 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCK
2025-06-14 09:00:49,739 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCK
2025-06-14 09:00:49,739 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104895.0, 'new_value': 109845.0}, {'field': 'total_amount', 'old_value': 104895.0, 'new_value': 109845.0}, {'field': 'order_count', 'old_value': 110, 'new_value': 115}]
2025-06-14 09:00:49,739 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOK
2025-06-14 09:00:50,161 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOK
2025-06-14 09:00:50,161 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 200007.0, 'new_value': 217064.0}, {'field': 'total_amount', 'old_value': 200007.0, 'new_value': 217064.0}, {'field': 'order_count', 'old_value': 40, 'new_value': 46}]
2025-06-14 09:00:50,161 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRK
2025-06-14 09:00:50,677 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRK
2025-06-14 09:00:50,677 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49760.0, 'new_value': 50697.0}, {'field': 'offline_amount', 'old_value': 31400.6, 'new_value': 32446.4}, {'field': 'total_amount', 'old_value': 81160.6, 'new_value': 83143.4}, {'field': 'order_count', 'old_value': 523, 'new_value': 541}]
2025-06-14 09:00:50,677 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0L
2025-06-14 09:00:51,208 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0L
2025-06-14 09:00:51,208 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18305.29, 'new_value': 18351.14}, {'field': 'total_amount', 'old_value': 18305.29, 'new_value': 18351.14}, {'field': 'order_count', 'old_value': 84, 'new_value': 85}]
2025-06-14 09:00:51,208 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8L
2025-06-14 09:00:51,692 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8L
2025-06-14 09:00:51,692 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 118105.28, 'new_value': 129088.5}, {'field': 'offline_amount', 'old_value': 323277.03, 'new_value': 341371.44}, {'field': 'total_amount', 'old_value': 441382.31, 'new_value': 470459.94}, {'field': 'order_count', 'old_value': 2715, 'new_value': 2927}]
2025-06-14 09:00:51,692 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEL
2025-06-14 09:00:52,177 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEL
2025-06-14 09:00:52,177 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106909.37, 'new_value': 113106.13}, {'field': 'total_amount', 'old_value': 106909.37, 'new_value': 113106.13}, {'field': 'order_count', 'old_value': 631, 'new_value': 669}]
2025-06-14 09:00:52,177 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNL
2025-06-14 09:00:52,630 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNL
2025-06-14 09:00:52,630 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18678.02, 'new_value': 20083.98}, {'field': 'offline_amount', 'old_value': 21751.45, 'new_value': 23160.27}, {'field': 'total_amount', 'old_value': 40429.47, 'new_value': 43244.25}, {'field': 'order_count', 'old_value': 3503, 'new_value': 3744}]
2025-06-14 09:00:52,630 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOL
2025-06-14 09:00:53,036 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOL
2025-06-14 09:00:53,036 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18710.42, 'new_value': 20015.22}, {'field': 'offline_amount', 'old_value': 505332.4, 'new_value': 546999.72}, {'field': 'total_amount', 'old_value': 524042.82, 'new_value': 567014.94}, {'field': 'order_count', 'old_value': 2372, 'new_value': 2592}]
2025-06-14 09:00:53,036 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSL
2025-06-14 09:00:53,521 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSL
2025-06-14 09:00:53,521 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43014.5, 'new_value': 45035.8}, {'field': 'total_amount', 'old_value': 43014.5, 'new_value': 45035.8}, {'field': 'order_count', 'old_value': 96, 'new_value': 103}]
2025-06-14 09:00:53,521 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFM
2025-06-14 09:00:54,052 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFM
2025-06-14 09:00:54,052 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 138009.93, 'new_value': 147654.36}, {'field': 'offline_amount', 'old_value': 665997.2, 'new_value': 714752.17}, {'field': 'total_amount', 'old_value': 804007.13, 'new_value': 862406.53}, {'field': 'order_count', 'old_value': 3860, 'new_value': 4160}]
2025-06-14 09:00:54,052 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIM
2025-06-14 09:00:54,489 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIM
2025-06-14 09:00:54,489 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 506939.0, 'new_value': 525221.0}, {'field': 'total_amount', 'old_value': 506939.0, 'new_value': 525221.0}, {'field': 'order_count', 'old_value': 293, 'new_value': 299}]
2025-06-14 09:00:54,489 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLM
2025-06-14 09:00:55,114 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLM
2025-06-14 09:00:55,114 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 187319.85, 'new_value': 212463.95}, {'field': 'total_amount', 'old_value': 187319.85, 'new_value': 212463.95}, {'field': 'order_count', 'old_value': 702, 'new_value': 867}]
2025-06-14 09:00:55,114 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOM
2025-06-14 09:00:55,567 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOM
2025-06-14 09:00:55,567 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 108992.5, 'new_value': 114355.8}, {'field': 'total_amount', 'old_value': 108992.5, 'new_value': 114355.8}, {'field': 'order_count', 'old_value': 148, 'new_value': 159}]
2025-06-14 09:00:55,567 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRM
2025-06-14 09:00:56,005 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRM
2025-06-14 09:00:56,005 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 753922.0, 'new_value': 811566.0}, {'field': 'total_amount', 'old_value': 753922.0, 'new_value': 811566.0}, {'field': 'order_count', 'old_value': 3462, 'new_value': 3710}]
2025-06-14 09:00:56,005 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWM
2025-06-14 09:00:56,427 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWM
2025-06-14 09:00:56,427 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 150308.79, 'new_value': 157305.79}, {'field': 'total_amount', 'old_value': 164303.79, 'new_value': 171300.79}, {'field': 'order_count', 'old_value': 34, 'new_value': 37}]
2025-06-14 09:00:56,427 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP01
2025-06-14 09:00:56,896 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP01
2025-06-14 09:00:56,896 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 62881.9, 'new_value': 67789.8}, {'field': 'offline_amount', 'old_value': 49855.8, 'new_value': 53248.6}, {'field': 'total_amount', 'old_value': 112737.7, 'new_value': 121038.4}, {'field': 'order_count', 'old_value': 2693, 'new_value': 2896}]
2025-06-14 09:00:56,896 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ01
2025-06-14 09:00:57,380 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ01
2025-06-14 09:00:57,380 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5400000.0, 'new_value': 5800000.0}, {'field': 'total_amount', 'old_value': 5400000.0, 'new_value': 5800000.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 30}]
2025-06-14 09:00:57,380 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD11
2025-06-14 09:00:57,911 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD11
2025-06-14 09:00:57,911 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32767.85, 'new_value': 35820.8}, {'field': 'total_amount', 'old_value': 32767.85, 'new_value': 35820.8}, {'field': 'order_count', 'old_value': 645, 'new_value': 677}]
2025-06-14 09:00:57,911 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML11
2025-06-14 09:00:58,333 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML11
2025-06-14 09:00:58,333 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 133532.24, 'new_value': 137766.24}, {'field': 'offline_amount', 'old_value': 2027.0, 'new_value': 2062.0}, {'field': 'total_amount', 'old_value': 135559.24, 'new_value': 139828.24}, {'field': 'order_count', 'old_value': 10615, 'new_value': 10850}]
2025-06-14 09:00:58,333 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP11
2025-06-14 09:00:58,786 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP11
2025-06-14 09:00:58,786 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34326.0, 'new_value': 35943.0}, {'field': 'total_amount', 'old_value': 46913.0, 'new_value': 48530.0}, {'field': 'order_count', 'old_value': 35, 'new_value': 38}]
2025-06-14 09:00:58,786 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR11
2025-06-14 09:00:59,208 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR11
2025-06-14 09:00:59,224 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8162.0, 'new_value': 9199.0}, {'field': 'offline_amount', 'old_value': 13927.0, 'new_value': 14914.0}, {'field': 'total_amount', 'old_value': 22089.0, 'new_value': 24113.0}, {'field': 'order_count', 'old_value': 178, 'new_value': 196}]
2025-06-14 09:00:59,224 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMY11
2025-06-14 09:00:59,692 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMY11
2025-06-14 09:00:59,692 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 128119.65, 'new_value': 129683.25}, {'field': 'total_amount', 'old_value': 128119.65, 'new_value': 129683.25}, {'field': 'order_count', 'old_value': 664, 'new_value': 676}]
2025-06-14 09:00:59,692 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ11
2025-06-14 09:01:00,114 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ11
2025-06-14 09:01:00,114 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 137514.0, 'new_value': 150388.0}, {'field': 'total_amount', 'old_value': 137514.0, 'new_value': 150388.0}, {'field': 'order_count', 'old_value': 171, 'new_value': 190}]
2025-06-14 09:01:00,114 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ21
2025-06-14 09:01:00,614 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ21
2025-06-14 09:01:00,614 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45563.0, 'new_value': 50979.0}, {'field': 'total_amount', 'old_value': 45563.0, 'new_value': 50979.0}, {'field': 'order_count', 'old_value': 5457, 'new_value': 5742}]
2025-06-14 09:01:00,614 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC31
2025-06-14 09:01:01,130 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC31
2025-06-14 09:01:01,130 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40469.0, 'new_value': 44519.0}, {'field': 'total_amount', 'old_value': 40469.0, 'new_value': 44519.0}, {'field': 'order_count', 'old_value': 217, 'new_value': 240}]
2025-06-14 09:01:01,130 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66271XO5WRCRF89PMI7Q6G2YE3F4HBOSBM0D
2025-06-14 09:01:01,614 - INFO - 更新表单数据成功: FINST-3PF66271XO5WRCRF89PMI7Q6G2YE3F4HBOSBM0D
2025-06-14 09:01:01,614 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1283.0, 'new_value': 2774.1}, {'field': 'total_amount', 'old_value': 1283.0, 'new_value': 2774.1}, {'field': 'order_count', 'old_value': 29, 'new_value': 58}]
2025-06-14 09:01:01,614 - INFO - 日期 2025-06 处理完成 - 更新: 55 条，插入: 0 条，错误: 0 条
2025-06-14 09:01:01,614 - INFO - 数据同步完成！更新: 55 条，插入: 0 条，错误: 0 条
2025-06-14 09:01:01,614 - INFO - =================同步完成====================
2025-06-14 12:00:03,371 - INFO - =================使用默认全量同步=============
2025-06-14 12:00:05,059 - INFO - MySQL查询成功，共获取 3930 条记录
2025-06-14 12:00:05,059 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-14 12:00:05,090 - INFO - 开始处理日期: 2025-01
2025-06-14 12:00:05,105 - INFO - Request Parameters - Page 1:
2025-06-14 12:00:05,105 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:05,105 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:06,340 - INFO - Response - Page 1:
2025-06-14 12:00:06,543 - INFO - 第 1 页获取到 100 条记录
2025-06-14 12:00:06,543 - INFO - Request Parameters - Page 2:
2025-06-14 12:00:06,543 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:06,543 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:07,512 - INFO - Response - Page 2:
2025-06-14 12:00:07,715 - INFO - 第 2 页获取到 100 条记录
2025-06-14 12:00:07,715 - INFO - Request Parameters - Page 3:
2025-06-14 12:00:07,715 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:07,715 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:08,309 - INFO - Response - Page 3:
2025-06-14 12:00:08,512 - INFO - 第 3 页获取到 100 条记录
2025-06-14 12:00:08,512 - INFO - Request Parameters - Page 4:
2025-06-14 12:00:08,512 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:08,512 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:09,043 - INFO - Response - Page 4:
2025-06-14 12:00:09,246 - INFO - 第 4 页获取到 100 条记录
2025-06-14 12:00:09,246 - INFO - Request Parameters - Page 5:
2025-06-14 12:00:09,246 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:09,246 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:09,777 - INFO - Response - Page 5:
2025-06-14 12:00:09,980 - INFO - 第 5 页获取到 100 条记录
2025-06-14 12:00:09,980 - INFO - Request Parameters - Page 6:
2025-06-14 12:00:09,980 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:09,980 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:10,652 - INFO - Response - Page 6:
2025-06-14 12:00:10,855 - INFO - 第 6 页获取到 100 条记录
2025-06-14 12:00:10,855 - INFO - Request Parameters - Page 7:
2025-06-14 12:00:10,855 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:10,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:11,371 - INFO - Response - Page 7:
2025-06-14 12:00:11,574 - INFO - 第 7 页获取到 82 条记录
2025-06-14 12:00:11,574 - INFO - 查询完成，共获取到 682 条记录
2025-06-14 12:00:11,574 - INFO - 获取到 682 条表单数据
2025-06-14 12:00:11,574 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-14 12:00:11,590 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 12:00:11,590 - INFO - 开始处理日期: 2025-02
2025-06-14 12:00:11,590 - INFO - Request Parameters - Page 1:
2025-06-14 12:00:11,590 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:11,590 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:12,293 - INFO - Response - Page 1:
2025-06-14 12:00:12,496 - INFO - 第 1 页获取到 100 条记录
2025-06-14 12:00:12,496 - INFO - Request Parameters - Page 2:
2025-06-14 12:00:12,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:12,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:13,059 - INFO - Response - Page 2:
2025-06-14 12:00:13,262 - INFO - 第 2 页获取到 100 条记录
2025-06-14 12:00:13,262 - INFO - Request Parameters - Page 3:
2025-06-14 12:00:13,262 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:13,262 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:13,777 - INFO - Response - Page 3:
2025-06-14 12:00:13,980 - INFO - 第 3 页获取到 100 条记录
2025-06-14 12:00:13,980 - INFO - Request Parameters - Page 4:
2025-06-14 12:00:13,980 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:13,980 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:14,480 - INFO - Response - Page 4:
2025-06-14 12:00:14,684 - INFO - 第 4 页获取到 100 条记录
2025-06-14 12:00:14,684 - INFO - Request Parameters - Page 5:
2025-06-14 12:00:14,684 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:14,684 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:15,230 - INFO - Response - Page 5:
2025-06-14 12:00:15,434 - INFO - 第 5 页获取到 100 条记录
2025-06-14 12:00:15,434 - INFO - Request Parameters - Page 6:
2025-06-14 12:00:15,434 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:15,434 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:15,949 - INFO - Response - Page 6:
2025-06-14 12:00:16,152 - INFO - 第 6 页获取到 100 条记录
2025-06-14 12:00:16,152 - INFO - Request Parameters - Page 7:
2025-06-14 12:00:16,152 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:16,152 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:16,590 - INFO - Response - Page 7:
2025-06-14 12:00:16,793 - INFO - 第 7 页获取到 70 条记录
2025-06-14 12:00:16,793 - INFO - 查询完成，共获取到 670 条记录
2025-06-14 12:00:16,793 - INFO - 获取到 670 条表单数据
2025-06-14 12:00:16,793 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-14 12:00:16,808 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 12:00:16,808 - INFO - 开始处理日期: 2025-03
2025-06-14 12:00:16,808 - INFO - Request Parameters - Page 1:
2025-06-14 12:00:16,808 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:16,808 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:17,324 - INFO - Response - Page 1:
2025-06-14 12:00:17,527 - INFO - 第 1 页获取到 100 条记录
2025-06-14 12:00:17,527 - INFO - Request Parameters - Page 2:
2025-06-14 12:00:17,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:17,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:18,105 - INFO - Response - Page 2:
2025-06-14 12:00:18,308 - INFO - 第 2 页获取到 100 条记录
2025-06-14 12:00:18,308 - INFO - Request Parameters - Page 3:
2025-06-14 12:00:18,308 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:18,308 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:18,840 - INFO - Response - Page 3:
2025-06-14 12:00:19,043 - INFO - 第 3 页获取到 100 条记录
2025-06-14 12:00:19,043 - INFO - Request Parameters - Page 4:
2025-06-14 12:00:19,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:19,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:19,527 - INFO - Response - Page 4:
2025-06-14 12:00:19,730 - INFO - 第 4 页获取到 100 条记录
2025-06-14 12:00:19,730 - INFO - Request Parameters - Page 5:
2025-06-14 12:00:19,730 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:19,730 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:20,277 - INFO - Response - Page 5:
2025-06-14 12:00:20,480 - INFO - 第 5 页获取到 100 条记录
2025-06-14 12:00:20,480 - INFO - Request Parameters - Page 6:
2025-06-14 12:00:20,480 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:20,480 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:21,012 - INFO - Response - Page 6:
2025-06-14 12:00:21,215 - INFO - 第 6 页获取到 100 条记录
2025-06-14 12:00:21,215 - INFO - Request Parameters - Page 7:
2025-06-14 12:00:21,215 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:21,215 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:21,668 - INFO - Response - Page 7:
2025-06-14 12:00:21,871 - INFO - 第 7 页获取到 61 条记录
2025-06-14 12:00:21,871 - INFO - 查询完成，共获取到 661 条记录
2025-06-14 12:00:21,871 - INFO - 获取到 661 条表单数据
2025-06-14 12:00:21,871 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-14 12:00:21,887 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 12:00:21,887 - INFO - 开始处理日期: 2025-04
2025-06-14 12:00:21,887 - INFO - Request Parameters - Page 1:
2025-06-14 12:00:21,887 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:21,887 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:22,402 - INFO - Response - Page 1:
2025-06-14 12:00:22,605 - INFO - 第 1 页获取到 100 条记录
2025-06-14 12:00:22,605 - INFO - Request Parameters - Page 2:
2025-06-14 12:00:22,605 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:22,605 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:23,324 - INFO - Response - Page 2:
2025-06-14 12:00:23,527 - INFO - 第 2 页获取到 100 条记录
2025-06-14 12:00:23,527 - INFO - Request Parameters - Page 3:
2025-06-14 12:00:23,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:23,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:24,074 - INFO - Response - Page 3:
2025-06-14 12:00:24,277 - INFO - 第 3 页获取到 100 条记录
2025-06-14 12:00:24,277 - INFO - Request Parameters - Page 4:
2025-06-14 12:00:24,277 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:24,277 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:24,793 - INFO - Response - Page 4:
2025-06-14 12:00:24,996 - INFO - 第 4 页获取到 100 条记录
2025-06-14 12:00:24,996 - INFO - Request Parameters - Page 5:
2025-06-14 12:00:24,996 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:24,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:25,496 - INFO - Response - Page 5:
2025-06-14 12:00:25,699 - INFO - 第 5 页获取到 100 条记录
2025-06-14 12:00:25,699 - INFO - Request Parameters - Page 6:
2025-06-14 12:00:25,699 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:25,699 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:26,183 - INFO - Response - Page 6:
2025-06-14 12:00:26,387 - INFO - 第 6 页获取到 100 条记录
2025-06-14 12:00:26,387 - INFO - Request Parameters - Page 7:
2025-06-14 12:00:26,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:26,387 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:26,808 - INFO - Response - Page 7:
2025-06-14 12:00:27,012 - INFO - 第 7 页获取到 56 条记录
2025-06-14 12:00:27,012 - INFO - 查询完成，共获取到 656 条记录
2025-06-14 12:00:27,012 - INFO - 获取到 656 条表单数据
2025-06-14 12:00:27,012 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-14 12:00:27,027 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 12:00:27,027 - INFO - 开始处理日期: 2025-05
2025-06-14 12:00:27,027 - INFO - Request Parameters - Page 1:
2025-06-14 12:00:27,027 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:27,027 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:27,574 - INFO - Response - Page 1:
2025-06-14 12:00:27,777 - INFO - 第 1 页获取到 100 条记录
2025-06-14 12:00:27,777 - INFO - Request Parameters - Page 2:
2025-06-14 12:00:27,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:27,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:28,293 - INFO - Response - Page 2:
2025-06-14 12:00:28,496 - INFO - 第 2 页获取到 100 条记录
2025-06-14 12:00:28,496 - INFO - Request Parameters - Page 3:
2025-06-14 12:00:28,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:28,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:28,949 - INFO - Response - Page 3:
2025-06-14 12:00:29,152 - INFO - 第 3 页获取到 100 条记录
2025-06-14 12:00:29,152 - INFO - Request Parameters - Page 4:
2025-06-14 12:00:29,152 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:29,152 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:29,652 - INFO - Response - Page 4:
2025-06-14 12:00:29,855 - INFO - 第 4 页获取到 100 条记录
2025-06-14 12:00:29,855 - INFO - Request Parameters - Page 5:
2025-06-14 12:00:29,855 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:29,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:30,355 - INFO - Response - Page 5:
2025-06-14 12:00:30,558 - INFO - 第 5 页获取到 100 条记录
2025-06-14 12:00:30,558 - INFO - Request Parameters - Page 6:
2025-06-14 12:00:30,558 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:30,558 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:31,043 - INFO - Response - Page 6:
2025-06-14 12:00:31,246 - INFO - 第 6 页获取到 100 条记录
2025-06-14 12:00:31,246 - INFO - Request Parameters - Page 7:
2025-06-14 12:00:31,246 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:31,246 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:31,668 - INFO - Response - Page 7:
2025-06-14 12:00:31,871 - INFO - 第 7 页获取到 40 条记录
2025-06-14 12:00:31,871 - INFO - 查询完成，共获取到 640 条记录
2025-06-14 12:00:31,871 - INFO - 获取到 640 条表单数据
2025-06-14 12:00:31,871 - INFO - 当前日期 2025-05 有 640 条MySQL数据需要处理
2025-06-14 12:00:31,887 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 12:00:31,887 - INFO - 开始处理日期: 2025-06
2025-06-14 12:00:31,887 - INFO - Request Parameters - Page 1:
2025-06-14 12:00:31,887 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:31,887 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:32,433 - INFO - Response - Page 1:
2025-06-14 12:00:32,637 - INFO - 第 1 页获取到 100 条记录
2025-06-14 12:00:32,637 - INFO - Request Parameters - Page 2:
2025-06-14 12:00:32,637 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:32,637 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:33,246 - INFO - Response - Page 2:
2025-06-14 12:00:33,449 - INFO - 第 2 页获取到 100 条记录
2025-06-14 12:00:33,449 - INFO - Request Parameters - Page 3:
2025-06-14 12:00:33,449 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:33,449 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:33,949 - INFO - Response - Page 3:
2025-06-14 12:00:34,152 - INFO - 第 3 页获取到 100 条记录
2025-06-14 12:00:34,152 - INFO - Request Parameters - Page 4:
2025-06-14 12:00:34,152 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:34,152 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:34,949 - INFO - Response - Page 4:
2025-06-14 12:00:35,152 - INFO - 第 4 页获取到 100 条记录
2025-06-14 12:00:35,152 - INFO - Request Parameters - Page 5:
2025-06-14 12:00:35,152 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:35,152 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:35,715 - INFO - Response - Page 5:
2025-06-14 12:00:35,918 - INFO - 第 5 页获取到 100 条记录
2025-06-14 12:00:35,918 - INFO - Request Parameters - Page 6:
2025-06-14 12:00:35,918 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:35,918 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:36,465 - INFO - Response - Page 6:
2025-06-14 12:00:36,668 - INFO - 第 6 页获取到 100 条记录
2025-06-14 12:00:36,668 - INFO - Request Parameters - Page 7:
2025-06-14 12:00:36,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 12:00:36,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 12:00:36,996 - INFO - Response - Page 7:
2025-06-14 12:00:37,199 - INFO - 第 7 页获取到 21 条记录
2025-06-14 12:00:37,199 - INFO - 查询完成，共获取到 621 条记录
2025-06-14 12:00:37,199 - INFO - 获取到 621 条表单数据
2025-06-14 12:00:37,199 - INFO - 当前日期 2025-06 有 621 条MySQL数据需要处理
2025-06-14 12:00:37,199 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMHX
2025-06-14 12:00:37,715 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMHX
2025-06-14 12:00:37,715 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 347191.0, 'new_value': 374537.0}, {'field': 'total_amount', 'old_value': 347191.0, 'new_value': 374537.0}, {'field': 'order_count', 'old_value': 2441, 'new_value': 2640}]
2025-06-14 12:00:37,715 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMJX
2025-06-14 12:00:38,090 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMJX
2025-06-14 12:00:38,090 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70238.32, 'new_value': 74410.95}, {'field': 'total_amount', 'old_value': 70238.32, 'new_value': 74410.95}, {'field': 'order_count', 'old_value': 1803, 'new_value': 1907}]
2025-06-14 12:00:38,090 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMLX
2025-06-14 12:00:38,558 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMLX
2025-06-14 12:00:38,558 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41047.6, 'new_value': 43891.79}, {'field': 'offline_amount', 'old_value': 421017.66, 'new_value': 454411.59}, {'field': 'total_amount', 'old_value': 458072.77, 'new_value': 494310.89}, {'field': 'order_count', 'old_value': 2231, 'new_value': 2431}]
2025-06-14 12:00:38,558 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMMX
2025-06-14 12:00:39,011 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMMX
2025-06-14 12:00:39,011 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82800.0, 'new_value': 94800.0}, {'field': 'total_amount', 'old_value': 82800.0, 'new_value': 94800.0}]
2025-06-14 12:00:39,011 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMQX
2025-06-14 12:00:39,621 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMQX
2025-06-14 12:00:39,621 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 158540.0, 'new_value': 166520.0}, {'field': 'total_amount', 'old_value': 175040.0, 'new_value': 183020.0}, {'field': 'order_count', 'old_value': 115, 'new_value': 123}]
2025-06-14 12:00:39,621 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMSX
2025-06-14 12:00:40,074 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMSX
2025-06-14 12:00:40,074 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12967.98, 'new_value': 14162.98}, {'field': 'offline_amount', 'old_value': 7004.66, 'new_value': 7708.66}, {'field': 'total_amount', 'old_value': 19972.64, 'new_value': 21871.64}, {'field': 'order_count', 'old_value': 818, 'new_value': 914}]
2025-06-14 12:00:40,074 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMUX
2025-06-14 12:00:40,574 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMUX
2025-06-14 12:00:40,574 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 222890.0, 'new_value': 246262.0}, {'field': 'offline_amount', 'old_value': 103044.0, 'new_value': 106444.0}, {'field': 'total_amount', 'old_value': 325934.0, 'new_value': 352706.0}, {'field': 'order_count', 'old_value': 373, 'new_value': 393}]
2025-06-14 12:00:40,574 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMWX
2025-06-14 12:00:41,043 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMWX
2025-06-14 12:00:41,043 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88740.0, 'new_value': 97600.0}, {'field': 'total_amount', 'old_value': 88740.0, 'new_value': 97600.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-06-14 12:00:41,043 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Y
2025-06-14 12:00:41,511 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Y
2025-06-14 12:00:41,511 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2686.0, 'new_value': 3130.0}, {'field': 'offline_amount', 'old_value': 38120.0, 'new_value': 41943.0}, {'field': 'total_amount', 'old_value': 40806.0, 'new_value': 45073.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 54}]
2025-06-14 12:00:41,511 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM6Y
2025-06-14 12:00:42,011 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM6Y
2025-06-14 12:00:42,011 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11645.5, 'new_value': 15822.5}, {'field': 'total_amount', 'old_value': 11645.5, 'new_value': 15822.5}, {'field': 'order_count', 'old_value': 2366, 'new_value': 3221}]
2025-06-14 12:00:42,011 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAY
2025-06-14 12:00:42,480 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAY
2025-06-14 12:00:42,480 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73302.0, 'new_value': 73306.0}, {'field': 'total_amount', 'old_value': 73302.0, 'new_value': 73306.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 49117}]
2025-06-14 12:00:42,480 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMDY
2025-06-14 12:00:42,949 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMDY
2025-06-14 12:00:42,949 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 135484.5, 'new_value': 140743.4}, {'field': 'total_amount', 'old_value': 135484.5, 'new_value': 140743.4}, {'field': 'order_count', 'old_value': 2039, 'new_value': 2132}]
2025-06-14 12:00:42,949 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMEY
2025-06-14 12:00:43,402 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMEY
2025-06-14 12:00:43,402 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11205.0, 'new_value': 13205.0}, {'field': 'total_amount', 'old_value': 21805.0, 'new_value': 23805.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-14 12:00:43,402 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMFY
2025-06-14 12:00:44,105 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMFY
2025-06-14 12:00:44,105 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50439.8, 'new_value': 64745.8}, {'field': 'total_amount', 'old_value': 50439.8, 'new_value': 64745.8}, {'field': 'order_count', 'old_value': 251, 'new_value': 318}]
2025-06-14 12:00:44,105 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGY
2025-06-14 12:00:44,543 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGY
2025-06-14 12:00:44,543 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2061.0, 'new_value': 2316.0}, {'field': 'offline_amount', 'old_value': 31396.32, 'new_value': 32694.43}, {'field': 'total_amount', 'old_value': 33457.32, 'new_value': 35010.43}, {'field': 'order_count', 'old_value': 236, 'new_value': 251}]
2025-06-14 12:00:44,543 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMHY
2025-06-14 12:00:45,090 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMHY
2025-06-14 12:00:45,090 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14639.0, 'new_value': 21036.0}, {'field': 'offline_amount', 'old_value': 78804.0, 'new_value': 99790.0}, {'field': 'total_amount', 'old_value': 93443.0, 'new_value': 120826.0}, {'field': 'order_count', 'old_value': 652, 'new_value': 844}]
2025-06-14 12:00:45,090 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJY
2025-06-14 12:00:45,496 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJY
2025-06-14 12:00:45,496 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22330.13, 'new_value': 24551.26}, {'field': 'offline_amount', 'old_value': 52000.12, 'new_value': 56733.12}, {'field': 'total_amount', 'old_value': 74330.25, 'new_value': 81284.38}, {'field': 'order_count', 'old_value': 838, 'new_value': 922}]
2025-06-14 12:00:45,496 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOY
2025-06-14 12:00:45,949 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOY
2025-06-14 12:00:45,949 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13646.0, 'new_value': 15146.0}, {'field': 'offline_amount', 'old_value': 25614.74, 'new_value': 28835.74}, {'field': 'total_amount', 'old_value': 39260.74, 'new_value': 43981.74}, {'field': 'order_count', 'old_value': 61, 'new_value': 66}]
2025-06-14 12:00:45,949 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPY
2025-06-14 12:00:46,402 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPY
2025-06-14 12:00:46,402 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28571.0, 'new_value': 31423.0}, {'field': 'total_amount', 'old_value': 28571.0, 'new_value': 31423.0}, {'field': 'order_count', 'old_value': 552, 'new_value': 606}]
2025-06-14 12:00:46,402 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMQY
2025-06-14 12:00:46,902 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMQY
2025-06-14 12:00:46,902 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13978.51, 'new_value': 18272.15}, {'field': 'offline_amount', 'old_value': 35871.01, 'new_value': 51205.12}, {'field': 'total_amount', 'old_value': 49849.52, 'new_value': 69477.27}, {'field': 'order_count', 'old_value': 729, 'new_value': 987}]
2025-06-14 12:00:46,902 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRY
2025-06-14 12:00:47,324 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRY
2025-06-14 12:00:47,324 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 69750.0, 'new_value': 77025.0}, {'field': 'offline_amount', 'old_value': 25423.5, 'new_value': 27046.95}, {'field': 'total_amount', 'old_value': 95173.5, 'new_value': 104071.95}, {'field': 'order_count', 'old_value': 647, 'new_value': 711}]
2025-06-14 12:00:47,324 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMSY
2025-06-14 12:00:47,840 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMSY
2025-06-14 12:00:47,840 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4993.0, 'new_value': 6993.0}, {'field': 'total_amount', 'old_value': 5561.2, 'new_value': 7561.2}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-06-14 12:00:47,840 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTY
2025-06-14 12:00:48,386 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTY
2025-06-14 12:00:48,386 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3415.0, 'new_value': 3689.0}, {'field': 'total_amount', 'old_value': 5869.0, 'new_value': 6143.0}, {'field': 'order_count', 'old_value': 99, 'new_value': 104}]
2025-06-14 12:00:48,386 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMVY
2025-06-14 12:00:48,808 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMVY
2025-06-14 12:00:48,808 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 120171.0, 'new_value': 126571.0}, {'field': 'total_amount', 'old_value': 120171.0, 'new_value': 126571.0}, {'field': 'order_count', 'old_value': 620, 'new_value': 645}]
2025-06-14 12:00:48,808 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYY
2025-06-14 12:00:49,293 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYY
2025-06-14 12:00:49,293 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44819.35, 'new_value': 50864.56}, {'field': 'total_amount', 'old_value': 44819.35, 'new_value': 50864.56}, {'field': 'order_count', 'old_value': 96, 'new_value': 106}]
2025-06-14 12:00:49,293 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZY
2025-06-14 12:00:49,808 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZY
2025-06-14 12:00:49,808 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92505.19, 'new_value': 100532.2}, {'field': 'total_amount', 'old_value': 92505.19, 'new_value': 100532.2}, {'field': 'order_count', 'old_value': 550, 'new_value': 611}]
2025-06-14 12:00:49,808 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM0Z
2025-06-14 12:00:50,277 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM0Z
2025-06-14 12:00:50,277 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45084.0, 'new_value': 45443.0}, {'field': 'offline_amount', 'old_value': 281272.0, 'new_value': 298393.0}, {'field': 'total_amount', 'old_value': 326356.0, 'new_value': 343836.0}, {'field': 'order_count', 'old_value': 50, 'new_value': 54}]
2025-06-14 12:00:50,277 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM1Z
2025-06-14 12:00:50,715 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM1Z
2025-06-14 12:00:50,715 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17661.0, 'new_value': 17927.0}, {'field': 'total_amount', 'old_value': 17661.0, 'new_value': 17927.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-06-14 12:00:50,715 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM2Z
2025-06-14 12:00:51,230 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM2Z
2025-06-14 12:00:51,230 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11434.85, 'new_value': 12617.15}, {'field': 'offline_amount', 'old_value': 12824.6, 'new_value': 13831.9}, {'field': 'total_amount', 'old_value': 24259.45, 'new_value': 26449.05}, {'field': 'order_count', 'old_value': 1184, 'new_value': 1302}]
2025-06-14 12:00:51,230 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Z
2025-06-14 12:00:51,668 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Z
2025-06-14 12:00:51,668 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 326139.54, 'new_value': 355413.43}, {'field': 'total_amount', 'old_value': 326139.54, 'new_value': 355413.43}, {'field': 'order_count', 'old_value': 2236, 'new_value': 2476}]
2025-06-14 12:00:51,668 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM6Z
2025-06-14 12:00:52,121 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM6Z
2025-06-14 12:00:52,121 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 389297.3, 'new_value': 442466.5}, {'field': 'total_amount', 'old_value': 389297.3, 'new_value': 442466.5}, {'field': 'order_count', 'old_value': 51, 'new_value': 53}]
2025-06-14 12:00:52,121 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM7Z
2025-06-14 12:00:52,589 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM7Z
2025-06-14 12:00:52,589 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65194.0, 'new_value': 76097.0}, {'field': 'total_amount', 'old_value': 65194.0, 'new_value': 76097.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 29}]
2025-06-14 12:00:52,589 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM8Z
2025-06-14 12:00:53,090 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM8Z
2025-06-14 12:00:53,090 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4153.0, 'new_value': 4778.0}, {'field': 'offline_amount', 'old_value': 10063.0, 'new_value': 10977.0}, {'field': 'total_amount', 'old_value': 14216.0, 'new_value': 15755.0}, {'field': 'order_count', 'old_value': 43, 'new_value': 49}]
2025-06-14 12:00:53,090 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM9Z
2025-06-14 12:00:53,605 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM9Z
2025-06-14 12:00:53,605 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8389.0, 'new_value': 9589.0}, {'field': 'total_amount', 'old_value': 16778.0, 'new_value': 17978.0}, {'field': 'order_count', 'old_value': 8116, 'new_value': 8121}]
2025-06-14 12:00:53,605 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAZ
2025-06-14 12:00:54,105 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAZ
2025-06-14 12:00:54,105 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77998.0, 'new_value': 95138.0}, {'field': 'total_amount', 'old_value': 77998.0, 'new_value': 95138.0}, {'field': 'order_count', 'old_value': 141, 'new_value': 171}]
2025-06-14 12:00:54,105 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMBZ
2025-06-14 12:00:54,558 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMBZ
2025-06-14 12:00:54,558 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5142.78, 'new_value': 5564.45}, {'field': 'offline_amount', 'old_value': 74944.9, 'new_value': 81513.45}, {'field': 'total_amount', 'old_value': 80087.68, 'new_value': 87077.9}, {'field': 'order_count', 'old_value': 891, 'new_value': 969}]
2025-06-14 12:00:54,558 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMCZ
2025-06-14 12:00:55,136 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMCZ
2025-06-14 12:00:55,152 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5917.5, 'new_value': 5983.8}, {'field': 'offline_amount', 'old_value': 31017.0, 'new_value': 31146.0}, {'field': 'total_amount', 'old_value': 36934.5, 'new_value': 37129.8}, {'field': 'order_count', 'old_value': 39, 'new_value': 42}]
2025-06-14 12:00:55,152 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMHZ
2025-06-14 12:00:55,558 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMHZ
2025-06-14 12:00:55,558 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5205.5, 'new_value': 5627.07}, {'field': 'offline_amount', 'old_value': 15284.21, 'new_value': 17272.71}, {'field': 'total_amount', 'old_value': 20489.71, 'new_value': 22899.78}, {'field': 'order_count', 'old_value': 432, 'new_value': 473}]
2025-06-14 12:00:55,558 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJZ
2025-06-14 12:00:56,105 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJZ
2025-06-14 12:00:56,105 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4690.72, 'new_value': 5939.84}, {'field': 'total_amount', 'old_value': 4690.72, 'new_value': 5939.84}, {'field': 'order_count', 'old_value': 8, 'new_value': 10}]
2025-06-14 12:00:56,105 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMKZ
2025-06-14 12:00:56,668 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMKZ
2025-06-14 12:00:56,668 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64769.29, 'new_value': 69531.13}, {'field': 'total_amount', 'old_value': 64769.29, 'new_value': 69531.13}, {'field': 'order_count', 'old_value': 310, 'new_value': 332}]
2025-06-14 12:00:56,668 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLZ
2025-06-14 12:00:57,105 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLZ
2025-06-14 12:00:57,105 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11046.0, 'new_value': 11767.0}, {'field': 'total_amount', 'old_value': 11046.0, 'new_value': 11767.0}, {'field': 'order_count', 'old_value': 55, 'new_value': 59}]
2025-06-14 12:00:57,105 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPZ
2025-06-14 12:00:57,605 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPZ
2025-06-14 12:00:57,605 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86358.0, 'new_value': 92808.0}, {'field': 'total_amount', 'old_value': 86358.0, 'new_value': 92808.0}, {'field': 'order_count', 'old_value': 3292, 'new_value': 3565}]
2025-06-14 12:00:57,605 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMQZ
2025-06-14 12:00:58,074 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMQZ
2025-06-14 12:00:58,074 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74061.5, 'new_value': 79945.9}, {'field': 'total_amount', 'old_value': 74061.5, 'new_value': 79945.9}, {'field': 'order_count', 'old_value': 310, 'new_value': 334}]
2025-06-14 12:00:58,074 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRZ
2025-06-14 12:00:58,543 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRZ
2025-06-14 12:00:58,543 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 66557.0, 'new_value': 70594.0}, {'field': 'offline_amount', 'old_value': 26678.85, 'new_value': 28773.85}, {'field': 'total_amount', 'old_value': 93235.85, 'new_value': 99367.85}, {'field': 'order_count', 'old_value': 656, 'new_value': 702}]
2025-06-14 12:00:58,543 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMSZ
2025-06-14 12:00:58,996 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMSZ
2025-06-14 12:00:58,996 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52968.0, 'new_value': 60968.0}, {'field': 'total_amount', 'old_value': 61572.0, 'new_value': 69572.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 24}]
2025-06-14 12:00:58,996 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTZ
2025-06-14 12:00:59,527 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTZ
2025-06-14 12:00:59,527 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66425.52, 'new_value': 73055.52}, {'field': 'total_amount', 'old_value': 66425.52, 'new_value': 73055.52}, {'field': 'order_count', 'old_value': 2312, 'new_value': 2533}]
2025-06-14 12:00:59,527 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYZ
2025-06-14 12:00:59,964 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYZ
2025-06-14 12:00:59,964 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26675.0, 'new_value': 30154.0}, {'field': 'offline_amount', 'old_value': 89747.0, 'new_value': 99820.0}, {'field': 'total_amount', 'old_value': 116422.0, 'new_value': 129974.0}, {'field': 'order_count', 'old_value': 87, 'new_value': 99}]
2025-06-14 12:00:59,964 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZZ
2025-06-14 12:01:00,418 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZZ
2025-06-14 12:01:00,418 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7429.06, 'new_value': 8283.06}, {'field': 'offline_amount', 'old_value': 167168.02, 'new_value': 179201.5}, {'field': 'total_amount', 'old_value': 174597.08, 'new_value': 187484.56}, {'field': 'order_count', 'old_value': 870, 'new_value': 943}]
2025-06-14 12:01:00,418 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM201
2025-06-14 12:01:00,871 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM201
2025-06-14 12:01:00,871 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11660.05, 'new_value': 13186.52}, {'field': 'offline_amount', 'old_value': 17730.82, 'new_value': 19009.73}, {'field': 'total_amount', 'old_value': 29390.87, 'new_value': 32196.25}, {'field': 'order_count', 'old_value': 1373, 'new_value': 1514}]
2025-06-14 12:01:00,871 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM701
2025-06-14 12:01:01,308 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM701
2025-06-14 12:01:01,308 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57800.0, 'new_value': 62800.0}, {'field': 'total_amount', 'old_value': 59399.0, 'new_value': 64399.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-06-14 12:01:01,308 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAB
2025-06-14 12:01:01,777 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAB
2025-06-14 12:01:01,777 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30782.1, 'new_value': 39687.34}, {'field': 'total_amount', 'old_value': 30782.1, 'new_value': 39687.34}, {'field': 'order_count', 'old_value': 1611, 'new_value': 2104}]
2025-06-14 12:01:01,777 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBB
2025-06-14 12:01:02,261 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBB
2025-06-14 12:01:02,261 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75880.0, 'new_value': 82191.0}, {'field': 'total_amount', 'old_value': 75880.0, 'new_value': 82191.0}, {'field': 'order_count', 'old_value': 1961, 'new_value': 2127}]
2025-06-14 12:01:02,261 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCB
2025-06-14 12:01:02,761 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCB
2025-06-14 12:01:02,761 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 93000.0}, {'field': 'offline_amount', 'old_value': 71473.0, 'new_value': 187593.0}, {'field': 'total_amount', 'old_value': 71473.0, 'new_value': 280593.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 42}]
2025-06-14 12:01:02,761 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMEB
2025-06-14 12:01:03,246 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMEB
2025-06-14 12:01:03,246 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 156673.73, 'new_value': 172892.66}, {'field': 'offline_amount', 'old_value': 723.0, 'new_value': 1069.0}, {'field': 'total_amount', 'old_value': 157396.73, 'new_value': 173961.66}, {'field': 'order_count', 'old_value': 1942, 'new_value': 2160}]
2025-06-14 12:01:03,246 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMFB
2025-06-14 12:01:03,824 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMFB
2025-06-14 12:01:03,824 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27100.45, 'new_value': 29619.94}, {'field': 'offline_amount', 'old_value': 217312.85, 'new_value': 238257.8}, {'field': 'total_amount', 'old_value': 244413.3, 'new_value': 267877.74}, {'field': 'order_count', 'old_value': 760, 'new_value': 838}]
2025-06-14 12:01:03,824 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGB
2025-06-14 12:01:04,293 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGB
2025-06-14 12:01:04,293 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64224.62, 'new_value': 70161.94}, {'field': 'total_amount', 'old_value': 64224.62, 'new_value': 70161.94}, {'field': 'order_count', 'old_value': 809, 'new_value': 891}]
2025-06-14 12:01:04,293 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMIB
2025-06-14 12:01:04,761 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMIB
2025-06-14 12:01:04,761 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30620.0, 'new_value': 40001.0}, {'field': 'total_amount', 'old_value': 30620.0, 'new_value': 40001.0}, {'field': 'order_count', 'old_value': 1788, 'new_value': 2295}]
2025-06-14 12:01:04,761 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJB
2025-06-14 12:01:05,230 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJB
2025-06-14 12:01:05,230 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38423.74, 'new_value': 41661.54}, {'field': 'total_amount', 'old_value': 38423.74, 'new_value': 41661.54}, {'field': 'order_count', 'old_value': 150, 'new_value': 161}]
2025-06-14 12:01:05,230 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMB
2025-06-14 12:01:05,683 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMB
2025-06-14 12:01:05,683 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6259.79, 'new_value': 6891.99}, {'field': 'offline_amount', 'old_value': 11741.42, 'new_value': 13033.37}, {'field': 'total_amount', 'old_value': 18001.21, 'new_value': 19925.36}, {'field': 'order_count', 'old_value': 590, 'new_value': 658}]
2025-06-14 12:01:05,683 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOB
2025-06-14 12:01:06,152 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOB
2025-06-14 12:01:06,152 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82985.59, 'new_value': 92121.05}, {'field': 'total_amount', 'old_value': 86533.95, 'new_value': 95669.41}, {'field': 'order_count', 'old_value': 559, 'new_value': 607}]
2025-06-14 12:01:06,152 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSB
2025-06-14 12:01:06,574 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSB
2025-06-14 12:01:06,574 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19486.0, 'new_value': 19685.0}, {'field': 'total_amount', 'old_value': 19486.0, 'new_value': 19685.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-06-14 12:01:06,574 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUB
2025-06-14 12:01:07,183 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUB
2025-06-14 12:01:07,183 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 194663.9, 'new_value': 205121.8}, {'field': 'offline_amount', 'old_value': 44940.4, 'new_value': 49856.1}, {'field': 'total_amount', 'old_value': 239604.3, 'new_value': 254977.9}, {'field': 'order_count', 'old_value': 299, 'new_value': 317}]
2025-06-14 12:01:07,183 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXB
2025-06-14 12:01:07,668 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXB
2025-06-14 12:01:07,668 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10563.04, 'new_value': 12040.04}, {'field': 'total_amount', 'old_value': 10563.04, 'new_value': 12040.04}, {'field': 'order_count', 'old_value': 478, 'new_value': 538}]
2025-06-14 12:01:07,668 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMZB
2025-06-14 12:01:08,121 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMZB
2025-06-14 12:01:08,121 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27194.0, 'new_value': 34593.0}, {'field': 'total_amount', 'old_value': 27194.0, 'new_value': 34593.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-14 12:01:08,121 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1C
2025-06-14 12:01:08,636 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1C
2025-06-14 12:01:08,636 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 300581.19, 'new_value': 317476.94}, {'field': 'total_amount', 'old_value': 300581.19, 'new_value': 317476.94}, {'field': 'order_count', 'old_value': 2282, 'new_value': 2436}]
2025-06-14 12:01:08,636 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM4C
2025-06-14 12:01:09,168 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM4C
2025-06-14 12:01:09,168 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-06-14 12:01:09,168 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5C
2025-06-14 12:01:09,652 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5C
2025-06-14 12:01:09,652 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52065.14, 'new_value': 55018.9}, {'field': 'offline_amount', 'old_value': 274749.53, 'new_value': 293915.77}, {'field': 'total_amount', 'old_value': 326814.67, 'new_value': 348934.67}, {'field': 'order_count', 'old_value': 837, 'new_value': 893}]
2025-06-14 12:01:09,652 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7C
2025-06-14 12:01:10,074 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7C
2025-06-14 12:01:10,074 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47633.79, 'new_value': 51644.29}, {'field': 'total_amount', 'old_value': 47633.79, 'new_value': 51644.29}, {'field': 'order_count', 'old_value': 1379, 'new_value': 1497}]
2025-06-14 12:01:10,074 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM8C
2025-06-14 12:01:10,574 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM8C
2025-06-14 12:01:10,574 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60334.49, 'new_value': 63982.82}, {'field': 'total_amount', 'old_value': 61338.49, 'new_value': 64986.82}, {'field': 'order_count', 'old_value': 85, 'new_value': 91}]
2025-06-14 12:01:10,574 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDC
2025-06-14 12:01:11,152 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDC
2025-06-14 12:01:11,152 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6804.1, 'new_value': 7173.7}, {'field': 'total_amount', 'old_value': 24967.0, 'new_value': 25336.6}, {'field': 'order_count', 'old_value': 82, 'new_value': 87}]
2025-06-14 12:01:11,152 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGC
2025-06-14 12:01:11,683 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGC
2025-06-14 12:01:11,683 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28146.6, 'new_value': 29870.65}, {'field': 'total_amount', 'old_value': 28147.6, 'new_value': 29871.65}, {'field': 'order_count', 'old_value': 99, 'new_value': 104}]
2025-06-14 12:01:11,683 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJC
2025-06-14 12:01:12,183 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJC
2025-06-14 12:01:12,183 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 107259.5, 'new_value': 114489.6}, {'field': 'offline_amount', 'old_value': 53710.9, 'new_value': 57405.15}, {'field': 'total_amount', 'old_value': 160970.4, 'new_value': 171894.75}, {'field': 'order_count', 'old_value': 670, 'new_value': 747}]
2025-06-14 12:01:12,183 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKC
2025-06-14 12:01:12,636 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKC
2025-06-14 12:01:12,636 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33974.0, 'new_value': 34059.0}, {'field': 'offline_amount', 'old_value': 190493.0, 'new_value': 205700.0}, {'field': 'total_amount', 'old_value': 224467.0, 'new_value': 239759.0}, {'field': 'order_count', 'old_value': 1726, 'new_value': 1884}]
2025-06-14 12:01:12,636 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMNC
2025-06-14 12:01:13,121 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMNC
2025-06-14 12:01:13,121 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41480.0, 'new_value': 44960.0}, {'field': 'total_amount', 'old_value': 55280.0, 'new_value': 58760.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-14 12:01:13,121 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOC
2025-06-14 12:01:13,652 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOC
2025-06-14 12:01:13,652 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31422.0, 'new_value': 34293.0}, {'field': 'total_amount', 'old_value': 31422.0, 'new_value': 34293.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-06-14 12:01:13,652 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQC
2025-06-14 12:01:14,136 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQC
2025-06-14 12:01:14,136 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 176233.11, 'new_value': 192286.76}, {'field': 'total_amount', 'old_value': 176233.11, 'new_value': 192286.76}, {'field': 'order_count', 'old_value': 556, 'new_value': 606}]
2025-06-14 12:01:14,136 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRC
2025-06-14 12:01:14,652 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRC
2025-06-14 12:01:14,652 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24733.0, 'new_value': 25733.0}, {'field': 'total_amount', 'old_value': 24733.0, 'new_value': 25733.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 40}]
2025-06-14 12:01:14,652 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXC
2025-06-14 12:01:15,167 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXC
2025-06-14 12:01:15,167 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22689.7, 'new_value': 24549.7}, {'field': 'offline_amount', 'old_value': 13554.8, 'new_value': 14684.9}, {'field': 'total_amount', 'old_value': 36244.5, 'new_value': 39234.6}, {'field': 'order_count', 'old_value': 124, 'new_value': 138}]
2025-06-14 12:01:15,167 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0D
2025-06-14 12:01:15,621 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0D
2025-06-14 12:01:15,621 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40754.16, 'new_value': 41870.16}, {'field': 'total_amount', 'old_value': 48185.16, 'new_value': 49301.16}, {'field': 'order_count', 'old_value': 423, 'new_value': 438}]
2025-06-14 12:01:15,621 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1D
2025-06-14 12:01:16,152 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1D
2025-06-14 12:01:16,152 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2252.0, 'new_value': 2389.0}, {'field': 'offline_amount', 'old_value': 13354.7, 'new_value': 14133.6}, {'field': 'total_amount', 'old_value': 15606.7, 'new_value': 16522.6}, {'field': 'order_count', 'old_value': 578, 'new_value': 615}]
2025-06-14 12:01:16,152 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM3D
2025-06-14 12:01:16,589 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM3D
2025-06-14 12:01:16,589 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10477.76, 'new_value': 11006.58}, {'field': 'offline_amount', 'old_value': 22507.98, 'new_value': 24225.28}, {'field': 'total_amount', 'old_value': 32985.74, 'new_value': 35231.86}, {'field': 'order_count', 'old_value': 1206, 'new_value': 1288}]
2025-06-14 12:01:16,589 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM8D
2025-06-14 12:01:17,136 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM8D
2025-06-14 12:01:17,136 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8703.0, 'new_value': 10387.0}, {'field': 'total_amount', 'old_value': 8703.0, 'new_value': 10387.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 57}]
2025-06-14 12:01:17,136 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAD
2025-06-14 12:01:17,605 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAD
2025-06-14 12:01:17,605 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40605.77, 'new_value': 42030.17}, {'field': 'offline_amount', 'old_value': 74018.16, 'new_value': 80259.16}, {'field': 'total_amount', 'old_value': 114623.93, 'new_value': 122289.33}, {'field': 'order_count', 'old_value': 659, 'new_value': 710}]
2025-06-14 12:01:17,605 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBD
2025-06-14 12:01:18,089 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBD
2025-06-14 12:01:18,089 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35707.26, 'new_value': 38756.62}, {'field': 'total_amount', 'old_value': 35707.26, 'new_value': 38756.62}, {'field': 'order_count', 'old_value': 994, 'new_value': 1072}]
2025-06-14 12:01:18,089 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDD
2025-06-14 12:01:18,589 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDD
2025-06-14 12:01:18,589 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16083.74, 'new_value': 20155.49}, {'field': 'offline_amount', 'old_value': 27512.83, 'new_value': 27578.35}, {'field': 'total_amount', 'old_value': 43596.57, 'new_value': 47733.84}, {'field': 'order_count', 'old_value': 251, 'new_value': 291}]
2025-06-14 12:01:18,589 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGD
2025-06-14 12:01:18,996 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGD
2025-06-14 12:01:18,996 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9403.0, 'new_value': 9532.0}, {'field': 'total_amount', 'old_value': 9403.0, 'new_value': 9532.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-06-14 12:01:18,996 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMID
2025-06-14 12:01:19,496 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMID
2025-06-14 12:01:19,496 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9112.88, 'new_value': 9674.16}, {'field': 'offline_amount', 'old_value': 124444.53, 'new_value': 134806.03}, {'field': 'total_amount', 'old_value': 133557.41, 'new_value': 144480.19}, {'field': 'order_count', 'old_value': 886, 'new_value': 961}]
2025-06-14 12:01:19,496 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKD
2025-06-14 12:01:19,949 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKD
2025-06-14 12:01:19,949 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20688.2, 'new_value': 24070.7}, {'field': 'total_amount', 'old_value': 20688.2, 'new_value': 24070.7}, {'field': 'order_count', 'old_value': 21, 'new_value': 24}]
2025-06-14 12:01:19,949 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMND
2025-06-14 12:01:20,480 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMND
2025-06-14 12:01:20,480 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 144456.29, 'new_value': 159781.94}, {'field': 'total_amount', 'old_value': 144456.29, 'new_value': 159781.94}, {'field': 'order_count', 'old_value': 4272, 'new_value': 4690}]
2025-06-14 12:01:20,480 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQD
2025-06-14 12:01:20,980 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQD
2025-06-14 12:01:20,980 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 75881.75, 'new_value': 80804.02}, {'field': 'offline_amount', 'old_value': 74159.05, 'new_value': 81786.55}, {'field': 'total_amount', 'old_value': 150040.8, 'new_value': 162590.57}, {'field': 'order_count', 'old_value': 1319, 'new_value': 1429}]
2025-06-14 12:01:20,980 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRD
2025-06-14 12:01:21,511 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRD
2025-06-14 12:01:21,511 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5290.0, 'new_value': 6170.0}, {'field': 'total_amount', 'old_value': 5290.0, 'new_value': 6170.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 27}]
2025-06-14 12:01:21,511 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSD
2025-06-14 12:01:22,074 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSD
2025-06-14 12:01:22,074 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14768.0, 'new_value': 16688.0}, {'field': 'total_amount', 'old_value': 48077.0, 'new_value': 49997.0}, {'field': 'order_count', 'old_value': 46, 'new_value': 49}]
2025-06-14 12:01:22,074 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMVD
2025-06-14 12:01:22,558 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMVD
2025-06-14 12:01:22,558 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 111950.0, 'new_value': 112342.0}, {'field': 'offline_amount', 'old_value': 115275.0, 'new_value': 116589.0}, {'field': 'total_amount', 'old_value': 227225.0, 'new_value': 228931.0}, {'field': 'order_count', 'old_value': 118, 'new_value': 120}]
2025-06-14 12:01:22,558 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUQ
2025-06-14 12:01:23,011 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUQ
2025-06-14 12:01:23,011 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23847.2, 'new_value': 25263.2}, {'field': 'total_amount', 'old_value': 23847.2, 'new_value': 25263.2}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-06-14 12:01:23,011 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0R
2025-06-14 12:01:23,464 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0R
2025-06-14 12:01:23,464 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23473.0, 'new_value': 28618.0}, {'field': 'total_amount', 'old_value': 23473.0, 'new_value': 28618.0}, {'field': 'order_count', 'old_value': 70, 'new_value': 89}]
2025-06-14 12:01:23,464 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2R
2025-06-14 12:01:23,917 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2R
2025-06-14 12:01:23,917 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65451.79, 'new_value': 69950.95}, {'field': 'total_amount', 'old_value': 65451.79, 'new_value': 69950.95}, {'field': 'order_count', 'old_value': 1813, 'new_value': 1947}]
2025-06-14 12:01:23,917 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6R
2025-06-14 12:01:24,324 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6R
2025-06-14 12:01:24,324 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8011.0, 'new_value': 8301.0}, {'field': 'total_amount', 'old_value': 8011.0, 'new_value': 8301.0}, {'field': 'order_count', 'old_value': 136, 'new_value': 141}]
2025-06-14 12:01:24,324 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7R
2025-06-14 12:01:24,855 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7R
2025-06-14 12:01:24,855 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26083.3, 'new_value': 28213.3}, {'field': 'total_amount', 'old_value': 32708.7, 'new_value': 34838.7}, {'field': 'order_count', 'old_value': 80, 'new_value': 85}]
2025-06-14 12:01:24,855 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMER
2025-06-14 12:01:25,355 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMER
2025-06-14 12:01:25,355 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39411.19, 'new_value': 46933.77}, {'field': 'total_amount', 'old_value': 39411.19, 'new_value': 46933.77}, {'field': 'order_count', 'old_value': 19, 'new_value': 23}]
2025-06-14 12:01:25,355 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKR
2025-06-14 12:01:25,824 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKR
2025-06-14 12:01:25,824 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 108086.72, 'new_value': 116310.72}, {'field': 'total_amount', 'old_value': 108086.72, 'new_value': 116310.72}, {'field': 'order_count', 'old_value': 604, 'new_value': 652}]
2025-06-14 12:01:25,824 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSR
2025-06-14 12:01:26,402 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSR
2025-06-14 12:01:26,402 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13614.6, 'new_value': 14180.4}, {'field': 'total_amount', 'old_value': 13614.6, 'new_value': 14180.4}, {'field': 'order_count', 'old_value': 128, 'new_value': 133}]
2025-06-14 12:01:26,417 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2S
2025-06-14 12:01:26,949 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2S
2025-06-14 12:01:26,949 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22565.5, 'new_value': 24197.05}, {'field': 'total_amount', 'old_value': 22574.5, 'new_value': 24206.05}, {'field': 'order_count', 'old_value': 899, 'new_value': 969}]
2025-06-14 12:01:26,949 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4S
2025-06-14 12:01:27,449 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4S
2025-06-14 12:01:27,449 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 161628.84, 'new_value': 179101.44}, {'field': 'total_amount', 'old_value': 181963.84, 'new_value': 199436.44}, {'field': 'order_count', 'old_value': 937, 'new_value': 1038}]
2025-06-14 12:01:27,449 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6S
2025-06-14 12:01:27,902 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6S
2025-06-14 12:01:27,902 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36508.0, 'new_value': 40080.0}, {'field': 'total_amount', 'old_value': 39323.0, 'new_value': 42895.0}, {'field': 'order_count', 'old_value': 146, 'new_value': 162}]
2025-06-14 12:01:27,902 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9S
2025-06-14 12:01:28,386 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9S
2025-06-14 12:01:28,386 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 95861.14, 'new_value': 102919.34}, {'field': 'offline_amount', 'old_value': 21300.77, 'new_value': 22441.27}, {'field': 'total_amount', 'old_value': 117161.91, 'new_value': 125360.61}, {'field': 'order_count', 'old_value': 468, 'new_value': 505}]
2025-06-14 12:01:28,386 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMAS
2025-06-14 12:01:28,902 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMAS
2025-06-14 12:01:28,902 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12072.3, 'new_value': 15238.93}, {'field': 'offline_amount', 'old_value': 8060.62, 'new_value': 10563.79}, {'field': 'total_amount', 'old_value': 20132.92, 'new_value': 25802.72}, {'field': 'order_count', 'old_value': 1237, 'new_value': 1582}]
2025-06-14 12:01:28,902 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFS
2025-06-14 12:01:29,371 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFS
2025-06-14 12:01:29,371 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28991.0, 'new_value': 30942.0}, {'field': 'total_amount', 'old_value': 28991.0, 'new_value': 30942.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 21}]
2025-06-14 12:01:29,371 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIS
2025-06-14 12:01:29,839 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIS
2025-06-14 12:01:29,839 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 337748.0, 'new_value': 365393.0}, {'field': 'total_amount', 'old_value': 337748.0, 'new_value': 365393.0}, {'field': 'order_count', 'old_value': 1071, 'new_value': 1151}]
2025-06-14 12:01:29,839 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPS
2025-06-14 12:01:30,339 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPS
2025-06-14 12:01:30,339 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49944.0, 'new_value': 55920.0}, {'field': 'offline_amount', 'old_value': 217825.0, 'new_value': 244020.0}, {'field': 'total_amount', 'old_value': 267769.0, 'new_value': 299940.0}, {'field': 'order_count', 'old_value': 341, 'new_value': 380}]
2025-06-14 12:01:30,339 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMRS
2025-06-14 12:01:30,761 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMRS
2025-06-14 12:01:30,761 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5106.43, 'new_value': 5492.43}, {'field': 'offline_amount', 'old_value': 14506.05, 'new_value': 15759.79}, {'field': 'total_amount', 'old_value': 19612.48, 'new_value': 21252.22}, {'field': 'order_count', 'old_value': 672, 'new_value': 726}]
2025-06-14 12:01:30,761 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTS
2025-06-14 12:01:31,214 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTS
2025-06-14 12:01:31,214 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1659.82, 'new_value': 2261.02}, {'field': 'offline_amount', 'old_value': 58060.99, 'new_value': 77508.73}, {'field': 'total_amount', 'old_value': 59720.81, 'new_value': 79769.75}, {'field': 'order_count', 'old_value': 917, 'new_value': 1190}]
2025-06-14 12:01:31,214 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVS
2025-06-14 12:01:31,761 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVS
2025-06-14 12:01:31,761 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 125515.0, 'new_value': 136978.0}, {'field': 'total_amount', 'old_value': 125515.0, 'new_value': 136978.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 28}]
2025-06-14 12:01:31,761 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0T
2025-06-14 12:01:32,214 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0T
2025-06-14 12:01:32,214 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 760138.7, 'new_value': 813390.7}, {'field': 'total_amount', 'old_value': 818617.4, 'new_value': 871869.4}, {'field': 'order_count', 'old_value': 1497, 'new_value': 1612}]
2025-06-14 12:01:32,214 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1T
2025-06-14 12:01:32,761 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1T
2025-06-14 12:01:32,761 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14467.83, 'new_value': 14866.83}, {'field': 'total_amount', 'old_value': 14467.83, 'new_value': 14866.83}, {'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-06-14 12:01:32,761 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5T
2025-06-14 12:01:33,245 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5T
2025-06-14 12:01:33,245 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 69281.45, 'new_value': 74592.7}, {'field': 'offline_amount', 'old_value': 44579.0, 'new_value': 47580.0}, {'field': 'total_amount', 'old_value': 113860.45, 'new_value': 122172.7}, {'field': 'order_count', 'old_value': 1192, 'new_value': 1283}]
2025-06-14 12:01:33,245 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6T
2025-06-14 12:01:33,636 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6T
2025-06-14 12:01:33,636 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 271531.07, 'new_value': 299424.28}, {'field': 'total_amount', 'old_value': 271785.23, 'new_value': 299678.44}, {'field': 'order_count', 'old_value': 691, 'new_value': 773}]
2025-06-14 12:01:33,636 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7T
2025-06-14 12:01:34,105 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7T
2025-06-14 12:01:34,105 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 257780.0, 'new_value': 282275.0}, {'field': 'total_amount', 'old_value': 257780.0, 'new_value': 282275.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 31}]
2025-06-14 12:01:34,105 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMAT
2025-06-14 12:01:34,589 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMAT
2025-06-14 12:01:34,589 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 133216.1, 'new_value': 143041.0}, {'field': 'total_amount', 'old_value': 133216.1, 'new_value': 143041.0}, {'field': 'order_count', 'old_value': 3976, 'new_value': 4308}]
2025-06-14 12:01:34,589 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMET
2025-06-14 12:01:35,011 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMET
2025-06-14 12:01:35,011 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70734.0, 'new_value': 76632.0}, {'field': 'total_amount', 'old_value': 70734.0, 'new_value': 76632.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 18}]
2025-06-14 12:01:35,011 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFT
2025-06-14 12:01:35,511 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFT
2025-06-14 12:01:35,511 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 317858.0, 'new_value': 350792.0}, {'field': 'total_amount', 'old_value': 317858.0, 'new_value': 350792.0}, {'field': 'order_count', 'old_value': 1539, 'new_value': 1681}]
2025-06-14 12:01:35,511 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKT
2025-06-14 12:01:35,980 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKT
2025-06-14 12:01:35,980 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20550.9, 'new_value': 23862.1}, {'field': 'total_amount', 'old_value': 20550.9, 'new_value': 23862.1}, {'field': 'order_count', 'old_value': 116, 'new_value': 153}]
2025-06-14 12:01:35,980 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBK
2025-06-14 12:01:36,449 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBK
2025-06-14 12:01:36,449 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 211543.0, 'new_value': 235243.0}, {'field': 'total_amount', 'old_value': 211543.0, 'new_value': 235243.0}, {'field': 'order_count', 'old_value': 209, 'new_value': 236}]
2025-06-14 12:01:36,449 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFK
2025-06-14 12:01:36,917 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFK
2025-06-14 12:01:36,917 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43413.0, 'new_value': 49413.0}, {'field': 'offline_amount', 'old_value': 69315.0, 'new_value': 73473.0}, {'field': 'total_amount', 'old_value': 112728.0, 'new_value': 122886.0}, {'field': 'order_count', 'old_value': 2571, 'new_value': 2772}]
2025-06-14 12:01:36,917 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGK
2025-06-14 12:01:37,370 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGK
2025-06-14 12:01:37,370 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13632.53, 'new_value': 14873.19}, {'field': 'offline_amount', 'old_value': 13390.7, 'new_value': 14703.82}, {'field': 'total_amount', 'old_value': 27023.23, 'new_value': 29577.01}, {'field': 'order_count', 'old_value': 1212, 'new_value': 1334}]
2025-06-14 12:01:37,386 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIK
2025-06-14 12:01:37,855 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIK
2025-06-14 12:01:37,855 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60844.88, 'new_value': 67858.79}, {'field': 'total_amount', 'old_value': 66160.07, 'new_value': 73173.98}, {'field': 'order_count', 'old_value': 3425, 'new_value': 3850}]
2025-06-14 12:01:37,855 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNK
2025-06-14 12:01:38,402 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNK
2025-06-14 12:01:38,402 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 169575.46, 'new_value': 182216.16}, {'field': 'total_amount', 'old_value': 171520.1, 'new_value': 184160.8}, {'field': 'order_count', 'old_value': 8573, 'new_value': 9285}]
2025-06-14 12:01:38,402 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQK
2025-06-14 12:01:38,808 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQK
2025-06-14 12:01:38,808 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23430.24, 'new_value': 26011.91}, {'field': 'offline_amount', 'old_value': 16448.15, 'new_value': 18127.36}, {'field': 'total_amount', 'old_value': 39878.39, 'new_value': 44139.27}, {'field': 'order_count', 'old_value': 2306, 'new_value': 2553}]
2025-06-14 12:01:38,808 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTK
2025-06-14 12:01:39,308 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTK
2025-06-14 12:01:39,308 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12973.0, 'new_value': 14473.0}, {'field': 'total_amount', 'old_value': 12973.0, 'new_value': 14473.0}, {'field': 'order_count', 'old_value': 127, 'new_value': 139}]
2025-06-14 12:01:39,308 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVK
2025-06-14 12:01:39,824 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVK
2025-06-14 12:01:39,824 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41989.06, 'new_value': 46175.42}, {'field': 'offline_amount', 'old_value': 42536.19, 'new_value': 48022.29}, {'field': 'total_amount', 'old_value': 84525.25, 'new_value': 94197.71}, {'field': 'order_count', 'old_value': 3545, 'new_value': 3931}]
2025-06-14 12:01:39,824 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWK
2025-06-14 12:01:40,261 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWK
2025-06-14 12:01:40,261 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 307064.77, 'new_value': 326449.67}, {'field': 'total_amount', 'old_value': 307064.77, 'new_value': 326449.67}, {'field': 'order_count', 'old_value': 3373, 'new_value': 3635}]
2025-06-14 12:01:40,261 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYK
2025-06-14 12:01:40,730 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYK
2025-06-14 12:01:40,730 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45062.0, 'new_value': 52309.0}, {'field': 'total_amount', 'old_value': 45062.0, 'new_value': 52309.0}, {'field': 'order_count', 'old_value': 32, 'new_value': 36}]
2025-06-14 12:01:40,730 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZK
2025-06-14 12:01:41,261 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZK
2025-06-14 12:01:41,261 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61086.0, 'new_value': 66452.0}, {'field': 'total_amount', 'old_value': 61086.0, 'new_value': 66452.0}, {'field': 'order_count', 'old_value': 216, 'new_value': 236}]
2025-06-14 12:01:41,261 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1L
2025-06-14 12:01:42,058 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1L
2025-06-14 12:01:42,058 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 151212.31, 'new_value': 162033.71}, {'field': 'offline_amount', 'old_value': 54997.4, 'new_value': 57997.4}, {'field': 'total_amount', 'old_value': 206209.71, 'new_value': 220031.11}, {'field': 'order_count', 'old_value': 638, 'new_value': 680}]
2025-06-14 12:01:42,058 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM5L
2025-06-14 12:01:42,574 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM5L
2025-06-14 12:01:42,574 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 114540.0, 'new_value': 124800.0}, {'field': 'total_amount', 'old_value': 114540.0, 'new_value': 124800.0}, {'field': 'order_count', 'old_value': 9545, 'new_value': 10400}]
2025-06-14 12:01:42,574 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBL
2025-06-14 12:01:43,027 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBL
2025-06-14 12:01:43,027 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 201.1, 'new_value': 329.22}, {'field': 'offline_amount', 'old_value': 17940.5, 'new_value': 19040.5}, {'field': 'total_amount', 'old_value': 18141.6, 'new_value': 19369.72}, {'field': 'order_count', 'old_value': 132, 'new_value': 140}]
2025-06-14 12:01:43,027 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCL
2025-06-14 12:01:43,527 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCL
2025-06-14 12:01:43,527 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 75226.43, 'new_value': 83102.23}, {'field': 'offline_amount', 'old_value': 198216.6, 'new_value': 215910.01}, {'field': 'total_amount', 'old_value': 273443.03, 'new_value': 299012.24}, {'field': 'order_count', 'old_value': 2450, 'new_value': 2749}]
2025-06-14 12:01:43,527 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDL
2025-06-14 12:01:43,980 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDL
2025-06-14 12:01:43,980 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 81646.69, 'new_value': 89181.5}, {'field': 'offline_amount', 'old_value': 98534.91, 'new_value': 108534.91}, {'field': 'total_amount', 'old_value': 180181.6, 'new_value': 197716.41}, {'field': 'order_count', 'old_value': 574, 'new_value': 635}]
2025-06-14 12:01:43,980 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMHL
2025-06-14 12:01:44,558 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMHL
2025-06-14 12:01:44,558 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 12000.0}, {'field': 'total_amount', 'old_value': 500.0, 'new_value': 12500.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-14 12:01:44,558 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKL
2025-06-14 12:01:45,089 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKL
2025-06-14 12:01:45,089 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4179.0, 'new_value': 4208.9}, {'field': 'offline_amount', 'old_value': 12679.9, 'new_value': 24566.7}, {'field': 'total_amount', 'old_value': 16858.9, 'new_value': 28775.6}, {'field': 'order_count', 'old_value': 25, 'new_value': 38}]
2025-06-14 12:01:45,089 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMML
2025-06-14 12:01:45,574 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMML
2025-06-14 12:01:45,574 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 110949.3, 'new_value': 117640.46}, {'field': 'total_amount', 'old_value': 110949.3, 'new_value': 117640.46}, {'field': 'order_count', 'old_value': 396, 'new_value': 417}]
2025-06-14 12:01:45,574 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPL
2025-06-14 12:01:46,027 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPL
2025-06-14 12:01:46,027 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24662.0, 'new_value': 24962.0}, {'field': 'total_amount', 'old_value': 24662.0, 'new_value': 24962.0}, {'field': 'order_count', 'old_value': 61, 'new_value': 62}]
2025-06-14 12:01:46,027 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTL
2025-06-14 12:01:46,495 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTL
2025-06-14 12:01:46,495 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1348.5, 'new_value': 1624.1}, {'field': 'offline_amount', 'old_value': 34332.6, 'new_value': 44795.1}, {'field': 'total_amount', 'old_value': 35681.1, 'new_value': 46419.2}, {'field': 'order_count', 'old_value': 221, 'new_value': 268}]
2025-06-14 12:01:46,495 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVL
2025-06-14 12:01:46,980 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVL
2025-06-14 12:01:46,980 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 163800.14, 'new_value': 179031.5}, {'field': 'total_amount', 'old_value': 163800.14, 'new_value': 179031.5}, {'field': 'order_count', 'old_value': 570, 'new_value': 626}]
2025-06-14 12:01:46,980 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYL
2025-06-14 12:01:47,433 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYL
2025-06-14 12:01:47,433 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14429.0, 'new_value': 15905.0}, {'field': 'total_amount', 'old_value': 14429.0, 'new_value': 15905.0}, {'field': 'order_count', 'old_value': 119, 'new_value': 128}]
2025-06-14 12:01:47,433 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0M
2025-06-14 12:01:47,933 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0M
2025-06-14 12:01:47,933 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69613.0, 'new_value': 78399.0}, {'field': 'total_amount', 'old_value': 78232.0, 'new_value': 87018.0}, {'field': 'order_count', 'old_value': 5306, 'new_value': 5992}]
2025-06-14 12:01:47,933 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1M
2025-06-14 12:01:48,386 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1M
2025-06-14 12:01:48,386 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42449.33, 'new_value': 46147.29}, {'field': 'offline_amount', 'old_value': 418278.27, 'new_value': 456627.27}, {'field': 'total_amount', 'old_value': 460727.6, 'new_value': 502774.56}, {'field': 'order_count', 'old_value': 1883, 'new_value': 2068}]
2025-06-14 12:01:48,386 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM2M
2025-06-14 12:01:48,870 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM2M
2025-06-14 12:01:48,870 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 173613.52, 'new_value': 175491.52}, {'field': 'offline_amount', 'old_value': 133553.24, 'new_value': 135929.24}, {'field': 'total_amount', 'old_value': 307166.76, 'new_value': 311420.76}, {'field': 'order_count', 'old_value': 2095, 'new_value': 2126}]
2025-06-14 12:01:48,870 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM6M
2025-06-14 12:01:49,324 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM6M
2025-06-14 12:01:49,324 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12906.92, 'new_value': 16906.92}, {'field': 'total_amount', 'old_value': 13464.92, 'new_value': 17464.92}, {'field': 'order_count', 'old_value': 34, 'new_value': 39}]
2025-06-14 12:01:49,324 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMAM
2025-06-14 12:01:49,808 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMAM
2025-06-14 12:01:49,808 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 900000.0, 'new_value': 950000.0}, {'field': 'total_amount', 'old_value': 1000000.0, 'new_value': 1050000.0}, {'field': 'order_count', 'old_value': 364, 'new_value': 365}]
2025-06-14 12:01:49,808 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCM
2025-06-14 12:01:50,386 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCM
2025-06-14 12:01:50,386 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122881.62, 'new_value': 131961.77}, {'field': 'total_amount', 'old_value': 122881.62, 'new_value': 131961.77}, {'field': 'order_count', 'old_value': 726, 'new_value': 781}]
2025-06-14 12:01:50,386 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDM
2025-06-14 12:01:50,917 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDM
2025-06-14 12:01:50,917 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 69831.0, 'new_value': 74851.0}, {'field': 'offline_amount', 'old_value': 41117.0, 'new_value': 43790.0}, {'field': 'total_amount', 'old_value': 110948.0, 'new_value': 118641.0}, {'field': 'order_count', 'old_value': 1592, 'new_value': 1730}]
2025-06-14 12:01:50,917 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEM
2025-06-14 12:01:51,370 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEM
2025-06-14 12:01:51,370 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 748141.82, 'new_value': 814772.64}, {'field': 'offline_amount', 'old_value': 114844.0, 'new_value': 123548.0}, {'field': 'total_amount', 'old_value': 862985.82, 'new_value': 938320.64}, {'field': 'order_count', 'old_value': 3250, 'new_value': 3527}]
2025-06-14 12:01:51,370 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGM
2025-06-14 12:01:51,886 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGM
2025-06-14 12:01:51,886 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13151.94, 'new_value': 13289.94}, {'field': 'offline_amount', 'old_value': 15719.6, 'new_value': 17289.6}, {'field': 'total_amount', 'old_value': 28871.54, 'new_value': 30579.54}, {'field': 'order_count', 'old_value': 6583, 'new_value': 6591}]
2025-06-14 12:01:51,886 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMHM
2025-06-14 12:01:52,339 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMHM
2025-06-14 12:01:52,339 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22366.06, 'new_value': 24705.42}, {'field': 'offline_amount', 'old_value': 222714.43, 'new_value': 240673.87}, {'field': 'total_amount', 'old_value': 245080.49, 'new_value': 265379.29}, {'field': 'order_count', 'old_value': 1122, 'new_value': 1207}]
2025-06-14 12:01:52,339 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJM
2025-06-14 12:01:52,870 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJM
2025-06-14 12:01:52,870 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 90080.8, 'new_value': 99334.3}, {'field': 'total_amount', 'old_value': 154247.6, 'new_value': 163501.1}, {'field': 'order_count', 'old_value': 3923, 'new_value': 4197}]
2025-06-14 12:01:52,870 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQM
2025-06-14 12:01:53,292 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQM
2025-06-14 12:01:53,292 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15064.2, 'new_value': 16064.2}, {'field': 'total_amount', 'old_value': 19781.95, 'new_value': 20781.95}, {'field': 'order_count', 'old_value': 146, 'new_value': 147}]
2025-06-14 12:01:53,292 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUM
2025-06-14 12:01:53,730 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUM
2025-06-14 12:01:53,730 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 84343.76, 'new_value': 94187.01}, {'field': 'offline_amount', 'old_value': 143125.42, 'new_value': 152769.88}, {'field': 'total_amount', 'old_value': 227469.18, 'new_value': 246956.89}, {'field': 'order_count', 'old_value': 1991, 'new_value': 2186}]
2025-06-14 12:01:53,730 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXM
2025-06-14 12:01:54,214 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXM
2025-06-14 12:01:54,214 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 90, 'new_value': 107}]
2025-06-14 12:01:54,214 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYM
2025-06-14 12:01:54,652 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYM
2025-06-14 12:01:54,652 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22726.28, 'new_value': 24617.24}, {'field': 'offline_amount', 'old_value': 27510.67, 'new_value': 29368.32}, {'field': 'total_amount', 'old_value': 50236.95, 'new_value': 53985.56}, {'field': 'order_count', 'old_value': 1317, 'new_value': 1412}]
2025-06-14 12:01:54,652 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZM
2025-06-14 12:01:55,089 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZM
2025-06-14 12:01:55,089 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52186.0, 'new_value': 57518.0}, {'field': 'total_amount', 'old_value': 52186.0, 'new_value': 57518.0}, {'field': 'order_count', 'old_value': 123, 'new_value': 136}]
2025-06-14 12:01:55,089 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0N
2025-06-14 12:01:55,527 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0N
2025-06-14 12:01:55,527 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23215.0, 'new_value': 24106.0}, {'field': 'total_amount', 'old_value': 23215.0, 'new_value': 24106.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-06-14 12:01:55,527 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR01
2025-06-14 12:01:56,011 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR01
2025-06-14 12:01:56,011 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 54983.0, 'new_value': 60323.0}, {'field': 'offline_amount', 'old_value': 54043.0, 'new_value': 58830.0}, {'field': 'total_amount', 'old_value': 109026.0, 'new_value': 119153.0}, {'field': 'order_count', 'old_value': 4433, 'new_value': 4902}]
2025-06-14 12:01:56,011 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT01
2025-06-14 12:01:56,558 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT01
2025-06-14 12:01:56,558 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 371570.0, 'new_value': 404598.0}, {'field': 'total_amount', 'old_value': 371570.0, 'new_value': 404598.0}, {'field': 'order_count', 'old_value': 470, 'new_value': 515}]
2025-06-14 12:01:56,558 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU01
2025-06-14 12:01:57,058 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU01
2025-06-14 12:01:57,058 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45322.8, 'new_value': 50210.4}, {'field': 'total_amount', 'old_value': 45322.8, 'new_value': 50210.4}, {'field': 'order_count', 'old_value': 207, 'new_value': 228}]
2025-06-14 12:01:57,058 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW01
2025-06-14 12:01:57,448 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW01
2025-06-14 12:01:57,448 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17634.32, 'new_value': 19317.42}, {'field': 'offline_amount', 'old_value': 21862.87, 'new_value': 24188.2}, {'field': 'total_amount', 'old_value': 39497.19, 'new_value': 43505.62}, {'field': 'order_count', 'old_value': 2055, 'new_value': 2267}]
2025-06-14 12:01:57,448 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM211
2025-06-14 12:01:57,964 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM211
2025-06-14 12:01:57,964 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66292.1, 'new_value': 71420.7}, {'field': 'total_amount', 'old_value': 66292.1, 'new_value': 71420.7}, {'field': 'order_count', 'old_value': 309, 'new_value': 337}]
2025-06-14 12:01:57,964 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM311
2025-06-14 12:01:58,464 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM311
2025-06-14 12:01:58,464 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47200.0, 'new_value': 48560.0}, {'field': 'total_amount', 'old_value': 47200.0, 'new_value': 48560.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-06-14 12:01:58,464 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM411
2025-06-14 12:01:59,011 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM411
2025-06-14 12:01:59,011 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19493.5, 'new_value': 21435.5}, {'field': 'total_amount', 'old_value': 19493.5, 'new_value': 21435.5}, {'field': 'order_count', 'old_value': 35, 'new_value': 37}]
2025-06-14 12:01:59,011 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM611
2025-06-14 12:01:59,620 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM611
2025-06-14 12:01:59,620 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7743.21, 'new_value': 8294.21}, {'field': 'offline_amount', 'old_value': 123503.8, 'new_value': 139429.8}, {'field': 'total_amount', 'old_value': 131247.01, 'new_value': 147724.01}, {'field': 'order_count', 'old_value': 690, 'new_value': 775}]
2025-06-14 12:01:59,620 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM711
2025-06-14 12:02:00,105 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM711
2025-06-14 12:02:00,105 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15991.0, 'new_value': 16825.0}, {'field': 'total_amount', 'old_value': 15991.0, 'new_value': 16825.0}, {'field': 'order_count', 'old_value': 46, 'new_value': 49}]
2025-06-14 12:02:00,105 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA11
2025-06-14 12:02:00,589 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA11
2025-06-14 12:02:00,589 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20084.0, 'new_value': 26924.0}, {'field': 'total_amount', 'old_value': 20084.0, 'new_value': 26924.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 23}]
2025-06-14 12:02:00,589 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME11
2025-06-14 12:02:01,136 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME11
2025-06-14 12:02:01,136 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68314.0, 'new_value': 71826.7}, {'field': 'total_amount', 'old_value': 68314.0, 'new_value': 71826.7}, {'field': 'order_count', 'old_value': 1903, 'new_value': 2005}]
2025-06-14 12:02:01,136 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMG11
2025-06-14 12:02:01,652 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMG11
2025-06-14 12:02:01,652 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104354.9, 'new_value': 114725.9}, {'field': 'total_amount', 'old_value': 104354.9, 'new_value': 114725.9}, {'field': 'order_count', 'old_value': 457, 'new_value': 503}]
2025-06-14 12:02:01,652 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMI11
2025-06-14 12:02:02,089 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMI11
2025-06-14 12:02:02,089 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2936.33, 'new_value': 3456.23}, {'field': 'offline_amount', 'old_value': 6933.04, 'new_value': 8628.29}, {'field': 'total_amount', 'old_value': 9869.37, 'new_value': 12084.52}, {'field': 'order_count', 'old_value': 105, 'new_value': 124}]
2025-06-14 12:02:02,089 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK11
2025-06-14 12:02:02,573 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK11
2025-06-14 12:02:02,573 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17300.05, 'new_value': 18981.05}, {'field': 'total_amount', 'old_value': 17300.05, 'new_value': 18981.05}, {'field': 'order_count', 'old_value': 364, 'new_value': 404}]
2025-06-14 12:02:02,573 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMM11
2025-06-14 12:02:03,027 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMM11
2025-06-14 12:02:03,027 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 141769.88, 'new_value': 156087.17}, {'field': 'total_amount', 'old_value': 175951.63, 'new_value': 190268.92}, {'field': 'order_count', 'old_value': 2946, 'new_value': 3100}]
2025-06-14 12:02:03,027 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN11
2025-06-14 12:02:03,495 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN11
2025-06-14 12:02:03,495 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 164737.0, 'new_value': 179537.0}, {'field': 'total_amount', 'old_value': 164737.0, 'new_value': 179537.0}, {'field': 'order_count', 'old_value': 137, 'new_value': 145}]
2025-06-14 12:02:03,495 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO11
2025-06-14 12:02:03,964 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO11
2025-06-14 12:02:03,964 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57422.0, 'new_value': 61911.0}, {'field': 'total_amount', 'old_value': 57590.0, 'new_value': 62079.0}, {'field': 'order_count', 'old_value': 186, 'new_value': 198}]
2025-06-14 12:02:03,964 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ11
2025-06-14 12:02:04,433 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ11
2025-06-14 12:02:04,433 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28227.51, 'new_value': 34480.51}, {'field': 'total_amount', 'old_value': 28698.51, 'new_value': 34951.51}, {'field': 'order_count', 'old_value': 178, 'new_value': 218}]
2025-06-14 12:02:04,433 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU11
2025-06-14 12:02:04,855 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU11
2025-06-14 12:02:04,855 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67184.47, 'new_value': 71953.2}, {'field': 'total_amount', 'old_value': 67184.47, 'new_value': 71953.2}, {'field': 'order_count', 'old_value': 310, 'new_value': 338}]
2025-06-14 12:02:04,855 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV11
2025-06-14 12:02:05,308 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV11
2025-06-14 12:02:05,308 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 149569.4, 'new_value': 162844.4}, {'field': 'total_amount', 'old_value': 149569.4, 'new_value': 162844.4}, {'field': 'order_count', 'old_value': 209, 'new_value': 224}]
2025-06-14 12:02:05,308 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW11
2025-06-14 12:02:05,777 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW11
2025-06-14 12:02:05,777 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35105.0, 'new_value': 37715.0}, {'field': 'total_amount', 'old_value': 35105.0, 'new_value': 37715.0}, {'field': 'order_count', 'old_value': 1636, 'new_value': 1750}]
2025-06-14 12:02:05,777 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX11
2025-06-14 12:02:06,308 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX11
2025-06-14 12:02:06,308 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41054.0, 'new_value': 44103.0}, {'field': 'offline_amount', 'old_value': 113305.0, 'new_value': 122650.0}, {'field': 'total_amount', 'old_value': 154359.0, 'new_value': 166753.0}, {'field': 'order_count', 'old_value': 693, 'new_value': 753}]
2025-06-14 12:02:06,308 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM021
2025-06-14 12:02:06,745 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM021
2025-06-14 12:02:06,745 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 213907.6, 'new_value': 226286.6}, {'field': 'total_amount', 'old_value': 213907.6, 'new_value': 226286.6}, {'field': 'order_count', 'old_value': 4648, 'new_value': 4931}]
2025-06-14 12:02:06,745 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM121
2025-06-14 12:02:07,214 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM121
2025-06-14 12:02:07,214 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48611.42, 'new_value': 52719.71}, {'field': 'offline_amount', 'old_value': 58164.26, 'new_value': 63798.98}, {'field': 'total_amount', 'old_value': 106775.68, 'new_value': 116518.69}, {'field': 'order_count', 'old_value': 2776, 'new_value': 3039}]
2025-06-14 12:02:07,214 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM221
2025-06-14 12:02:07,698 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM221
2025-06-14 12:02:07,698 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91637.25, 'new_value': 100462.52}, {'field': 'total_amount', 'old_value': 91637.25, 'new_value': 100462.52}, {'field': 'order_count', 'old_value': 398, 'new_value': 435}]
2025-06-14 12:02:07,698 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM421
2025-06-14 12:02:08,136 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM421
2025-06-14 12:02:08,136 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18131.0, 'new_value': 24149.0}, {'field': 'total_amount', 'old_value': 18131.0, 'new_value': 24149.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-14 12:02:08,136 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM521
2025-06-14 12:02:08,620 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM521
2025-06-14 12:02:08,620 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 85200.25, 'new_value': 94535.25}, {'field': 'total_amount', 'old_value': 140962.45, 'new_value': 150297.45}, {'field': 'order_count', 'old_value': 241, 'new_value': 260}]
2025-06-14 12:02:08,620 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM621
2025-06-14 12:02:09,058 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM621
2025-06-14 12:02:09,058 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45325.23, 'new_value': 47951.83}, {'field': 'total_amount', 'old_value': 45325.23, 'new_value': 47951.83}, {'field': 'order_count', 'old_value': 2893, 'new_value': 3077}]
2025-06-14 12:02:09,058 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM721
2025-06-14 12:02:09,495 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM721
2025-06-14 12:02:09,495 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1375.0, 'new_value': 1425.0}, {'field': 'offline_amount', 'old_value': 17635.0, 'new_value': 19185.0}, {'field': 'total_amount', 'old_value': 19010.0, 'new_value': 20610.0}, {'field': 'order_count', 'old_value': 253, 'new_value': 273}]
2025-06-14 12:02:09,495 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM821
2025-06-14 12:02:10,011 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM821
2025-06-14 12:02:10,011 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 72341.0, 'new_value': 78267.0}, {'field': 'offline_amount', 'old_value': 77706.0, 'new_value': 95274.0}, {'field': 'total_amount', 'old_value': 150047.0, 'new_value': 173541.0}, {'field': 'order_count', 'old_value': 87573, 'new_value': 111067}]
2025-06-14 12:02:10,011 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA21
2025-06-14 12:02:10,433 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA21
2025-06-14 12:02:10,433 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2633.12, 'new_value': 2678.02}, {'field': 'offline_amount', 'old_value': 10265.8, 'new_value': 12249.68}, {'field': 'total_amount', 'old_value': 12898.92, 'new_value': 14927.7}, {'field': 'order_count', 'old_value': 48, 'new_value': 53}]
2025-06-14 12:02:10,433 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB21
2025-06-14 12:02:10,933 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB21
2025-06-14 12:02:10,933 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34229.0, 'new_value': 45406.0}, {'field': 'total_amount', 'old_value': 34229.0, 'new_value': 45406.0}, {'field': 'order_count', 'old_value': 4864, 'new_value': 6578}]
2025-06-14 12:02:10,933 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC21
2025-06-14 12:02:11,448 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC21
2025-06-14 12:02:11,448 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22817.0, 'new_value': 30268.0}, {'field': 'total_amount', 'old_value': 22817.0, 'new_value': 30268.0}, {'field': 'order_count', 'old_value': 4864, 'new_value': 6578}]
2025-06-14 12:02:11,448 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD21
2025-06-14 12:02:11,886 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD21
2025-06-14 12:02:11,886 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11011.75, 'new_value': 11974.89}, {'field': 'offline_amount', 'old_value': 6476.69, 'new_value': 6797.69}, {'field': 'total_amount', 'old_value': 17488.44, 'new_value': 18772.58}, {'field': 'order_count', 'old_value': 646, 'new_value': 698}]
2025-06-14 12:02:11,886 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME21
2025-06-14 12:02:12,370 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME21
2025-06-14 12:02:12,370 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104469.0, 'new_value': 116895.0}, {'field': 'total_amount', 'old_value': 104469.0, 'new_value': 116895.0}, {'field': 'order_count', 'old_value': 11032, 'new_value': 12300}]
2025-06-14 12:02:12,370 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMI21
2025-06-14 12:02:12,886 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMI21
2025-06-14 12:02:12,886 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6108.25, 'new_value': 7523.51}, {'field': 'offline_amount', 'old_value': 23671.55, 'new_value': 30821.13}, {'field': 'total_amount', 'old_value': 29779.8, 'new_value': 38344.64}, {'field': 'order_count', 'old_value': 632, 'new_value': 839}]
2025-06-14 12:02:12,886 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK21
2025-06-14 12:02:13,370 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK21
2025-06-14 12:02:13,370 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 97197.0, 'new_value': 101692.0}, {'field': 'offline_amount', 'old_value': 549190.0, 'new_value': 598095.0}, {'field': 'total_amount', 'old_value': 646387.0, 'new_value': 699787.0}, {'field': 'order_count', 'old_value': 15862, 'new_value': 17389}]
2025-06-14 12:02:13,370 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML21
2025-06-14 12:02:13,917 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML21
2025-06-14 12:02:13,917 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 143623.0, 'new_value': 157987.0}, {'field': 'total_amount', 'old_value': 143623.0, 'new_value': 157987.0}, {'field': 'order_count', 'old_value': 3329, 'new_value': 3660}]
2025-06-14 12:02:13,917 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMM21
2025-06-14 12:02:14,323 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMM21
2025-06-14 12:02:14,323 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 10560.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 10560.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-14 12:02:14,323 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO21
2025-06-14 12:02:14,792 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO21
2025-06-14 12:02:14,792 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7206.0, 'new_value': 8363.0}, {'field': 'total_amount', 'old_value': 7206.0, 'new_value': 8363.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 18}]
2025-06-14 12:02:14,792 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP21
2025-06-14 12:02:15,339 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP21
2025-06-14 12:02:15,339 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20436.32, 'new_value': 21873.82}, {'field': 'total_amount', 'old_value': 20436.32, 'new_value': 21873.82}, {'field': 'order_count', 'old_value': 630, 'new_value': 668}]
2025-06-14 12:02:15,339 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS21
2025-06-14 12:02:15,730 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS21
2025-06-14 12:02:15,730 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 488893.7, 'new_value': 529386.65}, {'field': 'total_amount', 'old_value': 488893.7, 'new_value': 529386.65}, {'field': 'order_count', 'old_value': 1682, 'new_value': 1822}]
2025-06-14 12:02:15,730 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT21
2025-06-14 12:02:16,245 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT21
2025-06-14 12:02:16,245 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23153.9, 'new_value': 25366.9}, {'field': 'total_amount', 'old_value': 23153.9, 'new_value': 25366.9}, {'field': 'order_count', 'old_value': 127, 'new_value': 137}]
2025-06-14 12:02:16,245 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV21
2025-06-14 12:02:16,620 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV21
2025-06-14 12:02:16,620 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 353236.46, 'new_value': 374528.04}, {'field': 'total_amount', 'old_value': 353236.46, 'new_value': 374528.04}, {'field': 'order_count', 'old_value': 2908, 'new_value': 3103}]
2025-06-14 12:02:16,620 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW21
2025-06-14 12:02:17,073 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW21
2025-06-14 12:02:17,073 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 117894.0, 'new_value': 124565.0}, {'field': 'offline_amount', 'old_value': 7340.0, 'new_value': 7792.0}, {'field': 'total_amount', 'old_value': 125234.0, 'new_value': 132357.0}, {'field': 'order_count', 'old_value': 1192, 'new_value': 1273}]
2025-06-14 12:02:17,073 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ21
2025-06-14 12:02:17,542 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ21
2025-06-14 12:02:17,542 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14703.3, 'new_value': 16107.1}, {'field': 'offline_amount', 'old_value': 25433.0, 'new_value': 28356.9}, {'field': 'total_amount', 'old_value': 40136.3, 'new_value': 44464.0}, {'field': 'order_count', 'old_value': 1690, 'new_value': 1854}]
2025-06-14 12:02:17,542 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM131
2025-06-14 12:02:17,995 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM131
2025-06-14 12:02:17,995 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 351935.21, 'new_value': 394137.97}, {'field': 'total_amount', 'old_value': 351935.21, 'new_value': 394137.97}, {'field': 'order_count', 'old_value': 2147, 'new_value': 2396}]
2025-06-14 12:02:17,995 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM331
2025-06-14 12:02:18,464 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM331
2025-06-14 12:02:18,464 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16985.04, 'new_value': 18522.37}, {'field': 'total_amount', 'old_value': 17751.04, 'new_value': 19288.37}, {'field': 'order_count', 'old_value': 166, 'new_value': 180}]
2025-06-14 12:02:18,464 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM431
2025-06-14 12:02:18,933 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM431
2025-06-14 12:02:18,933 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15319.99, 'new_value': 16295.32}, {'field': 'total_amount', 'old_value': 15319.99, 'new_value': 16295.32}, {'field': 'order_count', 'old_value': 46, 'new_value': 50}]
2025-06-14 12:02:18,933 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM531
2025-06-14 12:02:19,370 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM531
2025-06-14 12:02:19,370 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 215613.75, 'new_value': 225610.56}, {'field': 'total_amount', 'old_value': 231846.68, 'new_value': 241843.49}, {'field': 'order_count', 'old_value': 282, 'new_value': 298}]
2025-06-14 12:02:19,370 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM731
2025-06-14 12:02:19,855 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM731
2025-06-14 12:02:19,855 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70883.0, 'new_value': 82269.0}, {'field': 'total_amount', 'old_value': 118546.0, 'new_value': 129932.0}, {'field': 'order_count', 'old_value': 2445, 'new_value': 2686}]
2025-06-14 12:02:19,855 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM931
2025-06-14 12:02:20,339 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM931
2025-06-14 12:02:20,339 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2371885.18, 'new_value': 2593256.18}, {'field': 'total_amount', 'old_value': 2371885.18, 'new_value': 2593256.18}, {'field': 'order_count', 'old_value': 50746, 'new_value': 54657}]
2025-06-14 12:02:20,339 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA31
2025-06-14 12:02:20,808 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA31
2025-06-14 12:02:20,808 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 494581.84, 'new_value': 538741.66}, {'field': 'total_amount', 'old_value': 494581.84, 'new_value': 538741.66}, {'field': 'order_count', 'old_value': 1491, 'new_value': 1598}]
2025-06-14 12:02:20,808 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB31
2025-06-14 12:02:21,292 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB31
2025-06-14 12:02:21,292 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 334757.7, 'new_value': 362466.1}, {'field': 'total_amount', 'old_value': 334757.7, 'new_value': 362466.1}, {'field': 'order_count', 'old_value': 895, 'new_value': 943}]
2025-06-14 12:02:21,292 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD31
2025-06-14 12:02:21,730 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD31
2025-06-14 12:02:21,730 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4643.9, 'new_value': 6185.0}, {'field': 'total_amount', 'old_value': 4643.9, 'new_value': 6185.0}, {'field': 'order_count', 'old_value': 388, 'new_value': 532}]
2025-06-14 12:02:21,730 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMF31
2025-06-14 12:02:22,261 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMF31
2025-06-14 12:02:22,261 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15952.08, 'new_value': 20588.42}, {'field': 'total_amount', 'old_value': 46470.93, 'new_value': 51107.27}, {'field': 'order_count', 'old_value': 3083, 'new_value': 3399}]
2025-06-14 12:02:22,261 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM01
2025-06-14 12:02:22,745 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM01
2025-06-14 12:02:22,745 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28572.26, 'new_value': 36969.96}, {'field': 'total_amount', 'old_value': 82758.03, 'new_value': 91155.73}, {'field': 'order_count', 'old_value': 5526, 'new_value': 6078}]
2025-06-14 12:02:22,745 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM31
2025-06-14 12:02:23,167 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM31
2025-06-14 12:02:23,167 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1229588.0, 'new_value': 1279588.0}, {'field': 'total_amount', 'old_value': 1229588.0, 'new_value': 1279588.0}, {'field': 'order_count', 'old_value': 1452, 'new_value': 1453}]
2025-06-14 12:02:23,167 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM61
2025-06-14 12:02:23,651 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM61
2025-06-14 12:02:23,651 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100000.0, 'new_value': 105000.0}, {'field': 'total_amount', 'old_value': 100000.0, 'new_value': 105000.0}, {'field': 'order_count', 'old_value': 131, 'new_value': 132}]
2025-06-14 12:02:23,651 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM71
2025-06-14 12:02:24,120 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM71
2025-06-14 12:02:24,120 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100000.0, 'new_value': 105000.0}, {'field': 'total_amount', 'old_value': 110000.0, 'new_value': 115000.0}, {'field': 'order_count', 'old_value': 221, 'new_value': 222}]
2025-06-14 12:02:24,120 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM81
2025-06-14 12:02:24,573 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM81
2025-06-14 12:02:24,573 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31266.0, 'new_value': 34622.0}, {'field': 'total_amount', 'old_value': 31266.0, 'new_value': 34622.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-06-14 12:02:24,589 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC1
2025-06-14 12:02:24,995 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC1
2025-06-14 12:02:24,995 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 321540.83, 'new_value': 355529.53}, {'field': 'total_amount', 'old_value': 321540.83, 'new_value': 355529.53}, {'field': 'order_count', 'old_value': 2263, 'new_value': 2368}]
2025-06-14 12:02:24,995 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF1
2025-06-14 12:02:25,401 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF1
2025-06-14 12:02:25,401 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26271.8, 'new_value': 27281.7}, {'field': 'total_amount', 'old_value': 30432.75, 'new_value': 31442.65}, {'field': 'order_count', 'old_value': 206, 'new_value': 208}]
2025-06-14 12:02:25,401 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ1
2025-06-14 12:02:25,839 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ1
2025-06-14 12:02:25,839 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70603.67, 'new_value': 75157.02}, {'field': 'total_amount', 'old_value': 70603.67, 'new_value': 75157.02}, {'field': 'order_count', 'old_value': 5032, 'new_value': 5352}]
2025-06-14 12:02:25,839 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK1
2025-06-14 12:02:26,355 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK1
2025-06-14 12:02:26,355 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122600.0, 'new_value': 135600.0}, {'field': 'total_amount', 'old_value': 122600.0, 'new_value': 135600.0}, {'field': 'order_count', 'old_value': 291, 'new_value': 318}]
2025-06-14 12:02:26,355 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML1
2025-06-14 12:02:26,855 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML1
2025-06-14 12:02:26,855 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 76246.6, 'new_value': 84340.04}, {'field': 'offline_amount', 'old_value': 21537.44, 'new_value': 22512.44}, {'field': 'total_amount', 'old_value': 97784.04, 'new_value': 106852.48}, {'field': 'order_count', 'old_value': 416, 'new_value': 461}]
2025-06-14 12:02:26,855 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMN1
2025-06-14 12:02:27,354 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMN1
2025-06-14 12:02:27,354 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100000.0, 'new_value': 105000.0}, {'field': 'total_amount', 'old_value': 100000.0, 'new_value': 105000.0}, {'field': 'order_count', 'old_value': 161, 'new_value': 162}]
2025-06-14 12:02:27,354 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO1
2025-06-14 12:02:27,839 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO1
2025-06-14 12:02:27,839 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97524.9, 'new_value': 106222.47}, {'field': 'total_amount', 'old_value': 97524.9, 'new_value': 106222.47}, {'field': 'order_count', 'old_value': 1589, 'new_value': 1716}]
2025-06-14 12:02:27,839 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ1
2025-06-14 12:02:28,308 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ1
2025-06-14 12:02:28,308 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 257555.0, 'new_value': 273502.0}, {'field': 'total_amount', 'old_value': 257555.0, 'new_value': 273502.0}, {'field': 'order_count', 'old_value': 266, 'new_value': 288}]
2025-06-14 12:02:28,308 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS1
2025-06-14 12:02:28,714 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS1
2025-06-14 12:02:28,714 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 240726.0, 'new_value': 251413.0}, {'field': 'total_amount', 'old_value': 240726.0, 'new_value': 251413.0}, {'field': 'order_count', 'old_value': 5526, 'new_value': 5769}]
2025-06-14 12:02:28,714 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW1
2025-06-14 12:02:29,183 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW1
2025-06-14 12:02:29,183 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65023.45, 'new_value': 69700.35}, {'field': 'total_amount', 'old_value': 65023.45, 'new_value': 69700.35}, {'field': 'order_count', 'old_value': 1657, 'new_value': 1781}]
2025-06-14 12:02:29,183 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY1
2025-06-14 12:02:29,620 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY1
2025-06-14 12:02:29,620 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42803.0, 'new_value': 44294.0}, {'field': 'total_amount', 'old_value': 42803.0, 'new_value': 44294.0}, {'field': 'order_count', 'old_value': 387, 'new_value': 407}]
2025-06-14 12:02:29,620 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM02
2025-06-14 12:02:30,120 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM02
2025-06-14 12:02:30,120 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42107.0, 'new_value': 45864.0}, {'field': 'total_amount', 'old_value': 42107.0, 'new_value': 45864.0}, {'field': 'order_count', 'old_value': 612, 'new_value': 687}]
2025-06-14 12:02:30,120 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM12
2025-06-14 12:02:30,542 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM12
2025-06-14 12:02:30,542 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13619.0, 'new_value': 21744.0}, {'field': 'total_amount', 'old_value': 13619.0, 'new_value': 21744.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-06-14 12:02:30,542 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM22
2025-06-14 12:02:30,979 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM22
2025-06-14 12:02:30,979 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51492.64, 'new_value': 55945.74}, {'field': 'total_amount', 'old_value': 81050.34, 'new_value': 85503.44}, {'field': 'order_count', 'old_value': 2014, 'new_value': 2128}]
2025-06-14 12:02:30,979 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM32
2025-06-14 12:02:31,386 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM32
2025-06-14 12:02:31,386 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50760.0, 'new_value': 56128.0}, {'field': 'total_amount', 'old_value': 50760.0, 'new_value': 56128.0}, {'field': 'order_count', 'old_value': 241, 'new_value': 263}]
2025-06-14 12:02:31,386 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM52
2025-06-14 12:02:31,854 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM52
2025-06-14 12:02:31,854 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23924.4, 'new_value': 27302.4}, {'field': 'total_amount', 'old_value': 23924.4, 'new_value': 27302.4}, {'field': 'order_count', 'old_value': 10, 'new_value': 12}]
2025-06-14 12:02:31,854 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMB2
2025-06-14 12:02:32,323 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMB2
2025-06-14 12:02:32,323 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21107.79, 'new_value': 22879.66}, {'field': 'total_amount', 'old_value': 21107.79, 'new_value': 22879.66}, {'field': 'order_count', 'old_value': 2682, 'new_value': 2898}]
2025-06-14 12:02:32,323 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC2
2025-06-14 12:02:32,729 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC2
2025-06-14 12:02:32,729 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1644189.0, 'new_value': 1993189.0}, {'field': 'total_amount', 'old_value': 2553089.0, 'new_value': 2902089.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-06-14 12:02:32,729 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMD2
2025-06-14 12:02:33,167 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMD2
2025-06-14 12:02:33,167 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 143745.15, 'new_value': 158368.25}, {'field': 'total_amount', 'old_value': 143745.15, 'new_value': 158368.25}, {'field': 'order_count', 'old_value': 403, 'new_value': 432}]
2025-06-14 12:02:33,167 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME2
2025-06-14 12:02:33,698 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME2
2025-06-14 12:02:33,698 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 151164.0, 'new_value': 163886.0}, {'field': 'total_amount', 'old_value': 151164.0, 'new_value': 163886.0}, {'field': 'order_count', 'old_value': 3484, 'new_value': 3834}]
2025-06-14 12:02:33,698 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF2
2025-06-14 12:02:34,229 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF2
2025-06-14 12:02:34,229 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53232.0, 'new_value': 57199.0}, {'field': 'total_amount', 'old_value': 53232.0, 'new_value': 57199.0}, {'field': 'order_count', 'old_value': 2942, 'new_value': 3181}]
2025-06-14 12:02:34,229 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH2
2025-06-14 12:02:34,714 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH2
2025-06-14 12:02:34,714 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1474736.05, 'new_value': 1577361.08}, {'field': 'total_amount', 'old_value': 1474736.05, 'new_value': 1577361.08}, {'field': 'order_count', 'old_value': 2763, 'new_value': 2985}]
2025-06-14 12:02:34,714 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMI2
2025-06-14 12:02:35,214 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMI2
2025-06-14 12:02:35,214 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2935.0, 'new_value': 3290.0}, {'field': 'offline_amount', 'old_value': 10396.0, 'new_value': 11294.0}, {'field': 'total_amount', 'old_value': 13331.0, 'new_value': 14584.0}, {'field': 'order_count', 'old_value': 53, 'new_value': 60}]
2025-06-14 12:02:35,214 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ2
2025-06-14 12:02:35,667 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ2
2025-06-14 12:02:35,667 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 399813.0, 'new_value': 424864.0}, {'field': 'total_amount', 'old_value': 399813.0, 'new_value': 424864.0}, {'field': 'order_count', 'old_value': 1953, 'new_value': 2070}]
2025-06-14 12:02:35,667 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK2
2025-06-14 12:02:36,167 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK2
2025-06-14 12:02:36,167 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4636131.73, 'new_value': 4955266.73}, {'field': 'total_amount', 'old_value': 4636131.73, 'new_value': 4955266.73}, {'field': 'order_count', 'old_value': 17135, 'new_value': 18353}]
2025-06-14 12:02:36,167 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML2
2025-06-14 12:02:36,620 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML2
2025-06-14 12:02:36,620 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77563.07, 'new_value': 86575.95}, {'field': 'total_amount', 'old_value': 77563.07, 'new_value': 86575.95}, {'field': 'order_count', 'old_value': 8642, 'new_value': 9464}]
2025-06-14 12:02:36,620 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM2
2025-06-14 12:02:37,073 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM2
2025-06-14 12:02:37,073 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 101034.93, 'new_value': 110582.8}, {'field': 'offline_amount', 'old_value': 87793.69, 'new_value': 95548.22}, {'field': 'total_amount', 'old_value': 188828.62, 'new_value': 206131.02}, {'field': 'order_count', 'old_value': 8076, 'new_value': 8894}]
2025-06-14 12:02:37,073 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO2
2025-06-14 12:02:37,542 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO2
2025-06-14 12:02:37,542 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 103288.9, 'new_value': 113465.9}, {'field': 'total_amount', 'old_value': 103288.9, 'new_value': 113465.9}, {'field': 'order_count', 'old_value': 3557, 'new_value': 3926}]
2025-06-14 12:02:37,542 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ2
2025-06-14 12:02:38,058 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ2
2025-06-14 12:02:38,058 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 95856.8, 'new_value': 110911.8}, {'field': 'total_amount', 'old_value': 130058.9, 'new_value': 145113.9}, {'field': 'order_count', 'old_value': 374, 'new_value': 414}]
2025-06-14 12:02:38,058 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMUP
2025-06-14 12:02:38,495 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMUP
2025-06-14 12:02:38,495 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80304.42, 'new_value': 88199.37}, {'field': 'total_amount', 'old_value': 80304.42, 'new_value': 88199.37}, {'field': 'order_count', 'old_value': 6101, 'new_value': 6710}]
2025-06-14 12:02:38,495 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMWP
2025-06-14 12:02:38,979 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMWP
2025-06-14 12:02:38,979 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102703.28, 'new_value': 113205.28}, {'field': 'total_amount', 'old_value': 102703.28, 'new_value': 113205.28}, {'field': 'order_count', 'old_value': 11429, 'new_value': 11639}]
2025-06-14 12:02:38,979 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBM3Q
2025-06-14 12:02:39,433 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBM3Q
2025-06-14 12:02:39,433 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1603.6, 'new_value': 3091.15}, {'field': 'offline_amount', 'old_value': 6095.3, 'new_value': 9809.25}, {'field': 'total_amount', 'old_value': 7698.9, 'new_value': 12900.4}, {'field': 'order_count', 'old_value': 752, 'new_value': 1320}]
2025-06-14 12:02:39,433 - INFO - 日期 2025-06 处理完成 - 更新: 254 条，插入: 0 条，错误: 0 条
2025-06-14 12:02:39,433 - INFO - 数据同步完成！更新: 254 条，插入: 0 条，错误: 0 条
2025-06-14 12:02:39,433 - INFO - =================同步完成====================
2025-06-14 15:00:02,908 - INFO - =================使用默认全量同步=============
2025-06-14 15:00:04,596 - INFO - MySQL查询成功，共获取 3930 条记录
2025-06-14 15:00:04,596 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-14 15:00:04,627 - INFO - 开始处理日期: 2025-01
2025-06-14 15:00:04,642 - INFO - Request Parameters - Page 1:
2025-06-14 15:00:04,642 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:04,642 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:06,064 - INFO - Response - Page 1:
2025-06-14 15:00:06,267 - INFO - 第 1 页获取到 100 条记录
2025-06-14 15:00:06,267 - INFO - Request Parameters - Page 2:
2025-06-14 15:00:06,267 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:06,267 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:06,861 - INFO - Response - Page 2:
2025-06-14 15:00:07,064 - INFO - 第 2 页获取到 100 条记录
2025-06-14 15:00:07,064 - INFO - Request Parameters - Page 3:
2025-06-14 15:00:07,064 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:07,064 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:07,611 - INFO - Response - Page 3:
2025-06-14 15:00:07,814 - INFO - 第 3 页获取到 100 条记录
2025-06-14 15:00:07,814 - INFO - Request Parameters - Page 4:
2025-06-14 15:00:07,814 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:07,814 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:08,330 - INFO - Response - Page 4:
2025-06-14 15:00:08,533 - INFO - 第 4 页获取到 100 条记录
2025-06-14 15:00:08,533 - INFO - Request Parameters - Page 5:
2025-06-14 15:00:08,533 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:08,533 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:09,017 - INFO - Response - Page 5:
2025-06-14 15:00:09,221 - INFO - 第 5 页获取到 100 条记录
2025-06-14 15:00:09,221 - INFO - Request Parameters - Page 6:
2025-06-14 15:00:09,221 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:09,221 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:09,767 - INFO - Response - Page 6:
2025-06-14 15:00:09,971 - INFO - 第 6 页获取到 100 条记录
2025-06-14 15:00:09,971 - INFO - Request Parameters - Page 7:
2025-06-14 15:00:09,971 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:09,971 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:10,424 - INFO - Response - Page 7:
2025-06-14 15:00:10,627 - INFO - 第 7 页获取到 82 条记录
2025-06-14 15:00:10,627 - INFO - 查询完成，共获取到 682 条记录
2025-06-14 15:00:10,627 - INFO - 获取到 682 条表单数据
2025-06-14 15:00:10,627 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-14 15:00:10,642 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 15:00:10,642 - INFO - 开始处理日期: 2025-02
2025-06-14 15:00:10,642 - INFO - Request Parameters - Page 1:
2025-06-14 15:00:10,642 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:10,642 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:11,174 - INFO - Response - Page 1:
2025-06-14 15:00:11,377 - INFO - 第 1 页获取到 100 条记录
2025-06-14 15:00:11,377 - INFO - Request Parameters - Page 2:
2025-06-14 15:00:11,377 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:11,377 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:11,845 - INFO - Response - Page 2:
2025-06-14 15:00:12,049 - INFO - 第 2 页获取到 100 条记录
2025-06-14 15:00:12,049 - INFO - Request Parameters - Page 3:
2025-06-14 15:00:12,049 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:12,049 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:12,549 - INFO - Response - Page 3:
2025-06-14 15:00:12,752 - INFO - 第 3 页获取到 100 条记录
2025-06-14 15:00:12,752 - INFO - Request Parameters - Page 4:
2025-06-14 15:00:12,752 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:12,752 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:13,267 - INFO - Response - Page 4:
2025-06-14 15:00:13,470 - INFO - 第 4 页获取到 100 条记录
2025-06-14 15:00:13,470 - INFO - Request Parameters - Page 5:
2025-06-14 15:00:13,470 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:13,470 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:13,924 - INFO - Response - Page 5:
2025-06-14 15:00:14,127 - INFO - 第 5 页获取到 100 条记录
2025-06-14 15:00:14,127 - INFO - Request Parameters - Page 6:
2025-06-14 15:00:14,127 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:14,127 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:14,674 - INFO - Response - Page 6:
2025-06-14 15:00:14,877 - INFO - 第 6 页获取到 100 条记录
2025-06-14 15:00:14,877 - INFO - Request Parameters - Page 7:
2025-06-14 15:00:14,877 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:14,877 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:15,361 - INFO - Response - Page 7:
2025-06-14 15:00:15,564 - INFO - 第 7 页获取到 70 条记录
2025-06-14 15:00:15,564 - INFO - 查询完成，共获取到 670 条记录
2025-06-14 15:00:15,564 - INFO - 获取到 670 条表单数据
2025-06-14 15:00:15,564 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-14 15:00:15,580 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 15:00:15,580 - INFO - 开始处理日期: 2025-03
2025-06-14 15:00:15,580 - INFO - Request Parameters - Page 1:
2025-06-14 15:00:15,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:15,580 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:16,111 - INFO - Response - Page 1:
2025-06-14 15:00:16,314 - INFO - 第 1 页获取到 100 条记录
2025-06-14 15:00:16,314 - INFO - Request Parameters - Page 2:
2025-06-14 15:00:16,314 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:16,314 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:16,830 - INFO - Response - Page 2:
2025-06-14 15:00:17,033 - INFO - 第 2 页获取到 100 条记录
2025-06-14 15:00:17,033 - INFO - Request Parameters - Page 3:
2025-06-14 15:00:17,033 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:17,033 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:17,767 - INFO - Response - Page 3:
2025-06-14 15:00:17,970 - INFO - 第 3 页获取到 100 条记录
2025-06-14 15:00:17,970 - INFO - Request Parameters - Page 4:
2025-06-14 15:00:17,970 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:17,970 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:18,502 - INFO - Response - Page 4:
2025-06-14 15:00:18,705 - INFO - 第 4 页获取到 100 条记录
2025-06-14 15:00:18,705 - INFO - Request Parameters - Page 5:
2025-06-14 15:00:18,705 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:18,705 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:19,174 - INFO - Response - Page 5:
2025-06-14 15:00:19,377 - INFO - 第 5 页获取到 100 条记录
2025-06-14 15:00:19,377 - INFO - Request Parameters - Page 6:
2025-06-14 15:00:19,377 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:19,377 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:19,877 - INFO - Response - Page 6:
2025-06-14 15:00:20,080 - INFO - 第 6 页获取到 100 条记录
2025-06-14 15:00:20,080 - INFO - Request Parameters - Page 7:
2025-06-14 15:00:20,080 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:20,080 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:20,517 - INFO - Response - Page 7:
2025-06-14 15:00:20,720 - INFO - 第 7 页获取到 61 条记录
2025-06-14 15:00:20,720 - INFO - 查询完成，共获取到 661 条记录
2025-06-14 15:00:20,720 - INFO - 获取到 661 条表单数据
2025-06-14 15:00:20,720 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-14 15:00:20,736 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 15:00:20,736 - INFO - 开始处理日期: 2025-04
2025-06-14 15:00:20,736 - INFO - Request Parameters - Page 1:
2025-06-14 15:00:20,736 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:20,736 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:21,283 - INFO - Response - Page 1:
2025-06-14 15:00:21,486 - INFO - 第 1 页获取到 100 条记录
2025-06-14 15:00:21,486 - INFO - Request Parameters - Page 2:
2025-06-14 15:00:21,486 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:21,486 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:21,970 - INFO - Response - Page 2:
2025-06-14 15:00:22,174 - INFO - 第 2 页获取到 100 条记录
2025-06-14 15:00:22,174 - INFO - Request Parameters - Page 3:
2025-06-14 15:00:22,174 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:22,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:22,674 - INFO - Response - Page 3:
2025-06-14 15:00:22,877 - INFO - 第 3 页获取到 100 条记录
2025-06-14 15:00:22,877 - INFO - Request Parameters - Page 4:
2025-06-14 15:00:22,877 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:22,877 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:23,408 - INFO - Response - Page 4:
2025-06-14 15:00:23,611 - INFO - 第 4 页获取到 100 条记录
2025-06-14 15:00:23,611 - INFO - Request Parameters - Page 5:
2025-06-14 15:00:23,611 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:23,611 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:24,033 - INFO - Response - Page 5:
2025-06-14 15:00:24,236 - INFO - 第 5 页获取到 100 条记录
2025-06-14 15:00:24,236 - INFO - Request Parameters - Page 6:
2025-06-14 15:00:24,236 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:24,236 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:24,689 - INFO - Response - Page 6:
2025-06-14 15:00:24,892 - INFO - 第 6 页获取到 100 条记录
2025-06-14 15:00:24,892 - INFO - Request Parameters - Page 7:
2025-06-14 15:00:24,892 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:24,892 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:25,424 - INFO - Response - Page 7:
2025-06-14 15:00:25,627 - INFO - 第 7 页获取到 56 条记录
2025-06-14 15:00:25,627 - INFO - 查询完成，共获取到 656 条记录
2025-06-14 15:00:25,627 - INFO - 获取到 656 条表单数据
2025-06-14 15:00:25,627 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-14 15:00:25,642 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 15:00:25,642 - INFO - 开始处理日期: 2025-05
2025-06-14 15:00:25,642 - INFO - Request Parameters - Page 1:
2025-06-14 15:00:25,642 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:25,642 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:26,205 - INFO - Response - Page 1:
2025-06-14 15:00:26,408 - INFO - 第 1 页获取到 100 条记录
2025-06-14 15:00:26,408 - INFO - Request Parameters - Page 2:
2025-06-14 15:00:26,408 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:26,408 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:26,830 - INFO - Response - Page 2:
2025-06-14 15:00:27,033 - INFO - 第 2 页获取到 100 条记录
2025-06-14 15:00:27,033 - INFO - Request Parameters - Page 3:
2025-06-14 15:00:27,033 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:27,033 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:27,517 - INFO - Response - Page 3:
2025-06-14 15:00:27,720 - INFO - 第 3 页获取到 100 条记录
2025-06-14 15:00:27,720 - INFO - Request Parameters - Page 4:
2025-06-14 15:00:27,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:27,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:28,252 - INFO - Response - Page 4:
2025-06-14 15:00:28,455 - INFO - 第 4 页获取到 100 条记录
2025-06-14 15:00:28,455 - INFO - Request Parameters - Page 5:
2025-06-14 15:00:28,455 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:28,455 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:28,955 - INFO - Response - Page 5:
2025-06-14 15:00:29,158 - INFO - 第 5 页获取到 100 条记录
2025-06-14 15:00:29,158 - INFO - Request Parameters - Page 6:
2025-06-14 15:00:29,158 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:29,158 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:29,689 - INFO - Response - Page 6:
2025-06-14 15:00:29,892 - INFO - 第 6 页获取到 100 条记录
2025-06-14 15:00:29,892 - INFO - Request Parameters - Page 7:
2025-06-14 15:00:29,892 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:29,892 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:30,298 - INFO - Response - Page 7:
2025-06-14 15:00:30,502 - INFO - 第 7 页获取到 40 条记录
2025-06-14 15:00:30,502 - INFO - 查询完成，共获取到 640 条记录
2025-06-14 15:00:30,502 - INFO - 获取到 640 条表单数据
2025-06-14 15:00:30,502 - INFO - 当前日期 2025-05 有 640 条MySQL数据需要处理
2025-06-14 15:00:30,517 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 15:00:30,517 - INFO - 开始处理日期: 2025-06
2025-06-14 15:00:30,517 - INFO - Request Parameters - Page 1:
2025-06-14 15:00:30,517 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:30,517 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:31,017 - INFO - Response - Page 1:
2025-06-14 15:00:31,220 - INFO - 第 1 页获取到 100 条记录
2025-06-14 15:00:31,220 - INFO - Request Parameters - Page 2:
2025-06-14 15:00:31,220 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:31,220 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:31,673 - INFO - Response - Page 2:
2025-06-14 15:00:31,877 - INFO - 第 2 页获取到 100 条记录
2025-06-14 15:00:31,877 - INFO - Request Parameters - Page 3:
2025-06-14 15:00:31,877 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:31,877 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:32,517 - INFO - Response - Page 3:
2025-06-14 15:00:32,720 - INFO - 第 3 页获取到 100 条记录
2025-06-14 15:00:32,720 - INFO - Request Parameters - Page 4:
2025-06-14 15:00:32,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:32,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:33,252 - INFO - Response - Page 4:
2025-06-14 15:00:33,455 - INFO - 第 4 页获取到 100 条记录
2025-06-14 15:00:33,455 - INFO - Request Parameters - Page 5:
2025-06-14 15:00:33,455 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:33,455 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:33,970 - INFO - Response - Page 5:
2025-06-14 15:00:34,173 - INFO - 第 5 页获取到 100 条记录
2025-06-14 15:00:34,173 - INFO - Request Parameters - Page 6:
2025-06-14 15:00:34,173 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:34,173 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:34,720 - INFO - Response - Page 6:
2025-06-14 15:00:34,923 - INFO - 第 6 页获取到 100 条记录
2025-06-14 15:00:34,923 - INFO - Request Parameters - Page 7:
2025-06-14 15:00:34,923 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 15:00:34,923 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 15:00:35,267 - INFO - Response - Page 7:
2025-06-14 15:00:35,470 - INFO - 第 7 页获取到 21 条记录
2025-06-14 15:00:35,470 - INFO - 查询完成，共获取到 621 条记录
2025-06-14 15:00:35,470 - INFO - 获取到 621 条表单数据
2025-06-14 15:00:35,470 - INFO - 当前日期 2025-06 有 621 条MySQL数据需要处理
2025-06-14 15:00:35,486 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCC
2025-06-14 15:00:35,892 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCC
2025-06-14 15:00:35,892 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78947.0, 'new_value': 82946.0}, {'field': 'total_amount', 'old_value': 78947.0, 'new_value': 82946.0}, {'field': 'order_count', 'old_value': 32, 'new_value': 33}]
2025-06-14 15:00:35,892 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM911
2025-06-14 15:00:36,345 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM911
2025-06-14 15:00:36,345 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7567.41, 'new_value': 8097.03}, {'field': 'offline_amount', 'old_value': 59155.0, 'new_value': 59657.0}, {'field': 'total_amount', 'old_value': 66722.41, 'new_value': 67754.03}, {'field': 'order_count', 'old_value': 32, 'new_value': 34}]
2025-06-14 15:00:36,345 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM921
2025-06-14 15:00:36,736 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM921
2025-06-14 15:00:36,736 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24300.03, 'new_value': 27475.05}, {'field': 'offline_amount', 'old_value': 34292.48, 'new_value': 37578.69}, {'field': 'total_amount', 'old_value': 58592.51, 'new_value': 65053.74}, {'field': 'order_count', 'old_value': 2891, 'new_value': 3241}]
2025-06-14 15:00:36,736 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH1
2025-06-14 15:00:37,205 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH1
2025-06-14 15:00:37,205 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107437.78, 'new_value': 117103.17}, {'field': 'total_amount', 'old_value': 107437.78, 'new_value': 117103.17}, {'field': 'order_count', 'old_value': 3531, 'new_value': 3873}]
2025-06-14 15:00:37,205 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM1
2025-06-14 15:00:37,658 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM1
2025-06-14 15:00:37,658 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17214.7, 'new_value': 18748.5}, {'field': 'offline_amount', 'old_value': 57689.0, 'new_value': 61197.0}, {'field': 'total_amount', 'old_value': 74903.7, 'new_value': 79945.5}, {'field': 'order_count', 'old_value': 968, 'new_value': 1031}]
2025-06-14 15:00:37,658 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM92
2025-06-14 15:00:38,127 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM92
2025-06-14 15:00:38,127 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28885.0, 'new_value': 32595.0}, {'field': 'total_amount', 'old_value': 32330.0, 'new_value': 36040.0}, {'field': 'order_count', 'old_value': 122, 'new_value': 136}]
2025-06-14 15:00:38,127 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS2
2025-06-14 15:00:38,533 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS2
2025-06-14 15:00:38,533 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87643.0, 'new_value': 92187.0}, {'field': 'total_amount', 'old_value': 87643.0, 'new_value': 92187.0}, {'field': 'order_count', 'old_value': 396, 'new_value': 412}]
2025-06-14 15:00:38,533 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMT2
2025-06-14 15:00:38,955 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMT2
2025-06-14 15:00:38,955 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30419.0, 'new_value': 32145.0}, {'field': 'total_amount', 'old_value': 34565.0, 'new_value': 36291.0}, {'field': 'order_count', 'old_value': 46, 'new_value': 51}]
2025-06-14 15:00:38,955 - INFO - 日期 2025-06 处理完成 - 更新: 8 条，插入: 0 条，错误: 0 条
2025-06-14 15:00:38,955 - INFO - 数据同步完成！更新: 8 条，插入: 0 条，错误: 0 条
2025-06-14 15:00:38,955 - INFO - =================同步完成====================
2025-06-14 18:00:02,352 - INFO - =================使用默认全量同步=============
2025-06-14 18:00:04,023 - INFO - MySQL查询成功，共获取 3930 条记录
2025-06-14 18:00:04,023 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-14 18:00:04,055 - INFO - 开始处理日期: 2025-01
2025-06-14 18:00:04,055 - INFO - Request Parameters - Page 1:
2025-06-14 18:00:04,055 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:04,055 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:05,461 - INFO - Response - Page 1:
2025-06-14 18:00:05,664 - INFO - 第 1 页获取到 100 条记录
2025-06-14 18:00:05,664 - INFO - Request Parameters - Page 2:
2025-06-14 18:00:05,664 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:05,664 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:06,211 - INFO - Response - Page 2:
2025-06-14 18:00:06,414 - INFO - 第 2 页获取到 100 条记录
2025-06-14 18:00:06,414 - INFO - Request Parameters - Page 3:
2025-06-14 18:00:06,414 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:06,414 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:06,867 - INFO - Response - Page 3:
2025-06-14 18:00:07,070 - INFO - 第 3 页获取到 100 条记录
2025-06-14 18:00:07,070 - INFO - Request Parameters - Page 4:
2025-06-14 18:00:07,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:07,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:07,758 - INFO - Response - Page 4:
2025-06-14 18:00:07,961 - INFO - 第 4 页获取到 100 条记录
2025-06-14 18:00:07,961 - INFO - Request Parameters - Page 5:
2025-06-14 18:00:07,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:07,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:08,430 - INFO - Response - Page 5:
2025-06-14 18:00:08,633 - INFO - 第 5 页获取到 100 条记录
2025-06-14 18:00:08,633 - INFO - Request Parameters - Page 6:
2025-06-14 18:00:08,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:08,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:09,164 - INFO - Response - Page 6:
2025-06-14 18:00:09,367 - INFO - 第 6 页获取到 100 条记录
2025-06-14 18:00:09,367 - INFO - Request Parameters - Page 7:
2025-06-14 18:00:09,367 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:09,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:09,852 - INFO - Response - Page 7:
2025-06-14 18:00:10,055 - INFO - 第 7 页获取到 82 条记录
2025-06-14 18:00:10,055 - INFO - 查询完成，共获取到 682 条记录
2025-06-14 18:00:10,055 - INFO - 获取到 682 条表单数据
2025-06-14 18:00:10,055 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-14 18:00:10,070 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 18:00:10,070 - INFO - 开始处理日期: 2025-02
2025-06-14 18:00:10,070 - INFO - Request Parameters - Page 1:
2025-06-14 18:00:10,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:10,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:10,570 - INFO - Response - Page 1:
2025-06-14 18:00:10,773 - INFO - 第 1 页获取到 100 条记录
2025-06-14 18:00:10,773 - INFO - Request Parameters - Page 2:
2025-06-14 18:00:10,773 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:10,773 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:11,336 - INFO - Response - Page 2:
2025-06-14 18:00:11,539 - INFO - 第 2 页获取到 100 条记录
2025-06-14 18:00:11,539 - INFO - Request Parameters - Page 3:
2025-06-14 18:00:11,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:11,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:12,055 - INFO - Response - Page 3:
2025-06-14 18:00:12,258 - INFO - 第 3 页获取到 100 条记录
2025-06-14 18:00:12,258 - INFO - Request Parameters - Page 4:
2025-06-14 18:00:12,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:12,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:12,773 - INFO - Response - Page 4:
2025-06-14 18:00:12,976 - INFO - 第 4 页获取到 100 条记录
2025-06-14 18:00:12,976 - INFO - Request Parameters - Page 5:
2025-06-14 18:00:12,976 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:12,976 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:13,492 - INFO - Response - Page 5:
2025-06-14 18:00:13,695 - INFO - 第 5 页获取到 100 条记录
2025-06-14 18:00:13,695 - INFO - Request Parameters - Page 6:
2025-06-14 18:00:13,695 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:13,695 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:14,180 - INFO - Response - Page 6:
2025-06-14 18:00:14,383 - INFO - 第 6 页获取到 100 条记录
2025-06-14 18:00:14,383 - INFO - Request Parameters - Page 7:
2025-06-14 18:00:14,383 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:14,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:14,867 - INFO - Response - Page 7:
2025-06-14 18:00:15,070 - INFO - 第 7 页获取到 70 条记录
2025-06-14 18:00:15,070 - INFO - 查询完成，共获取到 670 条记录
2025-06-14 18:00:15,070 - INFO - 获取到 670 条表单数据
2025-06-14 18:00:15,070 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-14 18:00:15,086 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 18:00:15,086 - INFO - 开始处理日期: 2025-03
2025-06-14 18:00:15,086 - INFO - Request Parameters - Page 1:
2025-06-14 18:00:15,086 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:15,086 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:15,664 - INFO - Response - Page 1:
2025-06-14 18:00:15,867 - INFO - 第 1 页获取到 100 条记录
2025-06-14 18:00:15,867 - INFO - Request Parameters - Page 2:
2025-06-14 18:00:15,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:15,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:16,367 - INFO - Response - Page 2:
2025-06-14 18:00:16,570 - INFO - 第 2 页获取到 100 条记录
2025-06-14 18:00:16,570 - INFO - Request Parameters - Page 3:
2025-06-14 18:00:16,570 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:16,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:17,086 - INFO - Response - Page 3:
2025-06-14 18:00:17,289 - INFO - 第 3 页获取到 100 条记录
2025-06-14 18:00:17,289 - INFO - Request Parameters - Page 4:
2025-06-14 18:00:17,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:17,289 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:17,773 - INFO - Response - Page 4:
2025-06-14 18:00:17,976 - INFO - 第 4 页获取到 100 条记录
2025-06-14 18:00:17,976 - INFO - Request Parameters - Page 5:
2025-06-14 18:00:17,976 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:17,976 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:18,461 - INFO - Response - Page 5:
2025-06-14 18:00:18,664 - INFO - 第 5 页获取到 100 条记录
2025-06-14 18:00:18,664 - INFO - Request Parameters - Page 6:
2025-06-14 18:00:18,664 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:18,664 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:19,367 - INFO - Response - Page 6:
2025-06-14 18:00:19,570 - INFO - 第 6 页获取到 100 条记录
2025-06-14 18:00:19,570 - INFO - Request Parameters - Page 7:
2025-06-14 18:00:19,570 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:19,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:20,039 - INFO - Response - Page 7:
2025-06-14 18:00:20,242 - INFO - 第 7 页获取到 61 条记录
2025-06-14 18:00:20,242 - INFO - 查询完成，共获取到 661 条记录
2025-06-14 18:00:20,242 - INFO - 获取到 661 条表单数据
2025-06-14 18:00:20,242 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-14 18:00:20,258 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 18:00:20,258 - INFO - 开始处理日期: 2025-04
2025-06-14 18:00:20,258 - INFO - Request Parameters - Page 1:
2025-06-14 18:00:20,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:20,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:20,758 - INFO - Response - Page 1:
2025-06-14 18:00:20,961 - INFO - 第 1 页获取到 100 条记录
2025-06-14 18:00:20,961 - INFO - Request Parameters - Page 2:
2025-06-14 18:00:20,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:20,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:21,414 - INFO - Response - Page 2:
2025-06-14 18:00:21,617 - INFO - 第 2 页获取到 100 条记录
2025-06-14 18:00:21,617 - INFO - Request Parameters - Page 3:
2025-06-14 18:00:21,617 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:21,617 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:22,195 - INFO - Response - Page 3:
2025-06-14 18:00:22,398 - INFO - 第 3 页获取到 100 条记录
2025-06-14 18:00:22,398 - INFO - Request Parameters - Page 4:
2025-06-14 18:00:22,398 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:22,398 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:22,961 - INFO - Response - Page 4:
2025-06-14 18:00:23,164 - INFO - 第 4 页获取到 100 条记录
2025-06-14 18:00:23,164 - INFO - Request Parameters - Page 5:
2025-06-14 18:00:23,164 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:23,164 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:23,711 - INFO - Response - Page 5:
2025-06-14 18:00:23,914 - INFO - 第 5 页获取到 100 条记录
2025-06-14 18:00:23,914 - INFO - Request Parameters - Page 6:
2025-06-14 18:00:23,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:23,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:24,414 - INFO - Response - Page 6:
2025-06-14 18:00:24,617 - INFO - 第 6 页获取到 100 条记录
2025-06-14 18:00:24,617 - INFO - Request Parameters - Page 7:
2025-06-14 18:00:24,617 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:24,617 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:25,039 - INFO - Response - Page 7:
2025-06-14 18:00:25,242 - INFO - 第 7 页获取到 56 条记录
2025-06-14 18:00:25,242 - INFO - 查询完成，共获取到 656 条记录
2025-06-14 18:00:25,242 - INFO - 获取到 656 条表单数据
2025-06-14 18:00:25,242 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-14 18:00:25,258 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 18:00:25,258 - INFO - 开始处理日期: 2025-05
2025-06-14 18:00:25,258 - INFO - Request Parameters - Page 1:
2025-06-14 18:00:25,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:25,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:25,758 - INFO - Response - Page 1:
2025-06-14 18:00:25,961 - INFO - 第 1 页获取到 100 条记录
2025-06-14 18:00:25,961 - INFO - Request Parameters - Page 2:
2025-06-14 18:00:25,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:25,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:26,508 - INFO - Response - Page 2:
2025-06-14 18:00:26,711 - INFO - 第 2 页获取到 100 条记录
2025-06-14 18:00:26,711 - INFO - Request Parameters - Page 3:
2025-06-14 18:00:26,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:26,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:27,195 - INFO - Response - Page 3:
2025-06-14 18:00:27,398 - INFO - 第 3 页获取到 100 条记录
2025-06-14 18:00:27,398 - INFO - Request Parameters - Page 4:
2025-06-14 18:00:27,398 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:27,398 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:27,867 - INFO - Response - Page 4:
2025-06-14 18:00:28,070 - INFO - 第 4 页获取到 100 条记录
2025-06-14 18:00:28,070 - INFO - Request Parameters - Page 5:
2025-06-14 18:00:28,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:28,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:28,555 - INFO - Response - Page 5:
2025-06-14 18:00:28,758 - INFO - 第 5 页获取到 100 条记录
2025-06-14 18:00:28,758 - INFO - Request Parameters - Page 6:
2025-06-14 18:00:28,758 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:28,758 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:29,242 - INFO - Response - Page 6:
2025-06-14 18:00:29,445 - INFO - 第 6 页获取到 100 条记录
2025-06-14 18:00:29,445 - INFO - Request Parameters - Page 7:
2025-06-14 18:00:29,445 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:29,445 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:29,820 - INFO - Response - Page 7:
2025-06-14 18:00:30,023 - INFO - 第 7 页获取到 40 条记录
2025-06-14 18:00:30,023 - INFO - 查询完成，共获取到 640 条记录
2025-06-14 18:00:30,023 - INFO - 获取到 640 条表单数据
2025-06-14 18:00:30,023 - INFO - 当前日期 2025-05 有 640 条MySQL数据需要处理
2025-06-14 18:00:30,039 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 18:00:30,039 - INFO - 开始处理日期: 2025-06
2025-06-14 18:00:30,039 - INFO - Request Parameters - Page 1:
2025-06-14 18:00:30,039 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:30,039 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:30,617 - INFO - Response - Page 1:
2025-06-14 18:00:30,820 - INFO - 第 1 页获取到 100 条记录
2025-06-14 18:00:30,820 - INFO - Request Parameters - Page 2:
2025-06-14 18:00:30,820 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:30,820 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:31,336 - INFO - Response - Page 2:
2025-06-14 18:00:31,539 - INFO - 第 2 页获取到 100 条记录
2025-06-14 18:00:31,539 - INFO - Request Parameters - Page 3:
2025-06-14 18:00:31,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:31,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:32,070 - INFO - Response - Page 3:
2025-06-14 18:00:32,273 - INFO - 第 3 页获取到 100 条记录
2025-06-14 18:00:32,273 - INFO - Request Parameters - Page 4:
2025-06-14 18:00:32,273 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:32,273 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:32,773 - INFO - Response - Page 4:
2025-06-14 18:00:32,976 - INFO - 第 4 页获取到 100 条记录
2025-06-14 18:00:32,976 - INFO - Request Parameters - Page 5:
2025-06-14 18:00:32,976 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:32,976 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:33,492 - INFO - Response - Page 5:
2025-06-14 18:00:33,695 - INFO - 第 5 页获取到 100 条记录
2025-06-14 18:00:33,695 - INFO - Request Parameters - Page 6:
2025-06-14 18:00:33,695 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:33,695 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:34,164 - INFO - Response - Page 6:
2025-06-14 18:00:34,367 - INFO - 第 6 页获取到 100 条记录
2025-06-14 18:00:34,367 - INFO - Request Parameters - Page 7:
2025-06-14 18:00:34,367 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 18:00:34,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 18:00:34,711 - INFO - Response - Page 7:
2025-06-14 18:00:34,914 - INFO - 第 7 页获取到 21 条记录
2025-06-14 18:00:34,914 - INFO - 查询完成，共获取到 621 条记录
2025-06-14 18:00:34,914 - INFO - 获取到 621 条表单数据
2025-06-14 18:00:34,914 - INFO - 当前日期 2025-06 有 621 条MySQL数据需要处理
2025-06-14 18:00:34,929 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM7L
2025-06-14 18:00:35,351 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM7L
2025-06-14 18:00:35,351 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124691.8, 'new_value': 131216.3}, {'field': 'total_amount', 'old_value': 124691.8, 'new_value': 131216.3}, {'field': 'order_count', 'old_value': 1441, 'new_value': 1475}]
2025-06-14 18:00:35,351 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN21
2025-06-14 18:00:35,867 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN21
2025-06-14 18:00:35,867 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5320.0, 'new_value': 5500.0}, {'field': 'total_amount', 'old_value': 8683.0, 'new_value': 8863.0}, {'field': 'order_count', 'old_value': 81, 'new_value': 87}]
2025-06-14 18:00:35,867 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM42
2025-06-14 18:00:36,336 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM42
2025-06-14 18:00:36,336 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6840.0, 'new_value': 8040.0}, {'field': 'total_amount', 'old_value': 6840.0, 'new_value': 8040.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-06-14 18:00:36,336 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA2
2025-06-14 18:00:36,726 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA2
2025-06-14 18:00:36,726 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6703.0, 'new_value': 7402.0}, {'field': 'total_amount', 'old_value': 8902.0, 'new_value': 9601.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 23}]
2025-06-14 18:00:36,726 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW2
2025-06-14 18:00:37,351 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW2
2025-06-14 18:00:37,351 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18816.0, 'new_value': 20585.0}, {'field': 'total_amount', 'old_value': 18816.0, 'new_value': 20585.0}, {'field': 'order_count', 'old_value': 1864, 'new_value': 2026}]
2025-06-14 18:00:37,351 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX2
2025-06-14 18:00:37,836 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX2
2025-06-14 18:00:37,836 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 589000.0, 'new_value': 629000.0}, {'field': 'total_amount', 'old_value': 589000.0, 'new_value': 629000.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-06-14 18:00:37,836 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY2
2025-06-14 18:00:38,273 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY2
2025-06-14 18:00:38,273 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 162539.0, 'new_value': 178723.0}, {'field': 'total_amount', 'old_value': 162539.0, 'new_value': 178723.0}, {'field': 'order_count', 'old_value': 4703, 'new_value': 5089}]
2025-06-14 18:00:38,273 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM33
2025-06-14 18:00:38,758 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM33
2025-06-14 18:00:38,758 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18835.0, 'new_value': 21415.0}, {'field': 'total_amount', 'old_value': 18835.0, 'new_value': 21415.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 39}]
2025-06-14 18:00:38,758 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM43
2025-06-14 18:00:39,179 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM43
2025-06-14 18:00:39,179 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72211.0, 'new_value': 75794.0}, {'field': 'total_amount', 'old_value': 72211.0, 'new_value': 75794.0}, {'field': 'order_count', 'old_value': 1619, 'new_value': 1710}]
2025-06-14 18:00:39,179 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM53
2025-06-14 18:00:39,679 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM53
2025-06-14 18:00:39,679 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3600.0, 'new_value': 6180.0}, {'field': 'total_amount', 'old_value': 3600.0, 'new_value': 6180.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-06-14 18:00:39,679 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM63
2025-06-14 18:00:40,101 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM63
2025-06-14 18:00:40,101 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 135085.42, 'new_value': 138285.42}, {'field': 'total_amount', 'old_value': 135085.42, 'new_value': 138285.42}, {'field': 'order_count', 'old_value': 44, 'new_value': 45}]
2025-06-14 18:00:40,101 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM73
2025-06-14 18:00:40,679 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM73
2025-06-14 18:00:40,679 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14622.0, 'new_value': 14760.0}, {'field': 'total_amount', 'old_value': 14622.0, 'new_value': 14760.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-06-14 18:00:40,679 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA3
2025-06-14 18:00:41,148 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA3
2025-06-14 18:00:41,148 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105000.0, 'new_value': 112000.0}, {'field': 'total_amount', 'old_value': 105000.0, 'new_value': 112000.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-06-14 18:00:41,148 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME3
2025-06-14 18:00:41,648 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME3
2025-06-14 18:00:41,648 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62883.0, 'new_value': 69602.0}, {'field': 'total_amount', 'old_value': 62883.0, 'new_value': 69602.0}, {'field': 'order_count', 'old_value': 2741, 'new_value': 2975}]
2025-06-14 18:00:41,648 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF3
2025-06-14 18:00:42,101 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF3
2025-06-14 18:00:42,101 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74119.88, 'new_value': 79537.88}, {'field': 'total_amount', 'old_value': 74119.88, 'new_value': 79537.88}, {'field': 'order_count', 'old_value': 457, 'new_value': 506}]
2025-06-14 18:00:42,101 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG3
2025-06-14 18:00:42,570 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG3
2025-06-14 18:00:42,570 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46207.0, 'new_value': 51207.0}, {'field': 'total_amount', 'old_value': 46207.0, 'new_value': 51207.0}, {'field': 'order_count', 'old_value': 362, 'new_value': 395}]
2025-06-14 18:00:42,570 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK3
2025-06-14 18:00:43,070 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK3
2025-06-14 18:00:43,070 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17059.0, 'new_value': 18552.0}, {'field': 'total_amount', 'old_value': 17059.0, 'new_value': 18552.0}, {'field': 'order_count', 'old_value': 1603, 'new_value': 1742}]
2025-06-14 18:00:43,070 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML3
2025-06-14 18:00:43,523 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML3
2025-06-14 18:00:43,523 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6295.0, 'new_value': 6751.0}, {'field': 'total_amount', 'old_value': 6295.0, 'new_value': 6751.0}, {'field': 'order_count', 'old_value': 168, 'new_value': 175}]
2025-06-14 18:00:43,523 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMM3
2025-06-14 18:00:43,976 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMM3
2025-06-14 18:00:43,976 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27140.0, 'new_value': 30000.0}, {'field': 'total_amount', 'old_value': 27140.0, 'new_value': 30000.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-14 18:00:43,976 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMP3
2025-06-14 18:00:44,648 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMP3
2025-06-14 18:00:44,648 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2571.0, 'new_value': 2870.0}, {'field': 'total_amount', 'old_value': 2571.0, 'new_value': 2870.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-06-14 18:00:44,648 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMLP
2025-06-14 18:00:45,054 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMLP
2025-06-14 18:00:45,054 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2679105.0, 'new_value': 2910160.0}, {'field': 'total_amount', 'old_value': 2679105.0, 'new_value': 2910160.0}, {'field': 'order_count', 'old_value': 49053, 'new_value': 54243}]
2025-06-14 18:00:45,054 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMMP
2025-06-14 18:00:45,508 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMMP
2025-06-14 18:00:45,508 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36789.0, 'new_value': 36989.0}, {'field': 'total_amount', 'old_value': 36789.0, 'new_value': 36989.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-06-14 18:00:45,508 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMOP
2025-06-14 18:00:45,992 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMOP
2025-06-14 18:00:45,992 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112915.34, 'new_value': 126735.34}, {'field': 'total_amount', 'old_value': 112915.34, 'new_value': 126735.34}, {'field': 'order_count', 'old_value': 236, 'new_value': 240}]
2025-06-14 18:00:45,992 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMPP
2025-06-14 18:00:46,461 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMPP
2025-06-14 18:00:46,461 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4462.7, 'new_value': 4618.7}, {'field': 'total_amount', 'old_value': 7280.7, 'new_value': 7436.7}, {'field': 'order_count', 'old_value': 28, 'new_value': 29}]
2025-06-14 18:00:46,461 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMQP
2025-06-14 18:00:46,883 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMQP
2025-06-14 18:00:46,883 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3625.0, 'new_value': 4205.0}, {'field': 'total_amount', 'old_value': 3625.0, 'new_value': 4205.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 26}]
2025-06-14 18:00:46,883 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMRP
2025-06-14 18:00:47,367 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMRP
2025-06-14 18:00:47,367 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3063900.0, 'new_value': 3169860.0}, {'field': 'total_amount', 'old_value': 3063900.0, 'new_value': 3169860.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-06-14 18:00:47,367 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMSP
2025-06-14 18:00:47,883 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMSP
2025-06-14 18:00:47,883 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69249.0, 'new_value': 86726.0}, {'field': 'total_amount', 'old_value': 69249.0, 'new_value': 86726.0}, {'field': 'order_count', 'old_value': 72, 'new_value': 93}]
2025-06-14 18:00:47,883 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMTP
2025-06-14 18:00:48,336 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMTP
2025-06-14 18:00:48,336 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16871.22, 'new_value': 18480.48}, {'field': 'total_amount', 'old_value': 16871.22, 'new_value': 18480.48}, {'field': 'order_count', 'old_value': 655, 'new_value': 730}]
2025-06-14 18:00:48,336 - INFO - 日期 2025-06 处理完成 - 更新: 28 条，插入: 0 条，错误: 0 条
2025-06-14 18:00:48,351 - INFO - 数据同步完成！更新: 28 条，插入: 0 条，错误: 0 条
2025-06-14 18:00:48,351 - INFO - =================同步完成====================
2025-06-14 21:00:02,506 - INFO - =================使用默认全量同步=============
2025-06-14 21:00:04,193 - INFO - MySQL查询成功，共获取 3930 条记录
2025-06-14 21:00:04,193 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-14 21:00:04,224 - INFO - 开始处理日期: 2025-01
2025-06-14 21:00:04,224 - INFO - Request Parameters - Page 1:
2025-06-14 21:00:04,224 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:04,224 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:05,162 - INFO - Response - Page 1:
2025-06-14 21:00:05,365 - INFO - 第 1 页获取到 100 条记录
2025-06-14 21:00:05,365 - INFO - Request Parameters - Page 2:
2025-06-14 21:00:05,365 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:05,365 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:06,318 - INFO - Response - Page 2:
2025-06-14 21:00:06,521 - INFO - 第 2 页获取到 100 条记录
2025-06-14 21:00:06,521 - INFO - Request Parameters - Page 3:
2025-06-14 21:00:06,521 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:06,521 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:07,036 - INFO - Response - Page 3:
2025-06-14 21:00:07,240 - INFO - 第 3 页获取到 100 条记录
2025-06-14 21:00:07,240 - INFO - Request Parameters - Page 4:
2025-06-14 21:00:07,240 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:07,240 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:07,755 - INFO - Response - Page 4:
2025-06-14 21:00:07,958 - INFO - 第 4 页获取到 100 条记录
2025-06-14 21:00:07,958 - INFO - Request Parameters - Page 5:
2025-06-14 21:00:07,958 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:07,958 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:08,614 - INFO - Response - Page 5:
2025-06-14 21:00:08,818 - INFO - 第 5 页获取到 100 条记录
2025-06-14 21:00:08,818 - INFO - Request Parameters - Page 6:
2025-06-14 21:00:08,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:08,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:09,349 - INFO - Response - Page 6:
2025-06-14 21:00:09,552 - INFO - 第 6 页获取到 100 条记录
2025-06-14 21:00:09,552 - INFO - Request Parameters - Page 7:
2025-06-14 21:00:09,552 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:09,552 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:10,083 - INFO - Response - Page 7:
2025-06-14 21:00:10,286 - INFO - 第 7 页获取到 82 条记录
2025-06-14 21:00:10,286 - INFO - 查询完成，共获取到 682 条记录
2025-06-14 21:00:10,286 - INFO - 获取到 682 条表单数据
2025-06-14 21:00:10,286 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-14 21:00:10,302 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 21:00:10,302 - INFO - 开始处理日期: 2025-02
2025-06-14 21:00:10,302 - INFO - Request Parameters - Page 1:
2025-06-14 21:00:10,302 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:10,302 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:10,864 - INFO - Response - Page 1:
2025-06-14 21:00:11,067 - INFO - 第 1 页获取到 100 条记录
2025-06-14 21:00:11,067 - INFO - Request Parameters - Page 2:
2025-06-14 21:00:11,067 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:11,067 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:11,599 - INFO - Response - Page 2:
2025-06-14 21:00:11,802 - INFO - 第 2 页获取到 100 条记录
2025-06-14 21:00:11,802 - INFO - Request Parameters - Page 3:
2025-06-14 21:00:11,802 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:11,802 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:12,286 - INFO - Response - Page 3:
2025-06-14 21:00:12,489 - INFO - 第 3 页获取到 100 条记录
2025-06-14 21:00:12,489 - INFO - Request Parameters - Page 4:
2025-06-14 21:00:12,489 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:12,489 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:12,911 - INFO - Response - Page 4:
2025-06-14 21:00:13,114 - INFO - 第 4 页获取到 100 条记录
2025-06-14 21:00:13,114 - INFO - Request Parameters - Page 5:
2025-06-14 21:00:13,114 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:13,114 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:13,661 - INFO - Response - Page 5:
2025-06-14 21:00:13,864 - INFO - 第 5 页获取到 100 条记录
2025-06-14 21:00:13,864 - INFO - Request Parameters - Page 6:
2025-06-14 21:00:13,864 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:13,864 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:14,348 - INFO - Response - Page 6:
2025-06-14 21:00:14,551 - INFO - 第 6 页获取到 100 条记录
2025-06-14 21:00:14,551 - INFO - Request Parameters - Page 7:
2025-06-14 21:00:14,551 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:14,551 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:15,020 - INFO - Response - Page 7:
2025-06-14 21:00:15,223 - INFO - 第 7 页获取到 70 条记录
2025-06-14 21:00:15,223 - INFO - 查询完成，共获取到 670 条记录
2025-06-14 21:00:15,223 - INFO - 获取到 670 条表单数据
2025-06-14 21:00:15,223 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-14 21:00:15,239 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 21:00:15,239 - INFO - 开始处理日期: 2025-03
2025-06-14 21:00:15,239 - INFO - Request Parameters - Page 1:
2025-06-14 21:00:15,239 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:15,239 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:15,770 - INFO - Response - Page 1:
2025-06-14 21:00:15,973 - INFO - 第 1 页获取到 100 条记录
2025-06-14 21:00:15,973 - INFO - Request Parameters - Page 2:
2025-06-14 21:00:15,973 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:15,973 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:16,536 - INFO - Response - Page 2:
2025-06-14 21:00:16,739 - INFO - 第 2 页获取到 100 条记录
2025-06-14 21:00:16,739 - INFO - Request Parameters - Page 3:
2025-06-14 21:00:16,739 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:16,739 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:17,270 - INFO - Response - Page 3:
2025-06-14 21:00:17,473 - INFO - 第 3 页获取到 100 条记录
2025-06-14 21:00:17,473 - INFO - Request Parameters - Page 4:
2025-06-14 21:00:17,473 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:17,473 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:17,989 - INFO - Response - Page 4:
2025-06-14 21:00:18,192 - INFO - 第 4 页获取到 100 条记录
2025-06-14 21:00:18,192 - INFO - Request Parameters - Page 5:
2025-06-14 21:00:18,192 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:18,192 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:18,770 - INFO - Response - Page 5:
2025-06-14 21:00:18,973 - INFO - 第 5 页获取到 100 条记录
2025-06-14 21:00:18,973 - INFO - Request Parameters - Page 6:
2025-06-14 21:00:18,973 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:18,973 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:19,442 - INFO - Response - Page 6:
2025-06-14 21:00:19,645 - INFO - 第 6 页获取到 100 条记录
2025-06-14 21:00:19,645 - INFO - Request Parameters - Page 7:
2025-06-14 21:00:19,645 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:19,645 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:20,113 - INFO - Response - Page 7:
2025-06-14 21:00:20,316 - INFO - 第 7 页获取到 61 条记录
2025-06-14 21:00:20,316 - INFO - 查询完成，共获取到 661 条记录
2025-06-14 21:00:20,316 - INFO - 获取到 661 条表单数据
2025-06-14 21:00:20,316 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-14 21:00:20,332 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 21:00:20,332 - INFO - 开始处理日期: 2025-04
2025-06-14 21:00:20,332 - INFO - Request Parameters - Page 1:
2025-06-14 21:00:20,332 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:20,332 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:20,832 - INFO - Response - Page 1:
2025-06-14 21:00:21,035 - INFO - 第 1 页获取到 100 条记录
2025-06-14 21:00:21,035 - INFO - Request Parameters - Page 2:
2025-06-14 21:00:21,035 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:21,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:21,598 - INFO - Response - Page 2:
2025-06-14 21:00:21,801 - INFO - 第 2 页获取到 100 条记录
2025-06-14 21:00:21,801 - INFO - Request Parameters - Page 3:
2025-06-14 21:00:21,801 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:21,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:22,238 - INFO - Response - Page 3:
2025-06-14 21:00:22,441 - INFO - 第 3 页获取到 100 条记录
2025-06-14 21:00:22,441 - INFO - Request Parameters - Page 4:
2025-06-14 21:00:22,441 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:22,441 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:22,894 - INFO - Response - Page 4:
2025-06-14 21:00:23,097 - INFO - 第 4 页获取到 100 条记录
2025-06-14 21:00:23,097 - INFO - Request Parameters - Page 5:
2025-06-14 21:00:23,097 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:23,097 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:23,597 - INFO - Response - Page 5:
2025-06-14 21:00:23,800 - INFO - 第 5 页获取到 100 条记录
2025-06-14 21:00:23,800 - INFO - Request Parameters - Page 6:
2025-06-14 21:00:23,800 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:23,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:24,238 - INFO - Response - Page 6:
2025-06-14 21:00:24,441 - INFO - 第 6 页获取到 100 条记录
2025-06-14 21:00:24,441 - INFO - Request Parameters - Page 7:
2025-06-14 21:00:24,441 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:24,441 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:24,878 - INFO - Response - Page 7:
2025-06-14 21:00:25,082 - INFO - 第 7 页获取到 56 条记录
2025-06-14 21:00:25,082 - INFO - 查询完成，共获取到 656 条记录
2025-06-14 21:00:25,082 - INFO - 获取到 656 条表单数据
2025-06-14 21:00:25,082 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-14 21:00:25,097 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-14 21:00:25,097 - INFO - 开始处理日期: 2025-05
2025-06-14 21:00:25,097 - INFO - Request Parameters - Page 1:
2025-06-14 21:00:25,097 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:25,097 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:25,691 - INFO - Response - Page 1:
2025-06-14 21:00:25,894 - INFO - 第 1 页获取到 100 条记录
2025-06-14 21:00:25,894 - INFO - Request Parameters - Page 2:
2025-06-14 21:00:25,894 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:25,894 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:26,472 - INFO - Response - Page 2:
2025-06-14 21:00:26,675 - INFO - 第 2 页获取到 100 条记录
2025-06-14 21:00:26,675 - INFO - Request Parameters - Page 3:
2025-06-14 21:00:26,675 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:26,675 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:27,238 - INFO - Response - Page 3:
2025-06-14 21:00:27,441 - INFO - 第 3 页获取到 100 条记录
2025-06-14 21:00:27,441 - INFO - Request Parameters - Page 4:
2025-06-14 21:00:27,441 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:27,441 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:27,925 - INFO - Response - Page 4:
2025-06-14 21:00:28,128 - INFO - 第 4 页获取到 100 条记录
2025-06-14 21:00:28,128 - INFO - Request Parameters - Page 5:
2025-06-14 21:00:28,128 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:28,128 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:28,659 - INFO - Response - Page 5:
2025-06-14 21:00:28,862 - INFO - 第 5 页获取到 100 条记录
2025-06-14 21:00:28,862 - INFO - Request Parameters - Page 6:
2025-06-14 21:00:28,862 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:28,862 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:29,441 - INFO - Response - Page 6:
2025-06-14 21:00:29,644 - INFO - 第 6 页获取到 100 条记录
2025-06-14 21:00:29,644 - INFO - Request Parameters - Page 7:
2025-06-14 21:00:29,644 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:29,644 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:30,003 - INFO - Response - Page 7:
2025-06-14 21:00:30,206 - INFO - 第 7 页获取到 40 条记录
2025-06-14 21:00:30,206 - INFO - 查询完成，共获取到 640 条记录
2025-06-14 21:00:30,206 - INFO - 获取到 640 条表单数据
2025-06-14 21:00:30,206 - INFO - 当前日期 2025-05 有 640 条MySQL数据需要处理
2025-06-14 21:00:30,222 - INFO - 开始更新记录 - 表单实例ID: FINST-JAC66MB11S4WW8P59B7WX8S5TNJO336PMTOBMQ6
2025-06-14 21:00:30,690 - INFO - 更新表单数据成功: FINST-JAC66MB11S4WW8P59B7WX8S5TNJO336PMTOBMQ6
2025-06-14 21:00:30,690 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 108952.05, 'new_value': 53514.81}, {'field': 'offline_amount', 'old_value': 1128216.87, 'new_value': 1182761.32}, {'field': 'total_amount', 'old_value': 1237168.92, 'new_value': 1236276.13}]
2025-06-14 21:00:30,690 - INFO - 开始更新记录 - 表单实例ID: FINST-TKF66981MO5WD3YX5FMY56GSE1OI37QRMTOBMF1
2025-06-14 21:00:31,143 - INFO - 更新表单数据成功: FINST-TKF66981MO5WD3YX5FMY56GSE1OI37QRMTOBMF1
2025-06-14 21:00:31,143 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 189157.03, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 132200.45, 'new_value': 418096.07}, {'field': 'total_amount', 'old_value': 321357.48, 'new_value': 418096.07}]
2025-06-14 21:00:31,143 - INFO - 开始更新记录 - 表单实例ID: FINST-TKF66981MO5WD3YX5FMY56GSE1OI37QRMTOBM12
2025-06-14 21:00:31,659 - INFO - 更新表单数据成功: FINST-TKF66981MO5WD3YX5FMY56GSE1OI37QRMTOBM12
2025-06-14 21:00:31,659 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120516.9, 'new_value': 117371.9}, {'field': 'total_amount', 'old_value': 120516.9, 'new_value': 117371.9}]
2025-06-14 21:00:31,659 - INFO - 开始更新记录 - 表单实例ID: FINST-VFF66XA1KQ2W2WLG87XYYBGH1GHN2ESWMTOBMJZ
2025-06-14 21:00:32,143 - INFO - 更新表单数据成功: FINST-VFF66XA1KQ2W2WLG87XYYBGH1GHN2ESWMTOBMJZ
2025-06-14 21:00:32,143 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1913.05, 'new_value': 1238.49}, {'field': 'offline_amount', 'old_value': 751418.06, 'new_value': 753644.47}, {'field': 'total_amount', 'old_value': 753331.11, 'new_value': 754882.96}]
2025-06-14 21:00:32,143 - INFO - 开始更新记录 - 表单实例ID: FINST-WBF66B81QA2WH8JHA7ECY85SHGL32MBZMTOBMG11
2025-06-14 21:00:32,581 - INFO - 更新表单数据成功: FINST-WBF66B81QA2WH8JHA7ECY85SHGL32MBZMTOBMG11
2025-06-14 21:00:32,581 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18362.45, 'new_value': 873.45}, {'field': 'offline_amount', 'old_value': 348565.0, 'new_value': 368060.36}, {'field': 'total_amount', 'old_value': 366927.45, 'new_value': 368933.81}]
2025-06-14 21:00:32,581 - INFO - 开始更新记录 - 表单实例ID: FINST-RTA66X61LO5W721S827476KZ4D8Z1GU1NTOBMU
2025-06-14 21:00:33,050 - INFO - 更新表单数据成功: FINST-RTA66X61LO5W721S827476KZ4D8Z1GU1NTOBMU
2025-06-14 21:00:33,050 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 432770.0, 'new_value': 440246.0}, {'field': 'total_amount', 'old_value': 432770.0, 'new_value': 440246.0}]
2025-06-14 21:00:33,050 - INFO - 开始更新记录 - 表单实例ID: FINST-5TD66N914E2WK805DQ5DHC98NSLM3IB4NTOBMWF
2025-06-14 21:00:33,440 - INFO - 更新表单数据成功: FINST-5TD66N914E2WK805DQ5DHC98NSLM3IB4NTOBMWF
2025-06-14 21:00:33,440 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58631.66, 'new_value': 57474.5}, {'field': 'total_amount', 'old_value': 58631.66, 'new_value': 57474.5}]
2025-06-14 21:00:33,440 - INFO - 开始更新记录 - 表单实例ID: FINST-5TD66N914E2WK805DQ5DHC98NSLM3IB4NTOBMXF
2025-06-14 21:00:33,909 - INFO - 更新表单数据成功: FINST-5TD66N914E2WK805DQ5DHC98NSLM3IB4NTOBMXF
2025-06-14 21:00:33,909 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 161630.61, 'new_value': 153658.41}, {'field': 'offline_amount', 'old_value': 806836.03, 'new_value': 814309.23}, {'field': 'total_amount', 'old_value': 968466.64, 'new_value': 967967.64}]
2025-06-14 21:00:33,909 - INFO - 开始更新记录 - 表单实例ID: FINST-5TD66N914E2WK805DQ5DHC98NSLM3IB4NTOBM0G
2025-06-14 21:00:34,487 - INFO - 更新表单数据成功: FINST-5TD66N914E2WK805DQ5DHC98NSLM3IB4NTOBM0G
2025-06-14 21:00:34,487 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 111194.93, 'new_value': 97174.36}, {'field': 'offline_amount', 'old_value': 1133524.84, 'new_value': 1147465.21}, {'field': 'total_amount', 'old_value': 1242845.44, 'new_value': 1244639.57}]
2025-06-14 21:00:34,487 - INFO - 日期 2025-05 处理完成 - 更新: 9 条，插入: 0 条，错误: 0 条
2025-06-14 21:00:34,487 - INFO - 开始处理日期: 2025-06
2025-06-14 21:00:34,487 - INFO - Request Parameters - Page 1:
2025-06-14 21:00:34,487 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:34,487 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:34,909 - INFO - Response - Page 1:
2025-06-14 21:00:35,112 - INFO - 第 1 页获取到 100 条记录
2025-06-14 21:00:35,112 - INFO - Request Parameters - Page 2:
2025-06-14 21:00:35,112 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:35,112 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:35,612 - INFO - Response - Page 2:
2025-06-14 21:00:35,815 - INFO - 第 2 页获取到 100 条记录
2025-06-14 21:00:35,815 - INFO - Request Parameters - Page 3:
2025-06-14 21:00:35,815 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:35,815 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:36,362 - INFO - Response - Page 3:
2025-06-14 21:00:36,565 - INFO - 第 3 页获取到 100 条记录
2025-06-14 21:00:36,565 - INFO - Request Parameters - Page 4:
2025-06-14 21:00:36,565 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:36,565 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:37,080 - INFO - Response - Page 4:
2025-06-14 21:00:37,284 - INFO - 第 4 页获取到 100 条记录
2025-06-14 21:00:37,284 - INFO - Request Parameters - Page 5:
2025-06-14 21:00:37,284 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:37,284 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:37,908 - INFO - Response - Page 5:
2025-06-14 21:00:38,112 - INFO - 第 5 页获取到 100 条记录
2025-06-14 21:00:38,112 - INFO - Request Parameters - Page 6:
2025-06-14 21:00:38,112 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:38,112 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:38,721 - INFO - Response - Page 6:
2025-06-14 21:00:38,924 - INFO - 第 6 页获取到 100 条记录
2025-06-14 21:00:38,924 - INFO - Request Parameters - Page 7:
2025-06-14 21:00:38,924 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-14 21:00:38,924 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-14 21:00:39,268 - INFO - Response - Page 7:
2025-06-14 21:00:39,471 - INFO - 第 7 页获取到 21 条记录
2025-06-14 21:00:39,471 - INFO - 查询完成，共获取到 621 条记录
2025-06-14 21:00:39,471 - INFO - 获取到 621 条表单数据
2025-06-14 21:00:39,471 - INFO - 当前日期 2025-06 有 621 条MySQL数据需要处理
2025-06-14 21:00:39,471 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMSX
2025-06-14 21:00:39,971 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMSX
2025-06-14 21:00:39,971 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14162.98, 'new_value': 14834.98}, {'field': 'offline_amount', 'old_value': 7708.66, 'new_value': 8573.66}, {'field': 'total_amount', 'old_value': 21871.64, 'new_value': 23408.64}, {'field': 'order_count', 'old_value': 914, 'new_value': 972}]
2025-06-14 21:00:39,986 - INFO - 日期 2025-06 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-06-14 21:00:39,986 - INFO - 数据同步完成！更新: 10 条，插入: 0 条，错误: 0 条
2025-06-14 21:00:39,986 - INFO - =================同步完成====================
