# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
import sys

from typing import List

from alibabacloud_dingtalk.yida_1_0.client import Client as dingtalkyida_1_0Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dingtalk.yida_1_0 import models as dingtalkyida__1__0_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_util.client import Client as UtilClient


class Sample:
    def __init__(self):
        pass

    @staticmethod
    def create_client() -> dingtalkyida_1_0Client:
        """
        使用 Token 初始化账号Client
        @return: Client
        @throws Exception
        """
        config = open_api_models.Config()
        config.protocol = 'https'
        config.region_id = 'central'
        return dingtalkyida_1_0Client(config)
#批量删除钉钉宜搭表单数据
    @staticmethod
    def main(
        args: List[str],
    ) -> None:
        client = Sample.create_client()
        batch_removal_by_form_instance_id_list_headers = dingtalkyida__1__0_models.BatchRemovalByFormInstanceIdListHeaders()
        batch_removal_by_form_instance_id_list_headers.x_acs_dingtalk_access_token = '<your access token>'
        batch_removal_by_form_instance_id_list_request = dingtalkyida__1__0_models.BatchRemovalByFormInstanceIdListRequest(
            form_uuid='FORM-GX866MC1NC1VOFF6WVQW33FD16E23L3CPMKVKA',
            app_type='APP_XCE0EVXS6DYG3YDYC5RD',
            asynchronous_execution=True,
            system_token='09866181UTZVVD4R3DC955FNKIM52HVPU5WWK7',
            form_instance_id_list=[
                'FINST-J8766S91O2UYN87ZX3XOF1MY8MBA2912BSV0L24'
            ],
            user_id='ding173982232112232',
            execute_expression=False
        )
        try:
            client.batch_removal_by_form_instance_id_list_with_options(batch_removal_by_form_instance_id_list_request, batch_removal_by_form_instance_id_list_headers, util_models.RuntimeOptions())
        #返回示例
        #HTTP/1.1 200 OK
        except Exception as err:
            if not UtilClient.empty(err.code) and not UtilClient.empty(err.message):
                # err 中含有 code 和 message 属性，可帮助开发定位问题
                pass

if __name__ == '__main__':
    Sample.main(sys.argv[1:])


# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
import os
import sys

from typing import List

from alibabacloud_dingtalk.yida_2_0.client import Client as dingtalkyida_2_0Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dingtalk.yida_2_0 import models as dingtalkyida__2__0_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_util.client import Client as UtilClient


class Sample1:
    def __init__(self):
        pass

    @staticmethod
    def create_client() -> dingtalkyida_2_0Client:
        """
        使用 Token 初始化账号Client
        @return: Client
        @throws Exception
        """
        config = open_api_models.Config()
        config.protocol = 'https'
        config.region_id = 'central'
        return dingtalkyida_2_0Client(config)
#批量查询钉钉宜搭表单实例ID
    @staticmethod
    def main(
        args: List[str],
    ) -> None:
        client = Sample.create_client()
        search_form_data_id_list_headers = dingtalkyida__2__0_models.SearchFormDataIdListHeaders()
        search_form_data_id_list_headers.x_acs_dingtalk_access_token = '<your access token>'
        search_form_data_id_list_request = dingtalkyida__2__0_models.SearchFormDataIdListRequest(
            page_number=1,
            page_size=100,
            modified_to_time_gmt='2021-09-10',
            system_token='hexxxx',
            modified_from_time_gmt='2021-05-01',
            language='zh_CN',
            search_field_json='123',
            user_id='manager123',
            originator_id='manager123',
            create_to_time_gmt='2021-05-01',
            create_from_time_gmt='2021-05-01',
            use_alias=False
        )
        try:
            client.search_form_data_id_list_with_options('APP_PBKTxxx', 'FORM-EF6Y4Gxxx', search_form_data_id_list_request, search_form_data_id_list_headers, util_models.RuntimeOptions())
#返回示例
#         HTTP/1.1 200 OK
# Content-Type:application/json

# {
#   "totalCount" : 10,
#   "pageNumber" : 1,
#   "data" : [ "INST-cadfafasdxxx" ]
# }
        
        except Exception as err:
            if not UtilClient.empty(err.code) and not UtilClient.empty(err.message):
                # err 中含有 code 和 message 属性，可帮助开发定位问题
                pass

    @staticmethod
    async def main_async(
        args: List[str],
    ) -> None:
        client = Sample.create_client()
        search_form_data_id_list_headers = dingtalkyida__2__0_models.SearchFormDataIdListHeaders()
        search_form_data_id_list_headers.x_acs_dingtalk_access_token = '<your access token>'
        search_form_data_id_list_request = dingtalkyida__2__0_models.SearchFormDataIdListRequest(
            page_number=1,
            page_size=100,
            modified_to_time_gmt='2021-09-10',
            system_token='hexxxx',
            modified_from_time_gmt='2021-05-01',
            language='zh_CN',
            search_field_json='123',
            user_id='manager123',
            originator_id='manager123',
            create_to_time_gmt='2021-05-01',
            create_from_time_gmt='2021-05-01',
            use_alias=False
        )
        try:
            await client.search_form_data_id_list_with_options_async('APP_PBKTxxx', 'FORM-EF6Y4Gxxx', search_form_data_id_list_request, search_form_data_id_list_headers, util_models.RuntimeOptions())
        except Exception as err:
            if not UtilClient.empty(err.code) and not UtilClient.empty(err.message):
                # err 中含有 code 和 message 属性，可帮助开发定位问题
                pass


if __name__ == '__main__':
    Sample.main(sys.argv[1:])