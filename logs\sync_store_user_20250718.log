2025-07-18 09:00:02,717 - INFO - 数据库连接成功
2025-07-18 09:00:02,889 - INFO - 获取钉钉access_token成功
2025-07-18 09:00:02,889 - INFO - 宜搭客户端初始化成功
2025-07-18 09:00:02,889 - INFO - 正在获取数据库店铺信息...
2025-07-18 09:00:02,889 - INFO - 第一步：获取基础店铺信息...
2025-07-18 09:00:02,936 - INFO - 获取基础店铺信息成功，共 1276 条记录
2025-07-18 09:00:02,936 - INFO - 第二步：获取上个月或本月有销售记录的店铺...
2025-07-18 09:00:03,030 - INFO - 上个月或本月有销售记录的店铺数量: 664
2025-07-18 09:00:03,030 - INFO - 第三步：获取上个月以前有销售记录的店铺...
2025-07-18 09:00:03,217 - INFO - 上个月以前有销售记录的店铺数量: 651
2025-07-18 09:00:03,217 - INFO - 检测到新店铺数量: 13
2025-07-18 09:00:03,217 - INFO - 新店铺编码列表: ['100101299', '100101312', '100101297', '100101302', '100101310', '100101266', '100101426', '100100073', '100101278', '100101313', '100101272', '100101291', '100101301']
2025-07-18 09:00:03,217 - INFO - 成功获取数据库店铺信息，共 1276 条记录
2025-07-18 09:00:03,217 - INFO - 正在获取宜搭店铺信息...
2025-07-18 09:00:04,639 - INFO - 店铺 100101285 的userid值为空
2025-07-18 09:00:04,639 - INFO - 店铺 100101312 的userid值为空
2025-07-18 09:00:04,639 - INFO - 店铺 100101313 的userid值为空
2025-07-18 09:00:04,639 - INFO - 店铺 ********* 的userid值为空
2025-07-18 09:00:04,639 - INFO - 店铺 100101426 的userid值为空
2025-07-18 09:00:04,639 - INFO - 店铺 100101428 的userid值为空
2025-07-18 09:00:04,639 - INFO - 店铺 100101427 的userid值为空
2025-07-18 09:00:04,639 - INFO - 店铺 100100657 的userid值为空
2025-07-18 09:00:04,639 - INFO - 店铺 100101429 的userid值为空
2025-07-18 09:00:04,639 - INFO - 店铺 100101425 的userid值为空
2025-07-18 09:00:04,639 - INFO - 店铺 100101417 的userid值为空
2025-07-18 09:00:04,639 - INFO - 店铺 100101317 的userid值为空
2025-07-18 09:00:04,639 - INFO - 店铺 100101318 的userid值为空
2025-07-18 09:00:04,639 - INFO - 店铺 100101319 的userid值为空
2025-07-18 09:00:04,639 - INFO - 店铺 100101316 的userid值为空
2025-07-18 09:00:05,624 - INFO - 店铺 100100832 的userid值为空
2025-07-18 09:00:05,624 - INFO - 店铺 100098453 的userid值为空
2025-07-18 09:00:05,624 - INFO - 店铺 100101303 的userid值为空
2025-07-18 09:00:05,624 - INFO - 店铺 100101288 的userid值为空
2025-07-18 09:00:05,624 - INFO - 店铺 100099214 的userid值为空
2025-07-18 09:00:06,858 - INFO - 店铺 100098428 的userid值为空
2025-07-18 09:00:06,858 - INFO - 店铺 100099748 的userid值为空
2025-07-18 09:00:07,686 - INFO - 店铺 100098437 的userid值为空
2025-07-18 09:00:07,686 - INFO - 店铺 100100085 的userid值为空
2025-07-18 09:00:07,686 - INFO - 店铺 100100110 的userid值为空
2025-07-18 09:00:07,686 - INFO - 店铺 100100135 的userid值为空
2025-07-18 09:00:07,686 - INFO - 店铺 100100854 的userid值为空
2025-07-18 09:00:07,686 - INFO - 店铺 100100842 的userid值为空
2025-07-18 09:00:07,686 - INFO - 店铺 100098300 的userid值为空
2025-07-18 09:00:07,686 - INFO - 店铺 100100048 的userid值为空
2025-07-18 09:00:07,686 - INFO - 店铺 100098427 的userid值为空
2025-07-18 09:00:07,686 - INFO - 店铺 100100071 的userid值为空
2025-07-18 09:00:07,686 - INFO - 店铺 100098274 的userid值为空
2025-07-18 09:00:07,686 - INFO - 店铺 100098440 的userid值为空
2025-07-18 09:00:07,686 - INFO - 店铺 100100091 的userid值为空
2025-07-18 09:00:07,686 - INFO - 店铺 100100113 的userid值为空
2025-07-18 09:00:07,686 - INFO - 店铺 100098303 的userid值为空
2025-07-18 09:00:07,686 - INFO - 店铺 100100140 的userid值为空
2025-07-18 09:00:07,686 - INFO - 店铺 100100851 的userid值为空
2025-07-18 09:00:07,686 - INFO - 店铺 100098289 的userid值为空
2025-07-18 09:00:07,686 - INFO - 店铺 100100839 的userid值为空
2025-07-18 09:00:07,686 - INFO - 店铺 100098260 的userid值为空
2025-07-18 09:00:07,686 - INFO - 店铺 100100070 的userid值为空
2025-07-18 09:00:07,686 - INFO - 店铺 100100089 的userid值为空
2025-07-18 09:00:08,358 - INFO - 店铺 100100112 的userid值为空
2025-07-18 09:00:08,358 - INFO - 店铺 100098273 的userid值为空
2025-07-18 09:00:08,358 - INFO - 店铺 100100852 的userid值为空
2025-07-18 09:00:08,358 - INFO - 店铺 100098486 的userid值为空
2025-07-18 09:00:08,358 - INFO - 店铺 100100840 的userid值为空
2025-07-18 09:00:08,358 - INFO - 店铺 100100101 的userid值为空
2025-07-18 09:00:08,358 - INFO - 店铺 100098296 的userid值为空
2025-07-18 09:00:08,358 - INFO - 店铺 100100130 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100098355 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100100849 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100100129 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100100845 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100098477 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100100033 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100098295 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100098250 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100100850 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100100050 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100098237 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100100083 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100098276 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100100108 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100100094 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100100117 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100098474 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100100134 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100100858 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100098343 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100100846 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100100829 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100098398 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100098284 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100100049 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100100106 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100100133 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100098249 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100100092 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100098297 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100099109 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100100114 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100098275 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100098342 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100100141 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100100855 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100098597 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100100843 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100098385 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100100037 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100100371 的userid值为空
2025-07-18 09:00:08,374 - INFO - 店铺 100100053 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100100075 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100098447 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100100096 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100098461 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100100856 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100100844 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100098384 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100100035 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100100052 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100100074 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100098445 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100100095 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100100121 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100100067 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100100088 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100100111 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100100136 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100100853 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100098287 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100098485 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100100841 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100099117 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100099272 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100101121 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100099265 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100099243 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100100369 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100099231 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100099216 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100100361 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100100328 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100099189 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100099931 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100100278 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100099304 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100099305 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100101122 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100099289 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100101265 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100100438 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100101275 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100099266 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100101304 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100099212 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100099188 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100099150 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100100224 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100099332 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100100881 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100099299 的userid值为空
2025-07-18 09:00:08,999 - INFO - 店铺 100100456 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099284 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100100880 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100101119 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099829 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099263 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099153 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100100236 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099936 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100101120 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100100447 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100100865 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099264 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100100875 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099186 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099824 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100100264 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099147 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100100217 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099930 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099346 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099323 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099297 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099259 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100100964 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099187 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099984 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100100265 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099928 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099148 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100100894 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099983 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099796 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099298 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100101118 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099261 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100100435 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099870 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099199 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099985 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100100262 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099317 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099978 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100100436 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100101181 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100100321 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099185 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100100233 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099345 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099980 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099322 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100100893 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100100437 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099282 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100100892 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100100433 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099979 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100100665 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099923 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100101183 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099197 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100100241 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 100099176 的userid值为空
2025-07-18 09:00:09,827 - INFO - 店铺 ********* 的userid值为空
2025-07-18 09:00:09,842 - INFO - 店铺 100098583 的userid值为空
2025-07-18 09:00:10,608 - INFO - 店铺 100101173 的userid值为空
2025-07-18 09:00:10,608 - INFO - 店铺 100100944 的userid值为空
2025-07-18 09:00:10,608 - INFO - 店铺 100099803 的userid值为空
2025-07-18 09:00:10,608 - INFO - 店铺 100098579 的userid值为空
2025-07-18 09:00:10,608 - INFO - 店铺 100101153 的userid值为空
2025-07-18 09:00:10,608 - INFO - 店铺 100099804 的userid值为空
2025-07-18 09:00:10,608 - INFO - 店铺 100100359 的userid值为空
2025-07-18 09:00:10,608 - INFO - 店铺 100100344 的userid值为空
2025-07-18 09:00:10,608 - INFO - 店铺 100100818 的userid值为空
2025-07-18 09:00:10,608 - INFO - 店铺 100099863 的userid值为空
2025-07-18 09:00:10,608 - INFO - 店铺 100100347 的userid值为空
2025-07-18 09:00:10,608 - INFO - 店铺 100100667 的userid值为空
2025-07-18 09:00:10,608 - INFO - 店铺 100100891 的userid值为空
2025-07-18 09:00:10,608 - INFO - 店铺 100099853 的userid值为空
2025-07-18 09:00:10,608 - INFO - 店铺 100100890 的userid值为空
2025-07-18 09:00:10,608 - INFO - 店铺 100100342 的userid值为空
2025-07-18 09:00:10,608 - INFO - 店铺 100099861 的userid值为空
2025-07-18 09:00:10,608 - INFO - 店铺 100100670 的userid值为空
2025-07-18 09:00:10,608 - INFO - 店铺 100100341 的userid值为空
2025-07-18 09:00:10,608 - INFO - 店铺 100100887 的userid值为空
2025-07-18 09:00:10,608 - INFO - 店铺 100100481 的userid值为空
2025-07-18 09:00:10,608 - INFO - 店铺 100100886 的userid值为空
2025-07-18 09:00:10,608 - INFO - 店铺 100099911 的userid值为空
2025-07-18 09:00:10,608 - INFO - 店铺 100100646 的userid值为空
2025-07-18 09:00:10,608 - INFO - 店铺 100100889 的userid值为空
2025-07-18 09:00:10,608 - INFO - 店铺 100100888 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100100883 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100100922 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100100882 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100100016 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100099835 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100100885 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100099910 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100100884 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100099956 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100099303 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100100006 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100100900 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100099903 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100099283 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100100024 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100100293 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100100434 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100098391 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100100044 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100099198 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100100314 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100100061 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100099177 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100100249 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100098436 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100100848 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100100833 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100099294 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100100453 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100100199 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100098358 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100099280 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100101129 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100100379 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100100229 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100100663 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100099256 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100099237 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100100374 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100100339 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100100042 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100099192 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100098405 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100100060 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100100082 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100099295 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100099281 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100100664 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100099101 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100100380 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100100375 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100101243 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100099226 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100101241 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100099193 的userid值为空
2025-07-18 09:00:11,311 - INFO - 店铺 100101224 的userid值为空
2025-07-18 09:00:11,327 - INFO - 店铺 100099313 的userid值为空
2025-07-18 09:00:11,327 - INFO - 店铺 100100084 的userid值为空
2025-07-18 09:00:11,327 - INFO - 店铺 100099292 的userid值为空
2025-07-18 09:00:11,327 - INFO - 店铺 100101242 的userid值为空
2025-07-18 09:00:11,327 - INFO - 店铺 100101256 的userid值为空
2025-07-18 09:00:11,327 - INFO - 店铺 100099277 的userid值为空
2025-07-18 09:00:11,327 - INFO - 店铺 100100918 的userid值为空
2025-07-18 09:00:11,327 - INFO - 店铺 100101125 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100101154 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100100377 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100101156 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100099271 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100101132 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100101155 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100099217 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100101283 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100099190 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100101284 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100101282 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100101287 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100101296 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100101295 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100099293 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100101308 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100100378 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100099249 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100100373 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100099218 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100100335 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100099191 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100100291 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100099171 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100100238 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100099338 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100099307 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100099290 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100099274 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100101123 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100100466 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100100368 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100100327 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100100269 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100101124 的userid值为空
2025-07-18 09:00:12,170 - INFO - 店铺 100099291 的userid值为空
2025-07-18 09:00:12,186 - INFO - 店铺 100099275 的userid值为空
2025-07-18 09:00:12,186 - INFO - 店铺 100100926 的userid值为空
2025-07-18 09:00:12,186 - INFO - 店铺 100100376 的userid值为空
2025-07-18 09:00:12,186 - INFO - 店铺 100099270 的userid值为空
2025-07-18 09:00:12,186 - INFO - 店铺 100098351 的userid值为空
2025-07-18 09:00:12,186 - INFO - 店铺 100100156 的userid值为空
2025-07-18 09:00:12,186 - INFO - 店铺 100099615 的userid值为空
2025-07-18 09:00:12,186 - INFO - 店铺 100099905 的userid值为空
2025-07-18 09:00:12,186 - INFO - 店铺 100098488 的userid值为空
2025-07-18 09:00:12,186 - INFO - 店铺 100098350 的userid值为空
2025-07-18 09:00:12,186 - INFO - 店铺 100099503 的userid值为空
2025-07-18 09:00:12,186 - INFO - 店铺 100100027 的userid值为空
2025-07-18 09:00:12,186 - INFO - 店铺 100099613 的userid值为空
2025-07-18 09:00:12,186 - INFO - 店铺 100100004 的userid值为空
2025-07-18 09:00:12,186 - INFO - 店铺 100100154 的userid值为空
2025-07-18 09:00:12,186 - INFO - 店铺 100099500 的userid值为空
2025-07-18 09:00:12,186 - INFO - 店铺 100101180 的userid值为空
2025-07-18 09:00:12,983 - INFO - 店铺 100100046 的userid值为空
2025-07-18 09:00:12,983 - INFO - 店铺 100099899 的userid值为空
2025-07-18 09:00:12,983 - INFO - 店铺 100098424 的userid值为空
2025-07-18 09:00:12,983 - INFO - 店铺 100098246 的userid值为空
2025-07-18 09:00:12,983 - INFO - 店铺 100100128 的userid值为空
2025-07-18 09:00:12,983 - INFO - 店铺 100100182 的userid值为空
2025-07-18 09:00:12,983 - INFO - 店铺 100100917 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100100201 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100098360 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100098572 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100099995 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100098377 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100100026 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100098598 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100100300 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100100279 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100098596 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100099681 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100100045 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100100870 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100099588 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100098423 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100099675 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100099988 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100098258 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100100177 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100100162 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100100198 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100099793 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100100187 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100099104 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100099816 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100100904 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100100210 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100099499 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100100148 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100100175 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100099913 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100100196 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100100303 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100100280 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100100029 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100098317 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100098397 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100100860 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100100255 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100100661 的userid值为空
2025-07-18 09:00:12,999 - INFO - 店铺 100098589 的userid值为空
2025-07-18 09:00:13,811 - INFO - 店铺 100100152 的userid值为空
2025-07-18 09:00:13,811 - INFO - 店铺 100099525 的userid值为空
2025-07-18 09:00:13,811 - INFO - 店铺 100100160 的userid值为空
2025-07-18 09:00:13,811 - INFO - 店铺 100100180 的userid值为空
2025-07-18 09:00:13,811 - INFO - 店铺 100098594 的userid值为空
2025-07-18 09:00:13,811 - INFO - 店铺 100099119 的userid值为空
2025-07-18 09:00:13,811 - INFO - 店铺 100099987 的userid值为空
2025-07-18 09:00:13,811 - INFO - 店铺 100100185 的userid值为空
2025-07-18 09:00:13,811 - INFO - 店铺 100098561 的userid值为空
2025-07-18 09:00:13,811 - INFO - 店铺 100098348 的userid值为空
2025-07-18 09:00:13,811 - INFO - 店铺 100100207 的userid值为空
2025-07-18 09:00:13,811 - INFO - 店铺 100100861 的userid值为空
2025-07-18 09:00:13,811 - INFO - 店铺 100098559 的userid值为空
2025-07-18 09:00:13,811 - INFO - 店铺 100098362 的userid值为空
2025-07-18 09:00:13,811 - INFO - 店铺 100100150 的userid值为空
2025-07-18 09:00:13,811 - INFO - 店铺 100100258 的userid值为空
2025-07-18 09:00:13,811 - INFO - 店铺 100099906 的userid值为空
2025-07-18 09:00:13,811 - INFO - 店铺 100100178 的userid值为空
2025-07-18 09:00:13,811 - INFO - 店铺 100099811 的userid值为空
2025-07-18 09:00:13,811 - INFO - 店铺 100100028 的userid值为空
2025-07-18 09:00:13,811 - INFO - 店铺 100098588 的userid值为空
2025-07-18 09:00:13,811 - INFO - 店铺 100100143 的userid值为空
2025-07-18 09:00:13,811 - INFO - 店铺 100100320 的userid值为空
2025-07-18 09:00:13,811 - INFO - 店铺 100099585 的userid值为空
2025-07-18 09:00:13,811 - INFO - 店铺 100098247 的userid值为空
2025-07-18 09:00:13,827 - INFO - 店铺 100100166 的userid值为空
2025-07-18 09:00:13,827 - INFO - 店铺 100098396 的userid值为空
2025-07-18 09:00:13,827 - INFO - 店铺 100099797 的userid值为空
2025-07-18 09:00:13,827 - INFO - 店铺 100100047 的userid值为空
2025-07-18 09:00:13,827 - INFO - 店铺 100100810 的userid值为空
2025-07-18 09:00:13,827 - INFO - 店铺 100100192 的userid值为空
2025-07-18 09:00:13,827 - INFO - 店铺 100100460 的userid值为空
2025-07-18 09:00:13,827 - INFO - 店铺 100101190 的userid值为空
2025-07-18 09:00:13,827 - INFO - 店铺 100100834 的userid值为空
2025-07-18 09:00:13,827 - INFO - 店铺 100099831 的userid值为空
2025-07-18 09:00:13,827 - INFO - 店铺 100100214 的userid值为空
2025-07-18 09:00:13,827 - INFO - 店铺 100100459 的userid值为空
2025-07-18 09:00:13,827 - INFO - 店铺 100099650 的userid值为空
2025-07-18 09:00:13,827 - INFO - 店铺 100098367 的userid值为空
2025-07-18 09:00:13,827 - INFO - 店铺 100100478 的userid值为空
2025-07-18 09:00:13,827 - INFO - 店铺 100100261 的userid值为空
2025-07-18 09:00:13,827 - INFO - 店铺 100101200 的userid值为空
2025-07-18 09:00:13,827 - INFO - 店铺 100100020 的userid值为空
2025-07-18 09:00:13,827 - INFO - 店铺 100100465 的userid值为空
2025-07-18 09:00:13,827 - INFO - 店铺 100101236 的userid值为空
2025-07-18 09:00:13,827 - INFO - 店铺 100100041 的userid值为空
2025-07-18 09:00:13,827 - INFO - 店铺 100100859 的userid值为空
2025-07-18 09:00:13,827 - INFO - 店铺 100100142 的userid值为空
2025-07-18 09:00:13,827 - INFO - 店铺 100101254 的userid值为空
2025-07-18 09:00:13,827 - INFO - 店铺 100101246 的userid值为空
2025-07-18 09:00:13,827 - INFO - 店铺 100100056 的userid值为空
2025-07-18 09:00:13,827 - INFO - 店铺 100099794 的userid值为空
2025-07-18 09:00:13,827 - INFO - 店铺 100100189 的userid值为空
2025-07-18 09:00:13,827 - INFO - 店铺 100099629 的userid值为空
2025-07-18 09:00:13,827 - INFO - 店铺 100101292 的userid值为空
2025-07-18 09:00:13,827 - INFO - 店铺 100100266 的userid值为空
2025-07-18 09:00:13,827 - INFO - 店铺 100101306 的userid值为空
2025-07-18 09:00:13,827 - INFO - 店铺 100100081 的userid值为空
2025-07-18 09:00:13,827 - INFO - 店铺 100100212 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100101309 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100098449 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100098268 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100099636 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100100838 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100101314 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100100260 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100100030 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100100480 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100100462 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100100461 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100100146 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100099553 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100100172 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100100365 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100100040 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100100195 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100099541 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100100251 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100099847 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100100216 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100100055 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100098369 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100099555 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100098433 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100099992 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100100366 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100100076 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100100144 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100099507 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100098253 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100100169 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100098448 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100099806 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100100097 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100100194 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100099506 的userid值为空
2025-07-18 09:00:14,436 - INFO - 店铺 100099587 的userid值为空
2025-07-18 09:00:14,452 - INFO - 店铺 100100847 的userid值为空
2025-07-18 09:00:14,452 - INFO - 店铺 100100215 的userid值为空
2025-07-18 09:00:14,452 - INFO - 店铺 100099539 的userid值为空
2025-07-18 09:00:14,452 - INFO - 店铺 ********* 的userid值为空
2025-07-18 09:00:14,452 - INFO - 店铺 ********* 的userid值为空
2025-07-18 09:00:14,452 - INFO - 店铺 ********* 的userid值为空
2025-07-18 09:00:14,452 - INFO - 店铺 ********* 的userid值为空
2025-07-18 09:00:14,452 - INFO - 获取宜搭店铺信息成功，共 1276 条记录
2025-07-18 09:00:14,452 - INFO - 成功获取宜搭店铺信息，共 1276 条记录
2025-07-18 09:00:14,452 - INFO - 正在获取用户信息并处理oa_account...
2025-07-18 09:00:14,452 - INFO - 获取用户信息成功，共 72 条记录
2025-07-18 09:00:14,749 - INFO - 需要查询的手机号数量: 40
2025-07-18 09:00:20,811 - INFO - 处理oa_account完成，缓存大小: 40
2025-07-18 09:00:20,811 - INFO - 数据库数据框列名: ['project_code', 'project_name', 'store_code', 'store_name', 'store_status', 'oa_account', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'ifduojing', 'userid', 'is_new_store']
2025-07-18 09:00:20,811 - INFO - 宜搭数据框列名: ['store_code', 'store_name', 'userid', 'status', 'is_duojing', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'is_new_store', 'form_instance_id']
2025-07-18 09:00:20,811 - INFO - 开始对比数据库和宜搭数据...
2025-07-18 09:00:20,811 - INFO - 数据库数据框列名: ['project_code', 'project_name', 'store_code', 'store_name', 'store_status', 'oa_account', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'ifduojing', 'userid', 'is_new_store']
2025-07-18 09:00:20,811 - INFO - 宜搭数据框列名: ['store_code', 'store_name', 'userid', 'status', 'is_duojing', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'is_new_store', 'form_instance_id']
2025-07-18 09:00:20,811 - INFO - 仅在数据库存在的记录数: 0
2025-07-18 09:00:20,811 - INFO - 仅在宜搭存在的记录数: 0
2025-07-18 09:00:20,842 - INFO - 店铺 ********* 存在字段差异: ['fz_store_code_diff']
2025-07-18 09:00:20,889 - INFO - 检测到店铺名称变更 - 店铺编码: *********, 原名称: 编辅侠, 新名称: 童创
2025-07-18 09:00:20,889 - INFO - 店铺 ********* 存在字段差异: ['store_name_diff', 'fz_store_code_diff']
2025-07-18 09:00:20,952 - INFO - 店铺 ********* 存在字段差异: ['status_diff']
2025-07-18 09:00:20,967 - INFO - 店铺 ********* 存在字段差异: ['status_diff']
2025-07-18 09:00:21,030 - INFO - 店铺 100099764 存在字段差异: ['userid_diff']
2025-07-18 09:00:21,030 - INFO - 店铺 100099764 userid差异 - 数据库: {'1821a225bb6138fac03322a4fde8424d'}, 宜搭: {'16d2416f00a04889068d42e44b182785'}
2025-07-18 09:00:21,030 - INFO - 店铺 100099764 - 仅在数据库存在的userid: {'1821a225bb6138fac03322a4fde8424d'}
2025-07-18 09:00:21,030 - INFO - 店铺 100099764 - 仅在宜搭存在的userid: {'16d2416f00a04889068d42e44b182785'}
2025-07-18 09:00:21,030 - INFO - 店铺 100100968 存在字段差异: ['userid_diff']
2025-07-18 09:00:21,030 - INFO - 店铺 100100968 userid差异 - 数据库: {'1821a225bb6138fac03322a4fde8424d'}, 宜搭: {'192ae1b7a30d7d7319ab66d444aa66f7'}
2025-07-18 09:00:21,030 - INFO - 店铺 100100968 - 仅在数据库存在的userid: {'1821a225bb6138fac03322a4fde8424d'}
2025-07-18 09:00:21,030 - INFO - 店铺 100100968 - 仅在宜搭存在的userid: {'192ae1b7a30d7d7319ab66d444aa66f7'}
2025-07-18 09:00:21,045 - INFO - 店铺 100099766 存在字段差异: ['userid_diff']
2025-07-18 09:00:21,045 - INFO - 店铺 100099766 userid差异 - 数据库: {'192ae1b7a30d7d7319ab66d444aa66f7'}, 宜搭: {'1821a225bb6138fac03322a4fde8424d'}
2025-07-18 09:00:21,045 - INFO - 店铺 100099766 - 仅在数据库存在的userid: {'192ae1b7a30d7d7319ab66d444aa66f7'}
2025-07-18 09:00:21,045 - INFO - 店铺 100099766 - 仅在宜搭存在的userid: {'1821a225bb6138fac03322a4fde8424d'}
2025-07-18 09:00:21,045 - INFO - 店铺 100099746 存在字段差异: ['userid_diff']
2025-07-18 09:00:21,045 - INFO - 店铺 100099746 userid差异 - 数据库: {'192ae1b7a30d7d7319ab66d444aa66f7'}, 宜搭: {'1821a225bb6138fac03322a4fde8424d'}
2025-07-18 09:00:21,045 - INFO - 店铺 100099746 - 仅在数据库存在的userid: {'192ae1b7a30d7d7319ab66d444aa66f7'}
2025-07-18 09:00:21,045 - INFO - 店铺 100099746 - 仅在宜搭存在的userid: {'1821a225bb6138fac03322a4fde8424d'}
2025-07-18 09:00:21,045 - INFO - 店铺 100099738 存在字段差异: ['userid_diff']
2025-07-18 09:00:21,045 - INFO - 店铺 100099738 userid差异 - 数据库: {'16d2416c64282711fb71a184c10997bb'}, 宜搭: {'192ae1b7a30d7d7319ab66d444aa66f7'}
2025-07-18 09:00:21,045 - INFO - 店铺 100099738 - 仅在数据库存在的userid: {'16d2416c64282711fb71a184c10997bb'}
2025-07-18 09:00:21,045 - INFO - 店铺 100099738 - 仅在宜搭存在的userid: {'192ae1b7a30d7d7319ab66d444aa66f7'}
2025-07-18 09:00:21,045 - INFO - 店铺 100101172 存在字段差异: ['userid_diff']
2025-07-18 09:00:21,045 - INFO - 店铺 100101172 userid差异 - 数据库: {'1821a225bb6138fac03322a4fde8424d'}, 宜搭: {'192ae1b7a30d7d7319ab66d444aa66f7'}
2025-07-18 09:00:21,045 - INFO - 店铺 100101172 - 仅在数据库存在的userid: {'1821a225bb6138fac03322a4fde8424d'}
2025-07-18 09:00:21,045 - INFO - 店铺 100101172 - 仅在宜搭存在的userid: {'192ae1b7a30d7d7319ab66d444aa66f7'}
2025-07-18 09:00:21,045 - INFO - 店铺 100100351 存在字段差异: ['userid_diff']
2025-07-18 09:00:21,045 - INFO - 店铺 100100351 userid差异 - 数据库: {'1821a225bb6138fac03322a4fde8424d'}, 宜搭: {'192ae1b7a30d7d7319ab66d444aa66f7'}
2025-07-18 09:00:21,045 - INFO - 店铺 100100351 - 仅在数据库存在的userid: {'1821a225bb6138fac03322a4fde8424d'}
2025-07-18 09:00:21,045 - INFO - 店铺 100100351 - 仅在宜搭存在的userid: {'192ae1b7a30d7d7319ab66d444aa66f7'}
2025-07-18 09:00:21,045 - INFO - 店铺 100101233 存在字段差异: ['userid_diff']
2025-07-18 09:00:21,045 - INFO - 店铺 100101233 userid差异 - 数据库: {'16d2416f00a04889068d42e44b182785'}, 宜搭: {'192ae1b7a30d7d7319ab66d444aa66f7'}
2025-07-18 09:00:21,045 - INFO - 店铺 100101233 - 仅在数据库存在的userid: {'16d2416f00a04889068d42e44b182785'}
2025-07-18 09:00:21,045 - INFO - 店铺 100101233 - 仅在宜搭存在的userid: {'192ae1b7a30d7d7319ab66d444aa66f7'}
2025-07-18 09:00:21,045 - INFO - 检测到店铺名称变更 - 店铺编码: 100101278, 原名称: 一年四季, 新名称: 一年四季24小时智能健身
2025-07-18 09:00:21,045 - INFO - 店铺 100101278 存在字段差异: ['store_name_diff']
2025-07-18 09:00:21,045 - INFO - 店铺 100101291 存在字段差异: ['userid_diff']
2025-07-18 09:00:21,045 - INFO - 店铺 100101291 userid差异 - 数据库: {'16d2416c64282711fb71a184c10997bb'}, 宜搭: {'16d2416f00a04889068d42e44b182785'}
2025-07-18 09:00:21,045 - INFO - 店铺 100101291 - 仅在数据库存在的userid: {'16d2416c64282711fb71a184c10997bb'}
2025-07-18 09:00:21,045 - INFO - 店铺 100101291 - 仅在宜搭存在的userid: {'16d2416f00a04889068d42e44b182785'}
2025-07-18 09:00:21,108 - INFO - 店铺 100099610 存在字段差异: ['fz_store_code_diff']
2025-07-18 09:00:21,123 - INFO - 店铺 100099652 存在字段差异: ['fz_store_code_diff']
2025-07-18 09:00:21,139 - INFO - 数据对比完成：
2025-07-18 09:00:21,139 - INFO - - 需要插入的记录数: 0
2025-07-18 09:00:21,139 - INFO - - 需要更新状态为禁用的记录数: 0
2025-07-18 09:00:21,139 - INFO - - 需要更新的记录数: 16
2025-07-18 09:00:21,139 - INFO - - 店铺名称变更数: 2
2025-07-18 09:00:21,139 - INFO - 开始处理店铺名称变更 - 店铺编码: *********, 原名称: 编辅侠, 新名称: 童创
2025-07-18 09:00:21,139 - INFO - 查询第一个宜搭表单 - 店铺编码: *********
2025-07-18 09:00:21,139 - INFO - Request Parameters - Page 1:
2025-07-18 09:00:21,139 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-18 09:00:21,139 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "textField_m9nw1k6y", "value": "*********", "type": "TEXT", "operator": "eq", "componentName": "TextField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-18 09:00:29,248 - ERROR - 更新第一个宜搭表单失败 - 店铺编码: *********, 错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: BE6C2E42-25F0-7825-8909-489142878D5D Response: {'code': 'ServiceUnavailable', 'requestid': 'BE6C2E42-25F0-7825-8909-489142878D5D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: BE6C2E42-25F0-7825-8909-489142878D5D)
2025-07-18 09:00:29,248 - INFO - 查询第二个宜搭表单 - 店铺编码: *********
2025-07-18 09:00:29,248 - INFO - Request Parameters - Page 1:
2025-07-18 09:00:29,248 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-18 09:00:29,248 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "textField_m9tojheq", "value": "*********", "type": "TEXT", "operator": "eq", "componentName": "TextField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-18 09:00:29,545 - INFO - Response - Page 1:
2025-07-18 09:00:29,545 - INFO - 第 1 页获取到 7 条记录
2025-07-18 09:00:29,748 - INFO - 查询完成，共获取到 7 条记录
2025-07-18 09:00:29,748 - INFO - 更新第二个宜搭表单 - 店铺编码: *********, 表单ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMT1
2025-07-18 09:00:30,123 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMT1
2025-07-18 09:00:30,123 - INFO - 更新第二个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:00:30,123 - INFO - 更新第二个宜搭表单 - 店铺编码: *********, 表单ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFR
2025-07-18 09:00:30,592 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFR
2025-07-18 09:00:30,592 - INFO - 更新第二个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:00:30,592 - INFO - 更新第二个宜搭表单 - 店铺编码: *********, 表单ID: FINST-RTA66X61LO5W721S827476KZ4D8Z1GU1NTOBMH1
2025-07-18 09:00:31,077 - INFO - 更新表单数据成功: FINST-RTA66X61LO5W721S827476KZ4D8Z1GU1NTOBMH1
2025-07-18 09:00:31,077 - INFO - 更新第二个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:00:31,077 - INFO - 更新第二个宜搭表单 - 店铺编码: *********, 表单ID: FINST-UW9663710B2WNGC9E4D5WB0LG6R53BPEMTOBMQW
2025-07-18 09:00:31,608 - INFO - 更新表单数据成功: FINST-UW9663710B2WNGC9E4D5WB0LG6R53BPEMTOBMQW
2025-07-18 09:00:31,608 - INFO - 更新第二个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:00:31,608 - INFO - 更新第二个宜搭表单 - 店铺编码: *********, 表单ID: FINST-XO8662C1BA2WG329B611O99D7MRW2HR1MTOBMT3
2025-07-18 09:00:32,092 - INFO - 更新表单数据成功: FINST-XO8662C1BA2WG329B611O99D7MRW2HR1MTOBMT3
2025-07-18 09:00:32,092 - INFO - 更新第二个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:00:32,092 - INFO - 更新第二个宜搭表单 - 店铺编码: *********, 表单ID: FINST-KLF66HD1FS2WOYM69AZ3N5N0EL2U3I3HLTOBMIY
2025-07-18 09:00:32,561 - INFO - 更新表单数据成功: FINST-KLF66HD1FS2WOYM69AZ3N5N0EL2U3I3HLTOBMIY
2025-07-18 09:00:32,561 - INFO - 更新第二个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:00:32,561 - INFO - 更新第二个宜搭表单 - 店铺编码: *********, 表单ID: FINST-MUC66Q81KO5WE2W2B212K7ONSWPQ2LLOKTOBMQ1
2025-07-18 09:00:33,045 - INFO - 更新表单数据成功: FINST-MUC66Q81KO5WE2W2B212K7ONSWPQ2LLOKTOBMQ1
2025-07-18 09:00:33,045 - INFO - 更新第二个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:00:33,045 - INFO - 店铺名称变更处理完成 - 店铺编码: *********
2025-07-18 09:00:33,045 - INFO - 开始处理店铺名称变更 - 店铺编码: 100101278, 原名称: 一年四季, 新名称: 一年四季24小时智能健身
2025-07-18 09:00:33,045 - INFO - 查询第一个宜搭表单 - 店铺编码: 100101278
2025-07-18 09:00:33,045 - INFO - Request Parameters - Page 1:
2025-07-18 09:00:33,045 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-18 09:00:33,045 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "textField_m9nw1k6y", "value": "100101278", "type": "TEXT", "operator": "eq", "componentName": "TextField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-18 09:00:40,295 - INFO - Response - Page 1:
2025-07-18 09:00:40,295 - INFO - 第 1 页获取到 13 条记录
2025-07-18 09:00:40,498 - INFO - 查询完成，共获取到 13 条记录
2025-07-18 09:00:40,498 - INFO - 更新第一个宜搭表单 - 店铺编码: 100101278, 表单ID: FINST-CPC66T91LZ5X6DKC9CWJSDPDIQ2U2APFG26DM67
2025-07-18 09:00:41,264 - INFO - 更新表单数据成功: FINST-CPC66T91LZ5X6DKC9CWJSDPDIQ2U2APFG26DM67
2025-07-18 09:00:41,264 - INFO - 更新第一个宜搭表单成功 - 店铺编码: 100101278
2025-07-18 09:00:41,264 - INFO - 更新第一个宜搭表单 - 店铺编码: 100101278, 表单ID: FINST-2PF66KD1E06X9I3AC8R65AHK2T0Z2CCF2N4DMJ2
2025-07-18 09:00:41,780 - INFO - 更新表单数据成功: FINST-2PF66KD1E06X9I3AC8R65AHK2T0Z2CCF2N4DMJ2
2025-07-18 09:00:41,780 - INFO - 更新第一个宜搭表单成功 - 店铺编码: 100101278
2025-07-18 09:00:41,780 - INFO - 更新第一个宜搭表单 - 店铺编码: 100101278, 表单ID: FINST-3PF66X61ZG5XM9HAC8LX9C0S06J03YBUP34DM2F
2025-07-18 09:00:42,358 - INFO - 更新表单数据成功: FINST-3PF66X61ZG5XM9HAC8LX9C0S06J03YBUP34DM2F
2025-07-18 09:00:42,358 - INFO - 更新第一个宜搭表单成功 - 店铺编码: 100101278
2025-07-18 09:00:42,358 - INFO - 更新第一个宜搭表单 - 店铺编码: 100101278, 表单ID: FINST-MLF66JA1FZ4XU622CF8ZMC1RY9IZ2DJSVH2DMX
2025-07-18 09:00:42,936 - INFO - 更新表单数据成功: FINST-MLF66JA1FZ4XU622CF8ZMC1RY9IZ2DJSVH2DMX
2025-07-18 09:00:42,936 - INFO - 更新第一个宜搭表单成功 - 店铺编码: 100101278
2025-07-18 09:00:42,936 - INFO - 更新第一个宜搭表单 - 店铺编码: 100101278, 表单ID: FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMSA1
2025-07-18 09:00:43,514 - INFO - 更新表单数据成功: FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMSA1
2025-07-18 09:00:43,514 - INFO - 更新第一个宜搭表单成功 - 店铺编码: 100101278
2025-07-18 09:00:43,514 - INFO - 更新第一个宜搭表单 - 店铺编码: 100101278, 表单ID: FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMO8
2025-07-18 09:00:44,139 - INFO - 更新表单数据成功: FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMO8
2025-07-18 09:00:44,139 - INFO - 更新第一个宜搭表单成功 - 店铺编码: 100101278
2025-07-18 09:00:44,139 - INFO - 更新第一个宜搭表单 - 店铺编码: 100101278, 表单ID: FINST-XL866HB1P61X9Z25AS2PQ5WBPZ7W173ZSHXCMX4
2025-07-18 09:00:44,623 - INFO - 更新表单数据成功: FINST-XL866HB1P61X9Z25AS2PQ5WBPZ7W173ZSHXCMX4
2025-07-18 09:00:44,623 - INFO - 更新第一个宜搭表单成功 - 店铺编码: 100101278
2025-07-18 09:00:44,623 - INFO - 更新第一个宜搭表单 - 店铺编码: 100101278, 表单ID: FINST-SI766181080XWYANFCPHB457LNZL3UKLFLWCMWJ
2025-07-18 09:00:45,139 - INFO - 更新表单数据成功: FINST-SI766181080XWYANFCPHB457LNZL3UKLFLWCMWJ
2025-07-18 09:00:45,139 - INFO - 更新第一个宜搭表单成功 - 店铺编码: 100101278
2025-07-18 09:00:45,139 - INFO - 更新第一个宜搭表单 - 店铺编码: 100101278, 表单ID: FINST-DIC66I917AZWOUXN7LEDKCNJN33T2YO5KZUCMR7
2025-07-18 09:00:45,858 - INFO - 更新表单数据成功: FINST-DIC66I917AZWOUXN7LEDKCNJN33T2YO5KZUCMR7
2025-07-18 09:00:45,858 - INFO - 更新第一个宜搭表单成功 - 店铺编码: 100101278
2025-07-18 09:00:45,858 - INFO - 更新第一个宜搭表单 - 店铺编码: 100101278, 表单ID: FINST-FIG66R81AWYWRBEQBOYNG9GJ3WXG2SFPEXTCMB1
2025-07-18 09:00:46,389 - INFO - 更新表单数据成功: FINST-FIG66R81AWYWRBEQBOYNG9GJ3WXG2SFPEXTCMB1
2025-07-18 09:00:46,389 - INFO - 更新第一个宜搭表单成功 - 店铺编码: 100101278
2025-07-18 09:00:46,389 - INFO - 更新第一个宜搭表单 - 店铺编码: 100101278, 表单ID: FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMAH
2025-07-18 09:00:47,108 - INFO - 更新表单数据成功: FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMAH
2025-07-18 09:00:47,108 - INFO - 更新第一个宜搭表单成功 - 店铺编码: 100101278
2025-07-18 09:00:47,108 - INFO - 更新第一个宜搭表单 - 店铺编码: 100101278, 表单ID: FINST-MQA6627186VWCUKEARUSJ9RJRP0Z2KNPVRRCMYG
2025-07-18 09:00:47,686 - INFO - 更新表单数据成功: FINST-MQA6627186VWCUKEARUSJ9RJRP0Z2KNPVRRCMYG
2025-07-18 09:00:47,686 - INFO - 更新第一个宜搭表单成功 - 店铺编码: 100101278
2025-07-18 09:00:47,686 - INFO - 更新第一个宜搭表单 - 店铺编码: 100101278, 表单ID: FINST-HXD667B1J1UWID3QBR8DSAKFPMRA39A62BNCMP1
2025-07-18 09:00:48,217 - INFO - 更新表单数据成功: FINST-HXD667B1J1UWID3QBR8DSAKFPMRA39A62BNCMP1
2025-07-18 09:00:48,217 - INFO - 更新第一个宜搭表单成功 - 店铺编码: 100101278
2025-07-18 09:00:48,217 - INFO - 查询第二个宜搭表单 - 店铺编码: 100101278
2025-07-18 09:00:48,217 - INFO - Request Parameters - Page 1:
2025-07-18 09:00:48,217 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-18 09:00:48,217 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "textField_m9tojheq", "value": "100101278", "type": "TEXT", "operator": "eq", "componentName": "TextField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-18 09:00:48,451 - INFO - Response - Page 1:
2025-07-18 09:00:48,451 - INFO - 第 1 页获取到 1 条记录
2025-07-18 09:00:48,655 - INFO - 查询完成，共获取到 1 条记录
2025-07-18 09:00:48,655 - INFO - 更新第二个宜搭表单 - 店铺编码: 100101278, 表单ID: FINST-OLC66Z61N4TWEFD1CPN41AB37LED2LRT8ENCMBC
2025-07-18 09:00:49,155 - INFO - 更新表单数据成功: FINST-OLC66Z61N4TWEFD1CPN41AB37LED2LRT8ENCMBC
2025-07-18 09:00:49,155 - INFO - 更新第二个宜搭表单成功 - 店铺编码: 100101278
2025-07-18 09:00:49,155 - INFO - 店铺名称变更处理完成 - 店铺编码: 100101278
2025-07-18 09:00:50,326 - INFO - 邮件发送成功
2025-07-18 09:00:50,326 - INFO - 店铺名称更新邮件发送成功，共 2 条记录
2025-07-18 09:00:50,326 - INFO - 生成差异报告...
2025-07-18 09:00:50,623 - INFO - 差异报告已保存到文件: data/sync_store/store_info_diff_report_20250718.xlsx
2025-07-18 09:00:50,623 - INFO - 开始更新宜搭表单...
2025-07-18 09:00:50,623 - INFO - 开始更新宜搭表单数据...
2025-07-18 09:00:50,623 - INFO - 数据库记录数: 1276
2025-07-18 09:00:50,623 - INFO - 宜搭记录数: 1276
2025-07-18 09:00:50,623 - INFO - 数据库数据框列名: ['project_code', 'project_name', 'store_code', 'store_name', 'store_status', 'oa_account', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'ifduojing', 'userid', 'is_new_store']
2025-07-18 09:00:50,623 - INFO - 宜搭数据框列名: ['store_code', 'store_name', 'userid', 'status', 'is_duojing', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'is_new_store', 'form_instance_id']
2025-07-18 09:00:50,623 - INFO - 仅在数据库存在的记录数: 0
2025-07-18 09:00:50,623 - INFO - 仅在宜搭存在的记录数: 0
2025-07-18 09:00:50,639 - INFO - 店铺 ********* 存在字段差异: ['fz_store_code_diff']
2025-07-18 09:00:50,686 - INFO - 检测到店铺名称变更 - 店铺编码: *********, 原名称: 编辅侠, 新名称: 童创
2025-07-18 09:00:50,686 - INFO - 店铺 ********* 存在字段差异: ['store_name_diff', 'fz_store_code_diff']
2025-07-18 09:00:50,733 - INFO - 店铺 ********* 存在字段差异: ['status_diff']
2025-07-18 09:00:50,748 - INFO - 店铺 ********* 存在字段差异: ['status_diff']
2025-07-18 09:00:50,811 - INFO - 店铺 100099764 存在字段差异: ['userid_diff']
2025-07-18 09:00:50,811 - INFO - 店铺 100099764 userid差异 - 数据库: {'1821a225bb6138fac03322a4fde8424d'}, 宜搭: {'16d2416f00a04889068d42e44b182785'}
2025-07-18 09:00:50,811 - INFO - 店铺 100099764 - 仅在数据库存在的userid: {'1821a225bb6138fac03322a4fde8424d'}
2025-07-18 09:00:50,811 - INFO - 店铺 100099764 - 仅在宜搭存在的userid: {'16d2416f00a04889068d42e44b182785'}
2025-07-18 09:00:50,811 - INFO - 店铺 100100968 存在字段差异: ['userid_diff']
2025-07-18 09:00:50,811 - INFO - 店铺 100100968 userid差异 - 数据库: {'1821a225bb6138fac03322a4fde8424d'}, 宜搭: {'192ae1b7a30d7d7319ab66d444aa66f7'}
2025-07-18 09:00:50,811 - INFO - 店铺 100100968 - 仅在数据库存在的userid: {'1821a225bb6138fac03322a4fde8424d'}
2025-07-18 09:00:50,811 - INFO - 店铺 100100968 - 仅在宜搭存在的userid: {'192ae1b7a30d7d7319ab66d444aa66f7'}
2025-07-18 09:00:50,811 - INFO - 店铺 100099766 存在字段差异: ['userid_diff']
2025-07-18 09:00:50,811 - INFO - 店铺 100099766 userid差异 - 数据库: {'192ae1b7a30d7d7319ab66d444aa66f7'}, 宜搭: {'1821a225bb6138fac03322a4fde8424d'}
2025-07-18 09:00:50,811 - INFO - 店铺 100099766 - 仅在数据库存在的userid: {'192ae1b7a30d7d7319ab66d444aa66f7'}
2025-07-18 09:00:50,811 - INFO - 店铺 100099766 - 仅在宜搭存在的userid: {'1821a225bb6138fac03322a4fde8424d'}
2025-07-18 09:00:50,811 - INFO - 店铺 100099746 存在字段差异: ['userid_diff']
2025-07-18 09:00:50,811 - INFO - 店铺 100099746 userid差异 - 数据库: {'192ae1b7a30d7d7319ab66d444aa66f7'}, 宜搭: {'1821a225bb6138fac03322a4fde8424d'}
2025-07-18 09:00:50,811 - INFO - 店铺 100099746 - 仅在数据库存在的userid: {'192ae1b7a30d7d7319ab66d444aa66f7'}
2025-07-18 09:00:50,811 - INFO - 店铺 100099746 - 仅在宜搭存在的userid: {'1821a225bb6138fac03322a4fde8424d'}
2025-07-18 09:00:50,811 - INFO - 店铺 100099738 存在字段差异: ['userid_diff']
2025-07-18 09:00:50,811 - INFO - 店铺 100099738 userid差异 - 数据库: {'16d2416c64282711fb71a184c10997bb'}, 宜搭: {'192ae1b7a30d7d7319ab66d444aa66f7'}
2025-07-18 09:00:50,811 - INFO - 店铺 100099738 - 仅在数据库存在的userid: {'16d2416c64282711fb71a184c10997bb'}
2025-07-18 09:00:50,811 - INFO - 店铺 100099738 - 仅在宜搭存在的userid: {'192ae1b7a30d7d7319ab66d444aa66f7'}
2025-07-18 09:00:50,811 - INFO - 店铺 100101172 存在字段差异: ['userid_diff']
2025-07-18 09:00:50,811 - INFO - 店铺 100101172 userid差异 - 数据库: {'1821a225bb6138fac03322a4fde8424d'}, 宜搭: {'192ae1b7a30d7d7319ab66d444aa66f7'}
2025-07-18 09:00:50,811 - INFO - 店铺 100101172 - 仅在数据库存在的userid: {'1821a225bb6138fac03322a4fde8424d'}
2025-07-18 09:00:50,811 - INFO - 店铺 100101172 - 仅在宜搭存在的userid: {'192ae1b7a30d7d7319ab66d444aa66f7'}
2025-07-18 09:00:50,826 - INFO - 店铺 100100351 存在字段差异: ['userid_diff']
2025-07-18 09:00:50,826 - INFO - 店铺 100100351 userid差异 - 数据库: {'1821a225bb6138fac03322a4fde8424d'}, 宜搭: {'192ae1b7a30d7d7319ab66d444aa66f7'}
2025-07-18 09:00:50,826 - INFO - 店铺 100100351 - 仅在数据库存在的userid: {'1821a225bb6138fac03322a4fde8424d'}
2025-07-18 09:00:50,826 - INFO - 店铺 100100351 - 仅在宜搭存在的userid: {'192ae1b7a30d7d7319ab66d444aa66f7'}
2025-07-18 09:00:50,826 - INFO - 店铺 100101233 存在字段差异: ['userid_diff']
2025-07-18 09:00:50,826 - INFO - 店铺 100101233 userid差异 - 数据库: {'16d2416f00a04889068d42e44b182785'}, 宜搭: {'192ae1b7a30d7d7319ab66d444aa66f7'}
2025-07-18 09:00:50,826 - INFO - 店铺 100101233 - 仅在数据库存在的userid: {'16d2416f00a04889068d42e44b182785'}
2025-07-18 09:00:50,826 - INFO - 店铺 100101233 - 仅在宜搭存在的userid: {'192ae1b7a30d7d7319ab66d444aa66f7'}
2025-07-18 09:00:50,826 - INFO - 检测到店铺名称变更 - 店铺编码: 100101278, 原名称: 一年四季, 新名称: 一年四季24小时智能健身
2025-07-18 09:00:50,826 - INFO - 店铺 100101278 存在字段差异: ['store_name_diff']
2025-07-18 09:00:50,826 - INFO - 店铺 100101291 存在字段差异: ['userid_diff']
2025-07-18 09:00:50,826 - INFO - 店铺 100101291 userid差异 - 数据库: {'16d2416c64282711fb71a184c10997bb'}, 宜搭: {'16d2416f00a04889068d42e44b182785'}
2025-07-18 09:00:50,826 - INFO - 店铺 100101291 - 仅在数据库存在的userid: {'16d2416c64282711fb71a184c10997bb'}
2025-07-18 09:00:50,826 - INFO - 店铺 100101291 - 仅在宜搭存在的userid: {'16d2416f00a04889068d42e44b182785'}
2025-07-18 09:00:50,889 - INFO - 店铺 100099610 存在字段差异: ['fz_store_code_diff']
2025-07-18 09:00:50,889 - INFO - 店铺 100099652 存在字段差异: ['fz_store_code_diff']
2025-07-18 09:00:50,905 - INFO - 数据对比完成：
2025-07-18 09:00:50,905 - INFO - - 需要插入的记录数: 0
2025-07-18 09:00:50,905 - INFO - - 需要更新状态为禁用的记录数: 0
2025-07-18 09:00:50,905 - INFO - - 需要更新的记录数: 16
2025-07-18 09:00:50,905 - INFO - - 店铺名称变更数: 2
2025-07-18 09:00:50,905 - INFO - 开始处理店铺名称变更 - 店铺编码: *********, 原名称: 编辅侠, 新名称: 童创
2025-07-18 09:00:50,905 - INFO - 查询第一个宜搭表单 - 店铺编码: *********
2025-07-18 09:00:50,905 - INFO - Request Parameters - Page 1:
2025-07-18 09:00:50,905 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-18 09:00:50,905 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "textField_m9nw1k6y", "value": "*********", "type": "TEXT", "operator": "eq", "componentName": "TextField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-18 09:00:51,733 - INFO - Response - Page 1:
2025-07-18 09:00:51,733 - INFO - 第 1 页获取到 100 条记录
2025-07-18 09:00:51,936 - INFO - Request Parameters - Page 2:
2025-07-18 09:00:51,936 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-18 09:00:51,936 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "textField_m9nw1k6y", "value": "*********", "type": "TEXT", "operator": "eq", "componentName": "TextField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-18 09:00:52,639 - INFO - Response - Page 2:
2025-07-18 09:00:52,639 - INFO - 第 2 页获取到 51 条记录
2025-07-18 09:00:52,842 - INFO - 查询完成，共获取到 151 条记录
2025-07-18 09:00:52,842 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-33666PD1606XSTPQ9HLXL69HJHP23H5F3S6DM37
2025-07-18 09:00:53,389 - INFO - 更新表单数据成功: FINST-33666PD1606XSTPQ9HLXL69HJHP23H5F3S6DM37
2025-07-18 09:00:53,389 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:00:53,389 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-HXD667B16C6XMOI5B8CC99OJ0E9Q24NPRC5DMZ
2025-07-18 09:00:53,967 - INFO - 更新表单数据成功: FINST-HXD667B16C6XMOI5B8CC99OJ0E9Q24NPRC5DMZ
2025-07-18 09:00:53,967 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:00:53,967 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-N3G66S81HG5XCJAU6XXPP8SUQQUB2SYDEX3DM61
2025-07-18 09:00:54,451 - INFO - 更新表单数据成功: FINST-N3G66S81HG5XCJAU6XXPP8SUQQUB2SYDEX3DM61
2025-07-18 09:00:54,451 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:00:54,451 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-SI766181I9ZWZN5B9KZY2CDHK05W2I0OXH2DMOX2
2025-07-18 09:00:54,998 - INFO - 更新表单数据成功: FINST-SI766181I9ZWZN5B9KZY2CDHK05W2I0OXH2DMOX2
2025-07-18 09:00:54,998 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:00:54,998 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMT81
2025-07-18 09:00:55,483 - INFO - 更新表单数据成功: FINST-3RE66ZB1O61XZMGBB05G67QKM45E3ZNB721DMT81
2025-07-18 09:00:55,483 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:00:55,483 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMR71
2025-07-18 09:00:56,014 - INFO - 更新表单数据成功: FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMR71
2025-07-18 09:00:56,014 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:00:56,014 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-FQD66YB1GDYWLRUQF4JFU8H8NVO83ZMK6SWCMK21
2025-07-18 09:00:56,655 - INFO - 更新表单数据成功: FINST-FQD66YB1GDYWLRUQF4JFU8H8NVO83ZMK6SWCMK21
2025-07-18 09:00:56,655 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:00:56,655 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-I3D66ED105YWONCN8VZPHB5U6N8Q3W6OYCVCM1P
2025-07-18 09:00:57,248 - INFO - 更新表单数据成功: FINST-I3D66ED105YWONCN8VZPHB5U6N8Q3W6OYCVCM1P
2025-07-18 09:00:57,248 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:00:57,248 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-1PF66VA1TUYWZ4JRD0BFU7C0U9ND3Z6XGXTCMR
2025-07-18 09:00:57,764 - INFO - 更新表单数据成功: FINST-1PF66VA1TUYWZ4JRD0BFU7C0U9ND3Z6XGXTCMR
2025-07-18 09:00:57,764 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:00:57,764 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-737662B1S1VWZMZHBNQ0FDLNJWXL2EPNFISCM3K
2025-07-18 09:00:58,326 - INFO - 更新表单数据成功: FINST-737662B1S1VWZMZHBNQ0FDLNJWXL2EPNFISCM3K
2025-07-18 09:00:58,326 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:00:58,326 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCM01
2025-07-18 09:00:58,873 - INFO - 更新表单数据成功: FINST-UNG66081ETRW848ODR2AK5AQSIA02MVPD3KCM01
2025-07-18 09:00:58,873 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:00:58,873 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM15
2025-07-18 09:00:59,389 - INFO - 更新表单数据成功: FINST-E3G66QA1XQPWG9KH81Q6YCN8EY2Q2C7HHHICM15
2025-07-18 09:00:59,389 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:00:59,389 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMGA1
2025-07-18 09:00:59,858 - INFO - 更新表单数据成功: FINST-W3B66L71W8OW2WDHBV3RD4OTD7SB37B812HCMGA1
2025-07-18 09:00:59,858 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:00:59,858 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECM9
2025-07-18 09:01:00,358 - INFO - 更新表单数据成功: FINST-KLF66HD1L8OWP61D7E6AM98BU4C6316BUWECM9
2025-07-18 09:01:00,358 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:00,358 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECM31
2025-07-18 09:01:01,108 - INFO - 更新表单数据成功: FINST-7PF66N91PQNW8ZSD7C0JJ4ZNVQRV2IWY57ECM31
2025-07-18 09:01:01,108 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:01,108 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMJ1
2025-07-18 09:01:01,701 - INFO - 更新表单数据成功: FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMJ1
2025-07-18 09:01:01,701 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:01,701 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM19
2025-07-18 09:01:02,264 - INFO - 更新表单数据成功: FINST-8SG66JA157LWW4CEFDGM47NKZVEW2MOM9CBCM19
2025-07-18 09:01:02,264 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:02,264 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMH51
2025-07-18 09:01:02,842 - INFO - 更新表单数据成功: FINST-YVA662B1IZHW6MIU6T3MG7NUNLE429DPTW9CMH51
2025-07-18 09:01:02,842 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:02,842 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMIE
2025-07-18 09:01:03,467 - INFO - 更新表单数据成功: FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMIE
2025-07-18 09:01:03,467 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:03,467 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMN6
2025-07-18 09:01:04,029 - INFO - 更新表单数据成功: FINST-NS766991MZHWSQ3H9XSOZACJPHL22J51Y17CMN6
2025-07-18 09:01:04,029 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:04,029 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMUE
2025-07-18 09:01:04,701 - INFO - 更新表单数据成功: FINST-3PF66X61GWGWB3WLBR3U5AC24YAP3VO7IM5CMUE
2025-07-18 09:01:04,701 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:04,701 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMK1
2025-07-18 09:01:05,233 - INFO - 更新表单数据成功: FINST-DIC66I910VFW9QJS9IRQ7AHAD9HJ309N274CMK1
2025-07-18 09:01:05,233 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:05,233 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-XO8662C1RWEW9H1X9UGWE7GQHIC63YUBOR2CMD1
2025-07-18 09:01:05,826 - INFO - 更新表单数据成功: FINST-XO8662C1RWEW9H1X9UGWE7GQHIC63YUBOR2CMD1
2025-07-18 09:01:05,826 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:05,826 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CML
2025-07-18 09:01:06,451 - INFO - 更新表单数据成功: FINST-EEC66XC16JEWO2ZAA92DRCJBT7872IK57C1CML
2025-07-18 09:01:06,451 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:06,451 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-X3E66X815SCWVJC69HGNP9QNXTYE36UNQWZBMR1
2025-07-18 09:01:06,983 - INFO - 更新表单数据成功: FINST-X3E66X815SCWVJC69HGNP9QNXTYE36UNQWZBMR1
2025-07-18 09:01:06,983 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:06,983 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-OLC66Z611P8W074CE97BT6KL0HG137P8T1XBM5L
2025-07-18 09:01:07,498 - INFO - 更新表单数据成功: FINST-OLC66Z611P8W074CE97BT6KL0HG137P8T1XBM5L
2025-07-18 09:01:07,498 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:07,498 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-OLC66Z61WC6WXBK26SC50C715SXU2G83VWUBM3T
2025-07-18 09:01:08,108 - INFO - 更新表单数据成功: FINST-OLC66Z61WC6WXBK26SC50C715SXU2G83VWUBM3T
2025-07-18 09:01:08,108 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:08,108 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-1MD668B1FF9WJ1NN6P9IGC091TIC25W787UBM9
2025-07-18 09:01:08,592 - INFO - 更新表单数据成功: FINST-1MD668B1FF9WJ1NN6P9IGC091TIC25W787UBM9
2025-07-18 09:01:08,592 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:08,592 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-WBF66B81X82WGGVK79SRC581D4E83LE002SBM0L
2025-07-18 09:01:09,045 - INFO - 更新表单数据成功: FINST-WBF66B81X82WGGVK79SRC581D4E83LE002SBM0L
2025-07-18 09:01:09,045 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:09,045 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-PPA66671GO6WH4BU5QV3686T9NU82J0O3CRBMHB
2025-07-18 09:01:09,592 - INFO - 更新表单数据成功: FINST-PPA66671GO6WH4BU5QV3686T9NU82J0O3CRBMHB
2025-07-18 09:01:09,592 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:09,592 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-2PF66CD13P5WSDSAA5K80AHN38P12JUJ0XPBMZ
2025-07-18 09:01:10,186 - INFO - 更新表单数据成功: FINST-2PF66CD13P5WSDSAA5K80AHN38P12JUJ0XPBMZ
2025-07-18 09:01:10,186 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:10,186 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-2PF66KD1EO5WT7198F83C6OCUBNI2SVVFXOBMF2
2025-07-18 09:01:10,748 - INFO - 更新表单数据成功: FINST-2PF66KD1EO5WT7198F83C6OCUBNI2SVVFXOBMF2
2025-07-18 09:01:10,748 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:10,748 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-PPA66671MJ2WPXSZCUQUFC0DUOXA3TUQEXOBMO11
2025-07-18 09:01:11,326 - INFO - 更新表单数据成功: FINST-PPA66671MJ2WPXSZCUQUFC0DUOXA3TUQEXOBMO11
2025-07-18 09:01:11,326 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:11,326 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-LLF66O71YS2WWSQX94BL6AIJYWGG2ZL5DXOBM031
2025-07-18 09:01:11,904 - INFO - 更新表单数据成功: FINST-LLF66O71YS2WWSQX94BL6AIJYWGG2ZL5DXOBM031
2025-07-18 09:01:11,904 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:11,904 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-E3G66QA1A82WOHYZ9V5LN9MVPQU4257LBXOBM6A
2025-07-18 09:01:12,483 - INFO - 更新表单数据成功: FINST-E3G66QA1A82WOHYZ9V5LN9MVPQU4257LBXOBM6A
2025-07-18 09:01:12,483 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:12,483 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-XXF66LA1BD5WW5A1E0F4X6QTPCPN3S0CAXOBM95
2025-07-18 09:01:12,998 - INFO - 更新表单数据成功: FINST-XXF66LA1BD5WW5A1E0F4X6QTPCPN3S0CAXOBM95
2025-07-18 09:01:12,998 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:12,998 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-8PF66V71XM5WDMVLA3OKI8JCPQOU2RNE8XOBME1
2025-07-18 09:01:13,608 - INFO - 更新表单数据成功: FINST-8PF66V71XM5WDMVLA3OKI8JCPQOU2RNE8XOBME1
2025-07-18 09:01:13,608 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:13,608 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-3PF66E910Q5WJSVDDB6HA7BEGY903SVP7XOBME1
2025-07-18 09:01:14,139 - INFO - 更新表单数据成功: FINST-3PF66E910Q5WJSVDDB6HA7BEGY903SVP7XOBME1
2025-07-18 09:01:14,139 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:14,139 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-PGC66MB11Q5WQWCS7OKK6DY9J82B3CR46XOBMP
2025-07-18 09:01:14,701 - INFO - 更新表单数据成功: FINST-PGC66MB11Q5WQWCS7OKK6DY9J82B3CR46XOBMP
2025-07-18 09:01:14,701 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:14,701 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-T9D66B81P2VWRDXM8E68L9LI57193WKQKCQCM16
2025-07-18 09:01:15,233 - INFO - 更新表单数据成功: FINST-T9D66B81P2VWRDXM8E68L9LI57193WKQKCQCM16
2025-07-18 09:01:15,233 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:15,233 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-CL966QB19TUW5O3O7YBZW5CDAOG224D8MMPCMAG
2025-07-18 09:01:15,764 - INFO - 更新表单数据成功: FINST-CL966QB19TUW5O3O7YBZW5CDAOG224D8MMPCMAG
2025-07-18 09:01:15,764 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:15,764 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-H7966UC1UBUW5QKH71FQIAF7VST42UWR87OCMDI
2025-07-18 09:01:16,373 - INFO - 更新表单数据成功: FINST-H7966UC1UBUW5QKH71FQIAF7VST42UWR87OCMDI
2025-07-18 09:01:16,373 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:16,373 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMI1
2025-07-18 09:01:16,936 - INFO - 更新表单数据成功: FINST-F7D66UA1J2TW3N6RE3FKF91HW94T2H0CSRMCMI1
2025-07-18 09:01:16,936 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:16,936 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-2PF66TC1X3SWLXCZ6G8J96G5PYY33STXDCLCM61
2025-07-18 09:01:17,529 - INFO - 更新表单数据成功: FINST-2PF66TC1X3SWLXCZ6G8J96G5PYY33STXDCLCM61
2025-07-18 09:01:17,529 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:17,529 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-MLF662B1YI8WD3BV810JD50UAG9I3HVDV1XBMCR
2025-07-18 09:01:18,045 - INFO - 更新表单数据成功: FINST-MLF662B1YI8WD3BV810JD50UAG9I3HVDV1XBMCR
2025-07-18 09:01:18,045 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:18,045 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-2PF662C1VP5WZXGBEWW4V6I9KF0K3GPZ4XOBMP
2025-07-18 09:01:18,529 - INFO - 更新表单数据成功: FINST-2PF662C1VP5WZXGBEWW4V6I9KF0K3GPZ4XOBMP
2025-07-18 09:01:18,529 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:18,529 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-LR5668B1CN5W3PV3EDD3Y62KIL5N3FOM3XOBM71
2025-07-18 09:01:19,201 - INFO - 更新表单数据成功: FINST-LR5668B1CN5W3PV3EDD3Y62KIL5N3FOM3XOBM71
2025-07-18 09:01:19,201 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:19,201 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-AEF66BC1GP5WFO8L6A0ZMA6T226F3M6Z1XOBMA3
2025-07-18 09:01:19,873 - INFO - 更新表单数据成功: FINST-AEF66BC1GP5WFO8L6A0ZMA6T226F3M6Z1XOBMA3
2025-07-18 09:01:19,873 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:19,873 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-LLF66J71RQ2WXZOX7QALN8WLVQEM25Z10XOBMAY
2025-07-18 09:01:20,467 - INFO - 更新表单数据成功: FINST-LLF66J71RQ2WXZOX7QALN8WLVQEM25Z10XOBMAY
2025-07-18 09:01:20,467 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:20,467 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-LLF66FD1XD5WJOAQFD52087YGRJB2VTRYWOBM81
2025-07-18 09:01:20,920 - INFO - 更新表单数据成功: FINST-LLF66FD1XD5WJOAQFD52087YGRJB2VTRYWOBM81
2025-07-18 09:01:20,920 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:20,920 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-KLF66HD1ZP5W0LNBBZ0NO5NGE9FG38CKWWOBMO3
2025-07-18 09:01:21,592 - INFO - 更新表单数据成功: FINST-KLF66HD1ZP5W0LNBBZ0NO5NGE9FG38CKWWOBMO3
2025-07-18 09:01:21,592 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:21,592 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-RNA66D717V1WDUTRFVF9WBPIHHQB36ZSVWOBM0I
2025-07-18 09:01:22,092 - INFO - 更新表单数据成功: FINST-RNA66D717V1WDUTRFVF9WBPIHHQB36ZSVWOBM0I
2025-07-18 09:01:22,092 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:22,092 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-JAC66MB1KD5WNUZYA5WKM686S7EN3O41UWOBMO2
2025-07-18 09:01:22,748 - INFO - 更新表单数据成功: FINST-JAC66MB1KD5WNUZYA5WKM686S7EN3O41UWOBMO2
2025-07-18 09:01:22,748 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:22,748 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-6AG66W810F5WHM3G8LOZ88II1HBX3A9FSWOBMD1
2025-07-18 09:01:23,279 - INFO - 更新表单数据成功: FINST-6AG66W810F5WHM3G8LOZ88II1HBX3A9FSWOBMD1
2025-07-18 09:01:23,279 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:23,279 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-3PF66E91IP5W7U0SBMDNZ8D8UIWA2GKZQWOBMC
2025-07-18 09:01:23,873 - INFO - 更新表单数据成功: FINST-3PF66E91IP5W7U0SBMDNZ8D8UIWA2GKZQWOBMC
2025-07-18 09:01:23,873 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:23,873 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-L8D665C12Q5WPZAXEBUMR95Q47I92XKQPWOBM51
2025-07-18 09:01:24,357 - INFO - 更新表单数据成功: FINST-L8D665C12Q5WPZAXEBUMR95Q47I92XKQPWOBM51
2025-07-18 09:01:24,357 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:24,357 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-7PF66MD1JP5WM7D19MO3Y88AFZSL2XIAOWOBMI1
2025-07-18 09:01:24,842 - INFO - 更新表单数据成功: FINST-7PF66MD1JP5WM7D19MO3Y88AFZSL2XIAOWOBMI1
2025-07-18 09:01:24,857 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:24,857 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-90D66XA15Q5WSTHB9IUZA8S4HB0Z2G5NMWOBMP1
2025-07-18 09:01:25,326 - INFO - 更新表单数据成功: FINST-90D66XA15Q5WSTHB9IUZA8S4HB0Z2G5NMWOBMP1
2025-07-18 09:01:25,326 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:25,326 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-OLC66Z61W82WXY81BN9AR455DD5W3E3GLWOBM33
2025-07-18 09:01:25,904 - INFO - 更新表单数据成功: FINST-OLC66Z61W82WXY81BN9AR455DD5W3E3GLWOBM33
2025-07-18 09:01:25,904 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:25,904 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-IOC66G710D5WO9E06S04DDDQN9JY2V4FKWOBM25
2025-07-18 09:01:26,545 - INFO - 更新表单数据成功: FINST-IOC66G710D5WO9E06S04DDDQN9JY2V4FKWOBM25
2025-07-18 09:01:26,545 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:26,545 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-XL666BD10B2WTNTRE5FF1CDB011N3J1MIWOBMM6
2025-07-18 09:01:27,139 - INFO - 更新表单数据成功: FINST-XL666BD10B2WTNTRE5FF1CDB011N3J1MIWOBMM6
2025-07-18 09:01:27,139 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:27,139 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-XL666BD1SV2WFR1Z7FGT7C2QNWQS2QANHWOBM641
2025-07-18 09:01:27,654 - INFO - 更新表单数据成功: FINST-XL666BD1SV2WFR1Z7FGT7C2QNWQS2QANHWOBM641
2025-07-18 09:01:27,654 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:27,654 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-7PF66N91VO5WNF1SFX184A5GEN033ARBFWOBMN3
2025-07-18 09:01:28,139 - INFO - 更新表单数据成功: FINST-7PF66N91VO5WNF1SFX184A5GEN033ARBFWOBMN3
2025-07-18 09:01:28,139 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:28,139 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-K7666JC1FN5WM999CLOOM99ZH8NL273NEWOBMM
2025-07-18 09:01:28,779 - INFO - 更新表单数据成功: FINST-K7666JC1FN5WM999CLOOM99ZH8NL273NEWOBMM
2025-07-18 09:01:28,779 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:28,779 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-LLF66F714H5W4E0B8XJZ3822GH0Q3AHHCWOBMM2
2025-07-18 09:01:29,404 - INFO - 更新表单数据成功: FINST-LLF66F714H5W4E0B8XJZ3822GH0Q3AHHCWOBMM2
2025-07-18 09:01:29,404 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:29,404 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-X3E66X816P5WGY96CNCQF7S8K5TV25GMBWOBMX2
2025-07-18 09:01:30,045 - INFO - 更新表单数据成功: FINST-X3E66X816P5WGY96CNCQF7S8K5TV25GMBWOBMX2
2025-07-18 09:01:30,045 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:30,045 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-DUF66091ZO2WH8Z79M4IG8BV0RS13WG9AWOBMJ81
2025-07-18 09:01:30,623 - INFO - 更新表单数据成功: FINST-DUF66091ZO2WH8Z79M4IG8BV0RS13WG9AWOBMJ81
2025-07-18 09:01:30,623 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:30,623 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-KLF66HD1GQ5WHO6AF9YJR61OMDNH2RHS8WOBMH
2025-07-18 09:01:31,248 - INFO - 更新表单数据成功: FINST-KLF66HD1GQ5WHO6AF9YJR61OMDNH2RHS8WOBMH
2025-07-18 09:01:31,248 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:31,248 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-7PF66CC1HN5WJWLXBDYAZCBIGYF123TV7WOBM84
2025-07-18 09:01:31,717 - INFO - 更新表单数据成功: FINST-7PF66CC1HN5WJWLXBDYAZCBIGYF123TV7WOBM84
2025-07-18 09:01:31,717 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:31,717 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-RTA66X61GO5WYRTCFTF5Z3R5021R2OL26WOBMP
2025-07-18 09:01:32,201 - INFO - 更新表单数据成功: FINST-RTA66X61GO5WYRTCFTF5Z3R5021R2OL26WOBMP
2025-07-18 09:01:32,201 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:32,201 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-2FD66I71DP5WQWVU551VAA1VXE2837614WOBMB
2025-07-18 09:01:33,045 - INFO - 更新表单数据成功: FINST-2FD66I71DP5WQWVU551VAA1VXE2837614WOBMB
2025-07-18 09:01:33,045 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:33,045 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-XL666BD10B2WZ53TCLYH44YP5J6L3U5M2WOBMF8
2025-07-18 09:01:33,686 - INFO - 更新表单数据成功: FINST-XL666BD10B2WZ53TCLYH44YP5J6L3U5M2WOBMF8
2025-07-18 09:01:33,686 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:33,686 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-6IF66PC1QO2W0NARDWZL09KZRAR136391WOBMIN
2025-07-18 09:01:34,232 - INFO - 更新表单数据成功: FINST-6IF66PC1QO2W0NARDWZL09KZRAR136391WOBMIN
2025-07-18 09:01:34,232 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:34,232 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-7PF66N912P2W8S0RF53V15GEA25G27880WOBML
2025-07-18 09:01:34,873 - INFO - 更新表单数据成功: FINST-7PF66N912P2W8S0RF53V15GEA25G27880WOBML
2025-07-18 09:01:34,873 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:34,873 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-GNC66E91MB2W053C6LD5T474YI832FN9ZVOBMKZ
2025-07-18 09:01:35,467 - INFO - 更新表单数据成功: FINST-GNC66E91MB2W053C6LD5T474YI832FN9ZVOBMKZ
2025-07-18 09:01:35,467 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:35,467 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-BCC66FB1UA2WGGOHA53YSBT0NNE93WKWXVOBMG9
2025-07-18 09:01:36,045 - INFO - 更新表单数据成功: FINST-BCC66FB1UA2WGGOHA53YSBT0NNE93WKWXVOBMG9
2025-07-18 09:01:36,045 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:36,045 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-R1A66H910O5WB1AXCA4FT5BOFZPX2SHFWVOBMD
2025-07-18 09:01:36,685 - INFO - 更新表单数据成功: FINST-R1A66H910O5WB1AXCA4FT5BOFZPX2SHFWVOBMD
2025-07-18 09:01:36,685 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:36,685 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-2PF66TC1V72WN957C9HKHBZM53CL3CHYUVOBMK8
2025-07-18 09:01:37,264 - INFO - 更新表单数据成功: FINST-2PF66TC1V72WN957C9HKHBZM53CL3CHYUVOBMK8
2025-07-18 09:01:37,264 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:37,264 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-VFF66XA1XO5W8GMLBEWGI68WITYH3ZUJTVOBM01
2025-07-18 09:01:37,779 - INFO - 更新表单数据成功: FINST-VFF66XA1XO5W8GMLBEWGI68WITYH3ZUJTVOBM01
2025-07-18 09:01:37,779 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:37,779 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-RTA66X61HO5WYUWD74I93D9N0N1X28V2SVOBM01
2025-07-18 09:01:38,326 - INFO - 更新表单数据成功: FINST-RTA66X61HO5WYUWD74I93D9N0N1X28V2SVOBM01
2025-07-18 09:01:38,326 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:38,326 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-LLF66O717D2W814L9JSA5AAD21RP2GZPQVOBMC2
2025-07-18 09:01:38,904 - INFO - 更新表单数据成功: FINST-LLF66O717D2W814L9JSA5AAD21RP2GZPQVOBMC2
2025-07-18 09:01:38,904 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:38,904 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-KLF66HD10Q5W72K3BS3MP7IYIL2322ZCPVOBMG
2025-07-18 09:01:39,435 - INFO - 更新表单数据成功: FINST-KLF66HD10Q5W72K3BS3MP7IYIL2322ZCPVOBMG
2025-07-18 09:01:39,435 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:39,435 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-JAC66MB12R2WD0I2ADGL6CXLPT3P25SJNVOBM5G
2025-07-18 09:01:40,060 - INFO - 更新表单数据成功: FINST-JAC66MB12R2WD0I2ADGL6CXLPT3P25SJNVOBM5G
2025-07-18 09:01:40,060 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:40,060 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-8SG66JA18L5WDSZJBDCQQDK3TV1S2RPZLVOBM15
2025-07-18 09:01:40,592 - INFO - 更新表单数据成功: FINST-8SG66JA18L5WDSZJBDCQQDK3TV1S2RPZLVOBM15
2025-07-18 09:01:40,592 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:40,592 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-487664C1YP5WQUXLB27BN4FQHMY826UTKVOBM1
2025-07-18 09:01:41,092 - INFO - 更新表单数据成功: FINST-487664C1YP5WQUXLB27BN4FQHMY826UTKVOBM1
2025-07-18 09:01:41,092 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:41,092 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-HXD667B1KN2W67O2FOA664DFE3EW3VYGJVOBM2K
2025-07-18 09:01:41,576 - INFO - 更新表单数据成功: FINST-HXD667B1KN2W67O2FOA664DFE3EW3VYGJVOBM2K
2025-07-18 09:01:41,576 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:41,576 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-LFA66G91NJ2W1FSXAG28M9B1L08G2CXVHVOBMH7
2025-07-18 09:01:42,123 - INFO - 更新表单数据成功: FINST-LFA66G91NJ2W1FSXAG28M9B1L08G2CXVHVOBMH7
2025-07-18 09:01:42,123 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:42,123 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-90E66JD1TG3W0YNKF0T099S7H0FK31ALGVOBM0H
2025-07-18 09:01:42,654 - INFO - 更新表单数据成功: FINST-90E66JD1TG3W0YNKF0T099S7H0FK31ALGVOBM0H
2025-07-18 09:01:42,654 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:42,654 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-Q5D66871VP5WER5H9PKTO6LLFID1348CFVOBMK
2025-07-18 09:01:43,123 - INFO - 更新表单数据成功: FINST-Q5D66871VP5WER5H9PKTO6LLFID1348CFVOBMK
2025-07-18 09:01:43,123 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:43,123 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-L5766E71UE5WHUJA74QJG4L2718C34ILDVOBMU2
2025-07-18 09:01:43,732 - INFO - 更新表单数据成功: FINST-L5766E71UE5WHUJA74QJG4L2718C34ILDVOBMU2
2025-07-18 09:01:43,732 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:43,732 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-MLF66PA1J82WO23Y7KFYZ98GIKYC2JOGCVOBM8J
2025-07-18 09:01:44,310 - INFO - 更新表单数据成功: FINST-MLF66PA1J82WO23Y7KFYZ98GIKYC2JOGCVOBM8J
2025-07-18 09:01:44,310 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:44,310 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-3PF66V71ON4WG1JG7HDES4702JCD2GFJAVOBMQA
2025-07-18 09:01:44,779 - INFO - 更新表单数据成功: FINST-3PF66V71ON4WG1JG7HDES4702JCD2GFJAVOBMQA
2025-07-18 09:01:44,779 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:44,779 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-1MD668B13O5WHBAABJ43UBDJOOTF2TUH9VOBM81
2025-07-18 09:01:45,357 - INFO - 更新表单数据成功: FINST-1MD668B13O5WHBAABJ43UBDJOOTF2TUH9VOBM81
2025-07-18 09:01:45,357 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:45,357 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-S0E660A1DS4WHC9NA4MPP5U83YOS31VS7VOBMXI
2025-07-18 09:01:45,826 - INFO - 更新表单数据成功: FINST-S0E660A1DS4WHC9NA4MPP5U83YOS31VS7VOBMXI
2025-07-18 09:01:45,826 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:45,826 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-A17661C17C2WOX5VE8XHX7XM8G462EQZ5VOBM161
2025-07-18 09:01:46,357 - INFO - 更新表单数据成功: FINST-A17661C17C2WOX5VE8XHX7XM8G462EQZ5VOBM161
2025-07-18 09:01:46,357 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:46,357 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-AI866781OP5W7EQVDMTALB5BODVK3WH95VOBMY
2025-07-18 09:01:46,935 - INFO - 更新表单数据成功: FINST-AI866781OP5W7EQVDMTALB5BODVK3WH95VOBMY
2025-07-18 09:01:46,935 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:46,935 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-2PF66TC1PO5WK1WNC88539ZE50DE39J63VOBMF
2025-07-18 09:01:47,467 - INFO - 更新表单数据成功: FINST-2PF66TC1PO5WK1WNC88539ZE50DE39J63VOBMF
2025-07-18 09:01:47,467 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:47,467 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-6PF66691SO5WDM497HTIGAYB9X25280S1VOBMM
2025-07-18 09:01:48,045 - INFO - 更新表单数据成功: FINST-6PF66691SO5WDM497HTIGAYB9X25280S1VOBMM
2025-07-18 09:01:48,045 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:48,045 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-FQD66YB1L82WNL6B64ADE7YKN7YK39961VOBMM81
2025-07-18 09:01:48,607 - INFO - 更新表单数据成功: FINST-FQD66YB1L82WNL6B64ADE7YKN7YK39961VOBMM81
2025-07-18 09:01:48,607 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:48,607 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-5A966081SZ4W6KS3C8VSI6PD9F6E33TNZUOBMUH
2025-07-18 09:01:49,217 - INFO - 更新表单数据成功: FINST-5A966081SZ4W6KS3C8VSI6PD9F6E33TNZUOBMUH
2025-07-18 09:01:49,217 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:49,217 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-YPE66RB1C82WHXWRANFUF7BW5D352K9EXUOBMD8
2025-07-18 09:01:49,779 - INFO - 更新表单数据成功: FINST-YPE66RB1C82WHXWRANFUF7BW5D352K9EXUOBMD8
2025-07-18 09:01:49,779 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:49,779 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-80B66291IP5WUA75FCQMDAC4FHKE2FH0XUOBM11
2025-07-18 09:01:50,295 - INFO - 更新表单数据成功: FINST-80B66291IP5WUA75FCQMDAC4FHKE2FH0XUOBM11
2025-07-18 09:01:50,295 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:50,295 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-IQG66AD1JA2WT92O9H77973GEUHE29GFVUOBMM1
2025-07-18 09:01:50,904 - INFO - 更新表单数据成功: FINST-IQG66AD1JA2WT92O9H77973GEUHE29GFVUOBMM1
2025-07-18 09:01:50,904 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:50,904 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-SED66Q61U82WWR248TPLTDEQIJYF3A49UUOBMTE
2025-07-18 09:01:51,435 - INFO - 更新表单数据成功: FINST-SED66Q61U82WWR248TPLTDEQIJYF3A49UUOBMTE
2025-07-18 09:01:51,435 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:51,435 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-LLF66FD1AK4WMQ7IDZBR3CSUK2YP2BHRRUOBMB8
2025-07-18 09:01:51,982 - INFO - 更新表单数据成功: FINST-LLF66FD1AK4WMQ7IDZBR3CSUK2YP2BHRRUOBMB8
2025-07-18 09:01:51,982 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:51,982 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-W4G66DA1D82WTXJ66WKUIBE929LW3FGAQUOBMG11
2025-07-18 09:01:52,514 - INFO - 更新表单数据成功: FINST-W4G66DA1D82WTXJ66WKUIBE929LW3FGAQUOBMG11
2025-07-18 09:01:52,514 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:52,514 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-X3766I91BO5WTQ2D8DSJP68ZPIZU2LNSPUOBM51
2025-07-18 09:01:53,170 - INFO - 更新表单数据成功: FINST-X3766I91BO5WTQ2D8DSJP68ZPIZU2LNSPUOBM51
2025-07-18 09:01:53,170 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:53,170 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-R1A66H912A2WVG5EF7PAE7XQXEYZ295BNUOBMF9
2025-07-18 09:01:53,732 - INFO - 更新表单数据成功: FINST-R1A66H912A2WVG5EF7PAE7XQXEYZ295BNUOBMF9
2025-07-18 09:01:53,732 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:53,732 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-3PF66X61L82WZ79A7OWPW6KKQ2JT38MYMUOBMJE1
2025-07-18 09:01:54,185 - INFO - 更新表单数据成功: FINST-3PF66X61L82WZ79A7OWPW6KKQ2JT38MYMUOBMJE1
2025-07-18 09:01:54,185 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:54,185 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-6IF66PC1DN5WE8YF7Q80D9HBCM8V36OPLUOBMZ
2025-07-18 09:01:54,748 - INFO - 更新表单数据成功: FINST-6IF66PC1DN5WE8YF7Q80D9HBCM8V36OPLUOBMZ
2025-07-18 09:01:54,764 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:54,764 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-DOA66K91VQ2WV5598K8WR5L2X7S42FG0KUOBMY61
2025-07-18 09:01:55,373 - INFO - 更新表单数据成功: FINST-DOA66K91VQ2WV5598K8WR5L2X7S42FG0KUOBMY61
2025-07-18 09:01:55,373 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:55,373 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-VRA66VA1WK2WTLUD7R8BZ9370IZ4388RHUOBMS1
2025-07-18 09:01:55,810 - INFO - 更新表单数据成功: FINST-VRA66VA1WK2WTLUD7R8BZ9370IZ4388RHUOBMS1
2025-07-18 09:01:55,810 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:55,810 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-LLF66O71XI2WW579EXE1WA30LOIR38M8HUOBM8F
2025-07-18 09:01:56,389 - INFO - 更新表单数据成功: FINST-LLF66O71XI2WW579EXE1WA30LOIR38M8HUOBM8F
2025-07-18 09:01:56,389 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:56,389 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-HXD667B10A2WTWBTESJ3EATVBAQD26RVFUOBMQN
2025-07-18 09:01:56,935 - INFO - 更新表单数据成功: FINST-HXD667B10A2WTWBTESJ3EATVBAQD26RVFUOBMQN
2025-07-18 09:01:56,935 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:56,935 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-AJF66F716O5WVUV66B35BB6BLF4A2NFHDUOBMK
2025-07-18 09:01:57,435 - INFO - 更新表单数据成功: FINST-AJF66F716O5WVUV66B35BB6BLF4A2NFHDUOBMK
2025-07-18 09:01:57,435 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:57,435 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-8LC66GC14P5W3WSG6KSK16UAPOA72OL4CUOBM2
2025-07-18 09:01:58,076 - INFO - 更新表单数据成功: FINST-8LC66GC14P5W3WSG6KSK16UAPOA72OL4CUOBM2
2025-07-18 09:01:58,076 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:58,076 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-XBF66071TB2W8QQ6A5BCC95W1V872O5OBUOBM1U
2025-07-18 09:01:58,623 - INFO - 更新表单数据成功: FINST-XBF66071TB2W8QQ6A5BCC95W1V872O5OBUOBM1U
2025-07-18 09:01:58,623 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:58,623 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-FQD66YB1FN4WP5P7DMU9V8WYJR822VRH9UOBM7B
2025-07-18 09:01:59,076 - INFO - 更新表单数据成功: FINST-FQD66YB1FN4WP5P7DMU9V8WYJR822VRH9UOBM7B
2025-07-18 09:01:59,076 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:59,076 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-2K666OB1U82WDYICAMA3Q6NBCJ5J230O8UOBMMC1
2025-07-18 09:01:59,623 - INFO - 更新表单数据成功: FINST-2K666OB1U82WDYICAMA3Q6NBCJ5J230O8UOBMMC1
2025-07-18 09:01:59,623 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:01:59,623 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-QZE668D15A2W9ADU8CXYQ4P37IN52OT47UOBM551
2025-07-18 09:02:00,295 - INFO - 更新表单数据成功: FINST-QZE668D15A2W9ADU8CXYQ4P37IN52OT47UOBM551
2025-07-18 09:02:00,295 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:00,295 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-UW966371KD5WFY69C04WI6HLXCV43YCR4UOBMO4
2025-07-18 09:02:00,982 - INFO - 更新表单数据成功: FINST-UW966371KD5WFY69C04WI6HLXCV43YCR4UOBMO4
2025-07-18 09:02:00,982 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:00,982 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-6AG66W818C2WVQ5MFCYRX7ZLKIZH36JE3UOBM3C1
2025-07-18 09:02:01,607 - INFO - 更新表单数据成功: FINST-6AG66W818C2WVQ5MFCYRX7ZLKIZH36JE3UOBM3C1
2025-07-18 09:02:01,607 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:01,607 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-OLF66Q71082WZDCL63AIZA7AAADL33N02UOBMWX
2025-07-18 09:02:02,217 - INFO - 更新表单数据成功: FINST-OLF66Q71082WZDCL63AIZA7AAADL33N02UOBMWX
2025-07-18 09:02:02,217 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:02,217 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-W4G66DA1B82WWB8J9ZS1F6KZ6BDV3D4O1UOBM4C
2025-07-18 09:02:02,810 - INFO - 更新表单数据成功: FINST-W4G66DA1B82WWB8J9ZS1F6KZ6BDV3D4O1UOBM4C
2025-07-18 09:02:02,810 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:02,810 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-8LC66GC1WQ2WI19X78M6T5VOJ0YI3N0VZTOBMEW
2025-07-18 09:02:03,404 - INFO - 更新表单数据成功: FINST-8LC66GC1WQ2WI19X78M6T5VOJ0YI3N0VZTOBMEW
2025-07-18 09:02:03,404 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:03,404 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-RTA66X61AQ2W93E6CHIIW79YYCR13FAUYTOBM1N
2025-07-18 09:02:03,982 - INFO - 更新表单数据成功: FINST-RTA66X61AQ2W93E6CHIIW79YYCR13FAUYTOBM1N
2025-07-18 09:02:03,982 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:03,982 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-AEF66BC1482WBIMD9ORFSDU32WB73QICXTOBMHD1
2025-07-18 09:02:04,685 - INFO - 更新表单数据成功: FINST-AEF66BC1482WBIMD9ORFSDU32WB73QICXTOBMHD1
2025-07-18 09:02:04,685 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:04,685 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-W3B66L71UD3WSX8IEOWV9ACGUQDR2EK3WTOBMLA
2025-07-18 09:02:05,279 - INFO - 更新表单数据成功: FINST-W3B66L71UD3WSX8IEOWV9ACGUQDR2EK3WTOBMLA
2025-07-18 09:02:05,279 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:05,279 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-3Z966E91QF5WM2NQ88DLH4G6CX1J2LSDUTOBM62
2025-07-18 09:02:05,810 - INFO - 更新表单数据成功: FINST-3Z966E91QF5WM2NQ88DLH4G6CX1J2LSDUTOBM62
2025-07-18 09:02:05,810 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:05,810 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-S0E660A1EB2WLZZRAUWNU9Y92DLS26G8STOBMKO
2025-07-18 09:02:06,357 - INFO - 更新表单数据成功: FINST-S0E660A1EB2WLZZRAUWNU9Y92DLS26G8STOBMKO
2025-07-18 09:02:06,357 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:06,357 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-RTA66X61FO5WG23W5KO1U6Y8UHHM26CRRTOBMD1
2025-07-18 09:02:07,123 - INFO - 更新表单数据成功: FINST-RTA66X61FO5WG23W5KO1U6Y8UHHM26CRRTOBMD1
2025-07-18 09:02:07,138 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:07,138 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-XO8662C1BA2WG329B611O99D7MRW2AL9PTOBMD5
2025-07-18 09:02:07,701 - INFO - 更新表单数据成功: FINST-XO8662C1BA2WG329B611O99D7MRW2AL9PTOBMD5
2025-07-18 09:02:07,701 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:07,701 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-LFA66G91NJ2W1FSXAG28M9B1L08G22EKOTOBM46
2025-07-18 09:02:08,248 - INFO - 更新表单数据成功: FINST-LFA66G91NJ2W1FSXAG28M9B1L08G22EKOTOBM46
2025-07-18 09:02:08,248 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:08,248 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-3Z966E91FN5WSKTPBBX6W6MCQ7B73LZINTOBMP1
2025-07-18 09:02:08,826 - INFO - 更新表单数据成功: FINST-3Z966E91FN5WSKTPBBX6W6MCQ7B73LZINTOBMP1
2025-07-18 09:02:08,826 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:08,826 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-U1B66W912F5WGWINF7JK248A1II53IW4LTOBML
2025-07-18 09:02:09,467 - INFO - 更新表单数据成功: FINST-U1B66W912F5WGWINF7JK248A1II53IW4LTOBML
2025-07-18 09:02:09,467 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:09,467 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-V7966QC1B92WR7BXD4FTW4KG3FIL3G1SJTOBM7Y
2025-07-18 09:02:10,045 - INFO - 更新表单数据成功: FINST-V7966QC1B92WR7BXD4FTW4KG3FIL3G1SJTOBM7Y
2025-07-18 09:02:10,045 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:10,045 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-3PF66271CP2WOJUG93KKCDSSY6TL24LEITOBMH9
2025-07-18 09:02:10,607 - INFO - 更新表单数据成功: FINST-3PF66271CP2WOJUG93KKCDSSY6TL24LEITOBMH9
2025-07-18 09:02:10,607 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:10,607 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-FIG66R81IS2WSI9QE8Q9B52U8D1Q2YJRHTOBMOC1
2025-07-18 09:02:11,217 - INFO - 更新表单数据成功: FINST-FIG66R81IS2WSI9QE8Q9B52U8D1Q2YJRHTOBMOC1
2025-07-18 09:02:11,217 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:11,217 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-VME66K814E2WNPZE8B4SS9Z1RERQ3P2EFTOBM8S
2025-07-18 09:02:11,810 - INFO - 更新表单数据成功: FINST-VME66K814E2WNPZE8B4SS9Z1RERQ3P2EFTOBM8S
2025-07-18 09:02:11,810 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:11,810 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-M1B66ED1PL5WCYZ4CJHCG994DB1639F1FTOBMJ2
2025-07-18 09:02:12,420 - INFO - 更新表单数据成功: FINST-M1B66ED1PL5WCYZ4CJHCG994DB1639F1FTOBMJ2
2025-07-18 09:02:12,420 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:12,420 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-I3F66991CB2W79DQ8A128CCWY1PC3GUBDTOBMRF
2025-07-18 09:02:12,982 - INFO - 更新表单数据成功: FINST-I3F66991CB2W79DQ8A128CCWY1PC3GUBDTOBMRF
2025-07-18 09:02:12,982 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:12,982 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-OIF66BA1B82WIKJD7IZEYA8CWGGS20W2CTOBMF22
2025-07-18 09:02:13,529 - INFO - 更新表单数据成功: FINST-OIF66BA1B82WIKJD7IZEYA8CWGGS20W2CTOBMF22
2025-07-18 09:02:13,529 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:13,529 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-ORA66F81QO2WLBPRFGC1KC2S52XO2SFHATOBMIM
2025-07-18 09:02:14,216 - INFO - 更新表单数据成功: FINST-ORA66F81QO2WLBPRFGC1KC2S52XO2SFHATOBMIM
2025-07-18 09:02:14,216 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:14,216 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-U8966871T82WMWOPDDG2PA1DS5HH3LPK9TOBM16
2025-07-18 09:02:14,732 - INFO - 更新表单数据成功: FINST-U8966871T82WMWOPDDG2PA1DS5HH3LPK9TOBM16
2025-07-18 09:02:14,732 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:14,732 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-HXD667B1YB2WLVED66WZX9PV2WB03EG37TOBM0C
2025-07-18 09:02:15,420 - INFO - 更新表单数据成功: FINST-HXD667B1YB2WLVED66WZX9PV2WB03EG37TOBM0C
2025-07-18 09:02:15,420 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:15,420 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-KLF66HD13A2W5X8C9GCTBDA4WCCI2P7Q5TOBMKV
2025-07-18 09:02:15,982 - INFO - 更新表单数据成功: FINST-KLF66HD13A2W5X8C9GCTBDA4WCCI2P7Q5TOBMKV
2025-07-18 09:02:15,982 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:15,982 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-MLF66PA1TO4W9ELEBDOF68DPIGIX3MAD4TOBMLA
2025-07-18 09:02:16,513 - INFO - 更新表单数据成功: FINST-MLF66PA1TO4W9ELEBDOF68DPIGIX3MAD4TOBMLA
2025-07-18 09:02:16,513 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:16,513 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-3PF66X61IR2WUMAPA75RCDVF08TM3TD03TOBMXT
2025-07-18 09:02:17,170 - INFO - 更新表单数据成功: FINST-3PF66X61IR2WUMAPA75RCDVF08TM3TD03TOBMXT
2025-07-18 09:02:17,170 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:17,170 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-WBF66B817A2WFKIW8ORM47AKZ4K03E7J1TOBMZA
2025-07-18 09:02:17,857 - INFO - 更新表单数据成功: FINST-WBF66B817A2WFKIW8ORM47AKZ4K03E7J1TOBMZA
2025-07-18 09:02:17,857 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:17,857 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-ZX8665718C2W8ATCC0Y309B997JP2ND60TOBMH8
2025-07-18 09:02:18,420 - INFO - 更新表单数据成功: FINST-ZX8665718C2W8ATCC0Y309B997JP2ND60TOBMH8
2025-07-18 09:02:18,420 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:18,420 - INFO - 更新第一个宜搭表单 - 店铺编码: *********, 表单ID: FINST-RN766181KB2W6VB7D6MXS63XA6042WVTZSOBMMM
2025-07-18 09:02:18,982 - INFO - 更新表单数据成功: FINST-RN766181KB2W6VB7D6MXS63XA6042WVTZSOBMMM
2025-07-18 09:02:18,982 - INFO - 更新第一个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:18,982 - INFO - 查询第二个宜搭表单 - 店铺编码: *********
2025-07-18 09:02:18,982 - INFO - Request Parameters - Page 1:
2025-07-18 09:02:18,982 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-18 09:02:18,982 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "textField_m9tojheq", "value": "*********", "type": "TEXT", "operator": "eq", "componentName": "TextField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-18 09:02:19,326 - INFO - Response - Page 1:
2025-07-18 09:02:19,326 - INFO - 第 1 页获取到 7 条记录
2025-07-18 09:02:19,529 - INFO - 查询完成，共获取到 7 条记录
2025-07-18 09:02:19,529 - INFO - 更新第二个宜搭表单 - 店铺编码: *********, 表单ID: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMT1
2025-07-18 09:02:20,029 - INFO - 更新表单数据成功: FINST-HJ966H812SSW4UDWDRT9M5K6J0GB3UYMIFLCMT1
2025-07-18 09:02:20,029 - INFO - 更新第二个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:20,029 - INFO - 更新第二个宜搭表单 - 店铺编码: *********, 表单ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFR
2025-07-18 09:02:20,435 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFR
2025-07-18 09:02:20,435 - INFO - 更新第二个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:20,435 - INFO - 更新第二个宜搭表单 - 店铺编码: *********, 表单ID: FINST-RTA66X61LO5W721S827476KZ4D8Z1GU1NTOBMH1
2025-07-18 09:02:20,920 - INFO - 更新表单数据成功: FINST-RTA66X61LO5W721S827476KZ4D8Z1GU1NTOBMH1
2025-07-18 09:02:20,920 - INFO - 更新第二个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:20,920 - INFO - 更新第二个宜搭表单 - 店铺编码: *********, 表单ID: FINST-UW9663710B2WNGC9E4D5WB0LG6R53BPEMTOBMQW
2025-07-18 09:02:21,357 - INFO - 更新表单数据成功: FINST-UW9663710B2WNGC9E4D5WB0LG6R53BPEMTOBMQW
2025-07-18 09:02:21,357 - INFO - 更新第二个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:21,357 - INFO - 更新第二个宜搭表单 - 店铺编码: *********, 表单ID: FINST-XO8662C1BA2WG329B611O99D7MRW2HR1MTOBMT3
2025-07-18 09:02:21,857 - INFO - 更新表单数据成功: FINST-XO8662C1BA2WG329B611O99D7MRW2HR1MTOBMT3
2025-07-18 09:02:21,857 - INFO - 更新第二个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:21,857 - INFO - 更新第二个宜搭表单 - 店铺编码: *********, 表单ID: FINST-KLF66HD1FS2WOYM69AZ3N5N0EL2U3I3HLTOBMIY
2025-07-18 09:02:22,326 - INFO - 更新表单数据成功: FINST-KLF66HD1FS2WOYM69AZ3N5N0EL2U3I3HLTOBMIY
2025-07-18 09:02:22,326 - INFO - 更新第二个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:22,326 - INFO - 更新第二个宜搭表单 - 店铺编码: *********, 表单ID: FINST-MUC66Q81KO5WE2W2B212K7ONSWPQ2LLOKTOBMQ1
2025-07-18 09:02:22,701 - INFO - 更新表单数据成功: FINST-MUC66Q81KO5WE2W2B212K7ONSWPQ2LLOKTOBMQ1
2025-07-18 09:02:22,701 - INFO - 更新第二个宜搭表单成功 - 店铺编码: *********
2025-07-18 09:02:22,701 - INFO - 店铺名称变更处理完成 - 店铺编码: *********
2025-07-18 09:02:22,701 - INFO - 开始处理店铺名称变更 - 店铺编码: 100101278, 原名称: 一年四季, 新名称: 一年四季24小时智能健身
2025-07-18 09:02:22,716 - INFO - 查询第一个宜搭表单 - 店铺编码: 100101278
2025-07-18 09:02:22,716 - INFO - Request Parameters - Page 1:
2025-07-18 09:02:22,716 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-18 09:02:22,716 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "textField_m9nw1k6y", "value": "100101278", "type": "TEXT", "operator": "eq", "componentName": "TextField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-18 09:02:23,451 - INFO - Response - Page 1:
2025-07-18 09:02:23,451 - INFO - 第 1 页获取到 13 条记录
2025-07-18 09:02:23,654 - INFO - 查询完成，共获取到 13 条记录
2025-07-18 09:02:23,654 - INFO - 更新第一个宜搭表单 - 店铺编码: 100101278, 表单ID: FINST-HXD667B1J1UWID3QBR8DSAKFPMRA39A62BNCMP1
2025-07-18 09:02:24,170 - INFO - 更新表单数据成功: FINST-HXD667B1J1UWID3QBR8DSAKFPMRA39A62BNCMP1
2025-07-18 09:02:24,170 - INFO - 更新第一个宜搭表单成功 - 店铺编码: 100101278
2025-07-18 09:02:24,170 - INFO - 更新第一个宜搭表单 - 店铺编码: 100101278, 表单ID: FINST-MQA6627186VWCUKEARUSJ9RJRP0Z2KNPVRRCMYG
2025-07-18 09:02:24,763 - INFO - 更新表单数据成功: FINST-MQA6627186VWCUKEARUSJ9RJRP0Z2KNPVRRCMYG
2025-07-18 09:02:24,763 - INFO - 更新第一个宜搭表单成功 - 店铺编码: 100101278
2025-07-18 09:02:24,763 - INFO - 更新第一个宜搭表单 - 店铺编码: 100101278, 表单ID: FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMAH
2025-07-18 09:02:25,310 - INFO - 更新表单数据成功: FINST-MQA6627186VWCUKEARUSJ9RJRP0Z21FUVRRCMAH
2025-07-18 09:02:25,310 - INFO - 更新第一个宜搭表单成功 - 店铺编码: 100101278
2025-07-18 09:02:25,310 - INFO - 更新第一个宜搭表单 - 店铺编码: 100101278, 表单ID: FINST-FIG66R81AWYWRBEQBOYNG9GJ3WXG2SFPEXTCMB1
2025-07-18 09:02:25,982 - INFO - 更新表单数据成功: FINST-FIG66R81AWYWRBEQBOYNG9GJ3WXG2SFPEXTCMB1
2025-07-18 09:02:25,982 - INFO - 更新第一个宜搭表单成功 - 店铺编码: 100101278
2025-07-18 09:02:25,982 - INFO - 更新第一个宜搭表单 - 店铺编码: 100101278, 表单ID: FINST-DIC66I917AZWOUXN7LEDKCNJN33T2YO5KZUCMR7
2025-07-18 09:02:26,576 - INFO - 更新表单数据成功: FINST-DIC66I917AZWOUXN7LEDKCNJN33T2YO5KZUCMR7
2025-07-18 09:02:26,576 - INFO - 更新第一个宜搭表单成功 - 店铺编码: 100101278
2025-07-18 09:02:26,576 - INFO - 更新第一个宜搭表单 - 店铺编码: 100101278, 表单ID: FINST-SI766181080XWYANFCPHB457LNZL3UKLFLWCMWJ
2025-07-18 09:02:27,154 - INFO - 更新表单数据成功: FINST-SI766181080XWYANFCPHB457LNZL3UKLFLWCMWJ
2025-07-18 09:02:27,154 - INFO - 更新第一个宜搭表单成功 - 店铺编码: 100101278
2025-07-18 09:02:27,154 - INFO - 更新第一个宜搭表单 - 店铺编码: 100101278, 表单ID: FINST-XL866HB1P61X9Z25AS2PQ5WBPZ7W173ZSHXCMX4
2025-07-18 09:02:27,701 - INFO - 更新表单数据成功: FINST-XL866HB1P61X9Z25AS2PQ5WBPZ7W173ZSHXCMX4
2025-07-18 09:02:27,701 - INFO - 更新第一个宜搭表单成功 - 店铺编码: 100101278
2025-07-18 09:02:27,701 - INFO - 更新第一个宜搭表单 - 店铺编码: 100101278, 表单ID: FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMO8
2025-07-18 09:02:28,248 - INFO - 更新表单数据成功: FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMO8
2025-07-18 09:02:28,248 - INFO - 更新第一个宜搭表单成功 - 店铺编码: 100101278
2025-07-18 09:02:28,248 - INFO - 更新第一个宜搭表单 - 店铺编码: 100101278, 表单ID: FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMSA1
2025-07-18 09:02:28,795 - INFO - 更新表单数据成功: FINST-AEF66BC1T70XPYAWE2IB37OJ87WB3VZAL81DMSA1
2025-07-18 09:02:28,795 - INFO - 更新第一个宜搭表单成功 - 店铺编码: 100101278
2025-07-18 09:02:28,795 - INFO - 更新第一个宜搭表单 - 店铺编码: 100101278, 表单ID: FINST-MLF66JA1FZ4XU622CF8ZMC1RY9IZ2DJSVH2DMX
2025-07-18 09:02:29,373 - INFO - 更新表单数据成功: FINST-MLF66JA1FZ4XU622CF8ZMC1RY9IZ2DJSVH2DMX
2025-07-18 09:02:29,373 - INFO - 更新第一个宜搭表单成功 - 店铺编码: 100101278
2025-07-18 09:02:29,373 - INFO - 更新第一个宜搭表单 - 店铺编码: 100101278, 表单ID: FINST-3PF66X61ZG5XM9HAC8LX9C0S06J03YBUP34DM2F
2025-07-18 09:02:29,966 - INFO - 更新表单数据成功: FINST-3PF66X61ZG5XM9HAC8LX9C0S06J03YBUP34DM2F
2025-07-18 09:02:29,966 - INFO - 更新第一个宜搭表单成功 - 店铺编码: 100101278
2025-07-18 09:02:29,966 - INFO - 更新第一个宜搭表单 - 店铺编码: 100101278, 表单ID: FINST-2PF66KD1E06X9I3AC8R65AHK2T0Z2CCF2N4DMJ2
2025-07-18 09:02:30,529 - INFO - 更新表单数据成功: FINST-2PF66KD1E06X9I3AC8R65AHK2T0Z2CCF2N4DMJ2
2025-07-18 09:02:30,529 - INFO - 更新第一个宜搭表单成功 - 店铺编码: 100101278
2025-07-18 09:02:30,529 - INFO - 更新第一个宜搭表单 - 店铺编码: 100101278, 表单ID: FINST-CPC66T91LZ5X6DKC9CWJSDPDIQ2U2APFG26DM67
2025-07-18 09:02:31,076 - INFO - 更新表单数据成功: FINST-CPC66T91LZ5X6DKC9CWJSDPDIQ2U2APFG26DM67
2025-07-18 09:02:31,076 - INFO - 更新第一个宜搭表单成功 - 店铺编码: 100101278
2025-07-18 09:02:31,076 - INFO - 查询第二个宜搭表单 - 店铺编码: 100101278
2025-07-18 09:02:31,076 - INFO - Request Parameters - Page 1:
2025-07-18 09:02:31,076 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-18 09:02:31,076 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "textField_m9tojheq", "value": "100101278", "type": "TEXT", "operator": "eq", "componentName": "TextField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-18 09:02:31,341 - INFO - Response - Page 1:
2025-07-18 09:02:31,341 - INFO - 第 1 页获取到 1 条记录
2025-07-18 09:02:31,545 - INFO - 查询完成，共获取到 1 条记录
2025-07-18 09:02:31,545 - INFO - 更新第二个宜搭表单 - 店铺编码: 100101278, 表单ID: FINST-OLC66Z61N4TWEFD1CPN41AB37LED2LRT8ENCMBC
2025-07-18 09:02:31,982 - INFO - 更新表单数据成功: FINST-OLC66Z61N4TWEFD1CPN41AB37LED2LRT8ENCMBC
2025-07-18 09:02:31,982 - INFO - 更新第二个宜搭表单成功 - 店铺编码: 100101278
2025-07-18 09:02:31,982 - INFO - 店铺名称变更处理完成 - 店铺编码: 100101278
2025-07-18 09:02:33,170 - INFO - 邮件发送成功
2025-07-18 09:02:33,170 - INFO - 店铺名称更新邮件发送成功，共 2 条记录
2025-07-18 09:02:33,170 - INFO - 开始处理需要更新的记录，共 16 条
2025-07-18 09:02:33,185 - INFO - 正在处理第 1 条更新记录 - store_code: *********
2025-07-18 09:02:33,185 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "*********", "textField_m8e8g3lu": "小米之家", "employeeField_m8e8g3lw": ["195cbfaa191990374b814d747ed92fe3", "178911c91380e3f9961bcc84847a615f", "18b0e1e4b78fd2c55dc90c74e3b9f3cc", "189f858c407c7b3d480039a4090b4c13", "16340e95afb25e94fcd338840d78edb8"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103438", "textField_mb7rs39i": "李炳煌", "textField_mbc1lbzm": "P0299L25CO0088", "textField_m9jkl9nx": "否"}
2025-07-18 09:02:33,185 - INFO - 正在处理第 2 条更新记录 - store_code: *********
2025-07-18 09:02:33,185 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "*********", "textField_m8e8g3lu": "童创", "employeeField_m8e8g3lw": ["1953c39472ad2995a15b085419b99165", "18f13290b6949adbfcd0691437491a5a", "15e62a6607d73f5229798af46ed94466"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111290", "textField_mb7rs39i": "广州市荔湾区编辅教育培训有限公司", "textField_mbc1lbzm": "P0299L25CO0087", "textField_m9jkl9nx": "否"}
2025-07-18 09:02:33,185 - INFO - 正在处理第 3 条更新记录 - store_code: *********
2025-07-18 09:02:33,185 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "*********", "textField_m8e8g3lu": "童创", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0299L25CO0087", "textField_m9jkl9nx": "否"}
2025-07-18 09:02:33,185 - INFO - 正在处理第 4 条更新记录 - store_code: *********
2025-07-18 09:02:33,185 - INFO - 转换后的数据: {"selectField_m886ps6o": "武汉国金天地", "textField_m911r3pn": "*********", "textField_m8e8g3lu": "招商银行", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "禁用", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111495", "textField_mb7rs39i": "招商银行股份有限公司武汉分行", "textField_mbc1lbzm": "P0003L24CO0130", "textField_m9jkl9nx": "否"}
2025-07-18 09:02:33,185 - INFO - 正在处理第 5 条更新记录 - store_code: 100099764
2025-07-18 09:02:33,185 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州IFC国金天地", "textField_m911r3pn": "100099764", "textField_m8e8g3lu": "Peet's Coffee", "employeeField_m8e8g3lw": ["1821a225bb6138fac03322a4fde8424d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "106722", "textField_mb7rs39i": "皮氏咖啡（深圳）有限公司", "textField_mbc1lbzm": "10164L22CO0042", "textField_m9jkl9nx": "否"}
2025-07-18 09:02:33,185 - INFO - 正在处理第 6 条更新记录 - store_code: 100100968
2025-07-18 09:02:33,185 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州IFC国金天地", "textField_m911r3pn": "100100968", "textField_m8e8g3lu": "M Stand", "employeeField_m8e8g3lw": ["1821a225bb6138fac03322a4fde8424d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "108443", "textField_mb7rs39i": "广州艾恰餐饮服务有限公司", "textField_mbc1lbzm": "10164L24CO0021", "textField_m9jkl9nx": "否"}
2025-07-18 09:02:33,185 - INFO - 正在处理第 7 条更新记录 - store_code: 100099766
2025-07-18 09:02:33,185 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州IFC国金天地", "textField_m911r3pn": "100099766", "textField_m8e8g3lu": "滋粥楼小馆", "employeeField_m8e8g3lw": ["192ae1b7a30d7d7319ab66d444aa66f7"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103352", "textField_mb7rs39i": "广州市滋粥楼小馆餐饮管理有限公司", "textField_mbc1lbzm": "10164L25CO0015", "textField_m9jkl9nx": "否"}
2025-07-18 09:02:33,185 - INFO - 正在处理第 8 条更新记录 - store_code: 100099746
2025-07-18 09:02:33,185 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州IFC国金天地", "textField_m911r3pn": "100099746", "textField_m8e8g3lu": "常来小聚", "employeeField_m8e8g3lw": ["192ae1b7a30d7d7319ab66d444aa66f7"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "104779", "textField_mb7rs39i": "广州市常来大牌档餐饮有限责任公司", "textField_mbc1lbzm": "10164L25CO0022", "textField_m9jkl9nx": "否"}
2025-07-18 09:02:33,185 - INFO - 正在处理第 9 条更新记录 - store_code: 100099738
2025-07-18 09:02:33,185 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州IFC国金天地", "textField_m911r3pn": "100099738", "textField_m8e8g3lu": "全家便利店", "employeeField_m8e8g3lw": ["16d2416c64282711fb71a184c10997bb"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "101332", "textField_mb7rs39i": "广州市福满家连锁便利店有限公司", "textField_mbc1lbzm": "10164L25CO0020", "textField_m9jkl9nx": "否"}
2025-07-18 09:02:33,185 - INFO - 正在处理第 10 条更新记录 - store_code: 100101172
2025-07-18 09:02:33,185 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州IFC国金天地", "textField_m911r3pn": "100101172", "textField_m8e8g3lu": "美珍香", "employeeField_m8e8g3lw": ["1821a225bb6138fac03322a4fde8424d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "108583", "textField_mb7rs39i": "陈林波", "textField_mbc1lbzm": "10164L24CO0024", "textField_m9jkl9nx": "否"}
2025-07-18 09:02:33,185 - INFO - 正在处理第 11 条更新记录 - store_code: 100100351
2025-07-18 09:02:33,185 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州IFC国金天地", "textField_m911r3pn": "100100351", "textField_m8e8g3lu": "混果汁", "employeeField_m8e8g3lw": ["1821a225bb6138fac03322a4fde8424d"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "108080", "textField_mb7rs39i": "广州探索好奇餐饮管理有限公司", "textField_mbc1lbzm": "10164L23CO0041", "textField_m9jkl9nx": "否"}
2025-07-18 09:02:33,185 - INFO - 正在处理第 12 条更新记录 - store_code: 100101233
2025-07-18 09:02:33,201 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州IFC国金天地", "textField_m911r3pn": "100101233", "textField_m8e8g3lu": "星巴克（B1）", "employeeField_m8e8g3lw": ["16d2416f00a04889068d42e44b182785"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "101190", "textField_mb7rs39i": "广东星巴克咖啡有限公司", "textField_mbc1lbzm": "10164L23CO0042", "textField_m9jkl9nx": "否"}
2025-07-18 09:02:33,201 - INFO - 正在处理第 13 条更新记录 - store_code: 100101278
2025-07-18 09:02:33,201 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州IFC国金天地", "textField_m911r3pn": "100101278", "textField_m8e8g3lu": "一年四季24小时智能健身", "employeeField_m8e8g3lw": ["16d2416c64282711fb71a184c10997bb"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10164L25CO0023", "textField_m9jkl9nx": "是"}
2025-07-18 09:02:33,201 - INFO - 正在处理第 14 条更新记录 - store_code: 100101291
2025-07-18 09:02:33,201 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州IFC国金天地", "textField_m911r3pn": "100101291", "textField_m8e8g3lu": "常乐对症推拿", "employeeField_m8e8g3lw": ["16d2416c64282711fb71a184c10997bb"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10164L25CO0021", "textField_m9jkl9nx": "是"}
2025-07-18 09:02:33,201 - INFO - 正在处理第 15 条更新记录 - store_code: 100099610
2025-07-18 09:02:33,201 - INFO - 转换后的数据: {"selectField_m886ps6o": "武汉星汇维港", "textField_m911r3pn": "100099610", "textField_m8e8g3lu": "今天", "employeeField_m8e8g3lw": ["16d2416e996837cef3e7d79485192fc3"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "101816", "textField_mb7rs39i": "武汉今天梦想商贸有限公司", "textField_mbc1lbzm": "10630L25CO0026", "textField_m9jkl9nx": "否"}
2025-07-18 09:02:33,201 - INFO - 正在处理第 16 条更新记录 - store_code: 100099652
2025-07-18 09:02:33,201 - INFO - 转换后的数据: {"selectField_m886ps6o": "武汉星汇维港", "textField_m911r3pn": "100099652", "textField_m8e8g3lu": "海王星辰", "employeeField_m8e8g3lw": ["188227e479184e0b0a1d7234cc0830b0"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103242", "textField_mb7rs39i": "武汉海王星辰医药连锁有限公司星汇云锦店", "textField_mbc1lbzm": "10630L25CO0024", "textField_m9jkl9nx": "否"}
2025-07-18 09:02:33,201 - INFO - 处理剩余 16 条更新记录
2025-07-18 09:02:33,966 - INFO - 批量更新表单数据成功: 
2025-07-18 09:02:34,498 - INFO - 批量更新表单数据成功: 
2025-07-18 09:02:35,091 - INFO - 批量更新表单数据成功: 
2025-07-18 09:02:35,779 - INFO - 批量更新表单数据成功: 
2025-07-18 09:02:36,373 - INFO - 批量更新表单数据成功: 
2025-07-18 09:02:36,841 - INFO - 批量更新表单数据成功: 
2025-07-18 09:02:37,482 - INFO - 批量更新表单数据成功: 
2025-07-18 09:02:38,013 - INFO - 批量更新表单数据成功: 
2025-07-18 09:02:38,544 - INFO - 批量更新表单数据成功: 
2025-07-18 09:02:39,169 - INFO - 批量更新表单数据成功: 
2025-07-18 09:02:39,810 - INFO - 批量更新表单数据成功: 
2025-07-18 09:02:40,466 - INFO - 批量更新表单数据成功: 
2025-07-18 09:02:41,013 - INFO - 批量更新表单数据成功: 
2025-07-18 09:02:41,591 - INFO - 批量更新表单数据成功: 
2025-07-18 09:02:42,310 - INFO - 批量更新表单数据成功: 
2025-07-18 09:02:42,888 - INFO - 批量更新表单数据成功: 
2025-07-18 09:02:42,888 - INFO - 批量更新成功，form_instance_ids: ['FINST-3PF66V71E5VVUFXD6URWS5AZCEAX2MKHADCBM88', 'FINST-S0E660A1Z9VV01KQEWH79D4BKI5D2S9IADCBM3A', 'FINST-NS866I91WC6X888FBC0J25Q6ZRI62PPAOO6DMKL', 'FINST-9EA669D1BGVVNOZG7TCCP8XTN9SE24HKADCBMO3', 'FINST-MLF66PA1KFUVIJJUA4YPK5DDN68B380HADCBML1', 'FINST-MLF66PA1KFUVIJJUA4YPK5DDN68B380HADCBMN1', 'FINST-MLF66PA1KFUVIJJUA4YPK5DDN68B380HADCBMU1', 'FINST-MLF66PA1KFUVIJJUA4YPK5DDN68B380HADCBM42', 'FINST-MLF66PA1KFUVIJJUA4YPK5DDN68B380HADCBM82', 'FINST-MLF66PA1KFUVIJJUA4YPK5DDN68B380HADCBMB2', 'FINST-MLF66PA1KFUVIJJUA4YPK5DDN68B390HADCBMJ2', 'FINST-49866E71HEVVLI2WBRBVL4QQNEJL3E7HADCBMJA', 'FINST-49866E71HEVVLI2WBRBVL4QQNEJL3E7HADCBMLA', 'FINST-49866E71HEVVLI2WBRBVL4QQNEJL3E7HADCBMMA', 'FINST-487664C1TGSVYJ1LFH8U5C8QWPNS2Y5LADCBMEO', 'FINST-487664C1TGSVYJ1LFH8U5C8QWPNS2Y5LADCBM6P']
2025-07-18 09:02:42,888 - INFO - 宜搭表单更新完成
2025-07-18 09:02:42,888 - INFO - 数据处理完成
2025-07-18 09:02:42,888 - INFO - 数据库连接已关闭
