2025-06-23 01:30:33,735 - INFO - 使用默认增量同步（当天更新数据）
2025-06-23 01:30:33,735 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-23 01:30:33,735 - INFO - 查询参数: ('2025-06-23',)
2025-06-23 01:30:33,860 - INFO - MySQL查询成功，增量数据（日期: 2025-06-23），共获取 1 条记录
2025-06-23 01:30:33,860 - INFO - 获取到 1 个日期需要处理: ['2025-06-22']
2025-06-23 01:30:33,860 - INFO - 开始处理日期: 2025-06-22
2025-06-23 01:30:33,875 - INFO - Request Parameters - Page 1:
2025-06-23 01:30:33,875 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 01:30:33,875 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 01:30:41,985 - ERROR - 处理日期 2025-06-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B8A0E765-AB81-7350-8275-2826D6D6B387 Response: {'code': 'ServiceUnavailable', 'requestid': 'B8A0E765-AB81-7350-8275-2826D6D6B387', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B8A0E765-AB81-7350-8275-2826D6D6B387)
2025-06-23 01:30:41,985 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-23 01:31:42,000 - INFO - 开始同步昨天与今天的销售数据: 2025-06-22 至 2025-06-23
2025-06-23 01:31:42,000 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-23 01:31:42,000 - INFO - 查询参数: ('2025-06-22', '2025-06-23')
2025-06-23 01:31:42,125 - INFO - MySQL查询成功，时间段: 2025-06-22 至 2025-06-23，共获取 94 条记录
2025-06-23 01:31:42,125 - INFO - 获取到 1 个日期需要处理: ['2025-06-22']
2025-06-23 01:31:42,125 - INFO - 开始处理日期: 2025-06-22
2025-06-23 01:31:42,125 - INFO - Request Parameters - Page 1:
2025-06-23 01:31:42,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 01:31:42,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 01:31:42,687 - INFO - Response - Page 1:
2025-06-23 01:31:42,687 - INFO - 第 1 页获取到 16 条记录
2025-06-23 01:31:43,203 - INFO - 查询完成，共获取到 16 条记录
2025-06-23 01:31:43,203 - INFO - 获取到 16 条表单数据
2025-06-23 01:31:43,203 - INFO - 当前日期 2025-06-22 有 90 条MySQL数据需要处理
2025-06-23 01:31:43,203 - INFO - 开始批量插入 74 条新记录
2025-06-23 01:31:43,437 - INFO - 批量插入响应状态码: 200
2025-06-23 01:31:43,437 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 22 Jun 2025 17:31:43 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'EAA0D084-015E-7420-97FE-2A76701764F3', 'x-acs-trace-id': '78eecd4de334c04443326ed89933226b', 'etag': '2yCTaHYbjhTs51wlEaSWCng2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-23 01:31:43,437 - INFO - 批量插入响应体: {'result': ['FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CMP9', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CMQ9', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CMR9', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CMS9', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CMT9', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CMU9', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CMV9', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CMW9', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CMX9', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CMY9', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CMZ9', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CM0A', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CM1A', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CM2A', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CM3A', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CM4A', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CM5A', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CM6A', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CM7A', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CM8A', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CM9A', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CMAA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CMBA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CMCA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CMDA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMEA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMFA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMGA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMHA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMIA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMJA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMKA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMLA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMMA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMNA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMOA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMPA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMQA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMRA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMSA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMTA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMUA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMVA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMWA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMXA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMYA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMZA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CM0B', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CM1B', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CM2B']}
2025-06-23 01:31:43,437 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-23 01:31:43,437 - INFO - 成功插入的数据ID: ['FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CMP9', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CMQ9', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CMR9', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CMS9', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CMT9', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CMU9', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CMV9', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CMW9', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CMX9', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CMY9', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CMZ9', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CM0A', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CM1A', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CM2A', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CM3A', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CM4A', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CM5A', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CM6A', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CM7A', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CM8A', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CM9A', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CMAA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CMBA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CMCA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2KAW2Y7CMDA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMEA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMFA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMGA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMHA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMIA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMJA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMKA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMLA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMMA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMNA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMOA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMPA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMQA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMRA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMSA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMTA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMUA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMVA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMWA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMXA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMYA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CMZA', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CM0B', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CM1B', 'FINST-OLF66581APIW6I2CC5GKP6SU2GLI2LAW2Y7CM2B']
2025-06-23 01:31:48,640 - INFO - 批量插入响应状态码: 200
2025-06-23 01:31:48,640 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 22 Jun 2025 17:31:48 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1164', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '61258F47-CBB5-7DAC-A8D6-19F5436EECF1', 'x-acs-trace-id': '034ae33680f3abe9cbb83f6a8b64be20', 'etag': '1dSPeQ6I8dvFdeKLCx/rpFA4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-23 01:31:48,640 - INFO - 批量插入响应体: {'result': ['FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CMTM', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CMUM', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CMVM', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CMWM', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CMXM', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CMYM', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CMZM', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CM0N', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CM1N', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CM2N', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CM3N', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CM4N', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CM5N', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CM6N', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CM7N', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CM8N', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CM9N', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CMAN', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CMBN', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CMCN', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CMDN', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CMEN', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CMFN', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CMGN']}
2025-06-23 01:31:48,640 - INFO - 批量插入表单数据成功，批次 2，共 24 条记录
2025-06-23 01:31:48,640 - INFO - 成功插入的数据ID: ['FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CMTM', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CMUM', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CMVM', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CMWM', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CMXM', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CMYM', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CMZM', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CM0N', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CM1N', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CM2N', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CM3N', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CM4N', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CM5N', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CM6N', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CM7N', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CM8N', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CM9N', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CMAN', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CMBN', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CMCN', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CMDN', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CMEN', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CMFN', 'FINST-VRA66VA1RZHWPQY46CZPY5ACRMX923B03Y7CMGN']
2025-06-23 01:31:53,656 - INFO - 批量插入完成，共 74 条记录
2025-06-23 01:31:53,656 - INFO - 日期 2025-06-22 处理完成 - 更新: 0 条，插入: 74 条，错误: 0 条
2025-06-23 01:31:53,656 - INFO - 数据同步完成！更新: 0 条，插入: 74 条，错误: 0 条
2025-06-23 01:31:53,656 - INFO - 同步完成
2025-06-23 04:30:33,755 - INFO - 使用默认增量同步（当天更新数据）
2025-06-23 04:30:33,755 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-23 04:30:33,755 - INFO - 查询参数: ('2025-06-23',)
2025-06-23 04:30:33,896 - INFO - MySQL查询成功，增量数据（日期: 2025-06-23），共获取 1 条记录
2025-06-23 04:30:33,896 - INFO - 获取到 1 个日期需要处理: ['2025-06-22']
2025-06-23 04:30:33,896 - INFO - 开始处理日期: 2025-06-22
2025-06-23 04:30:33,896 - INFO - Request Parameters - Page 1:
2025-06-23 04:30:33,896 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 04:30:33,896 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 04:30:42,021 - ERROR - 处理日期 2025-06-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 0D8562C0-9BE9-7421-B6F2-EB15A0DA84B7 Response: {'code': 'ServiceUnavailable', 'requestid': '0D8562C0-9BE9-7421-B6F2-EB15A0DA84B7', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 0D8562C0-9BE9-7421-B6F2-EB15A0DA84B7)
2025-06-23 04:30:42,021 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-23 04:31:42,036 - INFO - 开始同步昨天与今天的销售数据: 2025-06-22 至 2025-06-23
2025-06-23 04:31:42,036 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-23 04:31:42,036 - INFO - 查询参数: ('2025-06-22', '2025-06-23')
2025-06-23 04:31:42,161 - INFO - MySQL查询成功，时间段: 2025-06-22 至 2025-06-23，共获取 94 条记录
2025-06-23 04:31:42,161 - INFO - 获取到 1 个日期需要处理: ['2025-06-22']
2025-06-23 04:31:42,161 - INFO - 开始处理日期: 2025-06-22
2025-06-23 04:31:42,161 - INFO - Request Parameters - Page 1:
2025-06-23 04:31:42,161 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 04:31:42,161 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 04:31:42,911 - INFO - Response - Page 1:
2025-06-23 04:31:42,911 - INFO - 第 1 页获取到 50 条记录
2025-06-23 04:31:43,411 - INFO - Request Parameters - Page 2:
2025-06-23 04:31:43,411 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 04:31:43,411 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 04:31:44,052 - INFO - Response - Page 2:
2025-06-23 04:31:44,052 - INFO - 第 2 页获取到 40 条记录
2025-06-23 04:31:44,567 - INFO - 查询完成，共获取到 90 条记录
2025-06-23 04:31:44,567 - INFO - 获取到 90 条表单数据
2025-06-23 04:31:44,567 - INFO - 当前日期 2025-06-22 有 90 条MySQL数据需要处理
2025-06-23 04:31:44,567 - INFO - 日期 2025-06-22 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-23 04:31:44,567 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-23 04:31:44,567 - INFO - 同步完成
2025-06-23 07:30:33,557 - INFO - 使用默认增量同步（当天更新数据）
2025-06-23 07:30:33,557 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-23 07:30:33,557 - INFO - 查询参数: ('2025-06-23',)
2025-06-23 07:30:33,682 - INFO - MySQL查询成功，增量数据（日期: 2025-06-23），共获取 2 条记录
2025-06-23 07:30:33,682 - INFO - 获取到 1 个日期需要处理: ['2025-06-22']
2025-06-23 07:30:33,682 - INFO - 开始处理日期: 2025-06-22
2025-06-23 07:30:33,682 - INFO - Request Parameters - Page 1:
2025-06-23 07:30:33,682 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 07:30:33,682 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 07:30:41,792 - ERROR - 处理日期 2025-06-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2BFC7B38-5A29-7F20-B03B-617EF6F6EFDD Response: {'code': 'ServiceUnavailable', 'requestid': '2BFC7B38-5A29-7F20-B03B-617EF6F6EFDD', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2BFC7B38-5A29-7F20-B03B-617EF6F6EFDD)
2025-06-23 07:30:41,792 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-23 07:31:41,807 - INFO - 开始同步昨天与今天的销售数据: 2025-06-22 至 2025-06-23
2025-06-23 07:31:41,807 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-23 07:31:41,807 - INFO - 查询参数: ('2025-06-22', '2025-06-23')
2025-06-23 07:31:41,932 - INFO - MySQL查询成功，时间段: 2025-06-22 至 2025-06-23，共获取 95 条记录
2025-06-23 07:31:41,932 - INFO - 获取到 1 个日期需要处理: ['2025-06-22']
2025-06-23 07:31:41,932 - INFO - 开始处理日期: 2025-06-22
2025-06-23 07:31:41,932 - INFO - Request Parameters - Page 1:
2025-06-23 07:31:41,932 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 07:31:41,932 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 07:31:42,666 - INFO - Response - Page 1:
2025-06-23 07:31:42,666 - INFO - 第 1 页获取到 50 条记录
2025-06-23 07:31:43,182 - INFO - Request Parameters - Page 2:
2025-06-23 07:31:43,182 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 07:31:43,182 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 07:31:43,807 - INFO - Response - Page 2:
2025-06-23 07:31:43,807 - INFO - 第 2 页获取到 40 条记录
2025-06-23 07:31:44,307 - INFO - 查询完成，共获取到 90 条记录
2025-06-23 07:31:44,307 - INFO - 获取到 90 条表单数据
2025-06-23 07:31:44,307 - INFO - 当前日期 2025-06-22 有 91 条MySQL数据需要处理
2025-06-23 07:31:44,307 - INFO - 开始批量插入 1 条新记录
2025-06-23 07:31:44,447 - INFO - 批量插入响应状态码: 200
2025-06-23 07:31:44,447 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 22 Jun 2025 23:31:44 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '61', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5BA1AA08-ABF7-7D28-BD74-CF3BCE5CDFBD', 'x-acs-trace-id': 'ef3357ea59e97c52fad8e98345be9936', 'etag': '6Kq2cCCNMWs7FsKsEqsoq2Q1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-23 07:31:44,447 - INFO - 批量插入响应体: {'result': ['FINST-VRA66VA1PZHWLA14AKVZIB4ACIEA3VUVXA8CMB41']}
2025-06-23 07:31:44,447 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-23 07:31:44,447 - INFO - 成功插入的数据ID: ['FINST-VRA66VA1PZHWLA14AKVZIB4ACIEA3VUVXA8CMB41']
2025-06-23 07:31:49,463 - INFO - 批量插入完成，共 1 条记录
2025-06-23 07:31:49,463 - INFO - 日期 2025-06-22 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-23 07:31:49,463 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 0 条
2025-06-23 07:31:49,463 - INFO - 同步完成
2025-06-23 10:30:33,579 - INFO - 使用默认增量同步（当天更新数据）
2025-06-23 10:30:33,579 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-23 10:30:33,579 - INFO - 查询参数: ('2025-06-23',)
2025-06-23 10:30:33,720 - INFO - MySQL查询成功，增量数据（日期: 2025-06-23），共获取 119 条记录
2025-06-23 10:30:33,720 - INFO - 获取到 2 个日期需要处理: ['2025-06-21', '2025-06-22']
2025-06-23 10:30:33,720 - INFO - 开始处理日期: 2025-06-21
2025-06-23 10:30:33,720 - INFO - Request Parameters - Page 1:
2025-06-23 10:30:33,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 10:30:33,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 10:30:39,720 - INFO - Response - Page 1:
2025-06-23 10:30:39,720 - INFO - 第 1 页获取到 50 条记录
2025-06-23 10:30:40,235 - INFO - Request Parameters - Page 2:
2025-06-23 10:30:40,235 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 10:30:40,235 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 10:30:48,344 - ERROR - 处理日期 2025-06-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E3D8D388-8AC5-718E-83E0-F618FBE489B4 Response: {'code': 'ServiceUnavailable', 'requestid': 'E3D8D388-8AC5-718E-83E0-F618FBE489B4', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E3D8D388-8AC5-718E-83E0-F618FBE489B4)
2025-06-23 10:30:48,344 - INFO - 开始处理日期: 2025-06-22
2025-06-23 10:30:48,344 - INFO - Request Parameters - Page 1:
2025-06-23 10:30:48,344 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 10:30:48,344 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 10:30:49,063 - INFO - Response - Page 1:
2025-06-23 10:30:49,063 - INFO - 第 1 页获取到 50 条记录
2025-06-23 10:30:49,563 - INFO - Request Parameters - Page 2:
2025-06-23 10:30:49,563 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 10:30:49,563 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 10:30:50,798 - INFO - Response - Page 2:
2025-06-23 10:30:50,798 - INFO - 第 2 页获取到 41 条记录
2025-06-23 10:30:51,298 - INFO - 查询完成，共获取到 91 条记录
2025-06-23 10:30:51,298 - INFO - 获取到 91 条表单数据
2025-06-23 10:30:51,298 - INFO - 当前日期 2025-06-22 有 118 条MySQL数据需要处理
2025-06-23 10:30:51,298 - INFO - 开始批量插入 116 条新记录
2025-06-23 10:30:51,563 - INFO - 批量插入响应状态码: 200
2025-06-23 10:30:51,563 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 23 Jun 2025 02:30:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2380', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B9B6C487-9990-765C-99DA-FE460B68090E', 'x-acs-trace-id': '73b5bf90c4f65f3135a2d522f55dbfd9', 'etag': '2BO35QJON9VLbKuoBOhDGKw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-23 10:30:51,563 - INFO - 批量插入响应体: {'result': ['FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CM4', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CM5', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CM6', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CM7', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CM8', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CM9', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CMA', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CMB', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CMC', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CMD', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CME', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CMF', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CMG', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CMH', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CMI', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CMJ', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CMK', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CML', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CMM', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CMN', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CMO', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMP', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMQ', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMR', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMS', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMT', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMU', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMV', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMW', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMX', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMY', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMZ', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CM01', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CM11', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CM21', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CM31', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CM41', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CM51', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CM61', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CM71', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CM81', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CM91', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMA1', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMB1', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMC1', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMD1', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CME1', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMF1', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMG1', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMH1']}
2025-06-23 10:30:51,563 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-23 10:30:51,563 - INFO - 成功插入的数据ID: ['FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CM4', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CM5', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CM6', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CM7', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CM8', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CM9', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CMA', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CMB', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CMC', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CMD', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CME', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CMF', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CMG', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CMH', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CMI', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CMJ', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CMK', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CML', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CMM', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CMN', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q939F8CH8CMO', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMP', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMQ', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMR', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMS', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMT', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMU', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMV', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMW', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMX', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMY', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMZ', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CM01', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CM11', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CM21', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CM31', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CM41', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CM51', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CM61', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CM71', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CM81', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CM91', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMA1', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMB1', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMC1', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMD1', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CME1', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMF1', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMG1', 'FINST-3PF66X617NJW8NFSFM2TKCZ0G2Q93AF8CH8CMH1']
2025-06-23 10:30:56,782 - INFO - 批量插入响应状态码: 200
2025-06-23 10:30:56,782 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 23 Jun 2025 02:30:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'FE6FC702-69AC-7DBD-A7D7-2E5C47C50288', 'x-acs-trace-id': 'a2cd2a5b90ca488bcf53be08e13f0e31', 'etag': '2ZE4MlzOQoVQkP55MgRIsgQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-23 10:30:56,782 - INFO - 批量插入响应体: {'result': ['FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM0E', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM1E', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM2E', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM3E', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM4E', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM5E', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM6E', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM7E', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM8E', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM9E', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMAE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMBE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMCE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMDE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMEE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMFE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMGE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMHE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMIE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMJE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMKE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMLE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMME', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMNE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMOE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMPE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMQE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMRE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMSE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMTE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMUE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMVE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMWE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMXE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMYE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMZE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM0F', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM1F', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM2F', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM3F', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM4F', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM5F', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM6F', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM7F', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM8F', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM9F', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMAF', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMBF', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMCF', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMDF']}
2025-06-23 10:30:56,782 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-06-23 10:30:56,782 - INFO - 成功插入的数据ID: ['FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM0E', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM1E', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM2E', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM3E', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM4E', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM5E', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM6E', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM7E', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM8E', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM9E', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMAE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMBE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMCE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMDE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMEE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMFE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMGE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMHE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMIE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMJE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMKE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMLE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMME', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMNE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMOE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMPE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMQE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMRE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMSE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMTE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMUE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMVE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMWE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMXE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMYE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMZE', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM0F', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM1F', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM2F', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM3F', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM4F', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM5F', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM6F', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM7F', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM8F', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CM9F', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMAF', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMBF', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMCF', 'FINST-T9D66B81HZHW8VH6FWO8Q9TKT3CO3MGCCH8CMDF']
2025-06-23 10:31:01,954 - INFO - 批量插入响应状态码: 200
2025-06-23 10:31:01,954 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 23 Jun 2025 02:31:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '764', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '9115EAA3-CED5-7FE1-AC89-268A758752B3', 'x-acs-trace-id': 'ae872c33106de2ffdd705b0e5d469c57', 'etag': '7G3PkKCxikdgBJAkDMv6E6w4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-23 10:31:01,954 - INFO - 批量插入响应体: {'result': ['FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CMF', 'FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CMG', 'FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CMH', 'FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CMI', 'FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CMJ', 'FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CMK', 'FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CML', 'FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CMM', 'FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CMN', 'FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CMO', 'FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CMP', 'FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CMQ', 'FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CMR', 'FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CMS', 'FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CMT', 'FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CMU']}
2025-06-23 10:31:01,954 - INFO - 批量插入表单数据成功，批次 3，共 16 条记录
2025-06-23 10:31:01,954 - INFO - 成功插入的数据ID: ['FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CMF', 'FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CMG', 'FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CMH', 'FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CMI', 'FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CMJ', 'FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CMK', 'FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CML', 'FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CMM', 'FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CMN', 'FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CMO', 'FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CMP', 'FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CMQ', 'FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CMR', 'FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CMS', 'FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CMT', 'FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CMU']
2025-06-23 10:31:06,969 - INFO - 批量插入完成，共 116 条记录
2025-06-23 10:31:06,969 - INFO - 日期 2025-06-22 处理完成 - 更新: 0 条，插入: 116 条，错误: 0 条
2025-06-23 10:31:06,969 - INFO - 数据同步完成！更新: 0 条，插入: 116 条，错误: 1 条
2025-06-23 10:32:06,985 - INFO - 开始同步昨天与今天的销售数据: 2025-06-22 至 2025-06-23
2025-06-23 10:32:06,985 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-23 10:32:06,985 - INFO - 查询参数: ('2025-06-22', '2025-06-23')
2025-06-23 10:32:07,125 - INFO - MySQL查询成功，时间段: 2025-06-22 至 2025-06-23，共获取 485 条记录
2025-06-23 10:32:07,125 - INFO - 获取到 1 个日期需要处理: ['2025-06-22']
2025-06-23 10:32:07,125 - INFO - 开始处理日期: 2025-06-22
2025-06-23 10:32:07,125 - INFO - Request Parameters - Page 1:
2025-06-23 10:32:07,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 10:32:07,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 10:32:07,828 - INFO - Response - Page 1:
2025-06-23 10:32:07,828 - INFO - 第 1 页获取到 50 条记录
2025-06-23 10:32:08,328 - INFO - Request Parameters - Page 2:
2025-06-23 10:32:08,328 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 10:32:08,328 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 10:32:08,969 - INFO - Response - Page 2:
2025-06-23 10:32:08,969 - INFO - 第 2 页获取到 50 条记录
2025-06-23 10:32:09,469 - INFO - Request Parameters - Page 3:
2025-06-23 10:32:09,469 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 10:32:09,469 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 10:32:10,172 - INFO - Response - Page 3:
2025-06-23 10:32:10,172 - INFO - 第 3 页获取到 50 条记录
2025-06-23 10:32:10,688 - INFO - Request Parameters - Page 4:
2025-06-23 10:32:10,688 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 10:32:10,688 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 10:32:11,313 - INFO - Response - Page 4:
2025-06-23 10:32:11,313 - INFO - 第 4 页获取到 50 条记录
2025-06-23 10:32:11,828 - INFO - Request Parameters - Page 5:
2025-06-23 10:32:11,828 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 10:32:11,828 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 10:32:12,313 - INFO - Response - Page 5:
2025-06-23 10:32:12,313 - INFO - 第 5 页获取到 7 条记录
2025-06-23 10:32:12,828 - INFO - 查询完成，共获取到 207 条记录
2025-06-23 10:32:12,828 - INFO - 获取到 207 条表单数据
2025-06-23 10:32:12,828 - INFO - 当前日期 2025-06-22 有 468 条MySQL数据需要处理
2025-06-23 10:32:12,828 - INFO - 开始更新记录 - 表单实例ID: FINST-I3F66991VZHW4W4Z5HEZ7BH4K3SP3PLOB87CMK6
2025-06-23 10:32:13,297 - INFO - 更新表单数据成功: FINST-I3F66991VZHW4W4Z5HEZ7BH4K3SP3PLOB87CMK6
2025-06-23 10:32:13,297 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1170.0, 'new_value': 637.0}, {'field': 'total_amount', 'old_value': 1170.0, 'new_value': 637.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/0519e32e965a434f96733a742bab800f.jpg?Expires=2060151182&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=UMnkVE1wYstBB99iiyAKq8L%2FR0o%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/a9c9a6cc5cd244169bad380c8c69c63d.jpg?Expires=2060149888&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=9GNMyWW1hem52nm9d%2FyICtAfEp8%3D'}]
2025-06-23 10:32:13,297 - INFO - 开始批量插入 261 条新记录
2025-06-23 10:32:13,563 - INFO - 批量插入响应状态码: 200
2025-06-23 10:32:13,563 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 23 Jun 2025 02:32:13 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2406', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0A3AC152-56C9-7AE0-BC9D-9AAE19F2123F', 'x-acs-trace-id': '1e11ebd81b2e60732bc01122805f058b', 'etag': '26zdKLtUlRpQpXUStjAr/Tw6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-23 10:32:13,563 - INFO - 批量插入响应体: {'result': ['FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMU', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMV', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMW', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMX', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMY', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMZ', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM01', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM11', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM21', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM31', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM41', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM51', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM61', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM71', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM81', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM91', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMA1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMB1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMC1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMD1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CME1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMF1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMG1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMH1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMI1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMJ1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMK1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CML1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMM1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMN1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMO1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMP1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMQ1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMR1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMS1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMT1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMU1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMV1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMW1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMX1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMY1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMZ1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM02', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM12', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM22', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM32', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM42', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM52', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM62', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM72']}
2025-06-23 10:32:13,563 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-23 10:32:13,563 - INFO - 成功插入的数据ID: ['FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMU', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMV', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMW', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMX', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMY', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMZ', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM01', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM11', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM21', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM31', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM41', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM51', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM61', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM71', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM81', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM91', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMA1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMB1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMC1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMD1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CME1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMF1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMG1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMH1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMI1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMJ1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMK1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CML1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMM1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMN1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMO1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMP1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMQ1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMR1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMS1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMT1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMU1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMV1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMW1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMX1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMY1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CMZ1', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM02', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM12', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM22', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM32', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM42', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM52', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM62', 'FINST-UW966371NLJWROIPBP4J2DNT4QP82VOZDH8CM72']
2025-06-23 10:32:18,781 - INFO - 批量插入响应状态码: 200
2025-06-23 10:32:18,781 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 23 Jun 2025 02:32:19 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2396', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '262ADC69-32E2-7BDF-B1D6-7EC0487DFDB8', 'x-acs-trace-id': 'c079b106722994e516a3418079999e0f', 'etag': '20QVogUzEBxhNKepDgmHZXw6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-23 10:32:18,781 - INFO - 批量插入响应体: {'result': ['FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMK', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CML', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMM', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMN', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMO', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMP', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMQ', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMR', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMS', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMT', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMU', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMV', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMW', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMX', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMY', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMZ', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CM01', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CM11', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CM21', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CM31', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CM41', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CM51', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CM61', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CM71', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CM81', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CM91', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMA1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMB1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMC1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMD1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CME1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMF1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMG1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMH1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMI1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMJ1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMK1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CML1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMM1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMN1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMO1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMP1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMQ1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMR1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMS1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMT1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMU1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMV1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMW1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMX1']}
2025-06-23 10:32:18,781 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-06-23 10:32:18,781 - INFO - 成功插入的数据ID: ['FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMK', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CML', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMM', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMN', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMO', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMP', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMQ', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMR', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMS', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMT', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMU', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMV', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMW', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMX', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMY', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMZ', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CM01', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CM11', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CM21', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CM31', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CM41', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CM51', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CM61', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CM71', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CM81', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CM91', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMA1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMB1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMC1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMD1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CME1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMF1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMG1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMH1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMI1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMJ1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMK1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CML1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMM1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMN1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMO1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMP1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMQ1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMR1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMS1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMT1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMU1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMV1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMW1', 'FINST-LR5668B1SLJWDMPN8UGP959XL7692IQ3EH8CMX1']
2025-06-23 10:32:24,031 - INFO - 批量插入响应状态码: 200
2025-06-23 10:32:24,031 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 23 Jun 2025 02:32:24 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2401', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7BE879B7-90A4-7D1B-BA2C-19AC5663F2E3', 'x-acs-trace-id': 'c88c679d01e53f1f8131c3b19c88a94d', 'etag': '2RvsF4pJLXzFo1u20UGU2Fw1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-23 10:32:24,031 - INFO - 批量插入响应体: {'result': ['FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMP', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMQ', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMR', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMS', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMT', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMU', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMV', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMW', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMX', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMY', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMZ', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CM01', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CM11', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CM21', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CM31', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CM41', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CM51', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CM61', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CM71', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CM81', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CM91', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMA1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMB1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMC1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMD1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CME1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMF1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMG1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMH1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMI1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMJ1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMK1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CML1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMM1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMN1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMO1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMP1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMQ1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMR1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMS1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMT1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMU1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMV1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMW1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMX1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMY1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMZ1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CM02', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CM12', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CM22']}
2025-06-23 10:32:24,031 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-06-23 10:32:24,031 - INFO - 成功插入的数据ID: ['FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMP', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMQ', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMR', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMS', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMT', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMU', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMV', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMW', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMX', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMY', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMZ', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CM01', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CM11', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CM21', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CM31', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CM41', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CM51', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CM61', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CM71', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CM81', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CM91', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMA1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMB1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMC1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMD1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CME1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMF1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMG1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMH1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMI1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMJ1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMK1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CML1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMM1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMN1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMO1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMP1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMQ1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMR1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMS1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMT1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMU1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMV1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMW1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMX1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMY1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CMZ1', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CM02', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CM12', 'FINST-7PF66BA17YIWS9GME05YY9LSD85E37S7EH8CM22']
2025-06-23 10:32:29,266 - INFO - 批量插入响应状态码: 200
2025-06-23 10:32:29,266 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 23 Jun 2025 02:32:29 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2393', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'A9FF7F00-E500-7C5F-8A01-2EF99EA72DA5', 'x-acs-trace-id': '379c061ee876244090d842a684221de1', 'etag': '2jUYjULOqa9dW+j/9yCtqdg3', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-23 10:32:29,266 - INFO - 批量插入响应体: {'result': ['FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53ETBEH8CMH', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53ETBEH8CMI', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53ETBEH8CMJ', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMK', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CML', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMM', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMN', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMO', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMP', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMQ', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMR', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMS', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMT', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMU', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMV', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMW', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMX', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMY', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMZ', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CM01', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CM11', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CM21', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CM31', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CM41', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CM51', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CM61', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CM71', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CM81', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CM91', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMA1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMB1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMC1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMD1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CME1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMF1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMG1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMH1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMI1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMJ1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMK1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CML1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMM1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMN1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMO1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMP1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMQ1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMR1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMS1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMT1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMU1']}
2025-06-23 10:32:29,266 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-06-23 10:32:29,266 - INFO - 成功插入的数据ID: ['FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53ETBEH8CMH', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53ETBEH8CMI', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53ETBEH8CMJ', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMK', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CML', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMM', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMN', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMO', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMP', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMQ', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMR', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMS', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMT', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMU', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMV', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMW', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMX', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMY', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMZ', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CM01', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CM11', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CM21', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CM31', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CM41', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CM51', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CM61', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CM71', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CM81', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CM91', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMA1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMB1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMC1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMD1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CME1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMF1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMG1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMH1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMI1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMJ1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMK1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CML1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMM1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMN1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMO1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMP1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMQ1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMR1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMS1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMT1', 'FINST-0KG66Q71HLJWDN9M6HXPU5IYFXA53FTBEH8CMU1']
2025-06-23 10:32:34,484 - INFO - 批量插入响应状态码: 200
2025-06-23 10:32:34,500 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 23 Jun 2025 02:32:34 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'CE875576-81F6-7567-86AF-9D5D2FD98BCF', 'x-acs-trace-id': '3c04fc8f64bec0bff58c7cc8d88653e7', 'etag': '2DDOqybAikQ6oICnWnzo17Q2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-23 10:32:34,500 - INFO - 批量插入响应体: {'result': ['FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMRD', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMSD', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMTD', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMUD', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMVD', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMWD', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMXD', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMYD', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMZD', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CM0E', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CM1E', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CM2E', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CM3E', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CM4E', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CM5E', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CM6E', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CM7E', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CM8E', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CM9E', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMAE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMBE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMCE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMDE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMEE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMFE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMGE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMHE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMIE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMJE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMKE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMLE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMME', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMNE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMOE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMPE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMQE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMRE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMSE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMTE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMUE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMVE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMWE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMXE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMYE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMZE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CM0F', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CM1F', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CM2F', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CM3F', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CM4F']}
2025-06-23 10:32:34,500 - INFO - 批量插入表单数据成功，批次 5，共 50 条记录
2025-06-23 10:32:34,500 - INFO - 成功插入的数据ID: ['FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMRD', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMSD', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMTD', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMUD', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMVD', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMWD', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMXD', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMYD', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMZD', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CM0E', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CM1E', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CM2E', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CM3E', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CM4E', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CM5E', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CM6E', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CM7E', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CM8E', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CM9E', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMAE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMBE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMCE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMDE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMEE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMFE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMGE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMHE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMIE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMJE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMKE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMLE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMME', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMNE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMOE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMPE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMQE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMRE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMSE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMTE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMUE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMVE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMWE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMXE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMYE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CMZE', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CM0F', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CM1F', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CM2F', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CM3F', 'FINST-FD966QA1OZHWJFEQ8ZZSGBWA9FE33SUFEH8CM4F']
2025-06-23 10:32:39,719 - INFO - 批量插入响应状态码: 200
2025-06-23 10:32:39,719 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 23 Jun 2025 02:32:40 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '529', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'EBC237E5-2A5C-76ED-9612-F2FB4A59205F', 'x-acs-trace-id': '8c80d6a3c10f0115b37006a1db7a7997', 'etag': '53Fh5Ox6w9sKtpf+Tn2Ku0A9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-23 10:32:39,719 - INFO - 批量插入响应体: {'result': ['FINST-XRF66A81LLJW3IX7BVTKT4A4TEK73QUJEH8CMC', 'FINST-XRF66A81LLJW3IX7BVTKT4A4TEK73QUJEH8CMD', 'FINST-XRF66A81LLJW3IX7BVTKT4A4TEK73QUJEH8CME', 'FINST-XRF66A81LLJW3IX7BVTKT4A4TEK73QUJEH8CMF', 'FINST-XRF66A81LLJW3IX7BVTKT4A4TEK73QUJEH8CMG', 'FINST-XRF66A81LLJW3IX7BVTKT4A4TEK73QUJEH8CMH', 'FINST-XRF66A81LLJW3IX7BVTKT4A4TEK73QUJEH8CMI', 'FINST-XRF66A81LLJW3IX7BVTKT4A4TEK73QUJEH8CMJ', 'FINST-XRF66A81LLJW3IX7BVTKT4A4TEK73QUJEH8CMK', 'FINST-XRF66A81LLJW3IX7BVTKT4A4TEK73QUJEH8CML', 'FINST-XRF66A81LLJW3IX7BVTKT4A4TEK73QUJEH8CMM']}
2025-06-23 10:32:39,719 - INFO - 批量插入表单数据成功，批次 6，共 11 条记录
2025-06-23 10:32:39,719 - INFO - 成功插入的数据ID: ['FINST-XRF66A81LLJW3IX7BVTKT4A4TEK73QUJEH8CMC', 'FINST-XRF66A81LLJW3IX7BVTKT4A4TEK73QUJEH8CMD', 'FINST-XRF66A81LLJW3IX7BVTKT4A4TEK73QUJEH8CME', 'FINST-XRF66A81LLJW3IX7BVTKT4A4TEK73QUJEH8CMF', 'FINST-XRF66A81LLJW3IX7BVTKT4A4TEK73QUJEH8CMG', 'FINST-XRF66A81LLJW3IX7BVTKT4A4TEK73QUJEH8CMH', 'FINST-XRF66A81LLJW3IX7BVTKT4A4TEK73QUJEH8CMI', 'FINST-XRF66A81LLJW3IX7BVTKT4A4TEK73QUJEH8CMJ', 'FINST-XRF66A81LLJW3IX7BVTKT4A4TEK73QUJEH8CMK', 'FINST-XRF66A81LLJW3IX7BVTKT4A4TEK73QUJEH8CML', 'FINST-XRF66A81LLJW3IX7BVTKT4A4TEK73QUJEH8CMM']
2025-06-23 10:32:44,734 - INFO - 批量插入完成，共 261 条记录
2025-06-23 10:32:44,734 - INFO - 日期 2025-06-22 处理完成 - 更新: 1 条，插入: 261 条，错误: 0 条
2025-06-23 10:32:44,734 - INFO - 数据同步完成！更新: 1 条，插入: 261 条，错误: 0 条
2025-06-23 10:32:44,734 - INFO - 同步完成
2025-06-23 13:30:33,584 - INFO - 使用默认增量同步（当天更新数据）
2025-06-23 13:30:33,584 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-23 13:30:33,584 - INFO - 查询参数: ('2025-06-23',)
2025-06-23 13:30:33,709 - INFO - MySQL查询成功，增量数据（日期: 2025-06-23），共获取 125 条记录
2025-06-23 13:30:33,709 - INFO - 获取到 2 个日期需要处理: ['2025-06-21', '2025-06-22']
2025-06-23 13:30:33,709 - INFO - 开始处理日期: 2025-06-21
2025-06-23 13:30:33,725 - INFO - Request Parameters - Page 1:
2025-06-23 13:30:33,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 13:30:33,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 13:30:41,834 - ERROR - 处理日期 2025-06-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5C54F279-0220-712F-B161-D055BA4522EC Response: {'code': 'ServiceUnavailable', 'requestid': '5C54F279-0220-712F-B161-D055BA4522EC', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 5C54F279-0220-712F-B161-D055BA4522EC)
2025-06-23 13:30:41,834 - INFO - 开始处理日期: 2025-06-22
2025-06-23 13:30:41,834 - INFO - Request Parameters - Page 1:
2025-06-23 13:30:41,834 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 13:30:41,834 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 13:30:48,412 - INFO - Response - Page 1:
2025-06-23 13:30:48,412 - INFO - 第 1 页获取到 50 条记录
2025-06-23 13:30:48,928 - INFO - Request Parameters - Page 2:
2025-06-23 13:30:48,928 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 13:30:48,928 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 13:30:49,568 - INFO - Response - Page 2:
2025-06-23 13:30:49,568 - INFO - 第 2 页获取到 50 条记录
2025-06-23 13:30:50,084 - INFO - Request Parameters - Page 3:
2025-06-23 13:30:50,084 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 13:30:50,084 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 13:30:50,709 - INFO - Response - Page 3:
2025-06-23 13:30:50,709 - INFO - 第 3 页获取到 50 条记录
2025-06-23 13:30:51,225 - INFO - Request Parameters - Page 4:
2025-06-23 13:30:51,225 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 13:30:51,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 13:30:51,818 - INFO - Response - Page 4:
2025-06-23 13:30:51,818 - INFO - 第 4 页获取到 50 条记录
2025-06-23 13:30:52,334 - INFO - Request Parameters - Page 5:
2025-06-23 13:30:52,334 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 13:30:52,334 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 13:30:53,037 - INFO - Response - Page 5:
2025-06-23 13:30:53,037 - INFO - 第 5 页获取到 50 条记录
2025-06-23 13:30:53,553 - INFO - Request Parameters - Page 6:
2025-06-23 13:30:53,553 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 13:30:53,553 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 13:30:54,225 - INFO - Response - Page 6:
2025-06-23 13:30:54,225 - INFO - 第 6 页获取到 50 条记录
2025-06-23 13:30:54,740 - INFO - Request Parameters - Page 7:
2025-06-23 13:30:54,740 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 13:30:54,740 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 13:30:55,428 - INFO - Response - Page 7:
2025-06-23 13:30:55,428 - INFO - 第 7 页获取到 50 条记录
2025-06-23 13:30:55,928 - INFO - Request Parameters - Page 8:
2025-06-23 13:30:55,928 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 13:30:55,928 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 13:30:56,568 - INFO - Response - Page 8:
2025-06-23 13:30:56,568 - INFO - 第 8 页获取到 50 条记录
2025-06-23 13:30:57,084 - INFO - Request Parameters - Page 9:
2025-06-23 13:30:57,084 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 13:30:57,084 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 13:30:57,772 - INFO - Response - Page 9:
2025-06-23 13:30:57,772 - INFO - 第 9 页获取到 50 条记录
2025-06-23 13:30:58,272 - INFO - Request Parameters - Page 10:
2025-06-23 13:30:58,272 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 13:30:58,272 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 13:30:58,850 - INFO - Response - Page 10:
2025-06-23 13:30:58,850 - INFO - 第 10 页获取到 18 条记录
2025-06-23 13:30:59,365 - INFO - 查询完成，共获取到 468 条记录
2025-06-23 13:30:59,365 - INFO - 获取到 468 条表单数据
2025-06-23 13:30:59,365 - INFO - 当前日期 2025-06-22 有 123 条MySQL数据需要处理
2025-06-23 13:30:59,365 - INFO - 开始更新记录 - 表单实例ID: FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CMT
2025-06-23 13:30:59,959 - INFO - 更新表单数据成功: FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CMT
2025-06-23 13:30:59,959 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30000.0, 'new_value': 34224.12}, {'field': 'total_amount', 'old_value': 30000.0, 'new_value': 34224.12}, {'field': 'order_count', 'old_value': 50, 'new_value': 179}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/58289bd3a65b4dcfa8a1497334434057.png?Expires=2060151182&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=onn0hRA04A%2F6Qbz5KCnP4JFMXUA%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/02f38e20ac5e4e25ba4b78043668cfcd.jpg?Expires=2060151182&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=Jl4xxTkXdPryZglgz9YKBkP4nrA%3D'}]
2025-06-23 13:30:59,959 - INFO - 开始批量插入 4 条新记录
2025-06-23 13:31:00,100 - INFO - 批量插入响应状态码: 200
2025-06-23 13:31:00,100 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 23 Jun 2025 05:31:00 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '204', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '28F98EC0-EEBF-758F-8E71-A2A8EFF1D303', 'x-acs-trace-id': '3d2ff11fdafdb8fc2a12bafa72527ac5', 'etag': '2eCnfiS5cPiHkAOnFgKdFgg4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-23 13:31:00,100 - INFO - 批量插入响应体: {'result': ['FINST-W4G66DA1LLJW06LR9L2Q86WS0V5L3PEWRN8CM62', 'FINST-W4G66DA1LLJW06LR9L2Q86WS0V5L3PEWRN8CM72', 'FINST-W4G66DA1LLJW06LR9L2Q86WS0V5L3PEWRN8CM82', 'FINST-W4G66DA1LLJW06LR9L2Q86WS0V5L3PEWRN8CM92']}
2025-06-23 13:31:00,100 - INFO - 批量插入表单数据成功，批次 1，共 4 条记录
2025-06-23 13:31:00,100 - INFO - 成功插入的数据ID: ['FINST-W4G66DA1LLJW06LR9L2Q86WS0V5L3PEWRN8CM62', 'FINST-W4G66DA1LLJW06LR9L2Q86WS0V5L3PEWRN8CM72', 'FINST-W4G66DA1LLJW06LR9L2Q86WS0V5L3PEWRN8CM82', 'FINST-W4G66DA1LLJW06LR9L2Q86WS0V5L3PEWRN8CM92']
2025-06-23 13:31:05,115 - INFO - 批量插入完成，共 4 条记录
2025-06-23 13:31:05,115 - INFO - 日期 2025-06-22 处理完成 - 更新: 1 条，插入: 4 条，错误: 0 条
2025-06-23 13:31:05,115 - INFO - 数据同步完成！更新: 1 条，插入: 4 条，错误: 1 条
2025-06-23 13:32:05,130 - INFO - 开始同步昨天与今天的销售数据: 2025-06-22 至 2025-06-23
2025-06-23 13:32:05,130 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-23 13:32:05,130 - INFO - 查询参数: ('2025-06-22', '2025-06-23')
2025-06-23 13:32:05,271 - INFO - MySQL查询成功，时间段: 2025-06-22 至 2025-06-23，共获取 489 条记录
2025-06-23 13:32:05,271 - INFO - 获取到 1 个日期需要处理: ['2025-06-22']
2025-06-23 13:32:05,271 - INFO - 开始处理日期: 2025-06-22
2025-06-23 13:32:05,271 - INFO - Request Parameters - Page 1:
2025-06-23 13:32:05,271 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 13:32:05,271 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 13:32:05,974 - INFO - Response - Page 1:
2025-06-23 13:32:05,974 - INFO - 第 1 页获取到 50 条记录
2025-06-23 13:32:06,490 - INFO - Request Parameters - Page 2:
2025-06-23 13:32:06,490 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 13:32:06,490 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 13:32:07,209 - INFO - Response - Page 2:
2025-06-23 13:32:07,209 - INFO - 第 2 页获取到 50 条记录
2025-06-23 13:32:07,724 - INFO - Request Parameters - Page 3:
2025-06-23 13:32:07,724 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 13:32:07,724 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 13:32:08,412 - INFO - Response - Page 3:
2025-06-23 13:32:08,412 - INFO - 第 3 页获取到 50 条记录
2025-06-23 13:32:08,912 - INFO - Request Parameters - Page 4:
2025-06-23 13:32:08,912 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 13:32:08,912 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 13:32:09,537 - INFO - Response - Page 4:
2025-06-23 13:32:09,537 - INFO - 第 4 页获取到 50 条记录
2025-06-23 13:32:10,052 - INFO - Request Parameters - Page 5:
2025-06-23 13:32:10,052 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 13:32:10,052 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 13:32:10,693 - INFO - Response - Page 5:
2025-06-23 13:32:10,693 - INFO - 第 5 页获取到 50 条记录
2025-06-23 13:32:11,193 - INFO - Request Parameters - Page 6:
2025-06-23 13:32:11,193 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 13:32:11,193 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 13:32:11,865 - INFO - Response - Page 6:
2025-06-23 13:32:11,865 - INFO - 第 6 页获取到 50 条记录
2025-06-23 13:32:12,365 - INFO - Request Parameters - Page 7:
2025-06-23 13:32:12,365 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 13:32:12,365 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 13:32:13,021 - INFO - Response - Page 7:
2025-06-23 13:32:13,021 - INFO - 第 7 页获取到 50 条记录
2025-06-23 13:32:13,537 - INFO - Request Parameters - Page 8:
2025-06-23 13:32:13,537 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 13:32:13,537 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 13:32:14,209 - INFO - Response - Page 8:
2025-06-23 13:32:14,209 - INFO - 第 8 页获取到 50 条记录
2025-06-23 13:32:14,709 - INFO - Request Parameters - Page 9:
2025-06-23 13:32:14,709 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 13:32:14,709 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 13:32:15,302 - INFO - Response - Page 9:
2025-06-23 13:32:15,302 - INFO - 第 9 页获取到 50 条记录
2025-06-23 13:32:15,818 - INFO - Request Parameters - Page 10:
2025-06-23 13:32:15,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 13:32:15,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 13:32:16,396 - INFO - Response - Page 10:
2025-06-23 13:32:16,396 - INFO - 第 10 页获取到 22 条记录
2025-06-23 13:32:16,912 - INFO - 查询完成，共获取到 472 条记录
2025-06-23 13:32:16,912 - INFO - 获取到 472 条表单数据
2025-06-23 13:32:16,912 - INFO - 当前日期 2025-06-22 有 472 条MySQL数据需要处理
2025-06-23 13:32:16,927 - INFO - 日期 2025-06-22 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-23 13:32:16,927 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-23 13:32:16,927 - INFO - 同步完成
2025-06-23 16:30:33,578 - INFO - 使用默认增量同步（当天更新数据）
2025-06-23 16:30:33,578 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-23 16:30:33,578 - INFO - 查询参数: ('2025-06-23',)
2025-06-23 16:30:33,718 - INFO - MySQL查询成功，增量数据（日期: 2025-06-23），共获取 134 条记录
2025-06-23 16:30:33,718 - INFO - 获取到 2 个日期需要处理: ['2025-06-21', '2025-06-22']
2025-06-23 16:30:33,718 - INFO - 开始处理日期: 2025-06-21
2025-06-23 16:30:33,718 - INFO - Request Parameters - Page 1:
2025-06-23 16:30:33,718 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 16:30:33,718 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 16:30:40,000 - INFO - Response - Page 1:
2025-06-23 16:30:40,000 - INFO - 第 1 页获取到 50 条记录
2025-06-23 16:30:40,515 - INFO - Request Parameters - Page 2:
2025-06-23 16:30:40,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 16:30:40,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 16:30:48,625 - ERROR - 处理日期 2025-06-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 0BC4E2F2-F507-76E2-B56C-17B541ECB698 Response: {'code': 'ServiceUnavailable', 'requestid': '0BC4E2F2-F507-76E2-B56C-17B541ECB698', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 0BC4E2F2-F507-76E2-B56C-17B541ECB698)
2025-06-23 16:30:48,625 - INFO - 开始处理日期: 2025-06-22
2025-06-23 16:30:48,625 - INFO - Request Parameters - Page 1:
2025-06-23 16:30:48,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 16:30:48,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 16:30:50,031 - INFO - Response - Page 1:
2025-06-23 16:30:50,031 - INFO - 第 1 页获取到 50 条记录
2025-06-23 16:30:50,546 - INFO - Request Parameters - Page 2:
2025-06-23 16:30:50,546 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 16:30:50,546 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 16:30:51,203 - INFO - Response - Page 2:
2025-06-23 16:30:51,203 - INFO - 第 2 页获取到 50 条记录
2025-06-23 16:30:51,718 - INFO - Request Parameters - Page 3:
2025-06-23 16:30:51,718 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 16:30:51,718 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 16:30:52,421 - INFO - Response - Page 3:
2025-06-23 16:30:52,421 - INFO - 第 3 页获取到 50 条记录
2025-06-23 16:30:52,921 - INFO - Request Parameters - Page 4:
2025-06-23 16:30:52,921 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 16:30:52,921 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 16:30:53,531 - INFO - Response - Page 4:
2025-06-23 16:30:53,531 - INFO - 第 4 页获取到 50 条记录
2025-06-23 16:30:54,031 - INFO - Request Parameters - Page 5:
2025-06-23 16:30:54,031 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 16:30:54,031 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 16:30:54,656 - INFO - Response - Page 5:
2025-06-23 16:30:54,656 - INFO - 第 5 页获取到 50 条记录
2025-06-23 16:30:55,171 - INFO - Request Parameters - Page 6:
2025-06-23 16:30:55,171 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 16:30:55,171 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 16:30:55,843 - INFO - Response - Page 6:
2025-06-23 16:30:55,843 - INFO - 第 6 页获取到 50 条记录
2025-06-23 16:30:56,359 - INFO - Request Parameters - Page 7:
2025-06-23 16:30:56,359 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 16:30:56,359 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 16:30:56,999 - INFO - Response - Page 7:
2025-06-23 16:30:56,999 - INFO - 第 7 页获取到 50 条记录
2025-06-23 16:30:57,515 - INFO - Request Parameters - Page 8:
2025-06-23 16:30:57,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 16:30:57,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 16:30:58,109 - INFO - Response - Page 8:
2025-06-23 16:30:58,109 - INFO - 第 8 页获取到 50 条记录
2025-06-23 16:30:58,609 - INFO - Request Parameters - Page 9:
2025-06-23 16:30:58,609 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 16:30:58,609 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 16:30:59,249 - INFO - Response - Page 9:
2025-06-23 16:30:59,249 - INFO - 第 9 页获取到 50 条记录
2025-06-23 16:30:59,749 - INFO - Request Parameters - Page 10:
2025-06-23 16:30:59,749 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 16:30:59,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 16:31:00,296 - INFO - Response - Page 10:
2025-06-23 16:31:00,296 - INFO - 第 10 页获取到 22 条记录
2025-06-23 16:31:00,812 - INFO - 查询完成，共获取到 472 条记录
2025-06-23 16:31:00,812 - INFO - 获取到 472 条表单数据
2025-06-23 16:31:00,812 - INFO - 当前日期 2025-06-22 有 131 条MySQL数据需要处理
2025-06-23 16:31:00,812 - INFO - 开始更新记录 - 表单实例ID: FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CMS
2025-06-23 16:31:01,312 - INFO - 更新表单数据成功: FINST-SWC66P91LLJWZG00EG516C8A8KEP2DGGCH8CMS
2025-06-23 16:31:01,312 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 432.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 432.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 2}]
2025-06-23 16:31:01,312 - INFO - 开始批量插入 8 条新记录
2025-06-23 16:31:01,468 - INFO - 批量插入响应状态码: 200
2025-06-23 16:31:01,468 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 23 Jun 2025 08:31:01 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '396', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0791AF91-FE19-7710-BB7D-25FA473E017B', 'x-acs-trace-id': 'bce88124adb3344cfa5183a9f18da19d', 'etag': '3DPFmfJTQb+UKm3IesfRXUA6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-23 16:31:01,468 - INFO - 批量插入响应体: {'result': ['FINST-RN766181WQJWMKW67ORUE9BKWIYD30VE7U8CMA2', 'FINST-RN766181WQJWMKW67ORUE9BKWIYD30VE7U8CMB2', 'FINST-RN766181WQJWMKW67ORUE9BKWIYD30VE7U8CMC2', 'FINST-RN766181WQJWMKW67ORUE9BKWIYD30VE7U8CMD2', 'FINST-RN766181WQJWMKW67ORUE9BKWIYD30VE7U8CME2', 'FINST-RN766181WQJWMKW67ORUE9BKWIYD30VE7U8CMF2', 'FINST-RN766181WQJWMKW67ORUE9BKWIYD30VE7U8CMG2', 'FINST-RN766181WQJWMKW67ORUE9BKWIYD30VE7U8CMH2']}
2025-06-23 16:31:01,468 - INFO - 批量插入表单数据成功，批次 1，共 8 条记录
2025-06-23 16:31:01,468 - INFO - 成功插入的数据ID: ['FINST-RN766181WQJWMKW67ORUE9BKWIYD30VE7U8CMA2', 'FINST-RN766181WQJWMKW67ORUE9BKWIYD30VE7U8CMB2', 'FINST-RN766181WQJWMKW67ORUE9BKWIYD30VE7U8CMC2', 'FINST-RN766181WQJWMKW67ORUE9BKWIYD30VE7U8CMD2', 'FINST-RN766181WQJWMKW67ORUE9BKWIYD30VE7U8CME2', 'FINST-RN766181WQJWMKW67ORUE9BKWIYD30VE7U8CMF2', 'FINST-RN766181WQJWMKW67ORUE9BKWIYD30VE7U8CMG2', 'FINST-RN766181WQJWMKW67ORUE9BKWIYD30VE7U8CMH2']
2025-06-23 16:31:06,484 - INFO - 批量插入完成，共 8 条记录
2025-06-23 16:31:06,484 - INFO - 日期 2025-06-22 处理完成 - 更新: 1 条，插入: 8 条，错误: 0 条
2025-06-23 16:31:06,484 - INFO - 数据同步完成！更新: 1 条，插入: 8 条，错误: 1 条
2025-06-23 16:32:06,499 - INFO - 开始同步昨天与今天的销售数据: 2025-06-22 至 2025-06-23
2025-06-23 16:32:06,499 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-23 16:32:06,499 - INFO - 查询参数: ('2025-06-22', '2025-06-23')
2025-06-23 16:32:06,640 - INFO - MySQL查询成功，时间段: 2025-06-22 至 2025-06-23，共获取 522 条记录
2025-06-23 16:32:06,640 - INFO - 获取到 1 个日期需要处理: ['2025-06-22']
2025-06-23 16:32:06,640 - INFO - 开始处理日期: 2025-06-22
2025-06-23 16:32:06,640 - INFO - Request Parameters - Page 1:
2025-06-23 16:32:06,640 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 16:32:06,640 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 16:32:07,296 - INFO - Response - Page 1:
2025-06-23 16:32:07,296 - INFO - 第 1 页获取到 50 条记录
2025-06-23 16:32:07,796 - INFO - Request Parameters - Page 2:
2025-06-23 16:32:07,796 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 16:32:07,796 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 16:32:08,624 - INFO - Response - Page 2:
2025-06-23 16:32:08,624 - INFO - 第 2 页获取到 50 条记录
2025-06-23 16:32:09,140 - INFO - Request Parameters - Page 3:
2025-06-23 16:32:09,140 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 16:32:09,140 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 16:32:09,765 - INFO - Response - Page 3:
2025-06-23 16:32:09,765 - INFO - 第 3 页获取到 50 条记录
2025-06-23 16:32:10,265 - INFO - Request Parameters - Page 4:
2025-06-23 16:32:10,265 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 16:32:10,265 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 16:32:10,936 - INFO - Response - Page 4:
2025-06-23 16:32:10,936 - INFO - 第 4 页获取到 50 条记录
2025-06-23 16:32:11,452 - INFO - Request Parameters - Page 5:
2025-06-23 16:32:11,452 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 16:32:11,452 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 16:32:12,140 - INFO - Response - Page 5:
2025-06-23 16:32:12,140 - INFO - 第 5 页获取到 50 条记录
2025-06-23 16:32:12,655 - INFO - Request Parameters - Page 6:
2025-06-23 16:32:12,655 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 16:32:12,655 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 16:32:13,280 - INFO - Response - Page 6:
2025-06-23 16:32:13,280 - INFO - 第 6 页获取到 50 条记录
2025-06-23 16:32:13,780 - INFO - Request Parameters - Page 7:
2025-06-23 16:32:13,780 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 16:32:13,780 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 16:32:14,452 - INFO - Response - Page 7:
2025-06-23 16:32:14,452 - INFO - 第 7 页获取到 50 条记录
2025-06-23 16:32:14,952 - INFO - Request Parameters - Page 8:
2025-06-23 16:32:14,952 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 16:32:14,952 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 16:32:15,624 - INFO - Response - Page 8:
2025-06-23 16:32:15,624 - INFO - 第 8 页获取到 50 条记录
2025-06-23 16:32:16,140 - INFO - Request Parameters - Page 9:
2025-06-23 16:32:16,140 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 16:32:16,140 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 16:32:16,780 - INFO - Response - Page 9:
2025-06-23 16:32:16,780 - INFO - 第 9 页获取到 50 条记录
2025-06-23 16:32:17,296 - INFO - Request Parameters - Page 10:
2025-06-23 16:32:17,296 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 16:32:17,296 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 16:32:17,936 - INFO - Response - Page 10:
2025-06-23 16:32:17,936 - INFO - 第 10 页获取到 30 条记录
2025-06-23 16:32:18,452 - INFO - 查询完成，共获取到 480 条记录
2025-06-23 16:32:18,452 - INFO - 获取到 480 条表单数据
2025-06-23 16:32:18,452 - INFO - 当前日期 2025-06-22 有 501 条MySQL数据需要处理
2025-06-23 16:32:18,468 - INFO - 开始批量插入 21 条新记录
2025-06-23 16:32:18,686 - INFO - 批量插入响应状态码: 200
2025-06-23 16:32:18,686 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 23 Jun 2025 08:32:19 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1020', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '287A5E46-09BA-7DD0-9CFE-0618EFA602FB', 'x-acs-trace-id': '74033c4c8398e2fb96be746312274032', 'etag': '1EN9F/2A+0bh1xm3DvEM98g0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-23 16:32:18,686 - INFO - 批量插入响应体: {'result': ['FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2VF29U8CM53', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2VF29U8CM63', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2VF29U8CM73', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2VF29U8CM83', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2WF29U8CM93', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2WF29U8CMA3', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2WF29U8CMB3', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2WF29U8CMC3', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2WF29U8CMD3', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2WF29U8CME3', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2WF29U8CMF3', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2WF29U8CMG3', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2WF29U8CMH3', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2WF29U8CMI3', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2WF29U8CMJ3', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2WF29U8CMK3', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2WF29U8CML3', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2WF29U8CMM3', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2WF29U8CMN3', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2WF29U8CMO3', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2WF29U8CMP3']}
2025-06-23 16:32:18,686 - INFO - 批量插入表单数据成功，批次 1，共 21 条记录
2025-06-23 16:32:18,686 - INFO - 成功插入的数据ID: ['FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2VF29U8CM53', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2VF29U8CM63', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2VF29U8CM73', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2VF29U8CM83', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2WF29U8CM93', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2WF29U8CMA3', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2WF29U8CMB3', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2WF29U8CMC3', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2WF29U8CMD3', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2WF29U8CME3', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2WF29U8CMF3', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2WF29U8CMG3', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2WF29U8CMH3', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2WF29U8CMI3', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2WF29U8CMJ3', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2WF29U8CMK3', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2WF29U8CML3', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2WF29U8CMM3', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2WF29U8CMN3', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2WF29U8CMO3', 'FINST-AAG66KB1ELJWM6VC6U49Z9KV47OK2WF29U8CMP3']
2025-06-23 16:32:23,702 - INFO - 批量插入完成，共 21 条记录
2025-06-23 16:32:23,702 - INFO - 日期 2025-06-22 处理完成 - 更新: 0 条，插入: 21 条，错误: 0 条
2025-06-23 16:32:23,702 - INFO - 数据同步完成！更新: 0 条，插入: 21 条，错误: 0 条
2025-06-23 16:32:23,702 - INFO - 同步完成
2025-06-23 19:30:34,739 - INFO - 使用默认增量同步（当天更新数据）
2025-06-23 19:30:34,739 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-23 19:30:34,739 - INFO - 查询参数: ('2025-06-23',)
2025-06-23 19:30:34,880 - INFO - MySQL查询成功，增量数据（日期: 2025-06-23），共获取 134 条记录
2025-06-23 19:30:34,880 - INFO - 获取到 2 个日期需要处理: ['2025-06-21', '2025-06-22']
2025-06-23 19:30:34,880 - INFO - 开始处理日期: 2025-06-21
2025-06-23 19:30:34,880 - INFO - Request Parameters - Page 1:
2025-06-23 19:30:34,880 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 19:30:34,880 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 19:30:43,006 - ERROR - 处理日期 2025-06-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3DBCAF5B-3410-72FC-9FC2-244078098EC2 Response: {'code': 'ServiceUnavailable', 'requestid': '3DBCAF5B-3410-72FC-9FC2-244078098EC2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3DBCAF5B-3410-72FC-9FC2-244078098EC2)
2025-06-23 19:30:43,006 - INFO - 开始处理日期: 2025-06-22
2025-06-23 19:30:43,006 - INFO - Request Parameters - Page 1:
2025-06-23 19:30:43,006 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 19:30:43,006 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 19:30:48,569 - INFO - Response - Page 1:
2025-06-23 19:30:48,569 - INFO - 第 1 页获取到 50 条记录
2025-06-23 19:30:49,069 - INFO - Request Parameters - Page 2:
2025-06-23 19:30:49,069 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 19:30:49,069 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 19:30:49,694 - INFO - Response - Page 2:
2025-06-23 19:30:49,694 - INFO - 第 2 页获取到 50 条记录
2025-06-23 19:30:50,194 - INFO - Request Parameters - Page 3:
2025-06-23 19:30:50,194 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 19:30:50,194 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 19:30:50,850 - INFO - Response - Page 3:
2025-06-23 19:30:50,850 - INFO - 第 3 页获取到 50 条记录
2025-06-23 19:30:51,366 - INFO - Request Parameters - Page 4:
2025-06-23 19:30:51,366 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 19:30:51,366 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 19:30:51,991 - INFO - Response - Page 4:
2025-06-23 19:30:51,991 - INFO - 第 4 页获取到 50 条记录
2025-06-23 19:30:52,507 - INFO - Request Parameters - Page 5:
2025-06-23 19:30:52,507 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 19:30:52,507 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 19:30:53,147 - INFO - Response - Page 5:
2025-06-23 19:30:53,147 - INFO - 第 5 页获取到 50 条记录
2025-06-23 19:30:53,647 - INFO - Request Parameters - Page 6:
2025-06-23 19:30:53,647 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 19:30:53,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 19:30:54,272 - INFO - Response - Page 6:
2025-06-23 19:30:54,272 - INFO - 第 6 页获取到 50 条记录
2025-06-23 19:30:54,772 - INFO - Request Parameters - Page 7:
2025-06-23 19:30:54,772 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 19:30:54,772 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 19:30:55,382 - INFO - Response - Page 7:
2025-06-23 19:30:55,382 - INFO - 第 7 页获取到 50 条记录
2025-06-23 19:30:55,882 - INFO - Request Parameters - Page 8:
2025-06-23 19:30:55,882 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 19:30:55,882 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 19:30:56,601 - INFO - Response - Page 8:
2025-06-23 19:30:56,601 - INFO - 第 8 页获取到 50 条记录
2025-06-23 19:30:57,117 - INFO - Request Parameters - Page 9:
2025-06-23 19:30:57,117 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 19:30:57,117 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 19:30:57,804 - INFO - Response - Page 9:
2025-06-23 19:30:57,804 - INFO - 第 9 页获取到 50 条记录
2025-06-23 19:30:58,320 - INFO - Request Parameters - Page 10:
2025-06-23 19:30:58,320 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 19:30:58,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 19:30:58,914 - INFO - Response - Page 10:
2025-06-23 19:30:58,914 - INFO - 第 10 页获取到 50 条记录
2025-06-23 19:30:59,429 - INFO - Request Parameters - Page 11:
2025-06-23 19:30:59,429 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 19:30:59,429 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 19:30:59,914 - INFO - Response - Page 11:
2025-06-23 19:30:59,914 - INFO - 第 11 页获取到 1 条记录
2025-06-23 19:31:00,429 - INFO - 查询完成，共获取到 501 条记录
2025-06-23 19:31:00,429 - INFO - 获取到 501 条表单数据
2025-06-23 19:31:00,429 - INFO - 当前日期 2025-06-22 有 131 条MySQL数据需要处理
2025-06-23 19:31:00,429 - INFO - 日期 2025-06-22 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-23 19:31:00,429 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-23 19:32:00,451 - INFO - 开始同步昨天与今天的销售数据: 2025-06-22 至 2025-06-23
2025-06-23 19:32:00,451 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-23 19:32:00,451 - INFO - 查询参数: ('2025-06-22', '2025-06-23')
2025-06-23 19:32:00,591 - INFO - MySQL查询成功，时间段: 2025-06-22 至 2025-06-23，共获取 524 条记录
2025-06-23 19:32:00,591 - INFO - 获取到 1 个日期需要处理: ['2025-06-22']
2025-06-23 19:32:00,591 - INFO - 开始处理日期: 2025-06-22
2025-06-23 19:32:00,591 - INFO - Request Parameters - Page 1:
2025-06-23 19:32:00,591 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 19:32:00,591 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 19:32:01,357 - INFO - Response - Page 1:
2025-06-23 19:32:01,357 - INFO - 第 1 页获取到 50 条记录
2025-06-23 19:32:01,873 - INFO - Request Parameters - Page 2:
2025-06-23 19:32:01,873 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 19:32:01,873 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 19:32:02,607 - INFO - Response - Page 2:
2025-06-23 19:32:02,607 - INFO - 第 2 页获取到 50 条记录
2025-06-23 19:32:03,107 - INFO - Request Parameters - Page 3:
2025-06-23 19:32:03,107 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 19:32:03,107 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 19:32:03,717 - INFO - Response - Page 3:
2025-06-23 19:32:03,717 - INFO - 第 3 页获取到 50 条记录
2025-06-23 19:32:04,232 - INFO - Request Parameters - Page 4:
2025-06-23 19:32:04,232 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 19:32:04,232 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 19:32:04,826 - INFO - Response - Page 4:
2025-06-23 19:32:04,826 - INFO - 第 4 页获取到 50 条记录
2025-06-23 19:32:05,326 - INFO - Request Parameters - Page 5:
2025-06-23 19:32:05,326 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 19:32:05,326 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 19:32:05,998 - INFO - Response - Page 5:
2025-06-23 19:32:05,998 - INFO - 第 5 页获取到 50 条记录
2025-06-23 19:32:06,514 - INFO - Request Parameters - Page 6:
2025-06-23 19:32:06,514 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 19:32:06,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 19:32:07,123 - INFO - Response - Page 6:
2025-06-23 19:32:07,139 - INFO - 第 6 页获取到 50 条记录
2025-06-23 19:32:07,639 - INFO - Request Parameters - Page 7:
2025-06-23 19:32:07,639 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 19:32:07,639 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 19:32:08,358 - INFO - Response - Page 7:
2025-06-23 19:32:08,358 - INFO - 第 7 页获取到 50 条记录
2025-06-23 19:32:08,858 - INFO - Request Parameters - Page 8:
2025-06-23 19:32:08,858 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 19:32:08,858 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 19:32:09,467 - INFO - Response - Page 8:
2025-06-23 19:32:09,467 - INFO - 第 8 页获取到 50 条记录
2025-06-23 19:32:09,983 - INFO - Request Parameters - Page 9:
2025-06-23 19:32:09,983 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 19:32:09,983 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 19:32:10,592 - INFO - Response - Page 9:
2025-06-23 19:32:10,608 - INFO - 第 9 页获取到 50 条记录
2025-06-23 19:32:11,108 - INFO - Request Parameters - Page 10:
2025-06-23 19:32:11,108 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 19:32:11,108 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 19:32:11,749 - INFO - Response - Page 10:
2025-06-23 19:32:11,749 - INFO - 第 10 页获取到 50 条记录
2025-06-23 19:32:12,249 - INFO - Request Parameters - Page 11:
2025-06-23 19:32:12,249 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 19:32:12,249 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 19:32:12,717 - INFO - Response - Page 11:
2025-06-23 19:32:12,717 - INFO - 第 11 页获取到 1 条记录
2025-06-23 19:32:13,217 - INFO - 查询完成，共获取到 501 条记录
2025-06-23 19:32:13,217 - INFO - 获取到 501 条表单数据
2025-06-23 19:32:13,217 - INFO - 当前日期 2025-06-22 有 503 条MySQL数据需要处理
2025-06-23 19:32:13,233 - INFO - 开始批量插入 2 条新记录
2025-06-23 19:32:13,389 - INFO - 批量插入响应状态码: 200
2025-06-23 19:32:13,389 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 23 Jun 2025 11:32:13 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B5FFB098-31CD-7076-9AF4-BB74091A570B', 'x-acs-trace-id': '6039b920d2ad6db714f86a751586d802', 'etag': '1/vOj/i6lIEsR1TidmlRIDw8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-23 19:32:13,389 - INFO - 批量插入响应体: {'result': ['FINST-RI766091BVJWQHGT9D83GCQU294S2BAFO09CM91', 'FINST-RI766091BVJWQHGT9D83GCQU294S2BAFO09CMA1']}
2025-06-23 19:32:13,389 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-06-23 19:32:13,389 - INFO - 成功插入的数据ID: ['FINST-RI766091BVJWQHGT9D83GCQU294S2BAFO09CM91', 'FINST-RI766091BVJWQHGT9D83GCQU294S2BAFO09CMA1']
2025-06-23 19:32:18,405 - INFO - 批量插入完成，共 2 条记录
2025-06-23 19:32:18,405 - INFO - 日期 2025-06-22 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-06-23 19:32:18,405 - INFO - 数据同步完成！更新: 0 条，插入: 2 条，错误: 0 条
2025-06-23 19:32:18,405 - INFO - 同步完成
2025-06-23 22:30:34,428 - INFO - 使用默认增量同步（当天更新数据）
2025-06-23 22:30:34,428 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-23 22:30:34,428 - INFO - 查询参数: ('2025-06-23',)
2025-06-23 22:30:34,584 - INFO - MySQL查询成功，增量数据（日期: 2025-06-23），共获取 188 条记录
2025-06-23 22:30:34,584 - INFO - 获取到 3 个日期需要处理: ['2025-06-21', '2025-06-22', '2025-06-23']
2025-06-23 22:30:34,584 - INFO - 开始处理日期: 2025-06-21
2025-06-23 22:30:34,584 - INFO - Request Parameters - Page 1:
2025-06-23 22:30:34,584 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 22:30:34,584 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 22:30:42,726 - ERROR - 处理日期 2025-06-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 90A151C9-072D-7E46-9ECA-99AAF992A2D6 Response: {'code': 'ServiceUnavailable', 'requestid': '90A151C9-072D-7E46-9ECA-99AAF992A2D6', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 90A151C9-072D-7E46-9ECA-99AAF992A2D6)
2025-06-23 22:30:42,726 - INFO - 开始处理日期: 2025-06-22
2025-06-23 22:30:42,726 - INFO - Request Parameters - Page 1:
2025-06-23 22:30:42,726 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 22:30:42,726 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 22:30:43,413 - INFO - Response - Page 1:
2025-06-23 22:30:43,413 - INFO - 第 1 页获取到 50 条记录
2025-06-23 22:30:43,929 - INFO - Request Parameters - Page 2:
2025-06-23 22:30:43,929 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 22:30:43,929 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 22:30:44,601 - INFO - Response - Page 2:
2025-06-23 22:30:44,601 - INFO - 第 2 页获取到 50 条记录
2025-06-23 22:30:45,116 - INFO - Request Parameters - Page 3:
2025-06-23 22:30:45,116 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 22:30:45,116 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 22:30:45,773 - INFO - Response - Page 3:
2025-06-23 22:30:45,773 - INFO - 第 3 页获取到 50 条记录
2025-06-23 22:30:46,273 - INFO - Request Parameters - Page 4:
2025-06-23 22:30:46,273 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 22:30:46,273 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 22:30:51,773 - INFO - Response - Page 4:
2025-06-23 22:30:51,773 - INFO - 第 4 页获取到 50 条记录
2025-06-23 22:30:52,273 - INFO - Request Parameters - Page 5:
2025-06-23 22:30:52,273 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 22:30:52,273 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 22:30:52,914 - INFO - Response - Page 5:
2025-06-23 22:30:52,914 - INFO - 第 5 页获取到 50 条记录
2025-06-23 22:30:53,414 - INFO - Request Parameters - Page 6:
2025-06-23 22:30:53,414 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 22:30:53,414 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 22:30:54,024 - INFO - Response - Page 6:
2025-06-23 22:30:54,024 - INFO - 第 6 页获取到 50 条记录
2025-06-23 22:30:54,539 - INFO - Request Parameters - Page 7:
2025-06-23 22:30:54,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 22:30:54,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 22:30:55,211 - INFO - Response - Page 7:
2025-06-23 22:30:55,211 - INFO - 第 7 页获取到 50 条记录
2025-06-23 22:30:55,727 - INFO - Request Parameters - Page 8:
2025-06-23 22:30:55,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 22:30:55,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 22:30:56,305 - INFO - Response - Page 8:
2025-06-23 22:30:56,305 - INFO - 第 8 页获取到 50 条记录
2025-06-23 22:30:56,821 - INFO - Request Parameters - Page 9:
2025-06-23 22:30:56,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 22:30:56,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 22:30:57,493 - INFO - Response - Page 9:
2025-06-23 22:30:57,493 - INFO - 第 9 页获取到 50 条记录
2025-06-23 22:30:58,008 - INFO - Request Parameters - Page 10:
2025-06-23 22:30:58,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 22:30:58,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 22:30:58,602 - INFO - Response - Page 10:
2025-06-23 22:30:58,602 - INFO - 第 10 页获取到 50 条记录
2025-06-23 22:30:59,118 - INFO - Request Parameters - Page 11:
2025-06-23 22:30:59,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 22:30:59,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 22:30:59,555 - INFO - Response - Page 11:
2025-06-23 22:30:59,555 - INFO - 第 11 页获取到 3 条记录
2025-06-23 22:31:00,055 - INFO - 查询完成，共获取到 503 条记录
2025-06-23 22:31:00,055 - INFO - 获取到 503 条表单数据
2025-06-23 22:31:00,055 - INFO - 当前日期 2025-06-22 有 131 条MySQL数据需要处理
2025-06-23 22:31:00,071 - INFO - 日期 2025-06-22 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-23 22:31:00,071 - INFO - 开始处理日期: 2025-06-23
2025-06-23 22:31:00,071 - INFO - Request Parameters - Page 1:
2025-06-23 22:31:00,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 22:31:00,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 22:31:00,493 - INFO - Response - Page 1:
2025-06-23 22:31:00,493 - INFO - 查询完成，共获取到 0 条记录
2025-06-23 22:31:00,493 - INFO - 获取到 0 条表单数据
2025-06-23 22:31:00,493 - INFO - 当前日期 2025-06-23 有 53 条MySQL数据需要处理
2025-06-23 22:31:00,493 - INFO - 开始批量插入 53 条新记录
2025-06-23 22:31:00,743 - INFO - 批量插入响应状态码: 200
2025-06-23 22:31:00,743 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 23 Jun 2025 14:30:59 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0BB40DFA-AA08-7A7C-B537-DC1BF774B4D9', 'x-acs-trace-id': '471a3789899d6c68515b83a0be7faa0f', 'etag': '2x0ZYlmmqrVyx9PO0vfL1Og2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-23 22:31:00,743 - INFO - 批量插入响应体: {'result': ['FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMP6', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMQ6', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMR6', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMS6', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMT6', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMU6', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMV6', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMW6', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMX6', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMY6', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMZ6', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CM07', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CM17', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CM27', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CM37', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CM47', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CM57', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CM67', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CM77', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CM87', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CM97', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMA7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMB7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMC7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMD7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CME7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMF7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMG7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMH7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMI7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMJ7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMK7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CML7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMM7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMN7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMO7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMP7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMQ7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMR7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMS7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMT7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMU7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMV7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMW7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMX7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMY7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMZ7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CM08', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CM18', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CM28']}
2025-06-23 22:31:00,743 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-23 22:31:00,743 - INFO - 成功插入的数据ID: ['FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMP6', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMQ6', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMR6', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMS6', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMT6', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMU6', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMV6', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMW6', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMX6', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMY6', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMZ6', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CM07', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CM17', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CM27', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CM37', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CM47', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CM57', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CM67', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CM77', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CM87', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CM97', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMA7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMB7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMC7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMD7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CME7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMF7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMG7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMH7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMI7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMJ7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMK7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CML7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMM7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMN7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMO7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMP7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMQ7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMR7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMS7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMT7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMU7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMV7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMW7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMX7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMY7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CMZ7', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CM08', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CM18', 'FINST-MLF662B1LQJWPJE49D9D24RXQEWO2XRB279CM28']
2025-06-23 22:31:05,884 - INFO - 批量插入响应状态码: 200
2025-06-23 22:31:05,900 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 23 Jun 2025 14:31:04 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '156', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6535767F-75D5-7C89-9688-EC22CA7D3EB2', 'x-acs-trace-id': 'd0f80fccff4f017f2c6e7232645efdc9', 'etag': '1WYuVjGXpYRIMmxvZvyLdNw6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-23 22:31:05,900 - INFO - 批量插入响应体: {'result': ['FINST-X3E66X81IKIW5ZZ6B33MW5EDPGEW3ZQF279CMDD', 'FINST-X3E66X81IKIW5ZZ6B33MW5EDPGEW3ZQF279CMED', 'FINST-X3E66X81IKIW5ZZ6B33MW5EDPGEW3ZQF279CMFD']}
2025-06-23 22:31:05,900 - INFO - 批量插入表单数据成功，批次 2，共 3 条记录
2025-06-23 22:31:05,900 - INFO - 成功插入的数据ID: ['FINST-X3E66X81IKIW5ZZ6B33MW5EDPGEW3ZQF279CMDD', 'FINST-X3E66X81IKIW5ZZ6B33MW5EDPGEW3ZQF279CMED', 'FINST-X3E66X81IKIW5ZZ6B33MW5EDPGEW3ZQF279CMFD']
2025-06-23 22:31:10,916 - INFO - 批量插入完成，共 53 条记录
2025-06-23 22:31:10,916 - INFO - 日期 2025-06-23 处理完成 - 更新: 0 条，插入: 53 条，错误: 0 条
2025-06-23 22:31:10,916 - INFO - 数据同步完成！更新: 0 条，插入: 53 条，错误: 1 条
2025-06-23 22:32:10,937 - INFO - 开始同步昨天与今天的销售数据: 2025-06-22 至 2025-06-23
2025-06-23 22:32:10,937 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-23 22:32:10,937 - INFO - 查询参数: ('2025-06-22', '2025-06-23')
2025-06-23 22:32:11,078 - INFO - MySQL查询成功，时间段: 2025-06-22 至 2025-06-23，共获取 578 条记录
2025-06-23 22:32:11,078 - INFO - 获取到 2 个日期需要处理: ['2025-06-22', '2025-06-23']
2025-06-23 22:32:11,078 - INFO - 开始处理日期: 2025-06-22
2025-06-23 22:32:11,078 - INFO - Request Parameters - Page 1:
2025-06-23 22:32:11,078 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 22:32:11,078 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 22:32:11,828 - INFO - Response - Page 1:
2025-06-23 22:32:11,828 - INFO - 第 1 页获取到 50 条记录
2025-06-23 22:32:12,344 - INFO - Request Parameters - Page 2:
2025-06-23 22:32:12,344 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 22:32:12,344 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 22:32:13,094 - INFO - Response - Page 2:
2025-06-23 22:32:13,094 - INFO - 第 2 页获取到 50 条记录
2025-06-23 22:32:13,609 - INFO - Request Parameters - Page 3:
2025-06-23 22:32:13,609 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 22:32:13,609 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 22:32:14,266 - INFO - Response - Page 3:
2025-06-23 22:32:14,266 - INFO - 第 3 页获取到 50 条记录
2025-06-23 22:32:14,781 - INFO - Request Parameters - Page 4:
2025-06-23 22:32:14,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 22:32:14,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 22:32:15,469 - INFO - Response - Page 4:
2025-06-23 22:32:15,469 - INFO - 第 4 页获取到 50 条记录
2025-06-23 22:32:15,969 - INFO - Request Parameters - Page 5:
2025-06-23 22:32:15,969 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 22:32:15,969 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 22:32:16,625 - INFO - Response - Page 5:
2025-06-23 22:32:16,625 - INFO - 第 5 页获取到 50 条记录
2025-06-23 22:32:17,125 - INFO - Request Parameters - Page 6:
2025-06-23 22:32:17,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 22:32:17,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 22:32:17,782 - INFO - Response - Page 6:
2025-06-23 22:32:17,782 - INFO - 第 6 页获取到 50 条记录
2025-06-23 22:32:18,282 - INFO - Request Parameters - Page 7:
2025-06-23 22:32:18,282 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 22:32:18,282 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 22:32:18,907 - INFO - Response - Page 7:
2025-06-23 22:32:18,907 - INFO - 第 7 页获取到 50 条记录
2025-06-23 22:32:19,407 - INFO - Request Parameters - Page 8:
2025-06-23 22:32:19,407 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 22:32:19,407 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 22:32:20,063 - INFO - Response - Page 8:
2025-06-23 22:32:20,063 - INFO - 第 8 页获取到 50 条记录
2025-06-23 22:32:20,563 - INFO - Request Parameters - Page 9:
2025-06-23 22:32:20,563 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 22:32:20,563 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 22:32:21,141 - INFO - Response - Page 9:
2025-06-23 22:32:21,141 - INFO - 第 9 页获取到 50 条记录
2025-06-23 22:32:21,657 - INFO - Request Parameters - Page 10:
2025-06-23 22:32:21,657 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 22:32:21,657 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 22:32:22,329 - INFO - Response - Page 10:
2025-06-23 22:32:22,329 - INFO - 第 10 页获取到 50 条记录
2025-06-23 22:32:22,845 - INFO - Request Parameters - Page 11:
2025-06-23 22:32:22,845 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 22:32:22,845 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750521600000, 1750607999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 22:32:23,266 - INFO - Response - Page 11:
2025-06-23 22:32:23,266 - INFO - 第 11 页获取到 3 条记录
2025-06-23 22:32:23,766 - INFO - 查询完成，共获取到 503 条记录
2025-06-23 22:32:23,766 - INFO - 获取到 503 条表单数据
2025-06-23 22:32:23,766 - INFO - 当前日期 2025-06-22 有 503 条MySQL数据需要处理
2025-06-23 22:32:23,782 - INFO - 日期 2025-06-22 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-23 22:32:23,782 - INFO - 开始处理日期: 2025-06-23
2025-06-23 22:32:23,782 - INFO - Request Parameters - Page 1:
2025-06-23 22:32:23,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 22:32:23,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 22:32:24,423 - INFO - Response - Page 1:
2025-06-23 22:32:24,423 - INFO - 第 1 页获取到 50 条记录
2025-06-23 22:32:24,923 - INFO - Request Parameters - Page 2:
2025-06-23 22:32:24,923 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-23 22:32:24,923 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-23 22:32:25,329 - INFO - Response - Page 2:
2025-06-23 22:32:25,329 - INFO - 第 2 页获取到 3 条记录
2025-06-23 22:32:25,845 - INFO - 查询完成，共获取到 53 条记录
2025-06-23 22:32:25,845 - INFO - 获取到 53 条表单数据
2025-06-23 22:32:25,845 - INFO - 当前日期 2025-06-23 有 53 条MySQL数据需要处理
2025-06-23 22:32:25,845 - INFO - 日期 2025-06-23 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-23 22:32:25,845 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-23 22:32:25,845 - INFO - 同步完成
