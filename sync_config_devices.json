{"db_config": {"host": "localhost", "port": 43306, "user": "root", "password": "Hxp@1987!@#", "database": "mydatabase", "charset": "utf8mb4", "cursorclass": "pymysql.cursors.DictCursor"}, "yida_config": {"APP_TYPE": "APP_D7E6ZB94ZUL5Q1GUAOLD", "SYSTEM_TOKEN": "BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2", "USER_ID": "hex<PERSON><PERSON>g", "LANGUAGE": "zh_CN", "FORM_UUID": "FORM-860B31FAA4B54C4EB515B4F38B94968BQZMX"}, "sql_query": "SELECT project_name, shop_id, shop_entity_name, shop_entity_id, device_id, device_type, device_state, is_deleted FROM shop_devices_detail WHERE 1=1", "field_mapping": {"project_name": "selectField_mdqto6fg", "shop_id": "textField_mdqto6fh", "shop_entity_name": "textField_mdqto6fi", "shop_entity_id": "textField_mdqto6fj", "device_id": "textField_mdqto6fk", "device_type": "selectField_mdqto6fl", "device_state": "selectField_mdqto6fn", "is_deleted": "selectField_mdqto6fo"}, "key_fields": ["shop_entity_id", "device_id"], "compare_fields": ["shop_entity_name", "device_type", "device_state", "is_deleted"], "log_config": {"level": 20, "format": "%(asctime)s - %(levelname)s - %(message)s", "encoding": "utf-8", "filename_prefix": "devices_sync"}, "batch_config": {"batch_size": 30, "delay_seconds": 1}}