2025-06-11 08:00:03,377 - INFO - ==================================================
2025-06-11 08:00:03,392 - INFO - 程序启动 - 版本 v1.0.0
2025-06-11 08:00:03,392 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250611.log
2025-06-11 08:00:03,392 - INFO - ==================================================
2025-06-11 08:00:03,392 - INFO - 程序入口点: __main__
2025-06-11 08:00:03,392 - INFO - ==================================================
2025-06-11 08:00:03,392 - INFO - 程序启动 - 版本 v1.0.1
2025-06-11 08:00:03,392 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250611.log
2025-06-11 08:00:03,392 - INFO - ==================================================
2025-06-11 08:00:03,392 - INFO - MySQL数据库连接成功
2025-06-11 08:00:03,752 - INFO - MySQL数据库连接成功
2025-06-11 08:00:03,752 - INFO - sales_data表已存在，无需创建
2025-06-11 08:00:03,752 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-06-11 08:00:03,752 - INFO - DataSyncManager初始化完成
2025-06-11 08:00:03,752 - INFO - 开始更新店铺映射表...
2025-06-11 08:00:03,752 - INFO - 开始获取最近7天的店铺信息，时间范围: 2025-06-04 至 2025-06-10
2025-06-11 08:00:03,752 - INFO - 查询日期 ******** 的店铺信息
2025-06-11 08:00:03,752 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:00:03,752 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'E635E1EB6F00B93217CB79C5C5D85270'}
2025-06-11 08:00:04,877 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:00:04,877 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-11 08:00:05,377 - INFO - 查询日期 ******** 的店铺信息
2025-06-11 08:00:05,377 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:00:05,377 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'FAE9671FE457784A8C374F8DACB84465'}
2025-06-11 08:00:06,080 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:00:06,080 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-11 08:00:06,595 - INFO - 查询日期 ******** 的店铺信息
2025-06-11 08:00:06,595 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:00:06,595 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'F1361049A0B67513A03A83BFB730B4E7'}
2025-06-11 08:00:07,533 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:00:07,533 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-11 08:00:08,048 - INFO - 查询日期 ******** 的店铺信息
2025-06-11 08:00:08,048 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:00:08,048 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '2CF0D0B8CABE6375ABBD017C64388B1A'}
2025-06-11 08:00:09,127 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:00:09,127 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-11 08:00:09,642 - INFO - 查询日期 ******** 的店铺信息
2025-06-11 08:00:09,642 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:00:09,642 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '45B6A343BF31326C4205FCCDF0893275'}
2025-06-11 08:00:10,298 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:00:10,298 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-11 08:00:10,798 - INFO - 查询日期 ******** 的店铺信息
2025-06-11 08:00:10,798 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:00:10,798 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'B0B6A5B1E00886DCBCBD2CF50638FEA4'}
2025-06-11 08:00:11,751 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:00:11,751 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-11 08:00:12,267 - INFO - 查询日期 ******** 的店铺信息
2025-06-11 08:00:12,267 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:00:12,267 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'B385B8CA19A5130EBD504BEAD387E755'}
2025-06-11 08:00:13,033 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:00:13,033 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-11 08:00:13,736 - INFO - 店铺映射表更新完成，总计: 326条，成功: 326条 (更新: 326条, 插入: 0条)
2025-06-11 08:00:13,736 - INFO - 店铺映射表更新完成
2025-06-11 08:00:13,736 - INFO - 未提供日期参数，使用默认值
2025-06-11 08:00:13,736 - INFO - 开始执行综合数据同步，参数: start_date=None, end_date=None
2025-06-11 08:00:13,736 - INFO - 开始综合数据同步流程...
2025-06-11 08:00:13,736 - INFO - 当前错误日期列表为空
2025-06-11 08:00:13,736 - INFO - 正在获取数衍平台日销售数据...
2025-06-11 08:00:13,736 - INFO - 开始获取最近7天的店铺信息，时间范围: 2025-06-04 至 2025-06-10
2025-06-11 08:00:13,736 - INFO - 查询日期 ******** 的店铺信息
2025-06-11 08:00:13,736 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:00:13,736 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'A03D44EFEA0CCF706CA68D417E284E9F'}
2025-06-11 08:00:14,408 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:00:14,408 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-11 08:00:14,923 - INFO - 查询日期 ******** 的店铺信息
2025-06-11 08:00:14,923 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:00:14,923 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'F9106C490235F849E7A78F5E358BFA12'}
2025-06-11 08:00:15,720 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:00:15,720 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-11 08:00:16,236 - INFO - 查询日期 ******** 的店铺信息
2025-06-11 08:00:16,236 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:00:16,236 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '8A0EAF6205F586A4B6D10D9C82C7FB6E'}
2025-06-11 08:00:17,111 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:00:17,111 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-11 08:00:17,611 - INFO - 查询日期 ******** 的店铺信息
2025-06-11 08:00:17,611 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:00:17,611 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '9D73843B88137051529C67D3ED1D9B37'}
2025-06-11 08:00:18,330 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:00:18,330 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-11 08:00:18,845 - INFO - 查询日期 ******** 的店铺信息
2025-06-11 08:00:18,845 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:00:18,845 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '9A10A273769466EF8A8C4D74E3684F03'}
2025-06-11 08:00:19,673 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:00:19,673 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-11 08:00:20,189 - INFO - 查询日期 ******** 的店铺信息
2025-06-11 08:00:20,189 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:00:20,189 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '118B77B87A4AF0408A0ABD593A6DAF85'}
2025-06-11 08:00:20,845 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:00:20,845 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-11 08:00:21,361 - INFO - 查询日期 ******** 的店铺信息
2025-06-11 08:00:21,361 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:00:21,361 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '9406FEC07E530BE49B3A8116AB87A19D'}
2025-06-11 08:00:21,970 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:00:21,986 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-11 08:00:22,689 - INFO - 店铺映射表更新完成，总计: 326条，成功: 326条 (更新: 326条, 插入: 0条)
2025-06-11 08:00:22,689 - INFO - 查询数衍平台数据，时间段为: 2025-04-11, 2025-06-10
2025-06-11 08:00:22,689 - INFO - 正在获取********至********的数据
2025-06-11 08:00:22,689 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:00:22,689 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'C8179DFCA16EDBAC28347E223D1E6F76'}
2025-06-11 08:00:24,439 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:00:24,455 - INFO - 过滤后保留 432 条记录
2025-06-11 08:00:26,470 - INFO - 正在获取********至********的数据
2025-06-11 08:00:26,470 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:00:26,470 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '76B0F991EAF96BB2F64E007AC4AABF7C'}
2025-06-11 08:00:27,751 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:00:27,751 - INFO - 过滤后保留 434 条记录
2025-06-11 08:00:29,783 - INFO - 正在获取********至********的数据
2025-06-11 08:00:29,783 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:00:29,783 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'ABC17DC544AE8FB7F6A8F4864F728695'}
2025-06-11 08:00:31,017 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:00:31,017 - INFO - 过滤后保留 424 条记录
2025-06-11 08:00:33,033 - INFO - 正在获取********至********的数据
2025-06-11 08:00:33,033 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:00:33,033 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'CF6DE6E01E6A00CD23FB0AB266F0915E'}
2025-06-11 08:00:34,470 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:00:34,486 - INFO - 过滤后保留 436 条记录
2025-06-11 08:00:36,501 - INFO - 正在获取********至********的数据
2025-06-11 08:00:36,501 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:00:36,501 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '987A9AC6EE092F0CF645FCE9EC4832F9'}
2025-06-11 08:00:38,095 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:00:38,095 - INFO - 过滤后保留 431 条记录
2025-06-11 08:00:40,111 - INFO - 正在获取********至********的数据
2025-06-11 08:00:40,111 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:00:40,111 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '1927DF79BD010812F16F5B993CE654EA'}
2025-06-11 08:00:41,439 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:00:41,439 - INFO - 过滤后保留 425 条记录
2025-06-11 08:00:43,439 - INFO - 正在获取********至********的数据
2025-06-11 08:00:43,439 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:00:43,439 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'EAAF30923B781B8F9F7D578F3555964E'}
2025-06-11 08:00:45,001 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:00:45,001 - INFO - 过滤后保留 414 条记录
2025-06-11 08:00:47,001 - INFO - 正在获取********至********的数据
2025-06-11 08:00:47,001 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:00:47,001 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'B45B979F2FA5C62CD51A1F1D4E6408EC'}
2025-06-11 08:00:48,423 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:00:48,423 - INFO - 过滤后保留 427 条记录
2025-06-11 08:00:50,423 - INFO - 正在获取********至********的数据
2025-06-11 08:00:50,423 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:00:50,423 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '0151677DD1DD98A142376C0D6C2C2AFB'}
2025-06-11 08:00:51,595 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:00:51,595 - INFO - 过滤后保留 428 条记录
2025-06-11 08:00:53,611 - INFO - 正在获取********至********的数据
2025-06-11 08:00:53,611 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:00:53,611 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'DB7103D5C49177E6F70ACCADB23BC9A1'}
2025-06-11 08:00:54,876 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:00:54,876 - INFO - 过滤后保留 429 条记录
2025-06-11 08:00:56,892 - INFO - 正在获取********至********的数据
2025-06-11 08:00:56,892 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:00:56,892 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'CC76612BCD9AE58A261AC9DCBACF5F28'}
2025-06-11 08:00:58,548 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:00:58,548 - INFO - 过滤后保留 427 条记录
2025-06-11 08:01:00,564 - INFO - 正在获取********至********的数据
2025-06-11 08:01:00,564 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:01:00,564 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'EAD422FF877653D398D419D2938B8BD9'}
2025-06-11 08:01:01,829 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:01:01,829 - INFO - 过滤后保留 428 条记录
2025-06-11 08:01:03,829 - INFO - 正在获取********至********的数据
2025-06-11 08:01:03,829 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:01:03,829 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '1FC4AEC0AFCEACB73FDD74FFBC1F2CD5'}
2025-06-11 08:01:05,189 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:01:05,189 - INFO - 过滤后保留 417 条记录
2025-06-11 08:01:07,204 - INFO - 正在获取********至********的数据
2025-06-11 08:01:07,204 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:01:07,204 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '6133BB1F8CD0AEB4E2BB28174FC15B35'}
2025-06-11 08:01:08,517 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:01:08,517 - INFO - 过滤后保留 415 条记录
2025-06-11 08:01:10,532 - INFO - 正在获取********至********的数据
2025-06-11 08:01:10,532 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:01:10,532 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'E19F547A9147DC50894E49C33D741027'}
2025-06-11 08:01:11,860 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:01:11,876 - INFO - 过滤后保留 433 条记录
2025-06-11 08:01:13,892 - INFO - 正在获取********至********的数据
2025-06-11 08:01:13,892 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:01:13,892 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'A03EADAFE4DBC365077670B082AF8927'}
2025-06-11 08:01:15,173 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:01:15,173 - INFO - 过滤后保留 433 条记录
2025-06-11 08:01:17,189 - INFO - 正在获取********至********的数据
2025-06-11 08:01:17,189 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:01:17,189 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '4006C00E38C2C7853B99C98E7B842D06'}
2025-06-11 08:01:18,439 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:01:18,454 - INFO - 过滤后保留 417 条记录
2025-06-11 08:01:20,470 - INFO - 正在获取********至********的数据
2025-06-11 08:01:20,470 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:01:20,470 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '3B442FC011B2D45626121B15986FE262'}
2025-06-11 08:01:21,907 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:01:21,923 - INFO - 过滤后保留 420 条记录
2025-06-11 08:01:23,923 - INFO - 正在获取********至********的数据
2025-06-11 08:01:23,923 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:01:23,923 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'C72F27EC69C0B13CBFDDA87C0325ADA2'}
2025-06-11 08:01:25,407 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:01:25,407 - INFO - 过滤后保留 431 条记录
2025-06-11 08:01:27,423 - INFO - 正在获取********至********的数据
2025-06-11 08:01:27,423 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:01:27,423 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'C2C9CB6F5FAF3514E61E8FD006063730'}
2025-06-11 08:01:28,704 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:01:28,720 - INFO - 过滤后保留 423 条记录
2025-06-11 08:01:30,735 - INFO - 正在获取********至********的数据
2025-06-11 08:01:30,735 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:01:30,735 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '00110303A011D5D12CF0C304011D5645'}
2025-06-11 08:01:32,095 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:01:32,095 - INFO - 过滤后保留 416 条记录
2025-06-11 08:01:34,110 - INFO - 正在获取********至********的数据
2025-06-11 08:01:34,110 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:01:34,110 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '4E2053EDC8EC42C3ED64FC4342C8FB88'}
2025-06-11 08:01:35,579 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:01:35,595 - INFO - 过滤后保留 423 条记录
2025-06-11 08:01:37,610 - INFO - 正在获取********至********的数据
2025-06-11 08:01:37,610 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:01:37,610 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '********************************'}
2025-06-11 08:01:38,876 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:01:38,876 - INFO - 过滤后保留 414 条记录
2025-06-11 08:01:40,892 - INFO - 正在获取********至********的数据
2025-06-11 08:01:40,892 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:01:40,892 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'D8A27F7DCAFDA8857FC3B7129C20E340'}
2025-06-11 08:01:42,157 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:01:42,157 - INFO - 过滤后保留 413 条记录
2025-06-11 08:01:44,157 - INFO - 正在获取********至********的数据
2025-06-11 08:01:44,157 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:01:44,157 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'CFAC0C182518345EDFB41362F644D84D'}
2025-06-11 08:01:45,532 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:01:45,532 - INFO - 过滤后保留 414 条记录
2025-06-11 08:01:47,548 - INFO - 正在获取********至********的数据
2025-06-11 08:01:47,548 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:01:47,548 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '9BDFECCB353E383D2D8F9D5E1F7D4E49'}
2025-06-11 08:01:48,938 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:01:48,938 - INFO - 过滤后保留 415 条记录
2025-06-11 08:01:50,954 - INFO - 正在获取********至********的数据
2025-06-11 08:01:50,954 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:01:50,954 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'A4C860D8F6F14F47B80F82C061C649DA'}
2025-06-11 08:01:52,501 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:01:52,501 - INFO - 过滤后保留 401 条记录
2025-06-11 08:01:54,501 - INFO - 正在获取********至********的数据
2025-06-11 08:01:54,501 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:01:54,501 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '8D4AC5DB540238A4425BE8F205803C88'}
2025-06-11 08:01:56,032 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:01:56,032 - INFO - 过滤后保留 402 条记录
2025-06-11 08:01:58,048 - INFO - 正在获取********至********的数据
2025-06-11 08:01:58,048 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:01:58,048 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'EF5D08670BB98EE740C41832A78C7EDA'}
2025-06-11 08:01:59,423 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:01:59,438 - INFO - 过滤后保留 409 条记录
2025-06-11 08:02:01,454 - INFO - 正在获取********至********的数据
2025-06-11 08:02:01,454 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:02:01,454 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'D9CAF09D217625598C91DACB35D844CE'}
2025-06-11 08:02:02,641 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:02:02,641 - INFO - 过滤后保留 399 条记录
2025-06-11 08:02:04,657 - INFO - 正在获取********至********的数据
2025-06-11 08:02:04,657 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-11 08:02:04,657 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '64F87ACB09CD241C30AF437D12312032'}
2025-06-11 08:02:05,610 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-11 08:02:05,626 - INFO - 过滤后保留 202 条记录
2025-06-11 08:02:07,641 - INFO - 开始保存数据到MySQL数据库，共 12832 条记录待处理
2025-06-11 08:02:14,719 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDP34FLR400I86N3H2U1MG001EVM, sale_time=2025-05-18
2025-06-11 08:02:14,719 - INFO - 变更字段: recommend_amount: 27326.3 -> 27825.8, amount: 27326 -> 27825, count: 53 -> 54, instore_amount: 27326.3 -> 27825.8, instore_count: 53 -> 54
2025-06-11 08:02:17,173 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1I6E0VAU3IFJEQ22MH147FMU0M0013E8, sale_time=2025-06-01
2025-06-11 08:02:17,173 - INFO - 变更字段: recommend_amount: 0.0 -> 1135.0, daily_bill_amount: 0.0 -> 1135.0
2025-06-11 08:02:17,532 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1I6E0VAU3IFJEQ22MH147FMU0M0013E8, sale_time=2025-06-03
2025-06-11 08:02:17,532 - INFO - 变更字段: recommend_amount: 0.0 -> 836.4, daily_bill_amount: 0.0 -> 836.4
2025-06-11 08:02:17,532 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1I6E0VAU3IFJEQ22MH147FMU0M0013E8, sale_time=2025-06-02
2025-06-11 08:02:17,532 - INFO - 变更字段: recommend_amount: 0.0 -> 2780.7, daily_bill_amount: 0.0 -> 2780.7
2025-06-11 08:02:17,594 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S, sale_time=2025-06-02
2025-06-11 08:02:17,594 - INFO - 变更字段: recommend_amount: 10592.1 -> 10729.4, amount: 10592 -> 10729, count: 289 -> 290, instore_amount: 10023.3 -> 10160.6, instore_count: 275 -> 276
2025-06-11 08:02:17,907 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1I6E0VAU3IFJEQ22MH147FMU0M0013E8, sale_time=2025-06-05
2025-06-11 08:02:17,907 - INFO - 变更字段: recommend_amount: 0.0 -> 2475.7, daily_bill_amount: 0.0 -> 2475.7
2025-06-11 08:02:18,157 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDNMK1P3900I86N3H2U1UO001E7U, sale_time=2025-06-04
2025-06-11 08:02:18,157 - INFO - 变更字段: amount: 2801 -> 2811, count: 59 -> 60, instore_amount: 1802.1 -> 1812.0, instore_count: 42 -> 43
2025-06-11 08:02:18,298 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1I6E0VAU3IFJEQ22MH147FMU0M0013E8, sale_time=2025-06-07
2025-06-11 08:02:18,298 - INFO - 变更字段: recommend_amount: 0.0 -> 2357.9, daily_bill_amount: 0.0 -> 2357.9
2025-06-11 08:02:18,298 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1I6E0VAU3IFJEQ22MH147FMU0M0013E8, sale_time=2025-06-06
2025-06-11 08:02:18,298 - INFO - 变更字段: recommend_amount: 0.0 -> 2425.5, daily_bill_amount: 0.0 -> 2425.5
2025-06-11 08:02:18,391 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT72H2L6227Q2OVBN4IS62001D24, sale_time=2025-06-07
2025-06-11 08:02:18,391 - INFO - 变更字段: amount: 135 -> 514, count: 1 -> 2, instore_amount: 214.0 -> 593.0, instore_count: 1 -> 2
2025-06-11 08:02:18,501 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDP34FLR400I86N3H2U1MG001EVM, sale_time=2025-06-07
2025-06-11 08:02:18,501 - INFO - 变更字段: recommend_amount: 17536.26 -> 18167.26, amount: 17536 -> 18167, count: 82 -> 83, instore_amount: 17582.26 -> 18213.26, instore_count: 82 -> 83
2025-06-11 08:02:18,516 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJP082LC00I86N3H2U1JM001ESS, sale_time=2025-06-07
2025-06-11 08:02:18,516 - INFO - 变更字段: recommend_amount: 946.4 -> 955.0, amount: 946 -> 955, instore_amount: 946.4 -> 955.0
2025-06-11 08:02:18,673 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=F56E8E23D2584556A30D1378611DF4AE, sale_time=2025-06-09
2025-06-11 08:02:18,673 - INFO - 变更字段: recommend_amount: 0.0 -> 5399.7, daily_bill_amount: 0.0 -> 5399.7
2025-06-11 08:02:18,688 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=6692283A183D432BAE322E1032539CE8, sale_time=2025-06-09
2025-06-11 08:02:18,688 - INFO - 变更字段: daily_bill_amount: 0.0 -> 12423.9
2025-06-11 08:02:18,704 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1I6E0VAU3IFJEQ22MH147FMU0M0013E8, sale_time=2025-06-09
2025-06-11 08:02:18,704 - INFO - 变更字段: recommend_amount: 0.0 -> 429.11, daily_bill_amount: 0.0 -> 429.11, amount: 58 -> 685, count: 2 -> 3, instore_amount: 58.9 -> 685.9, instore_count: 2 -> 3
2025-06-11 08:02:18,704 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1I6E0VAU3IFJEQ22MH147FMU0M0013E8, sale_time=2025-06-08
2025-06-11 08:02:18,704 - INFO - 变更字段: recommend_amount: 0.0 -> 935.0, daily_bill_amount: 0.0 -> 935.0
2025-06-11 08:02:18,719 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EE2TORA7KI7Q2OV4FVC7FC0014BT, sale_time=2025-06-09
2025-06-11 08:02:18,719 - INFO - 变更字段: amount: 2012 -> 2146, count: 59 -> 60, instore_amount: 1368.9 -> 1503.04, instore_count: 46 -> 47
2025-06-11 08:02:18,735 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDTG15Q4SH7Q2OV4FVC7CO001499, sale_time=2025-06-09
2025-06-11 08:02:18,735 - INFO - 变更字段: amount: 3515 -> 3498, online_amount: 1303.5 -> 1287.0
2025-06-11 08:02:18,751 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDQDVL1EG67Q2OV4FVC7B800147P, sale_time=2025-06-09
2025-06-11 08:02:18,751 - INFO - 变更字段: amount: 1120 -> 1139, count: 53 -> 54, instore_amount: 405.0 -> 424.0, instore_count: 14 -> 15
2025-06-11 08:02:18,766 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTCIR7D5JD7Q2OVBN4IS7U001D40, sale_time=2025-06-09
2025-06-11 08:02:18,766 - INFO - 变更字段: count: 10 -> 11, instore_count: 10 -> 11
2025-06-11 08:02:18,766 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT72H2L6227Q2OVBN4IS62001D24, sale_time=2025-06-09
2025-06-11 08:02:18,766 - INFO - 变更字段: amount: 313 -> 1812, count: 1 -> 3, instore_amount: 421.0 -> 1920.03, instore_count: 1 -> 3
2025-06-11 08:02:18,798 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1I16GTCIOI81KU0UR9LEHSI4JM001PFU, sale_time=2025-06-09
2025-06-11 08:02:18,798 - INFO - 变更字段: recommend_amount: 748.29 -> 2159.44, daily_bill_amount: 748.29 -> 2159.44
2025-06-11 08:02:18,798 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-06-09
2025-06-11 08:02:18,798 - INFO - 变更字段: recommend_amount: 2385.14 -> 2418.24, amount: 2385 -> 2418, count: 128 -> 129, online_amount: 2108.81 -> 2141.91, online_count: 112 -> 113
2025-06-11 08:02:18,813 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEO8LGKTTH42F6DB81RH8V001P57, sale_time=2025-06-09
2025-06-11 08:02:18,813 - INFO - 变更字段: recommend_amount: 1944.58 -> 1948.58, amount: 1944 -> 1948, instore_amount: 1957.61 -> 1961.61
2025-06-11 08:02:18,813 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEO8LGKTTH42F6DB81RH8V001P57, sale_time=2025-06-08
2025-06-11 08:02:18,813 - INFO - 变更字段: recommend_amount: 1872.33 -> 1874.33, amount: 1872 -> 1874, instore_amount: 1931.72 -> 1933.72
2025-06-11 08:02:18,813 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEMFCSIPU442F6DB81RH7T001P45, sale_time=2025-06-09
2025-06-11 08:02:18,813 - INFO - 变更字段: recommend_amount: 6885.5 -> 7868.0, daily_bill_amount: 6885.5 -> 7868.0
2025-06-11 08:02:18,829 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJ92AGQAT42F6DB81RH6G001P2O, sale_time=2025-06-09
2025-06-11 08:02:18,829 - INFO - 变更字段: count: 144 -> 145, online_amount: 2254.5 -> 2254.6, online_count: 105 -> 106
2025-06-11 08:02:18,829 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=B5A5FB25D4B04323BCABB528AF5E427E, sale_time=2025-06-09
2025-06-11 08:02:18,829 - INFO - 变更字段: recommend_amount: 1207.72 -> 1172.43, amount: 1207 -> 1172, online_amount: 750.62 -> 715.33
2025-06-11 08:02:18,844 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HO90F90E71NK12I1UUTD5AE7C001O7G, sale_time=2025-06-09
2025-06-11 08:02:18,844 - INFO - 变更字段: recommend_amount: 4911.8 -> 4925.6, amount: 4911 -> 4925, count: 235 -> 237, online_amount: 3775.21 -> 3789.01, online_count: 177 -> 179
2025-06-11 08:02:18,844 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEB9O4F53S0I86N3H2U1VT001F93, sale_time=2025-06-09
2025-06-11 08:02:18,844 - INFO - 变更字段: recommend_amount: 0.0 -> 14425.94, daily_bill_amount: 0.0 -> 14425.94
2025-06-11 08:02:18,860 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEA4041D6F0I86N3H2U1V9001F8F, sale_time=2025-06-09
2025-06-11 08:02:18,860 - INFO - 变更字段: amount: 15073 -> 15095, count: 141 -> 142, online_amount: 1886.02 -> 1908.32, online_count: 67 -> 68
2025-06-11 08:02:18,876 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE25DAIM3B0I86N3H2U1RD001F4J, sale_time=2025-06-09
2025-06-11 08:02:18,876 - INFO - 变更字段: recommend_amount: 0.0 -> 10429.34, daily_bill_amount: 0.0 -> 10429.34
2025-06-11 08:02:18,876 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-06-09
2025-06-11 08:02:18,876 - INFO - 变更字段: amount: 7335 -> 7434, count: 332 -> 337, instore_amount: 2352.8 -> 2419.6, instore_count: 122 -> 124, online_amount: 5037.78 -> 5069.58, online_count: 210 -> 213
2025-06-11 08:02:18,923 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCE13J2R9CI0I86N3H2U13D001ECJ, sale_time=2025-06-09
2025-06-11 08:02:18,923 - INFO - 变更字段: recommend_amount: 0.0 -> 4615.0, daily_bill_amount: 0.0 -> 4615.0
2025-06-11 08:02:18,938 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDU5QKVU3D0I86N3H2U11T001EB3, sale_time=2025-06-09
2025-06-11 08:02:18,938 - INFO - 变更字段: amount: 3343 -> 3344, count: 221 -> 228, online_amount: 3306.94 -> 3337.44, online_count: 213 -> 220
2025-06-11 08:02:18,938 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDSK489TE20I86N3H2U114001EAA, sale_time=2025-06-09
2025-06-11 08:02:18,938 - INFO - 变更字段: recommend_amount: 7884.72 -> 7925.12, amount: 7884 -> 7925, count: 60 -> 61, instore_amount: 7714.22 -> 7754.62, instore_count: 57 -> 58
2025-06-11 08:02:18,954 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-06-09
2025-06-11 08:02:18,954 - INFO - 变更字段: instore_amount: 3732.22 -> 3740.22, instore_count: 218 -> 221, online_amount: 1217.7 -> 1209.7, online_count: 86 -> 83
2025-06-11 08:02:18,954 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-06-08
2025-06-11 08:02:18,954 - INFO - 变更字段: instore_amount: 4017.2 -> 4024.2, instore_count: 267 -> 269, online_amount: 2157.0 -> 2150.0, online_count: 160 -> 158
2025-06-11 08:02:18,969 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1I14PQ9FMDG1FG3MDGKU4P0I8N00178C, sale_time=2025-06-09
2025-06-11 08:02:18,969 - INFO - 变更字段: recommend_amount: 7146.26 -> 7566.77, amount: 7146 -> 7566, count: 294 -> 325, online_amount: 7451.96 -> 7926.47, online_count: 294 -> 325
2025-06-11 08:02:18,969 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1I14PQ9FMDG1FG3MDGKU4P0I8N00178C, sale_time=2025-06-08
2025-06-11 08:02:18,969 - INFO - 变更字段: recommend_amount: 654.25 -> 687.15, amount: 654 -> 687, count: 52 -> 54, online_amount: 736.85 -> 769.75, online_count: 52 -> 54
2025-06-11 08:02:19,001 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R, sale_time=2025-06-09
2025-06-11 08:02:19,001 - INFO - 变更字段: count: 162 -> 163, instore_count: 69 -> 70
2025-06-11 08:02:19,001 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0NLURMV9D3IKSIOCDI7R2001G23, sale_time=2025-06-09
2025-06-11 08:02:19,001 - INFO - 变更字段: recommend_amount: 0.0 -> 3641.99, daily_bill_amount: 0.0 -> 3641.99
2025-06-11 08:02:19,016 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK, sale_time=2025-06-09
2025-06-11 08:02:19,016 - INFO - 变更字段: amount: 5505 -> 5734, count: 40 -> 41, instore_amount: 5039.8 -> 5268.8, instore_count: 30 -> 31
2025-06-11 08:02:19,016 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8, sale_time=2025-06-09
2025-06-11 08:02:19,016 - INFO - 变更字段: amount: 27556 -> 29765, count: 108 -> 110, instore_amount: 26317.17 -> 28500.78, instore_count: 68 -> 69, online_amount: 1239.7 -> 1264.6, online_count: 40 -> 41
2025-06-11 08:02:19,016 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=E79F261889C1492982227C207062C267, sale_time=2025-06-09
2025-06-11 08:02:19,016 - INFO - 变更字段: amount: 11496 -> 11197
2025-06-11 08:02:19,032 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUISOVAPU1P7AV8LHQQGIDU001EK7, sale_time=2025-06-09
2025-06-11 08:02:19,032 - INFO - 变更字段: amount: 8735 -> 10876, count: 7 -> 8, instore_amount: 8735.9 -> 10876.8, instore_count: 7 -> 8
2025-06-11 08:02:19,032 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIS7OTCALF7AV8LHQQGIDO001EK1, sale_time=2025-06-09
2025-06-11 08:02:19,032 - INFO - 变更字段: amount: 27121 -> 33187, count: 142 -> 149, instore_amount: 25263.04 -> 31329.49, instore_count: 119 -> 126
2025-06-11 08:02:19,063 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=14EB204A0BDE44888B43308269C1626A, sale_time=2025-06-09
2025-06-11 08:02:19,063 - INFO - 变更字段: amount: 1110 -> 1104
2025-06-11 08:02:19,313 - INFO - MySQL数据保存完成，统计信息：
2025-06-11 08:02:19,313 - INFO - - 总记录数: 12832
2025-06-11 08:02:19,313 - INFO - - 成功插入: 213
2025-06-11 08:02:19,313 - INFO - - 成功更新: 48
2025-06-11 08:02:19,313 - INFO - - 无需更新: 12571
2025-06-11 08:02:19,313 - INFO - - 处理失败: 0
2025-06-11 08:02:19,313 - INFO - 成功获取数衍平台数据，共 12832 条记录
2025-06-11 08:02:19,313 - INFO - 正在更新MySQL月度汇总数据...
2025-06-11 08:02:19,360 - INFO - 月度数据表清空完成
2025-06-11 08:02:19,641 - INFO - 月度汇总数据更新完成，处理了 1404 条汇总记录
2025-06-11 08:02:19,657 - INFO - 成功更新月度汇总数据，共 1404 条记录
2025-06-11 08:02:19,657 - INFO - 正在获取宜搭日销售表单数据...
2025-06-11 08:02:19,657 - INFO - 开始获取宜搭每日表单数据，总时间范围: 2025-04-11 00:00:00 至 2025-06-10 23:59:59
2025-06-11 08:02:19,657 - INFO - 查询分段 1: 2025-04-11 至 2025-04-12
2025-06-11 08:02:19,657 - INFO - 查询日期范围: 2025-04-11 至 2025-04-12，使用分页查询，每页 100 条记录
2025-06-11 08:02:19,657 - INFO - Request Parameters - Page 1:
2025-06-11 08:02:19,657 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:02:19,657 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800657, 1744387200657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:02:23,860 - INFO - API请求耗时: 4203ms
2025-06-11 08:02:23,860 - INFO - Response - Page 1
2025-06-11 08:02:23,860 - INFO - 第 1 页获取到 100 条记录
2025-06-11 08:02:24,360 - INFO - Request Parameters - Page 2:
2025-06-11 08:02:24,360 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:02:24,360 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800657, 1744387200657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:02:25,063 - INFO - API请求耗时: 703ms
2025-06-11 08:02:25,063 - INFO - Response - Page 2
2025-06-11 08:02:25,063 - INFO - 第 2 页获取到 100 条记录
2025-06-11 08:02:25,579 - INFO - Request Parameters - Page 3:
2025-06-11 08:02:25,579 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:02:25,579 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800657, 1744387200657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:02:26,297 - INFO - API请求耗时: 719ms
2025-06-11 08:02:26,297 - INFO - Response - Page 3
2025-06-11 08:02:26,297 - INFO - 第 3 页获取到 100 条记录
2025-06-11 08:02:26,797 - INFO - Request Parameters - Page 4:
2025-06-11 08:02:26,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:02:26,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800657, 1744387200657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:02:27,469 - INFO - API请求耗时: 672ms
2025-06-11 08:02:27,469 - INFO - Response - Page 4
2025-06-11 08:02:27,469 - INFO - 第 4 页获取到 100 条记录
2025-06-11 08:02:27,969 - INFO - Request Parameters - Page 5:
2025-06-11 08:02:27,969 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:02:27,969 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800657, 1744387200657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:02:28,547 - INFO - API请求耗时: 578ms
2025-06-11 08:02:28,547 - INFO - Response - Page 5
2025-06-11 08:02:28,547 - INFO - 第 5 页获取到 42 条记录
2025-06-11 08:02:28,547 - INFO - 查询完成，共获取到 442 条记录
2025-06-11 08:02:28,547 - INFO - 分段 1 查询成功，获取到 442 条记录
2025-06-11 08:02:29,563 - INFO - 查询分段 2: 2025-04-13 至 2025-04-14
2025-06-11 08:02:29,563 - INFO - 查询日期范围: 2025-04-13 至 2025-04-14，使用分页查询，每页 100 条记录
2025-06-11 08:02:29,563 - INFO - Request Parameters - Page 1:
2025-06-11 08:02:29,563 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:02:29,563 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600657, 1744560000657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:02:37,672 - ERROR - API请求失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A1D8022D-BD2E-7751-B1F5-CD3FF324D850 Response: {'code': 'ServiceUnavailable', 'requestid': 'A1D8022D-BD2E-7751-B1F5-CD3FF324D850', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503}
2025-06-11 08:02:37,672 - WARNING - 已将错误日期范围添加到排除列表: 2025-04-13 至 2025-04-14
2025-06-11 08:02:37,672 - ERROR - 服务不可用，将等待后重试
2025-06-11 08:02:37,672 - ERROR - 获取第 1 页数据时出错: 服务不可用: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A1D8022D-BD2E-7751-B1F5-CD3FF324D850 Response: {'code': 'ServiceUnavailable', 'requestid': 'A1D8022D-BD2E-7751-B1F5-CD3FF324D850', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503}
2025-06-11 08:02:37,672 - WARNING - 服务暂时不可用，等待 6 秒后重试...
2025-06-11 08:02:43,688 - WARNING - 服务暂时不可用，将等待更长时间: 10秒
2025-06-11 08:02:43,688 - WARNING - 获取表单数据失败 (尝试 1/3): 服务不可用: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A1D8022D-BD2E-7751-B1F5-CD3FF324D850 Response: {'code': 'ServiceUnavailable', 'requestid': 'A1D8022D-BD2E-7751-B1F5-CD3FF324D850', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503}，将在 10 秒后重试...
2025-06-11 08:02:53,704 - INFO - Request Parameters - Page 1:
2025-06-11 08:02:53,704 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:02:53,704 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600657, 1744560000657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:02:54,391 - INFO - API请求耗时: 688ms
2025-06-11 08:02:54,391 - INFO - Response - Page 1
2025-06-11 08:02:54,391 - INFO - 第 1 页获取到 100 条记录
2025-06-11 08:02:54,907 - INFO - Request Parameters - Page 2:
2025-06-11 08:02:54,907 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:02:54,907 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600657, 1744560000657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:02:55,750 - INFO - API请求耗时: 844ms
2025-06-11 08:02:55,750 - INFO - Response - Page 2
2025-06-11 08:02:55,750 - INFO - 第 2 页获取到 100 条记录
2025-06-11 08:02:56,266 - INFO - Request Parameters - Page 3:
2025-06-11 08:02:56,266 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:02:56,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600657, 1744560000657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:02:57,063 - INFO - API请求耗时: 797ms
2025-06-11 08:02:57,063 - INFO - Response - Page 3
2025-06-11 08:02:57,063 - INFO - 第 3 页获取到 100 条记录
2025-06-11 08:02:57,579 - INFO - Request Parameters - Page 4:
2025-06-11 08:02:57,579 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:02:57,579 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600657, 1744560000657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:02:58,250 - INFO - API请求耗时: 672ms
2025-06-11 08:02:58,250 - INFO - Response - Page 4
2025-06-11 08:02:58,250 - INFO - 第 4 页获取到 100 条记录
2025-06-11 08:02:58,750 - INFO - Request Parameters - Page 5:
2025-06-11 08:02:58,750 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:02:58,750 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600657, 1744560000657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:02:59,391 - INFO - API请求耗时: 641ms
2025-06-11 08:02:59,391 - INFO - Response - Page 5
2025-06-11 08:02:59,391 - INFO - 第 5 页获取到 30 条记录
2025-06-11 08:02:59,391 - INFO - 查询完成，共获取到 430 条记录
2025-06-11 08:02:59,391 - INFO - 分段 2 查询成功，获取到 430 条记录
2025-06-11 08:03:00,407 - INFO - 查询分段 3: 2025-04-15 至 2025-04-16
2025-06-11 08:03:00,407 - INFO - 查询日期范围: 2025-04-15 至 2025-04-16，使用分页查询，每页 100 条记录
2025-06-11 08:03:00,407 - INFO - Request Parameters - Page 1:
2025-06-11 08:03:00,407 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:00,407 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400657, 1744732800657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:01,203 - INFO - API请求耗时: 797ms
2025-06-11 08:03:01,203 - INFO - Response - Page 1
2025-06-11 08:03:01,203 - INFO - 第 1 页获取到 100 条记录
2025-06-11 08:03:01,719 - INFO - Request Parameters - Page 2:
2025-06-11 08:03:01,719 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:01,719 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400657, 1744732800657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:02,422 - INFO - API请求耗时: 703ms
2025-06-11 08:03:02,422 - INFO - Response - Page 2
2025-06-11 08:03:02,422 - INFO - 第 2 页获取到 100 条记录
2025-06-11 08:03:02,922 - INFO - Request Parameters - Page 3:
2025-06-11 08:03:02,922 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:02,922 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400657, 1744732800657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:03,657 - INFO - API请求耗时: 734ms
2025-06-11 08:03:03,657 - INFO - Response - Page 3
2025-06-11 08:03:03,657 - INFO - 第 3 页获取到 100 条记录
2025-06-11 08:03:04,157 - INFO - Request Parameters - Page 4:
2025-06-11 08:03:04,157 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:04,157 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400657, 1744732800657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:05,000 - INFO - API请求耗时: 844ms
2025-06-11 08:03:05,000 - INFO - Response - Page 4
2025-06-11 08:03:05,000 - INFO - 第 4 页获取到 100 条记录
2025-06-11 08:03:05,516 - INFO - Request Parameters - Page 5:
2025-06-11 08:03:05,516 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:05,516 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400657, 1744732800657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:06,063 - INFO - API请求耗时: 547ms
2025-06-11 08:03:06,063 - INFO - Response - Page 5
2025-06-11 08:03:06,063 - INFO - 第 5 页获取到 20 条记录
2025-06-11 08:03:06,063 - INFO - 查询完成，共获取到 420 条记录
2025-06-11 08:03:06,063 - INFO - 分段 3 查询成功，获取到 420 条记录
2025-06-11 08:03:07,063 - INFO - 查询分段 4: 2025-04-17 至 2025-04-18
2025-06-11 08:03:07,063 - INFO - 查询日期范围: 2025-04-17 至 2025-04-18，使用分页查询，每页 100 条记录
2025-06-11 08:03:07,063 - INFO - Request Parameters - Page 1:
2025-06-11 08:03:07,063 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:07,063 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200657, 1744905600657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:07,750 - INFO - API请求耗时: 687ms
2025-06-11 08:03:07,750 - INFO - Response - Page 1
2025-06-11 08:03:07,766 - INFO - 第 1 页获取到 100 条记录
2025-06-11 08:03:08,266 - INFO - Request Parameters - Page 2:
2025-06-11 08:03:08,266 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:08,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200657, 1744905600657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:08,985 - INFO - API请求耗时: 719ms
2025-06-11 08:03:08,985 - INFO - Response - Page 2
2025-06-11 08:03:08,985 - INFO - 第 2 页获取到 100 条记录
2025-06-11 08:03:09,485 - INFO - Request Parameters - Page 3:
2025-06-11 08:03:09,485 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:09,485 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200657, 1744905600657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:10,219 - INFO - API请求耗时: 734ms
2025-06-11 08:03:10,219 - INFO - Response - Page 3
2025-06-11 08:03:10,219 - INFO - 第 3 页获取到 100 条记录
2025-06-11 08:03:10,735 - INFO - Request Parameters - Page 4:
2025-06-11 08:03:10,735 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:10,735 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200657, 1744905600657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:11,469 - INFO - API请求耗时: 734ms
2025-06-11 08:03:11,469 - INFO - Response - Page 4
2025-06-11 08:03:11,469 - INFO - 第 4 页获取到 100 条记录
2025-06-11 08:03:11,969 - INFO - Request Parameters - Page 5:
2025-06-11 08:03:11,969 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:11,969 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200657, 1744905600657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:12,532 - INFO - API请求耗时: 562ms
2025-06-11 08:03:12,532 - INFO - Response - Page 5
2025-06-11 08:03:12,532 - INFO - 第 5 页获取到 34 条记录
2025-06-11 08:03:12,532 - INFO - 查询完成，共获取到 434 条记录
2025-06-11 08:03:12,532 - INFO - 分段 4 查询成功，获取到 434 条记录
2025-06-11 08:03:13,532 - INFO - 查询分段 5: 2025-04-19 至 2025-04-20
2025-06-11 08:03:13,532 - INFO - 查询日期范围: 2025-04-19 至 2025-04-20，使用分页查询，每页 100 条记录
2025-06-11 08:03:13,532 - INFO - Request Parameters - Page 1:
2025-06-11 08:03:13,532 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:13,532 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744992000657, 1745078400657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:14,203 - INFO - API请求耗时: 672ms
2025-06-11 08:03:14,203 - INFO - Response - Page 1
2025-06-11 08:03:14,203 - INFO - 第 1 页获取到 100 条记录
2025-06-11 08:03:14,703 - INFO - Request Parameters - Page 2:
2025-06-11 08:03:14,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:14,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744992000657, 1745078400657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:15,360 - INFO - API请求耗时: 656ms
2025-06-11 08:03:15,360 - INFO - Response - Page 2
2025-06-11 08:03:15,360 - INFO - 第 2 页获取到 100 条记录
2025-06-11 08:03:15,875 - INFO - Request Parameters - Page 3:
2025-06-11 08:03:15,875 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:15,875 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744992000657, 1745078400657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:16,594 - INFO - API请求耗时: 719ms
2025-06-11 08:03:16,594 - INFO - Response - Page 3
2025-06-11 08:03:16,594 - INFO - 第 3 页获取到 100 条记录
2025-06-11 08:03:17,110 - INFO - Request Parameters - Page 4:
2025-06-11 08:03:17,110 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:17,110 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744992000657, 1745078400657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:17,797 - INFO - API请求耗时: 688ms
2025-06-11 08:03:17,797 - INFO - Response - Page 4
2025-06-11 08:03:17,797 - INFO - 第 4 页获取到 100 条记录
2025-06-11 08:03:18,313 - INFO - Request Parameters - Page 5:
2025-06-11 08:03:18,313 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:18,313 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744992000657, 1745078400657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:19,000 - INFO - API请求耗时: 687ms
2025-06-11 08:03:19,000 - INFO - Response - Page 5
2025-06-11 08:03:19,000 - INFO - 第 5 页获取到 32 条记录
2025-06-11 08:03:19,000 - INFO - 查询完成，共获取到 432 条记录
2025-06-11 08:03:19,016 - INFO - 分段 5 查询成功，获取到 432 条记录
2025-06-11 08:03:20,031 - INFO - 查询分段 6: 2025-04-21 至 2025-04-22
2025-06-11 08:03:20,031 - INFO - 查询日期范围: 2025-04-21 至 2025-04-22，使用分页查询，每页 100 条记录
2025-06-11 08:03:20,031 - INFO - Request Parameters - Page 1:
2025-06-11 08:03:20,031 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:20,031 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800657, 1745251200657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:20,750 - INFO - API请求耗时: 719ms
2025-06-11 08:03:20,750 - INFO - Response - Page 1
2025-06-11 08:03:20,750 - INFO - 第 1 页获取到 100 条记录
2025-06-11 08:03:21,266 - INFO - Request Parameters - Page 2:
2025-06-11 08:03:21,266 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:21,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800657, 1745251200657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:22,000 - INFO - API请求耗时: 734ms
2025-06-11 08:03:22,000 - INFO - Response - Page 2
2025-06-11 08:03:22,000 - INFO - 第 2 页获取到 100 条记录
2025-06-11 08:03:22,500 - INFO - Request Parameters - Page 3:
2025-06-11 08:03:22,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:22,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800657, 1745251200657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:23,360 - INFO - API请求耗时: 859ms
2025-06-11 08:03:23,360 - INFO - Response - Page 3
2025-06-11 08:03:23,360 - INFO - 第 3 页获取到 100 条记录
2025-06-11 08:03:23,860 - INFO - Request Parameters - Page 4:
2025-06-11 08:03:23,860 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:23,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800657, 1745251200657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:24,688 - INFO - API请求耗时: 828ms
2025-06-11 08:03:24,688 - INFO - Response - Page 4
2025-06-11 08:03:24,688 - INFO - 第 4 页获取到 100 条记录
2025-06-11 08:03:25,188 - INFO - Request Parameters - Page 5:
2025-06-11 08:03:25,188 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:25,188 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800657, 1745251200657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:25,656 - INFO - API请求耗时: 469ms
2025-06-11 08:03:25,656 - INFO - Response - Page 5
2025-06-11 08:03:25,656 - INFO - 第 5 页获取到 24 条记录
2025-06-11 08:03:25,656 - INFO - 查询完成，共获取到 424 条记录
2025-06-11 08:03:25,656 - INFO - 分段 6 查询成功，获取到 424 条记录
2025-06-11 08:03:26,672 - INFO - 查询分段 7: 2025-04-23 至 2025-04-24
2025-06-11 08:03:26,672 - INFO - 查询日期范围: 2025-04-23 至 2025-04-24，使用分页查询，每页 100 条记录
2025-06-11 08:03:26,672 - INFO - Request Parameters - Page 1:
2025-06-11 08:03:26,672 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:26,672 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600657, 1745424000657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:27,578 - INFO - API请求耗时: 906ms
2025-06-11 08:03:27,578 - INFO - Response - Page 1
2025-06-11 08:03:27,578 - INFO - 第 1 页获取到 100 条记录
2025-06-11 08:03:28,094 - INFO - Request Parameters - Page 2:
2025-06-11 08:03:28,094 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:28,094 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600657, 1745424000657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:28,797 - INFO - API请求耗时: 703ms
2025-06-11 08:03:28,797 - INFO - Response - Page 2
2025-06-11 08:03:28,797 - INFO - 第 2 页获取到 100 条记录
2025-06-11 08:03:29,328 - INFO - Request Parameters - Page 3:
2025-06-11 08:03:29,328 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:29,328 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600657, 1745424000657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:30,016 - INFO - API请求耗时: 687ms
2025-06-11 08:03:30,016 - INFO - Response - Page 3
2025-06-11 08:03:30,031 - INFO - 第 3 页获取到 100 条记录
2025-06-11 08:03:30,531 - INFO - Request Parameters - Page 4:
2025-06-11 08:03:30,531 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:30,531 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600657, 1745424000657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:31,250 - INFO - API请求耗时: 719ms
2025-06-11 08:03:31,250 - INFO - Response - Page 4
2025-06-11 08:03:31,250 - INFO - 第 4 页获取到 100 条记录
2025-06-11 08:03:31,750 - INFO - Request Parameters - Page 5:
2025-06-11 08:03:31,750 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:31,750 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600657, 1745424000657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:32,266 - INFO - API请求耗时: 516ms
2025-06-11 08:03:32,266 - INFO - Response - Page 5
2025-06-11 08:03:32,266 - INFO - 第 5 页获取到 14 条记录
2025-06-11 08:03:32,266 - INFO - 查询完成，共获取到 414 条记录
2025-06-11 08:03:32,281 - INFO - 分段 7 查询成功，获取到 414 条记录
2025-06-11 08:03:33,281 - INFO - 查询分段 8: 2025-04-25 至 2025-04-26
2025-06-11 08:03:33,281 - INFO - 查询日期范围: 2025-04-25 至 2025-04-26，使用分页查询，每页 100 条记录
2025-06-11 08:03:33,281 - INFO - Request Parameters - Page 1:
2025-06-11 08:03:33,281 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:33,281 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400657, 1745596800657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:33,969 - INFO - API请求耗时: 687ms
2025-06-11 08:03:33,969 - INFO - Response - Page 1
2025-06-11 08:03:33,985 - INFO - 第 1 页获取到 100 条记录
2025-06-11 08:03:34,485 - INFO - Request Parameters - Page 2:
2025-06-11 08:03:34,485 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:34,485 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400657, 1745596800657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:35,125 - INFO - API请求耗时: 641ms
2025-06-11 08:03:35,125 - INFO - Response - Page 2
2025-06-11 08:03:35,125 - INFO - 第 2 页获取到 100 条记录
2025-06-11 08:03:35,641 - INFO - Request Parameters - Page 3:
2025-06-11 08:03:35,641 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:35,641 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400657, 1745596800657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:36,360 - INFO - API请求耗时: 719ms
2025-06-11 08:03:36,360 - INFO - Response - Page 3
2025-06-11 08:03:36,360 - INFO - 第 3 页获取到 100 条记录
2025-06-11 08:03:36,875 - INFO - Request Parameters - Page 4:
2025-06-11 08:03:36,875 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:36,875 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400657, 1745596800657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:37,594 - INFO - API请求耗时: 719ms
2025-06-11 08:03:37,594 - INFO - Response - Page 4
2025-06-11 08:03:37,594 - INFO - 第 4 页获取到 100 条记录
2025-06-11 08:03:38,094 - INFO - Request Parameters - Page 5:
2025-06-11 08:03:38,094 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:38,094 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400657, 1745596800657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:38,610 - INFO - API请求耗时: 516ms
2025-06-11 08:03:38,610 - INFO - Response - Page 5
2025-06-11 08:03:38,610 - INFO - 第 5 页获取到 36 条记录
2025-06-11 08:03:38,610 - INFO - 查询完成，共获取到 436 条记录
2025-06-11 08:03:38,610 - INFO - 分段 8 查询成功，获取到 436 条记录
2025-06-11 08:03:39,610 - INFO - 查询分段 9: 2025-04-27 至 2025-04-28
2025-06-11 08:03:39,610 - INFO - 查询日期范围: 2025-04-27 至 2025-04-28，使用分页查询，每页 100 条记录
2025-06-11 08:03:39,610 - INFO - Request Parameters - Page 1:
2025-06-11 08:03:39,610 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:39,610 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200657, 1745769600657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:40,235 - INFO - API请求耗时: 625ms
2025-06-11 08:03:40,235 - INFO - Response - Page 1
2025-06-11 08:03:40,235 - INFO - 第 1 页获取到 100 条记录
2025-06-11 08:03:40,735 - INFO - Request Parameters - Page 2:
2025-06-11 08:03:40,735 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:40,735 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200657, 1745769600657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:41,422 - INFO - API请求耗时: 687ms
2025-06-11 08:03:41,422 - INFO - Response - Page 2
2025-06-11 08:03:41,422 - INFO - 第 2 页获取到 100 条记录
2025-06-11 08:03:41,922 - INFO - Request Parameters - Page 3:
2025-06-11 08:03:41,922 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:41,922 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200657, 1745769600657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:42,625 - INFO - API请求耗时: 703ms
2025-06-11 08:03:42,625 - INFO - Response - Page 3
2025-06-11 08:03:42,625 - INFO - 第 3 页获取到 100 条记录
2025-06-11 08:03:43,125 - INFO - Request Parameters - Page 4:
2025-06-11 08:03:43,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:43,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200657, 1745769600657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:43,922 - INFO - API请求耗时: 797ms
2025-06-11 08:03:43,922 - INFO - Response - Page 4
2025-06-11 08:03:43,922 - INFO - 第 4 页获取到 100 条记录
2025-06-11 08:03:44,438 - INFO - Request Parameters - Page 5:
2025-06-11 08:03:44,438 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:44,438 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200657, 1745769600657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:44,984 - INFO - API请求耗时: 547ms
2025-06-11 08:03:44,984 - INFO - Response - Page 5
2025-06-11 08:03:44,984 - INFO - 第 5 页获取到 24 条记录
2025-06-11 08:03:44,984 - INFO - 查询完成，共获取到 424 条记录
2025-06-11 08:03:44,984 - INFO - 分段 9 查询成功，获取到 424 条记录
2025-06-11 08:03:46,000 - INFO - 查询分段 10: 2025-04-29 至 2025-04-30
2025-06-11 08:03:46,000 - INFO - 查询日期范围: 2025-04-29 至 2025-04-30，使用分页查询，每页 100 条记录
2025-06-11 08:03:46,000 - INFO - Request Parameters - Page 1:
2025-06-11 08:03:46,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:46,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000657, 1745942400657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:46,656 - INFO - API请求耗时: 656ms
2025-06-11 08:03:46,656 - INFO - Response - Page 1
2025-06-11 08:03:46,672 - INFO - 第 1 页获取到 100 条记录
2025-06-11 08:03:47,188 - INFO - Request Parameters - Page 2:
2025-06-11 08:03:47,188 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:47,188 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000657, 1745942400657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:47,922 - INFO - API请求耗时: 734ms
2025-06-11 08:03:47,922 - INFO - Response - Page 2
2025-06-11 08:03:47,922 - INFO - 第 2 页获取到 100 条记录
2025-06-11 08:03:48,422 - INFO - Request Parameters - Page 3:
2025-06-11 08:03:48,422 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:48,422 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000657, 1745942400657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:49,125 - INFO - API请求耗时: 703ms
2025-06-11 08:03:49,125 - INFO - Response - Page 3
2025-06-11 08:03:49,125 - INFO - 第 3 页获取到 100 条记录
2025-06-11 08:03:49,641 - INFO - Request Parameters - Page 4:
2025-06-11 08:03:49,641 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:49,641 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000657, 1745942400657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:50,328 - INFO - API请求耗时: 687ms
2025-06-11 08:03:50,328 - INFO - Response - Page 4
2025-06-11 08:03:50,328 - INFO - 第 4 页获取到 100 条记录
2025-06-11 08:03:50,828 - INFO - Request Parameters - Page 5:
2025-06-11 08:03:50,828 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:50,828 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000657, 1745942400657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:51,406 - INFO - API请求耗时: 578ms
2025-06-11 08:03:51,422 - INFO - Response - Page 5
2025-06-11 08:03:51,422 - INFO - 第 5 页获取到 34 条记录
2025-06-11 08:03:51,422 - INFO - 查询完成，共获取到 434 条记录
2025-06-11 08:03:51,422 - INFO - 分段 10 查询成功，获取到 434 条记录
2025-06-11 08:03:52,438 - INFO - 查询分段 11: 2025-05-01 至 2025-05-02
2025-06-11 08:03:52,438 - INFO - 查询日期范围: 2025-05-01 至 2025-05-02，使用分页查询，每页 100 条记录
2025-06-11 08:03:52,438 - INFO - Request Parameters - Page 1:
2025-06-11 08:03:52,438 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:52,438 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800657, 1746115200657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:53,156 - INFO - API请求耗时: 719ms
2025-06-11 08:03:53,156 - INFO - Response - Page 1
2025-06-11 08:03:53,156 - INFO - 第 1 页获取到 100 条记录
2025-06-11 08:03:53,656 - INFO - Request Parameters - Page 2:
2025-06-11 08:03:53,656 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:53,656 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800657, 1746115200657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:54,453 - INFO - API请求耗时: 781ms
2025-06-11 08:03:54,453 - INFO - Response - Page 2
2025-06-11 08:03:54,453 - INFO - 第 2 页获取到 100 条记录
2025-06-11 08:03:54,969 - INFO - Request Parameters - Page 3:
2025-06-11 08:03:54,969 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:54,969 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800657, 1746115200657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:55,672 - INFO - API请求耗时: 703ms
2025-06-11 08:03:55,672 - INFO - Response - Page 3
2025-06-11 08:03:55,672 - INFO - 第 3 页获取到 100 条记录
2025-06-11 08:03:56,188 - INFO - Request Parameters - Page 4:
2025-06-11 08:03:56,188 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:56,188 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800657, 1746115200657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:56,906 - INFO - API请求耗时: 719ms
2025-06-11 08:03:56,906 - INFO - Response - Page 4
2025-06-11 08:03:56,906 - INFO - 第 4 页获取到 100 条记录
2025-06-11 08:03:57,406 - INFO - Request Parameters - Page 5:
2025-06-11 08:03:57,406 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:57,406 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800657, 1746115200657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:57,984 - INFO - API请求耗时: 578ms
2025-06-11 08:03:57,984 - INFO - Response - Page 5
2025-06-11 08:03:57,984 - INFO - 第 5 页获取到 22 条记录
2025-06-11 08:03:57,984 - INFO - 查询完成，共获取到 422 条记录
2025-06-11 08:03:57,984 - INFO - 分段 11 查询成功，获取到 422 条记录
2025-06-11 08:03:59,000 - INFO - 查询分段 12: 2025-05-03 至 2025-05-04
2025-06-11 08:03:59,000 - INFO - 查询日期范围: 2025-05-03 至 2025-05-04，使用分页查询，每页 100 条记录
2025-06-11 08:03:59,000 - INFO - Request Parameters - Page 1:
2025-06-11 08:03:59,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:03:59,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746201600657, 1746288000657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:03:59,797 - INFO - API请求耗时: 797ms
2025-06-11 08:03:59,797 - INFO - Response - Page 1
2025-06-11 08:03:59,797 - INFO - 第 1 页获取到 100 条记录
2025-06-11 08:04:00,328 - INFO - Request Parameters - Page 2:
2025-06-11 08:04:00,328 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:00,328 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746201600657, 1746288000657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:01,031 - INFO - API请求耗时: 703ms
2025-06-11 08:04:01,031 - INFO - Response - Page 2
2025-06-11 08:04:01,031 - INFO - 第 2 页获取到 100 条记录
2025-06-11 08:04:01,531 - INFO - Request Parameters - Page 3:
2025-06-11 08:04:01,531 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:01,531 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746201600657, 1746288000657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:02,219 - INFO - API请求耗时: 687ms
2025-06-11 08:04:02,219 - INFO - Response - Page 3
2025-06-11 08:04:02,219 - INFO - 第 3 页获取到 100 条记录
2025-06-11 08:04:02,719 - INFO - Request Parameters - Page 4:
2025-06-11 08:04:02,719 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:02,719 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746201600657, 1746288000657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:03,516 - INFO - API请求耗时: 797ms
2025-06-11 08:04:03,516 - INFO - Response - Page 4
2025-06-11 08:04:03,516 - INFO - 第 4 页获取到 100 条记录
2025-06-11 08:04:04,016 - INFO - Request Parameters - Page 5:
2025-06-11 08:04:04,016 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:04,016 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746201600657, 1746288000657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:04,609 - INFO - API请求耗时: 594ms
2025-06-11 08:04:04,609 - INFO - Response - Page 5
2025-06-11 08:04:04,609 - INFO - 第 5 页获取到 30 条记录
2025-06-11 08:04:04,609 - INFO - 查询完成，共获取到 430 条记录
2025-06-11 08:04:04,609 - INFO - 分段 12 查询成功，获取到 430 条记录
2025-06-11 08:04:05,625 - INFO - 查询分段 13: 2025-05-05 至 2025-05-06
2025-06-11 08:04:05,625 - INFO - 查询日期范围: 2025-05-05 至 2025-05-06，使用分页查询，每页 100 条记录
2025-06-11 08:04:05,625 - INFO - Request Parameters - Page 1:
2025-06-11 08:04:05,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:05,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400657, 1746460800657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:06,250 - INFO - API请求耗时: 625ms
2025-06-11 08:04:06,250 - INFO - Response - Page 1
2025-06-11 08:04:06,250 - INFO - 第 1 页获取到 100 条记录
2025-06-11 08:04:06,750 - INFO - Request Parameters - Page 2:
2025-06-11 08:04:06,750 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:06,750 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400657, 1746460800657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:07,516 - INFO - API请求耗时: 766ms
2025-06-11 08:04:07,516 - INFO - Response - Page 2
2025-06-11 08:04:07,516 - INFO - 第 2 页获取到 100 条记录
2025-06-11 08:04:08,016 - INFO - Request Parameters - Page 3:
2025-06-11 08:04:08,016 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:08,016 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400657, 1746460800657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:08,719 - INFO - API请求耗时: 703ms
2025-06-11 08:04:08,719 - INFO - Response - Page 3
2025-06-11 08:04:08,719 - INFO - 第 3 页获取到 100 条记录
2025-06-11 08:04:09,219 - INFO - Request Parameters - Page 4:
2025-06-11 08:04:09,219 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:09,219 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400657, 1746460800657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:09,906 - INFO - API请求耗时: 687ms
2025-06-11 08:04:09,906 - INFO - Response - Page 4
2025-06-11 08:04:09,906 - INFO - 第 4 页获取到 100 条记录
2025-06-11 08:04:10,422 - INFO - Request Parameters - Page 5:
2025-06-11 08:04:10,422 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:10,422 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400657, 1746460800657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:10,859 - INFO - API请求耗时: 437ms
2025-06-11 08:04:10,859 - INFO - Response - Page 5
2025-06-11 08:04:10,859 - INFO - 第 5 页获取到 8 条记录
2025-06-11 08:04:10,859 - INFO - 查询完成，共获取到 408 条记录
2025-06-11 08:04:10,859 - INFO - 分段 13 查询成功，获取到 408 条记录
2025-06-11 08:04:11,875 - INFO - 查询分段 14: 2025-05-07 至 2025-05-08
2025-06-11 08:04:11,875 - INFO - 查询日期范围: 2025-05-07 至 2025-05-08，使用分页查询，每页 100 条记录
2025-06-11 08:04:11,875 - INFO - Request Parameters - Page 1:
2025-06-11 08:04:11,875 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:11,875 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200657, 1746633600657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:12,594 - INFO - API请求耗时: 719ms
2025-06-11 08:04:12,594 - INFO - Response - Page 1
2025-06-11 08:04:12,594 - INFO - 第 1 页获取到 100 条记录
2025-06-11 08:04:13,094 - INFO - Request Parameters - Page 2:
2025-06-11 08:04:13,094 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:13,094 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200657, 1746633600657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:13,750 - INFO - API请求耗时: 656ms
2025-06-11 08:04:13,750 - INFO - Response - Page 2
2025-06-11 08:04:13,750 - INFO - 第 2 页获取到 100 条记录
2025-06-11 08:04:14,250 - INFO - Request Parameters - Page 3:
2025-06-11 08:04:14,250 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:14,250 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200657, 1746633600657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:14,969 - INFO - API请求耗时: 719ms
2025-06-11 08:04:14,969 - INFO - Response - Page 3
2025-06-11 08:04:14,969 - INFO - 第 3 页获取到 100 条记录
2025-06-11 08:04:15,484 - INFO - Request Parameters - Page 4:
2025-06-11 08:04:15,484 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:15,484 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200657, 1746633600657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:16,234 - INFO - API请求耗时: 750ms
2025-06-11 08:04:16,234 - INFO - Response - Page 4
2025-06-11 08:04:16,234 - INFO - 第 4 页获取到 100 条记录
2025-06-11 08:04:16,734 - INFO - Request Parameters - Page 5:
2025-06-11 08:04:16,734 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:16,734 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200657, 1746633600657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:17,297 - INFO - API请求耗时: 562ms
2025-06-11 08:04:17,297 - INFO - Response - Page 5
2025-06-11 08:04:17,297 - INFO - 第 5 页获取到 16 条记录
2025-06-11 08:04:17,297 - INFO - 查询完成，共获取到 416 条记录
2025-06-11 08:04:17,297 - INFO - 分段 14 查询成功，获取到 416 条记录
2025-06-11 08:04:18,297 - INFO - 查询分段 15: 2025-05-09 至 2025-05-10
2025-06-11 08:04:18,297 - INFO - 查询日期范围: 2025-05-09 至 2025-05-10，使用分页查询，每页 100 条记录
2025-06-11 08:04:18,297 - INFO - Request Parameters - Page 1:
2025-06-11 08:04:18,297 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:18,297 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000657, 1746806400657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:19,031 - INFO - API请求耗时: 734ms
2025-06-11 08:04:19,031 - INFO - Response - Page 1
2025-06-11 08:04:19,031 - INFO - 第 1 页获取到 100 条记录
2025-06-11 08:04:19,547 - INFO - Request Parameters - Page 2:
2025-06-11 08:04:19,547 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:19,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000657, 1746806400657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:20,344 - INFO - API请求耗时: 797ms
2025-06-11 08:04:20,344 - INFO - Response - Page 2
2025-06-11 08:04:20,344 - INFO - 第 2 页获取到 100 条记录
2025-06-11 08:04:20,844 - INFO - Request Parameters - Page 3:
2025-06-11 08:04:20,844 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:20,844 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000657, 1746806400657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:21,703 - INFO - API请求耗时: 859ms
2025-06-11 08:04:21,703 - INFO - Response - Page 3
2025-06-11 08:04:21,703 - INFO - 第 3 页获取到 100 条记录
2025-06-11 08:04:22,203 - INFO - Request Parameters - Page 4:
2025-06-11 08:04:22,203 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:22,203 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000657, 1746806400657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:22,984 - INFO - API请求耗时: 781ms
2025-06-11 08:04:22,984 - INFO - Response - Page 4
2025-06-11 08:04:22,984 - INFO - 第 4 页获取到 100 条记录
2025-06-11 08:04:23,484 - INFO - Request Parameters - Page 5:
2025-06-11 08:04:23,484 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:23,484 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000657, 1746806400657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:24,031 - INFO - API请求耗时: 547ms
2025-06-11 08:04:24,031 - INFO - Response - Page 5
2025-06-11 08:04:24,031 - INFO - 第 5 页获取到 36 条记录
2025-06-11 08:04:24,031 - INFO - 查询完成，共获取到 436 条记录
2025-06-11 08:04:24,031 - INFO - 分段 15 查询成功，获取到 436 条记录
2025-06-11 08:04:25,047 - INFO - 查询分段 16: 2025-05-11 至 2025-05-12
2025-06-11 08:04:25,047 - INFO - 查询日期范围: 2025-05-11 至 2025-05-12，使用分页查询，每页 100 条记录
2025-06-11 08:04:25,047 - INFO - Request Parameters - Page 1:
2025-06-11 08:04:25,047 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:25,047 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800657, 1746979200657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:25,703 - INFO - API请求耗时: 656ms
2025-06-11 08:04:25,703 - INFO - Response - Page 1
2025-06-11 08:04:25,703 - INFO - 第 1 页获取到 100 条记录
2025-06-11 08:04:26,219 - INFO - Request Parameters - Page 2:
2025-06-11 08:04:26,219 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:26,219 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800657, 1746979200657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:26,828 - INFO - API请求耗时: 609ms
2025-06-11 08:04:26,828 - INFO - Response - Page 2
2025-06-11 08:04:26,828 - INFO - 第 2 页获取到 100 条记录
2025-06-11 08:04:27,328 - INFO - Request Parameters - Page 3:
2025-06-11 08:04:27,328 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:27,328 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800657, 1746979200657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:28,047 - INFO - API请求耗时: 719ms
2025-06-11 08:04:28,047 - INFO - Response - Page 3
2025-06-11 08:04:28,047 - INFO - 第 3 页获取到 100 条记录
2025-06-11 08:04:28,547 - INFO - Request Parameters - Page 4:
2025-06-11 08:04:28,547 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:28,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800657, 1746979200657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:29,359 - INFO - API请求耗时: 812ms
2025-06-11 08:04:29,359 - INFO - Response - Page 4
2025-06-11 08:04:29,359 - INFO - 第 4 页获取到 100 条记录
2025-06-11 08:04:29,859 - INFO - Request Parameters - Page 5:
2025-06-11 08:04:29,859 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:29,859 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800657, 1746979200657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:30,437 - INFO - API请求耗时: 578ms
2025-06-11 08:04:30,437 - INFO - Response - Page 5
2025-06-11 08:04:30,437 - INFO - 第 5 页获取到 30 条记录
2025-06-11 08:04:30,437 - INFO - 查询完成，共获取到 430 条记录
2025-06-11 08:04:30,437 - INFO - 分段 16 查询成功，获取到 430 条记录
2025-06-11 08:04:31,437 - INFO - 查询分段 17: 2025-05-13 至 2025-05-14
2025-06-11 08:04:31,437 - INFO - 查询日期范围: 2025-05-13 至 2025-05-14，使用分页查询，每页 100 条记录
2025-06-11 08:04:31,437 - INFO - Request Parameters - Page 1:
2025-06-11 08:04:31,437 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:31,437 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600657, 1747152000657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:32,265 - INFO - API请求耗时: 828ms
2025-06-11 08:04:32,265 - INFO - Response - Page 1
2025-06-11 08:04:32,265 - INFO - 第 1 页获取到 100 条记录
2025-06-11 08:04:32,765 - INFO - Request Parameters - Page 2:
2025-06-11 08:04:32,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:32,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600657, 1747152000657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:33,422 - INFO - API请求耗时: 656ms
2025-06-11 08:04:33,422 - INFO - Response - Page 2
2025-06-11 08:04:33,422 - INFO - 第 2 页获取到 100 条记录
2025-06-11 08:04:33,922 - INFO - Request Parameters - Page 3:
2025-06-11 08:04:33,922 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:33,922 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600657, 1747152000657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:34,687 - INFO - API请求耗时: 766ms
2025-06-11 08:04:34,687 - INFO - Response - Page 3
2025-06-11 08:04:34,687 - INFO - 第 3 页获取到 100 条记录
2025-06-11 08:04:35,203 - INFO - Request Parameters - Page 4:
2025-06-11 08:04:35,203 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:35,203 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600657, 1747152000657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:35,906 - INFO - API请求耗时: 703ms
2025-06-11 08:04:35,906 - INFO - Response - Page 4
2025-06-11 08:04:35,906 - INFO - 第 4 页获取到 100 条记录
2025-06-11 08:04:36,406 - INFO - Request Parameters - Page 5:
2025-06-11 08:04:36,406 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:36,406 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600657, 1747152000657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:36,859 - INFO - API请求耗时: 453ms
2025-06-11 08:04:36,859 - INFO - Response - Page 5
2025-06-11 08:04:36,859 - INFO - 第 5 页获取到 14 条记录
2025-06-11 08:04:36,859 - INFO - 查询完成，共获取到 414 条记录
2025-06-11 08:04:36,859 - INFO - 分段 17 查询成功，获取到 414 条记录
2025-06-11 08:04:37,859 - INFO - 查询分段 18: 2025-05-15 至 2025-05-16
2025-06-11 08:04:37,859 - INFO - 查询日期范围: 2025-05-15 至 2025-05-16，使用分页查询，每页 100 条记录
2025-06-11 08:04:37,859 - INFO - Request Parameters - Page 1:
2025-06-11 08:04:37,859 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:37,859 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400657, 1747324800657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:38,562 - INFO - API请求耗时: 703ms
2025-06-11 08:04:38,562 - INFO - Response - Page 1
2025-06-11 08:04:38,562 - INFO - 第 1 页获取到 100 条记录
2025-06-11 08:04:39,078 - INFO - Request Parameters - Page 2:
2025-06-11 08:04:39,078 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:39,078 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400657, 1747324800657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:39,797 - INFO - API请求耗时: 719ms
2025-06-11 08:04:39,797 - INFO - Response - Page 2
2025-06-11 08:04:39,797 - INFO - 第 2 页获取到 100 条记录
2025-06-11 08:04:40,312 - INFO - Request Parameters - Page 3:
2025-06-11 08:04:40,312 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:40,312 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400657, 1747324800657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:41,015 - INFO - API请求耗时: 703ms
2025-06-11 08:04:41,015 - INFO - Response - Page 3
2025-06-11 08:04:41,015 - INFO - 第 3 页获取到 100 条记录
2025-06-11 08:04:41,531 - INFO - Request Parameters - Page 4:
2025-06-11 08:04:41,531 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:41,531 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400657, 1747324800657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:42,234 - INFO - API请求耗时: 703ms
2025-06-11 08:04:42,234 - INFO - Response - Page 4
2025-06-11 08:04:42,234 - INFO - 第 4 页获取到 100 条记录
2025-06-11 08:04:42,734 - INFO - Request Parameters - Page 5:
2025-06-11 08:04:42,734 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:42,734 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400657, 1747324800657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:43,312 - INFO - API请求耗时: 578ms
2025-06-11 08:04:43,312 - INFO - Response - Page 5
2025-06-11 08:04:43,312 - INFO - 第 5 页获取到 20 条记录
2025-06-11 08:04:43,312 - INFO - 查询完成，共获取到 420 条记录
2025-06-11 08:04:43,312 - INFO - 分段 18 查询成功，获取到 420 条记录
2025-06-11 08:04:44,328 - INFO - 查询分段 19: 2025-05-17 至 2025-05-18
2025-06-11 08:04:44,328 - INFO - 查询日期范围: 2025-05-17 至 2025-05-18，使用分页查询，每页 100 条记录
2025-06-11 08:04:44,328 - INFO - Request Parameters - Page 1:
2025-06-11 08:04:44,328 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:44,328 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747411200657, 1747497600657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:45,125 - INFO - API请求耗时: 797ms
2025-06-11 08:04:45,125 - INFO - Response - Page 1
2025-06-11 08:04:45,125 - INFO - 第 1 页获取到 100 条记录
2025-06-11 08:04:45,640 - INFO - Request Parameters - Page 2:
2025-06-11 08:04:45,640 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:45,640 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747411200657, 1747497600657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:46,343 - INFO - API请求耗时: 703ms
2025-06-11 08:04:46,343 - INFO - Response - Page 2
2025-06-11 08:04:46,343 - INFO - 第 2 页获取到 100 条记录
2025-06-11 08:04:46,843 - INFO - Request Parameters - Page 3:
2025-06-11 08:04:46,843 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:46,843 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747411200657, 1747497600657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:47,500 - INFO - API请求耗时: 656ms
2025-06-11 08:04:47,500 - INFO - Response - Page 3
2025-06-11 08:04:47,500 - INFO - 第 3 页获取到 100 条记录
2025-06-11 08:04:48,015 - INFO - Request Parameters - Page 4:
2025-06-11 08:04:48,015 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:48,015 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747411200657, 1747497600657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:48,672 - INFO - API请求耗时: 656ms
2025-06-11 08:04:48,672 - INFO - Response - Page 4
2025-06-11 08:04:48,672 - INFO - 第 4 页获取到 100 条记录
2025-06-11 08:04:49,187 - INFO - Request Parameters - Page 5:
2025-06-11 08:04:49,187 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:49,187 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747411200657, 1747497600657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:49,781 - INFO - API请求耗时: 594ms
2025-06-11 08:04:49,781 - INFO - Response - Page 5
2025-06-11 08:04:49,781 - INFO - 第 5 页获取到 32 条记录
2025-06-11 08:04:49,781 - INFO - 查询完成，共获取到 432 条记录
2025-06-11 08:04:49,781 - INFO - 分段 19 查询成功，获取到 432 条记录
2025-06-11 08:04:50,797 - INFO - 查询分段 20: 2025-05-19 至 2025-05-20
2025-06-11 08:04:50,797 - INFO - 查询日期范围: 2025-05-19 至 2025-05-20，使用分页查询，每页 100 条记录
2025-06-11 08:04:50,797 - INFO - Request Parameters - Page 1:
2025-06-11 08:04:50,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:50,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747584000657, 1747670400657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:51,437 - INFO - API请求耗时: 641ms
2025-06-11 08:04:51,437 - INFO - Response - Page 1
2025-06-11 08:04:51,437 - INFO - 第 1 页获取到 100 条记录
2025-06-11 08:04:51,953 - INFO - Request Parameters - Page 2:
2025-06-11 08:04:51,953 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:51,953 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747584000657, 1747670400657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:52,718 - INFO - API请求耗时: 766ms
2025-06-11 08:04:52,718 - INFO - Response - Page 2
2025-06-11 08:04:52,734 - INFO - 第 2 页获取到 100 条记录
2025-06-11 08:04:53,234 - INFO - Request Parameters - Page 3:
2025-06-11 08:04:53,234 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:53,234 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747584000657, 1747670400657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:54,000 - INFO - API请求耗时: 766ms
2025-06-11 08:04:54,000 - INFO - Response - Page 3
2025-06-11 08:04:54,000 - INFO - 第 3 页获取到 100 条记录
2025-06-11 08:04:54,500 - INFO - Request Parameters - Page 4:
2025-06-11 08:04:54,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:54,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747584000657, 1747670400657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:55,281 - INFO - API请求耗时: 781ms
2025-06-11 08:04:55,281 - INFO - Response - Page 4
2025-06-11 08:04:55,281 - INFO - 第 4 页获取到 100 条记录
2025-06-11 08:04:55,781 - INFO - Request Parameters - Page 5:
2025-06-11 08:04:55,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:55,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747584000657, 1747670400657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:56,437 - INFO - API请求耗时: 656ms
2025-06-11 08:04:56,437 - INFO - Response - Page 5
2025-06-11 08:04:56,437 - INFO - 第 5 页获取到 26 条记录
2025-06-11 08:04:56,437 - INFO - 查询完成，共获取到 426 条记录
2025-06-11 08:04:56,437 - INFO - 分段 20 查询成功，获取到 426 条记录
2025-06-11 08:04:57,437 - INFO - 查询分段 21: 2025-05-21 至 2025-05-22
2025-06-11 08:04:57,437 - INFO - 查询日期范围: 2025-05-21 至 2025-05-22，使用分页查询，每页 100 条记录
2025-06-11 08:04:57,437 - INFO - Request Parameters - Page 1:
2025-06-11 08:04:57,437 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:57,437 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747756800657, 1747843200657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:58,125 - INFO - API请求耗时: 687ms
2025-06-11 08:04:58,125 - INFO - Response - Page 1
2025-06-11 08:04:58,125 - INFO - 第 1 页获取到 100 条记录
2025-06-11 08:04:58,640 - INFO - Request Parameters - Page 2:
2025-06-11 08:04:58,640 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:04:58,640 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747756800657, 1747843200657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:04:59,578 - INFO - API请求耗时: 938ms
2025-06-11 08:04:59,578 - INFO - Response - Page 2
2025-06-11 08:04:59,578 - INFO - 第 2 页获取到 100 条记录
2025-06-11 08:05:00,093 - INFO - Request Parameters - Page 3:
2025-06-11 08:05:00,093 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:00,093 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747756800657, 1747843200657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:00,843 - INFO - API请求耗时: 750ms
2025-06-11 08:05:00,843 - INFO - Response - Page 3
2025-06-11 08:05:00,843 - INFO - 第 3 页获取到 100 条记录
2025-06-11 08:05:01,359 - INFO - Request Parameters - Page 4:
2025-06-11 08:05:01,359 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:01,359 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747756800657, 1747843200657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:02,000 - INFO - API请求耗时: 641ms
2025-06-11 08:05:02,000 - INFO - Response - Page 4
2025-06-11 08:05:02,000 - INFO - 第 4 页获取到 100 条记录
2025-06-11 08:05:02,515 - INFO - Request Parameters - Page 5:
2025-06-11 08:05:02,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:02,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747756800657, 1747843200657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:03,046 - INFO - API请求耗时: 531ms
2025-06-11 08:05:03,046 - INFO - Response - Page 5
2025-06-11 08:05:03,046 - INFO - 第 5 页获取到 16 条记录
2025-06-11 08:05:03,046 - INFO - 查询完成，共获取到 416 条记录
2025-06-11 08:05:03,062 - INFO - 分段 21 查询成功，获取到 416 条记录
2025-06-11 08:05:04,062 - INFO - 查询分段 22: 2025-05-23 至 2025-05-24
2025-06-11 08:05:04,062 - INFO - 查询日期范围: 2025-05-23 至 2025-05-24，使用分页查询，每页 100 条记录
2025-06-11 08:05:04,062 - INFO - Request Parameters - Page 1:
2025-06-11 08:05:04,062 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:04,062 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747929600657, 1748016000657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:04,843 - INFO - API请求耗时: 781ms
2025-06-11 08:05:04,843 - INFO - Response - Page 1
2025-06-11 08:05:04,843 - INFO - 第 1 页获取到 100 条记录
2025-06-11 08:05:05,359 - INFO - Request Parameters - Page 2:
2025-06-11 08:05:05,359 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:05,359 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747929600657, 1748016000657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:06,093 - INFO - API请求耗时: 734ms
2025-06-11 08:05:06,093 - INFO - Response - Page 2
2025-06-11 08:05:06,093 - INFO - 第 2 页获取到 100 条记录
2025-06-11 08:05:06,593 - INFO - Request Parameters - Page 3:
2025-06-11 08:05:06,593 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:06,593 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747929600657, 1748016000657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:07,328 - INFO - API请求耗时: 734ms
2025-06-11 08:05:07,328 - INFO - Response - Page 3
2025-06-11 08:05:07,328 - INFO - 第 3 页获取到 100 条记录
2025-06-11 08:05:07,828 - INFO - Request Parameters - Page 4:
2025-06-11 08:05:07,828 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:07,828 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747929600657, 1748016000657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:08,515 - INFO - API请求耗时: 687ms
2025-06-11 08:05:08,515 - INFO - Response - Page 4
2025-06-11 08:05:08,515 - INFO - 第 4 页获取到 100 条记录
2025-06-11 08:05:09,015 - INFO - Request Parameters - Page 5:
2025-06-11 08:05:09,015 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:09,015 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747929600657, 1748016000657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:09,578 - INFO - API请求耗时: 562ms
2025-06-11 08:05:09,578 - INFO - Response - Page 5
2025-06-11 08:05:09,578 - INFO - 第 5 页获取到 30 条记录
2025-06-11 08:05:09,578 - INFO - 查询完成，共获取到 430 条记录
2025-06-11 08:05:09,578 - INFO - 分段 22 查询成功，获取到 430 条记录
2025-06-11 08:05:10,593 - INFO - 查询分段 23: 2025-05-25 至 2025-05-26
2025-06-11 08:05:10,593 - INFO - 查询日期范围: 2025-05-25 至 2025-05-26，使用分页查询，每页 100 条记录
2025-06-11 08:05:10,593 - INFO - Request Parameters - Page 1:
2025-06-11 08:05:10,593 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:10,593 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748102400657, 1748188800657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:11,328 - INFO - API请求耗时: 734ms
2025-06-11 08:05:11,328 - INFO - Response - Page 1
2025-06-11 08:05:11,328 - INFO - 第 1 页获取到 100 条记录
2025-06-11 08:05:11,828 - INFO - Request Parameters - Page 2:
2025-06-11 08:05:11,828 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:11,828 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748102400657, 1748188800657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:12,546 - INFO - API请求耗时: 719ms
2025-06-11 08:05:12,546 - INFO - Response - Page 2
2025-06-11 08:05:12,546 - INFO - 第 2 页获取到 100 条记录
2025-06-11 08:05:13,046 - INFO - Request Parameters - Page 3:
2025-06-11 08:05:13,046 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:13,046 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748102400657, 1748188800657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:13,796 - INFO - API请求耗时: 750ms
2025-06-11 08:05:13,796 - INFO - Response - Page 3
2025-06-11 08:05:13,796 - INFO - 第 3 页获取到 100 条记录
2025-06-11 08:05:14,312 - INFO - Request Parameters - Page 4:
2025-06-11 08:05:14,312 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:14,312 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748102400657, 1748188800657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:15,124 - INFO - API请求耗时: 812ms
2025-06-11 08:05:15,124 - INFO - Response - Page 4
2025-06-11 08:05:15,124 - INFO - 第 4 页获取到 100 条记录
2025-06-11 08:05:15,640 - INFO - Request Parameters - Page 5:
2025-06-11 08:05:15,640 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:15,640 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748102400657, 1748188800657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:16,093 - INFO - API请求耗时: 453ms
2025-06-11 08:05:16,093 - INFO - Response - Page 5
2025-06-11 08:05:16,093 - INFO - 第 5 页获取到 10 条记录
2025-06-11 08:05:16,093 - INFO - 查询完成，共获取到 410 条记录
2025-06-11 08:05:16,093 - INFO - 分段 23 查询成功，获取到 410 条记录
2025-06-11 08:05:17,093 - INFO - 查询分段 24: 2025-05-27 至 2025-05-28
2025-06-11 08:05:17,093 - INFO - 查询日期范围: 2025-05-27 至 2025-05-28，使用分页查询，每页 100 条记录
2025-06-11 08:05:17,093 - INFO - Request Parameters - Page 1:
2025-06-11 08:05:17,093 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:17,093 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748275200657, 1748361600657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:17,843 - INFO - API请求耗时: 750ms
2025-06-11 08:05:17,843 - INFO - Response - Page 1
2025-06-11 08:05:17,843 - INFO - 第 1 页获取到 100 条记录
2025-06-11 08:05:18,359 - INFO - Request Parameters - Page 2:
2025-06-11 08:05:18,359 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:18,359 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748275200657, 1748361600657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:19,078 - INFO - API请求耗时: 719ms
2025-06-11 08:05:19,078 - INFO - Response - Page 2
2025-06-11 08:05:19,078 - INFO - 第 2 页获取到 100 条记录
2025-06-11 08:05:19,593 - INFO - Request Parameters - Page 3:
2025-06-11 08:05:19,593 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:19,593 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748275200657, 1748361600657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:20,437 - INFO - API请求耗时: 844ms
2025-06-11 08:05:20,437 - INFO - Response - Page 3
2025-06-11 08:05:20,437 - INFO - 第 3 页获取到 100 条记录
2025-06-11 08:05:20,937 - INFO - Request Parameters - Page 4:
2025-06-11 08:05:20,937 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:20,937 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748275200657, 1748361600657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:21,687 - INFO - API请求耗时: 750ms
2025-06-11 08:05:21,687 - INFO - Response - Page 4
2025-06-11 08:05:21,687 - INFO - 第 4 页获取到 100 条记录
2025-06-11 08:05:22,187 - INFO - Request Parameters - Page 5:
2025-06-11 08:05:22,187 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:22,187 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748275200657, 1748361600657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:22,734 - INFO - API请求耗时: 547ms
2025-06-11 08:05:22,734 - INFO - Response - Page 5
2025-06-11 08:05:22,734 - INFO - 第 5 页获取到 16 条记录
2025-06-11 08:05:22,734 - INFO - 查询完成，共获取到 416 条记录
2025-06-11 08:05:22,734 - INFO - 分段 24 查询成功，获取到 416 条记录
2025-06-11 08:05:23,749 - INFO - 查询分段 25: 2025-05-29 至 2025-05-30
2025-06-11 08:05:23,749 - INFO - 查询日期范围: 2025-05-29 至 2025-05-30，使用分页查询，每页 100 条记录
2025-06-11 08:05:23,749 - INFO - Request Parameters - Page 1:
2025-06-11 08:05:23,749 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:23,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748448000657, 1748534400657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:24,484 - INFO - API请求耗时: 734ms
2025-06-11 08:05:24,484 - INFO - Response - Page 1
2025-06-11 08:05:24,484 - INFO - 第 1 页获取到 100 条记录
2025-06-11 08:05:24,999 - INFO - Request Parameters - Page 2:
2025-06-11 08:05:24,999 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:24,999 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748448000657, 1748534400657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:25,781 - INFO - API请求耗时: 781ms
2025-06-11 08:05:25,781 - INFO - Response - Page 2
2025-06-11 08:05:25,781 - INFO - 第 2 页获取到 100 条记录
2025-06-11 08:05:26,281 - INFO - Request Parameters - Page 3:
2025-06-11 08:05:26,281 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:26,281 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748448000657, 1748534400657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:27,031 - INFO - API请求耗时: 750ms
2025-06-11 08:05:27,031 - INFO - Response - Page 3
2025-06-11 08:05:27,031 - INFO - 第 3 页获取到 100 条记录
2025-06-11 08:05:27,546 - INFO - Request Parameters - Page 4:
2025-06-11 08:05:27,546 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:27,546 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748448000657, 1748534400657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:28,249 - INFO - API请求耗时: 703ms
2025-06-11 08:05:28,249 - INFO - Response - Page 4
2025-06-11 08:05:28,249 - INFO - 第 4 页获取到 100 条记录
2025-06-11 08:05:28,765 - INFO - Request Parameters - Page 5:
2025-06-11 08:05:28,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:28,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748448000657, 1748534400657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:29,234 - INFO - API请求耗时: 469ms
2025-06-11 08:05:29,234 - INFO - Response - Page 5
2025-06-11 08:05:29,249 - INFO - 第 5 页获取到 16 条记录
2025-06-11 08:05:29,249 - INFO - 查询完成，共获取到 416 条记录
2025-06-11 08:05:29,249 - INFO - 分段 25 查询成功，获取到 416 条记录
2025-06-11 08:05:30,249 - INFO - 查询分段 26: 2025-05-31 至 2025-06-01
2025-06-11 08:05:30,249 - INFO - 查询日期范围: 2025-05-31 至 2025-06-01，使用分页查询，每页 100 条记录
2025-06-11 08:05:30,249 - INFO - Request Parameters - Page 1:
2025-06-11 08:05:30,249 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:30,249 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748620800657, 1748707200657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:30,953 - INFO - API请求耗时: 703ms
2025-06-11 08:05:30,953 - INFO - Response - Page 1
2025-06-11 08:05:30,953 - INFO - 第 1 页获取到 100 条记录
2025-06-11 08:05:31,453 - INFO - Request Parameters - Page 2:
2025-06-11 08:05:31,453 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:31,453 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748620800657, 1748707200657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:32,124 - INFO - API请求耗时: 672ms
2025-06-11 08:05:32,124 - INFO - Response - Page 2
2025-06-11 08:05:32,124 - INFO - 第 2 页获取到 100 条记录
2025-06-11 08:05:32,640 - INFO - Request Parameters - Page 3:
2025-06-11 08:05:32,640 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:32,640 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748620800657, 1748707200657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:33,343 - INFO - API请求耗时: 703ms
2025-06-11 08:05:33,343 - INFO - Response - Page 3
2025-06-11 08:05:33,343 - INFO - 第 3 页获取到 100 条记录
2025-06-11 08:05:33,843 - INFO - Request Parameters - Page 4:
2025-06-11 08:05:33,843 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:33,843 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748620800657, 1748707200657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:34,499 - INFO - API请求耗时: 656ms
2025-06-11 08:05:34,499 - INFO - Response - Page 4
2025-06-11 08:05:34,499 - INFO - 第 4 页获取到 100 条记录
2025-06-11 08:05:34,999 - INFO - Request Parameters - Page 5:
2025-06-11 08:05:34,999 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:34,999 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748620800657, 1748707200657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:35,531 - INFO - API请求耗时: 531ms
2025-06-11 08:05:35,531 - INFO - Response - Page 5
2025-06-11 08:05:35,531 - INFO - 第 5 页获取到 12 条记录
2025-06-11 08:05:35,531 - INFO - 查询完成，共获取到 412 条记录
2025-06-11 08:05:35,531 - INFO - 分段 26 查询成功，获取到 412 条记录
2025-06-11 08:05:36,546 - INFO - 查询分段 27: 2025-06-02 至 2025-06-03
2025-06-11 08:05:36,546 - INFO - 查询日期范围: 2025-06-02 至 2025-06-03，使用分页查询，每页 100 条记录
2025-06-11 08:05:36,546 - INFO - Request Parameters - Page 1:
2025-06-11 08:05:36,546 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:36,546 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748793600657, 1748880000657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:37,156 - INFO - API请求耗时: 609ms
2025-06-11 08:05:37,156 - INFO - Response - Page 1
2025-06-11 08:05:37,156 - INFO - 第 1 页获取到 100 条记录
2025-06-11 08:05:37,671 - INFO - Request Parameters - Page 2:
2025-06-11 08:05:37,671 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:37,671 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748793600657, 1748880000657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:38,265 - INFO - API请求耗时: 594ms
2025-06-11 08:05:38,265 - INFO - Response - Page 2
2025-06-11 08:05:38,265 - INFO - 第 2 页获取到 100 条记录
2025-06-11 08:05:38,765 - INFO - Request Parameters - Page 3:
2025-06-11 08:05:38,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:38,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748793600657, 1748880000657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:39,468 - INFO - API请求耗时: 703ms
2025-06-11 08:05:39,468 - INFO - Response - Page 3
2025-06-11 08:05:39,468 - INFO - 第 3 页获取到 100 条记录
2025-06-11 08:05:39,968 - INFO - Request Parameters - Page 4:
2025-06-11 08:05:39,968 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:39,968 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748793600657, 1748880000657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:40,702 - INFO - API请求耗时: 734ms
2025-06-11 08:05:40,702 - INFO - Response - Page 4
2025-06-11 08:05:40,702 - INFO - 第 4 页获取到 98 条记录
2025-06-11 08:05:40,702 - INFO - 查询完成，共获取到 398 条记录
2025-06-11 08:05:40,702 - INFO - 分段 27 查询成功，获取到 398 条记录
2025-06-11 08:05:41,718 - INFO - 查询分段 28: 2025-06-04 至 2025-06-05
2025-06-11 08:05:41,718 - INFO - 查询日期范围: 2025-06-04 至 2025-06-05，使用分页查询，每页 100 条记录
2025-06-11 08:05:41,718 - INFO - Request Parameters - Page 1:
2025-06-11 08:05:41,718 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:41,718 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748966400657, 1749052800657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:42,390 - INFO - API请求耗时: 672ms
2025-06-11 08:05:42,390 - INFO - Response - Page 1
2025-06-11 08:05:42,390 - INFO - 第 1 页获取到 100 条记录
2025-06-11 08:05:42,890 - INFO - Request Parameters - Page 2:
2025-06-11 08:05:42,890 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:42,890 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748966400657, 1749052800657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:43,593 - INFO - API请求耗时: 703ms
2025-06-11 08:05:43,593 - INFO - Response - Page 2
2025-06-11 08:05:43,593 - INFO - 第 2 页获取到 100 条记录
2025-06-11 08:05:44,093 - INFO - Request Parameters - Page 3:
2025-06-11 08:05:44,093 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:44,093 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748966400657, 1749052800657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:44,718 - INFO - API请求耗时: 625ms
2025-06-11 08:05:44,718 - INFO - Response - Page 3
2025-06-11 08:05:44,718 - INFO - 第 3 页获取到 100 条记录
2025-06-11 08:05:45,234 - INFO - Request Parameters - Page 4:
2025-06-11 08:05:45,234 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:45,234 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748966400657, 1749052800657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:45,952 - INFO - API请求耗时: 719ms
2025-06-11 08:05:45,952 - INFO - Response - Page 4
2025-06-11 08:05:45,952 - INFO - 第 4 页获取到 100 条记录
2025-06-11 08:05:46,452 - INFO - Request Parameters - Page 5:
2025-06-11 08:05:46,452 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:46,452 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748966400657, 1749052800657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:46,827 - INFO - API请求耗时: 375ms
2025-06-11 08:05:46,827 - INFO - Response - Page 5
2025-06-11 08:05:46,827 - INFO - 第 5 页获取到 1 条记录
2025-06-11 08:05:46,827 - INFO - 查询完成，共获取到 401 条记录
2025-06-11 08:05:46,827 - INFO - 分段 28 查询成功，获取到 401 条记录
2025-06-11 08:05:47,843 - INFO - 查询分段 29: 2025-06-06 至 2025-06-07
2025-06-11 08:05:47,843 - INFO - 查询日期范围: 2025-06-06 至 2025-06-07，使用分页查询，每页 100 条记录
2025-06-11 08:05:47,843 - INFO - Request Parameters - Page 1:
2025-06-11 08:05:47,843 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:47,843 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749139200657, 1749225600657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:48,577 - INFO - API请求耗时: 734ms
2025-06-11 08:05:48,577 - INFO - Response - Page 1
2025-06-11 08:05:48,577 - INFO - 第 1 页获取到 100 条记录
2025-06-11 08:05:49,077 - INFO - Request Parameters - Page 2:
2025-06-11 08:05:49,077 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:49,077 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749139200657, 1749225600657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:49,890 - INFO - API请求耗时: 812ms
2025-06-11 08:05:49,890 - INFO - Response - Page 2
2025-06-11 08:05:49,890 - INFO - 第 2 页获取到 100 条记录
2025-06-11 08:05:50,406 - INFO - Request Parameters - Page 3:
2025-06-11 08:05:50,406 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:50,406 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749139200657, 1749225600657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:51,093 - INFO - API请求耗时: 687ms
2025-06-11 08:05:51,093 - INFO - Response - Page 3
2025-06-11 08:05:51,093 - INFO - 第 3 页获取到 100 条记录
2025-06-11 08:05:51,593 - INFO - Request Parameters - Page 4:
2025-06-11 08:05:51,593 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:51,593 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749139200657, 1749225600657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:52,359 - INFO - API请求耗时: 766ms
2025-06-11 08:05:52,359 - INFO - Response - Page 4
2025-06-11 08:05:52,359 - INFO - 第 4 页获取到 100 条记录
2025-06-11 08:05:52,859 - INFO - Request Parameters - Page 5:
2025-06-11 08:05:52,859 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:52,859 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749139200657, 1749225600657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:53,234 - INFO - API请求耗时: 375ms
2025-06-11 08:05:53,234 - INFO - Response - Page 5
2025-06-11 08:05:53,234 - INFO - 第 5 页获取到 5 条记录
2025-06-11 08:05:53,234 - INFO - 查询完成，共获取到 405 条记录
2025-06-11 08:05:53,234 - INFO - 分段 29 查询成功，获取到 405 条记录
2025-06-11 08:05:54,249 - INFO - 查询分段 30: 2025-06-08 至 2025-06-09
2025-06-11 08:05:54,249 - INFO - 查询日期范围: 2025-06-08 至 2025-06-09，使用分页查询，每页 100 条记录
2025-06-11 08:05:54,249 - INFO - Request Parameters - Page 1:
2025-06-11 08:05:54,249 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:54,249 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749312000657, 1749398400657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:54,952 - INFO - API请求耗时: 703ms
2025-06-11 08:05:54,952 - INFO - Response - Page 1
2025-06-11 08:05:54,952 - INFO - 第 1 页获取到 100 条记录
2025-06-11 08:05:55,468 - INFO - Request Parameters - Page 2:
2025-06-11 08:05:55,468 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:55,468 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749312000657, 1749398400657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:56,280 - INFO - API请求耗时: 812ms
2025-06-11 08:05:56,280 - INFO - Response - Page 2
2025-06-11 08:05:56,280 - INFO - 第 2 页获取到 92 条记录
2025-06-11 08:05:56,280 - INFO - 查询完成，共获取到 192 条记录
2025-06-11 08:05:56,280 - INFO - 分段 30 查询成功，获取到 192 条记录
2025-06-11 08:05:57,296 - INFO - 查询分段 31: 2025-06-10 至 2025-06-10
2025-06-11 08:05:57,296 - INFO - 查询日期范围: 2025-06-10 至 2025-06-10，使用分页查询，每页 100 条记录
2025-06-11 08:05:57,296 - INFO - Request Parameters - Page 1:
2025-06-11 08:05:57,296 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:05:57,296 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749484800657, 1749571199657], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:05:57,749 - INFO - API请求耗时: 453ms
2025-06-11 08:05:57,749 - INFO - Response - Page 1
2025-06-11 08:05:57,749 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-11 08:05:57,749 - INFO - 查询完成，共获取到 0 条记录
2025-06-11 08:05:57,749 - WARNING - 分段 31 查询返回空数据
2025-06-11 08:05:58,765 - INFO - 宜搭每日表单数据查询完成，共 31 个分段，成功获取 12420 条记录，失败 0 次
2025-06-11 08:05:58,765 - INFO - 成功获取宜搭日销售表单数据，共 12420 条记录
2025-06-11 08:05:58,765 - INFO - 宜搭日销售数据项结构示例: ['formInstanceId', 'formData']
2025-06-11 08:05:58,765 - INFO - 开始对比和同步日销售数据...
2025-06-11 08:05:59,109 - INFO - 成功创建宜搭日销售数据索引，共 6307 条记录
2025-06-11 08:05:59,109 - INFO - 开始处理数衍数据，共 12832 条记录
2025-06-11 08:05:59,249 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: F56E8E23D2584556A30D1378611DF4AE
2025-06-11 08:05:59,249 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: F56E8E23D2584556A30D1378611DF4AE
2025-06-11 08:05:59,249 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: D384CB5088914FB296DE32297895B8D6
2025-06-11 08:05:59,249 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: D384CB5088914FB296DE32297895B8D6
2025-06-11 08:05:59,249 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: BF554F536BF14762AEB7110E7BD583B7
2025-06-11 08:05:59,249 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: BF554F536BF14762AEB7110E7BD583B7
2025-06-11 08:05:59,249 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: AC07B70DB49845A8A52846E099EBC515
2025-06-11 08:05:59,249 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: AC07B70DB49845A8A52846E099EBC515
2025-06-11 08:05:59,249 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: AA76628FACEC4C13BD44C8280B45416D
2025-06-11 08:05:59,249 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: AA76628FACEC4C13BD44C8280B45416D
2025-06-11 08:05:59,249 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: A93C60005A8F41B092F6C5A8C21577CB
2025-06-11 08:05:59,249 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: A93C60005A8F41B092F6C5A8C21577CB
2025-06-11 08:05:59,249 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 78655ECA4A32471AB7842F8DE2018120
2025-06-11 08:05:59,249 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 78655ECA4A32471AB7842F8DE2018120
2025-06-11 08:05:59,249 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 6B7571A27AF84C73B4FC04CCBDB83D9B
2025-06-11 08:05:59,249 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 6B7571A27AF84C73B4FC04CCBDB83D9B
2025-06-11 08:05:59,249 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 6692283A183D432BAE322E1032539CE8
2025-06-11 08:05:59,265 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 6692283A183D432BAE322E1032539CE8
2025-06-11 08:05:59,265 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 3BB9A16AE8544997965802FAA3B83381
2025-06-11 08:05:59,265 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 3BB9A16AE8544997965802FAA3B83381
2025-06-11 08:05:59,265 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1INM7RG8OF1EDQ0UI43B5T3TBA0016KV
2025-06-11 08:05:59,265 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1INM7RG8OF1EDQ0UI43B5T3TBA0016KV
2025-06-11 08:05:59,265 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1IBBJ3RNCSAVNO7A70STAEF09Q001IPV
2025-06-11 08:05:59,265 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1IBBJ3RNCSAVNO7A70STAEF09Q001IPV
2025-06-11 08:05:59,265 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1I6E0VAU3IFJEQ22MH147FMU0M0013E8
2025-06-11 08:05:59,265 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HVORJ88U7D2IL1AIB692RTFU8001185
2025-06-11 08:05:59,265 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HVORJ88U7D2IL1AIB692RTFU8001185
2025-06-11 08:05:59,265 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HV6P9SGUVGG9S36QDA69ST70J0015SA
2025-06-11 08:05:59,265 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HV6P9SGUVGG9S36QDA69ST70J0015SA
2025-06-11 08:05:59,265 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HOE1A3UTAESD606LODAUCEHAF001M2A
2025-06-11 08:05:59,265 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HOE1A3UTAESD606LODAUCEHAF001M2A
2025-06-11 08:05:59,265 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HMVHOKFUN7GGQ26TEPQN7CSO4001IA6
2025-06-11 08:05:59,265 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HMVHOKFUN7GGQ26TEPQN7CSO4001IA6
2025-06-11 08:05:59,265 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HINIR4GO8E5NM5U25UDHUFEGO001L3K
2025-06-11 08:05:59,265 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HINIR4GO8E5NM5U25UDHUFEGO001L3K
2025-06-11 08:05:59,265 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HI85875BG16NA6U1G19QP117C001UB5
2025-06-11 08:05:59,265 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HI85875BG16NA6U1G19QP117C001UB5
2025-06-11 08:05:59,265 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HHDS9O4DUBH2L6U1G19QP11V40018CI
2025-06-11 08:05:59,265 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HHDS9O4DUBH2L6U1G19QP11V40018CI
2025-06-11 08:05:59,265 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HHDRIR0P38R666U1G19QP11V00018CE
2025-06-11 08:05:59,265 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HHDRIR0P38R666U1G19QP11V00018CE
2025-06-11 08:05:59,265 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HB8D7KAC5K1G3723F7K257LIN001TSQ
2025-06-11 08:05:59,265 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1HB8D7KAC5K1G3723F7K257LIN001TSQ
2025-06-11 08:05:59,265 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H9P9UCRQKEOIF52ASKKUBQUNH0018FA
2025-06-11 08:05:59,265 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H9P9UCRQKEOIF52ASKKUBQUNH0018FA
2025-06-11 08:05:59,265 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H9P9S64J8E8R652ASKKUBQUMU0018EN
2025-06-11 08:05:59,280 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H9P9S64J8E8R652ASKKUBQUMU0018EN
2025-06-11 08:05:59,280 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H9P9PGR3703D752ASKKUBQUM50018DU
2025-06-11 08:05:59,280 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H9P9PGR3703D752ASKKUBQUM50018DU
2025-06-11 08:05:59,280 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EE2TORA7KI7Q2OV4FVC7FC0014BT
2025-06-11 08:05:59,280 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EE2TORA7KI7Q2OV4FVC7FC0014BT
2025-06-11 08:05:59,280 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EE29HIJ7QK7Q2OV4FVC7F40014BL
2025-06-11 08:05:59,280 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EE29HIJ7QK7Q2OV4FVC7F40014BL
2025-06-11 08:05:59,280 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EE1H27HPQN7Q2OV4FVC7EO0014B9
2025-06-11 08:05:59,280 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EE1H27HPQN7Q2OV4FVC7EO0014B9
2025-06-11 08:05:59,280 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EE0PKSKM647Q2OV4FVC7EC0014AT
2025-06-11 08:05:59,280 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EE0PKSKM647Q2OV4FVC7EC0014AT
2025-06-11 08:05:59,280 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDVNVP6LRA7Q2OV4FVC7DS0014AD
2025-06-11 08:05:59,280 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDVNVP6LRA7Q2OV4FVC7DS0014AD
2025-06-11 08:05:59,280 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDVG061A0H7Q2OV4FVC7DO0014A9
2025-06-11 08:05:59,280 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDVG061A0H7Q2OV4FVC7DO0014A9
2025-06-11 08:05:59,280 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDV84M589R7Q2OV4FVC7DK0014A5
2025-06-11 08:05:59,280 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDV84M589R7Q2OV4FVC7DK0014A5
2025-06-11 08:05:59,280 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDV0D9P6J27Q2OV4FVC7DG0014A1
2025-06-11 08:05:59,280 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDV0D9P6J27Q2OV4FVC7DG0014A1
2025-06-11 08:05:59,280 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDUNDNMH3D7Q2OV4FVC7DC00149T
2025-06-11 08:05:59,280 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDUNDNMH3D7Q2OV4FVC7DC00149T
2025-06-11 08:05:59,280 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDTG15Q4SH7Q2OV4FVC7CO001499
2025-06-11 08:05:59,280 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDTG15Q4SH7Q2OV4FVC7CO001499
2025-06-11 08:05:59,280 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDT8J32K6E7Q2OV4FVC7CK001495
2025-06-11 08:05:59,280 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDT8J32K6E7Q2OV4FVC7CK001495
2025-06-11 08:05:59,280 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDSTUTUPTG7Q2OV4FVC7CG001491
2025-06-11 08:05:59,280 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDSTUTUPTG7Q2OV4FVC7CG001491
2025-06-11 08:05:59,280 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDSM3EAQJB7Q2OV4FVC7CC00148T
2025-06-11 08:05:59,280 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDSM3EAQJB7Q2OV4FVC7CC00148T
2025-06-11 08:05:59,296 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDSD911K3E7Q2OV4FVC7C800148P
2025-06-11 08:05:59,296 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDSD911K3E7Q2OV4FVC7C800148P
2025-06-11 08:05:59,296 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDS537TI7U7Q2OV4FVC7C400148L
2025-06-11 08:05:59,296 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDS537TI7U7Q2OV4FVC7C400148L
2025-06-11 08:05:59,296 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDRLHCKFK97Q2OV4FVC7BS00148D
2025-06-11 08:05:59,296 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDRLHCKFK97Q2OV4FVC7BS00148D
2025-06-11 08:05:59,296 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDQDVL1EG67Q2OV4FVC7B800147P
2025-06-11 08:05:59,296 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDQDVL1EG67Q2OV4FVC7B800147P
2025-06-11 08:05:59,296 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDOE3O1J9R7Q2OV4FVC7A800146P
2025-06-11 08:05:59,296 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDOE3O1J9R7Q2OV4FVC7A800146P
2025-06-11 08:05:59,296 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDN6L4LMS87Q2OV4FVC79K001465
2025-06-11 08:05:59,296 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDN6L4LMS87Q2OV4FVC79K001465
2025-06-11 08:05:59,296 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDMMR23CHP7Q2OV4FVC79C00145T
2025-06-11 08:05:59,296 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDMMR23CHP7Q2OV4FVC79C00145T
2025-06-11 08:05:59,296 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDKDOANT3H7Q2OV4FVC78800144P
2025-06-11 08:05:59,296 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDKDOANT3H7Q2OV4FVC78800144P
2025-06-11 08:05:59,296 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDJ5HVP5F47Q2OV4FVC77O001449
2025-06-11 08:05:59,296 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1H31EDJ5HVP5F47Q2OV4FVC77O001449
2025-06-11 08:05:59,296 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GMFA53VGVIAPH7Q2OV4FVC7GS0017K2
2025-06-11 08:05:59,296 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S
2025-06-11 08:05:59,296 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S
2025-06-11 08:05:59,296 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTE3QTM66G7Q2OVBN4IS8M001D4O
2025-06-11 08:05:59,296 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTE3QTM66G7Q2OVBN4IS8M001D4O
2025-06-11 08:05:59,296 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G
2025-06-11 08:05:59,296 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G
2025-06-11 08:05:59,296 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTD2B070E67Q2OVBN4IS86001D48
2025-06-11 08:05:59,296 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTD2B070E67Q2OVBN4IS86001D48
2025-06-11 08:05:59,312 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTCIR7D5JD7Q2OVBN4IS7U001D40
2025-06-11 08:05:59,312 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTCIR7D5JD7Q2OVBN4IS7U001D40
2025-06-11 08:05:59,312 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S
2025-06-11 08:05:59,312 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S
2025-06-11 08:05:59,312 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTBJPIR4ME7Q2OVBN4IS7E001D3G
2025-06-11 08:05:59,312 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTBJPIR4ME7Q2OVBN4IS7E001D3G
2025-06-11 08:05:59,312 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTAKS3N4EI7Q2OVBN4IS76001D38
2025-06-11 08:05:59,312 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTAKS3N4EI7Q2OVBN4IS76001D38
2025-06-11 08:05:59,312 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTA575PTKJ7Q2OVBN4IS72001D34
2025-06-11 08:05:59,312 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINTA575PTKJ7Q2OVBN4IS72001D34
2025-06-11 08:05:59,312 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINT8VPFOREN7Q2OVBN4IS6Q001D2S
2025-06-11 08:05:59,312 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINT8VPFOREN7Q2OVBN4IS6Q001D2S
2025-06-11 08:05:59,312 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINT72H2L6227Q2OVBN4IS62001D24
2025-06-11 08:05:59,312 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINT72H2L6227Q2OVBN4IS62001D24
2025-06-11 08:05:59,312 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINT6QJG5GQO7Q2OVBN4IS5U001D20
2025-06-11 08:05:59,312 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINT6QJG5GQO7Q2OVBN4IS5U001D20
2025-06-11 08:05:59,312 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S
2025-06-11 08:05:59,312 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S
2025-06-11 08:05:59,312 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O
2025-06-11 08:05:59,312 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O
2025-06-11 08:05:59,312 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINSCOR6MVCC7Q2OV392411100148T
2025-06-11 08:05:59,312 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINSCOR6MVCC7Q2OV392411100148T
2025-06-11 08:05:59,312 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINSC23H1J3M7Q2OV392410L00148H
2025-06-11 08:05:59,312 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINSC23H1J3M7Q2OV392410L00148H
2025-06-11 08:05:59,312 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINPMIJNMLUJ7Q2OV392410G00148C
2025-06-11 08:05:59,312 - INFO - 跳过错误日期数据: ********, shopId: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shopEntityId: 1GDINPMIJNMLUJ7Q2OV392410G00148C
2025-06-11 08:05:59,312 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: A48FB8F8F66644F59454F3E73DFCEB92
2025-06-11 08:05:59,312 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: A48FB8F8F66644F59454F3E73DFCEB92
2025-06-11 08:05:59,327 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 7987833E6DE549FCBAC0AAF7A1D27E61
2025-06-11 08:05:59,327 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 7987833E6DE549FCBAC0AAF7A1D27E61
2025-06-11 08:05:59,327 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 6FEB527E4B354363BD1420A3FF0FB3E3
2025-06-11 08:05:59,327 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 6FEB527E4B354363BD1420A3FF0FB3E3
2025-06-11 08:05:59,327 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 6D1F9FC749FA44C6B70CA818C3E7FB77
2025-06-11 08:05:59,327 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 6D1F9FC749FA44C6B70CA818C3E7FB77
2025-06-11 08:05:59,327 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 48AFA71F437742278E0CD956382F1110
2025-06-11 08:05:59,327 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 48AFA71F437742278E0CD956382F1110
2025-06-11 08:05:59,327 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1I16HSQFKC90AM6QNN0HOT12RK0013M7
2025-06-11 08:05:59,327 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1I16HSQFKC90AM6QNN0HOT12RK0013M7
2025-06-11 08:05:59,327 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1I16GTCIOI81KU0UR9LEHSI4JM001PFU
2025-06-11 08:05:59,327 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1I16GTCIOI81KU0UR9LEHSI4JM001PFU
2025-06-11 08:05:59,327 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGETHMN6B8P42F6DB81RHC2001P8A
2025-06-11 08:05:59,327 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGETHMN6B8P42F6DB81RHC2001P8A
2025-06-11 08:05:59,327 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGERRPAJGP042F6DB81RHB6001P7E
2025-06-11 08:05:59,327 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGERRPAJGP042F6DB81RHB6001P7E
2025-06-11 08:05:59,327 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGERL0RJ5BM42F6DB81RHB2001P7A
2025-06-11 08:05:59,327 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGERL0RJ5BM42F6DB81RHB2001P7A
2025-06-11 08:05:59,327 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGERF6AAOTL42F6DB81RHAU001P76
2025-06-11 08:05:59,327 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGERF6AAOTL42F6DB81RHAU001P76
2025-06-11 08:05:59,327 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEQIR94JOQ42F6DB81RHAE001P6M
2025-06-11 08:05:59,327 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEQIR94JOQ42F6DB81RHAE001P6M
2025-06-11 08:05:59,327 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEQ5682UCT42F6DB81RHA6001P6E
2025-06-11 08:05:59,327 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEQ5682UCT42F6DB81RHA6001P6E
2025-06-11 08:05:59,327 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEPVAL5TUK42F6DB81RHA2001P6A
2025-06-11 08:05:59,327 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEPVAL5TUK42F6DB81RHA2001P6A
2025-06-11 08:05:59,327 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEOTOKMGDD42F6DB81RH9E001P5M
2025-06-11 08:05:59,327 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEOTOKMGDD42F6DB81RH9E001P5M
2025-06-11 08:05:59,343 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEO8LGKTTH42F6DB81RH8V001P57
2025-06-11 08:05:59,343 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEO8LGKTTH42F6DB81RH8V001P57
2025-06-11 08:05:59,343 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEO2283NPC42F6DB81RH8Q001P52
2025-06-11 08:05:59,343 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEO2283NPC42F6DB81RH8Q001P52
2025-06-11 08:05:59,343 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEMMCI75GD42F6DB81RH81001P49
2025-06-11 08:05:59,343 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEMMCI75GD42F6DB81RH81001P49
2025-06-11 08:05:59,343 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEMFCSIPU442F6DB81RH7T001P45
2025-06-11 08:05:59,343 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEMFCSIPU442F6DB81RH7T001P45
2025-06-11 08:05:59,343 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEM2RU37BD42F6DB81RH7L001P3T
2025-06-11 08:05:59,343 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEM2RU37BD42F6DB81RH7L001P3T
2025-06-11 08:05:59,343 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEKSIE6DGB42F6DB81RH77001P3F
2025-06-11 08:05:59,343 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEKSIE6DGB42F6DB81RH77001P3F
2025-06-11 08:05:59,343 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEKLLRRBCQ42F6DB81RH73001P3B
2025-06-11 08:05:59,343 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEKLLRRBCQ42F6DB81RH73001P3B
2025-06-11 08:05:59,343 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEJQAOTB2542F6DB81RH6O001P30
2025-06-11 08:05:59,343 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEJQAOTB2542F6DB81RH6O001P30
2025-06-11 08:05:59,343 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEJ92AGQAT42F6DB81RH6G001P2O
2025-06-11 08:05:59,343 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 1HTVGEJ92AGQAT42F6DB81RH6G001P2O
2025-06-11 08:05:59,343 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 17590B062D954DB088AC6EE572EFECE9
2025-06-11 08:05:59,343 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 17590B062D954DB088AC6EE572EFECE9
2025-06-11 08:05:59,343 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 101F34500A0D43DF833463DEFB95F423
2025-06-11 08:05:59,343 - INFO - 跳过错误日期数据: ********, shopId: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shopEntityId: 101F34500A0D43DF833463DEFB95F423
2025-06-11 08:05:59,343 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: EC9758A692DF47FBA8F7C97344079C9E
2025-06-11 08:05:59,343 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: EC9758A692DF47FBA8F7C97344079C9E
2025-06-11 08:05:59,343 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: E214E9E9A4534AE1943BBACB09056E2E
2025-06-11 08:05:59,343 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: E214E9E9A4534AE1943BBACB09056E2E
2025-06-11 08:05:59,343 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: B5A5FB25D4B04323BCABB528AF5E427E
2025-06-11 08:05:59,343 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: B5A5FB25D4B04323BCABB528AF5E427E
2025-06-11 08:05:59,359 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: B358872E0DBE420182AF77D4C47644F6
2025-06-11 08:05:59,359 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: B358872E0DBE420182AF77D4C47644F6
2025-06-11 08:05:59,359 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 6245DA9460784DDC85246900484DAA79
2025-06-11 08:05:59,359 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 6245DA9460784DDC85246900484DAA79
2025-06-11 08:05:59,359 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1IFRR231GUB4QN7QBECDAL3H28001J69
2025-06-11 08:05:59,359 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1IFRR231GUB4QN7QBECDAL3H28001J69
2025-06-11 08:05:59,359 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1IFRPJDJBMAPL77QBECDAL3H1H001J5I
2025-06-11 08:05:59,359 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1IFRPJDJBMAPL77QBECDAL3H1H001J5I
2025-06-11 08:05:59,359 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HO90F90E71NK12I1UUTD5AE7C001O7G
2025-06-11 08:05:59,359 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HO90F90E71NK12I1UUTD5AE7C001O7G
2025-06-11 08:05:59,359 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HLFG4AKMT96P82UAQ9ONTBKHO001HHS
2025-06-11 08:05:59,359 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HLFG4AKMT96P82UAQ9ONTBKHO001HHS
2025-06-11 08:05:59,359 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEC0U8246I0I86N3H2U10A001F9G
2025-06-11 08:05:59,359 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEC0U8246I0I86N3H2U10A001F9G
2025-06-11 08:05:59,359 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEBP5Q03GB0I86N3H2U106001F9C
2025-06-11 08:05:59,359 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEBP5Q03GB0I86N3H2U106001F9C
2025-06-11 08:05:59,359 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEBHLLHVNF0I86N3H2U102001F98
2025-06-11 08:05:59,359 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEBHLLHVNF0I86N3H2U102001F98
2025-06-11 08:05:59,359 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEB9O4F53S0I86N3H2U1VT001F93
2025-06-11 08:05:59,359 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEB9O4F53S0I86N3H2U1VT001F93
2025-06-11 08:05:59,359 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEAIN5DMKK0I86N3H2U1VH001F8N
2025-06-11 08:05:59,359 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEAIN5DMKK0I86N3H2U1VH001F8N
2025-06-11 08:05:59,359 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEAB2RFI0N0I86N3H2U1VD001F8J
2025-06-11 08:05:59,359 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEAB2RFI0N0I86N3H2U1VD001F8J
2025-06-11 08:05:59,359 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEA4041D6F0I86N3H2U1V9001F8F
2025-06-11 08:05:59,359 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEEA4041D6F0I86N3H2U1V9001F8F
2025-06-11 08:05:59,359 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE85EF5FB90I86N3H2U1U9001F7F
2025-06-11 08:05:59,359 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE85EF5FB90I86N3H2U1U9001F7F
2025-06-11 08:05:59,359 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE7UKVCV6D0I86N3H2U1U5001F7B
2025-06-11 08:05:59,374 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE7UKVCV6D0I86N3H2U1U5001F7B
2025-06-11 08:05:59,374 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE4GES1U770I86N3H2U1SF001F5L
2025-06-11 08:05:59,374 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE4GES1U770I86N3H2U1SF001F5L
2025-06-11 08:05:59,374 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE48KDQ6AS0I86N3H2U1SB001F5H
2025-06-11 08:05:59,374 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE48KDQ6AS0I86N3H2U1SB001F5H
2025-06-11 08:05:59,374 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE3PJ2TE9A0I86N3H2U1S3001F59
2025-06-11 08:05:59,374 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE3PJ2TE9A0I86N3H2U1S3001F59
2025-06-11 08:05:59,374 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE39GOJVP40I86N3H2U1RR001F51
2025-06-11 08:05:59,374 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE39GOJVP40I86N3H2U1RR001F51
2025-06-11 08:05:59,374 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE2CVHLBFV0I86N3H2U1RH001F4N
2025-06-11 08:05:59,374 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE2CVHLBFV0I86N3H2U1RH001F4N
2025-06-11 08:05:59,374 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE25DAIM3B0I86N3H2U1RD001F4J
2025-06-11 08:05:59,374 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE25DAIM3B0I86N3H2U1RD001F4J
2025-06-11 08:05:59,374 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE1T4E94FK0I86N3H2U1R9001F4F
2025-06-11 08:05:59,374 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE1T4E94FK0I86N3H2U1R9001F4F
2025-06-11 08:05:59,374 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE1EFF1UHG0I86N3H2U1R1001F47
2025-06-11 08:05:59,374 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE1EFF1UHG0I86N3H2U1R1001F47
2025-06-11 08:05:59,374 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE0VODGC6J0I86N3H2U1QP001F3V
2025-06-11 08:05:59,374 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE0VODGC6J0I86N3H2U1QP001F3V
2025-06-11 08:05:59,374 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE0A2N9KG60I86N3H2U1QB001F3H
2025-06-11 08:05:59,374 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEE0A2N9KG60I86N3H2U1QB001F3H
2025-06-11 08:05:59,374 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDVQHT7NSH0I86N3H2U1Q2001F38
2025-06-11 08:05:59,374 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDVQHT7NSH0I86N3H2U1Q2001F38
2025-06-11 08:05:59,374 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDVIE6UN1G0I86N3H2U1PU001F34
2025-06-11 08:05:59,374 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDVIE6UN1G0I86N3H2U1PU001F34
2025-06-11 08:05:59,374 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDVBILA3OJ0I86N3H2U1PQ001F30
2025-06-11 08:05:59,374 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDVBILA3OJ0I86N3H2U1PQ001F30
2025-06-11 08:05:59,374 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDUSSKMEM60I86N3H2U1PI001F2O
2025-06-11 08:05:59,390 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDUSSKMEM60I86N3H2U1PI001F2O
2025-06-11 08:05:59,390 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDULBESLOI0I86N3H2U1PE001F2K
2025-06-11 08:05:59,390 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDULBESLOI0I86N3H2U1PE001F2K
2025-06-11 08:05:59,390 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDQR0OPQVI0I86N3H2U1NE001F0K
2025-06-11 08:05:59,390 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDPJQDDVRC0I86N3H2U1MO001EVU
2025-06-11 08:05:59,390 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDPJQDDVRC0I86N3H2U1MO001EVU
2025-06-11 08:05:59,390 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDP34FLR400I86N3H2U1MG001EVM
2025-06-11 08:05:59,390 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDP34FLR400I86N3H2U1MG001EVM
2025-06-11 08:05:59,390 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDOR4RUGJG0I86N3H2U1MC001EVI
2025-06-11 08:05:59,390 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDOR4RUGJG0I86N3H2U1MC001EVI
2025-06-11 08:05:59,390 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDJP082LC00I86N3H2U1JM001ESS
2025-06-11 08:05:59,390 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDJGUIA2QR0I86N3H2U1JH001ESN
2025-06-11 08:05:59,390 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDJGUIA2QR0I86N3H2U1JH001ESN
2025-06-11 08:05:59,390 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDJ943QH9Q0I86N3H2U1JD001ESJ
2025-06-11 08:05:59,390 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDJ943QH9Q0I86N3H2U1JD001ESJ
2025-06-11 08:05:59,390 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3
2025-06-11 08:05:59,390 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3
2025-06-11 08:05:59,390 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDG7SC9VNO0I86N3H2U1HP001EQV
2025-06-11 08:05:59,390 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDG7SC9VNO0I86N3H2U1HP001EQV
2025-06-11 08:05:59,390 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ
2025-06-11 08:05:59,390 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ
2025-06-11 08:05:59,390 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDF8HFHI690I86N3H2U1H9001EQF
2025-06-11 08:05:59,390 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDF8HFHI690I86N3H2U1H9001EQF
2025-06-11 08:05:59,390 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDF11AKIN30I86N3H2U1H5001EQB
2025-06-11 08:05:59,390 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDEQ2M9E710I86N3H2U1H1001EQ7
2025-06-11 08:05:59,390 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDEQ2M9E710I86N3H2U1H1001EQ7
2025-06-11 08:05:59,390 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDEISQT8KE0I86N3H2U1GT001EQ3
2025-06-11 08:05:59,390 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDEISQT8KE0I86N3H2U1GT001EQ3
2025-06-11 08:05:59,405 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDCE3748SO0I86N3H2U1FP001EOV
2025-06-11 08:05:59,405 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDCE3748SO0I86N3H2U1FP001EOV
2025-06-11 08:05:59,405 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDAAQ1NK8J0I86N3H2U1EL001ENR
2025-06-11 08:05:59,405 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCEDAAQ1NK8J0I86N3H2U1EL001ENR
2025-06-11 08:05:59,405 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCE9H11376450I86N3H2U19G001EIM
2025-06-11 08:05:59,405 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCE9H11376450I86N3H2U19G001EIM
2025-06-11 08:05:59,405 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCE9GHTM38HU0I86N3H2U198001EIE
2025-06-11 08:05:59,405 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCE9G31FV3GL0I86N3H2U190001EI6
2025-06-11 08:05:59,405 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCE9G31FV3GL0I86N3H2U190001EI6
2025-06-11 08:05:59,405 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCE9EN1GSFF80I86N3H2U18C001EHI
2025-06-11 08:05:59,405 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCE9EG92S1SB0I86N3H2U188001EHE
2025-06-11 08:05:59,405 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCE9EG92S1SB0I86N3H2U188001EHE
2025-06-11 08:05:59,405 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCE9D9OBM41R0I86N3H2U17K001EGQ
2025-06-11 08:05:59,405 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCE13J2R9CI0I86N3H2U13D001ECJ
2025-06-11 08:05:59,405 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCE13J2R9CI0I86N3H2U13D001ECJ
2025-06-11 08:05:59,405 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCE0JS3BF7R0I86N3H2U135001ECB
2025-06-11 08:05:59,405 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCE0JS3BF7R0I86N3H2U135001ECB
2025-06-11 08:05:59,405 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDVSS4V3460I86N3H2U12P001EBV
2025-06-11 08:05:59,405 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDVSS4V3460I86N3H2U12P001EBV
2025-06-11 08:05:59,405 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDVBEGSM760I86N3H2U12H001EBN
2025-06-11 08:05:59,405 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDVBEGSM760I86N3H2U12H001EBN
2025-06-11 08:05:59,405 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDUDM4PLNS0I86N3H2U121001EB7
2025-06-11 08:05:59,405 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDUDM4PLNS0I86N3H2U121001EB7
2025-06-11 08:05:59,405 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDU5QKVU3D0I86N3H2U11T001EB3
2025-06-11 08:05:59,405 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDU5QKVU3D0I86N3H2U11T001EB3
2025-06-11 08:05:59,405 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDTTV5HD0Q0I86N3H2U11P001EAV
2025-06-11 08:05:59,421 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDTTV5HD0Q0I86N3H2U11P001EAV
2025-06-11 08:05:59,421 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDSK489TE20I86N3H2U114001EAA
2025-06-11 08:05:59,421 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDSK489TE20I86N3H2U114001EAA
2025-06-11 08:05:59,421 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDSC7N3PHM0I86N3H2U10T001EA3
2025-06-11 08:05:59,421 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDSC7N3PHM0I86N3H2U10T001EA3
2025-06-11 08:05:59,421 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDS341MMSU0I86N3H2U10P001E9V
2025-06-11 08:05:59,421 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDS341MMSU0I86N3H2U10P001E9V
2025-06-11 08:05:59,421 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDRR6FJ7A60I86N3H2U10L001E9R
2025-06-11 08:05:59,421 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDRR6FJ7A60I86N3H2U10L001E9R
2025-06-11 08:05:59,421 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDQQG9THS10I86N3H2U108001E9E
2025-06-11 08:05:59,421 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDQQG9THS10I86N3H2U108001E9E
2025-06-11 08:05:59,421 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDQJKO8O0D0I86N3H2U104001E9A
2025-06-11 08:05:59,421 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDQJKO8O0D0I86N3H2U104001E9A
2025-06-11 08:05:59,421 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDQ4GA7M630I86N3H2U1VS001E92
2025-06-11 08:05:59,421 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDQ4GA7M630I86N3H2U1VS001E92
2025-06-11 08:05:59,421 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q
2025-06-11 08:05:59,421 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q
2025-06-11 08:05:59,421 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I
2025-06-11 08:05:59,421 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I
2025-06-11 08:05:59,421 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDOM98AG2E0I86N3H2U1V8001E8E
2025-06-11 08:05:59,421 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDOM98AG2E0I86N3H2U1V8001E8E
2025-06-11 08:05:59,421 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDNMK1P3900I86N3H2U1UO001E7U
2025-06-11 08:05:59,421 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDNMK1P3900I86N3H2U1UO001E7U
2025-06-11 08:05:59,421 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q
2025-06-11 08:05:59,421 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q
2025-06-11 08:05:59,421 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDN4LTS5B80I86N3H2U1UG001E7M
2025-06-11 08:05:59,421 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDN4LTS5B80I86N3H2U1UG001E7M
2025-06-11 08:05:59,421 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDMFCJQF4F0I86N3H2U1UC001E7I
2025-06-11 08:05:59,421 - INFO - 跳过错误日期数据: ********, shopId: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shopEntityId: 1HGCCDMFCJQF4F0I86N3H2U1UC001E7I
2025-06-11 08:05:59,421 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 3F059827C9E04DEAA6B50797867EC52B
2025-06-11 08:05:59,437 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 3F059827C9E04DEAA6B50797867EC52B
2025-06-11 08:05:59,437 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1HRQIR9VACRH722I1UUTD5AEGF001EGK
2025-06-11 08:05:59,437 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1HRQIR9VACRH722I1UUTD5AEGF001EGK
2025-06-11 08:05:59,437 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ
2025-06-11 08:05:59,437 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ
2025-06-11 08:05:59,437 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1GPFAB4PUHT7OL7Q2OV4FVC7US001R67
2025-06-11 08:05:59,437 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1GPFAB4PUHT7OL7Q2OV4FVC7US001R67
2025-06-11 08:05:59,437 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS
2025-06-11 08:05:59,437 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS
2025-06-11 08:05:59,437 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3
2025-06-11 08:05:59,437 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3
2025-06-11 08:05:59,437 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1FE0MRVUM0P77G7Q2OV78BKOG4001PUK
2025-06-11 08:05:59,437 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1FE0MRVUM0P77G7Q2OV78BKOG4001PUK
2025-06-11 08:05:59,437 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1FE0MR50JEM3SR7Q2OVAE57DM4001Q85
2025-06-11 08:05:59,437 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1FE0MR50JEM3SR7Q2OVAE57DM4001Q85
2025-06-11 08:05:59,437 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1FE0MO2IE70S367Q2OVAE57DLH001Q7I
2025-06-11 08:05:59,437 - INFO - 跳过错误日期数据: ********, shopId: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shopEntityId: 1FE0MO2IE70S367Q2OVAE57DLH001Q7I
2025-06-11 08:05:59,437 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0TQNGGEI53IKSIOCDI7U6001G57
2025-06-11 08:05:59,437 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0TQNGGEI53IKSIOCDI7U6001G57
2025-06-11 08:05:59,437 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V
2025-06-11 08:05:59,437 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V
2025-06-11 08:05:59,437 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R
2025-06-11 08:05:59,437 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R
2025-06-11 08:05:59,437 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B
2025-06-11 08:05:59,437 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B
2025-06-11 08:05:59,437 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0NLURMV9D3IKSIOCDI7R2001G23
2025-06-11 08:05:59,437 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0NLURMV9D3IKSIOCDI7R2001G23
2025-06-11 08:05:59,437 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0LAK18ROU3IKSIOCDI7Q6001G17
2025-06-11 08:05:59,452 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0LAK18ROU3IKSIOCDI7Q6001G17
2025-06-11 08:05:59,452 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0J21PQQ9M3IKSIOCDI7P2001G03
2025-06-11 08:05:59,452 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0J21PQQ9M3IKSIOCDI7P2001G03
2025-06-11 08:05:59,452 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR
2025-06-11 08:05:59,452 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR
2025-06-11 08:05:59,452 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0BI1TJFBK3IKSIOCDI7LH001FSI
2025-06-11 08:05:59,452 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0BI1TJFBK3IKSIOCDI7LH001FSI
2025-06-11 08:05:59,452 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0A4NM87423IKSIOCDI7KU001FRV
2025-06-11 08:05:59,452 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ0A4NM87423IKSIOCDI7KU001FRV
2025-06-11 08:05:59,452 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR
2025-06-11 08:05:59,452 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR
2025-06-11 08:05:59,452 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ08R852VK83IKSIOCDI7KE001FRF
2025-06-11 08:05:59,452 - INFO - 跳过错误日期数据: ********, shopId: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shopEntityId: 1HKQQ08R852VK83IKSIOCDI7KE001FRF
2025-06-11 08:05:59,452 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE3ODLGMBD6AJB6QM8HA8A0011R8
2025-06-11 08:05:59,452 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE3ODLGMBD6AJB6QM8HA8A0011R8
2025-06-11 08:05:59,452 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE3GMAAFL56AJB6QM8HA860011R4
2025-06-11 08:05:59,452 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE3GMAAFL56AJB6QM8HA860011R4
2025-06-11 08:05:59,452 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE38FF3LOL6AJB6QM8HA820011R0
2025-06-11 08:05:59,452 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE38FF3LOL6AJB6QM8HA820011R0
2025-06-11 08:05:59,452 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE2PLB45826AJB6QM8HA7Q0011QO
2025-06-11 08:05:59,452 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE2PLB45826AJB6QM8HA7Q0011QO
2025-06-11 08:05:59,452 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK
2025-06-11 08:05:59,452 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK
2025-06-11 08:05:59,452 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE29O5UH0D6AJB6QM8HA7I0011QG
2025-06-11 08:05:59,452 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE29O5UH0D6AJB6QM8HA7I0011QG
2025-06-11 08:05:59,452 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8
2025-06-11 08:05:59,452 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8
2025-06-11 08:05:59,452 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRD2F2MCTBD6AJB6QM8HA650011P3
2025-06-11 08:05:59,468 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRD2F2MCTBD6AJB6QM8HA650011P3
2025-06-11 08:05:59,468 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRD0GVFB5C86AJB6QM8HA590011O7
2025-06-11 08:05:59,468 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRD0GVFB5C86AJB6QM8HA590011O7
2025-06-11 08:05:59,468 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRCVBQ1LQ2P6AJB6QM8HA4T0011NR
2025-06-11 08:05:59,468 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS7255PESAA7AV8LHQQGIH8001KNH, shopEntityId: 1HRKRCVBQ1LQ2P6AJB6QM8HA4T0011NR
2025-06-11 08:05:59,468 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: E79F261889C1492982227C207062C267
2025-06-11 08:05:59,468 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: E79F261889C1492982227C207062C267
2025-06-11 08:05:59,468 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: C207460918D74AAAB2E154B47B74F863
2025-06-11 08:05:59,468 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: C207460918D74AAAB2E154B47B74F863
2025-06-11 08:05:59,468 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 41D3E4ED4CEA49C09C36DE504B997534
2025-06-11 08:05:59,468 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 41D3E4ED4CEA49C09C36DE504B997534
2025-06-11 08:05:59,468 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUISOVAPU1P7AV8LHQQGIDU001EK7
2025-06-11 08:05:59,468 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUISOVAPU1P7AV8LHQQGIDU001EK7
2025-06-11 08:05:59,468 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUISLI3BD9P7AV8LHQQGIDR001EK4
2025-06-11 08:05:59,468 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUISLI3BD9P7AV8LHQQGIDR001EK4
2025-06-11 08:05:59,468 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIS7OTCALF7AV8LHQQGIDO001EK1
2025-06-11 08:05:59,468 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIS7OTCALF7AV8LHQQGIDO001EK1
2025-06-11 08:05:59,468 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIS34G4B697AV8LHQQGIDL001EJU
2025-06-11 08:05:59,468 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIS34G4B697AV8LHQQGIDL001EJU
2025-06-11 08:05:59,468 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR
2025-06-11 08:05:59,468 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR
2025-06-11 08:05:59,468 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIRL782OM57AV8LHQQGIDF001EJO
2025-06-11 08:05:59,468 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIRL782OM57AV8LHQQGIDF001EJO
2025-06-11 08:05:59,468 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIRGKS2JA27AV8LHQQGIDC001EJL
2025-06-11 08:05:59,468 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIRGKS2JA27AV8LHQQGIDC001EJL
2025-06-11 08:05:59,468 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIRBVE2CQT7AV8LHQQGID9001EJI
2025-06-11 08:05:59,468 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIRBVE2CQT7AV8LHQQGID9001EJI
2025-06-11 08:05:59,484 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIR7B0BL957AV8LHQQGID6001EJF
2025-06-11 08:05:59,484 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIR7B0BL957AV8LHQQGID6001EJF
2025-06-11 08:05:59,484 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIR2ETOHPO6E7AERKQ83K0001UO2
2025-06-11 08:05:59,484 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIR2ETOHPO6E7AERKQ83K0001UO2
2025-06-11 08:05:59,484 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIQTRHD5AN6E7AERKQ83JT001UNV
2025-06-11 08:05:59,484 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIQTRHD5AN6E7AERKQ83JT001UNV
2025-06-11 08:05:59,484 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIQP8527T06E7AERKQ83JQ001UNS
2025-06-11 08:05:59,484 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIQP8527T06E7AERKQ83JQ001UNS
2025-06-11 08:05:59,484 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIQG1BD1C36E7AERKQ83JN001UNP
2025-06-11 08:05:59,484 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIQG1BD1C36E7AERKQ83JN001UNP
2025-06-11 08:05:59,484 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIQ716B84L7AV8LHQQGID0001EJ9
2025-06-11 08:05:59,484 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIQ716B84L7AV8LHQQGID0001EJ9
2025-06-11 08:05:59,484 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIPOL2K5B16E7AERKQ83JE001UNG
2025-06-11 08:05:59,484 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIPOL2K5B16E7AERKQ83JE001UNG
2025-06-11 08:05:59,484 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIPK0KE3MN6E7AERKQ83JB001UND
2025-06-11 08:05:59,484 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIPK0KE3MN6E7AERKQ83JB001UND
2025-06-11 08:05:59,484 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIPGJC2SUF6E7AERKQ83J8001UNA
2025-06-11 08:05:59,484 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIPGJC2SUF6E7AERKQ83J8001UNA
2025-06-11 08:05:59,484 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIPC0VTE5M6E7AERKQ83J5001UN7
2025-06-11 08:05:59,484 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIPC0VTE5M6E7AERKQ83J5001UN7
2025-06-11 08:05:59,484 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIP3GSKTFR6E7AERKQ83J2001UN4
2025-06-11 08:05:59,484 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 1HRKUIP3GSKTFR6E7AERKQ83J2001UN4
2025-06-11 08:05:59,484 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 14EB204A0BDE44888B43308269C1626A
2025-06-11 08:05:59,484 - INFO - 跳过错误日期数据: ********, shopId: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shopEntityId: 14EB204A0BDE44888B43308269C1626A
2025-06-11 08:06:01,359 - INFO - 更新表单数据成功: FINST-1PF66VA16V1WWFBR7T1NO5S0RXDM3ZAJJONBMEC
2025-06-11 08:06:01,359 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 191.0, 'new_value': 78.0}, {'field': 'dailyBillAmount', 'old_value': 191.0, 'new_value': 78.0}]
2025-06-11 08:06:02,155 - INFO - 更新表单数据成功: FINST-7PF66N917V1W2JXZ7Y11Y5RLXNRR2I35KONBMND
2025-06-11 08:06:02,155 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 55.0, 'new_value': 0.0}, {'field': 'dailyBillAmount', 'old_value': 55.0, 'new_value': 0.0}]
2025-06-11 08:06:02,890 - INFO - 更新表单数据成功: FINST-MLF66PA1Z72WLMW46L5E24ZF0L703I3TKONBMK7
2025-06-11 08:06:02,890 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 11487.0, 'new_value': 10989.3}, {'field': 'dailyBillAmount', 'old_value': 11487.0, 'new_value': 10989.3}]
2025-06-11 08:06:03,374 - INFO - 更新表单数据成功: FINST-MLF66PA1Z72WLMW46L5E24ZF0L703I3TKONBMO7
2025-06-11 08:06:03,374 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 655.0, 'new_value': 224.0}, {'field': 'dailyBillAmount', 'old_value': 655.0, 'new_value': 224.0}]
2025-06-11 08:06:04,155 - INFO - 更新表单数据成功: FINST-LFA66G91XB2WLLNW6GTRO7PFCB7F25L3LONBM901
2025-06-11 08:06:04,155 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 201.0, 'new_value': 0.0}, {'field': 'dailyBillAmount', 'old_value': 201.0, 'new_value': 0.0}]
2025-06-11 08:06:04,780 - INFO - 更新表单数据成功: FINST-2PF66TC1CM2WH3308DOQ74XNH31O2EBELONBMN5
2025-06-11 08:06:04,780 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 7868.1, 'new_value': 5965.9}, {'field': 'dailyBillAmount', 'old_value': 7868.1, 'new_value': 5965.9}]
2025-06-11 08:06:05,249 - INFO - 更新表单数据成功: FINST-2PF66TC1CM2WH3308DOQ74XNH31O2EBELONBMR5
2025-06-11 08:06:05,249 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1701.2, 'new_value': 22.0}, {'field': 'dailyBillAmount', 'old_value': 1701.2, 'new_value': 22.0}]
2025-06-11 08:06:05,827 - INFO - 更新表单数据成功: FINST-IQE66ZC1ZN2WO4TM9M1M664GXTZH29URLONBMDS
2025-06-11 08:06:05,827 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 11316.1, 'new_value': 8199.0}, {'field': 'dailyBillAmount', 'old_value': 11316.1, 'new_value': 8199.0}]
2025-06-11 08:06:06,265 - INFO - 更新表单数据成功: FINST-IQE66ZC1ZN2WO4TM9M1M664GXTZH29URLONBMGS
2025-06-11 08:06:06,265 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1468.9, 'new_value': 150.0}, {'field': 'dailyBillAmount', 'old_value': 1468.9, 'new_value': 150.0}]
2025-06-11 08:06:06,843 - INFO - 更新表单数据成功: FINST-NU966I81CM4WIF35ANIG39F0JSXW2GJ2MONBMG3
2025-06-11 08:06:06,843 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 22027.0, 'new_value': 11594.1}, {'field': 'dailyBillAmount', 'old_value': 22027.0, 'new_value': 11594.1}]
2025-06-11 08:06:07,374 - INFO - 更新表单数据成功: FINST-2HF66O615A2WVEHJAD4YR7HFDH1H2HD5MONBMTU
2025-06-11 08:06:07,374 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 27326.3, 'new_value': 27825.8}, {'field': 'amount', 'old_value': 27326.3, 'new_value': 27825.8}, {'field': 'count', 'old_value': 53, 'new_value': 54}, {'field': 'instoreAmount', 'old_value': 27326.3, 'new_value': 27825.8}, {'field': 'instoreCount', 'old_value': 53, 'new_value': 54}]
2025-06-11 08:06:07,937 - INFO - 更新表单数据成功: FINST-M1B66ED1OW3WX2L39NP7GB0IQ2IK3MDDMONBM6I
2025-06-11 08:06:07,937 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 27662.7, 'new_value': 12656.0}, {'field': 'dailyBillAmount', 'old_value': 27662.7, 'new_value': 12656.0}]
2025-06-11 08:06:08,437 - INFO - 更新表单数据成功: FINST-M1B66ED1OW3WX2L39NP7GB0IQ2IK3MDDMONBMAI
2025-06-11 08:06:08,437 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 6426.5, 'new_value': 6252.0}, {'field': 'dailyBillAmount', 'old_value': 6426.5, 'new_value': 6252.0}]
2025-06-11 08:06:09,015 - INFO - 更新表单数据成功: FINST-8PF66V719O2W1MYPEPGCZ48KY9UA2X2OMONBM79
2025-06-11 08:06:09,015 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 9265.1, 'new_value': 6684.6}, {'field': 'dailyBillAmount', 'old_value': 9265.1, 'new_value': 6684.6}]
2025-06-11 08:06:09,640 - INFO - 更新表单数据成功: FINST-L5766E71MK4WIOXR6ZSLM79VSUV13KPYMONBMY8
2025-06-11 08:06:09,640 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 18865.6, 'new_value': 9443.3}, {'field': 'dailyBillAmount', 'old_value': 18865.6, 'new_value': 9443.3}]
2025-06-11 08:06:10,499 - INFO - 更新表单数据成功: FINST-5A966081KO4W1B5QAOAP9606HUJB3O5CNONBM63
2025-06-11 08:06:10,499 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 13459.1, 'new_value': 5405.9}, {'field': 'dailyBillAmount', 'old_value': 13459.1, 'new_value': 5405.9}]
2025-06-11 08:06:11,187 - INFO - 更新表单数据成功: FINST-LLF66B71VC2WPA2BF0N3CABZC5NQ3PWMNONBMP2
2025-06-11 08:06:11,187 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 24001.9, 'new_value': 8193.3}, {'field': 'dailyBillAmount', 'old_value': 24001.9, 'new_value': 8193.3}]
2025-06-11 08:06:11,827 - INFO - 更新表单数据成功: FINST-2PF66CD1OQ2WA3B28N9LM7MANRWZ1RQXNONBMOU
2025-06-11 08:06:11,827 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 28350.4, 'new_value': 10474.3}, {'field': 'dailyBillAmount', 'old_value': 28350.4, 'new_value': 10474.3}]
2025-06-11 08:06:12,421 - INFO - 更新表单数据成功: FINST-2PF66CD1OQ2WA3B28N9LM7MANRWZ1RQXNONBMSU
2025-06-11 08:06:12,421 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 108.9, 'new_value': 0.0}, {'field': 'dailyBillAmount', 'old_value': 108.9, 'new_value': 0.0}]
2025-06-11 08:06:12,921 - INFO - 更新表单数据成功: FINST-XL666BD1QX2W6V276MVEH7N8B7UN24Q5OONBMTT
2025-06-11 08:06:12,921 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1I6E0VAU3IFJEQ22MH147FMU0M0013E8_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 1135.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 1135.0}]
2025-06-11 08:06:13,483 - INFO - 更新表单数据成功: FINST-2PF66CD1RQ2WO5F2A2M0EAR552RN2VFGOONBMW7
2025-06-11 08:06:13,483 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1I6E0VAU3IFJEQ22MH147FMU0M0013E8_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 836.4}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 836.4}]
2025-06-11 08:06:14,015 - INFO - 更新表单数据成功: FINST-X8D66N81BS4WA45VBGFDT402U4EZ2C6ROONBM45
2025-06-11 08:06:14,015 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1I6E0VAU3IFJEQ22MH147FMU0M0013E8_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 2475.7}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 2475.7}]
2025-06-11 08:06:14,624 - INFO - 更新表单数据成功: FINST-XMC66R919C3WDD4OC4X5L8NF6SG92YR1PONBM1N
2025-06-11 08:06:14,640 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1I6E0VAU3IFJEQ22MH147FMU0M0013E8_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 2357.9}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 2357.9}]
2025-06-11 08:06:15,093 - INFO - 更新表单数据成功: FINST-XMC66R919C3WDD4OC4X5L8NF6SG92ZR1PONBMQO
2025-06-11 08:06:15,093 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDJ5HVP5F47Q2OV4FVC77O001449_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 5579.4, 'new_value': 14869.85}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 14869.85}]
2025-06-11 08:06:15,546 - INFO - 更新表单数据成功: FINST-2FD66I715C2W6DAT9TWWM5SF8E9M3PE4PONBMCD
2025-06-11 08:06:15,546 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_********, 变更字段: [{'field': 'amount', 'old_value': 135.0, 'new_value': 514.0}, {'field': 'count', 'old_value': 1, 'new_value': 2}, {'field': 'instoreAmount', 'old_value': 214.0, 'new_value': 593.0}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 2}]
2025-06-11 08:06:16,124 - INFO - 更新表单数据成功: FINST-3RE66ZB14P2W1R0EBR10FCVGPFHB3WPH2COBM3Y
2025-06-11 08:06:16,124 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_********, 变更字段: [{'field': 'amount', 'old_value': 2485.0, 'new_value': 4167.0}, {'field': 'count', 'old_value': 5, 'new_value': 6}, {'field': 'instoreAmount', 'old_value': 2485.0, 'new_value': 4167.0}, {'field': 'instoreCount', 'old_value': 5, 'new_value': 6}]
2025-06-11 08:06:16,624 - INFO - 更新表单数据成功: FINST-2FD66I715C2W6DAT9TWWM5SF8E9M3QE4PONBM9F
2025-06-11 08:06:16,624 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 6556.27, 'new_value': 6559.27}, {'field': 'amount', 'old_value': 6556.2699999999995, 'new_value': 6559.2699999999995}, {'field': 'count', 'old_value': 296, 'new_value': 297}, {'field': 'onlineAmount', 'old_value': 4232.66, 'new_value': 4235.66}, {'field': 'onlineCount', 'old_value': 201, 'new_value': 202}]
2025-06-11 08:06:17,077 - INFO - 更新表单数据成功: FINST-2FD66I715C2W6DAT9TWWM5SF8E9M3QE4PONBMCF
2025-06-11 08:06:17,077 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_********, 变更字段: [{'field': 'amount', 'old_value': 9232.43, 'new_value': 9759.93}, {'field': 'count', 'old_value': 106, 'new_value': 107}, {'field': 'instoreAmount', 'old_value': 5953.64, 'new_value': 6481.14}, {'field': 'instoreCount', 'old_value': 26, 'new_value': 27}]
2025-06-11 08:06:17,593 - INFO - 更新表单数据成功: FINST-3PF66O71LS2W4I4JC725OCYBXUW32W17PONBMOW
2025-06-11 08:06:17,608 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_********, 变更字段: [{'field': 'amount', 'old_value': 8770.8, 'new_value': 8770.1}, {'field': 'onlineAmount', 'old_value': 5570.9, 'new_value': 5570.2}]
2025-06-11 08:06:18,062 - INFO - 更新表单数据成功: FINST-3PF66O71LS2W4I4JC725OCYBXUW32W17PONBM2X
2025-06-11 08:06:18,062 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 17536.26, 'new_value': 18167.26}, {'field': 'amount', 'old_value': 17536.26, 'new_value': 18167.26}, {'field': 'count', 'old_value': 82, 'new_value': 83}, {'field': 'instoreAmount', 'old_value': 17582.26, 'new_value': 18213.26}, {'field': 'instoreCount', 'old_value': 82, 'new_value': 83}]
2025-06-11 08:06:18,671 - INFO - 更新表单数据成功: FINST-3PF66O71LS2W4I4JC725OCYBXUW32W17PONBM6X
2025-06-11 08:06:18,671 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJP082LC00I86N3H2U1JM001ESS_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 946.4, 'new_value': 955.0}, {'field': 'amount', 'old_value': 946.4, 'new_value': 955.0}, {'field': 'instoreAmount', 'old_value': 946.4, 'new_value': 955.0}]
2025-06-11 08:06:19,233 - INFO - 更新表单数据成功: FINST-3PF66O71LS2W4I4JC725OCYBXUW32W17PONBMUY
2025-06-11 08:06:19,233 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_********, 变更字段: [{'field': 'instoreAmount', 'old_value': 4688.75, 'new_value': 4690.85}, {'field': 'instoreCount', 'old_value': 291, 'new_value': 292}, {'field': 'onlineAmount', 'old_value': 2145.5, 'new_value': 2143.4}, {'field': 'onlineCount', 'old_value': 188, 'new_value': 187}]
2025-06-11 08:06:19,718 - INFO - 更新表单数据成功: FINST-W3B66L71M82WFNUFB1B5M90MNLYJ3MP9PONBMVT
2025-06-11 08:06:19,718 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_********, 变更字段: [{'field': 'amount', 'old_value': 21594.27, 'new_value': 21597.27}, {'field': 'onlineAmount', 'old_value': 933.05, 'new_value': 936.05}]
2025-06-11 08:06:20,218 - INFO - 更新表单数据成功: FINST-QVA66B81CP5W2IQXEOLCOD2LQ1ZL398GKRPBM12
2025-06-11 08:06:20,218 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 5399.7}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5399.7}]
2025-06-11 08:06:20,687 - INFO - 更新表单数据成功: FINST-QVA66B81CP5W2IQXEOLCOD2LQ1ZL398GKRPBM92
2025-06-11 08:06:20,687 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6692283A183D432BAE322E1032539CE8_********, 变更字段: [{'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 12423.9}]
2025-06-11 08:06:21,108 - INFO - 更新表单数据成功: FINST-QVA66B81CP5W2IQXEOLCOD2LQ1ZL398GKRPBMC2
2025-06-11 08:06:21,108 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1I6E0VAU3IFJEQ22MH147FMU0M0013E8_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 429.11}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 429.11}, {'field': 'amount', 'old_value': 58.9, 'new_value': 685.9}, {'field': 'count', 'old_value': 2, 'new_value': 3}, {'field': 'instoreAmount', 'old_value': 58.9, 'new_value': 685.9}, {'field': 'instoreCount', 'old_value': 2, 'new_value': 3}]
2025-06-11 08:06:21,608 - INFO - 更新表单数据成功: FINST-QVA66B81CP5W2IQXEOLCOD2LQ1ZL398GKRPBMM2
2025-06-11 08:06:21,608 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE2TORA7KI7Q2OV4FVC7FC0014BT_********, 变更字段: [{'field': 'amount', 'old_value': 2012.21, 'new_value': 2146.35}, {'field': 'count', 'old_value': 59, 'new_value': 60}, {'field': 'instoreAmount', 'old_value': 1368.9, 'new_value': 1503.04}, {'field': 'instoreCount', 'old_value': 46, 'new_value': 47}]
2025-06-11 08:06:22,108 - INFO - 更新表单数据成功: FINST-QVA66B81CP5W2IQXEOLCOD2LQ1ZL398GKRPBMU2
2025-06-11 08:06:22,108 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_********, 变更字段: [{'field': 'amount', 'old_value': 3515.45, 'new_value': 3498.95}, {'field': 'onlineAmount', 'old_value': 1303.5, 'new_value': 1287.0}]
2025-06-11 08:06:22,562 - INFO - 更新表单数据成功: FINST-QVA66B81CP5W2IQXEOLCOD2LQ1ZL3A8GKRPBM03
2025-06-11 08:06:22,562 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_********, 变更字段: [{'field': 'amount', 'old_value': 1120.0, 'new_value': 1139.0}, {'field': 'count', 'old_value': 53, 'new_value': 54}, {'field': 'instoreAmount', 'old_value': 405.0, 'new_value': 424.0}, {'field': 'instoreCount', 'old_value': 14, 'new_value': 15}]
2025-06-11 08:06:22,983 - INFO - 更新表单数据成功: FINST-QVA66B81CP5W2IQXEOLCOD2LQ1ZL3A8GKRPBMA3
2025-06-11 08:06:22,983 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCIR7D5JD7Q2OVBN4IS7U001D40_********, 变更字段: [{'field': 'count', 'old_value': 10, 'new_value': 11}, {'field': 'instoreCount', 'old_value': 10, 'new_value': 11}]
2025-06-11 08:06:23,421 - INFO - 更新表单数据成功: FINST-QVA66B81CP5W2IQXEOLCOD2LQ1ZL3A8GKRPBME3
2025-06-11 08:06:23,421 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_********, 变更字段: [{'field': 'amount', 'old_value': 313.0, 'new_value': 1812.03}, {'field': 'count', 'old_value': 1, 'new_value': 3}, {'field': 'instoreAmount', 'old_value': 421.0, 'new_value': 1920.03}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 3}]
2025-06-11 08:06:23,843 - INFO - 更新表单数据成功: FINST-QVA66B81CP5W2IQXEOLCOD2LQ1ZL3A8GKRPBMN3
2025-06-11 08:06:23,843 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 748.29, 'new_value': 2159.44}, {'field': 'dailyBillAmount', 'old_value': 748.29, 'new_value': 2159.44}]
2025-06-11 08:06:24,327 - INFO - 更新表单数据成功: FINST-QVA66B81CP5W2IQXEOLCOD2LQ1ZL3A8GKRPBMS3
2025-06-11 08:06:24,327 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2385.14, 'new_value': 2418.24}, {'field': 'amount', 'old_value': 2385.14, 'new_value': 2418.2400000000002}, {'field': 'count', 'old_value': 128, 'new_value': 129}, {'field': 'onlineAmount', 'old_value': 2108.81, 'new_value': 2141.91}, {'field': 'onlineCount', 'old_value': 112, 'new_value': 113}]
2025-06-11 08:06:24,874 - INFO - 更新表单数据成功: FINST-QVA66B81CP5W2IQXEOLCOD2LQ1ZL3A8GKRPBMW3
2025-06-11 08:06:24,874 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1944.58, 'new_value': 1948.58}, {'field': 'amount', 'old_value': 1944.58, 'new_value': 1948.58}, {'field': 'instoreAmount', 'old_value': 1957.61, 'new_value': 1961.61}]
2025-06-11 08:06:25,327 - INFO - 更新表单数据成功: FINST-QVA66B81CP5W2IQXEOLCOD2LQ1ZL3A8GKRPBMZ3
2025-06-11 08:06:25,327 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 6885.5, 'new_value': 7868.0}, {'field': 'dailyBillAmount', 'old_value': 6885.5, 'new_value': 7868.0}]
2025-06-11 08:06:25,827 - INFO - 更新表单数据成功: FINST-QVA66B81CP5W2IQXEOLCOD2LQ1ZL3A8GKRPBM44
2025-06-11 08:06:25,827 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_********, 变更字段: [{'field': 'amount', 'old_value': 2514.87, 'new_value': 2514.97}, {'field': 'count', 'old_value': 144, 'new_value': 145}, {'field': 'onlineAmount', 'old_value': 2254.5, 'new_value': 2254.6}, {'field': 'onlineCount', 'old_value': 105, 'new_value': 106}]
2025-06-11 08:06:26,265 - INFO - 更新表单数据成功: FINST-QVA66B81CP5W2IQXEOLCOD2LQ1ZL3A8GKRPBM74
2025-06-11 08:06:26,265 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1207.72, 'new_value': 1172.43}, {'field': 'amount', 'old_value': 1207.7199999999998, 'new_value': 1172.4299999999998}, {'field': 'onlineAmount', 'old_value': 750.62, 'new_value': 715.33}]
2025-06-11 08:06:26,687 - INFO - 更新表单数据成功: FINST-IQG66AD18P5WU1E97MVV29HF1GJU28XIKRPBMT6
2025-06-11 08:06:26,687 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 4911.8, 'new_value': 4925.6}, {'field': 'amount', 'old_value': 4911.799999999999, 'new_value': 4925.599999999999}, {'field': 'count', 'old_value': 235, 'new_value': 237}, {'field': 'onlineAmount', 'old_value': 3775.21, 'new_value': 3789.01}, {'field': 'onlineCount', 'old_value': 177, 'new_value': 179}]
2025-06-11 08:06:27,155 - INFO - 更新表单数据成功: FINST-IQG66AD18P5WU1E97MVV29HF1GJU28XIKRPBMX6
2025-06-11 08:06:27,155 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 14425.94}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 14425.94}]
2025-06-11 08:06:27,608 - INFO - 更新表单数据成功: FINST-IQG66AD18P5WU1E97MVV29HF1GJU28XIKRPBM07
2025-06-11 08:06:27,608 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEA4041D6F0I86N3H2U1V9001F8F_********, 变更字段: [{'field': 'amount', 'old_value': 15073.16, 'new_value': 15095.460000000001}, {'field': 'count', 'old_value': 141, 'new_value': 142}, {'field': 'onlineAmount', 'old_value': 1886.02, 'new_value': 1908.32}, {'field': 'onlineCount', 'old_value': 67, 'new_value': 68}]
2025-06-11 08:06:28,093 - INFO - 更新表单数据成功: FINST-IQG66AD18P5WU1E97MVV29HF1GJU28XIKRPBM87
2025-06-11 08:06:28,108 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE25DAIM3B0I86N3H2U1RD001F4J_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 10429.34}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 10429.34}]
2025-06-11 08:06:28,530 - INFO - 更新表单数据成功: FINST-IQG66AD18P5WU1E97MVV29HF1GJU28XIKRPBMC7
2025-06-11 08:06:28,530 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_********, 变更字段: [{'field': 'amount', 'old_value': 7335.71, 'new_value': 7434.31}, {'field': 'count', 'old_value': 332, 'new_value': 337}, {'field': 'instoreAmount', 'old_value': 2352.8, 'new_value': 2419.6}, {'field': 'instoreCount', 'old_value': 122, 'new_value': 124}, {'field': 'onlineAmount', 'old_value': 5037.78, 'new_value': 5069.58}, {'field': 'onlineCount', 'old_value': 210, 'new_value': 213}]
2025-06-11 08:06:29,108 - INFO - 更新表单数据成功: FINST-IQG66AD18P5WU1E97MVV29HF1GJU28XIKRPBMZ7
2025-06-11 08:06:29,108 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE13J2R9CI0I86N3H2U13D001ECJ_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 4615.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 4615.0}]
2025-06-11 08:06:29,593 - INFO - 更新表单数据成功: FINST-IQG66AD18P5WU1E97MVV29HF1GJU28XIKRPBM38
2025-06-11 08:06:29,593 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_********, 变更字段: [{'field': 'amount', 'old_value': 3343.94, 'new_value': 3344.44}, {'field': 'count', 'old_value': 221, 'new_value': 228}, {'field': 'onlineAmount', 'old_value': 3306.94, 'new_value': 3337.44}, {'field': 'onlineCount', 'old_value': 213, 'new_value': 220}]
2025-06-11 08:06:30,030 - INFO - 更新表单数据成功: FINST-IQG66AD18P5WU1E97MVV29HF1GJU28XIKRPBM58
2025-06-11 08:06:30,030 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSK489TE20I86N3H2U114001EAA_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 7884.72, 'new_value': 7925.12}, {'field': 'amount', 'old_value': 7884.72, 'new_value': 7925.12}, {'field': 'count', 'old_value': 60, 'new_value': 61}, {'field': 'instoreAmount', 'old_value': 7714.22, 'new_value': 7754.62}, {'field': 'instoreCount', 'old_value': 57, 'new_value': 58}]
2025-06-11 08:06:30,499 - INFO - 更新表单数据成功: FINST-IQG66AD18P5WU1E97MVV29HF1GJU29XIKRPBMA8
2025-06-11 08:06:30,499 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_********, 变更字段: [{'field': 'instoreAmount', 'old_value': 3732.22, 'new_value': 3740.22}, {'field': 'instoreCount', 'old_value': 218, 'new_value': 221}, {'field': 'onlineAmount', 'old_value': 1217.7, 'new_value': 1209.7}, {'field': 'onlineCount', 'old_value': 86, 'new_value': 83}]
2025-06-11 08:06:30,952 - INFO - 更新表单数据成功: FINST-IQG66AD18P5WU1E97MVV29HF1GJU29XIKRPBMJ8
2025-06-11 08:06:30,952 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1I14PQ9FMDG1FG3MDGKU4P0I8N00178C_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 7146.26, 'new_value': 7566.77}, {'field': 'amount', 'old_value': 7146.26, 'new_value': 7566.77}, {'field': 'count', 'old_value': 294, 'new_value': 325}, {'field': 'onlineAmount', 'old_value': 7451.96, 'new_value': 7926.47}, {'field': 'onlineCount', 'old_value': 294, 'new_value': 325}]
2025-06-11 08:06:31,390 - INFO - 更新表单数据成功: FINST-IQG66AD18P5WU1E97MVV29HF1GJU29XIKRPBMU8
2025-06-11 08:06:31,390 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_********, 变更字段: [{'field': 'count', 'old_value': 162, 'new_value': 163}, {'field': 'instoreCount', 'old_value': 69, 'new_value': 70}]
2025-06-11 08:06:31,843 - INFO - 更新表单数据成功: FINST-IQG66AD18P5WU1E97MVV29HF1GJU29XIKRPBMW8
2025-06-11 08:06:31,843 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 3641.99}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 3641.99}]
2025-06-11 08:06:32,358 - INFO - 更新表单数据成功: FINST-IQG66AD18P5WU1E97MVV29HF1GJU29XIKRPBM39
2025-06-11 08:06:32,358 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK_********, 变更字段: [{'field': 'amount', 'old_value': 5505.26, 'new_value': 5734.26}, {'field': 'count', 'old_value': 40, 'new_value': 41}, {'field': 'instoreAmount', 'old_value': 5039.8, 'new_value': 5268.8}, {'field': 'instoreCount', 'old_value': 30, 'new_value': 31}]
2025-06-11 08:06:32,780 - INFO - 更新表单数据成功: FINST-IQG66AD18P5WU1E97MVV29HF1GJU29XIKRPBM59
2025-06-11 08:06:32,780 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_********, 变更字段: [{'field': 'amount', 'old_value': 27556.87, 'new_value': 29765.38}, {'field': 'count', 'old_value': 108, 'new_value': 110}, {'field': 'instoreAmount', 'old_value': 26317.17, 'new_value': 28500.78}, {'field': 'instoreCount', 'old_value': 68, 'new_value': 69}, {'field': 'onlineAmount', 'old_value': 1239.7, 'new_value': 1264.6}, {'field': 'onlineCount', 'old_value': 40, 'new_value': 41}]
2025-06-11 08:06:33,280 - INFO - 更新表单数据成功: FINST-IQG66AD18P5WU1E97MVV29HF1GJU29XIKRPBM89
2025-06-11 08:06:33,280 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_********, 变更字段: [{'field': 'amount', 'old_value': 11496.95, 'new_value': 11197.550000000001}]
2025-06-11 08:06:33,733 - INFO - 更新表单数据成功: FINST-IQG66AD18P5WU1E97MVV29HF1GJU29XIKRPBMB9
2025-06-11 08:06:33,733 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_********, 变更字段: [{'field': 'amount', 'old_value': 8735.9, 'new_value': 10876.8}, {'field': 'count', 'old_value': 7, 'new_value': 8}, {'field': 'instoreAmount', 'old_value': 8735.9, 'new_value': 10876.8}, {'field': 'instoreCount', 'old_value': 7, 'new_value': 8}]
2025-06-11 08:06:34,202 - INFO - 更新表单数据成功: FINST-IQG66AD18P5WU1E97MVV29HF1GJU29XIKRPBMD9
2025-06-11 08:06:34,202 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_********, 变更字段: [{'field': 'amount', 'old_value': 27121.24, 'new_value': 33187.69}, {'field': 'count', 'old_value': 142, 'new_value': 149}, {'field': 'instoreAmount', 'old_value': 25263.04, 'new_value': 31329.49}, {'field': 'instoreCount', 'old_value': 119, 'new_value': 126}]
2025-06-11 08:06:34,671 - INFO - 更新表单数据成功: FINST-UW9663718Q5WJGEL7VIBU8QC0L7B34ELKRPBMRB
2025-06-11 08:06:34,671 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_********, 变更字段: [{'field': 'amount', 'old_value': 1110.4199999999998, 'new_value': 1104.52}]
2025-06-11 08:06:34,874 - INFO - 正在批量插入每日数据，批次 1/64，共 100 条记录
2025-06-11 08:06:35,421 - INFO - 批量插入每日数据成功，批次 1，100 条记录
2025-06-11 08:06:38,436 - INFO - 正在批量插入每日数据，批次 2/64，共 100 条记录
2025-06-11 08:06:38,811 - INFO - 批量插入每日数据成功，批次 2，100 条记录
2025-06-11 08:06:41,827 - INFO - 正在批量插入每日数据，批次 3/64，共 100 条记录
2025-06-11 08:06:42,296 - INFO - 批量插入每日数据成功，批次 3，100 条记录
2025-06-11 08:06:45,311 - INFO - 正在批量插入每日数据，批次 4/64，共 100 条记录
2025-06-11 08:06:45,733 - INFO - 批量插入每日数据成功，批次 4，100 条记录
2025-06-11 08:06:48,749 - INFO - 正在批量插入每日数据，批次 5/64，共 100 条记录
2025-06-11 08:06:49,358 - INFO - 批量插入每日数据成功，批次 5，100 条记录
2025-06-11 08:06:52,374 - INFO - 正在批量插入每日数据，批次 6/64，共 100 条记录
2025-06-11 08:06:52,796 - INFO - 批量插入每日数据成功，批次 6，100 条记录
2025-06-11 08:06:55,811 - INFO - 正在批量插入每日数据，批次 7/64，共 100 条记录
2025-06-11 08:06:56,405 - INFO - 批量插入每日数据成功，批次 7，100 条记录
2025-06-11 08:06:59,421 - INFO - 正在批量插入每日数据，批次 8/64，共 100 条记录
2025-06-11 08:06:59,889 - INFO - 批量插入每日数据成功，批次 8，100 条记录
2025-06-11 08:07:02,921 - INFO - 正在批量插入每日数据，批次 9/64，共 100 条记录
2025-06-11 08:07:03,358 - INFO - 批量插入每日数据成功，批次 9，100 条记录
2025-06-11 08:07:06,374 - INFO - 正在批量插入每日数据，批次 10/64，共 100 条记录
2025-06-11 08:07:06,796 - INFO - 批量插入每日数据成功，批次 10，100 条记录
2025-06-11 08:07:09,811 - INFO - 正在批量插入每日数据，批次 11/64，共 100 条记录
2025-06-11 08:07:10,202 - INFO - 批量插入每日数据成功，批次 11，100 条记录
2025-06-11 08:07:13,217 - INFO - 正在批量插入每日数据，批次 12/64，共 100 条记录
2025-06-11 08:07:13,655 - INFO - 批量插入每日数据成功，批次 12，100 条记录
2025-06-11 08:07:16,671 - INFO - 正在批量插入每日数据，批次 13/64，共 100 条记录
2025-06-11 08:07:17,077 - INFO - 批量插入每日数据成功，批次 13，100 条记录
2025-06-11 08:07:20,092 - INFO - 正在批量插入每日数据，批次 14/64，共 100 条记录
2025-06-11 08:07:20,483 - INFO - 批量插入每日数据成功，批次 14，100 条记录
2025-06-11 08:07:23,499 - INFO - 正在批量插入每日数据，批次 15/64，共 100 条记录
2025-06-11 08:07:24,108 - INFO - 批量插入每日数据成功，批次 15，100 条记录
2025-06-11 08:07:27,124 - INFO - 正在批量插入每日数据，批次 16/64，共 100 条记录
2025-06-11 08:07:27,499 - INFO - 批量插入每日数据成功，批次 16，100 条记录
2025-06-11 08:07:30,514 - INFO - 正在批量插入每日数据，批次 17/64，共 100 条记录
2025-06-11 08:07:30,889 - INFO - 批量插入每日数据成功，批次 17，100 条记录
2025-06-11 08:07:33,905 - INFO - 正在批量插入每日数据，批次 18/64，共 100 条记录
2025-06-11 08:07:34,295 - INFO - 批量插入每日数据成功，批次 18，100 条记录
2025-06-11 08:07:37,311 - INFO - 正在批量插入每日数据，批次 19/64，共 100 条记录
2025-06-11 08:07:37,764 - INFO - 批量插入每日数据成功，批次 19，100 条记录
2025-06-11 08:07:40,780 - INFO - 正在批量插入每日数据，批次 20/64，共 100 条记录
2025-06-11 08:07:41,155 - INFO - 批量插入每日数据成功，批次 20，100 条记录
2025-06-11 08:07:44,170 - INFO - 正在批量插入每日数据，批次 21/64，共 100 条记录
2025-06-11 08:07:44,577 - INFO - 批量插入每日数据成功，批次 21，100 条记录
2025-06-11 08:07:47,592 - INFO - 正在批量插入每日数据，批次 22/64，共 100 条记录
2025-06-11 08:07:48,014 - INFO - 批量插入每日数据成功，批次 22，100 条记录
2025-06-11 08:07:51,030 - INFO - 正在批量插入每日数据，批次 23/64，共 100 条记录
2025-06-11 08:07:51,483 - INFO - 批量插入每日数据成功，批次 23，100 条记录
2025-06-11 08:07:54,498 - INFO - 正在批量插入每日数据，批次 24/64，共 100 条记录
2025-06-11 08:07:54,889 - INFO - 批量插入每日数据成功，批次 24，100 条记录
2025-06-11 08:07:57,905 - INFO - 正在批量插入每日数据，批次 25/64，共 100 条记录
2025-06-11 08:07:58,373 - INFO - 批量插入每日数据成功，批次 25，100 条记录
2025-06-11 08:08:01,389 - INFO - 正在批量插入每日数据，批次 26/64，共 100 条记录
2025-06-11 08:08:01,952 - INFO - 批量插入每日数据成功，批次 26，100 条记录
2025-06-11 08:08:04,967 - INFO - 正在批量插入每日数据，批次 27/64，共 100 条记录
2025-06-11 08:08:05,311 - INFO - 批量插入每日数据成功，批次 27，100 条记录
2025-06-11 08:08:08,327 - INFO - 正在批量插入每日数据，批次 28/64，共 100 条记录
2025-06-11 08:08:08,811 - INFO - 批量插入每日数据成功，批次 28，100 条记录
2025-06-11 08:08:11,826 - INFO - 正在批量插入每日数据，批次 29/64，共 100 条记录
2025-06-11 08:08:12,186 - INFO - 批量插入每日数据成功，批次 29，100 条记录
2025-06-11 08:08:15,201 - INFO - 正在批量插入每日数据，批次 30/64，共 100 条记录
2025-06-11 08:08:15,733 - INFO - 批量插入每日数据成功，批次 30，100 条记录
2025-06-11 08:08:18,748 - INFO - 正在批量插入每日数据，批次 31/64，共 100 条记录
2025-06-11 08:08:19,233 - INFO - 批量插入每日数据成功，批次 31，100 条记录
2025-06-11 08:08:22,248 - INFO - 正在批量插入每日数据，批次 32/64，共 100 条记录
2025-06-11 08:08:22,576 - INFO - 批量插入每日数据成功，批次 32，100 条记录
2025-06-11 08:08:25,608 - INFO - 正在批量插入每日数据，批次 33/64，共 100 条记录
2025-06-11 08:08:26,155 - INFO - 批量插入每日数据成功，批次 33，100 条记录
2025-06-11 08:08:29,170 - INFO - 正在批量插入每日数据，批次 34/64，共 100 条记录
2025-06-11 08:08:29,654 - INFO - 批量插入每日数据成功，批次 34，100 条记录
2025-06-11 08:08:32,670 - INFO - 正在批量插入每日数据，批次 35/64，共 100 条记录
2025-06-11 08:08:33,154 - INFO - 批量插入每日数据成功，批次 35，100 条记录
2025-06-11 08:08:36,170 - INFO - 正在批量插入每日数据，批次 36/64，共 100 条记录
2025-06-11 08:08:36,576 - INFO - 批量插入每日数据成功，批次 36，100 条记录
2025-06-11 08:08:39,592 - INFO - 正在批量插入每日数据，批次 37/64，共 100 条记录
2025-06-11 08:08:40,045 - INFO - 批量插入每日数据成功，批次 37，100 条记录
2025-06-11 08:08:43,061 - INFO - 正在批量插入每日数据，批次 38/64，共 100 条记录
2025-06-11 08:08:43,498 - INFO - 批量插入每日数据成功，批次 38，100 条记录
2025-06-11 08:08:46,514 - INFO - 正在批量插入每日数据，批次 39/64，共 100 条记录
2025-06-11 08:08:46,889 - INFO - 批量插入每日数据成功，批次 39，100 条记录
2025-06-11 08:08:49,904 - INFO - 正在批量插入每日数据，批次 40/64，共 100 条记录
2025-06-11 08:08:50,326 - INFO - 批量插入每日数据成功，批次 40，100 条记录
2025-06-11 08:08:53,326 - INFO - 正在批量插入每日数据，批次 41/64，共 100 条记录
2025-06-11 08:08:53,732 - INFO - 批量插入每日数据成功，批次 41，100 条记录
2025-06-11 08:08:56,748 - INFO - 正在批量插入每日数据，批次 42/64，共 100 条记录
2025-06-11 08:08:57,264 - INFO - 批量插入每日数据成功，批次 42，100 条记录
2025-06-11 08:09:00,279 - INFO - 正在批量插入每日数据，批次 43/64，共 100 条记录
2025-06-11 08:09:00,748 - INFO - 批量插入每日数据成功，批次 43，100 条记录
2025-06-11 08:09:03,748 - INFO - 正在批量插入每日数据，批次 44/64，共 100 条记录
2025-06-11 08:09:04,217 - INFO - 批量插入每日数据成功，批次 44，100 条记录
2025-06-11 08:09:07,232 - INFO - 正在批量插入每日数据，批次 45/64，共 100 条记录
2025-06-11 08:09:07,623 - INFO - 批量插入每日数据成功，批次 45，100 条记录
2025-06-11 08:09:10,639 - INFO - 正在批量插入每日数据，批次 46/64，共 100 条记录
2025-06-11 08:09:11,092 - INFO - 批量插入每日数据成功，批次 46，100 条记录
2025-06-11 08:09:14,107 - INFO - 正在批量插入每日数据，批次 47/64，共 100 条记录
2025-06-11 08:09:14,514 - INFO - 批量插入每日数据成功，批次 47，100 条记录
2025-06-11 08:09:17,529 - INFO - 正在批量插入每日数据，批次 48/64，共 100 条记录
2025-06-11 08:09:17,967 - INFO - 批量插入每日数据成功，批次 48，100 条记录
2025-06-11 08:09:20,982 - INFO - 正在批量插入每日数据，批次 49/64，共 100 条记录
2025-06-11 08:09:21,514 - INFO - 批量插入每日数据成功，批次 49，100 条记录
2025-06-11 08:09:24,545 - INFO - 正在批量插入每日数据，批次 50/64，共 100 条记录
2025-06-11 08:09:24,967 - INFO - 批量插入每日数据成功，批次 50，100 条记录
2025-06-11 08:09:27,982 - INFO - 正在批量插入每日数据，批次 51/64，共 100 条记录
2025-06-11 08:09:28,388 - INFO - 批量插入每日数据成功，批次 51，100 条记录
2025-06-11 08:09:31,420 - INFO - 正在批量插入每日数据，批次 52/64，共 100 条记录
2025-06-11 08:09:31,826 - INFO - 批量插入每日数据成功，批次 52，100 条记录
2025-06-11 08:09:34,826 - INFO - 正在批量插入每日数据，批次 53/64，共 100 条记录
2025-06-11 08:09:35,357 - INFO - 批量插入每日数据成功，批次 53，100 条记录
2025-06-11 08:09:38,373 - INFO - 正在批量插入每日数据，批次 54/64，共 100 条记录
2025-06-11 08:09:38,826 - INFO - 批量插入每日数据成功，批次 54，100 条记录
2025-06-11 08:09:41,841 - INFO - 正在批量插入每日数据，批次 55/64，共 100 条记录
2025-06-11 08:09:42,295 - INFO - 批量插入每日数据成功，批次 55，100 条记录
2025-06-11 08:09:45,310 - INFO - 正在批量插入每日数据，批次 56/64，共 100 条记录
2025-06-11 08:09:45,748 - INFO - 批量插入每日数据成功，批次 56，100 条记录
2025-06-11 08:09:48,748 - INFO - 正在批量插入每日数据，批次 57/64，共 100 条记录
2025-06-11 08:09:49,263 - INFO - 批量插入每日数据成功，批次 57，100 条记录
2025-06-11 08:09:52,279 - INFO - 正在批量插入每日数据，批次 58/64，共 100 条记录
2025-06-11 08:09:52,748 - INFO - 批量插入每日数据成功，批次 58，100 条记录
2025-06-11 08:09:55,763 - INFO - 正在批量插入每日数据，批次 59/64，共 100 条记录
2025-06-11 08:09:56,232 - INFO - 批量插入每日数据成功，批次 59，100 条记录
2025-06-11 08:09:59,248 - INFO - 正在批量插入每日数据，批次 60/64，共 100 条记录
2025-06-11 08:09:59,732 - INFO - 批量插入每日数据成功，批次 60，100 条记录
2025-06-11 08:10:02,748 - INFO - 正在批量插入每日数据，批次 61/64，共 100 条记录
2025-06-11 08:10:03,232 - INFO - 批量插入每日数据成功，批次 61，100 条记录
2025-06-11 08:10:06,248 - INFO - 正在批量插入每日数据，批次 62/64，共 100 条记录
2025-06-11 08:10:06,669 - INFO - 批量插入每日数据成功，批次 62，100 条记录
2025-06-11 08:10:09,685 - INFO - 正在批量插入每日数据，批次 63/64，共 100 条记录
2025-06-11 08:10:10,169 - INFO - 批量插入每日数据成功，批次 63，100 条记录
2025-06-11 08:10:13,185 - INFO - 正在批量插入每日数据，批次 64/64，共 6 条记录
2025-06-11 08:10:13,357 - INFO - 批量插入每日数据成功，批次 64，6 条记录
2025-06-11 08:10:16,373 - INFO - 批量插入每日数据完成: 总计 6306 条，成功 6306 条，失败 0 条
2025-06-11 08:10:16,373 - INFO - 批量插入日销售数据完成，共 6306 条记录
2025-06-11 08:10:16,373 - INFO - 日销售数据同步完成！更新: 65 条，插入: 6306 条，错误: 0 条，跳过: 6461 条
2025-06-11 08:10:16,373 - INFO - 正在获取宜搭月销售表单数据...
2025-06-11 08:10:16,373 - INFO - 开始获取宜搭月度表单数据，总时间范围: 2024-06-01 至 2025-06-30
2025-06-11 08:10:16,373 - INFO - 查询月度分段 1: 2024-06-01 至 2024-08-31
2025-06-11 08:10:16,373 - INFO - 查询日期范围: 2024-06-01 至 2024-08-31，使用分页查询，每页 100 条记录
2025-06-11 08:10:16,373 - INFO - Request Parameters - Page 1:
2025-06-11 08:10:16,373 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:10:16,373 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1717171200000, 1725033600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:10:17,263 - INFO - API请求耗时: 891ms
2025-06-11 08:10:17,263 - INFO - Response - Page 1
2025-06-11 08:10:17,263 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-11 08:10:17,263 - INFO - 查询完成，共获取到 0 条记录
2025-06-11 08:10:17,263 - WARNING - 月度分段 1 查询返回空数据
2025-06-11 08:10:17,263 - INFO - 尝试将月度分段 1 再细分为单月查询
2025-06-11 08:10:17,263 - INFO - 查询日期范围: 2024-06-01 至 2024-06-30，使用分页查询，每页 100 条记录
2025-06-11 08:10:17,263 - INFO - Request Parameters - Page 1:
2025-06-11 08:10:17,263 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:10:17,263 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1717171200000, 1719676800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:10:17,498 - INFO - API请求耗时: 234ms
2025-06-11 08:10:17,498 - INFO - Response - Page 1
2025-06-11 08:10:17,498 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-11 08:10:17,498 - INFO - 查询完成，共获取到 0 条记录
2025-06-11 08:10:17,498 - WARNING - 单月查询返回空数据: 2024-06
2025-06-11 08:10:18,013 - INFO - 查询日期范围: 2024-07-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-06-11 08:10:18,013 - INFO - Request Parameters - Page 1:
2025-06-11 08:10:18,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:10:18,013 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1719763200000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:10:18,232 - INFO - API请求耗时: 219ms
2025-06-11 08:10:18,232 - INFO - Response - Page 1
2025-06-11 08:10:18,232 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-11 08:10:18,232 - INFO - 查询完成，共获取到 0 条记录
2025-06-11 08:10:18,232 - WARNING - 单月查询返回空数据: 2024-07
2025-06-11 08:10:18,748 - INFO - 查询日期范围: 2024-08-01 至 2024-08-31，使用分页查询，每页 100 条记录
2025-06-11 08:10:18,748 - INFO - Request Parameters - Page 1:
2025-06-11 08:10:18,748 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:10:18,748 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1725033600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:10:18,966 - INFO - API请求耗时: 219ms
2025-06-11 08:10:18,966 - INFO - Response - Page 1
2025-06-11 08:10:18,966 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-11 08:10:18,966 - INFO - 查询完成，共获取到 0 条记录
2025-06-11 08:10:18,966 - WARNING - 单月查询返回空数据: 2024-08
2025-06-11 08:10:20,482 - INFO - 查询月度分段 2: 2024-09-01 至 2024-11-30
2025-06-11 08:10:20,482 - INFO - 查询日期范围: 2024-09-01 至 2024-11-30，使用分页查询，每页 100 条记录
2025-06-11 08:10:20,482 - INFO - Request Parameters - Page 1:
2025-06-11 08:10:20,482 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:10:20,482 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1725120000000, 1732896000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:10:20,716 - INFO - API请求耗时: 234ms
2025-06-11 08:10:20,716 - INFO - Response - Page 1
2025-06-11 08:10:20,716 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-11 08:10:20,716 - INFO - 查询完成，共获取到 0 条记录
2025-06-11 08:10:20,716 - WARNING - 月度分段 2 查询返回空数据
2025-06-11 08:10:20,716 - INFO - 尝试将月度分段 2 再细分为单月查询
2025-06-11 08:10:20,716 - INFO - 查询日期范围: 2024-09-01 至 2024-09-30，使用分页查询，每页 100 条记录
2025-06-11 08:10:20,716 - INFO - Request Parameters - Page 1:
2025-06-11 08:10:20,716 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:10:20,716 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1725120000000, 1727625600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:10:20,919 - INFO - API请求耗时: 203ms
2025-06-11 08:10:20,919 - INFO - Response - Page 1
2025-06-11 08:10:20,919 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-11 08:10:20,919 - INFO - 查询完成，共获取到 0 条记录
2025-06-11 08:10:20,919 - WARNING - 单月查询返回空数据: 2024-09
2025-06-11 08:10:21,419 - INFO - 查询日期范围: 2024-10-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-06-11 08:10:21,419 - INFO - Request Parameters - Page 1:
2025-06-11 08:10:21,419 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:10:21,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1727712000000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:10:21,622 - INFO - API请求耗时: 203ms
2025-06-11 08:10:21,622 - INFO - Response - Page 1
2025-06-11 08:10:21,622 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-11 08:10:21,622 - INFO - 查询完成，共获取到 0 条记录
2025-06-11 08:10:21,622 - WARNING - 单月查询返回空数据: 2024-10
2025-06-11 08:10:22,122 - INFO - 查询日期范围: 2024-11-01 至 2024-11-30，使用分页查询，每页 100 条记录
2025-06-11 08:10:22,122 - INFO - Request Parameters - Page 1:
2025-06-11 08:10:22,122 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:10:22,122 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1732896000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:10:22,310 - INFO - API请求耗时: 187ms
2025-06-11 08:10:22,326 - INFO - Response - Page 1
2025-06-11 08:10:22,326 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-11 08:10:22,326 - INFO - 查询完成，共获取到 0 条记录
2025-06-11 08:10:22,326 - WARNING - 单月查询返回空数据: 2024-11
2025-06-11 08:10:23,841 - INFO - 查询月度分段 3: 2024-12-01 至 2025-02-28
2025-06-11 08:10:23,841 - INFO - 查询日期范围: 2024-12-01 至 2025-02-28，使用分页查询，每页 100 条记录
2025-06-11 08:10:23,841 - INFO - Request Parameters - Page 1:
2025-06-11 08:10:23,841 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:10:23,841 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:10:24,482 - INFO - API请求耗时: 641ms
2025-06-11 08:10:24,482 - INFO - Response - Page 1
2025-06-11 08:10:24,482 - INFO - 第 1 页获取到 100 条记录
2025-06-11 08:10:24,997 - INFO - Request Parameters - Page 2:
2025-06-11 08:10:24,997 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:10:24,997 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:10:25,685 - INFO - API请求耗时: 687ms
2025-06-11 08:10:25,685 - INFO - Response - Page 2
2025-06-11 08:10:25,685 - INFO - 第 2 页获取到 100 条记录
2025-06-11 08:10:26,201 - INFO - Request Parameters - Page 3:
2025-06-11 08:10:26,201 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:10:26,201 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:10:26,794 - INFO - API请求耗时: 594ms
2025-06-11 08:10:26,794 - INFO - Response - Page 3
2025-06-11 08:10:26,794 - INFO - 第 3 页获取到 100 条记录
2025-06-11 08:10:27,294 - INFO - Request Parameters - Page 4:
2025-06-11 08:10:27,294 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:10:27,294 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:10:27,857 - INFO - API请求耗时: 562ms
2025-06-11 08:10:27,857 - INFO - Response - Page 4
2025-06-11 08:10:27,857 - INFO - 第 4 页获取到 100 条记录
2025-06-11 08:10:28,357 - INFO - Request Parameters - Page 5:
2025-06-11 08:10:28,357 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:10:28,357 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:10:28,982 - INFO - API请求耗时: 625ms
2025-06-11 08:10:28,982 - INFO - Response - Page 5
2025-06-11 08:10:28,982 - INFO - 第 5 页获取到 94 条记录
2025-06-11 08:10:28,982 - INFO - 查询完成，共获取到 494 条记录
2025-06-11 08:10:28,982 - INFO - 月度分段 3 查询成功，获取到 494 条记录
2025-06-11 08:10:29,982 - INFO - 查询月度分段 4: 2025-03-01 至 2025-05-31
2025-06-11 08:10:29,982 - INFO - 查询日期范围: 2025-03-01 至 2025-05-31，使用分页查询，每页 100 条记录
2025-06-11 08:10:29,982 - INFO - Request Parameters - Page 1:
2025-06-11 08:10:29,982 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:10:29,982 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:10:30,529 - INFO - API请求耗时: 547ms
2025-06-11 08:10:30,529 - INFO - Response - Page 1
2025-06-11 08:10:30,529 - INFO - 第 1 页获取到 100 条记录
2025-06-11 08:10:31,029 - INFO - Request Parameters - Page 2:
2025-06-11 08:10:31,029 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:10:31,029 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:10:31,544 - INFO - API请求耗时: 516ms
2025-06-11 08:10:31,544 - INFO - Response - Page 2
2025-06-11 08:10:31,544 - INFO - 第 2 页获取到 100 条记录
2025-06-11 08:10:32,060 - INFO - Request Parameters - Page 3:
2025-06-11 08:10:32,060 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:10:32,060 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:10:32,607 - INFO - API请求耗时: 547ms
2025-06-11 08:10:32,607 - INFO - Response - Page 3
2025-06-11 08:10:32,607 - INFO - 第 3 页获取到 100 条记录
2025-06-11 08:10:33,122 - INFO - Request Parameters - Page 4:
2025-06-11 08:10:33,122 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:10:33,122 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:10:33,732 - INFO - API请求耗时: 609ms
2025-06-11 08:10:33,732 - INFO - Response - Page 4
2025-06-11 08:10:33,732 - INFO - 第 4 页获取到 100 条记录
2025-06-11 08:10:34,232 - INFO - Request Parameters - Page 5:
2025-06-11 08:10:34,232 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:10:34,232 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:10:34,841 - INFO - API请求耗时: 609ms
2025-06-11 08:10:34,841 - INFO - Response - Page 5
2025-06-11 08:10:34,841 - INFO - 第 5 页获取到 100 条记录
2025-06-11 08:10:35,341 - INFO - Request Parameters - Page 6:
2025-06-11 08:10:35,341 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:10:35,341 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:10:35,826 - INFO - API请求耗时: 484ms
2025-06-11 08:10:35,826 - INFO - Response - Page 6
2025-06-11 08:10:35,826 - INFO - 第 6 页获取到 100 条记录
2025-06-11 08:10:36,341 - INFO - Request Parameters - Page 7:
2025-06-11 08:10:36,341 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:10:36,341 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:10:36,904 - INFO - API请求耗时: 562ms
2025-06-11 08:10:36,904 - INFO - Response - Page 7
2025-06-11 08:10:36,904 - INFO - 第 7 页获取到 98 条记录
2025-06-11 08:10:36,904 - INFO - 查询完成，共获取到 698 条记录
2025-06-11 08:10:36,904 - INFO - 月度分段 4 查询成功，获取到 698 条记录
2025-06-11 08:10:37,904 - INFO - 查询月度分段 5: 2025-06-01 至 2025-06-30
2025-06-11 08:10:37,904 - INFO - 查询日期范围: 2025-06-01 至 2025-06-30，使用分页查询，每页 100 条记录
2025-06-11 08:10:37,904 - INFO - Request Parameters - Page 1:
2025-06-11 08:10:37,904 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:10:37,904 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:10:38,497 - INFO - API请求耗时: 594ms
2025-06-11 08:10:38,497 - INFO - Response - Page 1
2025-06-11 08:10:38,497 - INFO - 第 1 页获取到 100 条记录
2025-06-11 08:10:39,013 - INFO - Request Parameters - Page 2:
2025-06-11 08:10:39,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:10:39,013 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:10:39,560 - INFO - API请求耗时: 547ms
2025-06-11 08:10:39,560 - INFO - Response - Page 2
2025-06-11 08:10:39,560 - INFO - 第 2 页获取到 100 条记录
2025-06-11 08:10:40,075 - INFO - Request Parameters - Page 3:
2025-06-11 08:10:40,075 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-11 08:10:40,075 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-11 08:10:40,482 - INFO - API请求耗时: 406ms
2025-06-11 08:10:40,482 - INFO - Response - Page 3
2025-06-11 08:10:40,482 - INFO - 第 3 页获取到 11 条记录
2025-06-11 08:10:40,482 - INFO - 查询完成，共获取到 211 条记录
2025-06-11 08:10:40,482 - INFO - 月度分段 5 查询成功，获取到 211 条记录
2025-06-11 08:10:41,497 - INFO - 宜搭月度表单数据查询完成，共 5 个分段，成功获取 1403 条记录，失败 0 次
2025-06-11 08:10:41,497 - INFO - 成功获取宜搭月销售表单数据，共 1403 条记录
2025-06-11 08:10:41,497 - INFO - 宜搭月销售数据项结构示例: ['formInstanceId', 'formData']
2025-06-11 08:10:41,497 - INFO - 正在从MySQL获取月度汇总数据...
2025-06-11 08:10:41,544 - INFO - 成功获取MySQL月度汇总数据，共 1404 条记录
2025-06-11 08:10:42,232 - INFO - 月度汇总数据已导出到Excel文件: logs/数衍平台月度数据_20250611.xlsx
2025-06-11 08:10:42,310 - INFO - 成功创建宜搭月销售数据索引，共 1403 条记录
2025-06-11 08:10:42,325 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:42,325 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:42,794 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM4I
2025-06-11 08:10:42,794 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 240562.52, 'new_value': 270344.66}, {'field': 'dailyBillAmount', 'old_value': 240562.52, 'new_value': 270344.66}, {'field': 'amount', 'old_value': 179336.26, 'new_value': 202166.96}, {'field': 'count', 'old_value': 890, 'new_value': 988}, {'field': 'instoreAmount', 'old_value': 179336.26, 'new_value': 202166.96}, {'field': 'instoreCount', 'old_value': 890, 'new_value': 988}]
2025-06-11 08:10:42,794 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:43,310 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM5I
2025-06-11 08:10:43,310 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 176540.61, 'new_value': 190463.72}, {'field': 'dailyBillAmount', 'old_value': 176540.61, 'new_value': 190463.72}, {'field': 'amount', 'old_value': 271910.0, 'new_value': 293847.1}, {'field': 'count', 'old_value': 990, 'new_value': 1086}, {'field': 'instoreAmount', 'old_value': 270715.0, 'new_value': 292524.0}, {'field': 'instoreCount', 'old_value': 983, 'new_value': 1077}, {'field': 'onlineAmount', 'old_value': 1290.0, 'new_value': 1418.1}, {'field': 'onlineCount', 'old_value': 7, 'new_value': 9}]
2025-06-11 08:10:43,310 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:43,732 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM6I
2025-06-11 08:10:43,732 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 19349.1, 'new_value': 22986.4}, {'field': 'dailyBillAmount', 'old_value': 19349.1, 'new_value': 22986.4}, {'field': 'amount', 'old_value': 23848.4, 'new_value': 27722.0}, {'field': 'count', 'old_value': 106, 'new_value': 122}, {'field': 'instoreAmount', 'old_value': 9396.9, 'new_value': 10977.9}, {'field': 'instoreCount', 'old_value': 9, 'new_value': 11}, {'field': 'onlineAmount', 'old_value': 16422.9, 'new_value': 18715.5}, {'field': 'onlineCount', 'old_value': 97, 'new_value': 111}]
2025-06-11 08:10:43,732 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:44,200 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM7I
2025-06-11 08:10:44,200 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GPFAB4PUHT7OL7Q2OV4FVC7US001R67_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 40530.1, 'new_value': 42693.2}, {'field': 'amount', 'old_value': 40530.1, 'new_value': 42693.2}, {'field': 'count', 'old_value': 43, 'new_value': 47}, {'field': 'instoreAmount', 'old_value': 40530.1, 'new_value': 42693.2}, {'field': 'instoreCount', 'old_value': 43, 'new_value': 47}]
2025-06-11 08:10:44,200 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:44,685 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM9I
2025-06-11 08:10:44,685 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_3F059827C9E04DEAA6B50797867EC52B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 231241.68, 'new_value': 257446.39}, {'field': 'dailyBillAmount', 'old_value': 231241.68, 'new_value': 257446.39}, {'field': 'amount', 'old_value': 47009.45, 'new_value': 52120.45}, {'field': 'count', 'old_value': 241, 'new_value': 262}, {'field': 'instoreAmount', 'old_value': 47009.45, 'new_value': 52120.45}, {'field': 'instoreCount', 'old_value': 241, 'new_value': 262}]
2025-06-11 08:10:44,685 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:45,185 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM8I
2025-06-11 08:10:45,185 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 257114.69, 'new_value': 285953.31}, {'field': 'dailyBillAmount', 'old_value': 257114.69, 'new_value': 285953.31}, {'field': 'amount', 'old_value': 154949.77, 'new_value': 167989.22}, {'field': 'count', 'old_value': 1098, 'new_value': 1210}, {'field': 'instoreAmount', 'old_value': 139371.63, 'new_value': 150868.33}, {'field': 'instoreCount', 'old_value': 595, 'new_value': 645}, {'field': 'onlineAmount', 'old_value': 17849.45, 'new_value': 19521.45}, {'field': 'onlineCount', 'old_value': 503, 'new_value': 565}]
2025-06-11 08:10:45,200 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:45,669 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM1I
2025-06-11 08:10:45,669 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MO2IE70S367Q2OVAE57DLH001Q7I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 73808.65, 'new_value': 79576.53}, {'field': 'dailyBillAmount', 'old_value': 73808.65, 'new_value': 79576.53}, {'field': 'amount', 'old_value': 2169.7, 'new_value': 2338.4}, {'field': 'count', 'old_value': 27, 'new_value': 29}, {'field': 'onlineAmount', 'old_value': 2169.7, 'new_value': 2338.4}, {'field': 'onlineCount', 'old_value': 27, 'new_value': 29}]
2025-06-11 08:10:45,669 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:46,107 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM2I
2025-06-11 08:10:46,107 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 177823.2, 'new_value': 192592.98}, {'field': 'dailyBillAmount', 'old_value': 177823.2, 'new_value': 192592.98}, {'field': 'amount', 'old_value': 100051.6, 'new_value': 109133.5}, {'field': 'count', 'old_value': 917, 'new_value': 1019}, {'field': 'instoreAmount', 'old_value': 46681.5, 'new_value': 49584.9}, {'field': 'instoreCount', 'old_value': 367, 'new_value': 405}, {'field': 'onlineAmount', 'old_value': 53491.3, 'new_value': 59669.8}, {'field': 'onlineCount', 'old_value': 550, 'new_value': 614}]
2025-06-11 08:10:46,107 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:46,591 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM3I
2025-06-11 08:10:46,591 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MRVUM0P77G7Q2OV78BKOG4001PUK_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 109531.7, 'new_value': 117811.97}, {'field': 'dailyBillAmount', 'old_value': 109531.7, 'new_value': 117811.97}, {'field': 'amount', 'old_value': 111021.94, 'new_value': 119455.44}, {'field': 'count', 'old_value': 718, 'new_value': 778}, {'field': 'instoreAmount', 'old_value': 102327.13, 'new_value': 110031.13}, {'field': 'instoreCount', 'old_value': 598, 'new_value': 650}, {'field': 'onlineAmount', 'old_value': 8913.71, 'new_value': 9643.21}, {'field': 'onlineCount', 'old_value': 120, 'new_value': 128}]
2025-06-11 08:10:46,732 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:47,154 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMIJ
2025-06-11 08:10:47,154 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVOQTVVFR41OA22BBK6R0G53G001SV2_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 30138.1, 'new_value': 33686.2}, {'field': 'amount', 'old_value': 30138.1, 'new_value': 33686.2}, {'field': 'count', 'old_value': 10, 'new_value': 12}, {'field': 'instoreAmount', 'old_value': 30138.1, 'new_value': 33686.2}, {'field': 'instoreCount', 'old_value': 10, 'new_value': 12}]
2025-06-11 08:10:47,154 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:47,669 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMUJ
2025-06-11 08:10:47,669 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_BF554F536BF14762AEB7110E7BD583B7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 179943.69, 'new_value': 209547.69}, {'field': 'dailyBillAmount', 'old_value': 179943.69, 'new_value': 209547.69}, {'field': 'amount', 'old_value': 306487.66, 'new_value': 338059.8}, {'field': 'count', 'old_value': 409, 'new_value': 447}, {'field': 'instoreAmount', 'old_value': 306487.66, 'new_value': 338059.8}, {'field': 'instoreCount', 'old_value': 409, 'new_value': 447}]
2025-06-11 08:10:47,669 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:48,091 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMTJ
2025-06-11 08:10:48,091 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AC07B70DB49845A8A52846E099EBC515_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 132015.24, 'new_value': 143801.84}, {'field': 'dailyBillAmount', 'old_value': 132015.24, 'new_value': 143801.84}, {'field': 'amount', 'old_value': 143991.24, 'new_value': 155777.84}, {'field': 'count', 'old_value': 452, 'new_value': 495}, {'field': 'instoreAmount', 'old_value': 143991.24, 'new_value': 155777.84}, {'field': 'instoreCount', 'old_value': 452, 'new_value': 495}]
2025-06-11 08:10:48,107 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:48,544 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMSJ
2025-06-11 08:10:48,544 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AA76628FACEC4C13BD44C8280B45416D_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 9802.23, 'new_value': 11050.93}, {'field': 'dailyBillAmount', 'old_value': 9802.23, 'new_value': 11050.93}, {'field': 'amount', 'old_value': 9802.23, 'new_value': 11050.93}, {'field': 'count', 'old_value': 14, 'new_value': 16}, {'field': 'instoreAmount', 'old_value': 9802.23, 'new_value': 11776.56}, {'field': 'instoreCount', 'old_value': 14, 'new_value': 16}]
2025-06-11 08:10:48,544 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:49,060 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMRJ
2025-06-11 08:10:49,060 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_A93C60005A8F41B092F6C5A8C21577CB_2025-06, 变更字段: [{'field': 'amount', 'old_value': 27323.41, 'new_value': 27665.61}, {'field': 'count', 'old_value': 182, 'new_value': 188}, {'field': 'instoreAmount', 'old_value': 27323.41, 'new_value': 27665.61}, {'field': 'instoreCount', 'old_value': 182, 'new_value': 188}]
2025-06-11 08:10:49,075 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:49,482 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMQJ
2025-06-11 08:10:49,482 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78C3CF1DDB8443E8A4A4D425F7CA9756_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 67823.44, 'new_value': 75703.2}, {'field': 'dailyBillAmount', 'old_value': 61621.44, 'new_value': 69501.2}, {'field': 'amount', 'old_value': 67823.44, 'new_value': 75703.2}, {'field': 'count', 'old_value': 342, 'new_value': 385}, {'field': 'instoreAmount', 'old_value': 67823.44, 'new_value': 75703.2}, {'field': 'instoreCount', 'old_value': 342, 'new_value': 385}]
2025-06-11 08:10:49,482 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:49,904 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMPJ
2025-06-11 08:10:49,904 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78655ECA4A32471AB7842F8DE2018120_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 87816.0, 'new_value': 117112.0}, {'field': 'amount', 'old_value': 87816.0, 'new_value': 117112.0}, {'field': 'count', 'old_value': 19, 'new_value': 23}, {'field': 'instoreAmount', 'old_value': 87816.0, 'new_value': 117112.0}, {'field': 'instoreCount', 'old_value': 19, 'new_value': 23}]
2025-06-11 08:10:49,904 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:50,419 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMOJ
2025-06-11 08:10:50,419 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6B7571A27AF84C73B4FC04CCBDB83D9B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 13508.53, 'new_value': 15128.73}, {'field': 'amount', 'old_value': 13508.53, 'new_value': 15128.73}, {'field': 'count', 'old_value': 129, 'new_value': 139}, {'field': 'instoreAmount', 'old_value': 13508.53, 'new_value': 15128.73}, {'field': 'instoreCount', 'old_value': 129, 'new_value': 139}]
2025-06-11 08:10:50,419 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:50,841 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMNJ
2025-06-11 08:10:50,841 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6692283A183D432BAE322E1032539CE8_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 108509.9, 'new_value': 124081.9}, {'field': 'dailyBillAmount', 'old_value': 69312.55, 'new_value': 81736.45}, {'field': 'amount', 'old_value': 108509.9, 'new_value': 124081.9}, {'field': 'count', 'old_value': 181, 'new_value': 201}, {'field': 'instoreAmount', 'old_value': 108509.9, 'new_value': 124081.9}, {'field': 'instoreCount', 'old_value': 181, 'new_value': 201}]
2025-06-11 08:10:50,841 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:51,325 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMMJ
2025-06-11 08:10:51,325 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_3BB9A16AE8544997965802FAA3B83381_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 14017.16, 'new_value': 15255.35}, {'field': 'dailyBillAmount', 'old_value': 14017.16, 'new_value': 15255.35}, {'field': 'amount', 'old_value': 15749.25, 'new_value': 17193.14}, {'field': 'count', 'old_value': 441, 'new_value': 487}, {'field': 'instoreAmount', 'old_value': 15749.25, 'new_value': 17193.14}, {'field': 'instoreCount', 'old_value': 441, 'new_value': 487}]
2025-06-11 08:10:51,325 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:51,825 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMLJ
2025-06-11 08:10:51,825 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1IBBJ3RNCSAVNO7A70STAEF09Q001IPV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 72929.58, 'new_value': 78756.58}, {'field': 'dailyBillAmount', 'old_value': 63381.58, 'new_value': 69208.58}, {'field': 'amount', 'old_value': 72929.58, 'new_value': 78756.58}, {'field': 'count', 'old_value': 303, 'new_value': 331}, {'field': 'instoreAmount', 'old_value': 72929.58, 'new_value': 78756.58}, {'field': 'instoreCount', 'old_value': 303, 'new_value': 331}]
2025-06-11 08:10:51,825 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:52,341 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMKJ
2025-06-11 08:10:52,341 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1I6E0VAU3IFJEQ22MH147FMU0M0013E8_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 16793.11}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 16793.11}, {'field': 'amount', 'old_value': 1124.0, 'new_value': 1760.0}, {'field': 'count', 'old_value': 18, 'new_value': 20}, {'field': 'instoreAmount', 'old_value': 1124.0, 'new_value': 1760.9}, {'field': 'instoreCount', 'old_value': 18, 'new_value': 20}]
2025-06-11 08:10:52,341 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:52,810 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMJJ
2025-06-11 08:10:52,810 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVORJ88U7D2IL1AIB692RTFU8001185_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 50870.54, 'new_value': 60140.46}, {'field': 'dailyBillAmount', 'old_value': 50870.54, 'new_value': 60140.46}, {'field': 'amount', 'old_value': 25321.94, 'new_value': 30381.58}, {'field': 'count', 'old_value': 284, 'new_value': 331}, {'field': 'instoreAmount', 'old_value': 24241.76, 'new_value': 29205.3}, {'field': 'instoreCount', 'old_value': 237, 'new_value': 278}, {'field': 'onlineAmount', 'old_value': 1614.39, 'new_value': 1810.49}, {'field': 'onlineCount', 'old_value': 47, 'new_value': 53}]
2025-06-11 08:10:52,810 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:53,216 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMHJ
2025-06-11 08:10:53,232 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HV6P9SGUVGG9S36QDA69ST70J0015SA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 74146.71, 'new_value': 78223.96}, {'field': 'dailyBillAmount', 'old_value': 74146.71, 'new_value': 78223.96}, {'field': 'amount', 'old_value': 89848.6, 'new_value': 94018.6}, {'field': 'count', 'old_value': 493, 'new_value': 521}, {'field': 'instoreAmount', 'old_value': 89848.6, 'new_value': 94018.6}, {'field': 'instoreCount', 'old_value': 493, 'new_value': 521}]
2025-06-11 08:10:53,232 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:53,825 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMWI
2025-06-11 08:10:53,825 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDRLHCKFK97Q2OV4FVC7BS00148D_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 117797.48, 'new_value': 129799.31}, {'field': 'dailyBillAmount', 'old_value': 117797.48, 'new_value': 129799.31}, {'field': 'amount', 'old_value': 65714.56, 'new_value': 71219.54}, {'field': 'count', 'old_value': 263, 'new_value': 287}, {'field': 'instoreAmount', 'old_value': 66729.2, 'new_value': 72401.2}, {'field': 'instoreCount', 'old_value': 263, 'new_value': 287}]
2025-06-11 08:10:53,825 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:54,419 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMGJ
2025-06-11 08:10:54,419 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 128865.3, 'new_value': 140753.77}, {'field': 'dailyBillAmount', 'old_value': 128865.3, 'new_value': 140753.77}, {'field': 'amount', 'old_value': 58466.74, 'new_value': 64721.23}, {'field': 'count', 'old_value': 598, 'new_value': 674}, {'field': 'instoreAmount', 'old_value': 28730.89, 'new_value': 31392.08}, {'field': 'instoreCount', 'old_value': 215, 'new_value': 238}, {'field': 'onlineAmount', 'old_value': 29735.85, 'new_value': 33329.15}, {'field': 'onlineCount', 'old_value': 383, 'new_value': 436}]
2025-06-11 08:10:54,419 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:54,857 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMFJ
2025-06-11 08:10:54,857 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HMVHOKFUN7GGQ26TEPQN7CSO4001IA6_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 40887.21, 'new_value': 45532.82}, {'field': 'dailyBillAmount', 'old_value': 40887.21, 'new_value': 45532.82}, {'field': 'amount', 'old_value': 34567.98, 'new_value': 38417.98}, {'field': 'count', 'old_value': 143, 'new_value': 161}, {'field': 'instoreAmount', 'old_value': 34413.3, 'new_value': 38263.3}, {'field': 'instoreCount', 'old_value': 140, 'new_value': 158}]
2025-06-11 08:10:54,857 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:55,279 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMEJ
2025-06-11 08:10:55,279 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HINIR4GO8E5NM5U25UDHUFEGO001L3K_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 23265.02, 'new_value': 26605.38}, {'field': 'amount', 'old_value': 23265.02, 'new_value': 26605.38}, {'field': 'count', 'old_value': 1064, 'new_value': 1236}, {'field': 'instoreAmount', 'old_value': 24622.03, 'new_value': 28147.9}, {'field': 'instoreCount', 'old_value': 1064, 'new_value': 1236}]
2025-06-11 08:10:55,279 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:55,732 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMAI
2025-06-11 08:10:55,732 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINPMIJNMLUJ7Q2OV392410G00148C_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 29684.86, 'new_value': 42248.41}, {'field': 'dailyBillAmount', 'old_value': 29684.86, 'new_value': 42248.41}]
2025-06-11 08:10:55,732 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:56,216 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMBI
2025-06-11 08:10:56,216 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_2025-06, 变更字段: [{'field': 'amount', 'old_value': 43624.0, 'new_value': 57076.0}, {'field': 'count', 'old_value': 65, 'new_value': 76}, {'field': 'instoreAmount', 'old_value': 43624.0, 'new_value': 57076.0}, {'field': 'instoreCount', 'old_value': 65, 'new_value': 76}]
2025-06-11 08:10:56,216 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:56,685 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMCI
2025-06-11 08:10:56,685 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 395693.07, 'new_value': 432734.23}, {'field': 'dailyBillAmount', 'old_value': 395693.07, 'new_value': 432734.23}, {'field': 'amount', 'old_value': -169167.94, 'new_value': -194589.06}, {'field': 'count', 'old_value': 374, 'new_value': 410}, {'field': 'instoreAmount', 'old_value': 221516.4, 'new_value': 234166.4}, {'field': 'instoreCount', 'old_value': 374, 'new_value': 410}]
2025-06-11 08:10:56,685 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:57,185 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMDI
2025-06-11 08:10:57,185 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 133717.0, 'new_value': 145493.0}, {'field': 'amount', 'old_value': 133717.0, 'new_value': 145493.0}, {'field': 'count', 'old_value': 502, 'new_value': 544}, {'field': 'instoreAmount', 'old_value': 133717.0, 'new_value': 145493.0}, {'field': 'instoreCount', 'old_value': 502, 'new_value': 544}]
2025-06-11 08:10:57,185 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:57,669 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMEI
2025-06-11 08:10:57,669 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6QJG5GQO7Q2OVBN4IS5U001D20_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 139952.03, 'new_value': 149916.02}, {'field': 'dailyBillAmount', 'old_value': 128067.63, 'new_value': 137750.42}, {'field': 'amount', 'old_value': 139952.03, 'new_value': 149916.02}, {'field': 'count', 'old_value': 442, 'new_value': 474}, {'field': 'instoreAmount', 'old_value': 139952.03, 'new_value': 149916.02}, {'field': 'instoreCount', 'old_value': 442, 'new_value': 474}]
2025-06-11 08:10:57,669 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:58,107 - INFO - 更新表单数据成功: FINST-AI866781XLYVAQQR8XBGXCAD2PZM3ON8IRFBMG1
2025-06-11 08:10:58,107 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_2025-06, 变更字段: [{'field': 'amount', 'old_value': 3688.7, 'new_value': 5691.2}, {'field': 'count', 'old_value': 15, 'new_value': 19}, {'field': 'instoreAmount', 'old_value': 4011.3, 'new_value': 6013.83}, {'field': 'instoreCount', 'old_value': 15, 'new_value': 19}]
2025-06-11 08:10:58,107 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:58,544 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMVJ
2025-06-11 08:10:58,544 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 40949.0, 'new_value': 46348.7}, {'field': 'dailyBillAmount', 'old_value': 40949.0, 'new_value': 46348.7}, {'field': 'amount', 'old_value': 11814.3, 'new_value': 14071.1}, {'field': 'count', 'old_value': 54, 'new_value': 65}, {'field': 'instoreAmount', 'old_value': 11814.3, 'new_value': 14071.1}, {'field': 'instoreCount', 'old_value': 54, 'new_value': 65}]
2025-06-11 08:10:58,544 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:59,028 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMGI
2025-06-11 08:10:59,028 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTA575PTKJ7Q2OVBN4IS72001D34_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 51208.9, 'new_value': 57610.15}, {'field': 'amount', 'old_value': 51208.9, 'new_value': 57610.15}, {'field': 'count', 'old_value': 1674, 'new_value': 1887}, {'field': 'instoreAmount', 'old_value': 45494.04, 'new_value': 51401.38}, {'field': 'instoreCount', 'old_value': 1534, 'new_value': 1731}, {'field': 'onlineAmount', 'old_value': 5714.86, 'new_value': 6208.77}, {'field': 'onlineCount', 'old_value': 140, 'new_value': 156}]
2025-06-11 08:10:59,028 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:10:59,560 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMHI
2025-06-11 08:10:59,560 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTAKS3N4EI7Q2OVBN4IS76001D38_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 93596.83, 'new_value': 102769.83}, {'field': 'dailyBillAmount', 'old_value': 93555.0, 'new_value': 102728.0}, {'field': 'amount', 'old_value': 77243.19, 'new_value': 81752.19}, {'field': 'count', 'old_value': 81, 'new_value': 90}, {'field': 'instoreAmount', 'old_value': 76427.0, 'new_value': 80936.0}, {'field': 'instoreCount', 'old_value': 76, 'new_value': 85}]
2025-06-11 08:10:59,560 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:00,216 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMII
2025-06-11 08:11:00,216 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTBJPIR4ME7Q2OVBN4IS7E001D3G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 154713.47, 'new_value': 169821.69}, {'field': 'dailyBillAmount', 'old_value': 154713.47, 'new_value': 169821.69}, {'field': 'amount', 'old_value': 154713.47, 'new_value': 169821.69}, {'field': 'count', 'old_value': 151, 'new_value': 168}, {'field': 'instoreAmount', 'old_value': 154713.47, 'new_value': 169821.69}, {'field': 'instoreCount', 'old_value': 151, 'new_value': 168}]
2025-06-11 08:11:00,216 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:00,700 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMJI
2025-06-11 08:11:00,700 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_2025-06, 变更字段: [{'field': 'amount', 'old_value': 13128.0, 'new_value': 15685.0}, {'field': 'count', 'old_value': 16, 'new_value': 19}, {'field': 'instoreAmount', 'old_value': 13128.0, 'new_value': 15685.0}, {'field': 'instoreCount', 'old_value': 16, 'new_value': 19}]
2025-06-11 08:11:00,700 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:01,169 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMKI
2025-06-11 08:11:01,169 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCIR7D5JD7Q2OVBN4IS7U001D40_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 30475.9, 'new_value': 35156.5}, {'field': 'dailyBillAmount', 'old_value': 30475.9, 'new_value': 35156.5}, {'field': 'amount', 'old_value': 34814.6, 'new_value': 39883.6}, {'field': 'count', 'old_value': 102, 'new_value': 118}, {'field': 'instoreAmount', 'old_value': 34814.6, 'new_value': 39883.6}, {'field': 'instoreCount', 'old_value': 102, 'new_value': 118}]
2025-06-11 08:11:01,169 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:01,778 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMDJ
2025-06-11 08:11:01,778 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HB8D7KAC5K1G3723F7K257LIN001TSQ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 10202.0, 'new_value': 10828.0}, {'field': 'dailyBillAmount', 'old_value': 10202.0, 'new_value': 10828.0}, {'field': 'amount', 'old_value': 10202.0, 'new_value': 10828.0}, {'field': 'count', 'old_value': 29, 'new_value': 32}, {'field': 'instoreAmount', 'old_value': 10589.0, 'new_value': 11215.0}, {'field': 'instoreCount', 'old_value': 29, 'new_value': 32}]
2025-06-11 08:11:01,778 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:02,232 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMMI
2025-06-11 08:11:02,247 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 75940.22, 'new_value': 81404.23}, {'field': 'dailyBillAmount', 'old_value': 75940.22, 'new_value': 81404.23}, {'field': 'amount', 'old_value': 99517.7, 'new_value': 105736.7}, {'field': 'count', 'old_value': 518, 'new_value': 560}, {'field': 'instoreAmount', 'old_value': 99615.7, 'new_value': 106192.7}, {'field': 'instoreCount', 'old_value': 518, 'new_value': 560}]
2025-06-11 08:11:02,247 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:02,700 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMNI
2025-06-11 08:11:02,700 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTE3QTM66G7Q2OVBN4IS8M001D4O_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 59061.35, 'new_value': 67545.35}, {'field': 'dailyBillAmount', 'old_value': 59061.35, 'new_value': 67545.35}, {'field': 'amount', 'old_value': 7157.07, 'new_value': 7774.99}, {'field': 'count', 'old_value': 535, 'new_value': 591}, {'field': 'instoreAmount', 'old_value': 8264.97, 'new_value': 8989.19}, {'field': 'instoreCount', 'old_value': 535, 'new_value': 591}]
2025-06-11 08:11:02,700 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:03,185 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMOI
2025-06-11 08:11:03,185 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 22631.72, 'new_value': 55953.21}, {'field': 'amount', 'old_value': 22631.72, 'new_value': 55952.81}, {'field': 'count', 'old_value': 536, 'new_value': 1561}, {'field': 'instoreAmount', 'old_value': 21570.46, 'new_value': 53485.65}, {'field': 'instoreCount', 'old_value': 498, 'new_value': 1480}, {'field': 'onlineAmount', 'old_value': 1641.62, 'new_value': 3583.22}, {'field': 'onlineCount', 'old_value': 38, 'new_value': 81}]
2025-06-11 08:11:03,185 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:03,638 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMPI
2025-06-11 08:11:03,638 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GMFA53VGVIAPH7Q2OV4FVC7GS0017K2_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 92999.7, 'new_value': 102604.66}, {'field': 'dailyBillAmount', 'old_value': 92999.7, 'new_value': 102604.66}, {'field': 'amount', 'old_value': 93267.97, 'new_value': 102872.93}, {'field': 'count', 'old_value': 250, 'new_value': 277}, {'field': 'instoreAmount', 'old_value': 93267.97, 'new_value': 102872.93}, {'field': 'instoreCount', 'old_value': 250, 'new_value': 277}]
2025-06-11 08:11:03,638 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:04,060 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMQI
2025-06-11 08:11:04,060 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDJ5HVP5F47Q2OV4FVC77O001449_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 93570.18, 'new_value': 98904.25}, {'field': 'dailyBillAmount', 'old_value': 93570.18, 'new_value': 98904.25}, {'field': 'amount', 'old_value': 28703.7, 'new_value': 28892.7}, {'field': 'count', 'old_value': 70, 'new_value': 71}, {'field': 'instoreAmount', 'old_value': 28703.7, 'new_value': 28892.7}, {'field': 'instoreCount', 'old_value': 70, 'new_value': 71}]
2025-06-11 08:11:04,060 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:04,466 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMRI
2025-06-11 08:11:04,466 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDKDOANT3H7Q2OV4FVC78800144P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 127189.2, 'new_value': 141085.18}, {'field': 'dailyBillAmount', 'old_value': 127189.2, 'new_value': 141085.18}, {'field': 'amount', 'old_value': 50623.7, 'new_value': 55003.4}, {'field': 'count', 'old_value': 197, 'new_value': 219}, {'field': 'instoreAmount', 'old_value': 50623.7, 'new_value': 55003.4}, {'field': 'instoreCount', 'old_value': 197, 'new_value': 219}]
2025-06-11 08:11:04,482 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:04,919 - INFO - 更新表单数据成功: FINST-AI866781XLYVAQQR8XBGXCAD2PZM3ON8IRFBMH1
2025-06-11 08:11:04,919 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDL7K17VIP7Q2OV4FVC78K001455_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6207.0, 'new_value': 6558.0}, {'field': 'amount', 'old_value': 6207.0, 'new_value': 6558.0}, {'field': 'count', 'old_value': 8, 'new_value': 9}, {'field': 'instoreAmount', 'old_value': 6207.0, 'new_value': 6558.0}, {'field': 'instoreCount', 'old_value': 8, 'new_value': 9}]
2025-06-11 08:11:04,919 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:05,341 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMSI
2025-06-11 08:11:05,341 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 34557.26, 'new_value': 38992.83}, {'field': 'dailyBillAmount', 'old_value': 34557.26, 'new_value': 38992.83}, {'field': 'amount', 'old_value': 7116.37, 'new_value': 8092.27}, {'field': 'count', 'old_value': 268, 'new_value': 303}, {'field': 'instoreAmount', 'old_value': 1980.4, 'new_value': 2397.0}, {'field': 'instoreCount', 'old_value': 50, 'new_value': 60}, {'field': 'onlineAmount', 'old_value': 5228.59, 'new_value': 5803.39}, {'field': 'onlineCount', 'old_value': 218, 'new_value': 243}]
2025-06-11 08:11:05,341 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:05,810 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMTI
2025-06-11 08:11:05,810 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDN6L4LMS87Q2OV4FVC79K001465_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 56330.74, 'new_value': 63425.56}, {'field': 'dailyBillAmount', 'old_value': 56330.74, 'new_value': 63425.56}, {'field': 'amount', 'old_value': 10774.88, 'new_value': 12236.06}, {'field': 'count', 'old_value': 267, 'new_value': 309}, {'field': 'instoreAmount', 'old_value': 8650.08, 'new_value': 9836.43}, {'field': 'instoreCount', 'old_value': 214, 'new_value': 250}, {'field': 'onlineAmount', 'old_value': 2148.95, 'new_value': 2423.78}, {'field': 'onlineCount', 'old_value': 53, 'new_value': 59}]
2025-06-11 08:11:05,810 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:06,294 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMUI
2025-06-11 08:11:06,294 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDOE3O1J9R7Q2OV4FVC7A800146P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 12564.69, 'new_value': 12580.69}, {'field': 'dailyBillAmount', 'old_value': 12564.69, 'new_value': 12580.69}, {'field': 'amount', 'old_value': 9811.6, 'new_value': 9846.6}, {'field': 'count', 'old_value': 348, 'new_value': 362}, {'field': 'instoreAmount', 'old_value': 9911.6, 'new_value': 9946.6}, {'field': 'instoreCount', 'old_value': 348, 'new_value': 362}]
2025-06-11 08:11:06,294 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:06,825 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMVI
2025-06-11 08:11:06,825 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 17862.09, 'new_value': 19485.98}, {'field': 'dailyBillAmount', 'old_value': 17862.09, 'new_value': 19485.98}, {'field': 'amount', 'old_value': 10176.51, 'new_value': 11217.21}, {'field': 'count', 'old_value': 552, 'new_value': 601}, {'field': 'instoreAmount', 'old_value': 3163.77, 'new_value': 3709.87}, {'field': 'instoreCount', 'old_value': 134, 'new_value': 150}, {'field': 'onlineAmount', 'old_value': 7287.24, 'new_value': 7781.84}, {'field': 'onlineCount', 'old_value': 418, 'new_value': 451}]
2025-06-11 08:11:06,841 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:07,263 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMCJ
2025-06-11 08:11:07,263 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9UCRQKEOIF52ASKKUBQUNH0018FA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 62583.1, 'new_value': 67161.0}, {'field': 'dailyBillAmount', 'old_value': 62583.1, 'new_value': 67161.0}, {'field': 'amount', 'old_value': 74082.1, 'new_value': 80150.0}, {'field': 'count', 'old_value': 274, 'new_value': 297}, {'field': 'instoreAmount', 'old_value': 74990.1, 'new_value': 81376.0}, {'field': 'instoreCount', 'old_value': 274, 'new_value': 297}]
2025-06-11 08:11:07,263 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:07,669 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMBJ
2025-06-11 08:11:07,669 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9S64J8E8R652ASKKUBQUMU0018EN_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 35892.58, 'new_value': 38076.92}, {'field': 'dailyBillAmount', 'old_value': 35892.58, 'new_value': 38076.92}, {'field': 'amount', 'old_value': 37045.0, 'new_value': 39288.88}, {'field': 'count', 'old_value': 184, 'new_value': 194}, {'field': 'instoreAmount', 'old_value': 35037.32, 'new_value': 37232.52}, {'field': 'instoreCount', 'old_value': 162, 'new_value': 171}, {'field': 'onlineAmount', 'old_value': 2007.68, 'new_value': 2056.36}, {'field': 'onlineCount', 'old_value': 22, 'new_value': 23}]
2025-06-11 08:11:07,669 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:08,185 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMAJ
2025-06-11 08:11:08,185 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9PGR3703D752ASKKUBQUM50018DU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 54523.09, 'new_value': 61770.49}, {'field': 'dailyBillAmount', 'old_value': 53525.77, 'new_value': 60598.1}, {'field': 'amount', 'old_value': 54523.09, 'new_value': 61770.49}, {'field': 'count', 'old_value': 706, 'new_value': 797}, {'field': 'instoreAmount', 'old_value': 52691.62, 'new_value': 59387.12}, {'field': 'instoreCount', 'old_value': 680, 'new_value': 763}, {'field': 'onlineAmount', 'old_value': 1885.47, 'new_value': 2437.37}, {'field': 'onlineCount', 'old_value': 26, 'new_value': 34}]
2025-06-11 08:11:08,185 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:08,669 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMLI
2025-06-11 08:11:08,669 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTD2B070E67Q2OVBN4IS86001D48_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 26356.94, 'new_value': 30170.94}, {'field': 'amount', 'old_value': 26356.94, 'new_value': 30170.94}, {'field': 'count', 'old_value': 42, 'new_value': 49}, {'field': 'instoreAmount', 'old_value': 26356.94, 'new_value': 30170.94}, {'field': 'instoreCount', 'old_value': 42, 'new_value': 49}]
2025-06-11 08:11:08,669 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:09,091 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM9J
2025-06-11 08:11:09,091 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE2TORA7KI7Q2OV4FVC7FC0014BT_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 52570.85, 'new_value': 57620.45}, {'field': 'dailyBillAmount', 'old_value': 52570.85, 'new_value': 57620.45}, {'field': 'amount', 'old_value': 31832.57, 'new_value': 35099.01}, {'field': 'count', 'old_value': 801, 'new_value': 872}, {'field': 'instoreAmount', 'old_value': 27788.9, 'new_value': 30218.19}, {'field': 'instoreCount', 'old_value': 682, 'new_value': 736}, {'field': 'onlineAmount', 'old_value': 4917.32, 'new_value': 5754.82}, {'field': 'onlineCount', 'old_value': 119, 'new_value': 136}]
2025-06-11 08:11:09,091 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:09,607 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM8J
2025-06-11 08:11:09,607 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE29HIJ7QK7Q2OV4FVC7F40014BL_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 30019.5, 'new_value': 34056.1}, {'field': 'amount', 'old_value': 30019.5, 'new_value': 34056.1}, {'field': 'count', 'old_value': 92, 'new_value': 106}, {'field': 'instoreAmount', 'old_value': 31066.1, 'new_value': 36999.5}, {'field': 'instoreCount', 'old_value': 92, 'new_value': 106}]
2025-06-11 08:11:09,607 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:10,122 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM7J
2025-06-11 08:11:10,122 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE1H27HPQN7Q2OV4FVC7EO0014B9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 20660.0, 'new_value': 23246.0}, {'field': 'dailyBillAmount', 'old_value': 20660.0, 'new_value': 23246.0}, {'field': 'amount', 'old_value': 22564.0, 'new_value': 25177.0}, {'field': 'count', 'old_value': 36, 'new_value': 45}, {'field': 'instoreAmount', 'old_value': 23132.0, 'new_value': 25962.0}, {'field': 'instoreCount', 'old_value': 36, 'new_value': 45}]
2025-06-11 08:11:10,138 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:10,528 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM6J
2025-06-11 08:11:10,528 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE0PKSKM647Q2OV4FVC7EC0014AT_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 33138.64, 'new_value': 36735.54}, {'field': 'dailyBillAmount', 'old_value': 33138.64, 'new_value': 36735.54}, {'field': 'amount', 'old_value': 30706.64, 'new_value': 34303.54}, {'field': 'count', 'old_value': 117, 'new_value': 132}, {'field': 'instoreAmount', 'old_value': 30945.64, 'new_value': 34542.54}, {'field': 'instoreCount', 'old_value': 117, 'new_value': 132}]
2025-06-11 08:11:10,528 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:10,997 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM5J
2025-06-11 08:11:10,997 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVNVP6LRA7Q2OV4FVC7DS0014AD_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 103741.5, 'new_value': 110001.5}, {'field': 'dailyBillAmount', 'old_value': 103741.5, 'new_value': 110001.5}, {'field': 'amount', 'old_value': 42834.0, 'new_value': 44761.0}, {'field': 'count', 'old_value': 120, 'new_value': 129}, {'field': 'instoreAmount', 'old_value': 42834.0, 'new_value': 44761.0}, {'field': 'instoreCount', 'old_value': 120, 'new_value': 129}]
2025-06-11 08:11:10,997 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:11,403 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM4J
2025-06-11 08:11:11,403 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVG061A0H7Q2OV4FVC7DO0014A9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 30489.3, 'new_value': 32823.3}, {'field': 'amount', 'old_value': 30489.3, 'new_value': 32823.3}, {'field': 'count', 'old_value': 710, 'new_value': 776}, {'field': 'instoreAmount', 'old_value': 30905.6, 'new_value': 33259.6}, {'field': 'instoreCount', 'old_value': 710, 'new_value': 776}]
2025-06-11 08:11:11,403 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:11,935 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM3J
2025-06-11 08:11:11,935 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV84M589R7Q2OV4FVC7DK0014A5_2025-06, 变更字段: [{'field': 'amount', 'old_value': 18245.02, 'new_value': 20436.81}, {'field': 'count', 'old_value': 234, 'new_value': 251}, {'field': 'instoreAmount', 'old_value': 18245.02, 'new_value': 20436.81}, {'field': 'instoreCount', 'old_value': 234, 'new_value': 251}]
2025-06-11 08:11:11,935 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:12,388 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM2J
2025-06-11 08:11:12,388 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV0D9P6J27Q2OV4FVC7DG0014A1_2025-06, 变更字段: [{'field': 'amount', 'old_value': 13064.78, 'new_value': 14124.21}, {'field': 'count', 'old_value': 524, 'new_value': 572}, {'field': 'onlineAmount', 'old_value': 7588.09, 'new_value': 8694.92}, {'field': 'onlineCount', 'old_value': 341, 'new_value': 389}]
2025-06-11 08:11:12,403 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:12,857 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM1J
2025-06-11 08:11:12,857 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 31339.14, 'new_value': 33554.99}, {'field': 'dailyBillAmount', 'old_value': 31339.14, 'new_value': 33554.99}, {'field': 'amount', 'old_value': 32274.66, 'new_value': 34611.13}, {'field': 'count', 'old_value': 1809, 'new_value': 1943}, {'field': 'instoreAmount', 'old_value': 17787.46, 'new_value': 19115.36}, {'field': 'instoreCount', 'old_value': 935, 'new_value': 1006}, {'field': 'onlineAmount', 'old_value': 15122.61, 'new_value': 16148.73}, {'field': 'onlineCount', 'old_value': 874, 'new_value': 937}]
2025-06-11 08:11:12,857 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:13,325 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM0J
2025-06-11 08:11:13,325 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDT8J32K6E7Q2OV4FVC7CK001495_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 19737.0, 'new_value': 22742.0}, {'field': 'dailyBillAmount', 'old_value': 19737.0, 'new_value': 22742.0}, {'field': 'amount', 'old_value': 19702.0, 'new_value': 22707.0}, {'field': 'count', 'old_value': 383, 'new_value': 440}, {'field': 'instoreAmount', 'old_value': 19702.0, 'new_value': 22707.0}, {'field': 'instoreCount', 'old_value': 383, 'new_value': 440}]
2025-06-11 08:11:13,325 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:13,747 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMZI
2025-06-11 08:11:13,747 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSTUTUPTG7Q2OV4FVC7CG001491_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 104075.4, 'new_value': 110910.3}, {'field': 'dailyBillAmount', 'old_value': 104075.4, 'new_value': 110910.3}, {'field': 'amount', 'old_value': 75994.45, 'new_value': 79104.75}, {'field': 'count', 'old_value': 200, 'new_value': 209}, {'field': 'instoreAmount', 'old_value': 76223.4, 'new_value': 79333.7}, {'field': 'instoreCount', 'old_value': 200, 'new_value': 209}]
2025-06-11 08:11:13,763 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:14,247 - INFO - 更新表单数据成功: FINST-90D66XA12PZVBSKCD49BM87RNO2X30KE17HBMG6
2025-06-11 08:11:14,247 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HHDRIR0P38R666U1G19QP11V00018CE_2025-06, 变更字段: [{'field': 'amount', 'old_value': 2100.2, 'new_value': 2891.32}, {'field': 'count', 'old_value': 5, 'new_value': 6}, {'field': 'instoreAmount', 'old_value': 2100.2, 'new_value': 2891.32}, {'field': 'instoreCount', 'old_value': 5, 'new_value': 6}]
2025-06-11 08:11:14,247 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:14,700 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMXI
2025-06-11 08:11:14,700 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 65292.03, 'new_value': 71631.51}, {'field': 'dailyBillAmount', 'old_value': 65292.03, 'new_value': 71631.51}, {'field': 'amount', 'old_value': 33519.48, 'new_value': 36415.56}, {'field': 'count', 'old_value': 1507, 'new_value': 1626}, {'field': 'instoreAmount', 'old_value': 34588.23, 'new_value': 37542.31}, {'field': 'instoreCount', 'old_value': 1507, 'new_value': 1626}]
2025-06-11 08:11:14,700 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:15,185 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMYI
2025-06-11 08:11:15,185 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSM3EAQJB7Q2OV4FVC7CC00148T_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 194225.9, 'new_value': 209283.1}, {'field': 'dailyBillAmount', 'old_value': 194225.9, 'new_value': 209283.1}, {'field': 'amount', 'old_value': 194225.9, 'new_value': 209283.1}, {'field': 'count', 'old_value': 238, 'new_value': 262}, {'field': 'instoreAmount', 'old_value': 194225.9, 'new_value': 209283.1}, {'field': 'instoreCount', 'old_value': 238, 'new_value': 262}]
2025-06-11 08:11:15,325 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-11 08:11:15,794 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMJ71
2025-06-11 08:11:15,794 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 392845.89, 'new_value': 393345.39}, {'field': 'amount', 'old_value': 390267.94, 'new_value': 390766.64}, {'field': 'count', 'old_value': 1126, 'new_value': 1127}, {'field': 'instoreAmount', 'old_value': 394311.84, 'new_value': 394811.34}, {'field': 'instoreCount', 'old_value': 1126, 'new_value': 1127}]
2025-06-11 08:11:15,794 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:16,278 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMOK
2025-06-11 08:11:16,278 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDCE3748SO0I86N3H2U1FP001EOV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 26165.0, 'new_value': 29122.0}, {'field': 'amount', 'old_value': 26165.0, 'new_value': 29122.0}, {'field': 'count', 'old_value': 24, 'new_value': 29}, {'field': 'instoreAmount', 'old_value': 26165.0, 'new_value': 29122.0}, {'field': 'instoreCount', 'old_value': 24, 'new_value': 29}]
2025-06-11 08:11:16,278 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:16,716 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMLK
2025-06-11 08:11:16,716 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9G31FV3GL0I86N3H2U190001EI6_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 180291.0, 'new_value': 208488.0}, {'field': 'dailyBillAmount', 'old_value': 180291.0, 'new_value': 208488.0}, {'field': 'amount', 'old_value': 197038.0, 'new_value': 204436.0}, {'field': 'count', 'old_value': 28, 'new_value': 30}, {'field': 'instoreAmount', 'old_value': 197038.0, 'new_value': 204436.0}, {'field': 'instoreCount', 'old_value': 28, 'new_value': 30}]
2025-06-11 08:11:16,732 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:17,216 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMKK
2025-06-11 08:11:17,216 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9ETPVO71I0I86N3H2U18G001EHM_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 23531.0, 'new_value': 24535.0}, {'field': 'amount', 'old_value': 23531.0, 'new_value': 24535.0}, {'field': 'count', 'old_value': 59, 'new_value': 61}, {'field': 'instoreAmount', 'old_value': 23531.0, 'new_value': 24535.0}, {'field': 'instoreCount', 'old_value': 59, 'new_value': 61}]
2025-06-11 08:11:17,216 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:17,669 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMFK
2025-06-11 08:11:17,669 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE13J2R9CI0I86N3H2U13D001ECJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 72564.0, 'new_value': 77179.0}, {'field': 'dailyBillAmount', 'old_value': 72564.0, 'new_value': 77179.0}, {'field': 'amount', 'old_value': 89359.0, 'new_value': 93694.0}, {'field': 'count', 'old_value': 81, 'new_value': 85}, {'field': 'instoreAmount', 'old_value': 96180.0, 'new_value': 100515.0}, {'field': 'instoreCount', 'old_value': 81, 'new_value': 85}]
2025-06-11 08:11:17,669 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:18,153 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMJK
2025-06-11 08:11:18,153 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EN1GSFF80I86N3H2U18C001EHI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 18329.0, 'new_value': 19163.0}, {'field': 'amount', 'old_value': 16741.0, 'new_value': 17575.0}, {'field': 'count', 'old_value': 24, 'new_value': 25}, {'field': 'instoreAmount', 'old_value': 16741.0, 'new_value': 17575.0}, {'field': 'instoreCount', 'old_value': 24, 'new_value': 25}]
2025-06-11 08:11:18,169 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:18,622 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMIK
2025-06-11 08:11:18,622 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EG92S1SB0I86N3H2U188001EHE_2025-06, 变更字段: [{'field': 'amount', 'old_value': 7041.0, 'new_value': 7729.0}, {'field': 'count', 'old_value': 20, 'new_value': 22}, {'field': 'instoreAmount', 'old_value': 7041.0, 'new_value': 7729.0}, {'field': 'instoreCount', 'old_value': 20, 'new_value': 22}]
2025-06-11 08:11:18,638 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:19,075 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMGK
2025-06-11 08:11:19,075 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_2025-06, 变更字段: [{'field': 'amount', 'old_value': 60896.11, 'new_value': 63982.01}, {'field': 'count', 'old_value': 142, 'new_value': 153}, {'field': 'instoreAmount', 'old_value': 62675.61, 'new_value': 65900.81}, {'field': 'instoreCount', 'old_value': 142, 'new_value': 153}]
2025-06-11 08:11:19,075 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:19,481 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMEK
2025-06-11 08:11:19,481 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE0JS3BF7R0I86N3H2U135001ECB_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 40340.58, 'new_value': 41454.79}, {'field': 'dailyBillAmount', 'old_value': 40340.58, 'new_value': 41454.79}, {'field': 'amount', 'old_value': 42452.74, 'new_value': 43832.66}, {'field': 'count', 'old_value': 1451, 'new_value': 1519}, {'field': 'instoreAmount', 'old_value': 42387.74, 'new_value': 43767.66}, {'field': 'instoreCount', 'old_value': 1447, 'new_value': 1515}]
2025-06-11 08:11:19,481 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:19,903 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMLS
2025-06-11 08:11:19,903 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 166152.49, 'new_value': 180578.43}, {'field': 'dailyBillAmount', 'old_value': 166152.49, 'new_value': 180578.43}, {'field': 'amount', 'old_value': 152258.04, 'new_value': 159172.04}, {'field': 'count', 'old_value': 680, 'new_value': 713}, {'field': 'instoreAmount', 'old_value': 152258.04, 'new_value': 159172.04}, {'field': 'instoreCount', 'old_value': 680, 'new_value': 713}]
2025-06-11 08:11:19,919 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:20,419 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMDK
2025-06-11 08:11:20,419 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVSS4V3460I86N3H2U12P001EBV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 24347.9, 'new_value': 26535.0}, {'field': 'dailyBillAmount', 'old_value': 24347.9, 'new_value': 26535.0}, {'field': 'amount', 'old_value': 28359.71, 'new_value': 31110.04}, {'field': 'count', 'old_value': 1000, 'new_value': 1109}, {'field': 'instoreAmount', 'old_value': 10492.35, 'new_value': 11035.48}, {'field': 'instoreCount', 'old_value': 359, 'new_value': 380}, {'field': 'onlineAmount', 'old_value': 18084.76, 'new_value': 20291.96}, {'field': 'onlineCount', 'old_value': 641, 'new_value': 729}]
2025-06-11 08:11:20,435 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:20,841 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMCK
2025-06-11 08:11:20,841 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVBEGSM760I86N3H2U12H001EBN_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 17941.2, 'new_value': 80926.75}, {'field': 'amount', 'old_value': 17940.8, 'new_value': 80926.35}, {'field': 'count', 'old_value': 12, 'new_value': 29}, {'field': 'instoreAmount', 'old_value': 17941.2, 'new_value': 80926.75}, {'field': 'instoreCount', 'old_value': 12, 'new_value': 29}]
2025-06-11 08:11:20,856 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:21,263 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMBK
2025-06-11 08:11:21,263 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 71448.39, 'new_value': 75124.04}, {'field': 'dailyBillAmount', 'old_value': 71448.39, 'new_value': 75124.04}, {'field': 'amount', 'old_value': 60065.33, 'new_value': 63536.44}, {'field': 'count', 'old_value': 1834, 'new_value': 1948}, {'field': 'instoreAmount', 'old_value': 32670.51, 'new_value': 34234.82}, {'field': 'instoreCount', 'old_value': 1338, 'new_value': 1412}, {'field': 'onlineAmount', 'old_value': 30931.3, 'new_value': 33066.1}, {'field': 'onlineCount', 'old_value': 496, 'new_value': 536}]
2025-06-11 08:11:21,263 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:21,778 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMAK
2025-06-11 08:11:21,778 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 61936.5, 'new_value': 65769.75}, {'field': 'dailyBillAmount', 'old_value': 61936.5, 'new_value': 65769.75}, {'field': 'amount', 'old_value': 37716.12, 'new_value': 40966.2}, {'field': 'count', 'old_value': 2720, 'new_value': 2920}, {'field': 'instoreAmount', 'old_value': 2269.5, 'new_value': 2456.5}, {'field': 'instoreCount', 'old_value': 159, 'new_value': 169}, {'field': 'onlineAmount', 'old_value': 36905.03, 'new_value': 40078.15}, {'field': 'onlineCount', 'old_value': 2561, 'new_value': 2751}]
2025-06-11 08:11:21,778 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:22,294 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBM9K
2025-06-11 08:11:22,294 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_2025-06, 变更字段: [{'field': 'amount', 'old_value': 59501.75, 'new_value': 65404.86}, {'field': 'count', 'old_value': 3070, 'new_value': 3343}, {'field': 'instoreAmount', 'old_value': 62314.73, 'new_value': 68513.04}, {'field': 'instoreCount', 'old_value': 3057, 'new_value': 3330}]
2025-06-11 08:11:22,310 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:22,763 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBM8K
2025-06-11 08:11:22,763 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSK489TE20I86N3H2U114001EAA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 47518.7, 'new_value': 49628.78}, {'field': 'amount', 'old_value': 47517.8, 'new_value': 49627.76}, {'field': 'count', 'old_value': 1186, 'new_value': 1245}, {'field': 'instoreAmount', 'old_value': 45241.33, 'new_value': 47277.91}, {'field': 'instoreCount', 'old_value': 1158, 'new_value': 1216}, {'field': 'onlineAmount', 'old_value': 2284.67, 'new_value': 2358.17}, {'field': 'onlineCount', 'old_value': 28, 'new_value': 29}]
2025-06-11 08:11:22,763 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:23,200 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM7K
2025-06-11 08:11:23,216 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 56028.01, 'new_value': 61199.56}, {'field': 'dailyBillAmount', 'old_value': 56028.01, 'new_value': 61199.56}, {'field': 'amount', 'old_value': 37568.53, 'new_value': 41487.69}, {'field': 'count', 'old_value': 1784, 'new_value': 1960}, {'field': 'instoreAmount', 'old_value': 6845.62, 'new_value': 7097.15}, {'field': 'instoreCount', 'old_value': 438, 'new_value': 466}, {'field': 'onlineAmount', 'old_value': 31517.41, 'new_value': 35284.11}, {'field': 'onlineCount', 'old_value': 1346, 'new_value': 1494}]
2025-06-11 08:11:23,216 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:23,794 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM6K
2025-06-11 08:11:23,794 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDS341MMSU0I86N3H2U10P001E9V_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 54738.52, 'new_value': 59232.86}, {'field': 'dailyBillAmount', 'old_value': 54738.52, 'new_value': 59232.86}, {'field': 'amount', 'old_value': 11346.94, 'new_value': 12208.92}, {'field': 'count', 'old_value': 366, 'new_value': 399}, {'field': 'instoreAmount', 'old_value': 11552.68, 'new_value': 12414.66}, {'field': 'instoreCount', 'old_value': 366, 'new_value': 399}]
2025-06-11 08:11:23,794 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:24,247 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM5K
2025-06-11 08:11:24,247 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDRR6FJ7A60I86N3H2U10L001E9R_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 41033.22, 'new_value': 44424.26}, {'field': 'amount', 'old_value': 41032.82, 'new_value': 44423.86}, {'field': 'count', 'old_value': 2361, 'new_value': 2547}, {'field': 'instoreAmount', 'old_value': 19623.28, 'new_value': 20609.02}, {'field': 'instoreCount', 'old_value': 1344, 'new_value': 1416}, {'field': 'onlineAmount', 'old_value': 21409.94, 'new_value': 23815.24}, {'field': 'onlineCount', 'old_value': 1017, 'new_value': 1131}]
2025-06-11 08:11:24,247 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:24,825 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM4K
2025-06-11 08:11:24,825 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQQG9THS10I86N3H2U108001E9E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 94494.02, 'new_value': 100171.01}, {'field': 'dailyBillAmount', 'old_value': 94494.02, 'new_value': 100171.01}, {'field': 'amount', 'old_value': 87913.89, 'new_value': 92539.69}, {'field': 'count', 'old_value': 2661, 'new_value': 2834}, {'field': 'instoreAmount', 'old_value': 88190.89, 'new_value': 92833.49}, {'field': 'instoreCount', 'old_value': 2661, 'new_value': 2834}]
2025-06-11 08:11:24,825 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:25,341 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM3K
2025-06-11 08:11:25,341 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 44540.41, 'new_value': 50608.21}, {'field': 'dailyBillAmount', 'old_value': 44540.41, 'new_value': 50608.21}, {'field': 'amount', 'old_value': 54593.91, 'new_value': 59567.34}, {'field': 'count', 'old_value': 3561, 'new_value': 3888}, {'field': 'instoreAmount', 'old_value': 40398.51, 'new_value': 44105.49}, {'field': 'instoreCount', 'old_value': 2393, 'new_value': 2631}, {'field': 'onlineAmount', 'old_value': 15355.15, 'new_value': 16781.55}, {'field': 'onlineCount', 'old_value': 1168, 'new_value': 1257}]
2025-06-11 08:11:25,341 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:25,841 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM2K
2025-06-11 08:11:25,841 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQ4GA7M630I86N3H2U1VS001E92_2025-06, 变更字段: [{'field': 'amount', 'old_value': -4276.83, 'new_value': -4687.22}, {'field': 'count', 'old_value': 37, 'new_value': 43}, {'field': 'instoreAmount', 'old_value': 953.0, 'new_value': 1037.0}, {'field': 'instoreCount', 'old_value': 10, 'new_value': 12}, {'field': 'onlineAmount', 'old_value': 613.0, 'new_value': 687.0}, {'field': 'onlineCount', 'old_value': 27, 'new_value': 31}]
2025-06-11 08:11:25,841 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:26,294 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM1K
2025-06-11 08:11:26,294 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 185594.36, 'new_value': 205539.51}, {'field': 'amount', 'old_value': 185594.36, 'new_value': 205539.51}, {'field': 'count', 'old_value': 4203, 'new_value': 4664}, {'field': 'instoreAmount', 'old_value': 135027.46, 'new_value': 148407.56}, {'field': 'instoreCount', 'old_value': 2809, 'new_value': 3107}, {'field': 'onlineAmount', 'old_value': 50566.9, 'new_value': 57131.95}, {'field': 'onlineCount', 'old_value': 1394, 'new_value': 1557}]
2025-06-11 08:11:26,294 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:26,794 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM0K
2025-06-11 08:11:26,794 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_2025-06, 变更字段: [{'field': 'amount', 'old_value': 191966.48, 'new_value': 196732.02}, {'field': 'count', 'old_value': 3438, 'new_value': 3522}, {'field': 'instoreAmount', 'old_value': 177945.38, 'new_value': 181599.18}, {'field': 'instoreCount', 'old_value': 3150, 'new_value': 3218}, {'field': 'onlineAmount', 'old_value': 14658.1, 'new_value': 15769.84}, {'field': 'onlineCount', 'old_value': 288, 'new_value': 304}]
2025-06-11 08:11:26,794 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:27,247 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM3S
2025-06-11 08:11:27,247 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 208197.78, 'new_value': 225840.8}, {'field': 'dailyBillAmount', 'old_value': 208197.78, 'new_value': 225840.8}, {'field': 'amount', 'old_value': 208095.89, 'new_value': 224231.62}, {'field': 'count', 'old_value': 2145, 'new_value': 2362}, {'field': 'instoreAmount', 'old_value': 166611.28, 'new_value': 176976.38}, {'field': 'instoreCount', 'old_value': 835, 'new_value': 884}, {'field': 'onlineAmount', 'old_value': 42625.36, 'new_value': 48413.06}, {'field': 'onlineCount', 'old_value': 1310, 'new_value': 1478}]
2025-06-11 08:11:27,247 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:27,685 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMYJ
2025-06-11 08:11:27,685 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 117325.81, 'new_value': 123614.77}, {'field': 'dailyBillAmount', 'old_value': 117325.81, 'new_value': 123614.77}, {'field': 'amount', 'old_value': 58573.21, 'new_value': 62192.19}, {'field': 'count', 'old_value': 1269, 'new_value': 1342}, {'field': 'instoreAmount', 'old_value': 50563.65, 'new_value': 53221.89}, {'field': 'instoreCount', 'old_value': 1099, 'new_value': 1155}, {'field': 'onlineAmount', 'old_value': 8854.26, 'new_value': 9829.24}, {'field': 'onlineCount', 'old_value': 170, 'new_value': 187}]
2025-06-11 08:11:27,685 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:28,106 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMWJ
2025-06-11 08:11:28,106 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDMFCJQF4F0I86N3H2U1UC001E7I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 69947.48, 'new_value': 73700.78}, {'field': 'amount', 'old_value': 69947.48, 'new_value': 73700.78}, {'field': 'count', 'old_value': 766, 'new_value': 814}, {'field': 'instoreAmount', 'old_value': 49108.62, 'new_value': 50758.1}, {'field': 'instoreCount', 'old_value': 468, 'new_value': 487}, {'field': 'onlineAmount', 'old_value': 22491.54, 'new_value': 24595.36}, {'field': 'onlineCount', 'old_value': 298, 'new_value': 327}]
2025-06-11 08:11:28,106 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:28,606 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMZJ
2025-06-11 08:11:28,606 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 96361.5, 'new_value': 106000.3}, {'field': 'dailyBillAmount', 'old_value': 146516.5, 'new_value': 160760.3}, {'field': 'amount', 'old_value': 96361.5, 'new_value': 106000.3}, {'field': 'count', 'old_value': 368, 'new_value': 408}, {'field': 'instoreAmount', 'old_value': 98437.1, 'new_value': 108313.9}, {'field': 'instoreCount', 'old_value': 368, 'new_value': 408}]
2025-06-11 08:11:28,606 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:29,044 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMWR
2025-06-11 08:11:29,044 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJP082LC00I86N3H2U1JM001ESS_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 10711.7, 'new_value': 11691.5}, {'field': 'amount', 'old_value': 10711.7, 'new_value': 11691.5}, {'field': 'count', 'old_value': 35, 'new_value': 39}, {'field': 'instoreAmount', 'old_value': 10711.7, 'new_value': 11691.5}, {'field': 'instoreCount', 'old_value': 35, 'new_value': 39}]
2025-06-11 08:11:29,044 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:29,622 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMVR
2025-06-11 08:11:29,622 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJGUIA2QR0I86N3H2U1JH001ESN_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7860.0, 'new_value': 8229.0}, {'field': 'amount', 'old_value': 7860.0, 'new_value': 8229.0}, {'field': 'count', 'old_value': 13, 'new_value': 14}, {'field': 'instoreAmount', 'old_value': 7860.0, 'new_value': 8229.0}, {'field': 'instoreCount', 'old_value': 13, 'new_value': 14}]
2025-06-11 08:11:29,622 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:30,138 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMUR
2025-06-11 08:11:30,138 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJ943QH9Q0I86N3H2U1JD001ESJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 25288.0, 'new_value': 25587.0}, {'field': 'amount', 'old_value': 25288.0, 'new_value': 25587.0}, {'field': 'count', 'old_value': 35, 'new_value': 38}, {'field': 'instoreAmount', 'old_value': 26086.0, 'new_value': 26385.0}, {'field': 'instoreCount', 'old_value': 35, 'new_value': 38}]
2025-06-11 08:11:30,138 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:30,622 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMTR
2025-06-11 08:11:30,622 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 378699.03, 'new_value': 393293.1}, {'field': 'dailyBillAmount', 'old_value': 378699.03, 'new_value': 393293.1}, {'field': 'amount', 'old_value': 24552.97, 'new_value': 25344.35}, {'field': 'count', 'old_value': 259, 'new_value': 271}, {'field': 'instoreAmount', 'old_value': 19517.72, 'new_value': 20085.75}, {'field': 'instoreCount', 'old_value': 188, 'new_value': 194}, {'field': 'onlineAmount', 'old_value': 5154.08, 'new_value': 5377.43}, {'field': 'onlineCount', 'old_value': 71, 'new_value': 77}]
2025-06-11 08:11:30,622 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:31,075 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMSR
2025-06-11 08:11:31,075 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDG7SC9VNO0I86N3H2U1HP001EQV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3066.0, 'new_value': 3437.0}, {'field': 'dailyBillAmount', 'old_value': 3066.0, 'new_value': 3437.0}, {'field': 'amount', 'old_value': 16863.0, 'new_value': 18348.0}, {'field': 'count', 'old_value': 44, 'new_value': 48}, {'field': 'instoreAmount', 'old_value': 17061.0, 'new_value': 18546.0}, {'field': 'instoreCount', 'old_value': 44, 'new_value': 48}]
2025-06-11 08:11:31,091 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:31,669 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMRR
2025-06-11 08:11:31,669 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 12139.7, 'new_value': 12645.7}, {'field': 'amount', 'old_value': 12139.7, 'new_value': 12645.7}, {'field': 'count', 'old_value': 73, 'new_value': 80}, {'field': 'instoreAmount', 'old_value': 12247.7, 'new_value': 12753.7}, {'field': 'instoreCount', 'old_value': 73, 'new_value': 80}]
2025-06-11 08:11:31,669 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:32,153 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMSK
2025-06-11 08:11:32,153 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF8HFHI690I86N3H2U1H9001EQF_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8132.0, 'new_value': 8562.0}, {'field': 'dailyBillAmount', 'old_value': 8132.0, 'new_value': 8562.0}, {'field': 'amount', 'old_value': 8127.0, 'new_value': 8557.0}, {'field': 'count', 'old_value': 36, 'new_value': 38}, {'field': 'instoreAmount', 'old_value': 8657.0, 'new_value': 9087.0}, {'field': 'instoreCount', 'old_value': 36, 'new_value': 38}]
2025-06-11 08:11:32,153 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:32,591 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMQK
2025-06-11 08:11:32,591 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEQ2M9E710I86N3H2U1H1001EQ7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 20595.57, 'new_value': 23681.2}, {'field': 'dailyBillAmount', 'old_value': 20595.57, 'new_value': 23681.2}, {'field': 'amount', 'old_value': 18854.13, 'new_value': 21895.53}, {'field': 'count', 'old_value': 191, 'new_value': 211}, {'field': 'instoreAmount', 'old_value': 17048.2, 'new_value': 20089.6}, {'field': 'instoreCount', 'old_value': 166, 'new_value': 186}]
2025-06-11 08:11:32,591 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:33,044 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMXJ
2025-06-11 08:11:33,044 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 125956.79, 'new_value': 136476.16}, {'field': 'dailyBillAmount', 'old_value': 125956.79, 'new_value': 136476.16}, {'field': 'amount', 'old_value': 10249.07, 'new_value': 10568.07}, {'field': 'count', 'old_value': 295, 'new_value': 319}, {'field': 'instoreAmount', 'old_value': 11253.64, 'new_value': 11663.14}, {'field': 'instoreCount', 'old_value': 295, 'new_value': 319}]
2025-06-11 08:11:33,044 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:33,528 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMPK
2025-06-11 08:11:33,528 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 93674.6, 'new_value': 96834.2}, {'field': 'dailyBillAmount', 'old_value': 93674.6, 'new_value': 96834.2}, {'field': 'amount', 'old_value': 92840.9, 'new_value': 96000.5}, {'field': 'count', 'old_value': 118, 'new_value': 123}, {'field': 'instoreAmount', 'old_value': 93817.9, 'new_value': 96977.5}, {'field': 'instoreCount', 'old_value': 118, 'new_value': 123}]
2025-06-11 08:11:33,528 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:33,981 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMXR
2025-06-11 08:11:33,981 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDOR4RUGJG0I86N3H2U1MC001EVI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 13949.0, 'new_value': 14489.0}, {'field': 'dailyBillAmount', 'old_value': 13949.0, 'new_value': 14489.0}, {'field': 'amount', 'old_value': 14031.0, 'new_value': 14571.0}, {'field': 'count', 'old_value': 31, 'new_value': 33}, {'field': 'instoreAmount', 'old_value': 15666.0, 'new_value': 16206.0}, {'field': 'instoreCount', 'old_value': 31, 'new_value': 33}]
2025-06-11 08:11:33,981 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:34,466 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMYR
2025-06-11 08:11:34,466 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 93087.87, 'new_value': 99480.99}, {'field': 'dailyBillAmount', 'old_value': 83320.27, 'new_value': 88381.39}, {'field': 'amount', 'old_value': 89903.86, 'new_value': 96296.72}, {'field': 'count', 'old_value': 477, 'new_value': 492}, {'field': 'instoreAmount', 'old_value': 90901.86, 'new_value': 97294.98}, {'field': 'instoreCount', 'old_value': 477, 'new_value': 492}]
2025-06-11 08:11:34,466 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:34,888 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM1S
2025-06-11 08:11:34,888 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDULBESLOI0I86N3H2U1PE001F2K_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 94701.87, 'new_value': 99598.37}, {'field': 'dailyBillAmount', 'old_value': 94701.87, 'new_value': 99598.37}, {'field': 'amount', 'old_value': 94106.87, 'new_value': 99003.37}, {'field': 'count', 'old_value': 539, 'new_value': 576}, {'field': 'instoreAmount', 'old_value': 94106.87, 'new_value': 99003.37}, {'field': 'instoreCount', 'old_value': 539, 'new_value': 576}]
2025-06-11 08:11:34,903 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:35,325 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM2S
2025-06-11 08:11:35,325 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_2025-06, 变更字段: [{'field': 'amount', 'old_value': 16584.59, 'new_value': 17426.98}, {'field': 'count', 'old_value': 1523, 'new_value': 1618}, {'field': 'instoreAmount', 'old_value': 17011.46, 'new_value': 17884.96}, {'field': 'instoreCount', 'old_value': 1523, 'new_value': 1618}]
2025-06-11 08:11:35,325 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:35,747 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM4S
2025-06-11 08:11:35,747 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVIE6UN1G0I86N3H2U1PU001F34_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 74021.3, 'new_value': 76720.8}, {'field': 'amount', 'old_value': 74021.3, 'new_value': 76720.8}, {'field': 'count', 'old_value': 467, 'new_value': 486}, {'field': 'instoreAmount', 'old_value': 74021.3, 'new_value': 76720.8}, {'field': 'instoreCount', 'old_value': 467, 'new_value': 486}]
2025-06-11 08:11:35,747 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:36,138 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM5S
2025-06-11 08:11:36,153 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_2025-06, 变更字段: [{'field': 'amount', 'old_value': 56295.47, 'new_value': 62908.5}, {'field': 'count', 'old_value': 2276, 'new_value': 2609}, {'field': 'instoreAmount', 'old_value': 18379.45, 'new_value': 20533.61}, {'field': 'instoreCount', 'old_value': 744, 'new_value': 865}, {'field': 'onlineAmount', 'old_value': 38784.2, 'new_value': 43400.48}, {'field': 'onlineCount', 'old_value': 1532, 'new_value': 1744}]
2025-06-11 08:11:36,153 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:36,669 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM6S
2025-06-11 08:11:36,669 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 28290.91, 'new_value': 30330.24}, {'field': 'dailyBillAmount', 'old_value': 28290.91, 'new_value': 30330.24}, {'field': 'amount', 'old_value': 43029.92, 'new_value': 46455.73}, {'field': 'count', 'old_value': 2180, 'new_value': 2338}, {'field': 'instoreAmount', 'old_value': 23321.75, 'new_value': 24806.76}, {'field': 'instoreCount', 'old_value': 1309, 'new_value': 1405}, {'field': 'onlineAmount', 'old_value': 20428.87, 'new_value': 22404.27}, {'field': 'onlineCount', 'old_value': 871, 'new_value': 933}]
2025-06-11 08:11:36,669 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:37,122 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM8S
2025-06-11 08:11:37,122 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1EFF1UHG0I86N3H2U1R1001F47_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 21614.0, 'new_value': 21694.0}, {'field': 'amount', 'old_value': 21614.0, 'new_value': 21694.0}, {'field': 'count', 'old_value': 17, 'new_value': 18}, {'field': 'instoreAmount', 'old_value': 21614.0, 'new_value': 21694.0}, {'field': 'instoreCount', 'old_value': 17, 'new_value': 18}]
2025-06-11 08:11:37,122 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:37,622 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM9S
2025-06-11 08:11:37,622 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1T4E94FK0I86N3H2U1R9001F4F_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 41722.45, 'new_value': 45221.1}, {'field': 'dailyBillAmount', 'old_value': 41722.45, 'new_value': 45221.1}, {'field': 'amount', 'old_value': 21906.37, 'new_value': 24400.17}, {'field': 'count', 'old_value': 1568, 'new_value': 1735}, {'field': 'instoreAmount', 'old_value': 3804.8, 'new_value': 3892.7}, {'field': 'instoreCount', 'old_value': 163, 'new_value': 169}, {'field': 'onlineAmount', 'old_value': 18101.57, 'new_value': 20507.47}, {'field': 'onlineCount', 'old_value': 1405, 'new_value': 1566}]
2025-06-11 08:11:37,622 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:38,091 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMAS
2025-06-11 08:11:38,091 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE25DAIM3B0I86N3H2U1RD001F4J_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 102410.66, 'new_value': 123173.01}, {'field': 'dailyBillAmount', 'old_value': 102410.66, 'new_value': 123173.01}, {'field': 'amount', 'old_value': 106164.4, 'new_value': 115930.3}, {'field': 'count', 'old_value': 952, 'new_value': 1058}, {'field': 'instoreAmount', 'old_value': 87051.79, 'new_value': 95535.39}, {'field': 'instoreCount', 'old_value': 578, 'new_value': 658}, {'field': 'onlineAmount', 'old_value': 19112.61, 'new_value': 20394.91}, {'field': 'onlineCount', 'old_value': 374, 'new_value': 400}]
2025-06-11 08:11:38,091 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:38,528 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMBS
2025-06-11 08:11:38,528 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE2CVHLBFV0I86N3H2U1RH001F4N_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 144704.16, 'new_value': 155486.83}, {'field': 'dailyBillAmount', 'old_value': 144704.16, 'new_value': 155486.83}, {'field': 'amount', 'old_value': 128759.16, 'new_value': 136203.86}, {'field': 'count', 'old_value': 732, 'new_value': 779}, {'field': 'instoreAmount', 'old_value': 131123.16, 'new_value': 139892.86}, {'field': 'instoreCount', 'old_value': 732, 'new_value': 779}]
2025-06-11 08:11:38,528 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:38,950 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMCS
2025-06-11 08:11:38,950 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 354968.09, 'new_value': 383351.2}, {'field': 'dailyBillAmount', 'old_value': 354968.09, 'new_value': 383351.2}, {'field': 'amount', 'old_value': 389800.7, 'new_value': 421747.71}, {'field': 'count', 'old_value': 2224, 'new_value': 2422}, {'field': 'instoreAmount', 'old_value': 294298.3, 'new_value': 314363.61}, {'field': 'instoreCount', 'old_value': 1173, 'new_value': 1257}, {'field': 'onlineAmount', 'old_value': 97458.86, 'new_value': 109590.16}, {'field': 'onlineCount', 'old_value': 1051, 'new_value': 1165}]
2025-06-11 08:11:38,966 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:39,450 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMDS
2025-06-11 08:11:39,450 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE3PJ2TE9A0I86N3H2U1S3001F59_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 117886.13, 'new_value': 124260.65}, {'field': 'dailyBillAmount', 'old_value': 117886.13, 'new_value': 124260.65}, {'field': 'amount', 'old_value': 167053.73, 'new_value': 176597.82}, {'field': 'count', 'old_value': 826, 'new_value': 881}, {'field': 'instoreAmount', 'old_value': 156765.8, 'new_value': 165079.79}, {'field': 'instoreCount', 'old_value': 633, 'new_value': 668}, {'field': 'onlineAmount', 'old_value': 10581.78, 'new_value': 11811.88}, {'field': 'onlineCount', 'old_value': 193, 'new_value': 213}]
2025-06-11 08:11:39,450 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:39,934 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMES
2025-06-11 08:11:39,934 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE48KDQ6AS0I86N3H2U1SB001F5H_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 143348.14, 'new_value': 151855.97}, {'field': 'dailyBillAmount', 'old_value': 143348.14, 'new_value': 151855.97}, {'field': 'amount', 'old_value': 136697.8, 'new_value': 144639.2}, {'field': 'count', 'old_value': 606, 'new_value': 646}, {'field': 'instoreAmount', 'old_value': 138567.6, 'new_value': 146509.0}, {'field': 'instoreCount', 'old_value': 606, 'new_value': 646}]
2025-06-11 08:11:39,934 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:40,341 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMFS
2025-06-11 08:11:40,341 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE4GES1U770I86N3H2U1SF001F5L_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 299160.47, 'new_value': 324187.94}, {'field': 'amount', 'old_value': 299160.47, 'new_value': 324187.94}, {'field': 'count', 'old_value': 2300, 'new_value': 2556}, {'field': 'instoreAmount', 'old_value': 299160.47, 'new_value': 324187.94}, {'field': 'instoreCount', 'old_value': 2300, 'new_value': 2556}]
2025-06-11 08:11:40,341 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:40,841 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMGS
2025-06-11 08:11:40,841 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 281579.79, 'new_value': 302856.62}, {'field': 'dailyBillAmount', 'old_value': 281579.79, 'new_value': 302856.62}, {'field': 'amount', 'old_value': 324349.22, 'new_value': 348691.59}, {'field': 'count', 'old_value': 2263, 'new_value': 2467}, {'field': 'instoreAmount', 'old_value': 186445.5, 'new_value': 198324.6}, {'field': 'instoreCount', 'old_value': 1015, 'new_value': 1093}, {'field': 'onlineAmount', 'old_value': 142442.9, 'new_value': 155657.6}, {'field': 'onlineCount', 'old_value': 1248, 'new_value': 1374}]
2025-06-11 08:11:40,841 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:41,294 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMHS
2025-06-11 08:11:41,294 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 179016.14, 'new_value': 187456.26}, {'field': 'dailyBillAmount', 'old_value': 179016.14, 'new_value': 187456.26}, {'field': 'amount', 'old_value': 178173.25, 'new_value': 185921.98}, {'field': 'count', 'old_value': 1796, 'new_value': 1913}, {'field': 'instoreAmount', 'old_value': 131017.66, 'new_value': 134871.56}, {'field': 'instoreCount', 'old_value': 887, 'new_value': 922}, {'field': 'onlineAmount', 'old_value': 47656.21, 'new_value': 51621.9}, {'field': 'onlineCount', 'old_value': 909, 'new_value': 991}]
2025-06-11 08:11:41,294 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:41,716 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMIS
2025-06-11 08:11:41,716 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEA4041D6F0I86N3H2U1V9001F8F_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 199250.12, 'new_value': 215770.41}, {'field': 'dailyBillAmount', 'old_value': 199250.12, 'new_value': 215770.41}, {'field': 'amount', 'old_value': 200355.34, 'new_value': 216628.04}, {'field': 'count', 'old_value': 1625, 'new_value': 1755}, {'field': 'instoreAmount', 'old_value': 181504.84, 'new_value': 196198.57}, {'field': 'instoreCount', 'old_value': 959, 'new_value': 1038}, {'field': 'onlineAmount', 'old_value': 18999.78, 'new_value': 20579.21}, {'field': 'onlineCount', 'old_value': 666, 'new_value': 717}]
2025-06-11 08:11:41,716 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:42,200 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMJS
2025-06-11 08:11:42,200 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAB2RFI0N0I86N3H2U1VD001F8J_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 53702.4, 'new_value': 58961.4}, {'field': 'amount', 'old_value': 53702.4, 'new_value': 58961.4}, {'field': 'count', 'old_value': 225, 'new_value': 246}, {'field': 'instoreAmount', 'old_value': 53702.4, 'new_value': 58961.4}, {'field': 'instoreCount', 'old_value': 225, 'new_value': 246}]
2025-06-11 08:11:42,200 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:42,591 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMKS
2025-06-11 08:11:42,591 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAIN5DMKK0I86N3H2U1VH001F8N_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 154272.62, 'new_value': 157091.12}, {'field': 'dailyBillAmount', 'old_value': 154272.62, 'new_value': 157091.12}, {'field': 'amount', 'old_value': -106093.78, 'new_value': -108455.48}, {'field': 'count', 'old_value': 309, 'new_value': 320}, {'field': 'instoreAmount', 'old_value': 2496.0, 'new_value': 2585.0}, {'field': 'instoreCount', 'old_value': 110, 'new_value': 113}, {'field': 'onlineAmount', 'old_value': 4736.02, 'new_value': 4980.32}, {'field': 'onlineCount', 'old_value': 199, 'new_value': 207}]
2025-06-11 08:11:42,591 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:43,044 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMMS
2025-06-11 08:11:43,044 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 185881.26, 'new_value': 198641.43}, {'field': 'dailyBillAmount', 'old_value': 185881.26, 'new_value': 198641.43}, {'field': 'amount', 'old_value': 53570.0, 'new_value': 54944.0}, {'field': 'count', 'old_value': 225, 'new_value': 236}, {'field': 'instoreAmount', 'old_value': 53738.7, 'new_value': 55112.7}, {'field': 'instoreCount', 'old_value': 215, 'new_value': 226}]
2025-06-11 08:11:43,044 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:43,497 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMNS
2025-06-11 08:11:43,497 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBP5Q03GB0I86N3H2U106001F9C_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 128253.49, 'new_value': 133654.23}, {'field': 'dailyBillAmount', 'old_value': 128253.49, 'new_value': 133654.23}, {'field': 'amount', 'old_value': 125584.6, 'new_value': 130885.66}, {'field': 'count', 'old_value': 744, 'new_value': 796}, {'field': 'instoreAmount', 'old_value': 120567.51, 'new_value': 125028.41}, {'field': 'instoreCount', 'old_value': 605, 'new_value': 634}, {'field': 'onlineAmount', 'old_value': 5075.15, 'new_value': 5915.31}, {'field': 'onlineCount', 'old_value': 139, 'new_value': 162}]
2025-06-11 08:11:43,497 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:43,981 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMOS
2025-06-11 08:11:43,981 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 127778.48, 'new_value': 137289.8}, {'field': 'dailyBillAmount', 'old_value': 127778.48, 'new_value': 137289.8}, {'field': 'amount', 'old_value': 54973.52, 'new_value': 58501.11}, {'field': 'count', 'old_value': 751, 'new_value': 817}, {'field': 'instoreAmount', 'old_value': 37938.04, 'new_value': 39970.04}, {'field': 'instoreCount', 'old_value': 211, 'new_value': 220}, {'field': 'onlineAmount', 'old_value': 17036.41, 'new_value': 18532.0}, {'field': 'onlineCount', 'old_value': 540, 'new_value': 597}]
2025-06-11 08:11:43,997 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:44,403 - INFO - 更新表单数据成功: FINST-AI866781XLYVAQQR8XBGXCAD2PZM3ON8IRFBMK1
2025-06-11 08:11:44,403 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_2025-06, 变更字段: [{'field': 'amount', 'old_value': 11540.0, 'new_value': 12930.0}, {'field': 'count', 'old_value': 8, 'new_value': 9}, {'field': 'instoreAmount', 'old_value': 11540.0, 'new_value': 12930.0}, {'field': 'instoreCount', 'old_value': 8, 'new_value': 9}]
2025-06-11 08:11:44,403 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:44,888 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMPS
2025-06-11 08:11:44,888 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 52196.66, 'new_value': 56744.11}, {'field': 'amount', 'old_value': 52195.82, 'new_value': 56742.67}, {'field': 'count', 'old_value': 2452, 'new_value': 2676}, {'field': 'instoreAmount', 'old_value': 13964.11, 'new_value': 15276.33}, {'field': 'instoreCount', 'old_value': 580, 'new_value': 637}, {'field': 'onlineAmount', 'old_value': 39396.72, 'new_value': 42680.23}, {'field': 'onlineCount', 'old_value': 1872, 'new_value': 2039}]
2025-06-11 08:11:44,888 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:45,450 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMQS
2025-06-11 08:11:45,450 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRPJDJBMAPL77QBECDAL3H1H001J5I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 16370.0, 'new_value': 16929.0}, {'field': 'amount', 'old_value': 16370.0, 'new_value': 16929.0}, {'field': 'count', 'old_value': 67, 'new_value': 70}, {'field': 'instoreAmount', 'old_value': 16370.0, 'new_value': 16929.0}, {'field': 'instoreCount', 'old_value': 67, 'new_value': 70}]
2025-06-11 08:11:45,450 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:45,919 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMRS
2025-06-11 08:11:45,919 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRR231GUB4QN7QBECDAL3H28001J69_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 147742.83, 'new_value': 155629.8}, {'field': 'dailyBillAmount', 'old_value': 147742.83, 'new_value': 155629.8}, {'field': 'amount', 'old_value': 59396.49, 'new_value': 61300.49}, {'field': 'count', 'old_value': 1149, 'new_value': 1198}, {'field': 'instoreAmount', 'old_value': 59795.3, 'new_value': 61712.1}, {'field': 'instoreCount', 'old_value': 1149, 'new_value': 1198}]
2025-06-11 08:11:45,919 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:46,325 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMSS
2025-06-11 08:11:46,325 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B358872E0DBE420182AF77D4C47644F6_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 63995.98, 'new_value': 71728.12}, {'field': 'amount', 'old_value': 63995.98, 'new_value': 71728.12}, {'field': 'count', 'old_value': 1472, 'new_value': 1673}, {'field': 'instoreAmount', 'old_value': 63995.98, 'new_value': 71728.12}, {'field': 'instoreCount', 'old_value': 1472, 'new_value': 1673}]
2025-06-11 08:11:46,325 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:46,778 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMTS
2025-06-11 08:11:46,778 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 15532.65, 'new_value': 16501.17}, {'field': 'amount', 'old_value': 15532.51, 'new_value': 16500.6}, {'field': 'count', 'old_value': 941, 'new_value': 1019}, {'field': 'instoreAmount', 'old_value': 6410.38, 'new_value': 6633.38}, {'field': 'instoreCount', 'old_value': 326, 'new_value': 346}, {'field': 'onlineAmount', 'old_value': 9382.82, 'new_value': 10133.17}, {'field': 'onlineCount', 'old_value': 615, 'new_value': 673}]
2025-06-11 08:11:46,778 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:47,263 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMUS
2025-06-11 08:11:47,263 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_E214E9E9A4534AE1943BBACB09056E2E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 19130.7, 'new_value': 19877.7}, {'field': 'amount', 'old_value': 19130.7, 'new_value': 19877.7}, {'field': 'count', 'old_value': 56, 'new_value': 58}, {'field': 'instoreAmount', 'old_value': 19130.7, 'new_value': 19877.7}, {'field': 'instoreCount', 'old_value': 56, 'new_value': 58}]
2025-06-11 08:11:47,278 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:47,716 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMZR
2025-06-11 08:11:47,716 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDPJQDDVRC0I86N3H2U1MO001EVU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 49101.0, 'new_value': 49855.0}, {'field': 'amount', 'old_value': 49101.0, 'new_value': 49855.0}, {'field': 'count', 'old_value': 280, 'new_value': 283}, {'field': 'instoreAmount', 'old_value': 53161.0, 'new_value': 53915.0}, {'field': 'instoreCount', 'old_value': 280, 'new_value': 283}]
2025-06-11 08:11:47,747 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:48,263 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMZS
2025-06-11 08:11:48,263 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 48603.53, 'new_value': 54669.6}, {'field': 'dailyBillAmount', 'old_value': 48612.17, 'new_value': 54745.34}, {'field': 'amount', 'old_value': 48602.81, 'new_value': 54668.88}, {'field': 'count', 'old_value': 2705, 'new_value': 3032}, {'field': 'instoreAmount', 'old_value': 24104.36, 'new_value': 26613.68}, {'field': 'instoreCount', 'old_value': 1284, 'new_value': 1426}, {'field': 'onlineAmount', 'old_value': 24817.61, 'new_value': 28401.46}, {'field': 'onlineCount', 'old_value': 1421, 'new_value': 1606}]
2025-06-11 08:11:48,263 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:48,700 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM0T
2025-06-11 08:11:48,700 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 26361.2, 'new_value': 28450.71}, {'field': 'amount', 'old_value': 26361.2, 'new_value': 28450.71}, {'field': 'count', 'old_value': 1648, 'new_value': 1798}, {'field': 'instoreAmount', 'old_value': 14527.52, 'new_value': 15170.92}, {'field': 'instoreCount', 'old_value': 798, 'new_value': 854}, {'field': 'onlineAmount', 'old_value': 13184.72, 'new_value': 14658.83}, {'field': 'onlineCount', 'old_value': 850, 'new_value': 944}]
2025-06-11 08:11:48,700 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:49,106 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM1T
2025-06-11 08:11:49,106 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V_2025-06, 变更字段: [{'field': 'amount', 'old_value': 68816.52, 'new_value': 71853.02}, {'field': 'count', 'old_value': 690, 'new_value': 730}, {'field': 'instoreAmount', 'old_value': 68841.72, 'new_value': 71894.92}, {'field': 'instoreCount', 'old_value': 690, 'new_value': 730}]
2025-06-11 08:11:49,106 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:49,606 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM2T
2025-06-11 08:11:49,606 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 37952.88, 'new_value': 41511.55}, {'field': 'dailyBillAmount', 'old_value': 39648.03, 'new_value': 43380.42}, {'field': 'amount', 'old_value': 37952.88, 'new_value': 41511.55}, {'field': 'count', 'old_value': 991, 'new_value': 1085}, {'field': 'instoreAmount', 'old_value': 34832.67, 'new_value': 38082.15}, {'field': 'instoreCount', 'old_value': 735, 'new_value': 806}, {'field': 'onlineAmount', 'old_value': 3150.51, 'new_value': 3459.7}, {'field': 'onlineCount', 'old_value': 256, 'new_value': 279}]
2025-06-11 08:11:49,606 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:50,044 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMVS
2025-06-11 08:11:50,044 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 62758.33, 'new_value': 69133.17}, {'field': 'dailyBillAmount', 'old_value': 51042.5, 'new_value': 55549.8}, {'field': 'amount', 'old_value': 62758.33, 'new_value': 69133.17}, {'field': 'count', 'old_value': 938, 'new_value': 1025}, {'field': 'instoreAmount', 'old_value': 59274.5, 'new_value': 65268.4}, {'field': 'instoreCount', 'old_value': 778, 'new_value': 848}, {'field': 'onlineAmount', 'old_value': 3607.83, 'new_value': 4014.77}, {'field': 'onlineCount', 'old_value': 160, 'new_value': 177}]
2025-06-11 08:11:50,044 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:50,544 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMWS
2025-06-11 08:11:50,544 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0BI1TJFBK3IKSIOCDI7LH001FSI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 11523.88, 'new_value': 12209.74}, {'field': 'amount', 'old_value': 11523.88, 'new_value': 12209.74}, {'field': 'count', 'old_value': 512, 'new_value': 545}, {'field': 'instoreAmount', 'old_value': 10138.08, 'new_value': 10769.74}, {'field': 'instoreCount', 'old_value': 467, 'new_value': 498}, {'field': 'onlineAmount', 'old_value': 1420.2, 'new_value': 1474.4}, {'field': 'onlineCount', 'old_value': 45, 'new_value': 47}]
2025-06-11 08:11:50,544 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:50,950 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMXS
2025-06-11 08:11:50,950 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 143692.2, 'new_value': 153444.94}, {'field': 'dailyBillAmount', 'old_value': 143692.2, 'new_value': 153444.94}, {'field': 'amount', 'old_value': 187904.21, 'new_value': 202483.1}, {'field': 'count', 'old_value': 1686, 'new_value': 1875}, {'field': 'instoreAmount', 'old_value': 177864.5, 'new_value': 191283.52}, {'field': 'instoreCount', 'old_value': 1144, 'new_value': 1260}, {'field': 'onlineAmount', 'old_value': 13596.03, 'new_value': 15404.49}, {'field': 'onlineCount', 'old_value': 542, 'new_value': 615}]
2025-06-11 08:11:50,950 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:51,403 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMYS
2025-06-11 08:11:51,403 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 45449.53, 'new_value': 49091.52}, {'field': 'dailyBillAmount', 'old_value': 45449.53, 'new_value': 49091.52}, {'field': 'amount', 'old_value': 14990.1, 'new_value': 15669.07}, {'field': 'count', 'old_value': 244, 'new_value': 262}, {'field': 'instoreAmount', 'old_value': 10412.53, 'new_value': 10850.6}, {'field': 'instoreCount', 'old_value': 151, 'new_value': 163}, {'field': 'onlineAmount', 'old_value': 4706.95, 'new_value': 4947.85}, {'field': 'onlineCount', 'old_value': 93, 'new_value': 99}]
2025-06-11 08:11:51,450 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:51,919 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMGT
2025-06-11 08:11:51,919 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 288637.16, 'new_value': 306161.63}, {'field': 'dailyBillAmount', 'old_value': 288637.16, 'new_value': 306161.63}, {'field': 'amount', 'old_value': 207023.9, 'new_value': 220555.4}, {'field': 'count', 'old_value': 1421, 'new_value': 1513}, {'field': 'instoreAmount', 'old_value': 147969.2, 'new_value': 155767.3}, {'field': 'instoreCount', 'old_value': 1143, 'new_value': 1207}, {'field': 'onlineAmount', 'old_value': 59054.7, 'new_value': 64788.1}, {'field': 'onlineCount', 'old_value': 278, 'new_value': 306}]
2025-06-11 08:11:51,919 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:52,372 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMHT
2025-06-11 08:11:52,372 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRGKS2JA27AV8LHQQGIDC001EJL_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 372453.03, 'new_value': 412834.6}, {'field': 'amount', 'old_value': 372453.03, 'new_value': 412834.6}, {'field': 'count', 'old_value': 1288, 'new_value': 1418}, {'field': 'instoreAmount', 'old_value': 372310.03, 'new_value': 412691.6}, {'field': 'instoreCount', 'old_value': 1287, 'new_value': 1417}]
2025-06-11 08:11:52,372 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:52,856 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMIT
2025-06-11 08:11:52,856 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRL782OM57AV8LHQQGIDF001EJO_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 190718.54, 'new_value': 210953.62}, {'field': 'dailyBillAmount', 'old_value': 169638.14, 'new_value': 186939.25}, {'field': 'amount', 'old_value': 190718.54, 'new_value': 210953.62}, {'field': 'count', 'old_value': 1229, 'new_value': 1379}, {'field': 'instoreAmount', 'old_value': 173732.21, 'new_value': 191593.6}, {'field': 'instoreCount', 'old_value': 739, 'new_value': 815}, {'field': 'onlineAmount', 'old_value': 17158.66, 'new_value': 19570.9}, {'field': 'onlineCount', 'old_value': 490, 'new_value': 564}]
2025-06-11 08:11:52,856 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:53,341 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMJT
2025-06-11 08:11:53,341 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 137378.37, 'new_value': 152724.27}, {'field': 'dailyBillAmount', 'old_value': 123309.98, 'new_value': 138621.21}, {'field': 'amount', 'old_value': 137378.37, 'new_value': 152724.27}, {'field': 'count', 'old_value': 432, 'new_value': 488}, {'field': 'instoreAmount', 'old_value': 124558.7, 'new_value': 138508.3}, {'field': 'instoreCount', 'old_value': 324, 'new_value': 364}, {'field': 'onlineAmount', 'old_value': 12986.5, 'new_value': 14382.8}, {'field': 'onlineCount', 'old_value': 108, 'new_value': 124}]
2025-06-11 08:11:53,341 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:53,825 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM3T
2025-06-11 08:11:53,825 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 72852.0, 'new_value': 84943.64}, {'field': 'dailyBillAmount', 'old_value': 72852.0, 'new_value': 84943.64}, {'field': 'amount', 'old_value': 9839.09, 'new_value': 10902.15}, {'field': 'count', 'old_value': 339, 'new_value': 395}, {'field': 'instoreAmount', 'old_value': 10840.12, 'new_value': 12203.93}, {'field': 'instoreCount', 'old_value': 339, 'new_value': 395}]
2025-06-11 08:11:53,841 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:54,263 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMAT
2025-06-11 08:11:54,278 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQ716B84L7AV8LHQQGID0001EJ9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 38242.65, 'new_value': 41029.87}, {'field': 'dailyBillAmount', 'old_value': 18965.27, 'new_value': 20076.3}, {'field': 'amount', 'old_value': 38242.65, 'new_value': 41029.87}, {'field': 'count', 'old_value': 973, 'new_value': 1054}, {'field': 'instoreAmount', 'old_value': 20489.7, 'new_value': 21695.03}, {'field': 'instoreCount', 'old_value': 518, 'new_value': 556}, {'field': 'onlineAmount', 'old_value': 18421.95, 'new_value': 20130.36}, {'field': 'onlineCount', 'old_value': 455, 'new_value': 498}]
2025-06-11 08:11:54,278 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:54,700 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM9T
2025-06-11 08:11:54,700 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_2025-06, 变更字段: [{'field': 'amount', 'old_value': 2224.83, 'new_value': 2511.22}, {'field': 'count', 'old_value': 107, 'new_value': 116}, {'field': 'onlineAmount', 'old_value': 2278.9, 'new_value': 2565.29}, {'field': 'onlineCount', 'old_value': 107, 'new_value': 116}]
2025-06-11 08:11:54,700 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:55,263 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM8T
2025-06-11 08:11:55,263 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 40283.97, 'new_value': 46840.48}, {'field': 'dailyBillAmount', 'old_value': 40283.97, 'new_value': 46840.48}, {'field': 'amount', 'old_value': 2268.03, 'new_value': 2726.58}, {'field': 'count', 'old_value': 87, 'new_value': 107}, {'field': 'instoreAmount', 'old_value': 2268.03, 'new_value': 2726.58}, {'field': 'instoreCount', 'old_value': 87, 'new_value': 107}]
2025-06-11 08:11:55,263 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:55,700 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMMT
2025-06-11 08:11:55,700 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISLI3BD9P7AV8LHQQGIDR001EK4_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 53805.8, 'new_value': 62886.8}, {'field': 'dailyBillAmount', 'old_value': 53805.8, 'new_value': 62886.8}, {'field': 'amount', 'old_value': 52571.0, 'new_value': 61638.0}, {'field': 'count', 'old_value': 92, 'new_value': 104}, {'field': 'instoreAmount', 'old_value': 52571.0, 'new_value': 61638.0}, {'field': 'instoreCount', 'old_value': 92, 'new_value': 104}]
2025-06-11 08:11:55,700 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:56,122 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM7T
2025-06-11 08:11:56,122 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPK0KE3MN6E7AERKQ83JB001UND_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 64346.94, 'new_value': 72872.11}, {'field': 'dailyBillAmount', 'old_value': 60729.74, 'new_value': 69254.91}, {'field': 'amount', 'old_value': 44649.5, 'new_value': 50089.37}, {'field': 'count', 'old_value': 1398, 'new_value': 1526}, {'field': 'instoreAmount', 'old_value': 9237.59, 'new_value': 11746.16}, {'field': 'instoreCount', 'old_value': 210, 'new_value': 242}, {'field': 'onlineAmount', 'old_value': 35646.84, 'new_value': 38638.94}, {'field': 'onlineCount', 'old_value': 1188, 'new_value': 1284}]
2025-06-11 08:11:56,122 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:56,575 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM6T
2025-06-11 08:11:56,575 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPGJC2SUF6E7AERKQ83J8001UNA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 124555.76, 'new_value': 141521.56}, {'field': 'amount', 'old_value': 124555.76, 'new_value': 141521.56}, {'field': 'count', 'old_value': 1317, 'new_value': 1534}, {'field': 'instoreAmount', 'old_value': 116399.3, 'new_value': 132181.4}, {'field': 'instoreCount', 'old_value': 1041, 'new_value': 1219}, {'field': 'onlineAmount', 'old_value': 9293.37, 'new_value': 10947.42}, {'field': 'onlineCount', 'old_value': 276, 'new_value': 315}]
2025-06-11 08:11:56,575 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:57,028 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM5T
2025-06-11 08:11:57,028 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPC0VTE5M6E7AERKQ83J5001UN7_2025-06, 变更字段: [{'field': 'amount', 'old_value': 6796.13, 'new_value': 7864.91}, {'field': 'count', 'old_value': 350, 'new_value': 398}, {'field': 'onlineAmount', 'old_value': 6854.76, 'new_value': 7923.54}, {'field': 'onlineCount', 'old_value': 350, 'new_value': 398}]
2025-06-11 08:11:57,028 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:57,434 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMQT
2025-06-11 08:11:57,434 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 127085.16, 'new_value': 137661.46}, {'field': 'dailyBillAmount', 'old_value': 127085.16, 'new_value': 137661.46}, {'field': 'amount', 'old_value': 130460.27, 'new_value': 140936.33}, {'field': 'count', 'old_value': 3669, 'new_value': 3996}, {'field': 'instoreAmount', 'old_value': 121547.11, 'new_value': 131392.42}, {'field': 'instoreCount', 'old_value': 3160, 'new_value': 3431}, {'field': 'onlineAmount', 'old_value': 11003.43, 'new_value': 12136.23}, {'field': 'onlineCount', 'old_value': 509, 'new_value': 565}]
2025-06-11 08:11:57,434 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:57,872 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMPT
2025-06-11 08:11:57,872 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_C207460918D74AAAB2E154B47B74F863_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 26826.4, 'new_value': 29312.87}, {'field': 'amount', 'old_value': 26826.4, 'new_value': 29312.87}, {'field': 'count', 'old_value': 255, 'new_value': 285}, {'field': 'instoreAmount', 'old_value': 27142.56, 'new_value': 29629.03}, {'field': 'instoreCount', 'old_value': 255, 'new_value': 285}]
2025-06-11 08:11:57,872 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:58,278 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMOT
2025-06-11 08:11:58,278 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7296.84, 'new_value': 7960.3}, {'field': 'amount', 'old_value': 7296.84, 'new_value': 7960.3}, {'field': 'count', 'old_value': 156, 'new_value': 170}, {'field': 'instoreAmount', 'old_value': 7306.62, 'new_value': 7970.08}, {'field': 'instoreCount', 'old_value': 156, 'new_value': 170}]
2025-06-11 08:11:58,278 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:58,731 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMNT
2025-06-11 08:11:58,731 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 47092.58, 'new_value': 52287.03}, {'field': 'dailyBillAmount', 'old_value': 47092.58, 'new_value': 52287.03}, {'field': 'amount', 'old_value': 38950.07, 'new_value': 48951.18}, {'field': 'count', 'old_value': 45, 'new_value': 56}, {'field': 'instoreAmount', 'old_value': 41565.15, 'new_value': 51345.05}, {'field': 'instoreCount', 'old_value': 39, 'new_value': 49}, {'field': 'onlineAmount', 'old_value': 769.72, 'new_value': 991.73}, {'field': 'onlineCount', 'old_value': 6, 'new_value': 7}]
2025-06-11 08:11:58,731 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:59,137 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMKT
2025-06-11 08:11:59,137 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS34G4B697AV8LHQQGIDL001EJU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 263326.4, 'new_value': 293759.7}, {'field': 'amount', 'old_value': 263326.4, 'new_value': 293759.7}, {'field': 'count', 'old_value': 1619, 'new_value': 1813}, {'field': 'instoreAmount', 'old_value': 238192.01, 'new_value': 266205.1}, {'field': 'instoreCount', 'old_value': 858, 'new_value': 961}, {'field': 'onlineAmount', 'old_value': 25170.42, 'new_value': 27590.63}, {'field': 'onlineCount', 'old_value': 761, 'new_value': 852}]
2025-06-11 08:11:59,137 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:11:59,637 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMLT
2025-06-11 08:11:59,637 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 262324.92, 'new_value': 292163.79}, {'field': 'dailyBillAmount', 'old_value': 262324.92, 'new_value': 292163.79}, {'field': 'amount', 'old_value': 245335.26, 'new_value': 279773.48}, {'field': 'count', 'old_value': 1258, 'new_value': 1432}, {'field': 'instoreAmount', 'old_value': 223585.87, 'new_value': 256283.87}, {'field': 'instoreCount', 'old_value': 1023, 'new_value': 1169}, {'field': 'onlineAmount', 'old_value': 22131.3, 'new_value': 23905.71}, {'field': 'onlineCount', 'old_value': 235, 'new_value': 263}]
2025-06-11 08:11:59,637 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:00,153 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM4T
2025-06-11 08:12:00,153 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIP3GSKTFR6E7AERKQ83J2001UN4_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 194749.32, 'new_value': 210312.42}, {'field': 'dailyBillAmount', 'old_value': 194749.32, 'new_value': 210312.42}, {'field': 'amount', 'old_value': 18442.7, 'new_value': 20996.3}, {'field': 'count', 'old_value': 97, 'new_value': 107}, {'field': 'instoreAmount', 'old_value': 18442.7, 'new_value': 20996.3}, {'field': 'instoreCount', 'old_value': 97, 'new_value': 107}]
2025-06-11 08:12:00,153 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:00,606 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMFT
2025-06-11 08:12:00,606 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 80309.26, 'new_value': 88686.45}, {'field': 'dailyBillAmount', 'old_value': 80309.26, 'new_value': 88686.45}, {'field': 'amount', 'old_value': 51488.11, 'new_value': 56784.37}, {'field': 'count', 'old_value': 1344, 'new_value': 1498}, {'field': 'instoreAmount', 'old_value': 34559.87, 'new_value': 38356.37}, {'field': 'instoreCount', 'old_value': 689, 'new_value': 763}, {'field': 'onlineAmount', 'old_value': 24273.14, 'new_value': 26732.9}, {'field': 'onlineCount', 'old_value': 655, 'new_value': 735}]
2025-06-11 08:12:00,606 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:01,059 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMBT
2025-06-11 08:12:01,059 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQG1BD1C36E7AERKQ83JN001UNP_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 52944.48, 'new_value': 60952.44}, {'field': 'amount', 'old_value': 52944.48, 'new_value': 60952.44}, {'field': 'count', 'old_value': 2577, 'new_value': 2950}, {'field': 'instoreAmount', 'old_value': 53742.43, 'new_value': 61867.74}, {'field': 'instoreCount', 'old_value': 2577, 'new_value': 2950}]
2025-06-11 08:12:01,059 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:01,512 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMCT
2025-06-11 08:12:01,512 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 20752.82, 'new_value': 23475.54}, {'field': 'dailyBillAmount', 'old_value': 20752.82, 'new_value': 23475.54}, {'field': 'amount', 'old_value': 13234.65, 'new_value': 15119.35}, {'field': 'count', 'old_value': 624, 'new_value': 714}, {'field': 'instoreAmount', 'old_value': 5606.28, 'new_value': 6326.58}, {'field': 'instoreCount', 'old_value': 178, 'new_value': 200}, {'field': 'onlineAmount', 'old_value': 7679.56, 'new_value': 8843.96}, {'field': 'onlineCount', 'old_value': 446, 'new_value': 514}]
2025-06-11 08:12:01,512 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:01,934 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMDT
2025-06-11 08:12:01,934 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQTRHD5AN6E7AERKQ83JT001UNV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 23590.52, 'new_value': 26430.25}, {'field': 'amount', 'old_value': 23590.52, 'new_value': 26430.25}, {'field': 'count', 'old_value': 773, 'new_value': 873}, {'field': 'instoreAmount', 'old_value': 11513.31, 'new_value': 12667.39}, {'field': 'instoreCount', 'old_value': 488, 'new_value': 549}, {'field': 'onlineAmount', 'old_value': 12185.26, 'new_value': 13870.91}, {'field': 'onlineCount', 'old_value': 285, 'new_value': 324}]
2025-06-11 08:12:01,934 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:02,403 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMET
2025-06-11 08:12:02,403 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 14746.98, 'new_value': 16309.67}, {'field': 'amount', 'old_value': 14746.98, 'new_value': 16309.67}, {'field': 'count', 'old_value': 375, 'new_value': 419}, {'field': 'instoreAmount', 'old_value': 11765.6, 'new_value': 13093.3}, {'field': 'instoreCount', 'old_value': 312, 'new_value': 350}, {'field': 'onlineAmount', 'old_value': 3241.38, 'new_value': 3564.57}, {'field': 'onlineCount', 'old_value': 63, 'new_value': 69}]
2025-06-11 08:12:02,419 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:02,997 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMYT
2025-06-11 08:12:02,997 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE3ODLGMBD6AJB6QM8HA8A0011R8_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 96261.85, 'new_value': 99946.97}, {'field': 'dailyBillAmount', 'old_value': 96261.85, 'new_value': 99946.97}, {'field': 'amount', 'old_value': 101127.6, 'new_value': 105749.6}, {'field': 'count', 'old_value': 734, 'new_value': 778}, {'field': 'instoreAmount', 'old_value': 101801.6, 'new_value': 106423.6}, {'field': 'instoreCount', 'old_value': 734, 'new_value': 778}]
2025-06-11 08:12:02,997 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:03,466 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMXT
2025-06-11 08:12:03,466 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 132270.67, 'new_value': 148036.02}, {'field': 'dailyBillAmount', 'old_value': 132270.67, 'new_value': 148036.02}, {'field': 'amount', 'old_value': 212792.03, 'new_value': 236688.33}, {'field': 'count', 'old_value': 362, 'new_value': 409}, {'field': 'instoreAmount', 'old_value': 210062.63, 'new_value': 233880.63}, {'field': 'instoreCount', 'old_value': 347, 'new_value': 393}, {'field': 'onlineAmount', 'old_value': 2729.4, 'new_value': 2807.7}, {'field': 'onlineCount', 'old_value': 15, 'new_value': 16}]
2025-06-11 08:12:03,466 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:03,997 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMWT
2025-06-11 08:12:03,997 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2PLB45826AJB6QM8HA7Q0011QO_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 323145.33, 'new_value': 357390.51}, {'field': 'dailyBillAmount', 'old_value': 323145.33, 'new_value': 357390.51}, {'field': 'amount', 'old_value': 359745.15, 'new_value': 393990.33}, {'field': 'count', 'old_value': 1565, 'new_value': 1728}, {'field': 'instoreAmount', 'old_value': 359745.15, 'new_value': 393990.33}, {'field': 'instoreCount', 'old_value': 1565, 'new_value': 1728}]
2025-06-11 08:12:03,997 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:04,481 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMVT
2025-06-11 08:12:04,481 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 100682.95, 'new_value': 109507.27}, {'field': 'dailyBillAmount', 'old_value': 100682.95, 'new_value': 109507.27}, {'field': 'amount', 'old_value': 99621.24, 'new_value': 108114.3}, {'field': 'count', 'old_value': 519, 'new_value': 569}, {'field': 'instoreAmount', 'old_value': 94609.6, 'new_value': 103084.3}, {'field': 'instoreCount', 'old_value': 435, 'new_value': 480}, {'field': 'onlineAmount', 'old_value': 6007.08, 'new_value': 6296.7}, {'field': 'onlineCount', 'old_value': 84, 'new_value': 89}]
2025-06-11 08:12:04,481 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:04,887 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMUT
2025-06-11 08:12:04,887 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE29O5UH0D6AJB6QM8HA7I0011QG_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 631753.9, 'new_value': 709478.13}, {'field': 'dailyBillAmount', 'old_value': 631753.9, 'new_value': 709478.13}, {'field': 'amount', 'old_value': 548409.0, 'new_value': 620432.0}, {'field': 'count', 'old_value': 1304, 'new_value': 1475}, {'field': 'instoreAmount', 'old_value': 577678.0, 'new_value': 651812.0}, {'field': 'instoreCount', 'old_value': 1304, 'new_value': 1475}]
2025-06-11 08:12:04,887 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:05,372 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMST
2025-06-11 08:12:05,372 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD2F2MCTBD6AJB6QM8HA650011P3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 403077.1, 'new_value': 444087.1}, {'field': 'amount', 'old_value': 403077.1, 'new_value': 444087.1}, {'field': 'count', 'old_value': 1290, 'new_value': 1423}, {'field': 'instoreAmount', 'old_value': 405022.1, 'new_value': 446032.1}, {'field': 'instoreCount', 'old_value': 1290, 'new_value': 1423}]
2025-06-11 08:12:05,372 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:05,794 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMTT
2025-06-11 08:12:05,794 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 331977.26, 'new_value': 370909.7}, {'field': 'dailyBillAmount', 'old_value': 331977.26, 'new_value': 370909.7}, {'field': 'amount', 'old_value': 232642.42, 'new_value': 257148.18}, {'field': 'count', 'old_value': 895, 'new_value': 992}, {'field': 'instoreAmount', 'old_value': 228116.45, 'new_value': 251430.99}, {'field': 'instoreCount', 'old_value': 532, 'new_value': 582}, {'field': 'onlineAmount', 'old_value': 10924.4, 'new_value': 12197.7}, {'field': 'onlineCount', 'old_value': 363, 'new_value': 410}]
2025-06-11 08:12:05,794 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:06,294 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMRT
2025-06-11 08:12:06,294 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD0GVFB5C86AJB6QM8HA590011O7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 232672.47, 'new_value': 269432.79}, {'field': 'dailyBillAmount', 'old_value': 232672.47, 'new_value': 269432.79}, {'field': 'amount', 'old_value': 220689.31, 'new_value': 251595.04}, {'field': 'count', 'old_value': 645, 'new_value': 726}, {'field': 'instoreAmount', 'old_value': 224464.55, 'new_value': 258145.95}, {'field': 'instoreCount', 'old_value': 524, 'new_value': 590}, {'field': 'onlineAmount', 'old_value': 3291.12, 'new_value': 3594.37}, {'field': 'onlineCount', 'old_value': 121, 'new_value': 136}]
2025-06-11 08:12:06,341 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:06,825 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMPW
2025-06-11 08:12:06,825 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_A48FB8F8F66644F59454F3E73DFCEB92_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 64541.86, 'new_value': 71518.49}, {'field': 'dailyBillAmount', 'old_value': 64541.86, 'new_value': 71518.49}, {'field': 'amount', 'old_value': 68032.55, 'new_value': 75652.55}, {'field': 'count', 'old_value': 448, 'new_value': 502}, {'field': 'instoreAmount', 'old_value': 64331.0, 'new_value': 71580.0}, {'field': 'instoreCount', 'old_value': 388, 'new_value': 436}, {'field': 'onlineAmount', 'old_value': 3733.55, 'new_value': 4131.55}, {'field': 'onlineCount', 'old_value': 60, 'new_value': 66}]
2025-06-11 08:12:06,825 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:07,247 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMFU
2025-06-11 08:12:07,247 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERL0RJ5BM42F6DB81RHB2001P7A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 46446.8, 'new_value': 49898.8}, {'field': 'dailyBillAmount', 'old_value': 46446.8, 'new_value': 49898.8}, {'field': 'amount', 'old_value': 55908.0, 'new_value': 60571.0}, {'field': 'count', 'old_value': 234, 'new_value': 250}, {'field': 'instoreAmount', 'old_value': 55908.0, 'new_value': 60571.0}, {'field': 'instoreCount', 'old_value': 234, 'new_value': 250}]
2025-06-11 08:12:07,262 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:07,778 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMGU
2025-06-11 08:12:07,778 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERRPAJGP042F6DB81RHB6001P7E_2025-06, 变更字段: [{'field': 'amount', 'old_value': 20220.0, 'new_value': 23211.2}, {'field': 'count', 'old_value': 107, 'new_value': 118}, {'field': 'instoreAmount', 'old_value': 20220.0, 'new_value': 23211.2}, {'field': 'instoreCount', 'old_value': 107, 'new_value': 118}]
2025-06-11 08:12:07,778 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:08,247 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMHU
2025-06-11 08:12:08,247 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 36691.0, 'new_value': 40489.0}, {'field': 'amount', 'old_value': 36691.0, 'new_value': 40489.0}, {'field': 'count', 'old_value': 399, 'new_value': 447}, {'field': 'instoreAmount', 'old_value': 36691.0, 'new_value': 40489.0}, {'field': 'instoreCount', 'old_value': 399, 'new_value': 447}]
2025-06-11 08:12:08,247 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:08,731 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMIU
2025-06-11 08:12:08,731 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8314.16, 'new_value': 11519.96}, {'field': 'dailyBillAmount', 'old_value': 8314.16, 'new_value': 11519.96}, {'field': 'amount', 'old_value': 918.84, 'new_value': 1435.66}, {'field': 'count', 'old_value': 30, 'new_value': 65}, {'field': 'instoreAmount', 'old_value': 1157.34, 'new_value': 1691.86}, {'field': 'instoreCount', 'old_value': 30, 'new_value': 65}]
2025-06-11 08:12:08,731 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:09,184 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMMW
2025-06-11 08:12:09,184 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6D1F9FC749FA44C6B70CA818C3E7FB77_2025-06, 变更字段: [{'field': 'amount', 'old_value': 15252.0, 'new_value': 16131.0}, {'field': 'count', 'old_value': 75, 'new_value': 81}, {'field': 'instoreAmount', 'old_value': 15252.0, 'new_value': 16131.0}, {'field': 'instoreCount', 'old_value': 75, 'new_value': 81}]
2025-06-11 08:12:09,184 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:09,653 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMNW
2025-06-11 08:12:09,653 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6FEB527E4B354363BD1420A3FF0FB3E3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 26237.92, 'new_value': 28779.8}, {'field': 'dailyBillAmount', 'old_value': 26237.92, 'new_value': 28779.8}, {'field': 'amount', 'old_value': 19952.65, 'new_value': 21970.35}, {'field': 'count', 'old_value': 624, 'new_value': 685}, {'field': 'instoreAmount', 'old_value': 19585.65, 'new_value': 21576.55}, {'field': 'instoreCount', 'old_value': 606, 'new_value': 665}, {'field': 'onlineAmount', 'old_value': 367.0, 'new_value': 393.8}, {'field': 'onlineCount', 'old_value': 18, 'new_value': 20}]
2025-06-11 08:12:09,653 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:10,075 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMOW
2025-06-11 08:12:10,075 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_7987833E6DE549FCBAC0AAF7A1D27E61_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 33350.08, 'new_value': 35681.73}, {'field': 'dailyBillAmount', 'old_value': 33350.08, 'new_value': 35681.73}, {'field': 'amount', 'old_value': 32642.3, 'new_value': 34891.8}, {'field': 'count', 'old_value': 160, 'new_value': 172}, {'field': 'instoreAmount', 'old_value': 32339.6, 'new_value': 34589.1}, {'field': 'instoreCount', 'old_value': 148, 'new_value': 160}]
2025-06-11 08:12:10,075 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:10,528 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMZT
2025-06-11 08:12:10,528 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_101F34500A0D43DF833463DEFB95F423_2025-06, 变更字段: [{'field': 'amount', 'old_value': 67322.8, 'new_value': 73616.5}, {'field': 'count', 'old_value': 445, 'new_value': 485}, {'field': 'instoreAmount', 'old_value': 68430.0, 'new_value': 74658.0}, {'field': 'instoreCount', 'old_value': 430, 'new_value': 468}, {'field': 'onlineAmount', 'old_value': 750.8, 'new_value': 816.5}, {'field': 'onlineCount', 'old_value': 15, 'new_value': 17}]
2025-06-11 08:12:10,528 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:11,012 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM0U
2025-06-11 08:12:11,012 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_17590B062D954DB088AC6EE572EFECE9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 10281.4, 'new_value': 13947.9}, {'field': 'dailyBillAmount', 'old_value': 10281.4, 'new_value': 13947.9}]
2025-06-11 08:12:11,012 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:11,591 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM1U
2025-06-11 08:12:11,591 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_2025-06, 变更字段: [{'field': 'amount', 'old_value': 31998.44, 'new_value': 34450.06}, {'field': 'count', 'old_value': 1767, 'new_value': 1900}, {'field': 'instoreAmount', 'old_value': 4452.04, 'new_value': 4861.86}, {'field': 'instoreCount', 'old_value': 396, 'new_value': 432}, {'field': 'onlineAmount', 'old_value': 28983.5, 'new_value': 31092.4}, {'field': 'onlineCount', 'old_value': 1371, 'new_value': 1468}]
2025-06-11 08:12:11,591 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:12,231 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM2U
2025-06-11 08:12:12,231 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 78275.66, 'new_value': 85310.03}, {'field': 'amount', 'old_value': 78274.82, 'new_value': 85309.19}, {'field': 'count', 'old_value': 1434, 'new_value': 1579}, {'field': 'instoreAmount', 'old_value': 64242.86, 'new_value': 70102.66}, {'field': 'instoreCount', 'old_value': 1187, 'new_value': 1311}, {'field': 'onlineAmount', 'old_value': 14032.8, 'new_value': 15207.37}, {'field': 'onlineCount', 'old_value': 247, 'new_value': 268}]
2025-06-11 08:12:12,231 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:12,716 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM3U
2025-06-11 08:12:12,716 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKLLRRBCQ42F6DB81RH73001P3B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 13201.6, 'new_value': 13913.0}, {'field': 'dailyBillAmount', 'old_value': 7769.0, 'new_value': 8269.0}, {'field': 'amount', 'old_value': 13201.6, 'new_value': 13913.0}, {'field': 'count', 'old_value': 78, 'new_value': 86}, {'field': 'instoreAmount', 'old_value': 13201.6, 'new_value': 13913.0}, {'field': 'instoreCount', 'old_value': 78, 'new_value': 86}]
2025-06-11 08:12:12,716 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:13,231 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM4U
2025-06-11 08:12:13,231 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5400.0, 'new_value': 5958.0}, {'field': 'dailyBillAmount', 'old_value': 5400.0, 'new_value': 5958.0}, {'field': 'amount', 'old_value': 9572.7, 'new_value': 10264.6}, {'field': 'count', 'old_value': 90, 'new_value': 99}, {'field': 'instoreAmount', 'old_value': 9740.7, 'new_value': 10432.6}, {'field': 'instoreCount', 'old_value': 90, 'new_value': 99}]
2025-06-11 08:12:13,231 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:13,700 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMEU
2025-06-11 08:12:13,700 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERF6AAOTL42F6DB81RHAU001P76_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 44478.64, 'new_value': 49823.3}, {'field': 'dailyBillAmount', 'old_value': 44478.64, 'new_value': 49823.3}, {'field': 'amount', 'old_value': 32435.11, 'new_value': 36551.91}, {'field': 'count', 'old_value': 296, 'new_value': 330}, {'field': 'instoreAmount', 'old_value': 32435.11, 'new_value': 36551.91}, {'field': 'instoreCount', 'old_value': 296, 'new_value': 330}]
2025-06-11 08:12:13,700 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:14,153 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM6U
2025-06-11 08:12:14,153 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 84535.1, 'new_value': 92437.7}, {'field': 'dailyBillAmount', 'old_value': 84535.1, 'new_value': 92437.7}, {'field': 'amount', 'old_value': 63129.33, 'new_value': 68809.28}, {'field': 'count', 'old_value': 2102, 'new_value': 2303}, {'field': 'instoreAmount', 'old_value': 59708.99, 'new_value': 65041.33}, {'field': 'instoreCount', 'old_value': 2004, 'new_value': 2195}, {'field': 'onlineAmount', 'old_value': 3775.34, 'new_value': 4122.95}, {'field': 'onlineCount', 'old_value': 98, 'new_value': 108}]
2025-06-11 08:12:14,153 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:14,637 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM7U
2025-06-11 08:12:14,637 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMMCI75GD42F6DB81RH81001P49_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 20104.1, 'new_value': 21984.7}, {'field': 'dailyBillAmount', 'old_value': 20104.1, 'new_value': 21984.7}, {'field': 'amount', 'old_value': 18629.4, 'new_value': 20510.0}, {'field': 'count', 'old_value': 115, 'new_value': 123}, {'field': 'instoreAmount', 'old_value': 19156.9, 'new_value': 21037.5}, {'field': 'instoreCount', 'old_value': 115, 'new_value': 123}]
2025-06-11 08:12:14,637 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:15,106 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM8U
2025-06-11 08:12:15,106 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO2283NPC42F6DB81RH8Q001P52_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 22257.47, 'new_value': 24901.89}, {'field': 'dailyBillAmount', 'old_value': 22257.47, 'new_value': 24901.89}]
2025-06-11 08:12:15,106 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:15,606 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM9U
2025-06-11 08:12:15,606 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 17954.21, 'new_value': 20194.55}, {'field': 'amount', 'old_value': 17952.91, 'new_value': 20192.67}, {'field': 'count', 'old_value': 1105, 'new_value': 1251}, {'field': 'instoreAmount', 'old_value': 18137.17, 'new_value': 20389.99}, {'field': 'instoreCount', 'old_value': 1105, 'new_value': 1251}]
2025-06-11 08:12:15,606 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:16,059 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMAU
2025-06-11 08:12:16,059 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEOTOKMGDD42F6DB81RH9E001P5M_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 27681.74, 'new_value': 30802.15}, {'field': 'dailyBillAmount', 'old_value': 27681.74, 'new_value': 30802.15}, {'field': 'amount', 'old_value': 28203.05, 'new_value': 31374.03}, {'field': 'count', 'old_value': 1412, 'new_value': 1577}, {'field': 'instoreAmount', 'old_value': 25597.7, 'new_value': 28588.2}, {'field': 'instoreCount', 'old_value': 1268, 'new_value': 1423}, {'field': 'onlineAmount', 'old_value': 2804.14, 'new_value': 2984.62}, {'field': 'onlineCount', 'old_value': 144, 'new_value': 154}]
2025-06-11 08:12:16,075 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:16,528 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMBU
2025-06-11 08:12:16,528 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEPVAL5TUK42F6DB81RHA2001P6A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 21513.15, 'new_value': 23178.2}, {'field': 'amount', 'old_value': 21513.15, 'new_value': 23178.2}, {'field': 'count', 'old_value': 1012, 'new_value': 1097}, {'field': 'instoreAmount', 'old_value': 12447.87, 'new_value': 13374.13}, {'field': 'instoreCount', 'old_value': 615, 'new_value': 666}, {'field': 'onlineAmount', 'old_value': 9103.1, 'new_value': 9841.89}, {'field': 'onlineCount', 'old_value': 397, 'new_value': 431}]
2025-06-11 08:12:16,528 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:17,012 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMCU
2025-06-11 08:12:17,012 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQ5682UCT42F6DB81RHA6001P6E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 16549.96, 'new_value': 18380.6}, {'field': 'dailyBillAmount', 'old_value': 16549.96, 'new_value': 18380.6}, {'field': 'amount', 'old_value': 11470.51, 'new_value': 12800.89}, {'field': 'count', 'old_value': 434, 'new_value': 483}, {'field': 'instoreAmount', 'old_value': 11616.91, 'new_value': 12947.29}, {'field': 'instoreCount', 'old_value': 434, 'new_value': 483}]
2025-06-11 08:12:17,012 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:17,622 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMDU
2025-06-11 08:12:17,622 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 24235.03, 'new_value': 26841.53}, {'field': 'amount', 'old_value': 24234.75, 'new_value': 26841.01}, {'field': 'count', 'old_value': 1426, 'new_value': 1562}, {'field': 'instoreAmount', 'old_value': 5052.01, 'new_value': 5545.53}, {'field': 'instoreCount', 'old_value': 249, 'new_value': 271}, {'field': 'onlineAmount', 'old_value': 19764.19, 'new_value': 21907.69}, {'field': 'onlineCount', 'old_value': 1177, 'new_value': 1291}]
2025-06-11 08:12:17,622 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-11 08:12:18,247 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM5U
2025-06-11 08:12:18,247 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEM2RU37BD42F6DB81RH7L001P3T_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 20256.0, 'new_value': 20685.0}, {'field': 'dailyBillAmount', 'old_value': 20256.0, 'new_value': 20685.0}]
2025-06-11 08:12:18,247 - WARNING - 批量插入月度数据失败，将在 2 秒后重试 (尝试 1/3): Object of type Decimal is not JSON serializable
2025-06-11 08:12:20,262 - WARNING - 批量插入月度数据失败，将在 4 秒后重试 (尝试 2/3): Object of type Decimal is not JSON serializable
2025-06-11 08:12:24,278 - ERROR - 批量插入月度数据失败达到最大重试次数，跳过当前批次: Object of type Decimal is not JSON serializable
2025-06-11 08:12:27,294 - INFO - 批量插入月度数据完成: 总计 1 条，成功 0 条，失败 1 条
2025-06-11 08:12:27,294 - INFO - 批量插入月销售数据完成，共 1 条记录
2025-06-11 08:12:27,294 - INFO - 月销售数据同步完成！更新: 202 条，插入: 1 条，错误: 0 条，跳过: 1201 条
2025-06-11 08:12:27,294 - INFO - 综合数据同步流程完成！
2025-06-11 08:12:27,340 - INFO - 综合数据同步完成
2025-06-11 08:12:27,340 - INFO - MySQL数据库连接已关闭
2025-06-11 08:12:27,340 - INFO - ==================================================
2025-06-11 08:12:27,340 - INFO - 程序退出
2025-06-11 08:12:27,340 - INFO - ==================================================
