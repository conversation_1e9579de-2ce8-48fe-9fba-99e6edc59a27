2025-05-25 00:00:02,065 - INFO - =================使用默认全量同步=============
2025-05-25 00:00:03,533 - INFO - MySQL查询成功，共获取 3298 条记录
2025-05-25 00:00:03,533 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-25 00:00:03,565 - INFO - 开始处理日期: 2025-01
2025-05-25 00:00:03,565 - INFO - Request Parameters - Page 1:
2025-05-25 00:00:03,565 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:00:03,565 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:00:04,986 - INFO - Response - Page 1:
2025-05-25 00:00:05,190 - INFO - 第 1 页获取到 100 条记录
2025-05-25 00:00:05,190 - INFO - Request Parameters - Page 2:
2025-05-25 00:00:05,190 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:00:05,190 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:00:05,705 - INFO - Response - Page 2:
2025-05-25 00:00:05,908 - INFO - 第 2 页获取到 100 条记录
2025-05-25 00:00:05,908 - INFO - Request Parameters - Page 3:
2025-05-25 00:00:05,908 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:00:05,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:00:06,752 - INFO - Response - Page 3:
2025-05-25 00:00:06,955 - INFO - 第 3 页获取到 100 条记录
2025-05-25 00:00:06,955 - INFO - Request Parameters - Page 4:
2025-05-25 00:00:06,955 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:00:06,955 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:00:07,533 - INFO - Response - Page 4:
2025-05-25 00:00:07,736 - INFO - 第 4 页获取到 100 条记录
2025-05-25 00:00:07,736 - INFO - Request Parameters - Page 5:
2025-05-25 00:00:07,736 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:00:07,736 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:00:08,221 - INFO - Response - Page 5:
2025-05-25 00:00:08,424 - INFO - 第 5 页获取到 100 条记录
2025-05-25 00:00:08,424 - INFO - Request Parameters - Page 6:
2025-05-25 00:00:08,424 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:00:08,424 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:00:08,955 - INFO - Response - Page 6:
2025-05-25 00:00:09,158 - INFO - 第 6 页获取到 100 条记录
2025-05-25 00:00:09,158 - INFO - Request Parameters - Page 7:
2025-05-25 00:00:09,158 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:00:09,158 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:00:09,815 - INFO - Response - Page 7:
2025-05-25 00:00:10,018 - INFO - 第 7 页获取到 82 条记录
2025-05-25 00:00:10,018 - INFO - 查询完成，共获取到 682 条记录
2025-05-25 00:00:10,018 - INFO - 获取到 682 条表单数据
2025-05-25 00:00:10,018 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-25 00:00:10,033 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 00:00:10,033 - INFO - 开始处理日期: 2025-02
2025-05-25 00:00:10,033 - INFO - Request Parameters - Page 1:
2025-05-25 00:00:10,049 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:00:10,049 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:00:10,549 - INFO - Response - Page 1:
2025-05-25 00:00:10,752 - INFO - 第 1 页获取到 100 条记录
2025-05-25 00:00:10,752 - INFO - Request Parameters - Page 2:
2025-05-25 00:00:10,752 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:00:10,752 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:00:11,236 - INFO - Response - Page 2:
2025-05-25 00:00:11,440 - INFO - 第 2 页获取到 100 条记录
2025-05-25 00:00:11,440 - INFO - Request Parameters - Page 3:
2025-05-25 00:00:11,440 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:00:11,440 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:00:11,893 - INFO - Response - Page 3:
2025-05-25 00:00:12,096 - INFO - 第 3 页获取到 100 条记录
2025-05-25 00:00:12,096 - INFO - Request Parameters - Page 4:
2025-05-25 00:00:12,096 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:00:12,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:00:12,518 - INFO - Response - Page 4:
2025-05-25 00:00:12,721 - INFO - 第 4 页获取到 100 条记录
2025-05-25 00:00:12,721 - INFO - Request Parameters - Page 5:
2025-05-25 00:00:12,721 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:00:12,721 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:00:13,174 - INFO - Response - Page 5:
2025-05-25 00:00:13,377 - INFO - 第 5 页获取到 100 条记录
2025-05-25 00:00:13,377 - INFO - Request Parameters - Page 6:
2025-05-25 00:00:13,377 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:00:13,377 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:00:13,846 - INFO - Response - Page 6:
2025-05-25 00:00:14,049 - INFO - 第 6 页获取到 100 条记录
2025-05-25 00:00:14,049 - INFO - Request Parameters - Page 7:
2025-05-25 00:00:14,049 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:00:14,049 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:00:14,518 - INFO - Response - Page 7:
2025-05-25 00:00:14,721 - INFO - 第 7 页获取到 70 条记录
2025-05-25 00:00:14,721 - INFO - 查询完成，共获取到 670 条记录
2025-05-25 00:00:14,721 - INFO - 获取到 670 条表单数据
2025-05-25 00:00:14,721 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-25 00:00:14,736 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 00:00:14,736 - INFO - 开始处理日期: 2025-03
2025-05-25 00:00:14,736 - INFO - Request Parameters - Page 1:
2025-05-25 00:00:14,736 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:00:14,736 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:00:15,236 - INFO - Response - Page 1:
2025-05-25 00:00:15,439 - INFO - 第 1 页获取到 100 条记录
2025-05-25 00:00:15,439 - INFO - Request Parameters - Page 2:
2025-05-25 00:00:15,439 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:00:15,439 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:00:16,064 - INFO - Response - Page 2:
2025-05-25 00:00:16,268 - INFO - 第 2 页获取到 100 条记录
2025-05-25 00:00:16,268 - INFO - Request Parameters - Page 3:
2025-05-25 00:00:16,268 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:00:16,268 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:00:16,752 - INFO - Response - Page 3:
2025-05-25 00:00:16,955 - INFO - 第 3 页获取到 100 条记录
2025-05-25 00:00:16,955 - INFO - Request Parameters - Page 4:
2025-05-25 00:00:16,955 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:00:16,955 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:00:17,439 - INFO - Response - Page 4:
2025-05-25 00:00:17,643 - INFO - 第 4 页获取到 100 条记录
2025-05-25 00:00:17,643 - INFO - Request Parameters - Page 5:
2025-05-25 00:00:17,643 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:00:17,643 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:00:18,174 - INFO - Response - Page 5:
2025-05-25 00:00:18,377 - INFO - 第 5 页获取到 100 条记录
2025-05-25 00:00:18,377 - INFO - Request Parameters - Page 6:
2025-05-25 00:00:18,377 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:00:18,377 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:00:18,908 - INFO - Response - Page 6:
2025-05-25 00:00:19,111 - INFO - 第 6 页获取到 100 条记录
2025-05-25 00:00:19,111 - INFO - Request Parameters - Page 7:
2025-05-25 00:00:19,111 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:00:19,111 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:00:19,596 - INFO - Response - Page 7:
2025-05-25 00:00:19,799 - INFO - 第 7 页获取到 61 条记录
2025-05-25 00:00:19,799 - INFO - 查询完成，共获取到 661 条记录
2025-05-25 00:00:19,799 - INFO - 获取到 661 条表单数据
2025-05-25 00:00:19,799 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-25 00:00:19,814 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 00:00:19,814 - INFO - 开始处理日期: 2025-04
2025-05-25 00:00:19,814 - INFO - Request Parameters - Page 1:
2025-05-25 00:00:19,814 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:00:19,814 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:00:20,424 - INFO - Response - Page 1:
2025-05-25 00:00:20,627 - INFO - 第 1 页获取到 100 条记录
2025-05-25 00:00:20,627 - INFO - Request Parameters - Page 2:
2025-05-25 00:00:20,627 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:00:20,627 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:00:21,127 - INFO - Response - Page 2:
2025-05-25 00:00:21,330 - INFO - 第 2 页获取到 100 条记录
2025-05-25 00:00:21,330 - INFO - Request Parameters - Page 3:
2025-05-25 00:00:21,330 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:00:21,330 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:00:21,799 - INFO - Response - Page 3:
2025-05-25 00:00:22,002 - INFO - 第 3 页获取到 100 条记录
2025-05-25 00:00:22,002 - INFO - Request Parameters - Page 4:
2025-05-25 00:00:22,002 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:00:22,002 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:00:22,518 - INFO - Response - Page 4:
2025-05-25 00:00:22,721 - INFO - 第 4 页获取到 100 条记录
2025-05-25 00:00:22,721 - INFO - Request Parameters - Page 5:
2025-05-25 00:00:22,721 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:00:22,721 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:00:23,283 - INFO - Response - Page 5:
2025-05-25 00:00:23,486 - INFO - 第 5 页获取到 100 条记录
2025-05-25 00:00:23,486 - INFO - Request Parameters - Page 6:
2025-05-25 00:00:23,486 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:00:23,486 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:00:23,939 - INFO - Response - Page 6:
2025-05-25 00:00:24,143 - INFO - 第 6 页获取到 100 条记录
2025-05-25 00:00:24,143 - INFO - Request Parameters - Page 7:
2025-05-25 00:00:24,143 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:00:24,143 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:00:24,533 - INFO - Response - Page 7:
2025-05-25 00:00:24,736 - INFO - 第 7 页获取到 56 条记录
2025-05-25 00:00:24,736 - INFO - 查询完成，共获取到 656 条记录
2025-05-25 00:00:24,736 - INFO - 获取到 656 条表单数据
2025-05-25 00:00:24,736 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-25 00:00:24,752 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 00:00:24,752 - INFO - 开始处理日期: 2025-05
2025-05-25 00:00:24,752 - INFO - Request Parameters - Page 1:
2025-05-25 00:00:24,752 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:00:24,752 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:00:25,299 - INFO - Response - Page 1:
2025-05-25 00:00:25,502 - INFO - 第 1 页获取到 100 条记录
2025-05-25 00:00:25,502 - INFO - Request Parameters - Page 2:
2025-05-25 00:00:25,502 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:00:25,502 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:00:25,986 - INFO - Response - Page 2:
2025-05-25 00:00:26,189 - INFO - 第 2 页获取到 100 条记录
2025-05-25 00:00:26,189 - INFO - Request Parameters - Page 3:
2025-05-25 00:00:26,189 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:00:26,189 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:00:26,643 - INFO - Response - Page 3:
2025-05-25 00:00:26,846 - INFO - 第 3 页获取到 100 条记录
2025-05-25 00:00:26,846 - INFO - Request Parameters - Page 4:
2025-05-25 00:00:26,846 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:00:26,846 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:00:27,518 - INFO - Response - Page 4:
2025-05-25 00:00:27,721 - INFO - 第 4 页获取到 100 条记录
2025-05-25 00:00:27,721 - INFO - Request Parameters - Page 5:
2025-05-25 00:00:27,721 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:00:27,721 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:00:28,189 - INFO - Response - Page 5:
2025-05-25 00:00:28,393 - INFO - 第 5 页获取到 100 条记录
2025-05-25 00:00:28,393 - INFO - Request Parameters - Page 6:
2025-05-25 00:00:28,393 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:00:28,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:00:28,830 - INFO - Response - Page 6:
2025-05-25 00:00:29,033 - INFO - 第 6 页获取到 100 条记录
2025-05-25 00:00:29,033 - INFO - Request Parameters - Page 7:
2025-05-25 00:00:29,033 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 00:00:29,033 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 00:00:29,408 - INFO - Response - Page 7:
2025-05-25 00:00:29,611 - INFO - 第 7 页获取到 29 条记录
2025-05-25 00:00:29,611 - INFO - 查询完成，共获取到 629 条记录
2025-05-25 00:00:29,611 - INFO - 获取到 629 条表单数据
2025-05-25 00:00:29,611 - INFO - 当前日期 2025-05 有 629 条MySQL数据需要处理
2025-05-25 00:00:29,611 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-25 00:00:30,064 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-25 00:00:30,064 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 109927.0, 'new_value': 112386.0}, {'field': 'offline_amount', 'old_value': 124788.28, 'new_value': 131039.28}, {'field': 'total_amount', 'old_value': 234715.28, 'new_value': 243425.28}, {'field': 'order_count', 'old_value': 5041, 'new_value': 5235}]
2025-05-25 00:00:30,064 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4F
2025-05-25 00:00:30,502 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4F
2025-05-25 00:00:30,502 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20186.1, 'new_value': 22130.6}, {'field': 'total_amount', 'old_value': 21178.1, 'new_value': 23122.6}, {'field': 'order_count', 'old_value': 2460, 'new_value': 2595}]
2025-05-25 00:00:30,502 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-25 00:00:30,846 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-25 00:00:30,846 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46764.2, 'new_value': 46845.5}, {'field': 'total_amount', 'old_value': 50724.2, 'new_value': 50805.5}, {'field': 'order_count', 'old_value': 359, 'new_value': 386}]
2025-05-25 00:00:30,846 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5F
2025-05-25 00:00:31,283 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5F
2025-05-25 00:00:31,283 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43957.0, 'new_value': 48690.0}, {'field': 'offline_amount', 'old_value': 162408.98, 'new_value': 175171.98}, {'field': 'total_amount', 'old_value': 206365.98, 'new_value': 223861.98}, {'field': 'order_count', 'old_value': 1393, 'new_value': 1531}]
2025-05-25 00:00:31,283 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7F
2025-05-25 00:00:31,830 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7F
2025-05-25 00:00:31,830 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23140.12, 'new_value': 24909.47}, {'field': 'offline_amount', 'old_value': 95613.86, 'new_value': 104034.56}, {'field': 'total_amount', 'old_value': 118753.98, 'new_value': 128944.03}, {'field': 'order_count', 'old_value': 1616, 'new_value': 1747}]
2025-05-25 00:00:31,830 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-25 00:00:32,330 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-25 00:00:32,330 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29668.2, 'new_value': 31110.2}, {'field': 'offline_amount', 'old_value': 152516.04, 'new_value': 162305.44}, {'field': 'total_amount', 'old_value': 182184.24, 'new_value': 193415.64}, {'field': 'order_count', 'old_value': 248, 'new_value': 252}]
2025-05-25 00:00:32,330 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-05-25 00:00:32,767 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-05-25 00:00:32,767 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12757.0, 'new_value': 14499.0}, {'field': 'total_amount', 'old_value': 12757.0, 'new_value': 14499.0}, {'field': 'order_count', 'old_value': 35, 'new_value': 38}]
2025-05-25 00:00:32,767 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9F
2025-05-25 00:00:33,189 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9F
2025-05-25 00:00:33,189 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23783.47, 'new_value': 24975.71}, {'field': 'offline_amount', 'old_value': 19188.5, 'new_value': 19869.21}, {'field': 'total_amount', 'old_value': 42971.97, 'new_value': 44844.92}, {'field': 'order_count', 'old_value': 2462, 'new_value': 2578}]
2025-05-25 00:00:33,189 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAF
2025-05-25 00:00:33,596 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAF
2025-05-25 00:00:33,596 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4694.26, 'new_value': 4996.04}, {'field': 'offline_amount', 'old_value': 103146.06, 'new_value': 108274.54}, {'field': 'total_amount', 'old_value': 107840.32, 'new_value': 113270.58}, {'field': 'order_count', 'old_value': 1764, 'new_value': 1853}]
2025-05-25 00:00:33,596 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-25 00:00:33,971 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-25 00:00:33,971 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48874.64, 'new_value': 50745.73}, {'field': 'offline_amount', 'old_value': 653021.81, 'new_value': 677118.46}, {'field': 'total_amount', 'old_value': 701896.45, 'new_value': 727864.19}, {'field': 'order_count', 'old_value': 2920, 'new_value': 3025}]
2025-05-25 00:00:33,971 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMBF
2025-05-25 00:00:34,392 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMBF
2025-05-25 00:00:34,392 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68720.24, 'new_value': 72012.86}, {'field': 'total_amount', 'old_value': 68720.24, 'new_value': 72012.86}, {'field': 'order_count', 'old_value': 3566, 'new_value': 3763}]
2025-05-25 00:00:34,392 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-25 00:00:34,767 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-25 00:00:34,767 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26883.57, 'new_value': 27626.57}, {'field': 'total_amount', 'old_value': 26883.57, 'new_value': 27626.57}, {'field': 'order_count', 'old_value': 154, 'new_value': 160}]
2025-05-25 00:00:34,767 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDF
2025-05-25 00:00:35,158 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDF
2025-05-25 00:00:35,158 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47053.6, 'new_value': 48984.6}, {'field': 'total_amount', 'old_value': 47053.6, 'new_value': 48984.6}, {'field': 'order_count', 'old_value': 203, 'new_value': 213}]
2025-05-25 00:00:35,158 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-25 00:00:35,596 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-25 00:00:35,596 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41811.01, 'new_value': 44545.73}, {'field': 'offline_amount', 'old_value': 83678.65, 'new_value': 89387.23}, {'field': 'total_amount', 'old_value': 125489.66, 'new_value': 133932.96}, {'field': 'order_count', 'old_value': 4608, 'new_value': 4891}]
2025-05-25 00:00:35,596 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-25 00:00:35,971 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-25 00:00:35,971 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86381.79, 'new_value': 92677.36}, {'field': 'total_amount', 'old_value': 86381.79, 'new_value': 92677.36}, {'field': 'order_count', 'old_value': 3315, 'new_value': 3548}]
2025-05-25 00:00:35,971 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-25 00:00:36,439 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-25 00:00:36,439 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9239.13, 'new_value': 10698.33}, {'field': 'total_amount', 'old_value': 22129.61, 'new_value': 23588.81}, {'field': 'order_count', 'old_value': 96, 'new_value': 101}]
2025-05-25 00:00:36,439 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-25 00:00:36,892 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-25 00:00:36,892 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53904.0, 'new_value': 54263.0}, {'field': 'total_amount', 'old_value': 53904.0, 'new_value': 54263.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 35}]
2025-05-25 00:00:36,892 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-25 00:00:37,314 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-25 00:00:37,314 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92667.1, 'new_value': 99282.8}, {'field': 'total_amount', 'old_value': 92667.1, 'new_value': 99282.8}, {'field': 'order_count', 'old_value': 283, 'new_value': 302}]
2025-05-25 00:00:37,314 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-25 00:00:37,674 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-25 00:00:37,674 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 177292.47, 'new_value': 190454.25}, {'field': 'offline_amount', 'old_value': 316156.35, 'new_value': 336220.08}, {'field': 'total_amount', 'old_value': 493448.82, 'new_value': 526674.33}, {'field': 'order_count', 'old_value': 13978, 'new_value': 14921}]
2025-05-25 00:00:37,674 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-25 00:00:38,080 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-25 00:00:38,080 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40999.0, 'new_value': 43603.0}, {'field': 'total_amount', 'old_value': 40999.0, 'new_value': 43603.0}, {'field': 'order_count', 'old_value': 109, 'new_value': 118}]
2025-05-25 00:00:38,080 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-25 00:00:38,486 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-25 00:00:38,486 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32542.0, 'new_value': 35741.0}, {'field': 'total_amount', 'old_value': 32542.0, 'new_value': 35741.0}, {'field': 'order_count', 'old_value': 102, 'new_value': 110}]
2025-05-25 00:00:38,486 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-05-25 00:00:38,955 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-05-25 00:00:38,955 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6024.0, 'new_value': 6404.0}, {'field': 'total_amount', 'old_value': 6024.0, 'new_value': 6404.0}, {'field': 'order_count', 'old_value': 30, 'new_value': 31}]
2025-05-25 00:00:38,955 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-25 00:00:39,346 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-25 00:00:39,346 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1638680.3, 'new_value': 1699753.57}, {'field': 'total_amount', 'old_value': 1638680.3, 'new_value': 1699753.57}, {'field': 'order_count', 'old_value': 13628, 'new_value': 14302}]
2025-05-25 00:00:39,346 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-25 00:00:39,736 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-25 00:00:39,736 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9257.06, 'new_value': 12650.36}, {'field': 'offline_amount', 'old_value': 79914.65, 'new_value': 85631.85}, {'field': 'total_amount', 'old_value': 89171.71, 'new_value': 98282.21}, {'field': 'order_count', 'old_value': 2473, 'new_value': 2675}]
2025-05-25 00:00:39,736 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-25 00:00:40,174 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-25 00:00:40,174 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9755.4, 'new_value': 10504.59}, {'field': 'offline_amount', 'old_value': 11176.58, 'new_value': 12091.83}, {'field': 'total_amount', 'old_value': 20931.98, 'new_value': 22596.42}, {'field': 'order_count', 'old_value': 1644, 'new_value': 1790}]
2025-05-25 00:00:40,174 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-25 00:00:40,611 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-25 00:00:40,611 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 82359.93, 'new_value': 85290.04}, {'field': 'offline_amount', 'old_value': 36774.89, 'new_value': 38431.03}, {'field': 'total_amount', 'old_value': 119134.82, 'new_value': 123721.07}, {'field': 'order_count', 'old_value': 7345, 'new_value': 7592}]
2025-05-25 00:00:40,611 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-25 00:00:40,971 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-25 00:00:40,971 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 88709.5, 'new_value': 93985.12}, {'field': 'offline_amount', 'old_value': 227756.66, 'new_value': 244366.9}, {'field': 'total_amount', 'old_value': 316466.16, 'new_value': 338352.02}, {'field': 'order_count', 'old_value': 10637, 'new_value': 11309}]
2025-05-25 00:00:40,971 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGF
2025-05-25 00:00:41,392 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGF
2025-05-25 00:00:41,392 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68369.0, 'new_value': 70776.0}, {'field': 'total_amount', 'old_value': 68369.0, 'new_value': 70776.0}, {'field': 'order_count', 'old_value': 3890, 'new_value': 4022}]
2025-05-25 00:00:41,392 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-25 00:00:41,814 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-25 00:00:41,814 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 282965.56, 'new_value': 298441.71}, {'field': 'offline_amount', 'old_value': 17701.62, 'new_value': 18543.42}, {'field': 'total_amount', 'old_value': 300667.18, 'new_value': 316985.13}, {'field': 'order_count', 'old_value': 11959, 'new_value': 12609}]
2025-05-25 00:00:41,814 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-25 00:00:42,189 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-25 00:00:42,189 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102235.4, 'new_value': 114893.9}, {'field': 'total_amount', 'old_value': 120562.3, 'new_value': 133220.8}, {'field': 'order_count', 'old_value': 170, 'new_value': 180}]
2025-05-25 00:00:42,189 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-25 00:00:42,642 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-25 00:00:42,642 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 117404.2, 'new_value': 125680.92}, {'field': 'offline_amount', 'old_value': 242173.49, 'new_value': 260661.8}, {'field': 'total_amount', 'old_value': 359577.69, 'new_value': 386342.72}, {'field': 'order_count', 'old_value': 4557, 'new_value': 4858}]
2025-05-25 00:00:42,642 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-25 00:00:43,049 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-25 00:00:43,049 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72443.7, 'new_value': 76855.15}, {'field': 'total_amount', 'old_value': 72443.7, 'new_value': 76855.15}, {'field': 'order_count', 'old_value': 532, 'new_value': 561}]
2025-05-25 00:00:43,049 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-25 00:00:43,455 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-25 00:00:43,455 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20115.6, 'new_value': 20293.37}, {'field': 'total_amount', 'old_value': 21765.6, 'new_value': 21943.37}, {'field': 'order_count', 'old_value': 422, 'new_value': 429}]
2025-05-25 00:00:43,455 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM
2025-05-25 00:00:43,861 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM
2025-05-25 00:00:43,861 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15541.0, 'new_value': 16009.0}, {'field': 'total_amount', 'old_value': 15541.0, 'new_value': 16009.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 30}]
2025-05-25 00:00:43,861 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHF
2025-05-25 00:00:44,267 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHF
2025-05-25 00:00:44,267 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90814.15, 'new_value': 103084.15}, {'field': 'total_amount', 'old_value': 90814.15, 'new_value': 103084.15}, {'field': 'order_count', 'old_value': 449, 'new_value': 511}]
2025-05-25 00:00:44,267 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-25 00:00:44,689 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-25 00:00:44,689 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26389.0, 'new_value': 26860.0}, {'field': 'total_amount', 'old_value': 26389.0, 'new_value': 26860.0}, {'field': 'order_count', 'old_value': 255, 'new_value': 263}]
2025-05-25 00:00:44,689 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-25 00:00:45,096 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-25 00:00:45,096 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 73378.66, 'new_value': 77239.88}, {'field': 'offline_amount', 'old_value': 108649.64, 'new_value': 116486.19}, {'field': 'total_amount', 'old_value': 182028.3, 'new_value': 193726.07}, {'field': 'order_count', 'old_value': 1872, 'new_value': 2009}]
2025-05-25 00:00:45,096 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-25 00:00:45,533 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-25 00:00:45,533 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9695.0, 'new_value': 10105.0}, {'field': 'total_amount', 'old_value': 11701.0, 'new_value': 12111.0}, {'field': 'order_count', 'old_value': 113, 'new_value': 117}]
2025-05-25 00:00:45,533 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-25 00:00:45,986 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-25 00:00:45,986 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26344.0, 'new_value': 27243.0}, {'field': 'total_amount', 'old_value': 26344.0, 'new_value': 27243.0}, {'field': 'order_count', 'old_value': 118, 'new_value': 124}]
2025-05-25 00:00:45,986 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-25 00:00:46,471 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-25 00:00:46,471 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 84577.4, 'new_value': 90358.4}, {'field': 'offline_amount', 'old_value': 124942.7, 'new_value': 141043.3}, {'field': 'total_amount', 'old_value': 209520.1, 'new_value': 231401.7}, {'field': 'order_count', 'old_value': 4239, 'new_value': 4677}]
2025-05-25 00:00:46,471 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-25 00:00:46,877 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-25 00:00:46,877 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 430176.58, 'new_value': 454564.08}, {'field': 'total_amount', 'old_value': 430176.58, 'new_value': 454564.08}, {'field': 'order_count', 'old_value': 5930, 'new_value': 6252}]
2025-05-25 00:00:46,877 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-25 00:00:47,314 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-25 00:00:47,314 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80564.57, 'new_value': 85443.43}, {'field': 'total_amount', 'old_value': 80564.57, 'new_value': 85443.43}, {'field': 'order_count', 'old_value': 2480, 'new_value': 2650}]
2025-05-25 00:00:47,314 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-25 00:00:47,736 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-25 00:00:47,736 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8815.46, 'new_value': 9291.55}, {'field': 'offline_amount', 'old_value': 27353.09, 'new_value': 29857.47}, {'field': 'total_amount', 'old_value': 36168.55, 'new_value': 39149.02}, {'field': 'order_count', 'old_value': 1253, 'new_value': 1358}]
2025-05-25 00:00:47,736 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-25 00:00:48,205 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-25 00:00:48,205 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 380282.4, 'new_value': 395190.0}, {'field': 'total_amount', 'old_value': 380282.4, 'new_value': 395190.0}, {'field': 'order_count', 'old_value': 1903, 'new_value': 1981}]
2025-05-25 00:00:48,205 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-25 00:00:48,549 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-25 00:00:48,549 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 125104.03, 'new_value': 130524.32}, {'field': 'offline_amount', 'old_value': 49059.26, 'new_value': 51915.32}, {'field': 'total_amount', 'old_value': 174163.29, 'new_value': 182439.64}, {'field': 'order_count', 'old_value': 10666, 'new_value': 11171}]
2025-05-25 00:00:48,549 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-25 00:00:48,971 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-25 00:00:48,971 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 186630.0, 'new_value': 195354.0}, {'field': 'total_amount', 'old_value': 186630.0, 'new_value': 195354.0}, {'field': 'order_count', 'old_value': 219, 'new_value': 237}]
2025-05-25 00:00:48,971 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-25 00:00:49,361 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-25 00:00:49,361 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 635320.89, 'new_value': 680633.32}, {'field': 'total_amount', 'old_value': 635320.89, 'new_value': 680633.32}, {'field': 'order_count', 'old_value': 12020, 'new_value': 12908}]
2025-05-25 00:00:49,361 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-25 00:00:49,814 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-25 00:00:49,814 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 245784.98, 'new_value': 259458.72}, {'field': 'total_amount', 'old_value': 257835.95, 'new_value': 271509.69}, {'field': 'order_count', 'old_value': 10971, 'new_value': 11570}]
2025-05-25 00:00:49,814 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-25 00:00:50,252 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-25 00:00:50,252 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 193027.0, 'new_value': 202271.0}, {'field': 'total_amount', 'old_value': 193027.0, 'new_value': 202271.0}, {'field': 'order_count', 'old_value': 593, 'new_value': 627}]
2025-05-25 00:00:50,252 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-25 00:00:50,642 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-25 00:00:50,642 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1615.28, 'new_value': 1736.11}, {'field': 'offline_amount', 'old_value': 20472.43, 'new_value': 21876.3}, {'field': 'total_amount', 'old_value': 22087.71, 'new_value': 23612.41}, {'field': 'order_count', 'old_value': 787, 'new_value': 848}]
2025-05-25 00:00:50,642 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-25 00:00:51,017 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-25 00:00:51,017 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6367.56, 'new_value': 6730.55}, {'field': 'offline_amount', 'old_value': 325104.14, 'new_value': 351970.04}, {'field': 'total_amount', 'old_value': 331471.7, 'new_value': 358700.59}, {'field': 'order_count', 'old_value': 16194, 'new_value': 17380}]
2025-05-25 00:00:51,017 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-25 00:00:51,424 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-25 00:00:51,424 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59224.06, 'new_value': 62829.09}, {'field': 'offline_amount', 'old_value': 79099.96, 'new_value': 83096.93}, {'field': 'total_amount', 'old_value': 138324.02, 'new_value': 145926.02}, {'field': 'order_count', 'old_value': 6369, 'new_value': 6724}]
2025-05-25 00:00:51,424 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-25 00:00:51,845 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-25 00:00:51,845 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27775.59, 'new_value': 28253.76}, {'field': 'offline_amount', 'old_value': 209219.94, 'new_value': 226405.44}, {'field': 'total_amount', 'old_value': 236995.53, 'new_value': 254659.2}, {'field': 'order_count', 'old_value': 7456, 'new_value': 8059}]
2025-05-25 00:00:51,845 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-25 00:00:52,283 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-25 00:00:52,283 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 210033.09, 'new_value': 227406.86}, {'field': 'total_amount', 'old_value': 229206.52, 'new_value': 246580.29}, {'field': 'order_count', 'old_value': 4723, 'new_value': 5104}]
2025-05-25 00:00:52,283 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIF
2025-05-25 00:00:52,705 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIF
2025-05-25 00:00:52,705 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45554.0, 'new_value': 54932.0}, {'field': 'total_amount', 'old_value': 45554.0, 'new_value': 54932.0}, {'field': 'order_count', 'old_value': 392, 'new_value': 406}]
2025-05-25 00:00:52,705 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-25 00:00:53,158 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-25 00:00:53,158 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31549.7, 'new_value': 32817.7}, {'field': 'total_amount', 'old_value': 31549.7, 'new_value': 32817.7}, {'field': 'order_count', 'old_value': 184, 'new_value': 193}]
2025-05-25 00:00:53,158 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLF
2025-05-25 00:00:53,611 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLF
2025-05-25 00:00:53,611 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11843.4, 'new_value': 12643.4}, {'field': 'offline_amount', 'old_value': 29295.0, 'new_value': 34655.0}, {'field': 'total_amount', 'old_value': 41138.4, 'new_value': 47298.4}, {'field': 'order_count', 'old_value': 57, 'new_value': 59}]
2025-05-25 00:00:53,611 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-25 00:00:53,970 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-25 00:00:53,970 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 75252.62, 'new_value': 77285.72}, {'field': 'offline_amount', 'old_value': 171014.47, 'new_value': 188129.01}, {'field': 'total_amount', 'old_value': 246267.09, 'new_value': 265414.73}, {'field': 'order_count', 'old_value': 4722, 'new_value': 5049}]
2025-05-25 00:00:53,970 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-25 00:00:54,345 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-25 00:00:54,345 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 161439.0, 'new_value': 173675.0}, {'field': 'total_amount', 'old_value': 161439.0, 'new_value': 173675.0}, {'field': 'order_count', 'old_value': 2723, 'new_value': 2918}]
2025-05-25 00:00:54,345 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-25 00:00:54,736 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-25 00:00:54,736 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 211414.94, 'new_value': 224473.13}, {'field': 'total_amount', 'old_value': 211414.94, 'new_value': 224473.13}, {'field': 'order_count', 'old_value': 1635, 'new_value': 1736}]
2025-05-25 00:00:54,736 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-25 00:00:55,174 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-25 00:00:55,174 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 61501.6, 'new_value': 69501.6}, {'field': 'offline_amount', 'old_value': 84619.63, 'new_value': 84872.28}, {'field': 'total_amount', 'old_value': 146121.23, 'new_value': 154373.88}, {'field': 'order_count', 'old_value': 4034, 'new_value': 4274}]
2025-05-25 00:00:55,174 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-25 00:00:55,549 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-25 00:00:55,549 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34003.68, 'new_value': 36003.68}, {'field': 'offline_amount', 'old_value': 43134.82, 'new_value': 45448.58}, {'field': 'total_amount', 'old_value': 77138.5, 'new_value': 81452.26}, {'field': 'order_count', 'old_value': 3792, 'new_value': 4005}]
2025-05-25 00:00:55,549 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOF
2025-05-25 00:00:55,939 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOF
2025-05-25 00:00:55,939 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10450.69, 'new_value': 10892.39}, {'field': 'offline_amount', 'old_value': 56712.87, 'new_value': 59594.72}, {'field': 'total_amount', 'old_value': 67163.56, 'new_value': 70487.11}, {'field': 'order_count', 'old_value': 1526, 'new_value': 1609}]
2025-05-25 00:00:55,939 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-25 00:00:56,330 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-25 00:00:56,330 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 109427.69, 'new_value': 114885.95}, {'field': 'offline_amount', 'old_value': 191398.74, 'new_value': 203381.53}, {'field': 'total_amount', 'old_value': 300826.43, 'new_value': 318267.48}, {'field': 'order_count', 'old_value': 9321, 'new_value': 9789}]
2025-05-25 00:00:56,330 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-25 00:00:56,783 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-25 00:00:56,783 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 190343.57, 'new_value': 199877.59}, {'field': 'offline_amount', 'old_value': 344946.92, 'new_value': 356946.92}, {'field': 'total_amount', 'old_value': 535290.49, 'new_value': 556824.51}, {'field': 'order_count', 'old_value': 1336, 'new_value': 1400}]
2025-05-25 00:00:56,783 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-25 00:00:57,174 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-25 00:00:57,174 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39435.2, 'new_value': 41112.66}, {'field': 'offline_amount', 'old_value': 22705.57, 'new_value': 24202.57}, {'field': 'total_amount', 'old_value': 62140.77, 'new_value': 65315.23}, {'field': 'order_count', 'old_value': 2715, 'new_value': 2868}]
2025-05-25 00:00:57,174 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-25 00:00:57,595 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-25 00:00:57,595 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14107.57, 'new_value': 14640.35}, {'field': 'offline_amount', 'old_value': 30393.2, 'new_value': 33027.9}, {'field': 'total_amount', 'old_value': 44500.77, 'new_value': 47668.25}, {'field': 'order_count', 'old_value': 1782, 'new_value': 1897}]
2025-05-25 00:00:57,595 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-25 00:00:57,970 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-25 00:00:57,970 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 483504.0, 'new_value': 497873.0}, {'field': 'total_amount', 'old_value': 483504.0, 'new_value': 497873.0}, {'field': 'order_count', 'old_value': 3282, 'new_value': 3469}]
2025-05-25 00:00:57,970 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSF
2025-05-25 00:00:58,361 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSF
2025-05-25 00:00:58,361 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52085.0, 'new_value': 61735.0}, {'field': 'total_amount', 'old_value': 52085.0, 'new_value': 61735.0}, {'field': 'order_count', 'old_value': 11163, 'new_value': 11848}]
2025-05-25 00:00:58,361 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTF
2025-05-25 00:00:58,720 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTF
2025-05-25 00:00:58,720 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76773.0, 'new_value': 91248.0}, {'field': 'total_amount', 'old_value': 76773.0, 'new_value': 91248.0}, {'field': 'order_count', 'old_value': 11163, 'new_value': 11848}]
2025-05-25 00:00:58,720 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVF
2025-05-25 00:00:59,127 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVF
2025-05-25 00:00:59,127 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94012.0, 'new_value': 95897.0}, {'field': 'total_amount', 'old_value': 94012.0, 'new_value': 95897.0}, {'field': 'order_count', 'old_value': 236, 'new_value': 241}]
2025-05-25 00:00:59,127 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYF
2025-05-25 00:00:59,549 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYF
2025-05-25 00:00:59,549 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 840.5, 'new_value': 969.5}, {'field': 'offline_amount', 'old_value': 37162.6, 'new_value': 40235.6}, {'field': 'total_amount', 'old_value': 38003.1, 'new_value': 41205.1}, {'field': 'order_count', 'old_value': 242, 'new_value': 262}]
2025-05-25 00:00:59,564 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-25 00:00:59,939 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-25 00:00:59,939 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48045.73, 'new_value': 54532.9}, {'field': 'total_amount', 'old_value': 48045.73, 'new_value': 54532.9}, {'field': 'order_count', 'old_value': 2756, 'new_value': 3156}]
2025-05-25 00:00:59,939 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-05-25 00:01:00,424 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-05-25 00:01:00,424 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4652.93, 'new_value': 4733.63}, {'field': 'offline_amount', 'old_value': 44693.74, 'new_value': 46775.99}, {'field': 'total_amount', 'old_value': 49346.67, 'new_value': 51509.62}, {'field': 'order_count', 'old_value': 1947, 'new_value': 2041}]
2025-05-25 00:01:00,424 - INFO - 日期 2025-05 处理完成 - 更新: 74 条，插入: 0 条，错误: 0 条
2025-05-25 00:01:00,424 - INFO - 数据同步完成！更新: 74 条，插入: 0 条，错误: 0 条
2025-05-25 00:01:00,439 - INFO - =================同步完成====================
2025-05-25 03:00:02,010 - INFO - =================使用默认全量同步=============
2025-05-25 03:00:03,432 - INFO - MySQL查询成功，共获取 3298 条记录
2025-05-25 03:00:03,432 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-25 03:00:03,463 - INFO - 开始处理日期: 2025-01
2025-05-25 03:00:03,463 - INFO - Request Parameters - Page 1:
2025-05-25 03:00:03,463 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:00:03,463 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:00:04,447 - INFO - Response - Page 1:
2025-05-25 03:00:04,651 - INFO - 第 1 页获取到 100 条记录
2025-05-25 03:00:04,651 - INFO - Request Parameters - Page 2:
2025-05-25 03:00:04,651 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:00:04,651 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:00:05,213 - INFO - Response - Page 2:
2025-05-25 03:00:05,416 - INFO - 第 2 页获取到 100 条记录
2025-05-25 03:00:05,416 - INFO - Request Parameters - Page 3:
2025-05-25 03:00:05,416 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:00:05,416 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:00:06,291 - INFO - Response - Page 3:
2025-05-25 03:00:06,494 - INFO - 第 3 页获取到 100 条记录
2025-05-25 03:00:06,494 - INFO - Request Parameters - Page 4:
2025-05-25 03:00:06,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:00:06,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:00:07,041 - INFO - Response - Page 4:
2025-05-25 03:00:07,244 - INFO - 第 4 页获取到 100 条记录
2025-05-25 03:00:07,244 - INFO - Request Parameters - Page 5:
2025-05-25 03:00:07,244 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:00:07,244 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:00:07,744 - INFO - Response - Page 5:
2025-05-25 03:00:07,947 - INFO - 第 5 页获取到 100 条记录
2025-05-25 03:00:07,947 - INFO - Request Parameters - Page 6:
2025-05-25 03:00:07,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:00:07,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:00:08,541 - INFO - Response - Page 6:
2025-05-25 03:00:08,744 - INFO - 第 6 页获取到 100 条记录
2025-05-25 03:00:08,744 - INFO - Request Parameters - Page 7:
2025-05-25 03:00:08,744 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:00:08,744 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:00:09,322 - INFO - Response - Page 7:
2025-05-25 03:00:09,526 - INFO - 第 7 页获取到 82 条记录
2025-05-25 03:00:09,526 - INFO - 查询完成，共获取到 682 条记录
2025-05-25 03:00:09,526 - INFO - 获取到 682 条表单数据
2025-05-25 03:00:09,526 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-25 03:00:09,541 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 03:00:09,541 - INFO - 开始处理日期: 2025-02
2025-05-25 03:00:09,541 - INFO - Request Parameters - Page 1:
2025-05-25 03:00:09,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:00:09,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:00:10,057 - INFO - Response - Page 1:
2025-05-25 03:00:10,260 - INFO - 第 1 页获取到 100 条记录
2025-05-25 03:00:10,260 - INFO - Request Parameters - Page 2:
2025-05-25 03:00:10,260 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:00:10,260 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:00:10,822 - INFO - Response - Page 2:
2025-05-25 03:00:11,026 - INFO - 第 2 页获取到 100 条记录
2025-05-25 03:00:11,026 - INFO - Request Parameters - Page 3:
2025-05-25 03:00:11,026 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:00:11,026 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:00:11,510 - INFO - Response - Page 3:
2025-05-25 03:00:11,713 - INFO - 第 3 页获取到 100 条记录
2025-05-25 03:00:11,713 - INFO - Request Parameters - Page 4:
2025-05-25 03:00:11,713 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:00:11,713 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:00:12,197 - INFO - Response - Page 4:
2025-05-25 03:00:12,401 - INFO - 第 4 页获取到 100 条记录
2025-05-25 03:00:12,401 - INFO - Request Parameters - Page 5:
2025-05-25 03:00:12,401 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:00:12,401 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:00:12,885 - INFO - Response - Page 5:
2025-05-25 03:00:13,088 - INFO - 第 5 页获取到 100 条记录
2025-05-25 03:00:13,088 - INFO - Request Parameters - Page 6:
2025-05-25 03:00:13,088 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:00:13,088 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:00:13,697 - INFO - Response - Page 6:
2025-05-25 03:00:13,900 - INFO - 第 6 页获取到 100 条记录
2025-05-25 03:00:13,900 - INFO - Request Parameters - Page 7:
2025-05-25 03:00:13,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:00:13,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:00:14,385 - INFO - Response - Page 7:
2025-05-25 03:00:14,588 - INFO - 第 7 页获取到 70 条记录
2025-05-25 03:00:14,588 - INFO - 查询完成，共获取到 670 条记录
2025-05-25 03:00:14,588 - INFO - 获取到 670 条表单数据
2025-05-25 03:00:14,588 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-25 03:00:14,604 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 03:00:14,604 - INFO - 开始处理日期: 2025-03
2025-05-25 03:00:14,604 - INFO - Request Parameters - Page 1:
2025-05-25 03:00:14,604 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:00:14,604 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:00:15,150 - INFO - Response - Page 1:
2025-05-25 03:00:15,354 - INFO - 第 1 页获取到 100 条记录
2025-05-25 03:00:15,354 - INFO - Request Parameters - Page 2:
2025-05-25 03:00:15,354 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:00:15,354 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:00:15,822 - INFO - Response - Page 2:
2025-05-25 03:00:16,025 - INFO - 第 2 页获取到 100 条记录
2025-05-25 03:00:16,025 - INFO - Request Parameters - Page 3:
2025-05-25 03:00:16,025 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:00:16,025 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:00:16,510 - INFO - Response - Page 3:
2025-05-25 03:00:16,713 - INFO - 第 3 页获取到 100 条记录
2025-05-25 03:00:16,713 - INFO - Request Parameters - Page 4:
2025-05-25 03:00:16,713 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:00:16,713 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:00:17,244 - INFO - Response - Page 4:
2025-05-25 03:00:17,447 - INFO - 第 4 页获取到 100 条记录
2025-05-25 03:00:17,447 - INFO - Request Parameters - Page 5:
2025-05-25 03:00:17,447 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:00:17,447 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:00:17,932 - INFO - Response - Page 5:
2025-05-25 03:00:18,135 - INFO - 第 5 页获取到 100 条记录
2025-05-25 03:00:18,135 - INFO - Request Parameters - Page 6:
2025-05-25 03:00:18,135 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:00:18,135 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:00:18,635 - INFO - Response - Page 6:
2025-05-25 03:00:18,838 - INFO - 第 6 页获取到 100 条记录
2025-05-25 03:00:18,838 - INFO - Request Parameters - Page 7:
2025-05-25 03:00:18,838 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:00:18,838 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:00:19,322 - INFO - Response - Page 7:
2025-05-25 03:00:19,525 - INFO - 第 7 页获取到 61 条记录
2025-05-25 03:00:19,525 - INFO - 查询完成，共获取到 661 条记录
2025-05-25 03:00:19,525 - INFO - 获取到 661 条表单数据
2025-05-25 03:00:19,525 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-25 03:00:19,541 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 03:00:19,541 - INFO - 开始处理日期: 2025-04
2025-05-25 03:00:19,541 - INFO - Request Parameters - Page 1:
2025-05-25 03:00:19,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:00:19,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:00:20,057 - INFO - Response - Page 1:
2025-05-25 03:00:20,260 - INFO - 第 1 页获取到 100 条记录
2025-05-25 03:00:20,260 - INFO - Request Parameters - Page 2:
2025-05-25 03:00:20,260 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:00:20,260 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:00:20,838 - INFO - Response - Page 2:
2025-05-25 03:00:21,041 - INFO - 第 2 页获取到 100 条记录
2025-05-25 03:00:21,041 - INFO - Request Parameters - Page 3:
2025-05-25 03:00:21,041 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:00:21,041 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:00:21,619 - INFO - Response - Page 3:
2025-05-25 03:00:21,822 - INFO - 第 3 页获取到 100 条记录
2025-05-25 03:00:21,822 - INFO - Request Parameters - Page 4:
2025-05-25 03:00:21,822 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:00:21,822 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:00:22,275 - INFO - Response - Page 4:
2025-05-25 03:00:22,479 - INFO - 第 4 页获取到 100 条记录
2025-05-25 03:00:22,479 - INFO - Request Parameters - Page 5:
2025-05-25 03:00:22,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:00:22,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:00:23,072 - INFO - Response - Page 5:
2025-05-25 03:00:23,275 - INFO - 第 5 页获取到 100 条记录
2025-05-25 03:00:23,275 - INFO - Request Parameters - Page 6:
2025-05-25 03:00:23,275 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:00:23,275 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:00:23,775 - INFO - Response - Page 6:
2025-05-25 03:00:23,979 - INFO - 第 6 页获取到 100 条记录
2025-05-25 03:00:23,979 - INFO - Request Parameters - Page 7:
2025-05-25 03:00:23,979 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:00:23,979 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:00:24,354 - INFO - Response - Page 7:
2025-05-25 03:00:24,557 - INFO - 第 7 页获取到 56 条记录
2025-05-25 03:00:24,557 - INFO - 查询完成，共获取到 656 条记录
2025-05-25 03:00:24,557 - INFO - 获取到 656 条表单数据
2025-05-25 03:00:24,557 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-25 03:00:24,572 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 03:00:24,572 - INFO - 开始处理日期: 2025-05
2025-05-25 03:00:24,572 - INFO - Request Parameters - Page 1:
2025-05-25 03:00:24,572 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:00:24,572 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:00:25,307 - INFO - Response - Page 1:
2025-05-25 03:00:25,510 - INFO - 第 1 页获取到 100 条记录
2025-05-25 03:00:25,510 - INFO - Request Parameters - Page 2:
2025-05-25 03:00:25,510 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:00:25,510 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:00:25,979 - INFO - Response - Page 2:
2025-05-25 03:00:26,182 - INFO - 第 2 页获取到 100 条记录
2025-05-25 03:00:26,182 - INFO - Request Parameters - Page 3:
2025-05-25 03:00:26,182 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:00:26,182 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:00:26,713 - INFO - Response - Page 3:
2025-05-25 03:00:26,916 - INFO - 第 3 页获取到 100 条记录
2025-05-25 03:00:26,916 - INFO - Request Parameters - Page 4:
2025-05-25 03:00:26,916 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:00:26,916 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:00:27,369 - INFO - Response - Page 4:
2025-05-25 03:00:27,572 - INFO - 第 4 页获取到 100 条记录
2025-05-25 03:00:27,572 - INFO - Request Parameters - Page 5:
2025-05-25 03:00:27,572 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:00:27,572 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:00:28,072 - INFO - Response - Page 5:
2025-05-25 03:00:28,275 - INFO - 第 5 页获取到 100 条记录
2025-05-25 03:00:28,275 - INFO - Request Parameters - Page 6:
2025-05-25 03:00:28,275 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:00:28,275 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:00:28,760 - INFO - Response - Page 6:
2025-05-25 03:00:28,963 - INFO - 第 6 页获取到 100 条记录
2025-05-25 03:00:28,963 - INFO - Request Parameters - Page 7:
2025-05-25 03:00:28,963 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 03:00:28,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 03:00:29,322 - INFO - Response - Page 7:
2025-05-25 03:00:29,525 - INFO - 第 7 页获取到 29 条记录
2025-05-25 03:00:29,525 - INFO - 查询完成，共获取到 629 条记录
2025-05-25 03:00:29,525 - INFO - 获取到 629 条表单数据
2025-05-25 03:00:29,525 - INFO - 当前日期 2025-05 有 629 条MySQL数据需要处理
2025-05-25 03:00:29,541 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-25 03:00:30,025 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-25 03:00:30,025 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49948.26, 'new_value': 53588.19}, {'field': 'offline_amount', 'old_value': 352878.03, 'new_value': 382394.07}, {'field': 'total_amount', 'old_value': 402826.29, 'new_value': 435982.26}, {'field': 'order_count', 'old_value': 3380, 'new_value': 3662}]
2025-05-25 03:00:30,025 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-25 03:00:30,447 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-25 03:00:30,447 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 333397.69, 'new_value': 359784.72}, {'field': 'total_amount', 'old_value': 355560.81, 'new_value': 381947.84}, {'field': 'order_count', 'old_value': 15105, 'new_value': 16176}]
2025-05-25 03:00:30,463 - INFO - 日期 2025-05 处理完成 - 更新: 2 条，插入: 0 条，错误: 0 条
2025-05-25 03:00:30,463 - INFO - 数据同步完成！更新: 2 条，插入: 0 条，错误: 0 条
2025-05-25 03:00:30,463 - INFO - =================同步完成====================
2025-05-25 06:00:01,970 - INFO - =================使用默认全量同步=============
2025-05-25 06:00:03,408 - INFO - MySQL查询成功，共获取 3298 条记录
2025-05-25 06:00:03,408 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-25 06:00:03,439 - INFO - 开始处理日期: 2025-01
2025-05-25 06:00:03,439 - INFO - Request Parameters - Page 1:
2025-05-25 06:00:03,439 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:00:03,439 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:00:04,377 - INFO - Response - Page 1:
2025-05-25 06:00:04,580 - INFO - 第 1 页获取到 100 条记录
2025-05-25 06:00:04,580 - INFO - Request Parameters - Page 2:
2025-05-25 06:00:04,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:00:04,580 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:00:05,517 - INFO - Response - Page 2:
2025-05-25 06:00:05,720 - INFO - 第 2 页获取到 100 条记录
2025-05-25 06:00:05,720 - INFO - Request Parameters - Page 3:
2025-05-25 06:00:05,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:00:05,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:00:06,236 - INFO - Response - Page 3:
2025-05-25 06:00:06,439 - INFO - 第 3 页获取到 100 条记录
2025-05-25 06:00:06,439 - INFO - Request Parameters - Page 4:
2025-05-25 06:00:06,439 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:00:06,439 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:00:06,986 - INFO - Response - Page 4:
2025-05-25 06:00:07,189 - INFO - 第 4 页获取到 100 条记录
2025-05-25 06:00:07,189 - INFO - Request Parameters - Page 5:
2025-05-25 06:00:07,189 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:00:07,189 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:00:07,752 - INFO - Response - Page 5:
2025-05-25 06:00:07,955 - INFO - 第 5 页获取到 100 条记录
2025-05-25 06:00:07,955 - INFO - Request Parameters - Page 6:
2025-05-25 06:00:07,955 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:00:07,955 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:00:08,486 - INFO - Response - Page 6:
2025-05-25 06:00:08,689 - INFO - 第 6 页获取到 100 条记录
2025-05-25 06:00:08,689 - INFO - Request Parameters - Page 7:
2025-05-25 06:00:08,689 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:00:08,689 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:00:09,205 - INFO - Response - Page 7:
2025-05-25 06:00:09,408 - INFO - 第 7 页获取到 82 条记录
2025-05-25 06:00:09,408 - INFO - 查询完成，共获取到 682 条记录
2025-05-25 06:00:09,408 - INFO - 获取到 682 条表单数据
2025-05-25 06:00:09,408 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-25 06:00:09,423 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 06:00:09,423 - INFO - 开始处理日期: 2025-02
2025-05-25 06:00:09,423 - INFO - Request Parameters - Page 1:
2025-05-25 06:00:09,423 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:00:09,423 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:00:09,955 - INFO - Response - Page 1:
2025-05-25 06:00:10,158 - INFO - 第 1 页获取到 100 条记录
2025-05-25 06:00:10,158 - INFO - Request Parameters - Page 2:
2025-05-25 06:00:10,158 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:00:10,158 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:00:10,673 - INFO - Response - Page 2:
2025-05-25 06:00:10,877 - INFO - 第 2 页获取到 100 条记录
2025-05-25 06:00:10,877 - INFO - Request Parameters - Page 3:
2025-05-25 06:00:10,877 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:00:10,877 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:00:11,377 - INFO - Response - Page 3:
2025-05-25 06:00:11,580 - INFO - 第 3 页获取到 100 条记录
2025-05-25 06:00:11,580 - INFO - Request Parameters - Page 4:
2025-05-25 06:00:11,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:00:11,580 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:00:12,220 - INFO - Response - Page 4:
2025-05-25 06:00:12,424 - INFO - 第 4 页获取到 100 条记录
2025-05-25 06:00:12,424 - INFO - Request Parameters - Page 5:
2025-05-25 06:00:12,424 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:00:12,424 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:00:12,892 - INFO - Response - Page 5:
2025-05-25 06:00:13,095 - INFO - 第 5 页获取到 100 条记录
2025-05-25 06:00:13,095 - INFO - Request Parameters - Page 6:
2025-05-25 06:00:13,095 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:00:13,095 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:00:13,783 - INFO - Response - Page 6:
2025-05-25 06:00:13,986 - INFO - 第 6 页获取到 100 条记录
2025-05-25 06:00:13,986 - INFO - Request Parameters - Page 7:
2025-05-25 06:00:13,986 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:00:13,986 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:00:14,580 - INFO - Response - Page 7:
2025-05-25 06:00:14,783 - INFO - 第 7 页获取到 70 条记录
2025-05-25 06:00:14,783 - INFO - 查询完成，共获取到 670 条记录
2025-05-25 06:00:14,783 - INFO - 获取到 670 条表单数据
2025-05-25 06:00:14,783 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-25 06:00:14,798 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 06:00:14,798 - INFO - 开始处理日期: 2025-03
2025-05-25 06:00:14,798 - INFO - Request Parameters - Page 1:
2025-05-25 06:00:14,798 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:00:14,798 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:00:15,330 - INFO - Response - Page 1:
2025-05-25 06:00:15,533 - INFO - 第 1 页获取到 100 条记录
2025-05-25 06:00:15,533 - INFO - Request Parameters - Page 2:
2025-05-25 06:00:15,533 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:00:15,533 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:00:16,048 - INFO - Response - Page 2:
2025-05-25 06:00:16,252 - INFO - 第 2 页获取到 100 条记录
2025-05-25 06:00:16,252 - INFO - Request Parameters - Page 3:
2025-05-25 06:00:16,252 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:00:16,252 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:00:16,720 - INFO - Response - Page 3:
2025-05-25 06:00:16,923 - INFO - 第 3 页获取到 100 条记录
2025-05-25 06:00:16,923 - INFO - Request Parameters - Page 4:
2025-05-25 06:00:16,923 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:00:16,923 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:00:17,423 - INFO - Response - Page 4:
2025-05-25 06:00:17,627 - INFO - 第 4 页获取到 100 条记录
2025-05-25 06:00:17,627 - INFO - Request Parameters - Page 5:
2025-05-25 06:00:17,627 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:00:17,627 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:00:18,142 - INFO - Response - Page 5:
2025-05-25 06:00:18,345 - INFO - 第 5 页获取到 100 条记录
2025-05-25 06:00:18,345 - INFO - Request Parameters - Page 6:
2025-05-25 06:00:18,345 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:00:18,345 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:00:18,877 - INFO - Response - Page 6:
2025-05-25 06:00:19,080 - INFO - 第 6 页获取到 100 条记录
2025-05-25 06:00:19,080 - INFO - Request Parameters - Page 7:
2025-05-25 06:00:19,080 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:00:19,080 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:00:19,548 - INFO - Response - Page 7:
2025-05-25 06:00:19,752 - INFO - 第 7 页获取到 61 条记录
2025-05-25 06:00:19,752 - INFO - 查询完成，共获取到 661 条记录
2025-05-25 06:00:19,752 - INFO - 获取到 661 条表单数据
2025-05-25 06:00:19,752 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-25 06:00:19,767 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 06:00:19,767 - INFO - 开始处理日期: 2025-04
2025-05-25 06:00:19,767 - INFO - Request Parameters - Page 1:
2025-05-25 06:00:19,767 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:00:19,767 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:00:20,361 - INFO - Response - Page 1:
2025-05-25 06:00:20,564 - INFO - 第 1 页获取到 100 条记录
2025-05-25 06:00:20,564 - INFO - Request Parameters - Page 2:
2025-05-25 06:00:20,564 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:00:20,564 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:00:21,064 - INFO - Response - Page 2:
2025-05-25 06:00:21,267 - INFO - 第 2 页获取到 100 条记录
2025-05-25 06:00:21,267 - INFO - Request Parameters - Page 3:
2025-05-25 06:00:21,267 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:00:21,267 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:00:21,736 - INFO - Response - Page 3:
2025-05-25 06:00:21,939 - INFO - 第 3 页获取到 100 条记录
2025-05-25 06:00:21,939 - INFO - Request Parameters - Page 4:
2025-05-25 06:00:21,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:00:21,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:00:22,611 - INFO - Response - Page 4:
2025-05-25 06:00:22,814 - INFO - 第 4 页获取到 100 条记录
2025-05-25 06:00:22,814 - INFO - Request Parameters - Page 5:
2025-05-25 06:00:22,814 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:00:22,814 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:00:23,267 - INFO - Response - Page 5:
2025-05-25 06:00:23,470 - INFO - 第 5 页获取到 100 条记录
2025-05-25 06:00:23,470 - INFO - Request Parameters - Page 6:
2025-05-25 06:00:23,470 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:00:23,470 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:00:23,986 - INFO - Response - Page 6:
2025-05-25 06:00:24,189 - INFO - 第 6 页获取到 100 条记录
2025-05-25 06:00:24,189 - INFO - Request Parameters - Page 7:
2025-05-25 06:00:24,189 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:00:24,189 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:00:24,595 - INFO - Response - Page 7:
2025-05-25 06:00:24,798 - INFO - 第 7 页获取到 56 条记录
2025-05-25 06:00:24,798 - INFO - 查询完成，共获取到 656 条记录
2025-05-25 06:00:24,798 - INFO - 获取到 656 条表单数据
2025-05-25 06:00:24,798 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-25 06:00:24,814 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 06:00:24,814 - INFO - 开始处理日期: 2025-05
2025-05-25 06:00:24,814 - INFO - Request Parameters - Page 1:
2025-05-25 06:00:24,814 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:00:24,814 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:00:25,330 - INFO - Response - Page 1:
2025-05-25 06:00:25,533 - INFO - 第 1 页获取到 100 条记录
2025-05-25 06:00:25,533 - INFO - Request Parameters - Page 2:
2025-05-25 06:00:25,533 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:00:25,533 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:00:26,033 - INFO - Response - Page 2:
2025-05-25 06:00:26,236 - INFO - 第 2 页获取到 100 条记录
2025-05-25 06:00:26,236 - INFO - Request Parameters - Page 3:
2025-05-25 06:00:26,236 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:00:26,236 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:00:26,783 - INFO - Response - Page 3:
2025-05-25 06:00:26,986 - INFO - 第 3 页获取到 100 条记录
2025-05-25 06:00:26,986 - INFO - Request Parameters - Page 4:
2025-05-25 06:00:26,986 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:00:26,986 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:00:27,486 - INFO - Response - Page 4:
2025-05-25 06:00:27,689 - INFO - 第 4 页获取到 100 条记录
2025-05-25 06:00:27,689 - INFO - Request Parameters - Page 5:
2025-05-25 06:00:27,689 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:00:27,689 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:00:28,142 - INFO - Response - Page 5:
2025-05-25 06:00:28,345 - INFO - 第 5 页获取到 100 条记录
2025-05-25 06:00:28,345 - INFO - Request Parameters - Page 6:
2025-05-25 06:00:28,345 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:00:28,345 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:00:28,783 - INFO - Response - Page 6:
2025-05-25 06:00:28,986 - INFO - 第 6 页获取到 100 条记录
2025-05-25 06:00:28,986 - INFO - Request Parameters - Page 7:
2025-05-25 06:00:28,986 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 06:00:28,986 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 06:00:29,376 - INFO - Response - Page 7:
2025-05-25 06:00:29,580 - INFO - 第 7 页获取到 29 条记录
2025-05-25 06:00:29,580 - INFO - 查询完成，共获取到 629 条记录
2025-05-25 06:00:29,580 - INFO - 获取到 629 条表单数据
2025-05-25 06:00:29,580 - INFO - 当前日期 2025-05 有 629 条MySQL数据需要处理
2025-05-25 06:00:29,595 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-25 06:00:30,048 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-25 06:00:30,048 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73544.0, 'new_value': 77551.0}, {'field': 'total_amount', 'old_value': 75394.0, 'new_value': 79401.0}, {'field': 'order_count', 'old_value': 435, 'new_value': 453}]
2025-05-25 06:00:30,048 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-25 06:00:30,048 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-05-25 06:00:30,064 - INFO - =================同步完成====================
2025-05-25 09:00:02,040 - INFO - =================使用默认全量同步=============
2025-05-25 09:00:03,493 - INFO - MySQL查询成功，共获取 3298 条记录
2025-05-25 09:00:03,493 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-25 09:00:03,525 - INFO - 开始处理日期: 2025-01
2025-05-25 09:00:03,525 - INFO - Request Parameters - Page 1:
2025-05-25 09:00:03,525 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:00:03,525 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:00:05,478 - INFO - Response - Page 1:
2025-05-25 09:00:05,681 - INFO - 第 1 页获取到 100 条记录
2025-05-25 09:00:05,681 - INFO - Request Parameters - Page 2:
2025-05-25 09:00:05,681 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:00:05,681 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:00:06,197 - INFO - Response - Page 2:
2025-05-25 09:00:06,400 - INFO - 第 2 页获取到 100 条记录
2025-05-25 09:00:06,400 - INFO - Request Parameters - Page 3:
2025-05-25 09:00:06,400 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:00:06,400 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:00:06,978 - INFO - Response - Page 3:
2025-05-25 09:00:07,181 - INFO - 第 3 页获取到 100 条记录
2025-05-25 09:00:07,181 - INFO - Request Parameters - Page 4:
2025-05-25 09:00:07,181 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:00:07,181 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:00:07,681 - INFO - Response - Page 4:
2025-05-25 09:00:07,884 - INFO - 第 4 页获取到 100 条记录
2025-05-25 09:00:07,884 - INFO - Request Parameters - Page 5:
2025-05-25 09:00:07,884 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:00:07,884 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:00:08,400 - INFO - Response - Page 5:
2025-05-25 09:00:08,603 - INFO - 第 5 页获取到 100 条记录
2025-05-25 09:00:08,603 - INFO - Request Parameters - Page 6:
2025-05-25 09:00:08,603 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:00:08,603 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:00:09,087 - INFO - Response - Page 6:
2025-05-25 09:00:09,290 - INFO - 第 6 页获取到 100 条记录
2025-05-25 09:00:09,290 - INFO - Request Parameters - Page 7:
2025-05-25 09:00:09,290 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:00:09,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:00:09,775 - INFO - Response - Page 7:
2025-05-25 09:00:09,978 - INFO - 第 7 页获取到 82 条记录
2025-05-25 09:00:09,978 - INFO - 查询完成，共获取到 682 条记录
2025-05-25 09:00:09,978 - INFO - 获取到 682 条表单数据
2025-05-25 09:00:09,978 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-25 09:00:09,993 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 09:00:09,993 - INFO - 开始处理日期: 2025-02
2025-05-25 09:00:09,993 - INFO - Request Parameters - Page 1:
2025-05-25 09:00:09,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:00:09,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:00:10,478 - INFO - Response - Page 1:
2025-05-25 09:00:10,681 - INFO - 第 1 页获取到 100 条记录
2025-05-25 09:00:10,681 - INFO - Request Parameters - Page 2:
2025-05-25 09:00:10,681 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:00:10,681 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:00:11,212 - INFO - Response - Page 2:
2025-05-25 09:00:11,415 - INFO - 第 2 页获取到 100 条记录
2025-05-25 09:00:11,415 - INFO - Request Parameters - Page 3:
2025-05-25 09:00:11,415 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:00:11,415 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:00:11,868 - INFO - Response - Page 3:
2025-05-25 09:00:12,072 - INFO - 第 3 页获取到 100 条记录
2025-05-25 09:00:12,072 - INFO - Request Parameters - Page 4:
2025-05-25 09:00:12,072 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:00:12,072 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:00:12,556 - INFO - Response - Page 4:
2025-05-25 09:00:12,759 - INFO - 第 4 页获取到 100 条记录
2025-05-25 09:00:12,759 - INFO - Request Parameters - Page 5:
2025-05-25 09:00:12,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:00:12,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:00:13,306 - INFO - Response - Page 5:
2025-05-25 09:00:13,509 - INFO - 第 5 页获取到 100 条记录
2025-05-25 09:00:13,509 - INFO - Request Parameters - Page 6:
2025-05-25 09:00:13,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:00:13,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:00:13,978 - INFO - Response - Page 6:
2025-05-25 09:00:14,181 - INFO - 第 6 页获取到 100 条记录
2025-05-25 09:00:14,181 - INFO - Request Parameters - Page 7:
2025-05-25 09:00:14,181 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:00:14,181 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:00:14,618 - INFO - Response - Page 7:
2025-05-25 09:00:14,822 - INFO - 第 7 页获取到 70 条记录
2025-05-25 09:00:14,822 - INFO - 查询完成，共获取到 670 条记录
2025-05-25 09:00:14,822 - INFO - 获取到 670 条表单数据
2025-05-25 09:00:14,822 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-25 09:00:14,837 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 09:00:14,837 - INFO - 开始处理日期: 2025-03
2025-05-25 09:00:14,837 - INFO - Request Parameters - Page 1:
2025-05-25 09:00:14,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:00:14,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:00:15,384 - INFO - Response - Page 1:
2025-05-25 09:00:15,587 - INFO - 第 1 页获取到 100 条记录
2025-05-25 09:00:15,587 - INFO - Request Parameters - Page 2:
2025-05-25 09:00:15,587 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:00:15,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:00:16,072 - INFO - Response - Page 2:
2025-05-25 09:00:16,275 - INFO - 第 2 页获取到 100 条记录
2025-05-25 09:00:16,275 - INFO - Request Parameters - Page 3:
2025-05-25 09:00:16,275 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:00:16,275 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:00:16,775 - INFO - Response - Page 3:
2025-05-25 09:00:16,978 - INFO - 第 3 页获取到 100 条记录
2025-05-25 09:00:16,978 - INFO - Request Parameters - Page 4:
2025-05-25 09:00:16,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:00:16,978 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:00:17,509 - INFO - Response - Page 4:
2025-05-25 09:00:17,712 - INFO - 第 4 页获取到 100 条记录
2025-05-25 09:00:17,712 - INFO - Request Parameters - Page 5:
2025-05-25 09:00:17,712 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:00:17,712 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:00:18,228 - INFO - Response - Page 5:
2025-05-25 09:00:18,431 - INFO - 第 5 页获取到 100 条记录
2025-05-25 09:00:18,431 - INFO - Request Parameters - Page 6:
2025-05-25 09:00:18,431 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:00:18,431 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:00:18,931 - INFO - Response - Page 6:
2025-05-25 09:00:19,134 - INFO - 第 6 页获取到 100 条记录
2025-05-25 09:00:19,134 - INFO - Request Parameters - Page 7:
2025-05-25 09:00:19,134 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:00:19,134 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:00:19,525 - INFO - Response - Page 7:
2025-05-25 09:00:19,728 - INFO - 第 7 页获取到 61 条记录
2025-05-25 09:00:19,728 - INFO - 查询完成，共获取到 661 条记录
2025-05-25 09:00:19,728 - INFO - 获取到 661 条表单数据
2025-05-25 09:00:19,728 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-25 09:00:19,743 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 09:00:19,743 - INFO - 开始处理日期: 2025-04
2025-05-25 09:00:19,743 - INFO - Request Parameters - Page 1:
2025-05-25 09:00:19,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:00:19,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:00:20,462 - INFO - Response - Page 1:
2025-05-25 09:00:20,665 - INFO - 第 1 页获取到 100 条记录
2025-05-25 09:00:20,665 - INFO - Request Parameters - Page 2:
2025-05-25 09:00:20,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:00:20,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:00:21,150 - INFO - Response - Page 2:
2025-05-25 09:00:21,353 - INFO - 第 2 页获取到 100 条记录
2025-05-25 09:00:21,353 - INFO - Request Parameters - Page 3:
2025-05-25 09:00:21,353 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:00:21,353 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:00:21,868 - INFO - Response - Page 3:
2025-05-25 09:00:22,071 - INFO - 第 3 页获取到 100 条记录
2025-05-25 09:00:22,071 - INFO - Request Parameters - Page 4:
2025-05-25 09:00:22,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:00:22,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:00:22,540 - INFO - Response - Page 4:
2025-05-25 09:00:22,743 - INFO - 第 4 页获取到 100 条记录
2025-05-25 09:00:22,743 - INFO - Request Parameters - Page 5:
2025-05-25 09:00:22,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:00:22,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:00:23,275 - INFO - Response - Page 5:
2025-05-25 09:00:23,478 - INFO - 第 5 页获取到 100 条记录
2025-05-25 09:00:23,478 - INFO - Request Parameters - Page 6:
2025-05-25 09:00:23,478 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:00:23,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:00:23,962 - INFO - Response - Page 6:
2025-05-25 09:00:24,165 - INFO - 第 6 页获取到 100 条记录
2025-05-25 09:00:24,165 - INFO - Request Parameters - Page 7:
2025-05-25 09:00:24,165 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:00:24,165 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:00:24,571 - INFO - Response - Page 7:
2025-05-25 09:00:24,775 - INFO - 第 7 页获取到 56 条记录
2025-05-25 09:00:24,775 - INFO - 查询完成，共获取到 656 条记录
2025-05-25 09:00:24,775 - INFO - 获取到 656 条表单数据
2025-05-25 09:00:24,775 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-25 09:00:24,790 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 09:00:24,790 - INFO - 开始处理日期: 2025-05
2025-05-25 09:00:24,790 - INFO - Request Parameters - Page 1:
2025-05-25 09:00:24,790 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:00:24,790 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:00:25,306 - INFO - Response - Page 1:
2025-05-25 09:00:25,509 - INFO - 第 1 页获取到 100 条记录
2025-05-25 09:00:25,509 - INFO - Request Parameters - Page 2:
2025-05-25 09:00:25,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:00:25,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:00:26,025 - INFO - Response - Page 2:
2025-05-25 09:00:26,228 - INFO - 第 2 页获取到 100 条记录
2025-05-25 09:00:26,228 - INFO - Request Parameters - Page 3:
2025-05-25 09:00:26,228 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:00:26,228 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:00:26,712 - INFO - Response - Page 3:
2025-05-25 09:00:26,915 - INFO - 第 3 页获取到 100 条记录
2025-05-25 09:00:26,915 - INFO - Request Parameters - Page 4:
2025-05-25 09:00:26,915 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:00:26,915 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:00:27,415 - INFO - Response - Page 4:
2025-05-25 09:00:27,618 - INFO - 第 4 页获取到 100 条记录
2025-05-25 09:00:27,618 - INFO - Request Parameters - Page 5:
2025-05-25 09:00:27,618 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:00:27,618 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:00:28,103 - INFO - Response - Page 5:
2025-05-25 09:00:28,306 - INFO - 第 5 页获取到 100 条记录
2025-05-25 09:00:28,306 - INFO - Request Parameters - Page 6:
2025-05-25 09:00:28,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:00:28,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:00:28,821 - INFO - Response - Page 6:
2025-05-25 09:00:29,025 - INFO - 第 6 页获取到 100 条记录
2025-05-25 09:00:29,025 - INFO - Request Parameters - Page 7:
2025-05-25 09:00:29,025 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 09:00:29,025 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 09:00:29,415 - INFO - Response - Page 7:
2025-05-25 09:00:29,618 - INFO - 第 7 页获取到 29 条记录
2025-05-25 09:00:29,618 - INFO - 查询完成，共获取到 629 条记录
2025-05-25 09:00:29,618 - INFO - 获取到 629 条表单数据
2025-05-25 09:00:29,618 - INFO - 当前日期 2025-05 有 629 条MySQL数据需要处理
2025-05-25 09:00:29,618 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-25 09:00:30,103 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-25 09:00:30,103 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9100080.0, 'new_value': 9700080.0}, {'field': 'total_amount', 'old_value': 9200080.0, 'new_value': 9800080.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 48}]
2025-05-25 09:00:30,103 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-25 09:00:30,540 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-25 09:00:30,540 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54887.0, 'new_value': 63051.0}, {'field': 'total_amount', 'old_value': 60304.32, 'new_value': 68468.32}, {'field': 'order_count', 'old_value': 450, 'new_value': 457}]
2025-05-25 09:00:30,540 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-25 09:00:31,009 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-25 09:00:31,009 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 56292.88, 'new_value': 58510.11}, {'field': 'offline_amount', 'old_value': 89060.7, 'new_value': 95354.55}, {'field': 'total_amount', 'old_value': 145353.58, 'new_value': 153864.66}, {'field': 'order_count', 'old_value': 5059, 'new_value': 5326}]
2025-05-25 09:00:31,009 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-25 09:00:31,509 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-25 09:00:31,509 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 290295.0, 'new_value': 312865.0}, {'field': 'total_amount', 'old_value': 290295.0, 'new_value': 312865.0}, {'field': 'order_count', 'old_value': 176, 'new_value': 185}]
2025-05-25 09:00:31,509 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-25 09:00:31,946 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-25 09:00:31,946 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23162.8, 'new_value': 24199.35}, {'field': 'total_amount', 'old_value': 23228.35, 'new_value': 24264.9}, {'field': 'order_count', 'old_value': 202, 'new_value': 212}]
2025-05-25 09:00:31,946 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-25 09:00:32,353 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-25 09:00:32,353 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 151383.0, 'new_value': 151702.0}, {'field': 'total_amount', 'old_value': 185129.15, 'new_value': 185448.15}, {'field': 'order_count', 'old_value': 34, 'new_value': 35}]
2025-05-25 09:00:32,353 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-25 09:00:32,821 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-25 09:00:32,821 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48779.0, 'new_value': 52208.0}, {'field': 'total_amount', 'old_value': 49127.0, 'new_value': 52556.0}, {'field': 'order_count', 'old_value': 103, 'new_value': 110}]
2025-05-25 09:00:32,821 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-25 09:00:33,212 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-25 09:00:33,212 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 781341.0, 'new_value': 818231.0}, {'field': 'total_amount', 'old_value': 781341.0, 'new_value': 818231.0}, {'field': 'order_count', 'old_value': 138, 'new_value': 144}]
2025-05-25 09:00:33,212 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-25 09:00:33,634 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-25 09:00:33,634 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 214642.03, 'new_value': 230575.37}, {'field': 'offline_amount', 'old_value': 101272.85, 'new_value': 105020.75}, {'field': 'total_amount', 'old_value': 315914.88, 'new_value': 335596.12}, {'field': 'order_count', 'old_value': 1204, 'new_value': 1294}]
2025-05-25 09:00:33,634 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-25 09:00:34,056 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-25 09:00:34,056 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20440.21, 'new_value': 21263.33}, {'field': 'offline_amount', 'old_value': 272803.15, 'new_value': 292746.52}, {'field': 'total_amount', 'old_value': 293243.36, 'new_value': 314009.85}, {'field': 'order_count', 'old_value': 1374, 'new_value': 1480}]
2025-05-25 09:00:34,056 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-25 09:00:34,493 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-25 09:00:34,493 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33521.9, 'new_value': 35411.62}, {'field': 'offline_amount', 'old_value': 500897.29, 'new_value': 533544.79}, {'field': 'total_amount', 'old_value': 534419.19, 'new_value': 568956.41}, {'field': 'order_count', 'old_value': 2935, 'new_value': 3091}]
2025-05-25 09:00:34,493 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-25 09:00:34,915 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-25 09:00:34,915 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 286476.0, 'new_value': 292170.0}, {'field': 'total_amount', 'old_value': 286476.0, 'new_value': 292170.0}, {'field': 'order_count', 'old_value': 236, 'new_value': 243}]
2025-05-25 09:00:34,915 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-25 09:00:35,493 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-25 09:00:35,493 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 273354.5, 'new_value': 299797.1}, {'field': 'total_amount', 'old_value': 273354.5, 'new_value': 299797.1}, {'field': 'order_count', 'old_value': 2977, 'new_value': 3205}]
2025-05-25 09:00:35,493 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-25 09:00:35,978 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-25 09:00:35,978 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 217304.3, 'new_value': 228304.3}, {'field': 'total_amount', 'old_value': 217304.3, 'new_value': 228304.3}, {'field': 'order_count', 'old_value': 386, 'new_value': 410}]
2025-05-25 09:00:35,978 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-25 09:00:36,399 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-25 09:00:36,399 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 451183.96, 'new_value': 481998.19}, {'field': 'total_amount', 'old_value': 559928.64, 'new_value': 590742.87}, {'field': 'order_count', 'old_value': 2297, 'new_value': 2405}]
2025-05-25 09:00:36,399 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-25 09:00:36,884 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-25 09:00:36,884 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 173464.0, 'new_value': 178148.0}, {'field': 'total_amount', 'old_value': 173597.0, 'new_value': 178281.0}, {'field': 'order_count', 'old_value': 122, 'new_value': 128}]
2025-05-25 09:00:36,884 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-25 09:00:37,368 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-25 09:00:37,368 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 96750.66, 'new_value': 102048.66}, {'field': 'total_amount', 'old_value': 102362.18, 'new_value': 107660.18}, {'field': 'order_count', 'old_value': 9264, 'new_value': 9830}]
2025-05-25 09:00:37,368 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-25 09:00:37,821 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-25 09:00:37,821 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68547.0, 'new_value': 69868.0}, {'field': 'total_amount', 'old_value': 68547.0, 'new_value': 69868.0}, {'field': 'order_count', 'old_value': 92, 'new_value': 94}]
2025-05-25 09:00:37,821 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYU
2025-05-25 09:00:38,212 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYU
2025-05-25 09:00:38,212 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-25 09:00:38,212 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-25 09:00:38,696 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-25 09:00:38,696 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 348144.81, 'new_value': 368277.38}, {'field': 'total_amount', 'old_value': 348144.81, 'new_value': 368277.38}, {'field': 'order_count', 'old_value': 1713, 'new_value': 1812}]
2025-05-25 09:00:38,696 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2V
2025-05-25 09:00:39,165 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2V
2025-05-25 09:00:39,165 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3499600.0, 'new_value': 3899500.0}, {'field': 'total_amount', 'old_value': 3499600.0, 'new_value': 3899500.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-25 09:00:39,165 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-25 09:00:39,681 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-25 09:00:39,681 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 69060.84, 'new_value': 72415.99}, {'field': 'offline_amount', 'old_value': 228546.33, 'new_value': 244915.22}, {'field': 'total_amount', 'old_value': 297607.17, 'new_value': 317331.21}, {'field': 'order_count', 'old_value': 3630, 'new_value': 3853}]
2025-05-25 09:00:39,681 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-25 09:00:40,103 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-25 09:00:40,103 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61502.5, 'new_value': 66715.3}, {'field': 'total_amount', 'old_value': 61502.5, 'new_value': 66715.3}, {'field': 'order_count', 'old_value': 1619, 'new_value': 1632}]
2025-05-25 09:00:40,103 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-25 09:00:40,618 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-25 09:00:40,618 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 627485.15, 'new_value': 658750.15}, {'field': 'total_amount', 'old_value': 627485.15, 'new_value': 658750.15}, {'field': 'order_count', 'old_value': 1693, 'new_value': 1807}]
2025-05-25 09:00:40,618 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-25 09:00:41,103 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-25 09:00:41,103 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 656060.0, 'new_value': 721161.0}, {'field': 'total_amount', 'old_value': 656060.0, 'new_value': 721161.0}, {'field': 'order_count', 'old_value': 100, 'new_value': 109}]
2025-05-25 09:00:41,103 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBV
2025-05-25 09:00:41,524 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBV
2025-05-25 09:00:41,524 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20758.0, 'new_value': 21258.0}, {'field': 'total_amount', 'old_value': 20758.0, 'new_value': 21258.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-05-25 09:00:41,524 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-25 09:00:41,946 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-25 09:00:41,946 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1596.0, 'new_value': 2229.0}, {'field': 'offline_amount', 'old_value': 567065.0, 'new_value': 584503.0}, {'field': 'total_amount', 'old_value': 568661.0, 'new_value': 586732.0}, {'field': 'order_count', 'old_value': 249, 'new_value': 262}]
2025-05-25 09:00:41,946 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-25 09:00:42,337 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-25 09:00:42,337 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 176631.0, 'new_value': 187363.0}, {'field': 'total_amount', 'old_value': 176631.0, 'new_value': 187363.0}, {'field': 'order_count', 'old_value': 3328, 'new_value': 3341}]
2025-05-25 09:00:42,337 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-25 09:00:42,868 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-25 09:00:42,868 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 275194.0, 'new_value': 282605.0}, {'field': 'total_amount', 'old_value': 275194.0, 'new_value': 282605.0}, {'field': 'order_count', 'old_value': 71, 'new_value': 74}]
2025-05-25 09:00:42,868 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-25 09:00:43,384 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-25 09:00:43,384 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 686389.0, 'new_value': 711538.0}, {'field': 'total_amount', 'old_value': 686389.0, 'new_value': 711538.0}, {'field': 'order_count', 'old_value': 165, 'new_value': 173}]
2025-05-25 09:00:43,384 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-25 09:00:43,821 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-25 09:00:43,821 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 662319.52, 'new_value': 705492.85}, {'field': 'total_amount', 'old_value': 662319.52, 'new_value': 705492.85}, {'field': 'order_count', 'old_value': 4618, 'new_value': 4923}]
2025-05-25 09:00:43,821 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-25 09:00:44,259 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-25 09:00:44,259 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69836.0, 'new_value': 73655.0}, {'field': 'total_amount', 'old_value': 69836.0, 'new_value': 73655.0}, {'field': 'order_count', 'old_value': 176, 'new_value': 188}]
2025-05-25 09:00:44,259 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-25 09:00:44,696 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-25 09:00:44,696 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9133.41, 'new_value': 9755.51}, {'field': 'total_amount', 'old_value': 24433.41, 'new_value': 25055.51}, {'field': 'order_count', 'old_value': 142, 'new_value': 151}]
2025-05-25 09:00:44,696 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-25 09:00:45,181 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-25 09:00:45,181 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 115893.6, 'new_value': 123553.4}, {'field': 'total_amount', 'old_value': 115893.6, 'new_value': 123553.4}, {'field': 'order_count', 'old_value': 226, 'new_value': 240}]
2025-05-25 09:00:45,181 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-25 09:00:45,696 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-25 09:00:45,696 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 101835.7, 'new_value': 111011.7}, {'field': 'offline_amount', 'old_value': 68235.18, 'new_value': 73419.78}, {'field': 'total_amount', 'old_value': 170070.88, 'new_value': 184431.48}, {'field': 'order_count', 'old_value': 1148, 'new_value': 1239}]
2025-05-25 09:00:45,696 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-25 09:00:46,181 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-25 09:00:46,181 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 322625.3, 'new_value': 339299.6}, {'field': 'total_amount', 'old_value': 322625.3, 'new_value': 339299.6}, {'field': 'order_count', 'old_value': 400, 'new_value': 417}]
2025-05-25 09:00:46,181 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-25 09:00:46,634 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-25 09:00:46,634 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 815080.0, 'new_value': 868614.0}, {'field': 'total_amount', 'old_value': 815080.0, 'new_value': 868614.0}, {'field': 'order_count', 'old_value': 50934, 'new_value': 50969}]
2025-05-25 09:00:46,634 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-25 09:00:47,087 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-25 09:00:47,087 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 610990.0, 'new_value': 640086.0}, {'field': 'total_amount', 'old_value': 610990.0, 'new_value': 640086.0}, {'field': 'order_count', 'old_value': 70, 'new_value': 74}]
2025-05-25 09:00:47,087 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-25 09:00:47,540 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-25 09:00:47,540 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 297847.97, 'new_value': 311997.67}, {'field': 'offline_amount', 'old_value': 1147259.49, 'new_value': 1219986.37}, {'field': 'total_amount', 'old_value': 1445107.46, 'new_value': 1531984.04}, {'field': 'order_count', 'old_value': 7255, 'new_value': 7667}]
2025-05-25 09:00:47,540 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-25 09:00:47,946 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-25 09:00:47,946 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 124251.1, 'new_value': 131017.5}, {'field': 'offline_amount', 'old_value': 102378.1, 'new_value': 106651.6}, {'field': 'total_amount', 'old_value': 226629.2, 'new_value': 237669.1}, {'field': 'order_count', 'old_value': 5295, 'new_value': 5553}]
2025-05-25 09:00:47,946 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-25 09:00:48,353 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-25 09:00:48,353 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 227663.03, 'new_value': 242299.74}, {'field': 'total_amount', 'old_value': 227663.03, 'new_value': 242299.74}, {'field': 'order_count', 'old_value': 1400, 'new_value': 1486}]
2025-05-25 09:00:48,353 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-25 09:00:48,759 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-25 09:00:48,759 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30350.26, 'new_value': 31433.01}, {'field': 'offline_amount', 'old_value': 314698.25, 'new_value': 338247.27}, {'field': 'total_amount', 'old_value': 345048.51, 'new_value': 369680.28}, {'field': 'order_count', 'old_value': 9608, 'new_value': 9754}]
2025-05-25 09:00:48,759 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6F
2025-05-25 09:00:49,212 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6F
2025-05-25 09:00:49,212 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11370.0, 'new_value': 15370.0}, {'field': 'total_amount', 'old_value': 11370.0, 'new_value': 15370.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-25 09:00:49,212 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-25 09:00:49,634 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-25 09:00:49,634 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63469.1, 'new_value': 94210.1}, {'field': 'total_amount', 'old_value': 64274.1, 'new_value': 95015.1}, {'field': 'order_count', 'old_value': 16297, 'new_value': 16304}]
2025-05-25 09:00:49,634 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-25 09:00:50,103 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-25 09:00:50,103 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1752190.0, 'new_value': 1823006.0}, {'field': 'total_amount', 'old_value': 1752190.0, 'new_value': 1823006.0}, {'field': 'order_count', 'old_value': 6906, 'new_value': 7255}]
2025-05-25 09:00:50,103 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-25 09:00:50,524 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-25 09:00:50,524 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 258862.9, 'new_value': 271379.0}, {'field': 'total_amount', 'old_value': 258862.9, 'new_value': 271379.0}, {'field': 'order_count', 'old_value': 1458, 'new_value': 1540}]
2025-05-25 09:00:50,524 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-25 09:00:50,931 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-25 09:00:50,931 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53828.1, 'new_value': 55623.3}, {'field': 'offline_amount', 'old_value': 41443.49, 'new_value': 43737.64}, {'field': 'total_amount', 'old_value': 95271.59, 'new_value': 99360.94}, {'field': 'order_count', 'old_value': 7989, 'new_value': 8315}]
2025-05-25 09:00:50,931 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-25 09:00:51,399 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-25 09:00:51,399 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87953.56, 'new_value': 91615.56}, {'field': 'total_amount', 'old_value': 87953.56, 'new_value': 91615.56}, {'field': 'order_count', 'old_value': 4522, 'new_value': 4713}]
2025-05-25 09:00:51,399 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-25 09:00:51,993 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-25 09:00:51,993 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 549628.0, 'new_value': 556697.0}, {'field': 'total_amount', 'old_value': 549628.0, 'new_value': 556697.0}, {'field': 'order_count', 'old_value': 87, 'new_value': 88}]
2025-05-25 09:00:51,993 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-25 09:00:52,431 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-25 09:00:52,431 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 590487.0, 'new_value': 598389.0}, {'field': 'total_amount', 'old_value': 590487.0, 'new_value': 598389.0}, {'field': 'order_count', 'old_value': 443, 'new_value': 468}]
2025-05-25 09:00:52,446 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-25 09:00:52,884 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-25 09:00:52,884 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 268857.0, 'new_value': 285754.0}, {'field': 'total_amount', 'old_value': 268857.0, 'new_value': 285754.0}, {'field': 'order_count', 'old_value': 330, 'new_value': 351}]
2025-05-25 09:00:52,884 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-25 09:00:53,306 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-25 09:00:53,306 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91208.0, 'new_value': 97462.0}, {'field': 'total_amount', 'old_value': 91208.0, 'new_value': 97462.0}, {'field': 'order_count', 'old_value': 6141, 'new_value': 6522}]
2025-05-25 09:00:53,306 - INFO - 日期 2025-05 处理完成 - 更新: 52 条，插入: 0 条，错误: 0 条
2025-05-25 09:00:53,306 - INFO - 数据同步完成！更新: 52 条，插入: 0 条，错误: 0 条
2025-05-25 09:00:53,306 - INFO - =================同步完成====================
2025-05-25 12:00:01,970 - INFO - =================使用默认全量同步=============
2025-05-25 12:00:03,454 - INFO - MySQL查询成功，共获取 3299 条记录
2025-05-25 12:00:03,454 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-25 12:00:03,485 - INFO - 开始处理日期: 2025-01
2025-05-25 12:00:03,485 - INFO - Request Parameters - Page 1:
2025-05-25 12:00:03,485 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:00:03,485 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:00:04,845 - INFO - Response - Page 1:
2025-05-25 12:00:05,048 - INFO - 第 1 页获取到 100 条记录
2025-05-25 12:00:05,048 - INFO - Request Parameters - Page 2:
2025-05-25 12:00:05,048 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:00:05,048 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:00:05,564 - INFO - Response - Page 2:
2025-05-25 12:00:05,767 - INFO - 第 2 页获取到 100 条记录
2025-05-25 12:00:05,767 - INFO - Request Parameters - Page 3:
2025-05-25 12:00:05,767 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:00:05,767 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:00:06,360 - INFO - Response - Page 3:
2025-05-25 12:00:06,564 - INFO - 第 3 页获取到 100 条记录
2025-05-25 12:00:06,564 - INFO - Request Parameters - Page 4:
2025-05-25 12:00:06,564 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:00:06,564 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:00:07,079 - INFO - Response - Page 4:
2025-05-25 12:00:07,282 - INFO - 第 4 页获取到 100 条记录
2025-05-25 12:00:07,282 - INFO - Request Parameters - Page 5:
2025-05-25 12:00:07,282 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:00:07,282 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:00:08,032 - INFO - Response - Page 5:
2025-05-25 12:00:08,235 - INFO - 第 5 页获取到 100 条记录
2025-05-25 12:00:08,235 - INFO - Request Parameters - Page 6:
2025-05-25 12:00:08,235 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:00:08,235 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:00:08,798 - INFO - Response - Page 6:
2025-05-25 12:00:09,001 - INFO - 第 6 页获取到 100 条记录
2025-05-25 12:00:09,001 - INFO - Request Parameters - Page 7:
2025-05-25 12:00:09,001 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:00:09,001 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:00:09,470 - INFO - Response - Page 7:
2025-05-25 12:00:09,673 - INFO - 第 7 页获取到 82 条记录
2025-05-25 12:00:09,673 - INFO - 查询完成，共获取到 682 条记录
2025-05-25 12:00:09,673 - INFO - 获取到 682 条表单数据
2025-05-25 12:00:09,673 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-25 12:00:09,689 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 12:00:09,689 - INFO - 开始处理日期: 2025-02
2025-05-25 12:00:09,689 - INFO - Request Parameters - Page 1:
2025-05-25 12:00:09,689 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:00:09,689 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:00:10,204 - INFO - Response - Page 1:
2025-05-25 12:00:10,407 - INFO - 第 1 页获取到 100 条记录
2025-05-25 12:00:10,407 - INFO - Request Parameters - Page 2:
2025-05-25 12:00:10,407 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:00:10,407 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:00:10,892 - INFO - Response - Page 2:
2025-05-25 12:00:11,095 - INFO - 第 2 页获取到 100 条记录
2025-05-25 12:00:11,095 - INFO - Request Parameters - Page 3:
2025-05-25 12:00:11,095 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:00:11,095 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:00:11,657 - INFO - Response - Page 3:
2025-05-25 12:00:11,860 - INFO - 第 3 页获取到 100 条记录
2025-05-25 12:00:11,860 - INFO - Request Parameters - Page 4:
2025-05-25 12:00:11,860 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:00:11,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:00:12,313 - INFO - Response - Page 4:
2025-05-25 12:00:12,517 - INFO - 第 4 页获取到 100 条记录
2025-05-25 12:00:12,517 - INFO - Request Parameters - Page 5:
2025-05-25 12:00:12,517 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:00:12,517 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:00:13,063 - INFO - Response - Page 5:
2025-05-25 12:00:13,267 - INFO - 第 5 页获取到 100 条记录
2025-05-25 12:00:13,267 - INFO - Request Parameters - Page 6:
2025-05-25 12:00:13,267 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:00:13,267 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:00:13,798 - INFO - Response - Page 6:
2025-05-25 12:00:14,001 - INFO - 第 6 页获取到 100 条记录
2025-05-25 12:00:14,001 - INFO - Request Parameters - Page 7:
2025-05-25 12:00:14,001 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:00:14,001 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:00:14,454 - INFO - Response - Page 7:
2025-05-25 12:00:14,657 - INFO - 第 7 页获取到 70 条记录
2025-05-25 12:00:14,657 - INFO - 查询完成，共获取到 670 条记录
2025-05-25 12:00:14,657 - INFO - 获取到 670 条表单数据
2025-05-25 12:00:14,657 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-25 12:00:14,673 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 12:00:14,673 - INFO - 开始处理日期: 2025-03
2025-05-25 12:00:14,673 - INFO - Request Parameters - Page 1:
2025-05-25 12:00:14,673 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:00:14,673 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:00:15,423 - INFO - Response - Page 1:
2025-05-25 12:00:15,626 - INFO - 第 1 页获取到 100 条记录
2025-05-25 12:00:15,626 - INFO - Request Parameters - Page 2:
2025-05-25 12:00:15,626 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:00:15,626 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:00:16,126 - INFO - Response - Page 2:
2025-05-25 12:00:16,329 - INFO - 第 2 页获取到 100 条记录
2025-05-25 12:00:16,329 - INFO - Request Parameters - Page 3:
2025-05-25 12:00:16,329 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:00:16,329 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:00:16,845 - INFO - Response - Page 3:
2025-05-25 12:00:17,048 - INFO - 第 3 页获取到 100 条记录
2025-05-25 12:00:17,048 - INFO - Request Parameters - Page 4:
2025-05-25 12:00:17,048 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:00:17,048 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:00:17,517 - INFO - Response - Page 4:
2025-05-25 12:00:17,720 - INFO - 第 4 页获取到 100 条记录
2025-05-25 12:00:17,720 - INFO - Request Parameters - Page 5:
2025-05-25 12:00:17,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:00:17,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:00:18,235 - INFO - Response - Page 5:
2025-05-25 12:00:18,438 - INFO - 第 5 页获取到 100 条记录
2025-05-25 12:00:18,438 - INFO - Request Parameters - Page 6:
2025-05-25 12:00:18,438 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:00:18,438 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:00:18,938 - INFO - Response - Page 6:
2025-05-25 12:00:19,142 - INFO - 第 6 页获取到 100 条记录
2025-05-25 12:00:19,142 - INFO - Request Parameters - Page 7:
2025-05-25 12:00:19,142 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:00:19,142 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:00:19,610 - INFO - Response - Page 7:
2025-05-25 12:00:19,813 - INFO - 第 7 页获取到 61 条记录
2025-05-25 12:00:19,813 - INFO - 查询完成，共获取到 661 条记录
2025-05-25 12:00:19,813 - INFO - 获取到 661 条表单数据
2025-05-25 12:00:19,813 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-25 12:00:19,829 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 12:00:19,829 - INFO - 开始处理日期: 2025-04
2025-05-25 12:00:19,829 - INFO - Request Parameters - Page 1:
2025-05-25 12:00:19,829 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:00:19,829 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:00:20,392 - INFO - Response - Page 1:
2025-05-25 12:00:20,595 - INFO - 第 1 页获取到 100 条记录
2025-05-25 12:00:20,595 - INFO - Request Parameters - Page 2:
2025-05-25 12:00:20,595 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:00:20,595 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:00:21,126 - INFO - Response - Page 2:
2025-05-25 12:00:21,329 - INFO - 第 2 页获取到 100 条记录
2025-05-25 12:00:21,329 - INFO - Request Parameters - Page 3:
2025-05-25 12:00:21,329 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:00:21,329 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:00:21,938 - INFO - Response - Page 3:
2025-05-25 12:00:22,142 - INFO - 第 3 页获取到 100 条记录
2025-05-25 12:00:22,142 - INFO - Request Parameters - Page 4:
2025-05-25 12:00:22,142 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:00:22,142 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:00:22,626 - INFO - Response - Page 4:
2025-05-25 12:00:22,829 - INFO - 第 4 页获取到 100 条记录
2025-05-25 12:00:22,829 - INFO - Request Parameters - Page 5:
2025-05-25 12:00:22,829 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:00:22,829 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:00:23,313 - INFO - Response - Page 5:
2025-05-25 12:00:23,517 - INFO - 第 5 页获取到 100 条记录
2025-05-25 12:00:23,517 - INFO - Request Parameters - Page 6:
2025-05-25 12:00:23,517 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:00:23,517 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:00:24,001 - INFO - Response - Page 6:
2025-05-25 12:00:24,204 - INFO - 第 6 页获取到 100 条记录
2025-05-25 12:00:24,204 - INFO - Request Parameters - Page 7:
2025-05-25 12:00:24,204 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:00:24,204 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:00:24,673 - INFO - Response - Page 7:
2025-05-25 12:00:24,876 - INFO - 第 7 页获取到 56 条记录
2025-05-25 12:00:24,876 - INFO - 查询完成，共获取到 656 条记录
2025-05-25 12:00:24,876 - INFO - 获取到 656 条表单数据
2025-05-25 12:00:24,876 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-25 12:00:24,892 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 12:00:24,892 - INFO - 开始处理日期: 2025-05
2025-05-25 12:00:24,892 - INFO - Request Parameters - Page 1:
2025-05-25 12:00:24,892 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:00:24,892 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:00:25,423 - INFO - Response - Page 1:
2025-05-25 12:00:25,626 - INFO - 第 1 页获取到 100 条记录
2025-05-25 12:00:25,626 - INFO - Request Parameters - Page 2:
2025-05-25 12:00:25,626 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:00:25,626 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:00:26,157 - INFO - Response - Page 2:
2025-05-25 12:00:26,360 - INFO - 第 2 页获取到 100 条记录
2025-05-25 12:00:26,360 - INFO - Request Parameters - Page 3:
2025-05-25 12:00:26,360 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:00:26,360 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:00:26,907 - INFO - Response - Page 3:
2025-05-25 12:00:27,110 - INFO - 第 3 页获取到 100 条记录
2025-05-25 12:00:27,110 - INFO - Request Parameters - Page 4:
2025-05-25 12:00:27,110 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:00:27,110 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:00:27,610 - INFO - Response - Page 4:
2025-05-25 12:00:27,813 - INFO - 第 4 页获取到 100 条记录
2025-05-25 12:00:27,813 - INFO - Request Parameters - Page 5:
2025-05-25 12:00:27,813 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:00:27,813 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:00:28,376 - INFO - Response - Page 5:
2025-05-25 12:00:28,579 - INFO - 第 5 页获取到 100 条记录
2025-05-25 12:00:28,579 - INFO - Request Parameters - Page 6:
2025-05-25 12:00:28,579 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:00:28,579 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:00:29,032 - INFO - Response - Page 6:
2025-05-25 12:00:29,235 - INFO - 第 6 页获取到 100 条记录
2025-05-25 12:00:29,235 - INFO - Request Parameters - Page 7:
2025-05-25 12:00:29,235 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 12:00:29,235 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 12:00:29,579 - INFO - Response - Page 7:
2025-05-25 12:00:29,782 - INFO - 第 7 页获取到 29 条记录
2025-05-25 12:00:29,782 - INFO - 查询完成，共获取到 629 条记录
2025-05-25 12:00:29,782 - INFO - 获取到 629 条表单数据
2025-05-25 12:00:29,782 - INFO - 当前日期 2025-05 有 630 条MySQL数据需要处理
2025-05-25 12:00:29,782 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2O
2025-05-25 12:00:30,235 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2O
2025-05-25 12:00:30,235 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 255200.0, 'new_value': 291200.0}, {'field': 'total_amount', 'old_value': 255200.0, 'new_value': 291200.0}]
2025-05-25 12:00:30,235 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-25 12:00:30,704 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-25 12:00:30,704 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1892.0, 'new_value': 1982.0}, {'field': 'offline_amount', 'old_value': 37168.0, 'new_value': 40308.0}, {'field': 'total_amount', 'old_value': 39060.0, 'new_value': 42290.0}, {'field': 'order_count', 'old_value': 538, 'new_value': 578}]
2025-05-25 12:00:30,704 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-25 12:00:31,173 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-25 12:00:31,173 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 352588.0, 'new_value': 371890.0}, {'field': 'total_amount', 'old_value': 352588.0, 'new_value': 371890.0}, {'field': 'order_count', 'old_value': 264, 'new_value': 276}]
2025-05-25 12:00:31,173 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-25 12:00:31,610 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-25 12:00:31,610 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45845.0, 'new_value': 46375.0}, {'field': 'total_amount', 'old_value': 47435.0, 'new_value': 47965.0}, {'field': 'order_count', 'old_value': 179, 'new_value': 181}]
2025-05-25 12:00:31,610 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-25 12:00:32,016 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-25 12:00:32,016 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 671733.98, 'new_value': 707117.98}, {'field': 'total_amount', 'old_value': 671733.98, 'new_value': 707117.98}, {'field': 'order_count', 'old_value': 2063, 'new_value': 2191}]
2025-05-25 12:00:32,016 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-25 12:00:32,438 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-25 12:00:32,438 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59010.0, 'new_value': 59754.0}, {'field': 'total_amount', 'old_value': 61907.0, 'new_value': 62651.0}, {'field': 'order_count', 'old_value': 32, 'new_value': 34}]
2025-05-25 12:00:32,438 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-25 12:00:32,845 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-25 12:00:32,845 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30667.87, 'new_value': 31890.87}, {'field': 'offline_amount', 'old_value': 15170.57, 'new_value': 15604.57}, {'field': 'total_amount', 'old_value': 45838.44, 'new_value': 47495.44}, {'field': 'order_count', 'old_value': 2326, 'new_value': 2398}]
2025-05-25 12:00:32,845 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-25 12:00:33,282 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-25 12:00:33,282 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50094.78, 'new_value': 51299.78}, {'field': 'total_amount', 'old_value': 50094.78, 'new_value': 51299.78}, {'field': 'order_count', 'old_value': 112, 'new_value': 120}]
2025-05-25 12:00:33,282 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-25 12:00:33,735 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-25 12:00:33,735 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 331980.0, 'new_value': 361500.0}, {'field': 'total_amount', 'old_value': 331980.0, 'new_value': 361500.0}, {'field': 'order_count', 'old_value': 199, 'new_value': 215}]
2025-05-25 12:00:33,735 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-25 12:00:34,173 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-25 12:00:34,173 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 101309.0, 'new_value': 109225.0}, {'field': 'total_amount', 'old_value': 101309.0, 'new_value': 109225.0}, {'field': 'order_count', 'old_value': 116, 'new_value': 122}]
2025-05-25 12:00:34,173 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGC
2025-05-25 12:00:34,673 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGC
2025-05-25 12:00:34,673 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80754.0, 'new_value': 86854.0}, {'field': 'total_amount', 'old_value': 80754.0, 'new_value': 86854.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-05-25 12:00:34,673 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-25 12:00:35,220 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-25 12:00:35,220 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47280.0, 'new_value': 49860.0}, {'field': 'total_amount', 'old_value': 51400.0, 'new_value': 53980.0}, {'field': 'order_count', 'old_value': 492, 'new_value': 514}]
2025-05-25 12:00:35,220 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJC
2025-05-25 12:00:35,657 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJC
2025-05-25 12:00:35,657 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31353.8, 'new_value': 31681.8}, {'field': 'total_amount', 'old_value': 33862.4, 'new_value': 34190.4}, {'field': 'order_count', 'old_value': 38, 'new_value': 40}]
2025-05-25 12:00:35,657 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMKC
2025-05-25 12:00:36,141 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMKC
2025-05-25 12:00:36,141 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44992.0, 'new_value': 46972.0}, {'field': 'total_amount', 'old_value': 44992.0, 'new_value': 46972.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-05-25 12:00:36,141 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-25 12:00:36,595 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-25 12:00:36,595 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 253612.0, 'new_value': 268210.0}, {'field': 'total_amount', 'old_value': 253612.0, 'new_value': 268210.0}, {'field': 'order_count', 'old_value': 52, 'new_value': 53}]
2025-05-25 12:00:36,595 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-25 12:00:37,032 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-25 12:00:37,032 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53098.46, 'new_value': 55091.35}, {'field': 'offline_amount', 'old_value': 103424.85, 'new_value': 107768.85}, {'field': 'total_amount', 'old_value': 156523.31, 'new_value': 162860.2}, {'field': 'order_count', 'old_value': 1807, 'new_value': 1870}]
2025-05-25 12:00:37,032 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-25 12:00:37,438 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-25 12:00:37,438 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19768.0, 'new_value': 20559.03}, {'field': 'offline_amount', 'old_value': 24373.81, 'new_value': 25425.61}, {'field': 'total_amount', 'old_value': 44141.81, 'new_value': 45984.64}, {'field': 'order_count', 'old_value': 2148, 'new_value': 2243}]
2025-05-25 12:00:37,438 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-25 12:00:37,938 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-25 12:00:37,938 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 279242.3, 'new_value': 291543.0}, {'field': 'total_amount', 'old_value': 394262.0, 'new_value': 406562.7}, {'field': 'order_count', 'old_value': 3126, 'new_value': 3283}]
2025-05-25 12:00:37,938 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-25 12:00:38,454 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-25 12:00:38,454 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96353.0, 'new_value': 100820.0}, {'field': 'total_amount', 'old_value': 96353.0, 'new_value': 100820.0}, {'field': 'order_count', 'old_value': 5271, 'new_value': 5500}]
2025-05-25 12:00:38,454 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-25 12:00:38,876 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-25 12:00:38,876 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 416695.71, 'new_value': 449382.71}, {'field': 'total_amount', 'old_value': 416695.71, 'new_value': 449382.71}, {'field': 'order_count', 'old_value': 400, 'new_value': 413}]
2025-05-25 12:00:38,876 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMYC
2025-05-25 12:00:39,282 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMYC
2025-05-25 12:00:39,282 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15977.0, 'new_value': 16327.0}, {'field': 'total_amount', 'old_value': 15977.0, 'new_value': 16327.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-25 12:00:39,282 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-25 12:00:39,720 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-25 12:00:39,720 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 132477.88, 'new_value': 136295.91}, {'field': 'total_amount', 'old_value': 132477.88, 'new_value': 136295.91}, {'field': 'order_count', 'old_value': 1555, 'new_value': 1605}]
2025-05-25 12:00:39,720 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-25 12:00:40,173 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-25 12:00:40,173 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1032563.0, 'new_value': 1072445.0}, {'field': 'offline_amount', 'old_value': 301896.0, 'new_value': 314895.0}, {'field': 'total_amount', 'old_value': 1334459.0, 'new_value': 1387340.0}, {'field': 'order_count', 'old_value': 1551, 'new_value': 1661}]
2025-05-25 12:00:40,173 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-25 12:00:40,657 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-25 12:00:40,657 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6678.55, 'new_value': 7483.35}, {'field': 'offline_amount', 'old_value': 75915.7, 'new_value': 80207.7}, {'field': 'total_amount', 'old_value': 82594.25, 'new_value': 87691.05}, {'field': 'order_count', 'old_value': 1812, 'new_value': 1949}]
2025-05-25 12:00:40,673 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-25 12:00:41,126 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-25 12:00:41,126 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 246905.0, 'new_value': 259405.0}, {'field': 'total_amount', 'old_value': 276905.0, 'new_value': 289405.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 46}]
2025-05-25 12:00:41,126 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-25 12:00:41,548 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-25 12:00:41,548 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 312721.89, 'new_value': 317221.89}, {'field': 'total_amount', 'old_value': 312721.89, 'new_value': 317221.89}, {'field': 'order_count', 'old_value': 53, 'new_value': 54}]
2025-05-25 12:00:41,548 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-25 12:00:41,985 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-25 12:00:41,985 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 279380.13, 'new_value': 285180.13}, {'field': 'total_amount', 'old_value': 318740.13, 'new_value': 324540.13}, {'field': 'order_count', 'old_value': 45, 'new_value': 46}]
2025-05-25 12:00:41,985 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-25 12:00:42,423 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-25 12:00:42,423 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15394.7, 'new_value': 15688.7}, {'field': 'offline_amount', 'old_value': 45873.6, 'new_value': 52813.6}, {'field': 'total_amount', 'old_value': 61268.3, 'new_value': 68502.3}, {'field': 'order_count', 'old_value': 646, 'new_value': 696}]
2025-05-25 12:00:42,423 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-25 12:00:42,876 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-25 12:00:42,876 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6233.0, 'new_value': 6479.0}, {'field': 'offline_amount', 'old_value': 19291.0, 'new_value': 20181.0}, {'field': 'total_amount', 'old_value': 25524.0, 'new_value': 26660.0}, {'field': 'order_count', 'old_value': 69, 'new_value': 72}]
2025-05-25 12:00:42,876 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-25 12:00:43,360 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-25 12:00:43,360 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 165604.0, 'new_value': 171554.0}, {'field': 'total_amount', 'old_value': 165604.0, 'new_value': 171554.0}, {'field': 'order_count', 'old_value': 327, 'new_value': 352}]
2025-05-25 12:00:43,360 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCD
2025-05-25 12:00:43,798 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCD
2025-05-25 12:00:43,798 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55879.5, 'new_value': 62267.5}, {'field': 'total_amount', 'old_value': 55879.5, 'new_value': 62267.5}, {'field': 'order_count', 'old_value': 21, 'new_value': 23}]
2025-05-25 12:00:43,798 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-25 12:00:44,251 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-25 12:00:44,251 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72003.0, 'new_value': 76589.0}, {'field': 'total_amount', 'old_value': 72003.0, 'new_value': 76589.0}, {'field': 'order_count', 'old_value': 626, 'new_value': 661}]
2025-05-25 12:00:44,251 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-25 12:00:44,782 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-25 12:00:44,782 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40796.97, 'new_value': 41539.27}, {'field': 'total_amount', 'old_value': 40796.97, 'new_value': 41539.27}, {'field': 'order_count', 'old_value': 2980, 'new_value': 3044}]
2025-05-25 12:00:44,782 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-25 12:00:45,204 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-25 12:00:45,204 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 162378.02, 'new_value': 171144.71}, {'field': 'offline_amount', 'old_value': 28179.54, 'new_value': 29144.76}, {'field': 'total_amount', 'old_value': 190557.56, 'new_value': 200289.47}, {'field': 'order_count', 'old_value': 695, 'new_value': 733}]
2025-05-25 12:00:45,204 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-25 12:00:45,626 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-25 12:00:45,626 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 151544.0, 'new_value': 162321.0}, {'field': 'offline_amount', 'old_value': 58678.01, 'new_value': 61419.01}, {'field': 'total_amount', 'old_value': 210222.01, 'new_value': 223740.01}, {'field': 'order_count', 'old_value': 1354, 'new_value': 1450}]
2025-05-25 12:00:45,626 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-25 12:00:46,079 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-25 12:00:46,079 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39974.0, 'new_value': 41495.0}, {'field': 'total_amount', 'old_value': 45350.0, 'new_value': 46871.0}, {'field': 'order_count', 'old_value': 204, 'new_value': 213}]
2025-05-25 12:00:46,079 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-25 12:00:46,563 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-25 12:00:46,563 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8312.0, 'new_value': 9107.0}, {'field': 'total_amount', 'old_value': 9805.0, 'new_value': 10600.0}, {'field': 'order_count', 'old_value': 187, 'new_value': 209}]
2025-05-25 12:00:46,563 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-25 12:00:47,126 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-25 12:00:47,126 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84182.43, 'new_value': 87938.46}, {'field': 'total_amount', 'old_value': 84182.43, 'new_value': 87938.46}, {'field': 'order_count', 'old_value': 2270, 'new_value': 2378}]
2025-05-25 12:00:47,126 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-25 12:00:47,548 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-25 12:00:47,548 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69263.94, 'new_value': 71409.44}, {'field': 'total_amount', 'old_value': 69263.94, 'new_value': 71409.44}, {'field': 'order_count', 'old_value': 121, 'new_value': 125}]
2025-05-25 12:00:47,548 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMMD
2025-05-25 12:00:48,079 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMMD
2025-05-25 12:00:48,079 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9573.0, 'new_value': 10253.0}, {'field': 'total_amount', 'old_value': 9573.0, 'new_value': 10253.0}, {'field': 'order_count', 'old_value': 304, 'new_value': 305}]
2025-05-25 12:00:48,079 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-25 12:00:48,501 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-25 12:00:48,501 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 197196.1, 'new_value': 198511.1}, {'field': 'total_amount', 'old_value': 197196.1, 'new_value': 198511.1}, {'field': 'order_count', 'old_value': 60, 'new_value': 62}]
2025-05-25 12:00:48,501 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-25 12:00:48,985 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-25 12:00:48,985 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 145162.0, 'new_value': 155264.0}, {'field': 'offline_amount', 'old_value': 62533.26, 'new_value': 66447.16}, {'field': 'total_amount', 'old_value': 207695.26, 'new_value': 221711.16}, {'field': 'order_count', 'old_value': 1469, 'new_value': 1563}]
2025-05-25 12:00:48,985 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-25 12:00:49,391 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-25 12:00:49,391 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10022.47, 'new_value': 10486.45}, {'field': 'offline_amount', 'old_value': 162709.49, 'new_value': 169720.92}, {'field': 'total_amount', 'old_value': 172731.96, 'new_value': 180207.37}, {'field': 'order_count', 'old_value': 1890, 'new_value': 1966}]
2025-05-25 12:00:49,391 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-25 12:00:49,876 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-25 12:00:49,876 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49688.0, 'new_value': 50928.3}, {'field': 'total_amount', 'old_value': 49699.9, 'new_value': 50940.2}, {'field': 'order_count', 'old_value': 287, 'new_value': 297}]
2025-05-25 12:00:49,876 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-25 12:00:50,391 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-25 12:00:50,391 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 122723.0, 'new_value': 137788.0}, {'field': 'offline_amount', 'old_value': 73143.0, 'new_value': 78651.0}, {'field': 'total_amount', 'old_value': 195866.0, 'new_value': 216439.0}, {'field': 'order_count', 'old_value': 60, 'new_value': 65}]
2025-05-25 12:00:50,391 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-25 12:00:50,829 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-25 12:00:50,829 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1550.0, 'new_value': 1600.0}, {'field': 'offline_amount', 'old_value': 27457.0, 'new_value': 29374.0}, {'field': 'total_amount', 'old_value': 29007.0, 'new_value': 30974.0}, {'field': 'order_count', 'old_value': 106, 'new_value': 113}]
2025-05-25 12:00:50,829 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-25 12:00:51,298 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-25 12:00:51,298 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7045.3, 'new_value': 7154.6}, {'field': 'offline_amount', 'old_value': 94261.09, 'new_value': 100947.82}, {'field': 'total_amount', 'old_value': 101306.39, 'new_value': 108102.42}, {'field': 'order_count', 'old_value': 2412, 'new_value': 2595}]
2025-05-25 12:00:51,298 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-25 12:00:51,735 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-25 12:00:51,735 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 192754.0, 'new_value': 211838.0}, {'field': 'total_amount', 'old_value': 192754.0, 'new_value': 211838.0}, {'field': 'order_count', 'old_value': 1021, 'new_value': 1107}]
2025-05-25 12:00:51,735 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-25 12:00:52,188 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-25 12:00:52,188 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 190644.21, 'new_value': 202160.65}, {'field': 'total_amount', 'old_value': 190644.21, 'new_value': 202160.65}, {'field': 'order_count', 'old_value': 640, 'new_value': 679}]
2025-05-25 12:00:52,188 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-25 12:00:52,641 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-25 12:00:52,641 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 79149.31, 'new_value': 82412.55}, {'field': 'offline_amount', 'old_value': 38648.01, 'new_value': 41026.13}, {'field': 'total_amount', 'old_value': 117797.32, 'new_value': 123438.68}, {'field': 'order_count', 'old_value': 4080, 'new_value': 4266}]
2025-05-25 12:00:52,641 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-25 12:00:53,110 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-25 12:00:53,110 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 134417.0, 'new_value': 140384.0}, {'field': 'total_amount', 'old_value': 134417.0, 'new_value': 140384.0}, {'field': 'order_count', 'old_value': 3365, 'new_value': 3508}]
2025-05-25 12:00:53,110 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-25 12:00:53,594 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-25 12:00:53,594 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 80684.02, 'new_value': 86335.32}, {'field': 'offline_amount', 'old_value': 833271.66, 'new_value': 877761.16}, {'field': 'total_amount', 'old_value': 913955.68, 'new_value': 964096.48}, {'field': 'order_count', 'old_value': 2906, 'new_value': 3047}]
2025-05-25 12:00:53,594 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-25 12:00:54,094 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-25 12:00:54,094 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21109.34, 'new_value': 23280.98}, {'field': 'total_amount', 'old_value': 21109.34, 'new_value': 23280.98}, {'field': 'order_count', 'old_value': 131, 'new_value': 139}]
2025-05-25 12:00:54,094 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-25 12:00:54,501 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-25 12:00:54,501 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 155966.0, 'new_value': 162565.0}, {'field': 'total_amount', 'old_value': 155966.0, 'new_value': 162565.0}, {'field': 'order_count', 'old_value': 5870, 'new_value': 6129}]
2025-05-25 12:00:54,501 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E
2025-05-25 12:00:54,954 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E
2025-05-25 12:00:54,954 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51000.0, 'new_value': 53374.0}, {'field': 'total_amount', 'old_value': 51000.0, 'new_value': 53374.0}, {'field': 'order_count', 'old_value': 61, 'new_value': 65}]
2025-05-25 12:00:54,954 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-25 12:00:55,360 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-25 12:00:55,360 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 461049.2, 'new_value': 469943.2}, {'field': 'total_amount', 'old_value': 492661.2, 'new_value': 501555.2}, {'field': 'order_count', 'old_value': 85, 'new_value': 87}]
2025-05-25 12:00:55,360 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1U
2025-05-25 12:00:55,813 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1U
2025-05-25 12:00:55,813 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78370.0, 'new_value': 83169.0}, {'field': 'total_amount', 'old_value': 78370.0, 'new_value': 83169.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-05-25 12:00:55,813 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-25 12:00:56,282 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-25 12:00:56,282 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 128044.31, 'new_value': 134096.54}, {'field': 'total_amount', 'old_value': 128044.31, 'new_value': 134096.54}, {'field': 'order_count', 'old_value': 4641, 'new_value': 4870}]
2025-05-25 12:00:56,282 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-25 12:00:56,735 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-25 12:00:56,735 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 121659.0, 'new_value': 124457.0}, {'field': 'offline_amount', 'old_value': 62672.0, 'new_value': 76171.0}, {'field': 'total_amount', 'old_value': 184331.0, 'new_value': 200628.0}, {'field': 'order_count', 'old_value': 69, 'new_value': 73}]
2025-05-25 12:00:56,735 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-25 12:00:57,204 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-25 12:00:57,204 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4205.93, 'new_value': 4537.87}, {'field': 'offline_amount', 'old_value': 125427.96, 'new_value': 131372.49}, {'field': 'total_amount', 'old_value': 129633.89, 'new_value': 135910.36}, {'field': 'order_count', 'old_value': 606, 'new_value': 628}]
2025-05-25 12:00:57,204 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-25 12:00:57,610 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-25 12:00:57,610 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51355.0, 'new_value': 51824.0}, {'field': 'total_amount', 'old_value': 51355.0, 'new_value': 51824.0}, {'field': 'order_count', 'old_value': 103, 'new_value': 106}]
2025-05-25 12:00:57,610 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-25 12:00:58,188 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-25 12:00:58,188 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 70590.67, 'new_value': 80473.26}, {'field': 'offline_amount', 'old_value': 478617.3, 'new_value': 512765.3}, {'field': 'total_amount', 'old_value': 549207.97, 'new_value': 593238.56}, {'field': 'order_count', 'old_value': 768, 'new_value': 829}]
2025-05-25 12:00:58,188 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-25 12:00:58,610 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-25 12:00:58,610 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 177137.65, 'new_value': 186840.1}, {'field': 'offline_amount', 'old_value': 392006.86, 'new_value': 418677.39}, {'field': 'total_amount', 'old_value': 569144.51, 'new_value': 605517.49}, {'field': 'order_count', 'old_value': 4193, 'new_value': 4431}]
2025-05-25 12:00:58,610 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-25 12:00:59,063 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-25 12:00:59,063 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 191773.42, 'new_value': 197773.78}, {'field': 'total_amount', 'old_value': 198127.58, 'new_value': 204127.94}, {'field': 'order_count', 'old_value': 1219, 'new_value': 1279}]
2025-05-25 12:00:59,063 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-25 12:00:59,516 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-25 12:00:59,516 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1567621.68, 'new_value': 1660792.88}, {'field': 'total_amount', 'old_value': 1621066.78, 'new_value': 1714237.98}, {'field': 'order_count', 'old_value': 2919, 'new_value': 3093}]
2025-05-25 12:00:59,516 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMFE
2025-05-25 12:00:59,985 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMFE
2025-05-25 12:00:59,985 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7544.0, 'new_value': 8518.0}, {'field': 'total_amount', 'old_value': 7544.0, 'new_value': 8518.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 22}]
2025-05-25 12:00:59,985 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-25 12:01:00,469 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-25 12:01:00,469 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36109.9, 'new_value': 37649.9}, {'field': 'total_amount', 'old_value': 36109.9, 'new_value': 37649.9}, {'field': 'order_count', 'old_value': 158, 'new_value': 165}]
2025-05-25 12:01:00,469 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-25 12:01:01,032 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-25 12:01:01,032 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53162.0, 'new_value': 55754.0}, {'field': 'total_amount', 'old_value': 81808.0, 'new_value': 84400.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 40}]
2025-05-25 12:01:01,032 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-25 12:01:01,438 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-25 12:01:01,438 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17815.0, 'new_value': 19033.0}, {'field': 'total_amount', 'old_value': 17815.0, 'new_value': 19033.0}, {'field': 'order_count', 'old_value': 307, 'new_value': 328}]
2025-05-25 12:01:01,438 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-25 12:01:01,907 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-25 12:01:01,907 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 113238.0, 'new_value': 115911.0}, {'field': 'total_amount', 'old_value': 113241.0, 'new_value': 115914.0}, {'field': 'order_count', 'old_value': 47, 'new_value': 48}]
2025-05-25 12:01:01,907 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-25 12:01:02,360 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-25 12:01:02,360 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6816.71, 'new_value': 7504.16}, {'field': 'offline_amount', 'old_value': 15189.03, 'new_value': 16463.79}, {'field': 'total_amount', 'old_value': 22005.74, 'new_value': 23967.95}, {'field': 'order_count', 'old_value': 744, 'new_value': 797}]
2025-05-25 12:01:02,360 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-25 12:01:02,782 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-25 12:01:02,782 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 166626.54, 'new_value': 172854.09}, {'field': 'offline_amount', 'old_value': 136248.86, 'new_value': 140893.27}, {'field': 'total_amount', 'old_value': 302875.4, 'new_value': 313747.36}, {'field': 'order_count', 'old_value': 2714, 'new_value': 2829}]
2025-05-25 12:01:02,782 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-25 12:01:03,204 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-25 12:01:03,204 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80916.93, 'new_value': 81040.03}, {'field': 'total_amount', 'old_value': 80920.23, 'new_value': 81043.33}, {'field': 'order_count', 'old_value': 43, 'new_value': 44}]
2025-05-25 12:01:03,204 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-25 12:01:03,673 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-25 12:01:03,673 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 339088.2, 'new_value': 360908.0}, {'field': 'offline_amount', 'old_value': 88649.2, 'new_value': 90676.6}, {'field': 'total_amount', 'old_value': 427737.4, 'new_value': 451584.6}, {'field': 'order_count', 'old_value': 543, 'new_value': 579}]
2025-05-25 12:01:03,673 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-25 12:01:04,126 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-25 12:01:04,126 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25419.0, 'new_value': 27458.0}, {'field': 'total_amount', 'old_value': 25419.0, 'new_value': 27458.0}, {'field': 'order_count', 'old_value': 72, 'new_value': 78}]
2025-05-25 12:01:04,126 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-25 12:01:04,548 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-25 12:01:04,548 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 154418.4, 'new_value': 165369.8}, {'field': 'total_amount', 'old_value': 161994.2, 'new_value': 172945.6}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-05-25 12:01:04,548 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-25 12:01:05,032 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-25 12:01:05,032 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72282.09, 'new_value': 75141.89}, {'field': 'total_amount', 'old_value': 76051.19, 'new_value': 78910.99}, {'field': 'order_count', 'old_value': 389, 'new_value': 397}]
2025-05-25 12:01:05,032 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-25 12:01:05,501 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-25 12:01:05,501 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52008.0, 'new_value': 56044.0}, {'field': 'offline_amount', 'old_value': 160950.0, 'new_value': 167207.0}, {'field': 'total_amount', 'old_value': 212958.0, 'new_value': 223251.0}, {'field': 'order_count', 'old_value': 4704, 'new_value': 4945}]
2025-05-25 12:01:05,501 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-25 12:01:05,969 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-25 12:01:05,969 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91683.64, 'new_value': 116558.64}, {'field': 'total_amount', 'old_value': 97023.64, 'new_value': 121898.64}, {'field': 'order_count', 'old_value': 29, 'new_value': 30}]
2025-05-25 12:01:05,969 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-25 12:01:06,454 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-25 12:01:06,454 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 253379.0, 'new_value': 259073.0}, {'field': 'total_amount', 'old_value': 279763.0, 'new_value': 285457.0}, {'field': 'order_count', 'old_value': 79, 'new_value': 82}]
2025-05-25 12:01:06,454 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-25 12:01:06,938 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-25 12:01:06,938 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 137990.13, 'new_value': 147738.58}, {'field': 'offline_amount', 'old_value': 107252.45, 'new_value': 114485.45}, {'field': 'total_amount', 'old_value': 245242.58, 'new_value': 262224.03}, {'field': 'order_count', 'old_value': 2447, 'new_value': 2594}]
2025-05-25 12:01:06,938 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-25 12:01:07,344 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-25 12:01:07,344 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 491137.04, 'new_value': 512657.04}, {'field': 'offline_amount', 'old_value': 209038.1, 'new_value': 227657.3}, {'field': 'total_amount', 'old_value': 700175.14, 'new_value': 740314.34}, {'field': 'order_count', 'old_value': 6281, 'new_value': 6557}]
2025-05-25 12:01:07,344 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-25 12:01:07,751 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-25 12:01:07,751 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36306.6, 'new_value': 39449.6}, {'field': 'offline_amount', 'old_value': 545.0, 'new_value': 553.0}, {'field': 'total_amount', 'old_value': 36851.6, 'new_value': 40002.6}, {'field': 'order_count', 'old_value': 160, 'new_value': 168}]
2025-05-25 12:01:07,751 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-25 12:01:08,204 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-25 12:01:08,204 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 75197.5, 'new_value': 76709.4}, {'field': 'offline_amount', 'old_value': 5971.15, 'new_value': 6841.65}, {'field': 'total_amount', 'old_value': 81168.65, 'new_value': 83551.05}, {'field': 'order_count', 'old_value': 243, 'new_value': 255}]
2025-05-25 12:01:08,204 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-25 12:01:08,626 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-25 12:01:08,626 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65045.66, 'new_value': 68270.58}, {'field': 'total_amount', 'old_value': 65045.66, 'new_value': 68270.58}, {'field': 'order_count', 'old_value': 1844, 'new_value': 1943}]
2025-05-25 12:01:08,626 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-25 12:01:09,048 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-25 12:01:09,048 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7955.4, 'new_value': 8510.3}, {'field': 'offline_amount', 'old_value': 43909.0, 'new_value': 46247.1}, {'field': 'total_amount', 'old_value': 51864.4, 'new_value': 54757.4}, {'field': 'order_count', 'old_value': 67, 'new_value': 71}]
2025-05-25 12:01:09,048 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-25 12:01:09,548 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-25 12:01:09,548 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 559481.0, 'new_value': 583281.0}, {'field': 'total_amount', 'old_value': 559481.0, 'new_value': 583281.0}, {'field': 'order_count', 'old_value': 63, 'new_value': 65}]
2025-05-25 12:01:09,548 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-25 12:01:09,954 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-25 12:01:09,954 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 120671.58, 'new_value': 127479.16}, {'field': 'offline_amount', 'old_value': 402119.54, 'new_value': 421477.08}, {'field': 'total_amount', 'old_value': 522791.12, 'new_value': 548956.24}, {'field': 'order_count', 'old_value': 2732, 'new_value': 2815}]
2025-05-25 12:01:09,954 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-25 12:01:10,376 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-25 12:01:10,376 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 390457.97, 'new_value': 408698.87}, {'field': 'total_amount', 'old_value': 390457.97, 'new_value': 408698.87}, {'field': 'order_count', 'old_value': 509, 'new_value': 519}]
2025-05-25 12:01:10,376 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-25 12:01:10,829 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-25 12:01:10,829 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14652.17, 'new_value': 15882.61}, {'field': 'offline_amount', 'old_value': 374951.86, 'new_value': 399130.52}, {'field': 'total_amount', 'old_value': 389604.03, 'new_value': 415013.13}, {'field': 'order_count', 'old_value': 1650, 'new_value': 1761}]
2025-05-25 12:01:10,829 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-25 12:01:11,313 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-25 12:01:11,313 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 77322.0, 'new_value': 80472.0}, {'field': 'offline_amount', 'old_value': 73480.09, 'new_value': 75650.76}, {'field': 'total_amount', 'old_value': 150802.09, 'new_value': 156122.76}, {'field': 'order_count', 'old_value': 183, 'new_value': 188}]
2025-05-25 12:01:11,313 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-25 12:01:11,704 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-25 12:01:11,704 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 145019.2, 'new_value': 154097.9}, {'field': 'total_amount', 'old_value': 145019.2, 'new_value': 154097.9}, {'field': 'order_count', 'old_value': 333, 'new_value': 361}]
2025-05-25 12:01:11,704 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-25 12:01:12,172 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-25 12:01:12,172 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 198496.57, 'new_value': 199907.88}, {'field': 'offline_amount', 'old_value': 81401.99, 'new_value': 98604.93}, {'field': 'total_amount', 'old_value': 279898.56, 'new_value': 298512.81}, {'field': 'order_count', 'old_value': 2999, 'new_value': 3129}]
2025-05-25 12:01:12,172 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-25 12:01:12,594 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-25 12:01:12,594 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10557.0, 'new_value': 11144.0}, {'field': 'total_amount', 'old_value': 10557.0, 'new_value': 11144.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 24}]
2025-05-25 12:01:12,594 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPK
2025-05-25 12:01:12,969 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPK
2025-05-25 12:01:12,969 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43364.0, 'new_value': 46926.0}, {'field': 'total_amount', 'old_value': 43364.0, 'new_value': 46926.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 22}]
2025-05-25 12:01:12,969 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-25 12:01:13,454 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-25 12:01:13,454 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95922.54, 'new_value': 97724.94}, {'field': 'total_amount', 'old_value': 95922.54, 'new_value': 97724.94}, {'field': 'order_count', 'old_value': 81, 'new_value': 83}]
2025-05-25 12:01:13,454 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-25 12:01:13,907 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-25 12:01:13,907 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38327.0, 'new_value': 41824.0}, {'field': 'total_amount', 'old_value': 38327.0, 'new_value': 41824.0}, {'field': 'order_count', 'old_value': 77, 'new_value': 85}]
2025-05-25 12:01:13,907 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-25 12:01:14,344 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-25 12:01:14,344 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 101694.55, 'new_value': 106018.56}, {'field': 'total_amount', 'old_value': 101694.55, 'new_value': 106018.56}, {'field': 'order_count', 'old_value': 707, 'new_value': 756}]
2025-05-25 12:01:14,344 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-25 12:01:14,797 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-25 12:01:14,797 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6589.0, 'new_value': 7039.0}, {'field': 'total_amount', 'old_value': 6589.0, 'new_value': 7039.0}, {'field': 'order_count', 'old_value': 36, 'new_value': 39}]
2025-05-25 12:01:14,797 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-25 12:01:15,344 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-25 12:01:15,344 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7275.65, 'new_value': 7400.53}, {'field': 'offline_amount', 'old_value': 27062.0, 'new_value': 30887.0}, {'field': 'total_amount', 'old_value': 34337.65, 'new_value': 38287.53}, {'field': 'order_count', 'old_value': 190, 'new_value': 195}]
2025-05-25 12:01:15,344 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-25 12:01:15,766 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-25 12:01:15,766 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41611.87, 'new_value': 43963.35}, {'field': 'total_amount', 'old_value': 42133.47, 'new_value': 44484.95}, {'field': 'order_count', 'old_value': 357, 'new_value': 382}]
2025-05-25 12:01:15,766 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-25 12:01:16,219 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-25 12:01:16,219 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5327.0, 'new_value': 5432.0}, {'field': 'offline_amount', 'old_value': 23196.5, 'new_value': 24335.0}, {'field': 'total_amount', 'old_value': 28523.5, 'new_value': 29767.0}, {'field': 'order_count', 'old_value': 1128, 'new_value': 1181}]
2025-05-25 12:01:16,219 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-25 12:01:16,735 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-25 12:01:16,735 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83253.86, 'new_value': 86464.46}, {'field': 'total_amount', 'old_value': 83253.86, 'new_value': 86464.46}, {'field': 'order_count', 'old_value': 309, 'new_value': 323}]
2025-05-25 12:01:16,735 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-25 12:01:17,188 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-25 12:01:17,188 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24707.1, 'new_value': 26097.1}, {'field': 'offline_amount', 'old_value': 19378.6, 'new_value': 20409.6}, {'field': 'total_amount', 'old_value': 44085.7, 'new_value': 46506.7}, {'field': 'order_count', 'old_value': 235, 'new_value': 248}]
2025-05-25 12:01:17,188 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9L
2025-05-25 12:01:17,688 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9L
2025-05-25 12:01:17,688 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58747.0, 'new_value': 62322.0}, {'field': 'total_amount', 'old_value': 58747.0, 'new_value': 62322.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 24}]
2025-05-25 12:01:17,688 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDL
2025-05-25 12:01:18,172 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDL
2025-05-25 12:01:18,172 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85493.0, 'new_value': 94293.0}, {'field': 'total_amount', 'old_value': 85493.0, 'new_value': 94293.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-05-25 12:01:18,172 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-25 12:01:18,594 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-25 12:01:18,594 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 179696.3, 'new_value': 186898.3}, {'field': 'total_amount', 'old_value': 179696.3, 'new_value': 186898.3}, {'field': 'order_count', 'old_value': 668, 'new_value': 698}]
2025-05-25 12:01:18,594 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-25 12:01:19,032 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-25 12:01:19,032 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44736.0, 'new_value': 51136.0}, {'field': 'total_amount', 'old_value': 47329.0, 'new_value': 53729.0}, {'field': 'order_count', 'old_value': 188, 'new_value': 203}]
2025-05-25 12:01:19,032 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-25 12:01:19,532 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-25 12:01:19,532 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15166.89, 'new_value': 16133.68}, {'field': 'offline_amount', 'old_value': 236090.84, 'new_value': 248731.24}, {'field': 'total_amount', 'old_value': 251257.73, 'new_value': 264864.92}, {'field': 'order_count', 'old_value': 13914, 'new_value': 14698}]
2025-05-25 12:01:19,532 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-25 12:01:19,954 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-25 12:01:19,954 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 272164.7, 'new_value': 283944.37}, {'field': 'total_amount', 'old_value': 272164.7, 'new_value': 283944.37}, {'field': 'order_count', 'old_value': 7490, 'new_value': 7835}]
2025-05-25 12:01:19,954 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-25 12:01:20,391 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-25 12:01:20,391 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46170.32, 'new_value': 46935.38}, {'field': 'offline_amount', 'old_value': 29658.0, 'new_value': 30187.0}, {'field': 'total_amount', 'old_value': 75828.32, 'new_value': 77122.38}, {'field': 'order_count', 'old_value': 941, 'new_value': 961}]
2025-05-25 12:01:20,391 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-25 12:01:20,813 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-25 12:01:20,813 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 135072.17, 'new_value': 144409.27}, {'field': 'total_amount', 'old_value': 135072.17, 'new_value': 144409.27}, {'field': 'order_count', 'old_value': 669, 'new_value': 707}]
2025-05-25 12:01:20,813 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-25 12:01:21,329 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-25 12:01:21,329 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21456.94, 'new_value': 22343.72}, {'field': 'offline_amount', 'old_value': 39248.16, 'new_value': 41167.92}, {'field': 'total_amount', 'old_value': 60705.1, 'new_value': 63511.64}, {'field': 'order_count', 'old_value': 2180, 'new_value': 2280}]
2025-05-25 12:01:21,329 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-25 12:01:21,797 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-25 12:01:21,797 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66226.0, 'new_value': 70886.0}, {'field': 'total_amount', 'old_value': 68634.0, 'new_value': 73294.0}, {'field': 'order_count', 'old_value': 283, 'new_value': 298}]
2025-05-25 12:01:21,797 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-25 12:01:22,391 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-25 12:01:22,391 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19367.2, 'new_value': 19602.2}, {'field': 'offline_amount', 'old_value': 50654.97, 'new_value': 57810.17}, {'field': 'total_amount', 'old_value': 70022.17, 'new_value': 77412.37}, {'field': 'order_count', 'old_value': 808, 'new_value': 864}]
2025-05-25 12:01:22,391 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-25 12:01:22,907 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-25 12:01:22,907 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 90458.8, 'new_value': 96176.2}, {'field': 'offline_amount', 'old_value': 128834.0, 'new_value': 135002.5}, {'field': 'total_amount', 'old_value': 219292.8, 'new_value': 231178.7}, {'field': 'order_count', 'old_value': 1433, 'new_value': 1494}]
2025-05-25 12:01:22,907 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-25 12:01:23,344 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-25 12:01:23,344 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 91024.29, 'new_value': 95753.12}, {'field': 'offline_amount', 'old_value': 67030.53, 'new_value': 69465.11}, {'field': 'total_amount', 'old_value': 158054.82, 'new_value': 165218.23}, {'field': 'order_count', 'old_value': 6727, 'new_value': 7047}]
2025-05-25 12:01:23,344 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-25 12:01:23,782 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-25 12:01:23,782 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20787.58, 'new_value': 21677.71}, {'field': 'offline_amount', 'old_value': 39062.45, 'new_value': 40607.79}, {'field': 'total_amount', 'old_value': 59850.03, 'new_value': 62285.5}, {'field': 'order_count', 'old_value': 3161, 'new_value': 3277}]
2025-05-25 12:01:23,782 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-25 12:01:24,251 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-25 12:01:24,251 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 103870.0, 'new_value': 107220.0}, {'field': 'total_amount', 'old_value': 103870.0, 'new_value': 107220.0}, {'field': 'order_count', 'old_value': 5171, 'new_value': 5269}]
2025-05-25 12:01:24,251 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-25 12:01:24,688 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-25 12:01:24,688 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28296.77, 'new_value': 30795.67}, {'field': 'total_amount', 'old_value': 30083.67, 'new_value': 32582.57}, {'field': 'order_count', 'old_value': 23, 'new_value': 24}]
2025-05-25 12:01:24,688 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-25 12:01:25,110 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-25 12:01:25,110 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47155.0, 'new_value': 48771.0}, {'field': 'total_amount', 'old_value': 47504.0, 'new_value': 49120.0}, {'field': 'order_count', 'old_value': 87, 'new_value': 91}]
2025-05-25 12:01:25,110 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-25 12:01:25,454 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-25 12:01:25,454 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47359.0, 'new_value': 49150.0}, {'field': 'total_amount', 'old_value': 47359.0, 'new_value': 49150.0}, {'field': 'order_count', 'old_value': 338, 'new_value': 353}]
2025-05-25 12:01:25,454 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-25 12:01:25,938 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-25 12:01:25,938 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57318.0, 'new_value': 60861.0}, {'field': 'total_amount', 'old_value': 57318.0, 'new_value': 60861.0}, {'field': 'order_count', 'old_value': 57, 'new_value': 61}]
2025-05-25 12:01:25,938 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-25 12:01:26,376 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-25 12:01:26,376 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107797.35, 'new_value': 115524.13}, {'field': 'total_amount', 'old_value': 107797.35, 'new_value': 115524.13}, {'field': 'order_count', 'old_value': 3190, 'new_value': 3374}]
2025-05-25 12:01:26,376 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-25 12:01:26,891 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-25 12:01:26,891 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 121659.78, 'new_value': 128230.87}, {'field': 'offline_amount', 'old_value': 242769.87, 'new_value': 252442.92}, {'field': 'total_amount', 'old_value': 364429.65, 'new_value': 380673.79}, {'field': 'order_count', 'old_value': 4281, 'new_value': 4455}]
2025-05-25 12:01:26,891 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO
2025-05-25 12:01:27,376 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO
2025-05-25 12:01:27,376 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28037.88, 'new_value': 31920.88}, {'field': 'total_amount', 'old_value': 28037.88, 'new_value': 31920.88}, {'field': 'order_count', 'old_value': 28, 'new_value': 30}]
2025-05-25 12:01:27,376 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-25 12:01:27,797 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-25 12:01:27,797 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 207912.0, 'new_value': 216528.0}, {'field': 'total_amount', 'old_value': 207912.0, 'new_value': 216528.0}, {'field': 'order_count', 'old_value': 17326, 'new_value': 18044}]
2025-05-25 12:01:27,797 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6M
2025-05-25 12:01:28,266 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6M
2025-05-25 12:01:28,266 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73580.0, 'new_value': 77580.0}, {'field': 'total_amount', 'old_value': 73580.0, 'new_value': 77580.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-05-25 12:01:28,266 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7M
2025-05-25 12:01:28,719 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7M
2025-05-25 12:01:28,719 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52640.0, 'new_value': 61660.0}, {'field': 'total_amount', 'old_value': 52640.0, 'new_value': 61660.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 17}]
2025-05-25 12:01:28,719 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-25 12:01:29,204 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-25 12:01:29,204 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 99694.98, 'new_value': 105629.98}, {'field': 'total_amount', 'old_value': 99694.98, 'new_value': 105629.98}, {'field': 'order_count', 'old_value': 873, 'new_value': 935}]
2025-05-25 12:01:29,204 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCO
2025-05-25 12:01:29,641 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCO
2025-05-25 12:01:29,641 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11816.2, 'new_value': 13966.2}, {'field': 'total_amount', 'old_value': 11816.2, 'new_value': 13966.2}, {'field': 'order_count', 'old_value': 9, 'new_value': 12}]
2025-05-25 12:01:29,641 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-25 12:01:30,110 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-25 12:01:30,110 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41004.9, 'new_value': 41823.2}, {'field': 'total_amount', 'old_value': 41004.9, 'new_value': 41823.2}, {'field': 'order_count', 'old_value': 60, 'new_value': 62}]
2025-05-25 12:01:30,110 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-25 12:01:30,610 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-25 12:01:30,610 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69840.7, 'new_value': 70456.7}, {'field': 'total_amount', 'old_value': 77190.7, 'new_value': 77806.7}, {'field': 'order_count', 'old_value': 41, 'new_value': 44}]
2025-05-25 12:01:30,610 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-25 12:01:31,141 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-25 12:01:31,141 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43189.8, 'new_value': 47229.1}, {'field': 'total_amount', 'old_value': 45490.1, 'new_value': 49529.4}, {'field': 'order_count', 'old_value': 141, 'new_value': 152}]
2025-05-25 12:01:31,141 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-25 12:01:31,579 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-25 12:01:31,579 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41550.55, 'new_value': 43745.45}, {'field': 'total_amount', 'old_value': 41550.55, 'new_value': 43745.45}, {'field': 'order_count', 'old_value': 1851, 'new_value': 1949}]
2025-05-25 12:01:31,579 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-25 12:01:31,985 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-25 12:01:31,985 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 100923.3, 'new_value': 110420.0}, {'field': 'total_amount', 'old_value': 232387.65, 'new_value': 241884.35}, {'field': 'order_count', 'old_value': 6115, 'new_value': 6372}]
2025-05-25 12:01:31,985 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-25 12:01:32,422 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-25 12:01:32,422 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 237112.0, 'new_value': 260412.0}, {'field': 'total_amount', 'old_value': 237112.0, 'new_value': 260412.0}, {'field': 'order_count', 'old_value': 114, 'new_value': 123}]
2025-05-25 12:01:32,422 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-25 12:01:32,860 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-25 12:01:32,860 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40410.51, 'new_value': 42684.9}, {'field': 'total_amount', 'old_value': 40410.51, 'new_value': 42684.9}, {'field': 'order_count', 'old_value': 165, 'new_value': 174}]
2025-05-25 12:01:32,860 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM8Q
2025-05-25 12:01:33,250 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM8Q
2025-05-25 12:01:33,250 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 180361.41, 'new_value': 191608.41}, {'field': 'total_amount', 'old_value': 180361.41, 'new_value': 191608.41}, {'field': 'order_count', 'old_value': 7695, 'new_value': 7901}]
2025-05-25 12:01:33,250 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-25 12:01:33,672 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-25 12:01:33,672 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52000.44, 'new_value': 54830.64}, {'field': 'offline_amount', 'old_value': 31245.43, 'new_value': 32536.15}, {'field': 'total_amount', 'old_value': 83245.87, 'new_value': 87366.79}, {'field': 'order_count', 'old_value': 4543, 'new_value': 4765}]
2025-05-25 12:01:33,672 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-25 12:01:34,188 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-25 12:01:34,188 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75632.0, 'new_value': 79831.0}, {'field': 'total_amount', 'old_value': 80842.0, 'new_value': 85041.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 51}]
2025-05-25 12:01:34,188 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-25 12:01:34,641 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-25 12:01:34,641 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86363.0, 'new_value': 98211.48}, {'field': 'total_amount', 'old_value': 113046.9, 'new_value': 124895.38}, {'field': 'order_count', 'old_value': 93, 'new_value': 102}]
2025-05-25 12:01:34,641 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-25 12:01:35,125 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-25 12:01:35,125 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19366.58, 'new_value': 20088.58}, {'field': 'total_amount', 'old_value': 19366.58, 'new_value': 20088.58}, {'field': 'order_count', 'old_value': 163, 'new_value': 169}]
2025-05-25 12:01:35,125 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-25 12:01:35,547 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-25 12:01:35,547 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 385436.03, 'new_value': 406534.57}, {'field': 'total_amount', 'old_value': 385436.03, 'new_value': 406534.57}, {'field': 'order_count', 'old_value': 1374, 'new_value': 1445}]
2025-05-25 12:01:35,547 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-25 12:01:35,954 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-25 12:01:35,954 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 285897.4, 'new_value': 300967.3}, {'field': 'total_amount', 'old_value': 285897.4, 'new_value': 300967.3}, {'field': 'order_count', 'old_value': 7237, 'new_value': 7583}]
2025-05-25 12:01:35,954 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-25 12:01:36,407 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-25 12:01:36,407 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36007.47, 'new_value': 37742.82}, {'field': 'total_amount', 'old_value': 36007.47, 'new_value': 37742.82}, {'field': 'order_count', 'old_value': 4657, 'new_value': 4868}]
2025-05-25 12:01:36,407 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-25 12:01:36,875 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-25 12:01:36,875 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23494.28, 'new_value': 24470.74}, {'field': 'offline_amount', 'old_value': 29544.78, 'new_value': 31743.64}, {'field': 'total_amount', 'old_value': 53039.06, 'new_value': 56214.38}, {'field': 'order_count', 'old_value': 2411, 'new_value': 2546}]
2025-05-25 12:01:36,875 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-25 12:01:37,297 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-25 12:01:37,297 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79262.0, 'new_value': 83390.0}, {'field': 'total_amount', 'old_value': 84463.0, 'new_value': 88591.0}, {'field': 'order_count', 'old_value': 240, 'new_value': 251}]
2025-05-25 12:01:37,297 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-25 12:01:37,813 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-25 12:01:37,813 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1587513.05, 'new_value': 1664837.26}, {'field': 'offline_amount', 'old_value': 154170.3, 'new_value': 155710.3}, {'field': 'total_amount', 'old_value': 1741683.35, 'new_value': 1820547.56}, {'field': 'order_count', 'old_value': 5987, 'new_value': 6246}]
2025-05-25 12:01:37,813 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-25 12:01:38,266 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-25 12:01:38,266 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 249021.8, 'new_value': 282069.0}, {'field': 'total_amount', 'old_value': 249021.8, 'new_value': 282069.0}, {'field': 'order_count', 'old_value': 64, 'new_value': 70}]
2025-05-25 12:01:38,266 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-05-25 12:01:38,719 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-05-25 12:01:38,719 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11785.0, 'new_value': 11865.0}, {'field': 'total_amount', 'old_value': 11785.0, 'new_value': 11865.0}, {'field': 'order_count', 'old_value': 83, 'new_value': 84}]
2025-05-25 12:01:38,719 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-25 12:01:39,204 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-25 12:01:39,204 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17240.4, 'new_value': 17506.9}, {'field': 'offline_amount', 'old_value': 42650.9, 'new_value': 43402.9}, {'field': 'total_amount', 'old_value': 59891.3, 'new_value': 60909.8}, {'field': 'order_count', 'old_value': 175, 'new_value': 184}]
2025-05-25 12:01:39,204 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-25 12:01:39,704 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-25 12:01:39,704 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 405355.08, 'new_value': 429042.08}, {'field': 'total_amount', 'old_value': 405355.08, 'new_value': 429042.08}, {'field': 'order_count', 'old_value': 2040, 'new_value': 2163}]
2025-05-25 12:01:39,704 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-25 12:01:40,172 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-25 12:01:40,172 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 135979.0, 'new_value': 139877.0}, {'field': 'total_amount', 'old_value': 135979.0, 'new_value': 139877.0}, {'field': 'order_count', 'old_value': 79, 'new_value': 81}]
2025-05-25 12:01:40,172 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-25 12:01:40,610 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-25 12:01:40,610 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9567.3, 'new_value': 10153.1}, {'field': 'offline_amount', 'old_value': 25767.9, 'new_value': 27268.9}, {'field': 'total_amount', 'old_value': 35335.2, 'new_value': 37422.0}, {'field': 'order_count', 'old_value': 90, 'new_value': 98}]
2025-05-25 12:01:40,610 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-25 12:01:41,079 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-25 12:01:41,079 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 557635.19, 'new_value': 587574.52}, {'field': 'total_amount', 'old_value': 559548.24, 'new_value': 589487.57}, {'field': 'order_count', 'old_value': 1338, 'new_value': 1403}]
2025-05-25 12:01:41,079 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-25 12:01:41,547 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-25 12:01:41,547 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 162630.0, 'new_value': 170310.0}, {'field': 'total_amount', 'old_value': 162631.0, 'new_value': 170311.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 26}]
2025-05-25 12:01:41,547 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO
2025-05-25 12:01:42,016 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO
2025-05-25 12:01:42,016 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1020162.0, 'new_value': 1046158.0}, {'field': 'total_amount', 'old_value': 1020162.0, 'new_value': 1046158.0}, {'field': 'order_count', 'old_value': 123, 'new_value': 127}]
2025-05-25 12:01:42,016 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-25 12:01:42,485 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-25 12:01:42,485 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41755.17, 'new_value': 45508.35}, {'field': 'offline_amount', 'old_value': 50161.2, 'new_value': 50316.99}, {'field': 'total_amount', 'old_value': 91916.37, 'new_value': 95825.34}, {'field': 'order_count', 'old_value': 311, 'new_value': 332}]
2025-05-25 12:01:42,485 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-25 12:01:43,016 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-25 12:01:43,016 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 133295.47, 'new_value': 143192.72}, {'field': 'total_amount', 'old_value': 133295.47, 'new_value': 143192.72}, {'field': 'order_count', 'old_value': 3458, 'new_value': 3695}]
2025-05-25 12:01:43,016 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUO
2025-05-25 12:01:43,485 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUO
2025-05-25 12:01:43,485 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77400.0, 'new_value': 104760.0}, {'field': 'total_amount', 'old_value': 77400.0, 'new_value': 104760.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 13}]
2025-05-25 12:01:43,485 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-25 12:01:43,891 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-25 12:01:43,891 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33535.74, 'new_value': 33817.01}, {'field': 'offline_amount', 'old_value': 290558.72, 'new_value': 303219.42}, {'field': 'total_amount', 'old_value': 324094.46, 'new_value': 337036.43}, {'field': 'order_count', 'old_value': 7492, 'new_value': 7844}]
2025-05-25 12:01:43,891 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-25 12:01:44,344 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-25 12:01:44,344 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 302833.25, 'new_value': 320111.44}, {'field': 'total_amount', 'old_value': 302833.25, 'new_value': 320111.44}, {'field': 'order_count', 'old_value': 2879, 'new_value': 3040}]
2025-05-25 12:01:44,344 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-25 12:01:44,750 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-25 12:01:44,750 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62754.0, 'new_value': 69472.0}, {'field': 'total_amount', 'old_value': 62754.0, 'new_value': 69472.0}, {'field': 'order_count', 'old_value': 308, 'new_value': 328}]
2025-05-25 12:01:44,750 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-25 12:01:45,266 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-25 12:01:45,266 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 292971.92, 'new_value': 307038.92}, {'field': 'offline_amount', 'old_value': 9273.5, 'new_value': 9468.5}, {'field': 'total_amount', 'old_value': 302245.42, 'new_value': 316507.42}, {'field': 'order_count', 'old_value': 2617, 'new_value': 2760}]
2025-05-25 12:01:45,266 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-25 12:01:45,735 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-25 12:01:45,735 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18807.0, 'new_value': 20817.0}, {'field': 'total_amount', 'old_value': 18807.0, 'new_value': 20817.0}, {'field': 'order_count', 'old_value': 100, 'new_value': 114}]
2025-05-25 12:01:45,735 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-25 12:01:46,172 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-25 12:01:46,172 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 395190.0, 'new_value': 395979.9}, {'field': 'total_amount', 'old_value': 395190.0, 'new_value': 395979.9}, {'field': 'order_count', 'old_value': 1981, 'new_value': 1982}]
2025-05-25 12:01:46,172 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-25 12:01:46,594 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-25 12:01:46,594 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25538.0, 'new_value': 26070.4}, {'field': 'total_amount', 'old_value': 25538.0, 'new_value': 26070.4}, {'field': 'order_count', 'old_value': 693, 'new_value': 724}]
2025-05-25 12:01:46,594 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-25 12:01:47,094 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-25 12:01:47,094 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11337.4, 'new_value': 11860.1}, {'field': 'total_amount', 'old_value': 11766.4, 'new_value': 12289.1}, {'field': 'order_count', 'old_value': 152, 'new_value': 159}]
2025-05-25 12:01:47,094 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-25 12:01:47,563 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-25 12:01:47,563 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6866.1, 'new_value': 7034.1}, {'field': 'offline_amount', 'old_value': 34966.65, 'new_value': 40839.75}, {'field': 'total_amount', 'old_value': 41832.75, 'new_value': 47873.85}, {'field': 'order_count', 'old_value': 479, 'new_value': 509}]
2025-05-25 12:01:47,563 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-25 12:01:48,047 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-25 12:01:48,047 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6594.0, 'new_value': 6809.0}, {'field': 'offline_amount', 'old_value': 30625.0, 'new_value': 33122.0}, {'field': 'total_amount', 'old_value': 37219.0, 'new_value': 39931.0}, {'field': 'order_count', 'old_value': 289, 'new_value': 306}]
2025-05-25 12:01:48,047 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-25 12:01:48,579 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-25 12:01:48,579 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14199.0, 'new_value': 15948.6}, {'field': 'total_amount', 'old_value': 48205.8, 'new_value': 49955.4}, {'field': 'order_count', 'old_value': 89, 'new_value': 95}]
2025-05-25 12:01:48,579 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-25 12:01:49,125 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-25 12:01:49,125 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 83342.32, 'new_value': 86826.64}, {'field': 'offline_amount', 'old_value': 214818.34, 'new_value': 220949.38}, {'field': 'total_amount', 'old_value': 298160.66, 'new_value': 307776.02}, {'field': 'order_count', 'old_value': 14671, 'new_value': 15121}]
2025-05-25 12:01:49,125 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-25 12:01:49,578 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-25 12:01:49,578 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 828373.0, 'new_value': 867369.0}, {'field': 'total_amount', 'old_value': 828373.0, 'new_value': 867369.0}, {'field': 'order_count', 'old_value': 1767, 'new_value': 1848}]
2025-05-25 12:01:49,578 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-25 12:01:50,016 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-25 12:01:50,016 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30017.78, 'new_value': 30928.19}, {'field': 'total_amount', 'old_value': 30017.78, 'new_value': 30928.19}, {'field': 'order_count', 'old_value': 1118, 'new_value': 1150}]
2025-05-25 12:01:50,016 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-25 12:01:50,469 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-25 12:01:50,469 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 56956.72, 'new_value': 59596.22}, {'field': 'offline_amount', 'old_value': 408243.11, 'new_value': 433214.22}, {'field': 'total_amount', 'old_value': 465199.83, 'new_value': 492810.44}, {'field': 'order_count', 'old_value': 2230, 'new_value': 2389}]
2025-05-25 12:01:50,469 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-25 12:01:50,953 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-25 12:01:50,953 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 777367.0, 'new_value': 816742.0}, {'field': 'total_amount', 'old_value': 777367.0, 'new_value': 816742.0}, {'field': 'order_count', 'old_value': 3503, 'new_value': 3671}]
2025-05-25 12:01:50,953 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-25 12:01:51,453 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-25 12:01:51,453 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31141.0, 'new_value': 36667.0}, {'field': 'total_amount', 'old_value': 31141.0, 'new_value': 36667.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-05-25 12:01:51,453 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-25 12:01:51,875 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-25 12:01:51,875 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46355.93, 'new_value': 49210.53}, {'field': 'offline_amount', 'old_value': 314109.52, 'new_value': 334162.14}, {'field': 'total_amount', 'old_value': 360465.45, 'new_value': 383372.67}, {'field': 'order_count', 'old_value': 2283, 'new_value': 2434}]
2025-05-25 12:01:51,875 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-25 12:01:52,375 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-25 12:01:52,375 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 195692.7, 'new_value': 206741.7}, {'field': 'total_amount', 'old_value': 195692.7, 'new_value': 206741.7}, {'field': 'order_count', 'old_value': 1082, 'new_value': 1144}]
2025-05-25 12:01:52,375 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-25 12:01:52,875 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-25 12:01:52,875 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 69269.0, 'new_value': 72634.0}, {'field': 'total_amount', 'old_value': 69269.0, 'new_value': 72634.0}, {'field': 'order_count', 'old_value': 2045, 'new_value': 2140}]
2025-05-25 12:01:52,875 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4E
2025-05-25 12:01:53,297 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4E
2025-05-25 12:01:53,297 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83414.0, 'new_value': 87294.0}, {'field': 'total_amount', 'old_value': 83414.0, 'new_value': 87294.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 24}]
2025-05-25 12:01:53,297 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-25 12:01:53,797 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-25 12:01:53,797 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84252.0, 'new_value': 89490.0}, {'field': 'total_amount', 'old_value': 99456.0, 'new_value': 104694.0}, {'field': 'order_count', 'old_value': 2269, 'new_value': 2406}]
2025-05-25 12:01:53,797 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-25 12:01:54,250 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-25 12:01:54,250 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22966.0, 'new_value': 23104.0}, {'field': 'total_amount', 'old_value': 22966.0, 'new_value': 23104.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 23}]
2025-05-25 12:01:54,250 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-25 12:01:54,735 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-25 12:01:54,735 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 123116.0, 'new_value': 136358.0}, {'field': 'total_amount', 'old_value': 123116.0, 'new_value': 136358.0}, {'field': 'order_count', 'old_value': 3930, 'new_value': 4362}]
2025-05-25 12:01:54,735 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-25 12:01:55,203 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-25 12:01:55,203 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 123472.79, 'new_value': 123547.79}, {'field': 'offline_amount', 'old_value': 312220.95, 'new_value': 344536.95}, {'field': 'total_amount', 'old_value': 435693.74, 'new_value': 468084.74}, {'field': 'order_count', 'old_value': 3952, 'new_value': 4092}]
2025-05-25 12:01:55,203 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-25 12:01:55,594 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-25 12:01:55,594 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 236868.46, 'new_value': 237166.46}, {'field': 'total_amount', 'old_value': 237703.46, 'new_value': 238001.46}, {'field': 'order_count', 'old_value': 44, 'new_value': 45}]
2025-05-25 12:01:55,594 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-25 12:01:55,985 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-25 12:01:55,985 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 87187.12, 'new_value': 90448.76}, {'field': 'offline_amount', 'old_value': 100067.82, 'new_value': 103956.44}, {'field': 'total_amount', 'old_value': 187254.94, 'new_value': 194405.2}, {'field': 'order_count', 'old_value': 7577, 'new_value': 7920}]
2025-05-25 12:01:55,985 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-25 12:01:56,407 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-25 12:01:56,407 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86267.68, 'new_value': 100055.4}, {'field': 'total_amount', 'old_value': 304047.6, 'new_value': 317835.32}, {'field': 'order_count', 'old_value': 550, 'new_value': 579}]
2025-05-25 12:01:56,407 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-25 12:01:56,907 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-25 12:01:56,907 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51298.0, 'new_value': 51953.0}, {'field': 'total_amount', 'old_value': 51298.0, 'new_value': 51953.0}, {'field': 'order_count', 'old_value': 111, 'new_value': 116}]
2025-05-25 12:01:56,907 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMJE
2025-05-25 12:01:57,375 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMJE
2025-05-25 12:01:57,375 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77714.0, 'new_value': 82612.0}, {'field': 'total_amount', 'old_value': 77714.0, 'new_value': 82612.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 23}]
2025-05-25 12:01:57,375 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMAQ
2025-05-25 12:01:57,828 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMAQ
2025-05-25 12:01:57,828 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95917.0, 'new_value': 98703.0}, {'field': 'total_amount', 'old_value': 95917.0, 'new_value': 98703.0}, {'field': 'order_count', 'old_value': 2362, 'new_value': 2435}]
2025-05-25 12:01:57,828 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-25 12:01:58,250 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-25 12:01:58,250 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 632572.72, 'new_value': 670046.72}, {'field': 'total_amount', 'old_value': 632572.72, 'new_value': 670046.72}, {'field': 'order_count', 'old_value': 4847, 'new_value': 5138}]
2025-05-25 12:01:58,250 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-25 12:01:58,813 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-25 12:01:58,813 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25323.0, 'new_value': 26592.0}, {'field': 'total_amount', 'old_value': 25323.0, 'new_value': 26592.0}, {'field': 'order_count', 'old_value': 142, 'new_value': 151}]
2025-05-25 12:01:58,813 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-25 12:01:59,344 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-25 12:01:59,344 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71627.29, 'new_value': 75658.11}, {'field': 'total_amount', 'old_value': 71627.29, 'new_value': 75658.11}, {'field': 'order_count', 'old_value': 4095, 'new_value': 4337}]
2025-05-25 12:01:59,344 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9P
2025-05-25 12:01:59,782 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9P
2025-05-25 12:01:59,782 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34094.0, 'new_value': 36570.0}, {'field': 'total_amount', 'old_value': 34094.0, 'new_value': 36570.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 54}]
2025-05-25 12:01:59,782 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-25 12:02:00,235 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-25 12:02:00,235 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53312.55, 'new_value': 55787.55}, {'field': 'offline_amount', 'old_value': 40326.4, 'new_value': 43859.4}, {'field': 'total_amount', 'old_value': 93638.95, 'new_value': 99646.95}, {'field': 'order_count', 'old_value': 1868, 'new_value': 1983}]
2025-05-25 12:02:00,235 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBP
2025-05-25 12:02:00,657 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBP
2025-05-25 12:02:00,657 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-05-25 12:02:00,657 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-25 12:02:01,125 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-25 12:02:01,125 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13536.45, 'new_value': 14312.45}, {'field': 'offline_amount', 'old_value': 258487.0, 'new_value': 274742.0}, {'field': 'total_amount', 'old_value': 272023.45, 'new_value': 289054.45}, {'field': 'order_count', 'old_value': 1450, 'new_value': 1533}]
2025-05-25 12:02:01,125 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-25 12:02:01,594 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-25 12:02:01,594 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2030000.0, 'new_value': 2130000.0}, {'field': 'total_amount', 'old_value': 2030000.0, 'new_value': 2130000.0}, {'field': 'order_count', 'old_value': 280, 'new_value': 281}]
2025-05-25 12:02:01,594 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-25 12:02:02,110 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-25 12:02:02,110 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 23, 'new_value': 24}]
2025-05-25 12:02:02,110 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-25 12:02:02,563 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-25 12:02:02,563 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 164439.0, 'new_value': 175087.0}, {'field': 'total_amount', 'old_value': 164439.0, 'new_value': 175087.0}, {'field': 'order_count', 'old_value': 2122, 'new_value': 2250}]
2025-05-25 12:02:02,563 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-25 12:02:03,219 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-25 12:02:03,219 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91801.53, 'new_value': 93411.53}, {'field': 'total_amount', 'old_value': 91801.53, 'new_value': 93411.53}, {'field': 'order_count', 'old_value': 120, 'new_value': 123}]
2025-05-25 12:02:03,219 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-25 12:02:03,719 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-25 12:02:03,719 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38187.45, 'new_value': 40770.35}, {'field': 'offline_amount', 'old_value': 1008513.89, 'new_value': 1074047.33}, {'field': 'total_amount', 'old_value': 1046701.34, 'new_value': 1114817.68}, {'field': 'order_count', 'old_value': 5125, 'new_value': 5425}]
2025-05-25 12:02:03,735 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-25 12:02:04,188 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-25 12:02:04,203 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 287425.0, 'new_value': 296989.0}, {'field': 'total_amount', 'old_value': 291725.0, 'new_value': 301289.0}, {'field': 'order_count', 'old_value': 202, 'new_value': 210}]
2025-05-25 12:02:04,203 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-25 12:02:04,657 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-25 12:02:04,657 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47924.35, 'new_value': 51351.05}, {'field': 'total_amount', 'old_value': 47924.35, 'new_value': 51351.05}, {'field': 'order_count', 'old_value': 180, 'new_value': 192}]
2025-05-25 12:02:04,657 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-25 12:02:05,110 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-25 12:02:05,110 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 419677.0, 'new_value': 456699.2}, {'field': 'total_amount', 'old_value': 599875.0, 'new_value': 636897.2}, {'field': 'order_count', 'old_value': 3986, 'new_value': 4023}]
2025-05-25 12:02:05,110 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-25 12:02:05,610 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-25 12:02:05,610 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 221233.11, 'new_value': 230290.6}, {'field': 'offline_amount', 'old_value': 671752.42, 'new_value': 713433.89}, {'field': 'total_amount', 'old_value': 892985.53, 'new_value': 943724.49}, {'field': 'order_count', 'old_value': 5352, 'new_value': 5640}]
2025-05-25 12:02:05,610 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-25 12:02:06,078 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-25 12:02:06,078 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 164414.64, 'new_value': 174474.53}, {'field': 'total_amount', 'old_value': 164414.64, 'new_value': 174474.53}, {'field': 'order_count', 'old_value': 6964, 'new_value': 7409}]
2025-05-25 12:02:06,078 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-25 12:02:06,500 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-25 12:02:06,500 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 524627.0, 'new_value': 560665.0}, {'field': 'total_amount', 'old_value': 524627.0, 'new_value': 560665.0}, {'field': 'order_count', 'old_value': 471, 'new_value': 505}]
2025-05-25 12:02:06,500 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-25 12:02:07,000 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-25 12:02:07,000 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 243770.96, 'new_value': 258031.45}, {'field': 'offline_amount', 'old_value': 137591.59, 'new_value': 143968.2}, {'field': 'total_amount', 'old_value': 381362.55, 'new_value': 401999.65}, {'field': 'order_count', 'old_value': 3102, 'new_value': 3174}]
2025-05-25 12:02:07,000 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-25 12:02:07,500 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-25 12:02:07,500 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78325.8, 'new_value': 81726.2}, {'field': 'total_amount', 'old_value': 80292.6, 'new_value': 83693.0}, {'field': 'order_count', 'old_value': 503, 'new_value': 532}]
2025-05-25 12:02:07,500 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-25 12:02:07,922 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-25 12:02:07,922 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46424.0, 'new_value': 49828.0}, {'field': 'total_amount', 'old_value': 46424.0, 'new_value': 49828.0}, {'field': 'order_count', 'old_value': 108, 'new_value': 117}]
2025-05-25 12:02:07,922 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-25 12:02:08,391 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-25 12:02:08,391 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 500000.0, 'new_value': 510000.0}, {'field': 'total_amount', 'old_value': 500000.0, 'new_value': 510000.0}, {'field': 'order_count', 'old_value': 147, 'new_value': 148}]
2025-05-25 12:02:08,391 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-25 12:02:08,891 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-25 12:02:08,891 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 480000.0, 'new_value': 490000.0}, {'field': 'total_amount', 'old_value': 480000.0, 'new_value': 490000.0}, {'field': 'order_count', 'old_value': 146, 'new_value': 147}]
2025-05-25 12:02:08,891 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-25 12:02:09,281 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-25 12:02:09,281 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3148674.0, 'new_value': 3248674.0}, {'field': 'total_amount', 'old_value': 3148674.0, 'new_value': 3248674.0}, {'field': 'order_count', 'old_value': 300, 'new_value': 301}]
2025-05-25 12:02:09,281 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-25 12:02:09,781 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-25 12:02:09,781 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 89839.0, 'new_value': 93525.0}, {'field': 'offline_amount', 'old_value': 1006558.0, 'new_value': 1054072.0}, {'field': 'total_amount', 'old_value': 1096397.0, 'new_value': 1147597.0}, {'field': 'order_count', 'old_value': 27785, 'new_value': 29058}]
2025-05-25 12:02:09,781 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-25 12:02:10,188 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-25 12:02:10,188 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 353876.3, 'new_value': 372037.34}, {'field': 'total_amount', 'old_value': 367386.78, 'new_value': 385547.82}, {'field': 'order_count', 'old_value': 1191, 'new_value': 1243}]
2025-05-25 12:02:10,188 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-25 12:02:10,672 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-25 12:02:10,672 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47159.0, 'new_value': 51101.0}, {'field': 'offline_amount', 'old_value': 239832.0, 'new_value': 242658.0}, {'field': 'total_amount', 'old_value': 286991.0, 'new_value': 293759.0}, {'field': 'order_count', 'old_value': 254, 'new_value': 266}]
2025-05-25 12:02:10,672 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-25 12:02:11,063 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-25 12:02:11,078 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 359.0}, {'field': 'offline_amount', 'old_value': 31878.0, 'new_value': 35406.0}, {'field': 'total_amount', 'old_value': 31878.0, 'new_value': 35765.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 53}]
2025-05-25 12:02:11,078 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1F
2025-05-25 12:02:11,485 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1F
2025-05-25 12:02:11,485 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25000.0, 'new_value': 25520.0}, {'field': 'total_amount', 'old_value': 25000.0, 'new_value': 25520.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-05-25 12:02:11,485 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-25 12:02:11,922 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-25 12:02:11,922 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21092.68, 'new_value': 22061.48}, {'field': 'offline_amount', 'old_value': 14667.86, 'new_value': 15419.86}, {'field': 'total_amount', 'old_value': 35760.54, 'new_value': 37481.34}, {'field': 'order_count', 'old_value': 1527, 'new_value': 1594}]
2025-05-25 12:02:11,922 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-25 12:02:12,391 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-25 12:02:12,391 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 279438.0, 'new_value': 285585.0}, {'field': 'total_amount', 'old_value': 279438.0, 'new_value': 285585.0}, {'field': 'order_count', 'old_value': 426, 'new_value': 436}]
2025-05-25 12:02:12,391 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-25 12:02:12,813 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-25 12:02:12,813 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35654.0, 'new_value': 36853.5}, {'field': 'total_amount', 'old_value': 35654.0, 'new_value': 36853.5}, {'field': 'order_count', 'old_value': 165, 'new_value': 174}]
2025-05-25 12:02:12,813 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-25 12:02:13,297 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-25 12:02:13,297 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15154.8, 'new_value': 16651.0}, {'field': 'offline_amount', 'old_value': 37149.6, 'new_value': 43222.1}, {'field': 'total_amount', 'old_value': 52304.4, 'new_value': 59873.1}, {'field': 'order_count', 'old_value': 541, 'new_value': 599}]
2025-05-25 12:02:13,297 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-25 12:02:13,703 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-25 12:02:13,703 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96788.0, 'new_value': 102460.0}, {'field': 'total_amount', 'old_value': 96788.0, 'new_value': 102460.0}, {'field': 'order_count', 'old_value': 413, 'new_value': 433}]
2025-05-25 12:02:13,703 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6F
2025-05-25 12:02:14,203 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6F
2025-05-25 12:02:14,203 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15370.0, 'new_value': 19370.0}, {'field': 'total_amount', 'old_value': 15370.0, 'new_value': 19370.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-05-25 12:02:14,203 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPF
2025-05-25 12:02:14,625 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPF
2025-05-25 12:02:14,625 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 108731.0, 'new_value': 109967.0}, {'field': 'total_amount', 'old_value': 108731.0, 'new_value': 109967.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 23}]
2025-05-25 12:02:14,625 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-25 12:02:15,125 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-25 12:02:15,125 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 162889.93, 'new_value': 170724.5}, {'field': 'offline_amount', 'old_value': 42145.89, 'new_value': 44495.05}, {'field': 'total_amount', 'old_value': 205035.82, 'new_value': 215219.55}, {'field': 'order_count', 'old_value': 11551, 'new_value': 12186}]
2025-05-25 12:02:15,125 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-25 12:02:15,594 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-25 12:02:15,594 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 244874.93, 'new_value': 260781.33}, {'field': 'total_amount', 'old_value': 267362.33, 'new_value': 283268.73}, {'field': 'order_count', 'old_value': 1477, 'new_value': 1567}]
2025-05-25 12:02:15,594 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-25 12:02:16,000 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-25 12:02:16,000 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 157002.39, 'new_value': 165324.71}, {'field': 'total_amount', 'old_value': 157002.39, 'new_value': 165324.71}, {'field': 'order_count', 'old_value': 7930, 'new_value': 8430}]
2025-05-25 12:02:16,000 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-25 12:02:16,453 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-25 12:02:16,453 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 148353.6, 'new_value': 155735.9}, {'field': 'total_amount', 'old_value': 148353.6, 'new_value': 155735.9}, {'field': 'order_count', 'old_value': 660, 'new_value': 699}]
2025-05-25 12:02:16,453 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-25 12:02:16,953 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-25 12:02:16,953 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122835.3, 'new_value': 129892.9}, {'field': 'total_amount', 'old_value': 122835.3, 'new_value': 129892.9}, {'field': 'order_count', 'old_value': 3389, 'new_value': 3592}]
2025-05-25 12:02:16,953 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-25 12:02:17,469 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-25 12:02:17,469 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8885.0, 'new_value': 10494.0}, {'field': 'total_amount', 'old_value': 19670.0, 'new_value': 21279.0}, {'field': 'order_count', 'old_value': 110, 'new_value': 116}]
2025-05-25 12:02:17,469 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-25 12:02:17,906 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-25 12:02:17,906 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 94356.3, 'new_value': 99923.9}, {'field': 'total_amount', 'old_value': 94356.3, 'new_value': 99923.9}, {'field': 'order_count', 'old_value': 453, 'new_value': 481}]
2025-05-25 12:02:17,906 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-25 12:02:18,469 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-25 12:02:18,469 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 330153.9, 'new_value': 355745.9}, {'field': 'total_amount', 'old_value': 330153.9, 'new_value': 355745.9}]
2025-05-25 12:02:18,469 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-25 12:02:18,938 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-25 12:02:18,938 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96069.0, 'new_value': 101891.0}, {'field': 'total_amount', 'old_value': 96069.0, 'new_value': 101891.0}, {'field': 'order_count', 'old_value': 1693, 'new_value': 1694}]
2025-05-25 12:02:18,938 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-25 12:02:19,391 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-25 12:02:19,391 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78235.26, 'new_value': 81603.05}, {'field': 'total_amount', 'old_value': 80516.37, 'new_value': 83884.16}, {'field': 'order_count', 'old_value': 378, 'new_value': 390}]
2025-05-25 12:02:19,391 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXP
2025-05-25 12:02:19,813 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXP
2025-05-25 12:02:19,813 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 108600.0, 'new_value': 119944.0}, {'field': 'total_amount', 'old_value': 108600.0, 'new_value': 119944.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 33}]
2025-05-25 12:02:19,813 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-25 12:02:20,235 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-25 12:02:20,235 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 701630.77, 'new_value': 747645.04}, {'field': 'total_amount', 'old_value': 701630.77, 'new_value': 747645.04}, {'field': 'order_count', 'old_value': 5668, 'new_value': 6002}]
2025-05-25 12:02:20,235 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-25 12:02:20,735 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-25 12:02:20,735 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22194.0, 'new_value': 23171.3}, {'field': 'offline_amount', 'old_value': 174558.4, 'new_value': 183024.4}, {'field': 'total_amount', 'old_value': 196752.4, 'new_value': 206195.7}, {'field': 'order_count', 'old_value': 6120, 'new_value': 6441}]
2025-05-25 12:02:20,735 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-25 12:02:21,313 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-25 12:02:21,313 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6428.0, 'new_value': 6785.0}, {'field': 'offline_amount', 'old_value': 4605.0, 'new_value': 5115.0}, {'field': 'total_amount', 'old_value': 11033.0, 'new_value': 11900.0}, {'field': 'order_count', 'old_value': 108, 'new_value': 125}]
2025-05-25 12:02:21,313 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-25 12:02:21,766 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-25 12:02:21,766 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50513.96, 'new_value': 52476.78}, {'field': 'offline_amount', 'old_value': 50953.76, 'new_value': 52857.0}, {'field': 'total_amount', 'old_value': 101467.72, 'new_value': 105333.78}, {'field': 'order_count', 'old_value': 5069, 'new_value': 5265}]
2025-05-25 12:02:21,766 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-25 12:02:22,219 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-25 12:02:22,219 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3298.0, 'new_value': 3501.0}, {'field': 'offline_amount', 'old_value': 25639.0, 'new_value': 27520.8}, {'field': 'total_amount', 'old_value': 28937.0, 'new_value': 31021.8}, {'field': 'order_count', 'old_value': 1053, 'new_value': 1132}]
2025-05-25 12:02:22,219 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-25 12:02:22,641 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-25 12:02:22,656 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 96496.44, 'new_value': 100134.52}, {'field': 'offline_amount', 'old_value': 106912.05, 'new_value': 111823.61}, {'field': 'total_amount', 'old_value': 203408.49, 'new_value': 211958.13}, {'field': 'order_count', 'old_value': 5152, 'new_value': 5360}]
2025-05-25 12:02:22,656 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3Q
2025-05-25 12:02:23,078 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3Q
2025-05-25 12:02:23,078 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70180.0, 'new_value': 77160.0}, {'field': 'total_amount', 'old_value': 70180.0, 'new_value': 77160.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-05-25 12:02:23,078 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-25 12:02:23,625 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-25 12:02:23,625 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 903651.0, 'new_value': 943478.0}, {'field': 'total_amount', 'old_value': 903651.0, 'new_value': 943478.0}, {'field': 'order_count', 'old_value': 1037, 'new_value': 1080}]
2025-05-25 12:02:23,625 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-25 12:02:24,078 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-25 12:02:24,078 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 189219.1, 'new_value': 190485.1}, {'field': 'total_amount', 'old_value': 195169.4, 'new_value': 196435.4}, {'field': 'order_count', 'old_value': 362, 'new_value': 389}]
2025-05-25 12:02:24,078 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-25 12:02:24,500 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-25 12:02:24,500 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39987.85, 'new_value': 42196.65}, {'field': 'offline_amount', 'old_value': 107730.0, 'new_value': 124174.0}, {'field': 'total_amount', 'old_value': 147717.85, 'new_value': 166370.65}, {'field': 'order_count', 'old_value': 1652, 'new_value': 1753}]
2025-05-25 12:02:24,500 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-25 12:02:25,016 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-25 12:02:25,016 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 126906.0, 'new_value': 134241.0}, {'field': 'offline_amount', 'old_value': 85966.0, 'new_value': 91157.0}, {'field': 'total_amount', 'old_value': 212872.0, 'new_value': 225398.0}, {'field': 'order_count', 'old_value': 2804, 'new_value': 2960}]
2025-05-25 12:02:25,016 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-25 12:02:25,453 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-25 12:02:25,453 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22758.67, 'new_value': 23990.64}, {'field': 'total_amount', 'old_value': 30404.07, 'new_value': 31636.04}, {'field': 'order_count', 'old_value': 299, 'new_value': 309}]
2025-05-25 12:02:25,453 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-25 12:02:25,922 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-25 12:02:25,922 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9863.5, 'new_value': 10186.1}, {'field': 'offline_amount', 'old_value': 142447.0, 'new_value': 169991.0}, {'field': 'total_amount', 'old_value': 152310.5, 'new_value': 180177.1}, {'field': 'order_count', 'old_value': 71, 'new_value': 76}]
2025-05-25 12:02:25,922 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-25 12:02:26,391 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-25 12:02:26,391 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 105998.87, 'new_value': 112011.1}, {'field': 'total_amount', 'old_value': 113227.94, 'new_value': 119240.17}, {'field': 'order_count', 'old_value': 608, 'new_value': 642}]
2025-05-25 12:02:26,391 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-25 12:02:26,906 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-25 12:02:26,906 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29332.66, 'new_value': 32194.66}, {'field': 'offline_amount', 'old_value': 30934.82, 'new_value': 31471.82}, {'field': 'total_amount', 'old_value': 60267.48, 'new_value': 63666.48}, {'field': 'order_count', 'old_value': 267, 'new_value': 282}]
2025-05-25 12:02:26,906 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-25 12:02:27,359 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-25 12:02:27,359 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4512.0, 'new_value': 5988.0}, {'field': 'offline_amount', 'old_value': 51311.0, 'new_value': 53881.0}, {'field': 'total_amount', 'old_value': 55823.0, 'new_value': 59869.0}, {'field': 'order_count', 'old_value': 434, 'new_value': 463}]
2025-05-25 12:02:27,359 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-25 12:02:27,813 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-25 12:02:27,813 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 200558.5, 'new_value': 213594.5}, {'field': 'total_amount', 'old_value': 200558.5, 'new_value': 213594.5}, {'field': 'order_count', 'old_value': 992, 'new_value': 1053}]
2025-05-25 12:02:27,813 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-25 12:02:28,219 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-25 12:02:28,219 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4718.0, 'new_value': 4844.0}, {'field': 'offline_amount', 'old_value': 19954.0, 'new_value': 20844.0}, {'field': 'total_amount', 'old_value': 24672.0, 'new_value': 25688.0}, {'field': 'order_count', 'old_value': 193, 'new_value': 201}]
2025-05-25 12:02:28,219 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-25 12:02:28,656 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-25 12:02:28,656 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46612.14, 'new_value': 50888.77}, {'field': 'total_amount', 'old_value': 53278.18, 'new_value': 57554.81}, {'field': 'order_count', 'old_value': 480, 'new_value': 533}]
2025-05-25 12:02:28,656 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-25 12:02:29,109 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-25 12:02:29,109 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 178482.5, 'new_value': 190711.45}, {'field': 'total_amount', 'old_value': 178482.5, 'new_value': 190711.45}, {'field': 'order_count', 'old_value': 674, 'new_value': 722}]
2025-05-25 12:02:29,109 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-25 12:02:29,641 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-25 12:02:29,641 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 61096.0, 'new_value': 63346.0}, {'field': 'offline_amount', 'old_value': 283122.0, 'new_value': 296100.0}, {'field': 'total_amount', 'old_value': 344218.0, 'new_value': 359446.0}, {'field': 'order_count', 'old_value': 1376, 'new_value': 1451}]
2025-05-25 12:02:29,641 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-25 12:02:30,063 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-25 12:02:30,063 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 196148.0, 'new_value': 203937.0}, {'field': 'total_amount', 'old_value': 196148.0, 'new_value': 203937.0}, {'field': 'order_count', 'old_value': 445, 'new_value': 458}]
2025-05-25 12:02:30,063 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJQ
2025-05-25 12:02:30,500 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJQ
2025-05-25 12:02:30,500 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34602.0, 'new_value': 49742.0}, {'field': 'total_amount', 'old_value': 34602.0, 'new_value': 49742.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 16}]
2025-05-25 12:02:30,500 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-25 12:02:30,906 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-25 12:02:30,906 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 156197.6, 'new_value': 169099.6}, {'field': 'total_amount', 'old_value': 299852.38, 'new_value': 312754.38}]
2025-05-25 12:02:30,906 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-25 12:02:31,391 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-25 12:02:31,391 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 287832.3, 'new_value': 314125.8}, {'field': 'total_amount', 'old_value': 287832.3, 'new_value': 314125.8}, {'field': 'order_count', 'old_value': 6264, 'new_value': 6846}]
2025-05-25 12:02:31,391 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-25 12:02:31,813 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-25 12:02:31,813 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49506.4, 'new_value': 51685.9}, {'field': 'total_amount', 'old_value': 49506.4, 'new_value': 51685.9}, {'field': 'order_count', 'old_value': 268, 'new_value': 280}]
2025-05-25 12:02:31,813 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-25 12:02:32,234 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-25 12:02:32,234 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 177788.0, 'new_value': 193577.0}, {'field': 'offline_amount', 'old_value': 167419.0, 'new_value': 176409.0}, {'field': 'total_amount', 'old_value': 345207.0, 'new_value': 369986.0}, {'field': 'order_count', 'old_value': 929, 'new_value': 1001}]
2025-05-25 12:02:32,234 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-25 12:02:32,703 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-25 12:02:32,703 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 679573.05, 'new_value': 745530.96}, {'field': 'total_amount', 'old_value': 679573.05, 'new_value': 745530.96}, {'field': 'order_count', 'old_value': 3730, 'new_value': 3971}]
2025-05-25 12:02:32,703 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-25 12:02:33,203 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-25 12:02:33,203 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 127922.84, 'new_value': 135407.18}, {'field': 'total_amount', 'old_value': 127922.84, 'new_value': 135407.18}, {'field': 'order_count', 'old_value': 8851, 'new_value': 9394}]
2025-05-25 12:02:33,203 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-25 12:02:33,688 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-25 12:02:33,688 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 404747.0, 'new_value': 426464.0}, {'field': 'total_amount', 'old_value': 404747.0, 'new_value': 426464.0}, {'field': 'order_count', 'old_value': 9169, 'new_value': 9675}]
2025-05-25 12:02:33,688 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-25 12:02:34,203 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-25 12:02:34,203 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 136012.0, 'new_value': 142258.0}, {'field': 'total_amount', 'old_value': 136012.0, 'new_value': 142258.0}, {'field': 'order_count', 'old_value': 10116, 'new_value': 10540}]
2025-05-25 12:02:34,203 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-25 12:02:34,641 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-25 12:02:34,641 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32032.0, 'new_value': 32775.0}, {'field': 'total_amount', 'old_value': 32032.0, 'new_value': 32775.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 40}]
2025-05-25 12:02:34,641 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-25 12:02:35,094 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-25 12:02:35,094 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68896.2, 'new_value': 72570.2}, {'field': 'total_amount', 'old_value': 69176.0, 'new_value': 72850.0}, {'field': 'order_count', 'old_value': 1021, 'new_value': 1087}]
2025-05-25 12:02:35,094 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-25 12:02:35,594 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-25 12:02:35,594 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25509.9, 'new_value': 26723.6}, {'field': 'offline_amount', 'old_value': 55426.0, 'new_value': 59356.8}, {'field': 'total_amount', 'old_value': 80935.9, 'new_value': 86080.4}, {'field': 'order_count', 'old_value': 3033, 'new_value': 3220}]
2025-05-25 12:02:35,594 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM6V
2025-05-25 12:02:36,109 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM6V
2025-05-25 12:02:36,109 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54880.0, 'new_value': 64780.0}, {'field': 'total_amount', 'old_value': 54880.0, 'new_value': 64780.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-25 12:02:36,109 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-25 12:02:36,625 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-25 12:02:36,625 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59813.18, 'new_value': 63879.18}, {'field': 'total_amount', 'old_value': 65173.15, 'new_value': 69239.15}, {'field': 'order_count', 'old_value': 1065, 'new_value': 1175}]
2025-05-25 12:02:36,625 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-25 12:02:37,172 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-25 12:02:37,172 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4607573.51, 'new_value': 4879302.67}, {'field': 'total_amount', 'old_value': 4607573.51, 'new_value': 4879302.67}, {'field': 'order_count', 'old_value': 94958, 'new_value': 100468}]
2025-05-25 12:02:37,172 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-25 12:02:37,688 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-25 12:02:37,688 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23581.21, 'new_value': 24333.88}, {'field': 'total_amount', 'old_value': 23581.21, 'new_value': 24333.88}, {'field': 'order_count', 'old_value': 104, 'new_value': 108}]
2025-05-25 12:02:37,688 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM9V
2025-05-25 12:02:38,156 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM9V
2025-05-25 12:02:38,156 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 149248.9, 'new_value': 166492.9}, {'field': 'total_amount', 'old_value': 149248.9, 'new_value': 166492.9}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-05-25 12:02:38,156 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-25 12:02:38,594 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-25 12:02:38,594 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 495479.39, 'new_value': 522960.39}, {'field': 'total_amount', 'old_value': 501025.75, 'new_value': 528506.75}, {'field': 'order_count', 'old_value': 5364, 'new_value': 5489}]
2025-05-25 12:02:38,594 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-25 12:02:39,094 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-25 12:02:39,094 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 188153.6, 'new_value': 195281.76}, {'field': 'total_amount', 'old_value': 188153.6, 'new_value': 195281.76}, {'field': 'order_count', 'old_value': 3449, 'new_value': 3601}]
2025-05-25 12:02:39,094 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-25 12:02:39,672 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-25 12:02:39,672 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 302459.0, 'new_value': 316591.0}, {'field': 'total_amount', 'old_value': 302459.0, 'new_value': 316591.0}, {'field': 'order_count', 'old_value': 6605, 'new_value': 6922}]
2025-05-25 12:02:39,672 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMDV
2025-05-25 12:02:40,156 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMDV
2025-05-25 12:02:40,156 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32451.0, 'new_value': 32540.0}, {'field': 'total_amount', 'old_value': 32478.9, 'new_value': 32567.9}, {'field': 'order_count', 'old_value': 30, 'new_value': 31}]
2025-05-25 12:02:40,156 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-25 12:02:40,594 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-25 12:02:40,594 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 116176.13, 'new_value': 122367.43}, {'field': 'total_amount', 'old_value': 121144.33, 'new_value': 127335.63}, {'field': 'order_count', 'old_value': 3088, 'new_value': 3225}]
2025-05-25 12:02:40,594 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-25 12:02:41,078 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-25 12:02:41,078 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 94584.25, 'new_value': 98620.95}, {'field': 'offline_amount', 'old_value': 367420.5, 'new_value': 393004.8}, {'field': 'total_amount', 'old_value': 462004.75, 'new_value': 491625.75}, {'field': 'order_count', 'old_value': 3351, 'new_value': 3542}]
2025-05-25 12:02:41,078 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-25 12:02:41,563 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-25 12:02:41,563 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50219.7, 'new_value': 53855.33}, {'field': 'total_amount', 'old_value': 84572.2, 'new_value': 88207.83}, {'field': 'order_count', 'old_value': 5529, 'new_value': 5782}]
2025-05-25 12:02:41,563 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-25 12:02:42,000 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-25 12:02:42,000 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 88435.35, 'new_value': 94680.34}, {'field': 'total_amount', 'old_value': 148289.74, 'new_value': 154534.73}, {'field': 'order_count', 'old_value': 9773, 'new_value': 10197}]
2025-05-25 12:02:42,000 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-25 12:02:42,469 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-25 12:02:42,469 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1089658.87, 'new_value': 1139452.14}, {'field': 'total_amount', 'old_value': 1089658.87, 'new_value': 1139452.14}, {'field': 'order_count', 'old_value': 3189, 'new_value': 3350}]
2025-05-25 12:02:42,469 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-25 12:02:42,906 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-25 12:02:42,906 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 170244.8, 'new_value': 177043.8}, {'field': 'total_amount', 'old_value': 170244.8, 'new_value': 177043.8}, {'field': 'order_count', 'old_value': 5973, 'new_value': 6217}]
2025-05-25 12:02:42,906 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G
2025-05-25 12:02:43,422 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G
2025-05-25 12:02:43,422 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-05-25 12:02:43,422 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-25 12:02:43,969 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-25 12:02:43,969 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 669749.69, 'new_value': 710927.83}, {'field': 'total_amount', 'old_value': 669749.69, 'new_value': 710927.83}, {'field': 'order_count', 'old_value': 3507, 'new_value': 3724}]
2025-05-25 12:02:43,969 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-25 12:02:44,391 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-25 12:02:44,391 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 725852.18, 'new_value': 763604.91}, {'field': 'total_amount', 'old_value': 725852.18, 'new_value': 763604.91}, {'field': 'order_count', 'old_value': 2080, 'new_value': 2176}]
2025-05-25 12:02:44,391 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-25 12:02:44,828 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-25 12:02:44,828 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 356054.0, 'new_value': 425025.0}, {'field': 'total_amount', 'old_value': 360778.0, 'new_value': 429749.0}, {'field': 'order_count', 'old_value': 86, 'new_value': 90}]
2025-05-25 12:02:44,828 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-25 12:02:45,234 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-25 12:02:45,234 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 283976.71, 'new_value': 296071.34}, {'field': 'total_amount', 'old_value': 283976.71, 'new_value': 296071.34}, {'field': 'order_count', 'old_value': 781, 'new_value': 811}]
2025-05-25 12:02:45,234 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-25 12:02:45,641 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-25 12:02:45,641 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124035.0, 'new_value': 126511.0}, {'field': 'total_amount', 'old_value': 124115.0, 'new_value': 126591.0}, {'field': 'order_count', 'old_value': 12300, 'new_value': 12544}]
2025-05-25 12:02:45,641 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-25 12:02:46,109 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-25 12:02:46,109 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62370.0, 'new_value': 62899.0}, {'field': 'total_amount', 'old_value': 77875.0, 'new_value': 78404.0}, {'field': 'order_count', 'old_value': 122, 'new_value': 123}]
2025-05-25 12:02:46,109 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-25 12:02:46,625 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-25 12:02:46,625 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109356.0, 'new_value': 115233.0}, {'field': 'total_amount', 'old_value': 109356.0, 'new_value': 115233.0}, {'field': 'order_count', 'old_value': 569, 'new_value': 595}]
2025-05-25 12:02:46,625 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-25 12:02:47,172 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-25 12:02:47,172 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122772.0, 'new_value': 132208.0}, {'field': 'total_amount', 'old_value': 122772.0, 'new_value': 132208.0}, {'field': 'order_count', 'old_value': 498, 'new_value': 548}]
2025-05-25 12:02:47,172 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-25 12:02:47,609 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-25 12:02:47,609 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 230885.0, 'new_value': 243385.0}, {'field': 'total_amount', 'old_value': 230885.0, 'new_value': 243385.0}, {'field': 'order_count', 'old_value': 541, 'new_value': 572}]
2025-05-25 12:02:47,609 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMKG
2025-05-25 12:02:48,156 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMKG
2025-05-25 12:02:48,156 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16440.0, 'new_value': 23016.0}, {'field': 'total_amount', 'old_value': 32028.0, 'new_value': 38604.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 10}]
2025-05-25 12:02:48,156 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-25 12:02:48,609 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-25 12:02:48,609 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48182.0, 'new_value': 50451.0}, {'field': 'total_amount', 'old_value': 48182.0, 'new_value': 50451.0}, {'field': 'order_count', 'old_value': 940, 'new_value': 978}]
2025-05-25 12:02:48,609 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-25 12:02:49,047 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-25 12:02:49,047 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 186960.0, 'new_value': 190979.0}, {'field': 'total_amount', 'old_value': 186960.0, 'new_value': 190979.0}, {'field': 'order_count', 'old_value': 19768, 'new_value': 20154}]
2025-05-25 12:02:49,047 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-25 12:02:49,547 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-25 12:02:49,547 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109111.0, 'new_value': 111428.0}, {'field': 'total_amount', 'old_value': 109111.0, 'new_value': 111428.0}, {'field': 'order_count', 'old_value': 1045, 'new_value': 1086}]
2025-05-25 12:02:49,547 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-25 12:02:50,031 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-25 12:02:50,031 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 137782.83, 'new_value': 146470.74}, {'field': 'total_amount', 'old_value': 137782.83, 'new_value': 146470.74}, {'field': 'order_count', 'old_value': 1174, 'new_value': 1255}]
2025-05-25 12:02:50,031 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQG
2025-05-25 12:02:50,531 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQG
2025-05-25 12:02:50,531 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 99280.0, 'new_value': 104276.0}, {'field': 'total_amount', 'old_value': 99282.0, 'new_value': 104278.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 47}]
2025-05-25 12:02:50,531 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-25 12:02:51,000 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-25 12:02:51,000 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34585.0, 'new_value': 36565.0}, {'field': 'offline_amount', 'old_value': 39365.4, 'new_value': 39565.4}, {'field': 'total_amount', 'old_value': 73950.4, 'new_value': 76130.4}, {'field': 'order_count', 'old_value': 106, 'new_value': 110}]
2025-05-25 12:02:51,000 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-25 12:02:51,422 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-25 12:02:51,422 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45422.04, 'new_value': 47534.04}, {'field': 'total_amount', 'old_value': 45422.04, 'new_value': 47534.04}, {'field': 'order_count', 'old_value': 778, 'new_value': 818}]
2025-05-25 12:02:51,422 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-25 12:02:51,875 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-25 12:02:51,875 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 118970.79, 'new_value': 124111.85}, {'field': 'offline_amount', 'old_value': 654421.82, 'new_value': 682753.28}, {'field': 'total_amount', 'old_value': 773392.61, 'new_value': 806865.13}, {'field': 'order_count', 'old_value': 1752, 'new_value': 1831}]
2025-05-25 12:02:51,875 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-25 12:02:52,391 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-25 12:02:52,391 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70770.6, 'new_value': 71720.6}, {'field': 'total_amount', 'old_value': 70770.6, 'new_value': 71720.6}, {'field': 'order_count', 'old_value': 44, 'new_value': 45}]
2025-05-25 12:02:52,391 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-25 12:02:52,781 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-25 12:02:52,781 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 78214.29, 'new_value': 81772.91}, {'field': 'offline_amount', 'old_value': 773806.71, 'new_value': 806131.55}, {'field': 'total_amount', 'old_value': 850146.67, 'new_value': 886030.13}, {'field': 'order_count', 'old_value': 4040, 'new_value': 4203}]
2025-05-25 12:02:52,797 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMLX
2025-05-25 12:02:53,312 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMLX
2025-05-25 12:02:53,312 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9900.0, 'new_value': 12880.0}, {'field': 'total_amount', 'old_value': 11838.95, 'new_value': 14818.95}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-05-25 12:02:53,312 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-05-25 12:02:53,719 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-05-25 12:02:53,719 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74351.0, 'new_value': 76351.0}, {'field': 'total_amount', 'old_value': 79669.0, 'new_value': 81669.0}, {'field': 'order_count', 'old_value': 40, 'new_value': 42}]
2025-05-25 12:02:53,719 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-25 12:02:54,203 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-25 12:02:54,203 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16088662.34, 'new_value': 16753810.2}, {'field': 'total_amount', 'old_value': 16088662.34, 'new_value': 16753810.2}, {'field': 'order_count', 'old_value': 23, 'new_value': 24}]
2025-05-25 12:02:54,203 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-25 12:02:54,687 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-25 12:02:54,687 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30510.96, 'new_value': 36338.13}, {'field': 'offline_amount', 'old_value': 50734.33, 'new_value': 53478.97}, {'field': 'total_amount', 'old_value': 81245.29, 'new_value': 89817.1}, {'field': 'order_count', 'old_value': 370, 'new_value': 412}]
2025-05-25 12:02:54,687 - INFO - 开始更新记录 - 表单实例ID: FINST-1PF66VA1DOOVZDTTANZS29HIONIP3LXHFP1BM44
2025-05-25 12:02:55,125 - INFO - 更新表单数据成功: FINST-1PF66VA1DOOVZDTTANZS29HIONIP3LXHFP1BM44
2025-05-25 12:02:55,125 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 488.59, 'new_value': 1432.83}, {'field': 'total_amount', 'old_value': 488.59, 'new_value': 1432.83}, {'field': 'order_count', 'old_value': 11, 'new_value': 36}]
2025-05-25 12:02:55,125 - INFO - 开始批量插入 1 条新记录
2025-05-25 12:02:55,281 - INFO - 批量插入响应状态码: 200
2025-05-25 12:02:55,281 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 25 May 2025 04:02:55 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '346BDFB6-D02F-7B1C-BE3A-271038219AF1', 'x-acs-trace-id': '6b31165be48c388b03ae2a5eaf01e1d6', 'etag': '60SDnrRSSrFz3t1+C9mozFA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-25 12:02:55,281 - INFO - 批量插入响应体: {'result': ['FINST-DO566BD1K3PVOT69FG8V77RAX3DA3W8XU43BMOC']}
2025-05-25 12:02:55,281 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-25 12:02:55,281 - INFO - 成功插入的数据ID: ['FINST-DO566BD1K3PVOT69FG8V77RAX3DA3W8XU43BMOC']
2025-05-25 12:02:58,297 - INFO - 批量插入完成，共 1 条记录
2025-05-25 12:02:58,297 - INFO - 日期 2025-05 处理完成 - 更新: 314 条，插入: 1 条，错误: 0 条
2025-05-25 12:02:58,297 - INFO - 数据同步完成！更新: 314 条，插入: 1 条，错误: 0 条
2025-05-25 12:02:58,297 - INFO - =================同步完成====================
2025-05-25 15:00:01,977 - INFO - =================使用默认全量同步=============
2025-05-25 15:00:03,461 - INFO - MySQL查询成功，共获取 3299 条记录
2025-05-25 15:00:03,461 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-25 15:00:03,492 - INFO - 开始处理日期: 2025-01
2025-05-25 15:00:03,492 - INFO - Request Parameters - Page 1:
2025-05-25 15:00:03,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:00:03,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:00:04,430 - INFO - Response - Page 1:
2025-05-25 15:00:04,633 - INFO - 第 1 页获取到 100 条记录
2025-05-25 15:00:04,633 - INFO - Request Parameters - Page 2:
2025-05-25 15:00:04,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:00:04,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:00:05,180 - INFO - Response - Page 2:
2025-05-25 15:00:05,383 - INFO - 第 2 页获取到 100 条记录
2025-05-25 15:00:05,383 - INFO - Request Parameters - Page 3:
2025-05-25 15:00:05,383 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:00:05,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:00:06,336 - INFO - Response - Page 3:
2025-05-25 15:00:06,539 - INFO - 第 3 页获取到 100 条记录
2025-05-25 15:00:06,539 - INFO - Request Parameters - Page 4:
2025-05-25 15:00:06,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:00:06,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:00:07,024 - INFO - Response - Page 4:
2025-05-25 15:00:07,227 - INFO - 第 4 页获取到 100 条记录
2025-05-25 15:00:07,227 - INFO - Request Parameters - Page 5:
2025-05-25 15:00:07,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:00:07,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:00:07,727 - INFO - Response - Page 5:
2025-05-25 15:00:07,930 - INFO - 第 5 页获取到 100 条记录
2025-05-25 15:00:07,930 - INFO - Request Parameters - Page 6:
2025-05-25 15:00:07,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:00:07,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:00:08,445 - INFO - Response - Page 6:
2025-05-25 15:00:08,649 - INFO - 第 6 页获取到 100 条记录
2025-05-25 15:00:08,649 - INFO - Request Parameters - Page 7:
2025-05-25 15:00:08,649 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:00:08,649 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:00:09,117 - INFO - Response - Page 7:
2025-05-25 15:00:09,320 - INFO - 第 7 页获取到 82 条记录
2025-05-25 15:00:09,320 - INFO - 查询完成，共获取到 682 条记录
2025-05-25 15:00:09,320 - INFO - 获取到 682 条表单数据
2025-05-25 15:00:09,320 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-25 15:00:09,336 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 15:00:09,336 - INFO - 开始处理日期: 2025-02
2025-05-25 15:00:09,336 - INFO - Request Parameters - Page 1:
2025-05-25 15:00:09,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:00:09,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:00:09,899 - INFO - Response - Page 1:
2025-05-25 15:00:10,102 - INFO - 第 1 页获取到 100 条记录
2025-05-25 15:00:10,102 - INFO - Request Parameters - Page 2:
2025-05-25 15:00:10,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:00:10,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:00:10,617 - INFO - Response - Page 2:
2025-05-25 15:00:10,820 - INFO - 第 2 页获取到 100 条记录
2025-05-25 15:00:10,820 - INFO - Request Parameters - Page 3:
2025-05-25 15:00:10,820 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:00:10,820 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:00:11,680 - INFO - Response - Page 3:
2025-05-25 15:00:11,883 - INFO - 第 3 页获取到 100 条记录
2025-05-25 15:00:11,883 - INFO - Request Parameters - Page 4:
2025-05-25 15:00:11,883 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:00:11,883 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:00:12,399 - INFO - Response - Page 4:
2025-05-25 15:00:12,602 - INFO - 第 4 页获取到 100 条记录
2025-05-25 15:00:12,602 - INFO - Request Parameters - Page 5:
2025-05-25 15:00:12,602 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:00:12,602 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:00:13,133 - INFO - Response - Page 5:
2025-05-25 15:00:13,336 - INFO - 第 5 页获取到 100 条记录
2025-05-25 15:00:13,336 - INFO - Request Parameters - Page 6:
2025-05-25 15:00:13,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:00:13,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:00:13,961 - INFO - Response - Page 6:
2025-05-25 15:00:14,164 - INFO - 第 6 页获取到 100 条记录
2025-05-25 15:00:14,164 - INFO - Request Parameters - Page 7:
2025-05-25 15:00:14,164 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:00:14,164 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:00:14,633 - INFO - Response - Page 7:
2025-05-25 15:00:14,836 - INFO - 第 7 页获取到 70 条记录
2025-05-25 15:00:14,836 - INFO - 查询完成，共获取到 670 条记录
2025-05-25 15:00:14,836 - INFO - 获取到 670 条表单数据
2025-05-25 15:00:14,836 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-25 15:00:14,852 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 15:00:14,852 - INFO - 开始处理日期: 2025-03
2025-05-25 15:00:14,852 - INFO - Request Parameters - Page 1:
2025-05-25 15:00:14,852 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:00:14,852 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:00:15,352 - INFO - Response - Page 1:
2025-05-25 15:00:15,555 - INFO - 第 1 页获取到 100 条记录
2025-05-25 15:00:15,555 - INFO - Request Parameters - Page 2:
2025-05-25 15:00:15,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:00:15,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:00:16,055 - INFO - Response - Page 2:
2025-05-25 15:00:16,258 - INFO - 第 2 页获取到 100 条记录
2025-05-25 15:00:16,258 - INFO - Request Parameters - Page 3:
2025-05-25 15:00:16,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:00:16,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:00:16,774 - INFO - Response - Page 3:
2025-05-25 15:00:16,977 - INFO - 第 3 页获取到 100 条记录
2025-05-25 15:00:16,977 - INFO - Request Parameters - Page 4:
2025-05-25 15:00:16,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:00:16,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:00:17,445 - INFO - Response - Page 4:
2025-05-25 15:00:17,649 - INFO - 第 4 页获取到 100 条记录
2025-05-25 15:00:17,649 - INFO - Request Parameters - Page 5:
2025-05-25 15:00:17,649 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:00:17,649 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:00:18,149 - INFO - Response - Page 5:
2025-05-25 15:00:18,352 - INFO - 第 5 页获取到 100 条记录
2025-05-25 15:00:18,352 - INFO - Request Parameters - Page 6:
2025-05-25 15:00:18,352 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:00:18,352 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:00:18,899 - INFO - Response - Page 6:
2025-05-25 15:00:19,102 - INFO - 第 6 页获取到 100 条记录
2025-05-25 15:00:19,102 - INFO - Request Parameters - Page 7:
2025-05-25 15:00:19,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:00:19,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:00:19,492 - INFO - Response - Page 7:
2025-05-25 15:00:19,695 - INFO - 第 7 页获取到 61 条记录
2025-05-25 15:00:19,695 - INFO - 查询完成，共获取到 661 条记录
2025-05-25 15:00:19,695 - INFO - 获取到 661 条表单数据
2025-05-25 15:00:19,695 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-25 15:00:19,711 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 15:00:19,711 - INFO - 开始处理日期: 2025-04
2025-05-25 15:00:19,711 - INFO - Request Parameters - Page 1:
2025-05-25 15:00:19,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:00:19,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:00:20,242 - INFO - Response - Page 1:
2025-05-25 15:00:20,445 - INFO - 第 1 页获取到 100 条记录
2025-05-25 15:00:20,445 - INFO - Request Parameters - Page 2:
2025-05-25 15:00:20,445 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:00:20,445 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:00:20,899 - INFO - Response - Page 2:
2025-05-25 15:00:21,102 - INFO - 第 2 页获取到 100 条记录
2025-05-25 15:00:21,102 - INFO - Request Parameters - Page 3:
2025-05-25 15:00:21,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:00:21,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:00:21,617 - INFO - Response - Page 3:
2025-05-25 15:00:21,820 - INFO - 第 3 页获取到 100 条记录
2025-05-25 15:00:21,820 - INFO - Request Parameters - Page 4:
2025-05-25 15:00:21,820 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:00:21,820 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:00:22,352 - INFO - Response - Page 4:
2025-05-25 15:00:22,555 - INFO - 第 4 页获取到 100 条记录
2025-05-25 15:00:22,555 - INFO - Request Parameters - Page 5:
2025-05-25 15:00:22,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:00:22,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:00:23,102 - INFO - Response - Page 5:
2025-05-25 15:00:23,305 - INFO - 第 5 页获取到 100 条记录
2025-05-25 15:00:23,305 - INFO - Request Parameters - Page 6:
2025-05-25 15:00:23,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:00:23,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:00:23,805 - INFO - Response - Page 6:
2025-05-25 15:00:24,008 - INFO - 第 6 页获取到 100 条记录
2025-05-25 15:00:24,008 - INFO - Request Parameters - Page 7:
2025-05-25 15:00:24,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:00:24,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:00:24,477 - INFO - Response - Page 7:
2025-05-25 15:00:24,680 - INFO - 第 7 页获取到 56 条记录
2025-05-25 15:00:24,680 - INFO - 查询完成，共获取到 656 条记录
2025-05-25 15:00:24,680 - INFO - 获取到 656 条表单数据
2025-05-25 15:00:24,680 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-25 15:00:24,695 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 15:00:24,695 - INFO - 开始处理日期: 2025-05
2025-05-25 15:00:24,695 - INFO - Request Parameters - Page 1:
2025-05-25 15:00:24,695 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:00:24,695 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:00:25,180 - INFO - Response - Page 1:
2025-05-25 15:00:25,383 - INFO - 第 1 页获取到 100 条记录
2025-05-25 15:00:25,383 - INFO - Request Parameters - Page 2:
2025-05-25 15:00:25,383 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:00:25,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:00:25,836 - INFO - Response - Page 2:
2025-05-25 15:00:26,039 - INFO - 第 2 页获取到 100 条记录
2025-05-25 15:00:26,039 - INFO - Request Parameters - Page 3:
2025-05-25 15:00:26,039 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:00:26,039 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:00:26,445 - INFO - Response - Page 3:
2025-05-25 15:00:26,648 - INFO - 第 3 页获取到 100 条记录
2025-05-25 15:00:26,648 - INFO - Request Parameters - Page 4:
2025-05-25 15:00:26,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:00:26,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:00:27,148 - INFO - Response - Page 4:
2025-05-25 15:00:27,352 - INFO - 第 4 页获取到 100 条记录
2025-05-25 15:00:27,352 - INFO - Request Parameters - Page 5:
2025-05-25 15:00:27,352 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:00:27,352 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:00:27,836 - INFO - Response - Page 5:
2025-05-25 15:00:28,039 - INFO - 第 5 页获取到 100 条记录
2025-05-25 15:00:28,039 - INFO - Request Parameters - Page 6:
2025-05-25 15:00:28,039 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:00:28,039 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:00:28,523 - INFO - Response - Page 6:
2025-05-25 15:00:28,727 - INFO - 第 6 页获取到 100 条记录
2025-05-25 15:00:28,727 - INFO - Request Parameters - Page 7:
2025-05-25 15:00:28,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 15:00:28,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 15:00:29,070 - INFO - Response - Page 7:
2025-05-25 15:00:29,273 - INFO - 第 7 页获取到 30 条记录
2025-05-25 15:00:29,273 - INFO - 查询完成，共获取到 630 条记录
2025-05-25 15:00:29,273 - INFO - 获取到 630 条表单数据
2025-05-25 15:00:29,273 - INFO - 当前日期 2025-05 有 630 条MySQL数据需要处理
2025-05-25 15:00:29,273 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-25 15:00:29,695 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-25 15:00:29,695 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 173990.27, 'new_value': 181754.57}, {'field': 'total_amount', 'old_value': 173990.27, 'new_value': 181754.57}, {'field': 'order_count', 'old_value': 6559, 'new_value': 6826}]
2025-05-25 15:00:29,695 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-25 15:00:30,133 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-25 15:00:30,133 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 380208.0, 'new_value': 413367.0}, {'field': 'offline_amount', 'old_value': 304944.0, 'new_value': 315577.0}, {'field': 'total_amount', 'old_value': 685152.0, 'new_value': 728944.0}, {'field': 'order_count', 'old_value': 728, 'new_value': 781}]
2025-05-25 15:00:30,133 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-25 15:00:30,586 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-25 15:00:30,586 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 816389.6, 'new_value': 949629.4}, {'field': 'total_amount', 'old_value': 859025.8, 'new_value': 992265.6}, {'field': 'order_count', 'old_value': 88, 'new_value': 90}]
2025-05-25 15:00:30,586 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-25 15:00:31,086 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-25 15:00:31,086 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 160385.9, 'new_value': 163741.38}, {'field': 'total_amount', 'old_value': 160385.9, 'new_value': 163741.38}, {'field': 'order_count', 'old_value': 253, 'new_value': 263}]
2025-05-25 15:00:31,086 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-25 15:00:31,508 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-25 15:00:31,508 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 645450.7, 'new_value': 678916.07}, {'field': 'total_amount', 'old_value': 645450.7, 'new_value': 678916.07}, {'field': 'order_count', 'old_value': 3408, 'new_value': 3662}]
2025-05-25 15:00:31,508 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-25 15:00:31,977 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-25 15:00:31,977 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 165637.73, 'new_value': 176413.77}, {'field': 'total_amount', 'old_value': 165637.73, 'new_value': 176413.77}, {'field': 'order_count', 'old_value': 952, 'new_value': 1012}]
2025-05-25 15:00:31,977 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-25 15:00:32,445 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-25 15:00:32,445 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 195222.65, 'new_value': 225830.65}, {'field': 'offline_amount', 'old_value': 111493.0, 'new_value': 117995.0}, {'field': 'total_amount', 'old_value': 306715.65, 'new_value': 343825.65}, {'field': 'order_count', 'old_value': 1725, 'new_value': 1845}]
2025-05-25 15:00:32,445 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-25 15:00:32,883 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-25 15:00:32,883 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13271.43, 'new_value': 13727.8}, {'field': 'offline_amount', 'old_value': 267369.44, 'new_value': 289441.08}, {'field': 'total_amount', 'old_value': 280640.87, 'new_value': 303168.88}, {'field': 'order_count', 'old_value': 1950, 'new_value': 2083}]
2025-05-25 15:00:32,883 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-25 15:00:33,336 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-25 15:00:33,336 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1166000.0, 'new_value': 1226000.0}, {'field': 'total_amount', 'old_value': 1166000.0, 'new_value': 1226000.0}, {'field': 'order_count', 'old_value': 342, 'new_value': 343}]
2025-05-25 15:00:33,336 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-25 15:00:33,789 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-25 15:00:33,789 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5786365.0, 'new_value': 6119904.0}, {'field': 'total_amount', 'old_value': 5786365.0, 'new_value': 6119904.0}, {'field': 'order_count', 'old_value': 98315, 'new_value': 103565}]
2025-05-25 15:00:33,789 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-25 15:00:34,367 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-25 15:00:34,367 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46204.98, 'new_value': 46232.98}, {'field': 'offline_amount', 'old_value': 428466.5, 'new_value': 461466.5}, {'field': 'total_amount', 'old_value': 474671.48, 'new_value': 507699.48}, {'field': 'order_count', 'old_value': 3704, 'new_value': 3967}]
2025-05-25 15:00:34,367 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0V
2025-05-25 15:00:34,805 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0V
2025-05-25 15:00:34,805 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30430.2, 'new_value': 36230.2}, {'field': 'total_amount', 'old_value': 30624.2, 'new_value': 36424.2}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-05-25 15:00:34,805 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-25 15:00:35,289 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-25 15:00:35,289 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27678.0, 'new_value': 29046.0}, {'field': 'total_amount', 'old_value': 27678.0, 'new_value': 29046.0}, {'field': 'order_count', 'old_value': 268, 'new_value': 283}]
2025-05-25 15:00:35,305 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-25 15:00:35,789 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-25 15:00:35,789 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 674095.94, 'new_value': 707050.43}, {'field': 'total_amount', 'old_value': 674095.94, 'new_value': 707050.43}, {'field': 'order_count', 'old_value': 5041, 'new_value': 5271}]
2025-05-25 15:00:35,789 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-25 15:00:36,242 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-25 15:00:36,242 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 823900.0, 'new_value': 839900.0}, {'field': 'total_amount', 'old_value': 823900.0, 'new_value': 839900.0}, {'field': 'order_count', 'old_value': 61, 'new_value': 62}]
2025-05-25 15:00:36,242 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-25 15:00:36,680 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-25 15:00:36,680 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 644636.12, 'new_value': 681095.71}, {'field': 'total_amount', 'old_value': 644636.12, 'new_value': 681095.71}, {'field': 'order_count', 'old_value': 7623, 'new_value': 7950}]
2025-05-25 15:00:36,680 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-25 15:00:37,133 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-25 15:00:37,133 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 192328.71, 'new_value': 203054.92}, {'field': 'offline_amount', 'old_value': 22774.5, 'new_value': 23917.0}, {'field': 'total_amount', 'old_value': 215103.21, 'new_value': 226971.92}, {'field': 'order_count', 'old_value': 9972, 'new_value': 10445}]
2025-05-25 15:00:37,133 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-25 15:00:37,539 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-25 15:00:37,539 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 145456.45, 'new_value': 151691.45}, {'field': 'offline_amount', 'old_value': 268690.35, 'new_value': 290416.03}, {'field': 'total_amount', 'old_value': 414146.8, 'new_value': 442107.48}, {'field': 'order_count', 'old_value': 3455, 'new_value': 3664}]
2025-05-25 15:00:37,539 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-25 15:00:38,023 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-25 15:00:38,023 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 951716.0, 'new_value': 1012452.0}, {'field': 'total_amount', 'old_value': 951716.0, 'new_value': 1012452.0}, {'field': 'order_count', 'old_value': 4159, 'new_value': 4441}]
2025-05-25 15:00:38,023 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-25 15:00:38,492 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-25 15:00:38,492 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3462651.59, 'new_value': 3605873.78}, {'field': 'total_amount', 'old_value': 3462651.59, 'new_value': 3605873.78}, {'field': 'order_count', 'old_value': 5918, 'new_value': 6194}]
2025-05-25 15:00:38,492 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-25 15:00:38,914 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-25 15:00:38,914 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 150479.97, 'new_value': 157320.08}, {'field': 'total_amount', 'old_value': 157919.61, 'new_value': 164759.72}, {'field': 'order_count', 'old_value': 11076, 'new_value': 11563}]
2025-05-25 15:00:38,914 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-25 15:00:39,352 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-25 15:00:39,352 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 273756.96, 'new_value': 283193.86}, {'field': 'offline_amount', 'old_value': 189062.24, 'new_value': 199696.44}, {'field': 'total_amount', 'old_value': 462819.2, 'new_value': 482890.3}, {'field': 'order_count', 'old_value': 18582, 'new_value': 19453}]
2025-05-25 15:00:39,352 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-25 15:00:39,742 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-25 15:00:39,742 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 441866.0, 'new_value': 455516.0}, {'field': 'total_amount', 'old_value': 441866.0, 'new_value': 455516.0}, {'field': 'order_count', 'old_value': 74, 'new_value': 79}]
2025-05-25 15:00:39,742 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-25 15:00:40,289 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-25 15:00:40,289 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30317.0, 'new_value': 32969.0}, {'field': 'total_amount', 'old_value': 30317.0, 'new_value': 32969.0}, {'field': 'order_count', 'old_value': 96, 'new_value': 103}]
2025-05-25 15:00:40,289 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-25 15:00:40,727 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-25 15:00:40,742 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 167675.5, 'new_value': 176263.41}, {'field': 'total_amount', 'old_value': 167675.5, 'new_value': 176263.41}, {'field': 'order_count', 'old_value': 17580, 'new_value': 18522}]
2025-05-25 15:00:40,742 - INFO - 日期 2025-05 处理完成 - 更新: 25 条，插入: 0 条，错误: 0 条
2025-05-25 15:00:40,742 - INFO - 数据同步完成！更新: 25 条，插入: 0 条，错误: 0 条
2025-05-25 15:00:40,742 - INFO - =================同步完成====================
2025-05-25 18:00:01,985 - INFO - =================使用默认全量同步=============
2025-05-25 18:00:03,422 - INFO - MySQL查询成功，共获取 3300 条记录
2025-05-25 18:00:03,422 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-25 18:00:03,453 - INFO - 开始处理日期: 2025-01
2025-05-25 18:00:03,453 - INFO - Request Parameters - Page 1:
2025-05-25 18:00:03,453 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:00:03,453 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:00:04,813 - INFO - Response - Page 1:
2025-05-25 18:00:05,016 - INFO - 第 1 页获取到 100 条记录
2025-05-25 18:00:05,016 - INFO - Request Parameters - Page 2:
2025-05-25 18:00:05,016 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:00:05,016 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:00:05,516 - INFO - Response - Page 2:
2025-05-25 18:00:05,719 - INFO - 第 2 页获取到 100 条记录
2025-05-25 18:00:05,719 - INFO - Request Parameters - Page 3:
2025-05-25 18:00:05,719 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:00:05,719 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:00:06,219 - INFO - Response - Page 3:
2025-05-25 18:00:06,422 - INFO - 第 3 页获取到 100 条记录
2025-05-25 18:00:06,422 - INFO - Request Parameters - Page 4:
2025-05-25 18:00:06,422 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:00:06,422 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:00:06,922 - INFO - Response - Page 4:
2025-05-25 18:00:07,125 - INFO - 第 4 页获取到 100 条记录
2025-05-25 18:00:07,125 - INFO - Request Parameters - Page 5:
2025-05-25 18:00:07,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:00:07,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:00:07,641 - INFO - Response - Page 5:
2025-05-25 18:00:07,844 - INFO - 第 5 页获取到 100 条记录
2025-05-25 18:00:07,844 - INFO - Request Parameters - Page 6:
2025-05-25 18:00:07,844 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:00:07,844 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:00:08,344 - INFO - Response - Page 6:
2025-05-25 18:00:08,547 - INFO - 第 6 页获取到 100 条记录
2025-05-25 18:00:08,547 - INFO - Request Parameters - Page 7:
2025-05-25 18:00:08,547 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:00:08,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:00:09,063 - INFO - Response - Page 7:
2025-05-25 18:00:09,266 - INFO - 第 7 页获取到 82 条记录
2025-05-25 18:00:09,266 - INFO - 查询完成，共获取到 682 条记录
2025-05-25 18:00:09,266 - INFO - 获取到 682 条表单数据
2025-05-25 18:00:09,266 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-25 18:00:09,281 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 18:00:09,281 - INFO - 开始处理日期: 2025-02
2025-05-25 18:00:09,281 - INFO - Request Parameters - Page 1:
2025-05-25 18:00:09,281 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:00:09,281 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:00:09,766 - INFO - Response - Page 1:
2025-05-25 18:00:09,969 - INFO - 第 1 页获取到 100 条记录
2025-05-25 18:00:09,969 - INFO - Request Parameters - Page 2:
2025-05-25 18:00:09,969 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:00:09,969 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:00:10,469 - INFO - Response - Page 2:
2025-05-25 18:00:10,672 - INFO - 第 2 页获取到 100 条记录
2025-05-25 18:00:10,672 - INFO - Request Parameters - Page 3:
2025-05-25 18:00:10,672 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:00:10,672 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:00:11,219 - INFO - Response - Page 3:
2025-05-25 18:00:11,422 - INFO - 第 3 页获取到 100 条记录
2025-05-25 18:00:11,422 - INFO - Request Parameters - Page 4:
2025-05-25 18:00:11,422 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:00:11,422 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:00:11,938 - INFO - Response - Page 4:
2025-05-25 18:00:12,141 - INFO - 第 4 页获取到 100 条记录
2025-05-25 18:00:12,141 - INFO - Request Parameters - Page 5:
2025-05-25 18:00:12,141 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:00:12,141 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:00:12,703 - INFO - Response - Page 5:
2025-05-25 18:00:12,906 - INFO - 第 5 页获取到 100 条记录
2025-05-25 18:00:12,906 - INFO - Request Parameters - Page 6:
2025-05-25 18:00:12,906 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:00:12,906 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:00:13,422 - INFO - Response - Page 6:
2025-05-25 18:00:13,625 - INFO - 第 6 页获取到 100 条记录
2025-05-25 18:00:13,625 - INFO - Request Parameters - Page 7:
2025-05-25 18:00:13,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:00:13,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:00:14,109 - INFO - Response - Page 7:
2025-05-25 18:00:14,313 - INFO - 第 7 页获取到 70 条记录
2025-05-25 18:00:14,313 - INFO - 查询完成，共获取到 670 条记录
2025-05-25 18:00:14,313 - INFO - 获取到 670 条表单数据
2025-05-25 18:00:14,313 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-25 18:00:14,328 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 18:00:14,328 - INFO - 开始处理日期: 2025-03
2025-05-25 18:00:14,328 - INFO - Request Parameters - Page 1:
2025-05-25 18:00:14,328 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:00:14,328 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:00:14,813 - INFO - Response - Page 1:
2025-05-25 18:00:15,016 - INFO - 第 1 页获取到 100 条记录
2025-05-25 18:00:15,016 - INFO - Request Parameters - Page 2:
2025-05-25 18:00:15,016 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:00:15,016 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:00:16,031 - INFO - Response - Page 2:
2025-05-25 18:00:16,234 - INFO - 第 2 页获取到 100 条记录
2025-05-25 18:00:16,234 - INFO - Request Parameters - Page 3:
2025-05-25 18:00:16,234 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:00:16,234 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:00:16,656 - INFO - Response - Page 3:
2025-05-25 18:00:16,859 - INFO - 第 3 页获取到 100 条记录
2025-05-25 18:00:16,859 - INFO - Request Parameters - Page 4:
2025-05-25 18:00:16,859 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:00:16,859 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:00:17,391 - INFO - Response - Page 4:
2025-05-25 18:00:17,594 - INFO - 第 4 页获取到 100 条记录
2025-05-25 18:00:17,594 - INFO - Request Parameters - Page 5:
2025-05-25 18:00:17,594 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:00:17,594 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:00:18,156 - INFO - Response - Page 5:
2025-05-25 18:00:18,359 - INFO - 第 5 页获取到 100 条记录
2025-05-25 18:00:18,359 - INFO - Request Parameters - Page 6:
2025-05-25 18:00:18,359 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:00:18,359 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:00:18,859 - INFO - Response - Page 6:
2025-05-25 18:00:19,063 - INFO - 第 6 页获取到 100 条记录
2025-05-25 18:00:19,063 - INFO - Request Parameters - Page 7:
2025-05-25 18:00:19,063 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:00:19,063 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:00:19,500 - INFO - Response - Page 7:
2025-05-25 18:00:19,703 - INFO - 第 7 页获取到 61 条记录
2025-05-25 18:00:19,703 - INFO - 查询完成，共获取到 661 条记录
2025-05-25 18:00:19,703 - INFO - 获取到 661 条表单数据
2025-05-25 18:00:19,703 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-25 18:00:19,719 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 18:00:19,719 - INFO - 开始处理日期: 2025-04
2025-05-25 18:00:19,719 - INFO - Request Parameters - Page 1:
2025-05-25 18:00:19,719 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:00:19,719 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:00:20,266 - INFO - Response - Page 1:
2025-05-25 18:00:20,469 - INFO - 第 1 页获取到 100 条记录
2025-05-25 18:00:20,469 - INFO - Request Parameters - Page 2:
2025-05-25 18:00:20,469 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:00:20,469 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:00:20,969 - INFO - Response - Page 2:
2025-05-25 18:00:21,172 - INFO - 第 2 页获取到 100 条记录
2025-05-25 18:00:21,172 - INFO - Request Parameters - Page 3:
2025-05-25 18:00:21,172 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:00:21,172 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:00:21,625 - INFO - Response - Page 3:
2025-05-25 18:00:21,828 - INFO - 第 3 页获取到 100 条记录
2025-05-25 18:00:21,828 - INFO - Request Parameters - Page 4:
2025-05-25 18:00:21,828 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:00:21,828 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:00:22,375 - INFO - Response - Page 4:
2025-05-25 18:00:22,578 - INFO - 第 4 页获取到 100 条记录
2025-05-25 18:00:22,578 - INFO - Request Parameters - Page 5:
2025-05-25 18:00:22,578 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:00:22,578 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:00:23,031 - INFO - Response - Page 5:
2025-05-25 18:00:23,234 - INFO - 第 5 页获取到 100 条记录
2025-05-25 18:00:23,234 - INFO - Request Parameters - Page 6:
2025-05-25 18:00:23,234 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:00:23,234 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:00:23,734 - INFO - Response - Page 6:
2025-05-25 18:00:23,938 - INFO - 第 6 页获取到 100 条记录
2025-05-25 18:00:23,938 - INFO - Request Parameters - Page 7:
2025-05-25 18:00:23,938 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:00:23,938 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:00:24,313 - INFO - Response - Page 7:
2025-05-25 18:00:24,516 - INFO - 第 7 页获取到 56 条记录
2025-05-25 18:00:24,516 - INFO - 查询完成，共获取到 656 条记录
2025-05-25 18:00:24,516 - INFO - 获取到 656 条表单数据
2025-05-25 18:00:24,516 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-25 18:00:24,531 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 18:00:24,531 - INFO - 开始处理日期: 2025-05
2025-05-25 18:00:24,531 - INFO - Request Parameters - Page 1:
2025-05-25 18:00:24,531 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:00:24,531 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:00:25,094 - INFO - Response - Page 1:
2025-05-25 18:00:25,297 - INFO - 第 1 页获取到 100 条记录
2025-05-25 18:00:25,297 - INFO - Request Parameters - Page 2:
2025-05-25 18:00:25,297 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:00:25,297 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:00:25,781 - INFO - Response - Page 2:
2025-05-25 18:00:25,984 - INFO - 第 2 页获取到 100 条记录
2025-05-25 18:00:25,984 - INFO - Request Parameters - Page 3:
2025-05-25 18:00:25,984 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:00:25,984 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:00:26,438 - INFO - Response - Page 3:
2025-05-25 18:00:26,641 - INFO - 第 3 页获取到 100 条记录
2025-05-25 18:00:26,641 - INFO - Request Parameters - Page 4:
2025-05-25 18:00:26,641 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:00:26,641 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:00:27,125 - INFO - Response - Page 4:
2025-05-25 18:00:27,328 - INFO - 第 4 页获取到 100 条记录
2025-05-25 18:00:27,328 - INFO - Request Parameters - Page 5:
2025-05-25 18:00:27,328 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:00:27,328 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:00:27,828 - INFO - Response - Page 5:
2025-05-25 18:00:28,031 - INFO - 第 5 页获取到 100 条记录
2025-05-25 18:00:28,031 - INFO - Request Parameters - Page 6:
2025-05-25 18:00:28,031 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:00:28,031 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:00:28,453 - INFO - Response - Page 6:
2025-05-25 18:00:28,656 - INFO - 第 6 页获取到 100 条记录
2025-05-25 18:00:28,656 - INFO - Request Parameters - Page 7:
2025-05-25 18:00:28,656 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 18:00:28,656 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 18:00:29,016 - INFO - Response - Page 7:
2025-05-25 18:00:29,219 - INFO - 第 7 页获取到 30 条记录
2025-05-25 18:00:29,219 - INFO - 查询完成，共获取到 630 条记录
2025-05-25 18:00:29,219 - INFO - 获取到 630 条表单数据
2025-05-25 18:00:29,219 - INFO - 当前日期 2025-05 有 631 条MySQL数据需要处理
2025-05-25 18:00:29,234 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4L
2025-05-25 18:00:29,703 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4L
2025-05-25 18:00:29,703 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4349.9, 'new_value': 5354.4}, {'field': 'total_amount', 'old_value': 4349.9, 'new_value': 5354.4}, {'field': 'order_count', 'old_value': 287, 'new_value': 385}]
2025-05-25 18:00:29,703 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBL
2025-05-25 18:00:30,156 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBL
2025-05-25 18:00:30,156 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34009.0, 'new_value': 35809.0}, {'field': 'total_amount', 'old_value': 34009.0, 'new_value': 35809.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-05-25 18:00:30,156 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGL
2025-05-25 18:00:30,750 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGL
2025-05-25 18:00:30,750 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37405.8, 'new_value': 40205.8}, {'field': 'total_amount', 'old_value': 37405.8, 'new_value': 40205.8}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-05-25 18:00:30,750 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-25 18:00:31,172 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-25 18:00:31,172 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45760.0, 'new_value': 47440.0}, {'field': 'total_amount', 'old_value': 45760.0, 'new_value': 47440.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-05-25 18:00:31,172 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-25 18:00:31,547 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-25 18:00:31,547 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 130000.84, 'new_value': 138133.85}, {'field': 'total_amount', 'old_value': 130000.84, 'new_value': 138133.85}, {'field': 'order_count', 'old_value': 11805, 'new_value': 12483}]
2025-05-25 18:00:31,547 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-25 18:00:31,984 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-25 18:00:31,984 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107329.0, 'new_value': 116648.0}, {'field': 'total_amount', 'old_value': 107329.0, 'new_value': 116648.0}, {'field': 'order_count', 'old_value': 3966, 'new_value': 4185}]
2025-05-25 18:00:31,984 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-25 18:00:32,391 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-25 18:00:32,391 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28041.07, 'new_value': 29559.07}, {'field': 'total_amount', 'old_value': 28041.07, 'new_value': 29559.07}, {'field': 'order_count', 'old_value': 2721, 'new_value': 2836}]
2025-05-25 18:00:32,391 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-25 18:00:32,891 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-25 18:00:32,891 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5288200.0, 'new_value': 7087200.0}, {'field': 'total_amount', 'old_value': 5288200.0, 'new_value': 7087200.0}, {'field': 'order_count', 'old_value': 66, 'new_value': 220}]
2025-05-25 18:00:32,891 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-25 18:00:33,406 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-25 18:00:33,406 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 402698.0, 'new_value': 413643.0}, {'field': 'total_amount', 'old_value': 411516.99, 'new_value': 422461.99}, {'field': 'order_count', 'old_value': 75, 'new_value': 77}]
2025-05-25 18:00:33,406 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-25 18:00:33,859 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-25 18:00:33,859 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 381697.64, 'new_value': 417802.64}, {'field': 'total_amount', 'old_value': 381697.64, 'new_value': 417802.64}, {'field': 'order_count', 'old_value': 729, 'new_value': 794}]
2025-05-25 18:00:33,859 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-25 18:00:34,312 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-25 18:00:34,312 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80512.0, 'new_value': 90509.0}, {'field': 'total_amount', 'old_value': 80512.0, 'new_value': 90509.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 34}]
2025-05-25 18:00:34,328 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-25 18:00:34,750 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-25 18:00:34,750 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29749.0, 'new_value': 31484.0}, {'field': 'total_amount', 'old_value': 31125.0, 'new_value': 32860.0}, {'field': 'order_count', 'old_value': 3198, 'new_value': 3367}]
2025-05-25 18:00:34,750 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMP3
2025-05-25 18:00:35,266 - INFO - 更新表单数据成功: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMP3
2025-05-25 18:00:35,266 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7427.26, 'new_value': 7827.76}, {'field': 'offline_amount', 'old_value': 110659.9, 'new_value': 118242.9}, {'field': 'total_amount', 'old_value': 118087.16, 'new_value': 126070.66}, {'field': 'order_count', 'old_value': 6234, 'new_value': 6515}]
2025-05-25 18:00:35,266 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-25 18:00:35,672 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-25 18:00:35,672 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91876.0, 'new_value': 97018.0}, {'field': 'total_amount', 'old_value': 91876.0, 'new_value': 97018.0}, {'field': 'order_count', 'old_value': 623, 'new_value': 655}]
2025-05-25 18:00:35,672 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-25 18:00:36,125 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-25 18:00:36,125 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 289516.0, 'new_value': 303655.0}, {'field': 'total_amount', 'old_value': 316606.0, 'new_value': 330745.0}, {'field': 'order_count', 'old_value': 6683, 'new_value': 6902}]
2025-05-25 18:00:36,125 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-25 18:00:36,547 - INFO - 更新表单数据成功: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-25 18:00:36,547 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106277.0, 'new_value': 111564.0}, {'field': 'total_amount', 'old_value': 106277.0, 'new_value': 111564.0}, {'field': 'order_count', 'old_value': 348, 'new_value': 362}]
2025-05-25 18:00:36,547 - INFO - 开始批量插入 1 条新记录
2025-05-25 18:00:36,703 - INFO - 批量插入响应状态码: 200
2025-05-25 18:00:36,703 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 25 May 2025 10:00:37 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '9F723FCF-7288-74DD-A22E-0AC1516D0937', 'x-acs-trace-id': '74e80b6b210ad6773ccad22e9b19732b', 'etag': '5VAf81j74N5F4tgtbv9bJOw9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-25 18:00:36,703 - INFO - 批量插入响应体: {'result': ['FINST-68E66TC1VHQVDV207ZXGN7K5YJZA293XMH3BM0']}
2025-05-25 18:00:36,703 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-25 18:00:36,703 - INFO - 成功插入的数据ID: ['FINST-68E66TC1VHQVDV207ZXGN7K5YJZA293XMH3BM0']
2025-05-25 18:00:39,719 - INFO - 批量插入完成，共 1 条记录
2025-05-25 18:00:39,719 - INFO - 日期 2025-05 处理完成 - 更新: 16 条，插入: 1 条，错误: 0 条
2025-05-25 18:00:39,719 - INFO - 数据同步完成！更新: 16 条，插入: 1 条，错误: 0 条
2025-05-25 18:00:39,719 - INFO - =================同步完成====================
2025-05-25 21:00:01,761 - INFO - =================使用默认全量同步=============
2025-05-25 21:00:03,198 - INFO - MySQL查询成功，共获取 3300 条记录
2025-05-25 21:00:03,198 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-25 21:00:03,229 - INFO - 开始处理日期: 2025-01
2025-05-25 21:00:03,245 - INFO - Request Parameters - Page 1:
2025-05-25 21:00:03,245 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:00:03,245 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:00:04,526 - INFO - Response - Page 1:
2025-05-25 21:00:04,729 - INFO - 第 1 页获取到 100 条记录
2025-05-25 21:00:04,729 - INFO - Request Parameters - Page 2:
2025-05-25 21:00:04,729 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:00:04,729 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:00:05,338 - INFO - Response - Page 2:
2025-05-25 21:00:05,541 - INFO - 第 2 页获取到 100 条记录
2025-05-25 21:00:05,541 - INFO - Request Parameters - Page 3:
2025-05-25 21:00:05,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:00:05,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:00:06,088 - INFO - Response - Page 3:
2025-05-25 21:00:06,291 - INFO - 第 3 页获取到 100 条记录
2025-05-25 21:00:06,291 - INFO - Request Parameters - Page 4:
2025-05-25 21:00:06,291 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:00:06,291 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:00:06,760 - INFO - Response - Page 4:
2025-05-25 21:00:06,963 - INFO - 第 4 页获取到 100 条记录
2025-05-25 21:00:06,963 - INFO - Request Parameters - Page 5:
2025-05-25 21:00:06,963 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:00:06,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:00:07,479 - INFO - Response - Page 5:
2025-05-25 21:00:07,682 - INFO - 第 5 页获取到 100 条记录
2025-05-25 21:00:07,682 - INFO - Request Parameters - Page 6:
2025-05-25 21:00:07,682 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:00:07,682 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:00:08,182 - INFO - Response - Page 6:
2025-05-25 21:00:08,385 - INFO - 第 6 页获取到 100 条记录
2025-05-25 21:00:08,385 - INFO - Request Parameters - Page 7:
2025-05-25 21:00:08,385 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:00:08,385 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:00:08,838 - INFO - Response - Page 7:
2025-05-25 21:00:09,041 - INFO - 第 7 页获取到 82 条记录
2025-05-25 21:00:09,041 - INFO - 查询完成，共获取到 682 条记录
2025-05-25 21:00:09,041 - INFO - 获取到 682 条表单数据
2025-05-25 21:00:09,041 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-25 21:00:09,057 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 21:00:09,057 - INFO - 开始处理日期: 2025-02
2025-05-25 21:00:09,057 - INFO - Request Parameters - Page 1:
2025-05-25 21:00:09,057 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:00:09,057 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:00:09,541 - INFO - Response - Page 1:
2025-05-25 21:00:09,744 - INFO - 第 1 页获取到 100 条记录
2025-05-25 21:00:09,744 - INFO - Request Parameters - Page 2:
2025-05-25 21:00:09,744 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:00:09,744 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:00:10,275 - INFO - Response - Page 2:
2025-05-25 21:00:10,478 - INFO - 第 2 页获取到 100 条记录
2025-05-25 21:00:10,478 - INFO - Request Parameters - Page 3:
2025-05-25 21:00:10,478 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:00:10,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:00:10,963 - INFO - Response - Page 3:
2025-05-25 21:00:11,166 - INFO - 第 3 页获取到 100 条记录
2025-05-25 21:00:11,166 - INFO - Request Parameters - Page 4:
2025-05-25 21:00:11,166 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:00:11,166 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:00:11,603 - INFO - Response - Page 4:
2025-05-25 21:00:11,806 - INFO - 第 4 页获取到 100 条记录
2025-05-25 21:00:11,806 - INFO - Request Parameters - Page 5:
2025-05-25 21:00:11,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:00:11,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:00:12,275 - INFO - Response - Page 5:
2025-05-25 21:00:12,478 - INFO - 第 5 页获取到 100 条记录
2025-05-25 21:00:12,478 - INFO - Request Parameters - Page 6:
2025-05-25 21:00:12,478 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:00:12,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:00:12,978 - INFO - Response - Page 6:
2025-05-25 21:00:13,181 - INFO - 第 6 页获取到 100 条记录
2025-05-25 21:00:13,181 - INFO - Request Parameters - Page 7:
2025-05-25 21:00:13,181 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:00:13,181 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:00:13,665 - INFO - Response - Page 7:
2025-05-25 21:00:13,868 - INFO - 第 7 页获取到 70 条记录
2025-05-25 21:00:13,868 - INFO - 查询完成，共获取到 670 条记录
2025-05-25 21:00:13,868 - INFO - 获取到 670 条表单数据
2025-05-25 21:00:13,868 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-25 21:00:13,884 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 21:00:13,884 - INFO - 开始处理日期: 2025-03
2025-05-25 21:00:13,884 - INFO - Request Parameters - Page 1:
2025-05-25 21:00:13,884 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:00:13,884 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:00:14,384 - INFO - Response - Page 1:
2025-05-25 21:00:14,587 - INFO - 第 1 页获取到 100 条记录
2025-05-25 21:00:14,587 - INFO - Request Parameters - Page 2:
2025-05-25 21:00:14,587 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:00:14,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:00:15,087 - INFO - Response - Page 2:
2025-05-25 21:00:15,290 - INFO - 第 2 页获取到 100 条记录
2025-05-25 21:00:15,290 - INFO - Request Parameters - Page 3:
2025-05-25 21:00:15,290 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:00:15,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:00:15,774 - INFO - Response - Page 3:
2025-05-25 21:00:15,977 - INFO - 第 3 页获取到 100 条记录
2025-05-25 21:00:15,977 - INFO - Request Parameters - Page 4:
2025-05-25 21:00:15,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:00:15,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:00:16,462 - INFO - Response - Page 4:
2025-05-25 21:00:16,665 - INFO - 第 4 页获取到 100 条记录
2025-05-25 21:00:16,665 - INFO - Request Parameters - Page 5:
2025-05-25 21:00:16,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:00:16,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:00:17,071 - INFO - Response - Page 5:
2025-05-25 21:00:17,274 - INFO - 第 5 页获取到 100 条记录
2025-05-25 21:00:17,274 - INFO - Request Parameters - Page 6:
2025-05-25 21:00:17,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:00:17,274 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:00:17,743 - INFO - Response - Page 6:
2025-05-25 21:00:17,946 - INFO - 第 6 页获取到 100 条记录
2025-05-25 21:00:17,946 - INFO - Request Parameters - Page 7:
2025-05-25 21:00:17,946 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:00:17,946 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:00:18,336 - INFO - Response - Page 7:
2025-05-25 21:00:18,540 - INFO - 第 7 页获取到 61 条记录
2025-05-25 21:00:18,540 - INFO - 查询完成，共获取到 661 条记录
2025-05-25 21:00:18,540 - INFO - 获取到 661 条表单数据
2025-05-25 21:00:18,540 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-25 21:00:18,555 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 21:00:18,555 - INFO - 开始处理日期: 2025-04
2025-05-25 21:00:18,555 - INFO - Request Parameters - Page 1:
2025-05-25 21:00:18,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:00:18,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:00:19,164 - INFO - Response - Page 1:
2025-05-25 21:00:19,368 - INFO - 第 1 页获取到 100 条记录
2025-05-25 21:00:19,368 - INFO - Request Parameters - Page 2:
2025-05-25 21:00:19,368 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:00:19,368 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:00:19,821 - INFO - Response - Page 2:
2025-05-25 21:00:20,024 - INFO - 第 2 页获取到 100 条记录
2025-05-25 21:00:20,024 - INFO - Request Parameters - Page 3:
2025-05-25 21:00:20,024 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:00:20,024 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:00:20,524 - INFO - Response - Page 3:
2025-05-25 21:00:20,727 - INFO - 第 3 页获取到 100 条记录
2025-05-25 21:00:20,727 - INFO - Request Parameters - Page 4:
2025-05-25 21:00:20,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:00:20,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:00:21,305 - INFO - Response - Page 4:
2025-05-25 21:00:21,508 - INFO - 第 4 页获取到 100 条记录
2025-05-25 21:00:21,508 - INFO - Request Parameters - Page 5:
2025-05-25 21:00:21,508 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:00:21,508 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:00:22,086 - INFO - Response - Page 5:
2025-05-25 21:00:22,289 - INFO - 第 5 页获取到 100 条记录
2025-05-25 21:00:22,289 - INFO - Request Parameters - Page 6:
2025-05-25 21:00:22,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:00:22,289 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:00:22,789 - INFO - Response - Page 6:
2025-05-25 21:00:22,992 - INFO - 第 6 页获取到 100 条记录
2025-05-25 21:00:22,992 - INFO - Request Parameters - Page 7:
2025-05-25 21:00:22,992 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:00:22,992 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:00:23,461 - INFO - Response - Page 7:
2025-05-25 21:00:23,664 - INFO - 第 7 页获取到 56 条记录
2025-05-25 21:00:23,664 - INFO - 查询完成，共获取到 656 条记录
2025-05-25 21:00:23,664 - INFO - 获取到 656 条表单数据
2025-05-25 21:00:23,664 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-25 21:00:23,679 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-25 21:00:23,679 - INFO - 开始处理日期: 2025-05
2025-05-25 21:00:23,679 - INFO - Request Parameters - Page 1:
2025-05-25 21:00:23,679 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:00:23,679 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:00:24,226 - INFO - Response - Page 1:
2025-05-25 21:00:24,429 - INFO - 第 1 页获取到 100 条记录
2025-05-25 21:00:24,429 - INFO - Request Parameters - Page 2:
2025-05-25 21:00:24,429 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:00:24,429 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:00:24,976 - INFO - Response - Page 2:
2025-05-25 21:00:25,179 - INFO - 第 2 页获取到 100 条记录
2025-05-25 21:00:25,179 - INFO - Request Parameters - Page 3:
2025-05-25 21:00:25,179 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:00:25,179 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:00:25,679 - INFO - Response - Page 3:
2025-05-25 21:00:25,882 - INFO - 第 3 页获取到 100 条记录
2025-05-25 21:00:25,882 - INFO - Request Parameters - Page 4:
2025-05-25 21:00:25,882 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:00:25,882 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:00:26,429 - INFO - Response - Page 4:
2025-05-25 21:00:26,632 - INFO - 第 4 页获取到 100 条记录
2025-05-25 21:00:26,632 - INFO - Request Parameters - Page 5:
2025-05-25 21:00:26,632 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:00:26,632 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:00:27,116 - INFO - Response - Page 5:
2025-05-25 21:00:27,320 - INFO - 第 5 页获取到 100 条记录
2025-05-25 21:00:27,320 - INFO - Request Parameters - Page 6:
2025-05-25 21:00:27,320 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:00:27,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:00:27,773 - INFO - Response - Page 6:
2025-05-25 21:00:27,976 - INFO - 第 6 页获取到 100 条记录
2025-05-25 21:00:27,976 - INFO - Request Parameters - Page 7:
2025-05-25 21:00:27,976 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-25 21:00:27,976 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-25 21:00:28,382 - INFO - Response - Page 7:
2025-05-25 21:00:28,585 - INFO - 第 7 页获取到 31 条记录
2025-05-25 21:00:28,585 - INFO - 查询完成，共获取到 631 条记录
2025-05-25 21:00:28,585 - INFO - 获取到 631 条表单数据
2025-05-25 21:00:28,585 - INFO - 当前日期 2025-05 有 631 条MySQL数据需要处理
2025-05-25 21:00:28,585 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-25 21:00:29,007 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-25 21:00:29,007 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1600.0, 'new_value': 1650.0}, {'field': 'offline_amount', 'old_value': 29374.0, 'new_value': 30602.0}, {'field': 'total_amount', 'old_value': 30974.0, 'new_value': 32252.0}, {'field': 'order_count', 'old_value': 113, 'new_value': 119}]
2025-05-25 21:00:29,007 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-25 21:00:29,429 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-25 21:00:29,429 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7154.6, 'new_value': 7242.13}, {'field': 'offline_amount', 'old_value': 100947.82, 'new_value': 103660.65}, {'field': 'total_amount', 'old_value': 108102.42, 'new_value': 110902.78}, {'field': 'order_count', 'old_value': 2595, 'new_value': 2676}]
2025-05-25 21:00:29,429 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-25 21:00:29,835 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-25 21:00:29,835 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 78038.06, 'new_value': 81062.16}, {'field': 'offline_amount', 'old_value': 1007608.76, 'new_value': 1062729.92}, {'field': 'total_amount', 'old_value': 1085646.82, 'new_value': 1143792.08}, {'field': 'order_count', 'old_value': 8838, 'new_value': 9261}]
2025-05-25 21:00:29,835 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-25 21:00:30,241 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-25 21:00:30,241 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 70219.75, 'new_value': 78562.65}, {'field': 'total_amount', 'old_value': 70219.75, 'new_value': 78562.65}, {'field': 'order_count', 'old_value': 400, 'new_value': 453}]
2025-05-25 21:00:30,241 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-25 21:00:30,710 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-25 21:00:30,710 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26097.1, 'new_value': 27426.6}, {'field': 'offline_amount', 'old_value': 20409.6, 'new_value': 21102.6}, {'field': 'total_amount', 'old_value': 46506.7, 'new_value': 48529.2}, {'field': 'order_count', 'old_value': 248, 'new_value': 262}]
2025-05-25 21:00:30,710 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-25 21:00:31,272 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-25 21:00:31,272 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16133.68, 'new_value': 16471.49}, {'field': 'offline_amount', 'old_value': 248731.24, 'new_value': 262151.94}, {'field': 'total_amount', 'old_value': 264864.92, 'new_value': 278623.43}, {'field': 'order_count', 'old_value': 14698, 'new_value': 15383}]
2025-05-25 21:00:31,272 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-25 21:00:31,710 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-25 21:00:31,710 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46935.38, 'new_value': 47959.31}, {'field': 'offline_amount', 'old_value': 30187.0, 'new_value': 32078.0}, {'field': 'total_amount', 'old_value': 77122.38, 'new_value': 80037.31}, {'field': 'order_count', 'old_value': 961, 'new_value': 999}]
2025-05-25 21:00:31,710 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-25 21:00:32,085 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-25 21:00:32,085 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 175087.0, 'new_value': 180853.0}, {'field': 'total_amount', 'old_value': 175087.0, 'new_value': 180853.0}, {'field': 'order_count', 'old_value': 2250, 'new_value': 2326}]
2025-05-25 21:00:32,085 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-25 21:00:32,538 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-25 21:00:32,538 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51351.05, 'new_value': 52863.45}, {'field': 'total_amount', 'old_value': 51351.05, 'new_value': 52863.45}, {'field': 'order_count', 'old_value': 192, 'new_value': 198}]
2025-05-25 21:00:32,538 - INFO - 日期 2025-05 处理完成 - 更新: 9 条，插入: 0 条，错误: 0 条
2025-05-25 21:00:32,538 - INFO - 数据同步完成！更新: 9 条，插入: 0 条，错误: 0 条
2025-05-25 21:00:32,538 - INFO - =================同步完成====================
