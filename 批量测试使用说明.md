# 批量账单导入测试工具使用说明

## 📋 概述

本工具用于批量生成账单数据并调用 `com.gooagoo.exportbill` 接口进行导入，同时将成功的数据保存到MySQL数据库中。

## 🗂️ 文件说明

- **`batch_bill_test.py`** - 主程序文件
- **`create_bill_table.sql`** - MySQL建表语句
- **`批量测试使用说明.md`** - 本说明文档

## 🔧 环境准备

### 1. Python依赖安装
```bash
pip install requests mysql-connector-python
```

### 2. MySQL数据库准备
```sql
-- 方式一：直接执行SQL文件
source d:/yida_program/create_bill_table.sql;

-- 方式二：手动执行建表语句
CREATE DATABASE IF NOT EXISTS `bill_test` DEFAULT CHARACTER SET utf8mb4;
USE `bill_test`;
-- 然后执行create_bill_table.sql中的建表语句
```

## ⚙️ 配置修改

### 1. 修改测试参数（文件顶部）
```python
# 指定测试日期（格式：YYYY-MM-DD）
TEST_DATE = "2025-07-06"

# 指定生成的记录数量
RECORD_COUNT = 10
```

### 2. 修改数据库配置
```python
DB_CONFIG = {
    'host': 'localhost',        # 数据库主机
    'port': 3306,              # 数据库端口
    'user': 'root',            # 数据库用户名
    'password': 'your_password', # 数据库密码（请修改）
    'database': 'bill_test',    # 数据库名
    'charset': 'utf8mb4'
}
```

## 📊 数据生成规则

### 1. 设备编号 (terminalNumber)
- 从固定列表随机选择：`["BBBB00000271"]`
- 可在代码中添加更多设备编号

### 2. 票据流水号 (billSerialNumber)
- 生成规则：`设备编号前6位 + 销售时间(YYYYMMDDHHMMSS) + 4位随机数`
- 示例：`BBBB0020250706142530001`

### 3. 金额设置
- **应收金额** (totalFee): 固定 `0.01`
- **实收金额** (receivableAmount): 固定 `0.00`
- **实付金额** (paidAmount): 固定 `0.00`

### 4. 商品数量 (totalNum)
- 随机整数：`1-10`

### 5. 账单类型 (exactBillType)
- 随机选择：`["1", "10102", "10103"]`
  - `1`: 普通结账单
  - `10102`: 美团外卖单
  - `10103`: 饿了么外卖单

### 6. 销售时间 (saleTime)
- 时间范围：指定日期的 `10:10-21:55`
- 格式：`YYYY-MM-DD HH:MM:SS`

### 7. 第三方订单号 (thirdPartyOrderNo)
- 生成规则：`设备编号前6位 + 时间戳 + 3位随机数`
- 示例：`BBBB001751798479123`

## 🚀 运行方式

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 运行程序
```bash
python batch_bill_test.py
```

## 📈 运行流程

1. **初始化数据库** - 自动创建数据库和表
2. **生成测试数据** - 按规则批量生成账单数据
3. **调用API接口** - 逐条调用账单导入接口
4. **收集成功记录** - 暂存API调用成功的数据
5. **批量插入数据库** - 所有接口调用完成后，批量插入数据库（高效）
6. **输出统计结果** - 显示成功率和详细统计

## 📋 输出示例

```
🔄 处理第 1/10 条记录...
   设备编号: BBBB00000271
   销售时间: 2025-07-06 14:25:30
   账单类型: 10102 - 美团外卖单
   流水号: BBBB0020250706142530001
   商品数量: 7.0
   第三方订单号: BBBB001751798479123
   ✅ 第 1 条记录导入成功

💾 开始批量插入数据库，共 10 条成功记录...
✅ 批量插入数据库成功，共插入 10 条记录

================================================================================
批量导入完成 - 统计结果
================================================================================
📊 总记录数: 10
✅ 接口成功: 10
❌ 接口失败: 0
💾 数据库保存: 10
📈 接口成功率: 100.0%
💽 数据库成功率: 100.0%
================================================================================
```

## 🗄️ 数据库表结构

### 表名：`bill_import_records`

| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| id | bigint(20) | 主键ID | 自增主键 |
| exact_bill_type | varchar(20) | 细分账单类型 | 非空 |
| bill_serial_number | varchar(100) | 票据流水号 | 非空，唯一 |
| terminal_number | varchar(50) | 设备编号 | 非空 |
| sale_time | datetime | 销售时间 | 非空 |
| third_party_order_no | varchar(100) | 第三方订单号 | 非空，唯一 |
| receivable_amount | decimal(10,2) | 实收金额 | 非空 |
| total_num | decimal(10,2) | 商品数量 | 非空 |
| total_fee | decimal(10,2) | 应收金额 | 非空 |
| paid_amount | decimal(10,2) | 实付金额 | 非空 |
| bill_type | varchar(10) | 账单类型 | 非空 |
| api_response | text | API响应结果 | 可空 |
| created_at | timestamp | 创建时间 | 自动生成 |
| updated_at | timestamp | 更新时间 | 自动更新 |

### 索引说明
- **主键索引**: `id`
- **唯一索引**: `bill_serial_number`, `third_party_order_no`
- **普通索引**: `terminal_number`, `sale_time`, `exact_bill_type`, `created_at`

## 📊 常用查询语句

### 1. 查询今天的记录
```sql
SELECT * FROM bill_import_records 
WHERE DATE(sale_time) = CURDATE() 
ORDER BY sale_time DESC;
```

### 2. 按账单类型统计
```sql
SELECT 
    exact_bill_type,
    COUNT(*) as record_count,
    SUM(total_fee) as total_amount
FROM bill_import_records 
GROUP BY exact_bill_type;
```

### 3. 查询指定设备的记录
```sql
SELECT * FROM bill_import_records 
WHERE terminal_number = 'BBBB00000271' 
ORDER BY sale_time DESC;
```

### 4. 按日期统计
```sql
SELECT 
    DATE(sale_time) as sale_date,
    COUNT(*) as daily_count,
    SUM(total_fee) as daily_amount
FROM bill_import_records 
GROUP BY DATE(sale_time)
ORDER BY sale_date DESC;
```

## 📝 日志文件

- **文件名**: `batch_bill_test_YYYYMMDD.log`
- **位置**: 程序运行目录
- **内容**: 详细的执行日志、API请求响应、错误信息

## ⚠️ 注意事项

1. **数据库权限**: 确保数据库用户有创建数据库和表的权限
2. **网络连接**: 确保能正常访问API接口和数据库
3. **唯一性约束**: 票据流水号和第三方订单号具有唯一性约束
4. **请求频率**: 程序自动控制请求间隔（0.5秒），避免过于频繁
5. **数据备份**: 建议定期备份数据库数据

## 🔧 故障排除

### 1. 数据库连接失败
- 检查数据库配置信息
- 确认数据库服务是否启动
- 验证用户名密码是否正确

### 2. API调用失败
- 检查网络连接
- 验证API配置信息
- 查看详细错误日志

### 3. 数据插入失败
- 检查唯一性约束冲突
- 验证数据格式是否正确
- 查看数据库错误日志

### 4. 依赖包安装失败
```bash
# 如果pip安装失败，可以尝试
pip install --upgrade pip
pip install requests mysql-connector-python -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

## 📞 技术支持

如遇问题，请：
1. 查看生成的日志文件
2. 检查数据库连接状态
3. 验证API配置信息
4. 查看程序输出的错误信息
