import hashlib
import time
import requests
import json
from openpyxl import Workbook
import logging
from datetime import datetime, timedelta
import schedule

# 配置日志记录
current_date = datetime.now().strftime('%Y%m%d')
logging.basicConfig(
    filename=f'logs/api_call_log_{current_date}.txt',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    encoding='utf-8'  # 添加 UTF-8 编码设置
)

def generate_sign(params, api_secret):
    """
    生成签名，采用MD5算法
    :param params: 参数字典
    :param api_secret: API密钥
    :return: 签名字符串
    """
    # 删除空值的参数
    params = {k: v for k, v in params.items() if v is not None and v != ""}
    # 按ASCII码从小到大排序
    sorted_keys = sorted(params)
    # 拼接成stringA
    stringA = '&'.join([f"{k}={params[k]}" for k in sorted_keys])
    # 拼接API密钥
    stringSignTemp = f"{stringA}&key={api_secret}"
    # MD5运算并转换为大写
    sign = hashlib.md5(stringSignTemp.encode('utf-8')).hexdigest().upper()
    return sign

def call_sale_query_api(app_id, app_key, api_secret, method, lower_method, url, business_data):
    try:
        timestamp = time.strftime("%Y%m%d%H%M%S", time.localtime())
        
        # 公共参数
        public_params = {
            "appId": app_id,
            "appKey": app_key,
            "method": method,
            "lowerMethod": lower_method,  # 业务接口名称
            "timestamp": timestamp,
            "messageFormat": "Json",
            "v": "1.0",
            "signMethod": "MD5"
        }
        
        # 将业务数据封装到 "data" 参数中
        data_param = json.dumps(business_data, ensure_ascii=False)
        all_params = {**public_params, "data": data_param}
        
        # 生成签名
        all_params["sign"] = generate_sign(all_params, api_secret)
        
        # 记录请求信息
        logging.info(f"Request URL: {url}")
        logging.info(f"Request Params: {all_params}")

        response = requests.post(url, data=all_params)
        result = response.json()
        
        # # 记录响应信息
        # logging.info(f"Response: {result}")
        
        return result
        
    except Exception as e:
        logging.error(f"API调用失败: {str(e)}")
        return {"error": str(e)}

def save_to_excel(data_list, shop_mapping, file_name="response_data.xlsx"):
    """
    将接口返回内容保存到Excel文件
    :param data_list: 接口返回的数据列表（每个元素是一个包含所有业务响应参数的字典）
    :param shop_mapping: shopId与项目名称的映射关系
    :param file_name: 保存的Excel文件名
    """
    wb = Workbook()
    ws = wb.active
    ws.title = "销售数据"

    # 写入表头（使用中文列名）
    headers = [
        "上报状态", "审核确认金额", "审核确认销售笔数", "日结金额", "综合销售额",
        "数衍平台店铺名称", "上报金额", "上报笔数", "净销售额", "总销售笔数（包括退款单）",
        "店内净销售额", "店内销售笔数", "线上净销售额", "线上销售笔数",
        "数衍平台机构ID", "数衍平台店铺ID", "三方机构编码", "三方商户编码", "销售日期", "推荐金额", "项目名称"
    ]
    ws.append(headers)

    # 写入数据
    for item in data_list:
        row = []
        # 将英文字段映射到中文列
        field_mapping = {
            "reviewStatus": "上报状态", "confirmMoney": "审核确认金额", "confirmBill": "审核确认销售笔数",
            "dailyBillAmount": "日结金额", "checkAmount": "综合销售额", "shopEntityName": "数衍平台店铺名称",
            "reportAmount": "上报金额", "reportCount": "上报笔数", "amount": "净销售额",
            "count": "总销售笔数（包括退款单）", "instoreAmount": "店内净销售额", "instoreCount": "店内销售笔数",
            "onlineAmount": "线上净销售额", "onlineCount": "线上销售笔数", "shopId": "数衍平台机构ID",
            "shopEntityId": "数衍平台店铺ID", "squareCode": "三方机构编码", "merchantCode": "三方商户编码",
            "saleTime": "销售日期", "recommendAmount": "推荐金额"
        }
        for header in headers:
            if header == "项目名称":
                project_name = shop_mapping.get(item.get("shopId", ""), "")
                row.append(project_name)
            else:
                # 根据中文列名找到对应的英文字段
                field = [k for k, v in field_mapping.items() if v == header][0]
                value = item.get(field, "")  # 如果字段不存在，默认值为空字符串
                # 对数值型字段进行特殊处理
                if isinstance(value, float) or isinstance(value, int):
                    row.append(value)
                else:
                    row.append(value if value else "")  # 如果值为空或None，填充为空字符串
        ws.append(row)

    # 保存Excel文件
    wb.save(file_name)
    logging.info(f"数据已保存到Excel文件: {file_name}")

def get_date_ranges():
    """
    生成过去30天的日期范围，每7天一个批次
    返回: [(start_date, end_date), ...]
    """
    today = datetime.now()
    ranges = []
    
    for i in range(30, 0, -7):
        end_date = today - timedelta(days=i)
        start_date = end_date - timedelta(days=6)
        if start_date < today - timedelta(days=30):
            start_date = today - timedelta(days=30)
        ranges.append((start_date.strftime("%Y%m%d"), end_date.strftime("%Y%m%d")))
    
    return ranges

def daily_task():
    """
    每日执行的任务
    """
    appId = "a5274b7e5d9a41939346c33c2c3443db"
    appKey = "2c9a5a628e7dab16018f5b055f3d0002"
    apiSecret = "07F77244AD915AC2BB3EECE8EF7AE4DB"
    method = "gogo.open.auto.routing"
    lowerMethod = "com.gooagoo.open.api.salequery"
    url = "http://api.gooagoo.com/oapi/rest"
    
    # shopId与项目名称的映射关系
    shop_mapping = {
        "1ETDLFB9DIMQME7Q2OVD93ISAI00189O": "广州维多利广场",
        "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE": "武汉国金天地",
        "1HFLOR99TBR11L6UBHOUTGCK1C001A3F": "广州悦汇城",
        "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV": "悦汇广场·南海",
        "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU": "广州国金天地",
        "1HRIS7255PESAA7AV8LHQQGIH8001KNH": "广州环贸天地",
        "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D": "武汉星汇维港购物中心"
    }

    date_ranges = get_date_ranges()
    all_data = []
    
    for start_date, end_date in date_ranges:
        business_data = {
            "fromDate": start_date,
            "toDate": end_date,
            "shopIds": list(shop_mapping.keys())
        }
        
        logging.info(f"开始获取{start_date}至{end_date}的销售数据")
        logging.info(f"Business data: {business_data}")
        
        response_data = call_sale_query_api(appId, appKey, apiSecret, method, lowerMethod, url, business_data)
        
        if 'data' in response_data and isinstance(response_data['data'], list):
            all_data.extend(response_data['data'])
        else:
            logging.error(f"获取{start_date}至{end_date}的数据失败")
        
        # 添加适当的延时，避免请求过于频繁
        time.sleep(2)
    
    if all_data:
        current_time = datetime.now().strftime("%Y%m%d")
        file_name = f"sales_data_30days_{current_time}.xlsx"
        save_to_excel(all_data, shop_mapping, file_name)
    else:
        logging.error("没有获取到任何数据")

if __name__ == "__main__":
    # 设置每天早上9点执行任务
    # schedule.every().day.at("09:00").do(daily_task)
    
    # 首次运行时立即执行一次
    daily_task()
    
    # # 保持程序运行
    # while True:
    #     schedule.run_pending()
    #     time.sleep(60)