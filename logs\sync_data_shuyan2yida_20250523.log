2025-05-23 08:00:03,599 - INFO - ==================================================
2025-05-23 08:00:03,599 - INFO - 程序启动 - 版本 v1.0.0
2025-05-23 08:00:03,599 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250523.log
2025-05-23 08:00:03,599 - INFO - ==================================================
2025-05-23 08:00:03,599 - INFO - 程序入口点: __main__
2025-05-23 08:00:03,599 - INFO - ==================================================
2025-05-23 08:00:03,599 - INFO - 程序启动 - 版本 v1.0.1
2025-05-23 08:00:03,599 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250523.log
2025-05-23 08:00:03,599 - INFO - ==================================================
2025-05-23 08:00:03,864 - INFO - 数据库文件已存在: data\sales_data.db
2025-05-23 08:00:03,880 - INFO - sales_data表已存在，无需创建
2025-05-23 08:00:03,880 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-05-23 08:00:03,880 - INFO - DataSyncManager初始化完成
2025-05-23 08:00:03,880 - INFO - 未提供日期参数，使用默认值
2025-05-23 08:00:03,880 - INFO - 开始执行综合数据同步，参数: start_date=None, end_date=None
2025-05-23 08:00:03,880 - INFO - 开始综合数据同步流程...
2025-05-23 08:00:03,880 - INFO - 正在获取数衍平台日销售数据...
2025-05-23 08:00:03,880 - INFO - 查询数衍平台数据，时间段为: 2025-03-23, 2025-05-22
2025-05-23 08:00:03,880 - INFO - 正在获取********至********的数据
2025-05-23 08:00:03,880 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-23 08:00:03,880 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '842AA67B92006D8701349100A93E04A9'}
2025-05-23 08:00:06,880 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-23 08:00:06,880 - INFO - 过滤后保留 1564 条记录
2025-05-23 08:00:08,896 - INFO - 正在获取********至********的数据
2025-05-23 08:00:08,896 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-23 08:00:08,896 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '5BCCA0A32E2340C3C7DA00B1DFC3486F'}
2025-05-23 08:00:11,192 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-23 08:00:11,192 - INFO - 过滤后保留 1522 条记录
2025-05-23 08:00:13,224 - INFO - 正在获取********至********的数据
2025-05-23 08:00:13,224 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-23 08:00:13,224 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '6160D830ACC2DFCDEF2F0C8316B42EAA'}
2025-05-23 08:00:15,286 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-23 08:00:15,302 - INFO - 过滤后保留 1496 条记录
2025-05-23 08:00:17,317 - INFO - 正在获取********至********的数据
2025-05-23 08:00:17,317 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-23 08:00:17,317 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '90DEA24F1536E02AD05CC10835AB8F21'}
2025-05-23 08:00:19,427 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-23 08:00:19,442 - INFO - 过滤后保留 1509 条记录
2025-05-23 08:00:21,458 - INFO - 正在获取********至********的数据
2025-05-23 08:00:21,458 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-23 08:00:21,458 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '77D3F9B2243E5D5548352CE5F1184125'}
2025-05-23 08:00:23,458 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-23 08:00:23,474 - INFO - 过滤后保留 1482 条记录
2025-05-23 08:00:25,474 - INFO - 正在获取********至********的数据
2025-05-23 08:00:25,474 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-23 08:00:25,474 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'AED3596E5050F47A717E56E6DF6F1D90'}
2025-05-23 08:00:27,099 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-23 08:00:27,114 - INFO - 过滤后保留 1492 条记录
2025-05-23 08:00:29,130 - INFO - 正在获取********至********的数据
2025-05-23 08:00:29,130 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-23 08:00:29,130 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'F77AE41C432FA3A0D632A06C7B90DBF3'}
2025-05-23 08:00:30,895 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-23 08:00:30,895 - INFO - 过滤后保留 1470 条记录
2025-05-23 08:00:32,911 - INFO - 正在获取********至********的数据
2025-05-23 08:00:32,911 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-23 08:00:32,911 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '5B0CDB003C3628377529A09D16AFEA4D'}
2025-05-23 08:00:34,505 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-23 08:00:34,520 - INFO - 过滤后保留 1480 条记录
2025-05-23 08:00:36,536 - INFO - 正在获取********至********的数据
2025-05-23 08:00:36,536 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-05-23 08:00:36,536 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '357365DB3CEFAB47DF22150305BEA6BA'}
2025-05-23 08:00:37,786 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-05-23 08:00:37,786 - INFO - 过滤后保留 1045 条记录
2025-05-23 08:00:39,802 - INFO - 开始保存数据到SQLite数据库，共 13060 条记录待处理
2025-05-23 08:00:40,005 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR, sale_time=2025-04-05
2025-05-23 08:00:40,005 - INFO - 变更字段: recommend_amount: 27815.44 -> 27569.44, amount: 27815 -> 27569, count: 75 -> 74, instore_amount: 25737.2 -> 25491.2, instore_count: 58 -> 57
2025-05-23 08:00:40,005 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR, sale_time=2025-04-02
2025-05-23 08:00:40,005 - INFO - 变更字段: recommend_amount: 13481.01 -> 13143.01, amount: 13481 -> 13143, count: 38 -> 37, instore_amount: 12696.0 -> 12358.0, instore_count: 31 -> 30
2025-05-23 08:00:40,083 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR, sale_time=2025-04-12
2025-05-23 08:00:40,083 - INFO - 变更字段: recommend_amount: 26559.69 -> 25183.69, amount: 26559 -> 25183, count: 87 -> 83, instore_amount: 24000.0 -> 22624.0, instore_count: 66 -> 62
2025-05-23 08:00:40,083 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR, sale_time=2025-04-11
2025-05-23 08:00:40,083 - INFO - 变更字段: recommend_amount: 18881.27 -> 18418.67, amount: 18881 -> 18418, count: 58 -> 57, instore_amount: 17778.2 -> 17315.6, instore_count: 49 -> 48
2025-05-23 08:00:40,083 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR, sale_time=2025-04-08
2025-05-23 08:00:40,083 - INFO - 变更字段: recommend_amount: 5801.88 -> 5534.88, amount: 5801 -> 5534, count: 32 -> 31, instore_amount: 4461.2 -> 4194.2, instore_count: 18 -> 17
2025-05-23 08:00:40,145 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR, sale_time=2025-04-19
2025-05-23 08:00:40,145 - INFO - 变更字段: recommend_amount: 42497.8 -> 42139.8, amount: 42497 -> 42139, count: 98 -> 97, instore_amount: 39379.8 -> 39021.8, instore_count: 77 -> 76
2025-05-23 08:00:40,145 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR, sale_time=2025-04-17
2025-05-23 08:00:40,145 - INFO - 变更字段: recommend_amount: 32698.32 -> 31462.32, amount: 32698 -> 31462, count: 64 -> 62, instore_amount: 32787.6 -> 31551.6, instore_count: 57 -> 55
2025-05-23 08:00:40,145 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR, sale_time=2025-04-16
2025-05-23 08:00:40,145 - INFO - 变更字段: recommend_amount: 35630.72 -> 34264.72, amount: 35630 -> 34264, count: 64 -> 63, instore_amount: 35110.1 -> 33744.1, instore_count: 57 -> 56
2025-05-23 08:00:40,208 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR, sale_time=2025-04-26
2025-05-23 08:00:40,208 - INFO - 变更字段: recommend_amount: 55972.28 -> 55674.28, amount: 55972 -> 55674, count: 87 -> 86, instore_amount: 53162.6 -> 52864.6, instore_count: 68 -> 67
2025-05-23 08:00:40,208 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR, sale_time=2025-04-22
2025-05-23 08:00:40,208 - INFO - 变更字段: recommend_amount: 27966.15 -> 27468.15, amount: 27966 -> 27468, count: 63 -> 62, instore_amount: 26436.2 -> 25938.2, instore_count: 51 -> 50
2025-05-23 08:00:40,270 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR, sale_time=2025-05-03
2025-05-23 08:00:40,270 - INFO - 变更字段: recommend_amount: 53839.98 -> 53501.98, amount: 53839 -> 53501, count: 90 -> 89, instore_amount: 51383.0 -> 51045.0, instore_count: 69 -> 68
2025-05-23 08:00:40,270 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR, sale_time=2025-04-30
2025-05-23 08:00:40,270 - INFO - 变更字段: recommend_amount: 30706.46 -> 29250.46, amount: 30706 -> 29250, count: 67 -> 64, instore_amount: 28282.4 -> 26826.4, instore_count: 51 -> 48
2025-05-23 08:00:40,270 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR, sale_time=2025-04-28
2025-05-23 08:00:40,270 - INFO - 变更字段: recommend_amount: 17465.69 -> 16629.69, amount: 17465 -> 16629, count: 50 -> 48, instore_amount: 16736.0 -> 15900.0, instore_count: 43 -> 41
2025-05-23 08:00:40,286 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S, sale_time=2025-05-10
2025-05-23 08:00:40,286 - INFO - 变更字段: recommend_amount: 0.0 -> 8105.0, daily_bill_amount: 0.0 -> 8105.0
2025-05-23 08:00:40,333 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR, sale_time=2025-05-09
2025-05-23 08:00:40,333 - INFO - 变更字段: recommend_amount: 16070.84 -> 15732.84, amount: 16070 -> 15732, count: 49 -> 48, instore_amount: 15034.1 -> 14696.1, instore_count: 37 -> 36
2025-05-23 08:00:40,333 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR, sale_time=2025-05-06
2025-05-23 08:00:40,333 - INFO - 变更字段: recommend_amount: 17684.98 -> 14376.98, amount: 17684 -> 14376, count: 39 -> 38, instore_amount: 16501.0 -> 13193.0, instore_count: 26 -> 25
2025-05-23 08:00:40,348 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S, sale_time=2025-05-17
2025-05-23 08:00:40,348 - INFO - 变更字段: recommend_amount: 0.0 -> 9350.0, daily_bill_amount: 0.0 -> 9350.0
2025-05-23 08:00:40,348 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S, sale_time=2025-05-16
2025-05-23 08:00:40,348 - INFO - 变更字段: recommend_amount: 0.0 -> 8178.0, daily_bill_amount: 0.0 -> 8178.0
2025-05-23 08:00:40,348 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S, sale_time=2025-05-15
2025-05-23 08:00:40,348 - INFO - 变更字段: recommend_amount: 0.0 -> 1772.0, daily_bill_amount: 0.0 -> 1772.0
2025-05-23 08:00:40,348 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S, sale_time=2025-05-14
2025-05-23 08:00:40,348 - INFO - 变更字段: recommend_amount: 0.0 -> 4971.0, daily_bill_amount: 0.0 -> 4971.0
2025-05-23 08:00:40,348 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S, sale_time=2025-05-13
2025-05-23 08:00:40,348 - INFO - 变更字段: recommend_amount: 0.0 -> 12302.0, daily_bill_amount: 0.0 -> 12302.0
2025-05-23 08:00:40,364 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S, sale_time=2025-05-12
2025-05-23 08:00:40,364 - INFO - 变更字段: recommend_amount: 0.0 -> 5790.0, daily_bill_amount: 0.0 -> 5790.0
2025-05-23 08:00:40,364 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S, sale_time=2025-05-11
2025-05-23 08:00:40,364 - INFO - 变更字段: recommend_amount: 0.0 -> 10966.0, daily_bill_amount: 0.0 -> 10966.0
2025-05-23 08:00:40,395 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR, sale_time=2025-05-16
2025-05-23 08:00:40,395 - INFO - 变更字段: recommend_amount: 16559.83 -> 16061.83, amount: 16559 -> 16061, count: 56 -> 55, instore_amount: 14448.0 -> 13950.0, instore_count: 41 -> 40
2025-05-23 08:00:40,395 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR, sale_time=2025-05-12
2025-05-23 08:00:40,395 - INFO - 变更字段: recommend_amount: 11753.34 -> 11015.34, amount: 11753 -> 11015, count: 39 -> 38, instore_amount: 11090.0 -> 10352.0, instore_count: 36 -> 35
2025-05-23 08:00:40,395 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR, sale_time=2025-05-11
2025-05-23 08:00:40,395 - INFO - 变更字段: recommend_amount: 34350.76 -> 32944.76, amount: 34350 -> 32944, count: 94 -> 92, instore_amount: 31333.0 -> 29927.0, instore_count: 71 -> 69
2025-05-23 08:00:40,411 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=F56E8E23D2584556A30D1378611DF4AE, sale_time=2025-05-21
2025-05-23 08:00:40,411 - INFO - 变更字段: recommend_amount: 0.0 -> 3608.7, daily_bill_amount: 0.0 -> 3608.7
2025-05-23 08:00:40,427 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S, sale_time=2025-05-18
2025-05-23 08:00:40,427 - INFO - 变更字段: recommend_amount: 12846.0 -> 14697.5, amount: 12846 -> 14697, count: 334 -> 335, instore_amount: 12208.7 -> 14060.2, instore_count: 314 -> 315
2025-05-23 08:00:40,427 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S, sale_time=2025-05-21
2025-05-23 08:00:40,427 - INFO - 变更字段: recommend_amount: 0.0 -> 3262.0, daily_bill_amount: 0.0 -> 3262.0
2025-05-23 08:00:40,427 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S, sale_time=2025-05-20
2025-05-23 08:00:40,427 - INFO - 变更字段: recommend_amount: 0.0 -> 6507.0, daily_bill_amount: 0.0 -> 6507.0
2025-05-23 08:00:40,427 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S, sale_time=2025-05-19
2025-05-23 08:00:40,427 - INFO - 变更字段: recommend_amount: 0.0 -> 4116.0, daily_bill_amount: 0.0 -> 4116.0
2025-05-23 08:00:40,427 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINSC23H1J3M7Q2OV392410L00148H, sale_time=2025-05-21
2025-05-23 08:00:40,427 - INFO - 变更字段: amount: 1079 -> 1188, count: 3 -> 5, instore_amount: 1079.9 -> 1188.9, instore_count: 3 -> 5
2025-05-23 08:00:40,427 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-05-21
2025-05-23 08:00:40,427 - INFO - 变更字段: count: 131 -> 132, online_count: 103 -> 104
2025-05-23 08:00:40,442 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HO90F90E71NK12I1UUTD5AE7C001O7G, sale_time=2025-05-19
2025-05-23 08:00:40,442 - INFO - 变更字段: recommend_amount: 4814.76 -> 4819.66, amount: 4814 -> 4819, count: 226 -> 227, online_amount: 3639.01 -> 3643.91, online_count: 169 -> 170
2025-05-23 08:00:40,442 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEB9O4F53S0I86N3H2U1VT001F93, sale_time=2025-05-21
2025-05-23 08:00:40,442 - INFO - 变更字段: recommend_amount: 0.0 -> 13537.53, daily_bill_amount: 0.0 -> 13537.53
2025-05-23 08:00:40,442 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDU5QKVU3D0I86N3H2U11T001EB3, sale_time=2025-05-21
2025-05-23 08:00:40,442 - INFO - 变更字段: amount: 3789 -> 3793, count: 277 -> 279, online_amount: 3794.22 -> 3798.12, online_count: 267 -> 269
2025-05-23 08:00:40,458 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-05-21
2025-05-23 08:00:40,458 - INFO - 变更字段: instore_amount: 5224.67 -> 5227.92, instore_count: 331 -> 332, online_amount: 1409.95 -> 1406.7, online_count: 109 -> 108
2025-05-23 08:00:40,458 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-05-20
2025-05-23 08:00:40,458 - INFO - 变更字段: instore_count: 374 -> 375, online_count: 136 -> 135
2025-05-23 08:00:40,458 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1I14PQ9FMDG1FG3MDGKU4P0I8N00178C, sale_time=2025-05-20
2025-05-23 08:00:40,458 - INFO - 变更字段: recommend_amount: 14210.63 -> 14221.03, amount: 14210 -> 14221, count: 584 -> 585, online_amount: 14449.07 -> 14459.47, online_count: 584 -> 585
2025-05-23 08:00:40,458 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1I14PQ9FMDG1FG3MDGKU4P0I8N00178C, sale_time=2025-05-19
2025-05-23 08:00:40,458 - INFO - 变更字段: recommend_amount: 660.51 -> 668.91, amount: 660 -> 668, count: 42 -> 43, online_amount: 673.51 -> 681.91, online_count: 42 -> 43
2025-05-23 08:00:40,458 - INFO - 更新记录成功: shop_id=1ETDLFB9DIMQME7Q2OVD93ISAI00189O, shop_entity_id=1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ, sale_time=2025-05-21
2025-05-23 08:00:40,458 - INFO - 变更字段: recommend_amount: 23195.65 -> 23251.1, daily_bill_amount: 23195.65 -> 23251.1
2025-05-23 08:00:40,458 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8, sale_time=2025-05-21
2025-05-23 08:00:40,458 - INFO - 变更字段: amount: 40137 -> 40160, count: 129 -> 130, instore_amount: 39602.21 -> 39625.1, instore_count: 87 -> 88
2025-05-23 08:00:40,473 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR, sale_time=2025-05-21
2025-05-23 08:00:40,473 - INFO - 变更字段: recommend_amount: 23443.54 -> 22779.54, amount: 23443 -> 22779, count: 62 -> 60, instore_amount: 21954.0 -> 21290.0, instore_count: 49 -> 47
2025-05-23 08:00:40,473 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR, sale_time=2025-05-19
2025-05-23 08:00:40,473 - INFO - 变更字段: recommend_amount: 15461.98 -> 15013.98, amount: 15461 -> 15013, count: 40 -> 39, instore_amount: 14132.0 -> 13684.0, instore_count: 30 -> 29
2025-05-23 08:00:40,473 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR, sale_time=2025-05-18
2025-05-23 08:00:40,473 - INFO - 变更字段: recommend_amount: 27042.34 -> 26672.34, amount: 27042 -> 26672, count: 74 -> 73, instore_amount: 24790.6 -> 24420.6, instore_count: 54 -> 53
2025-05-23 08:00:40,692 - INFO - SQLite数据保存完成，统计信息：
2025-05-23 08:00:40,692 - INFO - - 总记录数: 13060
2025-05-23 08:00:40,692 - INFO - - 成功插入: 207
2025-05-23 08:00:40,692 - INFO - - 成功更新: 45
2025-05-23 08:00:40,692 - INFO - - 无需更新: 12808
2025-05-23 08:00:40,692 - INFO - - 处理失败: 0
2025-05-23 08:00:46,130 - INFO - 数据已保存到Excel文件: logs\数衍平台数据导出_20250523.xlsx
2025-05-23 08:00:46,130 - INFO - 成功获取数衍平台数据，共 13060 条记录
2025-05-23 08:00:46,130 - INFO - 正在更新SQLite月度汇总数据...
2025-05-23 08:00:46,145 - INFO - 月度数据sqllite清空完成
2025-05-23 08:00:46,395 - INFO - 月度汇总数据更新完成，处理了 1192 条汇总记录
2025-05-23 08:00:46,395 - INFO - 成功更新月度汇总数据，共 1192 条记录
2025-05-23 08:00:46,395 - INFO - 正在获取宜搭日销售表单数据...
2025-05-23 08:00:46,395 - INFO - 开始获取宜搭每日表单数据，总时间范围: 2025-03-23 00:00:00 至 2025-05-22 23:59:59
2025-05-23 08:00:46,395 - INFO - 查询分段 1: 2025-03-23 至 2025-03-29
2025-05-23 08:00:46,395 - INFO - 查询日期范围: 2025-03-23 至 2025-03-29，使用分页查询，每页 100 条记录
2025-05-23 08:00:46,395 - INFO - Request Parameters - Page 1:
2025-05-23 08:00:46,395 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:00:46,395 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200395, 1743177600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:00:54,520 - ERROR - API请求失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: DC671E76-29C7-70F2-AB06-158D190B1A0E Response: {'code': 'ServiceUnavailable', 'requestid': 'DC671E76-29C7-70F2-AB06-158D190B1A0E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503}
2025-05-23 08:00:54,520 - ERROR - 服务不可用，将等待后重试
2025-05-23 08:00:54,520 - ERROR - 获取第 1 页数据时出错: 服务不可用: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: DC671E76-29C7-70F2-AB06-158D190B1A0E Response: {'code': 'ServiceUnavailable', 'requestid': 'DC671E76-29C7-70F2-AB06-158D190B1A0E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503}
2025-05-23 08:00:54,520 - WARNING - 服务暂时不可用，等待 6 秒后重试...
2025-05-23 08:01:00,536 - WARNING - 服务暂时不可用，将等待更长时间: 10秒
2025-05-23 08:01:00,536 - WARNING - 获取表单数据失败 (尝试 1/3): 服务不可用: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: DC671E76-29C7-70F2-AB06-158D190B1A0E Response: {'code': 'ServiceUnavailable', 'requestid': 'DC671E76-29C7-70F2-AB06-158D190B1A0E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503}，将在 10 秒后重试...
2025-05-23 08:01:10,551 - INFO - Request Parameters - Page 1:
2025-05-23 08:01:10,551 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:10,551 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200395, 1743177600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:11,364 - INFO - API请求耗时: 812ms
2025-05-23 08:01:11,364 - INFO - Response - Page 1
2025-05-23 08:01:11,364 - INFO - 第 1 页获取到 100 条记录
2025-05-23 08:01:11,864 - INFO - Request Parameters - Page 2:
2025-05-23 08:01:11,864 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:11,864 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200395, 1743177600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:12,629 - INFO - API请求耗时: 766ms
2025-05-23 08:01:12,629 - INFO - Response - Page 2
2025-05-23 08:01:12,629 - INFO - 第 2 页获取到 100 条记录
2025-05-23 08:01:13,129 - INFO - Request Parameters - Page 3:
2025-05-23 08:01:13,129 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:13,129 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200395, 1743177600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:13,833 - INFO - API请求耗时: 703ms
2025-05-23 08:01:13,833 - INFO - Response - Page 3
2025-05-23 08:01:13,833 - INFO - 第 3 页获取到 100 条记录
2025-05-23 08:01:14,333 - INFO - Request Parameters - Page 4:
2025-05-23 08:01:14,333 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:14,333 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200395, 1743177600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:15,004 - INFO - API请求耗时: 672ms
2025-05-23 08:01:15,004 - INFO - Response - Page 4
2025-05-23 08:01:15,004 - INFO - 第 4 页获取到 100 条记录
2025-05-23 08:01:15,520 - INFO - Request Parameters - Page 5:
2025-05-23 08:01:15,520 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:15,520 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200395, 1743177600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:16,223 - INFO - API请求耗时: 703ms
2025-05-23 08:01:16,223 - INFO - Response - Page 5
2025-05-23 08:01:16,223 - INFO - 第 5 页获取到 100 条记录
2025-05-23 08:01:16,739 - INFO - Request Parameters - Page 6:
2025-05-23 08:01:16,739 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:16,739 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200395, 1743177600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:17,473 - INFO - API请求耗时: 734ms
2025-05-23 08:01:17,473 - INFO - Response - Page 6
2025-05-23 08:01:17,473 - INFO - 第 6 页获取到 100 条记录
2025-05-23 08:01:17,973 - INFO - Request Parameters - Page 7:
2025-05-23 08:01:17,973 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:17,973 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200395, 1743177600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:18,676 - INFO - API请求耗时: 703ms
2025-05-23 08:01:18,676 - INFO - Response - Page 7
2025-05-23 08:01:18,676 - INFO - 第 7 页获取到 100 条记录
2025-05-23 08:01:19,192 - INFO - Request Parameters - Page 8:
2025-05-23 08:01:19,192 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:19,192 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200395, 1743177600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:19,973 - INFO - API请求耗时: 781ms
2025-05-23 08:01:19,973 - INFO - Response - Page 8
2025-05-23 08:01:19,973 - INFO - 第 8 页获取到 100 条记录
2025-05-23 08:01:20,473 - INFO - Request Parameters - Page 9:
2025-05-23 08:01:20,473 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:20,473 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200395, 1743177600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:21,364 - INFO - API请求耗时: 891ms
2025-05-23 08:01:21,364 - INFO - Response - Page 9
2025-05-23 08:01:21,364 - INFO - 第 9 页获取到 100 条记录
2025-05-23 08:01:21,864 - INFO - Request Parameters - Page 10:
2025-05-23 08:01:21,864 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:21,864 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200395, 1743177600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:22,629 - INFO - API请求耗时: 766ms
2025-05-23 08:01:22,629 - INFO - Response - Page 10
2025-05-23 08:01:22,629 - INFO - 第 10 页获取到 100 条记录
2025-05-23 08:01:23,129 - INFO - Request Parameters - Page 11:
2025-05-23 08:01:23,129 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:23,129 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200395, 1743177600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:23,864 - INFO - API请求耗时: 734ms
2025-05-23 08:01:23,864 - INFO - Response - Page 11
2025-05-23 08:01:23,864 - INFO - 第 11 页获取到 100 条记录
2025-05-23 08:01:24,364 - INFO - Request Parameters - Page 12:
2025-05-23 08:01:24,364 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:24,364 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200395, 1743177600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:25,004 - INFO - API请求耗时: 641ms
2025-05-23 08:01:25,004 - INFO - Response - Page 12
2025-05-23 08:01:25,020 - INFO - 第 12 页获取到 100 条记录
2025-05-23 08:01:25,536 - INFO - Request Parameters - Page 13:
2025-05-23 08:01:25,536 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:25,536 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200395, 1743177600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:26,176 - INFO - API请求耗时: 641ms
2025-05-23 08:01:26,176 - INFO - Response - Page 13
2025-05-23 08:01:26,192 - INFO - 第 13 页获取到 100 条记录
2025-05-23 08:01:26,692 - INFO - Request Parameters - Page 14:
2025-05-23 08:01:26,692 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:26,692 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200395, 1743177600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:27,348 - INFO - API请求耗时: 656ms
2025-05-23 08:01:27,348 - INFO - Response - Page 14
2025-05-23 08:01:27,348 - INFO - 第 14 页获取到 100 条记录
2025-05-23 08:01:27,848 - INFO - Request Parameters - Page 15:
2025-05-23 08:01:27,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:27,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200395, 1743177600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:28,582 - INFO - API请求耗时: 734ms
2025-05-23 08:01:28,582 - INFO - Response - Page 15
2025-05-23 08:01:28,582 - INFO - 第 15 页获取到 100 条记录
2025-05-23 08:01:29,082 - INFO - Request Parameters - Page 16:
2025-05-23 08:01:29,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:29,082 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200395, 1743177600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:29,754 - INFO - API请求耗时: 672ms
2025-05-23 08:01:29,754 - INFO - Response - Page 16
2025-05-23 08:01:29,754 - INFO - 第 16 页获取到 100 条记录
2025-05-23 08:01:30,270 - INFO - Request Parameters - Page 17:
2025-05-23 08:01:30,270 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:30,270 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200395, 1743177600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:30,989 - INFO - API请求耗时: 719ms
2025-05-23 08:01:30,989 - INFO - Response - Page 17
2025-05-23 08:01:30,989 - INFO - 第 17 页获取到 100 条记录
2025-05-23 08:01:31,504 - INFO - Request Parameters - Page 18:
2025-05-23 08:01:31,504 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:31,504 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200395, 1743177600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:32,239 - INFO - API请求耗时: 734ms
2025-05-23 08:01:32,239 - INFO - Response - Page 18
2025-05-23 08:01:32,239 - INFO - 第 18 页获取到 100 条记录
2025-05-23 08:01:32,754 - INFO - Request Parameters - Page 19:
2025-05-23 08:01:32,754 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:32,754 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200395, 1743177600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:33,489 - INFO - API请求耗时: 734ms
2025-05-23 08:01:33,489 - INFO - Response - Page 19
2025-05-23 08:01:33,489 - INFO - 第 19 页获取到 100 条记录
2025-05-23 08:01:34,004 - INFO - Request Parameters - Page 20:
2025-05-23 08:01:34,004 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:34,004 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200395, 1743177600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:34,692 - INFO - API请求耗时: 688ms
2025-05-23 08:01:34,692 - INFO - Response - Page 20
2025-05-23 08:01:34,692 - INFO - 第 20 页获取到 100 条记录
2025-05-23 08:01:35,207 - INFO - Request Parameters - Page 21:
2025-05-23 08:01:35,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:35,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200395, 1743177600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:35,942 - INFO - API请求耗时: 734ms
2025-05-23 08:01:35,942 - INFO - Response - Page 21
2025-05-23 08:01:35,942 - INFO - 第 21 页获取到 100 条记录
2025-05-23 08:01:36,457 - INFO - Request Parameters - Page 22:
2025-05-23 08:01:36,457 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:36,457 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200395, 1743177600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:37,176 - INFO - API请求耗时: 719ms
2025-05-23 08:01:37,176 - INFO - Response - Page 22
2025-05-23 08:01:37,176 - INFO - 第 22 页获取到 100 条记录
2025-05-23 08:01:37,692 - INFO - Request Parameters - Page 23:
2025-05-23 08:01:37,692 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:37,692 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200395, 1743177600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:38,457 - INFO - API请求耗时: 766ms
2025-05-23 08:01:38,457 - INFO - Response - Page 23
2025-05-23 08:01:38,457 - INFO - 第 23 页获取到 100 条记录
2025-05-23 08:01:38,973 - INFO - Request Parameters - Page 24:
2025-05-23 08:01:38,973 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:38,973 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200395, 1743177600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:39,661 - INFO - API请求耗时: 687ms
2025-05-23 08:01:39,661 - INFO - Response - Page 24
2025-05-23 08:01:39,661 - INFO - 第 24 页获取到 100 条记录
2025-05-23 08:01:40,176 - INFO - Request Parameters - Page 25:
2025-05-23 08:01:40,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:40,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200395, 1743177600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:41,004 - INFO - API请求耗时: 828ms
2025-05-23 08:01:41,004 - INFO - Response - Page 25
2025-05-23 08:01:41,004 - INFO - 第 25 页获取到 100 条记录
2025-05-23 08:01:41,520 - INFO - Request Parameters - Page 26:
2025-05-23 08:01:41,520 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:41,520 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200395, 1743177600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:42,192 - INFO - API请求耗时: 672ms
2025-05-23 08:01:42,192 - INFO - Response - Page 26
2025-05-23 08:01:42,192 - INFO - 第 26 页获取到 100 条记录
2025-05-23 08:01:42,707 - INFO - Request Parameters - Page 27:
2025-05-23 08:01:42,707 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:42,707 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200395, 1743177600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:43,473 - INFO - API请求耗时: 766ms
2025-05-23 08:01:43,473 - INFO - Response - Page 27
2025-05-23 08:01:43,473 - INFO - 第 27 页获取到 100 条记录
2025-05-23 08:01:43,989 - INFO - Request Parameters - Page 28:
2025-05-23 08:01:43,989 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:43,989 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200395, 1743177600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:44,676 - INFO - API请求耗时: 688ms
2025-05-23 08:01:44,676 - INFO - Response - Page 28
2025-05-23 08:01:44,676 - INFO - 第 28 页获取到 100 条记录
2025-05-23 08:01:45,192 - INFO - Request Parameters - Page 29:
2025-05-23 08:01:45,192 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:45,192 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742659200395, 1743177600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:45,832 - INFO - API请求耗时: 641ms
2025-05-23 08:01:45,832 - INFO - Response - Page 29
2025-05-23 08:01:45,832 - INFO - 第 29 页获取到 99 条记录
2025-05-23 08:01:45,832 - INFO - 查询完成，共获取到 2899 条记录
2025-05-23 08:01:45,832 - INFO - 分段 1 查询成功，获取到 2899 条记录
2025-05-23 08:01:46,848 - INFO - 查询分段 2: 2025-03-30 至 2025-04-05
2025-05-23 08:01:46,848 - INFO - 查询日期范围: 2025-03-30 至 2025-04-05，使用分页查询，每页 100 条记录
2025-05-23 08:01:46,848 - INFO - Request Parameters - Page 1:
2025-05-23 08:01:46,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:46,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000395, 1743782400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:47,504 - INFO - API请求耗时: 656ms
2025-05-23 08:01:47,504 - INFO - Response - Page 1
2025-05-23 08:01:47,504 - INFO - 第 1 页获取到 100 条记录
2025-05-23 08:01:48,004 - INFO - Request Parameters - Page 2:
2025-05-23 08:01:48,004 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:48,004 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000395, 1743782400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:48,754 - INFO - API请求耗时: 750ms
2025-05-23 08:01:48,754 - INFO - Response - Page 2
2025-05-23 08:01:48,754 - INFO - 第 2 页获取到 100 条记录
2025-05-23 08:01:49,254 - INFO - Request Parameters - Page 3:
2025-05-23 08:01:49,254 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:49,254 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000395, 1743782400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:49,957 - INFO - API请求耗时: 703ms
2025-05-23 08:01:49,957 - INFO - Response - Page 3
2025-05-23 08:01:49,957 - INFO - 第 3 页获取到 100 条记录
2025-05-23 08:01:50,473 - INFO - Request Parameters - Page 4:
2025-05-23 08:01:50,473 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:50,473 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000395, 1743782400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:51,082 - INFO - API请求耗时: 609ms
2025-05-23 08:01:51,082 - INFO - Response - Page 4
2025-05-23 08:01:51,082 - INFO - 第 4 页获取到 100 条记录
2025-05-23 08:01:51,598 - INFO - Request Parameters - Page 5:
2025-05-23 08:01:51,598 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:51,598 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000395, 1743782400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:52,332 - INFO - API请求耗时: 734ms
2025-05-23 08:01:52,332 - INFO - Response - Page 5
2025-05-23 08:01:52,332 - INFO - 第 5 页获取到 100 条记录
2025-05-23 08:01:52,848 - INFO - Request Parameters - Page 6:
2025-05-23 08:01:52,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:52,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000395, 1743782400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:53,567 - INFO - API请求耗时: 719ms
2025-05-23 08:01:53,567 - INFO - Response - Page 6
2025-05-23 08:01:53,567 - INFO - 第 6 页获取到 100 条记录
2025-05-23 08:01:54,067 - INFO - Request Parameters - Page 7:
2025-05-23 08:01:54,067 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:54,067 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000395, 1743782400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:55,020 - INFO - API请求耗时: 953ms
2025-05-23 08:01:55,020 - INFO - Response - Page 7
2025-05-23 08:01:55,035 - INFO - 第 7 页获取到 100 条记录
2025-05-23 08:01:55,535 - INFO - Request Parameters - Page 8:
2025-05-23 08:01:55,535 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:55,535 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000395, 1743782400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:56,223 - INFO - API请求耗时: 687ms
2025-05-23 08:01:56,223 - INFO - Response - Page 8
2025-05-23 08:01:56,223 - INFO - 第 8 页获取到 100 条记录
2025-05-23 08:01:56,723 - INFO - Request Parameters - Page 9:
2025-05-23 08:01:56,723 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:56,723 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000395, 1743782400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:57,379 - INFO - API请求耗时: 656ms
2025-05-23 08:01:57,379 - INFO - Response - Page 9
2025-05-23 08:01:57,379 - INFO - 第 9 页获取到 100 条记录
2025-05-23 08:01:57,879 - INFO - Request Parameters - Page 10:
2025-05-23 08:01:57,879 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:57,879 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000395, 1743782400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:58,567 - INFO - API请求耗时: 687ms
2025-05-23 08:01:58,567 - INFO - Response - Page 10
2025-05-23 08:01:58,567 - INFO - 第 10 页获取到 100 条记录
2025-05-23 08:01:59,082 - INFO - Request Parameters - Page 11:
2025-05-23 08:01:59,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:01:59,082 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000395, 1743782400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:01:59,770 - INFO - API请求耗时: 688ms
2025-05-23 08:01:59,770 - INFO - Response - Page 11
2025-05-23 08:01:59,770 - INFO - 第 11 页获取到 100 条记录
2025-05-23 08:02:00,285 - INFO - Request Parameters - Page 12:
2025-05-23 08:02:00,285 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:00,285 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000395, 1743782400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:00,989 - INFO - API请求耗时: 703ms
2025-05-23 08:02:00,989 - INFO - Response - Page 12
2025-05-23 08:02:00,989 - INFO - 第 12 页获取到 100 条记录
2025-05-23 08:02:01,504 - INFO - Request Parameters - Page 13:
2025-05-23 08:02:01,504 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:01,504 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000395, 1743782400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:02,160 - INFO - API请求耗时: 656ms
2025-05-23 08:02:02,160 - INFO - Response - Page 13
2025-05-23 08:02:02,160 - INFO - 第 13 页获取到 100 条记录
2025-05-23 08:02:02,676 - INFO - Request Parameters - Page 14:
2025-05-23 08:02:02,676 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:02,676 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000395, 1743782400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:03,426 - INFO - API请求耗时: 750ms
2025-05-23 08:02:03,426 - INFO - Response - Page 14
2025-05-23 08:02:03,426 - INFO - 第 14 页获取到 100 条记录
2025-05-23 08:02:03,926 - INFO - Request Parameters - Page 15:
2025-05-23 08:02:03,926 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:03,926 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000395, 1743782400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:04,598 - INFO - API请求耗时: 672ms
2025-05-23 08:02:04,598 - INFO - Response - Page 15
2025-05-23 08:02:04,598 - INFO - 第 15 页获取到 100 条记录
2025-05-23 08:02:05,098 - INFO - Request Parameters - Page 16:
2025-05-23 08:02:05,098 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:05,098 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000395, 1743782400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:05,817 - INFO - API请求耗时: 719ms
2025-05-23 08:02:05,817 - INFO - Response - Page 16
2025-05-23 08:02:05,817 - INFO - 第 16 页获取到 100 条记录
2025-05-23 08:02:06,332 - INFO - Request Parameters - Page 17:
2025-05-23 08:02:06,332 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:06,332 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000395, 1743782400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:06,988 - INFO - API请求耗时: 656ms
2025-05-23 08:02:06,988 - INFO - Response - Page 17
2025-05-23 08:02:06,988 - INFO - 第 17 页获取到 100 条记录
2025-05-23 08:02:07,488 - INFO - Request Parameters - Page 18:
2025-05-23 08:02:07,488 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:07,488 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000395, 1743782400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:08,176 - INFO - API请求耗时: 688ms
2025-05-23 08:02:08,176 - INFO - Response - Page 18
2025-05-23 08:02:08,176 - INFO - 第 18 页获取到 100 条记录
2025-05-23 08:02:08,692 - INFO - Request Parameters - Page 19:
2025-05-23 08:02:08,692 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:08,692 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000395, 1743782400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:09,395 - INFO - API请求耗时: 703ms
2025-05-23 08:02:09,395 - INFO - Response - Page 19
2025-05-23 08:02:09,395 - INFO - 第 19 页获取到 100 条记录
2025-05-23 08:02:09,895 - INFO - Request Parameters - Page 20:
2025-05-23 08:02:09,895 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:09,895 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000395, 1743782400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:10,660 - INFO - API请求耗时: 766ms
2025-05-23 08:02:10,660 - INFO - Response - Page 20
2025-05-23 08:02:10,660 - INFO - 第 20 页获取到 100 条记录
2025-05-23 08:02:11,176 - INFO - Request Parameters - Page 21:
2025-05-23 08:02:11,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:11,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000395, 1743782400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:11,863 - INFO - API请求耗时: 688ms
2025-05-23 08:02:11,863 - INFO - Response - Page 21
2025-05-23 08:02:11,863 - INFO - 第 21 页获取到 100 条记录
2025-05-23 08:02:12,363 - INFO - Request Parameters - Page 22:
2025-05-23 08:02:12,363 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:12,363 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000395, 1743782400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:13,098 - INFO - API请求耗时: 734ms
2025-05-23 08:02:13,098 - INFO - Response - Page 22
2025-05-23 08:02:13,098 - INFO - 第 22 页获取到 100 条记录
2025-05-23 08:02:13,613 - INFO - Request Parameters - Page 23:
2025-05-23 08:02:13,613 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:13,613 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000395, 1743782400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:14,285 - INFO - API请求耗时: 672ms
2025-05-23 08:02:14,285 - INFO - Response - Page 23
2025-05-23 08:02:14,285 - INFO - 第 23 页获取到 100 条记录
2025-05-23 08:02:14,785 - INFO - Request Parameters - Page 24:
2025-05-23 08:02:14,785 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:14,785 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000395, 1743782400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:15,863 - INFO - API请求耗时: 1078ms
2025-05-23 08:02:15,863 - INFO - Response - Page 24
2025-05-23 08:02:15,863 - INFO - 第 24 页获取到 100 条记录
2025-05-23 08:02:16,379 - INFO - Request Parameters - Page 25:
2025-05-23 08:02:16,379 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:16,379 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000395, 1743782400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:17,020 - INFO - API请求耗时: 641ms
2025-05-23 08:02:17,020 - INFO - Response - Page 25
2025-05-23 08:02:17,020 - INFO - 第 25 页获取到 100 条记录
2025-05-23 08:02:17,520 - INFO - Request Parameters - Page 26:
2025-05-23 08:02:17,520 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:17,520 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000395, 1743782400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:18,270 - INFO - API请求耗时: 750ms
2025-05-23 08:02:18,270 - INFO - Response - Page 26
2025-05-23 08:02:18,270 - INFO - 第 26 页获取到 100 条记录
2025-05-23 08:02:18,770 - INFO - Request Parameters - Page 27:
2025-05-23 08:02:18,770 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:18,770 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000395, 1743782400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:19,488 - INFO - API请求耗时: 719ms
2025-05-23 08:02:19,488 - INFO - Response - Page 27
2025-05-23 08:02:19,488 - INFO - 第 27 页获取到 100 条记录
2025-05-23 08:02:19,988 - INFO - Request Parameters - Page 28:
2025-05-23 08:02:19,988 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:19,988 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000395, 1743782400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:20,660 - INFO - API请求耗时: 672ms
2025-05-23 08:02:20,660 - INFO - Response - Page 28
2025-05-23 08:02:20,660 - INFO - 第 28 页获取到 100 条记录
2025-05-23 08:02:21,176 - INFO - Request Parameters - Page 29:
2025-05-23 08:02:21,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:21,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000395, 1743782400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:21,582 - INFO - API请求耗时: 406ms
2025-05-23 08:02:21,598 - INFO - Response - Page 29
2025-05-23 08:02:21,598 - INFO - 第 29 页获取到 16 条记录
2025-05-23 08:02:21,598 - INFO - 查询完成，共获取到 2816 条记录
2025-05-23 08:02:21,598 - INFO - 分段 2 查询成功，获取到 2816 条记录
2025-05-23 08:02:22,598 - INFO - 查询分段 3: 2025-04-06 至 2025-04-12
2025-05-23 08:02:22,598 - INFO - 查询日期范围: 2025-04-06 至 2025-04-12，使用分页查询，每页 100 条记录
2025-05-23 08:02:22,598 - INFO - Request Parameters - Page 1:
2025-05-23 08:02:22,598 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:22,598 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800395, 1744387200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:23,270 - INFO - API请求耗时: 672ms
2025-05-23 08:02:23,270 - INFO - Response - Page 1
2025-05-23 08:02:23,270 - INFO - 第 1 页获取到 100 条记录
2025-05-23 08:02:23,785 - INFO - Request Parameters - Page 2:
2025-05-23 08:02:23,785 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:23,785 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800395, 1744387200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:24,488 - INFO - API请求耗时: 703ms
2025-05-23 08:02:24,488 - INFO - Response - Page 2
2025-05-23 08:02:24,488 - INFO - 第 2 页获取到 100 条记录
2025-05-23 08:02:25,004 - INFO - Request Parameters - Page 3:
2025-05-23 08:02:25,004 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:25,004 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800395, 1744387200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:25,676 - INFO - API请求耗时: 672ms
2025-05-23 08:02:25,676 - INFO - Response - Page 3
2025-05-23 08:02:25,676 - INFO - 第 3 页获取到 100 条记录
2025-05-23 08:02:26,192 - INFO - Request Parameters - Page 4:
2025-05-23 08:02:26,192 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:26,192 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800395, 1744387200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:26,848 - INFO - API请求耗时: 656ms
2025-05-23 08:02:26,848 - INFO - Response - Page 4
2025-05-23 08:02:26,863 - INFO - 第 4 页获取到 100 条记录
2025-05-23 08:02:27,363 - INFO - Request Parameters - Page 5:
2025-05-23 08:02:27,363 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:27,363 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800395, 1744387200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:28,082 - INFO - API请求耗时: 719ms
2025-05-23 08:02:28,082 - INFO - Response - Page 5
2025-05-23 08:02:28,082 - INFO - 第 5 页获取到 100 条记录
2025-05-23 08:02:28,582 - INFO - Request Parameters - Page 6:
2025-05-23 08:02:28,582 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:28,582 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800395, 1744387200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:29,457 - INFO - API请求耗时: 875ms
2025-05-23 08:02:29,457 - INFO - Response - Page 6
2025-05-23 08:02:29,457 - INFO - 第 6 页获取到 100 条记录
2025-05-23 08:02:29,957 - INFO - Request Parameters - Page 7:
2025-05-23 08:02:29,957 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:29,957 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800395, 1744387200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:30,691 - INFO - API请求耗时: 734ms
2025-05-23 08:02:30,691 - INFO - Response - Page 7
2025-05-23 08:02:30,691 - INFO - 第 7 页获取到 100 条记录
2025-05-23 08:02:31,191 - INFO - Request Parameters - Page 8:
2025-05-23 08:02:31,191 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:31,191 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800395, 1744387200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:31,895 - INFO - API请求耗时: 703ms
2025-05-23 08:02:31,895 - INFO - Response - Page 8
2025-05-23 08:02:31,895 - INFO - 第 8 页获取到 100 条记录
2025-05-23 08:02:32,395 - INFO - Request Parameters - Page 9:
2025-05-23 08:02:32,395 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:32,395 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800395, 1744387200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:33,082 - INFO - API请求耗时: 687ms
2025-05-23 08:02:33,082 - INFO - Response - Page 9
2025-05-23 08:02:33,082 - INFO - 第 9 页获取到 100 条记录
2025-05-23 08:02:33,598 - INFO - Request Parameters - Page 10:
2025-05-23 08:02:33,598 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:33,598 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800395, 1744387200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:34,332 - INFO - API请求耗时: 734ms
2025-05-23 08:02:34,332 - INFO - Response - Page 10
2025-05-23 08:02:34,332 - INFO - 第 10 页获取到 100 条记录
2025-05-23 08:02:34,848 - INFO - Request Parameters - Page 11:
2025-05-23 08:02:34,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:34,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800395, 1744387200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:35,520 - INFO - API请求耗时: 672ms
2025-05-23 08:02:35,520 - INFO - Response - Page 11
2025-05-23 08:02:35,520 - INFO - 第 11 页获取到 100 条记录
2025-05-23 08:02:36,020 - INFO - Request Parameters - Page 12:
2025-05-23 08:02:36,020 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:36,020 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800395, 1744387200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:36,676 - INFO - API请求耗时: 656ms
2025-05-23 08:02:36,676 - INFO - Response - Page 12
2025-05-23 08:02:36,676 - INFO - 第 12 页获取到 100 条记录
2025-05-23 08:02:37,191 - INFO - Request Parameters - Page 13:
2025-05-23 08:02:37,191 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:37,191 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800395, 1744387200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:37,879 - INFO - API请求耗时: 687ms
2025-05-23 08:02:37,879 - INFO - Response - Page 13
2025-05-23 08:02:37,879 - INFO - 第 13 页获取到 100 条记录
2025-05-23 08:02:38,379 - INFO - Request Parameters - Page 14:
2025-05-23 08:02:38,379 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:38,379 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800395, 1744387200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:39,098 - INFO - API请求耗时: 719ms
2025-05-23 08:02:39,098 - INFO - Response - Page 14
2025-05-23 08:02:39,098 - INFO - 第 14 页获取到 100 条记录
2025-05-23 08:02:39,613 - INFO - Request Parameters - Page 15:
2025-05-23 08:02:39,613 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:39,613 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800395, 1744387200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:40,551 - INFO - API请求耗时: 938ms
2025-05-23 08:02:40,551 - INFO - Response - Page 15
2025-05-23 08:02:40,551 - INFO - 第 15 页获取到 100 条记录
2025-05-23 08:02:41,066 - INFO - Request Parameters - Page 16:
2025-05-23 08:02:41,066 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:41,066 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800395, 1744387200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:41,723 - INFO - API请求耗时: 656ms
2025-05-23 08:02:41,723 - INFO - Response - Page 16
2025-05-23 08:02:41,723 - INFO - 第 16 页获取到 100 条记录
2025-05-23 08:02:42,223 - INFO - Request Parameters - Page 17:
2025-05-23 08:02:42,223 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:42,223 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800395, 1744387200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:42,988 - INFO - API请求耗时: 766ms
2025-05-23 08:02:42,988 - INFO - Response - Page 17
2025-05-23 08:02:42,988 - INFO - 第 17 页获取到 100 条记录
2025-05-23 08:02:43,504 - INFO - Request Parameters - Page 18:
2025-05-23 08:02:43,504 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:43,504 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800395, 1744387200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:44,113 - INFO - API请求耗时: 609ms
2025-05-23 08:02:44,113 - INFO - Response - Page 18
2025-05-23 08:02:44,113 - INFO - 第 18 页获取到 100 条记录
2025-05-23 08:02:44,629 - INFO - Request Parameters - Page 19:
2025-05-23 08:02:44,629 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:44,629 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800395, 1744387200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:45,301 - INFO - API请求耗时: 672ms
2025-05-23 08:02:45,301 - INFO - Response - Page 19
2025-05-23 08:02:45,301 - INFO - 第 19 页获取到 100 条记录
2025-05-23 08:02:45,801 - INFO - Request Parameters - Page 20:
2025-05-23 08:02:45,801 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:45,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800395, 1744387200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:46,504 - INFO - API请求耗时: 703ms
2025-05-23 08:02:46,504 - INFO - Response - Page 20
2025-05-23 08:02:46,504 - INFO - 第 20 页获取到 100 条记录
2025-05-23 08:02:47,019 - INFO - Request Parameters - Page 21:
2025-05-23 08:02:47,019 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:47,019 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800395, 1744387200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:47,894 - INFO - API请求耗时: 875ms
2025-05-23 08:02:47,894 - INFO - Response - Page 21
2025-05-23 08:02:47,894 - INFO - 第 21 页获取到 100 条记录
2025-05-23 08:02:48,410 - INFO - Request Parameters - Page 22:
2025-05-23 08:02:48,410 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:48,410 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800395, 1744387200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:49,348 - INFO - API请求耗时: 938ms
2025-05-23 08:02:49,348 - INFO - Response - Page 22
2025-05-23 08:02:49,348 - INFO - 第 22 页获取到 100 条记录
2025-05-23 08:02:49,863 - INFO - Request Parameters - Page 23:
2025-05-23 08:02:49,863 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:49,863 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800395, 1744387200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:50,504 - INFO - API请求耗时: 641ms
2025-05-23 08:02:50,504 - INFO - Response - Page 23
2025-05-23 08:02:50,504 - INFO - 第 23 页获取到 100 条记录
2025-05-23 08:02:51,019 - INFO - Request Parameters - Page 24:
2025-05-23 08:02:51,019 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:51,019 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800395, 1744387200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:51,676 - INFO - API请求耗时: 656ms
2025-05-23 08:02:51,676 - INFO - Response - Page 24
2025-05-23 08:02:51,676 - INFO - 第 24 页获取到 100 条记录
2025-05-23 08:02:52,176 - INFO - Request Parameters - Page 25:
2025-05-23 08:02:52,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:52,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800395, 1744387200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:52,848 - INFO - API请求耗时: 672ms
2025-05-23 08:02:52,848 - INFO - Response - Page 25
2025-05-23 08:02:52,848 - INFO - 第 25 页获取到 100 条记录
2025-05-23 08:02:53,363 - INFO - Request Parameters - Page 26:
2025-05-23 08:02:53,363 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:53,363 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800395, 1744387200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:54,051 - INFO - API请求耗时: 687ms
2025-05-23 08:02:54,051 - INFO - Response - Page 26
2025-05-23 08:02:54,051 - INFO - 第 26 页获取到 100 条记录
2025-05-23 08:02:54,566 - INFO - Request Parameters - Page 27:
2025-05-23 08:02:54,566 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:54,566 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800395, 1744387200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:55,269 - INFO - API请求耗时: 703ms
2025-05-23 08:02:55,269 - INFO - Response - Page 27
2025-05-23 08:02:55,269 - INFO - 第 27 页获取到 100 条记录
2025-05-23 08:02:55,785 - INFO - Request Parameters - Page 28:
2025-05-23 08:02:55,785 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:55,785 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743868800395, 1744387200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:56,457 - INFO - API请求耗时: 672ms
2025-05-23 08:02:56,457 - INFO - Response - Page 28
2025-05-23 08:02:56,457 - INFO - 第 28 页获取到 83 条记录
2025-05-23 08:02:56,457 - INFO - 查询完成，共获取到 2783 条记录
2025-05-23 08:02:56,457 - INFO - 分段 3 查询成功，获取到 2783 条记录
2025-05-23 08:02:57,473 - INFO - 查询分段 4: 2025-04-13 至 2025-04-19
2025-05-23 08:02:57,473 - INFO - 查询日期范围: 2025-04-13 至 2025-04-19，使用分页查询，每页 100 条记录
2025-05-23 08:02:57,473 - INFO - Request Parameters - Page 1:
2025-05-23 08:02:57,473 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:57,473 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600395, 1744992000395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:58,160 - INFO - API请求耗时: 687ms
2025-05-23 08:02:58,160 - INFO - Response - Page 1
2025-05-23 08:02:58,160 - INFO - 第 1 页获取到 100 条记录
2025-05-23 08:02:58,660 - INFO - Request Parameters - Page 2:
2025-05-23 08:02:58,660 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:58,660 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600395, 1744992000395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:02:59,441 - INFO - API请求耗时: 781ms
2025-05-23 08:02:59,441 - INFO - Response - Page 2
2025-05-23 08:02:59,441 - INFO - 第 2 页获取到 100 条记录
2025-05-23 08:02:59,941 - INFO - Request Parameters - Page 3:
2025-05-23 08:02:59,941 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:02:59,941 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600395, 1744992000395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:00,660 - INFO - API请求耗时: 719ms
2025-05-23 08:03:00,660 - INFO - Response - Page 3
2025-05-23 08:03:00,660 - INFO - 第 3 页获取到 100 条记录
2025-05-23 08:03:01,160 - INFO - Request Parameters - Page 4:
2025-05-23 08:03:01,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:01,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600395, 1744992000395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:01,848 - INFO - API请求耗时: 687ms
2025-05-23 08:03:01,848 - INFO - Response - Page 4
2025-05-23 08:03:01,848 - INFO - 第 4 页获取到 100 条记录
2025-05-23 08:03:02,348 - INFO - Request Parameters - Page 5:
2025-05-23 08:03:02,348 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:02,348 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600395, 1744992000395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:03,019 - INFO - API请求耗时: 672ms
2025-05-23 08:03:03,019 - INFO - Response - Page 5
2025-05-23 08:03:03,019 - INFO - 第 5 页获取到 100 条记录
2025-05-23 08:03:03,519 - INFO - Request Parameters - Page 6:
2025-05-23 08:03:03,519 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:03,519 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600395, 1744992000395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:04,176 - INFO - API请求耗时: 656ms
2025-05-23 08:03:04,176 - INFO - Response - Page 6
2025-05-23 08:03:04,176 - INFO - 第 6 页获取到 100 条记录
2025-05-23 08:03:04,691 - INFO - Request Parameters - Page 7:
2025-05-23 08:03:04,691 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:04,691 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600395, 1744992000395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:05,332 - INFO - API请求耗时: 641ms
2025-05-23 08:03:05,332 - INFO - Response - Page 7
2025-05-23 08:03:05,332 - INFO - 第 7 页获取到 100 条记录
2025-05-23 08:03:05,847 - INFO - Request Parameters - Page 8:
2025-05-23 08:03:05,847 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:05,847 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600395, 1744992000395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:06,582 - INFO - API请求耗时: 734ms
2025-05-23 08:03:06,582 - INFO - Response - Page 8
2025-05-23 08:03:06,582 - INFO - 第 8 页获取到 100 条记录
2025-05-23 08:03:07,082 - INFO - Request Parameters - Page 9:
2025-05-23 08:03:07,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:07,082 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600395, 1744992000395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:07,707 - INFO - API请求耗时: 625ms
2025-05-23 08:03:07,707 - INFO - Response - Page 9
2025-05-23 08:03:07,707 - INFO - 第 9 页获取到 100 条记录
2025-05-23 08:03:08,207 - INFO - Request Parameters - Page 10:
2025-05-23 08:03:08,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:08,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600395, 1744992000395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:08,941 - INFO - API请求耗时: 734ms
2025-05-23 08:03:08,941 - INFO - Response - Page 10
2025-05-23 08:03:08,941 - INFO - 第 10 页获取到 100 条记录
2025-05-23 08:03:09,441 - INFO - Request Parameters - Page 11:
2025-05-23 08:03:09,441 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:09,441 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600395, 1744992000395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:10,082 - INFO - API请求耗时: 641ms
2025-05-23 08:03:10,082 - INFO - Response - Page 11
2025-05-23 08:03:10,097 - INFO - 第 11 页获取到 100 条记录
2025-05-23 08:03:10,597 - INFO - Request Parameters - Page 12:
2025-05-23 08:03:10,597 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:10,597 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600395, 1744992000395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:11,285 - INFO - API请求耗时: 687ms
2025-05-23 08:03:11,285 - INFO - Response - Page 12
2025-05-23 08:03:11,285 - INFO - 第 12 页获取到 100 条记录
2025-05-23 08:03:11,801 - INFO - Request Parameters - Page 13:
2025-05-23 08:03:11,801 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:11,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600395, 1744992000395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:12,551 - INFO - API请求耗时: 750ms
2025-05-23 08:03:12,551 - INFO - Response - Page 13
2025-05-23 08:03:12,551 - INFO - 第 13 页获取到 100 条记录
2025-05-23 08:03:13,051 - INFO - Request Parameters - Page 14:
2025-05-23 08:03:13,051 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:13,051 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600395, 1744992000395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:13,707 - INFO - API请求耗时: 656ms
2025-05-23 08:03:13,707 - INFO - Response - Page 14
2025-05-23 08:03:13,707 - INFO - 第 14 页获取到 100 条记录
2025-05-23 08:03:14,222 - INFO - Request Parameters - Page 15:
2025-05-23 08:03:14,222 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:14,222 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600395, 1744992000395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:14,941 - INFO - API请求耗时: 719ms
2025-05-23 08:03:14,941 - INFO - Response - Page 15
2025-05-23 08:03:14,941 - INFO - 第 15 页获取到 100 条记录
2025-05-23 08:03:15,457 - INFO - Request Parameters - Page 16:
2025-05-23 08:03:15,457 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:15,457 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600395, 1744992000395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:16,160 - INFO - API请求耗时: 703ms
2025-05-23 08:03:16,160 - INFO - Response - Page 16
2025-05-23 08:03:16,160 - INFO - 第 16 页获取到 100 条记录
2025-05-23 08:03:16,676 - INFO - Request Parameters - Page 17:
2025-05-23 08:03:16,676 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:16,676 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600395, 1744992000395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:17,332 - INFO - API请求耗时: 656ms
2025-05-23 08:03:17,332 - INFO - Response - Page 17
2025-05-23 08:03:17,332 - INFO - 第 17 页获取到 100 条记录
2025-05-23 08:03:17,847 - INFO - Request Parameters - Page 18:
2025-05-23 08:03:17,847 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:17,847 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600395, 1744992000395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:18,551 - INFO - API请求耗时: 703ms
2025-05-23 08:03:18,551 - INFO - Response - Page 18
2025-05-23 08:03:18,551 - INFO - 第 18 页获取到 100 条记录
2025-05-23 08:03:19,066 - INFO - Request Parameters - Page 19:
2025-05-23 08:03:19,066 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:19,066 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600395, 1744992000395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:19,801 - INFO - API请求耗时: 734ms
2025-05-23 08:03:19,801 - INFO - Response - Page 19
2025-05-23 08:03:19,801 - INFO - 第 19 页获取到 100 条记录
2025-05-23 08:03:20,316 - INFO - Request Parameters - Page 20:
2025-05-23 08:03:20,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:20,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600395, 1744992000395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:21,051 - INFO - API请求耗时: 734ms
2025-05-23 08:03:21,051 - INFO - Response - Page 20
2025-05-23 08:03:21,051 - INFO - 第 20 页获取到 100 条记录
2025-05-23 08:03:21,566 - INFO - Request Parameters - Page 21:
2025-05-23 08:03:21,566 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:21,566 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600395, 1744992000395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:22,222 - INFO - API请求耗时: 656ms
2025-05-23 08:03:22,222 - INFO - Response - Page 21
2025-05-23 08:03:22,222 - INFO - 第 21 页获取到 100 条记录
2025-05-23 08:03:22,722 - INFO - Request Parameters - Page 22:
2025-05-23 08:03:22,722 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:22,722 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600395, 1744992000395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:23,379 - INFO - API请求耗时: 656ms
2025-05-23 08:03:23,379 - INFO - Response - Page 22
2025-05-23 08:03:23,394 - INFO - 第 22 页获取到 100 条记录
2025-05-23 08:03:23,894 - INFO - Request Parameters - Page 23:
2025-05-23 08:03:23,894 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:23,894 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600395, 1744992000395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:24,551 - INFO - API请求耗时: 656ms
2025-05-23 08:03:24,551 - INFO - Response - Page 23
2025-05-23 08:03:24,551 - INFO - 第 23 页获取到 100 条记录
2025-05-23 08:03:25,051 - INFO - Request Parameters - Page 24:
2025-05-23 08:03:25,051 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:25,051 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600395, 1744992000395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:25,738 - INFO - API请求耗时: 687ms
2025-05-23 08:03:25,738 - INFO - Response - Page 24
2025-05-23 08:03:25,738 - INFO - 第 24 页获取到 100 条记录
2025-05-23 08:03:26,238 - INFO - Request Parameters - Page 25:
2025-05-23 08:03:26,238 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:26,238 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600395, 1744992000395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:26,910 - INFO - API请求耗时: 672ms
2025-05-23 08:03:26,910 - INFO - Response - Page 25
2025-05-23 08:03:26,910 - INFO - 第 25 页获取到 100 条记录
2025-05-23 08:03:27,425 - INFO - Request Parameters - Page 26:
2025-05-23 08:03:27,425 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:27,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600395, 1744992000395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:28,097 - INFO - API请求耗时: 672ms
2025-05-23 08:03:28,097 - INFO - Response - Page 26
2025-05-23 08:03:28,097 - INFO - 第 26 页获取到 100 条记录
2025-05-23 08:03:28,613 - INFO - Request Parameters - Page 27:
2025-05-23 08:03:28,613 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:28,613 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600395, 1744992000395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:29,222 - INFO - API请求耗时: 609ms
2025-05-23 08:03:29,222 - INFO - Response - Page 27
2025-05-23 08:03:29,222 - INFO - 第 27 页获取到 100 条记录
2025-05-23 08:03:29,722 - INFO - Request Parameters - Page 28:
2025-05-23 08:03:29,722 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:29,722 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600395, 1744992000395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:30,425 - INFO - API请求耗时: 703ms
2025-05-23 08:03:30,425 - INFO - Response - Page 28
2025-05-23 08:03:30,425 - INFO - 第 28 页获取到 95 条记录
2025-05-23 08:03:30,425 - INFO - 查询完成，共获取到 2795 条记录
2025-05-23 08:03:30,425 - INFO - 分段 4 查询成功，获取到 2795 条记录
2025-05-23 08:03:31,441 - INFO - 查询分段 5: 2025-04-20 至 2025-04-26
2025-05-23 08:03:31,441 - INFO - 查询日期范围: 2025-04-20 至 2025-04-26，使用分页查询，每页 100 条记录
2025-05-23 08:03:31,441 - INFO - Request Parameters - Page 1:
2025-05-23 08:03:31,441 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:31,441 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400395, 1745596800395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:32,129 - INFO - API请求耗时: 687ms
2025-05-23 08:03:32,129 - INFO - Response - Page 1
2025-05-23 08:03:32,129 - INFO - 第 1 页获取到 100 条记录
2025-05-23 08:03:32,644 - INFO - Request Parameters - Page 2:
2025-05-23 08:03:32,644 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:32,644 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400395, 1745596800395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:33,347 - INFO - API请求耗时: 703ms
2025-05-23 08:03:33,347 - INFO - Response - Page 2
2025-05-23 08:03:33,347 - INFO - 第 2 页获取到 100 条记录
2025-05-23 08:03:33,863 - INFO - Request Parameters - Page 3:
2025-05-23 08:03:33,863 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:33,863 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400395, 1745596800395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:34,519 - INFO - API请求耗时: 656ms
2025-05-23 08:03:34,519 - INFO - Response - Page 3
2025-05-23 08:03:34,519 - INFO - 第 3 页获取到 100 条记录
2025-05-23 08:03:35,035 - INFO - Request Parameters - Page 4:
2025-05-23 08:03:35,035 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:35,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400395, 1745596800395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:35,707 - INFO - API请求耗时: 672ms
2025-05-23 08:03:35,707 - INFO - Response - Page 4
2025-05-23 08:03:35,707 - INFO - 第 4 页获取到 100 条记录
2025-05-23 08:03:36,222 - INFO - Request Parameters - Page 5:
2025-05-23 08:03:36,222 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:36,222 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400395, 1745596800395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:36,879 - INFO - API请求耗时: 656ms
2025-05-23 08:03:36,879 - INFO - Response - Page 5
2025-05-23 08:03:36,879 - INFO - 第 5 页获取到 100 条记录
2025-05-23 08:03:37,379 - INFO - Request Parameters - Page 6:
2025-05-23 08:03:37,379 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:37,379 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400395, 1745596800395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:38,004 - INFO - API请求耗时: 625ms
2025-05-23 08:03:38,004 - INFO - Response - Page 6
2025-05-23 08:03:38,004 - INFO - 第 6 页获取到 100 条记录
2025-05-23 08:03:38,519 - INFO - Request Parameters - Page 7:
2025-05-23 08:03:38,519 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:38,519 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400395, 1745596800395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:39,222 - INFO - API请求耗时: 703ms
2025-05-23 08:03:39,222 - INFO - Response - Page 7
2025-05-23 08:03:39,222 - INFO - 第 7 页获取到 100 条记录
2025-05-23 08:03:39,738 - INFO - Request Parameters - Page 8:
2025-05-23 08:03:39,738 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:39,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400395, 1745596800395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:40,394 - INFO - API请求耗时: 656ms
2025-05-23 08:03:40,394 - INFO - Response - Page 8
2025-05-23 08:03:40,394 - INFO - 第 8 页获取到 100 条记录
2025-05-23 08:03:40,894 - INFO - Request Parameters - Page 9:
2025-05-23 08:03:40,894 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:40,894 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400395, 1745596800395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:41,535 - INFO - API请求耗时: 641ms
2025-05-23 08:03:41,535 - INFO - Response - Page 9
2025-05-23 08:03:41,550 - INFO - 第 9 页获取到 100 条记录
2025-05-23 08:03:42,050 - INFO - Request Parameters - Page 10:
2025-05-23 08:03:42,050 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:42,050 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400395, 1745596800395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:42,754 - INFO - API请求耗时: 703ms
2025-05-23 08:03:42,754 - INFO - Response - Page 10
2025-05-23 08:03:42,754 - INFO - 第 10 页获取到 100 条记录
2025-05-23 08:03:43,269 - INFO - Request Parameters - Page 11:
2025-05-23 08:03:43,269 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:43,269 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400395, 1745596800395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:43,957 - INFO - API请求耗时: 687ms
2025-05-23 08:03:43,957 - INFO - Response - Page 11
2025-05-23 08:03:43,957 - INFO - 第 11 页获取到 100 条记录
2025-05-23 08:03:44,457 - INFO - Request Parameters - Page 12:
2025-05-23 08:03:44,457 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:44,457 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400395, 1745596800395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:45,144 - INFO - API请求耗时: 687ms
2025-05-23 08:03:45,144 - INFO - Response - Page 12
2025-05-23 08:03:45,144 - INFO - 第 12 页获取到 100 条记录
2025-05-23 08:03:45,644 - INFO - Request Parameters - Page 13:
2025-05-23 08:03:45,644 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:45,644 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400395, 1745596800395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:46,503 - INFO - API请求耗时: 859ms
2025-05-23 08:03:46,503 - INFO - Response - Page 13
2025-05-23 08:03:46,503 - INFO - 第 13 页获取到 100 条记录
2025-05-23 08:03:47,019 - INFO - Request Parameters - Page 14:
2025-05-23 08:03:47,019 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:47,019 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400395, 1745596800395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:47,660 - INFO - API请求耗时: 641ms
2025-05-23 08:03:47,660 - INFO - Response - Page 14
2025-05-23 08:03:47,660 - INFO - 第 14 页获取到 100 条记录
2025-05-23 08:03:48,160 - INFO - Request Parameters - Page 15:
2025-05-23 08:03:48,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:48,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400395, 1745596800395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:48,863 - INFO - API请求耗时: 703ms
2025-05-23 08:03:48,863 - INFO - Response - Page 15
2025-05-23 08:03:48,863 - INFO - 第 15 页获取到 100 条记录
2025-05-23 08:03:49,378 - INFO - Request Parameters - Page 16:
2025-05-23 08:03:49,378 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:49,378 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400395, 1745596800395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:50,175 - INFO - API请求耗时: 797ms
2025-05-23 08:03:50,175 - INFO - Response - Page 16
2025-05-23 08:03:50,175 - INFO - 第 16 页获取到 100 条记录
2025-05-23 08:03:50,691 - INFO - Request Parameters - Page 17:
2025-05-23 08:03:50,691 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:50,691 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400395, 1745596800395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:51,347 - INFO - API请求耗时: 656ms
2025-05-23 08:03:51,347 - INFO - Response - Page 17
2025-05-23 08:03:51,347 - INFO - 第 17 页获取到 100 条记录
2025-05-23 08:03:51,863 - INFO - Request Parameters - Page 18:
2025-05-23 08:03:51,863 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:51,863 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400395, 1745596800395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:52,535 - INFO - API请求耗时: 672ms
2025-05-23 08:03:52,535 - INFO - Response - Page 18
2025-05-23 08:03:52,550 - INFO - 第 18 页获取到 100 条记录
2025-05-23 08:03:53,050 - INFO - Request Parameters - Page 19:
2025-05-23 08:03:53,050 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:53,050 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400395, 1745596800395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:53,816 - INFO - API请求耗时: 766ms
2025-05-23 08:03:53,816 - INFO - Response - Page 19
2025-05-23 08:03:53,816 - INFO - 第 19 页获取到 100 条记录
2025-05-23 08:03:54,316 - INFO - Request Parameters - Page 20:
2025-05-23 08:03:54,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:54,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400395, 1745596800395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:55,035 - INFO - API请求耗时: 719ms
2025-05-23 08:03:55,035 - INFO - Response - Page 20
2025-05-23 08:03:55,035 - INFO - 第 20 页获取到 100 条记录
2025-05-23 08:03:55,550 - INFO - Request Parameters - Page 21:
2025-05-23 08:03:55,550 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:55,550 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400395, 1745596800395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:56,191 - INFO - API请求耗时: 641ms
2025-05-23 08:03:56,191 - INFO - Response - Page 21
2025-05-23 08:03:56,191 - INFO - 第 21 页获取到 100 条记录
2025-05-23 08:03:56,707 - INFO - Request Parameters - Page 22:
2025-05-23 08:03:56,707 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:56,707 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400395, 1745596800395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:57,394 - INFO - API请求耗时: 687ms
2025-05-23 08:03:57,394 - INFO - Response - Page 22
2025-05-23 08:03:57,394 - INFO - 第 22 页获取到 100 条记录
2025-05-23 08:03:57,910 - INFO - Request Parameters - Page 23:
2025-05-23 08:03:57,910 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:57,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400395, 1745596800395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:58,582 - INFO - API请求耗时: 672ms
2025-05-23 08:03:58,582 - INFO - Response - Page 23
2025-05-23 08:03:58,582 - INFO - 第 23 页获取到 100 条记录
2025-05-23 08:03:59,097 - INFO - Request Parameters - Page 24:
2025-05-23 08:03:59,097 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:03:59,097 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400395, 1745596800395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:03:59,800 - INFO - API请求耗时: 703ms
2025-05-23 08:03:59,800 - INFO - Response - Page 24
2025-05-23 08:03:59,800 - INFO - 第 24 页获取到 100 条记录
2025-05-23 08:04:00,316 - INFO - Request Parameters - Page 25:
2025-05-23 08:04:00,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:00,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400395, 1745596800395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:00,957 - INFO - API请求耗时: 641ms
2025-05-23 08:04:00,957 - INFO - Response - Page 25
2025-05-23 08:04:00,972 - INFO - 第 25 页获取到 100 条记录
2025-05-23 08:04:01,472 - INFO - Request Parameters - Page 26:
2025-05-23 08:04:01,472 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:01,472 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400395, 1745596800395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:02,207 - INFO - API请求耗时: 734ms
2025-05-23 08:04:02,207 - INFO - Response - Page 26
2025-05-23 08:04:02,207 - INFO - 第 26 页获取到 100 条记录
2025-05-23 08:04:02,722 - INFO - Request Parameters - Page 27:
2025-05-23 08:04:02,722 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:02,722 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400395, 1745596800395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:03,441 - INFO - API请求耗时: 719ms
2025-05-23 08:04:03,441 - INFO - Response - Page 27
2025-05-23 08:04:03,441 - INFO - 第 27 页获取到 100 条记录
2025-05-23 08:04:03,957 - INFO - Request Parameters - Page 28:
2025-05-23 08:04:03,957 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:03,957 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745078400395, 1745596800395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:04,550 - INFO - API请求耗时: 594ms
2025-05-23 08:04:04,566 - INFO - Response - Page 28
2025-05-23 08:04:04,566 - INFO - 第 28 页获取到 43 条记录
2025-05-23 08:04:04,566 - INFO - 查询完成，共获取到 2743 条记录
2025-05-23 08:04:04,566 - INFO - 分段 5 查询成功，获取到 2743 条记录
2025-05-23 08:04:05,581 - INFO - 查询分段 6: 2025-04-27 至 2025-05-03
2025-05-23 08:04:05,581 - INFO - 查询日期范围: 2025-04-27 至 2025-05-03，使用分页查询，每页 100 条记录
2025-05-23 08:04:05,581 - INFO - Request Parameters - Page 1:
2025-05-23 08:04:05,581 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:05,581 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200395, 1746201600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:06,378 - INFO - API请求耗时: 797ms
2025-05-23 08:04:06,378 - INFO - Response - Page 1
2025-05-23 08:04:06,378 - INFO - 第 1 页获取到 100 条记录
2025-05-23 08:04:06,878 - INFO - Request Parameters - Page 2:
2025-05-23 08:04:06,878 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:06,878 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200395, 1746201600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:07,535 - INFO - API请求耗时: 656ms
2025-05-23 08:04:07,535 - INFO - Response - Page 2
2025-05-23 08:04:07,535 - INFO - 第 2 页获取到 100 条记录
2025-05-23 08:04:08,035 - INFO - Request Parameters - Page 3:
2025-05-23 08:04:08,035 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:08,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200395, 1746201600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:09,050 - INFO - API请求耗时: 1016ms
2025-05-23 08:04:09,050 - INFO - Response - Page 3
2025-05-23 08:04:09,066 - INFO - 第 3 页获取到 100 条记录
2025-05-23 08:04:09,566 - INFO - Request Parameters - Page 4:
2025-05-23 08:04:09,566 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:09,566 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200395, 1746201600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:10,285 - INFO - API请求耗时: 719ms
2025-05-23 08:04:10,285 - INFO - Response - Page 4
2025-05-23 08:04:10,285 - INFO - 第 4 页获取到 100 条记录
2025-05-23 08:04:10,800 - INFO - Request Parameters - Page 5:
2025-05-23 08:04:10,800 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:10,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200395, 1746201600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:11,519 - INFO - API请求耗时: 719ms
2025-05-23 08:04:11,519 - INFO - Response - Page 5
2025-05-23 08:04:11,519 - INFO - 第 5 页获取到 100 条记录
2025-05-23 08:04:12,019 - INFO - Request Parameters - Page 6:
2025-05-23 08:04:12,019 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:12,019 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200395, 1746201600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:12,675 - INFO - API请求耗时: 656ms
2025-05-23 08:04:12,675 - INFO - Response - Page 6
2025-05-23 08:04:12,675 - INFO - 第 6 页获取到 100 条记录
2025-05-23 08:04:13,175 - INFO - Request Parameters - Page 7:
2025-05-23 08:04:13,175 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:13,175 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200395, 1746201600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:13,847 - INFO - API请求耗时: 672ms
2025-05-23 08:04:13,847 - INFO - Response - Page 7
2025-05-23 08:04:13,847 - INFO - 第 7 页获取到 100 条记录
2025-05-23 08:04:14,363 - INFO - Request Parameters - Page 8:
2025-05-23 08:04:14,363 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:14,363 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200395, 1746201600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:15,003 - INFO - API请求耗时: 641ms
2025-05-23 08:04:15,003 - INFO - Response - Page 8
2025-05-23 08:04:15,003 - INFO - 第 8 页获取到 100 条记录
2025-05-23 08:04:15,503 - INFO - Request Parameters - Page 9:
2025-05-23 08:04:15,503 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:15,503 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200395, 1746201600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:16,144 - INFO - API请求耗时: 641ms
2025-05-23 08:04:16,144 - INFO - Response - Page 9
2025-05-23 08:04:16,144 - INFO - 第 9 页获取到 100 条记录
2025-05-23 08:04:16,660 - INFO - Request Parameters - Page 10:
2025-05-23 08:04:16,660 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:16,660 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200395, 1746201600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:17,347 - INFO - API请求耗时: 688ms
2025-05-23 08:04:17,347 - INFO - Response - Page 10
2025-05-23 08:04:17,347 - INFO - 第 10 页获取到 100 条记录
2025-05-23 08:04:17,863 - INFO - Request Parameters - Page 11:
2025-05-23 08:04:17,863 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:17,863 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200395, 1746201600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:18,550 - INFO - API请求耗时: 687ms
2025-05-23 08:04:18,550 - INFO - Response - Page 11
2025-05-23 08:04:18,550 - INFO - 第 11 页获取到 100 条记录
2025-05-23 08:04:19,066 - INFO - Request Parameters - Page 12:
2025-05-23 08:04:19,066 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:19,066 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200395, 1746201600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:19,785 - INFO - API请求耗时: 719ms
2025-05-23 08:04:19,785 - INFO - Response - Page 12
2025-05-23 08:04:19,785 - INFO - 第 12 页获取到 100 条记录
2025-05-23 08:04:20,300 - INFO - Request Parameters - Page 13:
2025-05-23 08:04:20,300 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:20,300 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200395, 1746201600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:21,003 - INFO - API请求耗时: 703ms
2025-05-23 08:04:21,003 - INFO - Response - Page 13
2025-05-23 08:04:21,003 - INFO - 第 13 页获取到 100 条记录
2025-05-23 08:04:21,503 - INFO - Request Parameters - Page 14:
2025-05-23 08:04:21,503 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:21,503 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200395, 1746201600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:22,113 - INFO - API请求耗时: 609ms
2025-05-23 08:04:22,113 - INFO - Response - Page 14
2025-05-23 08:04:22,128 - INFO - 第 14 页获取到 100 条记录
2025-05-23 08:04:22,644 - INFO - Request Parameters - Page 15:
2025-05-23 08:04:22,644 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:22,644 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200395, 1746201600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:23,316 - INFO - API请求耗时: 672ms
2025-05-23 08:04:23,316 - INFO - Response - Page 15
2025-05-23 08:04:23,316 - INFO - 第 15 页获取到 100 条记录
2025-05-23 08:04:23,816 - INFO - Request Parameters - Page 16:
2025-05-23 08:04:23,816 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:23,816 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200395, 1746201600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:24,738 - INFO - API请求耗时: 922ms
2025-05-23 08:04:24,738 - INFO - Response - Page 16
2025-05-23 08:04:24,738 - INFO - 第 16 页获取到 100 条记录
2025-05-23 08:04:25,253 - INFO - Request Parameters - Page 17:
2025-05-23 08:04:25,253 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:25,253 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200395, 1746201600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:25,956 - INFO - API请求耗时: 703ms
2025-05-23 08:04:25,956 - INFO - Response - Page 17
2025-05-23 08:04:25,956 - INFO - 第 17 页获取到 100 条记录
2025-05-23 08:04:26,472 - INFO - Request Parameters - Page 18:
2025-05-23 08:04:26,472 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:26,472 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200395, 1746201600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:27,206 - INFO - API请求耗时: 734ms
2025-05-23 08:04:27,206 - INFO - Response - Page 18
2025-05-23 08:04:27,206 - INFO - 第 18 页获取到 100 条记录
2025-05-23 08:04:27,722 - INFO - Request Parameters - Page 19:
2025-05-23 08:04:27,722 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:27,722 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200395, 1746201600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:28,425 - INFO - API请求耗时: 703ms
2025-05-23 08:04:28,425 - INFO - Response - Page 19
2025-05-23 08:04:28,425 - INFO - 第 19 页获取到 100 条记录
2025-05-23 08:04:28,925 - INFO - Request Parameters - Page 20:
2025-05-23 08:04:28,925 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:28,925 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200395, 1746201600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:29,597 - INFO - API请求耗时: 672ms
2025-05-23 08:04:29,597 - INFO - Response - Page 20
2025-05-23 08:04:29,597 - INFO - 第 20 页获取到 100 条记录
2025-05-23 08:04:30,113 - INFO - Request Parameters - Page 21:
2025-05-23 08:04:30,113 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:30,113 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200395, 1746201600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:30,784 - INFO - API请求耗时: 672ms
2025-05-23 08:04:30,784 - INFO - Response - Page 21
2025-05-23 08:04:30,784 - INFO - 第 21 页获取到 100 条记录
2025-05-23 08:04:31,300 - INFO - Request Parameters - Page 22:
2025-05-23 08:04:31,300 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:31,300 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200395, 1746201600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:32,050 - INFO - API请求耗时: 750ms
2025-05-23 08:04:32,050 - INFO - Response - Page 22
2025-05-23 08:04:32,050 - INFO - 第 22 页获取到 100 条记录
2025-05-23 08:04:32,566 - INFO - Request Parameters - Page 23:
2025-05-23 08:04:32,566 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:32,566 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200395, 1746201600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:33,269 - INFO - API请求耗时: 703ms
2025-05-23 08:04:33,269 - INFO - Response - Page 23
2025-05-23 08:04:33,269 - INFO - 第 23 页获取到 100 条记录
2025-05-23 08:04:33,784 - INFO - Request Parameters - Page 24:
2025-05-23 08:04:33,784 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:33,784 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200395, 1746201600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:34,534 - INFO - API请求耗时: 750ms
2025-05-23 08:04:34,534 - INFO - Response - Page 24
2025-05-23 08:04:34,534 - INFO - 第 24 页获取到 100 条记录
2025-05-23 08:04:35,050 - INFO - Request Parameters - Page 25:
2025-05-23 08:04:35,050 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:35,050 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200395, 1746201600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:35,722 - INFO - API请求耗时: 672ms
2025-05-23 08:04:35,722 - INFO - Response - Page 25
2025-05-23 08:04:35,722 - INFO - 第 25 页获取到 100 条记录
2025-05-23 08:04:36,222 - INFO - Request Parameters - Page 26:
2025-05-23 08:04:36,222 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:36,222 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200395, 1746201600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:36,941 - INFO - API请求耗时: 719ms
2025-05-23 08:04:36,941 - INFO - Response - Page 26
2025-05-23 08:04:36,941 - INFO - 第 26 页获取到 100 条记录
2025-05-23 08:04:37,456 - INFO - Request Parameters - Page 27:
2025-05-23 08:04:37,456 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:37,456 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200395, 1746201600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:38,159 - INFO - API请求耗时: 703ms
2025-05-23 08:04:38,159 - INFO - Response - Page 27
2025-05-23 08:04:38,159 - INFO - 第 27 页获取到 100 条记录
2025-05-23 08:04:38,675 - INFO - Request Parameters - Page 28:
2025-05-23 08:04:38,675 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:38,675 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200395, 1746201600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:39,347 - INFO - API请求耗时: 672ms
2025-05-23 08:04:39,347 - INFO - Response - Page 28
2025-05-23 08:04:39,347 - INFO - 第 28 页获取到 100 条记录
2025-05-23 08:04:39,847 - INFO - Request Parameters - Page 29:
2025-05-23 08:04:39,847 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:39,847 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 29, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200395, 1746201600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:40,503 - INFO - API请求耗时: 656ms
2025-05-23 08:04:40,503 - INFO - Response - Page 29
2025-05-23 08:04:40,519 - INFO - 第 29 页获取到 100 条记录
2025-05-23 08:04:41,034 - INFO - Request Parameters - Page 30:
2025-05-23 08:04:41,034 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:41,034 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 30, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200395, 1746201600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:41,831 - INFO - API请求耗时: 797ms
2025-05-23 08:04:41,831 - INFO - Response - Page 30
2025-05-23 08:04:41,831 - INFO - 第 30 页获取到 100 条记录
2025-05-23 08:04:42,347 - INFO - Request Parameters - Page 31:
2025-05-23 08:04:42,347 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:42,347 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 31, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200395, 1746201600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:42,956 - INFO - API请求耗时: 609ms
2025-05-23 08:04:42,956 - INFO - Response - Page 31
2025-05-23 08:04:42,956 - INFO - 第 31 页获取到 100 条记录
2025-05-23 08:04:43,472 - INFO - Request Parameters - Page 32:
2025-05-23 08:04:43,472 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:43,472 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 32, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200395, 1746201600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:44,175 - INFO - API请求耗时: 703ms
2025-05-23 08:04:44,175 - INFO - Response - Page 32
2025-05-23 08:04:44,175 - INFO - 第 32 页获取到 100 条记录
2025-05-23 08:04:44,675 - INFO - Request Parameters - Page 33:
2025-05-23 08:04:44,675 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:44,675 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 33, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200395, 1746201600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:45,331 - INFO - API请求耗时: 656ms
2025-05-23 08:04:45,331 - INFO - Response - Page 33
2025-05-23 08:04:45,331 - INFO - 第 33 页获取到 100 条记录
2025-05-23 08:04:45,831 - INFO - Request Parameters - Page 34:
2025-05-23 08:04:45,831 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:45,831 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 34, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200395, 1746201600395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:46,409 - INFO - API请求耗时: 578ms
2025-05-23 08:04:46,409 - INFO - Response - Page 34
2025-05-23 08:04:46,409 - INFO - 第 34 页获取到 45 条记录
2025-05-23 08:04:46,409 - INFO - 查询完成，共获取到 3345 条记录
2025-05-23 08:04:46,409 - INFO - 分段 6 查询成功，获取到 3345 条记录
2025-05-23 08:04:47,425 - INFO - 查询分段 7: 2025-05-04 至 2025-05-10
2025-05-23 08:04:47,425 - INFO - 查询日期范围: 2025-05-04 至 2025-05-10，使用分页查询，每页 100 条记录
2025-05-23 08:04:47,425 - INFO - Request Parameters - Page 1:
2025-05-23 08:04:47,425 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:47,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000395, 1746806400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:48,206 - INFO - API请求耗时: 781ms
2025-05-23 08:04:48,206 - INFO - Response - Page 1
2025-05-23 08:04:48,206 - INFO - 第 1 页获取到 100 条记录
2025-05-23 08:04:48,722 - INFO - Request Parameters - Page 2:
2025-05-23 08:04:48,722 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:48,722 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000395, 1746806400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:49,331 - INFO - API请求耗时: 609ms
2025-05-23 08:04:49,331 - INFO - Response - Page 2
2025-05-23 08:04:49,331 - INFO - 第 2 页获取到 100 条记录
2025-05-23 08:04:49,831 - INFO - Request Parameters - Page 3:
2025-05-23 08:04:49,831 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:49,831 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000395, 1746806400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:50,503 - INFO - API请求耗时: 672ms
2025-05-23 08:04:50,503 - INFO - Response - Page 3
2025-05-23 08:04:50,503 - INFO - 第 3 页获取到 100 条记录
2025-05-23 08:04:51,019 - INFO - Request Parameters - Page 4:
2025-05-23 08:04:51,019 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:51,019 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000395, 1746806400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:51,675 - INFO - API请求耗时: 656ms
2025-05-23 08:04:51,675 - INFO - Response - Page 4
2025-05-23 08:04:51,675 - INFO - 第 4 页获取到 100 条记录
2025-05-23 08:04:52,191 - INFO - Request Parameters - Page 5:
2025-05-23 08:04:52,191 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:52,191 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000395, 1746806400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:52,831 - INFO - API请求耗时: 641ms
2025-05-23 08:04:52,831 - INFO - Response - Page 5
2025-05-23 08:04:52,831 - INFO - 第 5 页获取到 100 条记录
2025-05-23 08:04:53,331 - INFO - Request Parameters - Page 6:
2025-05-23 08:04:53,331 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:53,331 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000395, 1746806400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:54,050 - INFO - API请求耗时: 719ms
2025-05-23 08:04:54,050 - INFO - Response - Page 6
2025-05-23 08:04:54,050 - INFO - 第 6 页获取到 100 条记录
2025-05-23 08:04:54,566 - INFO - Request Parameters - Page 7:
2025-05-23 08:04:54,566 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:54,566 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000395, 1746806400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:55,300 - INFO - API请求耗时: 734ms
2025-05-23 08:04:55,300 - INFO - Response - Page 7
2025-05-23 08:04:55,300 - INFO - 第 7 页获取到 100 条记录
2025-05-23 08:04:55,800 - INFO - Request Parameters - Page 8:
2025-05-23 08:04:55,800 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:55,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000395, 1746806400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:56,441 - INFO - API请求耗时: 641ms
2025-05-23 08:04:56,441 - INFO - Response - Page 8
2025-05-23 08:04:56,441 - INFO - 第 8 页获取到 100 条记录
2025-05-23 08:04:56,956 - INFO - Request Parameters - Page 9:
2025-05-23 08:04:56,956 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:56,956 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000395, 1746806400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:57,628 - INFO - API请求耗时: 672ms
2025-05-23 08:04:57,628 - INFO - Response - Page 9
2025-05-23 08:04:57,628 - INFO - 第 9 页获取到 100 条记录
2025-05-23 08:04:58,128 - INFO - Request Parameters - Page 10:
2025-05-23 08:04:58,128 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:58,128 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000395, 1746806400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:58,784 - INFO - API请求耗时: 656ms
2025-05-23 08:04:58,784 - INFO - Response - Page 10
2025-05-23 08:04:58,784 - INFO - 第 10 页获取到 100 条记录
2025-05-23 08:04:59,300 - INFO - Request Parameters - Page 11:
2025-05-23 08:04:59,300 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:04:59,300 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000395, 1746806400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:04:59,894 - INFO - API请求耗时: 594ms
2025-05-23 08:04:59,894 - INFO - Response - Page 11
2025-05-23 08:04:59,894 - INFO - 第 11 页获取到 100 条记录
2025-05-23 08:05:00,409 - INFO - Request Parameters - Page 12:
2025-05-23 08:05:00,409 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:00,409 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000395, 1746806400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:01,081 - INFO - API请求耗时: 672ms
2025-05-23 08:05:01,081 - INFO - Response - Page 12
2025-05-23 08:05:01,081 - INFO - 第 12 页获取到 100 条记录
2025-05-23 08:05:01,597 - INFO - Request Parameters - Page 13:
2025-05-23 08:05:01,597 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:01,597 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000395, 1746806400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:02,425 - INFO - API请求耗时: 828ms
2025-05-23 08:05:02,425 - INFO - Response - Page 13
2025-05-23 08:05:02,425 - INFO - 第 13 页获取到 100 条记录
2025-05-23 08:05:02,925 - INFO - Request Parameters - Page 14:
2025-05-23 08:05:02,925 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:02,925 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000395, 1746806400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:03,659 - INFO - API请求耗时: 734ms
2025-05-23 08:05:03,659 - INFO - Response - Page 14
2025-05-23 08:05:03,659 - INFO - 第 14 页获取到 100 条记录
2025-05-23 08:05:04,159 - INFO - Request Parameters - Page 15:
2025-05-23 08:05:04,159 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:04,159 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000395, 1746806400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:04,847 - INFO - API请求耗时: 687ms
2025-05-23 08:05:04,847 - INFO - Response - Page 15
2025-05-23 08:05:04,847 - INFO - 第 15 页获取到 100 条记录
2025-05-23 08:05:05,362 - INFO - Request Parameters - Page 16:
2025-05-23 08:05:05,362 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:05,362 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000395, 1746806400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:06,300 - INFO - API请求耗时: 937ms
2025-05-23 08:05:06,300 - INFO - Response - Page 16
2025-05-23 08:05:06,300 - INFO - 第 16 页获取到 100 条记录
2025-05-23 08:05:06,815 - INFO - Request Parameters - Page 17:
2025-05-23 08:05:06,815 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:06,815 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000395, 1746806400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:07,425 - INFO - API请求耗时: 609ms
2025-05-23 08:05:07,425 - INFO - Response - Page 17
2025-05-23 08:05:07,425 - INFO - 第 17 页获取到 100 条记录
2025-05-23 08:05:07,940 - INFO - Request Parameters - Page 18:
2025-05-23 08:05:07,940 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:07,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000395, 1746806400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:08,644 - INFO - API请求耗时: 703ms
2025-05-23 08:05:08,644 - INFO - Response - Page 18
2025-05-23 08:05:08,644 - INFO - 第 18 页获取到 100 条记录
2025-05-23 08:05:09,159 - INFO - Request Parameters - Page 19:
2025-05-23 08:05:09,159 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:09,159 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000395, 1746806400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:09,878 - INFO - API请求耗时: 719ms
2025-05-23 08:05:09,878 - INFO - Response - Page 19
2025-05-23 08:05:09,878 - INFO - 第 19 页获取到 100 条记录
2025-05-23 08:05:10,378 - INFO - Request Parameters - Page 20:
2025-05-23 08:05:10,378 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:10,378 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000395, 1746806400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:11,175 - INFO - API请求耗时: 797ms
2025-05-23 08:05:11,175 - INFO - Response - Page 20
2025-05-23 08:05:11,175 - INFO - 第 20 页获取到 100 条记录
2025-05-23 08:05:11,690 - INFO - Request Parameters - Page 21:
2025-05-23 08:05:11,690 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:11,690 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000395, 1746806400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:12,409 - INFO - API请求耗时: 719ms
2025-05-23 08:05:12,409 - INFO - Response - Page 21
2025-05-23 08:05:12,409 - INFO - 第 21 页获取到 100 条记录
2025-05-23 08:05:12,925 - INFO - Request Parameters - Page 22:
2025-05-23 08:05:12,925 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:12,925 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000395, 1746806400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:13,690 - INFO - API请求耗时: 766ms
2025-05-23 08:05:13,690 - INFO - Response - Page 22
2025-05-23 08:05:13,706 - INFO - 第 22 页获取到 100 条记录
2025-05-23 08:05:14,206 - INFO - Request Parameters - Page 23:
2025-05-23 08:05:14,206 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:14,206 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000395, 1746806400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:14,972 - INFO - API请求耗时: 766ms
2025-05-23 08:05:14,972 - INFO - Response - Page 23
2025-05-23 08:05:14,972 - INFO - 第 23 页获取到 100 条记录
2025-05-23 08:05:15,487 - INFO - Request Parameters - Page 24:
2025-05-23 08:05:15,487 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:15,487 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000395, 1746806400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:16,331 - INFO - API请求耗时: 844ms
2025-05-23 08:05:16,331 - INFO - Response - Page 24
2025-05-23 08:05:16,331 - INFO - 第 24 页获取到 100 条记录
2025-05-23 08:05:16,847 - INFO - Request Parameters - Page 25:
2025-05-23 08:05:16,847 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:16,847 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000395, 1746806400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:17,565 - INFO - API请求耗时: 719ms
2025-05-23 08:05:17,565 - INFO - Response - Page 25
2025-05-23 08:05:17,565 - INFO - 第 25 页获取到 100 条记录
2025-05-23 08:05:18,065 - INFO - Request Parameters - Page 26:
2025-05-23 08:05:18,065 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:18,065 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000395, 1746806400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:18,690 - INFO - API请求耗时: 625ms
2025-05-23 08:05:18,690 - INFO - Response - Page 26
2025-05-23 08:05:18,690 - INFO - 第 26 页获取到 100 条记录
2025-05-23 08:05:19,206 - INFO - Request Parameters - Page 27:
2025-05-23 08:05:19,206 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:19,206 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000395, 1746806400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:20,003 - INFO - API请求耗时: 797ms
2025-05-23 08:05:20,003 - INFO - Response - Page 27
2025-05-23 08:05:20,003 - INFO - 第 27 页获取到 100 条记录
2025-05-23 08:05:20,519 - INFO - Request Parameters - Page 28:
2025-05-23 08:05:20,519 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:20,519 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746288000395, 1746806400395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:21,003 - INFO - API请求耗时: 484ms
2025-05-23 08:05:21,003 - INFO - Response - Page 28
2025-05-23 08:05:21,003 - INFO - 第 28 页获取到 19 条记录
2025-05-23 08:05:21,003 - INFO - 查询完成，共获取到 2719 条记录
2025-05-23 08:05:21,003 - INFO - 分段 7 查询成功，获取到 2719 条记录
2025-05-23 08:05:22,019 - INFO - 查询分段 8: 2025-05-11 至 2025-05-17
2025-05-23 08:05:22,019 - INFO - 查询日期范围: 2025-05-11 至 2025-05-17，使用分页查询，每页 100 条记录
2025-05-23 08:05:22,019 - INFO - Request Parameters - Page 1:
2025-05-23 08:05:22,019 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:22,019 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800395, 1747411200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:22,690 - INFO - API请求耗时: 672ms
2025-05-23 08:05:22,690 - INFO - Response - Page 1
2025-05-23 08:05:22,690 - INFO - 第 1 页获取到 100 条记录
2025-05-23 08:05:23,206 - INFO - Request Parameters - Page 2:
2025-05-23 08:05:23,206 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:23,206 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800395, 1747411200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:23,893 - INFO - API请求耗时: 687ms
2025-05-23 08:05:23,893 - INFO - Response - Page 2
2025-05-23 08:05:23,893 - INFO - 第 2 页获取到 100 条记录
2025-05-23 08:05:24,393 - INFO - Request Parameters - Page 3:
2025-05-23 08:05:24,393 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:24,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800395, 1747411200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:25,097 - INFO - API请求耗时: 703ms
2025-05-23 08:05:25,097 - INFO - Response - Page 3
2025-05-23 08:05:25,097 - INFO - 第 3 页获取到 100 条记录
2025-05-23 08:05:25,597 - INFO - Request Parameters - Page 4:
2025-05-23 08:05:25,597 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:25,597 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800395, 1747411200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:26,362 - INFO - API请求耗时: 766ms
2025-05-23 08:05:26,362 - INFO - Response - Page 4
2025-05-23 08:05:26,362 - INFO - 第 4 页获取到 100 条记录
2025-05-23 08:05:26,862 - INFO - Request Parameters - Page 5:
2025-05-23 08:05:26,862 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:26,862 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800395, 1747411200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:27,581 - INFO - API请求耗时: 719ms
2025-05-23 08:05:27,581 - INFO - Response - Page 5
2025-05-23 08:05:27,581 - INFO - 第 5 页获取到 100 条记录
2025-05-23 08:05:28,081 - INFO - Request Parameters - Page 6:
2025-05-23 08:05:28,081 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:28,081 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800395, 1747411200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:28,784 - INFO - API请求耗时: 703ms
2025-05-23 08:05:28,784 - INFO - Response - Page 6
2025-05-23 08:05:28,784 - INFO - 第 6 页获取到 100 条记录
2025-05-23 08:05:29,300 - INFO - Request Parameters - Page 7:
2025-05-23 08:05:29,300 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:29,300 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800395, 1747411200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:29,956 - INFO - API请求耗时: 656ms
2025-05-23 08:05:29,956 - INFO - Response - Page 7
2025-05-23 08:05:29,956 - INFO - 第 7 页获取到 100 条记录
2025-05-23 08:05:30,456 - INFO - Request Parameters - Page 8:
2025-05-23 08:05:30,456 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:30,456 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800395, 1747411200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:31,143 - INFO - API请求耗时: 687ms
2025-05-23 08:05:31,143 - INFO - Response - Page 8
2025-05-23 08:05:31,143 - INFO - 第 8 页获取到 100 条记录
2025-05-23 08:05:31,643 - INFO - Request Parameters - Page 9:
2025-05-23 08:05:31,643 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:31,643 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 9, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800395, 1747411200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:32,347 - INFO - API请求耗时: 703ms
2025-05-23 08:05:32,347 - INFO - Response - Page 9
2025-05-23 08:05:32,347 - INFO - 第 9 页获取到 100 条记录
2025-05-23 08:05:32,847 - INFO - Request Parameters - Page 10:
2025-05-23 08:05:32,847 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:32,847 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 10, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800395, 1747411200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:33,534 - INFO - API请求耗时: 687ms
2025-05-23 08:05:33,550 - INFO - Response - Page 10
2025-05-23 08:05:33,550 - INFO - 第 10 页获取到 100 条记录
2025-05-23 08:05:34,050 - INFO - Request Parameters - Page 11:
2025-05-23 08:05:34,050 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:34,050 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 11, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800395, 1747411200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:34,722 - INFO - API请求耗时: 672ms
2025-05-23 08:05:34,722 - INFO - Response - Page 11
2025-05-23 08:05:34,722 - INFO - 第 11 页获取到 100 条记录
2025-05-23 08:05:35,222 - INFO - Request Parameters - Page 12:
2025-05-23 08:05:35,222 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:35,222 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 12, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800395, 1747411200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:35,940 - INFO - API请求耗时: 719ms
2025-05-23 08:05:35,940 - INFO - Response - Page 12
2025-05-23 08:05:35,956 - INFO - 第 12 页获取到 100 条记录
2025-05-23 08:05:36,472 - INFO - Request Parameters - Page 13:
2025-05-23 08:05:36,472 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:36,472 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 13, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800395, 1747411200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:37,143 - INFO - API请求耗时: 672ms
2025-05-23 08:05:37,143 - INFO - Response - Page 13
2025-05-23 08:05:37,143 - INFO - 第 13 页获取到 100 条记录
2025-05-23 08:05:37,659 - INFO - Request Parameters - Page 14:
2025-05-23 08:05:37,659 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:37,659 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 14, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800395, 1747411200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:38,331 - INFO - API请求耗时: 672ms
2025-05-23 08:05:38,331 - INFO - Response - Page 14
2025-05-23 08:05:38,331 - INFO - 第 14 页获取到 100 条记录
2025-05-23 08:05:38,831 - INFO - Request Parameters - Page 15:
2025-05-23 08:05:38,831 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:38,831 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 15, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800395, 1747411200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:39,503 - INFO - API请求耗时: 672ms
2025-05-23 08:05:39,518 - INFO - Response - Page 15
2025-05-23 08:05:39,518 - INFO - 第 15 页获取到 100 条记录
2025-05-23 08:05:40,034 - INFO - Request Parameters - Page 16:
2025-05-23 08:05:40,034 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:40,034 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 16, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800395, 1747411200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:40,721 - INFO - API请求耗时: 687ms
2025-05-23 08:05:40,721 - INFO - Response - Page 16
2025-05-23 08:05:40,721 - INFO - 第 16 页获取到 100 条记录
2025-05-23 08:05:41,237 - INFO - Request Parameters - Page 17:
2025-05-23 08:05:41,237 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:41,237 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 17, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800395, 1747411200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:41,940 - INFO - API请求耗时: 703ms
2025-05-23 08:05:41,940 - INFO - Response - Page 17
2025-05-23 08:05:41,940 - INFO - 第 17 页获取到 100 条记录
2025-05-23 08:05:42,440 - INFO - Request Parameters - Page 18:
2025-05-23 08:05:42,440 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:42,440 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 18, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800395, 1747411200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:43,128 - INFO - API请求耗时: 687ms
2025-05-23 08:05:43,128 - INFO - Response - Page 18
2025-05-23 08:05:43,128 - INFO - 第 18 页获取到 100 条记录
2025-05-23 08:05:43,628 - INFO - Request Parameters - Page 19:
2025-05-23 08:05:43,628 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:43,628 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 19, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800395, 1747411200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:44,268 - INFO - API请求耗时: 641ms
2025-05-23 08:05:44,268 - INFO - Response - Page 19
2025-05-23 08:05:44,268 - INFO - 第 19 页获取到 100 条记录
2025-05-23 08:05:44,784 - INFO - Request Parameters - Page 20:
2025-05-23 08:05:44,784 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:44,784 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 20, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800395, 1747411200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:45,440 - INFO - API请求耗时: 656ms
2025-05-23 08:05:45,440 - INFO - Response - Page 20
2025-05-23 08:05:45,440 - INFO - 第 20 页获取到 100 条记录
2025-05-23 08:05:45,956 - INFO - Request Parameters - Page 21:
2025-05-23 08:05:45,956 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:45,956 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 21, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800395, 1747411200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:46,628 - INFO - API请求耗时: 672ms
2025-05-23 08:05:46,628 - INFO - Response - Page 21
2025-05-23 08:05:46,628 - INFO - 第 21 页获取到 100 条记录
2025-05-23 08:05:47,143 - INFO - Request Parameters - Page 22:
2025-05-23 08:05:47,143 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:47,143 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 22, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800395, 1747411200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:47,768 - INFO - API请求耗时: 625ms
2025-05-23 08:05:47,768 - INFO - Response - Page 22
2025-05-23 08:05:47,768 - INFO - 第 22 页获取到 100 条记录
2025-05-23 08:05:48,284 - INFO - Request Parameters - Page 23:
2025-05-23 08:05:48,284 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:48,284 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 23, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800395, 1747411200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:49,081 - INFO - API请求耗时: 797ms
2025-05-23 08:05:49,096 - INFO - Response - Page 23
2025-05-23 08:05:49,096 - INFO - 第 23 页获取到 100 条记录
2025-05-23 08:05:49,612 - INFO - Request Parameters - Page 24:
2025-05-23 08:05:49,612 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:49,612 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 24, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800395, 1747411200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:50,300 - INFO - API请求耗时: 687ms
2025-05-23 08:05:50,300 - INFO - Response - Page 24
2025-05-23 08:05:50,300 - INFO - 第 24 页获取到 100 条记录
2025-05-23 08:05:50,815 - INFO - Request Parameters - Page 25:
2025-05-23 08:05:50,815 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:50,815 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 25, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800395, 1747411200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:51,690 - INFO - API请求耗时: 875ms
2025-05-23 08:05:51,690 - INFO - Response - Page 25
2025-05-23 08:05:51,690 - INFO - 第 25 页获取到 100 条记录
2025-05-23 08:05:52,206 - INFO - Request Parameters - Page 26:
2025-05-23 08:05:52,206 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:52,206 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 26, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800395, 1747411200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:52,925 - INFO - API请求耗时: 719ms
2025-05-23 08:05:52,925 - INFO - Response - Page 26
2025-05-23 08:05:52,925 - INFO - 第 26 页获取到 100 条记录
2025-05-23 08:05:53,425 - INFO - Request Parameters - Page 27:
2025-05-23 08:05:53,425 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:53,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 27, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800395, 1747411200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:54,081 - INFO - API请求耗时: 656ms
2025-05-23 08:05:54,081 - INFO - Response - Page 27
2025-05-23 08:05:54,081 - INFO - 第 27 页获取到 100 条记录
2025-05-23 08:05:54,596 - INFO - Request Parameters - Page 28:
2025-05-23 08:05:54,596 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:54,596 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 28, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800395, 1747411200395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:55,112 - INFO - API请求耗时: 516ms
2025-05-23 08:05:55,112 - INFO - Response - Page 28
2025-05-23 08:05:55,112 - INFO - 第 28 页获取到 20 条记录
2025-05-23 08:05:55,112 - INFO - 查询完成，共获取到 2720 条记录
2025-05-23 08:05:55,112 - INFO - 分段 8 查询成功，获取到 2720 条记录
2025-05-23 08:05:56,128 - INFO - 查询分段 9: 2025-05-18 至 2025-05-22
2025-05-23 08:05:56,128 - INFO - 查询日期范围: 2025-05-18 至 2025-05-22，使用分页查询，每页 100 条记录
2025-05-23 08:05:56,128 - INFO - Request Parameters - Page 1:
2025-05-23 08:05:56,128 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:56,128 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600395, 1747929599395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:56,737 - INFO - API请求耗时: 609ms
2025-05-23 08:05:56,737 - INFO - Response - Page 1
2025-05-23 08:05:56,737 - INFO - 第 1 页获取到 100 条记录
2025-05-23 08:05:57,237 - INFO - Request Parameters - Page 2:
2025-05-23 08:05:57,237 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:57,237 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600395, 1747929599395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:57,862 - INFO - API请求耗时: 625ms
2025-05-23 08:05:57,862 - INFO - Response - Page 2
2025-05-23 08:05:57,862 - INFO - 第 2 页获取到 100 条记录
2025-05-23 08:05:58,378 - INFO - Request Parameters - Page 3:
2025-05-23 08:05:58,378 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:58,378 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600395, 1747929599395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:05:59,034 - INFO - API请求耗时: 656ms
2025-05-23 08:05:59,034 - INFO - Response - Page 3
2025-05-23 08:05:59,034 - INFO - 第 3 页获取到 100 条记录
2025-05-23 08:05:59,550 - INFO - Request Parameters - Page 4:
2025-05-23 08:05:59,550 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:05:59,550 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600395, 1747929599395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:06:00,159 - INFO - API请求耗时: 609ms
2025-05-23 08:06:00,159 - INFO - Response - Page 4
2025-05-23 08:06:00,159 - INFO - 第 4 页获取到 100 条记录
2025-05-23 08:06:00,659 - INFO - Request Parameters - Page 5:
2025-05-23 08:06:00,659 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:06:00,659 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600395, 1747929599395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:06:01,503 - INFO - API请求耗时: 844ms
2025-05-23 08:06:01,503 - INFO - Response - Page 5
2025-05-23 08:06:01,503 - INFO - 第 5 页获取到 100 条记录
2025-05-23 08:06:02,018 - INFO - Request Parameters - Page 6:
2025-05-23 08:06:02,018 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:06:02,018 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747497600395, 1747929599395], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:06:02,643 - INFO - API请求耗时: 625ms
2025-05-23 08:06:02,643 - INFO - Response - Page 6
2025-05-23 08:06:02,643 - INFO - 第 6 页获取到 98 条记录
2025-05-23 08:06:02,643 - INFO - 查询完成，共获取到 598 条记录
2025-05-23 08:06:02,643 - INFO - 分段 9 查询成功，获取到 598 条记录
2025-05-23 08:06:03,659 - INFO - 宜搭每日表单数据查询完成，共 9 个分段，成功获取 23418 条记录，失败 0 次
2025-05-23 08:06:03,659 - INFO - 成功获取宜搭日销售表单数据，共 23418 条记录
2025-05-23 08:06:03,659 - INFO - 宜搭日销售数据项结构示例: ['formInstanceId', 'formData']
2025-05-23 08:06:03,659 - INFO - 开始对比和同步日销售数据...
2025-05-23 08:06:04,346 - INFO - 成功创建宜搭日销售数据索引，共 10865 条记录
2025-05-23 08:06:04,346 - INFO - 开始处理数衍数据，共 13060 条记录
2025-05-23 08:06:04,862 - INFO - 更新表单数据成功: FINST-6PF66691GAGV6PDBD461I9TCKQQC2WNF49PAM35
2025-05-23 08:06:04,862 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 27815.44, 'new_value': 27569.44}, {'field': 'amount', 'old_value': 27815.44, 'new_value': 27569.44}, {'field': 'count', 'old_value': 75, 'new_value': 74}, {'field': 'instoreAmount', 'old_value': 25737.2, 'new_value': 25491.2}, {'field': 'instoreCount', 'old_value': 58, 'new_value': 57}]
2025-05-23 08:06:05,315 - INFO - 更新表单数据成功: FINST-S0E660A1ZAGVZCG78OHT65723C9J2KNB39PAM82
2025-05-23 08:06:05,315 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_20250402, 变更字段: [{'field': 'recommendAmount', 'old_value': 13481.01, 'new_value': 13143.01}, {'field': 'amount', 'old_value': 13481.01, 'new_value': 13143.01}, {'field': 'count', 'old_value': 38, 'new_value': 37}, {'field': 'instoreAmount', 'old_value': 12696.0, 'new_value': 12358.0}, {'field': 'instoreCount', 'old_value': 31, 'new_value': 30}]
2025-05-23 08:06:05,784 - INFO - 更新表单数据成功: FINST-PAB66N718AGVMM80DTXSG7O9ZWYR2AAE59PAMK
2025-05-23 08:06:05,784 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_3F059827C9E04DEAA6B50797867EC52B_********, 变更字段: [{'field': 'amount', 'old_value': 9306.0, 'new_value': 9347.0}, {'field': 'count', 'old_value': 50, 'new_value': 51}, {'field': 'instoreAmount', 'old_value': 9306.0, 'new_value': 9347.0}, {'field': 'instoreCount', 'old_value': 50, 'new_value': 51}]
2025-05-23 08:06:06,268 - INFO - 更新表单数据成功: FINST-6IF66PC15AGVIEJ6CRRIA6I47JB32UKJ59PAMW2
2025-05-23 08:06:06,268 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 26559.69, 'new_value': 25183.69}, {'field': 'amount', 'old_value': 26559.69, 'new_value': 25183.69}, {'field': 'count', 'old_value': 87, 'new_value': 83}, {'field': 'instoreAmount', 'old_value': 24000.0, 'new_value': 22624.0}, {'field': 'instoreCount', 'old_value': 66, 'new_value': 62}]
2025-05-23 08:06:06,753 - INFO - 更新表单数据成功: FINST-6PF66691GAGV6PDBD461I9TCKQQC2WNF49PAMX4
2025-05-23 08:06:06,753 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_20250411, 变更字段: [{'field': 'recommendAmount', 'old_value': 18881.27, 'new_value': 18418.67}, {'field': 'amount', 'old_value': 18881.27, 'new_value': 18418.67}, {'field': 'count', 'old_value': 58, 'new_value': 57}, {'field': 'instoreAmount', 'old_value': 17778.2, 'new_value': 17315.6}, {'field': 'instoreCount', 'old_value': 49, 'new_value': 48}]
2025-05-23 08:06:07,221 - INFO - 更新表单数据成功: FINST-6PF66691GAGV6PDBD461I9TCKQQC2WNF49PAM05
2025-05-23 08:06:07,221 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_20250408, 变更字段: [{'field': 'recommendAmount', 'old_value': 5801.88, 'new_value': 5534.88}, {'field': 'amount', 'old_value': 5801.88, 'new_value': 5534.88}, {'field': 'count', 'old_value': 32, 'new_value': 31}, {'field': 'instoreAmount', 'old_value': 4461.2, 'new_value': 4194.2}, {'field': 'instoreCount', 'old_value': 18, 'new_value': 17}]
2025-05-23 08:06:07,721 - INFO - 更新表单数据成功: FINST-GX9663D1W9GVID6SFHIH9B77OGVW3UBN69PAM12
2025-05-23 08:06:07,721 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 42497.8, 'new_value': 42139.8}, {'field': 'amount', 'old_value': 42497.8, 'new_value': 42139.8}, {'field': 'count', 'old_value': 98, 'new_value': 97}, {'field': 'instoreAmount', 'old_value': 39379.8, 'new_value': 39021.8}, {'field': 'instoreCount', 'old_value': 77, 'new_value': 76}]
2025-05-23 08:06:08,065 - INFO - 更新表单数据成功: FINST-6IF66PC15AGVIEJ6CRRIA6I47JB32UKJ59PAMR2
2025-05-23 08:06:08,065 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_20250417, 变更字段: [{'field': 'recommendAmount', 'old_value': 32698.32, 'new_value': 31462.32}, {'field': 'amount', 'old_value': 32698.32, 'new_value': 31462.32}, {'field': 'count', 'old_value': 64, 'new_value': 62}, {'field': 'instoreAmount', 'old_value': 32787.6, 'new_value': 31551.6}, {'field': 'instoreCount', 'old_value': 57, 'new_value': 55}]
2025-05-23 08:06:08,596 - INFO - 更新表单数据成功: FINST-6IF66PC15AGVIEJ6CRRIA6I47JB32UKJ59PAMS2
2025-05-23 08:06:08,596 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_20250416, 变更字段: [{'field': 'recommendAmount', 'old_value': 35630.72, 'new_value': 34264.72}, {'field': 'amount', 'old_value': 35630.72, 'new_value': 34264.72}, {'field': 'count', 'old_value': 64, 'new_value': 63}, {'field': 'instoreAmount', 'old_value': 35110.1, 'new_value': 33744.1}, {'field': 'instoreCount', 'old_value': 57, 'new_value': 56}]
2025-05-23 08:06:09,112 - INFO - 更新表单数据成功: FINST-YJ86647149GVX8U27X3RLC641E713WNL79PAM35
2025-05-23 08:06:09,112 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 55972.28, 'new_value': 55674.28}, {'field': 'amount', 'old_value': 55972.28, 'new_value': 55674.28}, {'field': 'count', 'old_value': 87, 'new_value': 86}, {'field': 'instoreAmount', 'old_value': 53162.6, 'new_value': 52864.6}, {'field': 'instoreCount', 'old_value': 68, 'new_value': 67}]
2025-05-23 08:06:09,581 - INFO - 更新表单数据成功: FINST-GX9663D1W9GVID6SFHIH9B77OGVW3UBN69PAMY1
2025-05-23 08:06:09,581 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_20250422, 变更字段: [{'field': 'recommendAmount', 'old_value': 27966.15, 'new_value': 27468.15}, {'field': 'amount', 'old_value': 27966.15, 'new_value': 27468.15}, {'field': 'count', 'old_value': 63, 'new_value': 62}, {'field': 'instoreAmount', 'old_value': 26436.2, 'new_value': 25938.2}, {'field': 'instoreCount', 'old_value': 51, 'new_value': 50}]
2025-05-23 08:06:10,003 - INFO - 更新表单数据成功: FINST-7PF66CC1E3ZU7QJQEW19T6YC2UT43Y8SNBAAMLB1
2025-05-23 08:06:10,003 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B358872E0DBE420182AF77D4C47644F6_20250501, 变更字段: [{'field': 'recommendAmount', 'old_value': 15127.1, 'new_value': 15095.06}, {'field': 'amount', 'old_value': 15127.1, 'new_value': 15095.06}]
2025-05-23 08:06:10,456 - INFO - 更新表单数据成功: FINST-KLF66RD1Y5ZURETCC3I535JWYEFU3RLS5W8AM4F
2025-05-23 08:06:10,456 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_3F059827C9E04DEAA6B50797867EC52B_20250501, 变更字段: [{'field': 'amount', 'old_value': 12908.0, 'new_value': 12441.0}, {'field': 'count', 'old_value': 55, 'new_value': 54}, {'field': 'instoreAmount', 'old_value': 12908.0, 'new_value': 12441.0}, {'field': 'instoreCount', 'old_value': 55, 'new_value': 54}]
2025-05-23 08:06:10,893 - INFO - 更新表单数据成功: FINST-1OC66A911AGVN2VTA5ZPQ9RDVSAP3HMP89PAMS
2025-05-23 08:06:10,893 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 53839.98, 'new_value': 53501.98}, {'field': 'amount', 'old_value': 53839.98, 'new_value': 53501.98}, {'field': 'count', 'old_value': 90, 'new_value': 89}, {'field': 'instoreAmount', 'old_value': 51383.0, 'new_value': 51045.0}, {'field': 'instoreCount', 'old_value': 69, 'new_value': 68}]
2025-05-23 08:06:11,346 - INFO - 更新表单数据成功: FINST-YJ86647149GVX8U27X3RLC641E713WNL79PAMZ4
2025-05-23 08:06:11,346 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_20250430, 变更字段: [{'field': 'recommendAmount', 'old_value': 30706.46, 'new_value': 29250.46}, {'field': 'amount', 'old_value': 30706.46, 'new_value': 29250.46}, {'field': 'count', 'old_value': 67, 'new_value': 64}, {'field': 'instoreAmount', 'old_value': 28282.4, 'new_value': 26826.4}, {'field': 'instoreCount', 'old_value': 51, 'new_value': 48}]
2025-05-23 08:06:11,721 - INFO - 更新表单数据成功: FINST-YJ86647149GVX8U27X3RLC641E713WNL79PAM15
2025-05-23 08:06:11,721 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_20250428, 变更字段: [{'field': 'recommendAmount', 'old_value': 17465.69, 'new_value': 16629.69}, {'field': 'amount', 'old_value': 17465.69, 'new_value': 16629.69}, {'field': 'count', 'old_value': 50, 'new_value': 48}, {'field': 'instoreAmount', 'old_value': 16736.0, 'new_value': 15900.0}, {'field': 'instoreCount', 'old_value': 43, 'new_value': 41}]
2025-05-23 08:06:12,174 - INFO - 更新表单数据成功: FINST-1OC66A91MWFVNQ10EE1LFCM14YOP3JGX89PAMW3
2025-05-23 08:06:12,174 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 8105.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 8105.0}]
2025-05-23 08:06:12,690 - INFO - 更新表单数据成功: FINST-YJ866471LDLVL4CJEGQLZ3PFGBGU3C5HN6XAM4E
2025-05-23 08:06:12,690 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_20250509, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 7811.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 7811.0}]
2025-05-23 08:06:13,128 - INFO - 更新表单数据成功: FINST-FPB66VB12VJVDZD48AQE8872V7N63TV27RVAMAE
2025-05-23 08:06:13,128 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_20250508, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 8222.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 8222.0}]
2025-05-23 08:06:13,643 - INFO - 更新表单数据成功: FINST-W4G66DA1CYFVU7R6E7XYM9LI10RK3YD599PAMK3
2025-05-23 08:06:13,643 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_********, 变更字段: [{'field': 'amount', 'old_value': 42761.9, 'new_value': 42931.9}, {'field': 'count', 'old_value': 298, 'new_value': 299}, {'field': 'instoreAmount', 'old_value': 21410.9, 'new_value': 21580.9}, {'field': 'instoreCount', 'old_value': 106, 'new_value': 107}]
2025-05-23 08:06:14,143 - INFO - 更新表单数据成功: FINST-OIF66BA14AGVGWDY676ZT4Y2NTKM2ZYM89PAM83
2025-05-23 08:06:14,143 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_20250509, 变更字段: [{'field': 'recommendAmount', 'old_value': 16070.84, 'new_value': 15732.84}, {'field': 'amount', 'old_value': 16070.84, 'new_value': 15732.84}, {'field': 'count', 'old_value': 49, 'new_value': 48}, {'field': 'instoreAmount', 'old_value': 15034.1, 'new_value': 14696.1}, {'field': 'instoreCount', 'old_value': 37, 'new_value': 36}]
2025-05-23 08:06:14,690 - INFO - 更新表单数据成功: FINST-1OC66A911AGVN2VTA5ZPQ9RDVSAP3HMP89PAMP
2025-05-23 08:06:14,690 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_20250506, 变更字段: [{'field': 'recommendAmount', 'old_value': 17684.98, 'new_value': 14376.98}, {'field': 'amount', 'old_value': 17684.98, 'new_value': 14376.98}, {'field': 'count', 'old_value': 39, 'new_value': 38}, {'field': 'instoreAmount', 'old_value': 16501.0, 'new_value': 13193.0}, {'field': 'instoreCount', 'old_value': 26, 'new_value': 25}]
2025-05-23 08:06:15,128 - INFO - 更新表单数据成功: FINST-OIF66RB1WBHVN0LD9Q95I9QQL12W3LZMAWSAM1L
2025-05-23 08:06:15,128 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 9350.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 9350.0}]
2025-05-23 08:06:15,596 - INFO - 更新表单数据成功: FINST-NU966I81DCHVHQKKAL3N2D0K20162D65UGRAMA7
2025-05-23 08:06:15,596 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_20250516, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 8178.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 8178.0}]
2025-05-23 08:06:16,081 - INFO - 更新表单数据成功: FINST-LR5668B10BGVQHUZCR3SE4OP310X20MAD1QAML1
2025-05-23 08:06:16,081 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_20250515, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 1772.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 1772.0}]
2025-05-23 08:06:16,534 - INFO - 更新表单数据成功: FINST-1OC66A91MWFVNQ10EE1LFCM14YOP3JGX89PAMS3
2025-05-23 08:06:16,549 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_20250514, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 4971.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 4971.0}]
2025-05-23 08:06:16,971 - INFO - 更新表单数据成功: FINST-1OC66A91MWFVNQ10EE1LFCM14YOP3JGX89PAMT3
2025-05-23 08:06:16,971 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_20250513, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 12302.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 12302.0}]
2025-05-23 08:06:17,409 - INFO - 更新表单数据成功: FINST-1OC66A91MWFVNQ10EE1LFCM14YOP3JGX89PAMU3
2025-05-23 08:06:17,409 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_20250512, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 5790.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 5790.0}]
2025-05-23 08:06:17,862 - INFO - 更新表单数据成功: FINST-FD966QA18DKVI6ZBDY9RGAQR61J93AGD7RVAMI8
2025-05-23 08:06:17,862 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_********, 变更字段: [{'field': 'amount', 'old_value': 39410.55, 'new_value': 39638.55}, {'field': 'count', 'old_value': 275, 'new_value': 276}, {'field': 'instoreAmount', 'old_value': 23540.4, 'new_value': 23768.4}, {'field': 'instoreCount', 'old_value': 130, 'new_value': 131}]
2025-05-23 08:06:18,331 - INFO - 更新表单数据成功: FINST-A17661C1HFHVUTF77C3YSA9LPDJ02N3DUGRAMC5
2025-05-23 08:06:18,331 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_20250516, 变更字段: [{'field': 'recommendAmount', 'old_value': 16559.83, 'new_value': 16061.83}, {'field': 'amount', 'old_value': 16559.83, 'new_value': 16061.83}, {'field': 'count', 'old_value': 56, 'new_value': 55}, {'field': 'instoreAmount', 'old_value': 14448.0, 'new_value': 13950.0}, {'field': 'instoreCount', 'old_value': 41, 'new_value': 40}]
2025-05-23 08:06:18,768 - INFO - 更新表单数据成功: FINST-XL866HB1NAGVD1TYEI9H79REHI3F3TWF99PAM06
2025-05-23 08:06:18,768 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_20250512, 变更字段: [{'field': 'recommendAmount', 'old_value': 11753.34, 'new_value': 11015.34}, {'field': 'amount', 'old_value': 11753.34, 'new_value': 11015.34}, {'field': 'count', 'old_value': 39, 'new_value': 38}, {'field': 'instoreAmount', 'old_value': 11090.0, 'new_value': 10352.0}, {'field': 'instoreCount', 'old_value': 36, 'new_value': 35}]
2025-05-23 08:06:19,221 - INFO - 更新表单数据成功: FINST-COC668A1LALVU8SI901F1DM7SSF23DEU3MYAMT7
2025-05-23 08:06:19,221 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_20250521, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 3608.7}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 3608.7}]
2025-05-23 08:06:19,674 - INFO - 更新表单数据成功: FINST-XBF66071SBMVMRJKFB1RH6AFE9QZ1O2X3MYAM7A
2025-05-23 08:06:19,674 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_20250521, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 3262.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 3262.0}]
2025-05-23 08:06:20,174 - INFO - 更新表单数据成功: FINST-B2766R81LBLVS8FVCIXSE88YWGI5360PN6XAMYF
2025-05-23 08:06:20,174 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_20250520, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 6507.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 6507.0}]
2025-05-23 08:06:20,659 - INFO - 更新表单数据成功: FINST-8LC66GC10EKVH1QRFQ06H980BXSM2ITA7RVAMK1
2025-05-23 08:06:20,659 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_20250519, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 4116.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 4116.0}]
2025-05-23 08:06:21,362 - INFO - 更新表单数据成功: FINST-XBF66071SBMVMRJKFB1RH6AFE9QZ1P2X3MYAMQA
2025-05-23 08:06:21,362 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_20250521, 变更字段: [{'field': 'amount', 'old_value': 1079.9, 'new_value': 1188.9}, {'field': 'count', 'old_value': 3, 'new_value': 5}, {'field': 'instoreAmount', 'old_value': 1079.9, 'new_value': 1188.9}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 5}]
2025-05-23 08:06:21,893 - INFO - 更新表单数据成功: FINST-X0G66U81YEHVDNKYEIQ55DT3P0S92ORZ3MYAMMS
2025-05-23 08:06:21,893 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_20250519, 变更字段: [{'field': 'recommendAmount', 'old_value': 4814.76, 'new_value': 4819.66}, {'field': 'amount', 'old_value': 4814.759999999999, 'new_value': 4819.66}, {'field': 'count', 'old_value': 226, 'new_value': 227}, {'field': 'onlineAmount', 'old_value': 3639.01, 'new_value': 3643.91}, {'field': 'onlineCount', 'old_value': 169, 'new_value': 170}]
2025-05-23 08:06:22,299 - INFO - 更新表单数据成功: FINST-X0G66U81YEHVDNKYEIQ55DT3P0S92PRZ3MYAMWS
2025-05-23 08:06:22,299 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_20250521, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 13537.53}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 13537.53}]
2025-05-23 08:06:22,752 - INFO - 更新表单数据成功: FINST-LLF66J71Q4NVCL9C6OFDU6A1JF0Z2KQYQUYAMN1
2025-05-23 08:06:22,752 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_20250520, 变更字段: [{'field': 'instoreCount', 'old_value': 374, 'new_value': 375}, {'field': 'onlineCount', 'old_value': 136, 'new_value': 135}]
2025-05-23 08:06:23,206 - INFO - 更新表单数据成功: FINST-LLF66J71Q4NVCL9C6OFDU6A1JF0Z2KQYQUYAMZ1
2025-05-23 08:06:23,206 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1I14PQ9FMDG1FG3MDGKU4P0I8N00178C_20250520, 变更字段: [{'field': 'recommendAmount', 'old_value': 14210.63, 'new_value': 14221.03}, {'field': 'amount', 'old_value': 14210.63, 'new_value': 14221.029999999999}, {'field': 'count', 'old_value': 584, 'new_value': 585}, {'field': 'onlineAmount', 'old_value': 14449.07, 'new_value': 14459.47}, {'field': 'onlineCount', 'old_value': 584, 'new_value': 585}]
2025-05-23 08:06:23,690 - INFO - 更新表单数据成功: FINST-LLF66J71Q4NVCL9C6OFDU6A1JF0Z2KQYQUYAM02
2025-05-23 08:06:23,690 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1I14PQ9FMDG1FG3MDGKU4P0I8N00178C_20250519, 变更字段: [{'field': 'recommendAmount', 'old_value': 660.51, 'new_value': 668.91}, {'field': 'amount', 'old_value': 660.51, 'new_value': 668.91}, {'field': 'count', 'old_value': 42, 'new_value': 43}, {'field': 'onlineAmount', 'old_value': 673.51, 'new_value': 681.91}, {'field': 'onlineCount', 'old_value': 42, 'new_value': 43}]
2025-05-23 08:06:24,143 - INFO - 更新表单数据成功: FINST-VOC66Y91P2LVA2IQB1IBS4GZB5B624H24MYAMQR
2025-05-23 08:06:24,143 - INFO - 更新日销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ_20250521, 变更字段: [{'field': 'recommendAmount', 'old_value': 23195.65, 'new_value': 23251.1}, {'field': 'dailyBillAmount', 'old_value': 23195.65, 'new_value': 23251.1}]
2025-05-23 08:06:24,565 - INFO - 更新表单数据成功: FINST-1OC66A91A9MVE2TH6IGV36HCBWIH2K254MYAM5C
2025-05-23 08:06:24,565 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_20250521, 变更字段: [{'field': 'amount', 'old_value': 40137.21000000001, 'new_value': 40160.100000000006}, {'field': 'count', 'old_value': 129, 'new_value': 130}, {'field': 'instoreAmount', 'old_value': 39602.21, 'new_value': 39625.1}, {'field': 'instoreCount', 'old_value': 87, 'new_value': 88}]
2025-05-23 08:06:24,987 - INFO - 更新表单数据成功: FINST-1OC66A91A9MVE2TH6IGV36HCBWIH2K254MYAMSC
2025-05-23 08:06:24,987 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_20250521, 变更字段: [{'field': 'recommendAmount', 'old_value': 23443.54, 'new_value': 22779.54}, {'field': 'amount', 'old_value': 23443.54, 'new_value': 22779.54}, {'field': 'count', 'old_value': 62, 'new_value': 60}, {'field': 'instoreAmount', 'old_value': 21954.0, 'new_value': 21290.0}, {'field': 'instoreCount', 'old_value': 49, 'new_value': 47}]
2025-05-23 08:06:25,424 - INFO - 更新表单数据成功: FINST-KLF66WC1WCKVPOSACQFQZB4VN5ZK23QI7RVAMDC
2025-05-23 08:06:25,424 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_20250519, 变更字段: [{'field': 'recommendAmount', 'old_value': 15461.98, 'new_value': 15013.98}, {'field': 'amount', 'old_value': 15461.98, 'new_value': 15013.98}, {'field': 'count', 'old_value': 40, 'new_value': 39}, {'field': 'instoreAmount', 'old_value': 14132.0, 'new_value': 13684.0}, {'field': 'instoreCount', 'old_value': 30, 'new_value': 29}]
2025-05-23 08:06:25,502 - INFO - 正在批量插入每日数据，批次 1/22，共 100 条记录
2025-05-23 08:06:25,893 - INFO - 批量插入每日数据成功，批次 1，100 条记录
2025-05-23 08:06:28,909 - INFO - 正在批量插入每日数据，批次 2/22，共 100 条记录
2025-05-23 08:06:29,362 - INFO - 批量插入每日数据成功，批次 2，100 条记录
2025-05-23 08:06:32,377 - INFO - 正在批量插入每日数据，批次 3/22，共 100 条记录
2025-05-23 08:06:32,815 - INFO - 批量插入每日数据成功，批次 3，100 条记录
2025-05-23 08:06:35,831 - INFO - 正在批量插入每日数据，批次 4/22，共 100 条记录
2025-05-23 08:06:36,393 - INFO - 批量插入每日数据成功，批次 4，100 条记录
2025-05-23 08:06:39,409 - INFO - 正在批量插入每日数据，批次 5/22，共 100 条记录
2025-05-23 08:06:39,846 - INFO - 批量插入每日数据成功，批次 5，100 条记录
2025-05-23 08:06:42,862 - INFO - 正在批量插入每日数据，批次 6/22，共 100 条记录
2025-05-23 08:06:43,252 - INFO - 批量插入每日数据成功，批次 6，100 条记录
2025-05-23 08:06:46,268 - INFO - 正在批量插入每日数据，批次 7/22，共 100 条记录
2025-05-23 08:06:46,721 - INFO - 批量插入每日数据成功，批次 7，100 条记录
2025-05-23 08:06:49,737 - INFO - 正在批量插入每日数据，批次 8/22，共 100 条记录
2025-05-23 08:06:50,159 - INFO - 批量插入每日数据成功，批次 8，100 条记录
2025-05-23 08:06:53,174 - INFO - 正在批量插入每日数据，批次 9/22，共 100 条记录
2025-05-23 08:06:53,596 - INFO - 批量插入每日数据成功，批次 9，100 条记录
2025-05-23 08:06:56,612 - INFO - 正在批量插入每日数据，批次 10/22，共 100 条记录
2025-05-23 08:06:57,034 - INFO - 批量插入每日数据成功，批次 10，100 条记录
2025-05-23 08:07:00,049 - INFO - 正在批量插入每日数据，批次 11/22，共 100 条记录
2025-05-23 08:07:00,424 - INFO - 批量插入每日数据成功，批次 11，100 条记录
2025-05-23 08:07:03,440 - INFO - 正在批量插入每日数据，批次 12/22，共 100 条记录
2025-05-23 08:07:03,846 - INFO - 批量插入每日数据成功，批次 12，100 条记录
2025-05-23 08:07:06,862 - INFO - 正在批量插入每日数据，批次 13/22，共 100 条记录
2025-05-23 08:07:07,393 - INFO - 批量插入每日数据成功，批次 13，100 条记录
2025-05-23 08:07:10,408 - INFO - 正在批量插入每日数据，批次 14/22，共 100 条记录
2025-05-23 08:07:10,877 - INFO - 批量插入每日数据成功，批次 14，100 条记录
2025-05-23 08:07:13,893 - INFO - 正在批量插入每日数据，批次 15/22，共 100 条记录
2025-05-23 08:07:14,268 - INFO - 批量插入每日数据成功，批次 15，100 条记录
2025-05-23 08:07:17,283 - INFO - 正在批量插入每日数据，批次 16/22，共 100 条记录
2025-05-23 08:07:17,737 - INFO - 批量插入每日数据成功，批次 16，100 条记录
2025-05-23 08:07:20,752 - INFO - 正在批量插入每日数据，批次 17/22，共 100 条记录
2025-05-23 08:07:21,127 - INFO - 批量插入每日数据成功，批次 17，100 条记录
2025-05-23 08:07:24,143 - INFO - 正在批量插入每日数据，批次 18/22，共 100 条记录
2025-05-23 08:07:24,596 - INFO - 批量插入每日数据成功，批次 18，100 条记录
2025-05-23 08:07:27,611 - INFO - 正在批量插入每日数据，批次 19/22，共 100 条记录
2025-05-23 08:07:27,986 - INFO - 批量插入每日数据成功，批次 19，100 条记录
2025-05-23 08:07:31,002 - INFO - 正在批量插入每日数据，批次 20/22，共 100 条记录
2025-05-23 08:07:31,393 - INFO - 批量插入每日数据成功，批次 20，100 条记录
2025-05-23 08:07:34,408 - INFO - 正在批量插入每日数据，批次 21/22，共 100 条记录
2025-05-23 08:07:34,877 - INFO - 批量插入每日数据成功，批次 21，100 条记录
2025-05-23 08:07:37,893 - INFO - 正在批量插入每日数据，批次 22/22，共 95 条记录
2025-05-23 08:07:38,330 - INFO - 批量插入每日数据成功，批次 22，95 条记录
2025-05-23 08:07:41,346 - INFO - 批量插入每日数据完成: 总计 2195 条，成功 2195 条，失败 0 条
2025-05-23 08:07:41,346 - INFO - 批量插入日销售数据完成，共 2195 条记录
2025-05-23 08:07:41,346 - INFO - 日销售数据同步完成！更新: 45 条，插入: 2195 条，错误: 0 条，跳过: 10820 条
2025-05-23 08:07:41,346 - INFO - 正在获取宜搭月销售表单数据...
2025-05-23 08:07:41,346 - INFO - 开始获取宜搭月度表单数据，总时间范围: 2024-05-01 至 2025-05-31
2025-05-23 08:07:41,346 - INFO - 查询月度分段 1: 2024-05-01 至 2024-07-31
2025-05-23 08:07:41,346 - INFO - 查询日期范围: 2024-05-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-05-23 08:07:41,346 - INFO - Request Parameters - Page 1:
2025-05-23 08:07:41,346 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:07:41,346 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1714492800000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:07:42,064 - INFO - API请求耗时: 719ms
2025-05-23 08:07:42,064 - INFO - Response - Page 1
2025-05-23 08:07:42,064 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-23 08:07:42,064 - INFO - 查询完成，共获取到 0 条记录
2025-05-23 08:07:42,064 - WARNING - 月度分段 1 查询返回空数据
2025-05-23 08:07:42,064 - INFO - 尝试将月度分段 1 再细分为单月查询
2025-05-23 08:07:42,064 - INFO - 查询日期范围: 2024-05-01 至 2024-05-31，使用分页查询，每页 100 条记录
2025-05-23 08:07:42,064 - INFO - Request Parameters - Page 1:
2025-05-23 08:07:42,064 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:07:42,064 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1714492800000, 1717084800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:07:42,283 - INFO - API请求耗时: 219ms
2025-05-23 08:07:42,283 - INFO - Response - Page 1
2025-05-23 08:07:42,283 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-23 08:07:42,283 - INFO - 查询完成，共获取到 0 条记录
2025-05-23 08:07:42,283 - WARNING - 单月查询返回空数据: 2024-05
2025-05-23 08:07:42,799 - INFO - 查询日期范围: 2024-06-01 至 2024-06-30，使用分页查询，每页 100 条记录
2025-05-23 08:07:42,799 - INFO - Request Parameters - Page 1:
2025-05-23 08:07:42,799 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:07:42,799 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1717171200000, 1719676800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:07:42,986 - INFO - API请求耗时: 187ms
2025-05-23 08:07:42,986 - INFO - Response - Page 1
2025-05-23 08:07:42,986 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-23 08:07:42,986 - INFO - 查询完成，共获取到 0 条记录
2025-05-23 08:07:43,002 - WARNING - 单月查询返回空数据: 2024-06
2025-05-23 08:07:43,518 - INFO - 查询日期范围: 2024-07-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-05-23 08:07:43,518 - INFO - Request Parameters - Page 1:
2025-05-23 08:07:43,518 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:07:43,518 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1719763200000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:07:43,721 - INFO - API请求耗时: 203ms
2025-05-23 08:07:43,721 - INFO - Response - Page 1
2025-05-23 08:07:43,721 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-23 08:07:43,721 - INFO - 查询完成，共获取到 0 条记录
2025-05-23 08:07:43,721 - WARNING - 单月查询返回空数据: 2024-07
2025-05-23 08:07:45,236 - INFO - 查询月度分段 2: 2024-08-01 至 2024-10-31
2025-05-23 08:07:45,236 - INFO - 查询日期范围: 2024-08-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-05-23 08:07:45,236 - INFO - Request Parameters - Page 1:
2025-05-23 08:07:45,236 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:07:45,236 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:07:45,455 - INFO - API请求耗时: 219ms
2025-05-23 08:07:45,455 - INFO - Response - Page 1
2025-05-23 08:07:45,455 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-23 08:07:45,455 - INFO - 查询完成，共获取到 0 条记录
2025-05-23 08:07:45,455 - WARNING - 月度分段 2 查询返回空数据
2025-05-23 08:07:45,455 - INFO - 尝试将月度分段 2 再细分为单月查询
2025-05-23 08:07:45,455 - INFO - 查询日期范围: 2024-08-01 至 2024-08-31，使用分页查询，每页 100 条记录
2025-05-23 08:07:45,455 - INFO - Request Parameters - Page 1:
2025-05-23 08:07:45,455 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:07:45,455 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1725033600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:07:45,674 - INFO - API请求耗时: 219ms
2025-05-23 08:07:45,674 - INFO - Response - Page 1
2025-05-23 08:07:45,674 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-23 08:07:45,674 - INFO - 查询完成，共获取到 0 条记录
2025-05-23 08:07:45,674 - WARNING - 单月查询返回空数据: 2024-08
2025-05-23 08:07:46,174 - INFO - 查询日期范围: 2024-09-01 至 2024-09-30，使用分页查询，每页 100 条记录
2025-05-23 08:07:46,174 - INFO - Request Parameters - Page 1:
2025-05-23 08:07:46,174 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:07:46,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1725120000000, 1727625600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:07:46,486 - INFO - API请求耗时: 312ms
2025-05-23 08:07:46,486 - INFO - Response - Page 1
2025-05-23 08:07:46,486 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-23 08:07:46,486 - INFO - 查询完成，共获取到 0 条记录
2025-05-23 08:07:46,486 - WARNING - 单月查询返回空数据: 2024-09
2025-05-23 08:07:46,986 - INFO - 查询日期范围: 2024-10-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-05-23 08:07:46,986 - INFO - Request Parameters - Page 1:
2025-05-23 08:07:46,986 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:07:46,986 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1727712000000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:07:47,205 - INFO - API请求耗时: 219ms
2025-05-23 08:07:47,205 - INFO - Response - Page 1
2025-05-23 08:07:47,205 - INFO - 第 1 页没有数据，已到达最后一页
2025-05-23 08:07:47,205 - INFO - 查询完成，共获取到 0 条记录
2025-05-23 08:07:47,205 - WARNING - 单月查询返回空数据: 2024-10
2025-05-23 08:07:48,736 - INFO - 查询月度分段 3: 2024-11-01 至 2025-01-31
2025-05-23 08:07:48,736 - INFO - 查询日期范围: 2024-11-01 至 2025-01-31，使用分页查询，每页 100 条记录
2025-05-23 08:07:48,736 - INFO - Request Parameters - Page 1:
2025-05-23 08:07:48,736 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:07:48,736 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:07:49,361 - INFO - API请求耗时: 625ms
2025-05-23 08:07:49,377 - INFO - Response - Page 1
2025-05-23 08:07:49,377 - INFO - 第 1 页获取到 100 条记录
2025-05-23 08:07:49,893 - INFO - Request Parameters - Page 2:
2025-05-23 08:07:49,893 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:07:49,893 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:07:50,471 - INFO - API请求耗时: 578ms
2025-05-23 08:07:50,471 - INFO - Response - Page 2
2025-05-23 08:07:50,471 - INFO - 第 2 页获取到 100 条记录
2025-05-23 08:07:50,971 - INFO - Request Parameters - Page 3:
2025-05-23 08:07:50,971 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:07:50,971 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1738252800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:07:51,346 - INFO - API请求耗时: 375ms
2025-05-23 08:07:51,346 - INFO - Response - Page 3
2025-05-23 08:07:51,361 - INFO - 第 3 页获取到 48 条记录
2025-05-23 08:07:51,361 - INFO - 查询完成，共获取到 248 条记录
2025-05-23 08:07:51,361 - INFO - 月度分段 3 查询成功，获取到 248 条记录
2025-05-23 08:07:52,377 - INFO - 查询月度分段 4: 2025-02-01 至 2025-04-30
2025-05-23 08:07:52,377 - INFO - 查询日期范围: 2025-02-01 至 2025-04-30，使用分页查询，每页 100 条记录
2025-05-23 08:07:52,377 - INFO - Request Parameters - Page 1:
2025-05-23 08:07:52,377 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:07:52,377 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:07:52,939 - INFO - API请求耗时: 563ms
2025-05-23 08:07:52,939 - INFO - Response - Page 1
2025-05-23 08:07:52,939 - INFO - 第 1 页获取到 100 条记录
2025-05-23 08:07:53,455 - INFO - Request Parameters - Page 2:
2025-05-23 08:07:53,455 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:07:53,455 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:07:53,986 - INFO - API请求耗时: 531ms
2025-05-23 08:07:53,986 - INFO - Response - Page 2
2025-05-23 08:07:53,986 - INFO - 第 2 页获取到 100 条记录
2025-05-23 08:07:54,502 - INFO - Request Parameters - Page 3:
2025-05-23 08:07:54,502 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:07:54,502 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:07:55,111 - INFO - API请求耗时: 609ms
2025-05-23 08:07:55,111 - INFO - Response - Page 3
2025-05-23 08:07:55,111 - INFO - 第 3 页获取到 100 条记录
2025-05-23 08:07:55,611 - INFO - Request Parameters - Page 4:
2025-05-23 08:07:55,611 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:07:55,611 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:07:56,174 - INFO - API请求耗时: 562ms
2025-05-23 08:07:56,174 - INFO - Response - Page 4
2025-05-23 08:07:56,174 - INFO - 第 4 页获取到 100 条记录
2025-05-23 08:07:56,689 - INFO - Request Parameters - Page 5:
2025-05-23 08:07:56,689 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:07:56,689 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:07:57,221 - INFO - API请求耗时: 531ms
2025-05-23 08:07:57,221 - INFO - Response - Page 5
2025-05-23 08:07:57,221 - INFO - 第 5 页获取到 100 条记录
2025-05-23 08:07:57,721 - INFO - Request Parameters - Page 6:
2025-05-23 08:07:57,721 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:07:57,721 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:07:58,486 - INFO - API请求耗时: 766ms
2025-05-23 08:07:58,486 - INFO - Response - Page 6
2025-05-23 08:07:58,486 - INFO - 第 6 页获取到 100 条记录
2025-05-23 08:07:59,002 - INFO - Request Parameters - Page 7:
2025-05-23 08:07:59,002 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:07:59,002 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:07:59,533 - INFO - API请求耗时: 531ms
2025-05-23 08:07:59,533 - INFO - Response - Page 7
2025-05-23 08:07:59,533 - INFO - 第 7 页获取到 100 条记录
2025-05-23 08:08:00,049 - INFO - Request Parameters - Page 8:
2025-05-23 08:08:00,049 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:08:00,049 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738339200000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:08:00,377 - INFO - API请求耗时: 328ms
2025-05-23 08:08:00,377 - INFO - Response - Page 8
2025-05-23 08:08:00,377 - INFO - 第 8 页获取到 16 条记录
2025-05-23 08:08:00,377 - INFO - 查询完成，共获取到 716 条记录
2025-05-23 08:08:00,377 - INFO - 月度分段 4 查询成功，获取到 716 条记录
2025-05-23 08:08:01,377 - INFO - 查询月度分段 5: 2025-05-01 至 2025-05-31
2025-05-23 08:08:01,377 - INFO - 查询日期范围: 2025-05-01 至 2025-05-31，使用分页查询，每页 100 条记录
2025-05-23 08:08:01,377 - INFO - Request Parameters - Page 1:
2025-05-23 08:08:01,377 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:08:01,377 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:08:01,924 - INFO - API请求耗时: 547ms
2025-05-23 08:08:01,924 - INFO - Response - Page 1
2025-05-23 08:08:01,924 - INFO - 第 1 页获取到 100 条记录
2025-05-23 08:08:02,424 - INFO - Request Parameters - Page 2:
2025-05-23 08:08:02,424 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:08:02,424 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:08:03,064 - INFO - API请求耗时: 641ms
2025-05-23 08:08:03,064 - INFO - Response - Page 2
2025-05-23 08:08:03,064 - INFO - 第 2 页获取到 100 条记录
2025-05-23 08:08:03,564 - INFO - Request Parameters - Page 3:
2025-05-23 08:08:03,564 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:08:03,564 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:08:03,939 - INFO - API请求耗时: 375ms
2025-05-23 08:08:03,939 - INFO - Response - Page 3
2025-05-23 08:08:03,939 - INFO - 第 3 页获取到 24 条记录
2025-05-23 08:08:03,939 - INFO - 查询完成，共获取到 224 条记录
2025-05-23 08:08:03,939 - INFO - 月度分段 5 查询成功，获取到 224 条记录
2025-05-23 08:08:04,955 - INFO - 宜搭月度表单数据查询完成，共 5 个分段，成功获取 1188 条记录，失败 0 次
2025-05-23 08:08:04,955 - INFO - 成功获取宜搭月销售表单数据，共 1188 条记录
2025-05-23 08:08:04,955 - INFO - 宜搭月销售数据项结构示例: ['formInstanceId', 'formData']
2025-05-23 08:08:04,955 - INFO - 正在从SQLite获取月度汇总数据...
2025-05-23 08:08:04,955 - INFO - 成功获取SQLite月度汇总数据，共 1192 条记录
2025-05-23 08:08:05,017 - INFO - 成功创建宜搭月销售数据索引，共 1188 条记录
2025-05-23 08:08:05,486 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMKJ
2025-05-23 08:08:05,486 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MO2IE70S367Q2OVAE57DLH001Q7I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 146840.3, 'new_value': 151357.69}, {'field': 'dailyBillAmount', 'old_value': 146840.3, 'new_value': 151357.69}, {'field': 'amount', 'old_value': 4493.4, 'new_value': 4548.3}, {'field': 'count', 'old_value': 63, 'new_value': 64}, {'field': 'onlineAmount', 'old_value': 4569.4, 'new_value': 4624.3}, {'field': 'onlineCount', 'old_value': 63, 'new_value': 64}]
2025-05-23 08:08:05,908 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMLJ
2025-05-23 08:08:05,908 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 384115.03, 'new_value': 396995.25}, {'field': 'dailyBillAmount', 'old_value': 384115.03, 'new_value': 396995.25}, {'field': 'amount', 'old_value': 212750.1, 'new_value': 219034.6}, {'field': 'count', 'old_value': 1985, 'new_value': 2056}, {'field': 'instoreAmount', 'old_value': 87821.4, 'new_value': 89809.2}, {'field': 'instoreCount', 'old_value': 670, 'new_value': 688}, {'field': 'onlineAmount', 'old_value': 125281.1, 'new_value': 129577.8}, {'field': 'onlineCount', 'old_value': 1315, 'new_value': 1368}]
2025-05-23 08:08:06,392 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMNJ
2025-05-23 08:08:06,392 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 568747.84, 'new_value': 592810.84}, {'field': 'dailyBillAmount', 'old_value': 568747.84, 'new_value': 592810.84}, {'field': 'amount', 'old_value': 414489.54, 'new_value': 430425.25}, {'field': 'count', 'old_value': 1994, 'new_value': 2067}, {'field': 'instoreAmount', 'old_value': 414489.54, 'new_value': 430425.25}, {'field': 'instoreCount', 'old_value': 1994, 'new_value': 2067}]
2025-05-23 08:08:06,877 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMOJ
2025-05-23 08:08:06,877 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 450866.43, 'new_value': 463316.18}, {'field': 'dailyBillAmount', 'old_value': 450866.43, 'new_value': 463316.18}, {'field': 'amount', 'old_value': 733789.0, 'new_value': 768151.0}, {'field': 'count', 'old_value': 2520, 'new_value': 2632}, {'field': 'instoreAmount', 'old_value': 735039.0, 'new_value': 769401.0}, {'field': 'instoreCount', 'old_value': 2520, 'new_value': 2632}]
2025-05-23 08:08:07,314 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMPJ
2025-05-23 08:08:07,314 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 54792.6, 'new_value': 57385.4}, {'field': 'dailyBillAmount', 'old_value': 54792.6, 'new_value': 57385.4}, {'field': 'amount', 'old_value': 72695.21, 'new_value': 75880.71}, {'field': 'count', 'old_value': 251, 'new_value': 267}, {'field': 'instoreAmount', 'old_value': 40956.9, 'new_value': 42254.9}, {'field': 'instoreCount', 'old_value': 39, 'new_value': 40}, {'field': 'onlineAmount', 'old_value': 35835.22, 'new_value': 37722.72}, {'field': 'onlineCount', 'old_value': 212, 'new_value': 227}]
2025-05-23 08:08:07,767 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMQJ
2025-05-23 08:08:07,767 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GPFAB4PUHT7OL7Q2OV4FVC7US001R67_2025-05, 变更字段: [{'field': 'count', 'old_value': 113, 'new_value': 118}, {'field': 'instoreCount', 'old_value': 113, 'new_value': 118}]
2025-05-23 08:08:08,111 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMRJ
2025-05-23 08:08:08,111 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 616142.59, 'new_value': 640755.34}, {'field': 'dailyBillAmount', 'old_value': 616142.59, 'new_value': 640755.34}, {'field': 'amount', 'old_value': 567486.75, 'new_value': 584377.65}, {'field': 'count', 'old_value': 4071, 'new_value': 4204}, {'field': 'instoreAmount', 'old_value': 460729.81, 'new_value': 475374.41000000003}, {'field': 'instoreCount', 'old_value': 1985, 'new_value': 2057}, {'field': 'onlineAmount', 'old_value': 110439.17, 'new_value': 112757.17}, {'field': 'onlineCount', 'old_value': 2086, 'new_value': 2147}]
2025-05-23 08:08:08,533 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMTJ
2025-05-23 08:08:08,533 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1I14PQ9FMDG1FG3MDGKU4P0I8N00178C_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 220073.97, 'new_value': 228325.2}, {'field': 'amount', 'old_value': 220064.6, 'new_value': 228316.03}, {'field': 'count', 'old_value': 9102, 'new_value': 9428}, {'field': 'onlineAmount', 'old_value': 224833.95, 'new_value': 233715.28}, {'field': 'onlineCount', 'old_value': 9102, 'new_value': 9428}]
2025-05-23 08:08:08,908 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMUJ
2025-05-23 08:08:08,908 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_3F059827C9E04DEAA6B50797867EC52B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 621996.53, 'new_value': 647689.51}, {'field': 'dailyBillAmount', 'old_value': 621996.53, 'new_value': 647689.51}, {'field': 'amount', 'old_value': 157378.17, 'new_value': 168948.43}, {'field': 'count', 'old_value': 877, 'new_value': 966}, {'field': 'instoreAmount', 'old_value': 157378.17, 'new_value': 168948.43}, {'field': 'instoreCount', 'old_value': 877, 'new_value': 966}]
2025-05-23 08:08:09,392 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMVJ
2025-05-23 08:08:09,392 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINPMIJNMLUJ7Q2OV392410G00148C_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 444803.16, 'new_value': 462346.71}, {'field': 'dailyBillAmount', 'old_value': 444803.16, 'new_value': 462346.71}, {'field': 'amount', 'old_value': 23876.0, 'new_value': 25614.0}, {'field': 'count', 'old_value': 32, 'new_value': 34}, {'field': 'instoreAmount', 'old_value': 23876.0, 'new_value': 25614.0}, {'field': 'instoreCount', 'old_value': 32, 'new_value': 34}]
2025-05-23 08:08:09,830 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMWJ
2025-05-23 08:08:09,830 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_2025-05, 变更字段: [{'field': 'amount', 'old_value': 92439.2, 'new_value': 93957.3}, {'field': 'count', 'old_value': 258, 'new_value': 266}, {'field': 'instoreAmount', 'old_value': 92440.0, 'new_value': 93959.0}, {'field': 'instoreCount', 'old_value': 258, 'new_value': 266}]
2025-05-23 08:08:10,299 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYJ
2025-05-23 08:08:10,299 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 860035.66, 'new_value': 878332.13}, {'field': 'dailyBillAmount', 'old_value': 860035.66, 'new_value': 878332.13}, {'field': 'amount', 'old_value': -325637.91000000003, 'new_value': -336567.21}, {'field': 'count', 'old_value': 906, 'new_value': 923}, {'field': 'instoreAmount', 'old_value': 554274.99, 'new_value': 563563.91}, {'field': 'instoreCount', 'old_value': 906, 'new_value': 923}]
2025-05-23 08:08:10,767 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZJ
2025-05-23 08:08:10,767 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 332345.0, 'new_value': 345415.0}, {'field': 'amount', 'old_value': 332345.0, 'new_value': 345415.0}, {'field': 'count', 'old_value': 1161, 'new_value': 1201}, {'field': 'instoreAmount', 'old_value': 332345.0, 'new_value': 345415.0}, {'field': 'instoreCount', 'old_value': 1161, 'new_value': 1201}]
2025-05-23 08:08:11,236 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0K
2025-05-23 08:08:11,236 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6QJG5GQO7Q2OVBN4IS5U001D20_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 346508.29, 'new_value': 353674.52}, {'field': 'dailyBillAmount', 'old_value': 257691.39, 'new_value': 264343.62}, {'field': 'amount', 'old_value': 346508.29, 'new_value': 353674.52}, {'field': 'count', 'old_value': 1199, 'new_value': 1222}, {'field': 'instoreAmount', 'old_value': 346508.29, 'new_value': 353674.52}, {'field': 'instoreCount', 'old_value': 1199, 'new_value': 1222}]
2025-05-23 08:08:11,830 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1K
2025-05-23 08:08:11,830 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 165434.15, 'new_value': 170713.15}, {'field': 'dailyBillAmount', 'old_value': 165434.15, 'new_value': 170713.15}, {'field': 'amount', 'old_value': 11620.8, 'new_value': 11966.5}, {'field': 'count', 'old_value': 85, 'new_value': 91}, {'field': 'instoreAmount', 'old_value': 13669.7, 'new_value': 14221.5}, {'field': 'instoreCount', 'old_value': 85, 'new_value': 91}]
2025-05-23 08:08:12,299 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2K
2025-05-23 08:08:12,299 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT8VPFOREN7Q2OVBN4IS6Q001D2S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 87796.65, 'new_value': 89815.56}, {'field': 'dailyBillAmount', 'old_value': 87796.65, 'new_value': 89815.56}, {'field': 'amount', 'old_value': 54379.47, 'new_value': 55030.13}, {'field': 'count', 'old_value': 801, 'new_value': 811}, {'field': 'instoreAmount', 'old_value': 56330.37, 'new_value': 56981.03}, {'field': 'instoreCount', 'old_value': 801, 'new_value': 811}]
2025-05-23 08:08:12,752 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3K
2025-05-23 08:08:12,752 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTA575PTKJ7Q2OVBN4IS72001D34_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 126334.58, 'new_value': 131485.55}, {'field': 'dailyBillAmount', 'old_value': 72384.07, 'new_value': 76726.92}, {'field': 'amount', 'old_value': 126333.72, 'new_value': 131484.69}, {'field': 'count', 'old_value': 4345, 'new_value': 4510}, {'field': 'instoreAmount', 'old_value': 110743.37, 'new_value': 114450.02}, {'field': 'instoreCount', 'old_value': 3951, 'new_value': 4087}, {'field': 'onlineAmount', 'old_value': 15591.21, 'new_value': 17035.53}, {'field': 'onlineCount', 'old_value': 394, 'new_value': 423}]
2025-05-23 08:08:13,236 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4K
2025-05-23 08:08:13,236 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTAKS3N4EI7Q2OVBN4IS76001D38_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 272495.22, 'new_value': 291157.22}, {'field': 'dailyBillAmount', 'old_value': 267105.0, 'new_value': 285767.0}, {'field': 'amount', 'old_value': 231261.91, 'new_value': 241441.91}, {'field': 'count', 'old_value': 212, 'new_value': 220}, {'field': 'instoreAmount', 'old_value': 231091.0, 'new_value': 241271.0}, {'field': 'instoreCount', 'old_value': 210, 'new_value': 218}]
2025-05-23 08:08:13,721 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5K
2025-05-23 08:08:13,721 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTBJPIR4ME7Q2OVBN4IS7E001D3G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 480098.59, 'new_value': 498193.22}, {'field': 'dailyBillAmount', 'old_value': 479594.04, 'new_value': 497688.67}, {'field': 'amount', 'old_value': 480098.59, 'new_value': 498193.22}, {'field': 'count', 'old_value': 426, 'new_value': 444}, {'field': 'instoreAmount', 'old_value': 480099.59, 'new_value': 498194.22}, {'field': 'instoreCount', 'old_value': 426, 'new_value': 444}]
2025-05-23 08:08:14,142 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM6K
2025-05-23 08:08:14,142 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 70517.0, 'new_value': 148612.0}, {'field': 'dailyBillAmount', 'old_value': 70517.0, 'new_value': 148612.0}, {'field': 'amount', 'old_value': 36006.0, 'new_value': 37025.0}, {'field': 'count', 'old_value': 62, 'new_value': 64}, {'field': 'instoreAmount', 'old_value': 36006.0, 'new_value': 37025.0}, {'field': 'instoreCount', 'old_value': 62, 'new_value': 64}]
2025-05-23 08:08:14,642 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7K
2025-05-23 08:08:14,642 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCIR7D5JD7Q2OVBN4IS7U001D40_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 90331.6, 'new_value': 92675.6}, {'field': 'dailyBillAmount', 'old_value': 90331.6, 'new_value': 92675.6}, {'field': 'amount', 'old_value': 100429.3, 'new_value': 102753.3}, {'field': 'count', 'old_value': 266, 'new_value': 273}, {'field': 'instoreAmount', 'old_value': 100435.2, 'new_value': 102759.2}, {'field': 'instoreCount', 'old_value': 266, 'new_value': 273}]
2025-05-23 08:08:15,064 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8K
2025-05-23 08:08:15,064 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTD2B070E67Q2OVBN4IS86001D48_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 138414.87, 'new_value': 147096.7}, {'field': 'amount', 'old_value': 138414.87, 'new_value': 147096.7}, {'field': 'count', 'old_value': 169, 'new_value': 175}, {'field': 'instoreAmount', 'old_value': 138541.87, 'new_value': 147223.7}, {'field': 'instoreCount', 'old_value': 169, 'new_value': 175}]
2025-05-23 08:08:15,471 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9K
2025-05-23 08:08:15,471 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 194228.72, 'new_value': 197698.72}, {'field': 'dailyBillAmount', 'old_value': 194228.72, 'new_value': 197698.72}, {'field': 'amount', 'old_value': 204437.85, 'new_value': 208042.85}, {'field': 'count', 'old_value': 1369, 'new_value': 1399}, {'field': 'instoreAmount', 'old_value': 205646.85, 'new_value': 209251.85}, {'field': 'instoreCount', 'old_value': 1369, 'new_value': 1399}]
2025-05-23 08:08:15,861 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAK
2025-05-23 08:08:15,861 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTE3QTM66G7Q2OVBN4IS8M001D4O_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 131189.37, 'new_value': 137966.18}, {'field': 'dailyBillAmount', 'old_value': 131189.37, 'new_value': 137966.18}, {'field': 'amount', 'old_value': 13219.78, 'new_value': 13696.75}, {'field': 'count', 'old_value': 1171, 'new_value': 1237}, {'field': 'instoreAmount', 'old_value': 17326.41, 'new_value': 18197.76}, {'field': 'instoreCount', 'old_value': 1171, 'new_value': 1237}]
2025-05-23 08:08:16,345 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBK
2025-05-23 08:08:16,345 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 225401.62, 'new_value': 245568.51}, {'field': 'amount', 'old_value': 225397.97, 'new_value': 245564.36000000002}, {'field': 'count', 'old_value': 5519, 'new_value': 5703}, {'field': 'instoreAmount', 'old_value': 218779.97, 'new_value': 239147.16}, {'field': 'instoreCount', 'old_value': 5316, 'new_value': 5492}, {'field': 'onlineAmount', 'old_value': 9790.93, 'new_value': 10068.93}, {'field': 'onlineCount', 'old_value': 203, 'new_value': 211}]
2025-05-23 08:08:16,783 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMCK
2025-05-23 08:08:16,783 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GMFA53VGVIAPH7Q2OV4FVC7GS0017K2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 202983.57, 'new_value': 213544.6}, {'field': 'dailyBillAmount', 'old_value': 202983.57, 'new_value': 213544.6}, {'field': 'amount', 'old_value': 202983.57, 'new_value': 213544.6}, {'field': 'count', 'old_value': 623, 'new_value': 647}, {'field': 'instoreAmount', 'old_value': 202983.57, 'new_value': 213544.6}, {'field': 'instoreCount', 'old_value': 623, 'new_value': 647}]
2025-05-23 08:08:17,236 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMDK
2025-05-23 08:08:17,236 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDJ5HVP5F47Q2OV4FVC77O001449_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 176098.53, 'new_value': 180620.51}, {'field': 'dailyBillAmount', 'old_value': 176098.53, 'new_value': 180620.51}, {'field': 'amount', 'old_value': 61612.2, 'new_value': 62733.2}, {'field': 'count', 'old_value': 145, 'new_value': 148}, {'field': 'instoreAmount', 'old_value': 61612.2, 'new_value': 62733.2}, {'field': 'instoreCount', 'old_value': 145, 'new_value': 148}]
2025-05-23 08:08:17,705 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMEK
2025-05-23 08:08:17,705 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDKDOANT3H7Q2OV4FVC78800144P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 365790.1, 'new_value': 371996.84}, {'field': 'dailyBillAmount', 'old_value': 365790.1, 'new_value': 371996.84}, {'field': 'amount', 'old_value': 150789.0, 'new_value': 153677.4}, {'field': 'count', 'old_value': 561, 'new_value': 571}, {'field': 'instoreAmount', 'old_value': 150789.26, 'new_value': 153677.66}, {'field': 'instoreCount', 'old_value': 561, 'new_value': 571}]
2025-05-23 08:08:18,158 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMGK
2025-05-23 08:08:18,158 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 76096.41, 'new_value': 80383.68}, {'field': 'dailyBillAmount', 'old_value': 76096.41, 'new_value': 80383.68}, {'field': 'amount', 'old_value': 23008.23, 'new_value': 24418.0}, {'field': 'count', 'old_value': 849, 'new_value': 896}, {'field': 'instoreAmount', 'old_value': 5437.83, 'new_value': 5801.53}, {'field': 'instoreCount', 'old_value': 140, 'new_value': 152}, {'field': 'onlineAmount', 'old_value': 17824.92, 'new_value': 18870.99}, {'field': 'onlineCount', 'old_value': 709, 'new_value': 744}]
2025-05-23 08:08:18,611 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMHK
2025-05-23 08:08:18,611 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDN6L4LMS87Q2OV4FVC79K001465_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 122261.32, 'new_value': 128109.88}, {'field': 'dailyBillAmount', 'old_value': 122261.32, 'new_value': 128109.88}, {'field': 'amount', 'old_value': 19948.39, 'new_value': 21515.11}, {'field': 'count', 'old_value': 488, 'new_value': 518}, {'field': 'instoreAmount', 'old_value': 17018.91, 'new_value': 18557.01}, {'field': 'instoreCount', 'old_value': 431, 'new_value': 460}, {'field': 'onlineAmount', 'old_value': 2930.17, 'new_value': 2958.79}, {'field': 'onlineCount', 'old_value': 57, 'new_value': 58}]
2025-05-23 08:08:19,095 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMIK
2025-05-23 08:08:19,095 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDOE3O1J9R7Q2OV4FVC7A800146P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 18041.03, 'new_value': 18559.03}, {'field': 'dailyBillAmount', 'old_value': 18041.03, 'new_value': 18559.03}, {'field': 'amount', 'old_value': 14873.18, 'new_value': 14893.18}, {'field': 'count', 'old_value': 513, 'new_value': 519}, {'field': 'instoreAmount', 'old_value': 15240.78, 'new_value': 15260.78}, {'field': 'instoreCount', 'old_value': 513, 'new_value': 519}]
2025-05-23 08:08:19,533 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMJK
2025-05-23 08:08:19,549 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 39959.9, 'new_value': 41102.9}, {'field': 'dailyBillAmount', 'old_value': 39959.9, 'new_value': 41102.9}, {'field': 'amount', 'old_value': 25614.079999999998, 'new_value': 26482.18}, {'field': 'count', 'old_value': 1375, 'new_value': 1410}, {'field': 'instoreAmount', 'old_value': 13003.13, 'new_value': 13493.13}, {'field': 'instoreCount', 'old_value': 557, 'new_value': 570}, {'field': 'onlineAmount', 'old_value': 13159.93, 'new_value': 13629.63}, {'field': 'onlineCount', 'old_value': 818, 'new_value': 840}]
2025-05-23 08:08:20,002 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMKK
2025-05-23 08:08:20,002 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDRLHCKFK97Q2OV4FVC7BS00148D_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 280268.02999999997, 'new_value': 291114.02999999997}, {'field': 'dailyBillAmount', 'old_value': 280268.02999999997, 'new_value': 291114.02999999997}, {'field': 'amount', 'old_value': 126747.45999999999, 'new_value': 131316.06}, {'field': 'count', 'old_value': 524, 'new_value': 548}, {'field': 'instoreAmount', 'old_value': 131206.52, 'new_value': 135815.52}, {'field': 'instoreCount', 'old_value': 524, 'new_value': 548}]
2025-05-23 08:08:20,470 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMLK
2025-05-23 08:08:20,470 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDS537TI7U7Q2OV4FVC7C400148L_2025-05, 变更字段: [{'field': 'amount', 'old_value': 14249.71, 'new_value': 15458.84}, {'field': 'count', 'old_value': 129, 'new_value': 137}, {'field': 'instoreAmount', 'old_value': 14323.949999999999, 'new_value': 15533.08}, {'field': 'instoreCount', 'old_value': 129, 'new_value': 137}]
2025-05-23 08:08:20,924 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMMK
2025-05-23 08:08:20,924 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 174511.41, 'new_value': 179537.85}, {'field': 'dailyBillAmount', 'old_value': 174511.41, 'new_value': 179537.85}, {'field': 'amount', 'old_value': 83894.92, 'new_value': 87000.53}, {'field': 'count', 'old_value': 3537, 'new_value': 3677}, {'field': 'instoreAmount', 'old_value': 85620.07, 'new_value': 88818.78}, {'field': 'instoreCount', 'old_value': 3537, 'new_value': 3677}]
2025-05-23 08:08:21,361 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMNK
2025-05-23 08:08:21,377 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSM3EAQJB7Q2OV4FVC7CC00148T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 399476.8, 'new_value': 415384.1}, {'field': 'dailyBillAmount', 'old_value': 399476.8, 'new_value': 415384.1}, {'field': 'amount', 'old_value': 399476.8, 'new_value': 415384.1}, {'field': 'count', 'old_value': 501, 'new_value': 522}, {'field': 'instoreAmount', 'old_value': 399476.8, 'new_value': 415384.1}, {'field': 'instoreCount', 'old_value': 501, 'new_value': 522}]
2025-05-23 08:08:21,783 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMOK
2025-05-23 08:08:21,783 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSTUTUPTG7Q2OV4FVC7CG001491_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 183270.39, 'new_value': 187626.49}, {'field': 'dailyBillAmount', 'old_value': 183270.39, 'new_value': 187626.49}, {'field': 'amount', 'old_value': 104000.25, 'new_value': 106994.95}, {'field': 'count', 'old_value': 272, 'new_value': 279}, {'field': 'instoreAmount', 'old_value': 105416.85, 'new_value': 108411.55}, {'field': 'instoreCount', 'old_value': 272, 'new_value': 279}]
2025-05-23 08:08:22,252 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMPK
2025-05-23 08:08:22,252 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDT8J32K6E7Q2OV4FVC7CK001495_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 42849.0, 'new_value': 45749.0}, {'field': 'dailyBillAmount', 'old_value': 42849.0, 'new_value': 45749.0}, {'field': 'amount', 'old_value': 42849.0, 'new_value': 45749.0}, {'field': 'count', 'old_value': 842, 'new_value': 908}, {'field': 'instoreAmount', 'old_value': 42888.0, 'new_value': 45788.0}, {'field': 'instoreCount', 'old_value': 842, 'new_value': 908}]
2025-05-23 08:08:22,689 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMQK
2025-05-23 08:08:22,689 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 77072.33, 'new_value': 80076.18}, {'field': 'dailyBillAmount', 'old_value': 77072.33, 'new_value': 80076.18}, {'field': 'amount', 'old_value': 79373.33, 'new_value': 82818.23}, {'field': 'count', 'old_value': 4201, 'new_value': 4365}, {'field': 'instoreAmount', 'old_value': 37881.23, 'new_value': 39601.03}, {'field': 'instoreCount', 'old_value': 1907, 'new_value': 1983}, {'field': 'onlineAmount', 'old_value': 42542.54, 'new_value': 44362.74}, {'field': 'onlineCount', 'old_value': 2294, 'new_value': 2382}]
2025-05-23 08:08:23,392 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMRK
2025-05-23 08:08:23,392 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDUNDNMH3D7Q2OV4FVC7DC00149T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 27066.93, 'new_value': 28918.37}, {'field': 'dailyBillAmount', 'old_value': 27066.93, 'new_value': 28918.37}, {'field': 'amount', 'old_value': 37507.56, 'new_value': 39828.12}, {'field': 'count', 'old_value': 1097, 'new_value': 1167}, {'field': 'instoreAmount', 'old_value': 33842.63, 'new_value': 36068.47}, {'field': 'instoreCount', 'old_value': 952, 'new_value': 1016}, {'field': 'onlineAmount', 'old_value': 3721.1, 'new_value': 3879.92}, {'field': 'onlineCount', 'old_value': 145, 'new_value': 151}]
2025-05-23 08:08:23,814 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMSK
2025-05-23 08:08:23,814 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV0D9P6J27Q2OV4FVC7DG0014A1_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 54658.3, 'new_value': 58194.3}, {'field': 'dailyBillAmount', 'old_value': 54658.3, 'new_value': 58194.3}, {'field': 'amount', 'old_value': 54528.94, 'new_value': 58043.76}, {'field': 'count', 'old_value': 2129, 'new_value': 2268}, {'field': 'instoreAmount', 'old_value': 35451.58, 'new_value': 37827.26}, {'field': 'instoreCount', 'old_value': 1268, 'new_value': 1352}, {'field': 'onlineAmount', 'old_value': 19377.99, 'new_value': 20517.13}, {'field': 'onlineCount', 'old_value': 861, 'new_value': 916}]
2025-05-23 08:08:24,267 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMTK
2025-05-23 08:08:24,267 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV84M589R7Q2OV4FVC7DK0014A5_2025-05, 变更字段: [{'field': 'amount', 'old_value': 57736.46, 'new_value': 58591.37}, {'field': 'count', 'old_value': 697, 'new_value': 715}, {'field': 'instoreAmount', 'old_value': 58163.36, 'new_value': 59018.27}, {'field': 'instoreCount', 'old_value': 697, 'new_value': 715}]
2025-05-23 08:08:24,611 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMUK
2025-05-23 08:08:24,611 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVG061A0H7Q2OV4FVC7DO0014A9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 60471.4, 'new_value': 62532.0}, {'field': 'amount', 'old_value': 60470.9, 'new_value': 62531.5}, {'field': 'count', 'old_value': 1525, 'new_value': 1580}, {'field': 'instoreAmount', 'old_value': 61520.4, 'new_value': 63581.0}, {'field': 'instoreCount', 'old_value': 1525, 'new_value': 1580}]
2025-05-23 08:08:25,002 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMVK
2025-05-23 08:08:25,002 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVNVP6LRA7Q2OV4FVC7DS0014AD_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 284044.42, 'new_value': 292232.42}, {'field': 'dailyBillAmount', 'old_value': 284044.42, 'new_value': 292232.42}, {'field': 'amount', 'old_value': 87665.82, 'new_value': 90204.82}, {'field': 'count', 'old_value': 312, 'new_value': 323}, {'field': 'instoreAmount', 'old_value': 87665.82, 'new_value': 90204.82}, {'field': 'instoreCount', 'old_value': 312, 'new_value': 323}]
2025-05-23 08:08:25,439 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMWK
2025-05-23 08:08:25,439 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE0PKSKM647Q2OV4FVC7EC0014AT_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 80547.26, 'new_value': 81874.06}, {'field': 'dailyBillAmount', 'old_value': 80547.26, 'new_value': 81874.06}, {'field': 'amount', 'old_value': 77849.46, 'new_value': 79176.26}, {'field': 'count', 'old_value': 279, 'new_value': 284}, {'field': 'instoreAmount', 'old_value': 79984.09, 'new_value': 81310.89}, {'field': 'instoreCount', 'old_value': 279, 'new_value': 284}]
2025-05-23 08:08:25,877 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXK
2025-05-23 08:08:25,877 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE1H27HPQN7Q2OV4FVC7EO0014B9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 44472.0, 'new_value': 44841.0}, {'field': 'dailyBillAmount', 'old_value': 44472.0, 'new_value': 44841.0}, {'field': 'amount', 'old_value': 55619.0, 'new_value': 55988.0}, {'field': 'count', 'old_value': 105, 'new_value': 106}, {'field': 'instoreAmount', 'old_value': 59717.0, 'new_value': 60086.0}, {'field': 'instoreCount', 'old_value': 105, 'new_value': 106}]
2025-05-23 08:08:26,299 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYK
2025-05-23 08:08:26,299 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE29HIJ7QK7Q2OV4FVC7F40014BL_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 72590.55, 'new_value': 78067.45}, {'field': 'dailyBillAmount', 'old_value': 70097.35, 'new_value': 75574.25}, {'field': 'amount', 'old_value': 72588.25, 'new_value': 78065.15}, {'field': 'count', 'old_value': 222, 'new_value': 236}, {'field': 'instoreAmount', 'old_value': 81380.75, 'new_value': 86857.65}, {'field': 'instoreCount', 'old_value': 222, 'new_value': 236}]
2025-05-23 08:08:26,908 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZK
2025-05-23 08:08:26,908 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE2TORA7KI7Q2OV4FVC7FC0014BT_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 99996.99, 'new_value': 103755.74}, {'field': 'dailyBillAmount', 'old_value': 99996.99, 'new_value': 103755.74}, {'field': 'amount', 'old_value': 55513.95, 'new_value': 58126.33}, {'field': 'count', 'old_value': 1533, 'new_value': 1598}, {'field': 'instoreAmount', 'old_value': 48335.82, 'new_value': 50546.17}, {'field': 'instoreCount', 'old_value': 1299, 'new_value': 1349}, {'field': 'onlineAmount', 'old_value': 8176.91, 'new_value': 8618.94}, {'field': 'onlineCount', 'old_value': 234, 'new_value': 249}]
2025-05-23 08:08:27,361 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0L
2025-05-23 08:08:27,361 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9PGR3703D752ASKKUBQUM50018DU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 147635.19, 'new_value': 153716.82}, {'field': 'dailyBillAmount', 'old_value': 142689.25, 'new_value': 148622.86}, {'field': 'amount', 'old_value': 147635.19, 'new_value': 153716.82}, {'field': 'count', 'old_value': 1816, 'new_value': 1897}, {'field': 'instoreAmount', 'old_value': 140356.25, 'new_value': 145758.25}, {'field': 'instoreCount', 'old_value': 1734, 'new_value': 1806}, {'field': 'onlineAmount', 'old_value': 7338.18, 'new_value': 8017.8099999999995}, {'field': 'onlineCount', 'old_value': 82, 'new_value': 91}]
2025-05-23 08:08:27,799 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1L
2025-05-23 08:08:27,799 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9S64J8E8R652ASKKUBQUMU0018EN_2025-05, 变更字段: [{'field': 'amount', 'old_value': 91104.74, 'new_value': 92865.8}, {'field': 'count', 'old_value': 390, 'new_value': 399}, {'field': 'instoreAmount', 'old_value': 87784.61, 'new_value': 89515.67}, {'field': 'instoreCount', 'old_value': 349, 'new_value': 357}, {'field': 'onlineAmount', 'old_value': 3320.13, 'new_value': 3350.13}, {'field': 'onlineCount', 'old_value': 41, 'new_value': 42}]
2025-05-23 08:08:28,299 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2L
2025-05-23 08:08:28,299 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9UCRQKEOIF52ASKKUBQUNH0018FA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 179658.5, 'new_value': 181667.5}, {'field': 'dailyBillAmount', 'old_value': 179658.5, 'new_value': 181667.5}, {'field': 'amount', 'old_value': 181334.0, 'new_value': 183087.0}, {'field': 'count', 'old_value': 665, 'new_value': 673}, {'field': 'instoreAmount', 'old_value': 184218.9, 'new_value': 185971.9}, {'field': 'instoreCount', 'old_value': 665, 'new_value': 673}]
2025-05-23 08:08:28,861 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3L
2025-05-23 08:08:28,861 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HB8D7KAC5K1G3723F7K257LIN001TSQ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 40162.0, 'new_value': 41302.0}, {'field': 'dailyBillAmount', 'old_value': 40162.0, 'new_value': 41302.0}, {'field': 'amount', 'old_value': 37360.0, 'new_value': 38500.0}, {'field': 'count', 'old_value': 91, 'new_value': 93}, {'field': 'instoreAmount', 'old_value': 37953.0, 'new_value': 39093.0}, {'field': 'instoreCount', 'old_value': 91, 'new_value': 93}]
2025-05-23 08:08:29,314 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4L
2025-05-23 08:08:29,314 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HHDRIR0P38R666U1G19QP11V00018CE_2025-05, 变更字段: [{'field': 'amount', 'old_value': 17534.14, 'new_value': 18332.14}, {'field': 'count', 'old_value': 34, 'new_value': 35}, {'field': 'instoreAmount', 'old_value': 17815.54, 'new_value': 18613.54}, {'field': 'instoreCount', 'old_value': 34, 'new_value': 35}]
2025-05-23 08:08:29,767 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM6L
2025-05-23 08:08:29,767 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HI85875BG16NA6U1G19QP117C001UB5_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 20932.9, 'new_value': 21580.3}, {'field': 'dailyBillAmount', 'old_value': 20932.9, 'new_value': 21580.3}, {'field': 'amount', 'old_value': 16091.61, 'new_value': 16624.81}, {'field': 'count', 'old_value': 726, 'new_value': 751}, {'field': 'instoreAmount', 'old_value': 16284.66, 'new_value': 16817.86}, {'field': 'instoreCount', 'old_value': 726, 'new_value': 751}]
2025-05-23 08:08:30,158 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7L
2025-05-23 08:08:30,158 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HINIR4GO8E5NM5U25UDHUFEGO001L3K_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 38522.54, 'new_value': 41466.909999999996}, {'field': 'amount', 'old_value': 38521.07, 'new_value': 41465.44}, {'field': 'count', 'old_value': 1971, 'new_value': 2114}, {'field': 'instoreAmount', 'old_value': 44538.13, 'new_value': 48010.95}, {'field': 'instoreCount', 'old_value': 1971, 'new_value': 2114}]
2025-05-23 08:08:30,658 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM8L
2025-05-23 08:08:30,658 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HMVHOKFUN7GGQ26TEPQN7CSO4001IA6_2025-05, 变更字段: [{'field': 'amount', 'old_value': 88813.1, 'new_value': 92176.6}, {'field': 'count', 'old_value': 366, 'new_value': 380}, {'field': 'instoreAmount', 'old_value': 88813.1, 'new_value': 92176.6}, {'field': 'instoreCount', 'old_value': 365, 'new_value': 379}]
2025-05-23 08:08:31,111 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9L
2025-05-23 08:08:31,111 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 322291.86, 'new_value': 336124.68}, {'field': 'dailyBillAmount', 'old_value': 322291.86, 'new_value': 336124.68}, {'field': 'amount', 'old_value': 189195.23, 'new_value': 196173.37}, {'field': 'count', 'old_value': 2197, 'new_value': 2280}, {'field': 'instoreAmount', 'old_value': 79905.01, 'new_value': 84081.86}, {'field': 'instoreCount', 'old_value': 927, 'new_value': 974}, {'field': 'onlineAmount', 'old_value': 109291.13, 'new_value': 112092.42}, {'field': 'onlineCount', 'old_value': 1270, 'new_value': 1306}]
2025-05-23 08:08:31,549 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAL
2025-05-23 08:08:31,549 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HV6P9SGUVGG9S36QDA69ST70J0015SA_2025-05, 变更字段: [{'field': 'amount', 'old_value': 198413.4, 'new_value': 200915.4}, {'field': 'count', 'old_value': 1189, 'new_value': 1209}, {'field': 'instoreAmount', 'old_value': 199193.3, 'new_value': 201695.3}, {'field': 'instoreCount', 'old_value': 1189, 'new_value': 1209}]
2025-05-23 08:08:32,002 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMCL
2025-05-23 08:08:32,002 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVORJ88U7D2IL1AIB692RTFU8001185_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 137685.83, 'new_value': 144133.91}, {'field': 'dailyBillAmount', 'old_value': 137685.83, 'new_value': 144133.91}, {'field': 'amount', 'old_value': 90635.97, 'new_value': 93902.82}, {'field': 'count', 'old_value': 1023, 'new_value': 1057}, {'field': 'instoreAmount', 'old_value': 82327.73, 'new_value': 85464.78}, {'field': 'instoreCount', 'old_value': 717, 'new_value': 746}, {'field': 'onlineAmount', 'old_value': 10099.41, 'new_value': 10229.21}, {'field': 'onlineCount', 'old_value': 306, 'new_value': 311}]
2025-05-23 08:08:32,455 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMDL
2025-05-23 08:08:32,455 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1I6E0VAU3IFJEQ22MH147FMU0M0013E8_2025-05, 变更字段: [{'field': 'amount', 'old_value': 1977.6, 'new_value': 2052.5}, {'field': 'count', 'old_value': 23, 'new_value': 25}, {'field': 'instoreAmount', 'old_value': 1977.6, 'new_value': 2052.5}, {'field': 'instoreCount', 'old_value': 23, 'new_value': 25}]
2025-05-23 08:08:32,799 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMEL
2025-05-23 08:08:32,799 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1IBBJ3RNCSAVNO7A70STAEF09Q001IPV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 166438.3, 'new_value': 171059.3}, {'field': 'dailyBillAmount', 'old_value': 161632.55, 'new_value': 166253.55}, {'field': 'amount', 'old_value': 166438.3, 'new_value': 171059.3}, {'field': 'count', 'old_value': 703, 'new_value': 722}, {'field': 'instoreAmount', 'old_value': 166438.3, 'new_value': 171059.3}, {'field': 'instoreCount', 'old_value': 703, 'new_value': 722}]
2025-05-23 08:08:33,252 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMFL
2025-05-23 08:08:33,252 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_3BB9A16AE8544997965802FAA3B83381_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 19311.84, 'new_value': 20322.24}, {'field': 'dailyBillAmount', 'old_value': 19311.84, 'new_value': 20322.24}, {'field': 'amount', 'old_value': 23338.74, 'new_value': 24441.24}, {'field': 'count', 'old_value': 684, 'new_value': 719}, {'field': 'instoreAmount', 'old_value': 23358.54, 'new_value': 24461.04}, {'field': 'instoreCount', 'old_value': 684, 'new_value': 719}]
2025-05-23 08:08:33,658 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMGL
2025-05-23 08:08:33,658 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6692283A183D432BAE322E1032539CE8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 257266.2, 'new_value': 262469.2}, {'field': 'amount', 'old_value': 257266.2, 'new_value': 262469.2}, {'field': 'count', 'old_value': 394, 'new_value': 403}, {'field': 'instoreAmount', 'old_value': 257266.2, 'new_value': 262469.2}, {'field': 'instoreCount', 'old_value': 394, 'new_value': 403}]
2025-05-23 08:08:34,127 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMHL
2025-05-23 08:08:34,127 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6B7571A27AF84C73B4FC04CCBDB83D9B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 40403.48, 'new_value': 41675.82}, {'field': 'amount', 'old_value': 40403.48, 'new_value': 41675.82}, {'field': 'count', 'old_value': 328, 'new_value': 334}, {'field': 'instoreAmount', 'old_value': 40403.48, 'new_value': 41675.82}, {'field': 'instoreCount', 'old_value': 328, 'new_value': 334}]
2025-05-23 08:08:34,564 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMIL
2025-05-23 08:08:34,564 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78655ECA4A32471AB7842F8DE2018120_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 269243.0, 'new_value': 275442.0}, {'field': 'amount', 'old_value': 269243.0, 'new_value': 275442.0}, {'field': 'count', 'old_value': 60, 'new_value': 61}, {'field': 'instoreAmount', 'old_value': 269243.0, 'new_value': 275442.0}, {'field': 'instoreCount', 'old_value': 60, 'new_value': 61}]
2025-05-23 08:08:35,033 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMJL
2025-05-23 08:08:35,033 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_A93C60005A8F41B092F6C5A8C21577CB_2025-05, 变更字段: [{'field': 'amount', 'old_value': 33873.95, 'new_value': 35154.05}, {'field': 'count', 'old_value': 448, 'new_value': 457}, {'field': 'instoreAmount', 'old_value': 33873.95, 'new_value': 35154.05}, {'field': 'instoreCount', 'old_value': 448, 'new_value': 457}]
2025-05-23 08:08:35,470 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMKL
2025-05-23 08:08:35,470 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AA76628FACEC4C13BD44C8280B45416D_2025-05, 变更字段: [{'field': 'amount', 'old_value': 38117.0, 'new_value': 39580.3}, {'field': 'count', 'old_value': 46, 'new_value': 48}, {'field': 'instoreAmount', 'old_value': 39015.0, 'new_value': 40478.3}, {'field': 'instoreCount', 'old_value': 46, 'new_value': 48}]
2025-05-23 08:08:35,892 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMLL
2025-05-23 08:08:35,892 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AC07B70DB49845A8A52846E099EBC515_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 366787.88, 'new_value': 379532.88}, {'field': 'dailyBillAmount', 'old_value': 366787.88, 'new_value': 379532.88}, {'field': 'amount', 'old_value': 373958.88, 'new_value': 386703.88}, {'field': 'count', 'old_value': 1181, 'new_value': 1218}, {'field': 'instoreAmount', 'old_value': 373958.88, 'new_value': 386703.88}, {'field': 'instoreCount', 'old_value': 1181, 'new_value': 1218}]
2025-05-23 08:08:36,345 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMML
2025-05-23 08:08:36,345 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_BF554F536BF14762AEB7110E7BD583B7_2025-05, 变更字段: [{'field': 'amount', 'old_value': 852535.51, 'new_value': 881783.39}, {'field': 'count', 'old_value': 1105, 'new_value': 1141}, {'field': 'instoreAmount', 'old_value': 852535.68, 'new_value': 881783.56}, {'field': 'instoreCount', 'old_value': 1105, 'new_value': 1141}]
2025-05-23 08:08:36,845 - INFO - 更新表单数据成功: FINST-2XF66ID1O6ZU6RGLC9HPAA12ROM12M9VUG7AMH01
2025-05-23 08:08:36,845 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_D384CB5088914FB296DE32297895B8D6_2025-05, 变更字段: [{'field': 'count', 'old_value': 9, 'new_value': 17}, {'field': 'instoreCount', 'old_value': 9, 'new_value': 17}]
2025-05-23 08:08:37,377 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMNL
2025-05-23 08:08:37,377 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 128279.2, 'new_value': 131887.9}, {'field': 'dailyBillAmount', 'old_value': 128279.2, 'new_value': 131887.9}, {'field': 'amount', 'old_value': 26900.0, 'new_value': 27812.9}, {'field': 'count', 'old_value': 102, 'new_value': 106}, {'field': 'instoreAmount', 'old_value': 26901.5, 'new_value': 27814.4}, {'field': 'instoreCount', 'old_value': 102, 'new_value': 106}]
2025-05-23 08:08:37,736 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMOL
2025-05-23 08:08:37,736 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDMFCJQF4F0I86N3H2U1UC001E7I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 172786.62, 'new_value': 174431.88999999998}, {'field': 'amount', 'old_value': 172784.1, 'new_value': 174429.37}, {'field': 'count', 'old_value': 1778, 'new_value': 1806}, {'field': 'instoreAmount', 'old_value': 112126.02, 'new_value': 112853.53}, {'field': 'instoreCount', 'old_value': 1015, 'new_value': 1024}, {'field': 'onlineAmount', 'old_value': 65054.82, 'new_value': 66022.98}, {'field': 'onlineCount', 'old_value': 763, 'new_value': 782}]
2025-05-23 08:08:38,189 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMPL
2025-05-23 08:08:38,205 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 275650.92, 'new_value': 287472.13}, {'field': 'dailyBillAmount', 'old_value': 275650.92, 'new_value': 287472.13}, {'field': 'amount', 'old_value': 25410.82, 'new_value': 26432.0}, {'field': 'count', 'old_value': 786, 'new_value': 818}, {'field': 'instoreAmount', 'old_value': 29366.26, 'new_value': 30457.14}, {'field': 'instoreCount', 'old_value': 786, 'new_value': 818}]
2025-05-23 08:08:38,595 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMQL
2025-05-23 08:08:38,595 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 261202.78, 'new_value': 265620.35}, {'field': 'dailyBillAmount', 'old_value': 261202.78, 'new_value': 265620.35}, {'field': 'amount', 'old_value': 134870.46, 'new_value': 137884.05}, {'field': 'count', 'old_value': 3016, 'new_value': 3090}, {'field': 'instoreAmount', 'old_value': 113404.19, 'new_value': 115644.65000000001}, {'field': 'instoreCount', 'old_value': 2532, 'new_value': 2585}, {'field': 'onlineAmount', 'old_value': 23596.21, 'new_value': 24369.34}, {'field': 'onlineCount', 'old_value': 484, 'new_value': 505}]
2025-05-23 08:08:39,002 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMRL
2025-05-23 08:08:39,002 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 246971.2, 'new_value': 250717.4}, {'field': 'amount', 'old_value': 246969.6, 'new_value': 250715.8}, {'field': 'count', 'old_value': 977, 'new_value': 996}, {'field': 'instoreAmount', 'old_value': 250061.1, 'new_value': 253807.3}, {'field': 'instoreCount', 'old_value': 977, 'new_value': 996}]
2025-05-23 08:08:39,423 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMSL
2025-05-23 08:08:39,423 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 417507.72, 'new_value': 429150.81}, {'field': 'dailyBillAmount', 'old_value': 417507.72, 'new_value': 429150.81}, {'field': 'amount', 'old_value': 398167.54, 'new_value': 400942.05}, {'field': 'count', 'old_value': 7538, 'new_value': 7581}, {'field': 'instoreAmount', 'old_value': 372532.06, 'new_value': 374378.1}, {'field': 'instoreCount', 'old_value': 7041, 'new_value': 7067}, {'field': 'onlineAmount', 'old_value': 27201.93, 'new_value': 28140.3}, {'field': 'onlineCount', 'old_value': 497, 'new_value': 514}]
2025-05-23 08:08:39,892 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMTL
2025-05-23 08:08:39,892 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 271570.27, 'new_value': 291633.17}, {'field': 'amount', 'old_value': 239285.0, 'new_value': 259347.9}, {'field': 'count', 'old_value': 5768, 'new_value': 6255}, {'field': 'instoreAmount', 'old_value': 203302.0, 'new_value': 212952.4}, {'field': 'instoreCount', 'old_value': 4476, 'new_value': 4698}, {'field': 'onlineAmount', 'old_value': 36142.8, 'new_value': 46555.3}, {'field': 'onlineCount', 'old_value': 1292, 'new_value': 1557}]
2025-05-23 08:08:40,283 - INFO - 更新表单数据成功: FINST-RI766091Q8ZUDV4P6CERK73FVYBF3UQMQBAAMQC1
2025-05-23 08:08:40,283 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_2025-05, 变更字段: [{'field': 'amount', 'old_value': 138804.37, 'new_value': 144254.46}, {'field': 'count', 'old_value': 9482, 'new_value': 9826}, {'field': 'instoreAmount', 'old_value': 114659.72, 'new_value': 118588.75}, {'field': 'instoreCount', 'old_value': 7617, 'new_value': 7865}, {'field': 'onlineAmount', 'old_value': 27535.21, 'new_value': 29061.25}, {'field': 'onlineCount', 'old_value': 1865, 'new_value': 1961}]
2025-05-23 08:08:40,736 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMVL
2025-05-23 08:08:40,736 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQQG9THS10I86N3H2U108001E9E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 228820.96, 'new_value': 235047.61}, {'field': 'dailyBillAmount', 'old_value': 228820.96, 'new_value': 235047.61}, {'field': 'amount', 'old_value': 220353.74, 'new_value': 226197.78}, {'field': 'count', 'old_value': 6438, 'new_value': 6605}, {'field': 'instoreAmount', 'old_value': 221769.15, 'new_value': 227726.69}, {'field': 'instoreCount', 'old_value': 6438, 'new_value': 6605}]
2025-05-23 08:08:41,127 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMWL
2025-05-23 08:08:41,127 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDRR6FJ7A60I86N3H2U10L001E9R_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 67500.85, 'new_value': 69831.47}, {'field': 'amount', 'old_value': 67497.6, 'new_value': 69828.22}, {'field': 'count', 'old_value': 3620, 'new_value': 3744}, {'field': 'instoreAmount', 'old_value': 39413.78, 'new_value': 40311.54}, {'field': 'instoreCount', 'old_value': 2265, 'new_value': 2327}, {'field': 'onlineAmount', 'old_value': 28087.07, 'new_value': 29519.93}, {'field': 'onlineCount', 'old_value': 1355, 'new_value': 1417}]
2025-05-23 08:08:41,595 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMXL
2025-05-23 08:08:41,595 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDS341MMSU0I86N3H2U10P001E9V_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 117940.15, 'new_value': 121166.89}, {'field': 'dailyBillAmount', 'old_value': 117940.15, 'new_value': 121166.89}, {'field': 'amount', 'old_value': 24824.36, 'new_value': 25602.170000000002}, {'field': 'count', 'old_value': 867, 'new_value': 903}, {'field': 'instoreAmount', 'old_value': 25643.260000000002, 'new_value': 26454.48}, {'field': 'instoreCount', 'old_value': 867, 'new_value': 903}]
2025-05-23 08:08:42,095 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYL
2025-05-23 08:08:42,095 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_2025-05, 变更字段: [{'field': 'amount', 'old_value': 80700.67, 'new_value': 84162.0}, {'field': 'count', 'old_value': 4022, 'new_value': 4177}, {'field': 'instoreAmount', 'old_value': 18064.41, 'new_value': 18513.940000000002}, {'field': 'instoreCount', 'old_value': 1284, 'new_value': 1322}, {'field': 'onlineAmount', 'old_value': 63997.99, 'new_value': 67118.99}, {'field': 'onlineCount', 'old_value': 2738, 'new_value': 2855}]
2025-05-23 08:08:42,502 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZL
2025-05-23 08:08:42,502 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSK489TE20I86N3H2U114001EAA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 86546.65, 'new_value': 89168.73}, {'field': 'amount', 'old_value': 86545.77, 'new_value': 89167.85}, {'field': 'count', 'old_value': 2289, 'new_value': 2354}, {'field': 'instoreAmount', 'old_value': 83517.75, 'new_value': 85932.43000000001}, {'field': 'instoreCount', 'old_value': 2231, 'new_value': 2293}, {'field': 'onlineAmount', 'old_value': 3985.31, 'new_value': 4192.71}, {'field': 'onlineCount', 'old_value': 58, 'new_value': 61}]
2025-05-23 08:08:42,986 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0M
2025-05-23 08:08:42,986 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_2025-05, 变更字段: [{'field': 'amount', 'old_value': 136354.75, 'new_value': 142993.54}, {'field': 'count', 'old_value': 5584, 'new_value': 5838}, {'field': 'instoreAmount', 'old_value': 139333.02, 'new_value': 145976.51}, {'field': 'instoreCount', 'old_value': 5533, 'new_value': 5784}, {'field': 'onlineAmount', 'old_value': 1886.51, 'new_value': 1977.01}, {'field': 'onlineCount', 'old_value': 51, 'new_value': 54}]
2025-05-23 08:08:43,455 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1M
2025-05-23 08:08:43,455 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 155487.95, 'new_value': 159751.77}, {'field': 'dailyBillAmount', 'old_value': 155487.95, 'new_value': 159751.77}, {'field': 'amount', 'old_value': 101358.84, 'new_value': 105081.83}, {'field': 'count', 'old_value': 8504, 'new_value': 8718}, {'field': 'instoreAmount', 'old_value': 7040.71, 'new_value': 7229.71}, {'field': 'instoreCount', 'old_value': 392, 'new_value': 403}, {'field': 'onlineAmount', 'old_value': 99214.93, 'new_value': 102814.12}, {'field': 'onlineCount', 'old_value': 8112, 'new_value': 8315}]
2025-05-23 08:08:43,908 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2M
2025-05-23 08:08:43,908 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 140390.99, 'new_value': 143740.61}, {'field': 'dailyBillAmount', 'old_value': 140390.99, 'new_value': 143740.61}, {'field': 'amount', 'old_value': 120287.3, 'new_value': 123089.6}, {'field': 'count', 'old_value': 3946, 'new_value': 4028}, {'field': 'instoreAmount', 'old_value': 66045.06, 'new_value': 67325.36}, {'field': 'instoreCount', 'old_value': 2852, 'new_value': 2898}, {'field': 'onlineAmount', 'old_value': 62492.51, 'new_value': 64033.41}, {'field': 'onlineCount', 'old_value': 1094, 'new_value': 1130}]
2025-05-23 08:08:44,361 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4M
2025-05-23 08:08:44,361 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVSS4V3460I86N3H2U12P001EBV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 49114.34, 'new_value': 50929.47}, {'field': 'dailyBillAmount', 'old_value': 49114.34, 'new_value': 50929.47}, {'field': 'amount', 'old_value': 66489.86, 'new_value': 68822.07}, {'field': 'count', 'old_value': 2559, 'new_value': 2652}, {'field': 'instoreAmount', 'old_value': 21746.45, 'new_value': 22082.45}, {'field': 'instoreCount', 'old_value': 917, 'new_value': 937}, {'field': 'onlineAmount', 'old_value': 45667.47, 'new_value': 47679.78}, {'field': 'onlineCount', 'old_value': 1642, 'new_value': 1715}]
2025-05-23 08:08:44,814 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5M
2025-05-23 08:08:44,814 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE0JS3BF7R0I86N3H2U135001ECB_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 81963.44, 'new_value': 83475.57}, {'field': 'dailyBillAmount', 'old_value': 81963.44, 'new_value': 83475.57}, {'field': 'amount', 'old_value': 84354.44, 'new_value': 85900.9}, {'field': 'count', 'old_value': 3005, 'new_value': 3059}, {'field': 'instoreAmount', 'old_value': 84354.44, 'new_value': 85900.9}, {'field': 'instoreCount', 'old_value': 3005, 'new_value': 3059}]
2025-05-23 08:08:45,330 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM6M
2025-05-23 08:08:45,330 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE13J2R9CI0I86N3H2U13D001ECJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 289557.0, 'new_value': 292085.0}, {'field': 'dailyBillAmount', 'old_value': 289557.0, 'new_value': 292085.0}, {'field': 'amount', 'old_value': 313978.0, 'new_value': 316606.0}, {'field': 'count', 'old_value': 251, 'new_value': 255}, {'field': 'instoreAmount', 'old_value': 342164.0, 'new_value': 344792.0}, {'field': 'instoreCount', 'old_value': 251, 'new_value': 255}]
2025-05-23 08:08:45,798 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7M
2025-05-23 08:08:45,798 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_2025-05, 变更字段: [{'field': 'amount', 'old_value': 208973.16, 'new_value': 216127.96}, {'field': 'count', 'old_value': 401, 'new_value': 416}, {'field': 'instoreAmount', 'old_value': 211646.66, 'new_value': 218801.46}, {'field': 'instoreCount', 'old_value': 401, 'new_value': 416}]
2025-05-23 08:08:46,298 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9M
2025-05-23 08:08:46,298 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EG92S1SB0I86N3H2U188001EHE_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 66051.0, 'new_value': 68290.0}, {'field': 'dailyBillAmount', 'old_value': 66051.0, 'new_value': 68290.0}, {'field': 'amount', 'old_value': 32314.0, 'new_value': 33521.0}, {'field': 'count', 'old_value': 87, 'new_value': 90}, {'field': 'instoreAmount', 'old_value': 33776.0, 'new_value': 34983.0}, {'field': 'instoreCount', 'old_value': 87, 'new_value': 90}]
2025-05-23 08:08:46,845 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMAM
2025-05-23 08:08:46,845 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EN1GSFF80I86N3H2U18C001EHI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 57234.0, 'new_value': 58611.0}, {'field': 'dailyBillAmount', 'old_value': 41384.0, 'new_value': 42292.0}, {'field': 'amount', 'old_value': 52992.0, 'new_value': 54369.0}, {'field': 'count', 'old_value': 69, 'new_value': 72}, {'field': 'instoreAmount', 'old_value': 52992.0, 'new_value': 54369.0}, {'field': 'instoreCount', 'old_value': 69, 'new_value': 72}]
2025-05-23 08:08:47,267 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBM
2025-05-23 08:08:47,267 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9ETPVO71I0I86N3H2U18G001EHM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 59469.0, 'new_value': 60999.299999999996}, {'field': 'amount', 'old_value': 59466.8, 'new_value': 60997.1}, {'field': 'count', 'old_value': 160, 'new_value': 164}, {'field': 'instoreAmount', 'old_value': 59469.0, 'new_value': 61488.0}, {'field': 'instoreCount', 'old_value': 160, 'new_value': 164}]
2025-05-23 08:08:47,705 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM571
2025-05-23 08:08:47,705 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9G31FV3GL0I86N3H2U190001EI6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 487309.0, 'new_value': 507906.0}, {'field': 'dailyBillAmount', 'old_value': 487309.0, 'new_value': 507906.0}, {'field': 'amount', 'old_value': 558936.0, 'new_value': 579533.0}, {'field': 'count', 'old_value': 67, 'new_value': 70}, {'field': 'instoreAmount', 'old_value': 558936.0, 'new_value': 579533.0}, {'field': 'instoreCount', 'old_value': 67, 'new_value': 70}]
2025-05-23 08:08:48,127 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM771
2025-05-23 08:08:48,127 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDCE3748SO0I86N3H2U1FP001EOV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 57487.0, 'new_value': 58606.0}, {'field': 'amount', 'old_value': 57487.0, 'new_value': 58606.0}, {'field': 'count', 'old_value': 66, 'new_value': 67}, {'field': 'instoreAmount', 'old_value': 57487.0, 'new_value': 58606.0}, {'field': 'instoreCount', 'old_value': 66, 'new_value': 67}]
2025-05-23 08:08:48,564 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM871
2025-05-23 08:08:48,564 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 201941.7, 'new_value': 213516.0}, {'field': 'dailyBillAmount', 'old_value': 201941.7, 'new_value': 213516.0}, {'field': 'amount', 'old_value': 293807.4, 'new_value': 304774.9}, {'field': 'count', 'old_value': 366, 'new_value': 377}, {'field': 'instoreAmount', 'old_value': 305302.86, 'new_value': 316270.36}, {'field': 'instoreCount', 'old_value': 366, 'new_value': 377}]
2025-05-23 08:08:49,033 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM971
2025-05-23 08:08:49,033 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEQ2M9E710I86N3H2U1H1001EQ7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 94407.65, 'new_value': 97046.15}, {'field': 'dailyBillAmount', 'old_value': 94407.65, 'new_value': 97046.15}, {'field': 'amount', 'old_value': 31116.28, 'new_value': 34047.48}, {'field': 'count', 'old_value': 297, 'new_value': 325}, {'field': 'instoreAmount', 'old_value': 30948.98, 'new_value': 33830.78}, {'field': 'instoreCount', 'old_value': 250, 'new_value': 277}, {'field': 'onlineAmount', 'old_value': 2728.6, 'new_value': 2778.0}, {'field': 'onlineCount', 'old_value': 47, 'new_value': 48}]
2025-05-23 08:08:49,533 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMA71
2025-05-23 08:08:49,533 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF11AKIN30I86N3H2U1H5001EQB_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 11323.0, 'new_value': 12019.0}, {'field': 'amount', 'old_value': 11323.0, 'new_value': 12019.0}, {'field': 'count', 'old_value': 31, 'new_value': 33}, {'field': 'instoreAmount', 'old_value': 11323.0, 'new_value': 12019.0}, {'field': 'instoreCount', 'old_value': 31, 'new_value': 33}]
2025-05-23 08:08:49,923 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMB71
2025-05-23 08:08:49,923 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF8HFHI690I86N3H2U1H9001EQF_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 31307.0, 'new_value': 31987.0}, {'field': 'dailyBillAmount', 'old_value': 31307.0, 'new_value': 31987.0}, {'field': 'amount', 'old_value': 36594.0, 'new_value': 37274.0}, {'field': 'count', 'old_value': 118, 'new_value': 121}, {'field': 'instoreAmount', 'old_value': 36594.0, 'new_value': 37274.0}, {'field': 'instoreCount', 'old_value': 118, 'new_value': 121}]
2025-05-23 08:08:50,423 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMC71
2025-05-23 08:08:50,423 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 31077.7, 'new_value': 31343.7}, {'field': 'amount', 'old_value': 31077.7, 'new_value': 31343.7}, {'field': 'count', 'old_value': 187, 'new_value': 190}, {'field': 'instoreAmount', 'old_value': 31415.7, 'new_value': 31681.7}, {'field': 'instoreCount', 'old_value': 187, 'new_value': 190}]
2025-05-23 08:08:50,830 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMD71
2025-05-23 08:08:50,830 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDG7SC9VNO0I86N3H2U1HP001EQV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 7074.0, 'new_value': 7493.0}, {'field': 'dailyBillAmount', 'old_value': 7074.0, 'new_value': 7493.0}, {'field': 'amount', 'old_value': 34673.0, 'new_value': 35929.0}, {'field': 'count', 'old_value': 105, 'new_value': 109}, {'field': 'instoreAmount', 'old_value': 35448.0, 'new_value': 36704.0}, {'field': 'instoreCount', 'old_value': 105, 'new_value': 109}]
2025-05-23 08:08:51,408 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AME71
2025-05-23 08:08:51,408 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 684353.4299999999, 'new_value': 697848.42}, {'field': 'dailyBillAmount', 'old_value': 684353.4299999999, 'new_value': 697848.42}, {'field': 'amount', 'old_value': 43683.29, 'new_value': 44882.89}, {'field': 'count', 'old_value': 419, 'new_value': 430}, {'field': 'instoreAmount', 'old_value': 36007.08, 'new_value': 36663.12}, {'field': 'instoreCount', 'old_value': 299, 'new_value': 305}, {'field': 'onlineAmount', 'old_value': 8699.24, 'new_value': 9242.8}, {'field': 'onlineCount', 'old_value': 120, 'new_value': 125}]
2025-05-23 08:08:51,861 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMF71
2025-05-23 08:08:51,861 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJ943QH9Q0I86N3H2U1JD001ESJ_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 68086.0, 'new_value': 68192.0}, {'field': 'amount', 'old_value': 67886.0, 'new_value': 67992.0}, {'field': 'count', 'old_value': 84, 'new_value': 87}, {'field': 'instoreAmount', 'old_value': 68385.0, 'new_value': 68999.0}, {'field': 'instoreCount', 'old_value': 84, 'new_value': 87}]
2025-05-23 08:08:52,314 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMG71
2025-05-23 08:08:52,314 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJGUIA2QR0I86N3H2U1JH001ESN_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 15379.0, 'new_value': 15541.0}, {'field': 'amount', 'old_value': 15379.0, 'new_value': 15541.0}, {'field': 'count', 'old_value': 25, 'new_value': 26}, {'field': 'instoreAmount', 'old_value': 15379.0, 'new_value': 15541.0}, {'field': 'instoreCount', 'old_value': 25, 'new_value': 26}]
2025-05-23 08:08:52,752 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMH71
2025-05-23 08:08:52,752 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJP082LC00I86N3H2U1JM001ESS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 20125.77, 'new_value': 21201.47}, {'field': 'amount', 'old_value': 20125.07, 'new_value': 21200.77}, {'field': 'count', 'old_value': 80, 'new_value': 84}, {'field': 'instoreAmount', 'old_value': 20125.77, 'new_value': 21201.47}, {'field': 'instoreCount', 'old_value': 80, 'new_value': 84}]
2025-05-23 08:08:53,173 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMI71
2025-05-23 08:08:53,173 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDOR4RUGJG0I86N3H2U1MC001EVI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 37548.0, 'new_value': 37576.0}, {'field': 'dailyBillAmount', 'old_value': 37548.0, 'new_value': 37576.0}, {'field': 'amount', 'old_value': 37747.0, 'new_value': 37775.0}, {'field': 'count', 'old_value': 90, 'new_value': 92}, {'field': 'instoreAmount', 'old_value': 38993.0, 'new_value': 39021.0}, {'field': 'instoreCount', 'old_value': 90, 'new_value': 92}]
2025-05-23 08:08:53,580 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMJ71
2025-05-23 08:08:53,580 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 308978.41, 'new_value': 313310.91}, {'field': 'dailyBillAmount', 'old_value': 288714.48, 'new_value': 293046.98}, {'field': 'amount', 'old_value': 307115.82, 'new_value': 311331.14}, {'field': 'count', 'old_value': 608, 'new_value': 617}, {'field': 'instoreAmount', 'old_value': 309150.92, 'new_value': 314818.94}, {'field': 'instoreCount', 'old_value': 608, 'new_value': 617}]
2025-05-23 08:08:54,064 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMK71
2025-05-23 08:08:54,064 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDPJQDDVRC0I86N3H2U1MO001EVU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 55697.0, 'new_value': 56741.0}, {'field': 'amount', 'old_value': 55697.0, 'new_value': 56741.0}, {'field': 'count', 'old_value': 253, 'new_value': 258}, {'field': 'instoreAmount', 'old_value': 56451.0, 'new_value': 57495.0}, {'field': 'instoreCount', 'old_value': 253, 'new_value': 258}]
2025-05-23 08:08:54,564 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AML71
2025-05-23 08:08:54,564 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDULBESLOI0I86N3H2U1PE001F2K_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 65370.579999999994, 'new_value': 68849.08}, {'field': 'dailyBillAmount', 'old_value': 65370.579999999994, 'new_value': 68849.08}, {'field': 'amount', 'old_value': 68633.09999999999, 'new_value': 72111.59999999999}, {'field': 'count', 'old_value': 413, 'new_value': 439}, {'field': 'instoreAmount', 'old_value': 68633.09999999999, 'new_value': 72111.59999999999}, {'field': 'instoreCount', 'old_value': 413, 'new_value': 439}]
2025-05-23 08:08:55,127 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMM71
2025-05-23 08:08:55,127 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_2025-05, 变更字段: [{'field': 'amount', 'old_value': 31572.67, 'new_value': 31633.07}, {'field': 'count', 'old_value': 3087, 'new_value': 3091}, {'field': 'instoreAmount', 'old_value': 33693.8, 'new_value': 33754.2}, {'field': 'instoreCount', 'old_value': 3087, 'new_value': 3091}]
2025-05-23 08:08:55,611 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMN71
2025-05-23 08:08:55,611 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 525166.86, 'new_value': 538088.75}, {'field': 'dailyBillAmount', 'old_value': 525166.86, 'new_value': 538088.75}, {'field': 'amount', 'old_value': 540798.27, 'new_value': 552634.18}, {'field': 'count', 'old_value': 5173, 'new_value': 5398}, {'field': 'instoreAmount', 'old_value': 411628.64, 'new_value': 418810.59}, {'field': 'instoreCount', 'old_value': 2024, 'new_value': 2079}, {'field': 'onlineAmount', 'old_value': 133694.85, 'new_value': 138606.15}, {'field': 'onlineCount', 'old_value': 3149, 'new_value': 3319}]
2025-05-23 08:08:56,033 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMO71
2025-05-23 08:08:56,033 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVIE6UN1G0I86N3H2U1PU001F34_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 165653.08, 'new_value': 166996.78}, {'field': 'amount', 'old_value': 165653.08, 'new_value': 166996.78}, {'field': 'count', 'old_value': 1112, 'new_value': 1127}, {'field': 'instoreAmount', 'old_value': 166088.08, 'new_value': 167431.78}, {'field': 'instoreCount', 'old_value': 1112, 'new_value': 1127}]
2025-05-23 08:08:56,455 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMQ71
2025-05-23 08:08:56,455 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 82331.85, 'new_value': 84642.71}, {'field': 'dailyBillAmount', 'old_value': 82331.85, 'new_value': 84642.71}, {'field': 'amount', 'old_value': 97142.23, 'new_value': 101964.34}, {'field': 'count', 'old_value': 4479, 'new_value': 4664}, {'field': 'instoreAmount', 'old_value': 49479.92, 'new_value': 51998.58}, {'field': 'instoreCount', 'old_value': 2620, 'new_value': 2705}, {'field': 'onlineAmount', 'old_value': 48688.94, 'new_value': 51045.08}, {'field': 'onlineCount', 'old_value': 1859, 'new_value': 1959}]
2025-05-23 08:08:56,861 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS71
2025-05-23 08:08:56,861 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1EFF1UHG0I86N3H2U1R1001F47_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 38503.0, 'new_value': 45682.0}, {'field': 'amount', 'old_value': 38503.0, 'new_value': 45682.0}, {'field': 'count', 'old_value': 24, 'new_value': 26}, {'field': 'instoreAmount', 'old_value': 38503.0, 'new_value': 45682.0}, {'field': 'instoreCount', 'old_value': 24, 'new_value': 26}]
2025-05-23 08:08:57,298 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT71
2025-05-23 08:08:57,298 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1T4E94FK0I86N3H2U1R9001F4F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 105881.13, 'new_value': 108296.05}, {'field': 'dailyBillAmount', 'old_value': 105881.13, 'new_value': 108296.05}, {'field': 'amount', 'old_value': 50913.0, 'new_value': 52386.63}, {'field': 'count', 'old_value': 3369, 'new_value': 3557}, {'field': 'instoreAmount', 'old_value': 7330.0, 'new_value': 7372.4}, {'field': 'instoreCount', 'old_value': 309, 'new_value': 314}, {'field': 'onlineAmount', 'old_value': 43583.0, 'new_value': 45014.23}, {'field': 'onlineCount', 'old_value': 3060, 'new_value': 3243}]
2025-05-23 08:08:57,720 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU71
2025-05-23 08:08:57,720 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE25DAIM3B0I86N3H2U1RD001F4J_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 294654.8, 'new_value': 304064.41}, {'field': 'dailyBillAmount', 'old_value': 294654.8, 'new_value': 304064.41}, {'field': 'amount', 'old_value': 273552.93, 'new_value': 283536.23}, {'field': 'count', 'old_value': 2401, 'new_value': 2488}, {'field': 'instoreAmount', 'old_value': 199411.79, 'new_value': 205762.09}, {'field': 'instoreCount', 'old_value': 972, 'new_value': 1014}, {'field': 'onlineAmount', 'old_value': 74142.36, 'new_value': 77775.36}, {'field': 'onlineCount', 'old_value': 1429, 'new_value': 1474}]
2025-05-23 08:08:58,158 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV71
2025-05-23 08:08:58,158 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE2CVHLBFV0I86N3H2U1RH001F4N_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 330936.55, 'new_value': 340098.96}, {'field': 'dailyBillAmount', 'old_value': 330936.55, 'new_value': 340098.96}, {'field': 'amount', 'old_value': 339521.66, 'new_value': 346824.06}, {'field': 'count', 'old_value': 2054, 'new_value': 2097}, {'field': 'instoreAmount', 'old_value': 309133.76, 'new_value': 316436.16}, {'field': 'instoreCount', 'old_value': 1726, 'new_value': 1769}]
2025-05-23 08:08:58,626 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW71
2025-05-23 08:08:58,626 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_2025-05, 变更字段: [{'field': 'amount', 'old_value': 941450.08, 'new_value': 969536.1}, {'field': 'count', 'old_value': 5150, 'new_value': 5358}, {'field': 'instoreAmount', 'old_value': 710055.98, 'new_value': 730187.41}, {'field': 'instoreCount', 'old_value': 2839, 'new_value': 2931}, {'field': 'onlineAmount', 'old_value': 238801.0, 'new_value': 247367.26}, {'field': 'onlineCount', 'old_value': 2311, 'new_value': 2427}]
2025-05-23 08:08:59,111 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMX71
2025-05-23 08:08:59,111 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE3PJ2TE9A0I86N3H2U1S3001F59_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 280887.93, 'new_value': 288095.05}, {'field': 'dailyBillAmount', 'old_value': 280887.93, 'new_value': 288095.05}, {'field': 'amount', 'old_value': 397545.42, 'new_value': 408007.22}, {'field': 'count', 'old_value': 1842, 'new_value': 1904}, {'field': 'instoreAmount', 'old_value': 373514.42, 'new_value': 383031.72}, {'field': 'instoreCount', 'old_value': 1482, 'new_value': 1525}, {'field': 'onlineAmount', 'old_value': 24581.5, 'new_value': 25565.7}, {'field': 'onlineCount', 'old_value': 360, 'new_value': 379}]
2025-05-23 08:08:59,626 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMY71
2025-05-23 08:08:59,626 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE48KDQ6AS0I86N3H2U1SB001F5H_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 325968.71, 'new_value': 333585.46}, {'field': 'dailyBillAmount', 'old_value': 325968.71, 'new_value': 333585.46}, {'field': 'amount', 'old_value': 307908.1, 'new_value': 314474.9}, {'field': 'count', 'old_value': 1373, 'new_value': 1408}, {'field': 'instoreAmount', 'old_value': 312762.7, 'new_value': 319550.5}, {'field': 'instoreCount', 'old_value': 1373, 'new_value': 1408}]
2025-05-23 08:09:00,048 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMCC
2025-05-23 08:09:00,048 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE4GES1U770I86N3H2U1SF001F5L_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 671272.68, 'new_value': 688927.68}, {'field': 'amount', 'old_value': 671271.98, 'new_value': 688926.98}, {'field': 'count', 'old_value': 5323, 'new_value': 5499}, {'field': 'instoreAmount', 'old_value': 671272.68, 'new_value': 688927.68}, {'field': 'instoreCount', 'old_value': 5323, 'new_value': 5499}]
2025-05-23 08:09:00,486 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMZ71
2025-05-23 08:09:00,486 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 535416.46, 'new_value': 553324.78}, {'field': 'dailyBillAmount', 'old_value': 535416.46, 'new_value': 553324.78}, {'field': 'amount', 'old_value': 671891.21, 'new_value': 692213.81}, {'field': 'count', 'old_value': 4655, 'new_value': 4810}, {'field': 'instoreAmount', 'old_value': 375044.6, 'new_value': 384009.7}, {'field': 'instoreCount', 'old_value': 1970, 'new_value': 2023}, {'field': 'onlineAmount', 'old_value': 306275.8, 'new_value': 317776.6}, {'field': 'onlineCount', 'old_value': 2685, 'new_value': 2787}]
2025-05-23 08:09:00,955 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM081
2025-05-23 08:09:00,955 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 341033.01, 'new_value': 349799.51}, {'field': 'dailyBillAmount', 'old_value': 341033.01, 'new_value': 349799.51}, {'field': 'amount', 'old_value': 412046.65, 'new_value': 420189.85}, {'field': 'count', 'old_value': 4478, 'new_value': 4620}, {'field': 'instoreAmount', 'old_value': 285778.22000000003, 'new_value': 289610.02}, {'field': 'instoreCount', 'old_value': 1937, 'new_value': 1976}, {'field': 'onlineAmount', 'old_value': 127944.27, 'new_value': 132288.72}, {'field': 'onlineCount', 'old_value': 2541, 'new_value': 2644}]
2025-05-23 08:09:01,439 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM181
2025-05-23 08:09:01,439 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEA4041D6F0I86N3H2U1V9001F8F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 434288.33999999997, 'new_value': 448541.23}, {'field': 'dailyBillAmount', 'old_value': 434288.33999999997, 'new_value': 448541.23}, {'field': 'amount', 'old_value': 440536.83, 'new_value': 455123.09}, {'field': 'count', 'old_value': 4166, 'new_value': 4307}, {'field': 'instoreAmount', 'old_value': 384474.9, 'new_value': 396845.05}, {'field': 'instoreCount', 'old_value': 2174, 'new_value': 2256}, {'field': 'onlineAmount', 'old_value': 57057.23, 'new_value': 59303.08}, {'field': 'onlineCount', 'old_value': 1992, 'new_value': 2051}]
2025-05-23 08:09:01,908 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM281
2025-05-23 08:09:01,908 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAB2RFI0N0I86N3H2U1VD001F8J_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 110182.8, 'new_value': 114520.8}, {'field': 'amount', 'old_value': 110182.3, 'new_value': 114520.3}, {'field': 'count', 'old_value': 491, 'new_value': 513}, {'field': 'instoreAmount', 'old_value': 110182.8, 'new_value': 114520.8}, {'field': 'instoreCount', 'old_value': 491, 'new_value': 513}]
2025-05-23 08:09:02,439 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM381
2025-05-23 08:09:02,439 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAIN5DMKK0I86N3H2U1VH001F8N_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 305780.39, 'new_value': 312421.14}, {'field': 'dailyBillAmount', 'old_value': 305780.39, 'new_value': 312421.14}, {'field': 'amount', 'old_value': -233772.48, 'new_value': -240613.03}, {'field': 'count', 'old_value': 847, 'new_value': 869}, {'field': 'instoreAmount', 'old_value': 5971.6, 'new_value': 6023.5}, {'field': 'instoreCount', 'old_value': 283, 'new_value': 287}, {'field': 'onlineAmount', 'old_value': 17786.02, 'new_value': 18351.67}, {'field': 'onlineCount', 'old_value': 564, 'new_value': 582}]
2025-05-23 08:09:02,845 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM481
2025-05-23 08:09:02,845 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 516634.72, 'new_value': 530172.25}, {'field': 'dailyBillAmount', 'old_value': 516634.72, 'new_value': 530172.25}, {'field': 'amount', 'old_value': 395593.59, 'new_value': 402147.31}, {'field': 'count', 'old_value': 1654, 'new_value': 1685}, {'field': 'instoreAmount', 'old_value': 395593.59, 'new_value': 402147.31}, {'field': 'instoreCount', 'old_value': 1654, 'new_value': 1685}]
2025-05-23 08:09:03,205 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM581
2025-05-23 08:09:03,205 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 322176.61, 'new_value': 335684.52}, {'field': 'dailyBillAmount', 'old_value': 322176.61, 'new_value': 335684.52}, {'field': 'amount', 'old_value': 137812.2, 'new_value': 141639.0}, {'field': 'count', 'old_value': 574, 'new_value': 587}, {'field': 'instoreAmount', 'old_value': 143802.6, 'new_value': 147629.4}, {'field': 'instoreCount', 'old_value': 555, 'new_value': 568}]
2025-05-23 08:09:03,595 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM681
2025-05-23 08:09:03,595 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBP5Q03GB0I86N3H2U106001F9C_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 269625.42, 'new_value': 274056.05}, {'field': 'dailyBillAmount', 'old_value': 269625.42, 'new_value': 274056.05}, {'field': 'amount', 'old_value': 261179.55000000002, 'new_value': 265274.69}, {'field': 'count', 'old_value': 1708, 'new_value': 1751}, {'field': 'instoreAmount', 'old_value': 246637.99, 'new_value': 250118.28}, {'field': 'instoreCount', 'old_value': 1329, 'new_value': 1353}, {'field': 'onlineAmount', 'old_value': 14684.32, 'new_value': 15320.56}, {'field': 'onlineCount', 'old_value': 379, 'new_value': 398}]
2025-05-23 08:09:04,033 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM781
2025-05-23 08:09:04,033 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 286399.55, 'new_value': 294893.42}, {'field': 'dailyBillAmount', 'old_value': 286399.55, 'new_value': 294893.42}, {'field': 'amount', 'old_value': 123520.92, 'new_value': 125588.83}, {'field': 'count', 'old_value': 2012, 'new_value': 2091}, {'field': 'instoreAmount', 'old_value': 71945.4, 'new_value': 72677.91}, {'field': 'instoreCount', 'old_value': 527, 'new_value': 541}, {'field': 'onlineAmount', 'old_value': 51578.77, 'new_value': 52914.17}, {'field': 'onlineCount', 'old_value': 1485, 'new_value': 1550}]
2025-05-23 08:09:04,470 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM881
2025-05-23 08:09:04,470 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_2025-05, 变更字段: [{'field': 'amount', 'old_value': 51392.0, 'new_value': 56166.0}, {'field': 'count', 'old_value': 28, 'new_value': 30}, {'field': 'instoreAmount', 'old_value': 51392.0, 'new_value': 56166.0}, {'field': 'instoreCount', 'old_value': 28, 'new_value': 30}]
2025-05-23 08:09:04,876 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM981
2025-05-23 08:09:04,876 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 118217.06, 'new_value': 123262.56999999999}, {'field': 'amount', 'old_value': 118206.97, 'new_value': 123252.58}, {'field': 'count', 'old_value': 5349, 'new_value': 5594}, {'field': 'instoreAmount', 'old_value': 42557.59, 'new_value': 43902.28}, {'field': 'instoreCount', 'old_value': 1720, 'new_value': 1777}, {'field': 'onlineAmount', 'old_value': 80446.63, 'new_value': 84235.65}, {'field': 'onlineCount', 'old_value': 3629, 'new_value': 3817}]
2025-05-23 08:09:05,470 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMA81
2025-05-23 08:09:05,470 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRPJDJBMAPL77QBECDAL3H1H001J5I_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 36467.9, 'new_value': 38441.9}, {'field': 'amount', 'old_value': 36467.9, 'new_value': 38441.9}, {'field': 'count', 'old_value': 169, 'new_value': 174}, {'field': 'instoreAmount', 'old_value': 36467.9, 'new_value': 38441.9}, {'field': 'instoreCount', 'old_value': 169, 'new_value': 174}]
2025-05-23 08:09:05,939 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMB81
2025-05-23 08:09:05,939 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRR231GUB4QN7QBECDAL3H28001J69_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 334352.07, 'new_value': 343423.07}, {'field': 'dailyBillAmount', 'old_value': 334352.07, 'new_value': 343423.07}, {'field': 'amount', 'old_value': 134053.7, 'new_value': 137566.3}, {'field': 'count', 'old_value': 2498, 'new_value': 2567}, {'field': 'instoreAmount', 'old_value': 135220.1, 'new_value': 138735.2}, {'field': 'instoreCount', 'old_value': 2498, 'new_value': 2567}]
2025-05-23 08:09:06,470 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMC81
2025-05-23 08:09:06,470 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B358872E0DBE420182AF77D4C47644F6_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 137121.26, 'new_value': 141046.7}, {'field': 'amount', 'old_value': 137120.06, 'new_value': 141045.5}, {'field': 'count', 'old_value': 3257, 'new_value': 3366}, {'field': 'instoreAmount', 'old_value': 137378.74, 'new_value': 141304.18}, {'field': 'instoreCount', 'old_value': 3257, 'new_value': 3366}]
2025-05-23 08:09:06,908 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMD81
2025-05-23 08:09:06,908 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 25539.010000000002, 'new_value': 26639.18}, {'field': 'amount', 'old_value': 25535.66, 'new_value': 26635.829999999998}, {'field': 'count', 'old_value': 1494, 'new_value': 1564}, {'field': 'instoreAmount', 'old_value': 13677.34, 'new_value': 13985.84}, {'field': 'instoreCount', 'old_value': 675, 'new_value': 692}, {'field': 'onlineAmount', 'old_value': 12335.18, 'new_value': 13126.85}, {'field': 'onlineCount', 'old_value': 819, 'new_value': 872}]
2025-05-23 08:09:07,330 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AME81
2025-05-23 08:09:07,330 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_E214E9E9A4534AE1943BBACB09056E2E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 40801.5, 'new_value': 43384.9}, {'field': 'amount', 'old_value': 40801.5, 'new_value': 43384.9}, {'field': 'count', 'old_value': 106, 'new_value': 110}, {'field': 'instoreAmount', 'old_value': 40801.5, 'new_value': 43384.9}, {'field': 'instoreCount', 'old_value': 106, 'new_value': 110}]
2025-05-23 08:09:07,830 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMH81
2025-05-23 08:09:07,830 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 162079.5, 'new_value': 167969.94}, {'field': 'dailyBillAmount', 'old_value': 135424.7, 'new_value': 139667.3}, {'field': 'amount', 'old_value': 162078.82, 'new_value': 167969.26}, {'field': 'count', 'old_value': 2256, 'new_value': 2348}, {'field': 'instoreAmount', 'old_value': 155649.0, 'new_value': 160873.5}, {'field': 'instoreCount', 'old_value': 1964, 'new_value': 2042}, {'field': 'onlineAmount', 'old_value': 6657.62, 'new_value': 7323.56}, {'field': 'onlineCount', 'old_value': 292, 'new_value': 306}]
2025-05-23 08:09:08,267 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMI81
2025-05-23 08:09:08,267 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0BI1TJFBK3IKSIOCDI7LH001FSI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 23956.64, 'new_value': 24754.74}, {'field': 'amount', 'old_value': 23955.84, 'new_value': 24753.94}, {'field': 'count', 'old_value': 1021, 'new_value': 1052}, {'field': 'instoreAmount', 'old_value': 20065.94, 'new_value': 20561.94}, {'field': 'instoreCount', 'old_value': 912, 'new_value': 937}, {'field': 'onlineAmount', 'old_value': 3930.9, 'new_value': 4233.0}, {'field': 'onlineCount', 'old_value': 109, 'new_value': 115}]
2025-05-23 08:09:08,642 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMJ81
2025-05-23 08:09:08,642 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 333292.21, 'new_value': 343926.05}, {'field': 'dailyBillAmount', 'old_value': 333292.21, 'new_value': 343926.05}, {'field': 'amount', 'old_value': 428863.66, 'new_value': 442713.67}, {'field': 'count', 'old_value': 4432, 'new_value': 4632}, {'field': 'instoreAmount', 'old_value': 405304.15, 'new_value': 417329.83}, {'field': 'instoreCount', 'old_value': 3092, 'new_value': 3213}, {'field': 'onlineAmount', 'old_value': 32937.1, 'new_value': 34761.43}, {'field': 'onlineCount', 'old_value': 1340, 'new_value': 1419}]
2025-05-23 08:09:09,080 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AML81
2025-05-23 08:09:09,080 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 129250.77, 'new_value': 132329.92}, {'field': 'dailyBillAmount', 'old_value': 129250.77, 'new_value': 132329.92}, {'field': 'amount', 'old_value': 32686.87, 'new_value': 33817.25}, {'field': 'count', 'old_value': 526, 'new_value': 543}, {'field': 'instoreAmount', 'old_value': 20740.24, 'new_value': 21673.61}, {'field': 'instoreCount', 'old_value': 270, 'new_value': 279}, {'field': 'onlineAmount', 'old_value': 12810.64, 'new_value': 13007.65}, {'field': 'onlineCount', 'old_value': 256, 'new_value': 264}]
2025-05-23 08:09:09,533 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMM81
2025-05-23 08:09:09,533 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 128968.08, 'new_value': 131448.19}, {'field': 'amount', 'old_value': 128966.2, 'new_value': 131446.31}, {'field': 'count', 'old_value': 7279, 'new_value': 7424}, {'field': 'onlineAmount', 'old_value': 50282.84, 'new_value': 52762.95}, {'field': 'onlineCount', 'old_value': 2823, 'new_value': 2968}]
2025-05-23 08:09:09,986 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMN81
2025-05-23 08:09:09,986 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 67016.49, 'new_value': 69996.91}, {'field': 'amount', 'old_value': 67007.02, 'new_value': 69987.44}, {'field': 'count', 'old_value': 4257, 'new_value': 4457}, {'field': 'instoreAmount', 'old_value': 29965.74, 'new_value': 31077.04}, {'field': 'instoreCount', 'old_value': 1728, 'new_value': 1798}, {'field': 'onlineAmount', 'old_value': 38961.14, 'new_value': 40914.03}, {'field': 'onlineCount', 'old_value': 2529, 'new_value': 2659}]
2025-05-23 08:09:10,423 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMO81
2025-05-23 08:09:10,423 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V_2025-05, 变更字段: [{'field': 'amount', 'old_value': 134154.58, 'new_value': 137094.2}, {'field': 'count', 'old_value': 1326, 'new_value': 1368}, {'field': 'instoreAmount', 'old_value': 134308.06, 'new_value': 137293.58}, {'field': 'instoreCount', 'old_value': 1326, 'new_value': 1368}]
2025-05-23 08:09:10,892 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMP81
2025-05-23 08:09:10,892 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 110073.98, 'new_value': 113652.91}, {'field': 'dailyBillAmount', 'old_value': 113776.42, 'new_value': 117495.45}, {'field': 'amount', 'old_value': 110068.17, 'new_value': 113647.1}, {'field': 'count', 'old_value': 2188, 'new_value': 2283}, {'field': 'instoreAmount', 'old_value': 105495.37, 'new_value': 108884.82}, {'field': 'instoreCount', 'old_value': 1835, 'new_value': 1913}, {'field': 'onlineAmount', 'old_value': 4674.85, 'new_value': 4864.33}, {'field': 'onlineCount', 'old_value': 353, 'new_value': 370}]
2025-05-23 08:09:11,455 - INFO - 更新表单数据成功: FINST-OLF665813NVUOH19CCHMM8B7MMTW2NSGNXX9MYA
2025-05-23 08:09:11,455 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_2025-04, 变更字段: [{'field': 'recommendAmount', 'old_value': 831131.39, 'new_value': 822393.79}, {'field': 'amount', 'old_value': 831131.39, 'new_value': 822387.68}, {'field': 'count', 'old_value': 1867, 'new_value': 1848}, {'field': 'instoreAmount', 'old_value': 783399.8, 'new_value': 774662.2}, {'field': 'instoreCount', 'old_value': 1487, 'new_value': 1468}]
2025-05-23 08:09:12,048 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMQ81
2025-05-23 08:09:12,048 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 169351.42, 'new_value': 179983.06}, {'field': 'dailyBillAmount', 'old_value': 169351.42, 'new_value': 179983.06}, {'field': 'amount', 'old_value': 21934.98, 'new_value': 23080.72}, {'field': 'count', 'old_value': 875, 'new_value': 922}, {'field': 'instoreAmount', 'old_value': 25406.87, 'new_value': 26610.11}, {'field': 'instoreCount', 'old_value': 875, 'new_value': 922}]
2025-05-23 08:09:12,392 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR81
2025-05-23 08:09:12,392 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIP3GSKTFR6E7AERKQ83J2001UN4_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 437474.79, 'new_value': 458394.49}, {'field': 'dailyBillAmount', 'old_value': 437474.79, 'new_value': 458394.49}, {'field': 'amount', 'old_value': 44405.36, 'new_value': 46332.159999999996}, {'field': 'count', 'old_value': 218, 'new_value': 228}, {'field': 'instoreAmount', 'old_value': 44631.36, 'new_value': 46558.159999999996}, {'field': 'instoreCount', 'old_value': 218, 'new_value': 228}]
2025-05-23 08:09:12,861 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS81
2025-05-23 08:09:12,861 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPC0VTE5M6E7AERKQ83J5001UN7_2025-05, 变更字段: [{'field': 'amount', 'old_value': 15003.1, 'new_value': 15667.19}, {'field': 'count', 'old_value': 768, 'new_value': 804}, {'field': 'onlineAmount', 'old_value': 15135.15, 'new_value': 15799.24}, {'field': 'onlineCount', 'old_value': 768, 'new_value': 804}]
2025-05-23 08:09:13,298 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT81
2025-05-23 08:09:13,298 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPGJC2SUF6E7AERKQ83J8001UNA_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 259533.17, 'new_value': 278187.34}, {'field': 'amount', 'old_value': 259379.39, 'new_value': 278033.56}, {'field': 'count', 'old_value': 2683, 'new_value': 2866}, {'field': 'instoreAmount', 'old_value': 247958.3, 'new_value': 264893.2}, {'field': 'instoreCount', 'old_value': 2267, 'new_value': 2412}, {'field': 'onlineAmount', 'old_value': 16787.25, 'new_value': 18527.45}, {'field': 'onlineCount', 'old_value': 416, 'new_value': 454}]
2025-05-23 08:09:13,751 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU81
2025-05-23 08:09:13,751 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPK0KE3MN6E7AERKQ83JB001UND_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 151316.61, 'new_value': 159680.8}, {'field': 'dailyBillAmount', 'old_value': 147516.13999999998, 'new_value': 155880.33}, {'field': 'amount', 'old_value': 114651.34, 'new_value': 120091.99}, {'field': 'count', 'old_value': 4129, 'new_value': 4319}, {'field': 'instoreAmount', 'old_value': 50836.58, 'new_value': 52663.26}, {'field': 'instoreCount', 'old_value': 1769, 'new_value': 1826}, {'field': 'onlineAmount', 'old_value': 65485.04, 'new_value': 69249.15}, {'field': 'onlineCount', 'old_value': 2360, 'new_value': 2493}]
2025-05-23 08:09:14,173 - INFO - 更新表单数据成功: FINST-VRA66VA1DNZUAADJA7KJPA7PQK7Q37OC8W8AM7W
2025-05-23 08:09:14,173 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 47511.18, 'new_value': 53417.06}, {'field': 'dailyBillAmount', 'old_value': 47511.18, 'new_value': 53417.06}, {'field': 'amount', 'old_value': 3931.92, 'new_value': 4344.15}, {'field': 'count', 'old_value': 179, 'new_value': 199}, {'field': 'instoreAmount', 'old_value': 3931.92, 'new_value': 4344.15}, {'field': 'instoreCount', 'old_value': 179, 'new_value': 199}]
2025-05-23 08:09:14,642 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV81
2025-05-23 08:09:14,642 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_2025-05, 变更字段: [{'field': 'amount', 'old_value': 5248.54, 'new_value': 5382.42}, {'field': 'count', 'old_value': 229, 'new_value': 237}, {'field': 'onlineAmount', 'old_value': 5248.54, 'new_value': 5382.42}, {'field': 'onlineCount', 'old_value': 229, 'new_value': 237}]
2025-05-23 08:09:15,111 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW81
2025-05-23 08:09:15,111 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQ716B84L7AV8LHQQGID0001EJ9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 90914.96, 'new_value': 94295.39}, {'field': 'dailyBillAmount', 'old_value': 46026.14, 'new_value': 47208.74}, {'field': 'amount', 'old_value': 90914.37, 'new_value': 94294.8}, {'field': 'count', 'old_value': 2230, 'new_value': 2306}, {'field': 'instoreAmount', 'old_value': 49128.37, 'new_value': 50889.46}, {'field': 'instoreCount', 'old_value': 1200, 'new_value': 1234}, {'field': 'onlineAmount', 'old_value': 43954.12, 'new_value': 45655.36}, {'field': 'onlineCount', 'old_value': 1030, 'new_value': 1072}]
2025-05-23 08:09:15,533 - INFO - 更新表单数据成功: FINST-2S666NA1S1BVINX7DH9TK9J53ZTQ3U9ODWIAM79
2025-05-23 08:09:15,533 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQG1BD1C36E7AERKQ83JN001UNP_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 40436.06, 'new_value': 45479.47}, {'field': 'amount', 'old_value': 40436.06, 'new_value': 45479.47}, {'field': 'count', 'old_value': 1540, 'new_value': 1701}, {'field': 'instoreAmount', 'old_value': 41006.82, 'new_value': 46050.23}, {'field': 'instoreCount', 'old_value': 1540, 'new_value': 1701}]
2025-05-23 08:09:15,970 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMX81
2025-05-23 08:09:15,970 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 42936.86, 'new_value': 45668.23}, {'field': 'dailyBillAmount', 'old_value': 42936.86, 'new_value': 45668.23}, {'field': 'amount', 'old_value': 34394.08, 'new_value': 36038.52}, {'field': 'count', 'old_value': 1552, 'new_value': 1630}, {'field': 'instoreAmount', 'old_value': 19586.47, 'new_value': 19934.97}, {'field': 'instoreCount', 'old_value': 667, 'new_value': 683}, {'field': 'onlineAmount', 'old_value': 14881.85, 'new_value': 16177.79}, {'field': 'onlineCount', 'old_value': 885, 'new_value': 947}]
2025-05-23 08:09:16,439 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMY81
2025-05-23 08:09:16,439 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQTRHD5AN6E7AERKQ83JT001UNV_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 72020.36, 'new_value': 74747.65}, {'field': 'amount', 'old_value': 72020.36, 'new_value': 74747.65}, {'field': 'count', 'old_value': 2204, 'new_value': 2294}, {'field': 'instoreAmount', 'old_value': 28607.27, 'new_value': 29624.81}, {'field': 'instoreCount', 'old_value': 1093, 'new_value': 1135}, {'field': 'onlineAmount', 'old_value': 43514.58, 'new_value': 45224.33}, {'field': 'onlineCount', 'old_value': 1111, 'new_value': 1159}]
2025-05-23 08:09:16,923 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMZ81
2025-05-23 08:09:16,939 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 43114.38, 'new_value': 44941.29}, {'field': 'amount', 'old_value': 43113.48, 'new_value': 44940.39}, {'field': 'count', 'old_value': 1008, 'new_value': 1065}, {'field': 'instoreAmount', 'old_value': 33500.25, 'new_value': 34931.3}, {'field': 'instoreCount', 'old_value': 811, 'new_value': 855}, {'field': 'onlineAmount', 'old_value': 9984.66, 'new_value': 10460.52}, {'field': 'onlineCount', 'old_value': 197, 'new_value': 210}]
2025-05-23 08:09:17,423 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM091
2025-05-23 08:09:17,439 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 204589.85, 'new_value': 215914.69}, {'field': 'dailyBillAmount', 'old_value': 204589.85, 'new_value': 215914.69}, {'field': 'amount', 'old_value': 134431.09, 'new_value': 140242.98}, {'field': 'count', 'old_value': 3358, 'new_value': 3519}, {'field': 'instoreAmount', 'old_value': 85003.97, 'new_value': 88789.67}, {'field': 'instoreCount', 'old_value': 1668, 'new_value': 1744}, {'field': 'onlineAmount', 'old_value': 60039.06, 'new_value': 62931.17}, {'field': 'onlineCount', 'old_value': 1690, 'new_value': 1775}]
2025-05-23 08:09:17,845 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM191
2025-05-23 08:09:17,845 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 619045.91, 'new_value': 637396.92}, {'field': 'dailyBillAmount', 'old_value': 619045.91, 'new_value': 637396.92}, {'field': 'amount', 'old_value': 564363.2, 'new_value': 579433.0}, {'field': 'count', 'old_value': 3360, 'new_value': 3459}, {'field': 'instoreAmount', 'old_value': 399896.8, 'new_value': 410923.7}, {'field': 'instoreCount', 'old_value': 2597, 'new_value': 2676}, {'field': 'onlineAmount', 'old_value': 164468.8, 'new_value': 168511.7}, {'field': 'onlineCount', 'old_value': 763, 'new_value': 783}]
2025-05-23 08:09:18,267 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM291
2025-05-23 08:09:18,267 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRGKS2JA27AV8LHQQGIDC001EJL_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 886954.32, 'new_value': 929948.92}, {'field': 'amount', 'old_value': 886953.82, 'new_value': 929948.42}, {'field': 'count', 'old_value': 3126, 'new_value': 3266}, {'field': 'instoreAmount', 'old_value': 886954.32, 'new_value': 929948.92}, {'field': 'instoreCount', 'old_value': 3126, 'new_value': 3266}]
2025-05-23 08:09:18,736 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM391
2025-05-23 08:09:18,736 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRL782OM57AV8LHQQGIDF001EJO_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 514175.83, 'new_value': 534490.45}, {'field': 'dailyBillAmount', 'old_value': 456927.23, 'new_value': 474231.45999999996}, {'field': 'amount', 'old_value': 514175.83, 'new_value': 534490.45}, {'field': 'count', 'old_value': 3166, 'new_value': 3318}, {'field': 'instoreAmount', 'old_value': 468901.85000000003, 'new_value': 487227.98}, {'field': 'instoreCount', 'old_value': 2002, 'new_value': 2089}, {'field': 'onlineAmount', 'old_value': 45559.18, 'new_value': 47573.6}, {'field': 'onlineCount', 'old_value': 1164, 'new_value': 1229}]
2025-05-23 08:09:19,236 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM491
2025-05-23 08:09:19,236 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 526251.9400000001, 'new_value': 532584.5700000001}, {'field': 'dailyBillAmount', 'old_value': 501531.45, 'new_value': 515813.08}, {'field': 'amount', 'old_value': 526251.9400000001, 'new_value': 532577.98}, {'field': 'count', 'old_value': 1238, 'new_value': 1273}, {'field': 'instoreAmount', 'old_value': 492491.2, 'new_value': 496289.8}, {'field': 'instoreCount', 'old_value': 960, 'new_value': 983}, {'field': 'onlineAmount', 'old_value': 33888.02, 'new_value': 36422.05}, {'field': 'onlineCount', 'old_value': 278, 'new_value': 290}]
2025-05-23 08:09:19,658 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM591
2025-05-23 08:09:19,658 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS34G4B697AV8LHQQGIDL001EJU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 610718.28, 'new_value': 633640.03}, {'field': 'amount', 'old_value': 610717.6, 'new_value': 633639.35}, {'field': 'count', 'old_value': 3167, 'new_value': 3330}, {'field': 'instoreAmount', 'old_value': 576403.27, 'new_value': 596817.46}, {'field': 'instoreCount', 'old_value': 2143, 'new_value': 2224}, {'field': 'onlineAmount', 'old_value': 34431.16, 'new_value': 36938.72}, {'field': 'onlineCount', 'old_value': 1024, 'new_value': 1106}]
2025-05-23 08:09:20,111 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM691
2025-05-23 08:09:20,111 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 712500.93, 'new_value': 749582.89}, {'field': 'dailyBillAmount', 'old_value': 712500.93, 'new_value': 749582.89}, {'field': 'amount', 'old_value': 642964.07, 'new_value': 675368.61}, {'field': 'count', 'old_value': 3210, 'new_value': 3379}, {'field': 'instoreAmount', 'old_value': 589241.67, 'new_value': 619346.27}, {'field': 'instoreCount', 'old_value': 2653, 'new_value': 2799}, {'field': 'onlineAmount', 'old_value': 54451.13, 'new_value': 56751.07}, {'field': 'onlineCount', 'old_value': 557, 'new_value': 580}]
2025-05-23 08:09:20,642 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM791
2025-05-23 08:09:20,642 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISLI3BD9P7AV8LHQQGIDR001EK4_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 160819.24, 'new_value': 174174.84}, {'field': 'dailyBillAmount', 'old_value': 159412.69, 'new_value': 172768.29}, {'field': 'amount', 'old_value': 157571.66, 'new_value': 170908.66}, {'field': 'count', 'old_value': 236, 'new_value': 249}, {'field': 'instoreAmount', 'old_value': 157571.66, 'new_value': 170908.66}, {'field': 'instoreCount', 'old_value': 236, 'new_value': 249}]
2025-05-23 08:09:21,126 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM891
2025-05-23 08:09:21,142 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 143516.72, 'new_value': 147454.38}, {'field': 'dailyBillAmount', 'old_value': 143516.72, 'new_value': 147454.38}, {'field': 'amount', 'old_value': 125623.73, 'new_value': 128743.84}, {'field': 'count', 'old_value': 210, 'new_value': 219}, {'field': 'instoreAmount', 'old_value': 123742.4, 'new_value': 126731.5}, {'field': 'instoreCount', 'old_value': 194, 'new_value': 202}, {'field': 'onlineAmount', 'old_value': 2848.25, 'new_value': 2979.26}, {'field': 'onlineCount', 'old_value': 16, 'new_value': 17}]
2025-05-23 08:09:21,548 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM991
2025-05-23 08:09:21,548 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 19569.079999999998, 'new_value': 20115.6}, {'field': 'amount', 'old_value': 19569.079999999998, 'new_value': 20115.6}, {'field': 'count', 'old_value': 405, 'new_value': 420}, {'field': 'instoreAmount', 'old_value': 19569.079999999998, 'new_value': 20115.6}, {'field': 'instoreCount', 'old_value': 405, 'new_value': 420}]
2025-05-23 08:09:21,986 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMA91
2025-05-23 08:09:21,986 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_C207460918D74AAAB2E154B47B74F863_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 82283.34, 'new_value': 84139.17}, {'field': 'amount', 'old_value': 82283.34, 'new_value': 84139.17}, {'field': 'count', 'old_value': 688, 'new_value': 709}, {'field': 'instoreAmount', 'old_value': 82834.18000000001, 'new_value': 84690.01}, {'field': 'instoreCount', 'old_value': 688, 'new_value': 709}]
2025-05-23 08:09:22,454 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMB91
2025-05-23 08:09:22,454 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 258880.83, 'new_value': 271346.01}, {'field': 'dailyBillAmount', 'old_value': 258880.83, 'new_value': 271346.01}, {'field': 'amount', 'old_value': 277665.56, 'new_value': 290124.23}, {'field': 'count', 'old_value': 7483, 'new_value': 7868}, {'field': 'instoreAmount', 'old_value': 263973.59, 'new_value': 275071.59}, {'field': 'instoreCount', 'old_value': 6784, 'new_value': 7108}, {'field': 'onlineAmount', 'old_value': 18159.56, 'new_value': 19558.23}, {'field': 'onlineCount', 'old_value': 699, 'new_value': 760}]
2025-05-23 08:09:22,892 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMC91
2025-05-23 08:09:22,892 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRCVBQ1LQ2P6AJB6QM8HA4T0011NR_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 81136.02, 'new_value': 89805.54}, {'field': 'dailyBillAmount', 'old_value': 81136.02, 'new_value': 89805.54}, {'field': 'amount', 'old_value': 83164.02, 'new_value': 91833.54}, {'field': 'count', 'old_value': 69, 'new_value': 74}, {'field': 'instoreAmount', 'old_value': 83164.02, 'new_value': 91833.54}, {'field': 'instoreCount', 'old_value': 69, 'new_value': 74}]
2025-05-23 08:09:23,376 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMD91
2025-05-23 08:09:23,376 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD0GVFB5C86AJB6QM8HA590011O7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 695270.64, 'new_value': 744812.8}, {'field': 'dailyBillAmount', 'old_value': 695270.64, 'new_value': 744812.8}, {'field': 'amount', 'old_value': 635983.54, 'new_value': 679216.12}, {'field': 'count', 'old_value': 1604, 'new_value': 1686}, {'field': 'instoreAmount', 'old_value': 660370.48, 'new_value': 706635.7}, {'field': 'instoreCount', 'old_value': 1333, 'new_value': 1402}, {'field': 'onlineAmount', 'old_value': 6030.39, 'new_value': 6274.69}, {'field': 'onlineCount', 'old_value': 271, 'new_value': 284}]
2025-05-23 08:09:23,876 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AME91
2025-05-23 08:09:23,876 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD2F2MCTBD6AJB6QM8HA650011P3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1053768.86, 'new_value': 1100280.96}, {'field': 'amount', 'old_value': 1053768.86, 'new_value': 1100280.96}, {'field': 'count', 'old_value': 3377, 'new_value': 3516}, {'field': 'instoreAmount', 'old_value': 1054979.86, 'new_value': 1101491.96}, {'field': 'instoreCount', 'old_value': 3377, 'new_value': 3516}]
2025-05-23 08:09:24,329 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMF91
2025-05-23 08:09:24,329 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_2025-05, 变更字段: [{'field': 'amount', 'old_value': 643064.42, 'new_value': 677857.46}, {'field': 'count', 'old_value': 2315, 'new_value': 2434}, {'field': 'instoreAmount', 'old_value': 626775.57, 'new_value': 660525.71}, {'field': 'instoreCount', 'old_value': 1393, 'new_value': 1475}, {'field': 'onlineAmount', 'old_value': 28064.52, 'new_value': 29107.52}, {'field': 'onlineCount', 'old_value': 922, 'new_value': 959}]
2025-05-23 08:09:24,783 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMG91
2025-05-23 08:09:24,783 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE29O5UH0D6AJB6QM8HA7I0011QG_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 1546959.29, 'new_value': 1617300.29}, {'field': 'dailyBillAmount', 'old_value': 1546959.29, 'new_value': 1617300.29}, {'field': 'amount', 'old_value': 1595689.0, 'new_value': 1667274.0}, {'field': 'count', 'old_value': 4317, 'new_value': 4467}, {'field': 'instoreAmount', 'old_value': 1595689.0, 'new_value': 1667274.0}, {'field': 'instoreCount', 'old_value': 4317, 'new_value': 4467}]
2025-05-23 08:09:25,251 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMH91
2025-05-23 08:09:25,251 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 238617.06, 'new_value': 247914.43}, {'field': 'dailyBillAmount', 'old_value': 238617.06, 'new_value': 247914.43}, {'field': 'amount', 'old_value': 244705.84, 'new_value': 254003.21}, {'field': 'count', 'old_value': 1321, 'new_value': 1376}, {'field': 'instoreAmount', 'old_value': 237062.6, 'new_value': 245736.0}, {'field': 'instoreCount', 'old_value': 1115, 'new_value': 1159}, {'field': 'onlineAmount', 'old_value': 12577.12, 'new_value': 13201.09}, {'field': 'onlineCount', 'old_value': 206, 'new_value': 217}]
2025-05-23 08:09:25,689 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMI91
2025-05-23 08:09:25,689 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2PLB45826AJB6QM8HA7Q0011QO_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 814220.86, 'new_value': 853463.3200000001}, {'field': 'dailyBillAmount', 'old_value': 814220.86, 'new_value': 853463.3200000001}, {'field': 'amount', 'old_value': 871311.48, 'new_value': 910516.81}, {'field': 'count', 'old_value': 3608, 'new_value': 3782}, {'field': 'instoreAmount', 'old_value': 871311.93, 'new_value': 910517.26}, {'field': 'instoreCount', 'old_value': 3608, 'new_value': 3782}]
2025-05-23 08:09:26,189 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMJ91
2025-05-23 08:09:26,189 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_2025-05, 变更字段: [{'field': 'amount', 'old_value': 602007.0, 'new_value': 622277.0}, {'field': 'count', 'old_value': 992, 'new_value': 1042}, {'field': 'instoreAmount', 'old_value': 597270.48, 'new_value': 617540.48}, {'field': 'instoreCount', 'old_value': 960, 'new_value': 1010}]
2025-05-23 08:09:26,642 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMK91
2025-05-23 08:09:26,642 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE3ODLGMBD6AJB6QM8HA8A0011R8_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 208718.65, 'new_value': 216143.74}, {'field': 'dailyBillAmount', 'old_value': 208718.65, 'new_value': 216143.74}, {'field': 'amount', 'old_value': 241936.3, 'new_value': 250583.3}, {'field': 'count', 'old_value': 1714, 'new_value': 1773}, {'field': 'instoreAmount', 'old_value': 245836.3, 'new_value': 254483.3}, {'field': 'instoreCount', 'old_value': 1714, 'new_value': 1773}]
2025-05-23 08:09:27,079 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMDC
2025-05-23 08:09:27,079 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_101F34500A0D43DF833463DEFB95F423_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 136844.25, 'new_value': 144790.04}, {'field': 'dailyBillAmount', 'old_value': 136844.25, 'new_value': 144790.04}, {'field': 'amount', 'old_value': 108056.47, 'new_value': 115499.47}, {'field': 'count', 'old_value': 716, 'new_value': 763}, {'field': 'instoreAmount', 'old_value': 107799.0, 'new_value': 115242.0}, {'field': 'instoreCount', 'old_value': 666, 'new_value': 713}]
2025-05-23 08:09:27,611 - INFO - 更新表单数据成功: FINST-2XF66ID1O6ZU6RGLC9HPAA12ROM12M9VUG7AMI01
2025-05-23 08:09:27,611 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_17590B062D954DB088AC6EE572EFECE9_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 45689.5, 'new_value': 47053.5}, {'field': 'dailyBillAmount', 'old_value': 45689.5, 'new_value': 47053.5}]
2025-05-23 08:09:28,095 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AML91
2025-05-23 08:09:28,095 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_2025-05, 变更字段: [{'field': 'amount', 'old_value': 131730.76, 'new_value': 136360.14}, {'field': 'count', 'old_value': 6349, 'new_value': 6588}, {'field': 'instoreAmount', 'old_value': 70595.92, 'new_value': 72847.2}, {'field': 'instoreCount', 'old_value': 3605, 'new_value': 3713}, {'field': 'onlineAmount', 'old_value': 64832.88, 'new_value': 67428.18000000001}, {'field': 'onlineCount', 'old_value': 2744, 'new_value': 2875}]
2025-05-23 08:09:28,548 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMM91
2025-05-23 08:09:28,548 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 183276.33, 'new_value': 192841.67}, {'field': 'amount', 'old_value': 183268.19, 'new_value': 192833.53}, {'field': 'count', 'old_value': 3418, 'new_value': 3572}, {'field': 'instoreAmount', 'old_value': 169297.15, 'new_value': 177740.3}, {'field': 'instoreCount', 'old_value': 3156, 'new_value': 3286}, {'field': 'onlineAmount', 'old_value': 13979.18, 'new_value': 15101.37}, {'field': 'onlineCount', 'old_value': 262, 'new_value': 286}]
2025-05-23 08:09:29,095 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMN91
2025-05-23 08:09:29,095 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKLLRRBCQ42F6DB81RH73001P3B_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 27301.7, 'new_value': 27330.7}, {'field': 'amount', 'old_value': 27301.7, 'new_value': 27330.7}, {'field': 'count', 'old_value': 190, 'new_value': 191}, {'field': 'instoreAmount', 'old_value': 27301.7, 'new_value': 27330.7}, {'field': 'instoreCount', 'old_value': 190, 'new_value': 191}]
2025-05-23 08:09:29,579 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMO91
2025-05-23 08:09:29,579 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 45481.7, 'new_value': 51212.7}, {'field': 'dailyBillAmount', 'old_value': 45481.7, 'new_value': 51212.7}, {'field': 'amount', 'old_value': 45193.4, 'new_value': 45277.6}, {'field': 'count', 'old_value': 413, 'new_value': 415}, {'field': 'instoreAmount', 'old_value': 45413.8, 'new_value': 45498.0}, {'field': 'instoreCount', 'old_value': 413, 'new_value': 415}]
2025-05-23 08:09:30,017 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMP91
2025-05-23 08:09:30,017 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEM2RU37BD42F6DB81RH7L001P3T_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 47000.0, 'new_value': 51000.0}, {'field': 'dailyBillAmount', 'old_value': 47000.0, 'new_value': 51000.0}]
2025-05-23 08:09:30,548 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMQ91
2025-05-23 08:09:30,548 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 183926.8, 'new_value': 193191.9}, {'field': 'dailyBillAmount', 'old_value': 183926.8, 'new_value': 193191.9}, {'field': 'amount', 'old_value': 154320.01, 'new_value': 159491.51}, {'field': 'count', 'old_value': 4240, 'new_value': 4434}, {'field': 'instoreAmount', 'old_value': 150521.82, 'new_value': 155560.92}, {'field': 'instoreCount', 'old_value': 4080, 'new_value': 4267}, {'field': 'onlineAmount', 'old_value': 6161.58, 'new_value': 6365.38}, {'field': 'onlineCount', 'old_value': 160, 'new_value': 167}]
2025-05-23 08:09:30,986 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR91
2025-05-23 08:09:30,986 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMMCI75GD42F6DB81RH81001P49_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 46966.8, 'new_value': 48108.0}, {'field': 'dailyBillAmount', 'old_value': 46966.8, 'new_value': 48108.0}, {'field': 'amount', 'old_value': 47062.3, 'new_value': 48203.5}, {'field': 'count', 'old_value': 267, 'new_value': 272}, {'field': 'instoreAmount', 'old_value': 49578.5, 'new_value': 50719.7}, {'field': 'instoreCount', 'old_value': 264, 'new_value': 269}]
2025-05-23 08:09:31,501 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMS91
2025-05-23 08:09:31,501 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO2283NPC42F6DB81RH8Q001P52_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 58294.58, 'new_value': 63630.72}, {'field': 'dailyBillAmount', 'old_value': 58294.58, 'new_value': 63630.72}]
2025-05-23 08:09:31,970 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT91
2025-05-23 08:09:31,970 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 40705.55, 'new_value': 42960.81}, {'field': 'amount', 'old_value': 40705.31, 'new_value': 42960.57}, {'field': 'count', 'old_value': 2365, 'new_value': 2505}, {'field': 'instoreAmount', 'old_value': 41427.87, 'new_value': 43723.88}, {'field': 'instoreCount', 'old_value': 2365, 'new_value': 2505}]
2025-05-23 08:09:32,423 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU91
2025-05-23 08:09:32,423 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEOTOKMGDD42F6DB81RH9E001P5M_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 65485.57, 'new_value': 68706.20999999999}, {'field': 'dailyBillAmount', 'old_value': 65485.57, 'new_value': 68706.20999999999}, {'field': 'amount', 'old_value': 67372.65, 'new_value': 70658.09}, {'field': 'count', 'old_value': 3278, 'new_value': 3453}, {'field': 'instoreAmount', 'old_value': 62702.4, 'new_value': 65668.1}, {'field': 'instoreCount', 'old_value': 3079, 'new_value': 3239}, {'field': 'onlineAmount', 'old_value': 4735.5, 'new_value': 5055.24}, {'field': 'onlineCount', 'old_value': 199, 'new_value': 214}]
2025-05-23 08:09:32,876 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV91
2025-05-23 08:09:32,876 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEPVAL5TUK42F6DB81RHA2001P6A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 44375.98, 'new_value': 46296.52}, {'field': 'amount', 'old_value': 44375.98, 'new_value': 46296.52}, {'field': 'count', 'old_value': 2162, 'new_value': 2267}, {'field': 'instoreAmount', 'old_value': 27663.87, 'new_value': 28777.62}, {'field': 'instoreCount', 'old_value': 1427, 'new_value': 1493}, {'field': 'onlineAmount', 'old_value': 16773.11, 'new_value': 17618.55}, {'field': 'onlineCount', 'old_value': 735, 'new_value': 774}]
2025-05-23 08:09:33,345 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW91
2025-05-23 08:09:33,345 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQ5682UCT42F6DB81RHA6001P6E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 31864.6, 'new_value': 33443.03}, {'field': 'dailyBillAmount', 'old_value': 31864.6, 'new_value': 33443.03}, {'field': 'amount', 'old_value': 22820.15, 'new_value': 23837.71}, {'field': 'count', 'old_value': 917, 'new_value': 959}, {'field': 'instoreAmount', 'old_value': 23009.55, 'new_value': 24092.75}, {'field': 'instoreCount', 'old_value': 917, 'new_value': 959}]
2025-05-23 08:09:33,798 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMT7
2025-05-23 08:09:33,798 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 60565.69, 'new_value': 62877.21}, {'field': 'amount', 'old_value': 60558.99, 'new_value': 62870.51}, {'field': 'count', 'old_value': 3673, 'new_value': 3773}, {'field': 'instoreAmount', 'old_value': 15822.49, 'new_value': 16606.03}, {'field': 'instoreCount', 'old_value': 970, 'new_value': 986}, {'field': 'onlineAmount', 'old_value': 46231.18, 'new_value': 48128.51}, {'field': 'onlineCount', 'old_value': 2703, 'new_value': 2787}]
2025-05-23 08:09:34,204 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMU7
2025-05-23 08:09:34,204 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERF6AAOTL42F6DB81RHAU001P76_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 112088.32, 'new_value': 118711.27}, {'field': 'dailyBillAmount', 'old_value': 112088.32, 'new_value': 118711.27}, {'field': 'amount', 'old_value': 93820.25, 'new_value': 98782.86}, {'field': 'count', 'old_value': 912, 'new_value': 957}, {'field': 'instoreAmount', 'old_value': 93820.25, 'new_value': 98782.86}, {'field': 'instoreCount', 'old_value': 912, 'new_value': 957}]
2025-05-23 08:09:34,626 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMV7
2025-05-23 08:09:34,626 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERL0RJ5BM42F6DB81RHB2001P7A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 86252.15, 'new_value': 89148.15}, {'field': 'dailyBillAmount', 'old_value': 86252.15, 'new_value': 89148.15}, {'field': 'amount', 'old_value': 96371.8, 'new_value': 100134.8}, {'field': 'count', 'old_value': 419, 'new_value': 434}, {'field': 'instoreAmount', 'old_value': 96371.8, 'new_value': 100134.8}, {'field': 'instoreCount', 'old_value': 419, 'new_value': 434}]
2025-05-23 08:09:35,111 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMW7
2025-05-23 08:09:35,111 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERRPAJGP042F6DB81RHB6001P7E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 56258.7, 'new_value': 56886.7}, {'field': 'dailyBillAmount', 'old_value': 56258.7, 'new_value': 56886.7}, {'field': 'amount', 'old_value': 47721.65, 'new_value': 47736.65}, {'field': 'count', 'old_value': 255, 'new_value': 256}, {'field': 'instoreAmount', 'old_value': 49158.65, 'new_value': 49173.65}, {'field': 'instoreCount', 'old_value': 255, 'new_value': 256}]
2025-05-23 08:09:35,564 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMX7
2025-05-23 08:09:35,579 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 111226.0, 'new_value': 116194.0}, {'field': 'amount', 'old_value': 111226.0, 'new_value': 116194.0}, {'field': 'count', 'old_value': 1154, 'new_value': 1193}, {'field': 'instoreAmount', 'old_value': 111226.0, 'new_value': 116194.0}, {'field': 'instoreCount', 'old_value': 1154, 'new_value': 1193}]
2025-05-23 08:09:36,001 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMY7
2025-05-23 08:09:36,001 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 27326.989999999998, 'new_value': 29269.68}, {'field': 'dailyBillAmount', 'old_value': 27326.989999999998, 'new_value': 29269.68}, {'field': 'amount', 'old_value': 4045.14, 'new_value': 4095.44}, {'field': 'count', 'old_value': 170, 'new_value': 174}, {'field': 'instoreAmount', 'old_value': 4496.22, 'new_value': 4559.32}, {'field': 'instoreCount', 'old_value': 170, 'new_value': 174}]
2025-05-23 08:09:36,454 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMZ7
2025-05-23 08:09:36,454 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16HSQFKC90AM6QNN0HOT12RK0013M7_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 19182.48, 'new_value': 20251.66}, {'field': 'dailyBillAmount', 'old_value': 19182.48, 'new_value': 20251.66}, {'field': 'amount', 'old_value': 19847.25, 'new_value': 20916.43}, {'field': 'count', 'old_value': 542, 'new_value': 566}, {'field': 'instoreAmount', 'old_value': 19860.61, 'new_value': 20929.79}, {'field': 'instoreCount', 'old_value': 540, 'new_value': 564}]
2025-05-23 08:09:36,923 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM08
2025-05-23 08:09:36,923 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_48AFA71F437742278E0CD956382F1110_2025-05, 变更字段: [{'field': 'amount', 'old_value': 56439.6, 'new_value': 59674.6}, {'field': 'count', 'old_value': 226, 'new_value': 237}, {'field': 'instoreAmount', 'old_value': 56628.6, 'new_value': 59863.6}, {'field': 'instoreCount', 'old_value': 225, 'new_value': 236}]
2025-05-23 08:09:37,376 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM18
2025-05-23 08:09:37,376 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6D1F9FC749FA44C6B70CA818C3E7FB77_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 37961.0, 'new_value': 39076.0}, {'field': 'dailyBillAmount', 'old_value': 37961.0, 'new_value': 39076.0}, {'field': 'amount', 'old_value': 40212.0, 'new_value': 41277.0}, {'field': 'count', 'old_value': 219, 'new_value': 224}, {'field': 'instoreAmount', 'old_value': 40226.0, 'new_value': 41291.0}, {'field': 'instoreCount', 'old_value': 219, 'new_value': 224}]
2025-05-23 08:09:37,829 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM28
2025-05-23 08:09:37,829 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6FEB527E4B354363BD1420A3FF0FB3E3_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 64614.35, 'new_value': 67715.29}, {'field': 'dailyBillAmount', 'old_value': 64614.35, 'new_value': 67715.29}, {'field': 'amount', 'old_value': 57360.53, 'new_value': 59980.28}, {'field': 'count', 'old_value': 1930, 'new_value': 2027}, {'field': 'instoreAmount', 'old_value': 52435.17, 'new_value': 54648.92}, {'field': 'instoreCount', 'old_value': 1701, 'new_value': 1778}, {'field': 'onlineAmount', 'old_value': 4961.8, 'new_value': 5367.8}, {'field': 'onlineCount', 'old_value': 229, 'new_value': 249}]
2025-05-23 08:09:38,314 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMEC
2025-05-23 08:09:38,314 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_7987833E6DE549FCBAC0AAF7A1D27E61_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 32101.79, 'new_value': 36877.78}, {'field': 'dailyBillAmount', 'old_value': 32101.79, 'new_value': 36877.78}, {'field': 'amount', 'old_value': 36091.03, 'new_value': 41280.3}, {'field': 'count', 'old_value': 233, 'new_value': 265}, {'field': 'instoreAmount', 'old_value': 35538.91, 'new_value': 40549.01}, {'field': 'instoreCount', 'old_value': 208, 'new_value': 229}, {'field': 'onlineAmount', 'old_value': 702.72, 'new_value': 881.89}, {'field': 'onlineCount', 'old_value': 25, 'new_value': 36}]
2025-05-23 08:09:38,782 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM38
2025-05-23 08:09:38,782 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_A48FB8F8F66644F59454F3E73DFCEB92_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 174307.61000000002, 'new_value': 186611.74}, {'field': 'dailyBillAmount', 'old_value': 174307.61000000002, 'new_value': 186611.74}, {'field': 'amount', 'old_value': 181198.6, 'new_value': 193802.6}, {'field': 'count', 'old_value': 1230, 'new_value': 1279}, {'field': 'instoreAmount', 'old_value': 175144.7, 'new_value': 187343.7}, {'field': 'instoreCount', 'old_value': 1100, 'new_value': 1142}, {'field': 'onlineAmount', 'old_value': 8632.9, 'new_value': 9043.9}, {'field': 'onlineCount', 'old_value': 130, 'new_value': 137}]
2025-05-23 08:09:38,782 - ERROR - 月度日期格式转换错误: 1746028800000, 不支持的月度日期类型: <class 'int'>
2025-05-23 08:09:38,782 - ERROR - 月度日期格式转换错误: 1746028800000, 不支持的月度日期类型: <class 'int'>
2025-05-23 08:09:38,782 - ERROR - 月度日期格式转换错误: 1746028800000, 不支持的月度日期类型: <class 'int'>
2025-05-23 08:09:38,782 - ERROR - 月度日期格式转换错误: 1746028800000, 不支持的月度日期类型: <class 'int'>
2025-05-23 08:09:38,782 - INFO - 正在批量插入月度数据，批次 1/1，共 4 条记录
2025-05-23 08:09:38,954 - INFO - 批量插入月度数据成功，批次 1，4 条记录
2025-05-23 08:09:41,970 - INFO - 批量插入月度数据完成: 总计 4 条，成功 4 条，失败 0 条
2025-05-23 08:09:41,970 - INFO - 批量插入月销售数据完成，共 4 条记录
2025-05-23 08:09:41,970 - INFO - 月销售数据同步完成！更新: 205 条，插入: 4 条，错误: 0 条，跳过: 983 条
2025-05-23 08:09:41,970 - INFO - 开始导出月度汇总数据到Excel，时间范围: 2024-6 至 2025-5
2025-05-23 08:09:42,548 - INFO - 月度汇总数据已导出到Excel文件: logs/数衍平台月度数据_20250523.xlsx
2025-05-23 08:09:42,548 - INFO - 综合数据同步流程完成！
2025-05-23 08:09:42,626 - INFO - 综合数据同步完成
2025-05-23 08:09:42,626 - INFO - ==================================================
2025-05-23 08:09:42,626 - INFO - 程序退出
2025-05-23 08:09:42,626 - INFO - ==================================================
