2025-07-21 01:30:33,990 - INFO - 使用默认增量同步（当天更新数据）
2025-07-21 01:30:33,990 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-21 01:30:33,990 - INFO - 查询参数: ('2025-07-21',)
2025-07-21 01:30:34,130 - INFO - MySQL查询成功，增量数据（日期: 2025-07-21），共获取 7 条记录
2025-07-21 01:30:34,130 - INFO - 获取到 1 个日期需要处理: ['2025-07-20']
2025-07-21 01:30:34,146 - INFO - 开始处理日期: 2025-07-20
2025-07-21 01:30:34,146 - INFO - Request Parameters - Page 1:
2025-07-21 01:30:34,146 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 01:30:34,146 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 01:30:42,255 - ERROR - 处理日期 2025-07-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 692FB11D-1C14-7A82-BC1C-B1D79E1F1D20 Response: {'code': 'ServiceUnavailable', 'requestid': '692FB11D-1C14-7A82-BC1C-B1D79E1F1D20', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 692FB11D-1C14-7A82-BC1C-B1D79E1F1D20)
2025-07-21 01:30:42,255 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-21 01:31:42,271 - INFO - 开始同步昨天与今天的销售数据: 2025-07-20 至 2025-07-21
2025-07-21 01:31:42,271 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-21 01:31:42,271 - INFO - 查询参数: ('2025-07-20', '2025-07-21')
2025-07-21 01:31:42,411 - INFO - MySQL查询成功，时间段: 2025-07-20 至 2025-07-21，共获取 150 条记录
2025-07-21 01:31:42,411 - INFO - 获取到 1 个日期需要处理: ['2025-07-20']
2025-07-21 01:31:42,411 - INFO - 开始处理日期: 2025-07-20
2025-07-21 01:31:42,411 - INFO - Request Parameters - Page 1:
2025-07-21 01:31:42,411 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 01:31:42,411 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 01:31:49,927 - INFO - Response - Page 1:
2025-07-21 01:31:49,927 - INFO - 第 1 页获取到 50 条记录
2025-07-21 01:31:50,442 - INFO - Request Parameters - Page 2:
2025-07-21 01:31:50,442 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 01:31:50,442 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 01:31:51,021 - INFO - Response - Page 2:
2025-07-21 01:31:51,021 - INFO - 第 2 页获取到 14 条记录
2025-07-21 01:31:51,536 - INFO - 查询完成，共获取到 64 条记录
2025-07-21 01:31:51,536 - INFO - 获取到 64 条表单数据
2025-07-21 01:31:51,536 - INFO - 当前日期 2025-07-20 有 142 条MySQL数据需要处理
2025-07-21 01:31:51,536 - INFO - 开始批量插入 78 条新记录
2025-07-21 01:31:51,771 - INFO - 批量插入响应状态码: 200
2025-07-21 01:31:51,771 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 20 Jul 2025 17:31:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0436F538-D986-7D84-9A17-2F7349BEEEC5', 'x-acs-trace-id': '5db39c592ef1cda83e786737f68327c2', 'etag': '2Z8jHifFtv6X87YnnDXPb1g2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-21 01:31:51,771 - INFO - 批量插入响应体: {'result': ['FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMOE', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMPE', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMQE', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMRE', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMSE', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMTE', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMUE', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMVE', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMWE', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMXE', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMYE', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMZE', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDM0F', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDM1F', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDM2F', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDM3F', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDM4F', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDM5F', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDM6F', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDM7F', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDM8F', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDM9F', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMAF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMBF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMCF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMDF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMEF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMFF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMGF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMHF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMIF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMJF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMKF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMLF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMMF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMNF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMOF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMPF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMQF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMRF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMSF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMTF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMUF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMVF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMWF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMXF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMYF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMZF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDM0G', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDM1G']}
2025-07-21 01:31:51,771 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-21 01:31:51,771 - INFO - 成功插入的数据ID: ['FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMOE', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMPE', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMQE', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMRE', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMSE', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMTE', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMUE', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMVE', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMWE', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMXE', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMYE', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMZE', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDM0F', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDM1F', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDM2F', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDM3F', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDM4F', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDM5F', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDM6F', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDM7F', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDM8F', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDM9F', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMAF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMBF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMCF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMDF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMEF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMFF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMGF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMHF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMIF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMJF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMKF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMLF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMMF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMNF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMOF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMPF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMQF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMRF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMSF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMTF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMUF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMVF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMWF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMXF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMYF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDMZF', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDM0G', 'FINST-OIF66BA1H4BXDHRIEAHH8DZDCZU93T2XEYBDM1G']
2025-07-21 01:31:57,005 - INFO - 批量插入响应状态码: 200
2025-07-21 01:31:57,005 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 20 Jul 2025 17:31:56 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1356', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '8124752A-C39B-7222-8129-FA0B6E7C9D76', 'x-acs-trace-id': '07eeb04b06463239062ab0d73619e065', 'etag': '1cDNeppnSKb+49YlwhXMNsw6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-21 01:31:57,005 - INFO - 批量插入响应体: {'result': ['FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMEA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMFA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMGA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMHA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMIA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMJA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMKA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMLA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMMA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMNA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMOA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMPA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMQA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMRA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMSA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMTA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMUA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMVA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMWA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMXA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMYA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMZA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDM0B', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDM1B', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDM2B', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDM3B', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDM4B', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDM5B']}
2025-07-21 01:31:57,005 - INFO - 批量插入表单数据成功，批次 2，共 28 条记录
2025-07-21 01:31:57,005 - INFO - 成功插入的数据ID: ['FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMEA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMFA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMGA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMHA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMIA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMJA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMKA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMLA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMMA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMNA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMOA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMPA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMQA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMRA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMSA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMTA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMUA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMVA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMWA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMXA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMYA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDMZA', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDM0B', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDM1B', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDM2B', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDM3B', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDM4B', 'FINST-7PF66N91J7BXQND5AYWOFBLEN6IS2841FYBDM5B']
2025-07-21 01:32:02,021 - INFO - 批量插入完成，共 78 条记录
2025-07-21 01:32:02,021 - INFO - 日期 2025-07-20 处理完成 - 更新: 0 条，插入: 78 条，错误: 0 条
2025-07-21 01:32:02,021 - INFO - 数据同步完成！更新: 0 条，插入: 78 条，错误: 0 条
2025-07-21 01:32:02,021 - INFO - 同步完成
2025-07-21 04:30:33,527 - INFO - 使用默认增量同步（当天更新数据）
2025-07-21 04:30:33,527 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-21 04:30:33,527 - INFO - 查询参数: ('2025-07-21',)
2025-07-21 04:30:33,684 - INFO - MySQL查询成功，增量数据（日期: 2025-07-21），共获取 7 条记录
2025-07-21 04:30:33,684 - INFO - 获取到 1 个日期需要处理: ['2025-07-20']
2025-07-21 04:30:33,684 - INFO - 开始处理日期: 2025-07-20
2025-07-21 04:30:33,684 - INFO - Request Parameters - Page 1:
2025-07-21 04:30:33,684 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 04:30:33,684 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 04:30:41,371 - INFO - Response - Page 1:
2025-07-21 04:30:41,371 - INFO - 第 1 页获取到 50 条记录
2025-07-21 04:30:41,887 - INFO - Request Parameters - Page 2:
2025-07-21 04:30:41,887 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 04:30:41,887 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 04:30:49,996 - ERROR - 处理日期 2025-07-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: DE789432-3440-7D3E-9852-A5177EACD301 Response: {'code': 'ServiceUnavailable', 'requestid': 'DE789432-3440-7D3E-9852-A5177EACD301', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: DE789432-3440-7D3E-9852-A5177EACD301)
2025-07-21 04:30:49,996 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-21 04:31:50,011 - INFO - 开始同步昨天与今天的销售数据: 2025-07-20 至 2025-07-21
2025-07-21 04:31:50,011 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-21 04:31:50,011 - INFO - 查询参数: ('2025-07-20', '2025-07-21')
2025-07-21 04:31:50,152 - INFO - MySQL查询成功，时间段: 2025-07-20 至 2025-07-21，共获取 150 条记录
2025-07-21 04:31:50,152 - INFO - 获取到 1 个日期需要处理: ['2025-07-20']
2025-07-21 04:31:50,152 - INFO - 开始处理日期: 2025-07-20
2025-07-21 04:31:50,152 - INFO - Request Parameters - Page 1:
2025-07-21 04:31:50,152 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 04:31:50,152 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 04:31:50,917 - INFO - Response - Page 1:
2025-07-21 04:31:50,917 - INFO - 第 1 页获取到 50 条记录
2025-07-21 04:31:51,433 - INFO - Request Parameters - Page 2:
2025-07-21 04:31:51,433 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 04:31:51,433 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 04:31:52,136 - INFO - Response - Page 2:
2025-07-21 04:31:52,136 - INFO - 第 2 页获取到 50 条记录
2025-07-21 04:31:52,652 - INFO - Request Parameters - Page 3:
2025-07-21 04:31:52,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 04:31:52,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 04:31:53,371 - INFO - Response - Page 3:
2025-07-21 04:31:53,371 - INFO - 第 3 页获取到 42 条记录
2025-07-21 04:31:53,886 - INFO - 查询完成，共获取到 142 条记录
2025-07-21 04:31:53,886 - INFO - 获取到 142 条表单数据
2025-07-21 04:31:53,886 - INFO - 当前日期 2025-07-20 有 142 条MySQL数据需要处理
2025-07-21 04:31:53,886 - INFO - 日期 2025-07-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-21 04:31:53,886 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-21 04:31:53,886 - INFO - 同步完成
2025-07-21 07:30:33,783 - INFO - 使用默认增量同步（当天更新数据）
2025-07-21 07:30:33,783 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-21 07:30:33,783 - INFO - 查询参数: ('2025-07-21',)
2025-07-21 07:30:33,924 - INFO - MySQL查询成功，增量数据（日期: 2025-07-21），共获取 8 条记录
2025-07-21 07:30:33,939 - INFO - 获取到 1 个日期需要处理: ['2025-07-20']
2025-07-21 07:30:33,939 - INFO - 开始处理日期: 2025-07-20
2025-07-21 07:30:33,939 - INFO - Request Parameters - Page 1:
2025-07-21 07:30:33,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 07:30:33,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 07:30:42,064 - ERROR - 处理日期 2025-07-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A60F81D6-E1A2-73DB-A815-AC1198FD587C Response: {'code': 'ServiceUnavailable', 'requestid': 'A60F81D6-E1A2-73DB-A815-AC1198FD587C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A60F81D6-E1A2-73DB-A815-AC1198FD587C)
2025-07-21 07:30:42,064 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-21 07:31:42,080 - INFO - 开始同步昨天与今天的销售数据: 2025-07-20 至 2025-07-21
2025-07-21 07:31:42,080 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-21 07:31:42,080 - INFO - 查询参数: ('2025-07-20', '2025-07-21')
2025-07-21 07:31:42,220 - INFO - MySQL查询成功，时间段: 2025-07-20 至 2025-07-21，共获取 151 条记录
2025-07-21 07:31:42,220 - INFO - 获取到 1 个日期需要处理: ['2025-07-20']
2025-07-21 07:31:42,220 - INFO - 开始处理日期: 2025-07-20
2025-07-21 07:31:42,220 - INFO - Request Parameters - Page 1:
2025-07-21 07:31:42,220 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 07:31:42,220 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 07:31:49,783 - INFO - Response - Page 1:
2025-07-21 07:31:49,783 - INFO - 第 1 页获取到 50 条记录
2025-07-21 07:31:50,283 - INFO - Request Parameters - Page 2:
2025-07-21 07:31:50,283 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 07:31:50,283 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 07:31:51,017 - INFO - Response - Page 2:
2025-07-21 07:31:51,017 - INFO - 第 2 页获取到 50 条记录
2025-07-21 07:31:51,533 - INFO - Request Parameters - Page 3:
2025-07-21 07:31:51,533 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 07:31:51,533 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 07:31:52,236 - INFO - Response - Page 3:
2025-07-21 07:31:52,236 - INFO - 第 3 页获取到 42 条记录
2025-07-21 07:31:52,736 - INFO - 查询完成，共获取到 142 条记录
2025-07-21 07:31:52,736 - INFO - 获取到 142 条表单数据
2025-07-21 07:31:52,736 - INFO - 当前日期 2025-07-20 有 143 条MySQL数据需要处理
2025-07-21 07:31:52,736 - INFO - 开始批量插入 1 条新记录
2025-07-21 07:31:52,923 - INFO - 批量插入响应状态码: 200
2025-07-21 07:31:52,923 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 20 Jul 2025 23:31:52 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E99BB761-7AC6-7D35-ABEF-C667558A1E98', 'x-acs-trace-id': '95fc271c0a5ed8d6459dc02aac9c0091', 'etag': '6RK2X5e5jeAt11Gu4VjZwSg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-21 07:31:52,923 - INFO - 批量插入响应体: {'result': ['FINST-8P666U9184AX9TUUB97IG43Z9RN231QW9BCDM2O']}
2025-07-21 07:31:52,923 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-21 07:31:52,923 - INFO - 成功插入的数据ID: ['FINST-8P666U9184AX9TUUB97IG43Z9RN231QW9BCDM2O']
2025-07-21 07:31:57,939 - INFO - 批量插入完成，共 1 条记录
2025-07-21 07:31:57,939 - INFO - 日期 2025-07-20 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-21 07:31:57,939 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 0 条
2025-07-21 07:31:57,939 - INFO - 同步完成
2025-07-21 10:30:33,839 - INFO - 使用默认增量同步（当天更新数据）
2025-07-21 10:30:33,839 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-21 10:30:33,839 - INFO - 查询参数: ('2025-07-21',)
2025-07-21 10:30:33,995 - INFO - MySQL查询成功，增量数据（日期: 2025-07-21），共获取 108 条记录
2025-07-21 10:30:33,995 - INFO - 获取到 2 个日期需要处理: ['2025-07-20', '2025-07-21']
2025-07-21 10:30:33,995 - INFO - 开始处理日期: 2025-07-20
2025-07-21 10:30:34,011 - INFO - Request Parameters - Page 1:
2025-07-21 10:30:34,011 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 10:30:34,011 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 10:30:42,136 - ERROR - 处理日期 2025-07-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3D1B1A19-B41D-7716-BA7E-AB3EB6CC62E6 Response: {'code': 'ServiceUnavailable', 'requestid': '3D1B1A19-B41D-7716-BA7E-AB3EB6CC62E6', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3D1B1A19-B41D-7716-BA7E-AB3EB6CC62E6)
2025-07-21 10:30:42,136 - INFO - 开始处理日期: 2025-07-21
2025-07-21 10:30:42,136 - INFO - Request Parameters - Page 1:
2025-07-21 10:30:42,136 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 10:30:42,136 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 10:30:42,604 - INFO - Response - Page 1:
2025-07-21 10:30:42,604 - INFO - 查询完成，共获取到 0 条记录
2025-07-21 10:30:42,604 - INFO - 获取到 0 条表单数据
2025-07-21 10:30:42,604 - INFO - 当前日期 2025-07-21 有 2 条MySQL数据需要处理
2025-07-21 10:30:42,604 - INFO - 开始批量插入 2 条新记录
2025-07-21 10:30:42,761 - INFO - 批量插入响应状态码: 200
2025-07-21 10:30:42,761 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 21 Jul 2025 02:30:42 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '106', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0E890043-0ED2-7C31-B2F4-7095E2E89FCB', 'x-acs-trace-id': '2bcc5998629187172b7b1d591a17898b', 'etag': '1mEzyXk/LmKrcp0uLfmCyYA6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-21 10:30:42,761 - INFO - 批量插入响应体: {'result': ['FINST-IQG66AD1B4CX5SLF7Q0U3A2I6CDK2PZVNHCDMC', 'FINST-IQG66AD1B4CX5SLF7Q0U3A2I6CDK2PZVNHCDMD']}
2025-07-21 10:30:42,761 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-07-21 10:30:42,761 - INFO - 成功插入的数据ID: ['FINST-IQG66AD1B4CX5SLF7Q0U3A2I6CDK2PZVNHCDMC', 'FINST-IQG66AD1B4CX5SLF7Q0U3A2I6CDK2PZVNHCDMD']
2025-07-21 10:30:47,776 - INFO - 批量插入完成，共 2 条记录
2025-07-21 10:30:47,776 - INFO - 日期 2025-07-21 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-07-21 10:30:47,776 - INFO - 数据同步完成！更新: 0 条，插入: 2 条，错误: 1 条
2025-07-21 10:31:47,791 - INFO - 开始同步昨天与今天的销售数据: 2025-07-20 至 2025-07-21
2025-07-21 10:31:47,791 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-21 10:31:47,791 - INFO - 查询参数: ('2025-07-20', '2025-07-21')
2025-07-21 10:31:47,948 - INFO - MySQL查询成功，时间段: 2025-07-20 至 2025-07-21，共获取 394 条记录
2025-07-21 10:31:47,948 - INFO - 获取到 2 个日期需要处理: ['2025-07-20', '2025-07-21']
2025-07-21 10:31:47,948 - INFO - 开始处理日期: 2025-07-20
2025-07-21 10:31:47,948 - INFO - Request Parameters - Page 1:
2025-07-21 10:31:47,948 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 10:31:47,948 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 10:31:48,838 - INFO - Response - Page 1:
2025-07-21 10:31:48,838 - INFO - 第 1 页获取到 50 条记录
2025-07-21 10:31:49,354 - INFO - Request Parameters - Page 2:
2025-07-21 10:31:49,354 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 10:31:49,354 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 10:31:50,088 - INFO - Response - Page 2:
2025-07-21 10:31:50,088 - INFO - 第 2 页获取到 50 条记录
2025-07-21 10:31:50,588 - INFO - Request Parameters - Page 3:
2025-07-21 10:31:50,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 10:31:50,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 10:31:51,323 - INFO - Response - Page 3:
2025-07-21 10:31:51,323 - INFO - 第 3 页获取到 43 条记录
2025-07-21 10:31:51,838 - INFO - 查询完成，共获取到 143 条记录
2025-07-21 10:31:51,838 - INFO - 获取到 143 条表单数据
2025-07-21 10:31:51,838 - INFO - 当前日期 2025-07-20 有 370 条MySQL数据需要处理
2025-07-21 10:31:51,838 - INFO - 开始批量插入 227 条新记录
2025-07-21 10:31:52,104 - INFO - 批量插入响应状态码: 200
2025-07-21 10:31:52,104 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 21 Jul 2025 02:31:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2394', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F876FC5C-6911-773A-9D8C-9DEA36FF25FD', 'x-acs-trace-id': '64e347a30d7d2ce3801f8fff10ce2207', 'etag': '2pXqVXT3s+4iud6TxNBTDGQ4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-21 10:31:52,104 - INFO - 批量插入响应体: {'result': ['FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMI', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMJ', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMK', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDML', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMM', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMN', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMO', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMP', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMQ', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMR', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMS', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMT', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMU', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMV', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMW', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMX', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMY', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMZ', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDM01', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDM11', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDM21', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDM31', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDM41', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDM51', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDM61', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDM71', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDM81', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDM91', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMA1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMB1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMC1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMD1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDME1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMF1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMG1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMH1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMI1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMJ1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMK1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDML1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMM1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMN1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMO1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMP1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMQ1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMR1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMS1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMT1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMU1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMV1']}
2025-07-21 10:31:52,104 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-21 10:31:52,104 - INFO - 成功插入的数据ID: ['FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMI', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMJ', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMK', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDML', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMM', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMN', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMO', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMP', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMQ', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMR', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMS', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMT', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMU', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMV', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMW', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMX', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMY', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMZ', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDM01', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDM11', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDM21', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDM31', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDM41', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDM51', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDM61', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDM71', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDM81', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDM91', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMA1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMB1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMC1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMD1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDME1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMF1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMG1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMH1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMI1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMJ1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMK1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDML1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMM1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMN1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMO1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMP1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMQ1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMR1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMS1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMT1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMU1', 'FINST-CJ966Q71V2CXCL9BEF1337G3VDUN3RHDPHCDMV1']
2025-07-21 10:31:57,385 - INFO - 批量插入响应状态码: 200
2025-07-21 10:31:57,385 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 21 Jul 2025 02:31:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '12C14932-FE45-76E9-9C61-3C97519F0C31', 'x-acs-trace-id': 'ef0476305785f02a02b78cc3d1fba863', 'etag': '2TaUiVnMtEmlK68/GXSabUw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-21 10:31:57,385 - INFO - 批量插入响应体: {'result': ['FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDMYW', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDMZW', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDM0X', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDM1X', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDM2X', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDM3X', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDM4X', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDM5X', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDM6X', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDM7X', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDM8X', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDM9X', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDMAX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDMBX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDMCX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDMDX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDMEX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDMFX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDMGX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDMHX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDMIX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDMJX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDMKX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDMLX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDMMX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDMNX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDMOX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDMPX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDMQX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDMRX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDMSX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDMTX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDMUX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDMVX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDMWX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDMXX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDMYX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDMZX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDM0Y', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDM1Y', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDM2Y', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDM3Y', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDM4Y', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDM5Y', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDM6Y', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDM7Y', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDM8Y', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDM9Y', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDMAY', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDMBY']}
2025-07-21 10:31:57,385 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-07-21 10:31:57,385 - INFO - 成功插入的数据ID: ['FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDMYW', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDMZW', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDM0X', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDM1X', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDM2X', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDM3X', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDM4X', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDM5X', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDM6X', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDM7X', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDM8X', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDM9X', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDMAX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDMBX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDMCX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDMDX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDMEX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDMFX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDMGX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDMHX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDMIX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDMJX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDMKX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDMLX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDMMX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDMNX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022KKHPHCDMOX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDMPX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDMQX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDMRX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDMSX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDMTX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDMUX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDMVX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDMWX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDMXX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDMYX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDMZX', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDM0Y', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDM1Y', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDM2Y', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDM3Y', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDM4Y', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDM5Y', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDM6Y', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDM7Y', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDM8Y', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDM9Y', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDMAY', 'FINST-NYC66LB1IE9XWCESF8S826MW08022LKHPHCDMBY']
2025-07-21 10:32:02,635 - INFO - 批量插入响应状态码: 200
2025-07-21 10:32:02,635 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 21 Jul 2025 02:32:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '40C0E211-20CA-7FB0-B3A5-F6CEC515F95F', 'x-acs-trace-id': '41cdb330d19386738f4a4169618607fa', 'etag': '25E8/3W/NAvc+z4VnRF7l5A2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-21 10:32:02,635 - INFO - 批量插入响应体: {'result': ['FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDM9Y', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMAY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMBY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMCY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMDY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMEY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMFY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMGY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMHY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMIY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMJY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMKY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMLY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMMY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMNY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMOY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMPY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMQY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMRY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMSY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMTY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMUY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMVY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMWY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMXY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMYY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMZY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDM0Z', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDM1Z', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDM2Z', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDM3Z', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDM4Z', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDM5Z', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDM6Z', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDM7Z', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDM8Z', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDM9Z', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMAZ', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMBZ', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMCZ', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMDZ', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMEZ', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMFZ', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMGZ', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMHZ', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMIZ', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMJZ', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMKZ', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMLZ', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMMZ']}
2025-07-21 10:32:02,635 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-07-21 10:32:02,635 - INFO - 成功插入的数据ID: ['FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDM9Y', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMAY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMBY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMCY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMDY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMEY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMFY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMGY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMHY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMIY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMJY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMKY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMLY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMMY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMNY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMOY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMPY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMQY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMRY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMSY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMTY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMUY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMVY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMWY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMXY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMYY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMZY', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDM0Z', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDM1Z', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDM2Z', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDM3Z', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDM4Z', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDM5Z', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDM6Z', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDM7Z', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDM8Z', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDM9Z', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMAZ', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMBZ', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMCZ', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMDZ', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMEZ', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMFZ', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMGZ', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMHZ', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMIZ', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMJZ', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMKZ', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMLZ', 'FINST-7PF66CC1PHAX1W88CI4AD8GB5T7A2JMLPHCDMMZ']
2025-07-21 10:32:07,869 - INFO - 批量插入响应状态码: 200
2025-07-21 10:32:07,869 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 21 Jul 2025 02:32:07 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D0D9268D-3DE9-7D2C-B289-662581806BC0', 'x-acs-trace-id': '827aaed64a9de94a4af5683f3271d427', 'etag': '2hnK7CzvO6Y35EMdbGOwdwQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-21 10:32:07,869 - INFO - 批量插入响应体: {'result': ['FINST-RI7660910IAXR8VNB4IE3BF83KM83UNPPHCDMVP', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83UNPPHCDMWP', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83UNPPHCDMXP', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83UNPPHCDMYP', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83UNPPHCDMZP', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83UNPPHCDM0Q', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83UNPPHCDM1Q', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83UNPPHCDM2Q', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83UNPPHCDM3Q', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83UNPPHCDM4Q', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83UNPPHCDM5Q', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83UNPPHCDM6Q', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83UNPPHCDM7Q', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83UNPPHCDM8Q', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83UNPPHCDM9Q', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMAQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMBQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMCQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMDQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMEQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMFQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMGQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMHQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMIQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMJQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMKQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMLQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMMQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMNQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMOQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMPQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMQQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMRQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMSQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMTQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMUQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMVQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMWQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMXQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMYQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMZQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDM0R', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDM1R', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDM2R', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDM3R', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDM4R', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDM5R', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDM6R', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDM7R', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDM8R']}
2025-07-21 10:32:07,869 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-07-21 10:32:07,869 - INFO - 成功插入的数据ID: ['FINST-RI7660910IAXR8VNB4IE3BF83KM83UNPPHCDMVP', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83UNPPHCDMWP', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83UNPPHCDMXP', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83UNPPHCDMYP', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83UNPPHCDMZP', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83UNPPHCDM0Q', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83UNPPHCDM1Q', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83UNPPHCDM2Q', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83UNPPHCDM3Q', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83UNPPHCDM4Q', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83UNPPHCDM5Q', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83UNPPHCDM6Q', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83UNPPHCDM7Q', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83UNPPHCDM8Q', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83UNPPHCDM9Q', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMAQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMBQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMCQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMDQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMEQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMFQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMGQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMHQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMIQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMJQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMKQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMLQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMMQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMNQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMOQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMPQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMQQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMRQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMSQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMTQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMUQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMVQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMWQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMXQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMYQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDMZQ', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDM0R', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDM1R', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDM2R', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDM3R', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDM4R', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDM5R', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDM6R', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDM7R', 'FINST-RI7660910IAXR8VNB4IE3BF83KM83VNPPHCDM8R']
2025-07-21 10:32:13,088 - INFO - 批量插入响应状态码: 200
2025-07-21 10:32:13,088 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 21 Jul 2025 02:32:12 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1308', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'CA873D35-4F83-737F-AFD5-8692BF64C434', 'x-acs-trace-id': '5d1dd5de75628b711a5561f7d2520c11', 'etag': '19xjrFj4ieFk0JX7geI/Qyg8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-21 10:32:13,088 - INFO - 批量插入响应体: {'result': ['FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDMO2', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDMP2', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDMQ2', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDMR2', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDMS2', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDMT2', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDMU2', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDMV2', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDMW2', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDMX2', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDMY2', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDMZ2', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDM03', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDM13', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDM23', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDM33', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDM43', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDM53', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDM63', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDM73', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDM83', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDM93', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDMA3', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDMB3', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDMC3', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDMD3', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDME3']}
2025-07-21 10:32:13,088 - INFO - 批量插入表单数据成功，批次 5，共 27 条记录
2025-07-21 10:32:13,088 - INFO - 成功插入的数据ID: ['FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDMO2', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDMP2', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDMQ2', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDMR2', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDMS2', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDMT2', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDMU2', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDMV2', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDMW2', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDMX2', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDMY2', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDMZ2', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDM03', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDM13', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDM23', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDM33', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDM43', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDM53', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDM63', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDM73', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDM83', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDM93', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDMA3', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDMB3', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDMC3', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDMD3', 'FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDME3']
2025-07-21 10:32:18,104 - INFO - 批量插入完成，共 227 条记录
2025-07-21 10:32:18,104 - INFO - 日期 2025-07-20 处理完成 - 更新: 0 条，插入: 227 条，错误: 0 条
2025-07-21 10:32:18,104 - INFO - 开始处理日期: 2025-07-21
2025-07-21 10:32:18,104 - INFO - Request Parameters - Page 1:
2025-07-21 10:32:18,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 10:32:18,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 10:32:18,604 - INFO - Response - Page 1:
2025-07-21 10:32:18,604 - INFO - 第 1 页获取到 2 条记录
2025-07-21 10:32:19,119 - INFO - 查询完成，共获取到 2 条记录
2025-07-21 10:32:19,119 - INFO - 获取到 2 条表单数据
2025-07-21 10:32:19,119 - INFO - 当前日期 2025-07-21 有 2 条MySQL数据需要处理
2025-07-21 10:32:19,119 - INFO - 日期 2025-07-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-21 10:32:19,119 - INFO - 数据同步完成！更新: 0 条，插入: 227 条，错误: 0 条
2025-07-21 10:32:19,119 - INFO - 同步完成
2025-07-21 13:30:33,658 - INFO - 使用默认增量同步（当天更新数据）
2025-07-21 13:30:33,658 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-21 13:30:33,658 - INFO - 查询参数: ('2025-07-21',)
2025-07-21 13:30:33,814 - INFO - MySQL查询成功，增量数据（日期: 2025-07-21），共获取 120 条记录
2025-07-21 13:30:33,814 - INFO - 获取到 3 个日期需要处理: ['2025-07-19', '2025-07-20', '2025-07-21']
2025-07-21 13:30:33,814 - INFO - 开始处理日期: 2025-07-19
2025-07-21 13:30:33,830 - INFO - Request Parameters - Page 1:
2025-07-21 13:30:33,830 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 13:30:33,830 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 13:30:41,939 - ERROR - 处理日期 2025-07-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5AB8AAE0-3CB5-731F-A841-415FFFB2256D Response: {'code': 'ServiceUnavailable', 'requestid': '5AB8AAE0-3CB5-731F-A841-415FFFB2256D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 5AB8AAE0-3CB5-731F-A841-415FFFB2256D)
2025-07-21 13:30:41,939 - INFO - 开始处理日期: 2025-07-20
2025-07-21 13:30:41,939 - INFO - Request Parameters - Page 1:
2025-07-21 13:30:41,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 13:30:41,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 13:30:49,986 - INFO - Response - Page 1:
2025-07-21 13:30:49,986 - INFO - 第 1 页获取到 50 条记录
2025-07-21 13:30:50,501 - INFO - Request Parameters - Page 2:
2025-07-21 13:30:50,501 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 13:30:50,501 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 13:30:51,220 - INFO - Response - Page 2:
2025-07-21 13:30:51,220 - INFO - 第 2 页获取到 50 条记录
2025-07-21 13:30:51,736 - INFO - Request Parameters - Page 3:
2025-07-21 13:30:51,736 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 13:30:51,736 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 13:30:52,454 - INFO - Response - Page 3:
2025-07-21 13:30:52,454 - INFO - 第 3 页获取到 50 条记录
2025-07-21 13:30:52,954 - INFO - Request Parameters - Page 4:
2025-07-21 13:30:52,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 13:30:52,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 13:30:53,658 - INFO - Response - Page 4:
2025-07-21 13:30:53,658 - INFO - 第 4 页获取到 50 条记录
2025-07-21 13:30:54,173 - INFO - Request Parameters - Page 5:
2025-07-21 13:30:54,173 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 13:30:54,173 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 13:30:54,907 - INFO - Response - Page 5:
2025-07-21 13:30:54,907 - INFO - 第 5 页获取到 50 条记录
2025-07-21 13:30:55,423 - INFO - Request Parameters - Page 6:
2025-07-21 13:30:55,423 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 13:30:55,423 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 13:30:56,220 - INFO - Response - Page 6:
2025-07-21 13:30:56,220 - INFO - 第 6 页获取到 50 条记录
2025-07-21 13:30:56,720 - INFO - Request Parameters - Page 7:
2025-07-21 13:30:56,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 13:30:56,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 13:30:57,454 - INFO - Response - Page 7:
2025-07-21 13:30:57,454 - INFO - 第 7 页获取到 50 条记录
2025-07-21 13:30:57,970 - INFO - Request Parameters - Page 8:
2025-07-21 13:30:57,970 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 13:30:57,970 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 13:30:58,611 - INFO - Response - Page 8:
2025-07-21 13:30:58,611 - INFO - 第 8 页获取到 20 条记录
2025-07-21 13:30:59,126 - INFO - 查询完成，共获取到 370 条记录
2025-07-21 13:30:59,126 - INFO - 获取到 370 条表单数据
2025-07-21 13:30:59,126 - INFO - 当前日期 2025-07-20 有 107 条MySQL数据需要处理
2025-07-21 13:30:59,126 - INFO - 开始更新记录 - 表单实例ID: FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMCC
2025-07-21 13:30:59,673 - INFO - 更新表单数据成功: FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMCC
2025-07-21 13:30:59,673 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7667.0, 'new_value': 67667.0}, {'field': 'total_amount', 'old_value': 53786.0, 'new_value': 113786.0}, {'field': 'order_count', 'old_value': 47, 'new_value': 56}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/603179ad92354bde93ead790370fd6aa.jpg?Expires=2067581145&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=69J0nY4w6StS%2BOtGKhwUBiPI4eA%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/fa22811ce9da4de89716c9e2b316166d.jpg?Expires=2067580947&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=h%2BdjOOotXPjxOMtz9m5SFl4enRY%3D'}]
2025-07-21 13:30:59,673 - INFO - 开始更新记录 - 表单实例ID: FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMFC
2025-07-21 13:31:00,251 - INFO - 更新表单数据成功: FINST-SL966GD13E9XFFNT7HH0G8LAY3KA3Q4UYRBDMFC
2025-07-21 13:31:00,251 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 50000.0}, {'field': 'total_amount', 'old_value': 7474.0, 'new_value': 57474.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 11}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/bb07de0e5d6743408afcb233c6712073.jpg?Expires=2067581145&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=%2BPn9eQzvrZIy5iZxKPjBJHrwojY%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/089ec34dcaaa4823b0b22db458a44fde.jpg?Expires=2067580947&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=kJbcP7FfE7aAaXdWgkGOfUWshEQ%3D'}]
2025-07-21 13:31:00,251 - INFO - 开始批量插入 7 条新记录
2025-07-21 13:31:00,392 - INFO - 批量插入响应状态码: 200
2025-07-21 13:31:00,392 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 21 Jul 2025 05:31:00 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '348', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'BE2FFFFF-CEBF-7681-BAB7-1B9446528527', 'x-acs-trace-id': '430cd88130a40a100c2e703748d1d77b', 'etag': '3bjBxyFC8d3Ed3DuJ9CHWaw8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-21 13:31:00,392 - INFO - 批量插入响应体: {'result': ['FINST-L4E66Y6154AX8C9XCC56X9Z3MM7E2OZQ3OCDMRU', 'FINST-L4E66Y6154AX8C9XCC56X9Z3MM7E2PZQ3OCDMSU', 'FINST-L4E66Y6154AX8C9XCC56X9Z3MM7E2PZQ3OCDMTU', 'FINST-L4E66Y6154AX8C9XCC56X9Z3MM7E2PZQ3OCDMUU', 'FINST-L4E66Y6154AX8C9XCC56X9Z3MM7E2PZQ3OCDMVU', 'FINST-L4E66Y6154AX8C9XCC56X9Z3MM7E2PZQ3OCDMWU', 'FINST-L4E66Y6154AX8C9XCC56X9Z3MM7E2PZQ3OCDMXU']}
2025-07-21 13:31:00,392 - INFO - 批量插入表单数据成功，批次 1，共 7 条记录
2025-07-21 13:31:00,392 - INFO - 成功插入的数据ID: ['FINST-L4E66Y6154AX8C9XCC56X9Z3MM7E2OZQ3OCDMRU', 'FINST-L4E66Y6154AX8C9XCC56X9Z3MM7E2PZQ3OCDMSU', 'FINST-L4E66Y6154AX8C9XCC56X9Z3MM7E2PZQ3OCDMTU', 'FINST-L4E66Y6154AX8C9XCC56X9Z3MM7E2PZQ3OCDMUU', 'FINST-L4E66Y6154AX8C9XCC56X9Z3MM7E2PZQ3OCDMVU', 'FINST-L4E66Y6154AX8C9XCC56X9Z3MM7E2PZQ3OCDMWU', 'FINST-L4E66Y6154AX8C9XCC56X9Z3MM7E2PZQ3OCDMXU']
2025-07-21 13:31:05,407 - INFO - 批量插入完成，共 7 条记录
2025-07-21 13:31:05,407 - INFO - 日期 2025-07-20 处理完成 - 更新: 2 条，插入: 7 条，错误: 0 条
2025-07-21 13:31:05,407 - INFO - 开始处理日期: 2025-07-21
2025-07-21 13:31:05,407 - INFO - Request Parameters - Page 1:
2025-07-21 13:31:05,407 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 13:31:05,407 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 13:31:05,923 - INFO - Response - Page 1:
2025-07-21 13:31:05,923 - INFO - 第 1 页获取到 2 条记录
2025-07-21 13:31:06,423 - INFO - 查询完成，共获取到 2 条记录
2025-07-21 13:31:06,423 - INFO - 获取到 2 条表单数据
2025-07-21 13:31:06,423 - INFO - 当前日期 2025-07-21 有 2 条MySQL数据需要处理
2025-07-21 13:31:06,423 - INFO - 日期 2025-07-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-21 13:31:06,423 - INFO - 数据同步完成！更新: 2 条，插入: 7 条，错误: 1 条
2025-07-21 13:32:06,438 - INFO - 开始同步昨天与今天的销售数据: 2025-07-20 至 2025-07-21
2025-07-21 13:32:06,438 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-21 13:32:06,438 - INFO - 查询参数: ('2025-07-20', '2025-07-21')
2025-07-21 13:32:06,595 - INFO - MySQL查询成功，时间段: 2025-07-20 至 2025-07-21，共获取 420 条记录
2025-07-21 13:32:06,595 - INFO - 获取到 2 个日期需要处理: ['2025-07-20', '2025-07-21']
2025-07-21 13:32:06,595 - INFO - 开始处理日期: 2025-07-20
2025-07-21 13:32:06,595 - INFO - Request Parameters - Page 1:
2025-07-21 13:32:06,595 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 13:32:06,595 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 13:32:07,407 - INFO - Response - Page 1:
2025-07-21 13:32:07,407 - INFO - 第 1 页获取到 50 条记录
2025-07-21 13:32:07,923 - INFO - Request Parameters - Page 2:
2025-07-21 13:32:07,923 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 13:32:07,923 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 13:32:08,704 - INFO - Response - Page 2:
2025-07-21 13:32:08,719 - INFO - 第 2 页获取到 50 条记录
2025-07-21 13:32:09,219 - INFO - Request Parameters - Page 3:
2025-07-21 13:32:09,219 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 13:32:09,219 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 13:32:09,923 - INFO - Response - Page 3:
2025-07-21 13:32:09,923 - INFO - 第 3 页获取到 50 条记录
2025-07-21 13:32:10,423 - INFO - Request Parameters - Page 4:
2025-07-21 13:32:10,423 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 13:32:10,423 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 13:32:11,173 - INFO - Response - Page 4:
2025-07-21 13:32:11,173 - INFO - 第 4 页获取到 50 条记录
2025-07-21 13:32:11,673 - INFO - Request Parameters - Page 5:
2025-07-21 13:32:11,673 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 13:32:11,673 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 13:32:12,407 - INFO - Response - Page 5:
2025-07-21 13:32:12,407 - INFO - 第 5 页获取到 50 条记录
2025-07-21 13:32:12,907 - INFO - Request Parameters - Page 6:
2025-07-21 13:32:12,907 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 13:32:12,907 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 13:32:13,704 - INFO - Response - Page 6:
2025-07-21 13:32:13,704 - INFO - 第 6 页获取到 50 条记录
2025-07-21 13:32:14,219 - INFO - Request Parameters - Page 7:
2025-07-21 13:32:14,219 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 13:32:14,219 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 13:32:14,891 - INFO - Response - Page 7:
2025-07-21 13:32:14,891 - INFO - 第 7 页获取到 50 条记录
2025-07-21 13:32:15,407 - INFO - Request Parameters - Page 8:
2025-07-21 13:32:15,407 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 13:32:15,407 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 13:32:16,048 - INFO - Response - Page 8:
2025-07-21 13:32:16,048 - INFO - 第 8 页获取到 27 条记录
2025-07-21 13:32:16,563 - INFO - 查询完成，共获取到 377 条记录
2025-07-21 13:32:16,563 - INFO - 获取到 377 条表单数据
2025-07-21 13:32:16,563 - INFO - 当前日期 2025-07-20 有 392 条MySQL数据需要处理
2025-07-21 13:32:16,579 - INFO - 开始批量插入 15 条新记录
2025-07-21 13:32:16,766 - INFO - 批量插入响应状态码: 200
2025-07-21 13:32:16,766 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 21 Jul 2025 05:32:16 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '732', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'FF9C0577-D233-7A0D-9F02-40990A0269DE', 'x-acs-trace-id': '01e2a9a355da50223703a992546bf102', 'etag': '7x+MD0ugnrsF60eQnrgAJsg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-21 13:32:16,766 - INFO - 批量插入响应体: {'result': ['FINST-YQ966PD10E9XHANOAKILUB890O883TWD5OCDMP1', 'FINST-YQ966PD10E9XHANOAKILUB890O883TWD5OCDMQ1', 'FINST-YQ966PD10E9XHANOAKILUB890O883TWD5OCDMR1', 'FINST-YQ966PD10E9XHANOAKILUB890O883TWD5OCDMS1', 'FINST-YQ966PD10E9XHANOAKILUB890O883TWD5OCDMT1', 'FINST-YQ966PD10E9XHANOAKILUB890O883TWD5OCDMU1', 'FINST-YQ966PD10E9XHANOAKILUB890O883TWD5OCDMV1', 'FINST-YQ966PD10E9XHANOAKILUB890O883TWD5OCDMW1', 'FINST-YQ966PD10E9XHANOAKILUB890O883TWD5OCDMX1', 'FINST-YQ966PD10E9XHANOAKILUB890O883TWD5OCDMY1', 'FINST-YQ966PD10E9XHANOAKILUB890O883UWD5OCDMZ1', 'FINST-YQ966PD10E9XHANOAKILUB890O883UWD5OCDM02', 'FINST-YQ966PD10E9XHANOAKILUB890O883UWD5OCDM12', 'FINST-YQ966PD10E9XHANOAKILUB890O883UWD5OCDM22', 'FINST-YQ966PD10E9XHANOAKILUB890O883UWD5OCDM32']}
2025-07-21 13:32:16,766 - INFO - 批量插入表单数据成功，批次 1，共 15 条记录
2025-07-21 13:32:16,766 - INFO - 成功插入的数据ID: ['FINST-YQ966PD10E9XHANOAKILUB890O883TWD5OCDMP1', 'FINST-YQ966PD10E9XHANOAKILUB890O883TWD5OCDMQ1', 'FINST-YQ966PD10E9XHANOAKILUB890O883TWD5OCDMR1', 'FINST-YQ966PD10E9XHANOAKILUB890O883TWD5OCDMS1', 'FINST-YQ966PD10E9XHANOAKILUB890O883TWD5OCDMT1', 'FINST-YQ966PD10E9XHANOAKILUB890O883TWD5OCDMU1', 'FINST-YQ966PD10E9XHANOAKILUB890O883TWD5OCDMV1', 'FINST-YQ966PD10E9XHANOAKILUB890O883TWD5OCDMW1', 'FINST-YQ966PD10E9XHANOAKILUB890O883TWD5OCDMX1', 'FINST-YQ966PD10E9XHANOAKILUB890O883TWD5OCDMY1', 'FINST-YQ966PD10E9XHANOAKILUB890O883UWD5OCDMZ1', 'FINST-YQ966PD10E9XHANOAKILUB890O883UWD5OCDM02', 'FINST-YQ966PD10E9XHANOAKILUB890O883UWD5OCDM12', 'FINST-YQ966PD10E9XHANOAKILUB890O883UWD5OCDM22', 'FINST-YQ966PD10E9XHANOAKILUB890O883UWD5OCDM32']
2025-07-21 13:32:21,782 - INFO - 批量插入完成，共 15 条记录
2025-07-21 13:32:21,782 - INFO - 日期 2025-07-20 处理完成 - 更新: 0 条，插入: 15 条，错误: 0 条
2025-07-21 13:32:21,782 - INFO - 开始处理日期: 2025-07-21
2025-07-21 13:32:21,782 - INFO - Request Parameters - Page 1:
2025-07-21 13:32:21,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 13:32:21,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 13:32:22,282 - INFO - Response - Page 1:
2025-07-21 13:32:22,282 - INFO - 第 1 页获取到 2 条记录
2025-07-21 13:32:22,782 - INFO - 查询完成，共获取到 2 条记录
2025-07-21 13:32:22,782 - INFO - 获取到 2 条表单数据
2025-07-21 13:32:22,782 - INFO - 当前日期 2025-07-21 有 2 条MySQL数据需要处理
2025-07-21 13:32:22,782 - INFO - 日期 2025-07-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-21 13:32:22,782 - INFO - 数据同步完成！更新: 0 条，插入: 15 条，错误: 0 条
2025-07-21 13:32:22,782 - INFO - 同步完成
2025-07-21 16:30:33,684 - INFO - 使用默认增量同步（当天更新数据）
2025-07-21 16:30:33,684 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-21 16:30:33,684 - INFO - 查询参数: ('2025-07-21',)
2025-07-21 16:30:33,840 - INFO - MySQL查询成功，增量数据（日期: 2025-07-21），共获取 122 条记录
2025-07-21 16:30:33,840 - INFO - 获取到 3 个日期需要处理: ['2025-07-19', '2025-07-20', '2025-07-21']
2025-07-21 16:30:33,840 - INFO - 开始处理日期: 2025-07-19
2025-07-21 16:30:33,840 - INFO - Request Parameters - Page 1:
2025-07-21 16:30:33,840 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 16:30:33,840 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 16:30:41,965 - ERROR - 处理日期 2025-07-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 03B4334F-9A58-7283-8547-9BC3E1B82E8F Response: {'code': 'ServiceUnavailable', 'requestid': '03B4334F-9A58-7283-8547-9BC3E1B82E8F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 03B4334F-9A58-7283-8547-9BC3E1B82E8F)
2025-07-21 16:30:41,965 - INFO - 开始处理日期: 2025-07-20
2025-07-21 16:30:41,965 - INFO - Request Parameters - Page 1:
2025-07-21 16:30:41,965 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 16:30:41,965 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 16:30:50,074 - ERROR - 处理日期 2025-07-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 981F35C9-44BA-7889-9E06-FB21F56DBE17 Response: {'code': 'ServiceUnavailable', 'requestid': '981F35C9-44BA-7889-9E06-FB21F56DBE17', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 981F35C9-44BA-7889-9E06-FB21F56DBE17)
2025-07-21 16:30:50,074 - INFO - 开始处理日期: 2025-07-21
2025-07-21 16:30:50,074 - INFO - Request Parameters - Page 1:
2025-07-21 16:30:50,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 16:30:50,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 16:30:53,621 - INFO - Response - Page 1:
2025-07-21 16:30:53,621 - INFO - 第 1 页获取到 2 条记录
2025-07-21 16:30:54,137 - INFO - 查询完成，共获取到 2 条记录
2025-07-21 16:30:54,137 - INFO - 获取到 2 条表单数据
2025-07-21 16:30:54,137 - INFO - 当前日期 2025-07-21 有 2 条MySQL数据需要处理
2025-07-21 16:30:54,137 - INFO - 日期 2025-07-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-21 16:30:54,137 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-07-21 16:31:54,152 - INFO - 开始同步昨天与今天的销售数据: 2025-07-20 至 2025-07-21
2025-07-21 16:31:54,152 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-21 16:31:54,152 - INFO - 查询参数: ('2025-07-20', '2025-07-21')
2025-07-21 16:31:54,308 - INFO - MySQL查询成功，时间段: 2025-07-20 至 2025-07-21，共获取 450 条记录
2025-07-21 16:31:54,308 - INFO - 获取到 2 个日期需要处理: ['2025-07-20', '2025-07-21']
2025-07-21 16:31:54,308 - INFO - 开始处理日期: 2025-07-20
2025-07-21 16:31:54,308 - INFO - Request Parameters - Page 1:
2025-07-21 16:31:54,308 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 16:31:54,308 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 16:31:55,136 - INFO - Response - Page 1:
2025-07-21 16:31:55,136 - INFO - 第 1 页获取到 50 条记录
2025-07-21 16:31:55,636 - INFO - Request Parameters - Page 2:
2025-07-21 16:31:55,636 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 16:31:55,636 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 16:31:56,511 - INFO - Response - Page 2:
2025-07-21 16:31:56,511 - INFO - 第 2 页获取到 50 条记录
2025-07-21 16:31:57,027 - INFO - Request Parameters - Page 3:
2025-07-21 16:31:57,027 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 16:31:57,027 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 16:31:57,792 - INFO - Response - Page 3:
2025-07-21 16:31:57,792 - INFO - 第 3 页获取到 50 条记录
2025-07-21 16:31:58,292 - INFO - Request Parameters - Page 4:
2025-07-21 16:31:58,292 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 16:31:58,292 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 16:31:59,058 - INFO - Response - Page 4:
2025-07-21 16:31:59,058 - INFO - 第 4 页获取到 50 条记录
2025-07-21 16:31:59,574 - INFO - Request Parameters - Page 5:
2025-07-21 16:31:59,574 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 16:31:59,574 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 16:32:00,324 - INFO - Response - Page 5:
2025-07-21 16:32:00,324 - INFO - 第 5 页获取到 50 条记录
2025-07-21 16:32:00,839 - INFO - Request Parameters - Page 6:
2025-07-21 16:32:00,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 16:32:00,839 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 16:32:01,589 - INFO - Response - Page 6:
2025-07-21 16:32:01,589 - INFO - 第 6 页获取到 50 条记录
2025-07-21 16:32:02,105 - INFO - Request Parameters - Page 7:
2025-07-21 16:32:02,105 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 16:32:02,105 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 16:32:02,871 - INFO - Response - Page 7:
2025-07-21 16:32:02,871 - INFO - 第 7 页获取到 50 条记录
2025-07-21 16:32:03,371 - INFO - Request Parameters - Page 8:
2025-07-21 16:32:03,371 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 16:32:03,371 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 16:32:04,042 - INFO - Response - Page 8:
2025-07-21 16:32:04,042 - INFO - 第 8 页获取到 42 条记录
2025-07-21 16:32:04,558 - INFO - 查询完成，共获取到 392 条记录
2025-07-21 16:32:04,558 - INFO - 获取到 392 条表单数据
2025-07-21 16:32:04,558 - INFO - 当前日期 2025-07-20 有 422 条MySQL数据需要处理
2025-07-21 16:32:04,574 - INFO - 开始批量插入 30 条新记录
2025-07-21 16:32:04,824 - INFO - 批量插入响应状态码: 200
2025-07-21 16:32:04,824 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 21 Jul 2025 08:32:04 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1449', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'BAEF87D0-10D4-7D18-BE52-0872A6C054CE', 'x-acs-trace-id': '28a5ff1472047b318a8e8a04ab884335', 'etag': '1ZYzwlXB3W48LR5IjsZ6clQ9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-21 16:32:04,824 - INFO - 批量插入响应体: {'result': ['FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMX', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMY', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMZ', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDM01', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDM11', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDM21', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDM31', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDM41', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDM51', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDM61', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDM71', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDM81', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDM91', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMA1', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMB1', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMC1', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMD1', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDME1', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMF1', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMG1', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMH1', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMI1', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMJ1', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMK1', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDML1', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMM1', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMN1', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMO1', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMP1', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMQ1']}
2025-07-21 16:32:04,824 - INFO - 批量插入表单数据成功，批次 1，共 30 条记录
2025-07-21 16:32:04,824 - INFO - 成功插入的数据ID: ['FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMX', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMY', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMZ', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDM01', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDM11', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDM21', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDM31', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDM41', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDM51', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDM61', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDM71', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDM81', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDM91', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMA1', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMB1', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMC1', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMD1', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDME1', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMF1', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMG1', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMH1', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMI1', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMJ1', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMK1', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDML1', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMM1', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMN1', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMO1', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMP1', 'FINST-2TG66D91ZD9X68H46DNC160GZWSH263MKUCDMQ1']
2025-07-21 16:32:09,839 - INFO - 批量插入完成，共 30 条记录
2025-07-21 16:32:09,839 - INFO - 日期 2025-07-20 处理完成 - 更新: 0 条，插入: 30 条，错误: 0 条
2025-07-21 16:32:09,839 - INFO - 开始处理日期: 2025-07-21
2025-07-21 16:32:09,839 - INFO - Request Parameters - Page 1:
2025-07-21 16:32:09,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 16:32:09,839 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 16:32:10,386 - INFO - Response - Page 1:
2025-07-21 16:32:10,386 - INFO - 第 1 页获取到 2 条记录
2025-07-21 16:32:10,902 - INFO - 查询完成，共获取到 2 条记录
2025-07-21 16:32:10,902 - INFO - 获取到 2 条表单数据
2025-07-21 16:32:10,902 - INFO - 当前日期 2025-07-21 有 2 条MySQL数据需要处理
2025-07-21 16:32:10,902 - INFO - 日期 2025-07-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-21 16:32:10,902 - INFO - 数据同步完成！更新: 0 条，插入: 30 条，错误: 0 条
2025-07-21 16:32:10,902 - INFO - 同步完成
2025-07-21 19:30:34,392 - INFO - 使用默认增量同步（当天更新数据）
2025-07-21 19:30:34,392 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-21 19:30:34,392 - INFO - 查询参数: ('2025-07-21',)
2025-07-21 19:30:34,548 - INFO - MySQL查询成功，增量数据（日期: 2025-07-21），共获取 137 条记录
2025-07-21 19:30:34,548 - INFO - 获取到 4 个日期需要处理: ['2025-07-18', '2025-07-19', '2025-07-20', '2025-07-21']
2025-07-21 19:30:34,548 - INFO - 开始处理日期: 2025-07-18
2025-07-21 19:30:34,548 - INFO - Request Parameters - Page 1:
2025-07-21 19:30:34,548 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 19:30:34,548 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 19:30:42,676 - ERROR - 处理日期 2025-07-18 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 934C3834-A63A-7BC8-83CB-40B61B51E29C Response: {'code': 'ServiceUnavailable', 'requestid': '934C3834-A63A-7BC8-83CB-40B61B51E29C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 934C3834-A63A-7BC8-83CB-40B61B51E29C)
2025-07-21 19:30:42,676 - INFO - 开始处理日期: 2025-07-19
2025-07-21 19:30:42,676 - INFO - Request Parameters - Page 1:
2025-07-21 19:30:42,676 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 19:30:42,676 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 19:30:50,805 - ERROR - 处理日期 2025-07-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 48A8DD2E-6461-7EC0-A693-7DB4BCA0C996 Response: {'code': 'ServiceUnavailable', 'requestid': '48A8DD2E-6461-7EC0-A693-7DB4BCA0C996', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 48A8DD2E-6461-7EC0-A693-7DB4BCA0C996)
2025-07-21 19:30:50,805 - INFO - 开始处理日期: 2025-07-20
2025-07-21 19:30:50,805 - INFO - Request Parameters - Page 1:
2025-07-21 19:30:50,805 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 19:30:50,805 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 19:30:51,555 - INFO - Response - Page 1:
2025-07-21 19:30:51,555 - INFO - 第 1 页获取到 50 条记录
2025-07-21 19:30:52,071 - INFO - Request Parameters - Page 2:
2025-07-21 19:30:52,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 19:30:52,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 19:30:52,805 - INFO - Response - Page 2:
2025-07-21 19:30:52,805 - INFO - 第 2 页获取到 50 条记录
2025-07-21 19:30:53,321 - INFO - Request Parameters - Page 3:
2025-07-21 19:30:53,321 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 19:30:53,321 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 19:30:54,056 - INFO - Response - Page 3:
2025-07-21 19:30:54,056 - INFO - 第 3 页获取到 50 条记录
2025-07-21 19:30:54,556 - INFO - Request Parameters - Page 4:
2025-07-21 19:30:54,556 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 19:30:54,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 19:30:55,228 - INFO - Response - Page 4:
2025-07-21 19:30:55,228 - INFO - 第 4 页获取到 50 条记录
2025-07-21 19:30:55,744 - INFO - Request Parameters - Page 5:
2025-07-21 19:30:55,744 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 19:30:55,744 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 19:30:56,526 - INFO - Response - Page 5:
2025-07-21 19:30:56,526 - INFO - 第 5 页获取到 50 条记录
2025-07-21 19:30:57,057 - INFO - Request Parameters - Page 6:
2025-07-21 19:30:57,057 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 19:30:57,057 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 19:30:57,839 - INFO - Response - Page 6:
2025-07-21 19:30:57,839 - INFO - 第 6 页获取到 50 条记录
2025-07-21 19:30:58,354 - INFO - Request Parameters - Page 7:
2025-07-21 19:30:58,354 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 19:30:58,354 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 19:30:59,058 - INFO - Response - Page 7:
2025-07-21 19:30:59,058 - INFO - 第 7 页获取到 50 条记录
2025-07-21 19:30:59,574 - INFO - Request Parameters - Page 8:
2025-07-21 19:30:59,574 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 19:30:59,574 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 19:31:00,246 - INFO - Response - Page 8:
2025-07-21 19:31:00,246 - INFO - 第 8 页获取到 50 条记录
2025-07-21 19:31:00,762 - INFO - Request Parameters - Page 9:
2025-07-21 19:31:00,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 19:31:00,762 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 19:31:01,449 - INFO - Response - Page 9:
2025-07-21 19:31:01,449 - INFO - 第 9 页获取到 22 条记录
2025-07-21 19:31:01,965 - INFO - 查询完成，共获取到 422 条记录
2025-07-21 19:31:01,965 - INFO - 获取到 422 条表单数据
2025-07-21 19:31:01,965 - INFO - 当前日期 2025-07-20 有 122 条MySQL数据需要处理
2025-07-21 19:31:01,965 - INFO - 开始更新记录 - 表单实例ID: FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDM83
2025-07-21 19:31:02,544 - INFO - 更新表单数据成功: FINST-6I766IB194AXTMC2DIWAU9DXIW943WOTPHCDM83
2025-07-21 19:31:02,544 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 4017.65}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 4017.65}, {'field': 'order_count', 'old_value': 0, 'new_value': 7}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/f3ed888b8fdb47f2ae253f7995c5571a.jpg?Expires=2067580947&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=03nFPOqwEh0Ncp75oI2uPgnGjYM%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/47bd473c79a34165995ec592d348e1cd.png?Expires=2067581145&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=DjtPTkUVJylBnP%2FY70Mg1mSjJkw%3D'}]
2025-07-21 19:31:02,544 - INFO - 开始批量插入 13 条新记录
2025-07-21 19:31:02,731 - INFO - 批量插入响应状态码: 200
2025-07-21 19:31:02,731 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 21 Jul 2025 11:31:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '636', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0A556088-9465-7E75-85AE-981EC1C31CBC', 'x-acs-trace-id': '9e6aba780d622fd84538a77d6c5dade8', 'etag': '6SpcVCEhsVglFkKwqgNW7hA6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-21 19:31:02,731 - INFO - 批量插入响应体: {'result': ['FINST-UW966371S7CXXCAABPLOVBPQFDAO2ZARY0DDMJ3', 'FINST-UW966371S7CXXCAABPLOVBPQFDAO2ZARY0DDMK3', 'FINST-UW966371S7CXXCAABPLOVBPQFDAO2ZARY0DDML3', 'FINST-UW966371S7CXXCAABPLOVBPQFDAO2ZARY0DDMM3', 'FINST-UW966371S7CXXCAABPLOVBPQFDAO2ZARY0DDMN3', 'FINST-UW966371S7CXXCAABPLOVBPQFDAO2ZARY0DDMO3', 'FINST-UW966371S7CXXCAABPLOVBPQFDAO2ZARY0DDMP3', 'FINST-UW966371S7CXXCAABPLOVBPQFDAO2ZARY0DDMQ3', 'FINST-UW966371S7CXXCAABPLOVBPQFDAO2ZARY0DDMR3', 'FINST-UW966371S7CXXCAABPLOVBPQFDAO2ZARY0DDMS3', 'FINST-UW966371S7CXXCAABPLOVBPQFDAO2ZARY0DDMT3', 'FINST-UW966371S7CXXCAABPLOVBPQFDAO2ZARY0DDMU3', 'FINST-UW966371S7CXXCAABPLOVBPQFDAO2ZARY0DDMV3']}
2025-07-21 19:31:02,731 - INFO - 批量插入表单数据成功，批次 1，共 13 条记录
2025-07-21 19:31:02,731 - INFO - 成功插入的数据ID: ['FINST-UW966371S7CXXCAABPLOVBPQFDAO2ZARY0DDMJ3', 'FINST-UW966371S7CXXCAABPLOVBPQFDAO2ZARY0DDMK3', 'FINST-UW966371S7CXXCAABPLOVBPQFDAO2ZARY0DDML3', 'FINST-UW966371S7CXXCAABPLOVBPQFDAO2ZARY0DDMM3', 'FINST-UW966371S7CXXCAABPLOVBPQFDAO2ZARY0DDMN3', 'FINST-UW966371S7CXXCAABPLOVBPQFDAO2ZARY0DDMO3', 'FINST-UW966371S7CXXCAABPLOVBPQFDAO2ZARY0DDMP3', 'FINST-UW966371S7CXXCAABPLOVBPQFDAO2ZARY0DDMQ3', 'FINST-UW966371S7CXXCAABPLOVBPQFDAO2ZARY0DDMR3', 'FINST-UW966371S7CXXCAABPLOVBPQFDAO2ZARY0DDMS3', 'FINST-UW966371S7CXXCAABPLOVBPQFDAO2ZARY0DDMT3', 'FINST-UW966371S7CXXCAABPLOVBPQFDAO2ZARY0DDMU3', 'FINST-UW966371S7CXXCAABPLOVBPQFDAO2ZARY0DDMV3']
2025-07-21 19:31:07,749 - INFO - 批量插入完成，共 13 条记录
2025-07-21 19:31:07,749 - INFO - 日期 2025-07-20 处理完成 - 更新: 1 条，插入: 13 条，错误: 0 条
2025-07-21 19:31:07,749 - INFO - 开始处理日期: 2025-07-21
2025-07-21 19:31:07,749 - INFO - Request Parameters - Page 1:
2025-07-21 19:31:07,749 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 19:31:07,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 19:31:08,249 - INFO - Response - Page 1:
2025-07-21 19:31:08,249 - INFO - 第 1 页获取到 2 条记录
2025-07-21 19:31:08,765 - INFO - 查询完成，共获取到 2 条记录
2025-07-21 19:31:08,765 - INFO - 获取到 2 条表单数据
2025-07-21 19:31:08,765 - INFO - 当前日期 2025-07-21 有 3 条MySQL数据需要处理
2025-07-21 19:31:08,765 - INFO - 开始批量插入 1 条新记录
2025-07-21 19:31:08,937 - INFO - 批量插入响应状态码: 200
2025-07-21 19:31:08,937 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 21 Jul 2025 11:31:08 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '********-C222-76A6-B65B-2C9D56EB96CE', 'x-acs-trace-id': '072b6874d1a53a6830812785942f4f60', 'etag': '6x8rgfnVGHPe8G6w4blff1A0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-21 19:31:08,937 - INFO - 批量插入响应体: {'result': ['FINST-LR5668B1QBCX4OZD6I1XL9NAC976253WY0DDML1']}
2025-07-21 19:31:08,937 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-21 19:31:08,937 - INFO - 成功插入的数据ID: ['FINST-LR5668B1QBCX4OZD6I1XL9NAC976253WY0DDML1']
2025-07-21 19:31:13,954 - INFO - 批量插入完成，共 1 条记录
2025-07-21 19:31:13,954 - INFO - 日期 2025-07-21 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-21 19:31:13,954 - INFO - 数据同步完成！更新: 1 条，插入: 14 条，错误: 2 条
2025-07-21 19:32:13,994 - INFO - 开始同步昨天与今天的销售数据: 2025-07-20 至 2025-07-21
2025-07-21 19:32:13,994 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-21 19:32:13,994 - INFO - 查询参数: ('2025-07-20', '2025-07-21')
2025-07-21 19:32:14,150 - INFO - MySQL查询成功，时间段: 2025-07-20 至 2025-07-21，共获取 467 条记录
2025-07-21 19:32:14,150 - INFO - 获取到 2 个日期需要处理: ['2025-07-20', '2025-07-21']
2025-07-21 19:32:14,150 - INFO - 开始处理日期: 2025-07-20
2025-07-21 19:32:14,150 - INFO - Request Parameters - Page 1:
2025-07-21 19:32:14,150 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 19:32:14,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 19:32:14,963 - INFO - Response - Page 1:
2025-07-21 19:32:14,963 - INFO - 第 1 页获取到 50 条记录
2025-07-21 19:32:15,463 - INFO - Request Parameters - Page 2:
2025-07-21 19:32:15,463 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 19:32:15,463 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 19:32:16,182 - INFO - Response - Page 2:
2025-07-21 19:32:16,182 - INFO - 第 2 页获取到 50 条记录
2025-07-21 19:32:16,682 - INFO - Request Parameters - Page 3:
2025-07-21 19:32:16,682 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 19:32:16,682 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 19:32:17,354 - INFO - Response - Page 3:
2025-07-21 19:32:17,354 - INFO - 第 3 页获取到 50 条记录
2025-07-21 19:32:17,855 - INFO - Request Parameters - Page 4:
2025-07-21 19:32:17,855 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 19:32:17,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 19:32:18,652 - INFO - Response - Page 4:
2025-07-21 19:32:18,652 - INFO - 第 4 页获取到 50 条记录
2025-07-21 19:32:19,168 - INFO - Request Parameters - Page 5:
2025-07-21 19:32:19,168 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 19:32:19,168 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 19:32:19,840 - INFO - Response - Page 5:
2025-07-21 19:32:19,840 - INFO - 第 5 页获取到 50 条记录
2025-07-21 19:32:20,340 - INFO - Request Parameters - Page 6:
2025-07-21 19:32:20,340 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 19:32:20,340 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 19:32:21,090 - INFO - Response - Page 6:
2025-07-21 19:32:21,090 - INFO - 第 6 页获取到 50 条记录
2025-07-21 19:32:21,591 - INFO - Request Parameters - Page 7:
2025-07-21 19:32:21,591 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 19:32:21,591 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 19:32:22,310 - INFO - Response - Page 7:
2025-07-21 19:32:22,310 - INFO - 第 7 页获取到 50 条记录
2025-07-21 19:32:22,810 - INFO - Request Parameters - Page 8:
2025-07-21 19:32:22,810 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 19:32:22,810 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 19:32:23,607 - INFO - Response - Page 8:
2025-07-21 19:32:23,607 - INFO - 第 8 页获取到 50 条记录
2025-07-21 19:32:24,123 - INFO - Request Parameters - Page 9:
2025-07-21 19:32:24,123 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 19:32:24,123 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 19:32:24,811 - INFO - Response - Page 9:
2025-07-21 19:32:24,811 - INFO - 第 9 页获取到 35 条记录
2025-07-21 19:32:25,311 - INFO - 查询完成，共获取到 435 条记录
2025-07-21 19:32:25,311 - INFO - 获取到 435 条表单数据
2025-07-21 19:32:25,311 - INFO - 当前日期 2025-07-20 有 438 条MySQL数据需要处理
2025-07-21 19:32:25,326 - INFO - 开始批量插入 3 条新记录
2025-07-21 19:32:25,467 - INFO - 批量插入响应状态码: 200
2025-07-21 19:32:25,467 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 21 Jul 2025 11:32:25 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '156', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2049706F-E842-79D0-86CA-95A30B8C83EC', 'x-acs-trace-id': 'f265fabdde8bc428a140819621143f05', 'etag': '10/ynzCiHwCB/0IqT+O33/w6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-21 19:32:25,467 - INFO - 批量插入响应体: {'result': ['FINST-TQB66671G7CX7XFFBVBM45K8PR5I3G4J01DDMQ1', 'FINST-TQB66671G7CX7XFFBVBM45K8PR5I3G4J01DDMR1', 'FINST-TQB66671G7CX7XFFBVBM45K8PR5I3G4J01DDMS1']}
2025-07-21 19:32:25,467 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-07-21 19:32:25,467 - INFO - 成功插入的数据ID: ['FINST-TQB66671G7CX7XFFBVBM45K8PR5I3G4J01DDMQ1', 'FINST-TQB66671G7CX7XFFBVBM45K8PR5I3G4J01DDMR1', 'FINST-TQB66671G7CX7XFFBVBM45K8PR5I3G4J01DDMS1']
2025-07-21 19:32:30,485 - INFO - 批量插入完成，共 3 条记录
2025-07-21 19:32:30,485 - INFO - 日期 2025-07-20 处理完成 - 更新: 0 条，插入: 3 条，错误: 0 条
2025-07-21 19:32:30,485 - INFO - 开始处理日期: 2025-07-21
2025-07-21 19:32:30,485 - INFO - Request Parameters - Page 1:
2025-07-21 19:32:30,485 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 19:32:30,485 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 19:32:31,001 - INFO - Response - Page 1:
2025-07-21 19:32:31,001 - INFO - 第 1 页获取到 3 条记录
2025-07-21 19:32:31,516 - INFO - 查询完成，共获取到 3 条记录
2025-07-21 19:32:31,516 - INFO - 获取到 3 条表单数据
2025-07-21 19:32:31,516 - INFO - 当前日期 2025-07-21 有 3 条MySQL数据需要处理
2025-07-21 19:32:31,516 - INFO - 日期 2025-07-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-21 19:32:31,516 - INFO - 数据同步完成！更新: 0 条，插入: 3 条，错误: 0 条
2025-07-21 19:32:31,516 - INFO - 同步完成
2025-07-21 22:30:35,470 - INFO - 使用默认增量同步（当天更新数据）
2025-07-21 22:30:35,470 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-21 22:30:35,470 - INFO - 查询参数: ('2025-07-21',)
2025-07-21 22:30:35,642 - INFO - MySQL查询成功，增量数据（日期: 2025-07-21），共获取 213 条记录
2025-07-21 22:30:35,642 - INFO - 获取到 4 个日期需要处理: ['2025-07-18', '2025-07-19', '2025-07-20', '2025-07-21']
2025-07-21 22:30:35,642 - INFO - 开始处理日期: 2025-07-18
2025-07-21 22:30:35,642 - INFO - Request Parameters - Page 1:
2025-07-21 22:30:35,642 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 22:30:35,642 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 22:30:43,771 - ERROR - 处理日期 2025-07-18 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B1D2909B-CC61-703B-96E2-E647DBC6F857 Response: {'code': 'ServiceUnavailable', 'requestid': 'B1D2909B-CC61-703B-96E2-E647DBC6F857', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B1D2909B-CC61-703B-96E2-E647DBC6F857)
2025-07-21 22:30:43,771 - INFO - 开始处理日期: 2025-07-19
2025-07-21 22:30:43,771 - INFO - Request Parameters - Page 1:
2025-07-21 22:30:43,771 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 22:30:43,771 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 22:30:44,537 - INFO - Response - Page 1:
2025-07-21 22:30:44,537 - INFO - 第 1 页获取到 50 条记录
2025-07-21 22:30:45,052 - INFO - Request Parameters - Page 2:
2025-07-21 22:30:45,052 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 22:30:45,052 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752854400000, 1752940799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 22:30:53,149 - ERROR - 处理日期 2025-07-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: EE1D025F-0D0E-734E-9D92-ADA594A48871 Response: {'code': 'ServiceUnavailable', 'requestid': 'EE1D025F-0D0E-734E-9D92-ADA594A48871', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: EE1D025F-0D0E-734E-9D92-ADA594A48871)
2025-07-21 22:30:53,149 - INFO - 开始处理日期: 2025-07-20
2025-07-21 22:30:53,149 - INFO - Request Parameters - Page 1:
2025-07-21 22:30:53,149 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 22:30:53,149 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 22:30:56,276 - INFO - Response - Page 1:
2025-07-21 22:30:56,276 - INFO - 第 1 页获取到 50 条记录
2025-07-21 22:30:56,791 - INFO - Request Parameters - Page 2:
2025-07-21 22:30:56,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 22:30:56,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 22:30:57,464 - INFO - Response - Page 2:
2025-07-21 22:30:57,464 - INFO - 第 2 页获取到 50 条记录
2025-07-21 22:30:57,964 - INFO - Request Parameters - Page 3:
2025-07-21 22:30:57,964 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 22:30:57,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 22:30:58,652 - INFO - Response - Page 3:
2025-07-21 22:30:58,652 - INFO - 第 3 页获取到 50 条记录
2025-07-21 22:30:59,167 - INFO - Request Parameters - Page 4:
2025-07-21 22:30:59,167 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 22:30:59,167 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 22:30:59,933 - INFO - Response - Page 4:
2025-07-21 22:30:59,933 - INFO - 第 4 页获取到 50 条记录
2025-07-21 22:31:00,434 - INFO - Request Parameters - Page 5:
2025-07-21 22:31:00,434 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 22:31:00,434 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 22:31:01,184 - INFO - Response - Page 5:
2025-07-21 22:31:01,184 - INFO - 第 5 页获取到 50 条记录
2025-07-21 22:31:01,700 - INFO - Request Parameters - Page 6:
2025-07-21 22:31:01,700 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 22:31:01,700 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 22:31:02,419 - INFO - Response - Page 6:
2025-07-21 22:31:02,419 - INFO - 第 6 页获取到 50 条记录
2025-07-21 22:31:02,919 - INFO - Request Parameters - Page 7:
2025-07-21 22:31:02,919 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 22:31:02,919 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 22:31:03,638 - INFO - Response - Page 7:
2025-07-21 22:31:03,638 - INFO - 第 7 页获取到 50 条记录
2025-07-21 22:31:04,138 - INFO - Request Parameters - Page 8:
2025-07-21 22:31:04,138 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 22:31:04,138 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 22:31:04,842 - INFO - Response - Page 8:
2025-07-21 22:31:04,842 - INFO - 第 8 页获取到 50 条记录
2025-07-21 22:31:05,357 - INFO - Request Parameters - Page 9:
2025-07-21 22:31:05,357 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 22:31:05,357 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 22:31:06,092 - INFO - Response - Page 9:
2025-07-21 22:31:06,092 - INFO - 第 9 页获取到 38 条记录
2025-07-21 22:31:06,608 - INFO - 查询完成，共获取到 438 条记录
2025-07-21 22:31:06,608 - INFO - 获取到 438 条表单数据
2025-07-21 22:31:06,608 - INFO - 当前日期 2025-07-20 有 147 条MySQL数据需要处理
2025-07-21 22:31:06,608 - INFO - 开始批量插入 25 条新记录
2025-07-21 22:31:06,842 - INFO - 批量插入响应状态码: 200
2025-07-21 22:31:06,842 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 21 Jul 2025 14:31:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1212', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '1767BDE4-B0EB-73E7-B9F2-AB370CB4AF51', 'x-acs-trace-id': 'ecd88ea486440065bbec95c0f625b577', 'etag': '1aDOyYEWsTLCSDzHs3KFQJg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-21 22:31:06,842 - INFO - 批量插入响应体: {'result': ['FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDMR2', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDMS2', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDMT2', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDMU2', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDMV2', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDMW2', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDMX2', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDMY2', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDMZ2', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDM03', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDM13', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDM23', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDM33', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDM43', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDM53', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDM63', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDM73', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDM83', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDM93', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDMA3', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDMB3', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDMC3', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDMD3', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDME3', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDMF3']}
2025-07-21 22:31:06,842 - INFO - 批量插入表单数据成功，批次 1，共 25 条记录
2025-07-21 22:31:06,842 - INFO - 成功插入的数据ID: ['FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDMR2', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDMS2', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDMT2', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDMU2', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDMV2', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDMW2', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDMX2', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDMY2', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDMZ2', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDM03', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDM13', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDM23', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDM33', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDM43', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDM53', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDM63', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDM73', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDM83', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDM93', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDMA3', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDMB3', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDMC3', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDMD3', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDME3', 'FINST-8SG66JA1DBCXE8RSB2E5DDQMFMK53AI8E7DDMF3']
2025-07-21 22:31:11,860 - INFO - 批量插入完成，共 25 条记录
2025-07-21 22:31:11,860 - INFO - 日期 2025-07-20 处理完成 - 更新: 0 条，插入: 25 条，错误: 0 条
2025-07-21 22:31:11,860 - INFO - 开始处理日期: 2025-07-21
2025-07-21 22:31:11,860 - INFO - Request Parameters - Page 1:
2025-07-21 22:31:11,860 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 22:31:11,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 22:31:12,344 - INFO - Response - Page 1:
2025-07-21 22:31:12,344 - INFO - 第 1 页获取到 3 条记录
2025-07-21 22:31:12,860 - INFO - 查询完成，共获取到 3 条记录
2025-07-21 22:31:12,860 - INFO - 获取到 3 条表单数据
2025-07-21 22:31:12,860 - INFO - 当前日期 2025-07-21 有 52 条MySQL数据需要处理
2025-07-21 22:31:12,860 - INFO - 开始批量插入 49 条新记录
2025-07-21 22:31:13,095 - INFO - 批量插入响应状态码: 200
2025-07-21 22:31:13,095 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 21 Jul 2025 14:31:08 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2364', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '04E9E2EE-C109-779A-AEEF-78220F8FBCA8', 'x-acs-trace-id': '4ea1130c5f0eabde8979300dd03836d9', 'etag': '2bJruvaA+VtQoNfPMvKgZNA4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-21 22:31:13,095 - INFO - 批量插入响应体: {'result': ['FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDM52', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDM62', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDM72', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDM82', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDM92', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMA2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMB2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMC2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMD2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDME2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMF2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMG2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMH2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMI2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMJ2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMK2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDML2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMM2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMN2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMO2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMP2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMQ2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMR2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMS2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMT2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMU2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMV2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMW2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMX2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMY2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMZ2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDM03', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDM13', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDM23', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDM33', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDM43', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDM53', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDM63', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDM73', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDM83', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDM93', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMA3', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMB3', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMC3', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMD3', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDME3', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMF3', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMG3', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMH3']}
2025-07-21 22:31:13,095 - INFO - 批量插入表单数据成功，批次 1，共 49 条记录
2025-07-21 22:31:13,095 - INFO - 成功插入的数据ID: ['FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDM52', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDM62', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDM72', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDM82', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDM92', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMA2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMB2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMC2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMD2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDME2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMF2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMG2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMH2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMI2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMJ2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMK2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDML2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMM2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMN2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMO2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMP2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMQ2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMR2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMS2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMT2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMU2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMV2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMW2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMX2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMY2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMZ2', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDM03', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDM13', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDM23', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDM33', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDM43', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDM53', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDM63', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDM73', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDM83', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDM93', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMA3', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMB3', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMC3', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMD3', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDME3', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMF3', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMG3', 'FINST-8LC66GC1Z3CXS80JFFOHG6Y4XGLD2UBDE7DDMH3']
2025-07-21 22:31:18,112 - INFO - 批量插入完成，共 49 条记录
2025-07-21 22:31:18,112 - INFO - 日期 2025-07-21 处理完成 - 更新: 0 条，插入: 49 条，错误: 0 条
2025-07-21 22:31:18,112 - INFO - 数据同步完成！更新: 0 条，插入: 74 条，错误: 2 条
2025-07-21 22:32:18,152 - INFO - 开始同步昨天与今天的销售数据: 2025-07-20 至 2025-07-21
2025-07-21 22:32:18,152 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-21 22:32:18,152 - INFO - 查询参数: ('2025-07-20', '2025-07-21')
2025-07-21 22:32:18,324 - INFO - MySQL查询成功，时间段: 2025-07-20 至 2025-07-21，共获取 613 条记录
2025-07-21 22:32:18,324 - INFO - 获取到 2 个日期需要处理: ['2025-07-20', '2025-07-21']
2025-07-21 22:32:18,324 - INFO - 开始处理日期: 2025-07-20
2025-07-21 22:32:18,324 - INFO - Request Parameters - Page 1:
2025-07-21 22:32:18,324 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 22:32:18,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 22:32:19,090 - INFO - Response - Page 1:
2025-07-21 22:32:19,090 - INFO - 第 1 页获取到 50 条记录
2025-07-21 22:32:19,590 - INFO - Request Parameters - Page 2:
2025-07-21 22:32:19,590 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 22:32:19,590 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 22:32:20,293 - INFO - Response - Page 2:
2025-07-21 22:32:20,293 - INFO - 第 2 页获取到 50 条记录
2025-07-21 22:32:20,809 - INFO - Request Parameters - Page 3:
2025-07-21 22:32:20,809 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 22:32:20,809 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 22:32:21,513 - INFO - Response - Page 3:
2025-07-21 22:32:21,513 - INFO - 第 3 页获取到 50 条记录
2025-07-21 22:32:22,028 - INFO - Request Parameters - Page 4:
2025-07-21 22:32:22,028 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 22:32:22,028 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 22:32:22,747 - INFO - Response - Page 4:
2025-07-21 22:32:22,747 - INFO - 第 4 页获取到 50 条记录
2025-07-21 22:32:23,248 - INFO - Request Parameters - Page 5:
2025-07-21 22:32:23,248 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 22:32:23,248 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 22:32:23,967 - INFO - Response - Page 5:
2025-07-21 22:32:23,967 - INFO - 第 5 页获取到 50 条记录
2025-07-21 22:32:24,467 - INFO - Request Parameters - Page 6:
2025-07-21 22:32:24,467 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 22:32:24,467 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 22:32:25,139 - INFO - Response - Page 6:
2025-07-21 22:32:25,139 - INFO - 第 6 页获取到 50 条记录
2025-07-21 22:32:25,639 - INFO - Request Parameters - Page 7:
2025-07-21 22:32:25,639 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 22:32:25,639 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 22:32:26,405 - INFO - Response - Page 7:
2025-07-21 22:32:26,405 - INFO - 第 7 页获取到 50 条记录
2025-07-21 22:32:26,905 - INFO - Request Parameters - Page 8:
2025-07-21 22:32:26,905 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 22:32:26,905 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 22:32:27,624 - INFO - Response - Page 8:
2025-07-21 22:32:27,624 - INFO - 第 8 页获取到 50 条记录
2025-07-21 22:32:28,140 - INFO - Request Parameters - Page 9:
2025-07-21 22:32:28,140 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 22:32:28,140 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 22:32:28,922 - INFO - Response - Page 9:
2025-07-21 22:32:28,922 - INFO - 第 9 页获取到 50 条记录
2025-07-21 22:32:29,438 - INFO - Request Parameters - Page 10:
2025-07-21 22:32:29,438 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 22:32:29,438 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752940800000, 1753027199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 22:32:30,016 - INFO - Response - Page 10:
2025-07-21 22:32:30,016 - INFO - 第 10 页获取到 13 条记录
2025-07-21 22:32:30,516 - INFO - 查询完成，共获取到 463 条记录
2025-07-21 22:32:30,516 - INFO - 获取到 463 条表单数据
2025-07-21 22:32:30,516 - INFO - 当前日期 2025-07-20 有 532 条MySQL数据需要处理
2025-07-21 22:32:30,532 - INFO - 开始批量插入 69 条新记录
2025-07-21 22:32:30,751 - INFO - 批量插入响应状态码: 200
2025-07-21 22:32:30,751 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 21 Jul 2025 14:32:26 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '8EF3E4CC-5FCA-7080-AB48-123F8E4E12C8', 'x-acs-trace-id': 'c481d6803a776f40c19cbeba43b76e25', 'etag': '2uhKEsXozkSvyWp+Epwu9fQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-21 22:32:30,751 - INFO - 批量插入响应体: {'result': ['FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMJ6', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMK6', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDML6', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMM6', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMN6', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMO6', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMP6', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMQ6', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMR6', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMS6', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMT6', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMU6', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMV6', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMW6', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMX6', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMY6', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMZ6', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDM07', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDM17', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDM27', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDM37', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDM47', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDM57', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDM67', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDM77', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDM87', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDM97', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMA7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMB7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMC7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMD7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDME7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMF7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMG7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMH7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMI7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMJ7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMK7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDML7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMM7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMN7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMO7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMP7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMQ7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMR7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMS7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMT7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMU7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMV7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMW7']}
2025-07-21 22:32:30,751 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-21 22:32:30,751 - INFO - 成功插入的数据ID: ['FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMJ6', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMK6', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDML6', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMM6', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMN6', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMO6', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMP6', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMQ6', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMR6', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMS6', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMT6', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMU6', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMV6', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMW6', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMX6', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMY6', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMZ6', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDM07', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDM17', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDM27', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDM37', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDM47', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDM57', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDM67', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDM77', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDM87', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDM97', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMA7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMB7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMC7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMD7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDME7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMF7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMG7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMH7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMI7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMJ7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMK7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDML7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMM7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMN7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMO7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMP7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMQ7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMR7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMS7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMT7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMU7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMV7', 'FINST-3PF66271S7CXMG0L7PK0775MQA152C81G7DDMW7']
2025-07-21 22:32:35,987 - INFO - 批量插入响应状态码: 200
2025-07-21 22:32:35,987 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 21 Jul 2025 14:32:31 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '924', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F0B4F31A-A904-789F-96D4-95EFBCD0DF3F', 'x-acs-trace-id': 'fb3a80c03c4584256da87215d18381be', 'etag': '9GeJM7gpRDb+9tDrTmrk09w4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-21 22:32:35,987 - INFO - 批量插入响应体: {'result': ['FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDM92', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDMA2', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDMB2', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDMC2', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDMD2', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDME2', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDMF2', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDMG2', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDMH2', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDMI2', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDMJ2', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDMK2', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDML2', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDMM2', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDMN2', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDMO2', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDMP2', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDMQ2', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDMR2']}
2025-07-21 22:32:35,987 - INFO - 批量插入表单数据成功，批次 2，共 19 条记录
2025-07-21 22:32:35,987 - INFO - 成功插入的数据ID: ['FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDM92', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDMA2', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDMB2', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDMC2', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDMD2', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDME2', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDMF2', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDMG2', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDMH2', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDMI2', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDMJ2', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDMK2', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDML2', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDMM2', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDMN2', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDMO2', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDMP2', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDMQ2', 'FINST-F7D66UA14BCXB6EADXFGOBZO22UD2F95G7DDMR2']
2025-07-21 22:32:41,005 - INFO - 批量插入完成，共 69 条记录
2025-07-21 22:32:41,005 - INFO - 日期 2025-07-20 处理完成 - 更新: 0 条，插入: 69 条，错误: 0 条
2025-07-21 22:32:41,005 - INFO - 开始处理日期: 2025-07-21
2025-07-21 22:32:41,005 - INFO - Request Parameters - Page 1:
2025-07-21 22:32:41,005 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 22:32:41,005 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 22:32:41,724 - INFO - Response - Page 1:
2025-07-21 22:32:41,724 - INFO - 第 1 页获取到 50 条记录
2025-07-21 22:32:42,224 - INFO - Request Parameters - Page 2:
2025-07-21 22:32:42,224 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-21 22:32:42,224 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-21 22:32:42,708 - INFO - Response - Page 2:
2025-07-21 22:32:42,708 - INFO - 第 2 页获取到 2 条记录
2025-07-21 22:32:43,209 - INFO - 查询完成，共获取到 52 条记录
2025-07-21 22:32:43,209 - INFO - 获取到 52 条表单数据
2025-07-21 22:32:43,209 - INFO - 当前日期 2025-07-21 有 52 条MySQL数据需要处理
2025-07-21 22:32:43,209 - INFO - 日期 2025-07-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-21 22:32:43,209 - INFO - 数据同步完成！更新: 0 条，插入: 69 条，错误: 0 条
2025-07-21 22:32:43,209 - INFO - 同步完成
