#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
批量删除宜搭表单实例
使用方法: python batch_delete_yida_form.py [开始日期(YYYYMMDD)] [结束日期(YYYYMMDD)]
"""

import sys
import logging
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
import json

# 导入宜搭 1.0 版本和 2.0 版本的客户端
from alibabacloud_dingtalk.yida_1_0.client import Client as dingtalkyida_1_0Client
from alibabacloud_dingtalk.yida_2_0.client import Client as dingtalkyida_2_0Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dingtalk.yida_1_0 import models as dingtalkyida__1__0_models
from alibabacloud_dingtalk.yida_2_0 import models as dingtalkyida__2__0_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_util.client import Client as UtilClient
from get_token import token

# 配置日志
today = datetime.now().strftime('%Y%m%d')
logging.basicConfig(
    filename=f'batch_delete_yida_form_{today}.log',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    encoding='utf-8'
)

# 宜搭表单配置信息
YIDA_CONFIG = {
    'APP_TYPE': 'APP_D7E6ZB94ZUL5Q1GUAOLD',
    'SYSTEM_TOKEN': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2',
    'USER_ID': 'hexuepeng',
    'LANGUAGE': 'zh_CN',
    'FORM_UUID': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P',  # 采集日销售表单
    # 'FORM_UUID': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30'  # 上报明细表
}

class YidaFormBatchDelete:
    def __init__(self):
        """初始化批量删除工具类"""
        self.access_token = token.get_token()
        # 创建两个不同版本的客户端
        self.client_v1 = self._create_client_v1()  # 1.0版本用于删除操作
        self.client_v2 = self._create_client_v2()  # 2.0版本用于查询操作

    def _create_client_v1(self) -> dingtalkyida_1_0Client:
        """
        创建宜搭1.0客户端，用于删除操作
        
        Returns:
            dingtalkyida_1_0Client: 宜搭1.0客户端实例
        """
        try:
            config = open_api_models.Config(
                protocol='https',
                region_id='central'
            )
            return dingtalkyida_1_0Client(config)
        except Exception as e:
            logging.error(f"创建宜搭1.0 API客户端失败: {str(e)}", exc_info=True)
            raise

    def _create_client_v2(self) -> dingtalkyida_2_0Client:
        """
        创建宜搭2.0客户端，用于查询操作
        
        Returns:
            dingtalkyida_2_0Client: 宜搭2.0客户端实例
        """
        try:
            config = open_api_models.Config(
                protocol='https',
                region_id='central'
            )
            return dingtalkyida_2_0Client(config)
        except Exception as e:
            logging.error(f"创建宜搭2.0 API客户端失败: {str(e)}", exc_info=True)
            raise

    def get_form_instance_ids(self, start_date: datetime, end_date: datetime, segment_days: int = 1) -> List[str]:
        """
        获取指定日期范围内的表单实例ID列表
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            segment_days: 每次查询的天数，默认7天
            
        Returns:
            List[str]: 表单实例ID列表
        """
        all_instance_ids = []
        current_start = start_date
        
        while current_start <= end_date:
            # 计算当前分段结束日期
            current_end = min(current_start + timedelta(days=segment_days - 1), end_date)
            
            logging.info(f"查询表单实例 - 日期范围: {current_start.strftime('%Y-%m-%d')} 至 {current_end.strftime('%Y-%m-%d')}")
            
            # 构建日期筛选条件
            search_condition = {
                "searchFieldJson": json.dumps([{
                    "key": "dateField_m9dkdkoz",  # 采集日销售表单-销售日期字段
                    # "key": "dateField_m9nw1k71",  # 上报明细表-销售日期字段
                    "value": [int(current_start.timestamp() * 1000), int(current_end.timestamp() * 1000)],
                    "type": "DOUBLE",
                    "operator": "between",
                    "componentName": "DateField"
                }])
            }
            
            try:
                instance_ids = self._search_form_data_ids(search_condition)
                if instance_ids:
                    all_instance_ids.extend(instance_ids)
                    logging.info(f"找到 {len(instance_ids)} 个表单实例")
                else:
                    logging.info("未找到表单实例")
                
                # 更新开始日期为下一个分段
                current_start = current_end + timedelta(days=1)
                
                # 添加延时避免请求过于频繁
                time.sleep(1)
                
            except Exception as e:
                logging.error(f"查询表单实例ID失败 ({current_start.strftime('%Y-%m-%d')} 至 {current_end.strftime('%Y-%m-%d')}): {str(e)}")
                # 继续下一个时间段
                current_start = current_end + timedelta(days=1)
        
        logging.info(f"总共找到 {len(all_instance_ids)} 个表单实例")
        return all_instance_ids

    def _search_form_data_ids(self, search_condition: Dict) -> List[str]:
        """
        使用宜搭2.0客户端搜索表单数据ID
        
        Args:
            search_condition: 搜索条件
            
        Returns:
            List[str]: 表单实例ID列表
        """
        try:
            all_instance_ids = []
            current_page = 1
            page_size = 100
            
            while True:
                headers = dingtalkyida__2__0_models.SearchFormDataIdListHeaders()
                headers.x_acs_dingtalk_access_token = self.access_token
                
                # 创建2.0版本的请求对象
                request = dingtalkyida__2__0_models.SearchFormDataIdListRequest(
                    page_number=current_page,
                    page_size=page_size,
                    system_token=YIDA_CONFIG['SYSTEM_TOKEN'],
                    language=YIDA_CONFIG['LANGUAGE'],
                    user_id=YIDA_CONFIG['USER_ID'],
                    search_field_json=search_condition.get("searchFieldJson", "[]"),
                    use_alias=False
                )
                
                # 使用2.0客户端查询ID列表
                result = self.client_v2.search_form_data_id_list_with_options(
                    YIDA_CONFIG['APP_TYPE'],
                    YIDA_CONFIG['FORM_UUID'],
                    request, 
                    headers, 
                    util_models.RuntimeOptions()
                )
                
                # 提取表单实例ID
                if result and result.body and result.body.data:
                    all_instance_ids.extend(result.body.data)
                    
                    # 判断是否是最后一页
                    if len(result.body.data) < page_size:
                        break
                    
                    current_page += 1
                else:
                    break
                
                # 添加延时避免请求过于频繁
                time.sleep(0.5)
            
            return all_instance_ids
            
        except Exception as e:
            error_msg = f"搜索表单实例ID失败: {str(e)}"
            if hasattr(e, 'code') and hasattr(e, 'message'):
                error_msg += f" (错误码: {e.code}, 错误信息: {e.message})"
            logging.error(error_msg)
            raise Exception(error_msg)

    def batch_delete_form_instances(self, instance_ids: List[str], batch_size: int = 500) -> bool:
        """
        使用宜搭1.0客户端批量删除表单实例
        
        Args:
            instance_ids: 表单实例ID列表
            batch_size: 每批删除的数量，默认20条
            
        Returns:
            bool: 是否全部删除成功
        """
        if not instance_ids:
            logging.warning("没有需要删除的表单实例")
            return True
        
        try:
            total_count = len(instance_ids)
            success_count = 0
            error_count = 0
            
            logging.info(f"开始批量删除，共 {total_count} 条记录，每批 {batch_size} 条")
            
            # 分批删除
            for i in range(0, total_count, batch_size):
                batch = instance_ids[i:i + batch_size]
                
                try:
                    headers = dingtalkyida__1__0_models.BatchRemovalByFormInstanceIdListHeaders()
                    headers.x_acs_dingtalk_access_token = self.access_token
                    
                    request = dingtalkyida__1__0_models.BatchRemovalByFormInstanceIdListRequest(
                        form_uuid=YIDA_CONFIG['FORM_UUID'],
                        app_type=YIDA_CONFIG['APP_TYPE'],
                        asynchronous_execution=True,
                        system_token=YIDA_CONFIG['SYSTEM_TOKEN'],
                        form_instance_id_list=batch,
                        user_id=YIDA_CONFIG['USER_ID'],
                        execute_expression=False
                    )
                    
                    # 使用1.0客户端执行删除操作
                    response = self.client_v1.batch_removal_by_form_instance_id_list_with_options(
                        request, 
                        headers, 
                        util_models.RuntimeOptions()
                    )
                    
                    # 检查响应结果
                    if response and response.status_code == 200:
                        batch_success = len(batch)
                        success_count += batch_success
                        logging.info(f"批次 {i//batch_size + 1} 删除成功，{batch_success} 条记录")
                    else:
                        error_count += len(batch)
                        logging.error(f"批次 {i//batch_size + 1} 删除失败: {response.status_code}")
                    
                except Exception as e:
                    error_count += len(batch)
                    logging.error(f"批次 {i//batch_size + 1} 删除出错: {str(e)}")
                
                # 添加延时避免请求过于频繁
                time.sleep(1)
            
            logging.info(f"批量删除完成，成功: {success_count}，失败: {error_count}")
            return error_count == 0
            
        except Exception as e:
            logging.error(f"批量删除出错: {str(e)}")
            return False

def parse_date(date_str: str) -> datetime:
    """
    解析日期字符串
    
    Args:
        date_str: 日期字符串，格式为YYYYMMDD
        
    Returns:
        datetime: 日期对象
    """
    try:
        return datetime.strptime(date_str, '%Y%m%d')
    except ValueError:
        raise ValueError(f"日期格式错误，应为YYYYMMDD，实际为: {date_str}")

def main():
    """主函数"""
    logging.info("="*50)
    logging.info("批量删除宜搭表单实例 - 开始执行")
    logging.info("="*50)
    
    try:
        # 解析命令行参数
        if len(sys.argv) < 3:
            print("使用方法: python batch_delete_yida_form.py [开始日期(YYYYMMDD)] [结束日期(YYYYMMDD)]")
            logging.error("参数不足，需要提供开始日期和结束日期")
            return
        
        start_date_str = sys.argv[1]
        end_date_str = sys.argv[2]
        
        try:
            start_date = parse_date(start_date_str)
            end_date = parse_date(end_date_str)
            
            # 设置时间
            start_date = start_date.replace(hour=0, minute=0, second=0)
            end_date = end_date.replace(hour=23, minute=59, second=59)
            
            if start_date > end_date:
                raise ValueError("开始日期不能晚于结束日期")
            
        except ValueError as e:
            print(f"日期格式错误: {str(e)}")
            logging.error(f"日期格式错误: {str(e)}")
            return
        
        logging.info(f"要删除的日期范围: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")
        
        # 创建删除工具实例
        deleter = YidaFormBatchDelete()
        
        # 获取表单实例ID
        instance_ids = deleter.get_form_instance_ids(start_date, end_date)
        
        if not instance_ids:
            print("未找到符合条件的表单实例，无需删除")
            logging.info("未找到符合条件的表单实例，无需删除")
            return
        
        # # 确认是否删除
        # print(f"找到 {len(instance_ids)} 条符合条件的表单实例，是否删除？(y/n)")
        # confirm = input().strip().lower()
        
        # if confirm != 'y':
        #     print("操作已取消")
        #     logging.info("用户取消删除操作")
        #     return
        
        # 执行批量删除
        success = deleter.batch_delete_form_instances(instance_ids)
        
        if success:
            print("所有表单实例删除成功")
            logging.info("所有表单实例删除成功")
        else:
            print("部分表单实例删除失败，请查看日志了解详情")
            logging.warning("部分表单实例删除失败")
        
    except Exception as e:
        print(f"程序执行错误: {str(e)}")
        logging.error(f"程序执行错误: {str(e)}", exc_info=True)
    finally:
        logging.info("="*50)
        logging.info("批量删除宜搭表单实例 - 执行结束")
        logging.info("="*50)

if __name__ == "__main__":
    main() 