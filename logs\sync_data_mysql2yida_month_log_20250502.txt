2025-05-02 00:00:04,182 - INFO - =================使用默认全量同步=============
2025-05-02 00:00:05,355 - INFO - MySQL查询成功，共获取 2903 条记录
2025-05-02 00:00:05,355 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-02 00:00:05,386 - INFO - 开始处理日期: 2025-01
2025-05-02 00:00:05,386 - INFO - Request Parameters - Page 1:
2025-05-02 00:00:05,386 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 00:00:05,386 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 00:00:06,466 - INFO - Response - Page 1:
2025-05-02 00:00:06,669 - INFO - 第 1 页获取到 100 条记录
2025-05-02 00:00:06,669 - INFO - Request Parameters - Page 2:
2025-05-02 00:00:06,669 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 00:00:06,669 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 00:00:07,201 - INFO - Response - Page 2:
2025-05-02 00:00:07,404 - INFO - 第 2 页获取到 100 条记录
2025-05-02 00:00:07,404 - INFO - Request Parameters - Page 3:
2025-05-02 00:00:07,404 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 00:00:07,404 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 00:00:07,921 - INFO - Response - Page 3:
2025-05-02 00:00:08,124 - INFO - 第 3 页获取到 100 条记录
2025-05-02 00:00:08,124 - INFO - Request Parameters - Page 4:
2025-05-02 00:00:08,124 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 00:00:08,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 00:00:08,593 - INFO - Response - Page 4:
2025-05-02 00:00:08,797 - INFO - 第 4 页获取到 100 条记录
2025-05-02 00:00:08,797 - INFO - Request Parameters - Page 5:
2025-05-02 00:00:08,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 00:00:08,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 00:00:09,297 - INFO - Response - Page 5:
2025-05-02 00:00:09,500 - INFO - 第 5 页获取到 100 条记录
2025-05-02 00:00:09,500 - INFO - Request Parameters - Page 6:
2025-05-02 00:00:09,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 00:00:09,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 00:00:10,079 - INFO - Response - Page 6:
2025-05-02 00:00:10,283 - INFO - 第 6 页获取到 100 条记录
2025-05-02 00:00:10,283 - INFO - Request Parameters - Page 7:
2025-05-02 00:00:10,283 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 00:00:10,283 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 00:00:10,736 - INFO - Response - Page 7:
2025-05-02 00:00:10,940 - INFO - 第 7 页获取到 82 条记录
2025-05-02 00:00:10,940 - INFO - 查询完成，共获取到 682 条记录
2025-05-02 00:00:10,940 - INFO - 获取到 682 条表单数据
2025-05-02 00:00:10,940 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-02 00:00:10,955 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-02 00:00:10,955 - INFO - 开始处理日期: 2025-02
2025-05-02 00:00:10,955 - INFO - Request Parameters - Page 1:
2025-05-02 00:00:10,955 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 00:00:10,955 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 00:00:11,424 - INFO - Response - Page 1:
2025-05-02 00:00:11,628 - INFO - 第 1 页获取到 100 条记录
2025-05-02 00:00:11,628 - INFO - Request Parameters - Page 2:
2025-05-02 00:00:11,628 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 00:00:11,628 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 00:00:12,128 - INFO - Response - Page 2:
2025-05-02 00:00:12,332 - INFO - 第 2 页获取到 100 条记录
2025-05-02 00:00:12,332 - INFO - Request Parameters - Page 3:
2025-05-02 00:00:12,332 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 00:00:12,332 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 00:00:12,817 - INFO - Response - Page 3:
2025-05-02 00:00:13,020 - INFO - 第 3 页获取到 100 条记录
2025-05-02 00:00:13,020 - INFO - Request Parameters - Page 4:
2025-05-02 00:00:13,020 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 00:00:13,020 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 00:00:13,505 - INFO - Response - Page 4:
2025-05-02 00:00:13,708 - INFO - 第 4 页获取到 100 条记录
2025-05-02 00:00:13,708 - INFO - Request Parameters - Page 5:
2025-05-02 00:00:13,708 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 00:00:13,708 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 00:00:14,162 - INFO - Response - Page 5:
2025-05-02 00:00:14,365 - INFO - 第 5 页获取到 100 条记录
2025-05-02 00:00:14,365 - INFO - Request Parameters - Page 6:
2025-05-02 00:00:14,365 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 00:00:14,365 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 00:00:14,835 - INFO - Response - Page 6:
2025-05-02 00:00:15,038 - INFO - 第 6 页获取到 100 条记录
2025-05-02 00:00:15,038 - INFO - Request Parameters - Page 7:
2025-05-02 00:00:15,038 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 00:00:15,038 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 00:00:15,460 - INFO - Response - Page 7:
2025-05-02 00:00:15,664 - INFO - 第 7 页获取到 70 条记录
2025-05-02 00:00:15,664 - INFO - 查询完成，共获取到 670 条记录
2025-05-02 00:00:15,664 - INFO - 获取到 670 条表单数据
2025-05-02 00:00:15,664 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-02 00:00:15,679 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-02 00:00:15,679 - INFO - 开始处理日期: 2025-03
2025-05-02 00:00:15,679 - INFO - Request Parameters - Page 1:
2025-05-02 00:00:15,679 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 00:00:15,679 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 00:00:16,180 - INFO - Response - Page 1:
2025-05-02 00:00:16,383 - INFO - 第 1 页获取到 100 条记录
2025-05-02 00:00:16,383 - INFO - Request Parameters - Page 2:
2025-05-02 00:00:16,383 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 00:00:16,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 00:00:16,852 - INFO - Response - Page 2:
2025-05-02 00:00:17,056 - INFO - 第 2 页获取到 100 条记录
2025-05-02 00:00:17,056 - INFO - Request Parameters - Page 3:
2025-05-02 00:00:17,056 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 00:00:17,056 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 00:00:17,588 - INFO - Response - Page 3:
2025-05-02 00:00:17,791 - INFO - 第 3 页获取到 100 条记录
2025-05-02 00:00:17,791 - INFO - Request Parameters - Page 4:
2025-05-02 00:00:17,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 00:00:17,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 00:00:18,245 - INFO - Response - Page 4:
2025-05-02 00:00:18,448 - INFO - 第 4 页获取到 100 条记录
2025-05-02 00:00:18,448 - INFO - Request Parameters - Page 5:
2025-05-02 00:00:18,448 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 00:00:18,448 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 00:00:19,183 - INFO - Response - Page 5:
2025-05-02 00:00:19,387 - INFO - 第 5 页获取到 100 条记录
2025-05-02 00:00:19,387 - INFO - Request Parameters - Page 6:
2025-05-02 00:00:19,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 00:00:19,387 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 00:00:19,840 - INFO - Response - Page 6:
2025-05-02 00:00:20,044 - INFO - 第 6 页获取到 100 条记录
2025-05-02 00:00:20,044 - INFO - Request Parameters - Page 7:
2025-05-02 00:00:20,044 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 00:00:20,044 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 00:00:20,466 - INFO - Response - Page 7:
2025-05-02 00:00:20,669 - INFO - 第 7 页获取到 61 条记录
2025-05-02 00:00:20,669 - INFO - 查询完成，共获取到 661 条记录
2025-05-02 00:00:20,669 - INFO - 获取到 661 条表单数据
2025-05-02 00:00:20,669 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-02 00:00:20,685 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-02 00:00:20,685 - INFO - 开始处理日期: 2025-04
2025-05-02 00:00:20,685 - INFO - Request Parameters - Page 1:
2025-05-02 00:00:20,685 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 00:00:20,685 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 00:00:21,185 - INFO - Response - Page 1:
2025-05-02 00:00:21,389 - INFO - 第 1 页获取到 100 条记录
2025-05-02 00:00:21,389 - INFO - Request Parameters - Page 2:
2025-05-02 00:00:21,389 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 00:00:21,389 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 00:00:22,030 - INFO - Response - Page 2:
2025-05-02 00:00:22,234 - INFO - 第 2 页获取到 100 条记录
2025-05-02 00:00:22,234 - INFO - Request Parameters - Page 3:
2025-05-02 00:00:22,234 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 00:00:22,234 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 00:00:22,656 - INFO - Response - Page 3:
2025-05-02 00:00:22,859 - INFO - 第 3 页获取到 100 条记录
2025-05-02 00:00:22,859 - INFO - Request Parameters - Page 4:
2025-05-02 00:00:22,859 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 00:00:22,859 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 00:00:23,344 - INFO - Response - Page 4:
2025-05-02 00:00:23,547 - INFO - 第 4 页获取到 100 条记录
2025-05-02 00:00:23,547 - INFO - Request Parameters - Page 5:
2025-05-02 00:00:23,547 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 00:00:23,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 00:00:24,032 - INFO - Response - Page 5:
2025-05-02 00:00:24,236 - INFO - 第 5 页获取到 100 条记录
2025-05-02 00:00:24,236 - INFO - Request Parameters - Page 6:
2025-05-02 00:00:24,236 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 00:00:24,236 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 00:00:24,705 - INFO - Response - Page 6:
2025-05-02 00:00:24,908 - INFO - 第 6 页获取到 100 条记录
2025-05-02 00:00:24,908 - INFO - Request Parameters - Page 7:
2025-05-02 00:00:24,908 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 00:00:24,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 00:00:25,299 - INFO - Response - Page 7:
2025-05-02 00:00:25,503 - INFO - 第 7 页获取到 27 条记录
2025-05-02 00:00:25,503 - INFO - 查询完成，共获取到 627 条记录
2025-05-02 00:00:25,503 - INFO - 获取到 627 条表单数据
2025-05-02 00:00:25,503 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-02 00:00:25,518 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MO7
2025-05-02 00:00:25,956 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9MO7
2025-05-02 00:00:25,956 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 604713.48, 'new_value': 628439.52}, {'field': 'total_amount', 'old_value': 604713.48, 'new_value': 628439.52}, {'field': 'order_count', 'old_value': 11808, 'new_value': 12231}]
2025-05-02 00:00:25,956 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MU7
2025-05-02 00:00:26,379 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MU7
2025-05-02 00:00:26,379 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 778500.0, 'new_value': 831500.0}, {'field': 'total_amount', 'old_value': 778500.0, 'new_value': 831500.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 34}]
2025-05-02 00:00:26,379 - INFO - 日期 2025-04 处理完成 - 更新: 2 条，插入: 0 条，错误: 0 条
2025-05-02 00:00:26,379 - INFO - 开始处理日期: 2025-05
2025-05-02 00:00:26,379 - INFO - Request Parameters - Page 1:
2025-05-02 00:00:26,379 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 00:00:26,379 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 00:00:26,645 - INFO - Response - Page 1:
2025-05-02 00:00:26,848 - INFO - 第 1 页获取到 5 条记录
2025-05-02 00:00:26,848 - INFO - 查询完成，共获取到 5 条记录
2025-05-02 00:00:26,848 - INFO - 获取到 5 条表单数据
2025-05-02 00:00:26,848 - INFO - 当前日期 2025-05 有 263 条MySQL数据需要处理
2025-05-02 00:00:26,848 - INFO - 开始批量插入 258 条新记录
2025-05-02 00:00:27,130 - INFO - 批量插入响应状态码: 200
2025-05-02 00:00:27,130 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 01 May 2025 16:00:23 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '********-6C9F-73B7-A14B-54A76021BE00', 'x-acs-trace-id': '9ed9bee78f33fa2cb9daf20b57f0af3d', 'etag': '4ySe4ZZLYQPrm1LKUP71bRA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-02 00:00:27,130 - INFO - 批量插入响应体: {'result': ['FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9C', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMEC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMKC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMLC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMMC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMOC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMSC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMWC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMYC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM1D', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMMD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMFE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMME', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F']}
2025-05-02 00:00:27,130 - INFO - 批量插入表单数据成功，批次 1，共 100 条记录
2025-05-02 00:00:27,130 - INFO - 成功插入的数据ID: ['FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9C', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMEC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMKC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMLC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMMC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMOC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMSC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMWC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMYC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM1D', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMMD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMFE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMME', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE', 'FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F']
2025-05-02 00:00:30,415 - INFO - 批量插入响应状态码: 200
2025-05-02 00:00:30,415 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 01 May 2025 16:00:26 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2323217B-B5EC-75CA-A194-FD5CE6589AF1', 'x-acs-trace-id': 'a6fad056766f791aeefc7f0c88a2baa9', 'etag': '4kLtQLZvlCXUpsWZ26vosYQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-02 00:00:30,415 - INFO - 批量插入响应体: {'result': ['FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPK', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0L', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1L', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4L', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9L', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMCL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5M', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6M', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7M', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8M', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMCM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8N', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN']}
2025-05-02 00:00:30,415 - INFO - 批量插入表单数据成功，批次 2，共 100 条记录
2025-05-02 00:00:30,415 - INFO - 成功插入的数据ID: ['FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPK', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0L', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1L', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4L', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9L', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMCL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5M', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6M', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7M', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8M', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMCM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZM', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8N', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N', 'FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN']
2025-05-02 00:00:33,637 - INFO - 批量插入响应状态码: 200
2025-05-02 00:00:33,637 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 01 May 2025 16:00:29 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2796', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4AE3EB2F-5150-7AC0-AA84-9A23F9AAAD1F', 'x-acs-trace-id': 'e44ac7449f582c648b1397d7a8d1a253', 'etag': '24ClR5KNCSWtUBoshOrBHxg6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-02 00:00:33,637 - INFO - 批量插入响应体: {'result': ['FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4E', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM9E', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMAE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMCE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMEE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMJE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMME', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1F', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6F', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F']}
2025-05-02 00:00:33,653 - INFO - 批量插入表单数据成功，批次 3，共 58 条记录
2025-05-02 00:00:33,653 - INFO - 成功插入的数据ID: ['FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4E', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM9E', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMAE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMCE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMEE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMJE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMME', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1F', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6F', 'FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F']
2025-05-02 00:00:36,672 - INFO - 批量插入完成，共 258 条记录
2025-05-02 00:00:36,672 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 258 条，错误: 0 条
2025-05-02 00:00:36,672 - INFO - 数据同步完成！更新: 2 条，插入: 258 条，错误: 0 条
2025-05-02 00:00:36,672 - INFO - =================同步完成====================
2025-05-02 03:00:04,211 - INFO - =================使用默认全量同步=============
2025-05-02 03:00:05,368 - INFO - MySQL查询成功，共获取 3003 条记录
2025-05-02 03:00:05,368 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-02 03:00:05,400 - INFO - 开始处理日期: 2025-01
2025-05-02 03:00:05,400 - INFO - Request Parameters - Page 1:
2025-05-02 03:00:05,400 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 03:00:05,400 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 03:00:06,291 - INFO - Response - Page 1:
2025-05-02 03:00:06,495 - INFO - 第 1 页获取到 100 条记录
2025-05-02 03:00:06,495 - INFO - Request Parameters - Page 2:
2025-05-02 03:00:06,495 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 03:00:06,495 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 03:00:07,073 - INFO - Response - Page 2:
2025-05-02 03:00:07,277 - INFO - 第 2 页获取到 100 条记录
2025-05-02 03:00:07,277 - INFO - Request Parameters - Page 3:
2025-05-02 03:00:07,277 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 03:00:07,277 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 03:00:07,762 - INFO - Response - Page 3:
2025-05-02 03:00:07,965 - INFO - 第 3 页获取到 100 条记录
2025-05-02 03:00:07,965 - INFO - Request Parameters - Page 4:
2025-05-02 03:00:07,965 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 03:00:07,965 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 03:00:08,450 - INFO - Response - Page 4:
2025-05-02 03:00:08,653 - INFO - 第 4 页获取到 100 条记录
2025-05-02 03:00:08,653 - INFO - Request Parameters - Page 5:
2025-05-02 03:00:08,653 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 03:00:08,653 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 03:00:09,216 - INFO - Response - Page 5:
2025-05-02 03:00:09,420 - INFO - 第 5 页获取到 100 条记录
2025-05-02 03:00:09,420 - INFO - Request Parameters - Page 6:
2025-05-02 03:00:09,420 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 03:00:09,420 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 03:00:09,905 - INFO - Response - Page 6:
2025-05-02 03:00:10,108 - INFO - 第 6 页获取到 100 条记录
2025-05-02 03:00:10,108 - INFO - Request Parameters - Page 7:
2025-05-02 03:00:10,108 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 03:00:10,108 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 03:00:10,577 - INFO - Response - Page 7:
2025-05-02 03:00:10,781 - INFO - 第 7 页获取到 82 条记录
2025-05-02 03:00:10,781 - INFO - 查询完成，共获取到 682 条记录
2025-05-02 03:00:10,781 - INFO - 获取到 682 条表单数据
2025-05-02 03:00:10,781 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-02 03:00:10,796 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-02 03:00:10,796 - INFO - 开始处理日期: 2025-02
2025-05-02 03:00:10,796 - INFO - Request Parameters - Page 1:
2025-05-02 03:00:10,796 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 03:00:10,796 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 03:00:11,250 - INFO - Response - Page 1:
2025-05-02 03:00:11,453 - INFO - 第 1 页获取到 100 条记录
2025-05-02 03:00:11,453 - INFO - Request Parameters - Page 2:
2025-05-02 03:00:11,453 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 03:00:11,453 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 03:00:11,922 - INFO - Response - Page 2:
2025-05-02 03:00:12,126 - INFO - 第 2 页获取到 100 条记录
2025-05-02 03:00:12,126 - INFO - Request Parameters - Page 3:
2025-05-02 03:00:12,126 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 03:00:12,126 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 03:00:12,595 - INFO - Response - Page 3:
2025-05-02 03:00:12,798 - INFO - 第 3 页获取到 100 条记录
2025-05-02 03:00:12,798 - INFO - Request Parameters - Page 4:
2025-05-02 03:00:12,798 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 03:00:12,798 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 03:00:13,346 - INFO - Response - Page 4:
2025-05-02 03:00:13,549 - INFO - 第 4 页获取到 100 条记录
2025-05-02 03:00:13,549 - INFO - Request Parameters - Page 5:
2025-05-02 03:00:13,549 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 03:00:13,549 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 03:00:14,097 - INFO - Response - Page 5:
2025-05-02 03:00:14,300 - INFO - 第 5 页获取到 100 条记录
2025-05-02 03:00:14,300 - INFO - Request Parameters - Page 6:
2025-05-02 03:00:14,300 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 03:00:14,300 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 03:00:14,879 - INFO - Response - Page 6:
2025-05-02 03:00:15,082 - INFO - 第 6 页获取到 100 条记录
2025-05-02 03:00:15,082 - INFO - Request Parameters - Page 7:
2025-05-02 03:00:15,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 03:00:15,082 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 03:00:15,489 - INFO - Response - Page 7:
2025-05-02 03:00:15,692 - INFO - 第 7 页获取到 70 条记录
2025-05-02 03:00:15,692 - INFO - 查询完成，共获取到 670 条记录
2025-05-02 03:00:15,692 - INFO - 获取到 670 条表单数据
2025-05-02 03:00:15,692 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-02 03:00:15,708 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-02 03:00:15,708 - INFO - 开始处理日期: 2025-03
2025-05-02 03:00:15,708 - INFO - Request Parameters - Page 1:
2025-05-02 03:00:15,708 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 03:00:15,708 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 03:00:16,177 - INFO - Response - Page 1:
2025-05-02 03:00:16,381 - INFO - 第 1 页获取到 100 条记录
2025-05-02 03:00:16,381 - INFO - Request Parameters - Page 2:
2025-05-02 03:00:16,381 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 03:00:16,381 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 03:00:16,850 - INFO - Response - Page 2:
2025-05-02 03:00:17,053 - INFO - 第 2 页获取到 100 条记录
2025-05-02 03:00:17,053 - INFO - Request Parameters - Page 3:
2025-05-02 03:00:17,053 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 03:00:17,053 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 03:00:17,554 - INFO - Response - Page 3:
2025-05-02 03:00:17,773 - INFO - 第 3 页获取到 100 条记录
2025-05-02 03:00:17,773 - INFO - Request Parameters - Page 4:
2025-05-02 03:00:17,773 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 03:00:17,773 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 03:00:18,273 - INFO - Response - Page 4:
2025-05-02 03:00:18,477 - INFO - 第 4 页获取到 100 条记录
2025-05-02 03:00:18,477 - INFO - Request Parameters - Page 5:
2025-05-02 03:00:18,477 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 03:00:18,477 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 03:00:18,962 - INFO - Response - Page 5:
2025-05-02 03:00:19,165 - INFO - 第 5 页获取到 100 条记录
2025-05-02 03:00:19,165 - INFO - Request Parameters - Page 6:
2025-05-02 03:00:19,165 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 03:00:19,165 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 03:00:19,697 - INFO - Response - Page 6:
2025-05-02 03:00:19,900 - INFO - 第 6 页获取到 100 条记录
2025-05-02 03:00:19,900 - INFO - Request Parameters - Page 7:
2025-05-02 03:00:19,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 03:00:19,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 03:00:20,323 - INFO - Response - Page 7:
2025-05-02 03:00:20,526 - INFO - 第 7 页获取到 61 条记录
2025-05-02 03:00:20,526 - INFO - 查询完成，共获取到 661 条记录
2025-05-02 03:00:20,526 - INFO - 获取到 661 条表单数据
2025-05-02 03:00:20,526 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-02 03:00:20,542 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-02 03:00:20,542 - INFO - 开始处理日期: 2025-04
2025-05-02 03:00:20,542 - INFO - Request Parameters - Page 1:
2025-05-02 03:00:20,542 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 03:00:20,542 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 03:00:21,011 - INFO - Response - Page 1:
2025-05-02 03:00:21,214 - INFO - 第 1 页获取到 100 条记录
2025-05-02 03:00:21,214 - INFO - Request Parameters - Page 2:
2025-05-02 03:00:21,214 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 03:00:21,214 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 03:00:21,699 - INFO - Response - Page 2:
2025-05-02 03:00:21,902 - INFO - 第 2 页获取到 100 条记录
2025-05-02 03:00:21,902 - INFO - Request Parameters - Page 3:
2025-05-02 03:00:21,902 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 03:00:21,902 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 03:00:22,450 - INFO - Response - Page 3:
2025-05-02 03:00:22,653 - INFO - 第 3 页获取到 100 条记录
2025-05-02 03:00:22,653 - INFO - Request Parameters - Page 4:
2025-05-02 03:00:22,653 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 03:00:22,653 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 03:00:23,123 - INFO - Response - Page 4:
2025-05-02 03:00:23,326 - INFO - 第 4 页获取到 100 条记录
2025-05-02 03:00:23,326 - INFO - Request Parameters - Page 5:
2025-05-02 03:00:23,326 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 03:00:23,326 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 03:00:23,811 - INFO - Response - Page 5:
2025-05-02 03:00:24,014 - INFO - 第 5 页获取到 100 条记录
2025-05-02 03:00:24,014 - INFO - Request Parameters - Page 6:
2025-05-02 03:00:24,014 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 03:00:24,014 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 03:00:24,562 - INFO - Response - Page 6:
2025-05-02 03:00:24,765 - INFO - 第 6 页获取到 100 条记录
2025-05-02 03:00:24,765 - INFO - Request Parameters - Page 7:
2025-05-02 03:00:24,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 03:00:24,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 03:00:25,125 - INFO - Response - Page 7:
2025-05-02 03:00:25,328 - INFO - 第 7 页获取到 27 条记录
2025-05-02 03:00:25,328 - INFO - 查询完成，共获取到 627 条记录
2025-05-02 03:00:25,328 - INFO - 获取到 627 条表单数据
2025-05-02 03:00:25,328 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-02 03:00:25,344 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-02 03:00:25,344 - INFO - 开始处理日期: 2025-05
2025-05-02 03:00:25,344 - INFO - Request Parameters - Page 1:
2025-05-02 03:00:25,344 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 03:00:25,344 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 03:00:25,813 - INFO - Response - Page 1:
2025-05-02 03:00:26,016 - INFO - 第 1 页获取到 100 条记录
2025-05-02 03:00:26,016 - INFO - Request Parameters - Page 2:
2025-05-02 03:00:26,016 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 03:00:26,016 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 03:00:26,501 - INFO - Response - Page 2:
2025-05-02 03:00:26,705 - INFO - 第 2 页获取到 100 条记录
2025-05-02 03:00:26,705 - INFO - Request Parameters - Page 3:
2025-05-02 03:00:26,705 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 03:00:26,705 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 03:00:27,284 - INFO - Response - Page 3:
2025-05-02 03:00:27,487 - INFO - 第 3 页获取到 63 条记录
2025-05-02 03:00:27,487 - INFO - 查询完成，共获取到 263 条记录
2025-05-02 03:00:27,487 - INFO - 获取到 263 条表单数据
2025-05-02 03:00:27,487 - INFO - 当前日期 2025-05 有 363 条MySQL数据需要处理
2025-05-02 03:00:27,487 - INFO - 开始批量插入 100 条新记录
2025-05-02 03:00:27,753 - INFO - 批量插入响应状态码: 200
2025-05-02 03:00:27,768 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 01 May 2025 19:00:11 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4C156150-1103-753F-B37A-CAEE7460E073', 'x-acs-trace-id': '8bcfcf000b260f69f3ec05c6f3233555', 'etag': '4U8kgl3tPwsQtCOozqt0b/g2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-02 03:00:27,768 - INFO - 批量插入响应体: {'result': ['FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVT', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1U', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0V', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2V', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3V', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW']}
2025-05-02 03:00:27,768 - INFO - 批量插入表单数据成功，批次 1，共 100 条记录
2025-05-02 03:00:27,768 - INFO - 成功插入的数据ID: ['FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVT', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1U', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0V', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2V', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3V', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW', 'FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW']
2025-05-02 03:00:30,787 - INFO - 批量插入完成，共 100 条记录
2025-05-02 03:00:30,787 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 100 条，错误: 0 条
2025-05-02 03:00:30,787 - INFO - 数据同步完成！更新: 0 条，插入: 100 条，错误: 0 条
2025-05-02 03:00:30,787 - INFO - =================同步完成====================
2025-05-02 06:00:01,486 - INFO - =================使用默认全量同步=============
2025-05-02 06:00:02,660 - INFO - MySQL查询成功，共获取 3011 条记录
2025-05-02 06:00:02,660 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-02 06:00:02,675 - INFO - 开始处理日期: 2025-01
2025-05-02 06:00:02,675 - INFO - Request Parameters - Page 1:
2025-05-02 06:00:02,675 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 06:00:02,691 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 06:00:03,676 - INFO - Response - Page 1:
2025-05-02 06:00:03,880 - INFO - 第 1 页获取到 100 条记录
2025-05-02 06:00:03,880 - INFO - Request Parameters - Page 2:
2025-05-02 06:00:03,880 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 06:00:03,880 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 06:00:04,380 - INFO - Response - Page 2:
2025-05-02 06:00:04,584 - INFO - 第 2 页获取到 100 条记录
2025-05-02 06:00:04,584 - INFO - Request Parameters - Page 3:
2025-05-02 06:00:04,584 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 06:00:04,584 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 06:00:05,256 - INFO - Response - Page 3:
2025-05-02 06:00:05,460 - INFO - 第 3 页获取到 100 条记录
2025-05-02 06:00:05,460 - INFO - Request Parameters - Page 4:
2025-05-02 06:00:05,460 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 06:00:05,460 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 06:00:05,976 - INFO - Response - Page 4:
2025-05-02 06:00:06,179 - INFO - 第 4 页获取到 100 条记录
2025-05-02 06:00:06,179 - INFO - Request Parameters - Page 5:
2025-05-02 06:00:06,179 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 06:00:06,179 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 06:00:06,633 - INFO - Response - Page 5:
2025-05-02 06:00:06,836 - INFO - 第 5 页获取到 100 条记录
2025-05-02 06:00:06,836 - INFO - Request Parameters - Page 6:
2025-05-02 06:00:06,836 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 06:00:06,836 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 06:00:07,368 - INFO - Response - Page 6:
2025-05-02 06:00:07,571 - INFO - 第 6 页获取到 100 条记录
2025-05-02 06:00:07,571 - INFO - Request Parameters - Page 7:
2025-05-02 06:00:07,571 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 06:00:07,571 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 06:00:08,056 - INFO - Response - Page 7:
2025-05-02 06:00:08,275 - INFO - 第 7 页获取到 82 条记录
2025-05-02 06:00:08,275 - INFO - 查询完成，共获取到 682 条记录
2025-05-02 06:00:08,275 - INFO - 获取到 682 条表单数据
2025-05-02 06:00:08,275 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-02 06:00:08,291 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-02 06:00:08,291 - INFO - 开始处理日期: 2025-02
2025-05-02 06:00:08,291 - INFO - Request Parameters - Page 1:
2025-05-02 06:00:08,291 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 06:00:08,291 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 06:00:08,776 - INFO - Response - Page 1:
2025-05-02 06:00:08,979 - INFO - 第 1 页获取到 100 条记录
2025-05-02 06:00:08,979 - INFO - Request Parameters - Page 2:
2025-05-02 06:00:08,979 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 06:00:08,979 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 06:00:09,495 - INFO - Response - Page 2:
2025-05-02 06:00:09,699 - INFO - 第 2 页获取到 100 条记录
2025-05-02 06:00:09,699 - INFO - Request Parameters - Page 3:
2025-05-02 06:00:09,699 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 06:00:09,699 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 06:00:10,246 - INFO - Response - Page 3:
2025-05-02 06:00:10,450 - INFO - 第 3 页获取到 100 条记录
2025-05-02 06:00:10,450 - INFO - Request Parameters - Page 4:
2025-05-02 06:00:10,450 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 06:00:10,450 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 06:00:10,903 - INFO - Response - Page 4:
2025-05-02 06:00:11,107 - INFO - 第 4 页获取到 100 条记录
2025-05-02 06:00:11,107 - INFO - Request Parameters - Page 5:
2025-05-02 06:00:11,107 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 06:00:11,107 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 06:00:11,560 - INFO - Response - Page 5:
2025-05-02 06:00:11,764 - INFO - 第 5 页获取到 100 条记录
2025-05-02 06:00:11,764 - INFO - Request Parameters - Page 6:
2025-05-02 06:00:11,764 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 06:00:11,764 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 06:00:12,327 - INFO - Response - Page 6:
2025-05-02 06:00:12,530 - INFO - 第 6 页获取到 100 条记录
2025-05-02 06:00:12,530 - INFO - Request Parameters - Page 7:
2025-05-02 06:00:12,530 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 06:00:12,530 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 06:00:12,984 - INFO - Response - Page 7:
2025-05-02 06:00:13,187 - INFO - 第 7 页获取到 70 条记录
2025-05-02 06:00:13,187 - INFO - 查询完成，共获取到 670 条记录
2025-05-02 06:00:13,187 - INFO - 获取到 670 条表单数据
2025-05-02 06:00:13,187 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-02 06:00:13,203 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-02 06:00:13,203 - INFO - 开始处理日期: 2025-03
2025-05-02 06:00:13,203 - INFO - Request Parameters - Page 1:
2025-05-02 06:00:13,203 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 06:00:13,203 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 06:00:13,672 - INFO - Response - Page 1:
2025-05-02 06:00:13,875 - INFO - 第 1 页获取到 100 条记录
2025-05-02 06:00:13,875 - INFO - Request Parameters - Page 2:
2025-05-02 06:00:13,875 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 06:00:13,875 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 06:00:14,376 - INFO - Response - Page 2:
2025-05-02 06:00:14,579 - INFO - 第 2 页获取到 100 条记录
2025-05-02 06:00:14,579 - INFO - Request Parameters - Page 3:
2025-05-02 06:00:14,579 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 06:00:14,579 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 06:00:15,080 - INFO - Response - Page 3:
2025-05-02 06:00:15,283 - INFO - 第 3 页获取到 100 条记录
2025-05-02 06:00:15,283 - INFO - Request Parameters - Page 4:
2025-05-02 06:00:15,283 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 06:00:15,283 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 06:00:15,752 - INFO - Response - Page 4:
2025-05-02 06:00:15,956 - INFO - 第 4 页获取到 100 条记录
2025-05-02 06:00:15,956 - INFO - Request Parameters - Page 5:
2025-05-02 06:00:15,956 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 06:00:15,956 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 06:00:16,441 - INFO - Response - Page 5:
2025-05-02 06:00:16,644 - INFO - 第 5 页获取到 100 条记录
2025-05-02 06:00:16,644 - INFO - Request Parameters - Page 6:
2025-05-02 06:00:16,644 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 06:00:16,644 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 06:00:17,160 - INFO - Response - Page 6:
2025-05-02 06:00:17,364 - INFO - 第 6 页获取到 100 条记录
2025-05-02 06:00:17,364 - INFO - Request Parameters - Page 7:
2025-05-02 06:00:17,364 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 06:00:17,364 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 06:00:17,802 - INFO - Response - Page 7:
2025-05-02 06:00:18,005 - INFO - 第 7 页获取到 61 条记录
2025-05-02 06:00:18,005 - INFO - 查询完成，共获取到 661 条记录
2025-05-02 06:00:18,005 - INFO - 获取到 661 条表单数据
2025-05-02 06:00:18,005 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-02 06:00:18,021 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-02 06:00:18,021 - INFO - 开始处理日期: 2025-04
2025-05-02 06:00:18,021 - INFO - Request Parameters - Page 1:
2025-05-02 06:00:18,021 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 06:00:18,021 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 06:00:18,490 - INFO - Response - Page 1:
2025-05-02 06:00:18,693 - INFO - 第 1 页获取到 100 条记录
2025-05-02 06:00:18,693 - INFO - Request Parameters - Page 2:
2025-05-02 06:00:18,693 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 06:00:18,693 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 06:00:19,209 - INFO - Response - Page 2:
2025-05-02 06:00:19,413 - INFO - 第 2 页获取到 100 条记录
2025-05-02 06:00:19,413 - INFO - Request Parameters - Page 3:
2025-05-02 06:00:19,413 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 06:00:19,413 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 06:00:19,960 - INFO - Response - Page 3:
2025-05-02 06:00:20,164 - INFO - 第 3 页获取到 100 条记录
2025-05-02 06:00:20,164 - INFO - Request Parameters - Page 4:
2025-05-02 06:00:20,164 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 06:00:20,164 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 06:00:20,633 - INFO - Response - Page 4:
2025-05-02 06:00:20,836 - INFO - 第 4 页获取到 100 条记录
2025-05-02 06:00:20,836 - INFO - Request Parameters - Page 5:
2025-05-02 06:00:20,836 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 06:00:20,836 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 06:00:21,321 - INFO - Response - Page 5:
2025-05-02 06:00:21,525 - INFO - 第 5 页获取到 100 条记录
2025-05-02 06:00:21,525 - INFO - Request Parameters - Page 6:
2025-05-02 06:00:21,525 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 06:00:21,525 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 06:00:21,994 - INFO - Response - Page 6:
2025-05-02 06:00:22,197 - INFO - 第 6 页获取到 100 条记录
2025-05-02 06:00:22,197 - INFO - Request Parameters - Page 7:
2025-05-02 06:00:22,197 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 06:00:22,197 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 06:00:22,526 - INFO - Response - Page 7:
2025-05-02 06:00:22,729 - INFO - 第 7 页获取到 27 条记录
2025-05-02 06:00:22,729 - INFO - 查询完成，共获取到 627 条记录
2025-05-02 06:00:22,729 - INFO - 获取到 627 条表单数据
2025-05-02 06:00:22,729 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-02 06:00:22,745 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-02 06:00:22,745 - INFO - 开始处理日期: 2025-05
2025-05-02 06:00:22,745 - INFO - Request Parameters - Page 1:
2025-05-02 06:00:22,745 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 06:00:22,745 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 06:00:23,230 - INFO - Response - Page 1:
2025-05-02 06:00:23,433 - INFO - 第 1 页获取到 100 条记录
2025-05-02 06:00:23,433 - INFO - Request Parameters - Page 2:
2025-05-02 06:00:23,433 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 06:00:23,433 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 06:00:23,918 - INFO - Response - Page 2:
2025-05-02 06:00:24,121 - INFO - 第 2 页获取到 100 条记录
2025-05-02 06:00:24,121 - INFO - Request Parameters - Page 3:
2025-05-02 06:00:24,121 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 06:00:24,121 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 06:00:24,559 - INFO - Response - Page 3:
2025-05-02 06:00:24,763 - INFO - 第 3 页获取到 100 条记录
2025-05-02 06:00:24,763 - INFO - Request Parameters - Page 4:
2025-05-02 06:00:24,763 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 06:00:24,763 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 06:00:25,169 - INFO - Response - Page 4:
2025-05-02 06:00:25,373 - INFO - 第 4 页获取到 63 条记录
2025-05-02 06:00:25,373 - INFO - 查询完成，共获取到 363 条记录
2025-05-02 06:00:25,373 - INFO - 获取到 363 条表单数据
2025-05-02 06:00:25,373 - INFO - 当前日期 2025-05 有 371 条MySQL数据需要处理
2025-05-02 06:00:25,373 - INFO - 开始批量插入 8 条新记录
2025-05-02 06:00:25,560 - INFO - 批量插入响应状态码: 200
2025-05-02 06:00:25,560 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 01 May 2025 21:59:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '396', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'CFED61EA-F223-7808-9A1D-C56FAF601568', 'x-acs-trace-id': 'ecbcbf16de2e0f647a2a88f56e61d596', 'etag': '3aSUgIrUmulCCnw6IhsaFgg6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-02 06:00:25,560 - INFO - 批量插入响应体: {'result': ['FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMOC', 'FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC', 'FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC', 'FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC', 'FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC', 'FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC', 'FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC', 'FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC']}
2025-05-02 06:00:25,560 - INFO - 批量插入表单数据成功，批次 1，共 8 条记录
2025-05-02 06:00:25,560 - INFO - 成功插入的数据ID: ['FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMOC', 'FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC', 'FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC', 'FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC', 'FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC', 'FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC', 'FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC', 'FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC']
2025-05-02 06:00:28,579 - INFO - 批量插入完成，共 8 条记录
2025-05-02 06:00:28,579 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 8 条，错误: 0 条
2025-05-02 06:00:28,579 - INFO - 数据同步完成！更新: 0 条，插入: 8 条，错误: 0 条
2025-05-02 06:00:28,579 - INFO - =================同步完成====================
2025-05-02 09:00:01,869 - INFO - =================使用默认全量同步=============
2025-05-02 09:00:03,056 - INFO - MySQL查询成功，共获取 3124 条记录
2025-05-02 09:00:03,056 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-02 09:00:03,072 - INFO - 开始处理日期: 2025-01
2025-05-02 09:00:03,088 - INFO - Request Parameters - Page 1:
2025-05-02 09:00:03,088 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 09:00:03,088 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 09:00:04,025 - INFO - Response - Page 1:
2025-05-02 09:00:04,228 - INFO - 第 1 页获取到 100 条记录
2025-05-02 09:00:04,228 - INFO - Request Parameters - Page 2:
2025-05-02 09:00:04,228 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 09:00:04,228 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 09:00:04,650 - INFO - Response - Page 2:
2025-05-02 09:00:04,853 - INFO - 第 2 页获取到 100 条记录
2025-05-02 09:00:04,853 - INFO - Request Parameters - Page 3:
2025-05-02 09:00:04,853 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 09:00:04,853 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 09:00:05,463 - INFO - Response - Page 3:
2025-05-02 09:00:05,666 - INFO - 第 3 页获取到 100 条记录
2025-05-02 09:00:05,666 - INFO - Request Parameters - Page 4:
2025-05-02 09:00:05,666 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 09:00:05,666 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 09:00:06,119 - INFO - Response - Page 4:
2025-05-02 09:00:06,322 - INFO - 第 4 页获取到 100 条记录
2025-05-02 09:00:06,322 - INFO - Request Parameters - Page 5:
2025-05-02 09:00:06,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 09:00:06,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 09:00:06,744 - INFO - Response - Page 5:
2025-05-02 09:00:06,947 - INFO - 第 5 页获取到 100 条记录
2025-05-02 09:00:06,947 - INFO - Request Parameters - Page 6:
2025-05-02 09:00:06,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 09:00:06,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 09:00:07,416 - INFO - Response - Page 6:
2025-05-02 09:00:07,619 - INFO - 第 6 页获取到 100 条记录
2025-05-02 09:00:07,619 - INFO - Request Parameters - Page 7:
2025-05-02 09:00:07,619 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 09:00:07,619 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 09:00:08,041 - INFO - Response - Page 7:
2025-05-02 09:00:08,244 - INFO - 第 7 页获取到 82 条记录
2025-05-02 09:00:08,244 - INFO - 查询完成，共获取到 682 条记录
2025-05-02 09:00:08,244 - INFO - 获取到 682 条表单数据
2025-05-02 09:00:08,244 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-02 09:00:08,260 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-02 09:00:08,260 - INFO - 开始处理日期: 2025-02
2025-05-02 09:00:08,260 - INFO - Request Parameters - Page 1:
2025-05-02 09:00:08,260 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 09:00:08,260 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 09:00:08,713 - INFO - Response - Page 1:
2025-05-02 09:00:08,916 - INFO - 第 1 页获取到 100 条记录
2025-05-02 09:00:08,916 - INFO - Request Parameters - Page 2:
2025-05-02 09:00:08,916 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 09:00:08,916 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 09:00:09,385 - INFO - Response - Page 2:
2025-05-02 09:00:09,588 - INFO - 第 2 页获取到 100 条记录
2025-05-02 09:00:09,588 - INFO - Request Parameters - Page 3:
2025-05-02 09:00:09,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 09:00:09,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 09:00:10,025 - INFO - Response - Page 3:
2025-05-02 09:00:10,228 - INFO - 第 3 页获取到 100 条记录
2025-05-02 09:00:10,228 - INFO - Request Parameters - Page 4:
2025-05-02 09:00:10,228 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 09:00:10,228 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 09:00:10,744 - INFO - Response - Page 4:
2025-05-02 09:00:10,947 - INFO - 第 4 页获取到 100 条记录
2025-05-02 09:00:10,947 - INFO - Request Parameters - Page 5:
2025-05-02 09:00:10,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 09:00:10,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 09:00:11,400 - INFO - Response - Page 5:
2025-05-02 09:00:11,603 - INFO - 第 5 页获取到 100 条记录
2025-05-02 09:00:11,603 - INFO - Request Parameters - Page 6:
2025-05-02 09:00:11,603 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 09:00:11,603 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 09:00:12,103 - INFO - Response - Page 6:
2025-05-02 09:00:12,306 - INFO - 第 6 页获取到 100 条记录
2025-05-02 09:00:12,306 - INFO - Request Parameters - Page 7:
2025-05-02 09:00:12,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 09:00:12,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 09:00:12,744 - INFO - Response - Page 7:
2025-05-02 09:00:12,947 - INFO - 第 7 页获取到 70 条记录
2025-05-02 09:00:12,947 - INFO - 查询完成，共获取到 670 条记录
2025-05-02 09:00:12,947 - INFO - 获取到 670 条表单数据
2025-05-02 09:00:12,947 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-02 09:00:12,963 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-02 09:00:12,963 - INFO - 开始处理日期: 2025-03
2025-05-02 09:00:12,963 - INFO - Request Parameters - Page 1:
2025-05-02 09:00:12,963 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 09:00:12,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 09:00:13,447 - INFO - Response - Page 1:
2025-05-02 09:00:13,650 - INFO - 第 1 页获取到 100 条记录
2025-05-02 09:00:13,650 - INFO - Request Parameters - Page 2:
2025-05-02 09:00:13,650 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 09:00:13,650 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 09:00:14,134 - INFO - Response - Page 2:
2025-05-02 09:00:14,338 - INFO - 第 2 页获取到 100 条记录
2025-05-02 09:00:14,338 - INFO - Request Parameters - Page 3:
2025-05-02 09:00:14,338 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 09:00:14,338 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 09:00:14,884 - INFO - Response - Page 3:
2025-05-02 09:00:15,088 - INFO - 第 3 页获取到 100 条记录
2025-05-02 09:00:15,088 - INFO - Request Parameters - Page 4:
2025-05-02 09:00:15,088 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 09:00:15,088 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 09:00:15,541 - INFO - Response - Page 4:
2025-05-02 09:00:15,744 - INFO - 第 4 页获取到 100 条记录
2025-05-02 09:00:15,744 - INFO - Request Parameters - Page 5:
2025-05-02 09:00:15,744 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 09:00:15,744 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 09:00:16,228 - INFO - Response - Page 5:
2025-05-02 09:00:16,431 - INFO - 第 5 页获取到 100 条记录
2025-05-02 09:00:16,431 - INFO - Request Parameters - Page 6:
2025-05-02 09:00:16,431 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 09:00:16,431 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 09:00:16,869 - INFO - Response - Page 6:
2025-05-02 09:00:17,072 - INFO - 第 6 页获取到 100 条记录
2025-05-02 09:00:17,072 - INFO - Request Parameters - Page 7:
2025-05-02 09:00:17,072 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 09:00:17,072 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 09:00:17,509 - INFO - Response - Page 7:
2025-05-02 09:00:17,713 - INFO - 第 7 页获取到 61 条记录
2025-05-02 09:00:17,713 - INFO - 查询完成，共获取到 661 条记录
2025-05-02 09:00:17,713 - INFO - 获取到 661 条表单数据
2025-05-02 09:00:17,713 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-02 09:00:17,728 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-02 09:00:17,728 - INFO - 开始处理日期: 2025-04
2025-05-02 09:00:17,728 - INFO - Request Parameters - Page 1:
2025-05-02 09:00:17,728 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 09:00:17,728 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 09:00:18,228 - INFO - Response - Page 1:
2025-05-02 09:00:18,431 - INFO - 第 1 页获取到 100 条记录
2025-05-02 09:00:18,431 - INFO - Request Parameters - Page 2:
2025-05-02 09:00:18,431 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 09:00:18,431 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 09:00:18,884 - INFO - Response - Page 2:
2025-05-02 09:00:19,088 - INFO - 第 2 页获取到 100 条记录
2025-05-02 09:00:19,088 - INFO - Request Parameters - Page 3:
2025-05-02 09:00:19,088 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 09:00:19,088 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 09:00:19,588 - INFO - Response - Page 3:
2025-05-02 09:00:19,791 - INFO - 第 3 页获取到 100 条记录
2025-05-02 09:00:19,791 - INFO - Request Parameters - Page 4:
2025-05-02 09:00:19,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 09:00:19,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 09:00:20,259 - INFO - Response - Page 4:
2025-05-02 09:00:20,463 - INFO - 第 4 页获取到 100 条记录
2025-05-02 09:00:20,463 - INFO - Request Parameters - Page 5:
2025-05-02 09:00:20,463 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 09:00:20,463 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 09:00:20,900 - INFO - Response - Page 5:
2025-05-02 09:00:21,103 - INFO - 第 5 页获取到 100 条记录
2025-05-02 09:00:21,103 - INFO - Request Parameters - Page 6:
2025-05-02 09:00:21,103 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 09:00:21,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 09:00:21,572 - INFO - Response - Page 6:
2025-05-02 09:00:21,775 - INFO - 第 6 页获取到 100 条记录
2025-05-02 09:00:21,775 - INFO - Request Parameters - Page 7:
2025-05-02 09:00:21,775 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 09:00:21,775 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 09:00:22,103 - INFO - Response - Page 7:
2025-05-02 09:00:22,306 - INFO - 第 7 页获取到 27 条记录
2025-05-02 09:00:22,306 - INFO - 查询完成，共获取到 627 条记录
2025-05-02 09:00:22,306 - INFO - 获取到 627 条表单数据
2025-05-02 09:00:22,306 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-02 09:00:22,322 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-02 09:00:22,322 - INFO - 开始处理日期: 2025-05
2025-05-02 09:00:22,322 - INFO - Request Parameters - Page 1:
2025-05-02 09:00:22,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 09:00:22,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 09:00:22,822 - INFO - Response - Page 1:
2025-05-02 09:00:23,025 - INFO - 第 1 页获取到 100 条记录
2025-05-02 09:00:23,025 - INFO - Request Parameters - Page 2:
2025-05-02 09:00:23,025 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 09:00:23,025 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 09:00:23,478 - INFO - Response - Page 2:
2025-05-02 09:00:23,681 - INFO - 第 2 页获取到 100 条记录
2025-05-02 09:00:23,681 - INFO - Request Parameters - Page 3:
2025-05-02 09:00:23,681 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 09:00:23,681 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 09:00:24,228 - INFO - Response - Page 3:
2025-05-02 09:00:24,431 - INFO - 第 3 页获取到 100 条记录
2025-05-02 09:00:24,431 - INFO - Request Parameters - Page 4:
2025-05-02 09:00:24,431 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 09:00:24,431 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 09:00:24,853 - INFO - Response - Page 4:
2025-05-02 09:00:25,056 - INFO - 第 4 页获取到 71 条记录
2025-05-02 09:00:25,056 - INFO - 查询完成，共获取到 371 条记录
2025-05-02 09:00:25,056 - INFO - 获取到 371 条表单数据
2025-05-02 09:00:25,056 - INFO - 当前日期 2025-05 有 484 条MySQL数据需要处理
2025-05-02 09:00:25,072 - INFO - 开始批量插入 113 条新记录
2025-05-02 09:00:25,369 - INFO - 批量插入响应状态码: 200
2025-05-02 09:00:25,369 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 02 May 2025 00:59:52 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'DCF3535C-0F8D-7E6B-9A1B-6B8EE9609A4B', 'x-acs-trace-id': 'eb16b851b68ae20e758c4bb3e4fbb65c', 'etag': '4uWOMUjhj+Jdapa77FFfKMA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-02 09:00:25,369 - INFO - 批量插入响应体: {'result': ['FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2O', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9P', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2Q', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3Q', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6Q', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ']}
2025-05-02 09:00:25,369 - INFO - 批量插入表单数据成功，批次 1，共 100 条记录
2025-05-02 09:00:25,369 - INFO - 成功插入的数据ID: ['FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2O', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9P', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2Q', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3Q', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6Q', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSQ', 'FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ']
2025-05-02 09:00:28,525 - INFO - 批量插入响应状态码: 200
2025-05-02 09:00:28,541 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 02 May 2025 00:59:55 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '636', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '1130815D-7F61-73C7-BB32-1A660900A176', 'x-acs-trace-id': '1cb02465009e11f5fc500283ac57f1ec', 'etag': '61Y3JgpJqUOMOFn2dqBaoCA6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-02 09:00:28,541 - INFO - 批量插入响应体: {'result': ['FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM6V', 'FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V', 'FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V', 'FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM9V', 'FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV', 'FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV', 'FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV', 'FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMDV', 'FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMEV', 'FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV', 'FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV', 'FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMHV', 'FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV']}
2025-05-02 09:00:28,541 - INFO - 批量插入表单数据成功，批次 2，共 13 条记录
2025-05-02 09:00:28,541 - INFO - 成功插入的数据ID: ['FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM6V', 'FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V', 'FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V', 'FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM9V', 'FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV', 'FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV', 'FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV', 'FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMDV', 'FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMEV', 'FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV', 'FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV', 'FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMHV', 'FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV']
2025-05-02 09:00:31,556 - INFO - 批量插入完成，共 113 条记录
2025-05-02 09:00:31,556 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 113 条，错误: 0 条
2025-05-02 09:00:31,556 - INFO - 数据同步完成！更新: 0 条，插入: 113 条，错误: 0 条
2025-05-02 09:00:31,556 - INFO - =================同步完成====================
2025-05-02 12:00:01,880 - INFO - =================使用默认全量同步=============
2025-05-02 12:00:03,067 - INFO - MySQL查询成功，共获取 3193 条记录
2025-05-02 12:00:03,067 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-02 12:00:03,099 - INFO - 开始处理日期: 2025-01
2025-05-02 12:00:03,099 - INFO - Request Parameters - Page 1:
2025-05-02 12:00:03,099 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 12:00:03,099 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 12:00:04,020 - INFO - Response - Page 1:
2025-05-02 12:00:04,224 - INFO - 第 1 页获取到 100 条记录
2025-05-02 12:00:04,224 - INFO - Request Parameters - Page 2:
2025-05-02 12:00:04,224 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 12:00:04,224 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 12:00:04,833 - INFO - Response - Page 2:
2025-05-02 12:00:05,036 - INFO - 第 2 页获取到 100 条记录
2025-05-02 12:00:05,036 - INFO - Request Parameters - Page 3:
2025-05-02 12:00:05,036 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 12:00:05,036 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 12:00:05,677 - INFO - Response - Page 3:
2025-05-02 12:00:05,880 - INFO - 第 3 页获取到 100 条记录
2025-05-02 12:00:05,880 - INFO - Request Parameters - Page 4:
2025-05-02 12:00:05,880 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 12:00:05,880 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 12:00:06,364 - INFO - Response - Page 4:
2025-05-02 12:00:06,567 - INFO - 第 4 页获取到 100 条记录
2025-05-02 12:00:06,567 - INFO - Request Parameters - Page 5:
2025-05-02 12:00:06,567 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 12:00:06,567 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 12:00:07,099 - INFO - Response - Page 5:
2025-05-02 12:00:07,302 - INFO - 第 5 页获取到 100 条记录
2025-05-02 12:00:07,302 - INFO - Request Parameters - Page 6:
2025-05-02 12:00:07,302 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 12:00:07,302 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 12:00:07,786 - INFO - Response - Page 6:
2025-05-02 12:00:07,989 - INFO - 第 6 页获取到 100 条记录
2025-05-02 12:00:07,989 - INFO - Request Parameters - Page 7:
2025-05-02 12:00:07,989 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 12:00:07,989 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 12:00:08,505 - INFO - Response - Page 7:
2025-05-02 12:00:08,708 - INFO - 第 7 页获取到 82 条记录
2025-05-02 12:00:08,708 - INFO - 查询完成，共获取到 682 条记录
2025-05-02 12:00:08,708 - INFO - 获取到 682 条表单数据
2025-05-02 12:00:08,708 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-02 12:00:08,724 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-02 12:00:08,724 - INFO - 开始处理日期: 2025-02
2025-05-02 12:00:08,724 - INFO - Request Parameters - Page 1:
2025-05-02 12:00:08,724 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 12:00:08,724 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 12:00:09,224 - INFO - Response - Page 1:
2025-05-02 12:00:09,427 - INFO - 第 1 页获取到 100 条记录
2025-05-02 12:00:09,427 - INFO - Request Parameters - Page 2:
2025-05-02 12:00:09,427 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 12:00:09,427 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 12:00:09,895 - INFO - Response - Page 2:
2025-05-02 12:00:10,099 - INFO - 第 2 页获取到 100 条记录
2025-05-02 12:00:10,099 - INFO - Request Parameters - Page 3:
2025-05-02 12:00:10,099 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 12:00:10,099 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 12:00:10,645 - INFO - Response - Page 3:
2025-05-02 12:00:10,848 - INFO - 第 3 页获取到 100 条记录
2025-05-02 12:00:10,848 - INFO - Request Parameters - Page 4:
2025-05-02 12:00:10,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 12:00:10,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 12:00:11,364 - INFO - Response - Page 4:
2025-05-02 12:00:11,567 - INFO - 第 4 页获取到 100 条记录
2025-05-02 12:00:11,567 - INFO - Request Parameters - Page 5:
2025-05-02 12:00:11,567 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 12:00:11,567 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 12:00:12,036 - INFO - Response - Page 5:
2025-05-02 12:00:12,239 - INFO - 第 5 页获取到 100 条记录
2025-05-02 12:00:12,239 - INFO - Request Parameters - Page 6:
2025-05-02 12:00:12,239 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 12:00:12,239 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 12:00:12,739 - INFO - Response - Page 6:
2025-05-02 12:00:12,942 - INFO - 第 6 页获取到 100 条记录
2025-05-02 12:00:12,942 - INFO - Request Parameters - Page 7:
2025-05-02 12:00:12,942 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 12:00:12,942 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 12:00:13,395 - INFO - Response - Page 7:
2025-05-02 12:00:13,598 - INFO - 第 7 页获取到 70 条记录
2025-05-02 12:00:13,598 - INFO - 查询完成，共获取到 670 条记录
2025-05-02 12:00:13,598 - INFO - 获取到 670 条表单数据
2025-05-02 12:00:13,598 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-02 12:00:13,614 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-02 12:00:13,614 - INFO - 开始处理日期: 2025-03
2025-05-02 12:00:13,614 - INFO - Request Parameters - Page 1:
2025-05-02 12:00:13,614 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 12:00:13,614 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 12:00:14,098 - INFO - Response - Page 1:
2025-05-02 12:00:14,302 - INFO - 第 1 页获取到 100 条记录
2025-05-02 12:00:14,302 - INFO - Request Parameters - Page 2:
2025-05-02 12:00:14,302 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 12:00:14,302 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 12:00:14,755 - INFO - Response - Page 2:
2025-05-02 12:00:14,958 - INFO - 第 2 页获取到 100 条记录
2025-05-02 12:00:14,958 - INFO - Request Parameters - Page 3:
2025-05-02 12:00:14,958 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 12:00:14,958 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 12:00:15,395 - INFO - Response - Page 3:
2025-05-02 12:00:15,598 - INFO - 第 3 页获取到 100 条记录
2025-05-02 12:00:15,598 - INFO - Request Parameters - Page 4:
2025-05-02 12:00:15,598 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 12:00:15,598 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 12:00:16,083 - INFO - Response - Page 4:
2025-05-02 12:00:16,286 - INFO - 第 4 页获取到 100 条记录
2025-05-02 12:00:16,286 - INFO - Request Parameters - Page 5:
2025-05-02 12:00:16,286 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 12:00:16,286 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 12:00:16,739 - INFO - Response - Page 5:
2025-05-02 12:00:16,942 - INFO - 第 5 页获取到 100 条记录
2025-05-02 12:00:16,942 - INFO - Request Parameters - Page 6:
2025-05-02 12:00:16,942 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 12:00:16,942 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 12:00:17,411 - INFO - Response - Page 6:
2025-05-02 12:00:17,614 - INFO - 第 6 页获取到 100 条记录
2025-05-02 12:00:17,614 - INFO - Request Parameters - Page 7:
2025-05-02 12:00:17,614 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 12:00:17,614 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 12:00:18,067 - INFO - Response - Page 7:
2025-05-02 12:00:18,270 - INFO - 第 7 页获取到 61 条记录
2025-05-02 12:00:18,270 - INFO - 查询完成，共获取到 661 条记录
2025-05-02 12:00:18,270 - INFO - 获取到 661 条表单数据
2025-05-02 12:00:18,270 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-02 12:00:18,286 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-02 12:00:18,286 - INFO - 开始处理日期: 2025-04
2025-05-02 12:00:18,286 - INFO - Request Parameters - Page 1:
2025-05-02 12:00:18,286 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 12:00:18,286 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 12:00:18,770 - INFO - Response - Page 1:
2025-05-02 12:00:18,973 - INFO - 第 1 页获取到 100 条记录
2025-05-02 12:00:18,973 - INFO - Request Parameters - Page 2:
2025-05-02 12:00:18,973 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 12:00:18,973 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 12:00:19,505 - INFO - Response - Page 2:
2025-05-02 12:00:19,708 - INFO - 第 2 页获取到 100 条记录
2025-05-02 12:00:19,708 - INFO - Request Parameters - Page 3:
2025-05-02 12:00:19,708 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 12:00:19,708 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 12:00:20,161 - INFO - Response - Page 3:
2025-05-02 12:00:20,364 - INFO - 第 3 页获取到 100 条记录
2025-05-02 12:00:20,364 - INFO - Request Parameters - Page 4:
2025-05-02 12:00:20,364 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 12:00:20,364 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 12:00:20,848 - INFO - Response - Page 4:
2025-05-02 12:00:21,052 - INFO - 第 4 页获取到 100 条记录
2025-05-02 12:00:21,052 - INFO - Request Parameters - Page 5:
2025-05-02 12:00:21,052 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 12:00:21,052 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 12:00:21,473 - INFO - Response - Page 5:
2025-05-02 12:00:21,677 - INFO - 第 5 页获取到 100 条记录
2025-05-02 12:00:21,677 - INFO - Request Parameters - Page 6:
2025-05-02 12:00:21,677 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 12:00:21,677 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 12:00:22,114 - INFO - Response - Page 6:
2025-05-02 12:00:22,317 - INFO - 第 6 页获取到 100 条记录
2025-05-02 12:00:22,317 - INFO - Request Parameters - Page 7:
2025-05-02 12:00:22,317 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 12:00:22,317 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 12:00:22,692 - INFO - Response - Page 7:
2025-05-02 12:00:22,895 - INFO - 第 7 页获取到 27 条记录
2025-05-02 12:00:22,895 - INFO - 查询完成，共获取到 627 条记录
2025-05-02 12:00:22,895 - INFO - 获取到 627 条表单数据
2025-05-02 12:00:22,895 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-02 12:00:22,911 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-02 12:00:22,911 - INFO - 开始处理日期: 2025-05
2025-05-02 12:00:22,911 - INFO - Request Parameters - Page 1:
2025-05-02 12:00:22,911 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 12:00:22,911 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 12:00:23,348 - INFO - Response - Page 1:
2025-05-02 12:00:23,552 - INFO - 第 1 页获取到 100 条记录
2025-05-02 12:00:23,552 - INFO - Request Parameters - Page 2:
2025-05-02 12:00:23,552 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 12:00:23,552 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 12:00:24,020 - INFO - Response - Page 2:
2025-05-02 12:00:24,223 - INFO - 第 2 页获取到 100 条记录
2025-05-02 12:00:24,223 - INFO - Request Parameters - Page 3:
2025-05-02 12:00:24,223 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 12:00:24,223 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 12:00:24,645 - INFO - Response - Page 3:
2025-05-02 12:00:24,848 - INFO - 第 3 页获取到 100 条记录
2025-05-02 12:00:24,848 - INFO - Request Parameters - Page 4:
2025-05-02 12:00:24,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 12:00:24,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 12:00:25,333 - INFO - Response - Page 4:
2025-05-02 12:00:25,536 - INFO - 第 4 页获取到 100 条记录
2025-05-02 12:00:25,536 - INFO - Request Parameters - Page 5:
2025-05-02 12:00:25,536 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 12:00:25,536 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 12:00:25,958 - INFO - Response - Page 5:
2025-05-02 12:00:26,161 - INFO - 第 5 页获取到 84 条记录
2025-05-02 12:00:26,161 - INFO - 查询完成，共获取到 484 条记录
2025-05-02 12:00:26,161 - INFO - 获取到 484 条表单数据
2025-05-02 12:00:26,161 - INFO - 当前日期 2025-05 有 553 条MySQL数据需要处理
2025-05-02 12:00:26,161 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0L
2025-05-02 12:00:26,630 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0L
2025-05-02 12:00:26,630 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 210000.0, 'new_value': 200000.0}, {'field': 'total_amount', 'old_value': 210000.0, 'new_value': 200000.0}]
2025-05-02 12:00:26,630 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-02 12:00:27,083 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-02 12:00:27,083 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 324765.0, 'new_value': 366331.0}, {'field': 'total_amount', 'old_value': 324765.0, 'new_value': 366331.0}, {'field': 'order_count', 'old_value': 5479, 'new_value': 4970}]
2025-05-02 12:00:27,083 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-02 12:00:27,489 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-02 12:00:27,489 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8597.0, 'new_value': 7981.5}, {'field': 'total_amount', 'old_value': 8597.0, 'new_value': 7981.5}]
2025-05-02 12:00:27,489 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-02 12:00:27,973 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-02 12:00:27,973 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8335.0, 'new_value': 10722.0}, {'field': 'total_amount', 'old_value': 8335.0, 'new_value': 10722.0}, {'field': 'order_count', 'old_value': 79, 'new_value': 45}]
2025-05-02 12:00:27,973 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-02 12:00:28,442 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-02 12:00:28,442 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2800.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2800.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-02 12:00:28,442 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-02 12:00:28,911 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-02 12:00:28,911 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100000.0, 'new_value': 200000.0}, {'field': 'total_amount', 'old_value': 100000.0, 'new_value': 200000.0}]
2025-05-02 12:00:28,911 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-02 12:00:29,505 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-02 12:00:29,505 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 150000.0, 'new_value': 300000.0}, {'field': 'total_amount', 'old_value': 150000.0, 'new_value': 300000.0}]
2025-05-02 12:00:29,520 - INFO - 开始批量插入 69 条新记录
2025-05-02 12:00:29,755 - INFO - 批量插入响应状态码: 200
2025-05-02 12:00:29,755 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 02 May 2025 03:59:56 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '3324', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B8BA190A-AB50-7AA5-A747-1CF5C60B6C2D', 'x-acs-trace-id': '9fcac90f425333bfaf79b64d176c85d7', 'etag': '3+SpOUETpCO69brJ7iW+dyg4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-02 12:00:29,755 - INFO - 批量插入响应体: {'result': ['FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4F', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5F', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6F', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7F', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8F', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9F', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMBF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMKF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6G', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8G', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9G', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMBG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMKG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG']}
2025-05-02 12:00:29,755 - INFO - 批量插入表单数据成功，批次 1，共 69 条记录
2025-05-02 12:00:29,755 - INFO - 成功插入的数据ID: ['FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4F', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5F', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6F', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7F', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8F', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9F', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMBF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMKF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZF', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6G', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8G', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9G', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMBG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMKG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG', 'FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG']
2025-05-02 12:00:32,770 - INFO - 批量插入完成，共 69 条记录
2025-05-02 12:00:32,770 - INFO - 日期 2025-05 处理完成 - 更新: 7 条，插入: 69 条，错误: 0 条
2025-05-02 12:00:32,770 - INFO - 数据同步完成！更新: 7 条，插入: 69 条，错误: 0 条
2025-05-02 12:00:32,770 - INFO - =================同步完成====================
2025-05-02 15:00:01,843 - INFO - =================使用默认全量同步=============
2025-05-02 15:00:03,031 - INFO - MySQL查询成功，共获取 3195 条记录
2025-05-02 15:00:03,031 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-02 15:00:03,062 - INFO - 开始处理日期: 2025-01
2025-05-02 15:00:03,062 - INFO - Request Parameters - Page 1:
2025-05-02 15:00:03,062 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 15:00:03,062 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 15:00:03,922 - INFO - Response - Page 1:
2025-05-02 15:00:04,125 - INFO - 第 1 页获取到 100 条记录
2025-05-02 15:00:04,125 - INFO - Request Parameters - Page 2:
2025-05-02 15:00:04,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 15:00:04,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 15:00:04,922 - INFO - Response - Page 2:
2025-05-02 15:00:05,125 - INFO - 第 2 页获取到 100 条记录
2025-05-02 15:00:05,125 - INFO - Request Parameters - Page 3:
2025-05-02 15:00:05,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 15:00:05,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 15:00:05,656 - INFO - Response - Page 3:
2025-05-02 15:00:05,859 - INFO - 第 3 页获取到 100 条记录
2025-05-02 15:00:05,859 - INFO - Request Parameters - Page 4:
2025-05-02 15:00:05,859 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 15:00:05,859 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 15:00:06,375 - INFO - Response - Page 4:
2025-05-02 15:00:06,593 - INFO - 第 4 页获取到 100 条记录
2025-05-02 15:00:06,593 - INFO - Request Parameters - Page 5:
2025-05-02 15:00:06,593 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 15:00:06,593 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 15:00:07,031 - INFO - Response - Page 5:
2025-05-02 15:00:07,234 - INFO - 第 5 页获取到 100 条记录
2025-05-02 15:00:07,234 - INFO - Request Parameters - Page 6:
2025-05-02 15:00:07,234 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 15:00:07,234 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 15:00:07,828 - INFO - Response - Page 6:
2025-05-02 15:00:08,031 - INFO - 第 6 页获取到 100 条记录
2025-05-02 15:00:08,031 - INFO - Request Parameters - Page 7:
2025-05-02 15:00:08,031 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 15:00:08,031 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 15:00:08,484 - INFO - Response - Page 7:
2025-05-02 15:00:08,687 - INFO - 第 7 页获取到 82 条记录
2025-05-02 15:00:08,687 - INFO - 查询完成，共获取到 682 条记录
2025-05-02 15:00:08,687 - INFO - 获取到 682 条表单数据
2025-05-02 15:00:08,687 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-02 15:00:08,703 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-02 15:00:08,703 - INFO - 开始处理日期: 2025-02
2025-05-02 15:00:08,703 - INFO - Request Parameters - Page 1:
2025-05-02 15:00:08,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 15:00:08,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 15:00:09,203 - INFO - Response - Page 1:
2025-05-02 15:00:09,406 - INFO - 第 1 页获取到 100 条记录
2025-05-02 15:00:09,406 - INFO - Request Parameters - Page 2:
2025-05-02 15:00:09,406 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 15:00:09,406 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 15:00:09,843 - INFO - Response - Page 2:
2025-05-02 15:00:10,046 - INFO - 第 2 页获取到 100 条记录
2025-05-02 15:00:10,046 - INFO - Request Parameters - Page 3:
2025-05-02 15:00:10,046 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 15:00:10,046 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 15:00:10,531 - INFO - Response - Page 3:
2025-05-02 15:00:10,734 - INFO - 第 3 页获取到 100 条记录
2025-05-02 15:00:10,734 - INFO - Request Parameters - Page 4:
2025-05-02 15:00:10,734 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 15:00:10,734 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 15:00:11,203 - INFO - Response - Page 4:
2025-05-02 15:00:11,406 - INFO - 第 4 页获取到 100 条记录
2025-05-02 15:00:11,406 - INFO - Request Parameters - Page 5:
2025-05-02 15:00:11,406 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 15:00:11,406 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 15:00:11,921 - INFO - Response - Page 5:
2025-05-02 15:00:12,125 - INFO - 第 5 页获取到 100 条记录
2025-05-02 15:00:12,125 - INFO - Request Parameters - Page 6:
2025-05-02 15:00:12,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 15:00:12,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 15:00:12,578 - INFO - Response - Page 6:
2025-05-02 15:00:12,781 - INFO - 第 6 页获取到 100 条记录
2025-05-02 15:00:12,781 - INFO - Request Parameters - Page 7:
2025-05-02 15:00:12,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 15:00:12,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 15:00:13,171 - INFO - Response - Page 7:
2025-05-02 15:00:13,375 - INFO - 第 7 页获取到 70 条记录
2025-05-02 15:00:13,375 - INFO - 查询完成，共获取到 670 条记录
2025-05-02 15:00:13,375 - INFO - 获取到 670 条表单数据
2025-05-02 15:00:13,375 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-02 15:00:13,390 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-02 15:00:13,390 - INFO - 开始处理日期: 2025-03
2025-05-02 15:00:13,390 - INFO - Request Parameters - Page 1:
2025-05-02 15:00:13,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 15:00:13,390 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 15:00:13,921 - INFO - Response - Page 1:
2025-05-02 15:00:14,125 - INFO - 第 1 页获取到 100 条记录
2025-05-02 15:00:14,125 - INFO - Request Parameters - Page 2:
2025-05-02 15:00:14,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 15:00:14,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 15:00:14,578 - INFO - Response - Page 2:
2025-05-02 15:00:14,781 - INFO - 第 2 页获取到 100 条记录
2025-05-02 15:00:14,781 - INFO - Request Parameters - Page 3:
2025-05-02 15:00:14,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 15:00:14,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 15:00:15,234 - INFO - Response - Page 3:
2025-05-02 15:00:15,437 - INFO - 第 3 页获取到 100 条记录
2025-05-02 15:00:15,437 - INFO - Request Parameters - Page 4:
2025-05-02 15:00:15,437 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 15:00:15,437 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 15:00:15,921 - INFO - Response - Page 4:
2025-05-02 15:00:16,125 - INFO - 第 4 页获取到 100 条记录
2025-05-02 15:00:16,125 - INFO - Request Parameters - Page 5:
2025-05-02 15:00:16,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 15:00:16,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 15:00:16,671 - INFO - Response - Page 5:
2025-05-02 15:00:16,875 - INFO - 第 5 页获取到 100 条记录
2025-05-02 15:00:16,875 - INFO - Request Parameters - Page 6:
2025-05-02 15:00:16,875 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 15:00:16,875 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 15:00:17,375 - INFO - Response - Page 6:
2025-05-02 15:00:17,578 - INFO - 第 6 页获取到 100 条记录
2025-05-02 15:00:17,578 - INFO - Request Parameters - Page 7:
2025-05-02 15:00:17,578 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 15:00:17,578 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 15:00:18,031 - INFO - Response - Page 7:
2025-05-02 15:00:18,234 - INFO - 第 7 页获取到 61 条记录
2025-05-02 15:00:18,234 - INFO - 查询完成，共获取到 661 条记录
2025-05-02 15:00:18,234 - INFO - 获取到 661 条表单数据
2025-05-02 15:00:18,234 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-02 15:00:18,250 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-02 15:00:18,250 - INFO - 开始处理日期: 2025-04
2025-05-02 15:00:18,250 - INFO - Request Parameters - Page 1:
2025-05-02 15:00:18,250 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 15:00:18,250 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 15:00:18,734 - INFO - Response - Page 1:
2025-05-02 15:00:18,953 - INFO - 第 1 页获取到 100 条记录
2025-05-02 15:00:18,953 - INFO - Request Parameters - Page 2:
2025-05-02 15:00:18,953 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 15:00:18,953 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 15:00:19,500 - INFO - Response - Page 2:
2025-05-02 15:00:19,703 - INFO - 第 2 页获取到 100 条记录
2025-05-02 15:00:19,703 - INFO - Request Parameters - Page 3:
2025-05-02 15:00:19,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 15:00:19,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 15:00:20,140 - INFO - Response - Page 3:
2025-05-02 15:00:20,343 - INFO - 第 3 页获取到 100 条记录
2025-05-02 15:00:20,343 - INFO - Request Parameters - Page 4:
2025-05-02 15:00:20,343 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 15:00:20,343 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 15:00:20,812 - INFO - Response - Page 4:
2025-05-02 15:00:21,031 - INFO - 第 4 页获取到 100 条记录
2025-05-02 15:00:21,031 - INFO - Request Parameters - Page 5:
2025-05-02 15:00:21,031 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 15:00:21,031 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 15:00:21,703 - INFO - Response - Page 5:
2025-05-02 15:00:21,906 - INFO - 第 5 页获取到 100 条记录
2025-05-02 15:00:21,906 - INFO - Request Parameters - Page 6:
2025-05-02 15:00:21,906 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 15:00:21,906 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 15:00:22,390 - INFO - Response - Page 6:
2025-05-02 15:00:22,593 - INFO - 第 6 页获取到 100 条记录
2025-05-02 15:00:22,593 - INFO - Request Parameters - Page 7:
2025-05-02 15:00:22,593 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 15:00:22,593 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 15:00:22,953 - INFO - Response - Page 7:
2025-05-02 15:00:23,156 - INFO - 第 7 页获取到 27 条记录
2025-05-02 15:00:23,156 - INFO - 查询完成，共获取到 627 条记录
2025-05-02 15:00:23,156 - INFO - 获取到 627 条表单数据
2025-05-02 15:00:23,156 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-02 15:00:23,171 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-02 15:00:23,171 - INFO - 开始处理日期: 2025-05
2025-05-02 15:00:23,171 - INFO - Request Parameters - Page 1:
2025-05-02 15:00:23,171 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 15:00:23,171 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 15:00:23,625 - INFO - Response - Page 1:
2025-05-02 15:00:23,828 - INFO - 第 1 页获取到 100 条记录
2025-05-02 15:00:23,828 - INFO - Request Parameters - Page 2:
2025-05-02 15:00:23,828 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 15:00:23,828 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 15:00:24,312 - INFO - Response - Page 2:
2025-05-02 15:00:24,515 - INFO - 第 2 页获取到 100 条记录
2025-05-02 15:00:24,515 - INFO - Request Parameters - Page 3:
2025-05-02 15:00:24,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 15:00:24,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 15:00:24,968 - INFO - Response - Page 3:
2025-05-02 15:00:25,171 - INFO - 第 3 页获取到 100 条记录
2025-05-02 15:00:25,171 - INFO - Request Parameters - Page 4:
2025-05-02 15:00:25,171 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 15:00:25,171 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 15:00:25,640 - INFO - Response - Page 4:
2025-05-02 15:00:25,843 - INFO - 第 4 页获取到 100 条记录
2025-05-02 15:00:25,843 - INFO - Request Parameters - Page 5:
2025-05-02 15:00:25,843 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 15:00:25,843 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 15:00:26,312 - INFO - Response - Page 5:
2025-05-02 15:00:26,515 - INFO - 第 5 页获取到 100 条记录
2025-05-02 15:00:26,515 - INFO - Request Parameters - Page 6:
2025-05-02 15:00:26,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 15:00:26,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 15:00:26,890 - INFO - Response - Page 6:
2025-05-02 15:00:27,093 - INFO - 第 6 页获取到 53 条记录
2025-05-02 15:00:27,093 - INFO - 查询完成，共获取到 553 条记录
2025-05-02 15:00:27,093 - INFO - 获取到 553 条表单数据
2025-05-02 15:00:27,093 - INFO - 当前日期 2025-05 有 555 条MySQL数据需要处理
2025-05-02 15:00:27,109 - INFO - 开始批量插入 2 条新记录
2025-05-02 15:00:27,265 - INFO - 批量插入响应状态码: 200
2025-05-02 15:00:27,265 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 02 May 2025 06:59:54 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '1EB65863-0780-74EA-90D9-35D3D8D4451A', 'x-acs-trace-id': '28ad44df772f83bf05091a4c49295d37', 'etag': '1Nsz2Tjp67RPhPrBlJ4nF3A8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-02 15:00:27,265 - INFO - 批量插入响应体: {'result': ['FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AM9C', 'FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC']}
2025-05-02 15:00:27,265 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-05-02 15:00:27,265 - INFO - 成功插入的数据ID: ['FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AM9C', 'FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC']
2025-05-02 15:00:30,281 - INFO - 批量插入完成，共 2 条记录
2025-05-02 15:00:30,281 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-05-02 15:00:30,281 - INFO - 数据同步完成！更新: 0 条，插入: 2 条，错误: 0 条
2025-05-02 15:00:30,281 - INFO - =================同步完成====================
2025-05-02 18:00:02,010 - INFO - =================使用默认全量同步=============
2025-05-02 18:00:03,182 - INFO - MySQL查询成功，共获取 3195 条记录
2025-05-02 18:00:03,182 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-02 18:00:03,213 - INFO - 开始处理日期: 2025-01
2025-05-02 18:00:03,213 - INFO - Request Parameters - Page 1:
2025-05-02 18:00:03,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 18:00:03,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 18:00:04,135 - INFO - Response - Page 1:
2025-05-02 18:00:04,338 - INFO - 第 1 页获取到 100 条记录
2025-05-02 18:00:04,338 - INFO - Request Parameters - Page 2:
2025-05-02 18:00:04,338 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 18:00:04,338 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 18:00:04,885 - INFO - Response - Page 2:
2025-05-02 18:00:05,088 - INFO - 第 2 页获取到 100 条记录
2025-05-02 18:00:05,088 - INFO - Request Parameters - Page 3:
2025-05-02 18:00:05,088 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 18:00:05,088 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 18:00:05,620 - INFO - Response - Page 3:
2025-05-02 18:00:05,823 - INFO - 第 3 页获取到 100 条记录
2025-05-02 18:00:05,823 - INFO - Request Parameters - Page 4:
2025-05-02 18:00:05,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 18:00:05,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 18:00:06,432 - INFO - Response - Page 4:
2025-05-02 18:00:06,635 - INFO - 第 4 页获取到 100 条记录
2025-05-02 18:00:06,635 - INFO - Request Parameters - Page 5:
2025-05-02 18:00:06,635 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 18:00:06,635 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 18:00:07,167 - INFO - Response - Page 5:
2025-05-02 18:00:07,370 - INFO - 第 5 页获取到 100 条记录
2025-05-02 18:00:07,370 - INFO - Request Parameters - Page 6:
2025-05-02 18:00:07,370 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 18:00:07,370 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 18:00:07,885 - INFO - Response - Page 6:
2025-05-02 18:00:08,088 - INFO - 第 6 页获取到 100 条记录
2025-05-02 18:00:08,088 - INFO - Request Parameters - Page 7:
2025-05-02 18:00:08,088 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 18:00:08,088 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 18:00:08,557 - INFO - Response - Page 7:
2025-05-02 18:00:08,760 - INFO - 第 7 页获取到 82 条记录
2025-05-02 18:00:08,760 - INFO - 查询完成，共获取到 682 条记录
2025-05-02 18:00:08,760 - INFO - 获取到 682 条表单数据
2025-05-02 18:00:08,760 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-02 18:00:08,776 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-02 18:00:08,776 - INFO - 开始处理日期: 2025-02
2025-05-02 18:00:08,776 - INFO - Request Parameters - Page 1:
2025-05-02 18:00:08,776 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 18:00:08,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 18:00:09,276 - INFO - Response - Page 1:
2025-05-02 18:00:09,479 - INFO - 第 1 页获取到 100 条记录
2025-05-02 18:00:09,479 - INFO - Request Parameters - Page 2:
2025-05-02 18:00:09,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 18:00:09,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 18:00:09,979 - INFO - Response - Page 2:
2025-05-02 18:00:10,182 - INFO - 第 2 页获取到 100 条记录
2025-05-02 18:00:10,182 - INFO - Request Parameters - Page 3:
2025-05-02 18:00:10,182 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 18:00:10,182 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 18:00:10,651 - INFO - Response - Page 3:
2025-05-02 18:00:10,854 - INFO - 第 3 页获取到 100 条记录
2025-05-02 18:00:10,854 - INFO - Request Parameters - Page 4:
2025-05-02 18:00:10,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 18:00:10,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 18:00:11,370 - INFO - Response - Page 4:
2025-05-02 18:00:11,573 - INFO - 第 4 页获取到 100 条记录
2025-05-02 18:00:11,573 - INFO - Request Parameters - Page 5:
2025-05-02 18:00:11,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 18:00:11,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 18:00:12,057 - INFO - Response - Page 5:
2025-05-02 18:00:12,260 - INFO - 第 5 页获取到 100 条记录
2025-05-02 18:00:12,260 - INFO - Request Parameters - Page 6:
2025-05-02 18:00:12,260 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 18:00:12,260 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 18:00:12,745 - INFO - Response - Page 6:
2025-05-02 18:00:12,948 - INFO - 第 6 页获取到 100 条记录
2025-05-02 18:00:12,948 - INFO - Request Parameters - Page 7:
2025-05-02 18:00:12,948 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 18:00:12,948 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 18:00:13,385 - INFO - Response - Page 7:
2025-05-02 18:00:13,588 - INFO - 第 7 页获取到 70 条记录
2025-05-02 18:00:13,588 - INFO - 查询完成，共获取到 670 条记录
2025-05-02 18:00:13,588 - INFO - 获取到 670 条表单数据
2025-05-02 18:00:13,588 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-02 18:00:13,604 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-02 18:00:13,604 - INFO - 开始处理日期: 2025-03
2025-05-02 18:00:13,604 - INFO - Request Parameters - Page 1:
2025-05-02 18:00:13,604 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 18:00:13,604 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 18:00:14,088 - INFO - Response - Page 1:
2025-05-02 18:00:14,291 - INFO - 第 1 页获取到 100 条记录
2025-05-02 18:00:14,291 - INFO - Request Parameters - Page 2:
2025-05-02 18:00:14,291 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 18:00:14,291 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 18:00:14,963 - INFO - Response - Page 2:
2025-05-02 18:00:15,166 - INFO - 第 2 页获取到 100 条记录
2025-05-02 18:00:15,166 - INFO - Request Parameters - Page 3:
2025-05-02 18:00:15,166 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 18:00:15,166 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 18:00:15,651 - INFO - Response - Page 3:
2025-05-02 18:00:15,854 - INFO - 第 3 页获取到 100 条记录
2025-05-02 18:00:15,854 - INFO - Request Parameters - Page 4:
2025-05-02 18:00:15,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 18:00:15,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 18:00:16,354 - INFO - Response - Page 4:
2025-05-02 18:00:16,557 - INFO - 第 4 页获取到 100 条记录
2025-05-02 18:00:16,557 - INFO - Request Parameters - Page 5:
2025-05-02 18:00:16,557 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 18:00:16,557 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 18:00:17,041 - INFO - Response - Page 5:
2025-05-02 18:00:17,245 - INFO - 第 5 页获取到 100 条记录
2025-05-02 18:00:17,245 - INFO - Request Parameters - Page 6:
2025-05-02 18:00:17,245 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 18:00:17,245 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 18:00:17,713 - INFO - Response - Page 6:
2025-05-02 18:00:17,916 - INFO - 第 6 页获取到 100 条记录
2025-05-02 18:00:17,916 - INFO - Request Parameters - Page 7:
2025-05-02 18:00:17,916 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 18:00:17,916 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 18:00:18,354 - INFO - Response - Page 7:
2025-05-02 18:00:18,557 - INFO - 第 7 页获取到 61 条记录
2025-05-02 18:00:18,557 - INFO - 查询完成，共获取到 661 条记录
2025-05-02 18:00:18,557 - INFO - 获取到 661 条表单数据
2025-05-02 18:00:18,557 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-02 18:00:18,573 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-02 18:00:18,573 - INFO - 开始处理日期: 2025-04
2025-05-02 18:00:18,573 - INFO - Request Parameters - Page 1:
2025-05-02 18:00:18,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 18:00:18,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 18:00:19,088 - INFO - Response - Page 1:
2025-05-02 18:00:19,291 - INFO - 第 1 页获取到 100 条记录
2025-05-02 18:00:19,291 - INFO - Request Parameters - Page 2:
2025-05-02 18:00:19,291 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 18:00:19,291 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 18:00:19,823 - INFO - Response - Page 2:
2025-05-02 18:00:20,026 - INFO - 第 2 页获取到 100 条记录
2025-05-02 18:00:20,026 - INFO - Request Parameters - Page 3:
2025-05-02 18:00:20,026 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 18:00:20,026 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 18:00:20,448 - INFO - Response - Page 3:
2025-05-02 18:00:20,651 - INFO - 第 3 页获取到 100 条记录
2025-05-02 18:00:20,651 - INFO - Request Parameters - Page 4:
2025-05-02 18:00:20,651 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 18:00:20,651 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 18:00:21,151 - INFO - Response - Page 4:
2025-05-02 18:00:21,354 - INFO - 第 4 页获取到 100 条记录
2025-05-02 18:00:21,354 - INFO - Request Parameters - Page 5:
2025-05-02 18:00:21,354 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 18:00:21,354 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 18:00:21,823 - INFO - Response - Page 5:
2025-05-02 18:00:22,026 - INFO - 第 5 页获取到 100 条记录
2025-05-02 18:00:22,026 - INFO - Request Parameters - Page 6:
2025-05-02 18:00:22,026 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 18:00:22,026 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 18:00:22,510 - INFO - Response - Page 6:
2025-05-02 18:00:22,713 - INFO - 第 6 页获取到 100 条记录
2025-05-02 18:00:22,713 - INFO - Request Parameters - Page 7:
2025-05-02 18:00:22,713 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 18:00:22,713 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 18:00:23,135 - INFO - Response - Page 7:
2025-05-02 18:00:23,338 - INFO - 第 7 页获取到 27 条记录
2025-05-02 18:00:23,338 - INFO - 查询完成，共获取到 627 条记录
2025-05-02 18:00:23,338 - INFO - 获取到 627 条表单数据
2025-05-02 18:00:23,338 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-02 18:00:23,354 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-02 18:00:23,354 - INFO - 开始处理日期: 2025-05
2025-05-02 18:00:23,354 - INFO - Request Parameters - Page 1:
2025-05-02 18:00:23,354 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 18:00:23,354 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 18:00:23,870 - INFO - Response - Page 1:
2025-05-02 18:00:24,073 - INFO - 第 1 页获取到 100 条记录
2025-05-02 18:00:24,073 - INFO - Request Parameters - Page 2:
2025-05-02 18:00:24,073 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 18:00:24,073 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 18:00:24,573 - INFO - Response - Page 2:
2025-05-02 18:00:24,776 - INFO - 第 2 页获取到 100 条记录
2025-05-02 18:00:24,776 - INFO - Request Parameters - Page 3:
2025-05-02 18:00:24,776 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 18:00:24,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 18:00:25,198 - INFO - Response - Page 3:
2025-05-02 18:00:25,401 - INFO - 第 3 页获取到 100 条记录
2025-05-02 18:00:25,401 - INFO - Request Parameters - Page 4:
2025-05-02 18:00:25,401 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 18:00:25,401 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 18:00:25,838 - INFO - Response - Page 4:
2025-05-02 18:00:26,041 - INFO - 第 4 页获取到 100 条记录
2025-05-02 18:00:26,041 - INFO - Request Parameters - Page 5:
2025-05-02 18:00:26,041 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 18:00:26,041 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 18:00:26,479 - INFO - Response - Page 5:
2025-05-02 18:00:26,682 - INFO - 第 5 页获取到 100 条记录
2025-05-02 18:00:26,682 - INFO - Request Parameters - Page 6:
2025-05-02 18:00:26,682 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 18:00:26,682 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 18:00:27,073 - INFO - Response - Page 6:
2025-05-02 18:00:27,276 - INFO - 第 6 页获取到 55 条记录
2025-05-02 18:00:27,276 - INFO - 查询完成，共获取到 555 条记录
2025-05-02 18:00:27,276 - INFO - 获取到 555 条表单数据
2025-05-02 18:00:27,276 - INFO - 当前日期 2025-05 有 555 条MySQL数据需要处理
2025-05-02 18:00:27,291 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGG
2025-05-02 18:00:27,838 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGG
2025-05-02 18:00:27,838 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 4177.0}, {'field': 'offline_amount', 'old_value': 6741.0, 'new_value': 1471.0}, {'field': 'total_amount', 'old_value': 6741.0, 'new_value': 5648.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 220}]
2025-05-02 18:00:27,838 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-02 18:00:27,838 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-05-02 18:00:27,838 - INFO - =================同步完成====================
2025-05-02 21:00:01,937 - INFO - =================使用默认全量同步=============
2025-05-02 21:00:03,109 - INFO - MySQL查询成功，共获取 3195 条记录
2025-05-02 21:00:03,125 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-02 21:00:03,140 - INFO - 开始处理日期: 2025-01
2025-05-02 21:00:03,156 - INFO - Request Parameters - Page 1:
2025-05-02 21:00:03,156 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:00:03,156 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:00:03,797 - INFO - Response - Page 1:
2025-05-02 21:00:04,000 - INFO - 第 1 页获取到 100 条记录
2025-05-02 21:00:04,000 - INFO - Request Parameters - Page 2:
2025-05-02 21:00:04,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:00:04,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:00:04,781 - INFO - Response - Page 2:
2025-05-02 21:00:04,984 - INFO - 第 2 页获取到 100 条记录
2025-05-02 21:00:04,984 - INFO - Request Parameters - Page 3:
2025-05-02 21:00:04,984 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:00:04,984 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:00:05,422 - INFO - Response - Page 3:
2025-05-02 21:00:05,625 - INFO - 第 3 页获取到 100 条记录
2025-05-02 21:00:05,625 - INFO - Request Parameters - Page 4:
2025-05-02 21:00:05,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:00:05,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:00:06,062 - INFO - Response - Page 4:
2025-05-02 21:00:06,265 - INFO - 第 4 页获取到 100 条记录
2025-05-02 21:00:06,265 - INFO - Request Parameters - Page 5:
2025-05-02 21:00:06,265 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:00:06,265 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:00:06,750 - INFO - Response - Page 5:
2025-05-02 21:00:06,953 - INFO - 第 5 页获取到 100 条记录
2025-05-02 21:00:06,953 - INFO - Request Parameters - Page 6:
2025-05-02 21:00:06,953 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:00:06,953 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:00:07,469 - INFO - Response - Page 6:
2025-05-02 21:00:07,672 - INFO - 第 6 页获取到 100 条记录
2025-05-02 21:00:07,672 - INFO - Request Parameters - Page 7:
2025-05-02 21:00:07,672 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:00:07,672 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:00:08,140 - INFO - Response - Page 7:
2025-05-02 21:00:08,344 - INFO - 第 7 页获取到 82 条记录
2025-05-02 21:00:08,344 - INFO - 查询完成，共获取到 682 条记录
2025-05-02 21:00:08,344 - INFO - 获取到 682 条表单数据
2025-05-02 21:00:08,344 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-02 21:00:08,359 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-02 21:00:08,359 - INFO - 开始处理日期: 2025-02
2025-05-02 21:00:08,359 - INFO - Request Parameters - Page 1:
2025-05-02 21:00:08,359 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:00:08,359 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:00:08,922 - INFO - Response - Page 1:
2025-05-02 21:00:09,125 - INFO - 第 1 页获取到 100 条记录
2025-05-02 21:00:09,125 - INFO - Request Parameters - Page 2:
2025-05-02 21:00:09,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:00:09,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:00:09,640 - INFO - Response - Page 2:
2025-05-02 21:00:09,844 - INFO - 第 2 页获取到 100 条记录
2025-05-02 21:00:09,844 - INFO - Request Parameters - Page 3:
2025-05-02 21:00:09,844 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:00:09,844 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:00:10,312 - INFO - Response - Page 3:
2025-05-02 21:00:10,515 - INFO - 第 3 页获取到 100 条记录
2025-05-02 21:00:10,515 - INFO - Request Parameters - Page 4:
2025-05-02 21:00:10,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:00:10,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:00:11,015 - INFO - Response - Page 4:
2025-05-02 21:00:11,219 - INFO - 第 4 页获取到 100 条记录
2025-05-02 21:00:11,219 - INFO - Request Parameters - Page 5:
2025-05-02 21:00:11,219 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:00:11,219 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:00:11,687 - INFO - Response - Page 5:
2025-05-02 21:00:11,890 - INFO - 第 5 页获取到 100 条记录
2025-05-02 21:00:11,890 - INFO - Request Parameters - Page 6:
2025-05-02 21:00:11,890 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:00:11,890 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:00:12,312 - INFO - Response - Page 6:
2025-05-02 21:00:12,515 - INFO - 第 6 页获取到 100 条记录
2025-05-02 21:00:12,515 - INFO - Request Parameters - Page 7:
2025-05-02 21:00:12,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:00:12,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:00:12,953 - INFO - Response - Page 7:
2025-05-02 21:00:13,156 - INFO - 第 7 页获取到 70 条记录
2025-05-02 21:00:13,156 - INFO - 查询完成，共获取到 670 条记录
2025-05-02 21:00:13,156 - INFO - 获取到 670 条表单数据
2025-05-02 21:00:13,156 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-02 21:00:13,172 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-02 21:00:13,172 - INFO - 开始处理日期: 2025-03
2025-05-02 21:00:13,172 - INFO - Request Parameters - Page 1:
2025-05-02 21:00:13,172 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:00:13,172 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:00:13,656 - INFO - Response - Page 1:
2025-05-02 21:00:13,859 - INFO - 第 1 页获取到 100 条记录
2025-05-02 21:00:13,859 - INFO - Request Parameters - Page 2:
2025-05-02 21:00:13,859 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:00:13,859 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:00:14,297 - INFO - Response - Page 2:
2025-05-02 21:00:14,500 - INFO - 第 2 页获取到 100 条记录
2025-05-02 21:00:14,500 - INFO - Request Parameters - Page 3:
2025-05-02 21:00:14,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:00:14,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:00:14,906 - INFO - Response - Page 3:
2025-05-02 21:00:15,109 - INFO - 第 3 页获取到 100 条记录
2025-05-02 21:00:15,109 - INFO - Request Parameters - Page 4:
2025-05-02 21:00:15,109 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:00:15,109 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:00:15,625 - INFO - Response - Page 4:
2025-05-02 21:00:15,828 - INFO - 第 4 页获取到 100 条记录
2025-05-02 21:00:15,828 - INFO - Request Parameters - Page 5:
2025-05-02 21:00:15,828 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:00:15,828 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:00:16,328 - INFO - Response - Page 5:
2025-05-02 21:00:16,531 - INFO - 第 5 页获取到 100 条记录
2025-05-02 21:00:16,531 - INFO - Request Parameters - Page 6:
2025-05-02 21:00:16,531 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:00:16,531 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:00:16,984 - INFO - Response - Page 6:
2025-05-02 21:00:17,187 - INFO - 第 6 页获取到 100 条记录
2025-05-02 21:00:17,187 - INFO - Request Parameters - Page 7:
2025-05-02 21:00:17,187 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:00:17,187 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:00:17,578 - INFO - Response - Page 7:
2025-05-02 21:00:17,781 - INFO - 第 7 页获取到 61 条记录
2025-05-02 21:00:17,781 - INFO - 查询完成，共获取到 661 条记录
2025-05-02 21:00:17,781 - INFO - 获取到 661 条表单数据
2025-05-02 21:00:17,781 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-02 21:00:17,797 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-02 21:00:17,797 - INFO - 开始处理日期: 2025-04
2025-05-02 21:00:17,797 - INFO - Request Parameters - Page 1:
2025-05-02 21:00:17,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:00:17,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:00:18,297 - INFO - Response - Page 1:
2025-05-02 21:00:18,500 - INFO - 第 1 页获取到 100 条记录
2025-05-02 21:00:18,500 - INFO - Request Parameters - Page 2:
2025-05-02 21:00:18,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:00:18,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:00:18,984 - INFO - Response - Page 2:
2025-05-02 21:00:19,187 - INFO - 第 2 页获取到 100 条记录
2025-05-02 21:00:19,187 - INFO - Request Parameters - Page 3:
2025-05-02 21:00:19,187 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:00:19,187 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:00:19,656 - INFO - Response - Page 3:
2025-05-02 21:00:19,859 - INFO - 第 3 页获取到 100 条记录
2025-05-02 21:00:19,859 - INFO - Request Parameters - Page 4:
2025-05-02 21:00:19,859 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:00:19,859 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:00:20,312 - INFO - Response - Page 4:
2025-05-02 21:00:20,515 - INFO - 第 4 页获取到 100 条记录
2025-05-02 21:00:20,515 - INFO - Request Parameters - Page 5:
2025-05-02 21:00:20,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:00:20,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:00:21,015 - INFO - Response - Page 5:
2025-05-02 21:00:21,218 - INFO - 第 5 页获取到 100 条记录
2025-05-02 21:00:21,218 - INFO - Request Parameters - Page 6:
2025-05-02 21:00:21,218 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:00:21,218 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:00:21,687 - INFO - Response - Page 6:
2025-05-02 21:00:21,890 - INFO - 第 6 页获取到 100 条记录
2025-05-02 21:00:21,890 - INFO - Request Parameters - Page 7:
2025-05-02 21:00:21,890 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:00:21,890 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:00:22,281 - INFO - Response - Page 7:
2025-05-02 21:00:22,484 - INFO - 第 7 页获取到 27 条记录
2025-05-02 21:00:22,484 - INFO - 查询完成，共获取到 627 条记录
2025-05-02 21:00:22,484 - INFO - 获取到 627 条表单数据
2025-05-02 21:00:22,484 - INFO - 当前日期 2025-04 有 627 条MySQL数据需要处理
2025-05-02 21:00:22,500 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-02 21:00:22,500 - INFO - 开始处理日期: 2025-05
2025-05-02 21:00:22,500 - INFO - Request Parameters - Page 1:
2025-05-02 21:00:22,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:00:22,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:00:22,922 - INFO - Response - Page 1:
2025-05-02 21:00:23,125 - INFO - 第 1 页获取到 100 条记录
2025-05-02 21:00:23,125 - INFO - Request Parameters - Page 2:
2025-05-02 21:00:23,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:00:23,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:00:23,609 - INFO - Response - Page 2:
2025-05-02 21:00:23,812 - INFO - 第 2 页获取到 100 条记录
2025-05-02 21:00:23,812 - INFO - Request Parameters - Page 3:
2025-05-02 21:00:23,812 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:00:23,812 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:00:24,297 - INFO - Response - Page 3:
2025-05-02 21:00:24,500 - INFO - 第 3 页获取到 100 条记录
2025-05-02 21:00:24,500 - INFO - Request Parameters - Page 4:
2025-05-02 21:00:24,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:00:24,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:00:24,953 - INFO - Response - Page 4:
2025-05-02 21:00:25,156 - INFO - 第 4 页获取到 100 条记录
2025-05-02 21:00:25,156 - INFO - Request Parameters - Page 5:
2025-05-02 21:00:25,156 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:00:25,156 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:00:25,640 - INFO - Response - Page 5:
2025-05-02 21:00:25,843 - INFO - 第 5 页获取到 100 条记录
2025-05-02 21:00:25,843 - INFO - Request Parameters - Page 6:
2025-05-02 21:00:25,843 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:00:25,843 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:00:26,234 - INFO - Response - Page 6:
2025-05-02 21:00:26,437 - INFO - 第 6 页获取到 55 条记录
2025-05-02 21:00:26,437 - INFO - 查询完成，共获取到 555 条记录
2025-05-02 21:00:26,437 - INFO - 获取到 555 条表单数据
2025-05-02 21:00:26,437 - INFO - 当前日期 2025-05 有 555 条MySQL数据需要处理
2025-05-02 21:00:26,437 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-02 21:00:26,890 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-02 21:00:26,890 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3096.0, 'new_value': 10545.0}, {'field': 'offline_amount', 'old_value': 6800.0, 'new_value': 9832.0}, {'field': 'total_amount', 'old_value': 9896.0, 'new_value': 20377.0}, {'field': 'order_count', 'old_value': 188, 'new_value': 405}]
2025-05-02 21:00:26,906 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-02 21:00:27,422 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-02 21:00:27,422 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17329.0, 'new_value': 49049.0}, {'field': 'total_amount', 'old_value': 17329.0, 'new_value': 49049.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 10}]
2025-05-02 21:00:27,422 - INFO - 日期 2025-05 处理完成 - 更新: 2 条，插入: 0 条，错误: 0 条
2025-05-02 21:00:27,422 - INFO - 数据同步完成！更新: 2 条，插入: 0 条，错误: 0 条
2025-05-02 21:00:27,422 - INFO - =================同步完成====================
