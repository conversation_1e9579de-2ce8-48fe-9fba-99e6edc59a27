2025-06-02 12:09:44,363 - INFO - ==================================================
2025-06-02 12:09:44,364 - INFO - 程序启动 - 版本 v1.0.0
2025-06-02 12:09:44,364 - INFO - 日志文件: logs\sync_data_shuyan2yida_********.log
2025-06-02 12:09:44,364 - INFO - ==================================================
2025-06-02 12:09:44,368 - INFO - 程序入口点: __main__
2025-06-02 12:09:44,368 - INFO - ==================================================
2025-06-02 12:09:44,368 - INFO - 程序启动 - 版本 v1.0.1
2025-06-02 12:09:44,369 - INFO - 日志文件: logs\sync_data_shuyan2yida_********.log
2025-06-02 12:09:44,369 - INFO - ==================================================
2025-06-02 12:09:44,875 - INFO - 数据库文件已存在: data\sales_data.db
2025-06-02 12:09:44,876 - INFO - sales_data表已存在，无需创建
2025-06-02 12:09:44,877 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-06-02 12:09:44,877 - INFO - DataSyncManager初始化完成
2025-06-02 12:09:44,877 - INFO - 接收到命令行参数: ['********', '********']
2025-06-02 12:09:44,877 - INFO - 使用指定的日期范围: ******** 至 ********
2025-06-02 12:09:44,878 - INFO - 开始执行综合数据同步，参数: start_date=********, end_date=********
2025-06-02 12:09:44,878 - INFO - 开始综合数据同步流程...
2025-06-02 12:09:44,878 - INFO - 正在获取数衍平台日销售数据...
2025-06-02 12:09:44,879 - INFO - 查询数衍平台数据，时间段为: 2025-03-16, 2025-06-02
2025-06-02 12:09:44,879 - INFO - 正在获取********至********的数据
2025-06-02 12:09:44,879 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:09:44,879 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '********120944', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'FF5A93475857F427302C320C84BD7E82'}
2025-06-02 12:09:47,782 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:09:47,785 - INFO - 过滤后保留 443 条记录
2025-06-02 12:09:49,801 - INFO - 正在获取********至********的数据
2025-06-02 12:09:49,801 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:09:49,801 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '********120949', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '936DABB5FC8575272799B9CF59109067'}
2025-06-02 12:09:52,075 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:09:52,092 - INFO - 过滤后保留 437 条记录
2025-06-02 12:09:54,100 - INFO - 正在获取********至********的数据
2025-06-02 12:09:54,100 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:09:54,100 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '********120954', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '66B39C3C477B74D4E2B56BE67D29B578'}
2025-06-02 12:09:55,882 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:09:55,885 - INFO - 过滤后保留 449 条记录
2025-06-02 12:09:57,887 - INFO - 正在获取********至********的数据
2025-06-02 12:09:57,887 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:09:57,888 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '********120957', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '8E711194266E479A1C632AC0E825714C'}
2025-06-02 12:09:59,700 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:09:59,715 - INFO - 过滤后保留 452 条记录
2025-06-02 12:10:01,716 - INFO - 正在获取********至********的数据
2025-06-02 12:10:01,716 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:10:01,716 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '********121001', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'E568F8EFC17260691CB0A0DC0776D6D8'}
2025-06-02 12:10:03,277 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:10:03,281 - INFO - 过滤后保留 442 条记录
2025-06-02 12:10:05,281 - INFO - 正在获取********至********的数据
2025-06-02 12:10:05,281 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:10:05,281 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '********121005', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'EB1A3FF2500A4625778BCE8601C3BA5F'}
2025-06-02 12:10:07,022 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:10:07,022 - INFO - 过滤后保留 449 条记录
2025-06-02 12:10:09,035 - INFO - 正在获取********至********的数据
2025-06-02 12:10:09,035 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:10:09,035 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '********121009', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '92CE8CCF08C8710D265F3CD2FB483E21'}
2025-06-02 12:10:10,581 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:10:10,581 - INFO - 过滤后保留 446 条记录
2025-06-02 12:10:12,594 - INFO - 正在获取********至********的数据
2025-06-02 12:10:12,594 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:10:12,594 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '********121012', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'AE0E58F7F4583C17AED2FA1A268A73D4'}
2025-06-02 12:10:14,078 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:10:14,078 - INFO - 过滤后保留 441 条记录
2025-06-02 12:10:16,079 - INFO - 正在获取********至********的数据
2025-06-02 12:10:16,079 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:10:16,079 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '********121016', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '3CF9041956E56F653D0A9FB68153FA40'}
2025-06-02 12:10:17,845 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:10:17,861 - INFO - 过滤后保留 431 条记录
2025-06-02 12:10:19,875 - INFO - 正在获取********至********的数据
2025-06-02 12:10:19,875 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:10:19,875 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '3E4EFC4EE918AD4A85E4738D897C4570'}
2025-06-02 12:10:21,311 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:10:21,311 - INFO - 过滤后保留 434 条记录
2025-06-02 12:10:23,326 - INFO - 正在获取********至********的数据
2025-06-02 12:10:23,326 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:10:23,326 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '9EE3D4AD4AF28E5BEECC6B22B1582704'}
2025-06-02 12:10:24,747 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:10:24,747 - INFO - 过滤后保留 431 条记录
2025-06-02 12:10:26,761 - INFO - 正在获取********至********的数据
2025-06-02 12:10:26,761 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:10:26,761 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'C419E9EBF3B56C820B2ADF5A6288549D'}
2025-06-02 12:10:28,073 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:10:28,073 - INFO - 过滤后保留 426 条记录
2025-06-02 12:10:30,087 - INFO - 正在获取********至********的数据
2025-06-02 12:10:30,087 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:10:30,087 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '2198629B66708FA19492BCB5CA11A902'}
2025-06-02 12:10:31,430 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:10:31,446 - INFO - 过滤后保留 423 条记录
2025-06-02 12:10:33,459 - INFO - 正在获取********至********的数据
2025-06-02 12:10:33,459 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:10:33,459 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'D8987FA57E80C47345546B33E167CE20'}
2025-06-02 12:10:34,986 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:10:34,986 - INFO - 过滤后保留 432 条记录
2025-06-02 12:10:37,001 - INFO - 正在获取********至********的数据
2025-06-02 12:10:37,001 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:10:37,001 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '0537BF6ACCEB48A8DCE126F83C9D44A7'}
2025-06-02 12:10:38,640 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:10:38,640 - INFO - 过滤后保留 434 条记录
2025-06-02 12:10:40,656 - INFO - 正在获取********至********的数据
2025-06-02 12:10:40,656 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:10:40,656 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '99012BD5CA9FB63E7B7F58B4AB6DEBDA'}
2025-06-02 12:10:42,326 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:10:42,342 - INFO - 过滤后保留 424 条记录
2025-06-02 12:10:44,357 - INFO - 正在获取********至********的数据
2025-06-02 12:10:44,357 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:10:44,357 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '82EB48601DDEE2DA8C23C04D11E43185'}
2025-06-02 12:10:46,494 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:10:46,494 - INFO - 过滤后保留 436 条记录
2025-06-02 12:10:48,508 - INFO - 正在获取********至********的数据
2025-06-02 12:10:48,508 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:10:48,508 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'C869AE931705464B2EF8B3DEC0F88EE4'}
2025-06-02 12:10:50,286 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:10:50,286 - INFO - 过滤后保留 431 条记录
2025-06-02 12:10:52,301 - INFO - 正在获取********至********的数据
2025-06-02 12:10:52,301 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:10:52,301 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '********************************'}
2025-06-02 12:10:54,268 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:10:54,268 - INFO - 过滤后保留 425 条记录
2025-06-02 12:10:56,282 - INFO - 正在获取********至********的数据
2025-06-02 12:10:56,282 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:10:56,282 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '64D967139C68E062958CF32141D28E7A'}
2025-06-02 12:10:58,138 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:10:58,138 - INFO - 过滤后保留 414 条记录
2025-06-02 12:11:00,152 - INFO - 正在获取********至********的数据
2025-06-02 12:11:00,152 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:11:00,152 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'F88ED9AD9CDD5BC57D47076B302A8DAF'}
2025-06-02 12:11:01,917 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:11:01,917 - INFO - 过滤后保留 427 条记录
2025-06-02 12:11:03,931 - INFO - 正在获取********至********的数据
2025-06-02 12:11:03,931 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:11:03,931 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '75EDE268642F5566F355A9F1298048E7'}
2025-06-02 12:11:05,681 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:11:05,681 - INFO - 过滤后保留 428 条记录
2025-06-02 12:11:07,694 - INFO - 正在获取********至********的数据
2025-06-02 12:11:07,694 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:11:07,694 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '3A4813DE40CE29EC7E88A07A7607B49C'}
2025-06-02 12:11:09,428 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:11:09,428 - INFO - 过滤后保留 429 条记录
2025-06-02 12:11:11,442 - INFO - 正在获取********至********的数据
2025-06-02 12:11:11,442 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:11:11,442 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '********121111', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'DEF62E21E6333F06F220C69441AD5150'}
2025-06-02 12:11:13,113 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:11:13,113 - INFO - 过滤后保留 423 条记录
2025-06-02 12:11:15,128 - INFO - 正在获取********至********的数据
2025-06-02 12:11:15,128 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:11:15,128 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '********121115', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'AADACEF5F5131CBC9991679A8E001682'}
2025-06-02 12:11:17,064 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:11:17,068 - INFO - 过滤后保留 426 条记录
2025-06-02 12:11:19,075 - INFO - 正在获取********至********的数据
2025-06-02 12:11:19,075 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:11:19,075 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '********121119', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '52AA0E8EE477436CE3284254986FD0D9'}
2025-06-02 12:11:20,555 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:11:20,571 - INFO - 过滤后保留 411 条记录
2025-06-02 12:11:22,587 - INFO - 正在获取********至********的数据
2025-06-02 12:11:22,587 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:11:22,588 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '********121122', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'C0E45F1A2E4AE6890A8F43AF36506AF6'}
2025-06-02 12:11:24,248 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:11:24,252 - INFO - 过滤后保留 413 条记录
2025-06-02 12:11:26,253 - INFO - 正在获取********至********的数据
2025-06-02 12:11:26,253 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:11:26,254 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '********121126', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'A5832FBB590FAF1FAEF1F31CE6753ABC'}
2025-06-02 12:11:27,811 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:11:27,815 - INFO - 过滤后保留 432 条记录
2025-06-02 12:11:29,816 - INFO - 正在获取********至********的数据
2025-06-02 12:11:29,816 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:11:29,816 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '********121129', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '0F05D3DBE8B7423D5925D89313C11C1F'}
2025-06-02 12:11:31,347 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:11:31,347 - INFO - 过滤后保留 431 条记录
2025-06-02 12:11:33,356 - INFO - 正在获取********至********的数据
2025-06-02 12:11:33,356 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:11:33,356 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '********121133', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '5DE6BECA79088DEF99D43CD903CF2E2E'}
2025-06-02 12:11:34,787 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:11:34,791 - INFO - 过滤后保留 417 条记录
2025-06-02 12:11:36,793 - INFO - 正在获取********至********的数据
2025-06-02 12:11:36,793 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:11:36,793 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '********121136', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '1C0BB8B19FAA451E4E82EACE95C2D0B1'}
2025-06-02 12:11:38,459 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:11:38,459 - INFO - 过滤后保留 420 条记录
2025-06-02 12:11:40,473 - INFO - 正在获取********至********的数据
2025-06-02 12:11:40,473 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:11:40,473 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '********121140', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '1E0FB1EBF312937A851453C45D682654'}
2025-06-02 12:11:42,395 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:11:42,410 - INFO - 过滤后保留 431 条记录
2025-06-02 12:11:44,412 - INFO - 正在获取********至********的数据
2025-06-02 12:11:44,412 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:11:44,413 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '********121144', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '298A2837B0AEBA2F571A60E396AAC00C'}
2025-06-02 12:11:45,774 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:11:45,778 - INFO - 过滤后保留 423 条记录
2025-06-02 12:11:47,793 - INFO - 正在获取********至********的数据
2025-06-02 12:11:47,793 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:11:47,793 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '********121147', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '161FB4F47BAAF87F747CCD6236608C30'}
2025-06-02 12:11:49,668 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:11:49,683 - INFO - 过滤后保留 416 条记录
2025-06-02 12:11:51,698 - INFO - 正在获取********至********的数据
2025-06-02 12:11:51,698 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:11:51,698 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '********121151', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'B0D050F3A74C717865B54B9DFD4CA8BE'}
2025-06-02 12:11:53,528 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:11:53,528 - INFO - 过滤后保留 423 条记录
2025-06-02 12:11:55,532 - INFO - 正在获取********至********的数据
2025-06-02 12:11:55,532 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:11:55,532 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '********121155', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '9FAADE1054841EA20E992C63A2C96BDD'}
2025-06-02 12:11:57,563 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:11:57,580 - INFO - 过滤后保留 414 条记录
2025-06-02 12:11:59,591 - INFO - 正在获取********至********的数据
2025-06-02 12:11:59,591 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:11:59,591 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '********121159', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'E697A8018615CB3DD58B4AB9267C0EEB'}
2025-06-02 12:12:01,239 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:12:01,243 - INFO - 过滤后保留 413 条记录
2025-06-02 12:12:03,245 - INFO - 正在获取********至********的数据
2025-06-02 12:12:03,245 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:12:03,245 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '********121203', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'A9A688655A013E4FEA774969C7590853'}
2025-06-02 12:12:04,821 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:12:04,821 - INFO - 过滤后保留 414 条记录
2025-06-02 12:12:06,822 - INFO - 正在获取********至********的数据
2025-06-02 12:12:06,822 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:12:06,822 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '********121206', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'F2E40D3765EA553668153264720D27C4'}
2025-06-02 12:12:08,733 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:12:08,737 - INFO - 过滤后保留 412 条记录
2025-06-02 12:12:10,738 - INFO - 正在获取********至********的数据
2025-06-02 12:12:10,738 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-02 12:12:10,738 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '********121210', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '7205F691E9A7FEC363131D6241D61634'}
2025-06-02 12:12:11,641 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-02 12:12:11,641 - INFO - 过滤后保留 146 条记录
2025-06-02 12:12:13,656 - INFO - 开始保存数据到SQLite数据库，共 16849 条记录待处理
2025-06-02 12:12:13,656 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT72H2L6227Q2OVBN4IS62001D24, sale_time=2025-03-17
2025-06-02 12:12:13,656 - INFO - 变更字段: amount: 441 -> 705, count: 3 -> 4, instore_amount: 441.3 -> 705.3, instore_count: 3 -> 4
2025-06-02 12:12:13,688 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOM98AG2E0I86N3H2U1V8001E8E, sale_time=2025-03-19
2025-06-02 12:12:13,688 - INFO - 变更字段: recommend_amount: 4312.5 -> 5068.5, amount: 4312 -> 5068, count: 26 -> 27, instore_amount: 5068.5 -> 5824.5, instore_count: 26 -> 27
2025-06-02 12:12:13,688 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOM98AG2E0I86N3H2U1V8001E8E, sale_time=2025-03-18
2025-06-02 12:12:13,688 - INFO - 变更字段: recommend_amount: 14300.3 -> 14902.1, amount: 14300 -> 14902, count: 35 -> 36, instore_amount: 14509.6 -> 15111.4, instore_count: 35 -> 36
2025-06-02 12:12:13,705 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIS7OTCALF7AV8LHQQGIDO001EK1, sale_time=2025-03-18
2025-06-02 12:12:13,706 - INFO - 变更字段: amount: 701 -> 1624, count: 3 -> 4, instore_amount: 694.0 -> 1617.0, instore_count: 2 -> 3
2025-06-02 12:12:14,171 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=F56E8E23D2584556A30D1378611DF4AE, sale_time=2025-05-08
2025-06-02 12:12:14,171 - INFO - 变更字段: amount: 1595 -> 1891, count: 6 -> 7, instore_amount: 1595.6 -> 1891.6, instore_count: 6 -> 7
2025-06-02 12:12:14,396 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCE13J2R9CI0I86N3H2U13D001ECJ, sale_time=2025-05-30
2025-06-02 12:12:14,397 - INFO - 变更字段: amount: 11834 -> 13160, count: 8 -> 9, instore_amount: 11834.0 -> 13160.0, instore_count: 8 -> 9
2025-06-02 12:12:14,407 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H9P9UCRQKEOIF52ASKKUBQUNH0018FA, sale_time=2025-06-01
2025-06-02 12:12:14,407 - INFO - 变更字段: amount: 10049 -> 10522, count: 39 -> 40, instore_amount: 10261.0 -> 10734.0, instore_count: 39 -> 40
2025-06-02 12:12:14,410 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H9P9S64J8E8R652ASKKUBQUMU0018EN, sale_time=2025-06-01
2025-06-02 12:12:14,410 - INFO - 变更字段: amount: 4347 -> 4426, count: 28 -> 29, instore_amount: 3683.5 -> 3762.7, instore_count: 22 -> 23
2025-06-02 12:12:14,411 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H9P9PGR3703D752ASKKUBQUM50018DU, sale_time=2025-06-01
2025-06-02 12:12:14,411 - INFO - 变更字段: daily_bill_amount: 1909.0 -> 4185.77
2025-06-02 12:12:14,414 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S, sale_time=2025-06-01
2025-06-02 12:12:14,415 - INFO - 变更字段: recommend_amount: 11890.62 -> 12039.62, amount: 11890 -> 12039, count: 246 -> 247, instore_amount: 11398.16 -> 11547.16, instore_count: 222 -> 223
2025-06-02 12:12:14,415 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTCIR7D5JD7Q2OVBN4IS7U001D40, sale_time=2025-06-01
2025-06-02 12:12:14,416 - INFO - 变更字段: recommend_amount: 0.0 -> 6190.4, daily_bill_amount: 0.0 -> 6190.4
2025-06-02 12:12:14,418 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-06-01
2025-06-02 12:12:14,418 - INFO - 变更字段: recommend_amount: 3485.92 -> 3506.02, amount: 3485 -> 3506, count: 217 -> 218, online_amount: 2904.1 -> 2924.2, online_count: 181 -> 182
2025-06-02 12:12:14,419 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEKSIE6DGB42F6DB81RH77001P3F, sale_time=2025-06-01
2025-06-02 12:12:14,419 - INFO - 变更字段: recommend_amount: 0.0 -> 216.0, daily_bill_amount: 0.0 -> 216.0
2025-06-02 12:12:14,420 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=B5A5FB25D4B04323BCABB528AF5E427E, sale_time=2025-06-01
2025-06-02 12:12:14,420 - INFO - 变更字段: recommend_amount: 3025.31 -> 3053.31, amount: 3025 -> 3053, count: 154 -> 155, instore_amount: 1823.6 -> 1851.6, instore_count: 82 -> 83
2025-06-02 12:12:14,421 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1IFRR231GUB4QN7QBECDAL3H28001J69, sale_time=2025-06-01
2025-06-02 12:12:14,421 - INFO - 变更字段: amount: 16268 -> 16272, count: 263 -> 264, instore_amount: 16310.8 -> 16315.2, instore_count: 263 -> 264
2025-06-02 12:12:14,422 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEAIN5DMKK0I86N3H2U1VH001F8N, sale_time=2025-06-01
2025-06-02 12:12:14,422 - INFO - 变更字段: amount: -32287 -> -32282, count: 69 -> 70, online_amount: 931.46 -> 937.06, online_count: 29 -> 30
2025-06-02 12:12:14,423 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE7UKVCV6D0I86N3H2U1U5001F7B, sale_time=2025-06-01
2025-06-02 12:12:14,423 - INFO - 变更字段: amount: 60651 -> 61656, count: 367 -> 371, instore_amount: 44016.3 -> 45021.3, instore_count: 218 -> 222
2025-06-02 12:12:14,424 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE39GOJVP40I86N3H2U1RR001F51, sale_time=2025-06-01
2025-06-02 12:12:14,425 - INFO - 变更字段: amount: 74740 -> 79568, count: 327 -> 340, instore_amount: 64373.91 -> 69202.04, instore_count: 233 -> 246
2025-06-02 12:12:14,425 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE2CVHLBFV0I86N3H2U1RH001F4N, sale_time=2025-06-01
2025-06-02 12:12:14,425 - INFO - 变更字段: amount: 34147 -> 34576, count: 175 -> 176, instore_amount: 34315.5 -> 34744.4, instore_count: 175 -> 176
2025-06-02 12:12:14,425 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE25DAIM3B0I86N3H2U1RD001F4J, sale_time=2025-06-01
2025-06-02 12:12:14,426 - INFO - 变更字段: amount: 26156 -> 26361, count: 150 -> 151, online_amount: 1790.4 -> 1995.0, online_count: 27 -> 28
2025-06-02 12:12:14,426 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE0VODGC6J0I86N3H2U1QP001F3V, sale_time=2025-06-01
2025-06-02 12:12:14,427 - INFO - 变更字段: recommend_amount: 1169.0 -> 1325.0, amount: 1169 -> 1325, count: 28 -> 32, instore_amount: 1169.0 -> 1325.0, instore_count: 28 -> 32
2025-06-02 12:12:14,427 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE0A2N9KG60I86N3H2U1QB001F3H, sale_time=2025-06-01
2025-06-02 12:12:14,427 - INFO - 变更字段: amount: 7513 -> 7491, count: 388 -> 389, instore_amount: 5719.92 -> 5697.42, instore_count: 282 -> 283
2025-06-02 12:12:14,428 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVBILA3OJ0I86N3H2U1PQ001F30, sale_time=2025-06-01
2025-06-02 12:12:14,428 - INFO - 变更字段: amount: 45659 -> 45661, count: 334 -> 335, online_amount: 4224.74 -> 4226.54, online_count: 141 -> 142
2025-06-02 12:12:14,428 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDUSSKMEM60I86N3H2U1PI001F2O, sale_time=2025-06-01
2025-06-02 12:12:14,428 - INFO - 变更字段: amount: 952 -> 3646, count: 73 -> 258, instore_amount: 965.0 -> 3753.18, instore_count: 73 -> 258
2025-06-02 12:12:14,429 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3, sale_time=2025-06-01
2025-06-02 12:12:14,429 - INFO - 变更字段: amount: 6081 -> 6861, count: 66 -> 75, instore_amount: 4193.7 -> 4973.2, instore_count: 43 -> 52
2025-06-02 12:12:14,430 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDF8HFHI690I86N3H2U1H9001EQF, sale_time=2025-06-01
2025-06-02 12:12:14,430 - INFO - 变更字段: amount: 815 -> 1110, count: 5 -> 6, instore_amount: 1006.0 -> 1301.0, instore_count: 5 -> 6
2025-06-02 12:12:14,431 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCE9H11376450I86N3H2U19G001EIM, sale_time=2025-06-01
2025-06-02 12:12:14,431 - INFO - 变更字段: recommend_amount: 1105.0 -> 1796.0, amount: 1105 -> 1796, count: 2 -> 3, instore_amount: 1105.0 -> 1796.0, instore_count: 2 -> 3
2025-06-02 12:12:14,432 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCE9D9OBM41R0I86N3H2U17K001EGQ, sale_time=2025-06-01
2025-06-02 12:12:14,432 - INFO - 变更字段: recommend_amount: 0.0 -> 16137.6, daily_bill_amount: 0.0 -> 16137.6, amount: 2045 -> 16972, count: 6 -> 29, instore_amount: 2045.5 -> 16972.11, instore_count: 6 -> 29
2025-06-02 12:12:14,432 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCE13J2R9CI0I86N3H2U13D001ECJ, sale_time=2025-06-01
2025-06-02 12:12:14,433 - INFO - 变更字段: amount: 21607 -> 22933, count: 20 -> 21, instore_amount: 23709.0 -> 25035.0, instore_count: 20 -> 21
2025-06-02 12:12:14,433 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDUDM4PLNS0I86N3H2U121001EB7, sale_time=2025-06-01
2025-06-02 12:12:14,433 - INFO - 变更字段: amount: 13451 -> 13675, count: 449 -> 450, online_amount: 5588.7 -> 5812.4, online_count: 82 -> 83
2025-06-02 12:12:14,433 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDU5QKVU3D0I86N3H2U11T001EB3, sale_time=2025-06-01
2025-06-02 12:12:14,433 - INFO - 变更字段: amount: 5493 -> 5557, count: 422 -> 436, online_amount: 5289.92 -> 5353.42, online_count: 384 -> 398
2025-06-02 12:12:14,433 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDU5QKVU3D0I86N3H2U11T001EB3, sale_time=2025-05-31
2025-06-02 12:12:14,433 - INFO - 变更字段: amount: 5402 -> 5407, count: 454 -> 459, online_amount: 5572.92 -> 5577.94, online_count: 423 -> 428
2025-06-02 12:12:14,433 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-06-01
2025-06-02 12:12:14,433 - INFO - 变更字段: amount: 7807 -> 7849, count: 500 -> 501, instore_amount: 6132.34 -> 6186.85, instore_count: 328 -> 332, online_amount: 1921.36 -> 1908.65, online_count: 172 -> 169
2025-06-02 12:12:14,433 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-05-31
2025-06-02 12:12:14,433 - INFO - 变更字段: instore_amount: 5435.73 -> 5437.73, instore_count: 335 -> 336, online_amount: 2288.09 -> 2286.09, online_count: 159 -> 158
2025-06-02 12:12:14,433 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I, sale_time=2025-06-01
2025-06-02 12:12:14,433 - INFO - 变更字段: recommend_amount: 0.0 -> 102541.97, daily_bill_amount: 0.0 -> 102541.97, amount: 87231 -> 87306, count: 1456 -> 1457, online_amount: 3939.1 -> 4014.09, online_count: 65 -> 66
2025-06-02 12:12:14,439 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R, sale_time=2025-05-31
2025-06-02 12:12:14,439 - INFO - 变更字段: recommend_amount: 4051.47 -> 4047.97, amount: 4051 -> 4047
2025-06-02 12:12:14,440 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR, sale_time=2025-06-01
2025-06-02 12:12:14,444 - INFO - 变更字段: amount: 52222 -> 54942, count: 254 -> 261, instore_amount: 51499.75 -> 54219.65, instore_count: 206 -> 213
2025-06-02 12:12:14,445 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE38FF3LOL6AJB6QM8HA820011R0, sale_time=2025-06-01
2025-06-02 12:12:14,445 - INFO - 变更字段: amount: 34587 -> 36342, count: 48 -> 49, instore_amount: 34222.0 -> 35977.0, instore_count: 47 -> 48
2025-06-02 12:12:14,446 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE29O5UH0D6AJB6QM8HA7I0011QG, sale_time=2025-06-01
2025-06-02 12:12:14,446 - INFO - 变更字段: amount: 62244 -> 62867, count: 159 -> 160, instore_amount: 67540.0 -> 68163.0, instore_count: 159 -> 160
2025-06-02 12:12:14,447 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUISOVAPU1P7AV8LHQQGIDU001EK7, sale_time=2025-06-01
2025-06-02 12:12:14,447 - INFO - 变更字段: amount: 3715 -> 7880, count: 6 -> 7, instore_amount: 3455.0 -> 7620.09, instore_count: 5 -> 6
2025-06-02 12:12:14,448 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIS7OTCALF7AV8LHQQGIDO001EK1, sale_time=2025-06-01
2025-06-02 12:12:14,448 - INFO - 变更字段: amount: 35466 -> 39018, count: 198 -> 202, instore_amount: 32830.59 -> 36382.39, instore_count: 175 -> 179
2025-06-02 12:12:14,594 - INFO - SQLite数据保存完成，统计信息：
2025-06-02 12:12:14,595 - INFO - - 总记录数: 16849
2025-06-02 12:12:14,595 - INFO - - 成功插入: 147
2025-06-02 12:12:14,596 - INFO - - 成功更新: 41
2025-06-02 12:12:14,596 - INFO - - 无需更新: 16661
2025-06-02 12:12:14,596 - INFO - - 处理失败: 0
2025-06-02 12:12:21,858 - INFO - 数据已保存到Excel文件: logs\数衍平台数据导出_********.xlsx
2025-06-02 12:12:21,874 - INFO - 成功获取数衍平台数据，共 16849 条记录
2025-06-02 12:12:21,874 - INFO - 正在更新SQLite月度汇总数据...
2025-06-02 12:12:21,874 - INFO - 月度数据sqllite清空完成
2025-06-02 12:12:22,116 - INFO - 月度汇总数据更新完成，处理了 1396 条汇总记录
2025-06-02 12:12:22,116 - INFO - 成功更新月度汇总数据，共 1396 条记录
2025-06-02 12:12:22,116 - INFO - 正在获取宜搭日销售表单数据...
2025-06-02 12:12:22,117 - INFO - 开始获取宜搭每日表单数据，总时间范围: 2025-03-16 00:00:00 至 2025-06-02 23:59:59
2025-06-02 12:12:22,117 - INFO - 查询分段 1: 2025-03-16 至 2025-03-17
2025-06-02 12:12:22,117 - INFO - 查询日期范围: 2025-03-16 至 2025-03-17，使用分页查询，每页 100 条记录
2025-06-02 12:12:22,117 - INFO - Request Parameters - Page 1:
2025-06-02 12:12:22,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:12:22,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742054400000, 1742140800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:12:24,678 - INFO - API请求耗时: 2560ms
2025-06-02 12:12:24,679 - INFO - Response - Page 1
2025-06-02 12:12:24,679 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:12:24,679 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:12:24,680 - WARNING - 分段 1 查询返回空数据
2025-06-02 12:12:25,681 - INFO - 查询分段 2: 2025-03-18 至 2025-03-19
2025-06-02 12:12:25,681 - INFO - 查询日期范围: 2025-03-18 至 2025-03-19，使用分页查询，每页 100 条记录
2025-06-02 12:12:25,681 - INFO - Request Parameters - Page 1:
2025-06-02 12:12:25,682 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:12:25,682 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742227200000, 1742313600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:12:25,983 - INFO - API请求耗时: 301ms
2025-06-02 12:12:25,983 - INFO - Response - Page 1
2025-06-02 12:12:25,983 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:12:25,984 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:12:25,984 - WARNING - 分段 2 查询返回空数据
2025-06-02 12:12:26,985 - INFO - 查询分段 3: 2025-03-20 至 2025-03-21
2025-06-02 12:12:26,985 - INFO - 查询日期范围: 2025-03-20 至 2025-03-21，使用分页查询，每页 100 条记录
2025-06-02 12:12:26,986 - INFO - Request Parameters - Page 1:
2025-06-02 12:12:26,986 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:12:26,986 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742400000000, 1742486400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:12:27,275 - INFO - API请求耗时: 288ms
2025-06-02 12:12:27,275 - INFO - Response - Page 1
2025-06-02 12:12:27,275 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:12:27,276 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:12:27,276 - WARNING - 分段 3 查询返回空数据
2025-06-02 12:12:28,276 - INFO - 查询分段 4: 2025-03-22 至 2025-03-23
2025-06-02 12:12:28,276 - INFO - 查询日期范围: 2025-03-22 至 2025-03-23，使用分页查询，每页 100 条记录
2025-06-02 12:12:28,276 - INFO - Request Parameters - Page 1:
2025-06-02 12:12:28,277 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:12:28,277 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742572800000, 1742659200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:12:28,548 - INFO - API请求耗时: 272ms
2025-06-02 12:12:28,548 - INFO - Response - Page 1
2025-06-02 12:12:28,548 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:12:28,548 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:12:28,548 - WARNING - 分段 4 查询返回空数据
2025-06-02 12:12:29,560 - INFO - 查询分段 5: 2025-03-24 至 2025-03-25
2025-06-02 12:12:29,560 - INFO - 查询日期范围: 2025-03-24 至 2025-03-25，使用分页查询，每页 100 条记录
2025-06-02 12:12:29,560 - INFO - Request Parameters - Page 1:
2025-06-02 12:12:29,560 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:12:29,560 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600000, 1742832000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:12:29,907 - INFO - API请求耗时: 347ms
2025-06-02 12:12:29,908 - INFO - Response - Page 1
2025-06-02 12:12:29,908 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:12:29,908 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:12:29,908 - WARNING - 分段 5 查询返回空数据
2025-06-02 12:12:30,911 - INFO - 查询分段 6: 2025-03-26 至 2025-03-27
2025-06-02 12:12:30,911 - INFO - 查询日期范围: 2025-03-26 至 2025-03-27，使用分页查询，每页 100 条记录
2025-06-02 12:12:30,911 - INFO - Request Parameters - Page 1:
2025-06-02 12:12:30,911 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:12:30,911 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400000, 1743004800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:12:31,206 - INFO - API请求耗时: 295ms
2025-06-02 12:12:31,206 - INFO - Response - Page 1
2025-06-02 12:12:31,206 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:12:31,206 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:12:31,206 - WARNING - 分段 6 查询返回空数据
2025-06-02 12:12:32,220 - INFO - 查询分段 7: 2025-03-28 至 2025-03-29
2025-06-02 12:12:32,220 - INFO - 查询日期范围: 2025-03-28 至 2025-03-29，使用分页查询，每页 100 条记录
2025-06-02 12:12:32,220 - INFO - Request Parameters - Page 1:
2025-06-02 12:12:32,220 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:12:32,220 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200000, 1743177600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:12:32,547 - INFO - API请求耗时: 327ms
2025-06-02 12:12:32,547 - INFO - Response - Page 1
2025-06-02 12:12:32,547 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:12:32,547 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:12:32,547 - WARNING - 分段 7 查询返回空数据
2025-06-02 12:12:33,548 - INFO - 查询分段 8: 2025-03-30 至 2025-03-31
2025-06-02 12:12:33,548 - INFO - 查询日期范围: 2025-03-30 至 2025-03-31，使用分页查询，每页 100 条记录
2025-06-02 12:12:33,548 - INFO - Request Parameters - Page 1:
2025-06-02 12:12:33,549 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:12:33,552 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000000, 1743350400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:12:33,879 - INFO - API请求耗时: 327ms
2025-06-02 12:12:33,879 - INFO - Response - Page 1
2025-06-02 12:12:33,880 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:12:33,880 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:12:33,880 - WARNING - 分段 8 查询返回空数据
2025-06-02 12:12:34,881 - INFO - 查询分段 9: 2025-04-01 至 2025-04-02
2025-06-02 12:12:34,881 - INFO - 查询日期范围: 2025-04-01 至 2025-04-02，使用分页查询，每页 100 条记录
2025-06-02 12:12:34,881 - INFO - Request Parameters - Page 1:
2025-06-02 12:12:34,881 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:12:34,882 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800000, 1743523200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:12:35,692 - INFO - API请求耗时: 809ms
2025-06-02 12:12:35,692 - INFO - Response - Page 1
2025-06-02 12:12:35,692 - INFO - 第 1 页获取到 100 条记录
2025-06-02 12:12:36,192 - INFO - Request Parameters - Page 2:
2025-06-02 12:12:36,192 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:12:36,192 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800000, 1743523200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:12:37,516 - INFO - API请求耗时: 1323ms
2025-06-02 12:12:37,516 - INFO - Response - Page 2
2025-06-02 12:12:37,516 - INFO - 第 2 页获取到 100 条记录
2025-06-02 12:12:38,016 - INFO - Request Parameters - Page 3:
2025-06-02 12:12:38,016 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:12:38,016 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800000, 1743523200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:12:38,405 - INFO - API请求耗时: 388ms
2025-06-02 12:12:38,405 - INFO - Response - Page 3
2025-06-02 12:12:38,405 - INFO - 第 3 页获取到 12 条记录
2025-06-02 12:12:38,405 - INFO - 查询完成，共获取到 212 条记录
2025-06-02 12:12:38,406 - INFO - 分段 9 查询成功，获取到 212 条记录
2025-06-02 12:12:39,408 - INFO - 查询分段 10: 2025-04-03 至 2025-04-04
2025-06-02 12:12:39,408 - INFO - 查询日期范围: 2025-04-03 至 2025-04-04，使用分页查询，每页 100 条记录
2025-06-02 12:12:39,408 - INFO - Request Parameters - Page 1:
2025-06-02 12:12:39,408 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:12:39,408 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600000, 1743696000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:12:39,721 - INFO - API请求耗时: 313ms
2025-06-02 12:12:39,722 - INFO - Response - Page 1
2025-06-02 12:12:39,722 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:12:39,722 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:12:39,722 - WARNING - 分段 10 查询返回空数据
2025-06-02 12:12:40,723 - INFO - 查询分段 11: 2025-04-05 至 2025-04-06
2025-06-02 12:12:40,723 - INFO - 查询日期范围: 2025-04-05 至 2025-04-06，使用分页查询，每页 100 条记录
2025-06-02 12:12:40,724 - INFO - Request Parameters - Page 1:
2025-06-02 12:12:40,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:12:40,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743782400000, 1743868800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:12:41,031 - INFO - API请求耗时: 303ms
2025-06-02 12:12:41,032 - INFO - Response - Page 1
2025-06-02 12:12:41,032 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:12:41,032 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:12:41,032 - WARNING - 分段 11 查询返回空数据
2025-06-02 12:12:42,036 - INFO - 查询分段 12: 2025-04-07 至 2025-04-08
2025-06-02 12:12:42,036 - INFO - 查询日期范围: 2025-04-07 至 2025-04-08，使用分页查询，每页 100 条记录
2025-06-02 12:12:42,036 - INFO - Request Parameters - Page 1:
2025-06-02 12:12:42,036 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:12:42,036 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200000, 1744041600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:12:42,393 - INFO - API请求耗时: 357ms
2025-06-02 12:12:42,393 - INFO - Response - Page 1
2025-06-02 12:12:42,393 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:12:42,393 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:12:42,393 - WARNING - 分段 12 查询返回空数据
2025-06-02 12:12:43,405 - INFO - 查询分段 13: 2025-04-09 至 2025-04-10
2025-06-02 12:12:43,405 - INFO - 查询日期范围: 2025-04-09 至 2025-04-10，使用分页查询，每页 100 条记录
2025-06-02 12:12:43,405 - INFO - Request Parameters - Page 1:
2025-06-02 12:12:43,405 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:12:43,405 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000000, 1744214400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:12:43,743 - INFO - API请求耗时: 338ms
2025-06-02 12:12:43,744 - INFO - Response - Page 1
2025-06-02 12:12:43,744 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:12:43,744 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:12:43,744 - WARNING - 分段 13 查询返回空数据
2025-06-02 12:12:44,745 - INFO - 查询分段 14: 2025-04-11 至 2025-04-12
2025-06-02 12:12:44,745 - INFO - 查询日期范围: 2025-04-11 至 2025-04-12，使用分页查询，每页 100 条记录
2025-06-02 12:12:44,745 - INFO - Request Parameters - Page 1:
2025-06-02 12:12:44,746 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:12:44,746 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800000, 1744387200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:12:45,035 - INFO - API请求耗时: 288ms
2025-06-02 12:12:45,035 - INFO - Response - Page 1
2025-06-02 12:12:45,035 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:12:45,035 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:12:45,035 - WARNING - 分段 14 查询返回空数据
2025-06-02 12:12:46,049 - INFO - 查询分段 15: 2025-04-13 至 2025-04-14
2025-06-02 12:12:46,049 - INFO - 查询日期范围: 2025-04-13 至 2025-04-14，使用分页查询，每页 100 条记录
2025-06-02 12:12:46,049 - INFO - Request Parameters - Page 1:
2025-06-02 12:12:46,049 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:12:46,049 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600000, 1744560000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:12:46,372 - INFO - API请求耗时: 322ms
2025-06-02 12:12:46,372 - INFO - Response - Page 1
2025-06-02 12:12:46,373 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:12:46,373 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:12:46,373 - WARNING - 分段 15 查询返回空数据
2025-06-02 12:12:47,387 - INFO - 查询分段 16: 2025-04-15 至 2025-04-16
2025-06-02 12:12:47,387 - INFO - 查询日期范围: 2025-04-15 至 2025-04-16，使用分页查询，每页 100 条记录
2025-06-02 12:12:47,387 - INFO - Request Parameters - Page 1:
2025-06-02 12:12:47,387 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:12:47,387 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400000, 1744732800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:12:47,707 - INFO - API请求耗时: 320ms
2025-06-02 12:12:47,707 - INFO - Response - Page 1
2025-06-02 12:12:47,707 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:12:47,707 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:12:47,707 - WARNING - 分段 16 查询返回空数据
2025-06-02 12:12:48,723 - INFO - 查询分段 17: 2025-04-17 至 2025-04-18
2025-06-02 12:12:48,723 - INFO - 查询日期范围: 2025-04-17 至 2025-04-18，使用分页查询，每页 100 条记录
2025-06-02 12:12:48,723 - INFO - Request Parameters - Page 1:
2025-06-02 12:12:48,723 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:12:48,723 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200000, 1744905600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:12:49,050 - INFO - API请求耗时: 328ms
2025-06-02 12:12:49,050 - INFO - Response - Page 1
2025-06-02 12:12:49,050 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:12:49,050 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:12:49,050 - WARNING - 分段 17 查询返回空数据
2025-06-02 12:12:50,051 - INFO - 查询分段 18: 2025-04-19 至 2025-04-20
2025-06-02 12:12:50,051 - INFO - 查询日期范围: 2025-04-19 至 2025-04-20，使用分页查询，每页 100 条记录
2025-06-02 12:12:50,051 - INFO - Request Parameters - Page 1:
2025-06-02 12:12:50,052 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:12:50,052 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744992000000, 1745078400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:12:50,359 - INFO - API请求耗时: 307ms
2025-06-02 12:12:50,359 - INFO - Response - Page 1
2025-06-02 12:12:50,359 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:12:50,359 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:12:50,359 - WARNING - 分段 18 查询返回空数据
2025-06-02 12:12:51,372 - INFO - 查询分段 19: 2025-04-21 至 2025-04-22
2025-06-02 12:12:51,372 - INFO - 查询日期范围: 2025-04-21 至 2025-04-22，使用分页查询，每页 100 条记录
2025-06-02 12:12:51,372 - INFO - Request Parameters - Page 1:
2025-06-02 12:12:51,372 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:12:51,372 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800000, 1745251200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:12:51,699 - INFO - API请求耗时: 327ms
2025-06-02 12:12:51,699 - INFO - Response - Page 1
2025-06-02 12:12:51,699 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:12:51,699 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:12:51,699 - WARNING - 分段 19 查询返回空数据
2025-06-02 12:12:52,700 - INFO - 查询分段 20: 2025-04-23 至 2025-04-24
2025-06-02 12:12:52,700 - INFO - 查询日期范围: 2025-04-23 至 2025-04-24，使用分页查询，每页 100 条记录
2025-06-02 12:12:52,701 - INFO - Request Parameters - Page 1:
2025-06-02 12:12:52,701 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:12:52,701 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600000, 1745424000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:12:53,048 - INFO - API请求耗时: 347ms
2025-06-02 12:12:53,048 - INFO - Response - Page 1
2025-06-02 12:12:53,048 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:12:53,048 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:12:53,048 - WARNING - 分段 20 查询返回空数据
2025-06-02 12:12:54,054 - INFO - 查询分段 21: 2025-04-25 至 2025-04-26
2025-06-02 12:12:54,054 - INFO - 查询日期范围: 2025-04-25 至 2025-04-26，使用分页查询，每页 100 条记录
2025-06-02 12:12:54,054 - INFO - Request Parameters - Page 1:
2025-06-02 12:12:54,054 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:12:54,054 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400000, 1745596800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:12:54,379 - INFO - API请求耗时: 325ms
2025-06-02 12:12:54,379 - INFO - Response - Page 1
2025-06-02 12:12:54,379 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:12:54,379 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:12:54,379 - WARNING - 分段 21 查询返回空数据
2025-06-02 12:12:55,380 - INFO - 查询分段 22: 2025-04-27 至 2025-04-28
2025-06-02 12:12:55,380 - INFO - 查询日期范围: 2025-04-27 至 2025-04-28，使用分页查询，每页 100 条记录
2025-06-02 12:12:55,380 - INFO - Request Parameters - Page 1:
2025-06-02 12:12:55,381 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:12:55,381 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200000, 1745769600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:12:55,685 - INFO - API请求耗时: 304ms
2025-06-02 12:12:55,686 - INFO - Response - Page 1
2025-06-02 12:12:55,686 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:12:55,686 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:12:55,686 - WARNING - 分段 22 查询返回空数据
2025-06-02 12:12:56,700 - INFO - 查询分段 23: 2025-04-29 至 2025-04-30
2025-06-02 12:12:56,700 - INFO - 查询日期范围: 2025-04-29 至 2025-04-30，使用分页查询，每页 100 条记录
2025-06-02 12:12:56,700 - INFO - Request Parameters - Page 1:
2025-06-02 12:12:56,700 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:12:56,700 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:12:57,043 - INFO - API请求耗时: 343ms
2025-06-02 12:12:57,043 - INFO - Response - Page 1
2025-06-02 12:12:57,043 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:12:57,043 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:12:57,043 - WARNING - 分段 23 查询返回空数据
2025-06-02 12:12:58,044 - INFO - 查询分段 24: 2025-05-01 至 2025-05-02
2025-06-02 12:12:58,044 - INFO - 查询日期范围: 2025-05-01 至 2025-05-02，使用分页查询，每页 100 条记录
2025-06-02 12:12:58,045 - INFO - Request Parameters - Page 1:
2025-06-02 12:12:58,045 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:12:58,045 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1746115200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:12:58,366 - INFO - API请求耗时: 320ms
2025-06-02 12:12:58,366 - INFO - Response - Page 1
2025-06-02 12:12:58,366 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:12:58,366 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:12:58,367 - WARNING - 分段 24 查询返回空数据
2025-06-02 12:12:59,368 - INFO - 查询分段 25: 2025-05-03 至 2025-05-04
2025-06-02 12:12:59,368 - INFO - 查询日期范围: 2025-05-03 至 2025-05-04，使用分页查询，每页 100 条记录
2025-06-02 12:12:59,368 - INFO - Request Parameters - Page 1:
2025-06-02 12:12:59,369 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:12:59,369 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746201600000, 1746288000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:12:59,682 - INFO - API请求耗时: 314ms
2025-06-02 12:12:59,682 - INFO - Response - Page 1
2025-06-02 12:12:59,682 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:12:59,682 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:12:59,682 - WARNING - 分段 25 查询返回空数据
2025-06-02 12:13:00,683 - INFO - 查询分段 26: 2025-05-05 至 2025-05-06
2025-06-02 12:13:00,683 - INFO - 查询日期范围: 2025-05-05 至 2025-05-06，使用分页查询，每页 100 条记录
2025-06-02 12:13:00,683 - INFO - Request Parameters - Page 1:
2025-06-02 12:13:00,684 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:13:00,684 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400000, 1746460800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:13:00,959 - INFO - API请求耗时: 274ms
2025-06-02 12:13:00,959 - INFO - Response - Page 1
2025-06-02 12:13:00,959 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:13:00,959 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:13:00,959 - WARNING - 分段 26 查询返回空数据
2025-06-02 12:13:01,968 - INFO - 查询分段 27: 2025-05-07 至 2025-05-08
2025-06-02 12:13:01,968 - INFO - 查询日期范围: 2025-05-07 至 2025-05-08，使用分页查询，每页 100 条记录
2025-06-02 12:13:01,968 - INFO - Request Parameters - Page 1:
2025-06-02 12:13:01,968 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:13:01,968 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200000, 1746633600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:13:02,218 - INFO - API请求耗时: 250ms
2025-06-02 12:13:02,218 - INFO - Response - Page 1
2025-06-02 12:13:02,218 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:13:02,218 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:13:02,218 - WARNING - 分段 27 查询返回空数据
2025-06-02 12:13:03,229 - INFO - 查询分段 28: 2025-05-09 至 2025-05-10
2025-06-02 12:13:03,229 - INFO - 查询日期范围: 2025-05-09 至 2025-05-10，使用分页查询，每页 100 条记录
2025-06-02 12:13:03,229 - INFO - Request Parameters - Page 1:
2025-06-02 12:13:03,229 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:13:03,229 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000000, 1746806400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:13:03,508 - INFO - API请求耗时: 279ms
2025-06-02 12:13:03,508 - INFO - Response - Page 1
2025-06-02 12:13:03,508 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:13:03,508 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:13:03,508 - WARNING - 分段 28 查询返回空数据
2025-06-02 12:13:04,509 - INFO - 查询分段 29: 2025-05-11 至 2025-05-12
2025-06-02 12:13:04,509 - INFO - 查询日期范围: 2025-05-11 至 2025-05-12，使用分页查询，每页 100 条记录
2025-06-02 12:13:04,509 - INFO - Request Parameters - Page 1:
2025-06-02 12:13:04,510 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:13:04,510 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800000, 1746979200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:13:04,806 - INFO - API请求耗时: 295ms
2025-06-02 12:13:04,807 - INFO - Response - Page 1
2025-06-02 12:13:04,807 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:13:04,807 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:13:04,808 - WARNING - 分段 29 查询返回空数据
2025-06-02 12:13:05,815 - INFO - 查询分段 30: 2025-05-13 至 2025-05-14
2025-06-02 12:13:05,815 - INFO - 查询日期范围: 2025-05-13 至 2025-05-14，使用分页查询，每页 100 条记录
2025-06-02 12:13:05,815 - INFO - Request Parameters - Page 1:
2025-06-02 12:13:05,815 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:13:05,815 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600000, 1747152000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:13:06,110 - INFO - API请求耗时: 294ms
2025-06-02 12:13:06,110 - INFO - Response - Page 1
2025-06-02 12:13:06,110 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:13:06,110 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:13:06,110 - WARNING - 分段 30 查询返回空数据
2025-06-02 12:13:07,120 - INFO - 查询分段 31: 2025-05-15 至 2025-05-16
2025-06-02 12:13:07,120 - INFO - 查询日期范围: 2025-05-15 至 2025-05-16，使用分页查询，每页 100 条记录
2025-06-02 12:13:07,120 - INFO - Request Parameters - Page 1:
2025-06-02 12:13:07,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:13:07,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400000, 1747324800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:13:07,398 - INFO - API请求耗时: 278ms
2025-06-02 12:13:07,398 - INFO - Response - Page 1
2025-06-02 12:13:07,398 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:13:07,398 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:13:07,398 - WARNING - 分段 31 查询返回空数据
2025-06-02 12:13:08,403 - INFO - 查询分段 32: 2025-05-17 至 2025-05-18
2025-06-02 12:13:08,403 - INFO - 查询日期范围: 2025-05-17 至 2025-05-18，使用分页查询，每页 100 条记录
2025-06-02 12:13:08,403 - INFO - Request Parameters - Page 1:
2025-06-02 12:13:08,403 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:13:08,403 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747411200000, 1747497600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:13:08,728 - INFO - API请求耗时: 325ms
2025-06-02 12:13:08,728 - INFO - Response - Page 1
2025-06-02 12:13:08,729 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:13:08,729 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:13:08,729 - WARNING - 分段 32 查询返回空数据
2025-06-02 12:13:09,730 - INFO - 查询分段 33: 2025-05-19 至 2025-05-20
2025-06-02 12:13:09,730 - INFO - 查询日期范围: 2025-05-19 至 2025-05-20，使用分页查询，每页 100 条记录
2025-06-02 12:13:09,730 - INFO - Request Parameters - Page 1:
2025-06-02 12:13:09,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:13:09,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747584000000, 1747670400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:13:10,011 - INFO - API请求耗时: 280ms
2025-06-02 12:13:10,012 - INFO - Response - Page 1
2025-06-02 12:13:10,012 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:13:10,013 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:13:10,013 - WARNING - 分段 33 查询返回空数据
2025-06-02 12:13:11,013 - INFO - 查询分段 34: 2025-05-21 至 2025-05-22
2025-06-02 12:13:11,013 - INFO - 查询日期范围: 2025-05-21 至 2025-05-22，使用分页查询，每页 100 条记录
2025-06-02 12:13:11,014 - INFO - Request Parameters - Page 1:
2025-06-02 12:13:11,014 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:13:11,014 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747756800000, 1747843200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:13:11,342 - INFO - API请求耗时: 328ms
2025-06-02 12:13:11,342 - INFO - Response - Page 1
2025-06-02 12:13:11,342 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:13:11,342 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:13:11,342 - WARNING - 分段 34 查询返回空数据
2025-06-02 12:13:12,343 - INFO - 查询分段 35: 2025-05-23 至 2025-05-24
2025-06-02 12:13:12,343 - INFO - 查询日期范围: 2025-05-23 至 2025-05-24，使用分页查询，每页 100 条记录
2025-06-02 12:13:12,343 - INFO - Request Parameters - Page 1:
2025-06-02 12:13:12,344 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:13:12,344 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747929600000, 1748016000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:13:12,622 - INFO - API请求耗时: 278ms
2025-06-02 12:13:12,622 - INFO - Response - Page 1
2025-06-02 12:13:12,622 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:13:12,622 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:13:12,622 - WARNING - 分段 35 查询返回空数据
2025-06-02 12:13:13,632 - INFO - 查询分段 36: 2025-05-25 至 2025-05-26
2025-06-02 12:13:13,632 - INFO - 查询日期范围: 2025-05-25 至 2025-05-26，使用分页查询，每页 100 条记录
2025-06-02 12:13:13,632 - INFO - Request Parameters - Page 1:
2025-06-02 12:13:13,632 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:13:13,632 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748102400000, 1748188800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:13:13,930 - INFO - API请求耗时: 297ms
2025-06-02 12:13:13,930 - INFO - Response - Page 1
2025-06-02 12:13:13,930 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:13:13,930 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:13:13,930 - WARNING - 分段 36 查询返回空数据
2025-06-02 12:13:14,945 - INFO - 查询分段 37: 2025-05-27 至 2025-05-28
2025-06-02 12:13:14,945 - INFO - 查询日期范围: 2025-05-27 至 2025-05-28，使用分页查询，每页 100 条记录
2025-06-02 12:13:14,945 - INFO - Request Parameters - Page 1:
2025-06-02 12:13:14,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:13:14,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748275200000, 1748361600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:13:15,276 - INFO - API请求耗时: 331ms
2025-06-02 12:13:15,276 - INFO - Response - Page 1
2025-06-02 12:13:15,276 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:13:15,277 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:13:15,277 - WARNING - 分段 37 查询返回空数据
2025-06-02 12:13:16,278 - INFO - 查询分段 38: 2025-05-29 至 2025-05-30
2025-06-02 12:13:16,278 - INFO - 查询日期范围: 2025-05-29 至 2025-05-30，使用分页查询，每页 100 条记录
2025-06-02 12:13:16,278 - INFO - Request Parameters - Page 1:
2025-06-02 12:13:16,279 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:13:16,279 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748448000000, 1748534400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:13:16,586 - INFO - API请求耗时: 308ms
2025-06-02 12:13:16,586 - INFO - Response - Page 1
2025-06-02 12:13:16,587 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:13:16,587 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:13:16,587 - WARNING - 分段 38 查询返回空数据
2025-06-02 12:13:17,589 - INFO - 查询分段 39: 2025-05-31 至 2025-06-01
2025-06-02 12:13:17,589 - INFO - 查询日期范围: 2025-05-31 至 2025-06-01，使用分页查询，每页 100 条记录
2025-06-02 12:13:17,591 - INFO - Request Parameters - Page 1:
2025-06-02 12:13:17,591 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:13:17,591 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748620800000, 1748707200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:13:17,927 - INFO - API请求耗时: 336ms
2025-06-02 12:13:17,927 - INFO - Response - Page 1
2025-06-02 12:13:17,928 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:13:17,928 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:13:17,928 - WARNING - 分段 39 查询返回空数据
2025-06-02 12:13:18,943 - INFO - 查询分段 40: 2025-06-02 至 2025-06-02
2025-06-02 12:13:18,943 - INFO - 查询日期范围: 2025-06-02 至 2025-06-02，使用分页查询，每页 100 条记录
2025-06-02 12:13:18,948 - INFO - Request Parameters - Page 1:
2025-06-02 12:13:18,948 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:13:18,948 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:13:19,282 - INFO - API请求耗时: 331ms
2025-06-02 12:13:19,283 - INFO - Response - Page 1
2025-06-02 12:13:19,283 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:13:19,283 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:13:19,283 - WARNING - 分段 40 查询返回空数据
2025-06-02 12:13:20,285 - INFO - 宜搭每日表单数据查询完成，共 40 个分段，成功获取 212 条记录，失败 0 次
2025-06-02 12:13:20,285 - INFO - 成功获取宜搭日销售表单数据，共 212 条记录
2025-06-02 12:13:20,285 - INFO - 宜搭日销售数据项结构示例: ['formInstanceId', 'formData']
2025-06-02 12:13:20,286 - INFO - 开始对比和同步日销售数据...
2025-06-02 12:13:20,293 - INFO - 成功创建宜搭日销售数据索引，共 212 条记录
2025-06-02 12:13:20,293 - INFO - 开始处理数衍数据，共 16849 条记录
2025-06-02 12:13:21,520 - INFO - 正在批量插入每日数据，批次 1/167，共 100 条记录
2025-06-02 12:13:21,969 - INFO - 批量插入每日数据成功，批次 1，100 条记录
2025-06-02 12:13:24,976 - INFO - 正在批量插入每日数据，批次 2/167，共 100 条记录
2025-06-02 12:13:25,417 - INFO - 批量插入每日数据成功，批次 2，100 条记录
2025-06-02 12:13:28,427 - INFO - 正在批量插入每日数据，批次 3/167，共 100 条记录
2025-06-02 12:13:28,835 - INFO - 批量插入每日数据成功，批次 3，100 条记录
2025-06-02 12:13:31,847 - INFO - 正在批量插入每日数据，批次 4/167，共 100 条记录
2025-06-02 12:13:32,225 - INFO - 批量插入每日数据成功，批次 4，100 条记录
2025-06-02 12:13:35,234 - INFO - 正在批量插入每日数据，批次 5/167，共 100 条记录
2025-06-02 12:13:35,841 - INFO - 批量插入每日数据成功，批次 5，100 条记录
2025-06-02 12:13:38,845 - INFO - 正在批量插入每日数据，批次 6/167，共 100 条记录
2025-06-02 12:13:39,214 - INFO - 批量插入每日数据成功，批次 6，100 条记录
2025-06-02 12:13:42,223 - INFO - 正在批量插入每日数据，批次 7/167，共 100 条记录
2025-06-02 12:13:42,753 - INFO - 批量插入每日数据成功，批次 7，100 条记录
2025-06-02 12:13:45,755 - INFO - 正在批量插入每日数据，批次 8/167，共 100 条记录
2025-06-02 12:13:46,154 - INFO - 批量插入每日数据成功，批次 8，100 条记录
2025-06-02 12:13:49,163 - INFO - 正在批量插入每日数据，批次 9/167，共 100 条记录
2025-06-02 12:13:49,619 - INFO - 批量插入每日数据成功，批次 9，100 条记录
2025-06-02 12:13:52,630 - INFO - 正在批量插入每日数据，批次 10/167，共 100 条记录
2025-06-02 12:13:53,023 - INFO - 批量插入每日数据成功，批次 10，100 条记录
2025-06-02 12:13:56,034 - INFO - 正在批量插入每日数据，批次 11/167，共 100 条记录
2025-06-02 12:13:56,532 - INFO - 批量插入每日数据成功，批次 11，100 条记录
2025-06-02 12:13:59,546 - INFO - 正在批量插入每日数据，批次 12/167，共 100 条记录
2025-06-02 12:14:00,061 - INFO - 批量插入每日数据成功，批次 12，100 条记录
2025-06-02 12:14:03,075 - INFO - 正在批量插入每日数据，批次 13/167，共 100 条记录
2025-06-02 12:14:03,432 - INFO - 批量插入每日数据成功，批次 13，100 条记录
2025-06-02 12:14:06,447 - INFO - 正在批量插入每日数据，批次 14/167，共 100 条记录
2025-06-02 12:14:06,897 - INFO - 批量插入每日数据成功，批次 14，100 条记录
2025-06-02 12:14:09,911 - INFO - 正在批量插入每日数据，批次 15/167，共 100 条记录
2025-06-02 12:14:10,348 - INFO - 批量插入每日数据成功，批次 15，100 条记录
2025-06-02 12:14:13,363 - INFO - 正在批量插入每日数据，批次 16/167，共 100 条记录
2025-06-02 12:14:13,753 - INFO - 批量插入每日数据成功，批次 16，100 条记录
2025-06-02 12:14:16,768 - INFO - 正在批量插入每日数据，批次 17/167，共 100 条记录
2025-06-02 12:14:17,250 - INFO - 批量插入每日数据成功，批次 17，100 条记录
2025-06-02 12:14:20,267 - INFO - 正在批量插入每日数据，批次 18/167，共 100 条记录
2025-06-02 12:14:20,749 - INFO - 批量插入每日数据成功，批次 18，100 条记录
2025-06-02 12:14:23,765 - INFO - 正在批量插入每日数据，批次 19/167，共 100 条记录
2025-06-02 12:14:24,138 - INFO - 批量插入每日数据成功，批次 19，100 条记录
2025-06-02 12:14:27,154 - INFO - 正在批量插入每日数据，批次 20/167，共 100 条记录
2025-06-02 12:14:27,560 - INFO - 批量插入每日数据成功，批次 20，100 条记录
2025-06-02 12:14:30,575 - INFO - 正在批量插入每日数据，批次 21/167，共 100 条记录
2025-06-02 12:14:30,965 - INFO - 批量插入每日数据成功，批次 21，100 条记录
2025-06-02 12:14:33,980 - INFO - 正在批量插入每日数据，批次 22/167，共 100 条记录
2025-06-02 12:14:34,401 - INFO - 批量插入每日数据成功，批次 22，100 条记录
2025-06-02 12:14:37,416 - INFO - 正在批量插入每日数据，批次 23/167，共 100 条记录
2025-06-02 12:14:37,899 - INFO - 批量插入每日数据成功，批次 23，100 条记录
2025-06-02 12:14:40,930 - INFO - 正在批量插入每日数据，批次 24/167，共 100 条记录
2025-06-02 12:14:41,445 - INFO - 批量插入每日数据成功，批次 24，100 条记录
2025-06-02 12:14:44,460 - INFO - 正在批量插入每日数据，批次 25/167，共 100 条记录
2025-06-02 12:14:44,849 - INFO - 批量插入每日数据成功，批次 25，100 条记录
2025-06-02 12:14:47,864 - INFO - 正在批量插入每日数据，批次 26/167，共 100 条记录
2025-06-02 12:14:48,285 - INFO - 批量插入每日数据成功，批次 26，100 条记录
2025-06-02 12:14:51,299 - INFO - 正在批量插入每日数据，批次 27/167，共 100 条记录
2025-06-02 12:14:51,704 - INFO - 批量插入每日数据成功，批次 27，100 条记录
2025-06-02 12:14:54,718 - INFO - 正在批量插入每日数据，批次 28/167，共 100 条记录
2025-06-02 12:14:55,156 - INFO - 批量插入每日数据成功，批次 28，100 条记录
2025-06-02 12:14:58,170 - INFO - 正在批量插入每日数据，批次 29/167，共 100 条记录
2025-06-02 12:14:58,717 - INFO - 批量插入每日数据成功，批次 29，100 条记录
2025-06-02 12:15:01,731 - INFO - 正在批量插入每日数据，批次 30/167，共 100 条记录
2025-06-02 12:15:02,153 - INFO - 批量插入每日数据成功，批次 30，100 条记录
2025-06-02 12:15:05,167 - INFO - 正在批量插入每日数据，批次 31/167，共 100 条记录
2025-06-02 12:15:05,543 - INFO - 批量插入每日数据成功，批次 31，100 条记录
2025-06-02 12:15:08,556 - INFO - 正在批量插入每日数据，批次 32/167，共 100 条记录
2025-06-02 12:15:08,992 - INFO - 批量插入每日数据成功，批次 32，100 条记录
2025-06-02 12:15:12,007 - INFO - 正在批量插入每日数据，批次 33/167，共 100 条记录
2025-06-02 12:15:12,412 - INFO - 批量插入每日数据成功，批次 33，100 条记录
2025-06-02 12:15:15,421 - INFO - 正在批量插入每日数据，批次 34/167，共 100 条记录
2025-06-02 12:15:15,773 - INFO - 批量插入每日数据成功，批次 34，100 条记录
2025-06-02 12:15:18,781 - INFO - 正在批量插入每日数据，批次 35/167，共 100 条记录
2025-06-02 12:15:19,146 - INFO - 批量插入每日数据成功，批次 35，100 条记录
2025-06-02 12:15:22,153 - INFO - 正在批量插入每日数据，批次 36/167，共 100 条记录
2025-06-02 12:15:22,552 - INFO - 批量插入每日数据成功，批次 36，100 条记录
2025-06-02 12:15:25,558 - INFO - 正在批量插入每日数据，批次 37/167，共 100 条记录
2025-06-02 12:15:25,971 - INFO - 批量插入每日数据成功，批次 37，100 条记录
2025-06-02 12:15:28,986 - INFO - 正在批量插入每日数据，批次 38/167，共 100 条记录
2025-06-02 12:15:29,470 - INFO - 批量插入每日数据成功，批次 38，100 条记录
2025-06-02 12:15:32,485 - INFO - 正在批量插入每日数据，批次 39/167，共 100 条记录
2025-06-02 12:15:32,890 - INFO - 批量插入每日数据成功，批次 39，100 条记录
2025-06-02 12:15:35,906 - INFO - 正在批量插入每日数据，批次 40/167，共 100 条记录
2025-06-02 12:15:36,372 - INFO - 批量插入每日数据成功，批次 40，100 条记录
2025-06-02 12:15:39,388 - INFO - 正在批量插入每日数据，批次 41/167，共 100 条记录
2025-06-02 12:15:39,792 - INFO - 批量插入每日数据成功，批次 41，100 条记录
2025-06-02 12:15:42,806 - INFO - 正在批量插入每日数据，批次 42/167，共 100 条记录
2025-06-02 12:15:43,242 - INFO - 批量插入每日数据成功，批次 42，100 条记录
2025-06-02 12:15:46,257 - INFO - 正在批量插入每日数据，批次 43/167，共 100 条记录
2025-06-02 12:15:46,677 - INFO - 批量插入每日数据成功，批次 43，100 条记录
2025-06-02 12:15:49,692 - INFO - 正在批量插入每日数据，批次 44/167，共 100 条记录
2025-06-02 12:15:50,082 - INFO - 批量插入每日数据成功，批次 44，100 条记录
2025-06-02 12:15:53,097 - INFO - 正在批量插入每日数据，批次 45/167，共 100 条记录
2025-06-02 12:15:53,564 - INFO - 批量插入每日数据成功，批次 45，100 条记录
2025-06-02 12:15:56,579 - INFO - 正在批量插入每日数据，批次 46/167，共 100 条记录
2025-06-02 12:15:57,000 - INFO - 批量插入每日数据成功，批次 46，100 条记录
2025-06-02 12:16:00,015 - INFO - 正在批量插入每日数据，批次 47/167，共 100 条记录
2025-06-02 12:16:00,483 - INFO - 批量插入每日数据成功，批次 47，100 条记录
2025-06-02 12:16:03,499 - INFO - 正在批量插入每日数据，批次 48/167，共 100 条记录
2025-06-02 12:16:03,888 - INFO - 批量插入每日数据成功，批次 48，100 条记录
2025-06-02 12:16:06,904 - INFO - 正在批量插入每日数据，批次 49/167，共 100 条记录
2025-06-02 12:16:07,309 - INFO - 批量插入每日数据成功，批次 49，100 条记录
2025-06-02 12:16:10,324 - INFO - 正在批量插入每日数据，批次 50/167，共 100 条记录
2025-06-02 12:16:10,714 - INFO - 批量插入每日数据成功，批次 50，100 条记录
2025-06-02 12:16:13,729 - INFO - 正在批量插入每日数据，批次 51/167，共 100 条记录
2025-06-02 12:16:14,134 - INFO - 批量插入每日数据成功，批次 51，100 条记录
2025-06-02 12:16:17,149 - INFO - 正在批量插入每日数据，批次 52/167，共 100 条记录
2025-06-02 12:16:17,586 - INFO - 批量插入每日数据成功，批次 52，100 条记录
2025-06-02 12:16:20,601 - INFO - 正在批量插入每日数据，批次 53/167，共 100 条记录
2025-06-02 12:16:21,069 - INFO - 批量插入每日数据成功，批次 53，100 条记录
2025-06-02 12:16:24,083 - INFO - 正在批量插入每日数据，批次 54/167，共 100 条记录
2025-06-02 12:16:24,582 - INFO - 批量插入每日数据成功，批次 54，100 条记录
2025-06-02 12:16:27,595 - INFO - 正在批量插入每日数据，批次 55/167，共 100 条记录
2025-06-02 12:16:28,109 - INFO - 批量插入每日数据成功，批次 55，100 条记录
2025-06-02 12:16:31,123 - INFO - 正在批量插入每日数据，批次 56/167，共 100 条记录
2025-06-02 12:16:31,560 - INFO - 批量插入每日数据成功，批次 56，100 条记录
2025-06-02 12:16:34,575 - INFO - 正在批量插入每日数据，批次 57/167，共 100 条记录
2025-06-02 12:16:35,013 - INFO - 批量插入每日数据成功，批次 57，100 条记录
2025-06-02 12:16:38,027 - INFO - 正在批量插入每日数据，批次 58/167，共 100 条记录
2025-06-02 12:16:38,543 - INFO - 批量插入每日数据成功，批次 58，100 条记录
2025-06-02 12:16:41,557 - INFO - 正在批量插入每日数据，批次 59/167，共 100 条记录
2025-06-02 12:16:41,993 - INFO - 批量插入每日数据成功，批次 59，100 条记录
2025-06-02 12:16:45,008 - INFO - 正在批量插入每日数据，批次 60/167，共 100 条记录
2025-06-02 12:16:45,429 - INFO - 批量插入每日数据成功，批次 60，100 条记录
2025-06-02 12:16:48,444 - INFO - 正在批量插入每日数据，批次 61/167，共 100 条记录
2025-06-02 12:16:48,944 - INFO - 批量插入每日数据成功，批次 61，100 条记录
2025-06-02 12:16:51,959 - INFO - 正在批量插入每日数据，批次 62/167，共 100 条记录
2025-06-02 12:16:52,489 - INFO - 批量插入每日数据成功，批次 62，100 条记录
2025-06-02 12:16:55,504 - INFO - 正在批量插入每日数据，批次 63/167，共 100 条记录
2025-06-02 12:16:55,956 - INFO - 批量插入每日数据成功，批次 63，100 条记录
2025-06-02 12:16:58,972 - INFO - 正在批量插入每日数据，批次 64/167，共 100 条记录
2025-06-02 12:16:59,408 - INFO - 批量插入每日数据成功，批次 64，100 条记录
2025-06-02 12:17:02,423 - INFO - 正在批量插入每日数据，批次 65/167，共 100 条记录
2025-06-02 12:17:02,782 - INFO - 批量插入每日数据成功，批次 65，100 条记录
2025-06-02 12:17:05,797 - INFO - 正在批量插入每日数据，批次 66/167，共 100 条记录
2025-06-02 12:17:06,264 - INFO - 批量插入每日数据成功，批次 66，100 条记录
2025-06-02 12:17:09,279 - INFO - 正在批量插入每日数据，批次 67/167，共 100 条记录
2025-06-02 12:17:09,684 - INFO - 批量插入每日数据成功，批次 67，100 条记录
2025-06-02 12:17:12,699 - INFO - 正在批量插入每日数据，批次 68/167，共 100 条记录
2025-06-02 12:17:13,166 - INFO - 批量插入每日数据成功，批次 68，100 条记录
2025-06-02 12:17:16,181 - INFO - 正在批量插入每日数据，批次 69/167，共 100 条记录
2025-06-02 12:17:16,617 - INFO - 批量插入每日数据成功，批次 69，100 条记录
2025-06-02 12:17:19,632 - INFO - 正在批量插入每日数据，批次 70/167，共 100 条记录
2025-06-02 12:17:20,022 - INFO - 批量插入每日数据成功，批次 70，100 条记录
2025-06-02 12:17:23,037 - INFO - 正在批量插入每日数据，批次 71/167，共 100 条记录
2025-06-02 12:17:23,583 - INFO - 批量插入每日数据成功，批次 71，100 条记录
2025-06-02 12:17:26,598 - INFO - 正在批量插入每日数据，批次 72/167，共 100 条记录
2025-06-02 12:17:27,097 - INFO - 批量插入每日数据成功，批次 72，100 条记录
2025-06-02 12:17:30,112 - INFO - 正在批量插入每日数据，批次 73/167，共 100 条记录
2025-06-02 12:17:30,548 - INFO - 批量插入每日数据成功，批次 73，100 条记录
2025-06-02 12:17:33,564 - INFO - 正在批量插入每日数据，批次 74/167，共 100 条记录
2025-06-02 12:17:33,968 - INFO - 批量插入每日数据成功，批次 74，100 条记录
2025-06-02 12:17:36,984 - INFO - 正在批量插入每日数据，批次 75/167，共 100 条记录
2025-06-02 12:17:37,529 - INFO - 批量插入每日数据成功，批次 75，100 条记录
2025-06-02 12:17:40,546 - INFO - 正在批量插入每日数据，批次 76/167，共 100 条记录
2025-06-02 12:17:40,920 - INFO - 批量插入每日数据成功，批次 76，100 条记录
2025-06-02 12:17:43,935 - INFO - 正在批量插入每日数据，批次 77/167，共 100 条记录
2025-06-02 12:17:44,325 - INFO - 批量插入每日数据成功，批次 77，100 条记录
2025-06-02 12:17:47,340 - INFO - 正在批量插入每日数据，批次 78/167，共 100 条记录
2025-06-02 12:17:47,807 - INFO - 批量插入每日数据成功，批次 78，100 条记录
2025-06-02 12:17:50,823 - INFO - 正在批量插入每日数据，批次 79/167，共 100 条记录
2025-06-02 12:17:51,431 - INFO - 批量插入每日数据成功，批次 79，100 条记录
2025-06-02 12:17:54,446 - INFO - 正在批量插入每日数据，批次 80/167，共 100 条记录
2025-06-02 12:17:54,898 - INFO - 批量插入每日数据成功，批次 80，100 条记录
2025-06-02 12:17:57,914 - INFO - 正在批量插入每日数据，批次 81/167，共 100 条记录
2025-06-02 12:17:58,396 - INFO - 批量插入每日数据成功，批次 81，100 条记录
2025-06-02 12:18:01,411 - INFO - 正在批量插入每日数据，批次 82/167，共 100 条记录
2025-06-02 12:18:01,834 - INFO - 批量插入每日数据成功，批次 82，100 条记录
2025-06-02 12:18:04,846 - INFO - 正在批量插入每日数据，批次 83/167，共 100 条记录
2025-06-02 12:18:05,254 - INFO - 批量插入每日数据成功，批次 83，100 条记录
2025-06-02 12:18:08,266 - INFO - 正在批量插入每日数据，批次 84/167，共 100 条记录
2025-06-02 12:18:08,672 - INFO - 批量插入每日数据成功，批次 84，100 条记录
2025-06-02 12:18:11,687 - INFO - 正在批量插入每日数据，批次 85/167，共 100 条记录
2025-06-02 12:18:12,186 - INFO - 批量插入每日数据成功，批次 85，100 条记录
2025-06-02 12:18:15,202 - INFO - 正在批量插入每日数据，批次 86/167，共 100 条记录
2025-06-02 12:18:15,685 - INFO - 批量插入每日数据成功，批次 86，100 条记录
2025-06-02 12:18:18,700 - INFO - 正在批量插入每日数据，批次 87/167，共 100 条记录
2025-06-02 12:18:19,168 - INFO - 批量插入每日数据成功，批次 87，100 条记录
2025-06-02 12:18:22,182 - INFO - 正在批量插入每日数据，批次 88/167，共 100 条记录
2025-06-02 12:18:22,650 - INFO - 批量插入每日数据成功，批次 88，100 条记录
2025-06-02 12:18:25,665 - INFO - 正在批量插入每日数据，批次 89/167，共 100 条记录
2025-06-02 12:18:26,069 - INFO - 批量插入每日数据成功，批次 89，100 条记录
2025-06-02 12:18:29,085 - INFO - 正在批量插入每日数据，批次 90/167，共 100 条记录
2025-06-02 12:18:29,567 - INFO - 批量插入每日数据成功，批次 90，100 条记录
2025-06-02 12:18:32,582 - INFO - 正在批量插入每日数据，批次 91/167，共 100 条记录
2025-06-02 12:18:33,098 - INFO - 批量插入每日数据成功，批次 91，100 条记录
2025-06-02 12:18:36,112 - INFO - 正在批量插入每日数据，批次 92/167，共 100 条记录
2025-06-02 12:18:36,628 - INFO - 批量插入每日数据成功，批次 92，100 条记录
2025-06-02 12:18:39,642 - INFO - 正在批量插入每日数据，批次 93/167，共 100 条记录
2025-06-02 12:18:40,112 - INFO - 批量插入每日数据成功，批次 93，100 条记录
2025-06-02 12:18:43,125 - INFO - 正在批量插入每日数据，批次 94/167，共 100 条记录
2025-06-02 12:18:43,610 - INFO - 批量插入每日数据成功，批次 94，100 条记录
2025-06-02 12:18:46,624 - INFO - 正在批量插入每日数据，批次 95/167，共 100 条记录
2025-06-02 12:18:47,124 - INFO - 批量插入每日数据成功，批次 95，100 条记录
2025-06-02 12:18:50,140 - INFO - 正在批量插入每日数据，批次 96/167，共 100 条记录
2025-06-02 12:18:50,560 - INFO - 批量插入每日数据成功，批次 96，100 条记录
2025-06-02 12:18:53,576 - INFO - 正在批量插入每日数据，批次 97/167，共 100 条记录
2025-06-02 12:18:53,996 - INFO - 批量插入每日数据成功，批次 97，100 条记录
2025-06-02 12:18:57,012 - INFO - 正在批量插入每日数据，批次 98/167，共 100 条记录
2025-06-02 12:18:57,432 - INFO - 批量插入每日数据成功，批次 98，100 条记录
2025-06-02 12:19:00,447 - INFO - 正在批量插入每日数据，批次 99/167，共 100 条记录
2025-06-02 12:19:00,806 - INFO - 批量插入每日数据成功，批次 99，100 条记录
2025-06-02 12:19:03,822 - INFO - 正在批量插入每日数据，批次 100/167，共 100 条记录
2025-06-02 12:19:04,258 - INFO - 批量插入每日数据成功，批次 100，100 条记录
2025-06-02 12:19:07,273 - INFO - 正在批量插入每日数据，批次 101/167，共 100 条记录
2025-06-02 12:19:07,741 - INFO - 批量插入每日数据成功，批次 101，100 条记录
2025-06-02 12:19:10,756 - INFO - 正在批量插入每日数据，批次 102/167，共 100 条记录
2025-06-02 12:19:11,365 - INFO - 批量插入每日数据成功，批次 102，100 条记录
2025-06-02 12:19:14,380 - INFO - 正在批量插入每日数据，批次 103/167，共 100 条记录
2025-06-02 12:19:14,847 - INFO - 批量插入每日数据成功，批次 103，100 条记录
2025-06-02 12:19:17,862 - INFO - 正在批量插入每日数据，批次 104/167，共 100 条记录
2025-06-02 12:19:18,283 - INFO - 批量插入每日数据成功，批次 104，100 条记录
2025-06-02 12:19:21,297 - INFO - 正在批量插入每日数据，批次 105/167，共 100 条记录
2025-06-02 12:19:21,640 - INFO - 批量插入每日数据成功，批次 105，100 条记录
2025-06-02 12:19:24,654 - INFO - 正在批量插入每日数据，批次 106/167，共 100 条记录
2025-06-02 12:19:25,091 - INFO - 批量插入每日数据成功，批次 106，100 条记录
2025-06-02 12:19:28,106 - INFO - 正在批量插入每日数据，批次 107/167，共 100 条记录
2025-06-02 12:19:28,527 - INFO - 批量插入每日数据成功，批次 107，100 条记录
2025-06-02 12:19:31,542 - INFO - 正在批量插入每日数据，批次 108/167，共 100 条记录
2025-06-02 12:19:31,979 - INFO - 批量插入每日数据成功，批次 108，100 条记录
2025-06-02 12:19:34,994 - INFO - 正在批量插入每日数据，批次 109/167，共 100 条记录
2025-06-02 12:19:35,462 - INFO - 批量插入每日数据成功，批次 109，100 条记录
2025-06-02 12:19:38,477 - INFO - 正在批量插入每日数据，批次 110/167，共 100 条记录
2025-06-02 12:19:39,053 - INFO - 批量插入每日数据成功，批次 110，100 条记录
2025-06-02 12:19:42,067 - INFO - 正在批量插入每日数据，批次 111/167，共 100 条记录
2025-06-02 12:19:42,549 - INFO - 批量插入每日数据成功，批次 111，100 条记录
2025-06-02 12:19:45,564 - INFO - 正在批量插入每日数据，批次 112/167，共 100 条记录
2025-06-02 12:19:46,031 - INFO - 批量插入每日数据成功，批次 112，100 条记录
2025-06-02 12:19:49,047 - INFO - 正在批量插入每日数据，批次 113/167，共 100 条记录
2025-06-02 12:19:49,514 - INFO - 批量插入每日数据成功，批次 113，100 条记录
2025-06-02 12:19:52,530 - INFO - 正在批量插入每日数据，批次 114/167，共 100 条记录
2025-06-02 12:19:52,951 - INFO - 批量插入每日数据成功，批次 114，100 条记录
2025-06-02 12:19:55,967 - INFO - 正在批量插入每日数据，批次 115/167，共 100 条记录
2025-06-02 12:19:56,356 - INFO - 批量插入每日数据成功，批次 115，100 条记录
2025-06-02 12:19:59,370 - INFO - 正在批量插入每日数据，批次 116/167，共 100 条记录
2025-06-02 12:19:59,948 - INFO - 批量插入每日数据成功，批次 116，100 条记录
2025-06-02 12:20:02,963 - INFO - 正在批量插入每日数据，批次 117/167，共 100 条记录
2025-06-02 12:20:03,401 - INFO - 批量插入每日数据成功，批次 117，100 条记录
2025-06-02 12:20:06,415 - INFO - 正在批量插入每日数据，批次 118/167，共 100 条记录
2025-06-02 12:20:06,821 - INFO - 批量插入每日数据成功，批次 118，100 条记录
2025-06-02 12:20:09,835 - INFO - 正在批量插入每日数据，批次 119/167，共 100 条记录
2025-06-02 12:20:10,209 - INFO - 批量插入每日数据成功，批次 119，100 条记录
2025-06-02 12:20:13,239 - INFO - 正在批量插入每日数据，批次 120/167，共 100 条记录
2025-06-02 12:20:13,644 - INFO - 批量插入每日数据成功，批次 120，100 条记录
2025-06-02 12:20:16,648 - INFO - 正在批量插入每日数据，批次 121/167，共 100 条记录
2025-06-02 12:20:17,060 - INFO - 批量插入每日数据成功，批次 121，100 条记录
2025-06-02 12:20:20,064 - INFO - 正在批量插入每日数据，批次 122/167，共 100 条记录
2025-06-02 12:20:20,502 - INFO - 批量插入每日数据成功，批次 122，100 条记录
2025-06-02 12:20:23,515 - INFO - 正在批量插入每日数据，批次 123/167，共 100 条记录
2025-06-02 12:20:23,916 - INFO - 批量插入每日数据成功，批次 123，100 条记录
2025-06-02 12:20:26,930 - INFO - 正在批量插入每日数据，批次 124/167，共 100 条记录
2025-06-02 12:20:27,409 - INFO - 批量插入每日数据成功，批次 124，100 条记录
2025-06-02 12:20:30,423 - INFO - 正在批量插入每日数据，批次 125/167，共 100 条记录
2025-06-02 12:20:30,872 - INFO - 批量插入每日数据成功，批次 125，100 条记录
2025-06-02 12:20:33,877 - INFO - 正在批量插入每日数据，批次 126/167，共 100 条记录
2025-06-02 12:20:34,216 - INFO - 批量插入每日数据成功，批次 126，100 条记录
2025-06-02 12:20:37,219 - INFO - 正在批量插入每日数据，批次 127/167，共 100 条记录
2025-06-02 12:20:37,730 - INFO - 批量插入每日数据成功，批次 127，100 条记录
2025-06-02 12:20:40,744 - INFO - 正在批量插入每日数据，批次 128/167，共 100 条记录
2025-06-02 12:20:41,161 - INFO - 批量插入每日数据成功，批次 128，100 条记录
2025-06-02 12:20:44,175 - INFO - 正在批量插入每日数据，批次 129/167，共 100 条记录
2025-06-02 12:20:44,608 - INFO - 批量插入每日数据成功，批次 129，100 条记录
2025-06-02 12:20:47,612 - INFO - 正在批量插入每日数据，批次 130/167，共 100 条记录
2025-06-02 12:20:47,982 - INFO - 批量插入每日数据成功，批次 130，100 条记录
2025-06-02 12:20:50,995 - INFO - 正在批量插入每日数据，批次 131/167，共 100 条记录
2025-06-02 12:20:51,396 - INFO - 批量插入每日数据成功，批次 131，100 条记录
2025-06-02 12:20:54,402 - INFO - 正在批量插入每日数据，批次 132/167，共 100 条记录
2025-06-02 12:20:54,838 - INFO - 批量插入每日数据成功，批次 132，100 条记录
2025-06-02 12:20:57,844 - INFO - 正在批量插入每日数据，批次 133/167，共 100 条记录
2025-06-02 12:20:58,281 - INFO - 批量插入每日数据成功，批次 133，100 条记录
2025-06-02 12:21:01,297 - INFO - 正在批量插入每日数据，批次 134/167，共 100 条记录
2025-06-02 12:21:01,777 - INFO - 批量插入每日数据成功，批次 134，100 条记录
2025-06-02 12:21:04,790 - INFO - 正在批量插入每日数据，批次 135/167，共 100 条记录
2025-06-02 12:21:05,288 - INFO - 批量插入每日数据成功，批次 135，100 条记录
2025-06-02 12:21:08,304 - INFO - 正在批量插入每日数据，批次 136/167，共 100 条记录
2025-06-02 12:21:08,696 - INFO - 批量插入每日数据成功，批次 136，100 条记录
2025-06-02 12:21:11,725 - INFO - 正在批量插入每日数据，批次 137/167，共 100 条记录
2025-06-02 12:21:12,161 - INFO - 批量插入每日数据成功，批次 137，100 条记录
2025-06-02 12:21:15,175 - INFO - 正在批量插入每日数据，批次 138/167，共 100 条记录
2025-06-02 12:21:15,607 - INFO - 批量插入每日数据成功，批次 138，100 条记录
2025-06-02 12:21:18,613 - INFO - 正在批量插入每日数据，批次 139/167，共 100 条记录
2025-06-02 12:21:19,294 - INFO - 批量插入每日数据成功，批次 139，100 条记录
2025-06-02 12:21:22,296 - INFO - 正在批量插入每日数据，批次 140/167，共 100 条记录
2025-06-02 12:21:22,731 - INFO - 批量插入每日数据成功，批次 140，100 条记录
2025-06-02 12:21:25,748 - INFO - 正在批量插入每日数据，批次 141/167，共 100 条记录
2025-06-02 12:21:26,146 - INFO - 批量插入每日数据成功，批次 141，100 条记录
2025-06-02 12:21:29,162 - INFO - 正在批量插入每日数据，批次 142/167，共 100 条记录
2025-06-02 12:21:29,595 - INFO - 批量插入每日数据成功，批次 142，100 条记录
2025-06-02 12:21:32,600 - INFO - 正在批量插入每日数据，批次 143/167，共 100 条记录
2025-06-02 12:21:33,021 - INFO - 批量插入每日数据成功，批次 143，100 条记录
2025-06-02 12:21:36,025 - INFO - 正在批量插入每日数据，批次 144/167，共 100 条记录
2025-06-02 12:21:36,444 - INFO - 批量插入每日数据成功，批次 144，100 条记录
2025-06-02 12:21:39,445 - INFO - 正在批量插入每日数据，批次 145/167，共 100 条记录
2025-06-02 12:21:39,835 - INFO - 批量插入每日数据成功，批次 145，100 条记录
2025-06-02 12:21:42,851 - INFO - 正在批量插入每日数据，批次 146/167，共 100 条记录
2025-06-02 12:21:43,252 - INFO - 批量插入每日数据成功，批次 146，100 条记录
2025-06-02 12:21:46,258 - INFO - 正在批量插入每日数据，批次 147/167，共 100 条记录
2025-06-02 12:21:46,693 - INFO - 批量插入每日数据成功，批次 147，100 条记录
2025-06-02 12:21:49,708 - INFO - 正在批量插入每日数据，批次 148/167，共 100 条记录
2025-06-02 12:21:50,204 - INFO - 批量插入每日数据成功，批次 148，100 条记录
2025-06-02 12:21:53,205 - INFO - 正在批量插入每日数据，批次 149/167，共 100 条记录
2025-06-02 12:21:53,685 - INFO - 批量插入每日数据成功，批次 149，100 条记录
2025-06-02 12:21:56,687 - INFO - 正在批量插入每日数据，批次 150/167，共 100 条记录
2025-06-02 12:21:57,057 - INFO - 批量插入每日数据成功，批次 150，100 条记录
2025-06-02 12:22:00,062 - INFO - 正在批量插入每日数据，批次 151/167，共 100 条记录
2025-06-02 12:22:00,557 - INFO - 批量插入每日数据成功，批次 151，100 条记录
2025-06-02 12:22:03,559 - INFO - 正在批量插入每日数据，批次 152/167，共 100 条记录
2025-06-02 12:22:03,898 - INFO - 批量插入每日数据成功，批次 152，100 条记录
2025-06-02 12:22:06,914 - INFO - 正在批量插入每日数据，批次 153/167，共 100 条记录
2025-06-02 12:22:07,366 - INFO - 批量插入每日数据成功，批次 153，100 条记录
2025-06-02 12:22:10,382 - INFO - 正在批量插入每日数据，批次 154/167，共 100 条记录
2025-06-02 12:22:10,849 - INFO - 批量插入每日数据成功，批次 154，100 条记录
2025-06-02 12:22:13,855 - INFO - 正在批量插入每日数据，批次 155/167，共 100 条记录
2025-06-02 12:22:14,276 - INFO - 批量插入每日数据成功，批次 155，100 条记录
2025-06-02 12:22:17,291 - INFO - 正在批量插入每日数据，批次 156/167，共 100 条记录
2025-06-02 12:22:17,786 - INFO - 批量插入每日数据成功，批次 156，100 条记录
2025-06-02 12:22:20,802 - INFO - 正在批量插入每日数据，批次 157/167，共 100 条记录
2025-06-02 12:22:21,145 - INFO - 批量插入每日数据成功，批次 157，100 条记录
2025-06-02 12:22:24,161 - INFO - 正在批量插入每日数据，批次 158/167，共 100 条记录
2025-06-02 12:22:24,535 - INFO - 批量插入每日数据成功，批次 158，100 条记录
2025-06-02 12:22:27,543 - INFO - 正在批量插入每日数据，批次 159/167，共 100 条记录
2025-06-02 12:22:28,059 - INFO - 批量插入每日数据成功，批次 159，100 条记录
2025-06-02 12:22:31,063 - INFO - 正在批量插入每日数据，批次 160/167，共 100 条记录
2025-06-02 12:22:31,562 - INFO - 批量插入每日数据成功，批次 160，100 条记录
2025-06-02 12:22:34,577 - INFO - 正在批量插入每日数据，批次 161/167，共 100 条记录
2025-06-02 12:22:35,035 - INFO - 批量插入每日数据成功，批次 161，100 条记录
2025-06-02 12:22:38,047 - INFO - 正在批量插入每日数据，批次 162/167，共 100 条记录
2025-06-02 12:22:38,452 - INFO - 批量插入每日数据成功，批次 162，100 条记录
2025-06-02 12:22:41,458 - INFO - 正在批量插入每日数据，批次 163/167，共 100 条记录
2025-06-02 12:22:41,877 - INFO - 批量插入每日数据成功，批次 163，100 条记录
2025-06-02 12:22:44,892 - INFO - 正在批量插入每日数据，批次 164/167，共 100 条记录
2025-06-02 12:22:45,313 - INFO - 批量插入每日数据成功，批次 164，100 条记录
2025-06-02 12:22:48,328 - INFO - 正在批量插入每日数据，批次 165/167，共 100 条记录
2025-06-02 12:22:48,827 - INFO - 批量插入每日数据成功，批次 165，100 条记录
2025-06-02 12:22:51,843 - INFO - 正在批量插入每日数据，批次 166/167，共 100 条记录
2025-06-02 12:22:52,233 - INFO - 批量插入每日数据成功，批次 166，100 条记录
2025-06-02 12:22:55,249 - INFO - 正在批量插入每日数据，批次 167/167，共 37 条记录
2025-06-02 12:22:55,514 - INFO - 批量插入每日数据成功，批次 167，37 条记录
2025-06-02 12:22:58,530 - INFO - 批量插入每日数据完成: 总计 16637 条，成功 16637 条，失败 0 条
2025-06-02 12:22:58,551 - INFO - 批量插入日销售数据完成，共 16637 条记录
2025-06-02 12:22:58,551 - INFO - 日销售数据同步完成！更新: 0 条，插入: 16637 条，错误: 0 条，跳过: 212 条
2025-06-02 12:22:58,552 - INFO - 正在获取宜搭月销售表单数据...
2025-06-02 12:22:58,552 - INFO - 开始获取宜搭月度表单数据，总时间范围: 2025-03-01 至 2025-06-30
2025-06-02 12:22:58,552 - INFO - 查询月度分段 1: 2025-03-01 至 2025-05-31
2025-06-02 12:22:58,552 - INFO - 查询日期范围: 2025-03-01 至 2025-05-31，使用分页查询，每页 100 条记录
2025-06-02 12:22:58,552 - INFO - Request Parameters - Page 1:
2025-06-02 12:22:58,553 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:22:58,553 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:22:59,388 - INFO - API请求耗时: 835ms
2025-06-02 12:22:59,388 - INFO - Response - Page 1
2025-06-02 12:22:59,388 - INFO - 第 1 页获取到 100 条记录
2025-06-02 12:22:59,903 - INFO - Request Parameters - Page 2:
2025-06-02 12:22:59,903 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:22:59,903 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:23:00,544 - INFO - API请求耗时: 641ms
2025-06-02 12:23:00,544 - INFO - Response - Page 2
2025-06-02 12:23:00,544 - INFO - 第 2 页获取到 100 条记录
2025-06-02 12:23:01,058 - INFO - Request Parameters - Page 3:
2025-06-02 12:23:01,058 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:23:01,058 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:23:01,604 - INFO - API请求耗时: 546ms
2025-06-02 12:23:01,604 - INFO - Response - Page 3
2025-06-02 12:23:01,604 - INFO - 第 3 页获取到 100 条记录
2025-06-02 12:23:02,118 - INFO - Request Parameters - Page 4:
2025-06-02 12:23:02,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:23:02,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:23:02,665 - INFO - API请求耗时: 547ms
2025-06-02 12:23:02,665 - INFO - Response - Page 4
2025-06-02 12:23:02,665 - INFO - 第 4 页获取到 100 条记录
2025-06-02 12:23:03,180 - INFO - Request Parameters - Page 5:
2025-06-02 12:23:03,180 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:23:03,180 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:23:03,883 - INFO - API请求耗时: 703ms
2025-06-02 12:23:03,883 - INFO - Response - Page 5
2025-06-02 12:23:03,883 - INFO - 第 5 页获取到 100 条记录
2025-06-02 12:23:04,398 - INFO - Request Parameters - Page 6:
2025-06-02 12:23:04,398 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:23:04,398 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:23:04,976 - INFO - API请求耗时: 578ms
2025-06-02 12:23:04,976 - INFO - Response - Page 6
2025-06-02 12:23:04,976 - INFO - 第 6 页获取到 100 条记录
2025-06-02 12:23:05,492 - INFO - Request Parameters - Page 7:
2025-06-02 12:23:05,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:23:05,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:23:06,116 - INFO - API请求耗时: 624ms
2025-06-02 12:23:06,116 - INFO - Response - Page 7
2025-06-02 12:23:06,116 - INFO - 第 7 页获取到 98 条记录
2025-06-02 12:23:06,116 - INFO - 查询完成，共获取到 698 条记录
2025-06-02 12:23:06,116 - INFO - 月度分段 1 查询成功，获取到 698 条记录
2025-06-02 12:23:07,132 - INFO - 查询月度分段 2: 2025-06-01 至 2025-06-30
2025-06-02 12:23:07,132 - INFO - 查询日期范围: 2025-06-01 至 2025-06-30，使用分页查询，每页 100 条记录
2025-06-02 12:23:07,132 - INFO - Request Parameters - Page 1:
2025-06-02 12:23:07,132 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:23:07,132 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:23:07,334 - INFO - API请求耗时: 202ms
2025-06-02 12:23:07,334 - INFO - Response - Page 1
2025-06-02 12:23:07,334 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-02 12:23:07,334 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 12:23:07,334 - WARNING - 月度分段 2 查询返回空数据
2025-06-02 12:23:08,350 - INFO - 宜搭月度表单数据查询完成，共 2 个分段，成功获取 698 条记录，失败 0 次
2025-06-02 12:23:08,350 - INFO - 成功获取宜搭月销售表单数据，共 698 条记录
2025-06-02 12:23:08,350 - INFO - 宜搭月销售数据项结构示例: ['formInstanceId', 'formData']
2025-06-02 12:23:08,350 - INFO - 正在从SQLite获取月度汇总数据...
2025-06-02 12:23:08,350 - INFO - 成功获取SQLite月度汇总数据，共 902 条记录
2025-06-02 12:23:08,396 - INFO - 成功创建宜搭月销售数据索引，共 698 条记录
2025-06-02 12:23:08,397 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:08,397 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:08,397 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:08,397 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:08,397 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:08,397 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:08,397 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:08,397 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:08,397 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:08,397 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-02 12:23:08,926 - INFO - 更新表单数据成功: FINST-RN766181H5VUOSQ29BNNDCKN1TOD3J4YMXX9MKE
2025-06-02 12:23:08,926 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_2025-03, 变更字段: [{'field': 'amount', 'old_value': 22788.82, 'new_value': 23052.52}, {'field': 'count', 'old_value': 126, 'new_value': 127}, {'field': 'instoreAmount', 'old_value': 26666.22, 'new_value': 26930.22}, {'field': 'instoreCount', 'old_value': 126, 'new_value': 127}]
2025-06-02 12:23:08,942 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-02 12:23:09,411 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMNL
2025-06-02 12:23:09,411 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_2025-05, 变更字段: [{'field': 'amount', 'old_value': 39748.4, 'new_value': 40043.8}, {'field': 'count', 'old_value': 157, 'new_value': 158}, {'field': 'instoreAmount', 'old_value': 39750.8, 'new_value': 40046.8}, {'field': 'instoreCount', 'old_value': 157, 'new_value': 158}]
2025-06-02 12:23:09,411 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,411 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,411 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,411 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,411 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,411 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,411 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,411 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,411 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,411 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,411 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,411 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,411 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,411 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,411 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:09,426 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-02 12:23:09,878 - INFO - 更新表单数据成功: FINST-FD966QA1M5VUULMLBGN4Z7W18VYC23U8NXX9MJL
2025-06-02 12:23:09,878 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_2025-03, 变更字段: [{'field': 'recommendAmount', 'old_value': 364516.7, 'new_value': 365874.5}, {'field': 'amount', 'old_value': 364516.7, 'new_value': 365873.9}, {'field': 'count', 'old_value': 1463, 'new_value': 1465}, {'field': 'instoreAmount', 'old_value': 368830.2, 'new_value': 370188.0}, {'field': 'instoreCount', 'old_value': 1463, 'new_value': 1465}]
2025-06-02 12:23:09,878 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-02 12:23:10,362 - INFO - 更新表单数据成功: FINST-RI766091Q8ZUDV4P6CERK73FVYBF3UQMQBAAMQC1
2025-06-02 12:23:10,362 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_2025-05, 变更字段: [{'field': 'instoreAmount', 'old_value': 159943.36, 'new_value': 159945.36}, {'field': 'instoreCount', 'old_value': 10553, 'new_value': 10554}, {'field': 'onlineAmount', 'old_value': 47301.07, 'new_value': 47299.07}, {'field': 'onlineCount', 'old_value': 3208, 'new_value': 3207}]
2025-06-02 12:23:10,362 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-02 12:23:10,909 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1M
2025-06-02 12:23:10,909 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_2025-05, 变更字段: [{'field': 'amount', 'old_value': 146930.84, 'new_value': 146935.84}, {'field': 'count', 'old_value': 11528, 'new_value': 11533}, {'field': 'onlineAmount', 'old_value': 141942.85, 'new_value': 141947.87}, {'field': 'onlineCount', 'old_value': 10878, 'new_value': 10883}]
2025-06-02 12:23:10,909 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-02 12:23:11,393 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM6M
2025-06-02 12:23:11,393 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE13J2R9CI0I86N3H2U13D001ECJ_2025-05, 变更字段: [{'field': 'amount', 'old_value': 407517.0, 'new_value': 408843.0}, {'field': 'count', 'old_value': 318, 'new_value': 319}, {'field': 'instoreAmount', 'old_value': 441588.0, 'new_value': 442914.0}, {'field': 'instoreCount', 'old_value': 318, 'new_value': 319}]
2025-06-02 12:23:11,393 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,393 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,393 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,393 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,393 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,393 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,393 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,393 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,393 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,393 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,393 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,393 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,393 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,393 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,393 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,393 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,393 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,393 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,393 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,393 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,393 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,393 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,409 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-02 12:23:11,940 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMN81
2025-06-02 12:23:11,940 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 96722.18, 'new_value': 96718.68}, {'field': 'amount', 'old_value': 96709.45, 'new_value': 96704.98}]
2025-06-02 12:23:11,940 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,940 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,940 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,940 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,940 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,940 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,940 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,940 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:11,940 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-02 12:23:12,409 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9MC4
2025-06-02 12:23:12,409 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_2025-03, 变更字段: [{'field': 'amount', 'old_value': 887670.18, 'new_value': 888592.74}, {'field': 'count', 'old_value': 4343, 'new_value': 4344}, {'field': 'instoreAmount', 'old_value': 789458.1, 'new_value': 790381.1}, {'field': 'instoreCount', 'old_value': 3255, 'new_value': 3256}]
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,425 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,440 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,440 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,442 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,442 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,442 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,443 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,443 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,443 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,444 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-02 12:23:12,450 - INFO - 正在批量插入月度数据，批次 1/3，共 100 条记录
2025-06-02 12:23:12,814 - INFO - 批量插入月度数据成功，批次 1，100 条记录
2025-06-02 12:23:15,829 - INFO - 正在批量插入月度数据，批次 2/3，共 100 条记录
2025-06-02 12:23:16,251 - INFO - 批量插入月度数据成功，批次 2，100 条记录
2025-06-02 12:23:19,266 - INFO - 正在批量插入月度数据，批次 3/3，共 4 条记录
2025-06-02 12:23:19,408 - INFO - 批量插入月度数据成功，批次 3，4 条记录
2025-06-02 12:23:22,423 - INFO - 批量插入月度数据完成: 总计 204 条，成功 204 条，失败 0 条
2025-06-02 12:23:22,423 - INFO - 批量插入月销售数据完成，共 204 条记录
2025-06-02 12:23:22,423 - INFO - 月销售数据同步完成！更新: 8 条，插入: 204 条，错误: 0 条，跳过: 690 条
2025-06-02 12:23:22,423 - INFO - 开始导出月度汇总数据到Excel，时间范围: 2025-3 至 2025-6
2025-06-02 12:23:22,797 - INFO - 月度汇总数据已导出到Excel文件: logs/数衍平台月度数据_********.xlsx
2025-06-02 12:23:22,797 - INFO - 综合数据同步流程完成！
2025-06-02 12:23:22,829 - INFO - 综合数据同步完成
2025-06-02 12:23:22,829 - INFO - ==================================================
2025-06-02 12:23:22,829 - INFO - 程序退出
2025-06-02 12:23:22,829 - INFO - ==================================================
