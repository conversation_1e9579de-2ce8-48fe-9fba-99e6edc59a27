2025-06-02 00:30:33,844 - INFO - 使用默认增量同步（当天更新数据）
2025-06-02 00:30:33,844 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 00:30:33,844 - INFO - 查询参数: ('2025-06-02',)
2025-06-02 00:30:33,923 - INFO - MySQL查询成功，增量数据（日期: 2025-06-02），共获取 1 条记录
2025-06-02 00:30:33,923 - INFO - 获取到 1 个日期需要处理: ['2025-06-01']
2025-06-02 00:30:33,923 - INFO - 开始处理日期: 2025-06-01
2025-06-02 00:30:33,923 - INFO - Request Parameters - Page 1:
2025-06-02 00:30:33,923 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:30:33,923 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:30:42,049 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2292D442-D196-7989-AAD7-6F90D7B781F1 Response: {'code': 'ServiceUnavailable', 'requestid': '2292D442-D196-7989-AAD7-6F90D7B781F1', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2292D442-D196-7989-AAD7-6F90D7B781F1)
2025-06-02 00:30:42,049 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-02 00:31:42,074 - INFO - 开始同步昨天与今天的销售数据: 2025-06-01 至 2025-06-02
2025-06-02 00:31:42,074 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 00:31:42,074 - INFO - 查询参数: ('2025-06-01', '2025-06-02')
2025-06-02 00:31:42,137 - INFO - MySQL查询成功，时间段: 2025-06-01 至 2025-06-02，共获取 94 条记录
2025-06-02 00:31:42,137 - INFO - 获取到 1 个日期需要处理: ['2025-06-01']
2025-06-02 00:31:42,137 - INFO - 开始处理日期: 2025-06-01
2025-06-02 00:31:42,137 - INFO - Request Parameters - Page 1:
2025-06-02 00:31:42,137 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 00:31:42,137 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 00:31:43,027 - INFO - Response - Page 1:
2025-06-02 00:31:43,027 - INFO - 第 1 页获取到 77 条记录
2025-06-02 00:31:43,231 - INFO - 查询完成，共获取到 77 条记录
2025-06-02 00:31:43,231 - INFO - 获取到 77 条表单数据
2025-06-02 00:31:43,231 - INFO - 当前日期 2025-06-01 有 94 条MySQL数据需要处理
2025-06-02 00:31:43,231 - INFO - 开始批量插入 17 条新记录
2025-06-02 00:31:43,402 - INFO - 批量插入响应状态码: 200
2025-06-02 00:31:43,402 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 01 Jun 2025 16:31:42 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '828', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6D636276-E61C-7A6F-AD9F-3CFF931B26D3', 'x-acs-trace-id': 'f8d5b3e7b0ae2608735f931e3e56b201', 'etag': '8Lz6tUyEXqKRJg8gdWM//CA8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-02 00:31:43,402 - INFO - 批量插入响应体: {'result': ['FINST-EEC66XC1NEUV7P2JC1YF8BRHU2BA39QTOVDBMFS', 'FINST-EEC66XC1NEUV7P2JC1YF8BRHU2BA39QTOVDBMGS', 'FINST-EEC66XC1NEUV7P2JC1YF8BRHU2BA39QTOVDBMHS', 'FINST-EEC66XC1NEUV7P2JC1YF8BRHU2BA39QTOVDBMIS', 'FINST-EEC66XC1NEUV7P2JC1YF8BRHU2BA39QTOVDBMJS', 'FINST-EEC66XC1NEUV7P2JC1YF8BRHU2BA39QTOVDBMKS', 'FINST-EEC66XC1NEUV7P2JC1YF8BRHU2BA39QTOVDBMLS', 'FINST-EEC66XC1NEUV7P2JC1YF8BRHU2BA39QTOVDBMMS', 'FINST-EEC66XC1NEUV7P2JC1YF8BRHU2BA39QTOVDBMNS', 'FINST-EEC66XC1NEUV7P2JC1YF8BRHU2BA39QTOVDBMOS', 'FINST-EEC66XC1NEUV7P2JC1YF8BRHU2BA39QTOVDBMPS', 'FINST-EEC66XC1NEUV7P2JC1YF8BRHU2BA39QTOVDBMQS', 'FINST-EEC66XC1NEUV7P2JC1YF8BRHU2BA39QTOVDBMRS', 'FINST-EEC66XC1NEUV7P2JC1YF8BRHU2BA39QTOVDBMSS', 'FINST-EEC66XC1NEUV7P2JC1YF8BRHU2BA39QTOVDBMTS', 'FINST-EEC66XC1NEUV7P2JC1YF8BRHU2BA39QTOVDBMUS', 'FINST-EEC66XC1NEUV7P2JC1YF8BRHU2BA39QTOVDBMVS']}
2025-06-02 00:31:43,402 - INFO - 批量插入表单数据成功，批次 1，共 17 条记录
2025-06-02 00:31:43,402 - INFO - 成功插入的数据ID: ['FINST-EEC66XC1NEUV7P2JC1YF8BRHU2BA39QTOVDBMFS', 'FINST-EEC66XC1NEUV7P2JC1YF8BRHU2BA39QTOVDBMGS', 'FINST-EEC66XC1NEUV7P2JC1YF8BRHU2BA39QTOVDBMHS', 'FINST-EEC66XC1NEUV7P2JC1YF8BRHU2BA39QTOVDBMIS', 'FINST-EEC66XC1NEUV7P2JC1YF8BRHU2BA39QTOVDBMJS', 'FINST-EEC66XC1NEUV7P2JC1YF8BRHU2BA39QTOVDBMKS', 'FINST-EEC66XC1NEUV7P2JC1YF8BRHU2BA39QTOVDBMLS', 'FINST-EEC66XC1NEUV7P2JC1YF8BRHU2BA39QTOVDBMMS', 'FINST-EEC66XC1NEUV7P2JC1YF8BRHU2BA39QTOVDBMNS', 'FINST-EEC66XC1NEUV7P2JC1YF8BRHU2BA39QTOVDBMOS', 'FINST-EEC66XC1NEUV7P2JC1YF8BRHU2BA39QTOVDBMPS', 'FINST-EEC66XC1NEUV7P2JC1YF8BRHU2BA39QTOVDBMQS', 'FINST-EEC66XC1NEUV7P2JC1YF8BRHU2BA39QTOVDBMRS', 'FINST-EEC66XC1NEUV7P2JC1YF8BRHU2BA39QTOVDBMSS', 'FINST-EEC66XC1NEUV7P2JC1YF8BRHU2BA39QTOVDBMTS', 'FINST-EEC66XC1NEUV7P2JC1YF8BRHU2BA39QTOVDBMUS', 'FINST-EEC66XC1NEUV7P2JC1YF8BRHU2BA39QTOVDBMVS']
2025-06-02 00:31:48,419 - INFO - 批量插入完成，共 17 条记录
2025-06-02 00:31:48,419 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 17 条，错误: 0 条
2025-06-02 00:31:48,419 - INFO - 数据同步完成！更新: 0 条，插入: 17 条，错误: 0 条
2025-06-02 00:31:48,419 - INFO - 同步完成
2025-06-02 01:30:34,091 - INFO - 使用默认增量同步（当天更新数据）
2025-06-02 01:30:34,091 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 01:30:34,091 - INFO - 查询参数: ('2025-06-02',)
2025-06-02 01:30:34,154 - INFO - MySQL查询成功，增量数据（日期: 2025-06-02），共获取 1 条记录
2025-06-02 01:30:34,154 - INFO - 获取到 1 个日期需要处理: ['2025-06-01']
2025-06-02 01:30:34,154 - INFO - 开始处理日期: 2025-06-01
2025-06-02 01:30:34,154 - INFO - Request Parameters - Page 1:
2025-06-02 01:30:34,154 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 01:30:34,154 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 01:30:42,280 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E2C13C03-C8AA-7C3E-BD7D-93720C477DF5 Response: {'code': 'ServiceUnavailable', 'requestid': 'E2C13C03-C8AA-7C3E-BD7D-93720C477DF5', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E2C13C03-C8AA-7C3E-BD7D-93720C477DF5)
2025-06-02 01:30:42,280 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-02 01:31:42,305 - INFO - 开始同步昨天与今天的销售数据: 2025-06-01 至 2025-06-02
2025-06-02 01:31:42,305 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 01:31:42,305 - INFO - 查询参数: ('2025-06-01', '2025-06-02')
2025-06-02 01:31:42,368 - INFO - MySQL查询成功，时间段: 2025-06-01 至 2025-06-02，共获取 94 条记录
2025-06-02 01:31:42,368 - INFO - 获取到 1 个日期需要处理: ['2025-06-01']
2025-06-02 01:31:42,368 - INFO - 开始处理日期: 2025-06-01
2025-06-02 01:31:42,368 - INFO - Request Parameters - Page 1:
2025-06-02 01:31:42,368 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 01:31:42,368 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 01:31:43,306 - INFO - Response - Page 1:
2025-06-02 01:31:43,306 - INFO - 第 1 页获取到 94 条记录
2025-06-02 01:31:43,509 - INFO - 查询完成，共获取到 94 条记录
2025-06-02 01:31:43,509 - INFO - 获取到 94 条表单数据
2025-06-02 01:31:43,509 - INFO - 当前日期 2025-06-01 有 94 条MySQL数据需要处理
2025-06-02 01:31:43,509 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 01:31:43,509 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 01:31:43,509 - INFO - 同步完成
2025-06-02 02:30:33,776 - INFO - 使用默认增量同步（当天更新数据）
2025-06-02 02:30:33,776 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 02:30:33,776 - INFO - 查询参数: ('2025-06-02',)
2025-06-02 02:30:33,839 - INFO - MySQL查询成功，增量数据（日期: 2025-06-02），共获取 1 条记录
2025-06-02 02:30:33,839 - INFO - 获取到 1 个日期需要处理: ['2025-06-01']
2025-06-02 02:30:33,839 - INFO - 开始处理日期: 2025-06-01
2025-06-02 02:30:33,839 - INFO - Request Parameters - Page 1:
2025-06-02 02:30:33,839 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 02:30:33,839 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 02:30:41,965 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D6305D27-FBEC-770B-9FC4-33E658A6FE0B Response: {'code': 'ServiceUnavailable', 'requestid': 'D6305D27-FBEC-770B-9FC4-33E658A6FE0B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D6305D27-FBEC-770B-9FC4-33E658A6FE0B)
2025-06-02 02:30:41,965 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-02 02:31:41,990 - INFO - 开始同步昨天与今天的销售数据: 2025-06-01 至 2025-06-02
2025-06-02 02:31:41,990 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 02:31:41,990 - INFO - 查询参数: ('2025-06-01', '2025-06-02')
2025-06-02 02:31:42,053 - INFO - MySQL查询成功，时间段: 2025-06-01 至 2025-06-02，共获取 94 条记录
2025-06-02 02:31:42,053 - INFO - 获取到 1 个日期需要处理: ['2025-06-01']
2025-06-02 02:31:42,053 - INFO - 开始处理日期: 2025-06-01
2025-06-02 02:31:42,053 - INFO - Request Parameters - Page 1:
2025-06-02 02:31:42,053 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 02:31:42,053 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 02:31:42,975 - INFO - Response - Page 1:
2025-06-02 02:31:42,975 - INFO - 第 1 页获取到 94 条记录
2025-06-02 02:31:43,178 - INFO - 查询完成，共获取到 94 条记录
2025-06-02 02:31:43,178 - INFO - 获取到 94 条表单数据
2025-06-02 02:31:43,178 - INFO - 当前日期 2025-06-01 有 94 条MySQL数据需要处理
2025-06-02 02:31:43,178 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 02:31:43,178 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 02:31:43,178 - INFO - 同步完成
2025-06-02 03:30:33,852 - INFO - 使用默认增量同步（当天更新数据）
2025-06-02 03:30:33,852 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 03:30:33,852 - INFO - 查询参数: ('2025-06-02',)
2025-06-02 03:30:33,914 - INFO - MySQL查询成功，增量数据（日期: 2025-06-02），共获取 1 条记录
2025-06-02 03:30:33,914 - INFO - 获取到 1 个日期需要处理: ['2025-06-01']
2025-06-02 03:30:33,914 - INFO - 开始处理日期: 2025-06-01
2025-06-02 03:30:33,930 - INFO - Request Parameters - Page 1:
2025-06-02 03:30:33,930 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:30:33,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:30:42,040 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: BE0317E9-A609-756A-8401-D94FC8FC70E7 Response: {'code': 'ServiceUnavailable', 'requestid': 'BE0317E9-A609-756A-8401-D94FC8FC70E7', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: BE0317E9-A609-756A-8401-D94FC8FC70E7)
2025-06-02 03:30:42,040 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-02 03:31:42,066 - INFO - 开始同步昨天与今天的销售数据: 2025-06-01 至 2025-06-02
2025-06-02 03:31:42,066 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 03:31:42,066 - INFO - 查询参数: ('2025-06-01', '2025-06-02')
2025-06-02 03:31:42,128 - INFO - MySQL查询成功，时间段: 2025-06-01 至 2025-06-02，共获取 94 条记录
2025-06-02 03:31:42,128 - INFO - 获取到 1 个日期需要处理: ['2025-06-01']
2025-06-02 03:31:42,128 - INFO - 开始处理日期: 2025-06-01
2025-06-02 03:31:42,128 - INFO - Request Parameters - Page 1:
2025-06-02 03:31:42,128 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 03:31:42,128 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 03:31:50,239 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3A656610-3359-70C2-AEBB-502B42DF2100 Response: {'code': 'ServiceUnavailable', 'requestid': '3A656610-3359-70C2-AEBB-502B42DF2100', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3A656610-3359-70C2-AEBB-502B42DF2100)
2025-06-02 03:31:50,239 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-02 03:31:50,239 - INFO - 同步完成
2025-06-02 04:30:34,130 - INFO - 使用默认增量同步（当天更新数据）
2025-06-02 04:30:34,130 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 04:30:34,130 - INFO - 查询参数: ('2025-06-02',)
2025-06-02 04:30:34,192 - INFO - MySQL查询成功，增量数据（日期: 2025-06-02），共获取 11 条记录
2025-06-02 04:30:34,192 - INFO - 获取到 1 个日期需要处理: ['2025-06-01']
2025-06-02 04:30:34,192 - INFO - 开始处理日期: 2025-06-01
2025-06-02 04:30:34,192 - INFO - Request Parameters - Page 1:
2025-06-02 04:30:34,192 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 04:30:34,192 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 04:30:42,334 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 678345B8-8C37-70A2-94B0-ACDF7731EF95 Response: {'code': 'ServiceUnavailable', 'requestid': '678345B8-8C37-70A2-94B0-ACDF7731EF95', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 678345B8-8C37-70A2-94B0-ACDF7731EF95)
2025-06-02 04:30:42,334 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-02 04:31:42,360 - INFO - 开始同步昨天与今天的销售数据: 2025-06-01 至 2025-06-02
2025-06-02 04:31:42,360 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 04:31:42,360 - INFO - 查询参数: ('2025-06-01', '2025-06-02')
2025-06-02 04:31:42,422 - INFO - MySQL查询成功，时间段: 2025-06-01 至 2025-06-02，共获取 134 条记录
2025-06-02 04:31:42,422 - INFO - 获取到 1 个日期需要处理: ['2025-06-01']
2025-06-02 04:31:42,422 - INFO - 开始处理日期: 2025-06-01
2025-06-02 04:31:42,422 - INFO - Request Parameters - Page 1:
2025-06-02 04:31:42,422 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 04:31:42,422 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 04:31:43,422 - INFO - Response - Page 1:
2025-06-02 04:31:43,422 - INFO - 第 1 页获取到 94 条记录
2025-06-02 04:31:43,625 - INFO - 查询完成，共获取到 94 条记录
2025-06-02 04:31:43,625 - INFO - 获取到 94 条表单数据
2025-06-02 04:31:43,625 - INFO - 当前日期 2025-06-01 有 134 条MySQL数据需要处理
2025-06-02 04:31:43,625 - INFO - 开始批量插入 40 条新记录
2025-06-02 04:31:43,844 - INFO - 批量插入响应状态码: 200
2025-06-02 04:31:43,844 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 01 Jun 2025 20:31:40 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1932', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '9DAEBE2C-7D0A-7981-A24D-E8374D0FF64C', 'x-acs-trace-id': '08d10f4712a9ff4c697d5b13ca38d0b8', 'etag': '1oaAWxRVgqr9W/B4yqk861Q2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-02 04:31:43,844 - INFO - 批量插入响应体: {'result': ['FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3GEF94EBM0F', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3GEF94EBM1F', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3GEF94EBM2F', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3GEF94EBM3F', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3GEF94EBM4F', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3GEF94EBM5F', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3GEF94EBM6F', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3GEF94EBM7F', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3GEF94EBM8F', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3GEF94EBM9F', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3GEF94EBMAF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3GEF94EBMBF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3GEF94EBMCF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3GEF94EBMDF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3GEF94EBMEF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3GEF94EBMFF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMGF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMHF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMIF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMJF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMKF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMLF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMMF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMNF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMOF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMPF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMQF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMRF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMSF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMTF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMUF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMVF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMWF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMXF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMYF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMZF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBM0G', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBM1G', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBM2G', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBM3G']}
2025-06-02 04:31:43,844 - INFO - 批量插入表单数据成功，批次 1，共 40 条记录
2025-06-02 04:31:43,844 - INFO - 成功插入的数据ID: ['FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3GEF94EBM0F', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3GEF94EBM1F', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3GEF94EBM2F', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3GEF94EBM3F', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3GEF94EBM4F', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3GEF94EBM5F', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3GEF94EBM6F', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3GEF94EBM7F', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3GEF94EBM8F', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3GEF94EBM9F', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3GEF94EBMAF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3GEF94EBMBF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3GEF94EBMCF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3GEF94EBMDF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3GEF94EBMEF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3GEF94EBMFF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMGF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMHF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMIF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMJF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMKF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMLF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMMF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMNF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMOF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMPF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMQF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMRF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMSF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMTF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMUF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMVF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMWF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMXF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMYF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBMZF', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBM0G', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBM1G', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBM2G', 'FINST-L5766E71CIWVSXWBEA86GA4Y3BBL3HEF94EBM3G']
2025-06-02 04:31:48,861 - INFO - 批量插入完成，共 40 条记录
2025-06-02 04:31:48,861 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 40 条，错误: 0 条
2025-06-02 04:31:48,861 - INFO - 数据同步完成！更新: 0 条，插入: 40 条，错误: 0 条
2025-06-02 04:31:48,861 - INFO - 同步完成
2025-06-02 05:30:34,095 - INFO - 使用默认增量同步（当天更新数据）
2025-06-02 05:30:34,095 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 05:30:34,095 - INFO - 查询参数: ('2025-06-02',)
2025-06-02 05:30:34,158 - INFO - MySQL查询成功，增量数据（日期: 2025-06-02），共获取 11 条记录
2025-06-02 05:30:34,158 - INFO - 获取到 1 个日期需要处理: ['2025-06-01']
2025-06-02 05:30:34,174 - INFO - 开始处理日期: 2025-06-01
2025-06-02 05:30:34,174 - INFO - Request Parameters - Page 1:
2025-06-02 05:30:34,174 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 05:30:34,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 05:30:42,284 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 9C40621F-2415-7D4E-8DE3-709DF0F45156 Response: {'code': 'ServiceUnavailable', 'requestid': '9C40621F-2415-7D4E-8DE3-709DF0F45156', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 9C40621F-2415-7D4E-8DE3-709DF0F45156)
2025-06-02 05:30:42,284 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-02 05:31:42,310 - INFO - 开始同步昨天与今天的销售数据: 2025-06-01 至 2025-06-02
2025-06-02 05:31:42,310 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 05:31:42,310 - INFO - 查询参数: ('2025-06-01', '2025-06-02')
2025-06-02 05:31:42,372 - INFO - MySQL查询成功，时间段: 2025-06-01 至 2025-06-02，共获取 134 条记录
2025-06-02 05:31:42,372 - INFO - 获取到 1 个日期需要处理: ['2025-06-01']
2025-06-02 05:31:42,372 - INFO - 开始处理日期: 2025-06-01
2025-06-02 05:31:42,372 - INFO - Request Parameters - Page 1:
2025-06-02 05:31:42,372 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 05:31:42,372 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 05:31:43,403 - INFO - Response - Page 1:
2025-06-02 05:31:43,403 - INFO - 第 1 页获取到 100 条记录
2025-06-02 05:31:43,607 - INFO - Request Parameters - Page 2:
2025-06-02 05:31:43,607 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 05:31:43,607 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 05:31:51,702 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2378FD71-364D-7738-9AB5-0EDA4CD0594F Response: {'code': 'ServiceUnavailable', 'requestid': '2378FD71-364D-7738-9AB5-0EDA4CD0594F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2378FD71-364D-7738-9AB5-0EDA4CD0594F)
2025-06-02 05:31:51,702 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-02 05:31:51,702 - INFO - 同步完成
2025-06-02 06:30:33,905 - INFO - 使用默认增量同步（当天更新数据）
2025-06-02 06:30:33,905 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 06:30:33,905 - INFO - 查询参数: ('2025-06-02',)
2025-06-02 06:30:33,967 - INFO - MySQL查询成功，增量数据（日期: 2025-06-02），共获取 11 条记录
2025-06-02 06:30:33,967 - INFO - 获取到 1 个日期需要处理: ['2025-06-01']
2025-06-02 06:30:33,967 - INFO - 开始处理日期: 2025-06-01
2025-06-02 06:30:33,967 - INFO - Request Parameters - Page 1:
2025-06-02 06:30:33,967 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:30:33,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:30:42,094 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E4BA877D-E0BF-7722-92FE-EF5EAFF7C6F4 Response: {'code': 'ServiceUnavailable', 'requestid': 'E4BA877D-E0BF-7722-92FE-EF5EAFF7C6F4', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E4BA877D-E0BF-7722-92FE-EF5EAFF7C6F4)
2025-06-02 06:30:42,094 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-02 06:31:42,119 - INFO - 开始同步昨天与今天的销售数据: 2025-06-01 至 2025-06-02
2025-06-02 06:31:42,119 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 06:31:42,119 - INFO - 查询参数: ('2025-06-01', '2025-06-02')
2025-06-02 06:31:42,181 - INFO - MySQL查询成功，时间段: 2025-06-01 至 2025-06-02，共获取 134 条记录
2025-06-02 06:31:42,181 - INFO - 获取到 1 个日期需要处理: ['2025-06-01']
2025-06-02 06:31:42,181 - INFO - 开始处理日期: 2025-06-01
2025-06-02 06:31:42,181 - INFO - Request Parameters - Page 1:
2025-06-02 06:31:42,181 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:31:42,181 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:31:43,119 - INFO - Response - Page 1:
2025-06-02 06:31:43,119 - INFO - 第 1 页获取到 100 条记录
2025-06-02 06:31:43,322 - INFO - Request Parameters - Page 2:
2025-06-02 06:31:43,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 06:31:43,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 06:31:43,994 - INFO - Response - Page 2:
2025-06-02 06:31:43,994 - INFO - 第 2 页获取到 34 条记录
2025-06-02 06:31:44,197 - INFO - 查询完成，共获取到 134 条记录
2025-06-02 06:31:44,197 - INFO - 获取到 134 条表单数据
2025-06-02 06:31:44,197 - INFO - 当前日期 2025-06-01 有 134 条MySQL数据需要处理
2025-06-02 06:31:44,197 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 06:31:44,197 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 06:31:44,197 - INFO - 同步完成
2025-06-02 07:30:34,074 - INFO - 使用默认增量同步（当天更新数据）
2025-06-02 07:30:34,074 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 07:30:34,074 - INFO - 查询参数: ('2025-06-02',)
2025-06-02 07:30:34,152 - INFO - MySQL查询成功，增量数据（日期: 2025-06-02），共获取 11 条记录
2025-06-02 07:30:34,152 - INFO - 获取到 1 个日期需要处理: ['2025-06-01']
2025-06-02 07:30:34,152 - INFO - 开始处理日期: 2025-06-01
2025-06-02 07:30:34,152 - INFO - Request Parameters - Page 1:
2025-06-02 07:30:34,152 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 07:30:34,152 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 07:30:42,278 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B18725F3-0D0B-7165-830D-E2B812BE4690 Response: {'code': 'ServiceUnavailable', 'requestid': 'B18725F3-0D0B-7165-830D-E2B812BE4690', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B18725F3-0D0B-7165-830D-E2B812BE4690)
2025-06-02 07:30:42,278 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-02 07:31:42,304 - INFO - 开始同步昨天与今天的销售数据: 2025-06-01 至 2025-06-02
2025-06-02 07:31:42,304 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 07:31:42,304 - INFO - 查询参数: ('2025-06-01', '2025-06-02')
2025-06-02 07:31:42,366 - INFO - MySQL查询成功，时间段: 2025-06-01 至 2025-06-02，共获取 134 条记录
2025-06-02 07:31:42,366 - INFO - 获取到 1 个日期需要处理: ['2025-06-01']
2025-06-02 07:31:42,366 - INFO - 开始处理日期: 2025-06-01
2025-06-02 07:31:42,366 - INFO - Request Parameters - Page 1:
2025-06-02 07:31:42,366 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 07:31:42,366 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 07:31:43,319 - INFO - Response - Page 1:
2025-06-02 07:31:43,319 - INFO - 第 1 页获取到 100 条记录
2025-06-02 07:31:43,523 - INFO - Request Parameters - Page 2:
2025-06-02 07:31:43,523 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 07:31:43,523 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 07:31:44,195 - INFO - Response - Page 2:
2025-06-02 07:31:44,195 - INFO - 第 2 页获取到 34 条记录
2025-06-02 07:31:44,398 - INFO - 查询完成，共获取到 134 条记录
2025-06-02 07:31:44,398 - INFO - 获取到 134 条表单数据
2025-06-02 07:31:44,398 - INFO - 当前日期 2025-06-01 有 134 条MySQL数据需要处理
2025-06-02 07:31:44,398 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 07:31:44,398 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 07:31:44,398 - INFO - 同步完成
2025-06-02 08:30:34,134 - INFO - 使用默认增量同步（当天更新数据）
2025-06-02 08:30:34,134 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 08:30:34,134 - INFO - 查询参数: ('2025-06-02',)
2025-06-02 08:30:34,196 - INFO - MySQL查询成功，增量数据（日期: 2025-06-02），共获取 13 条记录
2025-06-02 08:30:34,196 - INFO - 获取到 1 个日期需要处理: ['2025-06-01']
2025-06-02 08:30:34,196 - INFO - 开始处理日期: 2025-06-01
2025-06-02 08:30:34,196 - INFO - Request Parameters - Page 1:
2025-06-02 08:30:34,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 08:30:34,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 08:30:42,323 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C80817EC-AF1D-7CEC-8179-72DEF1BE6385 Response: {'code': 'ServiceUnavailable', 'requestid': 'C80817EC-AF1D-7CEC-8179-72DEF1BE6385', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C80817EC-AF1D-7CEC-8179-72DEF1BE6385)
2025-06-02 08:30:42,323 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-02 08:31:42,348 - INFO - 开始同步昨天与今天的销售数据: 2025-06-01 至 2025-06-02
2025-06-02 08:31:42,348 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 08:31:42,348 - INFO - 查询参数: ('2025-06-01', '2025-06-02')
2025-06-02 08:31:42,410 - INFO - MySQL查询成功，时间段: 2025-06-01 至 2025-06-02，共获取 145 条记录
2025-06-02 08:31:42,410 - INFO - 获取到 1 个日期需要处理: ['2025-06-01']
2025-06-02 08:31:42,410 - INFO - 开始处理日期: 2025-06-01
2025-06-02 08:31:42,410 - INFO - Request Parameters - Page 1:
2025-06-02 08:31:42,410 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 08:31:42,410 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 08:31:50,552 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E9DD4D3D-75D9-78F1-9F90-80A58A46A3F7 Response: {'code': 'ServiceUnavailable', 'requestid': 'E9DD4D3D-75D9-78F1-9F90-80A58A46A3F7', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E9DD4D3D-75D9-78F1-9F90-80A58A46A3F7)
2025-06-02 08:31:50,552 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-02 08:31:50,552 - INFO - 同步完成
2025-06-02 09:30:33,881 - INFO - 使用默认增量同步（当天更新数据）
2025-06-02 09:30:33,881 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 09:30:33,881 - INFO - 查询参数: ('2025-06-02',)
2025-06-02 09:30:33,959 - INFO - MySQL查询成功，增量数据（日期: 2025-06-02），共获取 84 条记录
2025-06-02 09:30:33,959 - INFO - 获取到 2 个日期需要处理: ['2025-05-31', '2025-06-01']
2025-06-02 09:30:33,959 - INFO - 开始处理日期: 2025-05-31
2025-06-02 09:30:33,959 - INFO - Request Parameters - Page 1:
2025-06-02 09:30:33,959 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:30:33,959 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:30:42,101 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 019A78D9-EAA8-77C8-85B5-7EC85503059C Response: {'code': 'ServiceUnavailable', 'requestid': '019A78D9-EAA8-77C8-85B5-7EC85503059C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 019A78D9-EAA8-77C8-85B5-7EC85503059C)
2025-06-02 09:30:42,101 - INFO - 开始处理日期: 2025-06-01
2025-06-02 09:30:42,101 - INFO - Request Parameters - Page 1:
2025-06-02 09:30:42,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:30:42,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:30:50,212 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 53CFAAF7-5CBC-7521-9014-1E359E13D12F Response: {'code': 'ServiceUnavailable', 'requestid': '53CFAAF7-5CBC-7521-9014-1E359E13D12F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 53CFAAF7-5CBC-7521-9014-1E359E13D12F)
2025-06-02 09:30:50,212 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-06-02 09:31:50,237 - INFO - 开始同步昨天与今天的销售数据: 2025-06-01 至 2025-06-02
2025-06-02 09:31:50,237 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 09:31:50,237 - INFO - 查询参数: ('2025-06-01', '2025-06-02')
2025-06-02 09:31:50,315 - INFO - MySQL查询成功，时间段: 2025-06-01 至 2025-06-02，共获取 393 条记录
2025-06-02 09:31:50,315 - INFO - 获取到 1 个日期需要处理: ['2025-06-01']
2025-06-02 09:31:50,315 - INFO - 开始处理日期: 2025-06-01
2025-06-02 09:31:50,315 - INFO - Request Parameters - Page 1:
2025-06-02 09:31:50,315 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:31:50,315 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:31:51,362 - INFO - Response - Page 1:
2025-06-02 09:31:51,362 - INFO - 第 1 页获取到 100 条记录
2025-06-02 09:31:51,565 - INFO - Request Parameters - Page 2:
2025-06-02 09:31:51,565 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 09:31:51,565 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 09:31:52,253 - INFO - Response - Page 2:
2025-06-02 09:31:52,253 - INFO - 第 2 页获取到 34 条记录
2025-06-02 09:31:52,456 - INFO - 查询完成，共获取到 134 条记录
2025-06-02 09:31:52,456 - INFO - 获取到 134 条表单数据
2025-06-02 09:31:52,456 - INFO - 当前日期 2025-06-01 有 393 条MySQL数据需要处理
2025-06-02 09:31:52,456 - INFO - 开始批量插入 259 条新记录
2025-06-02 09:31:52,784 - INFO - 批量插入响应状态码: 200
2025-06-02 09:31:52,784 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 02 Jun 2025 01:31:46 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '8538371B-99C5-7D92-A72A-F33932AB2091', 'x-acs-trace-id': 'ced6ddd765001b31bdb3a695f3aa9ef0', 'etag': '4m3EX2JRw/hdz2GAbvvRhXw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-02 09:31:52,784 - INFO - 批量插入响应体: {'result': ['FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMNN', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMON', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMPN', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMQN', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMRN', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMSN', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMTN', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMUN', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMVN', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMWN', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMXN', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMYN', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMZN', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM0O', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM1O', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM2O', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM3O', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM4O', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM5O', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM6O', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM7O', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM8O', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM9O', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMAO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMBO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMCO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMDO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMEO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMFO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMGO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMHO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMIO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMJO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMKO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMLO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMMO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMNO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMOO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMPO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMQO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMRO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMSO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMTO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMUO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMVO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMWO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMXO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMYO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMZO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM0P', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM1P', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM2P', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM3P', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM4P', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM5P', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM6P', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM7P', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM8P', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM9P', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMAP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMBP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMCP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMDP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMEP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMFP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMGP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMHP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMIP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMJP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMKP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMLP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMMP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMNP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMOP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMPP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMQP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMRP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMSP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMTP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMUP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBMVP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBMWP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBMXP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBMYP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBMZP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBM0Q', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBM1Q', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBM2Q', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBM3Q', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBM4Q', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBM5Q', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBM6Q', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBM7Q', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBM8Q', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBM9Q', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBMAQ', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBMBQ', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBMCQ', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBMDQ', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBMEQ']}
2025-06-02 09:31:52,784 - INFO - 批量插入表单数据成功，批次 1，共 100 条记录
2025-06-02 09:31:52,784 - INFO - 成功插入的数据ID: ['FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMNN', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMON', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMPN', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMQN', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMRN', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMSN', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMTN', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMUN', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMVN', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMWN', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMXN', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMYN', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMZN', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM0O', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM1O', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM2O', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM3O', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM4O', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM5O', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM6O', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM7O', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM8O', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM9O', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMAO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMBO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMCO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMDO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMEO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMFO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMGO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMHO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMIO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMJO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMKO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMLO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMMO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMNO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMOO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMPO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMQO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMRO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMSO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMTO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMUO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMVO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMWO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMXO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMYO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMZO', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM0P', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM1P', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM2P', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM3P', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM4P', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM5P', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM6P', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM7P', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM8P', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBM9P', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMAP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMBP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMCP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMDP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMEP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMFP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMGP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMHP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMIP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMJP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMKP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMLP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMMP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMNP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMOP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMPP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMQP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMRP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMSP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMTP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMUP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBMVP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBMWP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBMXP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBMYP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBMZP', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBM0Q', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBM1Q', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBM2Q', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBM3Q', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBM4Q', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBM5Q', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBM6Q', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBM7Q', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBM8Q', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBM9Q', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBMAQ', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBMBQ', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBMCQ', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBMDQ', 'FINST-PPA66671DCVVD21Q7MFQG8EYCHL73UYCZEEBMEQ']
2025-06-02 09:31:58,066 - INFO - 批量插入响应状态码: 200
2025-06-02 09:31:58,066 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 02 Jun 2025 01:31:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2B42B57A-57DB-7DB9-A823-CEC6BD460BFB', 'x-acs-trace-id': '552d130ee01b0de7d14be96048908653', 'etag': '4EBITjFnFij6Ofz/6vMmBFQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-02 09:31:58,066 - INFO - 批量插入响应体: {'result': ['FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMJ8', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMK8', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBML8', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMM8', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMN8', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMO8', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMP8', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMQ8', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMR8', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMS8', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMT8', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMU8', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMV8', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMW8', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMX8', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMY8', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMZ8', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM09', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM19', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM29', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM39', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM49', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM59', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM69', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM79', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM89', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM99', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMA9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMB9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMC9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMD9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBME9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMF9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMG9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMH9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMI9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMJ9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMK9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBML9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMM9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMN9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMO9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMP9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMQ9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMR9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMS9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMT9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMU9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMV9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMW9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMX9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMY9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMZ9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM0A', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM1A', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM2A', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM3A', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM4A', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM5A', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM6A', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM7A', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM8A', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM9A', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMAA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMBA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMCA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMDA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMEA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMFA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMGA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMHA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMIA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMJA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMKA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMLA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMMA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMNA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMOA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMPA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMQA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMRA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMSA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMTA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMUA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMVA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMWA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMXA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMYA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMZA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM0B', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM1B', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM2B', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM3B', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM4B', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM5B', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM6B', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM7B', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM8B', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM9B', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMAB']}
2025-06-02 09:31:58,066 - INFO - 批量插入表单数据成功，批次 2，共 100 条记录
2025-06-02 09:31:58,066 - INFO - 成功插入的数据ID: ['FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMJ8', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMK8', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBML8', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMM8', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMN8', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMO8', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMP8', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMQ8', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMR8', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMS8', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMT8', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMU8', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMV8', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMW8', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMX8', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMY8', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMZ8', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM09', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM19', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM29', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM39', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM49', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM59', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM69', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM79', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM89', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM99', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMA9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMB9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMC9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMD9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBME9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMF9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMG9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMH9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMI9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMJ9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMK9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBML9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMM9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMN9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMO9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMP9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMQ9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMR9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMS9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMT9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMU9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMV9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMW9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMX9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMY9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMZ9', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM0A', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM1A', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM2A', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM3A', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM4A', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM5A', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM6A', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM7A', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM8A', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM9A', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMAA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMBA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMCA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMDA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMEA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMFA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMGA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMHA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMIA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMJA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMKA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMLA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMMA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMNA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMOA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMPA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMQA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMRA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMSA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMTA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMUA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMVA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMWA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMXA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMYA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMZA', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM0B', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM1B', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM2B', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM3B', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM4B', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM5B', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM6B', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM7B', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM8B', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBM9B', 'FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMAB']
2025-06-02 09:32:03,302 - INFO - 批量插入响应状态码: 200
2025-06-02 09:32:03,302 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 02 Jun 2025 01:31:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2844', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '34C1CDED-042B-7B1C-B0AD-60EDE0F8F4D0', 'x-acs-trace-id': 'a9098b93ae6c72b1e4040cbad5bb4cd6', 'etag': '2qhSVj0tzxZ+rCGtb0qu3Rg4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-02 09:32:03,302 - INFO - 批量插入响应体: {'result': ['FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBM7F', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBM8F', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBM9F', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMAF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMBF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMCF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMDF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMEF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMFF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMGF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMHF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMIF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMJF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMKF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMLF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMMF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMNF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMOF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMPF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMQF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMRF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMSF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMTF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMUF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMVF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMWF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMXF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMYF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMZF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBM0G', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBM1G', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBM2G', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBM3G', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBM4G', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBM5G', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBM6G', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBM7G', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBM8G', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBM9G', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMAG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMBG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMCG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMDG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMEG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMFG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMGG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMHG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMIG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMJG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMKG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMLG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMMG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMNG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF213LZEEBMOG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF213LZEEBMPG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF213LZEEBMQG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF213LZEEBMRG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF213LZEEBMSG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF213LZEEBMTG']}
2025-06-02 09:32:03,302 - INFO - 批量插入表单数据成功，批次 3，共 59 条记录
2025-06-02 09:32:03,302 - INFO - 成功插入的数据ID: ['FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBM7F', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBM8F', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBM9F', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMAF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMBF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMCF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMDF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMEF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMFF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMGF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMHF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMIF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMJF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMKF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMLF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMMF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMNF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMOF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMPF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMQF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMRF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMSF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMTF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMUF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMVF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMWF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMXF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMYF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMZF', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBM0G', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBM1G', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBM2G', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBM3G', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBM4G', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBM5G', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBM6G', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBM7G', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBM8G', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBM9G', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMAG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMBG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMCG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMDG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMEG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMFG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMGG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMHG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMIG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMJG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMKG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMLG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMMG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF203LZEEBMNG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF213LZEEBMOG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF213LZEEBMPG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF213LZEEBMQG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF213LZEEBMRG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF213LZEEBMSG', 'FINST-COC668A1XFUVIORY5XDN77QA2NZF213LZEEBMTG']
2025-06-02 09:32:08,318 - INFO - 批量插入完成，共 259 条记录
2025-06-02 09:32:08,318 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 259 条，错误: 0 条
2025-06-02 09:32:08,318 - INFO - 数据同步完成！更新: 0 条，插入: 259 条，错误: 0 条
2025-06-02 09:32:08,318 - INFO - 同步完成
2025-06-02 10:30:33,800 - INFO - 使用默认增量同步（当天更新数据）
2025-06-02 10:30:33,800 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 10:30:33,800 - INFO - 查询参数: ('2025-06-02',)
2025-06-02 10:30:33,863 - INFO - MySQL查询成功，增量数据（日期: 2025-06-02），共获取 112 条记录
2025-06-02 10:30:33,863 - INFO - 获取到 2 个日期需要处理: ['2025-05-31', '2025-06-01']
2025-06-02 10:30:33,863 - INFO - 开始处理日期: 2025-05-31
2025-06-02 10:30:33,878 - INFO - Request Parameters - Page 1:
2025-06-02 10:30:33,878 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 10:30:33,878 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 10:30:41,989 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A1E7DC1F-B708-7AF6-805E-3F3787AE718E Response: {'code': 'ServiceUnavailable', 'requestid': 'A1E7DC1F-B708-7AF6-805E-3F3787AE718E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A1E7DC1F-B708-7AF6-805E-3F3787AE718E)
2025-06-02 10:30:41,989 - INFO - 开始处理日期: 2025-06-01
2025-06-02 10:30:41,989 - INFO - Request Parameters - Page 1:
2025-06-02 10:30:41,989 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 10:30:41,989 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 10:30:50,115 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2626CFA8-04E5-7E62-995A-BA87BE4CB959 Response: {'code': 'ServiceUnavailable', 'requestid': '2626CFA8-04E5-7E62-995A-BA87BE4CB959', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2626CFA8-04E5-7E62-995A-BA87BE4CB959)
2025-06-02 10:30:50,115 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-06-02 10:31:50,141 - INFO - 开始同步昨天与今天的销售数据: 2025-06-01 至 2025-06-02
2025-06-02 10:31:50,141 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 10:31:50,141 - INFO - 查询参数: ('2025-06-01', '2025-06-02')
2025-06-02 10:31:50,219 - INFO - MySQL查询成功，时间段: 2025-06-01 至 2025-06-02，共获取 417 条记录
2025-06-02 10:31:50,219 - INFO - 获取到 1 个日期需要处理: ['2025-06-01']
2025-06-02 10:31:50,219 - INFO - 开始处理日期: 2025-06-01
2025-06-02 10:31:50,219 - INFO - Request Parameters - Page 1:
2025-06-02 10:31:50,219 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 10:31:50,219 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 10:31:51,109 - INFO - Response - Page 1:
2025-06-02 10:31:51,109 - INFO - 第 1 页获取到 100 条记录
2025-06-02 10:31:51,313 - INFO - Request Parameters - Page 2:
2025-06-02 10:31:51,313 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 10:31:51,313 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 10:31:52,219 - INFO - Response - Page 2:
2025-06-02 10:31:52,219 - INFO - 第 2 页获取到 100 条记录
2025-06-02 10:31:52,422 - INFO - Request Parameters - Page 3:
2025-06-02 10:31:52,422 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 10:31:52,422 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 10:31:53,282 - INFO - Response - Page 3:
2025-06-02 10:31:53,282 - INFO - 第 3 页获取到 100 条记录
2025-06-02 10:31:53,485 - INFO - Request Parameters - Page 4:
2025-06-02 10:31:53,485 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 10:31:53,485 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 10:31:54,329 - INFO - Response - Page 4:
2025-06-02 10:31:54,329 - INFO - 第 4 页获取到 93 条记录
2025-06-02 10:31:54,532 - INFO - 查询完成，共获取到 393 条记录
2025-06-02 10:31:54,532 - INFO - 获取到 393 条表单数据
2025-06-02 10:31:54,532 - INFO - 当前日期 2025-06-01 有 417 条MySQL数据需要处理
2025-06-02 10:31:54,532 - INFO - 开始更新记录 - 表单实例ID: FINST-FD966QA14HRVZ9SYBJEGNDXLTWY23QUKZ5DBMUV
2025-06-02 10:31:55,251 - INFO - 更新表单数据成功: FINST-FD966QA14HRVZ9SYBJEGNDXLTWY23QUKZ5DBMUV
2025-06-02 10:31:55,251 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46052.0, 'new_value': 54727.0}, {'field': 'total_amount', 'old_value': 46052.0, 'new_value': 54727.0}, {'field': 'order_count', 'old_value': 288, 'new_value': 356}]
2025-06-02 10:31:55,251 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMWN
2025-06-02 10:31:55,766 - INFO - 更新表单数据成功: FINST-PPA66671DCVVD21Q7MFQG8EYCHL73TYCZEEBMWN
2025-06-02 10:31:55,766 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 0, 'new_value': 15}]
2025-06-02 10:31:55,782 - INFO - 开始更新记录 - 表单实例ID: FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMJ9
2025-06-02 10:31:56,220 - INFO - 更新表单数据成功: FINST-RNA66D71JAVVAAU6A0UL07SX6R982I1HZEEBMJ9
2025-06-02 10:31:56,220 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18659.94, 'new_value': 70480.0}, {'field': 'offline_amount', 'old_value': 838.0, 'new_value': 20077.44}, {'field': 'total_amount', 'old_value': 19497.94, 'new_value': 90557.44}, {'field': 'order_count', 'old_value': 42, 'new_value': 150}]
2025-06-02 10:31:56,220 - INFO - 开始更新记录 - 表单实例ID: FINST-COC668A1XFUVIORY5XDN77QA2NZF213LZEEBMQG
2025-06-02 10:31:56,751 - INFO - 更新表单数据成功: FINST-COC668A1XFUVIORY5XDN77QA2NZF213LZEEBMQG
2025-06-02 10:31:56,751 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 429588.0, 'new_value': 479588.0}, {'field': 'total_amount', 'old_value': 429588.0, 'new_value': 479588.0}]
2025-06-02 10:31:56,751 - INFO - 开始批量插入 24 条新记录
2025-06-02 10:31:56,923 - INFO - 批量插入响应状态码: 200
2025-06-02 10:31:56,923 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 02 Jun 2025 02:31:50 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1164', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'A5490665-FE47-7E2B-B13E-9C7D811BD8A0', 'x-acs-trace-id': 'ce9f517cf7013bcaae324d45d8b06e0c', 'etag': '1g8A570bd70IKVHW0hx8uwQ4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-02 10:31:56,923 - INFO - 批量插入响应体: {'result': ['FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBMOB', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBMPB', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBMQB', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBMRB', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBMSB', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBMTB', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBMUB', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBMVB', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBMWB', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBMXB', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBMYB', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBMZB', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBM0C', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBM1C', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBM2C', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBM3C', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBM4C', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBM5C', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBM6C', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBM7C', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBM8C', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBM9C', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBMAC', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBMBC']}
2025-06-02 10:31:56,923 - INFO - 批量插入表单数据成功，批次 1，共 24 条记录
2025-06-02 10:31:56,923 - INFO - 成功插入的数据ID: ['FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBMOB', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBMPB', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBMQB', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBMRB', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBMSB', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBMTB', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBMUB', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBMVB', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBMWB', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBMXB', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBMYB', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBMZB', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBM0C', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBM1C', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBM2C', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBM3C', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBM4C', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBM5C', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBM6C', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBM7C', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBM8C', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBM9C', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBMAC', 'FINST-1PF66VA1V7UVUA0SD08C68LG1V8R21IL4HEBMBC']
2025-06-02 10:32:01,939 - INFO - 批量插入完成，共 24 条记录
2025-06-02 10:32:01,939 - INFO - 日期 2025-06-01 处理完成 - 更新: 4 条，插入: 24 条，错误: 0 条
2025-06-02 10:32:01,939 - INFO - 数据同步完成！更新: 4 条，插入: 24 条，错误: 0 条
2025-06-02 10:32:01,939 - INFO - 同步完成
2025-06-02 11:30:34,172 - INFO - 使用默认增量同步（当天更新数据）
2025-06-02 11:30:34,172 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 11:30:34,172 - INFO - 查询参数: ('2025-06-02',)
2025-06-02 11:30:34,235 - INFO - MySQL查询成功，增量数据（日期: 2025-06-02），共获取 122 条记录
2025-06-02 11:30:34,235 - INFO - 获取到 2 个日期需要处理: ['2025-05-31', '2025-06-01']
2025-06-02 11:30:34,235 - INFO - 开始处理日期: 2025-05-31
2025-06-02 11:30:34,250 - INFO - Request Parameters - Page 1:
2025-06-02 11:30:34,250 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 11:30:34,250 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 11:30:42,361 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 479C48DF-1DDD-7814-A8D2-821886CFFDAE Response: {'code': 'ServiceUnavailable', 'requestid': '479C48DF-1DDD-7814-A8D2-821886CFFDAE', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 479C48DF-1DDD-7814-A8D2-821886CFFDAE)
2025-06-02 11:30:42,361 - INFO - 开始处理日期: 2025-06-01
2025-06-02 11:30:42,361 - INFO - Request Parameters - Page 1:
2025-06-02 11:30:42,361 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 11:30:42,361 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 11:30:50,472 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 781155DE-B3D5-7B22-9821-BCA0766FC125 Response: {'code': 'ServiceUnavailable', 'requestid': '781155DE-B3D5-7B22-9821-BCA0766FC125', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 781155DE-B3D5-7B22-9821-BCA0766FC125)
2025-06-02 11:30:50,472 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-06-02 11:31:50,497 - INFO - 开始同步昨天与今天的销售数据: 2025-06-01 至 2025-06-02
2025-06-02 11:31:50,497 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 11:31:50,497 - INFO - 查询参数: ('2025-06-01', '2025-06-02')
2025-06-02 11:31:50,575 - INFO - MySQL查询成功，时间段: 2025-06-01 至 2025-06-02，共获取 445 条记录
2025-06-02 11:31:50,575 - INFO - 获取到 1 个日期需要处理: ['2025-06-01']
2025-06-02 11:31:50,575 - INFO - 开始处理日期: 2025-06-01
2025-06-02 11:31:50,575 - INFO - Request Parameters - Page 1:
2025-06-02 11:31:50,575 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 11:31:50,575 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 11:31:58,701 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D9CB41BB-0CDB-7461-B6A1-608A23DCEAF1 Response: {'code': 'ServiceUnavailable', 'requestid': 'D9CB41BB-0CDB-7461-B6A1-608A23DCEAF1', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D9CB41BB-0CDB-7461-B6A1-608A23DCEAF1)
2025-06-02 11:31:58,701 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-02 11:31:58,701 - INFO - 同步完成
2025-06-02 12:30:33,903 - INFO - 使用默认增量同步（当天更新数据）
2025-06-02 12:30:33,903 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 12:30:33,903 - INFO - 查询参数: ('2025-06-02',)
2025-06-02 12:30:33,966 - INFO - MySQL查询成功，增量数据（日期: 2025-06-02），共获取 141 条记录
2025-06-02 12:30:33,966 - INFO - 获取到 2 个日期需要处理: ['2025-05-31', '2025-06-01']
2025-06-02 12:30:33,982 - INFO - 开始处理日期: 2025-05-31
2025-06-02 12:30:33,982 - INFO - Request Parameters - Page 1:
2025-06-02 12:30:33,982 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:30:33,982 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:30:42,108 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2DD7CAB6-E945-76DD-B04B-CB2A93F7D6B6 Response: {'code': 'ServiceUnavailable', 'requestid': '2DD7CAB6-E945-76DD-B04B-CB2A93F7D6B6', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2DD7CAB6-E945-76DD-B04B-CB2A93F7D6B6)
2025-06-02 12:30:42,108 - INFO - 开始处理日期: 2025-06-01
2025-06-02 12:30:42,108 - INFO - Request Parameters - Page 1:
2025-06-02 12:30:42,108 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:30:42,108 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:30:45,265 - INFO - Response - Page 1:
2025-06-02 12:30:45,265 - INFO - 第 1 页获取到 100 条记录
2025-06-02 12:30:45,468 - INFO - Request Parameters - Page 2:
2025-06-02 12:30:45,468 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:30:45,468 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:30:53,578 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: ECA4B2D9-86AA-7AEC-85D2-7C8377D37E87 Response: {'code': 'ServiceUnavailable', 'requestid': 'ECA4B2D9-86AA-7AEC-85D2-7C8377D37E87', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: ECA4B2D9-86AA-7AEC-85D2-7C8377D37E87)
2025-06-02 12:30:53,578 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-06-02 12:31:53,604 - INFO - 开始同步昨天与今天的销售数据: 2025-06-01 至 2025-06-02
2025-06-02 12:31:53,604 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 12:31:53,604 - INFO - 查询参数: ('2025-06-01', '2025-06-02')
2025-06-02 12:31:53,682 - INFO - MySQL查询成功，时间段: 2025-06-01 至 2025-06-02，共获取 464 条记录
2025-06-02 12:31:53,682 - INFO - 获取到 1 个日期需要处理: ['2025-06-01']
2025-06-02 12:31:53,682 - INFO - 开始处理日期: 2025-06-01
2025-06-02 12:31:53,682 - INFO - Request Parameters - Page 1:
2025-06-02 12:31:53,682 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:31:53,682 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:31:54,557 - INFO - Response - Page 1:
2025-06-02 12:31:54,557 - INFO - 第 1 页获取到 100 条记录
2025-06-02 12:31:54,760 - INFO - Request Parameters - Page 2:
2025-06-02 12:31:54,760 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:31:54,760 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:31:55,760 - INFO - Response - Page 2:
2025-06-02 12:31:55,760 - INFO - 第 2 页获取到 100 条记录
2025-06-02 12:31:55,963 - INFO - Request Parameters - Page 3:
2025-06-02 12:31:55,963 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:31:55,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:31:56,776 - INFO - Response - Page 3:
2025-06-02 12:31:56,776 - INFO - 第 3 页获取到 100 条记录
2025-06-02 12:31:56,979 - INFO - Request Parameters - Page 4:
2025-06-02 12:31:56,979 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:31:56,979 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:31:57,807 - INFO - Response - Page 4:
2025-06-02 12:31:57,807 - INFO - 第 4 页获取到 100 条记录
2025-06-02 12:31:58,011 - INFO - Request Parameters - Page 5:
2025-06-02 12:31:58,011 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 12:31:58,011 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 12:31:58,604 - INFO - Response - Page 5:
2025-06-02 12:31:58,604 - INFO - 第 5 页获取到 17 条记录
2025-06-02 12:31:58,808 - INFO - 查询完成，共获取到 417 条记录
2025-06-02 12:31:58,808 - INFO - 获取到 417 条表单数据
2025-06-02 12:31:58,808 - INFO - 当前日期 2025-06-01 有 464 条MySQL数据需要处理
2025-06-02 12:31:58,808 - INFO - 开始批量插入 47 条新记录
2025-06-02 12:31:59,042 - INFO - 批量插入响应状态码: 200
2025-06-02 12:31:59,042 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 02 Jun 2025 04:31:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2268', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '11DD6A69-165C-7E78-9B64-BDB06F510E28', 'x-acs-trace-id': 'a5a7d713f8f495d06f4f434214fe2eb6', 'etag': '2PK0mCDt5a2/ff6+1hiH/uQ8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-02 12:31:59,042 - INFO - 批量插入响应体: {'result': ['FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13QSXELEBMGP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13QSXELEBMHP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13QSXELEBMIP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13QSXELEBMJP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13QSXELEBMKP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13QSXELEBMLP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13QSXELEBMMP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13QSXELEBMNP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13QSXELEBMOP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13QSXELEBMPP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13QSXELEBMQP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13QSXELEBMRP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13QSXELEBMSP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13QSXELEBMTP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMUP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMVP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMWP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMXP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMYP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMZP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBM0Q', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBM1Q', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBM2Q', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBM3Q', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBM4Q', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBM5Q', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBM6Q', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBM7Q', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBM8Q', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBM9Q', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMAQ', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMBQ', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMCQ', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMDQ', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMEQ', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMFQ', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMGQ', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMHQ', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMIQ', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMJQ', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMKQ', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMLQ', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMMQ', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMNQ', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMOQ', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMPQ', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMQQ']}
2025-06-02 12:31:59,042 - INFO - 批量插入表单数据成功，批次 1，共 47 条记录
2025-06-02 12:31:59,042 - INFO - 成功插入的数据ID: ['FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13QSXELEBMGP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13QSXELEBMHP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13QSXELEBMIP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13QSXELEBMJP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13QSXELEBMKP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13QSXELEBMLP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13QSXELEBMMP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13QSXELEBMNP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13QSXELEBMOP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13QSXELEBMPP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13QSXELEBMQP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13QSXELEBMRP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13QSXELEBMSP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13QSXELEBMTP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMUP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMVP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMWP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMXP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMYP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMZP', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBM0Q', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBM1Q', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBM2Q', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBM3Q', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBM4Q', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBM5Q', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBM6Q', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBM7Q', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBM8Q', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBM9Q', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMAQ', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMBQ', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMCQ', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMDQ', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMEQ', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMFQ', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMGQ', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMHQ', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMIQ', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMJQ', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMKQ', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMLQ', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMMQ', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMNQ', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMOQ', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMPQ', 'FINST-5XA66LC1LAVVXSUFCLBSZ3940DR13RSXELEBMQQ']
2025-06-02 12:32:04,058 - INFO - 批量插入完成，共 47 条记录
2025-06-02 12:32:04,058 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 47 条，错误: 0 条
2025-06-02 12:32:04,058 - INFO - 数据同步完成！更新: 0 条，插入: 47 条，错误: 0 条
2025-06-02 12:32:04,058 - INFO - 同步完成
2025-06-02 13:30:34,090 - INFO - 使用默认增量同步（当天更新数据）
2025-06-02 13:30:34,090 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 13:30:34,090 - INFO - 查询参数: ('2025-06-02',)
2025-06-02 13:30:34,168 - INFO - MySQL查询成功，增量数据（日期: 2025-06-02），共获取 149 条记录
2025-06-02 13:30:34,168 - INFO - 获取到 2 个日期需要处理: ['2025-05-31', '2025-06-01']
2025-06-02 13:30:34,168 - INFO - 开始处理日期: 2025-05-31
2025-06-02 13:30:34,183 - INFO - Request Parameters - Page 1:
2025-06-02 13:30:34,183 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 13:30:34,183 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 13:30:42,294 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2C3021D0-C9AA-7219-A3B8-78D2E6D3B72F Response: {'code': 'ServiceUnavailable', 'requestid': '2C3021D0-C9AA-7219-A3B8-78D2E6D3B72F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2C3021D0-C9AA-7219-A3B8-78D2E6D3B72F)
2025-06-02 13:30:42,294 - INFO - 开始处理日期: 2025-06-01
2025-06-02 13:30:42,294 - INFO - Request Parameters - Page 1:
2025-06-02 13:30:42,294 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 13:30:42,294 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 13:30:50,420 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 37EB87CF-FA4C-7D84-9279-7E4ACEFBCA5B Response: {'code': 'ServiceUnavailable', 'requestid': '37EB87CF-FA4C-7D84-9279-7E4ACEFBCA5B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 37EB87CF-FA4C-7D84-9279-7E4ACEFBCA5B)
2025-06-02 13:30:50,420 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-06-02 13:31:50,446 - INFO - 开始同步昨天与今天的销售数据: 2025-06-01 至 2025-06-02
2025-06-02 13:31:50,446 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 13:31:50,446 - INFO - 查询参数: ('2025-06-01', '2025-06-02')
2025-06-02 13:31:50,524 - INFO - MySQL查询成功，时间段: 2025-06-01 至 2025-06-02，共获取 469 条记录
2025-06-02 13:31:50,524 - INFO - 获取到 1 个日期需要处理: ['2025-06-01']
2025-06-02 13:31:50,524 - INFO - 开始处理日期: 2025-06-01
2025-06-02 13:31:50,524 - INFO - Request Parameters - Page 1:
2025-06-02 13:31:50,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 13:31:50,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 13:31:51,446 - INFO - Response - Page 1:
2025-06-02 13:31:51,446 - INFO - 第 1 页获取到 100 条记录
2025-06-02 13:31:51,649 - INFO - Request Parameters - Page 2:
2025-06-02 13:31:51,649 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 13:31:51,649 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 13:31:52,462 - INFO - Response - Page 2:
2025-06-02 13:31:52,462 - INFO - 第 2 页获取到 100 条记录
2025-06-02 13:31:52,665 - INFO - Request Parameters - Page 3:
2025-06-02 13:31:52,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 13:31:52,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 13:31:53,743 - INFO - Response - Page 3:
2025-06-02 13:31:53,743 - INFO - 第 3 页获取到 100 条记录
2025-06-02 13:31:53,946 - INFO - Request Parameters - Page 4:
2025-06-02 13:31:53,946 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 13:31:53,946 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 13:31:54,759 - INFO - Response - Page 4:
2025-06-02 13:31:54,759 - INFO - 第 4 页获取到 100 条记录
2025-06-02 13:31:54,962 - INFO - Request Parameters - Page 5:
2025-06-02 13:31:54,962 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 13:31:54,962 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 13:32:03,073 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E6FFA5FC-C48E-7B49-9FA6-59D0D1C9A41A Response: {'code': 'ServiceUnavailable', 'requestid': 'E6FFA5FC-C48E-7B49-9FA6-59D0D1C9A41A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E6FFA5FC-C48E-7B49-9FA6-59D0D1C9A41A)
2025-06-02 13:32:03,073 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-02 13:32:03,073 - INFO - 同步完成
2025-06-02 14:30:34,134 - INFO - 使用默认增量同步（当天更新数据）
2025-06-02 14:30:34,134 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 14:30:34,134 - INFO - 查询参数: ('2025-06-02',)
2025-06-02 14:30:34,212 - INFO - MySQL查询成功，增量数据（日期: 2025-06-02），共获取 149 条记录
2025-06-02 14:30:34,212 - INFO - 获取到 2 个日期需要处理: ['2025-05-31', '2025-06-01']
2025-06-02 14:30:34,212 - INFO - 开始处理日期: 2025-05-31
2025-06-02 14:30:34,212 - INFO - Request Parameters - Page 1:
2025-06-02 14:30:34,212 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 14:30:34,212 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 14:30:42,338 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 815D3DDC-1ADA-70C5-9665-BA18AC433CD1 Response: {'code': 'ServiceUnavailable', 'requestid': '815D3DDC-1ADA-70C5-9665-BA18AC433CD1', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 815D3DDC-1ADA-70C5-9665-BA18AC433CD1)
2025-06-02 14:30:42,338 - INFO - 开始处理日期: 2025-06-01
2025-06-02 14:30:42,338 - INFO - Request Parameters - Page 1:
2025-06-02 14:30:42,338 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 14:30:42,338 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 14:30:50,449 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 8C90F9BC-C9A3-7C8E-992A-32F6D8050DC3 Response: {'code': 'ServiceUnavailable', 'requestid': '8C90F9BC-C9A3-7C8E-992A-32F6D8050DC3', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 8C90F9BC-C9A3-7C8E-992A-32F6D8050DC3)
2025-06-02 14:30:50,449 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-06-02 14:31:50,474 - INFO - 开始同步昨天与今天的销售数据: 2025-06-01 至 2025-06-02
2025-06-02 14:31:50,474 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 14:31:50,474 - INFO - 查询参数: ('2025-06-01', '2025-06-02')
2025-06-02 14:31:50,553 - INFO - MySQL查询成功，时间段: 2025-06-01 至 2025-06-02，共获取 469 条记录
2025-06-02 14:31:50,553 - INFO - 获取到 1 个日期需要处理: ['2025-06-01']
2025-06-02 14:31:50,553 - INFO - 开始处理日期: 2025-06-01
2025-06-02 14:31:50,553 - INFO - Request Parameters - Page 1:
2025-06-02 14:31:50,553 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 14:31:50,553 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 14:31:51,443 - INFO - Response - Page 1:
2025-06-02 14:31:51,443 - INFO - 第 1 页获取到 100 条记录
2025-06-02 14:31:51,646 - INFO - Request Parameters - Page 2:
2025-06-02 14:31:51,646 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 14:31:51,646 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 14:31:52,522 - INFO - Response - Page 2:
2025-06-02 14:31:52,522 - INFO - 第 2 页获取到 100 条记录
2025-06-02 14:31:52,725 - INFO - Request Parameters - Page 3:
2025-06-02 14:31:52,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 14:31:52,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 14:31:53,491 - INFO - Response - Page 3:
2025-06-02 14:31:53,491 - INFO - 第 3 页获取到 100 条记录
2025-06-02 14:31:53,694 - INFO - Request Parameters - Page 4:
2025-06-02 14:31:53,694 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 14:31:53,694 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 14:31:54,538 - INFO - Response - Page 4:
2025-06-02 14:31:54,553 - INFO - 第 4 页获取到 100 条记录
2025-06-02 14:31:54,756 - INFO - Request Parameters - Page 5:
2025-06-02 14:31:54,756 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 14:31:54,756 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 14:31:55,506 - INFO - Response - Page 5:
2025-06-02 14:31:55,522 - INFO - 第 5 页获取到 64 条记录
2025-06-02 14:31:55,725 - INFO - 查询完成，共获取到 464 条记录
2025-06-02 14:31:55,725 - INFO - 获取到 464 条表单数据
2025-06-02 14:31:55,725 - INFO - 当前日期 2025-06-01 有 469 条MySQL数据需要处理
2025-06-02 14:31:55,741 - INFO - 开始批量插入 5 条新记录
2025-06-02 14:31:55,897 - INFO - 批量插入响应状态码: 200
2025-06-02 14:31:55,897 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 02 Jun 2025 06:31:46 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '252', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '131CA82F-ED14-75E4-982F-7C7308718573', 'x-acs-trace-id': '328ca4154198c26b6cc9606d851bf683', 'etag': '2/fjKklFcaCN5YQAXfC3+gA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-02 14:31:55,897 - INFO - 批量插入响应体: {'result': ['FINST-SWC66P91IESV30Z2EIZTH85YMDLA3H16PPEBMCP', 'FINST-SWC66P91IESV30Z2EIZTH85YMDLA3H16PPEBMDP', 'FINST-SWC66P91IESV30Z2EIZTH85YMDLA3H16PPEBMEP', 'FINST-SWC66P91IESV30Z2EIZTH85YMDLA3H16PPEBMFP', 'FINST-SWC66P91IESV30Z2EIZTH85YMDLA3H16PPEBMGP']}
2025-06-02 14:31:55,897 - INFO - 批量插入表单数据成功，批次 1，共 5 条记录
2025-06-02 14:31:55,897 - INFO - 成功插入的数据ID: ['FINST-SWC66P91IESV30Z2EIZTH85YMDLA3H16PPEBMCP', 'FINST-SWC66P91IESV30Z2EIZTH85YMDLA3H16PPEBMDP', 'FINST-SWC66P91IESV30Z2EIZTH85YMDLA3H16PPEBMEP', 'FINST-SWC66P91IESV30Z2EIZTH85YMDLA3H16PPEBMFP', 'FINST-SWC66P91IESV30Z2EIZTH85YMDLA3H16PPEBMGP']
2025-06-02 14:32:00,914 - INFO - 批量插入完成，共 5 条记录
2025-06-02 14:32:00,914 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 5 条，错误: 0 条
2025-06-02 14:32:00,914 - INFO - 数据同步完成！更新: 0 条，插入: 5 条，错误: 0 条
2025-06-02 14:32:00,914 - INFO - 同步完成
2025-06-02 15:30:33,914 - INFO - 使用默认增量同步（当天更新数据）
2025-06-02 15:30:33,914 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 15:30:33,914 - INFO - 查询参数: ('2025-06-02',)
2025-06-02 15:30:33,992 - INFO - MySQL查询成功，增量数据（日期: 2025-06-02），共获取 153 条记录
2025-06-02 15:30:33,992 - INFO - 获取到 2 个日期需要处理: ['2025-05-31', '2025-06-01']
2025-06-02 15:30:33,992 - INFO - 开始处理日期: 2025-05-31
2025-06-02 15:30:33,992 - INFO - Request Parameters - Page 1:
2025-06-02 15:30:33,992 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:30:33,992 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:30:42,118 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 7C9ED778-4F6B-7DA7-8E3E-8A8AADE3B13C Response: {'code': 'ServiceUnavailable', 'requestid': '7C9ED778-4F6B-7DA7-8E3E-8A8AADE3B13C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 7C9ED778-4F6B-7DA7-8E3E-8A8AADE3B13C)
2025-06-02 15:30:42,118 - INFO - 开始处理日期: 2025-06-01
2025-06-02 15:30:42,118 - INFO - Request Parameters - Page 1:
2025-06-02 15:30:42,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:30:42,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:30:50,213 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D990F720-EF8D-7481-99B0-992360AE37D1 Response: {'code': 'ServiceUnavailable', 'requestid': 'D990F720-EF8D-7481-99B0-992360AE37D1', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D990F720-EF8D-7481-99B0-992360AE37D1)
2025-06-02 15:30:50,213 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-06-02 15:31:50,238 - INFO - 开始同步昨天与今天的销售数据: 2025-06-01 至 2025-06-02
2025-06-02 15:31:50,238 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 15:31:50,238 - INFO - 查询参数: ('2025-06-01', '2025-06-02')
2025-06-02 15:31:50,316 - INFO - MySQL查询成功，时间段: 2025-06-01 至 2025-06-02，共获取 469 条记录
2025-06-02 15:31:50,316 - INFO - 获取到 1 个日期需要处理: ['2025-06-01']
2025-06-02 15:31:50,316 - INFO - 开始处理日期: 2025-06-01
2025-06-02 15:31:50,316 - INFO - Request Parameters - Page 1:
2025-06-02 15:31:50,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:31:50,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:31:51,270 - INFO - Response - Page 1:
2025-06-02 15:31:51,270 - INFO - 第 1 页获取到 100 条记录
2025-06-02 15:31:51,473 - INFO - Request Parameters - Page 2:
2025-06-02 15:31:51,473 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:31:51,473 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:31:52,348 - INFO - Response - Page 2:
2025-06-02 15:31:52,348 - INFO - 第 2 页获取到 100 条记录
2025-06-02 15:31:52,551 - INFO - Request Parameters - Page 3:
2025-06-02 15:31:52,551 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:31:52,551 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:31:53,395 - INFO - Response - Page 3:
2025-06-02 15:31:53,395 - INFO - 第 3 页获取到 100 条记录
2025-06-02 15:31:53,598 - INFO - Request Parameters - Page 4:
2025-06-02 15:31:53,598 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:31:53,598 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:31:54,411 - INFO - Response - Page 4:
2025-06-02 15:31:54,411 - INFO - 第 4 页获取到 100 条记录
2025-06-02 15:31:54,614 - INFO - Request Parameters - Page 5:
2025-06-02 15:31:54,614 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 15:31:54,614 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 15:31:55,380 - INFO - Response - Page 5:
2025-06-02 15:31:55,380 - INFO - 第 5 页获取到 69 条记录
2025-06-02 15:31:55,583 - INFO - 查询完成，共获取到 469 条记录
2025-06-02 15:31:55,583 - INFO - 获取到 469 条表单数据
2025-06-02 15:31:55,583 - INFO - 当前日期 2025-06-01 有 469 条MySQL数据需要处理
2025-06-02 15:31:55,598 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 15:31:55,598 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 15:31:55,598 - INFO - 同步完成
2025-06-02 16:30:34,083 - INFO - 使用默认增量同步（当天更新数据）
2025-06-02 16:30:34,083 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 16:30:34,083 - INFO - 查询参数: ('2025-06-02',)
2025-06-02 16:30:34,162 - INFO - MySQL查询成功，增量数据（日期: 2025-06-02），共获取 153 条记录
2025-06-02 16:30:34,162 - INFO - 获取到 2 个日期需要处理: ['2025-05-31', '2025-06-01']
2025-06-02 16:30:34,162 - INFO - 开始处理日期: 2025-05-31
2025-06-02 16:30:34,162 - INFO - Request Parameters - Page 1:
2025-06-02 16:30:34,162 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 16:30:34,162 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 16:30:42,303 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 6085AF72-30F7-7EBD-9BB3-9F466B5A9EBA Response: {'code': 'ServiceUnavailable', 'requestid': '6085AF72-30F7-7EBD-9BB3-9F466B5A9EBA', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 6085AF72-30F7-7EBD-9BB3-9F466B5A9EBA)
2025-06-02 16:30:42,303 - INFO - 开始处理日期: 2025-06-01
2025-06-02 16:30:42,303 - INFO - Request Parameters - Page 1:
2025-06-02 16:30:42,303 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 16:30:42,303 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 16:30:45,288 - INFO - Response - Page 1:
2025-06-02 16:30:45,288 - INFO - 第 1 页获取到 100 条记录
2025-06-02 16:30:45,491 - INFO - Request Parameters - Page 2:
2025-06-02 16:30:45,491 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 16:30:45,491 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 16:30:46,304 - INFO - Response - Page 2:
2025-06-02 16:30:46,304 - INFO - 第 2 页获取到 100 条记录
2025-06-02 16:30:46,507 - INFO - Request Parameters - Page 3:
2025-06-02 16:30:46,507 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 16:30:46,507 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 16:30:47,336 - INFO - Response - Page 3:
2025-06-02 16:30:47,351 - INFO - 第 3 页获取到 100 条记录
2025-06-02 16:30:47,554 - INFO - Request Parameters - Page 4:
2025-06-02 16:30:47,554 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 16:30:47,554 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 16:30:55,649 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5D7FF094-D6D8-723B-BF63-27B8394905EC Response: {'code': 'ServiceUnavailable', 'requestid': '5D7FF094-D6D8-723B-BF63-27B8394905EC', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 5D7FF094-D6D8-723B-BF63-27B8394905EC)
2025-06-02 16:30:55,649 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-06-02 16:31:55,675 - INFO - 开始同步昨天与今天的销售数据: 2025-06-01 至 2025-06-02
2025-06-02 16:31:55,675 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 16:31:55,675 - INFO - 查询参数: ('2025-06-01', '2025-06-02')
2025-06-02 16:31:55,753 - INFO - MySQL查询成功，时间段: 2025-06-01 至 2025-06-02，共获取 469 条记录
2025-06-02 16:31:55,753 - INFO - 获取到 1 个日期需要处理: ['2025-06-01']
2025-06-02 16:31:55,753 - INFO - 开始处理日期: 2025-06-01
2025-06-02 16:31:55,753 - INFO - Request Parameters - Page 1:
2025-06-02 16:31:55,753 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 16:31:55,753 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 16:31:56,643 - INFO - Response - Page 1:
2025-06-02 16:31:56,643 - INFO - 第 1 页获取到 100 条记录
2025-06-02 16:31:56,847 - INFO - Request Parameters - Page 2:
2025-06-02 16:31:56,847 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 16:31:56,847 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 16:31:57,737 - INFO - Response - Page 2:
2025-06-02 16:31:57,737 - INFO - 第 2 页获取到 100 条记录
2025-06-02 16:31:57,941 - INFO - Request Parameters - Page 3:
2025-06-02 16:31:57,941 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 16:31:57,941 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 16:31:58,816 - INFO - Response - Page 3:
2025-06-02 16:31:58,816 - INFO - 第 3 页获取到 100 条记录
2025-06-02 16:31:59,019 - INFO - Request Parameters - Page 4:
2025-06-02 16:31:59,019 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 16:31:59,019 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 16:31:59,925 - INFO - Response - Page 4:
2025-06-02 16:31:59,925 - INFO - 第 4 页获取到 100 条记录
2025-06-02 16:32:00,128 - INFO - Request Parameters - Page 5:
2025-06-02 16:32:00,128 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 16:32:00,128 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 16:32:00,894 - INFO - Response - Page 5:
2025-06-02 16:32:00,894 - INFO - 第 5 页获取到 69 条记录
2025-06-02 16:32:01,097 - INFO - 查询完成，共获取到 469 条记录
2025-06-02 16:32:01,097 - INFO - 获取到 469 条表单数据
2025-06-02 16:32:01,097 - INFO - 当前日期 2025-06-01 有 469 条MySQL数据需要处理
2025-06-02 16:32:01,113 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 16:32:01,113 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 16:32:01,113 - INFO - 同步完成
2025-06-02 17:30:34,038 - INFO - 使用默认增量同步（当天更新数据）
2025-06-02 17:30:34,038 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 17:30:34,038 - INFO - 查询参数: ('2025-06-02',)
2025-06-02 17:30:34,116 - INFO - MySQL查询成功，增量数据（日期: 2025-06-02），共获取 196 条记录
2025-06-02 17:30:34,116 - INFO - 获取到 2 个日期需要处理: ['2025-05-31', '2025-06-01']
2025-06-02 17:30:34,116 - INFO - 开始处理日期: 2025-05-31
2025-06-02 17:30:34,116 - INFO - Request Parameters - Page 1:
2025-06-02 17:30:34,116 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 17:30:34,116 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 17:30:42,226 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2B452FA5-750C-7E34-9F47-031C554AACA5 Response: {'code': 'ServiceUnavailable', 'requestid': '2B452FA5-750C-7E34-9F47-031C554AACA5', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2B452FA5-750C-7E34-9F47-031C554AACA5)
2025-06-02 17:30:42,226 - INFO - 开始处理日期: 2025-06-01
2025-06-02 17:30:42,226 - INFO - Request Parameters - Page 1:
2025-06-02 17:30:42,226 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 17:30:42,226 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 17:30:50,337 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 648BA931-FB79-7BF9-A04B-3CA84C92A823 Response: {'code': 'ServiceUnavailable', 'requestid': '648BA931-FB79-7BF9-A04B-3CA84C92A823', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 648BA931-FB79-7BF9-A04B-3CA84C92A823)
2025-06-02 17:30:50,337 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-06-02 17:31:50,361 - INFO - 开始同步昨天与今天的销售数据: 2025-06-01 至 2025-06-02
2025-06-02 17:31:50,361 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 17:31:50,361 - INFO - 查询参数: ('2025-06-01', '2025-06-02')
2025-06-02 17:31:50,439 - INFO - MySQL查询成功，时间段: 2025-06-01 至 2025-06-02，共获取 512 条记录
2025-06-02 17:31:50,439 - INFO - 获取到 1 个日期需要处理: ['2025-06-01']
2025-06-02 17:31:50,439 - INFO - 开始处理日期: 2025-06-01
2025-06-02 17:31:50,439 - INFO - Request Parameters - Page 1:
2025-06-02 17:31:50,439 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 17:31:50,439 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 17:31:58,565 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 45AB6220-95D6-7286-82E4-5EB6429467C2 Response: {'code': 'ServiceUnavailable', 'requestid': '45AB6220-95D6-7286-82E4-5EB6429467C2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 45AB6220-95D6-7286-82E4-5EB6429467C2)
2025-06-02 17:31:58,565 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-02 17:31:58,565 - INFO - 同步完成
2025-06-02 18:30:33,635 - INFO - 使用默认增量同步（当天更新数据）
2025-06-02 18:30:33,651 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 18:30:33,651 - INFO - 查询参数: ('2025-06-02',)
2025-06-02 18:30:33,713 - INFO - MySQL查询成功，增量数据（日期: 2025-06-02），共获取 198 条记录
2025-06-02 18:30:33,713 - INFO - 获取到 3 个日期需要处理: ['2025-05-31', '2025-06-01', '2025-06-02']
2025-06-02 18:30:33,729 - INFO - 开始处理日期: 2025-05-31
2025-06-02 18:30:33,729 - INFO - Request Parameters - Page 1:
2025-06-02 18:30:33,729 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:30:33,729 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:30:41,854 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E1DF6993-C9BD-7DB3-B976-1A9FEEE627D6 Response: {'code': 'ServiceUnavailable', 'requestid': 'E1DF6993-C9BD-7DB3-B976-1A9FEEE627D6', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E1DF6993-C9BD-7DB3-B976-1A9FEEE627D6)
2025-06-02 18:30:41,854 - INFO - 开始处理日期: 2025-06-01
2025-06-02 18:30:41,854 - INFO - Request Parameters - Page 1:
2025-06-02 18:30:41,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:30:41,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:30:49,994 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 43F12024-E8F1-73A0-AD3D-B52F6722BD4F Response: {'code': 'ServiceUnavailable', 'requestid': '43F12024-E8F1-73A0-AD3D-B52F6722BD4F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 43F12024-E8F1-73A0-AD3D-B52F6722BD4F)
2025-06-02 18:30:49,994 - INFO - 开始处理日期: 2025-06-02
2025-06-02 18:30:49,994 - INFO - Request Parameters - Page 1:
2025-06-02 18:30:49,994 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:30:49,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:30:50,479 - INFO - Response - Page 1:
2025-06-02 18:30:50,479 - INFO - 查询完成，共获取到 0 条记录
2025-06-02 18:30:50,479 - INFO - 获取到 0 条表单数据
2025-06-02 18:30:50,479 - INFO - 当前日期 2025-06-02 有 1 条MySQL数据需要处理
2025-06-02 18:30:50,479 - INFO - 开始批量插入 1 条新记录
2025-06-02 18:30:50,635 - INFO - 批量插入响应状态码: 200
2025-06-02 18:30:50,635 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 02 Jun 2025 10:30:56 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2B38A4CF-F82F-7186-9A4A-3083B50C7927', 'x-acs-trace-id': '05a51bd58523b14f43e35053a8fa3e8e', 'etag': '6Z38N5pYGwv2kNtVOxZnivg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-02 18:30:50,635 - INFO - 批量插入响应体: {'result': ['FINST-XBF66071XUXV2E6J6MRJY5VCX4MW3SCQ8YEBMU1']}
2025-06-02 18:30:50,650 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-02 18:30:50,650 - INFO - 成功插入的数据ID: ['FINST-XBF66071XUXV2E6J6MRJY5VCX4MW3SCQ8YEBMU1']
2025-06-02 18:30:55,666 - INFO - 批量插入完成，共 1 条记录
2025-06-02 18:30:55,666 - INFO - 日期 2025-06-02 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-02 18:30:55,666 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 2 条
2025-06-02 18:31:55,681 - INFO - 开始同步昨天与今天的销售数据: 2025-06-01 至 2025-06-02
2025-06-02 18:31:55,681 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 18:31:55,681 - INFO - 查询参数: ('2025-06-01', '2025-06-02')
2025-06-02 18:31:55,759 - INFO - MySQL查询成功，时间段: 2025-06-01 至 2025-06-02，共获取 514 条记录
2025-06-02 18:31:55,759 - INFO - 获取到 2 个日期需要处理: ['2025-06-01', '2025-06-02']
2025-06-02 18:31:55,759 - INFO - 开始处理日期: 2025-06-01
2025-06-02 18:31:55,759 - INFO - Request Parameters - Page 1:
2025-06-02 18:31:55,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:31:55,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:31:56,666 - INFO - Response - Page 1:
2025-06-02 18:31:56,666 - INFO - 第 1 页获取到 100 条记录
2025-06-02 18:31:56,869 - INFO - Request Parameters - Page 2:
2025-06-02 18:31:56,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:31:56,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:31:57,744 - INFO - Response - Page 2:
2025-06-02 18:31:57,744 - INFO - 第 2 页获取到 100 条记录
2025-06-02 18:31:57,947 - INFO - Request Parameters - Page 3:
2025-06-02 18:31:57,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:31:57,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:31:58,822 - INFO - Response - Page 3:
2025-06-02 18:31:58,822 - INFO - 第 3 页获取到 100 条记录
2025-06-02 18:31:59,025 - INFO - Request Parameters - Page 4:
2025-06-02 18:31:59,025 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:31:59,025 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:31:59,791 - INFO - Response - Page 4:
2025-06-02 18:31:59,791 - INFO - 第 4 页获取到 100 条记录
2025-06-02 18:31:59,994 - INFO - Request Parameters - Page 5:
2025-06-02 18:31:59,994 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:31:59,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:32:00,728 - INFO - Response - Page 5:
2025-06-02 18:32:00,728 - INFO - 第 5 页获取到 69 条记录
2025-06-02 18:32:00,931 - INFO - 查询完成，共获取到 469 条记录
2025-06-02 18:32:00,931 - INFO - 获取到 469 条表单数据
2025-06-02 18:32:00,931 - INFO - 当前日期 2025-06-01 有 513 条MySQL数据需要处理
2025-06-02 18:32:00,947 - INFO - 开始批量插入 44 条新记录
2025-06-02 18:32:01,166 - INFO - 批量插入响应状态码: 200
2025-06-02 18:32:01,166 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 02 Jun 2025 10:32:07 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2124', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E1C0CC8B-FA90-767D-B898-015238FF75DA', 'x-acs-trace-id': '59883953c6417e30fda2c759045fc462', 'etag': '2T0MRAgjieU+4C5Qkt/MY9w4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-02 18:32:01,166 - INFO - 批量插入响应体: {'result': ['FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM1K', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM2K', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM3K', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM4K', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM5K', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM6K', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM7K', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM8K', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM9K', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMAK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMBK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMCK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMDK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMEK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMFK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMGK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMHK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMIK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMJK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMKK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMLK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMMK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMNK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMOK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMPK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMQK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMRK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMSK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMTK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMUK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMVK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMWK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMXK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMYK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMZK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM0L', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM1L', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM2L', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM3L', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM4L', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM5L', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM6L', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM7L', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM8L']}
2025-06-02 18:32:01,166 - INFO - 批量插入表单数据成功，批次 1，共 44 条记录
2025-06-02 18:32:01,166 - INFO - 成功插入的数据ID: ['FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM1K', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM2K', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM3K', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM4K', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM5K', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM6K', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM7K', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM8K', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM9K', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMAK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMBK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMCK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMDK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMEK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMFK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMGK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMHK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMIK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMJK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMKK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMLK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMMK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMNK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMOK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMPK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMQK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMRK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMSK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMTK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMUK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMVK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMWK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMXK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMYK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBMZK', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM0L', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM1L', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM2L', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM3L', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM4L', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM5L', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM6L', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM7L', 'FINST-Q5D66871DOUVTAD96KTRA56BWZ6D30S8AYEBM8L']
2025-06-02 18:32:06,181 - INFO - 批量插入完成，共 44 条记录
2025-06-02 18:32:06,181 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 44 条，错误: 0 条
2025-06-02 18:32:06,181 - INFO - 开始处理日期: 2025-06-02
2025-06-02 18:32:06,181 - INFO - Request Parameters - Page 1:
2025-06-02 18:32:06,181 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 18:32:06,181 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 18:32:06,712 - INFO - Response - Page 1:
2025-06-02 18:32:06,712 - INFO - 第 1 页获取到 1 条记录
2025-06-02 18:32:06,916 - INFO - 查询完成，共获取到 1 条记录
2025-06-02 18:32:06,916 - INFO - 获取到 1 条表单数据
2025-06-02 18:32:06,916 - INFO - 当前日期 2025-06-02 有 1 条MySQL数据需要处理
2025-06-02 18:32:06,916 - INFO - 日期 2025-06-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 18:32:06,916 - INFO - 数据同步完成！更新: 0 条，插入: 44 条，错误: 0 条
2025-06-02 18:32:06,916 - INFO - 同步完成
2025-06-02 19:30:33,634 - INFO - 使用默认增量同步（当天更新数据）
2025-06-02 19:30:33,634 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 19:30:33,634 - INFO - 查询参数: ('2025-06-02',)
2025-06-02 19:30:33,712 - INFO - MySQL查询成功，增量数据（日期: 2025-06-02），共获取 199 条记录
2025-06-02 19:30:33,712 - INFO - 获取到 3 个日期需要处理: ['2025-05-31', '2025-06-01', '2025-06-02']
2025-06-02 19:30:33,712 - INFO - 开始处理日期: 2025-05-31
2025-06-02 19:30:33,712 - INFO - Request Parameters - Page 1:
2025-06-02 19:30:33,712 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 19:30:33,712 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 19:30:41,837 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5D26DF28-1EB1-745F-A75A-A31D235C2170 Response: {'code': 'ServiceUnavailable', 'requestid': '5D26DF28-1EB1-745F-A75A-A31D235C2170', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 5D26DF28-1EB1-745F-A75A-A31D235C2170)
2025-06-02 19:30:41,837 - INFO - 开始处理日期: 2025-06-01
2025-06-02 19:30:41,837 - INFO - Request Parameters - Page 1:
2025-06-02 19:30:41,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 19:30:41,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 19:30:49,962 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2DB6C269-E270-7457-832C-6D66C5C372E8 Response: {'code': 'ServiceUnavailable', 'requestid': '2DB6C269-E270-7457-832C-6D66C5C372E8', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2DB6C269-E270-7457-832C-6D66C5C372E8)
2025-06-02 19:30:49,962 - INFO - 开始处理日期: 2025-06-02
2025-06-02 19:30:49,962 - INFO - Request Parameters - Page 1:
2025-06-02 19:30:49,962 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 19:30:49,962 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 19:30:50,478 - INFO - Response - Page 1:
2025-06-02 19:30:50,478 - INFO - 第 1 页获取到 1 条记录
2025-06-02 19:30:50,681 - INFO - 查询完成，共获取到 1 条记录
2025-06-02 19:30:50,681 - INFO - 获取到 1 条表单数据
2025-06-02 19:30:50,681 - INFO - 当前日期 2025-06-02 有 2 条MySQL数据需要处理
2025-06-02 19:30:50,681 - INFO - 开始批量插入 1 条新记录
2025-06-02 19:30:50,822 - INFO - 批量插入响应状态码: 200
2025-06-02 19:30:50,822 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 02 Jun 2025 11:30:50 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D3C44F38-33A2-7312-A4E0-99CA55649808', 'x-acs-trace-id': 'c7c460fd49d2cd7c49e99fb0d6cf9ebb', 'etag': '62MPRIiflCmczzRqBcRGhtA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-02 19:30:50,822 - INFO - 批量插入响应体: {'result': ['FINST-9EA669D1BDTV144EC62FQ4RJUQFX3YMRD0FBMVE']}
2025-06-02 19:30:50,822 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-02 19:30:50,822 - INFO - 成功插入的数据ID: ['FINST-9EA669D1BDTV144EC62FQ4RJUQFX3YMRD0FBMVE']
2025-06-02 19:30:55,837 - INFO - 批量插入完成，共 1 条记录
2025-06-02 19:30:55,837 - INFO - 日期 2025-06-02 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-02 19:30:55,837 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 2 条
2025-06-02 19:31:55,852 - INFO - 开始同步昨天与今天的销售数据: 2025-06-01 至 2025-06-02
2025-06-02 19:31:55,852 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 19:31:55,852 - INFO - 查询参数: ('2025-06-01', '2025-06-02')
2025-06-02 19:31:55,931 - INFO - MySQL查询成功，时间段: 2025-06-01 至 2025-06-02，共获取 515 条记录
2025-06-02 19:31:55,931 - INFO - 获取到 2 个日期需要处理: ['2025-06-01', '2025-06-02']
2025-06-02 19:31:55,931 - INFO - 开始处理日期: 2025-06-01
2025-06-02 19:31:55,931 - INFO - Request Parameters - Page 1:
2025-06-02 19:31:55,931 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 19:31:55,931 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 19:31:56,806 - INFO - Response - Page 1:
2025-06-02 19:31:56,806 - INFO - 第 1 页获取到 100 条记录
2025-06-02 19:31:57,009 - INFO - Request Parameters - Page 2:
2025-06-02 19:31:57,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 19:31:57,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 19:31:57,837 - INFO - Response - Page 2:
2025-06-02 19:31:57,852 - INFO - 第 2 页获取到 100 条记录
2025-06-02 19:31:58,056 - INFO - Request Parameters - Page 3:
2025-06-02 19:31:58,056 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 19:31:58,056 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 19:31:58,837 - INFO - Response - Page 3:
2025-06-02 19:31:58,837 - INFO - 第 3 页获取到 100 条记录
2025-06-02 19:31:59,040 - INFO - Request Parameters - Page 4:
2025-06-02 19:31:59,040 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 19:31:59,040 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 19:32:00,243 - INFO - Response - Page 4:
2025-06-02 19:32:00,243 - INFO - 第 4 页获取到 100 条记录
2025-06-02 19:32:00,446 - INFO - Request Parameters - Page 5:
2025-06-02 19:32:00,446 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 19:32:00,446 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 19:32:01,243 - INFO - Response - Page 5:
2025-06-02 19:32:01,243 - INFO - 第 5 页获取到 100 条记录
2025-06-02 19:32:01,446 - INFO - Request Parameters - Page 6:
2025-06-02 19:32:01,446 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 19:32:01,446 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 19:32:02,040 - INFO - Response - Page 6:
2025-06-02 19:32:02,040 - INFO - 第 6 页获取到 13 条记录
2025-06-02 19:32:02,243 - INFO - 查询完成，共获取到 513 条记录
2025-06-02 19:32:02,243 - INFO - 获取到 513 条表单数据
2025-06-02 19:32:02,243 - INFO - 当前日期 2025-06-01 有 513 条MySQL数据需要处理
2025-06-02 19:32:02,259 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 19:32:02,259 - INFO - 开始处理日期: 2025-06-02
2025-06-02 19:32:02,259 - INFO - Request Parameters - Page 1:
2025-06-02 19:32:02,259 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 19:32:02,259 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 19:32:02,743 - INFO - Response - Page 1:
2025-06-02 19:32:02,743 - INFO - 第 1 页获取到 2 条记录
2025-06-02 19:32:02,946 - INFO - 查询完成，共获取到 2 条记录
2025-06-02 19:32:02,946 - INFO - 获取到 2 条表单数据
2025-06-02 19:32:02,946 - INFO - 当前日期 2025-06-02 有 2 条MySQL数据需要处理
2025-06-02 19:32:02,946 - INFO - 日期 2025-06-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 19:32:02,946 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 19:32:02,946 - INFO - 同步完成
2025-06-02 20:30:33,558 - INFO - 使用默认增量同步（当天更新数据）
2025-06-02 20:30:33,558 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 20:30:33,574 - INFO - 查询参数: ('2025-06-02',)
2025-06-02 20:30:33,636 - INFO - MySQL查询成功，增量数据（日期: 2025-06-02），共获取 200 条记录
2025-06-02 20:30:33,636 - INFO - 获取到 3 个日期需要处理: ['2025-05-31', '2025-06-01', '2025-06-02']
2025-06-02 20:30:33,652 - INFO - 开始处理日期: 2025-05-31
2025-06-02 20:30:33,652 - INFO - Request Parameters - Page 1:
2025-06-02 20:30:33,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 20:30:33,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 20:30:41,793 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2F1D4851-B9EB-78F6-B34A-36E6826A64FC Response: {'code': 'ServiceUnavailable', 'requestid': '2F1D4851-B9EB-78F6-B34A-36E6826A64FC', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2F1D4851-B9EB-78F6-B34A-36E6826A64FC)
2025-06-02 20:30:41,793 - INFO - 开始处理日期: 2025-06-01
2025-06-02 20:30:41,793 - INFO - Request Parameters - Page 1:
2025-06-02 20:30:41,793 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 20:30:41,793 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 20:30:49,902 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: AD2CD51C-1CAF-729E-8491-5B779AF883AE Response: {'code': 'ServiceUnavailable', 'requestid': 'AD2CD51C-1CAF-729E-8491-5B779AF883AE', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: AD2CD51C-1CAF-729E-8491-5B779AF883AE)
2025-06-02 20:30:49,902 - INFO - 开始处理日期: 2025-06-02
2025-06-02 20:30:49,902 - INFO - Request Parameters - Page 1:
2025-06-02 20:30:49,902 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 20:30:49,902 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 20:30:50,418 - INFO - Response - Page 1:
2025-06-02 20:30:50,418 - INFO - 第 1 页获取到 2 条记录
2025-06-02 20:30:50,621 - INFO - 查询完成，共获取到 2 条记录
2025-06-02 20:30:50,621 - INFO - 获取到 2 条表单数据
2025-06-02 20:30:50,621 - INFO - 当前日期 2025-06-02 有 3 条MySQL数据需要处理
2025-06-02 20:30:50,621 - INFO - 开始批量插入 1 条新记录
2025-06-02 20:30:50,761 - INFO - 批量插入响应状态码: 200
2025-06-02 20:30:50,761 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 02 Jun 2025 12:30:50 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E1D11777-F03A-7AC6-93E5-391FE0078F81', 'x-acs-trace-id': '7df2f572f9bc657a1059312c5ef813fa', 'etag': '6Z5PqIVAz9E8v119w37twRg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-02 20:30:50,761 - INFO - 批量插入响应体: {'result': ['FINST-B2766R8178UVXMRG6BFGG5HP7KVI25EXI2FBMIV']}
2025-06-02 20:30:50,761 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-02 20:30:50,761 - INFO - 成功插入的数据ID: ['FINST-B2766R8178UVXMRG6BFGG5HP7KVI25EXI2FBMIV']
2025-06-02 20:30:55,777 - INFO - 批量插入完成，共 1 条记录
2025-06-02 20:30:55,777 - INFO - 日期 2025-06-02 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-02 20:30:55,777 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 2 条
2025-06-02 20:31:55,792 - INFO - 开始同步昨天与今天的销售数据: 2025-06-01 至 2025-06-02
2025-06-02 20:31:55,792 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 20:31:55,792 - INFO - 查询参数: ('2025-06-01', '2025-06-02')
2025-06-02 20:31:55,870 - INFO - MySQL查询成功，时间段: 2025-06-01 至 2025-06-02，共获取 516 条记录
2025-06-02 20:31:55,870 - INFO - 获取到 2 个日期需要处理: ['2025-06-01', '2025-06-02']
2025-06-02 20:31:55,886 - INFO - 开始处理日期: 2025-06-01
2025-06-02 20:31:55,886 - INFO - Request Parameters - Page 1:
2025-06-02 20:31:55,886 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 20:31:55,886 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 20:31:56,823 - INFO - Response - Page 1:
2025-06-02 20:31:56,823 - INFO - 第 1 页获取到 100 条记录
2025-06-02 20:31:57,027 - INFO - Request Parameters - Page 2:
2025-06-02 20:31:57,027 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 20:31:57,027 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 20:32:05,152 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 350D9FB6-F8FB-705E-ACFB-89F8379A47AC Response: {'code': 'ServiceUnavailable', 'requestid': '350D9FB6-F8FB-705E-ACFB-89F8379A47AC', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 350D9FB6-F8FB-705E-ACFB-89F8379A47AC)
2025-06-02 20:32:05,152 - INFO - 开始处理日期: 2025-06-02
2025-06-02 20:32:05,152 - INFO - Request Parameters - Page 1:
2025-06-02 20:32:05,152 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 20:32:05,152 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 20:32:05,683 - INFO - Response - Page 1:
2025-06-02 20:32:05,683 - INFO - 第 1 页获取到 3 条记录
2025-06-02 20:32:05,886 - INFO - 查询完成，共获取到 3 条记录
2025-06-02 20:32:05,886 - INFO - 获取到 3 条表单数据
2025-06-02 20:32:05,886 - INFO - 当前日期 2025-06-02 有 3 条MySQL数据需要处理
2025-06-02 20:32:05,886 - INFO - 日期 2025-06-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 20:32:05,886 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-02 20:32:05,886 - INFO - 同步完成
2025-06-02 21:30:33,637 - INFO - 使用默认增量同步（当天更新数据）
2025-06-02 21:30:33,637 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 21:30:33,637 - INFO - 查询参数: ('2025-06-02',)
2025-06-02 21:30:33,699 - INFO - MySQL查询成功，增量数据（日期: 2025-06-02），共获取 201 条记录
2025-06-02 21:30:33,715 - INFO - 获取到 3 个日期需要处理: ['2025-05-31', '2025-06-01', '2025-06-02']
2025-06-02 21:30:33,715 - INFO - 开始处理日期: 2025-05-31
2025-06-02 21:30:33,715 - INFO - Request Parameters - Page 1:
2025-06-02 21:30:33,715 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:30:33,715 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:30:41,840 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5C9701C2-EACB-7821-9F1C-05E885846125 Response: {'code': 'ServiceUnavailable', 'requestid': '5C9701C2-EACB-7821-9F1C-05E885846125', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 5C9701C2-EACB-7821-9F1C-05E885846125)
2025-06-02 21:30:41,840 - INFO - 开始处理日期: 2025-06-01
2025-06-02 21:30:41,840 - INFO - Request Parameters - Page 1:
2025-06-02 21:30:41,840 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:30:41,840 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:30:49,965 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 438F876C-2298-7EA3-B8C5-BACC506EA124 Response: {'code': 'ServiceUnavailable', 'requestid': '438F876C-2298-7EA3-B8C5-BACC506EA124', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 438F876C-2298-7EA3-B8C5-BACC506EA124)
2025-06-02 21:30:49,965 - INFO - 开始处理日期: 2025-06-02
2025-06-02 21:30:49,965 - INFO - Request Parameters - Page 1:
2025-06-02 21:30:49,965 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:30:49,965 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:30:52,480 - INFO - Response - Page 1:
2025-06-02 21:30:52,480 - INFO - 第 1 页获取到 3 条记录
2025-06-02 21:30:52,684 - INFO - 查询完成，共获取到 3 条记录
2025-06-02 21:30:52,684 - INFO - 获取到 3 条表单数据
2025-06-02 21:30:52,684 - INFO - 当前日期 2025-06-02 有 4 条MySQL数据需要处理
2025-06-02 21:30:52,684 - INFO - 开始批量插入 1 条新记录
2025-06-02 21:30:52,824 - INFO - 批量插入响应状态码: 200
2025-06-02 21:30:52,824 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 02 Jun 2025 13:30:52 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6424D71E-D0AC-784A-A42E-231D106DF237', 'x-acs-trace-id': 'cb09fad09fd6fa6e7e163fb736003ee2', 'etag': '5OP/K66d0SeHdiizuohUsWA9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-02 21:30:52,824 - INFO - 批量插入响应体: {'result': ['FINST-MKF66PA1O7VVBTCXEN9ZJ5GB1OBV34S4O4FBMB']}
2025-06-02 21:30:52,824 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-02 21:30:52,824 - INFO - 成功插入的数据ID: ['FINST-MKF66PA1O7VVBTCXEN9ZJ5GB1OBV34S4O4FBMB']
2025-06-02 21:30:57,840 - INFO - 批量插入完成，共 1 条记录
2025-06-02 21:30:57,840 - INFO - 日期 2025-06-02 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-02 21:30:57,840 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 2 条
2025-06-02 21:31:57,855 - INFO - 开始同步昨天与今天的销售数据: 2025-06-01 至 2025-06-02
2025-06-02 21:31:57,855 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 21:31:57,855 - INFO - 查询参数: ('2025-06-01', '2025-06-02')
2025-06-02 21:31:57,933 - INFO - MySQL查询成功，时间段: 2025-06-01 至 2025-06-02，共获取 517 条记录
2025-06-02 21:31:57,933 - INFO - 获取到 2 个日期需要处理: ['2025-06-01', '2025-06-02']
2025-06-02 21:31:57,933 - INFO - 开始处理日期: 2025-06-01
2025-06-02 21:31:57,933 - INFO - Request Parameters - Page 1:
2025-06-02 21:31:57,933 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:31:57,933 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:31:58,902 - INFO - Response - Page 1:
2025-06-02 21:31:58,902 - INFO - 第 1 页获取到 100 条记录
2025-06-02 21:31:59,105 - INFO - Request Parameters - Page 2:
2025-06-02 21:31:59,105 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:31:59,105 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:31:59,917 - INFO - Response - Page 2:
2025-06-02 21:31:59,917 - INFO - 第 2 页获取到 100 条记录
2025-06-02 21:32:00,121 - INFO - Request Parameters - Page 3:
2025-06-02 21:32:00,121 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:32:00,121 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:32:00,996 - INFO - Response - Page 3:
2025-06-02 21:32:00,996 - INFO - 第 3 页获取到 100 条记录
2025-06-02 21:32:01,199 - INFO - Request Parameters - Page 4:
2025-06-02 21:32:01,199 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:32:01,199 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:32:02,089 - INFO - Response - Page 4:
2025-06-02 21:32:02,089 - INFO - 第 4 页获取到 100 条记录
2025-06-02 21:32:02,292 - INFO - Request Parameters - Page 5:
2025-06-02 21:32:02,292 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:32:02,292 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:32:03,136 - INFO - Response - Page 5:
2025-06-02 21:32:03,136 - INFO - 第 5 页获取到 100 条记录
2025-06-02 21:32:03,339 - INFO - Request Parameters - Page 6:
2025-06-02 21:32:03,339 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:32:03,339 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:32:03,933 - INFO - Response - Page 6:
2025-06-02 21:32:03,933 - INFO - 第 6 页获取到 13 条记录
2025-06-02 21:32:04,136 - INFO - 查询完成，共获取到 513 条记录
2025-06-02 21:32:04,136 - INFO - 获取到 513 条表单数据
2025-06-02 21:32:04,136 - INFO - 当前日期 2025-06-01 有 513 条MySQL数据需要处理
2025-06-02 21:32:04,152 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 21:32:04,152 - INFO - 开始处理日期: 2025-06-02
2025-06-02 21:32:04,152 - INFO - Request Parameters - Page 1:
2025-06-02 21:32:04,152 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 21:32:04,152 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 21:32:04,714 - INFO - Response - Page 1:
2025-06-02 21:32:04,714 - INFO - 第 1 页获取到 4 条记录
2025-06-02 21:32:04,917 - INFO - 查询完成，共获取到 4 条记录
2025-06-02 21:32:04,917 - INFO - 获取到 4 条表单数据
2025-06-02 21:32:04,917 - INFO - 当前日期 2025-06-02 有 4 条MySQL数据需要处理
2025-06-02 21:32:04,917 - INFO - 日期 2025-06-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 21:32:04,917 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 21:32:04,917 - INFO - 同步完成
2025-06-02 22:30:33,573 - INFO - 使用默认增量同步（当天更新数据）
2025-06-02 22:30:33,573 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 22:30:33,573 - INFO - 查询参数: ('2025-06-02',)
2025-06-02 22:30:33,652 - INFO - MySQL查询成功，增量数据（日期: 2025-06-02），共获取 238 条记录
2025-06-02 22:30:33,652 - INFO - 获取到 3 个日期需要处理: ['2025-05-31', '2025-06-01', '2025-06-02']
2025-06-02 22:30:33,652 - INFO - 开始处理日期: 2025-05-31
2025-06-02 22:30:33,652 - INFO - Request Parameters - Page 1:
2025-06-02 22:30:33,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 22:30:33,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 22:30:41,777 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 686D4D9B-C561-7EAA-9058-A487608D2B11 Response: {'code': 'ServiceUnavailable', 'requestid': '686D4D9B-C561-7EAA-9058-A487608D2B11', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 686D4D9B-C561-7EAA-9058-A487608D2B11)
2025-06-02 22:30:41,777 - INFO - 开始处理日期: 2025-06-01
2025-06-02 22:30:41,777 - INFO - Request Parameters - Page 1:
2025-06-02 22:30:41,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 22:30:41,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 22:30:49,980 - INFO - Response - Page 1:
2025-06-02 22:30:49,980 - INFO - 第 1 页获取到 100 条记录
2025-06-02 22:30:50,183 - INFO - Request Parameters - Page 2:
2025-06-02 22:30:50,183 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 22:30:50,183 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 22:30:51,026 - INFO - Response - Page 2:
2025-06-02 22:30:51,026 - INFO - 第 2 页获取到 100 条记录
2025-06-02 22:30:51,230 - INFO - Request Parameters - Page 3:
2025-06-02 22:30:51,230 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 22:30:51,230 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 22:30:52,183 - INFO - Response - Page 3:
2025-06-02 22:30:52,183 - INFO - 第 3 页获取到 100 条记录
2025-06-02 22:30:52,386 - INFO - Request Parameters - Page 4:
2025-06-02 22:30:52,386 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 22:30:52,386 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 22:30:53,214 - INFO - Response - Page 4:
2025-06-02 22:30:53,214 - INFO - 第 4 页获取到 100 条记录
2025-06-02 22:30:53,417 - INFO - Request Parameters - Page 5:
2025-06-02 22:30:53,417 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 22:30:53,417 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 22:30:54,261 - INFO - Response - Page 5:
2025-06-02 22:30:54,261 - INFO - 第 5 页获取到 100 条记录
2025-06-02 22:30:54,464 - INFO - Request Parameters - Page 6:
2025-06-02 22:30:54,464 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 22:30:54,464 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 22:31:02,558 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3A20D6A4-6831-7110-B05F-06C3C02E26A7 Response: {'code': 'ServiceUnavailable', 'requestid': '3A20D6A4-6831-7110-B05F-06C3C02E26A7', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3A20D6A4-6831-7110-B05F-06C3C02E26A7)
2025-06-02 22:31:02,558 - INFO - 开始处理日期: 2025-06-02
2025-06-02 22:31:02,558 - INFO - Request Parameters - Page 1:
2025-06-02 22:31:02,558 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 22:31:02,558 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 22:31:04,995 - INFO - Response - Page 1:
2025-06-02 22:31:05,011 - INFO - 第 1 页获取到 4 条记录
2025-06-02 22:31:05,214 - INFO - 查询完成，共获取到 4 条记录
2025-06-02 22:31:05,214 - INFO - 获取到 4 条表单数据
2025-06-02 22:31:05,214 - INFO - 当前日期 2025-06-02 有 41 条MySQL数据需要处理
2025-06-02 22:31:05,214 - INFO - 开始批量插入 37 条新记录
2025-06-02 22:31:05,417 - INFO - 批量插入响应状态码: 200
2025-06-02 22:31:05,417 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 02 Jun 2025 14:31:05 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1788', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2CD7FB9B-CD2F-715B-A17A-BCD474DC3D2E', 'x-acs-trace-id': '2f536494b72ff505259cbdd273e1f83a', 'etag': '1olwncBwRxPvzls5bXIc+Vg8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-02 22:31:05,417 - INFO - 批量插入响应体: {'result': ['FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBM5L', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBM6L', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBM7L', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBM8L', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBM9L', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMAL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMBL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMCL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMDL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMEL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMFL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMGL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMHL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMIL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMJL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMKL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMLL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMML', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMNL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMOL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMPL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMQL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMRL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMSL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMTL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMUL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMVL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMWL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMXL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMYL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMZL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBM0M', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBM1M', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBM2M', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBM3M', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBM4M', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBM5M']}
2025-06-02 22:31:05,417 - INFO - 批量插入表单数据成功，批次 1，共 37 条记录
2025-06-02 22:31:05,417 - INFO - 成功插入的数据ID: ['FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBM5L', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBM6L', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBM7L', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBM8L', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBM9L', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMAL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMBL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMCL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMDL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMEL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMFL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMGL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMHL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMIL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMJL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMKL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMLL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMML', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMNL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMOL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMPL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMQL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMRL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMSL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMTL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMUL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMVL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMWL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMXL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMYL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBMZL', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBM0M', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBM1M', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBM2M', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBM3M', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBM4M', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G03GAKT6FBM5M']
2025-06-02 22:31:10,433 - INFO - 批量插入完成，共 37 条记录
2025-06-02 22:31:10,433 - INFO - 日期 2025-06-02 处理完成 - 更新: 0 条，插入: 37 条，错误: 0 条
2025-06-02 22:31:10,433 - INFO - 数据同步完成！更新: 0 条，插入: 37 条，错误: 2 条
2025-06-02 22:32:10,448 - INFO - 开始同步昨天与今天的销售数据: 2025-06-01 至 2025-06-02
2025-06-02 22:32:10,448 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 22:32:10,448 - INFO - 查询参数: ('2025-06-01', '2025-06-02')
2025-06-02 22:32:10,526 - INFO - MySQL查询成功，时间段: 2025-06-01 至 2025-06-02，共获取 563 条记录
2025-06-02 22:32:10,526 - INFO - 获取到 2 个日期需要处理: ['2025-06-01', '2025-06-02']
2025-06-02 22:32:10,526 - INFO - 开始处理日期: 2025-06-01
2025-06-02 22:32:10,526 - INFO - Request Parameters - Page 1:
2025-06-02 22:32:10,526 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 22:32:10,526 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 22:32:11,338 - INFO - Response - Page 1:
2025-06-02 22:32:11,338 - INFO - 第 1 页获取到 100 条记录
2025-06-02 22:32:11,542 - INFO - Request Parameters - Page 2:
2025-06-02 22:32:11,542 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 22:32:11,542 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 22:32:12,354 - INFO - Response - Page 2:
2025-06-02 22:32:12,354 - INFO - 第 2 页获取到 100 条记录
2025-06-02 22:32:12,557 - INFO - Request Parameters - Page 3:
2025-06-02 22:32:12,557 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 22:32:12,557 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 22:32:13,432 - INFO - Response - Page 3:
2025-06-02 22:32:13,432 - INFO - 第 3 页获取到 100 条记录
2025-06-02 22:32:13,635 - INFO - Request Parameters - Page 4:
2025-06-02 22:32:13,635 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 22:32:13,635 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 22:32:14,495 - INFO - Response - Page 4:
2025-06-02 22:32:14,495 - INFO - 第 4 页获取到 100 条记录
2025-06-02 22:32:14,698 - INFO - Request Parameters - Page 5:
2025-06-02 22:32:14,698 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 22:32:14,698 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 22:32:15,463 - INFO - Response - Page 5:
2025-06-02 22:32:15,463 - INFO - 第 5 页获取到 100 条记录
2025-06-02 22:32:15,667 - INFO - Request Parameters - Page 6:
2025-06-02 22:32:15,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 22:32:15,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 22:32:16,229 - INFO - Response - Page 6:
2025-06-02 22:32:16,229 - INFO - 第 6 页获取到 13 条记录
2025-06-02 22:32:16,432 - INFO - 查询完成，共获取到 513 条记录
2025-06-02 22:32:16,432 - INFO - 获取到 513 条表单数据
2025-06-02 22:32:16,432 - INFO - 当前日期 2025-06-01 有 513 条MySQL数据需要处理
2025-06-02 22:32:16,448 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 22:32:16,448 - INFO - 开始处理日期: 2025-06-02
2025-06-02 22:32:16,448 - INFO - Request Parameters - Page 1:
2025-06-02 22:32:16,448 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 22:32:16,448 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 22:32:17,135 - INFO - Response - Page 1:
2025-06-02 22:32:17,135 - INFO - 第 1 页获取到 41 条记录
2025-06-02 22:32:17,338 - INFO - 查询完成，共获取到 41 条记录
2025-06-02 22:32:17,338 - INFO - 获取到 41 条表单数据
2025-06-02 22:32:17,338 - INFO - 当前日期 2025-06-02 有 50 条MySQL数据需要处理
2025-06-02 22:32:17,338 - INFO - 开始批量插入 9 条新记录
2025-06-02 22:32:17,479 - INFO - 批量插入响应状态码: 200
2025-06-02 22:32:17,479 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 02 Jun 2025 14:32:17 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '444', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B2E798F0-D127-7EEF-BA4D-229CD098C516', 'x-acs-trace-id': 'ce28b272de3ad63ced21c4cb97ee7edc', 'etag': '411vqb57f7WBWae8T3jXZtg4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-02 22:32:17,479 - INFO - 批量插入响应体: {'result': ['FINST-7PF66MD1ZEVVZCF0DPKTFABK8II737W3V6FBMF9', 'FINST-7PF66MD1ZEVVZCF0DPKTFABK8II737W3V6FBMG9', 'FINST-7PF66MD1ZEVVZCF0DPKTFABK8II737W3V6FBMH9', 'FINST-7PF66MD1ZEVVZCF0DPKTFABK8II737W3V6FBMI9', 'FINST-7PF66MD1ZEVVZCF0DPKTFABK8II737W3V6FBMJ9', 'FINST-7PF66MD1ZEVVZCF0DPKTFABK8II737W3V6FBMK9', 'FINST-7PF66MD1ZEVVZCF0DPKTFABK8II737W3V6FBML9', 'FINST-7PF66MD1ZEVVZCF0DPKTFABK8II737W3V6FBMM9', 'FINST-7PF66MD1ZEVVZCF0DPKTFABK8II737W3V6FBMN9']}
2025-06-02 22:32:17,479 - INFO - 批量插入表单数据成功，批次 1，共 9 条记录
2025-06-02 22:32:17,479 - INFO - 成功插入的数据ID: ['FINST-7PF66MD1ZEVVZCF0DPKTFABK8II737W3V6FBMF9', 'FINST-7PF66MD1ZEVVZCF0DPKTFABK8II737W3V6FBMG9', 'FINST-7PF66MD1ZEVVZCF0DPKTFABK8II737W3V6FBMH9', 'FINST-7PF66MD1ZEVVZCF0DPKTFABK8II737W3V6FBMI9', 'FINST-7PF66MD1ZEVVZCF0DPKTFABK8II737W3V6FBMJ9', 'FINST-7PF66MD1ZEVVZCF0DPKTFABK8II737W3V6FBMK9', 'FINST-7PF66MD1ZEVVZCF0DPKTFABK8II737W3V6FBML9', 'FINST-7PF66MD1ZEVVZCF0DPKTFABK8II737W3V6FBMM9', 'FINST-7PF66MD1ZEVVZCF0DPKTFABK8II737W3V6FBMN9']
2025-06-02 22:32:22,495 - INFO - 批量插入完成，共 9 条记录
2025-06-02 22:32:22,495 - INFO - 日期 2025-06-02 处理完成 - 更新: 0 条，插入: 9 条，错误: 0 条
2025-06-02 22:32:22,495 - INFO - 数据同步完成！更新: 0 条，插入: 9 条，错误: 0 条
2025-06-02 22:32:22,495 - INFO - 同步完成
2025-06-02 23:30:33,534 - INFO - 使用默认增量同步（当天更新数据）
2025-06-02 23:30:33,534 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 23:30:33,534 - INFO - 查询参数: ('2025-06-02',)
2025-06-02 23:30:33,613 - INFO - MySQL查询成功，增量数据（日期: 2025-06-02），共获取 249 条记录
2025-06-02 23:30:33,613 - INFO - 获取到 3 个日期需要处理: ['2025-05-31', '2025-06-01', '2025-06-02']
2025-06-02 23:30:33,613 - INFO - 开始处理日期: 2025-05-31
2025-06-02 23:30:33,613 - INFO - Request Parameters - Page 1:
2025-06-02 23:30:33,613 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 23:30:33,613 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748620800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 23:30:41,753 - ERROR - 处理日期 2025-05-31 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 9EC2CCBE-CDCB-7FCD-B59B-892CEDAA2C73 Response: {'code': 'ServiceUnavailable', 'requestid': '9EC2CCBE-CDCB-7FCD-B59B-892CEDAA2C73', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 9EC2CCBE-CDCB-7FCD-B59B-892CEDAA2C73)
2025-06-02 23:30:41,753 - INFO - 开始处理日期: 2025-06-01
2025-06-02 23:30:41,753 - INFO - Request Parameters - Page 1:
2025-06-02 23:30:41,753 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 23:30:41,753 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 23:30:49,861 - ERROR - 处理日期 2025-06-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 140C12DA-7F38-7A7A-BC6B-375390FE64B2 Response: {'code': 'ServiceUnavailable', 'requestid': '140C12DA-7F38-7A7A-BC6B-375390FE64B2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 140C12DA-7F38-7A7A-BC6B-375390FE64B2)
2025-06-02 23:30:49,861 - INFO - 开始处理日期: 2025-06-02
2025-06-02 23:30:49,861 - INFO - Request Parameters - Page 1:
2025-06-02 23:30:49,861 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 23:30:49,861 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 23:30:57,392 - INFO - Response - Page 1:
2025-06-02 23:30:57,392 - INFO - 第 1 页获取到 50 条记录
2025-06-02 23:30:57,595 - INFO - 查询完成，共获取到 50 条记录
2025-06-02 23:30:57,595 - INFO - 获取到 50 条表单数据
2025-06-02 23:30:57,595 - INFO - 当前日期 2025-06-02 有 52 条MySQL数据需要处理
2025-06-02 23:30:57,595 - INFO - 开始批量插入 2 条新记录
2025-06-02 23:30:57,736 - INFO - 批量插入响应状态码: 200
2025-06-02 23:30:57,736 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 02 Jun 2025 15:30:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '18626C07-69B0-7D84-9D1A-578728B7E727', 'x-acs-trace-id': '8ee758fe5e116b3abbe065562d80799e', 'etag': '1eL78nZM6yTybFMZhXQRuZg8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-02 23:30:57,736 - INFO - 批量插入响应体: {'result': ['FINST-1MD668B1VKVVSQV37QQGE8FPEQU7327KY8FBMWN', 'FINST-1MD668B1VKVVSQV37QQGE8FPEQU7327KY8FBMXN']}
2025-06-02 23:30:57,736 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-06-02 23:30:57,736 - INFO - 成功插入的数据ID: ['FINST-1MD668B1VKVVSQV37QQGE8FPEQU7327KY8FBMWN', 'FINST-1MD668B1VKVVSQV37QQGE8FPEQU7327KY8FBMXN']
2025-06-02 23:31:02,751 - INFO - 批量插入完成，共 2 条记录
2025-06-02 23:31:02,751 - INFO - 日期 2025-06-02 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-06-02 23:31:02,751 - INFO - 数据同步完成！更新: 0 条，插入: 2 条，错误: 2 条
2025-06-02 23:32:02,762 - INFO - 开始同步昨天与今天的销售数据: 2025-06-01 至 2025-06-02
2025-06-02 23:32:02,762 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-06-02 23:32:02,762 - INFO - 查询参数: ('2025-06-01', '2025-06-02')
2025-06-02 23:32:02,840 - INFO - MySQL查询成功，时间段: 2025-06-01 至 2025-06-02，共获取 565 条记录
2025-06-02 23:32:02,840 - INFO - 获取到 2 个日期需要处理: ['2025-06-01', '2025-06-02']
2025-06-02 23:32:02,840 - INFO - 开始处理日期: 2025-06-01
2025-06-02 23:32:02,840 - INFO - Request Parameters - Page 1:
2025-06-02 23:32:02,840 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 23:32:02,840 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 23:32:03,746 - INFO - Response - Page 1:
2025-06-02 23:32:03,746 - INFO - 第 1 页获取到 100 条记录
2025-06-02 23:32:03,949 - INFO - Request Parameters - Page 2:
2025-06-02 23:32:03,949 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 23:32:03,949 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 23:32:04,746 - INFO - Response - Page 2:
2025-06-02 23:32:04,746 - INFO - 第 2 页获取到 100 条记录
2025-06-02 23:32:04,949 - INFO - Request Parameters - Page 3:
2025-06-02 23:32:04,949 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 23:32:04,949 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 23:32:05,715 - INFO - Response - Page 3:
2025-06-02 23:32:05,715 - INFO - 第 3 页获取到 100 条记录
2025-06-02 23:32:05,918 - INFO - Request Parameters - Page 4:
2025-06-02 23:32:05,918 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 23:32:05,918 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 23:32:06,762 - INFO - Response - Page 4:
2025-06-02 23:32:06,762 - INFO - 第 4 页获取到 100 条记录
2025-06-02 23:32:06,965 - INFO - Request Parameters - Page 5:
2025-06-02 23:32:06,965 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 23:32:06,965 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 23:32:07,808 - INFO - Response - Page 5:
2025-06-02 23:32:07,808 - INFO - 第 5 页获取到 100 条记录
2025-06-02 23:32:08,012 - INFO - Request Parameters - Page 6:
2025-06-02 23:32:08,012 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 23:32:08,012 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748707200000, 1748793599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 23:32:08,683 - INFO - Response - Page 6:
2025-06-02 23:32:08,683 - INFO - 第 6 页获取到 13 条记录
2025-06-02 23:32:08,887 - INFO - 查询完成，共获取到 513 条记录
2025-06-02 23:32:08,887 - INFO - 获取到 513 条表单数据
2025-06-02 23:32:08,887 - INFO - 当前日期 2025-06-01 有 513 条MySQL数据需要处理
2025-06-02 23:32:08,902 - INFO - 日期 2025-06-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 23:32:08,902 - INFO - 开始处理日期: 2025-06-02
2025-06-02 23:32:08,902 - INFO - Request Parameters - Page 1:
2025-06-02 23:32:08,902 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-02 23:32:08,902 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1748793600000, 1748879999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-02 23:32:09,683 - INFO - Response - Page 1:
2025-06-02 23:32:09,683 - INFO - 第 1 页获取到 52 条记录
2025-06-02 23:32:09,886 - INFO - 查询完成，共获取到 52 条记录
2025-06-02 23:32:09,886 - INFO - 获取到 52 条表单数据
2025-06-02 23:32:09,886 - INFO - 当前日期 2025-06-02 有 52 条MySQL数据需要处理
2025-06-02 23:32:09,886 - INFO - 日期 2025-06-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 23:32:09,886 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-02 23:32:09,886 - INFO - 同步完成
