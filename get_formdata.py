# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
import os
import sys
import logging
from typing import List, Dict, Any, Optional

# 设置日志级别为 WARNING，这样就不会显示 DEBUG 信息
logging.getLogger("alibabacloud_credentials").setLevel(logging.WARNING)

from alibabacloud_dingtalk.yida_2_0.client import Client as DingTalkYidaClient
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dingtalk.yida_2_0 import models as yida_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_util.client import Client as UtilClient
from get_token import token
from datetime import datetime

# 配置信息
CONFIG = {
    'APP_TYPE': 'APP_D7E6ZB94ZUL5Q1GUAOLD',
    'SYSTEM_TOKEN': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2',
    'USER_ID': 'hexuepeng',
    'LANGUAGE': 'zh_CN',
    'FORM_UUID': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P'
}

class YidaFormDataClient:
    def __init__(self):
        # 配置日志文件
        current_date = datetime.now().strftime('%Y%m%d')
        logging.basicConfig(
            filename=f'yida_form_log_{current_date}.txt',
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            encoding='utf-8'
        )
        
        self.access_token = token.get_token()
        self.client = self._create_client()

    def _create_client(self) -> DingTalkYidaClient:
        """
        初始化宜搭客户端
        
        Returns:
            DingTalkYidaClient: 宜搭客户端实例
        """
        config = open_api_models.Config(
            protocol='https',
            region_id='central'
        )
        return DingTalkYidaClient(config)

    def get_form_data(self, page_size: int = 100) -> List[Dict[str, Any]]:
        try:
            all_data = []
            current_page = 1
            
            while True:
                headers = yida_models.SearchFormDatasHeaders(
                    x_acs_dingtalk_access_token=self.access_token
                )
                
                request = yida_models.SearchFormDatasRequest(
                    app_type=CONFIG['APP_TYPE'],
                    system_token=CONFIG['SYSTEM_TOKEN'],
                    user_id=CONFIG['USER_ID'],
                    language=CONFIG['LANGUAGE'],
                    form_uuid=CONFIG['FORM_UUID'],
                    current_page=current_page,
                    page_size=page_size
                )
                
                # 记录请求参数
                logging.info(f"Request Parameters - Page {current_page}:")
                logging.info(f"Headers: {headers}")
                logging.info(f"Request: {request}")
                
                result = self.client.search_form_datas_with_options(
                    request, 
                    headers, 
                    util_models.RuntimeOptions()
                )
                
                # 记录响应结果
                logging.info(f"Response - Page {current_page}:")
                logging.info(f"Result: {result.body}")
                
                if not result or not result.body or not result.body.data:
                    break
                
                # 提取指定字段
                for item in result.body.data:
                    filtered_data = {
                        'formInstanceId': item.form_instance_id,
                        'formData': item.form_data
                    }
                    all_data.append(filtered_data)
                
                # 如果获取的数据少于页大小，说明已经是最后一页
                if len(result.body.data) < page_size:
                    break
                    
                current_page += 1
            
            return all_data
            
        except Exception as e:
            error_msg = f"获取表单数据失败: {str(e)}"
            if hasattr(e, 'code') and hasattr(e, 'message'):
                error_msg += f" (错误码: {e.code}, 错误信息: {e.message})"
            raise Exception(error_msg)

def main():
    try:
        client = YidaFormDataClient()
        form_data_list = client.get_form_data()
        
        if not form_data_list:
            print("未获取到表单数据")
            return
            
        for form_data in form_data_list:
            print(form_data)
            
    except Exception as e:
        print(f"程序执行出错: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()