2025-05-23 00:30:33,944 - INFO - 使用默认增量同步（当天更新数据）
2025-05-23 00:30:33,944 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 00:30:33,944 - INFO - 查询参数: ('2025-05-23',)
2025-05-23 00:30:34,007 - INFO - MySQL查询成功，增量数据（日期: 2025-05-23），共获取 0 条记录
2025-05-23 00:30:34,007 - ERROR - 未获取到MySQL数据
2025-05-23 00:31:34,022 - INFO - 开始同步昨天与今天的销售数据: 2025-05-22 至 2025-05-23
2025-05-23 00:31:34,022 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 00:31:34,022 - INFO - 查询参数: ('2025-05-22', '2025-05-23')
2025-05-23 00:31:34,069 - INFO - MySQL查询成功，时间段: 2025-05-22 至 2025-05-23，共获取 34 条记录
2025-05-23 00:31:34,084 - INFO - 获取到 1 个日期需要处理: ['2025-05-22']
2025-05-23 00:31:34,084 - INFO - 开始处理日期: 2025-05-22
2025-05-23 00:31:34,084 - INFO - Request Parameters - Page 1:
2025-05-23 00:31:34,084 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 00:31:34,084 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 00:31:42,194 - ERROR - 处理日期 2025-05-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B739E536-EDE1-71A9-8589-805D48A58649 Response: {'code': 'ServiceUnavailable', 'requestid': 'B739E536-EDE1-71A9-8589-805D48A58649', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B739E536-EDE1-71A9-8589-805D48A58649)
2025-05-23 00:31:42,194 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-23 00:31:42,194 - INFO - 同步完成
2025-05-23 01:30:34,109 - INFO - 使用默认增量同步（当天更新数据）
2025-05-23 01:30:34,109 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 01:30:34,109 - INFO - 查询参数: ('2025-05-23',)
2025-05-23 01:30:34,171 - INFO - MySQL查询成功，增量数据（日期: 2025-05-23），共获取 0 条记录
2025-05-23 01:30:34,171 - ERROR - 未获取到MySQL数据
2025-05-23 01:31:34,186 - INFO - 开始同步昨天与今天的销售数据: 2025-05-22 至 2025-05-23
2025-05-23 01:31:34,186 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 01:31:34,186 - INFO - 查询参数: ('2025-05-22', '2025-05-23')
2025-05-23 01:31:34,233 - INFO - MySQL查询成功，时间段: 2025-05-22 至 2025-05-23，共获取 34 条记录
2025-05-23 01:31:34,233 - INFO - 获取到 1 个日期需要处理: ['2025-05-22']
2025-05-23 01:31:34,233 - INFO - 开始处理日期: 2025-05-22
2025-05-23 01:31:34,249 - INFO - Request Parameters - Page 1:
2025-05-23 01:31:34,249 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 01:31:34,249 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 01:31:42,374 - ERROR - 处理日期 2025-05-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 01089D3E-E8D4-7C3B-A7FA-38FCFCF682EC Response: {'code': 'ServiceUnavailable', 'requestid': '01089D3E-E8D4-7C3B-A7FA-38FCFCF682EC', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 01089D3E-E8D4-7C3B-A7FA-38FCFCF682EC)
2025-05-23 01:31:42,374 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-23 01:31:42,374 - INFO - 同步完成
2025-05-23 02:30:33,726 - INFO - 使用默认增量同步（当天更新数据）
2025-05-23 02:30:33,726 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 02:30:33,726 - INFO - 查询参数: ('2025-05-23',)
2025-05-23 02:30:33,789 - INFO - MySQL查询成功，增量数据（日期: 2025-05-23），共获取 0 条记录
2025-05-23 02:30:33,789 - ERROR - 未获取到MySQL数据
2025-05-23 02:31:33,804 - INFO - 开始同步昨天与今天的销售数据: 2025-05-22 至 2025-05-23
2025-05-23 02:31:33,804 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 02:31:33,804 - INFO - 查询参数: ('2025-05-22', '2025-05-23')
2025-05-23 02:31:33,867 - INFO - MySQL查询成功，时间段: 2025-05-22 至 2025-05-23，共获取 34 条记录
2025-05-23 02:31:33,867 - INFO - 获取到 1 个日期需要处理: ['2025-05-22']
2025-05-23 02:31:33,867 - INFO - 开始处理日期: 2025-05-22
2025-05-23 02:31:33,867 - INFO - Request Parameters - Page 1:
2025-05-23 02:31:33,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 02:31:33,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 02:31:41,992 - ERROR - 处理日期 2025-05-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 182B42A8-EC8A-7CD9-B51A-AA91BAFA00C7 Response: {'code': 'ServiceUnavailable', 'requestid': '182B42A8-EC8A-7CD9-B51A-AA91BAFA00C7', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 182B42A8-EC8A-7CD9-B51A-AA91BAFA00C7)
2025-05-23 02:31:41,992 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-23 02:31:41,992 - INFO - 同步完成
2025-05-23 03:30:33,782 - INFO - 使用默认增量同步（当天更新数据）
2025-05-23 03:30:33,782 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 03:30:33,782 - INFO - 查询参数: ('2025-05-23',)
2025-05-23 03:30:33,844 - INFO - MySQL查询成功，增量数据（日期: 2025-05-23），共获取 4 条记录
2025-05-23 03:30:33,844 - INFO - 获取到 1 个日期需要处理: ['2025-05-22']
2025-05-23 03:30:33,844 - INFO - 开始处理日期: 2025-05-22
2025-05-23 03:30:33,844 - INFO - Request Parameters - Page 1:
2025-05-23 03:30:33,844 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:30:33,844 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:30:41,953 - ERROR - 处理日期 2025-05-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F21AF503-2482-702E-BDB3-83D79A56E212 Response: {'code': 'ServiceUnavailable', 'requestid': 'F21AF503-2482-702E-BDB3-83D79A56E212', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F21AF503-2482-702E-BDB3-83D79A56E212)
2025-05-23 03:30:41,953 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-23 03:31:41,969 - INFO - 开始同步昨天与今天的销售数据: 2025-05-22 至 2025-05-23
2025-05-23 03:31:41,969 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 03:31:41,969 - INFO - 查询参数: ('2025-05-22', '2025-05-23')
2025-05-23 03:31:42,031 - INFO - MySQL查询成功，时间段: 2025-05-22 至 2025-05-23，共获取 55 条记录
2025-05-23 03:31:42,031 - INFO - 获取到 1 个日期需要处理: ['2025-05-22']
2025-05-23 03:31:42,031 - INFO - 开始处理日期: 2025-05-22
2025-05-23 03:31:42,031 - INFO - Request Parameters - Page 1:
2025-05-23 03:31:42,031 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 03:31:42,031 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 03:31:42,750 - INFO - Response - Page 1:
2025-05-23 03:31:42,750 - INFO - 第 1 页获取到 34 条记录
2025-05-23 03:31:42,953 - INFO - 查询完成，共获取到 34 条记录
2025-05-23 03:31:42,953 - INFO - 获取到 34 条表单数据
2025-05-23 03:31:42,953 - INFO - 当前日期 2025-05-22 有 55 条MySQL数据需要处理
2025-05-23 03:31:42,953 - INFO - 开始批量插入 21 条新记录
2025-05-23 03:31:43,125 - INFO - 批量插入响应状态码: 200
2025-05-23 03:31:43,125 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 22 May 2025 19:31:43 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1020', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F6AA5248-181D-796D-A034-E5C40BFCA374', 'x-acs-trace-id': 'ef6efbac23dfd1d38cb3cb538c16cf26', 'etag': '102c44aHhlHi3w2KMXt3Lcg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-23 03:31:43,125 - INFO - 批量插入响应体: {'result': ['FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAM4F', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAM5F', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAM6F', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAM7F', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAM8F', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAM9F', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAMAF', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAMBF', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAMCF', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAMDF', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAMEF', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAMFF', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAMGF', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAMHF', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAMIF', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAMJF', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAMKF', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAMLF', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAMMF', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAMNF', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAMOF']}
2025-05-23 03:31:43,125 - INFO - 批量插入表单数据成功，批次 1，共 21 条记录
2025-05-23 03:31:43,125 - INFO - 成功插入的数据ID: ['FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAM4F', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAM5F', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAM6F', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAM7F', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAM8F', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAM9F', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAMAF', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAMBF', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAMCF', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAMDF', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAMEF', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAMFF', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAMGF', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAMHF', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAMIF', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAMJF', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAMKF', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAMLF', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAMMF', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAMNF', 'FINST-XL866HB1BALVYEBLAVOLS6XAW6CY2KYSPRZAMOF']
2025-05-23 03:31:48,140 - INFO - 批量插入完成，共 21 条记录
2025-05-23 03:31:48,140 - INFO - 日期 2025-05-22 处理完成 - 更新: 0 条，插入: 21 条，错误: 0 条
2025-05-23 03:31:48,140 - INFO - 数据同步完成！更新: 0 条，插入: 21 条，错误: 0 条
2025-05-23 03:31:48,140 - INFO - 同步完成
2025-05-23 04:30:33,914 - INFO - 使用默认增量同步（当天更新数据）
2025-05-23 04:30:33,914 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 04:30:33,914 - INFO - 查询参数: ('2025-05-23',)
2025-05-23 04:30:33,977 - INFO - MySQL查询成功，增量数据（日期: 2025-05-23），共获取 4 条记录
2025-05-23 04:30:33,977 - INFO - 获取到 1 个日期需要处理: ['2025-05-22']
2025-05-23 04:30:33,977 - INFO - 开始处理日期: 2025-05-22
2025-05-23 04:30:33,977 - INFO - Request Parameters - Page 1:
2025-05-23 04:30:33,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 04:30:33,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 04:30:42,118 - ERROR - 处理日期 2025-05-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: BAB4E285-E107-75E8-A092-E839729187E8 Response: {'code': 'ServiceUnavailable', 'requestid': 'BAB4E285-E107-75E8-A092-E839729187E8', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: BAB4E285-E107-75E8-A092-E839729187E8)
2025-05-23 04:30:42,118 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-23 04:31:42,133 - INFO - 开始同步昨天与今天的销售数据: 2025-05-22 至 2025-05-23
2025-05-23 04:31:42,133 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 04:31:42,133 - INFO - 查询参数: ('2025-05-22', '2025-05-23')
2025-05-23 04:31:42,195 - INFO - MySQL查询成功，时间段: 2025-05-22 至 2025-05-23，共获取 55 条记录
2025-05-23 04:31:42,195 - INFO - 获取到 1 个日期需要处理: ['2025-05-22']
2025-05-23 04:31:42,195 - INFO - 开始处理日期: 2025-05-22
2025-05-23 04:31:42,195 - INFO - Request Parameters - Page 1:
2025-05-23 04:31:42,195 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 04:31:42,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 04:31:42,945 - INFO - Response - Page 1:
2025-05-23 04:31:42,945 - INFO - 第 1 页获取到 55 条记录
2025-05-23 04:31:43,148 - INFO - 查询完成，共获取到 55 条记录
2025-05-23 04:31:43,148 - INFO - 获取到 55 条表单数据
2025-05-23 04:31:43,148 - INFO - 当前日期 2025-05-22 有 55 条MySQL数据需要处理
2025-05-23 04:31:43,148 - INFO - 日期 2025-05-22 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 04:31:43,148 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 04:31:43,148 - INFO - 同步完成
2025-05-23 05:30:34,219 - INFO - 使用默认增量同步（当天更新数据）
2025-05-23 05:30:34,235 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 05:30:34,235 - INFO - 查询参数: ('2025-05-23',)
2025-05-23 05:30:34,297 - INFO - MySQL查询成功，增量数据（日期: 2025-05-23），共获取 4 条记录
2025-05-23 05:30:34,297 - INFO - 获取到 1 个日期需要处理: ['2025-05-22']
2025-05-23 05:30:34,297 - INFO - 开始处理日期: 2025-05-22
2025-05-23 05:30:34,297 - INFO - Request Parameters - Page 1:
2025-05-23 05:30:34,297 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 05:30:34,297 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 05:30:42,422 - ERROR - 处理日期 2025-05-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C61CEF3F-288C-79F0-A4D1-3E0B70EC0341 Response: {'code': 'ServiceUnavailable', 'requestid': 'C61CEF3F-288C-79F0-A4D1-3E0B70EC0341', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C61CEF3F-288C-79F0-A4D1-3E0B70EC0341)
2025-05-23 05:30:42,422 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-23 05:31:42,437 - INFO - 开始同步昨天与今天的销售数据: 2025-05-22 至 2025-05-23
2025-05-23 05:31:42,437 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 05:31:42,437 - INFO - 查询参数: ('2025-05-22', '2025-05-23')
2025-05-23 05:31:42,500 - INFO - MySQL查询成功，时间段: 2025-05-22 至 2025-05-23，共获取 55 条记录
2025-05-23 05:31:42,500 - INFO - 获取到 1 个日期需要处理: ['2025-05-22']
2025-05-23 05:31:42,500 - INFO - 开始处理日期: 2025-05-22
2025-05-23 05:31:42,500 - INFO - Request Parameters - Page 1:
2025-05-23 05:31:42,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 05:31:42,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 05:31:43,266 - INFO - Response - Page 1:
2025-05-23 05:31:43,266 - INFO - 第 1 页获取到 55 条记录
2025-05-23 05:31:43,469 - INFO - 查询完成，共获取到 55 条记录
2025-05-23 05:31:43,469 - INFO - 获取到 55 条表单数据
2025-05-23 05:31:43,469 - INFO - 当前日期 2025-05-22 有 55 条MySQL数据需要处理
2025-05-23 05:31:43,469 - INFO - 日期 2025-05-22 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 05:31:43,469 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 05:31:43,469 - INFO - 同步完成
2025-05-23 06:30:33,915 - INFO - 使用默认增量同步（当天更新数据）
2025-05-23 06:30:33,915 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 06:30:33,915 - INFO - 查询参数: ('2025-05-23',)
2025-05-23 06:30:33,977 - INFO - MySQL查询成功，增量数据（日期: 2025-05-23），共获取 4 条记录
2025-05-23 06:30:33,977 - INFO - 获取到 1 个日期需要处理: ['2025-05-22']
2025-05-23 06:30:33,977 - INFO - 开始处理日期: 2025-05-22
2025-05-23 06:30:33,977 - INFO - Request Parameters - Page 1:
2025-05-23 06:30:33,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:30:33,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:30:42,102 - ERROR - 处理日期 2025-05-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: BEF7907E-34E6-7374-96EA-535A45AEA855 Response: {'code': 'ServiceUnavailable', 'requestid': 'BEF7907E-34E6-7374-96EA-535A45AEA855', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: BEF7907E-34E6-7374-96EA-535A45AEA855)
2025-05-23 06:30:42,102 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-23 06:31:42,117 - INFO - 开始同步昨天与今天的销售数据: 2025-05-22 至 2025-05-23
2025-05-23 06:31:42,117 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 06:31:42,117 - INFO - 查询参数: ('2025-05-22', '2025-05-23')
2025-05-23 06:31:42,180 - INFO - MySQL查询成功，时间段: 2025-05-22 至 2025-05-23，共获取 55 条记录
2025-05-23 06:31:42,180 - INFO - 获取到 1 个日期需要处理: ['2025-05-22']
2025-05-23 06:31:42,180 - INFO - 开始处理日期: 2025-05-22
2025-05-23 06:31:42,180 - INFO - Request Parameters - Page 1:
2025-05-23 06:31:42,180 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 06:31:42,180 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 06:31:42,930 - INFO - Response - Page 1:
2025-05-23 06:31:42,930 - INFO - 第 1 页获取到 55 条记录
2025-05-23 06:31:43,133 - INFO - 查询完成，共获取到 55 条记录
2025-05-23 06:31:43,133 - INFO - 获取到 55 条表单数据
2025-05-23 06:31:43,133 - INFO - 当前日期 2025-05-22 有 55 条MySQL数据需要处理
2025-05-23 06:31:43,133 - INFO - 日期 2025-05-22 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 06:31:43,133 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 06:31:43,133 - INFO - 同步完成
2025-05-23 07:30:33,798 - INFO - 使用默认增量同步（当天更新数据）
2025-05-23 07:30:33,798 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 07:30:33,798 - INFO - 查询参数: ('2025-05-23',)
2025-05-23 07:30:33,860 - INFO - MySQL查询成功，增量数据（日期: 2025-05-23），共获取 4 条记录
2025-05-23 07:30:33,860 - INFO - 获取到 1 个日期需要处理: ['2025-05-22']
2025-05-23 07:30:33,860 - INFO - 开始处理日期: 2025-05-22
2025-05-23 07:30:33,876 - INFO - Request Parameters - Page 1:
2025-05-23 07:30:33,876 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 07:30:33,876 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 07:30:42,001 - ERROR - 处理日期 2025-05-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 513A7D2E-B151-774A-B7BD-F2519CCE3798 Response: {'code': 'ServiceUnavailable', 'requestid': '513A7D2E-B151-774A-B7BD-F2519CCE3798', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 513A7D2E-B151-774A-B7BD-F2519CCE3798)
2025-05-23 07:30:42,001 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-23 07:31:42,016 - INFO - 开始同步昨天与今天的销售数据: 2025-05-22 至 2025-05-23
2025-05-23 07:31:42,016 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 07:31:42,016 - INFO - 查询参数: ('2025-05-22', '2025-05-23')
2025-05-23 07:31:42,078 - INFO - MySQL查询成功，时间段: 2025-05-22 至 2025-05-23，共获取 55 条记录
2025-05-23 07:31:42,078 - INFO - 获取到 1 个日期需要处理: ['2025-05-22']
2025-05-23 07:31:42,078 - INFO - 开始处理日期: 2025-05-22
2025-05-23 07:31:42,078 - INFO - Request Parameters - Page 1:
2025-05-23 07:31:42,078 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 07:31:42,078 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 07:31:50,188 - ERROR - 处理日期 2025-05-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 36FFC2CD-50CF-79F1-B982-8863017CFEF0 Response: {'code': 'ServiceUnavailable', 'requestid': '36FFC2CD-50CF-79F1-B982-8863017CFEF0', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 36FFC2CD-50CF-79F1-B982-8863017CFEF0)
2025-05-23 07:31:50,188 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-23 07:31:50,188 - INFO - 同步完成
2025-05-23 08:30:33,774 - INFO - 使用默认增量同步（当天更新数据）
2025-05-23 08:30:33,774 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 08:30:33,774 - INFO - 查询参数: ('2025-05-23',)
2025-05-23 08:30:33,837 - INFO - MySQL查询成功，增量数据（日期: 2025-05-23），共获取 15 条记录
2025-05-23 08:30:33,837 - INFO - 获取到 2 个日期需要处理: ['2025-05-21', '2025-05-22']
2025-05-23 08:30:33,837 - INFO - 开始处理日期: 2025-05-21
2025-05-23 08:30:33,853 - INFO - Request Parameters - Page 1:
2025-05-23 08:30:33,853 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:30:33,853 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:30:41,962 - ERROR - 处理日期 2025-05-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: AD23D792-3F70-7CAC-9935-87ABB5741EAE Response: {'code': 'ServiceUnavailable', 'requestid': 'AD23D792-3F70-7CAC-9935-87ABB5741EAE', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: AD23D792-3F70-7CAC-9935-87ABB5741EAE)
2025-05-23 08:30:41,962 - INFO - 开始处理日期: 2025-05-22
2025-05-23 08:30:41,962 - INFO - Request Parameters - Page 1:
2025-05-23 08:30:41,962 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:30:41,962 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:30:50,087 - ERROR - 处理日期 2025-05-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A0B30A2C-3459-7F8D-9F0A-BE4FB7984C9B Response: {'code': 'ServiceUnavailable', 'requestid': 'A0B30A2C-3459-7F8D-9F0A-BE4FB7984C9B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A0B30A2C-3459-7F8D-9F0A-BE4FB7984C9B)
2025-05-23 08:30:50,087 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-23 08:31:50,102 - INFO - 开始同步昨天与今天的销售数据: 2025-05-22 至 2025-05-23
2025-05-23 08:31:50,102 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 08:31:50,102 - INFO - 查询参数: ('2025-05-22', '2025-05-23')
2025-05-23 08:31:50,165 - INFO - MySQL查询成功，时间段: 2025-05-22 至 2025-05-23，共获取 62 条记录
2025-05-23 08:31:50,165 - INFO - 获取到 1 个日期需要处理: ['2025-05-22']
2025-05-23 08:31:50,165 - INFO - 开始处理日期: 2025-05-22
2025-05-23 08:31:50,165 - INFO - Request Parameters - Page 1:
2025-05-23 08:31:50,165 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 08:31:50,165 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 08:31:50,915 - INFO - Response - Page 1:
2025-05-23 08:31:50,915 - INFO - 第 1 页获取到 55 条记录
2025-05-23 08:31:51,118 - INFO - 查询完成，共获取到 55 条记录
2025-05-23 08:31:51,118 - INFO - 获取到 55 条表单数据
2025-05-23 08:31:51,118 - INFO - 当前日期 2025-05-22 有 62 条MySQL数据需要处理
2025-05-23 08:31:51,118 - INFO - 开始批量插入 7 条新记录
2025-05-23 08:31:51,258 - INFO - 批量插入响应状态码: 200
2025-05-23 08:31:51,258 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 23 May 2025 00:31:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '348', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '1D8EDD03-8566-7997-B9CE-53599C74DFE3', 'x-acs-trace-id': 'd85dddb40e208e896b2a0fe2de7a4230', 'etag': '3sE8XapuM7Sgp5eaIxKCHsw8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-23 08:31:51,258 - INFO - 批量插入响应体: {'result': ['FINST-2PF66TC1HALVAJ0ABS2DV8P98OGP2X7SF20BMAL', 'FINST-2PF66TC1HALVAJ0ABS2DV8P98OGP2X7SF20BMBL', 'FINST-2PF66TC1HALVAJ0ABS2DV8P98OGP2X7SF20BMCL', 'FINST-2PF66TC1HALVAJ0ABS2DV8P98OGP2X7SF20BMDL', 'FINST-2PF66TC1HALVAJ0ABS2DV8P98OGP2X7SF20BMEL', 'FINST-2PF66TC1HALVAJ0ABS2DV8P98OGP2X7SF20BMFL', 'FINST-2PF66TC1HALVAJ0ABS2DV8P98OGP2X7SF20BMGL']}
2025-05-23 08:31:51,258 - INFO - 批量插入表单数据成功，批次 1，共 7 条记录
2025-05-23 08:31:51,258 - INFO - 成功插入的数据ID: ['FINST-2PF66TC1HALVAJ0ABS2DV8P98OGP2X7SF20BMAL', 'FINST-2PF66TC1HALVAJ0ABS2DV8P98OGP2X7SF20BMBL', 'FINST-2PF66TC1HALVAJ0ABS2DV8P98OGP2X7SF20BMCL', 'FINST-2PF66TC1HALVAJ0ABS2DV8P98OGP2X7SF20BMDL', 'FINST-2PF66TC1HALVAJ0ABS2DV8P98OGP2X7SF20BMEL', 'FINST-2PF66TC1HALVAJ0ABS2DV8P98OGP2X7SF20BMFL', 'FINST-2PF66TC1HALVAJ0ABS2DV8P98OGP2X7SF20BMGL']
2025-05-23 08:31:56,274 - INFO - 批量插入完成，共 7 条记录
2025-05-23 08:31:56,274 - INFO - 日期 2025-05-22 处理完成 - 更新: 0 条，插入: 7 条，错误: 0 条
2025-05-23 08:31:56,274 - INFO - 数据同步完成！更新: 0 条，插入: 7 条，错误: 0 条
2025-05-23 08:31:56,274 - INFO - 同步完成
2025-05-23 09:30:34,205 - INFO - 使用默认增量同步（当天更新数据）
2025-05-23 09:30:34,205 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 09:30:34,205 - INFO - 查询参数: ('2025-05-23',)
2025-05-23 09:30:34,267 - INFO - MySQL查询成功，增量数据（日期: 2025-05-23），共获取 88 条记录
2025-05-23 09:30:34,267 - INFO - 获取到 2 个日期需要处理: ['2025-05-21', '2025-05-22']
2025-05-23 09:30:34,267 - INFO - 开始处理日期: 2025-05-21
2025-05-23 09:30:34,283 - INFO - Request Parameters - Page 1:
2025-05-23 09:30:34,283 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:30:34,283 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:30:42,392 - ERROR - 处理日期 2025-05-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 285BF495-B009-7664-B5C8-A140975E5F37 Response: {'code': 'ServiceUnavailable', 'requestid': '285BF495-B009-7664-B5C8-A140975E5F37', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 285BF495-B009-7664-B5C8-A140975E5F37)
2025-05-23 09:30:42,392 - INFO - 开始处理日期: 2025-05-22
2025-05-23 09:30:42,392 - INFO - Request Parameters - Page 1:
2025-05-23 09:30:42,392 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:30:42,392 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:30:50,517 - ERROR - 处理日期 2025-05-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 65A3EC71-439C-7D6B-B61C-17897AA0C728 Response: {'code': 'ServiceUnavailable', 'requestid': '65A3EC71-439C-7D6B-B61C-17897AA0C728', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 65A3EC71-439C-7D6B-B61C-17897AA0C728)
2025-05-23 09:30:50,517 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-23 09:31:50,532 - INFO - 开始同步昨天与今天的销售数据: 2025-05-22 至 2025-05-23
2025-05-23 09:31:50,532 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 09:31:50,532 - INFO - 查询参数: ('2025-05-22', '2025-05-23')
2025-05-23 09:31:50,595 - INFO - MySQL查询成功，时间段: 2025-05-22 至 2025-05-23，共获取 400 条记录
2025-05-23 09:31:50,595 - INFO - 获取到 1 个日期需要处理: ['2025-05-22']
2025-05-23 09:31:50,610 - INFO - 开始处理日期: 2025-05-22
2025-05-23 09:31:50,610 - INFO - Request Parameters - Page 1:
2025-05-23 09:31:50,610 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 09:31:50,610 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 09:31:58,735 - ERROR - 处理日期 2025-05-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 43D1D03F-CDA3-7086-ACE7-0B15141FF1AB Response: {'code': 'ServiceUnavailable', 'requestid': '43D1D03F-CDA3-7086-ACE7-0B15141FF1AB', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 43D1D03F-CDA3-7086-ACE7-0B15141FF1AB)
2025-05-23 09:31:58,735 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-23 09:31:58,735 - INFO - 同步完成
2025-05-23 10:30:33,932 - INFO - 使用默认增量同步（当天更新数据）
2025-05-23 10:30:33,932 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 10:30:33,932 - INFO - 查询参数: ('2025-05-23',)
2025-05-23 10:30:33,994 - INFO - MySQL查询成功，增量数据（日期: 2025-05-23），共获取 165 条记录
2025-05-23 10:30:33,994 - INFO - 获取到 2 个日期需要处理: ['2025-05-21', '2025-05-22']
2025-05-23 10:30:33,994 - INFO - 开始处理日期: 2025-05-21
2025-05-23 10:30:33,994 - INFO - Request Parameters - Page 1:
2025-05-23 10:30:33,994 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 10:30:33,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 10:30:42,119 - ERROR - 处理日期 2025-05-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2FC9CA0F-C956-78CB-8B53-8F0A3A4F195A Response: {'code': 'ServiceUnavailable', 'requestid': '2FC9CA0F-C956-78CB-8B53-8F0A3A4F195A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2FC9CA0F-C956-78CB-8B53-8F0A3A4F195A)
2025-05-23 10:30:42,119 - INFO - 开始处理日期: 2025-05-22
2025-05-23 10:30:42,119 - INFO - Request Parameters - Page 1:
2025-05-23 10:30:42,119 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 10:30:42,119 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 10:30:42,275 - ERROR - 处理日期 2025-05-22 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 731A04B7-53D8-76A1-B789-A7B098BA7818 Response: {'requestid': '731A04B7-53D8-76A1-B789-A7B098BA7818', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 731A04B7-53D8-76A1-B789-A7B098BA7818)
2025-05-23 10:30:42,275 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-23 10:31:42,290 - INFO - 开始同步昨天与今天的销售数据: 2025-05-22 至 2025-05-23
2025-05-23 10:31:42,290 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 10:31:42,290 - INFO - 查询参数: ('2025-05-22', '2025-05-23')
2025-05-23 10:31:42,369 - INFO - MySQL查询成功，时间段: 2025-05-22 至 2025-05-23，共获取 493 条记录
2025-05-23 10:31:42,369 - INFO - 获取到 1 个日期需要处理: ['2025-05-22']
2025-05-23 10:31:42,369 - INFO - 开始处理日期: 2025-05-22
2025-05-23 10:31:42,369 - INFO - Request Parameters - Page 1:
2025-05-23 10:31:42,369 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 10:31:42,369 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 10:31:50,509 - ERROR - 处理日期 2025-05-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2DBF1B46-1DAD-7390-9B85-0D7B3F3367B3 Response: {'code': 'ServiceUnavailable', 'requestid': '2DBF1B46-1DAD-7390-9B85-0D7B3F3367B3', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2DBF1B46-1DAD-7390-9B85-0D7B3F3367B3)
2025-05-23 10:31:50,509 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-23 10:31:50,509 - INFO - 同步完成
2025-05-23 11:30:34,236 - INFO - 使用默认增量同步（当天更新数据）
2025-05-23 11:30:34,236 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 11:30:34,236 - INFO - 查询参数: ('2025-05-23',)
2025-05-23 11:30:34,315 - INFO - MySQL查询成功，增量数据（日期: 2025-05-23），共获取 212 条记录
2025-05-23 11:30:34,315 - INFO - 获取到 2 个日期需要处理: ['2025-05-21', '2025-05-22']
2025-05-23 11:30:34,315 - INFO - 开始处理日期: 2025-05-21
2025-05-23 11:30:34,315 - INFO - Request Parameters - Page 1:
2025-05-23 11:30:34,315 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 11:30:34,315 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 11:30:42,424 - ERROR - 处理日期 2025-05-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: ED514C07-09FA-77F0-B7FF-6CD3FE93A2CB Response: {'code': 'ServiceUnavailable', 'requestid': 'ED514C07-09FA-77F0-B7FF-6CD3FE93A2CB', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: ED514C07-09FA-77F0-B7FF-6CD3FE93A2CB)
2025-05-23 11:30:42,424 - INFO - 开始处理日期: 2025-05-22
2025-05-23 11:30:42,424 - INFO - Request Parameters - Page 1:
2025-05-23 11:30:42,424 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 11:30:42,424 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 11:30:42,565 - ERROR - 处理日期 2025-05-22 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 38B35589-E4A0-7A6C-829B-C1169990E5FE Response: {'requestid': '38B35589-E4A0-7A6C-829B-C1169990E5FE', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 38B35589-E4A0-7A6C-829B-C1169990E5FE)
2025-05-23 11:30:42,565 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-23 11:31:42,580 - INFO - 开始同步昨天与今天的销售数据: 2025-05-22 至 2025-05-23
2025-05-23 11:31:42,580 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 11:31:42,580 - INFO - 查询参数: ('2025-05-22', '2025-05-23')
2025-05-23 11:31:42,658 - INFO - MySQL查询成功，时间段: 2025-05-22 至 2025-05-23，共获取 610 条记录
2025-05-23 11:31:42,658 - INFO - 获取到 1 个日期需要处理: ['2025-05-22']
2025-05-23 11:31:42,658 - INFO - 开始处理日期: 2025-05-22
2025-05-23 11:31:42,658 - INFO - Request Parameters - Page 1:
2025-05-23 11:31:42,658 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 11:31:42,658 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 11:31:50,783 - ERROR - 处理日期 2025-05-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 97A4949C-9FB3-755E-A43B-B9CF9E7ED73A Response: {'code': 'ServiceUnavailable', 'requestid': '97A4949C-9FB3-755E-A43B-B9CF9E7ED73A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 97A4949C-9FB3-755E-A43B-B9CF9E7ED73A)
2025-05-23 11:31:50,783 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-23 11:31:50,783 - INFO - 同步完成
2025-05-23 12:30:34,260 - INFO - 使用默认增量同步（当天更新数据）
2025-05-23 12:30:34,260 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 12:30:34,260 - INFO - 查询参数: ('2025-05-23',)
2025-05-23 12:30:34,323 - INFO - MySQL查询成功，增量数据（日期: 2025-05-23），共获取 215 条记录
2025-05-23 12:30:34,323 - INFO - 获取到 2 个日期需要处理: ['2025-05-21', '2025-05-22']
2025-05-23 12:30:34,338 - INFO - 开始处理日期: 2025-05-21
2025-05-23 12:30:34,338 - INFO - Request Parameters - Page 1:
2025-05-23 12:30:34,338 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:30:34,338 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:30:42,448 - ERROR - 处理日期 2025-05-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 1F213359-42F1-7BC8-944B-6820F1313F03 Response: {'code': 'ServiceUnavailable', 'requestid': '1F213359-42F1-7BC8-944B-6820F1313F03', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 1F213359-42F1-7BC8-944B-6820F1313F03)
2025-05-23 12:30:42,448 - INFO - 开始处理日期: 2025-05-22
2025-05-23 12:30:42,448 - INFO - Request Parameters - Page 1:
2025-05-23 12:30:42,448 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:30:42,448 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:30:42,604 - ERROR - 处理日期 2025-05-22 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 850D9DF6-7D8B-74D0-81A3-E705666BB19B Response: {'requestid': '850D9DF6-7D8B-74D0-81A3-E705666BB19B', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 850D9DF6-7D8B-74D0-81A3-E705666BB19B)
2025-05-23 12:30:42,604 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-23 12:31:42,619 - INFO - 开始同步昨天与今天的销售数据: 2025-05-22 至 2025-05-23
2025-05-23 12:31:42,619 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 12:31:42,619 - INFO - 查询参数: ('2025-05-22', '2025-05-23')
2025-05-23 12:31:42,697 - INFO - MySQL查询成功，时间段: 2025-05-22 至 2025-05-23，共获取 613 条记录
2025-05-23 12:31:42,697 - INFO - 获取到 1 个日期需要处理: ['2025-05-22']
2025-05-23 12:31:42,697 - INFO - 开始处理日期: 2025-05-22
2025-05-23 12:31:42,697 - INFO - Request Parameters - Page 1:
2025-05-23 12:31:42,697 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 12:31:42,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 12:31:43,541 - INFO - Response - Page 1:
2025-05-23 12:31:43,541 - INFO - 第 1 页获取到 62 条记录
2025-05-23 12:31:43,744 - INFO - 查询完成，共获取到 62 条记录
2025-05-23 12:31:43,744 - INFO - 获取到 62 条表单数据
2025-05-23 12:31:43,744 - INFO - 当前日期 2025-05-22 有 613 条MySQL数据需要处理
2025-05-23 12:31:43,744 - INFO - 开始更新记录 - 表单实例ID: FINST-GX9663D1T3NV2LTC7B60880JOD3I3NR632ZAMG
2025-05-23 12:31:44,182 - INFO - 更新表单数据成功: FINST-GX9663D1T3NV2LTC7B60880JOD3I3NR632ZAMG
2025-05-23 12:31:44,182 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33568.22, 'new_value': 674.92}, {'field': 'total_amount', 'old_value': 33568.22, 'new_value': 674.92}, {'field': 'order_count', 'old_value': 14, 'new_value': 4}]
2025-05-23 12:31:44,197 - INFO - 开始批量插入 551 条新记录
2025-05-23 12:31:44,494 - INFO - 批量插入响应状态码: 200
2025-05-23 12:31:44,494 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 23 May 2025 04:31:44 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '9C432083-D610-7B08-BB5C-5B51ACA865DD', 'x-acs-trace-id': '09e35fefdd8a8efc48a11b0d15b719c7', 'etag': '4K+0vNfqhBBt7n3prK40PkA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-23 12:31:44,494 - INFO - 批量插入响应体: {'result': ['FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM4D', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM5D', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM6D', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM7D', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM8D', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM9D', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMAD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMBD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMCD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMDD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMED', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMFD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMGD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMHD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMID', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMJD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMKD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMLD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMMD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMND', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMOD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMPD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMQD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMRD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMSD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMTD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMUD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMVD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMWD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMXD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMYD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMZD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM0E', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM1E', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM2E', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM3E', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM4E', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM5E', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM6E', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM7E', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM8E', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM9E', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMAE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMBE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMCE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMDE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMEE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMFE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMGE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMHE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMIE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMJE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMKE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMLE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMME', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMNE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMOE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMPE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMQE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMRE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMSE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMTE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMUE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMVE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMWE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMXE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMYE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMZE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM0F', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM1F', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM2F', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM3F', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM4F', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM5F', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM6F', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM7F', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM8F', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM9F', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMAF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMBF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMCF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMDF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMEF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMFF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMGF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMHF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMIF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMJF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMKF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMLF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMMF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMNF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMOF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMPF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMQF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMRF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMSF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMTF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMUF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMVF']}
2025-05-23 12:31:44,494 - INFO - 批量插入表单数据成功，批次 1，共 100 条记录
2025-05-23 12:31:44,494 - INFO - 成功插入的数据ID: ['FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM4D', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM5D', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM6D', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM7D', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM8D', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM9D', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMAD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMBD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMCD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMDD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMED', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMFD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMGD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMHD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMID', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMJD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMKD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMLD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMMD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMND', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMOD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMPD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMQD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMRD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMSD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMTD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMUD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMVD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMWD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMXD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMYD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMZD', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM0E', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM1E', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM2E', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM3E', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM4E', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM5E', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM6E', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM7E', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM8E', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM9E', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMAE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMBE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMCE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMDE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMEE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMFE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMGE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMHE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMIE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMJE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMKE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMLE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMME', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMNE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMOE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMPE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMQE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMRE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMSE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMTE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMUE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMVE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMWE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMXE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMYE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMZE', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM0F', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM1F', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM2F', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM3F', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM4F', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM5F', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM6F', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM7F', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM8F', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM9F', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMAF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMBF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMCF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMDF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMEF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMFF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMGF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMHF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMIF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMJF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMKF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMLF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMMF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMNF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMOF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMPF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMQF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMRF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMSF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMTF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMUF', 'FINST-OIF66BA16FNVGO7170L6096T0Z0I2L6A0B0BMVF']
2025-05-23 12:31:49,775 - INFO - 批量插入响应状态码: 200
2025-05-23 12:31:49,775 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 23 May 2025 04:31:50 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4CA1FAB8-ECA4-7401-BF74-C86C6FC53FE4', 'x-acs-trace-id': 'c7b6efff5ba27eafd0944549e53461f8', 'etag': '4rxublc9o9q3RK2ZnpC4pfQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-23 12:31:49,775 - INFO - 批量插入响应体: {'result': ['FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BME3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMF3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMG3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMH3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMI3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMJ3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMK3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BML3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMM3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMN3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMO3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMP3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMQ3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMR3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMS3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMT3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMU3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMV3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMW3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMX3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMY3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMZ3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM04', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM14', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM24', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM34', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM44', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM54', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM64', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM74', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM84', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM94', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMA4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMB4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMC4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMD4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BME4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMF4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMG4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMH4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMI4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMJ4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMK4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BML4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMM4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMN4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMO4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMP4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMQ4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMR4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMS4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMT4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMU4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMV4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMW4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMX4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMY4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMZ4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM05', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM15', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM25', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM35', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM45', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM55', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM65', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM75', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM85', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM95', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMA5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMB5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMC5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMD5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BME5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMF5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMG5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMH5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMI5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMJ5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMK5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BML5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMM5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMN5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMO5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMP5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMQ5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMR5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMS5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMT5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMU5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMV5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMW5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMX5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMY5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMZ5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM06', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM16', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM26', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM36', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM46', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM56']}
2025-05-23 12:31:49,775 - INFO - 批量插入表单数据成功，批次 2，共 100 条记录
2025-05-23 12:31:49,775 - INFO - 成功插入的数据ID: ['FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BME3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMF3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMG3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMH3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMI3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMJ3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMK3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BML3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMM3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMN3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMO3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMP3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMQ3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMR3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMS3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMT3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMU3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMV3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMW3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMX3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMY3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMZ3', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM04', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM14', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM24', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM34', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM44', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM54', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM64', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM74', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM84', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM94', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMA4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMB4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMC4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMD4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BME4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMF4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMG4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMH4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMI4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMJ4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMK4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BML4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMM4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMN4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMO4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMP4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMQ4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMR4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMS4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMT4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMU4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMV4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMW4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMX4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMY4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMZ4', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM05', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM15', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM25', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM35', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM45', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM55', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM65', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM75', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM85', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM95', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMA5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMB5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMC5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMD5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BME5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMF5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMG5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMH5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMI5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMJ5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMK5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BML5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMM5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMN5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMO5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMP5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMQ5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMR5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMS5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMT5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMU5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMV5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMW5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMX5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMY5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BMZ5', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM06', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM16', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM26', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM36', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM46', 'FINST-EZD66RB1U8MVHVX47ADNH7I8L4OC279E0B0BM56']
2025-05-23 12:31:55,041 - INFO - 批量插入响应状态码: 200
2025-05-23 12:31:55,041 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 23 May 2025 04:31:55 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4791', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'A637377D-6E85-7C9A-8322-738F4CB700C9', 'x-acs-trace-id': '0800c6e27c540ce1a539e07ae45caf65', 'etag': '4Dx/KZJTi31rYG8A4G3YReg1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-23 12:31:55,041 - INFO - 批量插入响应体: {'result': ['FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMF', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMG', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMH', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMI', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMJ', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMK', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BML', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMM', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMN', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMO', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMP', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMQ', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMR', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMS', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMT', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMU', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMV', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMW', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMX', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMY', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMZ', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM01', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM11', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM21', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM31', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM41', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM51', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM61', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM71', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM81', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM91', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMA1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMB1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMC1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMD1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BME1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMF1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMG1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMH1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMI1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMJ1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMK1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BML1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMM1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMN1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMO1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMP1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMQ1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMR1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMS1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMT1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMU1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMV1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMW1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMX1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMY1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMZ1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM02', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM12', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM22', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM32', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM42', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM52', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM62', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM72', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM82', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM92', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMA2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMB2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMC2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMD2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BME2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMF2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMG2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMH2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMI2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMJ2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMK2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BML2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMM2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMN2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMO2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMP2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMQ2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMR2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMS2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMT2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMU2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMV2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMW2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMX2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMY2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMZ2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM03', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM13', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM23', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM33', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM43', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM53', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM63']}
2025-05-23 12:31:55,041 - INFO - 批量插入表单数据成功，批次 3，共 100 条记录
2025-05-23 12:31:55,057 - INFO - 成功插入的数据ID: ['FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMF', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMG', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMH', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMI', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMJ', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMK', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BML', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMM', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMN', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMO', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMP', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMQ', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMR', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMS', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMT', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMU', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMV', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMW', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMX', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMY', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMZ', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM01', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM11', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM21', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM31', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM41', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM51', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM61', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM71', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM81', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM91', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMA1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMB1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMC1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMD1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BME1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMF1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMG1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMH1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMI1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMJ1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMK1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BML1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMM1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMN1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMO1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMP1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMQ1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMR1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMS1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMT1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMU1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMV1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMW1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMX1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMY1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMZ1', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM02', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM12', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM22', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM32', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM42', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM52', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM62', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM72', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM82', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM92', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMA2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMB2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMC2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMD2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BME2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMF2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMG2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMH2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMI2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMJ2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMK2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BML2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMM2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMN2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMO2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMP2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMQ2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMR2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMS2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMT2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMU2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMV2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMW2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMX2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMY2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BMZ2', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM03', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM13', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM23', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM33', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM43', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM53', 'FINST-FSC66G81K2OVO8D26Q0Y34LP5CY22UBI0B0BM63']
2025-05-23 12:32:00,353 - INFO - 批量插入响应状态码: 200
2025-05-23 12:32:00,353 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 23 May 2025 04:32:00 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4EAC4D98-6D0A-7A21-B84D-270F4537E407', 'x-acs-trace-id': 'ce7ddce2d79cde3d01a530056c078378', 'etag': '4KGnrG72gEY0lFI1+/Oblgw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-23 12:32:00,353 - INFO - 批量插入响应体: {'result': ['FINST-RNA66D71C4NVFIVHB0YC7DYP292N37FM0B0BMDL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N37FM0B0BMEL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N37FM0B0BMFL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N37FM0B0BMGL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N37FM0B0BMHL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMIL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMJL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMKL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMLL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMML', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMNL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMOL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMPL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMQL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMRL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMSL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMTL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMUL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMVL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMWL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMXL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMYL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMZL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM0M', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM1M', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM2M', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM3M', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM4M', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM5M', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM6M', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM7M', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM8M', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM9M', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMAM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMBM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMCM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMDM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMEM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMFM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMGM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMHM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMIM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMJM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMKM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMLM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMMM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMNM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMOM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMPM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMQM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMRM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMSM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMTM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMUM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMVM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMWM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMXM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMYM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMZM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM0N', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM1N', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM2N', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM3N', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM4N', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM5N', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM6N', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM7N', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM8N', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM9N', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMAN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMBN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMCN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMDN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMEN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMFN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMGN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMHN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMIN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMJN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMKN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMLN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMMN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMNN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMON', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMPN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMQN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMRN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMSN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMTN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMUN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMVN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMWN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMXN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMYN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMZN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM0O', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM1O', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM2O', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM3O', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM4O']}
2025-05-23 12:32:00,353 - INFO - 批量插入表单数据成功，批次 4，共 100 条记录
2025-05-23 12:32:00,353 - INFO - 成功插入的数据ID: ['FINST-RNA66D71C4NVFIVHB0YC7DYP292N37FM0B0BMDL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N37FM0B0BMEL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N37FM0B0BMFL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N37FM0B0BMGL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N37FM0B0BMHL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMIL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMJL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMKL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMLL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMML', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMNL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMOL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMPL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMQL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMRL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMSL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMTL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMUL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMVL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMWL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMXL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMYL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMZL', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM0M', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM1M', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM2M', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM3M', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM4M', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM5M', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM6M', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM7M', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM8M', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM9M', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMAM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMBM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMCM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMDM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMEM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMFM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMGM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMHM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMIM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMJM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMKM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMLM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMMM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMNM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMOM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMPM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMQM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMRM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMSM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMTM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMUM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMVM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMWM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMXM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMYM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMZM', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM0N', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM1N', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM2N', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM3N', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM4N', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM5N', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM6N', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM7N', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM8N', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM9N', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMAN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMBN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMCN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMDN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMEN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMFN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMGN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMHN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMIN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMJN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMKN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMLN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMMN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMNN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMON', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMPN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMQN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMRN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMSN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMTN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMUN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMVN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMWN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMXN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMYN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BMZN', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM0O', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM1O', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM2O', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM3O', 'FINST-RNA66D71C4NVFIVHB0YC7DYP292N38FM0B0BM4O']
2025-05-23 12:32:05,650 - INFO - 批量插入响应状态码: 200
2025-05-23 12:32:05,650 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 23 May 2025 04:32:05 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'FFC8F4E5-137A-7349-ACCD-5389EE49A24D', 'x-acs-trace-id': '690b39d3ae4ad89648bcb94c08def1b7', 'etag': '4YFRaFzFFivYcmKuC+94hIA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-23 12:32:05,650 - INFO - 批量插入响应体: {'result': ['FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMKF', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMLF', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMMF', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMNF', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMOF', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMPF', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMQF', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMRF', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMSF', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMTF', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMUF', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMVF', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMWF', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMXF', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMYF', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMZF', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM0G', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM1G', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM2G', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM3G', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM4G', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM5G', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM6G', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM7G', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM8G', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM9G', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMAG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMBG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMCG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMDG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMEG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMFG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMGG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMHG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMIG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMJG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMKG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMLG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMMG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMNG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMOG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMPG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMQG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMRG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMSG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMTG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMUG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMVG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMWG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMXG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMYG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMZG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM0H', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM1H', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM2H', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM3H', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM4H', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM5H', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM6H', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM7H', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM8H', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM9H', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMAH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMBH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMCH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMDH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMEH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMFH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMGH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMHH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMIH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMJH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMKH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMLH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMMH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMNH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMOH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMPH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMQH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMRH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMSH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMTH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMUH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMVH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMWH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMXH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMYH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMZH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM0I', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM1I', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM2I', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM3I', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM4I', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM5I', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM6I', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM7I', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM8I', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM9I', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMAI', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMBI']}
2025-05-23 12:32:05,650 - INFO - 批量插入表单数据成功，批次 5，共 100 条记录
2025-05-23 12:32:05,650 - INFO - 成功插入的数据ID: ['FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMKF', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMLF', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMMF', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMNF', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMOF', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMPF', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMQF', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMRF', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMSF', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMTF', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMUF', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMVF', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMWF', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMXF', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMYF', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMZF', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM0G', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM1G', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM2G', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM3G', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM4G', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM5G', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM6G', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM7G', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM8G', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM9G', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMAG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMBG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMCG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMDG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMEG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMFG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMGG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMHG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMIG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMJG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMKG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMLG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMMG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMNG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMOG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMPG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMQG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMRG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMSG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMTG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMUG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMVG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMWG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMXG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMYG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMZG', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM0H', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM1H', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM2H', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM3H', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM4H', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM5H', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM6H', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM7H', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM8H', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM9H', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMAH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMBH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMCH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMDH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMEH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMFH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMGH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMHH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMIH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMJH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMKH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMLH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMMH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMNH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMOH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMPH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMQH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMRH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMSH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMTH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMUH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMVH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMWH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMXH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMYH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMZH', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM0I', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM1I', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM2I', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM3I', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM4I', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM5I', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM6I', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM7I', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM8I', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BM9I', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMAI', 'FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMBI']
2025-05-23 12:32:10,885 - INFO - 批量插入响应状态码: 200
2025-05-23 12:32:10,885 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 23 May 2025 04:32:11 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2460', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E7456FA1-3A06-7009-8685-1B88C0CEB66E', 'x-acs-trace-id': '9d20ce2a5560b647f9c84adaeb79211e', 'etag': '29cp0o+jGzAJLVS0992hVTQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-23 12:32:10,885 - INFO - 批量插入响应体: {'result': ['FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMB7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMC7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMD7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BME7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMF7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMG7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMH7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMI7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMJ7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMK7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BML7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMM7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMN7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMO7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMP7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMQ7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMR7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMS7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMT7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMU7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMV7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMW7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMX7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMY7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMZ7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BM08', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BM18', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BM28', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BM38', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BM48', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BM58', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BM68', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BM78', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BM88', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BM98', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMA8', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMB8', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMC8', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMD8', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BME8', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMF8', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMG8', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMH8', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMI8', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMJ8', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMK8', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BML8', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMM8', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMN8', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMO8', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMP8']}
2025-05-23 12:32:10,885 - INFO - 批量插入表单数据成功，批次 6，共 51 条记录
2025-05-23 12:32:10,885 - INFO - 成功插入的数据ID: ['FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMB7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMC7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMD7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BME7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMF7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMG7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMH7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMI7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMJ7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMK7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BML7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMM7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMN7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMO7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMP7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMQ7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMR7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMS7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMT7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMU7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMV7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMW7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMX7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMY7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMZ7', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BM08', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BM18', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BM28', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BM38', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BM48', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BM58', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BM68', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BM78', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BM88', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BM98', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMA8', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMB8', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMC8', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMD8', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BME8', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMF8', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMG8', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMH8', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMI8', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMJ8', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMK8', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BML8', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMM8', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMN8', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMO8', 'FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMP8']
2025-05-23 12:32:15,900 - INFO - 批量插入完成，共 551 条记录
2025-05-23 12:32:15,900 - INFO - 日期 2025-05-22 处理完成 - 更新: 1 条，插入: 551 条，错误: 0 条
2025-05-23 12:32:15,900 - INFO - 数据同步完成！更新: 1 条，插入: 551 条，错误: 0 条
2025-05-23 12:32:15,900 - INFO - 同步完成
2025-05-23 13:30:33,784 - INFO - 使用默认增量同步（当天更新数据）
2025-05-23 13:30:33,784 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 13:30:33,784 - INFO - 查询参数: ('2025-05-23',)
2025-05-23 13:30:33,862 - INFO - MySQL查询成功，增量数据（日期: 2025-05-23），共获取 218 条记录
2025-05-23 13:30:33,862 - INFO - 获取到 2 个日期需要处理: ['2025-05-21', '2025-05-22']
2025-05-23 13:30:33,862 - INFO - 开始处理日期: 2025-05-21
2025-05-23 13:30:33,862 - INFO - Request Parameters - Page 1:
2025-05-23 13:30:33,862 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 13:30:33,862 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 13:30:41,987 - ERROR - 处理日期 2025-05-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 96225978-E26F-7087-8A55-D1C1ECC46235 Response: {'code': 'ServiceUnavailable', 'requestid': '96225978-E26F-7087-8A55-D1C1ECC46235', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 96225978-E26F-7087-8A55-D1C1ECC46235)
2025-05-23 13:30:41,987 - INFO - 开始处理日期: 2025-05-22
2025-05-23 13:30:41,987 - INFO - Request Parameters - Page 1:
2025-05-23 13:30:41,987 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 13:30:41,987 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 13:30:50,128 - ERROR - 处理日期 2025-05-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B229E5F9-224B-704B-B4B1-67B190D09CFA Response: {'code': 'ServiceUnavailable', 'requestid': 'B229E5F9-224B-704B-B4B1-67B190D09CFA', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B229E5F9-224B-704B-B4B1-67B190D09CFA)
2025-05-23 13:30:50,128 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-23 13:31:50,143 - INFO - 开始同步昨天与今天的销售数据: 2025-05-22 至 2025-05-23
2025-05-23 13:31:50,143 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 13:31:50,143 - INFO - 查询参数: ('2025-05-22', '2025-05-23')
2025-05-23 13:31:50,221 - INFO - MySQL查询成功，时间段: 2025-05-22 至 2025-05-23，共获取 615 条记录
2025-05-23 13:31:50,221 - INFO - 获取到 1 个日期需要处理: ['2025-05-22']
2025-05-23 13:31:50,221 - INFO - 开始处理日期: 2025-05-22
2025-05-23 13:31:50,221 - INFO - Request Parameters - Page 1:
2025-05-23 13:31:50,221 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 13:31:50,221 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 13:31:58,361 - ERROR - 处理日期 2025-05-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5A698A70-40EB-78A4-A985-39410BB02BC6 Response: {'code': 'ServiceUnavailable', 'requestid': '5A698A70-40EB-78A4-A985-39410BB02BC6', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 5A698A70-40EB-78A4-A985-39410BB02BC6)
2025-05-23 13:31:58,361 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-23 13:31:58,361 - INFO - 同步完成
2025-05-23 14:30:33,730 - INFO - 使用默认增量同步（当天更新数据）
2025-05-23 14:30:33,730 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 14:30:33,730 - INFO - 查询参数: ('2025-05-23',)
2025-05-23 14:30:33,808 - INFO - MySQL查询成功，增量数据（日期: 2025-05-23），共获取 218 条记录
2025-05-23 14:30:33,808 - INFO - 获取到 2 个日期需要处理: ['2025-05-21', '2025-05-22']
2025-05-23 14:30:33,808 - INFO - 开始处理日期: 2025-05-21
2025-05-23 14:30:33,808 - INFO - Request Parameters - Page 1:
2025-05-23 14:30:33,808 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 14:30:33,808 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 14:30:41,948 - ERROR - 处理日期 2025-05-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 80D732AD-A5F8-740C-A965-752A3FF24C24 Response: {'code': 'ServiceUnavailable', 'requestid': '80D732AD-A5F8-740C-A965-752A3FF24C24', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 80D732AD-A5F8-740C-A965-752A3FF24C24)
2025-05-23 14:30:41,948 - INFO - 开始处理日期: 2025-05-22
2025-05-23 14:30:41,948 - INFO - Request Parameters - Page 1:
2025-05-23 14:30:41,948 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 14:30:41,948 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 14:30:50,073 - ERROR - 处理日期 2025-05-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 301DA434-09E2-7B4F-92B0-E2839BFBE9EB Response: {'code': 'ServiceUnavailable', 'requestid': '301DA434-09E2-7B4F-92B0-E2839BFBE9EB', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 301DA434-09E2-7B4F-92B0-E2839BFBE9EB)
2025-05-23 14:30:50,073 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-23 14:31:50,088 - INFO - 开始同步昨天与今天的销售数据: 2025-05-22 至 2025-05-23
2025-05-23 14:31:50,088 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 14:31:50,088 - INFO - 查询参数: ('2025-05-22', '2025-05-23')
2025-05-23 14:31:50,167 - INFO - MySQL查询成功，时间段: 2025-05-22 至 2025-05-23，共获取 615 条记录
2025-05-23 14:31:50,167 - INFO - 获取到 1 个日期需要处理: ['2025-05-22']
2025-05-23 14:31:50,167 - INFO - 开始处理日期: 2025-05-22
2025-05-23 14:31:50,167 - INFO - Request Parameters - Page 1:
2025-05-23 14:31:50,167 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 14:31:50,167 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 14:31:58,276 - ERROR - 处理日期 2025-05-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: BE9188FE-BF59-7EAE-ABAF-461F1202BA4D Response: {'code': 'ServiceUnavailable', 'requestid': 'BE9188FE-BF59-7EAE-ABAF-461F1202BA4D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: BE9188FE-BF59-7EAE-ABAF-461F1202BA4D)
2025-05-23 14:31:58,276 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-23 14:31:58,276 - INFO - 同步完成
2025-05-23 15:30:33,738 - INFO - 使用默认增量同步（当天更新数据）
2025-05-23 15:30:33,738 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 15:30:33,738 - INFO - 查询参数: ('2025-05-23',)
2025-05-23 15:30:33,816 - INFO - MySQL查询成功，增量数据（日期: 2025-05-23），共获取 219 条记录
2025-05-23 15:30:33,816 - INFO - 获取到 2 个日期需要处理: ['2025-05-21', '2025-05-22']
2025-05-23 15:30:33,816 - INFO - 开始处理日期: 2025-05-21
2025-05-23 15:30:33,832 - INFO - Request Parameters - Page 1:
2025-05-23 15:30:33,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:30:33,832 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:30:41,941 - ERROR - 处理日期 2025-05-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 00DC9C95-510F-7C5C-94F1-8A5F7642A6F2 Response: {'code': 'ServiceUnavailable', 'requestid': '00DC9C95-510F-7C5C-94F1-8A5F7642A6F2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 00DC9C95-510F-7C5C-94F1-8A5F7642A6F2)
2025-05-23 15:30:41,941 - INFO - 开始处理日期: 2025-05-22
2025-05-23 15:30:41,941 - INFO - Request Parameters - Page 1:
2025-05-23 15:30:41,941 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:30:41,941 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:30:50,081 - ERROR - 处理日期 2025-05-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3E85C2F2-37D4-7211-AC80-B554C83B69B5 Response: {'code': 'ServiceUnavailable', 'requestid': '3E85C2F2-37D4-7211-AC80-B554C83B69B5', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3E85C2F2-37D4-7211-AC80-B554C83B69B5)
2025-05-23 15:30:50,081 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-23 15:31:50,097 - INFO - 开始同步昨天与今天的销售数据: 2025-05-22 至 2025-05-23
2025-05-23 15:31:50,097 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 15:31:50,097 - INFO - 查询参数: ('2025-05-22', '2025-05-23')
2025-05-23 15:31:50,175 - INFO - MySQL查询成功，时间段: 2025-05-22 至 2025-05-23，共获取 615 条记录
2025-05-23 15:31:50,175 - INFO - 获取到 1 个日期需要处理: ['2025-05-22']
2025-05-23 15:31:50,175 - INFO - 开始处理日期: 2025-05-22
2025-05-23 15:31:50,175 - INFO - Request Parameters - Page 1:
2025-05-23 15:31:50,175 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:31:50,175 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:31:51,503 - INFO - Response - Page 1:
2025-05-23 15:31:51,503 - INFO - 第 1 页获取到 100 条记录
2025-05-23 15:31:51,706 - INFO - Request Parameters - Page 2:
2025-05-23 15:31:51,706 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:31:51,706 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:31:52,581 - INFO - Response - Page 2:
2025-05-23 15:31:52,581 - INFO - 第 2 页获取到 100 条记录
2025-05-23 15:31:52,784 - INFO - Request Parameters - Page 3:
2025-05-23 15:31:52,784 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 15:31:52,784 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 15:32:00,893 - ERROR - 处理日期 2025-05-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B494AFD9-209B-72E1-BCBB-B86280EDA0D1 Response: {'code': 'ServiceUnavailable', 'requestid': 'B494AFD9-209B-72E1-BCBB-B86280EDA0D1', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B494AFD9-209B-72E1-BCBB-B86280EDA0D1)
2025-05-23 15:32:00,893 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-23 15:32:00,893 - INFO - 同步完成
2025-05-23 16:30:33,934 - INFO - 使用默认增量同步（当天更新数据）
2025-05-23 16:30:33,934 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 16:30:33,934 - INFO - 查询参数: ('2025-05-23',)
2025-05-23 16:30:34,012 - INFO - MySQL查询成功，增量数据（日期: 2025-05-23），共获取 220 条记录
2025-05-23 16:30:34,012 - INFO - 获取到 3 个日期需要处理: ['2025-05-14', '2025-05-21', '2025-05-22']
2025-05-23 16:30:34,012 - INFO - 开始处理日期: 2025-05-14
2025-05-23 16:30:34,012 - INFO - Request Parameters - Page 1:
2025-05-23 16:30:34,012 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 16:30:34,012 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 16:30:42,121 - ERROR - 处理日期 2025-05-14 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5C1993BD-172A-7CDA-9529-8276A3B7D08E Response: {'code': 'ServiceUnavailable', 'requestid': '5C1993BD-172A-7CDA-9529-8276A3B7D08E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 5C1993BD-172A-7CDA-9529-8276A3B7D08E)
2025-05-23 16:30:42,121 - INFO - 开始处理日期: 2025-05-21
2025-05-23 16:30:42,121 - INFO - Request Parameters - Page 1:
2025-05-23 16:30:42,121 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 16:30:42,121 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 16:30:50,262 - ERROR - 处理日期 2025-05-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C04557FC-FB17-7DDC-81CB-B47DAD33B78A Response: {'code': 'ServiceUnavailable', 'requestid': 'C04557FC-FB17-7DDC-81CB-B47DAD33B78A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C04557FC-FB17-7DDC-81CB-B47DAD33B78A)
2025-05-23 16:30:50,262 - INFO - 开始处理日期: 2025-05-22
2025-05-23 16:30:50,262 - INFO - Request Parameters - Page 1:
2025-05-23 16:30:50,262 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 16:30:50,262 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 16:30:58,371 - ERROR - 处理日期 2025-05-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 8CFD30AB-1F91-7C8B-BB7D-178E2ADE5DA7 Response: {'code': 'ServiceUnavailable', 'requestid': '8CFD30AB-1F91-7C8B-BB7D-178E2ADE5DA7', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 8CFD30AB-1F91-7C8B-BB7D-178E2ADE5DA7)
2025-05-23 16:30:58,371 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 3 条
2025-05-23 16:31:58,386 - INFO - 开始同步昨天与今天的销售数据: 2025-05-22 至 2025-05-23
2025-05-23 16:31:58,386 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 16:31:58,386 - INFO - 查询参数: ('2025-05-22', '2025-05-23')
2025-05-23 16:31:58,464 - INFO - MySQL查询成功，时间段: 2025-05-22 至 2025-05-23，共获取 615 条记录
2025-05-23 16:31:58,464 - INFO - 获取到 1 个日期需要处理: ['2025-05-22']
2025-05-23 16:31:58,480 - INFO - 开始处理日期: 2025-05-22
2025-05-23 16:31:58,480 - INFO - Request Parameters - Page 1:
2025-05-23 16:31:58,480 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 16:31:58,480 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 16:31:59,433 - INFO - Response - Page 1:
2025-05-23 16:31:59,433 - INFO - 第 1 页获取到 100 条记录
2025-05-23 16:31:59,636 - INFO - Request Parameters - Page 2:
2025-05-23 16:31:59,636 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 16:31:59,636 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 16:32:00,449 - INFO - Response - Page 2:
2025-05-23 16:32:00,449 - INFO - 第 2 页获取到 100 条记录
2025-05-23 16:32:00,652 - INFO - Request Parameters - Page 3:
2025-05-23 16:32:00,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 16:32:00,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 16:32:08,761 - ERROR - 处理日期 2025-05-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D6BF366A-D60D-7B0B-8585-4BC96AE61D3B Response: {'code': 'ServiceUnavailable', 'requestid': 'D6BF366A-D60D-7B0B-8585-4BC96AE61D3B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D6BF366A-D60D-7B0B-8585-4BC96AE61D3B)
2025-05-23 16:32:08,761 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-23 16:32:08,761 - INFO - 同步完成
2025-05-23 17:30:33,926 - INFO - 使用默认增量同步（当天更新数据）
2025-05-23 17:30:33,926 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 17:30:33,926 - INFO - 查询参数: ('2025-05-23',)
2025-05-23 17:30:34,004 - INFO - MySQL查询成功，增量数据（日期: 2025-05-23），共获取 221 条记录
2025-05-23 17:30:34,004 - INFO - 获取到 4 个日期需要处理: ['2025-05-14', '2025-05-21', '2025-05-22', '2025-05-23']
2025-05-23 17:30:34,004 - INFO - 开始处理日期: 2025-05-14
2025-05-23 17:30:34,004 - INFO - Request Parameters - Page 1:
2025-05-23 17:30:34,004 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 17:30:34,004 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 17:30:42,129 - ERROR - 处理日期 2025-05-14 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: DDB42E37-08D9-71C2-9A81-F3EC92910BCD Response: {'code': 'ServiceUnavailable', 'requestid': 'DDB42E37-08D9-71C2-9A81-F3EC92910BCD', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: DDB42E37-08D9-71C2-9A81-F3EC92910BCD)
2025-05-23 17:30:42,129 - INFO - 开始处理日期: 2025-05-21
2025-05-23 17:30:42,129 - INFO - Request Parameters - Page 1:
2025-05-23 17:30:42,129 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 17:30:42,129 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 17:30:50,254 - ERROR - 处理日期 2025-05-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 92B06BEE-C262-76C5-BB1D-3D8F12884252 Response: {'code': 'ServiceUnavailable', 'requestid': '92B06BEE-C262-76C5-BB1D-3D8F12884252', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 92B06BEE-C262-76C5-BB1D-3D8F12884252)
2025-05-23 17:30:50,254 - INFO - 开始处理日期: 2025-05-22
2025-05-23 17:30:50,254 - INFO - Request Parameters - Page 1:
2025-05-23 17:30:50,254 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 17:30:50,254 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 17:30:58,364 - ERROR - 处理日期 2025-05-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B64FA12C-3C27-73E7-8093-72A1ACA5E821 Response: {'code': 'ServiceUnavailable', 'requestid': 'B64FA12C-3C27-73E7-8093-72A1ACA5E821', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B64FA12C-3C27-73E7-8093-72A1ACA5E821)
2025-05-23 17:30:58,364 - INFO - 开始处理日期: 2025-05-23
2025-05-23 17:30:58,364 - INFO - Request Parameters - Page 1:
2025-05-23 17:30:58,364 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 17:30:58,364 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 17:31:01,754 - INFO - Response - Page 1:
2025-05-23 17:31:01,754 - INFO - 查询完成，共获取到 0 条记录
2025-05-23 17:31:01,754 - INFO - 获取到 0 条表单数据
2025-05-23 17:31:01,754 - INFO - 当前日期 2025-05-23 有 1 条MySQL数据需要处理
2025-05-23 17:31:01,754 - INFO - 开始批量插入 1 条新记录
2025-05-23 17:31:01,926 - INFO - 批量插入响应状态码: 200
2025-05-23 17:31:01,926 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 23 May 2025 09:31:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'CD90BD2A-2EFD-7517-8BFF-7DDE883FDBAE', 'x-acs-trace-id': 'ff945a789367ff5caf223c6dac5dcc90', 'etag': '5ecp6Ixtva1BBb9gDbmqG3g9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-23 17:31:01,926 - INFO - 批量插入响应体: {'result': ['FINST-VFF66XA12COV8OTCF7CR5DRUUPWQ2CB6PL0BMB']}
2025-05-23 17:31:01,926 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-23 17:31:01,926 - INFO - 成功插入的数据ID: ['FINST-VFF66XA12COV8OTCF7CR5DRUUPWQ2CB6PL0BMB']
2025-05-23 17:31:06,942 - INFO - 批量插入完成，共 1 条记录
2025-05-23 17:31:06,942 - INFO - 日期 2025-05-23 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-05-23 17:31:06,942 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 3 条
2025-05-23 17:32:06,957 - INFO - 开始同步昨天与今天的销售数据: 2025-05-22 至 2025-05-23
2025-05-23 17:32:06,957 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 17:32:06,957 - INFO - 查询参数: ('2025-05-22', '2025-05-23')
2025-05-23 17:32:07,035 - INFO - MySQL查询成功，时间段: 2025-05-22 至 2025-05-23，共获取 616 条记录
2025-05-23 17:32:07,035 - INFO - 获取到 2 个日期需要处理: ['2025-05-22', '2025-05-23']
2025-05-23 17:32:07,035 - INFO - 开始处理日期: 2025-05-22
2025-05-23 17:32:07,035 - INFO - Request Parameters - Page 1:
2025-05-23 17:32:07,035 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 17:32:07,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 17:32:15,160 - ERROR - 处理日期 2025-05-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 0DF8CE68-2912-7268-A8AC-3CF7C7735B91 Response: {'code': 'ServiceUnavailable', 'requestid': '0DF8CE68-2912-7268-A8AC-3CF7C7735B91', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 0DF8CE68-2912-7268-A8AC-3CF7C7735B91)
2025-05-23 17:32:15,160 - INFO - 开始处理日期: 2025-05-23
2025-05-23 17:32:15,160 - INFO - Request Parameters - Page 1:
2025-05-23 17:32:15,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 17:32:15,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 17:32:15,707 - INFO - Response - Page 1:
2025-05-23 17:32:15,707 - INFO - 第 1 页获取到 1 条记录
2025-05-23 17:32:15,910 - INFO - 查询完成，共获取到 1 条记录
2025-05-23 17:32:15,910 - INFO - 获取到 1 条表单数据
2025-05-23 17:32:15,910 - INFO - 当前日期 2025-05-23 有 1 条MySQL数据需要处理
2025-05-23 17:32:15,910 - INFO - 日期 2025-05-23 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 17:32:15,910 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-23 17:32:15,910 - INFO - 同步完成
2025-05-23 18:30:33,872 - INFO - 使用默认增量同步（当天更新数据）
2025-05-23 18:30:33,872 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 18:30:33,872 - INFO - 查询参数: ('2025-05-23',)
2025-05-23 18:30:33,935 - INFO - MySQL查询成功，增量数据（日期: 2025-05-23），共获取 221 条记录
2025-05-23 18:30:33,935 - INFO - 获取到 4 个日期需要处理: ['2025-05-14', '2025-05-21', '2025-05-22', '2025-05-23']
2025-05-23 18:30:33,935 - INFO - 开始处理日期: 2025-05-14
2025-05-23 18:30:33,950 - INFO - Request Parameters - Page 1:
2025-05-23 18:30:33,950 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:30:33,950 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:30:42,075 - ERROR - 处理日期 2025-05-14 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: DA5D19F1-F6F9-791E-A36E-54F1F3E27E9B Response: {'code': 'ServiceUnavailable', 'requestid': 'DA5D19F1-F6F9-791E-A36E-54F1F3E27E9B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: DA5D19F1-F6F9-791E-A36E-54F1F3E27E9B)
2025-05-23 18:30:42,075 - INFO - 开始处理日期: 2025-05-21
2025-05-23 18:30:42,075 - INFO - Request Parameters - Page 1:
2025-05-23 18:30:42,075 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:30:42,075 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:30:44,622 - INFO - Response - Page 1:
2025-05-23 18:30:44,622 - INFO - 第 1 页获取到 100 条记录
2025-05-23 18:30:44,825 - INFO - Request Parameters - Page 2:
2025-05-23 18:30:44,825 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:30:44,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:30:45,622 - INFO - Response - Page 2:
2025-05-23 18:30:45,622 - INFO - 第 2 页获取到 100 条记录
2025-05-23 18:30:45,825 - INFO - Request Parameters - Page 3:
2025-05-23 18:30:45,825 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:30:45,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:30:53,935 - ERROR - 处理日期 2025-05-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: AB840A04-45D5-7E49-81A8-CD3C6FC1C218 Response: {'code': 'ServiceUnavailable', 'requestid': 'AB840A04-45D5-7E49-81A8-CD3C6FC1C218', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: AB840A04-45D5-7E49-81A8-CD3C6FC1C218)
2025-05-23 18:30:53,935 - INFO - 开始处理日期: 2025-05-22
2025-05-23 18:30:53,935 - INFO - Request Parameters - Page 1:
2025-05-23 18:30:53,935 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:30:53,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:31:01,794 - INFO - Response - Page 1:
2025-05-23 18:31:01,794 - INFO - 第 1 页获取到 100 条记录
2025-05-23 18:31:01,997 - INFO - Request Parameters - Page 2:
2025-05-23 18:31:01,997 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:31:01,997 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:31:02,841 - INFO - Response - Page 2:
2025-05-23 18:31:02,841 - INFO - 第 2 页获取到 100 条记录
2025-05-23 18:31:03,044 - INFO - Request Parameters - Page 3:
2025-05-23 18:31:03,044 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:31:03,044 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:31:03,856 - INFO - Response - Page 3:
2025-05-23 18:31:03,856 - INFO - 第 3 页获取到 100 条记录
2025-05-23 18:31:04,060 - INFO - Request Parameters - Page 4:
2025-05-23 18:31:04,060 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:31:04,060 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:31:04,841 - INFO - Response - Page 4:
2025-05-23 18:31:04,841 - INFO - 第 4 页获取到 100 条记录
2025-05-23 18:31:05,044 - INFO - Request Parameters - Page 5:
2025-05-23 18:31:05,044 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:31:05,044 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:31:05,856 - INFO - Response - Page 5:
2025-05-23 18:31:05,856 - INFO - 第 5 页获取到 100 条记录
2025-05-23 18:31:06,060 - INFO - Request Parameters - Page 6:
2025-05-23 18:31:06,060 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:31:06,060 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:31:06,825 - INFO - Response - Page 6:
2025-05-23 18:31:06,825 - INFO - 第 6 页获取到 100 条记录
2025-05-23 18:31:07,028 - INFO - Request Parameters - Page 7:
2025-05-23 18:31:07,028 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:31:07,028 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:31:07,606 - INFO - Response - Page 7:
2025-05-23 18:31:07,606 - INFO - 第 7 页获取到 13 条记录
2025-05-23 18:31:07,810 - INFO - 查询完成，共获取到 613 条记录
2025-05-23 18:31:07,810 - INFO - 获取到 613 条表单数据
2025-05-23 18:31:07,810 - INFO - 当前日期 2025-05-22 有 214 条MySQL数据需要处理
2025-05-23 18:31:07,810 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM8D
2025-05-23 18:31:08,325 - INFO - 更新表单数据成功: FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BM8D
2025-05-23 18:31:08,325 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9685.64, 'new_value': 8841.16}, {'field': 'total_amount', 'old_value': 9685.64, 'new_value': 8841.16}, {'field': 'order_count', 'old_value': 88, 'new_value': 80}]
2025-05-23 18:31:08,325 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMVD
2025-05-23 18:31:08,856 - INFO - 更新表单数据成功: FINST-OIF66BA16FNVGO7170L6096T0Z0I2K6A0B0BMVD
2025-05-23 18:31:08,856 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1325.0, 'new_value': 1663.0}, {'field': 'total_amount', 'old_value': 1325.0, 'new_value': 1663.0}]
2025-05-23 18:31:08,856 - INFO - 开始更新记录 - 表单实例ID: FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMKH
2025-05-23 18:31:09,341 - INFO - 更新表单数据成功: FINST-ZX866571Q4MVXLPOASTUMCZD7G4932IQ0B0BMKH
2025-05-23 18:31:09,341 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5374.0, 'new_value': 4446.0}, {'field': 'total_amount', 'old_value': 5374.0, 'new_value': 4446.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-05-23 18:31:09,356 - INFO - 开始更新记录 - 表单实例ID: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMF7
2025-05-23 18:31:09,716 - INFO - 更新表单数据成功: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMF7
2025-05-23 18:31:09,716 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5000.0, 'new_value': 4809.5}, {'field': 'total_amount', 'old_value': 5000.0, 'new_value': 4809.5}, {'field': 'order_count', 'old_value': 1, 'new_value': 76}]
2025-05-23 18:31:09,716 - INFO - 开始更新记录 - 表单实例ID: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMG7
2025-05-23 18:31:10,247 - INFO - 更新表单数据成功: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMG7
2025-05-23 18:31:10,247 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12000.0, 'new_value': 14679.0}, {'field': 'total_amount', 'old_value': 12000.0, 'new_value': 14679.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-23 18:31:10,247 - INFO - 开始更新记录 - 表单实例ID: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMH7
2025-05-23 18:31:10,700 - INFO - 更新表单数据成功: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMH7
2025-05-23 18:31:10,700 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1100.0, 'new_value': 1139.0}, {'field': 'total_amount', 'old_value': 1100.0, 'new_value': 1139.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 113}]
2025-05-23 18:31:10,700 - INFO - 开始更新记录 - 表单实例ID: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMI7
2025-05-23 18:31:11,122 - INFO - 更新表单数据成功: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMI7
2025-05-23 18:31:11,122 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9000.0, 'new_value': 14748.74}, {'field': 'total_amount', 'old_value': 9000.0, 'new_value': 14748.74}, {'field': 'order_count', 'old_value': 1, 'new_value': 18}]
2025-05-23 18:31:11,122 - INFO - 开始更新记录 - 表单实例ID: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMJ7
2025-05-23 18:31:11,560 - INFO - 更新表单数据成功: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMJ7
2025-05-23 18:31:11,560 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 200.0, 'new_value': 100.0}, {'field': 'total_amount', 'old_value': 200.0, 'new_value': 100.0}]
2025-05-23 18:31:11,560 - INFO - 开始更新记录 - 表单实例ID: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMK7
2025-05-23 18:31:12,060 - INFO - 更新表单数据成功: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMK7
2025-05-23 18:31:12,060 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40000.0, 'new_value': 63100.0}, {'field': 'total_amount', 'old_value': 40000.0, 'new_value': 63100.0}]
2025-05-23 18:31:12,060 - INFO - 开始更新记录 - 表单实例ID: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMN7
2025-05-23 18:31:12,575 - INFO - 更新表单数据成功: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMN7
2025-05-23 18:31:12,575 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2171.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2171.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 11}]
2025-05-23 18:31:12,575 - INFO - 开始更新记录 - 表单实例ID: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMQ7
2025-05-23 18:31:13,106 - INFO - 更新表单数据成功: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMQ7
2025-05-23 18:31:13,106 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3500.0, 'new_value': 3796.0}, {'field': 'total_amount', 'old_value': 3500.0, 'new_value': 3796.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 108}]
2025-05-23 18:31:13,106 - INFO - 开始更新记录 - 表单实例ID: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMR7
2025-05-23 18:31:13,669 - INFO - 更新表单数据成功: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMR7
2025-05-23 18:31:13,669 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2500.0, 'new_value': 3079.15}, {'field': 'total_amount', 'old_value': 2500.0, 'new_value': 3079.15}, {'field': 'order_count', 'old_value': 1, 'new_value': 33}]
2025-05-23 18:31:13,669 - INFO - 开始更新记录 - 表单实例ID: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMT7
2025-05-23 18:31:14,106 - INFO - 更新表单数据成功: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMT7
2025-05-23 18:31:14,106 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3880.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 3880.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-23 18:31:14,106 - INFO - 开始更新记录 - 表单实例ID: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMU7
2025-05-23 18:31:14,606 - INFO - 更新表单数据成功: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMU7
2025-05-23 18:31:14,606 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2880.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2880.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-23 18:31:14,606 - INFO - 开始更新记录 - 表单实例ID: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMW7
2025-05-23 18:31:15,028 - INFO - 更新表单数据成功: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMW7
2025-05-23 18:31:15,028 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37000.0, 'new_value': 48000.0}, {'field': 'total_amount', 'old_value': 37000.0, 'new_value': 48000.0}]
2025-05-23 18:31:15,028 - INFO - 开始更新记录 - 表单实例ID: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMY7
2025-05-23 18:31:15,450 - INFO - 更新表单数据成功: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMY7
2025-05-23 18:31:15,450 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1300.0, 'new_value': 1228.0}, {'field': 'total_amount', 'old_value': 1300.0, 'new_value': 1228.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 109}]
2025-05-23 18:31:15,450 - INFO - 开始更新记录 - 表单实例ID: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMZ7
2025-05-23 18:31:15,888 - INFO - 更新表单数据成功: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BMZ7
2025-05-23 18:31:15,888 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2568.2}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2568.2}, {'field': 'order_count', 'old_value': 0, 'new_value': 113}]
2025-05-23 18:31:15,888 - INFO - 开始更新记录 - 表单实例ID: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BM08
2025-05-23 18:31:16,341 - INFO - 更新表单数据成功: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BM08
2025-05-23 18:31:16,341 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 464.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 464.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 8}]
2025-05-23 18:31:16,341 - INFO - 开始更新记录 - 表单实例ID: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BM18
2025-05-23 18:31:16,825 - INFO - 更新表单数据成功: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BM18
2025-05-23 18:31:16,825 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1680.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1680.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-23 18:31:16,825 - INFO - 开始更新记录 - 表单实例ID: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BM38
2025-05-23 18:31:17,278 - INFO - 更新表单数据成功: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BM38
2025-05-23 18:31:17,278 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 160000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 160000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-23 18:31:17,278 - INFO - 开始更新记录 - 表单实例ID: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BM48
2025-05-23 18:31:17,684 - INFO - 更新表单数据成功: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BM48
2025-05-23 18:31:17,684 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2864.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2864.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 3}]
2025-05-23 18:31:17,684 - INFO - 开始更新记录 - 表单实例ID: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BM68
2025-05-23 18:31:18,091 - INFO - 更新表单数据成功: FINST-MLF66JA199MVNES3CSVP0DDBPZ9N2JJU0B0BM68
2025-05-23 18:31:18,091 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 154.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 154.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 16}]
2025-05-23 18:31:18,091 - INFO - 开始批量插入 2 条新记录
2025-05-23 18:31:18,247 - INFO - 批量插入响应状态码: 200
2025-05-23 18:31:18,247 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 23 May 2025 10:31:18 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '106', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6D82C2ED-464D-74FF-A90A-238E2B5ED5A6', 'x-acs-trace-id': 'f0c2dcbcd4684dd069e2860accacd161', 'etag': '1llIgtTJu/IoKy7LKI8S3BQ6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-23 18:31:18,247 - INFO - 批量插入响应体: {'result': ['FINST-XBF66071AEOVVBWH80UQRBXI74IP2HPOUN0BME', 'FINST-XBF66071AEOVVBWH80UQRBXI74IP2HPOUN0BMF']}
2025-05-23 18:31:18,247 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-05-23 18:31:18,247 - INFO - 成功插入的数据ID: ['FINST-XBF66071AEOVVBWH80UQRBXI74IP2HPOUN0BME', 'FINST-XBF66071AEOVVBWH80UQRBXI74IP2HPOUN0BMF']
2025-05-23 18:31:23,263 - INFO - 批量插入完成，共 2 条记录
2025-05-23 18:31:23,263 - INFO - 日期 2025-05-22 处理完成 - 更新: 22 条，插入: 2 条，错误: 0 条
2025-05-23 18:31:23,263 - INFO - 开始处理日期: 2025-05-23
2025-05-23 18:31:23,263 - INFO - Request Parameters - Page 1:
2025-05-23 18:31:23,263 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:31:23,263 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:31:23,888 - INFO - Response - Page 1:
2025-05-23 18:31:23,888 - INFO - 第 1 页获取到 1 条记录
2025-05-23 18:31:24,091 - INFO - 查询完成，共获取到 1 条记录
2025-05-23 18:31:24,091 - INFO - 获取到 1 条表单数据
2025-05-23 18:31:24,091 - INFO - 当前日期 2025-05-23 有 1 条MySQL数据需要处理
2025-05-23 18:31:24,091 - INFO - 日期 2025-05-23 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 18:31:24,091 - INFO - 数据同步完成！更新: 22 条，插入: 2 条，错误: 2 条
2025-05-23 18:32:24,106 - INFO - 开始同步昨天与今天的销售数据: 2025-05-22 至 2025-05-23
2025-05-23 18:32:24,106 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 18:32:24,106 - INFO - 查询参数: ('2025-05-22', '2025-05-23')
2025-05-23 18:32:24,215 - INFO - MySQL查询成功，时间段: 2025-05-22 至 2025-05-23，共获取 616 条记录
2025-05-23 18:32:24,215 - INFO - 获取到 2 个日期需要处理: ['2025-05-22', '2025-05-23']
2025-05-23 18:32:24,231 - INFO - 开始处理日期: 2025-05-22
2025-05-23 18:32:24,231 - INFO - Request Parameters - Page 1:
2025-05-23 18:32:24,231 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:32:24,231 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:32:25,122 - INFO - Response - Page 1:
2025-05-23 18:32:25,122 - INFO - 第 1 页获取到 100 条记录
2025-05-23 18:32:25,325 - INFO - Request Parameters - Page 2:
2025-05-23 18:32:25,325 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:32:25,325 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:32:26,231 - INFO - Response - Page 2:
2025-05-23 18:32:26,231 - INFO - 第 2 页获取到 100 条记录
2025-05-23 18:32:26,434 - INFO - Request Parameters - Page 3:
2025-05-23 18:32:26,434 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:32:26,434 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:32:27,293 - INFO - Response - Page 3:
2025-05-23 18:32:27,293 - INFO - 第 3 页获取到 100 条记录
2025-05-23 18:32:27,497 - INFO - Request Parameters - Page 4:
2025-05-23 18:32:27,497 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:32:27,497 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:32:28,637 - INFO - Response - Page 4:
2025-05-23 18:32:28,637 - INFO - 第 4 页获取到 100 条记录
2025-05-23 18:32:28,840 - INFO - Request Parameters - Page 5:
2025-05-23 18:32:28,840 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:32:28,840 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:32:29,637 - INFO - Response - Page 5:
2025-05-23 18:32:29,637 - INFO - 第 5 页获取到 100 条记录
2025-05-23 18:32:29,840 - INFO - Request Parameters - Page 6:
2025-05-23 18:32:29,840 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:32:29,840 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:32:30,653 - INFO - Response - Page 6:
2025-05-23 18:32:30,653 - INFO - 第 6 页获取到 100 条记录
2025-05-23 18:32:30,856 - INFO - Request Parameters - Page 7:
2025-05-23 18:32:30,856 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:32:30,856 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:32:31,450 - INFO - Response - Page 7:
2025-05-23 18:32:31,450 - INFO - 第 7 页获取到 15 条记录
2025-05-23 18:32:31,653 - INFO - 查询完成，共获取到 615 条记录
2025-05-23 18:32:31,653 - INFO - 获取到 615 条表单数据
2025-05-23 18:32:31,653 - INFO - 当前日期 2025-05-22 有 615 条MySQL数据需要处理
2025-05-23 18:32:31,668 - INFO - 日期 2025-05-22 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 18:32:31,668 - INFO - 开始处理日期: 2025-05-23
2025-05-23 18:32:31,668 - INFO - Request Parameters - Page 1:
2025-05-23 18:32:31,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 18:32:31,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 18:32:32,168 - INFO - Response - Page 1:
2025-05-23 18:32:32,168 - INFO - 第 1 页获取到 1 条记录
2025-05-23 18:32:32,371 - INFO - 查询完成，共获取到 1 条记录
2025-05-23 18:32:32,371 - INFO - 获取到 1 条表单数据
2025-05-23 18:32:32,371 - INFO - 当前日期 2025-05-23 有 1 条MySQL数据需要处理
2025-05-23 18:32:32,371 - INFO - 日期 2025-05-23 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 18:32:32,371 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 18:32:32,371 - INFO - 同步完成
2025-05-23 19:30:34,710 - INFO - 使用默认增量同步（当天更新数据）
2025-05-23 19:30:34,710 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 19:30:34,710 - INFO - 查询参数: ('2025-05-23',)
2025-05-23 19:30:34,788 - INFO - MySQL查询成功，增量数据（日期: 2025-05-23），共获取 221 条记录
2025-05-23 19:30:34,788 - INFO - 获取到 4 个日期需要处理: ['2025-05-14', '2025-05-21', '2025-05-22', '2025-05-23']
2025-05-23 19:30:34,788 - INFO - 开始处理日期: 2025-05-14
2025-05-23 19:30:34,788 - INFO - Request Parameters - Page 1:
2025-05-23 19:30:34,788 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 19:30:34,788 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 19:30:42,898 - ERROR - 处理日期 2025-05-14 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2CDE3330-B8BD-78C1-859F-E3A14945DE98 Response: {'code': 'ServiceUnavailable', 'requestid': '2CDE3330-B8BD-78C1-859F-E3A14945DE98', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2CDE3330-B8BD-78C1-859F-E3A14945DE98)
2025-05-23 19:30:42,898 - INFO - 开始处理日期: 2025-05-21
2025-05-23 19:30:42,898 - INFO - Request Parameters - Page 1:
2025-05-23 19:30:42,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 19:30:42,898 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 19:30:45,477 - INFO - Response - Page 1:
2025-05-23 19:30:45,477 - INFO - 第 1 页获取到 100 条记录
2025-05-23 19:30:45,680 - INFO - Request Parameters - Page 2:
2025-05-23 19:30:45,680 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 19:30:45,680 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 19:30:53,775 - ERROR - 处理日期 2025-05-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C1FCD2D1-5F96-789A-993A-812FC423A1D3 Response: {'code': 'ServiceUnavailable', 'requestid': 'C1FCD2D1-5F96-789A-993A-812FC423A1D3', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C1FCD2D1-5F96-789A-993A-812FC423A1D3)
2025-05-23 19:30:53,775 - INFO - 开始处理日期: 2025-05-22
2025-05-23 19:30:53,775 - INFO - Request Parameters - Page 1:
2025-05-23 19:30:53,775 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 19:30:53,775 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 19:31:01,807 - INFO - Response - Page 1:
2025-05-23 19:31:01,807 - INFO - 第 1 页获取到 100 条记录
2025-05-23 19:31:02,010 - INFO - Request Parameters - Page 2:
2025-05-23 19:31:02,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 19:31:02,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 19:31:02,822 - INFO - Response - Page 2:
2025-05-23 19:31:02,822 - INFO - 第 2 页获取到 100 条记录
2025-05-23 19:31:03,025 - INFO - Request Parameters - Page 3:
2025-05-23 19:31:03,025 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 19:31:03,025 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 19:31:03,838 - INFO - Response - Page 3:
2025-05-23 19:31:03,838 - INFO - 第 3 页获取到 100 条记录
2025-05-23 19:31:04,041 - INFO - Request Parameters - Page 4:
2025-05-23 19:31:04,041 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 19:31:04,041 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 19:31:04,901 - INFO - Response - Page 4:
2025-05-23 19:31:04,901 - INFO - 第 4 页获取到 100 条记录
2025-05-23 19:31:05,104 - INFO - Request Parameters - Page 5:
2025-05-23 19:31:05,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 19:31:05,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 19:31:05,948 - INFO - Response - Page 5:
2025-05-23 19:31:05,948 - INFO - 第 5 页获取到 100 条记录
2025-05-23 19:31:06,151 - INFO - Request Parameters - Page 6:
2025-05-23 19:31:06,151 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 19:31:06,151 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 19:31:07,026 - INFO - Response - Page 6:
2025-05-23 19:31:07,026 - INFO - 第 6 页获取到 100 条记录
2025-05-23 19:31:07,229 - INFO - Request Parameters - Page 7:
2025-05-23 19:31:07,229 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 19:31:07,229 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 19:31:07,854 - INFO - Response - Page 7:
2025-05-23 19:31:07,854 - INFO - 第 7 页获取到 15 条记录
2025-05-23 19:31:08,057 - INFO - 查询完成，共获取到 615 条记录
2025-05-23 19:31:08,057 - INFO - 获取到 615 条表单数据
2025-05-23 19:31:08,057 - INFO - 当前日期 2025-05-22 有 214 条MySQL数据需要处理
2025-05-23 19:31:08,057 - INFO - 日期 2025-05-22 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 19:31:08,057 - INFO - 开始处理日期: 2025-05-23
2025-05-23 19:31:08,057 - INFO - Request Parameters - Page 1:
2025-05-23 19:31:08,057 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 19:31:08,057 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 19:31:08,588 - INFO - Response - Page 1:
2025-05-23 19:31:08,588 - INFO - 第 1 页获取到 1 条记录
2025-05-23 19:31:08,792 - INFO - 查询完成，共获取到 1 条记录
2025-05-23 19:31:08,792 - INFO - 获取到 1 条表单数据
2025-05-23 19:31:08,792 - INFO - 当前日期 2025-05-23 有 1 条MySQL数据需要处理
2025-05-23 19:31:08,792 - INFO - 日期 2025-05-23 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 19:31:08,792 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-23 19:32:08,813 - INFO - 开始同步昨天与今天的销售数据: 2025-05-22 至 2025-05-23
2025-05-23 19:32:08,813 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 19:32:08,813 - INFO - 查询参数: ('2025-05-22', '2025-05-23')
2025-05-23 19:32:08,891 - INFO - MySQL查询成功，时间段: 2025-05-22 至 2025-05-23，共获取 616 条记录
2025-05-23 19:32:08,891 - INFO - 获取到 2 个日期需要处理: ['2025-05-22', '2025-05-23']
2025-05-23 19:32:08,891 - INFO - 开始处理日期: 2025-05-22
2025-05-23 19:32:08,907 - INFO - Request Parameters - Page 1:
2025-05-23 19:32:08,907 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 19:32:08,907 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 19:32:09,766 - INFO - Response - Page 1:
2025-05-23 19:32:09,766 - INFO - 第 1 页获取到 100 条记录
2025-05-23 19:32:09,969 - INFO - Request Parameters - Page 2:
2025-05-23 19:32:09,969 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 19:32:09,969 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 19:32:10,782 - INFO - Response - Page 2:
2025-05-23 19:32:10,782 - INFO - 第 2 页获取到 100 条记录
2025-05-23 19:32:10,985 - INFO - Request Parameters - Page 3:
2025-05-23 19:32:10,985 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 19:32:10,985 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 19:32:11,844 - INFO - Response - Page 3:
2025-05-23 19:32:11,844 - INFO - 第 3 页获取到 100 条记录
2025-05-23 19:32:12,048 - INFO - Request Parameters - Page 4:
2025-05-23 19:32:12,048 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 19:32:12,048 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 19:32:12,860 - INFO - Response - Page 4:
2025-05-23 19:32:12,860 - INFO - 第 4 页获取到 100 条记录
2025-05-23 19:32:13,063 - INFO - Request Parameters - Page 5:
2025-05-23 19:32:13,063 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 19:32:13,063 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 19:32:13,860 - INFO - Response - Page 5:
2025-05-23 19:32:13,860 - INFO - 第 5 页获取到 100 条记录
2025-05-23 19:32:14,063 - INFO - Request Parameters - Page 6:
2025-05-23 19:32:14,063 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 19:32:14,063 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 19:32:14,970 - INFO - Response - Page 6:
2025-05-23 19:32:14,985 - INFO - 第 6 页获取到 100 条记录
2025-05-23 19:32:15,189 - INFO - Request Parameters - Page 7:
2025-05-23 19:32:15,189 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 19:32:15,189 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 19:32:15,782 - INFO - Response - Page 7:
2025-05-23 19:32:15,782 - INFO - 第 7 页获取到 15 条记录
2025-05-23 19:32:15,986 - INFO - 查询完成，共获取到 615 条记录
2025-05-23 19:32:15,986 - INFO - 获取到 615 条表单数据
2025-05-23 19:32:15,986 - INFO - 当前日期 2025-05-22 有 615 条MySQL数据需要处理
2025-05-23 19:32:16,001 - INFO - 日期 2025-05-22 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 19:32:16,001 - INFO - 开始处理日期: 2025-05-23
2025-05-23 19:32:16,001 - INFO - Request Parameters - Page 1:
2025-05-23 19:32:16,001 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 19:32:16,001 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 19:32:16,517 - INFO - Response - Page 1:
2025-05-23 19:32:16,517 - INFO - 第 1 页获取到 1 条记录
2025-05-23 19:32:16,720 - INFO - 查询完成，共获取到 1 条记录
2025-05-23 19:32:16,720 - INFO - 获取到 1 条表单数据
2025-05-23 19:32:16,720 - INFO - 当前日期 2025-05-23 有 1 条MySQL数据需要处理
2025-05-23 19:32:16,720 - INFO - 日期 2025-05-23 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 19:32:16,720 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 19:32:16,720 - INFO - 同步完成
2025-05-23 20:30:34,400 - INFO - 使用默认增量同步（当天更新数据）
2025-05-23 20:30:34,400 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 20:30:34,400 - INFO - 查询参数: ('2025-05-23',)
2025-05-23 20:30:34,478 - INFO - MySQL查询成功，增量数据（日期: 2025-05-23），共获取 221 条记录
2025-05-23 20:30:34,478 - INFO - 获取到 4 个日期需要处理: ['2025-05-14', '2025-05-21', '2025-05-22', '2025-05-23']
2025-05-23 20:30:34,478 - INFO - 开始处理日期: 2025-05-14
2025-05-23 20:30:34,493 - INFO - Request Parameters - Page 1:
2025-05-23 20:30:34,493 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 20:30:34,493 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 20:30:42,603 - ERROR - 处理日期 2025-05-14 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 55BE6484-267F-7898-A4F1-52B2DDDD39BB Response: {'code': 'ServiceUnavailable', 'requestid': '55BE6484-267F-7898-A4F1-52B2DDDD39BB', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 55BE6484-267F-7898-A4F1-52B2DDDD39BB)
2025-05-23 20:30:42,603 - INFO - 开始处理日期: 2025-05-21
2025-05-23 20:30:42,603 - INFO - Request Parameters - Page 1:
2025-05-23 20:30:42,603 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 20:30:42,603 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 20:30:50,714 - ERROR - 处理日期 2025-05-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 8B8D76F4-AFED-734D-978A-2BC87349B17D Response: {'code': 'ServiceUnavailable', 'requestid': '8B8D76F4-AFED-734D-978A-2BC87349B17D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 8B8D76F4-AFED-734D-978A-2BC87349B17D)
2025-05-23 20:30:50,714 - INFO - 开始处理日期: 2025-05-22
2025-05-23 20:30:50,714 - INFO - Request Parameters - Page 1:
2025-05-23 20:30:50,714 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 20:30:50,714 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 20:30:51,604 - INFO - Response - Page 1:
2025-05-23 20:30:51,604 - INFO - 第 1 页获取到 100 条记录
2025-05-23 20:30:51,807 - INFO - Request Parameters - Page 2:
2025-05-23 20:30:51,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 20:30:51,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 20:30:52,604 - INFO - Response - Page 2:
2025-05-23 20:30:52,604 - INFO - 第 2 页获取到 100 条记录
2025-05-23 20:30:52,808 - INFO - Request Parameters - Page 3:
2025-05-23 20:30:52,808 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 20:30:52,808 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 20:30:58,902 - INFO - Response - Page 3:
2025-05-23 20:30:58,902 - INFO - 第 3 页获取到 100 条记录
2025-05-23 20:30:59,105 - INFO - Request Parameters - Page 4:
2025-05-23 20:30:59,105 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 20:30:59,105 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 20:31:00,027 - INFO - Response - Page 4:
2025-05-23 20:31:00,027 - INFO - 第 4 页获取到 100 条记录
2025-05-23 20:31:00,230 - INFO - Request Parameters - Page 5:
2025-05-23 20:31:00,230 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 20:31:00,230 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 20:31:01,043 - INFO - Response - Page 5:
2025-05-23 20:31:01,043 - INFO - 第 5 页获取到 100 条记录
2025-05-23 20:31:01,246 - INFO - Request Parameters - Page 6:
2025-05-23 20:31:01,246 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 20:31:01,246 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 20:31:02,090 - INFO - Response - Page 6:
2025-05-23 20:31:02,090 - INFO - 第 6 页获取到 100 条记录
2025-05-23 20:31:02,293 - INFO - Request Parameters - Page 7:
2025-05-23 20:31:02,293 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 20:31:02,293 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 20:31:02,871 - INFO - Response - Page 7:
2025-05-23 20:31:02,871 - INFO - 第 7 页获取到 15 条记录
2025-05-23 20:31:03,074 - INFO - 查询完成，共获取到 615 条记录
2025-05-23 20:31:03,074 - INFO - 获取到 615 条表单数据
2025-05-23 20:31:03,074 - INFO - 当前日期 2025-05-22 有 214 条MySQL数据需要处理
2025-05-23 20:31:03,074 - INFO - 日期 2025-05-22 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 20:31:03,074 - INFO - 开始处理日期: 2025-05-23
2025-05-23 20:31:03,074 - INFO - Request Parameters - Page 1:
2025-05-23 20:31:03,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 20:31:03,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 20:31:03,574 - INFO - Response - Page 1:
2025-05-23 20:31:03,574 - INFO - 第 1 页获取到 1 条记录
2025-05-23 20:31:03,777 - INFO - 查询完成，共获取到 1 条记录
2025-05-23 20:31:03,777 - INFO - 获取到 1 条表单数据
2025-05-23 20:31:03,777 - INFO - 当前日期 2025-05-23 有 1 条MySQL数据需要处理
2025-05-23 20:31:03,777 - INFO - 日期 2025-05-23 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 20:31:03,777 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-23 20:32:03,799 - INFO - 开始同步昨天与今天的销售数据: 2025-05-22 至 2025-05-23
2025-05-23 20:32:03,799 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 20:32:03,799 - INFO - 查询参数: ('2025-05-22', '2025-05-23')
2025-05-23 20:32:03,893 - INFO - MySQL查询成功，时间段: 2025-05-22 至 2025-05-23，共获取 616 条记录
2025-05-23 20:32:03,893 - INFO - 获取到 2 个日期需要处理: ['2025-05-22', '2025-05-23']
2025-05-23 20:32:03,893 - INFO - 开始处理日期: 2025-05-22
2025-05-23 20:32:03,893 - INFO - Request Parameters - Page 1:
2025-05-23 20:32:03,893 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 20:32:03,893 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 20:32:04,736 - INFO - Response - Page 1:
2025-05-23 20:32:04,736 - INFO - 第 1 页获取到 100 条记录
2025-05-23 20:32:04,940 - INFO - Request Parameters - Page 2:
2025-05-23 20:32:04,940 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 20:32:04,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 20:32:05,815 - INFO - Response - Page 2:
2025-05-23 20:32:05,815 - INFO - 第 2 页获取到 100 条记录
2025-05-23 20:32:06,018 - INFO - Request Parameters - Page 3:
2025-05-23 20:32:06,018 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 20:32:06,018 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 20:32:06,940 - INFO - Response - Page 3:
2025-05-23 20:32:06,940 - INFO - 第 3 页获取到 100 条记录
2025-05-23 20:32:07,143 - INFO - Request Parameters - Page 4:
2025-05-23 20:32:07,143 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 20:32:07,143 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 20:32:08,252 - INFO - Response - Page 4:
2025-05-23 20:32:08,252 - INFO - 第 4 页获取到 100 条记录
2025-05-23 20:32:08,455 - INFO - Request Parameters - Page 5:
2025-05-23 20:32:08,455 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 20:32:08,455 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 20:32:09,221 - INFO - Response - Page 5:
2025-05-23 20:32:09,237 - INFO - 第 5 页获取到 100 条记录
2025-05-23 20:32:09,440 - INFO - Request Parameters - Page 6:
2025-05-23 20:32:09,440 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 20:32:09,440 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 20:32:10,221 - INFO - Response - Page 6:
2025-05-23 20:32:10,221 - INFO - 第 6 页获取到 100 条记录
2025-05-23 20:32:10,424 - INFO - Request Parameters - Page 7:
2025-05-23 20:32:10,424 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 20:32:10,424 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 20:32:11,003 - INFO - Response - Page 7:
2025-05-23 20:32:11,003 - INFO - 第 7 页获取到 15 条记录
2025-05-23 20:32:11,206 - INFO - 查询完成，共获取到 615 条记录
2025-05-23 20:32:11,206 - INFO - 获取到 615 条表单数据
2025-05-23 20:32:11,206 - INFO - 当前日期 2025-05-22 有 615 条MySQL数据需要处理
2025-05-23 20:32:11,221 - INFO - 日期 2025-05-22 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 20:32:11,221 - INFO - 开始处理日期: 2025-05-23
2025-05-23 20:32:11,221 - INFO - Request Parameters - Page 1:
2025-05-23 20:32:11,221 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 20:32:11,221 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 20:32:11,737 - INFO - Response - Page 1:
2025-05-23 20:32:11,737 - INFO - 第 1 页获取到 1 条记录
2025-05-23 20:32:11,940 - INFO - 查询完成，共获取到 1 条记录
2025-05-23 20:32:11,940 - INFO - 获取到 1 条表单数据
2025-05-23 20:32:11,940 - INFO - 当前日期 2025-05-23 有 1 条MySQL数据需要处理
2025-05-23 20:32:11,940 - INFO - 日期 2025-05-23 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 20:32:11,940 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 20:32:11,940 - INFO - 同步完成
2025-05-23 21:30:34,277 - INFO - 使用默认增量同步（当天更新数据）
2025-05-23 21:30:34,277 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 21:30:34,277 - INFO - 查询参数: ('2025-05-23',)
2025-05-23 21:30:34,355 - INFO - MySQL查询成功，增量数据（日期: 2025-05-23），共获取 221 条记录
2025-05-23 21:30:34,355 - INFO - 获取到 4 个日期需要处理: ['2025-05-14', '2025-05-21', '2025-05-22', '2025-05-23']
2025-05-23 21:30:34,355 - INFO - 开始处理日期: 2025-05-14
2025-05-23 21:30:34,355 - INFO - Request Parameters - Page 1:
2025-05-23 21:30:34,355 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:30:34,355 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:30:42,465 - ERROR - 处理日期 2025-05-14 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A6011D47-967F-705E-BC06-5DC896D48625 Response: {'code': 'ServiceUnavailable', 'requestid': 'A6011D47-967F-705E-BC06-5DC896D48625', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A6011D47-967F-705E-BC06-5DC896D48625)
2025-05-23 21:30:42,465 - INFO - 开始处理日期: 2025-05-21
2025-05-23 21:30:42,465 - INFO - Request Parameters - Page 1:
2025-05-23 21:30:42,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:30:42,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:30:50,591 - ERROR - 处理日期 2025-05-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 6FD9DAB8-5152-7B91-A316-B0CFDE19FAC5 Response: {'code': 'ServiceUnavailable', 'requestid': '6FD9DAB8-5152-7B91-A316-B0CFDE19FAC5', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 6FD9DAB8-5152-7B91-A316-B0CFDE19FAC5)
2025-05-23 21:30:50,591 - INFO - 开始处理日期: 2025-05-22
2025-05-23 21:30:50,591 - INFO - Request Parameters - Page 1:
2025-05-23 21:30:50,591 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:30:50,591 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:30:51,388 - INFO - Response - Page 1:
2025-05-23 21:30:51,388 - INFO - 第 1 页获取到 100 条记录
2025-05-23 21:30:51,591 - INFO - Request Parameters - Page 2:
2025-05-23 21:30:51,591 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:30:51,591 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:30:52,419 - INFO - Response - Page 2:
2025-05-23 21:30:52,419 - INFO - 第 2 页获取到 100 条记录
2025-05-23 21:30:52,622 - INFO - Request Parameters - Page 3:
2025-05-23 21:30:52,622 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:30:52,622 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:30:53,435 - INFO - Response - Page 3:
2025-05-23 21:30:53,435 - INFO - 第 3 页获取到 100 条记录
2025-05-23 21:30:53,638 - INFO - Request Parameters - Page 4:
2025-05-23 21:30:53,638 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:30:53,638 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:30:54,450 - INFO - Response - Page 4:
2025-05-23 21:30:54,450 - INFO - 第 4 页获取到 100 条记录
2025-05-23 21:30:54,654 - INFO - Request Parameters - Page 5:
2025-05-23 21:30:54,654 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:30:54,654 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:30:55,497 - INFO - Response - Page 5:
2025-05-23 21:30:55,497 - INFO - 第 5 页获取到 100 条记录
2025-05-23 21:30:55,700 - INFO - Request Parameters - Page 6:
2025-05-23 21:30:55,700 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:30:55,700 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:30:56,451 - INFO - Response - Page 6:
2025-05-23 21:30:56,451 - INFO - 第 6 页获取到 100 条记录
2025-05-23 21:30:56,654 - INFO - Request Parameters - Page 7:
2025-05-23 21:30:56,654 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:30:56,654 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:30:57,263 - INFO - Response - Page 7:
2025-05-23 21:30:57,263 - INFO - 第 7 页获取到 15 条记录
2025-05-23 21:30:57,466 - INFO - 查询完成，共获取到 615 条记录
2025-05-23 21:30:57,466 - INFO - 获取到 615 条表单数据
2025-05-23 21:30:57,466 - INFO - 当前日期 2025-05-22 有 214 条MySQL数据需要处理
2025-05-23 21:30:57,466 - INFO - 日期 2025-05-22 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 21:30:57,466 - INFO - 开始处理日期: 2025-05-23
2025-05-23 21:30:57,466 - INFO - Request Parameters - Page 1:
2025-05-23 21:30:57,466 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:30:57,466 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:30:57,966 - INFO - Response - Page 1:
2025-05-23 21:30:57,966 - INFO - 第 1 页获取到 1 条记录
2025-05-23 21:30:58,169 - INFO - 查询完成，共获取到 1 条记录
2025-05-23 21:30:58,169 - INFO - 获取到 1 条表单数据
2025-05-23 21:30:58,169 - INFO - 当前日期 2025-05-23 有 1 条MySQL数据需要处理
2025-05-23 21:30:58,169 - INFO - 日期 2025-05-23 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 21:30:58,169 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-23 21:31:58,191 - INFO - 开始同步昨天与今天的销售数据: 2025-05-22 至 2025-05-23
2025-05-23 21:31:58,191 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 21:31:58,191 - INFO - 查询参数: ('2025-05-22', '2025-05-23')
2025-05-23 21:31:58,285 - INFO - MySQL查询成功，时间段: 2025-05-22 至 2025-05-23，共获取 616 条记录
2025-05-23 21:31:58,285 - INFO - 获取到 2 个日期需要处理: ['2025-05-22', '2025-05-23']
2025-05-23 21:31:58,285 - INFO - 开始处理日期: 2025-05-22
2025-05-23 21:31:58,285 - INFO - Request Parameters - Page 1:
2025-05-23 21:31:58,285 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:31:58,285 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:31:59,144 - INFO - Response - Page 1:
2025-05-23 21:31:59,144 - INFO - 第 1 页获取到 100 条记录
2025-05-23 21:31:59,347 - INFO - Request Parameters - Page 2:
2025-05-23 21:31:59,347 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:31:59,347 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:32:00,285 - INFO - Response - Page 2:
2025-05-23 21:32:00,285 - INFO - 第 2 页获取到 100 条记录
2025-05-23 21:32:00,488 - INFO - Request Parameters - Page 3:
2025-05-23 21:32:00,488 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:32:00,488 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:32:01,347 - INFO - Response - Page 3:
2025-05-23 21:32:01,347 - INFO - 第 3 页获取到 100 条记录
2025-05-23 21:32:01,551 - INFO - Request Parameters - Page 4:
2025-05-23 21:32:01,551 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:32:01,551 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:32:02,410 - INFO - Response - Page 4:
2025-05-23 21:32:02,410 - INFO - 第 4 页获取到 100 条记录
2025-05-23 21:32:02,613 - INFO - Request Parameters - Page 5:
2025-05-23 21:32:02,613 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:32:02,613 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:32:03,441 - INFO - Response - Page 5:
2025-05-23 21:32:03,441 - INFO - 第 5 页获取到 100 条记录
2025-05-23 21:32:03,645 - INFO - Request Parameters - Page 6:
2025-05-23 21:32:03,645 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:32:03,645 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:32:04,566 - INFO - Response - Page 6:
2025-05-23 21:32:04,566 - INFO - 第 6 页获取到 100 条记录
2025-05-23 21:32:04,770 - INFO - Request Parameters - Page 7:
2025-05-23 21:32:04,770 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:32:04,770 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:32:05,442 - INFO - Response - Page 7:
2025-05-23 21:32:05,442 - INFO - 第 7 页获取到 15 条记录
2025-05-23 21:32:05,645 - INFO - 查询完成，共获取到 615 条记录
2025-05-23 21:32:05,645 - INFO - 获取到 615 条表单数据
2025-05-23 21:32:05,645 - INFO - 当前日期 2025-05-22 有 615 条MySQL数据需要处理
2025-05-23 21:32:05,660 - INFO - 日期 2025-05-22 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 21:32:05,660 - INFO - 开始处理日期: 2025-05-23
2025-05-23 21:32:05,660 - INFO - Request Parameters - Page 1:
2025-05-23 21:32:05,660 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 21:32:05,660 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 21:32:06,207 - INFO - Response - Page 1:
2025-05-23 21:32:06,207 - INFO - 第 1 页获取到 1 条记录
2025-05-23 21:32:06,410 - INFO - 查询完成，共获取到 1 条记录
2025-05-23 21:32:06,410 - INFO - 获取到 1 条表单数据
2025-05-23 21:32:06,410 - INFO - 当前日期 2025-05-23 有 1 条MySQL数据需要处理
2025-05-23 21:32:06,410 - INFO - 日期 2025-05-23 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 21:32:06,410 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 21:32:06,410 - INFO - 同步完成
2025-05-23 22:30:33,450 - INFO - 使用默认增量同步（当天更新数据）
2025-05-23 22:30:33,466 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 22:30:33,466 - INFO - 查询参数: ('2025-05-23',)
2025-05-23 22:30:33,528 - INFO - MySQL查询成功，增量数据（日期: 2025-05-23），共获取 245 条记录
2025-05-23 22:30:33,528 - INFO - 获取到 4 个日期需要处理: ['2025-05-14', '2025-05-21', '2025-05-22', '2025-05-23']
2025-05-23 22:30:33,528 - INFO - 开始处理日期: 2025-05-14
2025-05-23 22:30:33,544 - INFO - Request Parameters - Page 1:
2025-05-23 22:30:33,544 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 22:30:33,544 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 22:30:41,654 - ERROR - 处理日期 2025-05-14 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: ********-016F-7F4F-9FAB-7E3D145D67E4 Response: {'code': 'ServiceUnavailable', 'requestid': '********-016F-7F4F-9FAB-7E3D145D67E4', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: ********-016F-7F4F-9FAB-7E3D145D67E4)
2025-05-23 22:30:41,654 - INFO - 开始处理日期: 2025-05-21
2025-05-23 22:30:41,654 - INFO - Request Parameters - Page 1:
2025-05-23 22:30:41,654 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 22:30:41,654 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 22:30:44,248 - INFO - Response - Page 1:
2025-05-23 22:30:44,248 - INFO - 第 1 页获取到 100 条记录
2025-05-23 22:30:44,451 - INFO - Request Parameters - Page 2:
2025-05-23 22:30:44,451 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 22:30:44,451 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 22:30:52,561 - ERROR - 处理日期 2025-05-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 95B05E95-869E-7AA3-AB80-8D00F00E9917 Response: {'code': 'ServiceUnavailable', 'requestid': '95B05E95-869E-7AA3-AB80-8D00F00E9917', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 95B05E95-869E-7AA3-AB80-8D00F00E9917)
2025-05-23 22:30:52,577 - INFO - 开始处理日期: 2025-05-22
2025-05-23 22:30:52,577 - INFO - Request Parameters - Page 1:
2025-05-23 22:30:52,577 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 22:30:52,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 22:30:59,984 - INFO - Response - Page 1:
2025-05-23 22:30:59,984 - INFO - 第 1 页获取到 100 条记录
2025-05-23 22:31:00,187 - INFO - Request Parameters - Page 2:
2025-05-23 22:31:00,187 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 22:31:00,187 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 22:31:01,000 - INFO - Response - Page 2:
2025-05-23 22:31:01,000 - INFO - 第 2 页获取到 100 条记录
2025-05-23 22:31:01,203 - INFO - Request Parameters - Page 3:
2025-05-23 22:31:01,203 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 22:31:01,203 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 22:31:01,953 - INFO - Response - Page 3:
2025-05-23 22:31:01,953 - INFO - 第 3 页获取到 100 条记录
2025-05-23 22:31:02,156 - INFO - Request Parameters - Page 4:
2025-05-23 22:31:02,156 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 22:31:02,156 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 22:31:03,000 - INFO - Response - Page 4:
2025-05-23 22:31:03,000 - INFO - 第 4 页获取到 100 条记录
2025-05-23 22:31:03,203 - INFO - Request Parameters - Page 5:
2025-05-23 22:31:03,203 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 22:31:03,203 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 22:31:04,016 - INFO - Response - Page 5:
2025-05-23 22:31:04,016 - INFO - 第 5 页获取到 100 条记录
2025-05-23 22:31:04,219 - INFO - Request Parameters - Page 6:
2025-05-23 22:31:04,219 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 22:31:04,219 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 22:31:05,141 - INFO - Response - Page 6:
2025-05-23 22:31:05,141 - INFO - 第 6 页获取到 100 条记录
2025-05-23 22:31:05,344 - INFO - Request Parameters - Page 7:
2025-05-23 22:31:05,344 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 22:31:05,344 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 22:31:05,906 - INFO - Response - Page 7:
2025-05-23 22:31:05,906 - INFO - 第 7 页获取到 15 条记录
2025-05-23 22:31:06,110 - INFO - 查询完成，共获取到 615 条记录
2025-05-23 22:31:06,110 - INFO - 获取到 615 条表单数据
2025-05-23 22:31:06,125 - INFO - 当前日期 2025-05-22 有 214 条MySQL数据需要处理
2025-05-23 22:31:06,125 - INFO - 日期 2025-05-22 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 22:31:06,125 - INFO - 开始处理日期: 2025-05-23
2025-05-23 22:31:06,125 - INFO - Request Parameters - Page 1:
2025-05-23 22:31:06,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 22:31:06,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 22:31:06,703 - INFO - Response - Page 1:
2025-05-23 22:31:06,703 - INFO - 第 1 页获取到 1 条记录
2025-05-23 22:31:06,907 - INFO - 查询完成，共获取到 1 条记录
2025-05-23 22:31:06,907 - INFO - 获取到 1 条表单数据
2025-05-23 22:31:06,907 - INFO - 当前日期 2025-05-23 有 25 条MySQL数据需要处理
2025-05-23 22:31:06,907 - INFO - 开始批量插入 24 条新记录
2025-05-23 22:31:07,063 - INFO - 批量插入响应状态码: 200
2025-05-23 22:31:07,063 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 23 May 2025 14:31:06 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1140', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6AAC3C72-72A9-7214-B689-A1EB78DFCA33', 'x-acs-trace-id': '6e29fca5820fe4b1f87da654ca8e7774', 'etag': '1ohGPYMOPNUi+ywgLpOSozw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-23 22:31:07,063 - INFO - 批量插入响应体: {'result': ['FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BM2', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BM3', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BM4', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BM5', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BM6', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BM7', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BM8', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BM9', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BMA', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BMB', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BMC', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BMD', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BME', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BMF', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BMG', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BMH', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BMI', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BMJ', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BMK', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BML', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BMM', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BMN', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BMO', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BMP']}
2025-05-23 22:31:07,063 - INFO - 批量插入表单数据成功，批次 1，共 24 条记录
2025-05-23 22:31:07,063 - INFO - 成功插入的数据ID: ['FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BM2', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BM3', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BM4', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BM5', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BM6', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BM7', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BM8', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BM9', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BMA', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BMB', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BMC', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BMD', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BME', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BMF', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BMG', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BMH', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BMI', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BMJ', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BMK', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BML', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BMM', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BMN', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BMO', 'FINST-3PF66271PLOVCU5T6GASB9QS9BAH3Y02FW0BMP']
2025-05-23 22:31:12,079 - INFO - 批量插入完成，共 24 条记录
2025-05-23 22:31:12,079 - INFO - 日期 2025-05-23 处理完成 - 更新: 0 条，插入: 24 条，错误: 0 条
2025-05-23 22:31:12,079 - INFO - 数据同步完成！更新: 0 条，插入: 24 条，错误: 2 条
2025-05-23 22:32:12,100 - INFO - 开始同步昨天与今天的销售数据: 2025-05-22 至 2025-05-23
2025-05-23 22:32:12,100 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 22:32:12,100 - INFO - 查询参数: ('2025-05-22', '2025-05-23')
2025-05-23 22:32:12,178 - INFO - MySQL查询成功，时间段: 2025-05-22 至 2025-05-23，共获取 640 条记录
2025-05-23 22:32:12,178 - INFO - 获取到 2 个日期需要处理: ['2025-05-22', '2025-05-23']
2025-05-23 22:32:12,178 - INFO - 开始处理日期: 2025-05-22
2025-05-23 22:32:12,178 - INFO - Request Parameters - Page 1:
2025-05-23 22:32:12,178 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 22:32:12,194 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 22:32:12,991 - INFO - Response - Page 1:
2025-05-23 22:32:12,991 - INFO - 第 1 页获取到 100 条记录
2025-05-23 22:32:13,194 - INFO - Request Parameters - Page 2:
2025-05-23 22:32:13,194 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 22:32:13,194 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 22:32:13,991 - INFO - Response - Page 2:
2025-05-23 22:32:13,991 - INFO - 第 2 页获取到 100 条记录
2025-05-23 22:32:14,194 - INFO - Request Parameters - Page 3:
2025-05-23 22:32:14,194 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 22:32:14,194 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 22:32:14,960 - INFO - Response - Page 3:
2025-05-23 22:32:14,960 - INFO - 第 3 页获取到 100 条记录
2025-05-23 22:32:15,163 - INFO - Request Parameters - Page 4:
2025-05-23 22:32:15,163 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 22:32:15,163 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 22:32:15,960 - INFO - Response - Page 4:
2025-05-23 22:32:15,960 - INFO - 第 4 页获取到 100 条记录
2025-05-23 22:32:16,163 - INFO - Request Parameters - Page 5:
2025-05-23 22:32:16,163 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 22:32:16,163 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 22:32:16,929 - INFO - Response - Page 5:
2025-05-23 22:32:16,929 - INFO - 第 5 页获取到 100 条记录
2025-05-23 22:32:17,132 - INFO - Request Parameters - Page 6:
2025-05-23 22:32:17,132 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 22:32:17,132 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 22:32:17,929 - INFO - Response - Page 6:
2025-05-23 22:32:17,929 - INFO - 第 6 页获取到 100 条记录
2025-05-23 22:32:18,132 - INFO - Request Parameters - Page 7:
2025-05-23 22:32:18,132 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 22:32:18,132 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 22:32:18,742 - INFO - Response - Page 7:
2025-05-23 22:32:18,742 - INFO - 第 7 页获取到 15 条记录
2025-05-23 22:32:18,945 - INFO - 查询完成，共获取到 615 条记录
2025-05-23 22:32:18,945 - INFO - 获取到 615 条表单数据
2025-05-23 22:32:18,945 - INFO - 当前日期 2025-05-22 有 615 条MySQL数据需要处理
2025-05-23 22:32:18,960 - INFO - 日期 2025-05-22 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 22:32:18,960 - INFO - 开始处理日期: 2025-05-23
2025-05-23 22:32:18,960 - INFO - Request Parameters - Page 1:
2025-05-23 22:32:18,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 22:32:18,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 22:32:19,632 - INFO - Response - Page 1:
2025-05-23 22:32:19,632 - INFO - 第 1 页获取到 25 条记录
2025-05-23 22:32:19,835 - INFO - 查询完成，共获取到 25 条记录
2025-05-23 22:32:19,835 - INFO - 获取到 25 条表单数据
2025-05-23 22:32:19,835 - INFO - 当前日期 2025-05-23 有 25 条MySQL数据需要处理
2025-05-23 22:32:19,835 - INFO - 日期 2025-05-23 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 22:32:19,835 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 22:32:19,835 - INFO - 同步完成
2025-05-23 23:30:32,637 - INFO - 使用默认增量同步（当天更新数据）
2025-05-23 23:30:32,637 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 23:30:32,637 - INFO - 查询参数: ('2025-05-23',)
2025-05-23 23:30:32,715 - INFO - MySQL查询成功，增量数据（日期: 2025-05-23），共获取 251 条记录
2025-05-23 23:30:32,715 - INFO - 获取到 4 个日期需要处理: ['2025-05-14', '2025-05-21', '2025-05-22', '2025-05-23']
2025-05-23 23:30:32,715 - INFO - 开始处理日期: 2025-05-14
2025-05-23 23:30:32,715 - INFO - Request Parameters - Page 1:
2025-05-23 23:30:32,715 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 23:30:32,715 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747152000000, 1747238399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 23:30:40,840 - ERROR - 处理日期 2025-05-14 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: FC353DD2-22E4-7D88-97A0-5571B97C634A Response: {'code': 'ServiceUnavailable', 'requestid': 'FC353DD2-22E4-7D88-97A0-5571B97C634A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: FC353DD2-22E4-7D88-97A0-5571B97C634A)
2025-05-23 23:30:40,840 - INFO - 开始处理日期: 2025-05-21
2025-05-23 23:30:40,840 - INFO - Request Parameters - Page 1:
2025-05-23 23:30:40,840 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 23:30:40,840 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 23:30:48,935 - ERROR - 处理日期 2025-05-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 68CEF2E9-9CF3-74DA-9084-D2413903C1A7 Response: {'code': 'ServiceUnavailable', 'requestid': '68CEF2E9-9CF3-74DA-9084-D2413903C1A7', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 68CEF2E9-9CF3-74DA-9084-D2413903C1A7)
2025-05-23 23:30:48,935 - INFO - 开始处理日期: 2025-05-22
2025-05-23 23:30:48,935 - INFO - Request Parameters - Page 1:
2025-05-23 23:30:48,935 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 23:30:48,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 23:30:51,200 - INFO - Response - Page 1:
2025-05-23 23:30:51,200 - INFO - 第 1 页获取到 100 条记录
2025-05-23 23:30:51,403 - INFO - Request Parameters - Page 2:
2025-05-23 23:30:51,403 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 23:30:51,403 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 23:30:52,216 - INFO - Response - Page 2:
2025-05-23 23:30:52,216 - INFO - 第 2 页获取到 100 条记录
2025-05-23 23:30:52,419 - INFO - Request Parameters - Page 3:
2025-05-23 23:30:52,419 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 23:30:52,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 23:30:53,232 - INFO - Response - Page 3:
2025-05-23 23:30:53,232 - INFO - 第 3 页获取到 100 条记录
2025-05-23 23:30:53,435 - INFO - Request Parameters - Page 4:
2025-05-23 23:30:53,435 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 23:30:53,435 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 23:30:54,263 - INFO - Response - Page 4:
2025-05-23 23:30:54,263 - INFO - 第 4 页获取到 100 条记录
2025-05-23 23:30:54,466 - INFO - Request Parameters - Page 5:
2025-05-23 23:30:54,466 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 23:30:54,466 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 23:30:55,513 - INFO - Response - Page 5:
2025-05-23 23:30:55,513 - INFO - 第 5 页获取到 100 条记录
2025-05-23 23:30:55,716 - INFO - Request Parameters - Page 6:
2025-05-23 23:30:55,716 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 23:30:55,716 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 23:30:56,560 - INFO - Response - Page 6:
2025-05-23 23:30:56,560 - INFO - 第 6 页获取到 100 条记录
2025-05-23 23:30:56,763 - INFO - Request Parameters - Page 7:
2025-05-23 23:30:56,763 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 23:30:56,763 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 23:30:57,388 - INFO - Response - Page 7:
2025-05-23 23:30:57,388 - INFO - 第 7 页获取到 15 条记录
2025-05-23 23:30:57,591 - INFO - 查询完成，共获取到 615 条记录
2025-05-23 23:30:57,591 - INFO - 获取到 615 条表单数据
2025-05-23 23:30:57,591 - INFO - 当前日期 2025-05-22 有 214 条MySQL数据需要处理
2025-05-23 23:30:57,591 - INFO - 日期 2025-05-22 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 23:30:57,591 - INFO - 开始处理日期: 2025-05-23
2025-05-23 23:30:57,591 - INFO - Request Parameters - Page 1:
2025-05-23 23:30:57,591 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 23:30:57,591 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 23:30:58,279 - INFO - Response - Page 1:
2025-05-23 23:30:58,279 - INFO - 第 1 页获取到 25 条记录
2025-05-23 23:30:58,482 - INFO - 查询完成，共获取到 25 条记录
2025-05-23 23:30:58,482 - INFO - 获取到 25 条表单数据
2025-05-23 23:30:58,482 - INFO - 当前日期 2025-05-23 有 31 条MySQL数据需要处理
2025-05-23 23:30:58,482 - INFO - 开始批量插入 6 条新记录
2025-05-23 23:30:58,623 - INFO - 批量插入响应状态码: 200
2025-05-23 23:30:58,623 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 23 May 2025 15:30:58 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '300', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0EA88ADD-E5CC-7632-964F-D7B969669E8E', 'x-acs-trace-id': '48127a6789098e8f2f9d63689d9887a5', 'etag': '3+ymhfO+I+kZvk0OCW+N8Aw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-23 23:30:58,623 - INFO - 批量插入响应体: {'result': ['FINST-2PF66TC1D5MV32Y9C7Z3Y5I2DJ452P02KY0BMP3', 'FINST-2PF66TC1D5MV32Y9C7Z3Y5I2DJ452P02KY0BMQ3', 'FINST-2PF66TC1D5MV32Y9C7Z3Y5I2DJ452P02KY0BMR3', 'FINST-2PF66TC1D5MV32Y9C7Z3Y5I2DJ452P02KY0BMS3', 'FINST-2PF66TC1D5MV32Y9C7Z3Y5I2DJ452P02KY0BMT3', 'FINST-2PF66TC1D5MV32Y9C7Z3Y5I2DJ452P02KY0BMU3']}
2025-05-23 23:30:58,623 - INFO - 批量插入表单数据成功，批次 1，共 6 条记录
2025-05-23 23:30:58,623 - INFO - 成功插入的数据ID: ['FINST-2PF66TC1D5MV32Y9C7Z3Y5I2DJ452P02KY0BMP3', 'FINST-2PF66TC1D5MV32Y9C7Z3Y5I2DJ452P02KY0BMQ3', 'FINST-2PF66TC1D5MV32Y9C7Z3Y5I2DJ452P02KY0BMR3', 'FINST-2PF66TC1D5MV32Y9C7Z3Y5I2DJ452P02KY0BMS3', 'FINST-2PF66TC1D5MV32Y9C7Z3Y5I2DJ452P02KY0BMT3', 'FINST-2PF66TC1D5MV32Y9C7Z3Y5I2DJ452P02KY0BMU3']
2025-05-23 23:31:03,638 - INFO - 批量插入完成，共 6 条记录
2025-05-23 23:31:03,638 - INFO - 日期 2025-05-23 处理完成 - 更新: 0 条，插入: 6 条，错误: 0 条
2025-05-23 23:31:03,638 - INFO - 数据同步完成！更新: 0 条，插入: 6 条，错误: 2 条
2025-05-23 23:32:03,657 - INFO - 开始同步昨天与今天的销售数据: 2025-05-22 至 2025-05-23
2025-05-23 23:32:03,657 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-23 23:32:03,657 - INFO - 查询参数: ('2025-05-22', '2025-05-23')
2025-05-23 23:32:03,735 - INFO - MySQL查询成功，时间段: 2025-05-22 至 2025-05-23，共获取 646 条记录
2025-05-23 23:32:03,735 - INFO - 获取到 2 个日期需要处理: ['2025-05-22', '2025-05-23']
2025-05-23 23:32:03,735 - INFO - 开始处理日期: 2025-05-22
2025-05-23 23:32:03,735 - INFO - Request Parameters - Page 1:
2025-05-23 23:32:03,735 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 23:32:03,735 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 23:32:04,564 - INFO - Response - Page 1:
2025-05-23 23:32:04,564 - INFO - 第 1 页获取到 100 条记录
2025-05-23 23:32:04,767 - INFO - Request Parameters - Page 2:
2025-05-23 23:32:04,767 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 23:32:04,767 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 23:32:05,564 - INFO - Response - Page 2:
2025-05-23 23:32:05,564 - INFO - 第 2 页获取到 100 条记录
2025-05-23 23:32:05,767 - INFO - Request Parameters - Page 3:
2025-05-23 23:32:05,767 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 23:32:05,767 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 23:32:06,486 - INFO - Response - Page 3:
2025-05-23 23:32:06,486 - INFO - 第 3 页获取到 100 条记录
2025-05-23 23:32:06,689 - INFO - Request Parameters - Page 4:
2025-05-23 23:32:06,689 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 23:32:06,689 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 23:32:07,564 - INFO - Response - Page 4:
2025-05-23 23:32:07,564 - INFO - 第 4 页获取到 100 条记录
2025-05-23 23:32:07,767 - INFO - Request Parameters - Page 5:
2025-05-23 23:32:07,767 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 23:32:07,767 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 23:32:08,611 - INFO - Response - Page 5:
2025-05-23 23:32:08,611 - INFO - 第 5 页获取到 100 条记录
2025-05-23 23:32:08,814 - INFO - Request Parameters - Page 6:
2025-05-23 23:32:08,814 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 23:32:08,814 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 23:32:09,580 - INFO - Response - Page 6:
2025-05-23 23:32:09,580 - INFO - 第 6 页获取到 100 条记录
2025-05-23 23:32:09,783 - INFO - Request Parameters - Page 7:
2025-05-23 23:32:09,783 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 23:32:09,783 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747843200000, 1747929599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 23:32:10,408 - INFO - Response - Page 7:
2025-05-23 23:32:10,408 - INFO - 第 7 页获取到 15 条记录
2025-05-23 23:32:10,611 - INFO - 查询完成，共获取到 615 条记录
2025-05-23 23:32:10,611 - INFO - 获取到 615 条表单数据
2025-05-23 23:32:10,611 - INFO - 当前日期 2025-05-22 有 615 条MySQL数据需要处理
2025-05-23 23:32:10,626 - INFO - 日期 2025-05-22 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 23:32:10,626 - INFO - 开始处理日期: 2025-05-23
2025-05-23 23:32:10,626 - INFO - Request Parameters - Page 1:
2025-05-23 23:32:10,626 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-23 23:32:10,626 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747929600000, 1748015999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-23 23:32:11,283 - INFO - Response - Page 1:
2025-05-23 23:32:11,283 - INFO - 第 1 页获取到 31 条记录
2025-05-23 23:32:11,486 - INFO - 查询完成，共获取到 31 条记录
2025-05-23 23:32:11,486 - INFO - 获取到 31 条表单数据
2025-05-23 23:32:11,486 - INFO - 当前日期 2025-05-23 有 31 条MySQL数据需要处理
2025-05-23 23:32:11,486 - INFO - 日期 2025-05-23 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 23:32:11,486 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-23 23:32:11,486 - INFO - 同步完成
