2025-07-11 01:30:33,529 - INFO - 使用默认增量同步（当天更新数据）
2025-07-11 01:30:33,529 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-11 01:30:33,545 - INFO - 查询参数: ('2025-07-11',)
2025-07-11 01:30:33,623 - INFO - MySQL查询成功，增量数据（日期: 2025-07-11），共获取 0 条记录
2025-07-11 01:30:33,623 - ERROR - 未获取到MySQL数据
2025-07-11 01:31:33,638 - INFO - 开始同步昨天与今天的销售数据: 2025-07-10 至 2025-07-11
2025-07-11 01:31:33,638 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-11 01:31:33,638 - INFO - 查询参数: ('2025-07-10', '2025-07-11')
2025-07-11 01:31:33,779 - INFO - MySQL查询成功，时间段: 2025-07-10 至 2025-07-11，共获取 102 条记录
2025-07-11 01:31:33,779 - INFO - 获取到 1 个日期需要处理: ['2025-07-10']
2025-07-11 01:31:33,779 - INFO - 开始处理日期: 2025-07-10
2025-07-11 01:31:33,779 - INFO - Request Parameters - Page 1:
2025-07-11 01:31:33,779 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 01:31:33,779 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 01:31:41,013 - INFO - Response - Page 1:
2025-07-11 01:31:41,013 - INFO - 第 1 页获取到 50 条记录
2025-07-11 01:31:41,529 - INFO - Request Parameters - Page 2:
2025-07-11 01:31:41,529 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 01:31:41,529 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 01:31:49,654 - ERROR - 处理日期 2025-07-10 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 6A9F4D75-9632-78BA-921A-08AD23D5CA00 Response: {'code': 'ServiceUnavailable', 'requestid': '6A9F4D75-9632-78BA-921A-08AD23D5CA00', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 6A9F4D75-9632-78BA-921A-08AD23D5CA00)
2025-07-11 01:31:49,654 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-11 01:31:49,654 - INFO - 同步完成
2025-07-11 04:30:33,505 - INFO - 使用默认增量同步（当天更新数据）
2025-07-11 04:30:33,505 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-11 04:30:33,505 - INFO - 查询参数: ('2025-07-11',)
2025-07-11 04:30:33,599 - INFO - MySQL查询成功，增量数据（日期: 2025-07-11），共获取 0 条记录
2025-07-11 04:30:33,599 - ERROR - 未获取到MySQL数据
2025-07-11 04:31:33,614 - INFO - 开始同步昨天与今天的销售数据: 2025-07-10 至 2025-07-11
2025-07-11 04:31:33,614 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-11 04:31:33,614 - INFO - 查询参数: ('2025-07-10', '2025-07-11')
2025-07-11 04:31:33,755 - INFO - MySQL查询成功，时间段: 2025-07-10 至 2025-07-11，共获取 102 条记录
2025-07-11 04:31:33,755 - INFO - 获取到 1 个日期需要处理: ['2025-07-10']
2025-07-11 04:31:33,755 - INFO - 开始处理日期: 2025-07-10
2025-07-11 04:31:33,755 - INFO - Request Parameters - Page 1:
2025-07-11 04:31:33,755 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 04:31:33,755 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 04:31:41,880 - ERROR - 处理日期 2025-07-10 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 830C2AF9-491A-7487-B966-22C938430C4D Response: {'code': 'ServiceUnavailable', 'requestid': '830C2AF9-491A-7487-B966-22C938430C4D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 830C2AF9-491A-7487-B966-22C938430C4D)
2025-07-11 04:31:41,880 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-11 04:31:41,880 - INFO - 同步完成
2025-07-11 07:30:33,856 - INFO - 使用默认增量同步（当天更新数据）
2025-07-11 07:30:33,856 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-11 07:30:33,856 - INFO - 查询参数: ('2025-07-11',)
2025-07-11 07:30:33,997 - INFO - MySQL查询成功，增量数据（日期: 2025-07-11），共获取 3 条记录
2025-07-11 07:30:33,997 - INFO - 获取到 1 个日期需要处理: ['2025-07-10']
2025-07-11 07:30:33,997 - INFO - 开始处理日期: 2025-07-10
2025-07-11 07:30:33,997 - INFO - Request Parameters - Page 1:
2025-07-11 07:30:33,997 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 07:30:33,997 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 07:30:42,122 - ERROR - 处理日期 2025-07-10 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 6377CDCF-0950-78B5-B319-ECF55AA3E06C Response: {'code': 'ServiceUnavailable', 'requestid': '6377CDCF-0950-78B5-B319-ECF55AA3E06C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 6377CDCF-0950-78B5-B319-ECF55AA3E06C)
2025-07-11 07:30:42,122 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-11 07:31:42,137 - INFO - 开始同步昨天与今天的销售数据: 2025-07-10 至 2025-07-11
2025-07-11 07:31:42,137 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-11 07:31:42,137 - INFO - 查询参数: ('2025-07-10', '2025-07-11')
2025-07-11 07:31:42,278 - INFO - MySQL查询成功，时间段: 2025-07-10 至 2025-07-11，共获取 117 条记录
2025-07-11 07:31:42,278 - INFO - 获取到 1 个日期需要处理: ['2025-07-10']
2025-07-11 07:31:42,278 - INFO - 开始处理日期: 2025-07-10
2025-07-11 07:31:42,278 - INFO - Request Parameters - Page 1:
2025-07-11 07:31:42,278 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 07:31:42,278 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 07:31:48,996 - INFO - Response - Page 1:
2025-07-11 07:31:48,996 - INFO - 第 1 页获取到 50 条记录
2025-07-11 07:31:49,496 - INFO - Request Parameters - Page 2:
2025-07-11 07:31:49,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 07:31:49,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 07:31:50,137 - INFO - Response - Page 2:
2025-07-11 07:31:50,137 - INFO - 第 2 页获取到 21 条记录
2025-07-11 07:31:50,653 - INFO - 查询完成，共获取到 71 条记录
2025-07-11 07:31:50,653 - INFO - 获取到 71 条表单数据
2025-07-11 07:31:50,653 - INFO - 当前日期 2025-07-10 有 114 条MySQL数据需要处理
2025-07-11 07:31:50,653 - INFO - 开始批量插入 43 条新记录
2025-07-11 07:31:50,887 - INFO - 批量插入响应状态码: 200
2025-07-11 07:31:50,887 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 10 Jul 2025 23:31:50 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2119', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '364DFECF-81B4-78C4-A112-6921602EE7FD', 'x-acs-trace-id': 'c08900192b5a22b9d6d7aad1e884829d', 'etag': '20+BcOVnhqDVjt281EQdB2g9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-11 07:31:50,887 - INFO - 批量插入响应体: {'result': ['FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM301', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM401', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM501', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM601', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM701', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM801', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM901', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMA01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMB01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMC01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMD01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCME01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMF01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMG01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMH01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMI01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMJ01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMK01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCML01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMM01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMN01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMO01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMP01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMQ01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMR01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMS01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMT01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMU01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMV01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMW01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMX01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMY01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMZ01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM011', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM111', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM211', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM311', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM411', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM511', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM611', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM711', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM811', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM911']}
2025-07-11 07:31:50,887 - INFO - 批量插入表单数据成功，批次 1，共 43 条记录
2025-07-11 07:31:50,887 - INFO - 成功插入的数据ID: ['FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM301', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM401', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM501', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM601', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM701', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM801', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM901', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMA01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMB01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMC01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMD01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCME01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMF01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMG01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMH01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMI01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMJ01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMK01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCML01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMM01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMN01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMO01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMP01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMQ01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMR01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMS01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMT01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMU01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMV01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMW01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMX01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMY01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMZ01', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM011', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM111', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM211', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM311', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM411', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM511', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM611', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM711', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM811', 'FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM911']
2025-07-11 07:31:55,903 - INFO - 批量插入完成，共 43 条记录
2025-07-11 07:31:55,903 - INFO - 日期 2025-07-10 处理完成 - 更新: 0 条，插入: 43 条，错误: 0 条
2025-07-11 07:31:55,903 - INFO - 数据同步完成！更新: 0 条，插入: 43 条，错误: 0 条
2025-07-11 07:31:55,903 - INFO - 同步完成
2025-07-11 10:30:33,503 - INFO - 使用默认增量同步（当天更新数据）
2025-07-11 10:30:33,503 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-11 10:30:33,503 - INFO - 查询参数: ('2025-07-11',)
2025-07-11 10:30:33,644 - INFO - MySQL查询成功，增量数据（日期: 2025-07-11），共获取 170 条记录
2025-07-11 10:30:33,644 - INFO - 获取到 2 个日期需要处理: ['2025-07-09', '2025-07-10']
2025-07-11 10:30:33,644 - INFO - 开始处理日期: 2025-07-09
2025-07-11 10:30:33,659 - INFO - Request Parameters - Page 1:
2025-07-11 10:30:33,659 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 10:30:33,659 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751990400000, 1752076799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 10:30:41,769 - ERROR - 处理日期 2025-07-09 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 641C4F3E-E549-7342-8B39-F0A49E97DF74 Response: {'code': 'ServiceUnavailable', 'requestid': '641C4F3E-E549-7342-8B39-F0A49E97DF74', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 641C4F3E-E549-7342-8B39-F0A49E97DF74)
2025-07-11 10:30:41,769 - INFO - 开始处理日期: 2025-07-10
2025-07-11 10:30:41,769 - INFO - Request Parameters - Page 1:
2025-07-11 10:30:41,769 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 10:30:41,769 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 10:30:49,878 - ERROR - 处理日期 2025-07-10 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: ********-B46E-7DF8-AB4F-AB42CFC597E0 Response: {'code': 'ServiceUnavailable', 'requestid': '********-B46E-7DF8-AB4F-AB42CFC597E0', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: ********-B46E-7DF8-AB4F-AB42CFC597E0)
2025-07-11 10:30:49,878 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-07-11 10:31:49,893 - INFO - 开始同步昨天与今天的销售数据: 2025-07-10 至 2025-07-11
2025-07-11 10:31:49,893 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-11 10:31:49,893 - INFO - 查询参数: ('2025-07-10', '2025-07-11')
2025-07-11 10:31:50,049 - INFO - MySQL查询成功，时间段: 2025-07-10 至 2025-07-11，共获取 441 条记录
2025-07-11 10:31:50,049 - INFO - 获取到 1 个日期需要处理: ['2025-07-10']
2025-07-11 10:31:50,049 - INFO - 开始处理日期: 2025-07-10
2025-07-11 10:31:50,049 - INFO - Request Parameters - Page 1:
2025-07-11 10:31:50,049 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 10:31:50,049 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 10:31:50,893 - INFO - Response - Page 1:
2025-07-11 10:31:50,893 - INFO - 第 1 页获取到 50 条记录
2025-07-11 10:31:51,393 - INFO - Request Parameters - Page 2:
2025-07-11 10:31:51,393 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 10:31:51,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 10:31:52,112 - INFO - Response - Page 2:
2025-07-11 10:31:52,112 - INFO - 第 2 页获取到 50 条记录
2025-07-11 10:31:52,612 - INFO - Request Parameters - Page 3:
2025-07-11 10:31:52,612 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 10:31:52,612 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 10:31:53,190 - INFO - Response - Page 3:
2025-07-11 10:31:53,190 - INFO - 第 3 页获取到 14 条记录
2025-07-11 10:31:53,706 - INFO - 查询完成，共获取到 114 条记录
2025-07-11 10:31:53,706 - INFO - 获取到 114 条表单数据
2025-07-11 10:31:53,706 - INFO - 当前日期 2025-07-10 有 428 条MySQL数据需要处理
2025-07-11 10:31:53,706 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1P61X9Z25AS2PQ5WBPZ7W173ZSHXCMX4
2025-07-11 10:31:54,190 - INFO - 更新表单数据成功: FINST-XL866HB1P61X9Z25AS2PQ5WBPZ7W173ZSHXCMX4
2025-07-11 10:31:54,190 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30797.0, 'new_value': 35426.0}, {'field': 'total_amount', 'old_value': 30797.0, 'new_value': 35426.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 1}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-07-11 10:31:54,190 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM701
2025-07-11 10:31:54,737 - INFO - 更新表单数据成功: FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM701
2025-07-11 10:31:54,737 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 15620.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 15620.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 35}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-07-11 10:31:54,737 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMO01
2025-07-11 10:31:55,378 - INFO - 更新表单数据成功: FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMO01
2025-07-11 10:31:55,378 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14800.0, 'new_value': 34800.0}, {'field': 'total_amount', 'old_value': 14800.0, 'new_value': 34800.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-07-11 10:31:55,378 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMP01
2025-07-11 10:31:55,924 - INFO - 更新表单数据成功: FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMP01
2025-07-11 10:31:55,924 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9800.0, 'new_value': 39800.0}, {'field': 'total_amount', 'old_value': 9800.0, 'new_value': 39800.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-07-11 10:31:55,924 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMQ01
2025-07-11 10:31:56,409 - INFO - 更新表单数据成功: FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCMQ01
2025-07-11 10:31:56,409 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4500.0, 'new_value': 44500.0}, {'field': 'total_amount', 'old_value': 4500.0, 'new_value': 44500.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-07-11 10:31:56,409 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM501
2025-07-11 10:31:56,940 - INFO - 更新表单数据成功: FINST-ZNE66RC1361XI1UK962ABCTOEPXV2ZHCV0YCM501
2025-07-11 10:31:56,940 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33312.0, 'new_value': 43312.0}, {'field': 'offline_amount', 'old_value': 7555.0, 'new_value': 67555.0}, {'field': 'total_amount', 'old_value': 40867.0, 'new_value': 110867.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-07-11 10:31:56,940 - INFO - 开始批量插入 314 条新记录
2025-07-11 10:31:57,237 - INFO - 批量插入响应状态码: 200
2025-07-11 10:31:57,237 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 11 Jul 2025 02:31:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2383', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B6375C2C-315B-75CB-9A71-5B6B7D5689B7', 'x-acs-trace-id': '9cffac5dd394795f82797d23991435af', 'etag': '2jTHDLQtaOY11hRihsEa0NA3', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-11 10:31:57,237 - INFO - 批量插入响应体: {'result': ['FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3FSYA7YCM7', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCM8', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCM9', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMA', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMB', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMC', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMD', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCME', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMF', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMG', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMH', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMI', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMJ', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMK', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCML', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMM', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMN', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMO', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMP', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMQ', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMR', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMS', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMT', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMU', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMV', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMW', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMX', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMY', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMZ', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCM01', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCM11', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCM21', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCM31', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCM41', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCM51', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCM61', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCM71', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCM81', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCM91', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMA1', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMB1', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMC1', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMD1', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCME1', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMF1', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMG1', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMH1', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMI1', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMJ1', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMK1']}
2025-07-11 10:31:57,237 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-11 10:31:57,237 - INFO - 成功插入的数据ID: ['FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3FSYA7YCM7', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCM8', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCM9', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMA', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMB', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMC', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMD', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCME', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMF', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMG', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMH', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMI', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMJ', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMK', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCML', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMM', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMN', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMO', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMP', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMQ', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMR', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMS', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMT', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMU', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMV', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMW', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMX', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMY', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMZ', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCM01', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCM11', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCM21', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCM31', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCM41', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCM51', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCM61', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCM71', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCM81', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCM91', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMA1', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMB1', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMC1', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMD1', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCME1', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMF1', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMG1', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMH1', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMI1', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMJ1', 'FINST-K8C66U611Z1XWOHDFTPJ94FSZ7SA3GSYA7YCMK1']
2025-07-11 10:32:02,518 - INFO - 批量插入响应状态码: 200
2025-07-11 10:32:02,518 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 11 Jul 2025 02:32:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '60C8306B-E425-74D1-BDA3-C86A49C94C02', 'x-acs-trace-id': '084899b3a3184f73af4e037a6c290e50', 'etag': '2yJxQBoDu1cF8GO0ZiBeJRQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-11 10:32:02,518 - INFO - 批量插入响应体: {'result': ['FINST-1T666B91S60XO1UE6P13887KRY7M3VU2B7YCM5C', 'FINST-1T666B91S60XO1UE6P13887KRY7M3VU2B7YCM6C', 'FINST-1T666B91S60XO1UE6P13887KRY7M3VU2B7YCM7C', 'FINST-1T666B91S60XO1UE6P13887KRY7M3VU2B7YCM8C', 'FINST-1T666B91S60XO1UE6P13887KRY7M3VU2B7YCM9C', 'FINST-1T666B91S60XO1UE6P13887KRY7M3VU2B7YCMAC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3VU2B7YCMBC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMCC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMDC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMEC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMFC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMGC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMHC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMIC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMJC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMKC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMLC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMMC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMNC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMOC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMPC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMQC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMRC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMSC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMTC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMUC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMVC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMWC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMXC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMYC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMZC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCM0D', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCM1D', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCM2D', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCM3D', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCM4D', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCM5D', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCM6D', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCM7D', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCM8D', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCM9D', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMAD', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMBD', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMCD', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMDD', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMED', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMFD', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMGD', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMHD', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMID']}
2025-07-11 10:32:02,518 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-07-11 10:32:02,518 - INFO - 成功插入的数据ID: ['FINST-1T666B91S60XO1UE6P13887KRY7M3VU2B7YCM5C', 'FINST-1T666B91S60XO1UE6P13887KRY7M3VU2B7YCM6C', 'FINST-1T666B91S60XO1UE6P13887KRY7M3VU2B7YCM7C', 'FINST-1T666B91S60XO1UE6P13887KRY7M3VU2B7YCM8C', 'FINST-1T666B91S60XO1UE6P13887KRY7M3VU2B7YCM9C', 'FINST-1T666B91S60XO1UE6P13887KRY7M3VU2B7YCMAC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3VU2B7YCMBC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMCC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMDC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMEC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMFC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMGC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMHC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMIC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMJC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMKC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMLC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMMC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMNC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMOC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMPC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMQC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMRC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMSC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMTC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMUC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMVC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMWC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMXC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMYC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMZC', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCM0D', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCM1D', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCM2D', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCM3D', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCM4D', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCM5D', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCM6D', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCM7D', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCM8D', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCM9D', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMAD', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMBD', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMCD', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMDD', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMED', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMFD', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMGD', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMHD', 'FINST-1T666B91S60XO1UE6P13887KRY7M3WU2B7YCMID']
2025-07-11 10:32:07,768 - INFO - 批量插入响应状态码: 200
2025-07-11 10:32:07,768 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 11 Jul 2025 02:32:07 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2462', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '9F082581-E763-7C76-9A47-486F1DFB5E10', 'x-acs-trace-id': '23a0284d9c0c6912f31f7f5caf1e136f', 'etag': '2dF2oieTAiWyZLVUxMi8UHg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-11 10:32:07,768 - INFO - 批量插入响应体: {'result': ['FINST-R8666Q71PUUWOI0BDE23G8O0AI1431X6B7YCMG61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1431X6B7YCMH61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMI61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMJ61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMK61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCML61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMM61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMN61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMO61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMP61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMQ61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMR61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMS61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMT61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMU61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMV61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMW61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMX61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMY61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMZ61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCM071', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCM171', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCM271', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCM371', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCM471', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCM571', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCM671', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCM771', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCM871', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCM971', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMA71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMB71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMC71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMD71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCME71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMF71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMG71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMH71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMI71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMJ71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMK71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCML71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMM71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMN71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMO71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMP71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMQ71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMR71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMS71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMT71']}
2025-07-11 10:32:07,768 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-07-11 10:32:07,768 - INFO - 成功插入的数据ID: ['FINST-R8666Q71PUUWOI0BDE23G8O0AI1431X6B7YCMG61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1431X6B7YCMH61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMI61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMJ61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMK61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCML61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMM61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMN61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMO61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMP61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMQ61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMR61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMS61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMT61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMU61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMV61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMW61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMX61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMY61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMZ61', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCM071', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCM171', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCM271', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCM371', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCM471', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCM571', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCM671', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCM771', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCM871', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCM971', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMA71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMB71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMC71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMD71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCME71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMF71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMG71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMH71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMI71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMJ71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMK71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCML71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMM71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMN71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMO71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMP71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMQ71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMR71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMS71', 'FINST-R8666Q71PUUWOI0BDE23G8O0AI1432X6B7YCMT71']
2025-07-11 10:32:12,987 - INFO - 批量插入响应状态码: 200
2025-07-11 10:32:12,987 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 11 Jul 2025 02:32:12 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D65797B3-C2F7-7CE8-8FF9-40305D813911', 'x-acs-trace-id': '489a95bfb22e320e87f4d6e54e446564', 'etag': '2xGgVlNG5x8zi5uLjuJXswQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-11 10:32:12,987 - INFO - 批量插入响应体: {'result': ['FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMKV', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMLV', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMMV', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMNV', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMOV', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMPV', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMQV', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMRV', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMSV', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMTV', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMUV', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMVV', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMWV', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMXV', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMYV', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMZV', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCM0W', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCM1W', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCM2W', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCM3W', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCM4W', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCM5W', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCM6W', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCM7W', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCM8W', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCM9W', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMAW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMBW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMCW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMDW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMEW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMFW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMGW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMHW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMIW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMJW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMKW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMLW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI435YAB7YCMMW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI435YAB7YCMNW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI435YAB7YCMOW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI435YAB7YCMPW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI435YAB7YCMQW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI435YAB7YCMRW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI435YAB7YCMSW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI435YAB7YCMTW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI435YAB7YCMUW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI435YAB7YCMVW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI435YAB7YCMWW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI435YAB7YCMXW']}
2025-07-11 10:32:12,987 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-07-11 10:32:12,987 - INFO - 成功插入的数据ID: ['FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMKV', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMLV', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMMV', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMNV', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMOV', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMPV', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMQV', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMRV', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMSV', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMTV', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMUV', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMVV', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMWV', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMXV', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMYV', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMZV', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCM0W', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCM1W', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCM2W', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCM3W', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCM4W', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCM5W', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCM6W', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCM7W', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCM8W', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCM9W', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMAW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMBW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMCW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMDW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMEW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMFW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMGW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMHW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMIW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMJW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMKW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI434YAB7YCMLW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI435YAB7YCMMW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI435YAB7YCMNW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI435YAB7YCMOW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI435YAB7YCMPW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI435YAB7YCMQW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI435YAB7YCMRW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI435YAB7YCMSW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI435YAB7YCMTW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI435YAB7YCMUW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI435YAB7YCMVW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI435YAB7YCMWW', 'FINST-TQB66671D41X7QCBAFKKY8Z58BI435YAB7YCMXW']
2025-07-11 10:32:18,221 - INFO - 批量插入响应状态码: 200
2025-07-11 10:32:18,221 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 11 Jul 2025 02:32:18 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '08D0CF1E-532D-7951-866D-0964CF333D5A', 'x-acs-trace-id': '8e98d88c60df17f7e3590b85ac609c84', 'etag': '2aHUeuqO8gHpR6KmlXL3GaA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-11 10:32:18,221 - INFO - 批量插入响应体: {'result': ['FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMA1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMB1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMC1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMD1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCME1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMF1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMG1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMH1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMI1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMJ1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMK1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCML1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMM1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMN1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMO1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMP1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMQ1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMR1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMS1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMT1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMU1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMV1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMW1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMX1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMY1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMZ1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCM02', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCM12', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCM22', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCM32', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCM42', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCM52', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCM62', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCM72', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCM82', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCM92', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMA2', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMB2', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMC2', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMD2', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCME2', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMF2', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMG2', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMH2', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMI2', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMJ2', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMK2', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCML2', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMM2', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMN2']}
2025-07-11 10:32:18,221 - INFO - 批量插入表单数据成功，批次 5，共 50 条记录
2025-07-11 10:32:18,221 - INFO - 成功插入的数据ID: ['FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMA1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMB1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMC1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMD1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCME1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMF1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMG1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMH1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMI1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMJ1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMK1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCML1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMM1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMN1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMO1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMP1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMQ1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMR1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMS1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMT1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMU1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMV1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMW1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMX1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMY1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMZ1', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCM02', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCM12', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCM22', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCM32', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCM42', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCM52', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCM62', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCM72', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCM82', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCM92', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMA2', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMB2', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMC2', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMD2', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCME2', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMF2', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMG2', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMH2', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMI2', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMJ2', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMK2', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCML2', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMM2', 'FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMN2']
2025-07-11 10:32:23,471 - INFO - 批量插入响应状态码: 200
2025-07-11 10:32:23,471 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 11 Jul 2025 02:32:23 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2B850F4F-9A5D-7875-9DFE-E343CE01F63E', 'x-acs-trace-id': '29bdf699a40232c1a5b3a67028f6e8d3', 'etag': '2mqzX+PWm14EqTDG9pLsANg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-11 10:32:23,471 - INFO - 批量插入响应体: {'result': ['FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM3W', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM4W', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM5W', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM6W', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM7W', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM8W', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM9W', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMAW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMBW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMCW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMDW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMEW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMFW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMGW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMHW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMIW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMJW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMKW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMLW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMMW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMNW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMOW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMPW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMQW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMRW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMSW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMTW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMUW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMVW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMWW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMXW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMYW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMZW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM0X', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM1X', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM2X', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM3X', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM4X', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM5X', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM6X', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM7X', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM8X', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM9X', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMAX', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMBX', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMCX', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMDX', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMEX', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMFX', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMGX']}
2025-07-11 10:32:23,471 - INFO - 批量插入表单数据成功，批次 6，共 50 条记录
2025-07-11 10:32:23,471 - INFO - 成功插入的数据ID: ['FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM3W', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM4W', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM5W', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM6W', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM7W', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM8W', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM9W', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMAW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMBW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMCW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMDW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMEW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMFW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMGW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMHW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMIW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMJW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMKW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMLW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMMW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMNW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMOW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMPW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMQW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMRW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMSW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMTW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMUW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMVW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMWW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMXW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMYW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMZW', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM0X', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM1X', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM2X', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM3X', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM4X', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM5X', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM6X', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM7X', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM8X', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM9X', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMAX', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMBX', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMCX', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMDX', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMEX', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMFX', 'FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMGX']
2025-07-11 10:32:28,643 - INFO - 批量插入响应状态码: 200
2025-07-11 10:32:28,643 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 11 Jul 2025 02:32:28 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '670', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'A81B0514-BD8B-779E-AD0A-3434BB724C52', 'x-acs-trace-id': '675d271eac21e59ea7deb1c3da530a9f', 'etag': '6jFSG244dwCbJuY9A0reAQQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-11 10:32:28,643 - INFO - 批量插入响应体: {'result': ['FINST-0KG66Q712Z1XYQJI9GSJQ8Q0ELG7321NB7YCM2', 'FINST-0KG66Q712Z1XYQJI9GSJQ8Q0ELG7321NB7YCM3', 'FINST-0KG66Q712Z1XYQJI9GSJQ8Q0ELG7321NB7YCM4', 'FINST-0KG66Q712Z1XYQJI9GSJQ8Q0ELG7321NB7YCM5', 'FINST-0KG66Q712Z1XYQJI9GSJQ8Q0ELG7321NB7YCM6', 'FINST-0KG66Q712Z1XYQJI9GSJQ8Q0ELG7321NB7YCM7', 'FINST-0KG66Q712Z1XYQJI9GSJQ8Q0ELG7321NB7YCM8', 'FINST-0KG66Q712Z1XYQJI9GSJQ8Q0ELG7321NB7YCM9', 'FINST-0KG66Q712Z1XYQJI9GSJQ8Q0ELG7321NB7YCMA', 'FINST-0KG66Q712Z1XYQJI9GSJQ8Q0ELG7321NB7YCMB', 'FINST-0KG66Q712Z1XYQJI9GSJQ8Q0ELG7321NB7YCMC', 'FINST-0KG66Q712Z1XYQJI9GSJQ8Q0ELG7321NB7YCMD', 'FINST-0KG66Q712Z1XYQJI9GSJQ8Q0ELG7321NB7YCME', 'FINST-0KG66Q712Z1XYQJI9GSJQ8Q0ELG7321NB7YCMF']}
2025-07-11 10:32:28,643 - INFO - 批量插入表单数据成功，批次 7，共 14 条记录
2025-07-11 10:32:28,643 - INFO - 成功插入的数据ID: ['FINST-0KG66Q712Z1XYQJI9GSJQ8Q0ELG7321NB7YCM2', 'FINST-0KG66Q712Z1XYQJI9GSJQ8Q0ELG7321NB7YCM3', 'FINST-0KG66Q712Z1XYQJI9GSJQ8Q0ELG7321NB7YCM4', 'FINST-0KG66Q712Z1XYQJI9GSJQ8Q0ELG7321NB7YCM5', 'FINST-0KG66Q712Z1XYQJI9GSJQ8Q0ELG7321NB7YCM6', 'FINST-0KG66Q712Z1XYQJI9GSJQ8Q0ELG7321NB7YCM7', 'FINST-0KG66Q712Z1XYQJI9GSJQ8Q0ELG7321NB7YCM8', 'FINST-0KG66Q712Z1XYQJI9GSJQ8Q0ELG7321NB7YCM9', 'FINST-0KG66Q712Z1XYQJI9GSJQ8Q0ELG7321NB7YCMA', 'FINST-0KG66Q712Z1XYQJI9GSJQ8Q0ELG7321NB7YCMB', 'FINST-0KG66Q712Z1XYQJI9GSJQ8Q0ELG7321NB7YCMC', 'FINST-0KG66Q712Z1XYQJI9GSJQ8Q0ELG7321NB7YCMD', 'FINST-0KG66Q712Z1XYQJI9GSJQ8Q0ELG7321NB7YCME', 'FINST-0KG66Q712Z1XYQJI9GSJQ8Q0ELG7321NB7YCMF']
2025-07-11 10:32:33,659 - INFO - 批量插入完成，共 314 条记录
2025-07-11 10:32:33,659 - INFO - 日期 2025-07-10 处理完成 - 更新: 6 条，插入: 314 条，错误: 0 条
2025-07-11 10:32:33,659 - INFO - 数据同步完成！更新: 6 条，插入: 314 条，错误: 0 条
2025-07-11 10:32:33,659 - INFO - 同步完成
2025-07-11 13:30:33,854 - INFO - 使用默认增量同步（当天更新数据）
2025-07-11 13:30:33,854 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-11 13:30:33,854 - INFO - 查询参数: ('2025-07-11',)
2025-07-11 13:30:34,011 - INFO - MySQL查询成功，增量数据（日期: 2025-07-11），共获取 193 条记录
2025-07-11 13:30:34,011 - INFO - 获取到 3 个日期需要处理: ['2025-07-02', '2025-07-09', '2025-07-10']
2025-07-11 13:30:34,011 - INFO - 开始处理日期: 2025-07-02
2025-07-11 13:30:34,026 - INFO - Request Parameters - Page 1:
2025-07-11 13:30:34,026 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 13:30:34,026 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 13:30:42,135 - ERROR - 处理日期 2025-07-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 85D38E42-DAB3-7F72-8716-8C4720F0A4AC Response: {'code': 'ServiceUnavailable', 'requestid': '85D38E42-DAB3-7F72-8716-8C4720F0A4AC', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 85D38E42-DAB3-7F72-8716-8C4720F0A4AC)
2025-07-11 13:30:42,135 - INFO - 开始处理日期: 2025-07-09
2025-07-11 13:30:42,135 - INFO - Request Parameters - Page 1:
2025-07-11 13:30:42,135 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 13:30:42,135 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751990400000, 1752076799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 13:30:49,401 - INFO - Response - Page 1:
2025-07-11 13:30:49,401 - INFO - 第 1 页获取到 50 条记录
2025-07-11 13:30:49,917 - INFO - Request Parameters - Page 2:
2025-07-11 13:30:49,917 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 13:30:49,917 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751990400000, 1752076799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 13:30:50,682 - INFO - Response - Page 2:
2025-07-11 13:30:50,682 - INFO - 第 2 页获取到 50 条记录
2025-07-11 13:30:51,198 - INFO - Request Parameters - Page 3:
2025-07-11 13:30:51,198 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 13:30:51,198 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751990400000, 1752076799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 13:30:51,885 - INFO - Response - Page 3:
2025-07-11 13:30:51,885 - INFO - 第 3 页获取到 50 条记录
2025-07-11 13:30:52,385 - INFO - Request Parameters - Page 4:
2025-07-11 13:30:52,385 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 13:30:52,385 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751990400000, 1752076799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 13:30:53,057 - INFO - Response - Page 4:
2025-07-11 13:30:53,057 - INFO - 第 4 页获取到 50 条记录
2025-07-11 13:30:53,573 - INFO - Request Parameters - Page 5:
2025-07-11 13:30:53,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 13:30:53,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751990400000, 1752076799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 13:30:54,338 - INFO - Response - Page 5:
2025-07-11 13:30:54,338 - INFO - 第 5 页获取到 50 条记录
2025-07-11 13:30:54,854 - INFO - Request Parameters - Page 6:
2025-07-11 13:30:54,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 13:30:54,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751990400000, 1752076799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 13:30:55,510 - INFO - Response - Page 6:
2025-07-11 13:30:55,510 - INFO - 第 6 页获取到 50 条记录
2025-07-11 13:30:56,010 - INFO - Request Parameters - Page 7:
2025-07-11 13:30:56,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 13:30:56,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751990400000, 1752076799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 13:30:56,698 - INFO - Response - Page 7:
2025-07-11 13:30:56,698 - INFO - 第 7 页获取到 50 条记录
2025-07-11 13:30:57,213 - INFO - Request Parameters - Page 8:
2025-07-11 13:30:57,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 13:30:57,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751990400000, 1752076799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 13:30:57,995 - INFO - Response - Page 8:
2025-07-11 13:30:57,995 - INFO - 第 8 页获取到 50 条记录
2025-07-11 13:30:58,495 - INFO - Request Parameters - Page 9:
2025-07-11 13:30:58,495 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 13:30:58,495 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751990400000, 1752076799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 13:30:59,182 - INFO - Response - Page 9:
2025-07-11 13:30:59,182 - INFO - 第 9 页获取到 50 条记录
2025-07-11 13:30:59,698 - INFO - Request Parameters - Page 10:
2025-07-11 13:30:59,698 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 13:30:59,698 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751990400000, 1752076799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 13:31:00,401 - INFO - Response - Page 10:
2025-07-11 13:31:00,401 - INFO - 第 10 页获取到 41 条记录
2025-07-11 13:31:00,917 - INFO - 查询完成，共获取到 491 条记录
2025-07-11 13:31:00,917 - INFO - 获取到 491 条表单数据
2025-07-11 13:31:00,917 - INFO - 当前日期 2025-07-09 有 1 条MySQL数据需要处理
2025-07-11 13:31:00,917 - INFO - 开始更新记录 - 表单实例ID: FINST-2K666OB1K80XH1DVB98XTC495GUN3COO6SWCMKN
2025-07-11 13:31:01,495 - INFO - 更新表单数据成功: FINST-2K666OB1K80XH1DVB98XTC495GUN3COO6SWCMKN
2025-07-11 13:31:01,495 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5088.0, 'new_value': 5088.5}, {'field': 'total_amount', 'old_value': 5088.0, 'new_value': 5088.5}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-07-11 13:31:01,495 - INFO - 日期 2025-07-09 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-07-11 13:31:01,495 - INFO - 开始处理日期: 2025-07-10
2025-07-11 13:31:01,495 - INFO - Request Parameters - Page 1:
2025-07-11 13:31:01,495 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 13:31:01,495 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 13:31:02,307 - INFO - Response - Page 1:
2025-07-11 13:31:02,307 - INFO - 第 1 页获取到 50 条记录
2025-07-11 13:31:02,823 - INFO - Request Parameters - Page 2:
2025-07-11 13:31:02,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 13:31:02,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 13:31:03,557 - INFO - Response - Page 2:
2025-07-11 13:31:03,557 - INFO - 第 2 页获取到 50 条记录
2025-07-11 13:31:04,057 - INFO - Request Parameters - Page 3:
2025-07-11 13:31:04,057 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 13:31:04,057 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 13:31:04,745 - INFO - Response - Page 3:
2025-07-11 13:31:04,745 - INFO - 第 3 页获取到 50 条记录
2025-07-11 13:31:05,245 - INFO - Request Parameters - Page 4:
2025-07-11 13:31:05,245 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 13:31:05,245 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 13:31:05,979 - INFO - Response - Page 4:
2025-07-11 13:31:05,979 - INFO - 第 4 页获取到 50 条记录
2025-07-11 13:31:06,495 - INFO - Request Parameters - Page 5:
2025-07-11 13:31:06,495 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 13:31:06,495 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 13:31:07,151 - INFO - Response - Page 5:
2025-07-11 13:31:07,151 - INFO - 第 5 页获取到 50 条记录
2025-07-11 13:31:07,667 - INFO - Request Parameters - Page 6:
2025-07-11 13:31:07,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 13:31:07,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 13:31:08,354 - INFO - Response - Page 6:
2025-07-11 13:31:08,354 - INFO - 第 6 页获取到 50 条记录
2025-07-11 13:31:08,870 - INFO - Request Parameters - Page 7:
2025-07-11 13:31:08,870 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 13:31:08,870 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 13:31:09,557 - INFO - Response - Page 7:
2025-07-11 13:31:09,557 - INFO - 第 7 页获取到 50 条记录
2025-07-11 13:31:10,057 - INFO - Request Parameters - Page 8:
2025-07-11 13:31:10,057 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 13:31:10,057 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 13:31:10,776 - INFO - Response - Page 8:
2025-07-11 13:31:10,776 - INFO - 第 8 页获取到 50 条记录
2025-07-11 13:31:11,292 - INFO - Request Parameters - Page 9:
2025-07-11 13:31:11,292 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 13:31:11,292 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 13:31:11,948 - INFO - Response - Page 9:
2025-07-11 13:31:11,948 - INFO - 第 9 页获取到 28 条记录
2025-07-11 13:31:12,448 - INFO - 查询完成，共获取到 428 条记录
2025-07-11 13:31:12,448 - INFO - 获取到 428 条表单数据
2025-07-11 13:31:12,448 - INFO - 当前日期 2025-07-10 有 184 条MySQL数据需要处理
2025-07-11 13:31:12,448 - INFO - 开始更新记录 - 表单实例ID: FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCML1
2025-07-11 13:31:12,932 - INFO - 更新表单数据成功: FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCML1
2025-07-11 13:31:12,932 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 8503.95}, {'field': 'offline_amount', 'old_value': 8691.36, 'new_value': 1292.37}, {'field': 'total_amount', 'old_value': 8691.36, 'new_value': 9796.32}, {'field': 'order_count', 'old_value': 35, 'new_value': 36}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/b5974caf7a7446c9af035ec07c7cdbfb.jpg?Expires=2067326371&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=sTtOFEhGSOovrIv%2B1m3J98Fm7tM%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/c0891bad2401492c8eab07732bc33f9c.jpg?Expires=2067326195&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=DdusPc4w0vmUbv%2BQt5MWqwooGLw%3D'}]
2025-07-11 13:31:12,932 - INFO - 开始批量插入 21 条新记录
2025-07-11 13:31:13,166 - INFO - 批量插入响应状态码: 200
2025-07-11 13:31:13,166 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 11 Jul 2025 05:31:13 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1018', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'DC76F138-E1B7-7BB3-9290-D416C014A6D6', 'x-acs-trace-id': '97c7651262e6f80d2c6a44b1d386d9d9', 'etag': '1feJHeHjpP3qwJFOs5knS5A8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-11 13:31:13,166 - INFO - 批量插入响应体: {'result': ['FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCMY', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCMZ', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCM01', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCM11', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCM21', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCM31', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCM41', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCM51', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCM61', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCM71', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCM81', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCM91', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCMA1', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCMB1', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCMC1', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCMD1', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCME1', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCMF1', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCMG1', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCMH1', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCMI1']}
2025-07-11 13:31:13,166 - INFO - 批量插入表单数据成功，批次 1，共 21 条记录
2025-07-11 13:31:13,166 - INFO - 成功插入的数据ID: ['FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCMY', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCMZ', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCM01', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCM11', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCM21', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCM31', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCM41', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCM51', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCM61', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCM71', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCM81', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCM91', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCMA1', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCMB1', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCMC1', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCMD1', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCME1', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCMF1', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCMG1', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCMH1', 'FINST-IOC66G71CX1X1D51COUNKD1XHAG4326IPDYCMI1']
2025-07-11 13:31:18,182 - INFO - 批量插入完成，共 21 条记录
2025-07-11 13:31:18,182 - INFO - 日期 2025-07-10 处理完成 - 更新: 1 条，插入: 21 条，错误: 0 条
2025-07-11 13:31:18,182 - INFO - 数据同步完成！更新: 2 条，插入: 21 条，错误: 1 条
2025-07-11 13:32:18,197 - INFO - 开始同步昨天与今天的销售数据: 2025-07-10 至 2025-07-11
2025-07-11 13:32:18,197 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-11 13:32:18,197 - INFO - 查询参数: ('2025-07-10', '2025-07-11')
2025-07-11 13:32:18,354 - INFO - MySQL查询成功，时间段: 2025-07-10 至 2025-07-11，共获取 463 条记录
2025-07-11 13:32:18,354 - INFO - 获取到 1 个日期需要处理: ['2025-07-10']
2025-07-11 13:32:18,354 - INFO - 开始处理日期: 2025-07-10
2025-07-11 13:32:18,354 - INFO - Request Parameters - Page 1:
2025-07-11 13:32:18,354 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 13:32:18,354 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 13:32:19,072 - INFO - Response - Page 1:
2025-07-11 13:32:19,072 - INFO - 第 1 页获取到 50 条记录
2025-07-11 13:32:19,588 - INFO - Request Parameters - Page 2:
2025-07-11 13:32:19,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 13:32:19,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 13:32:20,338 - INFO - Response - Page 2:
2025-07-11 13:32:20,338 - INFO - 第 2 页获取到 50 条记录
2025-07-11 13:32:20,854 - INFO - Request Parameters - Page 3:
2025-07-11 13:32:20,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 13:32:20,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 13:32:21,588 - INFO - Response - Page 3:
2025-07-11 13:32:21,588 - INFO - 第 3 页获取到 50 条记录
2025-07-11 13:32:22,104 - INFO - Request Parameters - Page 4:
2025-07-11 13:32:22,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 13:32:22,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 13:32:22,791 - INFO - Response - Page 4:
2025-07-11 13:32:22,791 - INFO - 第 4 页获取到 50 条记录
2025-07-11 13:32:23,307 - INFO - Request Parameters - Page 5:
2025-07-11 13:32:23,307 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 13:32:23,307 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 13:32:24,010 - INFO - Response - Page 5:
2025-07-11 13:32:24,010 - INFO - 第 5 页获取到 50 条记录
2025-07-11 13:32:24,510 - INFO - Request Parameters - Page 6:
2025-07-11 13:32:24,510 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 13:32:24,510 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 13:32:25,166 - INFO - Response - Page 6:
2025-07-11 13:32:25,166 - INFO - 第 6 页获取到 50 条记录
2025-07-11 13:32:25,682 - INFO - Request Parameters - Page 7:
2025-07-11 13:32:25,682 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 13:32:25,682 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 13:32:26,510 - INFO - Response - Page 7:
2025-07-11 13:32:26,510 - INFO - 第 7 页获取到 50 条记录
2025-07-11 13:32:27,010 - INFO - Request Parameters - Page 8:
2025-07-11 13:32:27,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 13:32:27,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 13:32:27,744 - INFO - Response - Page 8:
2025-07-11 13:32:27,744 - INFO - 第 8 页获取到 50 条记录
2025-07-11 13:32:28,260 - INFO - Request Parameters - Page 9:
2025-07-11 13:32:28,260 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 13:32:28,260 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 13:32:28,947 - INFO - Response - Page 9:
2025-07-11 13:32:28,947 - INFO - 第 9 页获取到 49 条记录
2025-07-11 13:32:29,463 - INFO - 查询完成，共获取到 449 条记录
2025-07-11 13:32:29,463 - INFO - 获取到 449 条表单数据
2025-07-11 13:32:29,463 - INFO - 当前日期 2025-07-10 有 449 条MySQL数据需要处理
2025-07-11 13:32:29,478 - INFO - 日期 2025-07-10 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-11 13:32:29,478 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-11 13:32:29,478 - INFO - 同步完成
2025-07-11 16:30:34,150 - INFO - 使用默认增量同步（当天更新数据）
2025-07-11 16:30:34,150 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-11 16:30:34,150 - INFO - 查询参数: ('2025-07-11',)
2025-07-11 16:30:34,306 - INFO - MySQL查询成功，增量数据（日期: 2025-07-11），共获取 194 条记录
2025-07-11 16:30:34,306 - INFO - 获取到 3 个日期需要处理: ['2025-07-02', '2025-07-09', '2025-07-10']
2025-07-11 16:30:34,306 - INFO - 开始处理日期: 2025-07-02
2025-07-11 16:30:34,306 - INFO - Request Parameters - Page 1:
2025-07-11 16:30:34,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 16:30:34,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 16:30:42,415 - ERROR - 处理日期 2025-07-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 05F649C4-676B-779C-B034-2ABF76C1DF10 Response: {'code': 'ServiceUnavailable', 'requestid': '05F649C4-676B-779C-B034-2ABF76C1DF10', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 05F649C4-676B-779C-B034-2ABF76C1DF10)
2025-07-11 16:30:42,415 - INFO - 开始处理日期: 2025-07-09
2025-07-11 16:30:42,415 - INFO - Request Parameters - Page 1:
2025-07-11 16:30:42,415 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 16:30:42,415 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751990400000, 1752076799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 16:30:49,915 - INFO - Response - Page 1:
2025-07-11 16:30:49,915 - INFO - 第 1 页获取到 50 条记录
2025-07-11 16:30:50,431 - INFO - Request Parameters - Page 2:
2025-07-11 16:30:50,431 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 16:30:50,431 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751990400000, 1752076799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 16:30:51,244 - INFO - Response - Page 2:
2025-07-11 16:30:51,244 - INFO - 第 2 页获取到 50 条记录
2025-07-11 16:30:51,759 - INFO - Request Parameters - Page 3:
2025-07-11 16:30:51,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 16:30:51,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751990400000, 1752076799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 16:30:52,447 - INFO - Response - Page 3:
2025-07-11 16:30:52,447 - INFO - 第 3 页获取到 50 条记录
2025-07-11 16:30:52,962 - INFO - Request Parameters - Page 4:
2025-07-11 16:30:52,962 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 16:30:52,962 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751990400000, 1752076799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 16:30:53,650 - INFO - Response - Page 4:
2025-07-11 16:30:53,650 - INFO - 第 4 页获取到 50 条记录
2025-07-11 16:30:54,165 - INFO - Request Parameters - Page 5:
2025-07-11 16:30:54,165 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 16:30:54,165 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751990400000, 1752076799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 16:30:54,947 - INFO - Response - Page 5:
2025-07-11 16:30:54,947 - INFO - 第 5 页获取到 50 条记录
2025-07-11 16:30:55,447 - INFO - Request Parameters - Page 6:
2025-07-11 16:30:55,447 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 16:30:55,447 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751990400000, 1752076799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 16:30:56,165 - INFO - Response - Page 6:
2025-07-11 16:30:56,165 - INFO - 第 6 页获取到 50 条记录
2025-07-11 16:30:56,681 - INFO - Request Parameters - Page 7:
2025-07-11 16:30:56,681 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 16:30:56,681 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751990400000, 1752076799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 16:30:57,493 - INFO - Response - Page 7:
2025-07-11 16:30:57,493 - INFO - 第 7 页获取到 50 条记录
2025-07-11 16:30:58,009 - INFO - Request Parameters - Page 8:
2025-07-11 16:30:58,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 16:30:58,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751990400000, 1752076799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 16:30:58,775 - INFO - Response - Page 8:
2025-07-11 16:30:58,775 - INFO - 第 8 页获取到 50 条记录
2025-07-11 16:30:59,290 - INFO - Request Parameters - Page 9:
2025-07-11 16:30:59,290 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 16:30:59,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751990400000, 1752076799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 16:31:00,040 - INFO - Response - Page 9:
2025-07-11 16:31:00,040 - INFO - 第 9 页获取到 50 条记录
2025-07-11 16:31:00,556 - INFO - Request Parameters - Page 10:
2025-07-11 16:31:00,556 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 16:31:00,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751990400000, 1752076799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 16:31:01,243 - INFO - Response - Page 10:
2025-07-11 16:31:01,243 - INFO - 第 10 页获取到 41 条记录
2025-07-11 16:31:01,759 - INFO - 查询完成，共获取到 491 条记录
2025-07-11 16:31:01,759 - INFO - 获取到 491 条表单数据
2025-07-11 16:31:01,759 - INFO - 当前日期 2025-07-09 有 2 条MySQL数据需要处理
2025-07-11 16:31:01,759 - INFO - 开始批量插入 1 条新记录
2025-07-11 16:31:01,931 - INFO - 批量插入响应状态码: 200
2025-07-11 16:31:01,931 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 11 Jul 2025 08:31:01 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '61', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '578904EA-5F35-7B6A-9579-98AC22736795', 'x-acs-trace-id': '4f59c4258e14f0e8c57f0007b4300122', 'etag': '6t1Oy+smsXJPFH5HwMD06pQ1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-11 16:31:01,931 - INFO - 批量插入响应体: {'result': ['FINST-F7D66UA1R61XM889DSATI4K4ALZI24WQ4KYCMB41']}
2025-07-11 16:31:01,931 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-11 16:31:01,931 - INFO - 成功插入的数据ID: ['FINST-F7D66UA1R61XM889DSATI4K4ALZI24WQ4KYCMB41']
2025-07-11 16:31:06,947 - INFO - 批量插入完成，共 1 条记录
2025-07-11 16:31:06,947 - INFO - 日期 2025-07-09 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-11 16:31:06,947 - INFO - 开始处理日期: 2025-07-10
2025-07-11 16:31:06,947 - INFO - Request Parameters - Page 1:
2025-07-11 16:31:06,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 16:31:06,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 16:31:07,634 - INFO - Response - Page 1:
2025-07-11 16:31:07,634 - INFO - 第 1 页获取到 50 条记录
2025-07-11 16:31:08,150 - INFO - Request Parameters - Page 2:
2025-07-11 16:31:08,150 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 16:31:08,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 16:31:08,868 - INFO - Response - Page 2:
2025-07-11 16:31:08,868 - INFO - 第 2 页获取到 50 条记录
2025-07-11 16:31:09,384 - INFO - Request Parameters - Page 3:
2025-07-11 16:31:09,384 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 16:31:09,384 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 16:31:10,071 - INFO - Response - Page 3:
2025-07-11 16:31:10,071 - INFO - 第 3 页获取到 50 条记录
2025-07-11 16:31:10,587 - INFO - Request Parameters - Page 4:
2025-07-11 16:31:10,587 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 16:31:10,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 16:31:11,259 - INFO - Response - Page 4:
2025-07-11 16:31:11,259 - INFO - 第 4 页获取到 50 条记录
2025-07-11 16:31:11,775 - INFO - Request Parameters - Page 5:
2025-07-11 16:31:11,775 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 16:31:11,775 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 16:31:12,478 - INFO - Response - Page 5:
2025-07-11 16:31:12,478 - INFO - 第 5 页获取到 50 条记录
2025-07-11 16:31:12,993 - INFO - Request Parameters - Page 6:
2025-07-11 16:31:12,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 16:31:12,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 16:31:13,634 - INFO - Response - Page 6:
2025-07-11 16:31:13,634 - INFO - 第 6 页获取到 50 条记录
2025-07-11 16:31:14,134 - INFO - Request Parameters - Page 7:
2025-07-11 16:31:14,134 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 16:31:14,134 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 16:31:14,806 - INFO - Response - Page 7:
2025-07-11 16:31:14,806 - INFO - 第 7 页获取到 50 条记录
2025-07-11 16:31:15,306 - INFO - Request Parameters - Page 8:
2025-07-11 16:31:15,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 16:31:15,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 16:31:16,040 - INFO - Response - Page 8:
2025-07-11 16:31:16,040 - INFO - 第 8 页获取到 50 条记录
2025-07-11 16:31:16,540 - INFO - Request Parameters - Page 9:
2025-07-11 16:31:16,540 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 16:31:16,540 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 16:31:17,212 - INFO - Response - Page 9:
2025-07-11 16:31:17,212 - INFO - 第 9 页获取到 49 条记录
2025-07-11 16:31:17,728 - INFO - 查询完成，共获取到 449 条记录
2025-07-11 16:31:17,728 - INFO - 获取到 449 条表单数据
2025-07-11 16:31:17,728 - INFO - 当前日期 2025-07-10 有 184 条MySQL数据需要处理
2025-07-11 16:31:17,728 - INFO - 开始更新记录 - 表单实例ID: FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMP1
2025-07-11 16:31:18,259 - INFO - 更新表单数据成功: FINST-737662B1D61XYTOK9S4BMD4JKKXE2GZEB7YCMP1
2025-07-11 16:31:18,259 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 390.0}, {'field': 'total_amount', 'old_value': 600.0, 'new_value': 990.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 10}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/7c50256545114fd7a0733b97181ee8f7.jpg?Expires=2067326371&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=zbR2XST5s48KsZMAGoOgqL%2BnetQ%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/f818afd5c7604fea9a2d9bfb4c9ffb2b.jpg?Expires=2067326371&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=AREehKFes8wYJwgQt0YX6uPP9b8%3D'}]
2025-07-11 16:31:18,259 - INFO - 开始更新记录 - 表单实例ID: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMXW
2025-07-11 16:31:18,728 - INFO - 更新表单数据成功: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMXW
2025-07-11 16:31:18,728 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 4256.6}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 4256.6}, {'field': 'order_count', 'old_value': 0, 'new_value': 224}]
2025-07-11 16:31:18,728 - INFO - 开始更新记录 - 表单实例ID: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMRW
2025-07-11 16:31:19,212 - INFO - 更新表单数据成功: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMRW
2025-07-11 16:31:19,212 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2670.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2670.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 243}]
2025-07-11 16:31:19,212 - INFO - 开始更新记录 - 表单实例ID: FINST-0KG66Q712Z1XYQJI9GSJQ8Q0ELG7321NB7YCM5
2025-07-11 16:31:19,681 - INFO - 更新表单数据成功: FINST-0KG66Q712Z1XYQJI9GSJQ8Q0ELG7321NB7YCM5
2025-07-11 16:31:19,681 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 168.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 168.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-07-11 16:31:19,681 - INFO - 开始更新记录 - 表单实例ID: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMZW
2025-07-11 16:31:20,228 - INFO - 更新表单数据成功: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMZW
2025-07-11 16:31:20,228 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 10340.02}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 10340.02}, {'field': 'order_count', 'old_value': 0, 'new_value': 518}]
2025-07-11 16:31:20,228 - INFO - 开始更新记录 - 表单实例ID: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMBW
2025-07-11 16:31:20,853 - INFO - 更新表单数据成功: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMBW
2025-07-11 16:31:20,853 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 6547.6}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 6547.6}, {'field': 'order_count', 'old_value': 0, 'new_value': 82}]
2025-07-11 16:31:20,853 - INFO - 开始更新记录 - 表单实例ID: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMCX
2025-07-11 16:31:21,446 - INFO - 更新表单数据成功: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMCX
2025-07-11 16:31:21,446 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 7000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 7000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 2}]
2025-07-11 16:31:21,446 - INFO - 日期 2025-07-10 处理完成 - 更新: 7 条，插入: 0 条，错误: 0 条
2025-07-11 16:31:21,446 - INFO - 数据同步完成！更新: 7 条，插入: 1 条，错误: 1 条
2025-07-11 16:32:21,462 - INFO - 开始同步昨天与今天的销售数据: 2025-07-10 至 2025-07-11
2025-07-11 16:32:21,462 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-11 16:32:21,462 - INFO - 查询参数: ('2025-07-10', '2025-07-11')
2025-07-11 16:32:21,618 - INFO - MySQL查询成功，时间段: 2025-07-10 至 2025-07-11，共获取 463 条记录
2025-07-11 16:32:21,618 - INFO - 获取到 1 个日期需要处理: ['2025-07-10']
2025-07-11 16:32:21,618 - INFO - 开始处理日期: 2025-07-10
2025-07-11 16:32:21,618 - INFO - Request Parameters - Page 1:
2025-07-11 16:32:21,618 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 16:32:21,618 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 16:32:22,462 - INFO - Response - Page 1:
2025-07-11 16:32:22,462 - INFO - 第 1 页获取到 50 条记录
2025-07-11 16:32:22,962 - INFO - Request Parameters - Page 2:
2025-07-11 16:32:22,962 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 16:32:22,962 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 16:32:23,727 - INFO - Response - Page 2:
2025-07-11 16:32:23,727 - INFO - 第 2 页获取到 50 条记录
2025-07-11 16:32:24,227 - INFO - Request Parameters - Page 3:
2025-07-11 16:32:24,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 16:32:24,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 16:32:24,915 - INFO - Response - Page 3:
2025-07-11 16:32:24,915 - INFO - 第 3 页获取到 50 条记录
2025-07-11 16:32:25,415 - INFO - Request Parameters - Page 4:
2025-07-11 16:32:25,415 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 16:32:25,415 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 16:32:26,149 - INFO - Response - Page 4:
2025-07-11 16:32:26,149 - INFO - 第 4 页获取到 50 条记录
2025-07-11 16:32:26,665 - INFO - Request Parameters - Page 5:
2025-07-11 16:32:26,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 16:32:26,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 16:32:27,352 - INFO - Response - Page 5:
2025-07-11 16:32:27,352 - INFO - 第 5 页获取到 50 条记录
2025-07-11 16:32:27,868 - INFO - Request Parameters - Page 6:
2025-07-11 16:32:27,868 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 16:32:27,868 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 16:32:28,571 - INFO - Response - Page 6:
2025-07-11 16:32:28,571 - INFO - 第 6 页获取到 50 条记录
2025-07-11 16:32:29,071 - INFO - Request Parameters - Page 7:
2025-07-11 16:32:29,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 16:32:29,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 16:32:29,743 - INFO - Response - Page 7:
2025-07-11 16:32:29,743 - INFO - 第 7 页获取到 50 条记录
2025-07-11 16:32:30,258 - INFO - Request Parameters - Page 8:
2025-07-11 16:32:30,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 16:32:30,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 16:32:30,962 - INFO - Response - Page 8:
2025-07-11 16:32:30,962 - INFO - 第 8 页获取到 50 条记录
2025-07-11 16:32:31,477 - INFO - Request Parameters - Page 9:
2025-07-11 16:32:31,477 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 16:32:31,477 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 16:32:32,212 - INFO - Response - Page 9:
2025-07-11 16:32:32,212 - INFO - 第 9 页获取到 49 条记录
2025-07-11 16:32:32,727 - INFO - 查询完成，共获取到 449 条记录
2025-07-11 16:32:32,727 - INFO - 获取到 449 条表单数据
2025-07-11 16:32:32,727 - INFO - 当前日期 2025-07-10 有 449 条MySQL数据需要处理
2025-07-11 16:32:32,743 - INFO - 日期 2025-07-10 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-11 16:32:32,743 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-11 16:32:32,743 - INFO - 同步完成
2025-07-11 19:30:34,508 - INFO - 使用默认增量同步（当天更新数据）
2025-07-11 19:30:34,508 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-11 19:30:34,508 - INFO - 查询参数: ('2025-07-11',)
2025-07-11 19:30:34,665 - INFO - MySQL查询成功，增量数据（日期: 2025-07-11），共获取 209 条记录
2025-07-11 19:30:34,665 - INFO - 获取到 3 个日期需要处理: ['2025-07-02', '2025-07-09', '2025-07-10']
2025-07-11 19:30:34,665 - INFO - 开始处理日期: 2025-07-02
2025-07-11 19:30:34,665 - INFO - Request Parameters - Page 1:
2025-07-11 19:30:34,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 19:30:34,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 19:30:41,730 - INFO - Response - Page 1:
2025-07-11 19:30:41,730 - INFO - 第 1 页获取到 50 条记录
2025-07-11 19:30:42,246 - INFO - Request Parameters - Page 2:
2025-07-11 19:30:42,246 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 19:30:42,246 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 19:30:50,359 - ERROR - 处理日期 2025-07-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 65FA128B-2EC0-7D8B-954E-4F4CDE9906E8 Response: {'code': 'ServiceUnavailable', 'requestid': '65FA128B-2EC0-7D8B-954E-4F4CDE9906E8', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 65FA128B-2EC0-7D8B-954E-4F4CDE9906E8)
2025-07-11 19:30:50,359 - INFO - 开始处理日期: 2025-07-09
2025-07-11 19:30:50,359 - INFO - Request Parameters - Page 1:
2025-07-11 19:30:50,359 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 19:30:50,359 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751990400000, 1752076799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 19:30:52,766 - INFO - Response - Page 1:
2025-07-11 19:30:52,766 - INFO - 第 1 页获取到 50 条记录
2025-07-11 19:30:53,282 - INFO - Request Parameters - Page 2:
2025-07-11 19:30:53,282 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 19:30:53,282 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751990400000, 1752076799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 19:30:53,985 - INFO - Response - Page 2:
2025-07-11 19:30:53,985 - INFO - 第 2 页获取到 50 条记录
2025-07-11 19:30:54,501 - INFO - Request Parameters - Page 3:
2025-07-11 19:30:54,501 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 19:30:54,501 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751990400000, 1752076799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 19:30:55,329 - INFO - Response - Page 3:
2025-07-11 19:30:55,329 - INFO - 第 3 页获取到 50 条记录
2025-07-11 19:30:55,830 - INFO - Request Parameters - Page 4:
2025-07-11 19:30:55,830 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 19:30:55,830 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751990400000, 1752076799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 19:30:56,549 - INFO - Response - Page 4:
2025-07-11 19:30:56,549 - INFO - 第 4 页获取到 50 条记录
2025-07-11 19:30:57,049 - INFO - Request Parameters - Page 5:
2025-07-11 19:30:57,049 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 19:30:57,049 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751990400000, 1752076799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 19:30:57,784 - INFO - Response - Page 5:
2025-07-11 19:30:57,784 - INFO - 第 5 页获取到 50 条记录
2025-07-11 19:30:58,299 - INFO - Request Parameters - Page 6:
2025-07-11 19:30:58,299 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 19:30:58,299 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751990400000, 1752076799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 19:30:59,003 - INFO - Response - Page 6:
2025-07-11 19:30:59,003 - INFO - 第 6 页获取到 50 条记录
2025-07-11 19:30:59,519 - INFO - Request Parameters - Page 7:
2025-07-11 19:30:59,519 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 19:30:59,519 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751990400000, 1752076799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 19:31:00,238 - INFO - Response - Page 7:
2025-07-11 19:31:00,238 - INFO - 第 7 页获取到 50 条记录
2025-07-11 19:31:00,753 - INFO - Request Parameters - Page 8:
2025-07-11 19:31:00,753 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 19:31:00,753 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751990400000, 1752076799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 19:31:01,488 - INFO - Response - Page 8:
2025-07-11 19:31:01,488 - INFO - 第 8 页获取到 50 条记录
2025-07-11 19:31:01,988 - INFO - Request Parameters - Page 9:
2025-07-11 19:31:01,988 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 19:31:01,988 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751990400000, 1752076799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 19:31:02,676 - INFO - Response - Page 9:
2025-07-11 19:31:02,676 - INFO - 第 9 页获取到 50 条记录
2025-07-11 19:31:03,192 - INFO - Request Parameters - Page 10:
2025-07-11 19:31:03,192 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 19:31:03,192 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751990400000, 1752076799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 19:31:03,911 - INFO - Response - Page 10:
2025-07-11 19:31:03,927 - INFO - 第 10 页获取到 42 条记录
2025-07-11 19:31:04,442 - INFO - 查询完成，共获取到 492 条记录
2025-07-11 19:31:04,442 - INFO - 获取到 492 条表单数据
2025-07-11 19:31:04,442 - INFO - 当前日期 2025-07-09 有 2 条MySQL数据需要处理
2025-07-11 19:31:04,442 - INFO - 日期 2025-07-09 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-11 19:31:04,442 - INFO - 开始处理日期: 2025-07-10
2025-07-11 19:31:04,442 - INFO - Request Parameters - Page 1:
2025-07-11 19:31:04,442 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 19:31:04,442 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 19:31:05,130 - INFO - Response - Page 1:
2025-07-11 19:31:05,130 - INFO - 第 1 页获取到 50 条记录
2025-07-11 19:31:05,646 - INFO - Request Parameters - Page 2:
2025-07-11 19:31:05,646 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 19:31:05,646 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 19:31:06,287 - INFO - Response - Page 2:
2025-07-11 19:31:06,287 - INFO - 第 2 页获取到 50 条记录
2025-07-11 19:31:06,787 - INFO - Request Parameters - Page 3:
2025-07-11 19:31:06,787 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 19:31:06,787 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 19:31:07,506 - INFO - Response - Page 3:
2025-07-11 19:31:07,506 - INFO - 第 3 页获取到 50 条记录
2025-07-11 19:31:08,022 - INFO - Request Parameters - Page 4:
2025-07-11 19:31:08,022 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 19:31:08,022 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 19:31:08,819 - INFO - Response - Page 4:
2025-07-11 19:31:08,819 - INFO - 第 4 页获取到 50 条记录
2025-07-11 19:31:09,319 - INFO - Request Parameters - Page 5:
2025-07-11 19:31:09,319 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 19:31:09,319 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 19:31:09,976 - INFO - Response - Page 5:
2025-07-11 19:31:09,976 - INFO - 第 5 页获取到 50 条记录
2025-07-11 19:31:10,476 - INFO - Request Parameters - Page 6:
2025-07-11 19:31:10,476 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 19:31:10,476 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 19:31:11,195 - INFO - Response - Page 6:
2025-07-11 19:31:11,195 - INFO - 第 6 页获取到 50 条记录
2025-07-11 19:31:11,711 - INFO - Request Parameters - Page 7:
2025-07-11 19:31:11,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 19:31:11,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 19:31:12,383 - INFO - Response - Page 7:
2025-07-11 19:31:12,399 - INFO - 第 7 页获取到 50 条记录
2025-07-11 19:31:12,899 - INFO - Request Parameters - Page 8:
2025-07-11 19:31:12,899 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 19:31:12,899 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 19:31:13,634 - INFO - Response - Page 8:
2025-07-11 19:31:13,634 - INFO - 第 8 页获取到 50 条记录
2025-07-11 19:31:14,149 - INFO - Request Parameters - Page 9:
2025-07-11 19:31:14,149 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 19:31:14,149 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 19:31:14,822 - INFO - Response - Page 9:
2025-07-11 19:31:14,822 - INFO - 第 9 页获取到 49 条记录
2025-07-11 19:31:15,337 - INFO - 查询完成，共获取到 449 条记录
2025-07-11 19:31:15,337 - INFO - 获取到 449 条表单数据
2025-07-11 19:31:15,337 - INFO - 当前日期 2025-07-10 有 199 条MySQL数据需要处理
2025-07-11 19:31:15,337 - INFO - 开始更新记录 - 表单实例ID: FINST-0KG66Q712Z1XYQJI9GSJQ8Q0ELG7321NB7YCM6
2025-07-11 19:31:15,947 - INFO - 更新表单数据成功: FINST-0KG66Q712Z1XYQJI9GSJQ8Q0ELG7321NB7YCM6
2025-07-11 19:31:15,947 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 434.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 434.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 3}]
2025-07-11 19:31:15,947 - INFO - 开始更新记录 - 表单实例ID: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMTW
2025-07-11 19:31:16,447 - INFO - 更新表单数据成功: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMTW
2025-07-11 19:31:16,447 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3289.76}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 3289.76}, {'field': 'order_count', 'old_value': 0, 'new_value': 150}]
2025-07-11 19:31:16,447 - INFO - 开始更新记录 - 表单实例ID: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMLW
2025-07-11 19:31:17,041 - INFO - 更新表单数据成功: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMLW
2025-07-11 19:31:17,041 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3760.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 3760.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-07-11 19:31:17,041 - INFO - 开始批量插入 15 条新记录
2025-07-11 19:31:17,213 - INFO - 批量插入响应状态码: 200
2025-07-11 19:31:17,213 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 11 Jul 2025 11:31:16 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '732', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '572B7FF0-FFE2-76DA-B473-DDD74A57CE54', 'x-acs-trace-id': '33721c8a479ebe527efb08c24958b697', 'etag': '7f6d9gN6IArDGW2vMzpYG+g2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-11 19:31:17,213 - INFO - 批量插入响应体: {'result': ['FINST-LOG66Q61BA2X0DL5CHMZL91BUAWZ10TJKQYCM79', 'FINST-LOG66Q61BA2X0DL5CHMZL91BUAWZ10TJKQYCM89', 'FINST-LOG66Q61BA2X0DL5CHMZL91BUAWZ10TJKQYCM99', 'FINST-LOG66Q61BA2X0DL5CHMZL91BUAWZ10TJKQYCMA9', 'FINST-LOG66Q61BA2X0DL5CHMZL91BUAWZ10TJKQYCMB9', 'FINST-LOG66Q61BA2X0DL5CHMZL91BUAWZ10TJKQYCMC9', 'FINST-LOG66Q61BA2X0DL5CHMZL91BUAWZ10TJKQYCMD9', 'FINST-LOG66Q61BA2X0DL5CHMZL91BUAWZ10TJKQYCME9', 'FINST-LOG66Q61BA2X0DL5CHMZL91BUAWZ10TJKQYCMF9', 'FINST-LOG66Q61BA2X0DL5CHMZL91BUAWZ10TJKQYCMG9', 'FINST-LOG66Q61BA2X0DL5CHMZL91BUAWZ10TJKQYCMH9', 'FINST-LOG66Q61BA2X0DL5CHMZL91BUAWZ10TJKQYCMI9', 'FINST-LOG66Q61BA2X0DL5CHMZL91BUAWZ10TJKQYCMJ9', 'FINST-LOG66Q61BA2X0DL5CHMZL91BUAWZ10TJKQYCMK9', 'FINST-LOG66Q61BA2X0DL5CHMZL91BUAWZ10TJKQYCML9']}
2025-07-11 19:31:17,213 - INFO - 批量插入表单数据成功，批次 1，共 15 条记录
2025-07-11 19:31:17,213 - INFO - 成功插入的数据ID: ['FINST-LOG66Q61BA2X0DL5CHMZL91BUAWZ10TJKQYCM79', 'FINST-LOG66Q61BA2X0DL5CHMZL91BUAWZ10TJKQYCM89', 'FINST-LOG66Q61BA2X0DL5CHMZL91BUAWZ10TJKQYCM99', 'FINST-LOG66Q61BA2X0DL5CHMZL91BUAWZ10TJKQYCMA9', 'FINST-LOG66Q61BA2X0DL5CHMZL91BUAWZ10TJKQYCMB9', 'FINST-LOG66Q61BA2X0DL5CHMZL91BUAWZ10TJKQYCMC9', 'FINST-LOG66Q61BA2X0DL5CHMZL91BUAWZ10TJKQYCMD9', 'FINST-LOG66Q61BA2X0DL5CHMZL91BUAWZ10TJKQYCME9', 'FINST-LOG66Q61BA2X0DL5CHMZL91BUAWZ10TJKQYCMF9', 'FINST-LOG66Q61BA2X0DL5CHMZL91BUAWZ10TJKQYCMG9', 'FINST-LOG66Q61BA2X0DL5CHMZL91BUAWZ10TJKQYCMH9', 'FINST-LOG66Q61BA2X0DL5CHMZL91BUAWZ10TJKQYCMI9', 'FINST-LOG66Q61BA2X0DL5CHMZL91BUAWZ10TJKQYCMJ9', 'FINST-LOG66Q61BA2X0DL5CHMZL91BUAWZ10TJKQYCMK9', 'FINST-LOG66Q61BA2X0DL5CHMZL91BUAWZ10TJKQYCML9']
2025-07-11 19:31:22,231 - INFO - 批量插入完成，共 15 条记录
2025-07-11 19:31:22,231 - INFO - 日期 2025-07-10 处理完成 - 更新: 3 条，插入: 15 条，错误: 0 条
2025-07-11 19:31:22,231 - INFO - 数据同步完成！更新: 3 条，插入: 15 条，错误: 1 条
2025-07-11 19:32:22,270 - INFO - 开始同步昨天与今天的销售数据: 2025-07-10 至 2025-07-11
2025-07-11 19:32:22,270 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-11 19:32:22,270 - INFO - 查询参数: ('2025-07-10', '2025-07-11')
2025-07-11 19:32:22,427 - INFO - MySQL查询成功，时间段: 2025-07-10 至 2025-07-11，共获取 555 条记录
2025-07-11 19:32:22,427 - INFO - 获取到 1 个日期需要处理: ['2025-07-10']
2025-07-11 19:32:22,427 - INFO - 开始处理日期: 2025-07-10
2025-07-11 19:32:22,427 - INFO - Request Parameters - Page 1:
2025-07-11 19:32:22,427 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 19:32:22,442 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 19:32:23,130 - INFO - Response - Page 1:
2025-07-11 19:32:23,130 - INFO - 第 1 页获取到 50 条记录
2025-07-11 19:32:23,630 - INFO - Request Parameters - Page 2:
2025-07-11 19:32:23,630 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 19:32:23,630 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 19:32:24,318 - INFO - Response - Page 2:
2025-07-11 19:32:24,318 - INFO - 第 2 页获取到 50 条记录
2025-07-11 19:32:24,818 - INFO - Request Parameters - Page 3:
2025-07-11 19:32:24,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 19:32:24,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 19:32:25,568 - INFO - Response - Page 3:
2025-07-11 19:32:25,568 - INFO - 第 3 页获取到 50 条记录
2025-07-11 19:32:26,069 - INFO - Request Parameters - Page 4:
2025-07-11 19:32:26,069 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 19:32:26,069 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 19:32:26,741 - INFO - Response - Page 4:
2025-07-11 19:32:26,741 - INFO - 第 4 页获取到 50 条记录
2025-07-11 19:32:27,257 - INFO - Request Parameters - Page 5:
2025-07-11 19:32:27,257 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 19:32:27,257 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 19:32:27,898 - INFO - Response - Page 5:
2025-07-11 19:32:27,898 - INFO - 第 5 页获取到 50 条记录
2025-07-11 19:32:28,413 - INFO - Request Parameters - Page 6:
2025-07-11 19:32:28,413 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 19:32:28,413 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 19:32:29,070 - INFO - Response - Page 6:
2025-07-11 19:32:29,070 - INFO - 第 6 页获取到 50 条记录
2025-07-11 19:32:29,570 - INFO - Request Parameters - Page 7:
2025-07-11 19:32:29,570 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 19:32:29,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 19:32:30,258 - INFO - Response - Page 7:
2025-07-11 19:32:30,258 - INFO - 第 7 页获取到 50 条记录
2025-07-11 19:32:30,758 - INFO - Request Parameters - Page 8:
2025-07-11 19:32:30,758 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 19:32:30,758 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 19:32:31,508 - INFO - Response - Page 8:
2025-07-11 19:32:31,508 - INFO - 第 8 页获取到 50 条记录
2025-07-11 19:32:32,024 - INFO - Request Parameters - Page 9:
2025-07-11 19:32:32,024 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 19:32:32,024 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 19:32:32,696 - INFO - Response - Page 9:
2025-07-11 19:32:32,696 - INFO - 第 9 页获取到 50 条记录
2025-07-11 19:32:33,197 - INFO - Request Parameters - Page 10:
2025-07-11 19:32:33,197 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 19:32:33,197 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 19:32:33,775 - INFO - Response - Page 10:
2025-07-11 19:32:33,775 - INFO - 第 10 页获取到 14 条记录
2025-07-11 19:32:34,291 - INFO - 查询完成，共获取到 464 条记录
2025-07-11 19:32:34,291 - INFO - 获取到 464 条表单数据
2025-07-11 19:32:34,291 - INFO - 当前日期 2025-07-10 有 540 条MySQL数据需要处理
2025-07-11 19:32:34,306 - INFO - 开始批量插入 76 条新记录
2025-07-11 19:32:34,541 - INFO - 批量插入响应状态码: 200
2025-07-11 19:32:34,541 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 11 Jul 2025 11:32:34 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '81EFF22E-AD7D-76AE-BFDF-0E5B4C15AD6E', 'x-acs-trace-id': '829ca410db39ac245ca22c5dbbbea486', 'etag': '2MJBvEGyfJ2XXXI+H27YhQA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-11 19:32:34,541 - INFO - 批量插入响应体: {'result': ['FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCME8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMF8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMG8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMH8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMI8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMJ8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMK8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCML8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMM8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMN8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMO8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMP8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMQ8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMR8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMS8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMT8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMU8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMV8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMW8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMX8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMY8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMZ8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCM09', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCM19', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCM29', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCM39', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCM49', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCM59', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCM69', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCM79', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCM89', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCM99', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMA9', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMB9', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMC9', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMD9', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCME9', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMF9', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMG9', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMH9', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMI9', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMJ9', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMK9', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCML9', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213JG7MQYCMM9', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213JG7MQYCMN9', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213JG7MQYCMO9', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213JG7MQYCMP9', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213JG7MQYCMQ9', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213JG7MQYCMR9']}
2025-07-11 19:32:34,541 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-11 19:32:34,541 - INFO - 成功插入的数据ID: ['FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCME8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMF8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMG8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMH8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMI8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMJ8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMK8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCML8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMM8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMN8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMO8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMP8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMQ8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMR8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMS8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMT8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMU8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMV8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMW8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMX8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMY8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMZ8', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCM09', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCM19', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCM29', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCM39', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCM49', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCM59', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCM69', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCM79', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCM89', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCM99', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMA9', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMB9', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMC9', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMD9', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCME9', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMF9', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMG9', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMH9', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMI9', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMJ9', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCMK9', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213IG7MQYCML9', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213JG7MQYCMM9', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213JG7MQYCMN9', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213JG7MQYCMO9', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213JG7MQYCMP9', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213JG7MQYCMQ9', 'FINST-TKF669817Y1XTSXZCH6OHB6F3D213JG7MQYCMR9']
2025-07-11 19:32:39,746 - INFO - 批量插入响应状态码: 200
2025-07-11 19:32:39,746 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 11 Jul 2025 11:32:39 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1260', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B21C3F0E-CF20-70C0-A59A-64711E9BCD9C', 'x-acs-trace-id': '266baf74e0474f3986dcf29fd893de68', 'etag': '1FtVIgBxE9a9Apw4bA7YKgg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-11 19:32:39,746 - INFO - 批量插入响应体: {'result': ['FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCMM3', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCMN3', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCMO3', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCMP3', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCMQ3', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCMR3', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCMS3', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCMT3', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCMU3', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCMV3', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCMW3', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCMX3', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCMY3', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCMZ3', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCM04', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCM14', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCM24', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCM34', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCM44', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCM54', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCM64', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCM74', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCM84', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCM94', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCMA4', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCMB4']}
2025-07-11 19:32:39,746 - INFO - 批量插入表单数据成功，批次 2，共 26 条记录
2025-07-11 19:32:39,746 - INFO - 成功插入的数据ID: ['FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCMM3', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCMN3', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCMO3', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCMP3', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCMQ3', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCMR3', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCMS3', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCMT3', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCMU3', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCMV3', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCMW3', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCMX3', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCMY3', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCMZ3', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCM04', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCM14', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCM24', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCM34', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCM44', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCM54', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCM64', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCM74', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCM84', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCM94', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCMA4', 'FINST-VEC667D1JA2XGSYBA7RMUCS8OU1H36HBMQYCMB4']
2025-07-11 19:32:44,764 - INFO - 批量插入完成，共 76 条记录
2025-07-11 19:32:44,764 - INFO - 日期 2025-07-10 处理完成 - 更新: 0 条，插入: 76 条，错误: 0 条
2025-07-11 19:32:44,764 - INFO - 数据同步完成！更新: 0 条，插入: 76 条，错误: 0 条
2025-07-11 19:32:44,764 - INFO - 同步完成
2025-07-11 22:30:34,948 - INFO - 使用默认增量同步（当天更新数据）
2025-07-11 22:30:34,948 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-11 22:30:34,948 - INFO - 查询参数: ('2025-07-11',)
2025-07-11 22:30:35,105 - INFO - MySQL查询成功，增量数据（日期: 2025-07-11），共获取 291 条记录
2025-07-11 22:30:35,105 - INFO - 获取到 4 个日期需要处理: ['2025-07-02', '2025-07-09', '2025-07-10', '2025-07-11']
2025-07-11 22:30:35,105 - INFO - 开始处理日期: 2025-07-02
2025-07-11 22:30:35,105 - INFO - Request Parameters - Page 1:
2025-07-11 22:30:35,105 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 22:30:35,105 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751385600000, 1751471999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 22:30:43,233 - ERROR - 处理日期 2025-07-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: AA6A90A2-0185-75F6-821A-A57B06B7F1B1 Response: {'code': 'ServiceUnavailable', 'requestid': 'AA6A90A2-0185-75F6-821A-A57B06B7F1B1', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: AA6A90A2-0185-75F6-821A-A57B06B7F1B1)
2025-07-11 22:30:43,233 - INFO - 开始处理日期: 2025-07-09
2025-07-11 22:30:43,233 - INFO - Request Parameters - Page 1:
2025-07-11 22:30:43,233 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 22:30:43,233 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751990400000, 1752076799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 22:30:51,345 - ERROR - 处理日期 2025-07-09 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 85F60CF8-C139-764B-B8F1-B3FFE975E677 Response: {'code': 'ServiceUnavailable', 'requestid': '85F60CF8-C139-764B-B8F1-B3FFE975E677', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 85F60CF8-C139-764B-B8F1-B3FFE975E677)
2025-07-11 22:30:51,345 - INFO - 开始处理日期: 2025-07-10
2025-07-11 22:30:51,345 - INFO - Request Parameters - Page 1:
2025-07-11 22:30:51,345 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 22:30:51,345 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 22:30:52,768 - INFO - Response - Page 1:
2025-07-11 22:30:52,768 - INFO - 第 1 页获取到 50 条记录
2025-07-11 22:30:53,268 - INFO - Request Parameters - Page 2:
2025-07-11 22:30:53,268 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 22:30:53,268 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 22:30:53,956 - INFO - Response - Page 2:
2025-07-11 22:30:53,956 - INFO - 第 2 页获取到 50 条记录
2025-07-11 22:30:54,472 - INFO - Request Parameters - Page 3:
2025-07-11 22:30:54,472 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 22:30:54,472 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 22:30:55,113 - INFO - Response - Page 3:
2025-07-11 22:30:55,113 - INFO - 第 3 页获取到 50 条记录
2025-07-11 22:30:55,628 - INFO - Request Parameters - Page 4:
2025-07-11 22:30:55,628 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 22:30:55,628 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 22:30:56,332 - INFO - Response - Page 4:
2025-07-11 22:30:56,332 - INFO - 第 4 页获取到 50 条记录
2025-07-11 22:30:56,848 - INFO - Request Parameters - Page 5:
2025-07-11 22:30:56,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 22:30:56,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 22:30:57,535 - INFO - Response - Page 5:
2025-07-11 22:30:57,535 - INFO - 第 5 页获取到 50 条记录
2025-07-11 22:30:58,036 - INFO - Request Parameters - Page 6:
2025-07-11 22:30:58,036 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 22:30:58,036 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 22:30:58,708 - INFO - Response - Page 6:
2025-07-11 22:30:58,723 - INFO - 第 6 页获取到 50 条记录
2025-07-11 22:30:59,224 - INFO - Request Parameters - Page 7:
2025-07-11 22:30:59,224 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 22:30:59,224 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 22:30:59,943 - INFO - Response - Page 7:
2025-07-11 22:30:59,943 - INFO - 第 7 页获取到 50 条记录
2025-07-11 22:31:00,443 - INFO - Request Parameters - Page 8:
2025-07-11 22:31:00,443 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 22:31:00,443 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 22:31:01,099 - INFO - Response - Page 8:
2025-07-11 22:31:01,099 - INFO - 第 8 页获取到 50 条记录
2025-07-11 22:31:01,600 - INFO - Request Parameters - Page 9:
2025-07-11 22:31:01,600 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 22:31:01,600 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 22:31:02,350 - INFO - Response - Page 9:
2025-07-11 22:31:02,350 - INFO - 第 9 页获取到 50 条记录
2025-07-11 22:31:02,866 - INFO - Request Parameters - Page 10:
2025-07-11 22:31:02,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 22:31:02,866 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 22:31:03,585 - INFO - Response - Page 10:
2025-07-11 22:31:03,585 - INFO - 第 10 页获取到 50 条记录
2025-07-11 22:31:04,101 - INFO - Request Parameters - Page 11:
2025-07-11 22:31:04,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 22:31:04,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 22:31:04,773 - INFO - Response - Page 11:
2025-07-11 22:31:04,773 - INFO - 第 11 页获取到 40 条记录
2025-07-11 22:31:05,288 - INFO - 查询完成，共获取到 540 条记录
2025-07-11 22:31:05,288 - INFO - 获取到 540 条表单数据
2025-07-11 22:31:05,288 - INFO - 当前日期 2025-07-10 有 199 条MySQL数据需要处理
2025-07-11 22:31:05,288 - INFO - 开始更新记录 - 表单实例ID: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMHW
2025-07-11 22:31:05,945 - INFO - 更新表单数据成功: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMHW
2025-07-11 22:31:05,945 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 4200.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 4200.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 46}]
2025-07-11 22:31:05,945 - INFO - 开始更新记录 - 表单实例ID: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMOW
2025-07-11 22:31:06,523 - INFO - 更新表单数据成功: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMOW
2025-07-11 22:31:06,523 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 45000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 45000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-07-11 22:31:06,523 - INFO - 开始更新记录 - 表单实例ID: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMWW
2025-07-11 22:31:07,117 - INFO - 更新表单数据成功: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMWW
2025-07-11 22:31:07,117 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 340.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 340.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 15}]
2025-07-11 22:31:07,117 - INFO - 开始更新记录 - 表单实例ID: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM7W
2025-07-11 22:31:07,602 - INFO - 更新表单数据成功: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM7W
2025-07-11 22:31:07,602 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2984.65}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2984.65}, {'field': 'order_count', 'old_value': 0, 'new_value': 373}]
2025-07-11 22:31:07,602 - INFO - 开始更新记录 - 表单实例ID: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMFW
2025-07-11 22:31:08,165 - INFO - 更新表单数据成功: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMFW
2025-07-11 22:31:08,165 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2565.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2565.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 18}]
2025-07-11 22:31:08,165 - INFO - 开始更新记录 - 表单实例ID: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM5X
2025-07-11 22:31:08,727 - INFO - 更新表单数据成功: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM5X
2025-07-11 22:31:08,727 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2839.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2839.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 2}]
2025-07-11 22:31:08,727 - INFO - 开始更新记录 - 表单实例ID: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM9W
2025-07-11 22:31:09,228 - INFO - 更新表单数据成功: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM9W
2025-07-11 22:31:09,228 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 18784.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 18784.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 65}]
2025-07-11 22:31:09,228 - INFO - 开始更新记录 - 表单实例ID: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMNW
2025-07-11 22:31:09,759 - INFO - 更新表单数据成功: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMNW
2025-07-11 22:31:09,759 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 8422.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 8422.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 197}]
2025-07-11 22:31:09,759 - INFO - 开始更新记录 - 表单实例ID: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM0X
2025-07-11 22:31:10,275 - INFO - 更新表单数据成功: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM0X
2025-07-11 22:31:10,275 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1258.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1258.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 3}]
2025-07-11 22:31:10,275 - INFO - 开始更新记录 - 表单实例ID: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM3X
2025-07-11 22:31:10,806 - INFO - 更新表单数据成功: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCM3X
2025-07-11 22:31:10,806 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 5874.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 5874.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 254}]
2025-07-11 22:31:10,806 - INFO - 开始更新记录 - 表单实例ID: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMYW
2025-07-11 22:31:11,369 - INFO - 更新表单数据成功: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMYW
2025-07-11 22:31:11,369 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 4062.8}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 4062.8}, {'field': 'order_count', 'old_value': 0, 'new_value': 131}]
2025-07-11 22:31:11,369 - INFO - 日期 2025-07-10 处理完成 - 更新: 11 条，插入: 0 条，错误: 0 条
2025-07-11 22:31:11,369 - INFO - 开始处理日期: 2025-07-11
2025-07-11 22:31:11,369 - INFO - Request Parameters - Page 1:
2025-07-11 22:31:11,369 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 22:31:11,369 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 22:31:11,838 - INFO - Response - Page 1:
2025-07-11 22:31:11,838 - INFO - 查询完成，共获取到 0 条记录
2025-07-11 22:31:11,838 - INFO - 获取到 0 条表单数据
2025-07-11 22:31:11,838 - INFO - 当前日期 2025-07-11 有 78 条MySQL数据需要处理
2025-07-11 22:31:11,838 - INFO - 开始批量插入 78 条新记录
2025-07-11 22:31:12,119 - INFO - 批量插入响应状态码: 200
2025-07-11 22:31:12,119 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 11 Jul 2025 14:31:07 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '8D381036-59FA-72D8-AD6E-38674379F870', 'x-acs-trace-id': '566276b5f6dfe589a9918fffa2037d7f', 'etag': '2w9cg/+6x29rnmXqluSSJwQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-11 22:31:12,119 - INFO - 批量插入响应体: {'result': ['FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMK8', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCML8', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMM8', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMN8', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMO8', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMP8', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMQ8', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMR8', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMS8', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMT8', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMU8', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMV8', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMW8', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMX8', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMY8', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMZ8', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCM09', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCM19', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCM29', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCM39', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCM49', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCM59', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCM69', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCM79', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCM89', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCM99', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMA9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMB9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMC9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMD9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCME9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMF9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMG9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMH9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMI9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMJ9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMK9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCML9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMM9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMN9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMO9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMP9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2MWTZWYCMQ9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2MWTZWYCMR9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2MWTZWYCMS9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2MWTZWYCMT9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2MWTZWYCMU9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2MWTZWYCMV9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2MWTZWYCMW9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2MWTZWYCMX9']}
2025-07-11 22:31:12,119 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-11 22:31:12,119 - INFO - 成功插入的数据ID: ['FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMK8', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCML8', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMM8', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMN8', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMO8', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMP8', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMQ8', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMR8', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMS8', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMT8', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMU8', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMV8', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMW8', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMX8', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMY8', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMZ8', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCM09', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCM19', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCM29', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCM39', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCM49', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCM59', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCM69', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCM79', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCM89', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCM99', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMA9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMB9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMC9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMD9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCME9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMF9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMG9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMH9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMI9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMJ9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMK9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCML9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMM9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMN9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMO9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2LWTZWYCMP9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2MWTZWYCMQ9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2MWTZWYCMR9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2MWTZWYCMS9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2MWTZWYCMT9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2MWTZWYCMU9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2MWTZWYCMV9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2MWTZWYCMW9', 'FINST-FD966QA1WY1XOMBXBKOMTADLMYTY2MWTZWYCMX9']
2025-07-11 22:31:17,340 - INFO - 批量插入响应状态码: 200
2025-07-11 22:31:17,340 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 11 Jul 2025 14:31:12 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1384', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'BD35623E-DCA3-71CF-9C67-B91A0048E5AB', 'x-acs-trace-id': '2cd2c8eb0df5c2dab5bbeff61b539238', 'etag': '1ZGGT2XYHTtsYoHZqPgLPew4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-11 22:31:17,340 - INFO - 批量插入响应体: {'result': ['FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMWA2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMXA2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMYA2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMZA2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCM0B2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCM1B2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCM2B2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCM3B2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCM4B2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCM5B2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCM6B2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCM7B2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCM8B2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCM9B2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMAB2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMBB2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMCB2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMDB2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMEB2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMFB2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMGB2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMHB2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMIB2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMJB2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMKB2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMLB2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMMB2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMNB2']}
2025-07-11 22:31:17,340 - INFO - 批量插入表单数据成功，批次 2，共 28 条记录
2025-07-11 22:31:17,340 - INFO - 成功插入的数据ID: ['FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMWA2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMXA2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMYA2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMZA2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCM0B2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCM1B2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCM2B2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCM3B2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCM4B2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCM5B2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCM6B2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCM7B2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCM8B2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCM9B2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMAB2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMBB2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMCB2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMDB2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMEB2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMFB2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMGB2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMHB2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMIB2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMJB2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMKB2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMLB2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMMB2', 'FINST-DUF66091OVYW5K7XBMN5QBJOXFLV30YXZWYCMNB2']
2025-07-11 22:31:22,358 - INFO - 批量插入完成，共 78 条记录
2025-07-11 22:31:22,358 - INFO - 日期 2025-07-11 处理完成 - 更新: 0 条，插入: 78 条，错误: 0 条
2025-07-11 22:31:22,358 - INFO - 数据同步完成！更新: 11 条，插入: 78 条，错误: 2 条
2025-07-11 22:32:22,397 - INFO - 开始同步昨天与今天的销售数据: 2025-07-10 至 2025-07-11
2025-07-11 22:32:22,397 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-11 22:32:22,397 - INFO - 查询参数: ('2025-07-10', '2025-07-11')
2025-07-11 22:32:22,553 - INFO - MySQL查询成功，时间段: 2025-07-10 至 2025-07-11，共获取 637 条记录
2025-07-11 22:32:22,553 - INFO - 获取到 2 个日期需要处理: ['2025-07-10', '2025-07-11']
2025-07-11 22:32:22,569 - INFO - 开始处理日期: 2025-07-10
2025-07-11 22:32:22,569 - INFO - Request Parameters - Page 1:
2025-07-11 22:32:22,569 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 22:32:22,569 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 22:32:23,257 - INFO - Response - Page 1:
2025-07-11 22:32:23,257 - INFO - 第 1 页获取到 50 条记录
2025-07-11 22:32:23,773 - INFO - Request Parameters - Page 2:
2025-07-11 22:32:23,773 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 22:32:23,773 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 22:32:24,539 - INFO - Response - Page 2:
2025-07-11 22:32:24,539 - INFO - 第 2 页获取到 50 条记录
2025-07-11 22:32:25,054 - INFO - Request Parameters - Page 3:
2025-07-11 22:32:25,054 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 22:32:25,054 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 22:32:25,727 - INFO - Response - Page 3:
2025-07-11 22:32:25,727 - INFO - 第 3 页获取到 50 条记录
2025-07-11 22:32:26,242 - INFO - Request Parameters - Page 4:
2025-07-11 22:32:26,242 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 22:32:26,242 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 22:32:26,899 - INFO - Response - Page 4:
2025-07-11 22:32:26,899 - INFO - 第 4 页获取到 50 条记录
2025-07-11 22:32:27,399 - INFO - Request Parameters - Page 5:
2025-07-11 22:32:27,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 22:32:27,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 22:32:28,118 - INFO - Response - Page 5:
2025-07-11 22:32:28,118 - INFO - 第 5 页获取到 50 条记录
2025-07-11 22:32:28,634 - INFO - Request Parameters - Page 6:
2025-07-11 22:32:28,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 22:32:28,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 22:32:29,369 - INFO - Response - Page 6:
2025-07-11 22:32:29,369 - INFO - 第 6 页获取到 50 条记录
2025-07-11 22:32:29,869 - INFO - Request Parameters - Page 7:
2025-07-11 22:32:29,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 22:32:29,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 22:32:30,588 - INFO - Response - Page 7:
2025-07-11 22:32:30,588 - INFO - 第 7 页获取到 50 条记录
2025-07-11 22:32:31,104 - INFO - Request Parameters - Page 8:
2025-07-11 22:32:31,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 22:32:31,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 22:32:31,838 - INFO - Response - Page 8:
2025-07-11 22:32:31,838 - INFO - 第 8 页获取到 50 条记录
2025-07-11 22:32:32,354 - INFO - Request Parameters - Page 9:
2025-07-11 22:32:32,354 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 22:32:32,354 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 22:32:33,105 - INFO - Response - Page 9:
2025-07-11 22:32:33,105 - INFO - 第 9 页获取到 50 条记录
2025-07-11 22:32:33,620 - INFO - Request Parameters - Page 10:
2025-07-11 22:32:33,620 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 22:32:33,620 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 22:32:34,371 - INFO - Response - Page 10:
2025-07-11 22:32:34,371 - INFO - 第 10 页获取到 50 条记录
2025-07-11 22:32:34,871 - INFO - Request Parameters - Page 11:
2025-07-11 22:32:34,871 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 22:32:34,871 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 22:32:35,496 - INFO - Response - Page 11:
2025-07-11 22:32:35,496 - INFO - 第 11 页获取到 40 条记录
2025-07-11 22:32:36,012 - INFO - 查询完成，共获取到 540 条记录
2025-07-11 22:32:36,012 - INFO - 获取到 540 条表单数据
2025-07-11 22:32:36,012 - INFO - 当前日期 2025-07-10 有 540 条MySQL数据需要处理
2025-07-11 22:32:36,028 - INFO - 日期 2025-07-10 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-11 22:32:36,028 - INFO - 开始处理日期: 2025-07-11
2025-07-11 22:32:36,028 - INFO - Request Parameters - Page 1:
2025-07-11 22:32:36,028 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 22:32:36,028 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 22:32:36,731 - INFO - Response - Page 1:
2025-07-11 22:32:36,731 - INFO - 第 1 页获取到 50 条记录
2025-07-11 22:32:37,247 - INFO - Request Parameters - Page 2:
2025-07-11 22:32:37,247 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-11 22:32:37,247 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-11 22:32:37,841 - INFO - Response - Page 2:
2025-07-11 22:32:37,841 - INFO - 第 2 页获取到 28 条记录
2025-07-11 22:32:38,357 - INFO - 查询完成，共获取到 78 条记录
2025-07-11 22:32:38,357 - INFO - 获取到 78 条表单数据
2025-07-11 22:32:38,357 - INFO - 当前日期 2025-07-11 有 78 条MySQL数据需要处理
2025-07-11 22:32:38,357 - INFO - 日期 2025-07-11 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-11 22:32:38,357 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-11 22:32:38,357 - INFO - 同步完成
