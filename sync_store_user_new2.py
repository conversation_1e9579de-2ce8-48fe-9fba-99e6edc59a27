# -*- coding: utf-8 -*-
import os
import sys
import logging
import pymysql
import pandas as pd
import requests
import json
import time
from functools import wraps
from typing import List, Dict, Any, Optional, Tuple, Callable
from datetime import datetime, timedelta
from get_token import token
from alibabacloud_dingtalk.yida_1_0.client import Client as dingtalkyida_1_0Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dingtalk.yida_1_0 import models as dingtalkyida__1__0_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_dingtalk.yida_2_0.client import Client as DingTalkYidaClient
from alibabacloud_dingtalk.yida_2_0 import models as yida_models
import traceback
from sendemail import send_email

# 数据库配置
DB_CONFIG = {
    'host': '**************',
    'port': 3306,
    'user': 'c_hxp_ro_prod',
    'password': 'xm9P06O7ezGi6PZt',
    'database': 'yx_business',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

# 宜搭配置
YIDA_CONFIG = {
    'APP_TYPE': 'APP_D7E6ZB94ZUL5Q1GUAOLD',
    'SYSTEM_TOKEN': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2',
    'USER_ID': 'hexuepeng',
    'LANGUAGE': 'zh_CN',
    'FORM_UUID': 'FORM-328F978976574F1FBB0299F533812494G191',  # 店铺信息表单
    'FORM_UUID1': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30',  # 商户上报明细台帐
    'FORM_UUID2': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5'  # 销售上报月汇总
}

# 宜搭表单字段映射
YIDA_FIELD_MAPPING = {
    'project_name':'selectField_m886ps6o',#项目名称
    'store_code': 'textField_m911r3pn',    # 店铺编码
    'store_name': 'textField_m8e8g3lu',    # 店铺名称
    'userid': 'employeeField_m8e8g3lw',        # 钉钉userid（用于更新和插入）
    'userid_query': 'employeeField_m8e8g3lw_id',  # 钉钉userid（用于查询结果）
    'status': 'textField_m8e8g3lx',      # 状态
    'is_duojing': 'textField_ma0r7wp6' ,  # 是否多经
    'jde_customer_code':'textField_m8e8g3lv',#jde_customer_code
    'jde_customer_name':'textField_mb7rs39i',#jde_customer_name
    'fz_store_code':'textField_mbc1lbzm',#fz_store_code
    'is_new_store':'textField_m9jkl9nx'#是否新店铺
}

def retry_on_failure(max_retries: int = 3, delay: int = 1):
    """
    重试装饰器
    
    Args:
        max_retries: 最大重试次数
        delay: 重试间隔（秒）
    """
    def decorator(func: Callable):
        @wraps(func)
        def wrapper(*args, **kwargs):
            retries = 0
            while retries < max_retries:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    retries += 1
                    if retries == max_retries:
                        raise
                    logging.warning(f"操作失败，{delay}秒后进行第{retries}次重试: {str(e)}")
                    time.sleep(delay)
            return None
        return wrapper
    return decorator

class StoreUserManager:
    def __init__(self):
        # 配置日志
        current_date = datetime.now().strftime('%Y%m%d')
        logging.basicConfig(
            filename=f'logs/sync_store_user_{current_date}.log',
            level=logging.INFO,  # 将日志级别改为DEBUG
            format='%(asctime)s - %(levelname)s - %(message)s',
            encoding='utf-8'
        )
        
        # 初始化数据库连接
        try:
            self.mysql_conn = pymysql.connect(**DB_CONFIG)
            logging.info("数据库连接成功")
        except Exception as e:
            logging.error(f"数据库连接失败: {str(e)}")
            raise
        
        # 获取钉钉access_token
        try:
            self.access_token = token.get_token()
            logging.info("获取钉钉access_token成功")
        except Exception as e:
            logging.error(f"获取钉钉access_token失败: {str(e)}")
            raise
            
        # 初始化手机号到userid的缓存
        self._phone_to_userid_cache = {}

        # 初始化宜搭客户端
        try:
            self.yida_client_1_0, self.yida_client_2_0 = self._create_yida_client()
            logging.info("宜搭客户端初始化成功")
        except Exception as e:
            logging.error(f"宜搭客户端初始化失败: {str(e)}")
            raise

        # 添加批量处理配置
        self.batch_size = 50  # 每批处理的记录数

    def _create_yida_client(self) -> Tuple[dingtalkyida_1_0Client, DingTalkYidaClient]:
        """
        创建宜搭客户端
        
        Returns:
            Tuple[dingtalkyida_1_0Client, DingTalkYidaClient]: 宜搭客户端实例（1.0和2.0版本）
        """
        config = open_api_models.Config(
            protocol='https',
            region_id='central'
        )
        return dingtalkyida_1_0Client(config), DingTalkYidaClient(config)

    def get_store_info(self) -> pd.DataFrame:
        """
        获取店铺信息，包含是否为新店铺的判断
        新店铺定义：上个月（不含）以前没有任何销售记录，但在上个月或本月（即近两个月内）有销售记录的店铺
        采用分步查询优化性能
        Returns:
            pd.DataFrame: 店铺信息数据框
        """
        try:
            cursor = self.mysql_conn.cursor()

            # 第一步：获取基础店铺信息
            logging.info("第一步：获取基础店铺信息...")
            base_sql = """
            SELECT
                b.code AS project_code,
                b.name AS project_name,
                a.code AS store_code,
                a.name AS store_name,
                CASE
                    WHEN a.status = 1 THEN '正常'
                    WHEN a.status = 0 THEN '禁用'
                END AS store_status,
                a.oa_account,
                a.jde_customer_code,
                a.jde_customer_name,
                a.fz_store_code,
                CASE
                    WHEN a.name LIKE '%多经%' THEN '是'
                    ELSE '否'
                END AS ifduojing,
                '' as userid  -- 添加userid字段，将在process_oa_accounts中填充
            FROM
                yx_b_tenants a
            JOIN
                yx_b_projects b ON a.project_code = b.code
            WHERE
                a.type = 0
                AND a.deleted = 0
                AND b.deleted = 0
                AND b.code IN (
                    '*********',
                    '*********',
                    '*********',
                    '*********',
                    '*********',
                    '*********',
                    '*********'
                )
            """

            cursor.execute(base_sql)
            result = cursor.fetchall()
            logging.info(f"获取基础店铺信息成功，共 {len(result)} 条记录")

            # 转换为DataFrame
            df = pd.DataFrame(result)

            if df.empty:
                logging.warning("没有找到符合条件的店铺")
                return df

            # 第二步：获取上个月或本月有销售记录的店铺（近两个月内）
            logging.info("第二步：获取上个月或本月有销售记录的店铺...")
            recent_sales_sql = """
            SELECT DISTINCT r.store_code
            FROM yx_b_sales_record r
            WHERE r.sales_time >= DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH), '%Y-%m-01')
              AND r.status = 2
              AND r.approval_flow >= 1
              AND r.store_code IN ({})
            """.format(','.join([f"'{code}'" for code in df['store_code'].tolist()]))

            cursor.execute(recent_sales_sql)
            recent_sales_stores = [row['store_code'] for row in cursor.fetchall()]
            logging.info(f"上个月或本月有销售记录的店铺数量: {len(recent_sales_stores)}")

            # 第三步：获取上个月以前有销售记录的店铺
            logging.info("第三步：获取上个月以前有销售记录的店铺...")
            old_sales_sql = """
            SELECT DISTINCT r.store_code
            FROM yx_b_sales_record r
            WHERE r.sales_time < DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH), '%Y-%m-01')
              AND r.status = 2
              AND r.approval_flow >= 1
              AND r.store_code IN ({})
            """.format(','.join([f"'{code}'" for code in recent_sales_stores]))

            cursor.execute(old_sales_sql)
            old_sales_stores = [row['store_code'] for row in cursor.fetchall()]
            logging.info(f"上个月以前有销售记录的店铺数量: {len(old_sales_stores)}")

            # 第四步：判断新店铺（上个月或本月有销售但上个月以前没有销售的店铺）
            new_stores = set(recent_sales_stores) - set(old_sales_stores)
            logging.info(f"检测到新店铺数量: {len(new_stores)}")
            if new_stores:
                logging.info(f"新店铺编码列表: {list(new_stores)}")

            # 第五步：为DataFrame添加is_new_store字段
            df['is_new_store'] = df['store_code'].apply(lambda x: '是' if x in new_stores else '否')

            return df

        except Exception as e:
            logging.error(f"获取店铺信息失败: {str(e)}")
            raise

    def get_user_info(self) -> pd.DataFrame:
        """
        获取用户信息
        Returns:
            pd.DataFrame: 用户信息数据框
        """
        try:
            cursor = self.mysql_conn.cursor()
            
            sql = """
            SELECT 
                account,
                name,
                phone,
                '' as userid 
            FROM 
                yx_b_admins 
            WHERE 
                status = 1 
                AND phone is not null
            """
            
            cursor.execute(sql)
            result = cursor.fetchall()
            logging.info(f"获取用户信息成功，共 {len(result)} 条记录")
            
            # 转换为DataFrame
            df = pd.DataFrame(result)
            return df
            
        except Exception as e:
            logging.error(f"获取用户信息失败: {str(e)}")
            raise

    def get_userid_by_phone(self, phone: str) -> Optional[str]:
        """
        通过手机号获取userid，使用缓存避免重复API调用
        Args:
            phone: 手机号
        Returns:
            Optional[str]: userid，如果获取失败返回None
        """
        try:
            # 检查缓存中是否已有该手机号的userid
            if phone in self._phone_to_userid_cache:
                # logging.info(f"从缓存获取userid: {phone} -> {self._phone_to_userid_cache[phone]}")
                return self._phone_to_userid_cache[phone]
            
            # 缓存中没有，调用API获取
            url = f"https://oapi.dingtalk.com/topapi/v2/user/getbymobile?access_token={self.access_token}"
            data = {"mobile": phone}
            
            response = requests.post(url, json=data)
            result = response.json()
            
            if result.get('errcode') == 0 and result.get('result'):
                userid = result['result'].get('userid')
                # 将结果存入缓存
                self._phone_to_userid_cache[phone] = userid
                # logging.info(f"获取userid成功并缓存: {phone} -> {userid}")
                return userid
            else:
                logging.warning(f"获取userid失败: {phone}, 错误信息: {result.get('errmsg')}")
                return None
                
        except Exception as e:
            logging.error(f"调用钉钉API失败: {str(e)}")
            return None

    def process_oa_accounts(self, store_df: pd.DataFrame, user_df: pd.DataFrame) -> pd.DataFrame:
        """
        处理oa_account字段，匹配userid，使用缓存优化API调用
        Args:
            store_df: 店铺信息数据框
            user_df: 用户信息数据框
        Returns:
            pd.DataFrame: 处理后的数据框，userid列：
                - 当oa_account为空时，userid为空字符串
                - 当oa_account存在但未查询到userid时，过滤掉该用户
                - 多个userid用逗号分隔
        """
        try:
            # 收集所有需要查询的手机号
            phones_to_query = set()
            account_to_phone = {}
            
            # 遍历所有oa_account，收集需要查询的手机号
            for _, row in store_df.iterrows():
                if pd.isna(row['oa_account']):
                    continue
                    
                accounts = [acc.strip() for acc in row['oa_account'].split(',')]
                for account in accounts:
                    if account not in account_to_phone:
                        user_info = user_df[user_df['account'] == account]
                        if not user_info.empty and pd.notna(user_info.iloc[0]['phone']):
                            phone = user_info.iloc[0]['phone']
                            account_to_phone[account] = phone
                            phones_to_query.add(phone)
            
            logging.info(f"需要查询的手机号数量: {len(phones_to_query)}")
            
            # 为每个oa_account创建userid列表
            def get_userids(oa_accounts):
                # 当oa_account为空时，返回空字符串
                if pd.isna(oa_accounts):
                    return ""
                    
                accounts = [acc.strip() for acc in oa_accounts.split(',')]
                userids = []
                
                for account in accounts:
                    if account in account_to_phone:
                        phone = account_to_phone[account]
                        userid = self.get_userid_by_phone(phone)
                        if userid:  # 只添加成功获取到userid的项
                            userids.append(userid)
                
                # 如果过滤后列表为空，返回空字符串
                return ','.join(userids) if userids else ""
            
            # 应用处理函数
            store_df['userid'] = store_df['oa_account'].apply(get_userids)
            logging.info(f"处理oa_account完成，缓存大小: {len(self._phone_to_userid_cache)}")
            
            return store_df
            
        except Exception as e:
            logging.error(f"处理oa_account失败: {str(e)}")
            raise

    def save_results(self, df: pd.DataFrame):
        """
        保存结果到Excel
        Args:
            df: 要保存的数据框
        """
        try:
            current_date = datetime.now().strftime('%Y%m%d')
            filename = f'data/sync_store/store_user_info_{current_date}.xlsx'
            
            # 保存到Excel
            df.to_excel(filename, index=False, engine='openpyxl')
            logging.info(f"数据已保存到文件: {filename}")
            
        except Exception as e:
            logging.error(f"保存数据失败: {str(e)}")
            raise

    def get_yida_store_info(self) -> pd.DataFrame:
        """
        获取宜搭店铺信息
        
        Returns:
            pd.DataFrame: 宜搭店铺信息数据框，包含以下字段：
                - store_code: 店铺编码
                - store_name: 店铺名称
                - userid: 钉钉userid
                - status: 状态
                - is_duojing: 是否多经
                - form_instance_id: 表单实例ID
        """
        try:
            all_data = []
            current_page = 1
            page_size = 100
            
            while True:
                headers = dingtalkyida__1__0_models.SearchFormDataSecondGenerationNoTableFieldHeaders()
                headers.x_acs_dingtalk_access_token = self.access_token
                
                request = dingtalkyida__1__0_models.SearchFormDataSecondGenerationNoTableFieldRequest(
                    page_number=current_page,
                    form_uuid=YIDA_CONFIG['FORM_UUID'],
                    system_token=YIDA_CONFIG['SYSTEM_TOKEN'],
                    page_size=page_size,
                    user_id=YIDA_CONFIG['USER_ID'],
                    app_type=YIDA_CONFIG['APP_TYPE']
                )
                
                result = self.yida_client_1_0.search_form_data_second_generation_no_table_field_with_options(
                    request, 
                    headers, 
                    util_models.RuntimeOptions()
                )
                
                # 添加详细的API响应结构日志
                if result:
                    logging.debug(f"API响应类型: {type(result)}")
                    if hasattr(result, 'body'):
                        logging.debug(f"API响应body类型: {type(result.body)}")
                        logging.debug(f"API响应body属性: {dir(result.body)}")
                        
                        # 记录data属性的信息
                        if hasattr(result.body, 'data'):
                            logging.debug(f"data属性类型: {type(result.body.data)}")
                            if result.body.data:
                                logging.debug(f"data属性内容示例: {result.body.data}")
                                if isinstance(result.body.data, list):
                                    logging.debug(f"data列表长度: {len(result.body.data)}")
                                    if len(result.body.data) > 0:
                                        logging.debug(f"data第一项类型: {type(result.body.data[0])}")
                
                # 检查API响应是否有效
                if not result or not result.body or not hasattr(result.body, 'data') or not result.body.data:
                    logging.info("API响应没有有效的数据，结束查询")
                    break
                
                # 处理API响应数据 - 修复迭代处理逻辑
                data_items = []
                
                # 根据data类型处理
                if isinstance(result.body.data, list):
                    logging.debug(f"处理列表类型的data，长度: {len(result.body.data)}")
                    data_items = result.body.data
                else:
                    # 如果data不是列表，尝试其他处理
                    logging.debug("data不是列表类型，尝试其他处理方式")
                    
                    # 方式1：尝试将data作为单个项处理
                    data_items = [result.body.data]
                    logging.debug(f"将data作为单个项处理，创建包含1个元素的列表")
                
                # 处理数据项
                for item in data_items:
                    # 记录项的类型和属性
                    logging.debug(f"处理数据项，类型: {type(item)}")
                    logging.debug(f"数据项属性: {dir(item)}")
                    
                    # 检查item是否有form_data属性
                    if not hasattr(item, 'form_data'):
                        logging.warning(f"数据项缺少form_data属性，跳过")
                        continue
                    
                    form_data = item.form_data
                    
                    # 添加完整表单数据调试日志
                    store_code = form_data.get(YIDA_FIELD_MAPPING['store_code'], '')
                    logging.debug(f"宜搭表单数据 - 店铺编码: {store_code}")
                    logging.debug(f"宜搭完整表单数据: {json.dumps(form_data, ensure_ascii=False, default=str)[:1000]}")
                    
                    # 检查userid相关字段是否存在
                    userid_query_field = YIDA_FIELD_MAPPING['userid_query']
                    userid_field = YIDA_FIELD_MAPPING['userid']
                    
                    if userid_query_field in form_data:
                        logging.debug(f"店铺 {store_code} 的 {userid_query_field} 字段存在，值类型: {type(form_data[userid_query_field])}")
                        logging.debug(f"店铺 {store_code} 的 {userid_query_field} 字段值: {form_data[userid_query_field]}")
                    else:
                        logging.debug(f"店铺 {store_code} 的 {userid_query_field} 字段不存在")
                        
                    if userid_field in form_data:
                        logging.debug(f"店铺 {store_code} 的 {userid_field} 字段存在，值类型: {type(form_data[userid_field])}")
                        logging.debug(f"店铺 {store_code} 的 {userid_field} 字段值: {form_data[userid_field]}")
                    else:
                        logging.debug(f"店铺 {store_code} 的 {userid_field} 字段不存在")
                    
                    # 提取userid并确保格式正确 - 优化逻辑，尝试不同字段
                    userid_value = form_data.get(YIDA_FIELD_MAPPING['userid_query'], '')
                    
                    # 记录完整的原始值，不截断
                    if userid_value:
                        try:
                            userid_full_str = json.dumps(userid_value, ensure_ascii=False)
                            logging.debug(f"店铺 {store_code} 的完整userid值: {userid_full_str}")
                        except Exception as e:
                            logging.debug(f"序列化userid值时出错: {str(e)}")
                    
                    # 如果userid_query字段为空，尝试使用userid字段
                    if not userid_value and YIDA_FIELD_MAPPING['userid'] in form_data:
                        userid_value = form_data.get(YIDA_FIELD_MAPPING['userid'], '')
                        if userid_value:
                            logging.info(f"店铺 {store_code} 使用替代字段 {YIDA_FIELD_MAPPING['userid']} 获取userid值")
                            # 记录替代字段的完整值
                            try:
                                userid_full_str = json.dumps(userid_value, ensure_ascii=False)
                                logging.debug(f"店铺 {store_code} 替代字段的完整userid值: {userid_full_str}")
                            except Exception as e:
                                logging.debug(f"序列化替代字段userid值时出错: {str(e)}")
                    
                    # 记录原始userid值类型，便于调试
                    if userid_value:
                        try:
                            logging.debug(f"宜搭原始userid值类型: {type(userid_value)}")
                            if isinstance(userid_value, (list, tuple)):
                                logging.debug(f"列表类型userid的长度: {len(userid_value)}")
                                # 单独记录每个元素，避免截断
                                for i, uid_item in enumerate(userid_value):
                                    logging.debug(f"userid元素[{i}]: {uid_item}")
                            else:
                                logging.debug(f"userid值: {userid_value}")
                        except Exception as e:
                            logging.debug(f"记录userid详情时出错: {str(e)}")
                    else:
                        logging.info(f"店铺 {store_code} 的userid值为空")
                    
                    # 检查是否有form_instance_id属性
                    form_instance_id = ""
                    if hasattr(item, 'form_instance_id'):
                        form_instance_id = item.form_instance_id
                    elif hasattr(item, 'formInstanceId'):
                        form_instance_id = item.formInstanceId
                    else:
                        logging.warning(f"数据项缺少form_instance_id属性，使用临时ID")
                        form_instance_id = f"TEMP_{store_code}_{int(time.time())}"
                    
                    store_info = {
                        'store_code': form_data.get(YIDA_FIELD_MAPPING['store_code'], ''),
                        'store_name': form_data.get(YIDA_FIELD_MAPPING['store_name'], ''),
                        'userid': userid_value,  # 保持原始格式，由_normalize_userid_format方法处理
                        'status': form_data.get(YIDA_FIELD_MAPPING['status'], ''),
                        'is_duojing': form_data.get(YIDA_FIELD_MAPPING['is_duojing'], ''),
                        'jde_customer_code': form_data.get(YIDA_FIELD_MAPPING['jde_customer_code'], ''),
                        'jde_customer_name': form_data.get(YIDA_FIELD_MAPPING['jde_customer_name'], ''),
                        'fz_store_code': form_data.get(YIDA_FIELD_MAPPING['fz_store_code'], ''),
                        'is_new_store': form_data.get(YIDA_FIELD_MAPPING['is_new_store'], ''),
                        'form_instance_id': form_instance_id  # 添加表单实例ID
                    }
                    all_data.append(store_info)
                
                # 检查是否需要继续分页查询
                if isinstance(result.body.data, list) and len(result.body.data) < page_size:
                    logging.debug("返回记录数小于页大小，结束查询")
                    break
                elif not isinstance(result.body.data, list):
                    logging.debug("非列表类型结果，结束查询")
                    break
                    
                current_page += 1
                logging.debug(f"继续查询下一页: {current_page}")
            
            # 转换为DataFrame
            df = pd.DataFrame(all_data)
            logging.info(f"获取宜搭店铺信息成功，共 {len(df)} 条记录")
            return df
            
        except Exception as e:
            logging.error(f"获取宜搭店铺信息失败: {str(e)}")
            logging.error(f"错误详情: {traceback.format_exc()}")
            raise

    def compare_store_info(self, db_df: pd.DataFrame, yida_df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """
        对比数据库和宜搭的店铺信息
        
        Args:
            db_df: 数据库店铺信息数据框
            yida_df: 宜搭店铺信息数据框
            
        Returns:
            Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]: 
                - 仅在数据库存在的记录（需要插入）
                - 仅在宜搭存在的记录（需要更新状态为禁用）
                - 字段值不一致的记录（需要更新）
        """
        try:
            # 添加店铺名称变更收集列表
            store_name_changes = []
            
            # 打印两个数据框的列名，用于诊断
            logging.info(f"数据库数据框列名: {db_df.columns.tolist()}")
            logging.info(f"宜搭数据框列名: {yida_df.columns.tolist()}")
            
            # 确保店铺编码列名一致
            db_df = db_df.rename(columns={'store_code': 'store_code'})
            yida_df = yida_df.rename(columns={'store_code': 'store_code'})
            
            # 设置店铺编码为索引，但保留原列
            db_df.set_index('store_code', inplace=True, drop=False)
            yida_df.set_index('store_code', inplace=True, drop=False)
            
            # 找出仅在数据库存在的记录（需要插入）
            only_in_db = db_df[~db_df.index.isin(yida_df.index)]
            logging.info(f"仅在数据库存在的记录数: {len(only_in_db)}")
            if not only_in_db.empty:
                logging.info(f"需要插入的记录: {only_in_db.index.tolist()}")
            
            # 找出仅在宜搭存在的记录（需要更新状态为禁用）
            only_in_yida = yida_df[~yida_df.index.isin(db_df.index)]
            logging.info(f"仅在宜搭存在的记录数: {len(only_in_yida)}")
            if not only_in_yida.empty:
                logging.info(f"需要更新状态为禁用的记录: {only_in_yida.index.tolist()}")
                # 更新这些记录的状态为禁用
                for _, row in only_in_yida.iterrows():
                    logging.info(f"记录 {row.name} 在数据库中不存在，将更新状态为禁用")
            
            # 找出共同存在的记录
            common_codes = db_df.index.intersection(yida_df.index)
            
            # 对比字段值
            diff_records = []
            for code in common_codes:
                db_row = db_df.loc[code]
                yida_row = yida_df.loc[code]
                
                # 增加详细的userid比较日志
                logging.debug(f"店铺 {code} userid比较 - 数据库原始值: {db_row['userid']}, 宜搭原始值: {yida_row['userid']}")
                
                # 标准化处理数据库和宜搭的userid，确保格式一致
                try:
                    db_userid_set = self._normalize_userid_format(db_row['userid'])
                except Exception as e:
                    logging.error(f"店铺 {code} 标准化数据库userid时出错: {str(e)}")
                    db_userid_set = set()  # 出错时使用空集合

                try:
                    yida_userid_set = self._normalize_userid_format(yida_row['userid'])
                except Exception as e:
                    logging.error(f"店铺 {code} 标准化宜搭userid时出错: {str(e)}")
                    yida_userid_set = set()  # 出错时使用空集合
                
                # 输出标准化处理后的结果
                logging.debug(f"店铺 {code} userid比较 - 数据库标准化后: {db_userid_set}, 宜搭标准化后: {yida_userid_set}")
                
                # 检查每个字段是否一致
                differences = {
                    'store_code': code,
                    'store_name_diff': db_row['store_name'] != yida_row['store_name'],
                    'userid_diff': db_userid_set != yida_userid_set,  # 使用集合比较
                    'status_diff': db_row['store_status'] != yida_row['status'],
                    'is_duojing_diff': db_row['ifduojing'] != yida_row['is_duojing'],
                    'jde_customer_code_diff': db_row['jde_customer_code'] != yida_row['jde_customer_code'],
                    'jde_customer_name_diff': db_row['jde_customer_name'] != yida_row['jde_customer_name'],
                    'fz_store_code_diff': db_row['fz_store_code'] != yida_row['fz_store_code'],
                    'is_new_store_diff': db_row['is_new_store'] != yida_row['is_new_store'],
                    'db_store_name': db_row['store_name'],
                    'yida_store_name': yida_row['store_name'],
                    'db_userid': db_row['userid'],
                    'yida_userid': yida_row['userid'],
                    'db_status': db_row['store_status'],
                    'yida_status': yida_row['status'],
                    'db_is_duojing': db_row['ifduojing'],
                    'yida_is_duojing': yida_row['is_duojing'],
                    'db_jde_customer_code': db_row['jde_customer_code'],
                    'yida_jde_customer_code': yida_row['jde_customer_code'],
                    'db_jde_customer_name': db_row['jde_customer_name'],
                    'yida_jde_customer_name': yida_row['jde_customer_name'],
                    'db_fz_store_code': db_row['fz_store_code'],
                    'yida_fz_store_code': yida_row['fz_store_code'],
                    'db_is_new_store': db_row['is_new_store'],
                    'yida_is_new_store': yida_row['is_new_store'],
                    'form_instance_id': yida_row['form_instance_id']
                }
                
                # 收集店铺名称变更信息
                if differences['store_name_diff']:
                    store_name_changes.append({
                        'store_code': code,
                        'old_name': yida_row['store_name'],
                        'new_name': db_row['store_name']
                    })
                    logging.info(f"检测到店铺名称变更 - 店铺编码: {code}, 原名称: {yida_row['store_name']}, 新名称: {db_row['store_name']}")
                
                # 记录具体哪些字段存在差异
                diff_fields = [k for k, v in differences.items() if k.endswith('_diff') and v]
                
                # 只有当有实际字段差异时，才添加到差异记录中并记录日志
                if diff_fields:
                    diff_records.append(differences)
                    logging.info(f"店铺 {code} 存在字段差异: {diff_fields}")
                    # 详细记录userid差异，方便排查
                    if 'userid_diff' in diff_fields:
                        logging.info(f"店铺 {code} userid差异 - 数据库: {db_userid_set}, 宜搭: {yida_userid_set}")
                        # 输出更多比较信息
                        if db_userid_set and not yida_userid_set:
                            logging.info(f"店铺 {code} - 数据库有userid但宜搭为空")
                        elif not db_userid_set and yida_userid_set:
                            logging.info(f"店铺 {code} - 宜搭有userid但数据库为空")
                        else:
                            # 计算差集，查看具体哪些userid不同
                            db_only = db_userid_set - yida_userid_set
                            yida_only = yida_userid_set - db_userid_set
                            if db_only:
                                logging.info(f"店铺 {code} - 仅在数据库存在的userid: {db_only}")
                            if yida_only:
                                logging.info(f"店铺 {code} - 仅在宜搭存在的userid: {yida_only}")
            
            diff_df = pd.DataFrame(diff_records)
            
            logging.info(f"数据对比完成：")
            logging.info(f"- 需要插入的记录数: {len(only_in_db)}")
            logging.info(f"- 需要更新状态为禁用的记录数: {len(only_in_yida)}")
            logging.info(f"- 需要更新的记录数: {len(diff_df)}")
            logging.info(f"- 店铺名称变更数: {len(store_name_changes)}")
            
            # 如果有店铺名称变更，发送邮件通知
            if store_name_changes:
                self.process_store_name_changes(store_name_changes)
            
            return only_in_db, only_in_yida, diff_df
            
        except Exception as e:
            logging.error(f"对比店铺信息失败: {str(e)}")
            raise

    def generate_diff_report(self, only_in_db: pd.DataFrame, only_in_yida: pd.DataFrame, diff_df: pd.DataFrame):
        """
        生成差异报告
        
        Args:
            only_in_db: 仅在数据库存在的记录
            only_in_yida: 仅在宜搭存在的记录
            diff_df: 字段值不一致的记录
        """
        try:
            current_date = datetime.now().strftime('%Y%m%d')
            filename = f'data/sync_store/store_info_diff_report_{current_date}.xlsx'
            
            # 创建Excel写入器
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 写入仅在数据库存在的记录
                only_in_db.to_excel(writer, sheet_name='仅在数据库存在')
                
                # 写入仅在宜搭存在的记录
                only_in_yida.to_excel(writer, sheet_name='仅在宜搭存在')
                
                # 写入字段值不一致的记录
                if not diff_df.empty:
                    diff_df.to_excel(writer, sheet_name='字段值不一致')
                
                # 写入汇总统计
                summary_data = {
                    '统计项': ['仅在数据库存在的记录数', '仅在宜搭存在的记录数', '字段值不一致的记录数'],
                    '数量': [len(only_in_db), len(only_in_yida), len(diff_df)]
                }
                pd.DataFrame(summary_data).to_excel(writer, sheet_name='汇总统计', index=False)
            
            logging.info(f"差异报告已保存到文件: {filename}")
            
        except Exception as e:
            logging.error(f"生成差异报告失败: {str(e)}")
            raise

    def _normalize_userid_format(self, userid_value):
        """
        将不同格式的 userid 统一转换为排序后的集合，用于比较
        
        Args:
            userid_value: userid 值，可能是字符串、JSON数组字符串或其他格式
            
        Returns:
            set: 排序后的userid集合
        """
        # 初始化结果集合
        userids = set()
        
        # 为调试添加完整的输入值记录
        try:
            logging.debug(f"_normalize_userid_format开始处理: 输入类型={type(userid_value)}")
            if hasattr(userid_value, '__str__'):
                full_value = str(userid_value)
                logging.debug(f"输入值完整内容: {full_value}")
        except Exception as e:
            logging.debug(f"记录输入值时出错: {str(e)}")
               
        # 处理字符串
        if isinstance(userid_value, str):
            logging.debug("输入值为字符串")           
            # 处理普通字符串
            if userid_value.strip():
                logging.debug("处理普通字符串")
                for uid in userid_value.split(','):
                    if uid and uid.strip():
                        userids.add(uid.strip())
                        logging.debug(f"添加userid: {uid.strip()}")
            
            logging.debug(f"字符串处理完成，结果集合: {userids}")
            return userids
        
        # 处理列表、元组、集合
        if isinstance(userid_value, list):
            logging.debug(f"输入值为列表,长度={len(userid_value)}")
            userids=set(userid_value)
            logging.debug(f"列表处理完成，结果集合: {userids}")
            return userids
        
    def _convert_db_to_yida_fields(self, db_row: pd.Series) -> Dict[str, Any]:
        """将数据库行转换为宜搭表单字段格式"""
        try:
            def convert_value(value):
                if pd.isna(value) or value == '':
                    return ''
                return str(value).strip()
            
            def convert_employee_field(value):
                """将值转换为宜搭员工字段格式（数组）"""
                # 空值处理
                if pd.isna(value) or value == '':
                    return []
                
                # 已经是列表格式的情况
                if isinstance(value, (list, tuple)):
                    # 确保每个元素都是字符串
                    return [str(userid).strip() for userid in value if userid is not None and str(userid).strip()]
                
                # 字符串格式处理 - 将逗号分隔的字符串转换为数组
                if isinstance(value, str):
                    return [userid.strip() for userid in str(value).split(',') if userid.strip()]
                
                # 其他类型尝试转换为字符串后处理
                try:
                    return convert_employee_field(str(value))
                except:
                    return []
            
            form_data = {
                YIDA_FIELD_MAPPING['project_name']: convert_value(db_row['project_name']),  # 项目名称直接以字符串格式插入
                YIDA_FIELD_MAPPING['store_code']: convert_value(db_row['store_code']),  # 使用验证后的店铺编码
                YIDA_FIELD_MAPPING['store_name']: convert_value(db_row['store_name']),
                YIDA_FIELD_MAPPING['userid']: convert_employee_field(db_row['userid']),  # 使用userid字段 (用于更新和插入)
                YIDA_FIELD_MAPPING['status']: convert_value(db_row['store_status']),
                YIDA_FIELD_MAPPING['is_duojing']: convert_value(db_row['ifduojing']),
                YIDA_FIELD_MAPPING['jde_customer_code']: convert_value(db_row['jde_customer_code']),
                YIDA_FIELD_MAPPING['jde_customer_name']: convert_value(db_row['jde_customer_name']),
                YIDA_FIELD_MAPPING['fz_store_code']: convert_value(db_row['fz_store_code']),
                YIDA_FIELD_MAPPING['is_new_store']: convert_value(db_row['is_new_store'])  # 是否新店铺
            }
            
            logging.info(f"转换后的数据: {json.dumps(form_data, ensure_ascii=False)}")
            return form_data
            
        except Exception as e:
            logging.error(f"字段转换失败: {str(e)}")
            logging.error(f"问题数据: {json.dumps(db_row.to_dict(), ensure_ascii=False, default=str)}")
            raise

    def _create_yida_form_data(self, form_data: Dict[str, Any]) -> str:
        """
        创建宜搭表单数据
        
        Args:
            form_data: 表单数据
            
        Returns:
            str: JSON格式的表单数据
        """
        return json.dumps(form_data)

    @retry_on_failure(max_retries=3, delay=2)
    def _batch_create_form_data(self, form_data_list: List[Dict[str, Any]]) -> List[str]:
        """
        批量创建表单数据
        
        Args:
            form_data_list: 表单数据列表
            
        Returns:
            List[str]: 成功创建的表单实例ID列表
        """
        success_ids = []
        try:
            # 将数据转换为JSON字符串列表
            form_data_json_list = [self._create_yida_form_data(form_data) for form_data in form_data_list]
            
            headers = dingtalkyida__1__0_models.BatchSaveFormDataHeaders()
            headers.x_acs_dingtalk_access_token = self.access_token
            
            request = dingtalkyida__1__0_models.BatchSaveFormDataRequest(
                no_execute_expression=True,
                form_uuid=YIDA_CONFIG['FORM_UUID'],
                app_type=YIDA_CONFIG['APP_TYPE'],
                asynchronous_execution=True,
                system_token=YIDA_CONFIG['SYSTEM_TOKEN'],
                keep_running_after_exception=True,
                user_id=YIDA_CONFIG['USER_ID'],
                form_data_json_list=form_data_json_list
            )
            
            result = self.yida_client_1_0.batch_save_form_data_with_options(
                request, 
                headers, 
                util_models.RuntimeOptions()
            )
            
            if result and result.body and result.body.result:
                success_ids.extend(result.body.result)
                logging.info(f"批量创建表单数据成功: {len(form_data_list)} 条记录")
            else:
                logging.warning(f"批量创建表单数据响应成功但未返回结果")
                logging.warning(f"请求数据: {form_data_json_list}")
                
        except Exception as e:
            logging.error(f"批量创建表单数据失败: {str(e)}")
            
        return success_ids

    @retry_on_failure(max_retries=3, delay=2)
    def _batch_update_form_data(self, update_data_list: List[Dict[str, Any]]) -> List[str]:
        """
        批量更新表单数据
        
        Args:
            update_data_list: 更新数据列表，每个元素包含form_instance_id和form_data
            
        Returns:
            List[str]: 成功更新的表单实例ID列表
        """
        success_ids = []
        for update_data in update_data_list:
            try:
                form_data_str = self._create_yida_form_data(update_data['form_data'])
                
                headers = yida_models.UpdateFormDataHeaders()
                headers.x_acs_dingtalk_access_token = self.access_token
                
                request = yida_models.UpdateFormDataRequest(
                    form_uuid=YIDA_CONFIG['FORM_UUID'],
                    system_token=YIDA_CONFIG['SYSTEM_TOKEN'],
                    user_id=YIDA_CONFIG['USER_ID'],
                    app_type=YIDA_CONFIG['APP_TYPE'],
                    form_instance_id=update_data['form_instance_id'],
                    update_form_data_json=form_data_str,
                    use_alias=False,
                    use_latest_version=False
                )
                
                result = self.yida_client_2_0.update_form_data_with_options(
                    request, 
                    headers, 
                    util_models.RuntimeOptions()
                )
                
                # 检查响应是否成功
                if result and hasattr(result, 'status_code') and result.status_code == 200:
                    success_ids.append(update_data['form_instance_id'])
                    logging.info(f"批量更新表单数据成功: {update_data['form_data'].get('store_code', '')}")
                else:
                    logging.warning(f"批量更新表单数据响应异常: {result}")
                
            except Exception as e:
                logging.error(f"批量更新表单数据失败: {str(e)}")
                continue
                
        return success_ids

    def update_yida_form(self, db_df: pd.DataFrame, yida_df: pd.DataFrame):
        """
        更新宜搭表单数据
        
        Args:
            db_df: 数据库店铺信息数据框
            yida_df: 宜搭店铺信息数据框
        """
        try:
            logging.info("开始更新宜搭表单数据...")
            logging.info(f"数据库记录数: {len(db_df)}")
            logging.info(f"宜搭记录数: {len(yida_df)}")
            
            # 检查宜搭数据是否为空
            if len(yida_df) == 0:
                logging.info("宜搭数据为空，所有数据库记录将作为新增处理")
                # 创建空的DataFrame作为宜搭数据，确保有相同的列结构
                empty_yida_df = pd.DataFrame(columns=['store_code', 'store_name', 'userid', 'status', 'is_duojing', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'is_new_store', 'form_instance_id'])
                # 所有数据库记录都视为"仅在数据库存在"
                only_in_db, only_in_yida, diff_df = db_df.copy(), empty_yida_df, pd.DataFrame()
            else:
                # 获取需要更新的记录
                only_in_db, only_in_yida, diff_df = self.compare_store_info(db_df, yida_df)
            
            # 处理仅在数据库存在的记录（需要插入）
            if not only_in_db.empty:
                logging.info(f"开始处理需要插入的记录，共 {len(only_in_db)} 条")
                form_data_list = []
                for idx, row in only_in_db.iterrows():
                    logging.info(f"正在处理第 {len(form_data_list) + 1} 条插入记录 - store_code: {idx}")
                    form_data = self._convert_db_to_yida_fields(row)
                    form_data_list.append(form_data)
                    
                    # 达到批量处理大小或最后一批时进行处理
                    if len(form_data_list) >= self.batch_size:
                        logging.info(f"达到批量处理大小，开始批量插入 {len(form_data_list)} 条记录")
                        success_ids = self._batch_create_form_data(form_data_list)
                        logging.info(f"批量插入成功，form_instance_ids: {success_ids}")
                        form_data_list = []
                
                # 处理剩余的记录
                if form_data_list:
                    logging.info(f"处理剩余 {len(form_data_list)} 条插入记录")
                    success_ids = self._batch_create_form_data(form_data_list)
                    logging.info(f"批量插入成功，form_instance_ids: {success_ids}")
                
                # 发送新店铺插入邮件通知
                try:
                    logging.info(f"发送新店铺插入邮件通知")
                    self.process_new_store_insertions(only_in_db)
                except Exception as e:
                    logging.error(f"发送新店铺插入邮件通知失败，但不影响主要功能: {str(e)}")
            
            # 处理仅在宜搭存在的记录（需要更新状态为禁用）
            if not only_in_yida.empty:
                logging.info(f"开始处理需要更新状态为禁用的记录，共 {len(only_in_yida)} 条")
                update_data_list = []
                for idx, row in only_in_yida.iterrows():
                    logging.info(f"正在处理第 {len(update_data_list) + 1} 条禁用记录 - store_code: {idx}")
                    # 创建更新数据，将状态设置为禁用
                    form_data = {
                        YIDA_FIELD_MAPPING['store_code']: idx,  # 使用索引作为store_code
                        YIDA_FIELD_MAPPING['store_name']: row['store_name'],
                        YIDA_FIELD_MAPPING['userid']: row['userid'],
                        YIDA_FIELD_MAPPING['status']: '禁用',  # 更新状态为禁用
                        YIDA_FIELD_MAPPING['is_duojing']: row['is_duojing'],
                        YIDA_FIELD_MAPPING['jde_customer_code']: row['jde_customer_code'],
                        YIDA_FIELD_MAPPING['jde_customer_name']: row['jde_customer_name'],
                        YIDA_FIELD_MAPPING['fz_store_code']: row['fz_store_code'],
                        YIDA_FIELD_MAPPING['is_new_store']: row.get('is_new_store', '否')  # 是否新店铺，默认为否
                    }
                    
                    update_data = {
                        'form_instance_id': row['form_instance_id'],
                        'form_data': form_data
                    }
                    update_data_list.append(update_data)
                    
                    # 达到批量处理大小或最后一批时进行处理
                    if len(update_data_list) >= self.batch_size:
                        logging.info(f"达到批量处理大小，开始批量更新 {len(update_data_list)} 条记录")
                        success_ids = self._batch_update_form_data(update_data_list)
                        logging.info(f"批量更新成功，form_instance_ids: {success_ids}")
                        update_data_list = []
                
                # 处理剩余的记录
                if update_data_list:
                    logging.info(f"处理剩余 {len(update_data_list)} 条禁用记录")
                    success_ids = self._batch_update_form_data(update_data_list)
                    logging.info(f"批量更新成功，form_instance_ids: {success_ids}")
            
            # 处理字段值不一致的记录（需要更新）
            if not diff_df.empty:
                logging.info(f"开始处理需要更新的记录，共 {len(diff_df)} 条")
                update_data_list = []
                for idx, row in diff_df.iterrows():
                    store_code = row['store_code']
                    logging.info(f"正在处理第 {len(update_data_list) + 1} 条更新记录 - store_code: {store_code}")
                    
                    # 从db_df中获取完整记录
                    db_row = db_df[db_df['store_code'] == store_code]
                    if not db_row.empty:
                        form_data = self._convert_db_to_yida_fields(db_row.iloc[0])
                        
                        # 从yida_df中获取form_instance_id
                        yida_row = yida_df[yida_df['store_code'] == store_code]
                        if not yida_row.empty:
                            form_instance_id = yida_row.iloc[0]['form_instance_id']
                            update_data = {
                                'form_instance_id': form_instance_id,
                                'form_data': form_data
                            }
                            update_data_list.append(update_data)
                    
                    # 达到批量处理大小或最后一批时进行处理
                    if len(update_data_list) >= self.batch_size:
                        logging.info(f"达到批量处理大小，开始批量更新 {len(update_data_list)} 条记录")
                        success_ids = self._batch_update_form_data(update_data_list)
                        logging.info(f"批量更新成功，form_instance_ids: {success_ids}")
                        update_data_list = []
                
                # 处理剩余的记录
                if update_data_list:
                    logging.info(f"处理剩余 {len(update_data_list)} 条更新记录")
                    success_ids = self._batch_update_form_data(update_data_list)
                    logging.info(f"批量更新成功，form_instance_ids: {success_ids}")
            
            logging.info("宜搭表单更新完成")
            
        except Exception as e:
            logging.error(f"更新宜搭表单失败: {str(e)}")
            raise

    def close(self):
        """关闭数据库连接"""
        if hasattr(self, 'mysql_conn'):
            self.mysql_conn.close()
            logging.info("数据库连接已关闭")

    def process_store_name_changes(self, store_name_changes: List[Dict[str, str]]):
        """
        处理店铺名称变更：更新宜搭表单数据并发送邮件通知
        
        Args:
            store_name_changes: 店铺名称变更信息列表，每个元素包含store_code, old_name, new_name
        """
        if not store_name_changes:
            logging.info("没有店铺名称变更，不进行处理")
            return
        
        try:
            # 更新宜搭表单数据
            for change in store_name_changes:
                store_code = change['store_code']
                new_name = change['new_name']
                old_name = change['old_name']
                
                logging.info(f"开始处理店铺名称变更 - 店铺编码: {store_code}, 原名称: {old_name}, 新名称: {new_name}")
                
                # 更新第一个宜搭表单
                try:
                    logging.info(f"查询第一个宜搭表单 - 店铺编码: {store_code}")
                    form_data1 = self.get_form_data1(store_code=store_code)
                    if form_data1:
                        for item in form_data1:
                            form_instance_id = item.get('formInstanceId', '')
                            if form_instance_id:
                                logging.info(f"更新第一个宜搭表单 - 店铺编码: {store_code}, 表单ID: {form_instance_id}")
                                self.update_form_data1(form_instance_id, new_name)
                                logging.info(f"更新第一个宜搭表单成功 - 店铺编码: {store_code}")
                            else:
                                logging.warning(f"第一个宜搭表单缺少formInstanceId - 店铺编码: {store_code}")
                    else:
                        logging.info(f"未找到第一个宜搭表单数据 - 店铺编码: {store_code}")
                except Exception as e:
                    logging.error(f"更新第一个宜搭表单失败 - 店铺编码: {store_code}, 错误: {str(e)}")
                
                # 更新第二个宜搭表单
                try:
                    logging.info(f"查询第二个宜搭表单 - 店铺编码: {store_code}")
                    form_data2 = self.get_form_data2(store_code=store_code)
                    if form_data2:
                        for item in form_data2:
                            form_instance_id = item.get('formInstanceId', '')
                            if form_instance_id:
                                logging.info(f"更新第二个宜搭表单 - 店铺编码: {store_code}, 表单ID: {form_instance_id}")
                                self.update_form_data2(form_instance_id, new_name)
                                logging.info(f"更新第二个宜搭表单成功 - 店铺编码: {store_code}")
                            else:
                                logging.warning(f"第二个宜搭表单缺少formInstanceId - 店铺编码: {store_code}")
                    else:
                        logging.info(f"未找到第二个宜搭表单数据 - 店铺编码: {store_code}")
                except Exception as e:
                    logging.error(f"更新第二个宜搭表单失败 - 店铺编码: {store_code}, 错误: {str(e)}")
                
                logging.info(f"店铺名称变更处理完成 - 店铺编码: {store_code}")
            
            # 生成邮件主题
            current_date = datetime.now().strftime('%Y-%m-%d')
            subject = f"【系统通知】店铺名称更新 - {current_date}"
            
            # 生成邮件正文（HTML格式）
            html_body = f"""
            <html>
                <head>
                    <style>
                        table {{
                            border-collapse: collapse;
                            width: 100%;
                        }}
                        th, td {{
                            border: 1px solid #ddd;
                            padding: 8px;
                            text-align: left;
                        }}
                        th {{
                            background-color: #f2f2f2;
                        }}
                        tr:nth-child(even) {{
                            background-color: #f9f9f9;
                        }}
                    </style>
                </head>
                <body>
                    <h2>店铺名称更新通知</h2>
                    <p>系统检测到以下店铺名称发生变更：</p>
                    <table>
                        <tr>
                            <th>店铺编码</th>
                            <th>原名称</th>
                            <th>新名称</th>
                        </tr>
            """
            
            # 添加每个变更的店铺信息
            for change in store_name_changes:
                html_body += f"""
                        <tr>
                            <td>{change['store_code']}</td>
                            <td>{change['old_name']}</td>
                            <td>{change['new_name']}</td>
                        </tr>
                """
            
            # 完成HTML
            html_body += """
                    </table>
                    <p>此邮件由系统自动发送，请勿直接回复。</p>
                </body>
            </html>
            """
            
            # 发送邮件
            send_email(subject, html_body)
            logging.info(f"店铺名称更新邮件发送成功，共 {len(store_name_changes)} 条记录")
            
        except Exception as e:
            logging.error(f"处理店铺名称变更失败: {str(e)}")
            logging.error(traceback.format_exc())

    def process_new_store_insertions(self, new_stores: pd.DataFrame):
        """
        处理新店铺插入：发送邮件通知
        
        Args:
            new_stores: 新插入的店铺信息数据框
        """
        if new_stores.empty:
            logging.info("没有新插入的店铺，不进行处理")
            return
        
        try:
            # 生成邮件主题
            current_date = datetime.now().strftime('%Y-%m-%d')
            subject = f"【系统通知】新店铺信息插入 - {current_date}"
            
            # 生成邮件正文（HTML格式）
            html_body = f"""
            <html>
                <head>
                    <style>
                        table {{
                            border-collapse: collapse;
                            width: 100%;
                        }}
                        th, td {{
                            border: 1px solid #ddd;
                            padding: 8px;
                            text-align: left;
                        }}
                        th {{
                            background-color: #f2f2f2;
                        }}
                        tr:nth-child(even) {{
                            background-color: #f9f9f9;
                        }}
                    </style>
                </head>
                <body>
                    <h2>新店铺信息插入通知</h2>
                    <p>系统已成功将以下新店铺信息插入到宜搭表单：</p>
                    <table>
                        <tr>
                            <th>店铺编码</th>
                            <th>店铺名称</th>
                            <th>项目名称</th>
                            <th>状态</th>
                            <th>是否多经</th>
                            <th>是否新店铺</th>
                        </tr>
            """

            # 添加每个新插入的店铺信息
            for _, row in new_stores.iterrows():
                html_body += f"""
                        <tr>
                            <td>{row['store_code']}</td>
                            <td>{row['store_name']}</td>
                            <td>{row.get('project_name', '')}</td>
                            <td>{row['store_status']}</td>
                            <td>{row['ifduojing']}</td>
                            <td>{row.get('is_new_store', '否')}</td>
                        </tr>
                """

            # 完成HTML
            html_body += """
                    </table>
                    <p>此邮件由系统自动发送，请勿直接回复。</p>
                </body>
            </html>
            """

            # 发送邮件
            send_email(subject, html_body)
            logging.info(f"新店铺插入邮件发送成功，共 {len(new_stores)} 条记录")
            
        except Exception as e:
            logging.error(f"发送新店铺插入邮件失败: {str(e)}")
            logging.error(traceback.format_exc())

    def process_yida_json_data(self, json_data: Dict) -> pd.DataFrame:
        """
        直接处理宜搭API返回的JSON数据
        
        Args:
            json_data: 宜搭API返回的JSON数据
            
        Returns:
            pd.DataFrame: 处理后的数据框
        """
        try:
            logging.info("开始处理宜搭JSON数据")
            
            # 检查JSON数据格式
            if not isinstance(json_data, dict):
                logging.error(f"JSON数据格式错误，期望dict类型，实际为: {type(json_data)}")
                return pd.DataFrame()
            
            # 记录JSON数据结构
            logging.debug(f"JSON数据键: {json_data.keys()}")
            
            # 提取formData
            form_data = json_data.get('formData', {})
            if not form_data:
                logging.error("JSON数据中没有formData字段")
                return pd.DataFrame()
            
            # 提取formInstId
            form_instance_id = json_data.get('formInstId', '')
            if not form_instance_id:
                form_instance_id = json_data.get('formInstanceId', '')
                if not form_instance_id:
                    logging.warning("JSON数据中没有formInstId/formInstanceId字段，使用临时ID")
                    store_code = form_data.get(YIDA_FIELD_MAPPING['store_code'], '')
                    form_instance_id = f"TEMP_ID_{store_code}_{int(time.time())}"
            
            # 记录表单数据
            store_code = form_data.get(YIDA_FIELD_MAPPING['store_code'], '')
            logging.info(f"处理店铺数据: {store_code}")
            logging.debug(f"表单数据: {json.dumps(form_data, ensure_ascii=False, default=str)[:1000]}")
            
            # 提取userid
            userid_value = None
            
            # 尝试不同的字段名
            if YIDA_FIELD_MAPPING['userid_query'] in form_data:
                userid_value = form_data.get(YIDA_FIELD_MAPPING['userid_query'])
                logging.debug(f"从userid_query字段获取到userid值: {userid_value}")
            
            if not userid_value and YIDA_FIELD_MAPPING['userid'] in form_data:
                userid_value = form_data.get(YIDA_FIELD_MAPPING['userid'])
                logging.debug(f"从userid字段获取到userid值: {userid_value}")
            
            # 记录userid值
            if userid_value:
                try:
                    if isinstance(userid_value, (list, tuple)):
                        logging.debug(f"userid值为列表，长度: {len(userid_value)}")
                        for i, uid in enumerate(userid_value):
                            logging.debug(f"userid[{i}]: {uid}")
                    else:
                        logging.debug(f"userid值: {userid_value}")
                except Exception as e:
                    logging.warning(f"记录userid值时出错: {str(e)}")
            else:
                logging.warning("没有找到userid值")
            
            # 创建store_info字典
            store_info = {
                'store_code': form_data.get(YIDA_FIELD_MAPPING['store_code'], ''),
                'store_name': form_data.get(YIDA_FIELD_MAPPING['store_name'], ''),
                'userid': userid_value,
                'status': form_data.get(YIDA_FIELD_MAPPING['status'], ''),
                'is_duojing': form_data.get(YIDA_FIELD_MAPPING['is_duojing'], ''),
                'jde_customer_code': form_data.get(YIDA_FIELD_MAPPING['jde_customer_code'], ''),
                'jde_customer_name': form_data.get(YIDA_FIELD_MAPPING['jde_customer_name'], ''),
                'fz_store_code': form_data.get(YIDA_FIELD_MAPPING['fz_store_code'], ''),
                'is_new_store': form_data.get(YIDA_FIELD_MAPPING['is_new_store'], ''),
                'form_instance_id': form_instance_id
            }
            
            # 创建DataFrame
            df = pd.DataFrame([store_info])
            logging.info(f"成功处理宜搭JSON数据，生成1条记录")
            return df
            
        except Exception as e:
            logging.error(f"处理宜搭JSON数据时出错: {str(e)}")
            logging.error(f"错误详情: {traceback.format_exc()}")
            return pd.DataFrame()

    def get_form_data1(self, page_size: int = 100, store_code: str = None) -> List[Dict[str, Any]]:
        """
        获取宜搭表单数据-商户上报明细台帐
        Args:
            page_size: 每页数据条数，默认100
            store_code: 店铺编码
        Returns:
            List[Dict[str, Any]]: 表单数据列表
        """
        search_condition = [{
        "key": "textField_m9nw1k6y",
        "value": store_code,
        "type": "TEXT",
        "operator": "eq",
        "componentName": "TextField"
        }]
        try:
            all_data = []
            current_page = 1
            
            while True:
                headers = dingtalkyida__1__0_models.SearchFormDataSecondGenerationNoTableFieldHeaders()
                headers.x_acs_dingtalk_access_token = self.access_token
                
                request = dingtalkyida__1__0_models.SearchFormDataSecondGenerationNoTableFieldRequest(
                    page_number=current_page,
                    form_uuid=YIDA_CONFIG['FORM_UUID1'],
                    search_condition=json.dumps(search_condition) if search_condition else None,
                    system_token=YIDA_CONFIG['SYSTEM_TOKEN'],
                    page_size=page_size,
                    user_id=YIDA_CONFIG['USER_ID'],
                    app_type=YIDA_CONFIG['APP_TYPE']
                )
                
                # 记录请求参数
                logging.info(f"Request Parameters - Page {current_page}:")
                logging.info(f"Headers: {headers}")
                logging.info(f"Request: {request}")
                
                result = self.yida_client_1_0.search_form_data_second_generation_no_table_field_with_options(
                    request, 
                    headers, 
                    util_models.RuntimeOptions()
                )
                
                # 记录响应结果
                logging.info(f"Response - Page {current_page}:")
                # logging.info(f"Result: {result.body}")
                
                if not result or not result.body or not result.body.data:
                    break
                
                # 提取指定字段
                for item in result.body.data:
                    filtered_data = {
                        'formInstanceId': item.form_instance_id,
                        'formData': item.form_data
                    }
                    all_data.append(filtered_data)
                
                logging.info(f"第 {current_page} 页获取到 {len(result.body.data)} 条记录")
                time.sleep(0.2)
                # 如果获取的数据少于页大小，说明已经是最后一页
                if len(result.body.data) < page_size:
                    break
                    
                current_page += 1
            
            logging.info(f"查询完成，共获取到 {len(all_data)} 条记录")
            return all_data
            
        except Exception as e:
            error_msg = f"获取表单数据失败: {str(e)}"
            if hasattr(e, 'code') and hasattr(e, 'message'):
                error_msg += f" (错误码: {e.code}, 错误信息: {e.message})"
            raise Exception(error_msg)

    def update_form_data1(self, form_instance_id: str,  store_name: str):
        """更新宜搭表单数据-商户上报明细台帐"""
        try:
            client = DingTalkYidaClient(open_api_models.Config(
                protocol='https',
                region_id='central'
            ))
            processed_data = {
                "textField_m9nw1k6z": store_name
            }
            headers = yida_models.UpdateFormDataHeaders()
            headers.x_acs_dingtalk_access_token = self.access_token
                      
            request = yida_models.UpdateFormDataRequest()
            request.app_type = YIDA_CONFIG['APP_TYPE']
            request.system_token = YIDA_CONFIG['SYSTEM_TOKEN']
            request.user_id = YIDA_CONFIG['USER_ID']
            request.language = YIDA_CONFIG['LANGUAGE']
            request.form_instance_id = form_instance_id
            request.form_uuid = YIDA_CONFIG['FORM_UUID1']
            request.update_form_data_json = json.dumps(processed_data, ensure_ascii=False)
            request.use_alias = False
            request.use_latest_version = False
            
            response = client.update_form_data_with_options(request, headers, util_models.RuntimeOptions())
            logging.info(f"更新表单数据成功: {form_instance_id}")
            return response
            
        except Exception as e:
            logging.error(f"更新表单数据失败: {str(e)}")
            raise

    def get_form_data2(self, page_size: int = 100, store_code: str = None) -> List[Dict[str, Any]]:
        """
        获取宜搭表单数据-销售上报月汇总
        Args:
            page_size: 每页数据条数，默认100
            store_code: 店铺编码
        Returns:
            List[Dict[str, Any]]: 表单数据列表
        """
        search_condition = [{
        "key": "textField_m9tojheq",
        "value": store_code,
        "type": "TEXT",
        "operator": "eq",
        "componentName": "TextField"
        }]
        try:
            all_data = []
            current_page = 1
            
            while True:
                headers = dingtalkyida__1__0_models.SearchFormDataSecondGenerationNoTableFieldHeaders()
                headers.x_acs_dingtalk_access_token = self.access_token
                
                request = dingtalkyida__1__0_models.SearchFormDataSecondGenerationNoTableFieldRequest(
                    page_number=current_page,
                    form_uuid=YIDA_CONFIG['FORM_UUID2'],
                    search_condition=json.dumps(search_condition) if search_condition else None,
                    system_token=YIDA_CONFIG['SYSTEM_TOKEN'],
                    page_size=page_size,
                    user_id=YIDA_CONFIG['USER_ID'],
                    app_type=YIDA_CONFIG['APP_TYPE']
                )
                
                # 记录请求参数
                logging.info(f"Request Parameters - Page {current_page}:")
                logging.info(f"Headers: {headers}")
                logging.info(f"Request: {request}")
                
                result = self.yida_client_1_0.search_form_data_second_generation_no_table_field_with_options(
                    request, 
                    headers, 
                    util_models.RuntimeOptions()
                )
                
                # 记录响应结果
                logging.info(f"Response - Page {current_page}:")
                # logging.info(f"Result: {result.body}")
                
                if not result or not result.body or not result.body.data:
                    break
                
                # 提取指定字段
                for item in result.body.data:
                    filtered_data = {
                        'formInstanceId': item.form_instance_id,
                        'formData': item.form_data
                    }
                    all_data.append(filtered_data)
                
                logging.info(f"第 {current_page} 页获取到 {len(result.body.data)} 条记录")
                time.sleep(0.2)
                # 如果获取的数据少于页大小，说明已经是最后一页
                if len(result.body.data) < page_size:
                    break
                    
                current_page += 1
            
            logging.info(f"查询完成，共获取到 {len(all_data)} 条记录")
            return all_data
            
        except Exception as e:
            error_msg = f"获取表单数据失败: {str(e)}"
            if hasattr(e, 'code') and hasattr(e, 'message'):
                error_msg += f" (错误码: {e.code}, 错误信息: {e.message})"
            raise Exception(error_msg)

    def update_form_data2(self, form_instance_id: str,  store_name: str):
        """更新宜搭表单数据-销售上报月汇总"""
        try:
            client = DingTalkYidaClient(open_api_models.Config(
                protocol='https',
                region_id='central'
            ))
            processed_data = {
                "textField_m9tojher": store_name
            }
            headers = yida_models.UpdateFormDataHeaders()
            headers.x_acs_dingtalk_access_token = self.access_token
                      
            request = yida_models.UpdateFormDataRequest()
            request.app_type = YIDA_CONFIG['APP_TYPE']
            request.system_token = YIDA_CONFIG['SYSTEM_TOKEN']
            request.user_id = YIDA_CONFIG['USER_ID']
            request.language = YIDA_CONFIG['LANGUAGE']
            request.form_instance_id = form_instance_id
            request.form_uuid = YIDA_CONFIG['FORM_UUID2']
            request.update_form_data_json = json.dumps(processed_data, ensure_ascii=False)
            request.use_alias = False
            request.use_latest_version = False
            
            response = client.update_form_data_with_options(request, headers, util_models.RuntimeOptions())
            logging.info(f"更新表单数据成功: {form_instance_id}")
            return response
            
        except Exception as e:
            logging.error(f"更新表单数据失败: {str(e)}")
            raise

def main():
    try:
        # 初始化管理器
        manager = StoreUserManager()
        
        # 获取数据库店铺信息
        logging.info("正在获取数据库店铺信息...")
        db_store_df = manager.get_store_info()
        logging.info(f"成功获取数据库店铺信息，共 {len(db_store_df)} 条记录")
        
        # 获取宜搭店铺信息
        logging.info("正在获取宜搭店铺信息...")
        try:
            yida_store_df = manager.get_yida_store_info()
            logging.info(f"成功获取宜搭店铺信息，共 {len(yida_store_df)} 条记录")
        except Exception as e:
            logging.error(f"获取宜搭店铺信息出错，将使用空数据框处理: {str(e)}")
            # 创建空的DataFrame作为宜搭数据，确保有相同的列结构
            yida_store_df = pd.DataFrame(columns=['store_code', 'store_name', 'userid', 'status', 'is_duojing', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'is_new_store', 'form_instance_id'])
        
        # 获取用户信息并处理数据库中的oa_account
        logging.info("正在获取用户信息并处理oa_account...")
        user_df = manager.get_user_info()
        db_store_df = manager.process_oa_accounts(db_store_df, user_df)
        
        # 打印数据框列名，用于诊断
        logging.info(f"数据库数据框列名: {db_store_df.columns.tolist()}")
        logging.info(f"宜搭数据框列名: {yida_store_df.columns.tolist()}")
        
        # 检查宜搭数据框是否为空
        if len(yida_store_df) == 0:
            logging.info("宜搭数据为空，跳过对比步骤，直接将所有数据库记录视为新增")
            # 对比数据 - 此时所有数据库记录都会被视为"仅在数据库存在"
            only_in_db, only_in_yida, diff_df = db_store_df.copy(), yida_store_df, pd.DataFrame()
            # 生成差异报告
            manager.generate_diff_report(only_in_db, only_in_yida, diff_df)
        else:
            # 对比数据
            logging.info("开始对比数据库和宜搭数据...")
            only_in_db, only_in_yida, diff_df = manager.compare_store_info(db_store_df, yida_store_df)
            # 生成差异报告
            logging.info("生成差异报告...")
            manager.generate_diff_report(only_in_db, only_in_yida, diff_df)
        
        # 更新宜搭表单
        logging.info("开始更新宜搭表单...")
        manager.update_yida_form(db_store_df, yida_store_df)
        
        logging.info("数据处理完成")
        
    except Exception as e:
        logging.error(f"程序执行失败: {str(e)}")
        logging.error(f"错误详情: {traceback.format_exc()}")
        raise
    finally:
        if 'manager' in locals():
            manager.close()

if __name__ == '__main__':
    main() 