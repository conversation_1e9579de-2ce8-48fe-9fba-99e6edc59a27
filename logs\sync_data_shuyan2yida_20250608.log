2025-06-08 20:46:25,169 - INFO - ==================================================
2025-06-08 20:46:25,169 - INFO - 程序启动 - 版本 v1.0.0
2025-06-08 20:46:25,169 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250608.log
2025-06-08 20:46:25,169 - INFO - ==================================================
2025-06-08 20:46:25,170 - INFO - 程序入口点: __main__
2025-06-08 20:46:25,170 - INFO - ==================================================
2025-06-08 20:46:25,170 - INFO - 程序启动 - 版本 v1.0.1
2025-06-08 20:46:25,170 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250608.log
2025-06-08 20:46:25,170 - INFO - ==================================================
2025-06-08 20:46:25,176 - INFO - MySQL数据库连接成功
2025-06-08 20:46:25,693 - INFO - MySQL数据库连接成功
2025-06-08 20:46:25,695 - INFO - sales_data表已存在，无需创建
2025-06-08 20:46:25,697 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-06-08 20:46:25,697 - INFO - DataSyncManager初始化完成
2025-06-08 20:46:25,697 - INFO - 开始更新店铺映射表...
2025-06-08 20:46:25,698 - INFO - 开始获取最近7天的店铺信息，时间范围: 2025-06-01 至 2025-06-07
2025-06-08 20:46:25,698 - INFO - 查询日期 ******** 的店铺信息
2025-06-08 20:46:25,698 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:46:25,698 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '84D5FA50A06463C86C1FF9DEA9E58F30'}
2025-06-08 20:46:27,301 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:46:27,301 - INFO - 日期 ******** 获取到 328 条店铺记录
2025-06-08 20:46:27,815 - INFO - 查询日期 ******** 的店铺信息
2025-06-08 20:46:27,815 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:46:27,815 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'C97C1C44A8606BC855DD0F1BAD8CCE53'}
2025-06-08 20:46:29,095 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:46:29,095 - INFO - 日期 ******** 获取到 328 条店铺记录
2025-06-08 20:46:29,595 - INFO - 查询日期 ******** 的店铺信息
2025-06-08 20:46:29,595 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:46:29,595 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'D9B1CE7F7DC0CFBDAF4A88830B3BFAA7'}
2025-06-08 20:46:30,693 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:46:30,693 - INFO - 日期 ******** 获取到 328 条店铺记录
2025-06-08 20:46:31,206 - INFO - 查询日期 ******** 的店铺信息
2025-06-08 20:46:31,206 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:46:31,206 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '5013F14A14772B70E101DFCD2D92E9F1'}
2025-06-08 20:46:32,259 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:46:32,259 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-08 20:46:32,772 - INFO - 查询日期 ******** 的店铺信息
2025-06-08 20:46:32,772 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:46:32,772 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'F4D6C529B127EC4B6E84896BC11F3418'}
2025-06-08 20:46:33,787 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:46:33,787 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-08 20:46:34,299 - INFO - 查询日期 ******** 的店铺信息
2025-06-08 20:46:34,299 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:46:34,299 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '424AA65F68CF250856BEBDD7059D2EEA'}
2025-06-08 20:46:35,201 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:46:35,201 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-08 20:46:35,715 - INFO - 查询日期 ******** 的店铺信息
2025-06-08 20:46:35,715 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:46:35,715 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '55BF0A2553BA6B7C199D4528BBA49AAE'}
2025-06-08 20:46:36,574 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:46:36,574 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-08 20:46:37,314 - INFO - 店铺映射表更新完成，总计: 328条，成功: 328条 (更新: 328条, 插入: 0条)
2025-06-08 20:46:37,315 - INFO - 店铺映射表更新完成
2025-06-08 20:46:37,315 - INFO - 接收到命令行参数: ['********', '********']
2025-06-08 20:46:37,315 - INFO - 使用指定的日期范围: ******** 至 ********
2025-06-08 20:46:37,316 - INFO - 开始执行综合数据同步，参数: start_date=********, end_date=********
2025-06-08 20:46:37,316 - INFO - 开始综合数据同步流程...
2025-06-08 20:46:37,316 - INFO - 当前错误日期列表为空
2025-06-08 20:46:37,316 - INFO - 正在获取数衍平台日销售数据...
2025-06-08 20:46:37,317 - INFO - 开始获取最近7天的店铺信息，时间范围: 2025-06-01 至 2025-06-07
2025-06-08 20:46:37,317 - INFO - 查询日期 ******** 的店铺信息
2025-06-08 20:46:37,317 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:46:37,317 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'C6C4E0ACE067E014EF7F457F388943C8'}
2025-06-08 20:46:38,302 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:46:38,302 - INFO - 日期 ******** 获取到 328 条店铺记录
2025-06-08 20:46:38,804 - INFO - 查询日期 ******** 的店铺信息
2025-06-08 20:46:38,804 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:46:38,804 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'EFCA5736BC98E0A58F55142AF17ABDB2'}
2025-06-08 20:46:39,662 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:46:39,678 - INFO - 日期 ******** 获取到 328 条店铺记录
2025-06-08 20:46:40,191 - INFO - 查询日期 ******** 的店铺信息
2025-06-08 20:46:40,191 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:46:40,191 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'E11E99A260DE7467CDDDCF7BA56F318A'}
2025-06-08 20:46:41,096 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:46:41,096 - INFO - 日期 ******** 获取到 328 条店铺记录
2025-06-08 20:46:41,597 - INFO - 查询日期 ******** 的店铺信息
2025-06-08 20:46:41,597 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:46:41,597 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'C8B27BF4ACB25681E6521332D886461F'}
2025-06-08 20:46:42,635 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:46:42,635 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-08 20:46:43,147 - INFO - 查询日期 ******** 的店铺信息
2025-06-08 20:46:43,147 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:46:43,147 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'EAFFBF7750A023607AC59ABE4C7D0ECD'}
2025-06-08 20:46:44,037 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:46:44,037 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-08 20:46:44,550 - INFO - 查询日期 ******** 的店铺信息
2025-06-08 20:46:44,550 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:46:44,550 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '7A003A31261E04CCBC9ABA3A522F928D'}
2025-06-08 20:46:45,530 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:46:45,530 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-08 20:46:46,030 - INFO - 查询日期 ******** 的店铺信息
2025-06-08 20:46:46,030 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:46:46,030 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '34F11E2221DD00A39A4B307F743E3CBC'}
2025-06-08 20:46:46,904 - INFO - 获取 ******** 店铺信息成功: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:46:46,904 - INFO - 日期 ******** 获取到 326 条店铺记录
2025-06-08 20:46:47,622 - INFO - 店铺映射表更新完成，总计: 328条，成功: 328条 (更新: 328条, 插入: 0条)
2025-06-08 20:46:47,622 - INFO - 查询数衍平台数据，时间段为: 2025-01-01, 2025-06-07
2025-06-08 20:46:47,622 - INFO - 正在获取********至********的数据
2025-06-08 20:46:47,622 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:46:47,622 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'EE9338B2F359E19746E6C26ACFBBACB0'}
2025-06-08 20:46:50,385 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:46:50,385 - INFO - 过滤后保留 463 条记录
2025-06-08 20:46:52,399 - INFO - 正在获取********至********的数据
2025-06-08 20:46:52,399 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:46:52,399 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'C5E6CF2403C687A443C38E45CDDF0DDC'}
2025-06-08 20:46:54,398 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:46:54,413 - INFO - 过滤后保留 463 条记录
2025-06-08 20:46:56,414 - INFO - 正在获取********至********的数据
2025-06-08 20:46:56,414 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:46:56,414 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'AFC37BD5FDF719E6A4085408756FB6B6'}
2025-06-08 20:46:58,186 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:46:58,192 - INFO - 过滤后保留 453 条记录
2025-06-08 20:47:00,193 - INFO - 正在获取********至********的数据
2025-06-08 20:47:00,193 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:47:00,194 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'FF93653631E49CBB411EBE008027BEF9'}
2025-06-08 20:47:02,123 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:47:02,128 - INFO - 过滤后保留 460 条记录
2025-06-08 20:47:04,135 - INFO - 正在获取********至********的数据
2025-06-08 20:47:04,135 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:47:04,135 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'A71CA022295A922A99AD44A088D10A5D'}
2025-06-08 20:47:05,731 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:47:05,736 - INFO - 过滤后保留 452 条记录
2025-06-08 20:47:07,740 - INFO - 正在获取********至********的数据
2025-06-08 20:47:07,740 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:47:07,740 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '********************************'}
2025-06-08 20:47:09,395 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:47:09,410 - INFO - 过滤后保留 459 条记录
2025-06-08 20:47:11,423 - INFO - 正在获取********至********的数据
2025-06-08 20:47:11,423 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:47:11,423 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '6FE5923FB179EAC7968EA4645F886A9B'}
2025-06-08 20:47:13,044 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:47:13,044 - INFO - 过滤后保留 448 条记录
2025-06-08 20:47:15,058 - INFO - 正在获取********至********的数据
2025-06-08 20:47:15,058 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:47:15,058 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '********************************'}
2025-06-08 20:47:16,744 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:47:16,744 - INFO - 过滤后保留 450 条记录
2025-06-08 20:47:18,745 - INFO - 正在获取********至********的数据
2025-06-08 20:47:18,745 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:47:18,745 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'B427C1DAD04294CD0996EA7A743A6B8A'}
2025-06-08 20:47:20,300 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:47:20,300 - INFO - 过滤后保留 442 条记录
2025-06-08 20:47:22,312 - INFO - 正在获取********至********的数据
2025-06-08 20:47:22,312 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:47:22,312 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '20370647A3B4DC8400438E47437F3250'}
2025-06-08 20:47:24,045 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:47:24,045 - INFO - 过滤后保留 451 条记录
2025-06-08 20:47:26,050 - INFO - 正在获取********至********的数据
2025-06-08 20:47:26,050 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:47:26,050 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'BC6298081E2A72EFA8BAC2927CDA0DE3'}
2025-06-08 20:47:27,638 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:47:27,638 - INFO - 过滤后保留 446 条记录
2025-06-08 20:47:29,652 - INFO - 正在获取********至********的数据
2025-06-08 20:47:29,652 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:47:29,652 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '7DF67327A8B2F6E555F07F635EB3E19F'}
2025-06-08 20:47:31,525 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:47:31,541 - INFO - 过滤后保留 442 条记录
2025-06-08 20:47:33,555 - INFO - 正在获取********至********的数据
2025-06-08 20:47:33,555 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:47:33,555 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'B0B0CDE85EC56AA6706B9416F362FEE5'}
2025-06-08 20:47:35,398 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:47:35,398 - INFO - 过滤后保留 440 条记录
2025-06-08 20:47:37,413 - INFO - 正在获取********至********的数据
2025-06-08 20:47:37,413 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:47:37,413 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'D0DA4BB3ECB312CEEAEF2C9AAE05F545'}
2025-06-08 20:47:38,943 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:47:38,943 - INFO - 过滤后保留 346 条记录
2025-06-08 20:47:40,944 - INFO - 正在获取********至********的数据
2025-06-08 20:47:40,944 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:47:40,945 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '33A10DC195AEF463FF2B03845CDFE097'}
2025-06-08 20:47:42,736 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:47:42,736 - INFO - 过滤后保留 309 条记录
2025-06-08 20:47:44,737 - INFO - 正在获取********至********的数据
2025-06-08 20:47:44,737 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:47:44,737 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'F813AA131602FC3FC0BD956737CC0BD1'}
2025-06-08 20:47:46,543 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:47:46,549 - INFO - 过滤后保留 343 条记录
2025-06-08 20:47:48,555 - INFO - 正在获取********至********的数据
2025-06-08 20:47:48,555 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:47:48,555 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'A4300D47119A6465D408EAC172C4BA70'}
2025-06-08 20:47:50,093 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:47:50,093 - INFO - 过滤后保留 398 条记录
2025-06-08 20:47:52,093 - INFO - 正在获取********至********的数据
2025-06-08 20:47:52,093 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:47:52,093 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '6BD4DBCA8FDADB80A8EEC517335CD127'}
2025-06-08 20:47:53,494 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:47:53,499 - INFO - 过滤后保留 433 条记录
2025-06-08 20:47:55,507 - INFO - 正在获取********至********的数据
2025-06-08 20:47:55,507 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:47:55,507 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '0F1F4916D2367FF9EBD1C9E9158E5D89'}
2025-06-08 20:47:56,911 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:47:56,911 - INFO - 过滤后保留 443 条记录
2025-06-08 20:47:58,924 - INFO - 正在获取********至********的数据
2025-06-08 20:47:58,924 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:47:58,924 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '0CA6A3558C83979A90BF1E9B10405A2B'}
2025-06-08 20:48:00,597 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:48:00,613 - INFO - 过滤后保留 447 条记录
2025-06-08 20:48:02,626 - INFO - 正在获取********至********的数据
2025-06-08 20:48:02,626 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:48:02,626 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '1B5CF42DC4342E1B6A1C075BC30450D6'}
2025-06-08 20:48:04,061 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:48:04,061 - INFO - 过滤后保留 455 条记录
2025-06-08 20:48:06,075 - INFO - 正在获取********至********的数据
2025-06-08 20:48:06,075 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:48:06,075 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '21D4E166E646D75692584EAF771A5E48'}
2025-06-08 20:48:07,636 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:48:07,636 - INFO - 过滤后保留 452 条记录
2025-06-08 20:48:09,651 - INFO - 正在获取********至********的数据
2025-06-08 20:48:09,651 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:48:09,651 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '0AC6E2EA8D5846B0E557CD518BDBED87'}
2025-06-08 20:48:11,346 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:48:11,346 - INFO - 过滤后保留 458 条记录
2025-06-08 20:48:13,350 - INFO - 正在获取********至********的数据
2025-06-08 20:48:13,350 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:48:13,350 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '162D05CBC63FFD6DC81E0A5338EE5AA5'}
2025-06-08 20:48:15,141 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:48:15,141 - INFO - 过滤后保留 447 条记录
2025-06-08 20:48:17,156 - INFO - 正在获取********至********的数据
2025-06-08 20:48:17,156 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:48:17,156 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '585B55422F9668836238F32A83E6986B'}
2025-06-08 20:48:18,592 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:48:18,608 - INFO - 过滤后保留 440 条记录
2025-06-08 20:48:20,623 - INFO - 正在获取********至********的数据
2025-06-08 20:48:20,623 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:48:20,623 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'C9C4F6030027A63C27711CEB66584591'}
2025-06-08 20:48:22,277 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:48:22,277 - INFO - 过滤后保留 444 条记录
2025-06-08 20:48:24,277 - INFO - 正在获取********至********的数据
2025-06-08 20:48:24,277 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:48:24,277 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'B51362589C1D00A250C0D3DC978FC56A'}
2025-06-08 20:48:25,791 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:48:25,791 - INFO - 过滤后保留 454 条记录
2025-06-08 20:48:27,807 - INFO - 正在获取********至********的数据
2025-06-08 20:48:27,807 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:48:27,807 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'C06BED6334CBB3BE0C9A6F399B8475C4'}
2025-06-08 20:48:29,353 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:48:29,353 - INFO - 过滤后保留 431 条记录
2025-06-08 20:48:31,353 - INFO - 正在获取********至********的数据
2025-06-08 20:48:31,353 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:48:31,353 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '0F5B3930874EC68530A612623B967189'}
2025-06-08 20:48:32,977 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:48:32,993 - INFO - 过滤后保留 444 条记录
2025-06-08 20:48:34,992 - INFO - 正在获取********至********的数据
2025-06-08 20:48:34,992 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:48:34,992 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'CBAB052C6D114260EE6559FAE75ADC35'}
2025-06-08 20:48:36,539 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:48:36,539 - INFO - 过滤后保留 447 条记录
2025-06-08 20:48:38,553 - INFO - 正在获取********至********的数据
2025-06-08 20:48:38,553 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:48:38,553 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '0BB31E1EE9D7830C9B45F9431A52D07C'}
2025-06-08 20:48:40,069 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:48:40,069 - INFO - 过滤后保留 439 条记录
2025-06-08 20:48:42,083 - INFO - 正在获取********至********的数据
2025-06-08 20:48:42,083 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:48:42,083 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'B9051443E23E606133818169CB54314A'}
2025-06-08 20:48:43,771 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:48:43,771 - INFO - 过滤后保留 449 条记录
2025-06-08 20:48:45,785 - INFO - 正在获取********至********的数据
2025-06-08 20:48:45,785 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:48:45,785 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'F3B04548FA66E5EE3C52ACD9E62C5CFD'}
2025-06-08 20:48:47,380 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:48:47,380 - INFO - 过滤后保留 444 条记录
2025-06-08 20:48:49,394 - INFO - 正在获取********至********的数据
2025-06-08 20:48:49,394 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:48:49,394 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'B99FA213FB63904B26BCF254D0E83D83'}
2025-06-08 20:48:50,832 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:48:50,832 - INFO - 过滤后保留 459 条记录
2025-06-08 20:48:52,846 - INFO - 正在获取********至********的数据
2025-06-08 20:48:52,846 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:48:52,846 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '52B078E568E136E0C511B24031A445AC'}
2025-06-08 20:48:54,378 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:48:54,378 - INFO - 过滤后保留 443 条记录
2025-06-08 20:48:56,392 - INFO - 正在获取********至********的数据
2025-06-08 20:48:56,392 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:48:56,392 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '42AE4BD64DB01C7493DAD89DC91F95E1'}
2025-06-08 20:48:58,017 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:48:58,017 - INFO - 过滤后保留 444 条记录
2025-06-08 20:49:00,031 - INFO - 正在获取********至********的数据
2025-06-08 20:49:00,031 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:49:00,031 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '9E686159796E3ED688BD2BC3811CBA06'}
2025-06-08 20:49:01,391 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:49:01,407 - INFO - 过滤后保留 448 条记录
2025-06-08 20:49:03,422 - INFO - 正在获取********至********的数据
2025-06-08 20:49:03,422 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:49:03,422 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '270442F102FA29FC9065B126EE68F6AC'}
2025-06-08 20:49:04,904 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:49:04,904 - INFO - 过滤后保留 443 条记录
2025-06-08 20:49:06,919 - INFO - 正在获取********至********的数据
2025-06-08 20:49:06,919 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:49:06,919 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '********************************'}
2025-06-08 20:49:08,309 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:49:08,309 - INFO - 过滤后保留 437 条记录
2025-06-08 20:49:10,323 - INFO - 正在获取********至********的数据
2025-06-08 20:49:10,323 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:49:10,323 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '9802C42440A0E97FE50865F15E7C52BA'}
2025-06-08 20:49:11,713 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:49:11,713 - INFO - 过滤后保留 449 条记录
2025-06-08 20:49:13,728 - INFO - 正在获取********至********的数据
2025-06-08 20:49:13,728 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:49:13,728 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '********************************'}
2025-06-08 20:49:15,134 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:49:15,134 - INFO - 过滤后保留 452 条记录
2025-06-08 20:49:17,149 - INFO - 正在获取********至********的数据
2025-06-08 20:49:17,149 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:49:17,149 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'E2AAC592E338B4617E4C4C0A1DBA2F51'}
2025-06-08 20:49:18,526 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:49:18,541 - INFO - 过滤后保留 442 条记录
2025-06-08 20:49:20,554 - INFO - 正在获取********至********的数据
2025-06-08 20:49:20,554 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:49:20,554 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'E01D148DBBDB055A4C91F0D37DF2E4F5'}
2025-06-08 20:49:21,897 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:49:21,897 - INFO - 过滤后保留 449 条记录
2025-06-08 20:49:23,911 - INFO - 正在获取********至********的数据
2025-06-08 20:49:23,911 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:49:23,911 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '2BE16194C26DD28033C1594F882710F0'}
2025-06-08 20:49:25,989 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:49:25,989 - INFO - 过滤后保留 446 条记录
2025-06-08 20:49:28,004 - INFO - 正在获取********至********的数据
2025-06-08 20:49:28,004 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:49:28,004 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'F8761D9287E2F403D8548D11DFEC7F36'}
2025-06-08 20:49:29,566 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:49:29,566 - INFO - 过滤后保留 441 条记录
2025-06-08 20:49:31,581 - INFO - 正在获取********至********的数据
2025-06-08 20:49:31,581 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:49:31,581 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '09D5AA41CA1FDD3A35A056ADEAFE6631'}
2025-06-08 20:49:33,127 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:49:33,127 - INFO - 过滤后保留 431 条记录
2025-06-08 20:49:35,141 - INFO - 正在获取********至********的数据
2025-06-08 20:49:35,141 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:49:35,141 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '160F8B063BE11DF4F660C1AF2B1B3624'}
2025-06-08 20:49:36,844 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:49:36,860 - INFO - 过滤后保留 434 条记录
2025-06-08 20:49:38,875 - INFO - 正在获取********至********的数据
2025-06-08 20:49:38,875 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:49:38,875 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'E4E265047951D8E4532EC5A17C9927C7'}
2025-06-08 20:49:40,217 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:49:40,217 - INFO - 过滤后保留 431 条记录
2025-06-08 20:49:42,217 - INFO - 正在获取********至********的数据
2025-06-08 20:49:42,217 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:49:42,217 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'B912DEB6F53CED90D790A61FF971EE2A'}
2025-06-08 20:49:43,495 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:49:43,495 - INFO - 过滤后保留 426 条记录
2025-06-08 20:49:45,495 - INFO - 正在获取********至********的数据
2025-06-08 20:49:45,495 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:49:45,495 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '034D97E8835547BC17715BFC6F764282'}
2025-06-08 20:49:46,946 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:49:46,962 - INFO - 过滤后保留 423 条记录
2025-06-08 20:49:48,962 - INFO - 正在获取********至********的数据
2025-06-08 20:49:48,962 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:49:48,962 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'E4FF8B11689B9C5D0DF77CC3647631AE'}
2025-06-08 20:49:50,335 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:49:50,350 - INFO - 过滤后保留 432 条记录
2025-06-08 20:49:52,365 - INFO - 正在获取********至********的数据
2025-06-08 20:49:52,365 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:49:52,365 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'FD8028B93A5A0B68B8B5AD313DF793D5'}
2025-06-08 20:49:53,880 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:49:53,880 - INFO - 过滤后保留 434 条记录
2025-06-08 20:49:55,880 - INFO - 正在获取********至********的数据
2025-06-08 20:49:55,880 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:49:55,880 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '1C3BF794A1C0CA79BFA2070B87436183'}
2025-06-08 20:49:57,143 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:49:57,143 - INFO - 过滤后保留 424 条记录
2025-06-08 20:49:59,144 - INFO - 正在获取********至********的数据
2025-06-08 20:49:59,144 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:49:59,144 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '6855B77ED29F22B7CD6ACD879455F3A7'}
2025-06-08 20:50:00,532 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:50:00,548 - INFO - 过滤后保留 436 条记录
2025-06-08 20:50:02,549 - INFO - 正在获取********至********的数据
2025-06-08 20:50:02,549 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:50:02,549 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '7284B123633E5AB50EC0477EE10F840D'}
2025-06-08 20:50:03,957 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:50:03,957 - INFO - 过滤后保留 431 条记录
2025-06-08 20:50:05,969 - INFO - 正在获取********至********的数据
2025-06-08 20:50:05,969 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:50:05,969 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'A18FA0DED7989B537F32EDB5A47DBC9D'}
2025-06-08 20:50:07,327 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:50:07,327 - INFO - 过滤后保留 425 条记录
2025-06-08 20:50:09,339 - INFO - 正在获取********至********的数据
2025-06-08 20:50:09,339 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:50:09,339 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'F9D33AFE1EF995D09E8789E30D844163'}
2025-06-08 20:50:10,888 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:50:10,888 - INFO - 过滤后保留 414 条记录
2025-06-08 20:50:12,901 - INFO - 正在获取********至********的数据
2025-06-08 20:50:12,901 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:50:12,901 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '9057A1413DE00DC5D7889548870A76AD'}
2025-06-08 20:50:14,540 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:50:14,540 - INFO - 过滤后保留 427 条记录
2025-06-08 20:50:16,540 - INFO - 正在获取********至********的数据
2025-06-08 20:50:16,540 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:50:16,540 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'D54AB9ABFCAAAC7E52065E8AC6C10A7D'}
2025-06-08 20:50:17,930 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:50:17,930 - INFO - 过滤后保留 428 条记录
2025-06-08 20:50:19,945 - INFO - 正在获取********至********的数据
2025-06-08 20:50:19,945 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:50:19,945 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'C3202FA4243537345E2BF89ADAE2CC00'}
2025-06-08 20:50:21,398 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:50:21,398 - INFO - 过滤后保留 429 条记录
2025-06-08 20:50:23,413 - INFO - 正在获取********至********的数据
2025-06-08 20:50:23,413 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:50:23,413 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '5DEC1CE4D86738D58E23689C810C95D4'}
2025-06-08 20:50:24,850 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:50:24,850 - INFO - 过滤后保留 427 条记录
2025-06-08 20:50:26,864 - INFO - 正在获取********至********的数据
2025-06-08 20:50:26,864 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:50:26,864 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '58C9DF69413B0F2AC0E2DA64180E95C5'}
2025-06-08 20:50:28,318 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:50:28,333 - INFO - 过滤后保留 428 条记录
2025-06-08 20:50:30,348 - INFO - 正在获取********至********的数据
2025-06-08 20:50:30,348 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:50:30,348 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'B6E4387BE39A9545574F859962C9AB46'}
2025-06-08 20:50:31,739 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:50:31,739 - INFO - 过滤后保留 417 条记录
2025-06-08 20:50:33,753 - INFO - 正在获取********至********的数据
2025-06-08 20:50:33,753 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:50:33,753 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '545A3CFA5358BD8548DAEFED27AB95D5'}
2025-06-08 20:50:35,018 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:50:35,018 - INFO - 过滤后保留 415 条记录
2025-06-08 20:50:37,032 - INFO - 正在获取********至********的数据
2025-06-08 20:50:37,032 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:50:37,032 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '218B1BCA1316FBF906E9790D24B37626'}
2025-06-08 20:50:38,283 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:50:38,299 - INFO - 过滤后保留 433 条记录
2025-06-08 20:50:40,313 - INFO - 正在获取********至********的数据
2025-06-08 20:50:40,313 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:50:40,313 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'F2BDA05188C5A7FB10ABDA65D36A81ED'}
2025-06-08 20:50:41,548 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:50:41,548 - INFO - 过滤后保留 433 条记录
2025-06-08 20:50:43,562 - INFO - 正在获取********至********的数据
2025-06-08 20:50:43,562 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:50:43,562 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '5DFE8F68AABD5720133852C54D786B12'}
2025-06-08 20:50:44,796 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:50:44,796 - INFO - 过滤后保留 417 条记录
2025-06-08 20:50:46,810 - INFO - 正在获取********至********的数据
2025-06-08 20:50:46,810 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:50:46,810 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'E07F6AECCB855182DDEFB71594AEC8A0'}
2025-06-08 20:50:48,075 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:50:48,091 - INFO - 过滤后保留 420 条记录
2025-06-08 20:50:50,106 - INFO - 正在获取********至********的数据
2025-06-08 20:50:50,106 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:50:50,106 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'DA3A564128A69B064E077E0ED97FA205'}
2025-06-08 20:50:51,417 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:50:51,417 - INFO - 过滤后保留 431 条记录
2025-06-08 20:50:53,418 - INFO - 正在获取********至********的数据
2025-06-08 20:50:53,418 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:50:53,418 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'A840B80472097F2E6ACC9B75919C241C'}
2025-06-08 20:50:54,635 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:50:54,635 - INFO - 过滤后保留 423 条记录
2025-06-08 20:50:56,636 - INFO - 正在获取********至********的数据
2025-06-08 20:50:56,636 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:50:56,636 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '553D2C8CD01F915697FDBB4FDF3D8A2D'}
2025-06-08 20:50:57,837 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:50:57,837 - INFO - 过滤后保留 416 条记录
2025-06-08 20:50:59,838 - INFO - 正在获取********至********的数据
2025-06-08 20:50:59,838 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:50:59,838 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'B70E81B1BF516817FB58F3FAF00D1556'}
2025-06-08 20:51:01,071 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:51:01,071 - INFO - 过滤后保留 423 条记录
2025-06-08 20:51:03,072 - INFO - 正在获取********至********的数据
2025-06-08 20:51:03,072 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:51:03,072 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '16EC8D504AF551B88A0D3CEB4BCD6331'}
2025-06-08 20:51:04,273 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:51:04,289 - INFO - 过滤后保留 414 条记录
2025-06-08 20:51:06,290 - INFO - 正在获取********至********的数据
2025-06-08 20:51:06,290 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:51:06,290 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'F44E708DAEB885A664B1E8478B785F38'}
2025-06-08 20:51:07,522 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:51:07,538 - INFO - 过滤后保留 413 条记录
2025-06-08 20:51:09,539 - INFO - 正在获取********至********的数据
2025-06-08 20:51:09,539 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:51:09,539 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '0CDA4D5FCDDE255FCD73790CA8403BB4'}
2025-06-08 20:51:10,709 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:51:10,709 - INFO - 过滤后保留 414 条记录
2025-06-08 20:51:12,711 - INFO - 正在获取********至********的数据
2025-06-08 20:51:12,711 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:51:12,711 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'FCB6691B79B9A5B43815B9D1A643AE2C'}
2025-06-08 20:51:13,880 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:51:13,880 - INFO - 过滤后保留 415 条记录
2025-06-08 20:51:15,882 - INFO - 正在获取********至********的数据
2025-06-08 20:51:15,882 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:51:15,882 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '2A9275C7BB256DC6C042645951FEB7EE'}
2025-06-08 20:51:17,020 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:51:17,020 - INFO - 过滤后保留 400 条记录
2025-06-08 20:51:19,021 - INFO - 正在获取********至********的数据
2025-06-08 20:51:19,021 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:51:19,021 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'A1C57C4F308A624FCC6954C36CEAE5B0'}
2025-06-08 20:51:20,222 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:51:20,222 - INFO - 过滤后保留 398 条记录
2025-06-08 20:51:22,223 - INFO - 正在获取********至********的数据
2025-06-08 20:51:22,223 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-08 20:51:22,223 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'B1D2AEC8075D035097021E4B64067163'}
2025-06-08 20:51:23,284 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-08 20:51:23,284 - INFO - 过滤后保留 405 条记录
2025-06-08 20:51:25,284 - INFO - 开始保存数据到MySQL数据库，共 34112 条记录待处理
2025-06-08 20:52:05,540 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HO90F90E71NK12I1UUTD5AE7C001O7G, sale_time=2025-06-05
2025-06-08 20:52:05,540 - INFO - 变更字段: recommend_amount: 6070.41 -> 6073.41, amount: 6070 -> 6073, count: 253 -> 254, online_amount: 4942.71 -> 4945.71, online_count: 197 -> 198
2025-06-08 20:52:05,946 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDUSSKMEM60I86N3H2U1PI001F2O, sale_time=2025-06-07
2025-06-08 20:52:05,946 - INFO - 变更字段: amount: 2538 -> 2526
2025-06-08 20:52:06,005 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDUDM4PLNS0I86N3H2U121001EB7, sale_time=2025-06-07
2025-06-08 20:52:06,006 - INFO - 变更字段: amount: 8403 -> 8371
2025-06-08 20:52:06,193 - INFO - MySQL数据保存完成，统计信息：
2025-06-08 20:52:06,193 - INFO - - 总记录数: 34112
2025-06-08 20:52:06,193 - INFO - - 成功插入: 32703
2025-06-08 20:52:06,193 - INFO - - 成功更新: 3
2025-06-08 20:52:06,193 - INFO - - 无需更新: 1406
2025-06-08 20:52:06,193 - INFO - - 处理失败: 0
2025-06-08 20:52:06,193 - INFO - 成功获取数衍平台数据，共 34112 条记录
2025-06-08 20:52:06,193 - INFO - 正在更新MySQL月度汇总数据...
2025-06-08 20:52:06,193 - INFO - 月度数据表清空完成
2025-06-08 20:52:06,503 - INFO - 月度汇总数据更新完成，处理了 1403 条汇总记录
2025-06-08 20:52:06,503 - INFO - 成功更新月度汇总数据，共 1403 条记录
2025-06-08 20:52:06,519 - INFO - 正在获取宜搭日销售表单数据...
2025-06-08 20:52:06,519 - INFO - 开始获取宜搭每日表单数据，总时间范围: 2025-01-01 00:00:00 至 2025-06-07 23:59:59
2025-06-08 20:52:06,519 - INFO - 查询分段 1: 2025-01-01 至 2025-01-02
2025-06-08 20:52:06,519 - INFO - 查询日期范围: 2025-01-01 至 2025-01-02，使用分页查询，每页 100 条记录
2025-06-08 20:52:06,519 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:06,519 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:06,519 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1735747200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:06,862 - INFO - API请求耗时: 344ms
2025-06-08 20:52:06,862 - INFO - Response - Page 1
2025-06-08 20:52:06,862 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:06,862 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:06,862 - WARNING - 分段 1 查询返回空数据
2025-06-08 20:52:07,876 - INFO - 查询分段 2: 2025-01-03 至 2025-01-04
2025-06-08 20:52:07,876 - INFO - 查询日期范围: 2025-01-03 至 2025-01-04，使用分页查询，每页 100 条记录
2025-06-08 20:52:07,876 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:07,876 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:07,876 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735833600000, 1735920000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:14,921 - INFO - API请求耗时: 7045ms
2025-06-08 20:52:14,921 - INFO - Response - Page 1
2025-06-08 20:52:14,921 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:14,921 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:14,921 - WARNING - 分段 2 查询返回空数据
2025-06-08 20:52:15,935 - INFO - 查询分段 3: 2025-01-05 至 2025-01-06
2025-06-08 20:52:15,935 - INFO - 查询日期范围: 2025-01-05 至 2025-01-06，使用分页查询，每页 100 条记录
2025-06-08 20:52:15,935 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:15,935 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:15,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1736006400000, 1736092800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:16,185 - INFO - API请求耗时: 250ms
2025-06-08 20:52:16,185 - INFO - Response - Page 1
2025-06-08 20:52:16,185 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:16,185 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:16,185 - WARNING - 分段 3 查询返回空数据
2025-06-08 20:52:17,199 - INFO - 查询分段 4: 2025-01-07 至 2025-01-08
2025-06-08 20:52:17,199 - INFO - 查询日期范围: 2025-01-07 至 2025-01-08，使用分页查询，每页 100 条记录
2025-06-08 20:52:17,199 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:17,199 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:17,199 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1736179200000, 1736265600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:17,465 - INFO - API请求耗时: 266ms
2025-06-08 20:52:17,465 - INFO - Response - Page 1
2025-06-08 20:52:17,465 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:17,465 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:17,465 - WARNING - 分段 4 查询返回空数据
2025-06-08 20:52:18,479 - INFO - 查询分段 5: 2025-01-09 至 2025-01-10
2025-06-08 20:52:18,479 - INFO - 查询日期范围: 2025-01-09 至 2025-01-10，使用分页查询，每页 100 条记录
2025-06-08 20:52:18,479 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:18,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:18,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1736352000000, 1736438400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:18,728 - INFO - API请求耗时: 249ms
2025-06-08 20:52:18,728 - INFO - Response - Page 1
2025-06-08 20:52:18,728 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:18,728 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:18,728 - WARNING - 分段 5 查询返回空数据
2025-06-08 20:52:19,743 - INFO - 查询分段 6: 2025-01-11 至 2025-01-12
2025-06-08 20:52:19,743 - INFO - 查询日期范围: 2025-01-11 至 2025-01-12，使用分页查询，每页 100 条记录
2025-06-08 20:52:19,743 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:19,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:19,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1736524800000, 1736611200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:19,974 - INFO - API请求耗时: 231ms
2025-06-08 20:52:19,974 - INFO - Response - Page 1
2025-06-08 20:52:19,974 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:19,974 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:19,974 - WARNING - 分段 6 查询返回空数据
2025-06-08 20:52:20,988 - INFO - 查询分段 7: 2025-01-13 至 2025-01-14
2025-06-08 20:52:20,988 - INFO - 查询日期范围: 2025-01-13 至 2025-01-14，使用分页查询，每页 100 条记录
2025-06-08 20:52:20,988 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:20,988 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:20,988 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1736697600000, 1736784000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:21,298 - INFO - API请求耗时: 309ms
2025-06-08 20:52:21,314 - INFO - Response - Page 1
2025-06-08 20:52:21,314 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:21,314 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:21,314 - WARNING - 分段 7 查询返回空数据
2025-06-08 20:52:22,328 - INFO - 查询分段 8: 2025-01-15 至 2025-01-16
2025-06-08 20:52:22,328 - INFO - 查询日期范围: 2025-01-15 至 2025-01-16，使用分页查询，每页 100 条记录
2025-06-08 20:52:22,328 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:22,328 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:22,328 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1736870400000, 1736956800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:22,531 - INFO - API请求耗时: 203ms
2025-06-08 20:52:22,531 - INFO - Response - Page 1
2025-06-08 20:52:22,531 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:22,531 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:22,531 - WARNING - 分段 8 查询返回空数据
2025-06-08 20:52:23,546 - INFO - 查询分段 9: 2025-01-17 至 2025-01-18
2025-06-08 20:52:23,546 - INFO - 查询日期范围: 2025-01-17 至 2025-01-18，使用分页查询，每页 100 条记录
2025-06-08 20:52:23,546 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:23,546 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:23,546 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1737043200000, 1737129600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:23,812 - INFO - API请求耗时: 266ms
2025-06-08 20:52:23,812 - INFO - Response - Page 1
2025-06-08 20:52:23,812 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:23,812 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:23,812 - WARNING - 分段 9 查询返回空数据
2025-06-08 20:52:24,826 - INFO - 查询分段 10: 2025-01-19 至 2025-01-20
2025-06-08 20:52:24,826 - INFO - 查询日期范围: 2025-01-19 至 2025-01-20，使用分页查询，每页 100 条记录
2025-06-08 20:52:24,826 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:24,826 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:24,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1737216000000, 1737302400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:25,092 - INFO - API请求耗时: 266ms
2025-06-08 20:52:25,092 - INFO - Response - Page 1
2025-06-08 20:52:25,092 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:25,092 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:25,092 - WARNING - 分段 10 查询返回空数据
2025-06-08 20:52:26,107 - INFO - 查询分段 11: 2025-01-21 至 2025-01-22
2025-06-08 20:52:26,107 - INFO - 查询日期范围: 2025-01-21 至 2025-01-22，使用分页查询，每页 100 条记录
2025-06-08 20:52:26,107 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:26,107 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:26,107 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1737388800000, 1737475200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:26,310 - INFO - API请求耗时: 203ms
2025-06-08 20:52:26,310 - INFO - Response - Page 1
2025-06-08 20:52:26,310 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:26,310 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:26,310 - WARNING - 分段 11 查询返回空数据
2025-06-08 20:52:27,310 - INFO - 查询分段 12: 2025-01-23 至 2025-01-24
2025-06-08 20:52:27,310 - INFO - 查询日期范围: 2025-01-23 至 2025-01-24，使用分页查询，每页 100 条记录
2025-06-08 20:52:27,310 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:27,310 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:27,310 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1737561600000, 1737648000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:27,606 - INFO - API请求耗时: 296ms
2025-06-08 20:52:27,606 - INFO - Response - Page 1
2025-06-08 20:52:27,606 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:27,606 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:27,606 - WARNING - 分段 12 查询返回空数据
2025-06-08 20:52:28,621 - INFO - 查询分段 13: 2025-01-25 至 2025-01-26
2025-06-08 20:52:28,621 - INFO - 查询日期范围: 2025-01-25 至 2025-01-26，使用分页查询，每页 100 条记录
2025-06-08 20:52:28,621 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:28,621 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:28,621 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1737734400000, 1737820800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:28,901 - INFO - API请求耗时: 280ms
2025-06-08 20:52:28,901 - INFO - Response - Page 1
2025-06-08 20:52:28,901 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:28,901 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:28,901 - WARNING - 分段 13 查询返回空数据
2025-06-08 20:52:29,901 - INFO - 查询分段 14: 2025-01-27 至 2025-01-28
2025-06-08 20:52:29,901 - INFO - 查询日期范围: 2025-01-27 至 2025-01-28，使用分页查询，每页 100 条记录
2025-06-08 20:52:29,901 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:29,901 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:29,901 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1737907200000, 1737993600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:30,088 - INFO - API请求耗时: 186ms
2025-06-08 20:52:30,088 - INFO - Response - Page 1
2025-06-08 20:52:30,088 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:30,088 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:30,088 - WARNING - 分段 14 查询返回空数据
2025-06-08 20:52:31,103 - INFO - 查询分段 15: 2025-01-29 至 2025-01-30
2025-06-08 20:52:31,103 - INFO - 查询日期范围: 2025-01-29 至 2025-01-30，使用分页查询，每页 100 条记录
2025-06-08 20:52:31,103 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:31,103 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:31,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738080000000, 1738166400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:31,415 - INFO - API请求耗时: 312ms
2025-06-08 20:52:31,415 - INFO - Response - Page 1
2025-06-08 20:52:31,415 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:31,415 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:31,415 - WARNING - 分段 15 查询返回空数据
2025-06-08 20:52:32,431 - INFO - 查询分段 16: 2025-01-31 至 2025-02-01
2025-06-08 20:52:32,431 - INFO - 查询日期范围: 2025-01-31 至 2025-02-01，使用分页查询，每页 100 条记录
2025-06-08 20:52:32,431 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:32,431 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:32,431 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738252800000, 1738339200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:32,611 - INFO - API请求耗时: 181ms
2025-06-08 20:52:32,611 - INFO - Response - Page 1
2025-06-08 20:52:32,612 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:32,612 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:32,612 - WARNING - 分段 16 查询返回空数据
2025-06-08 20:52:33,616 - INFO - 查询分段 17: 2025-02-02 至 2025-02-03
2025-06-08 20:52:33,616 - INFO - 查询日期范围: 2025-02-02 至 2025-02-03，使用分页查询，每页 100 条记录
2025-06-08 20:52:33,616 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:33,616 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:33,616 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738425600000, 1738512000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:33,881 - INFO - API请求耗时: 265ms
2025-06-08 20:52:33,881 - INFO - Response - Page 1
2025-06-08 20:52:33,881 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:33,881 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:33,881 - WARNING - 分段 17 查询返回空数据
2025-06-08 20:52:34,896 - INFO - 查询分段 18: 2025-02-04 至 2025-02-05
2025-06-08 20:52:34,896 - INFO - 查询日期范围: 2025-02-04 至 2025-02-05，使用分页查询，每页 100 条记录
2025-06-08 20:52:34,896 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:34,896 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:34,896 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738598400000, 1738684800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:35,129 - INFO - API请求耗时: 233ms
2025-06-08 20:52:35,129 - INFO - Response - Page 1
2025-06-08 20:52:35,129 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:35,129 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:35,129 - WARNING - 分段 18 查询返回空数据
2025-06-08 20:52:36,145 - INFO - 查询分段 19: 2025-02-06 至 2025-02-07
2025-06-08 20:52:36,145 - INFO - 查询日期范围: 2025-02-06 至 2025-02-07，使用分页查询，每页 100 条记录
2025-06-08 20:52:36,145 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:36,145 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:36,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738771200000, 1738857600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:36,347 - INFO - API请求耗时: 202ms
2025-06-08 20:52:36,347 - INFO - Response - Page 1
2025-06-08 20:52:36,347 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:36,347 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:36,347 - WARNING - 分段 19 查询返回空数据
2025-06-08 20:52:37,346 - INFO - 查询分段 20: 2025-02-08 至 2025-02-09
2025-06-08 20:52:37,346 - INFO - 查询日期范围: 2025-02-08 至 2025-02-09，使用分页查询，每页 100 条记录
2025-06-08 20:52:37,346 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:37,346 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:37,346 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1738944000000, 1739030400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:37,643 - INFO - API请求耗时: 297ms
2025-06-08 20:52:37,659 - INFO - Response - Page 1
2025-06-08 20:52:37,659 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:37,659 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:37,659 - WARNING - 分段 20 查询返回空数据
2025-06-08 20:52:38,659 - INFO - 查询分段 21: 2025-02-10 至 2025-02-11
2025-06-08 20:52:38,659 - INFO - 查询日期范围: 2025-02-10 至 2025-02-11，使用分页查询，每页 100 条记录
2025-06-08 20:52:38,659 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:38,659 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:38,659 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1739116800000, 1739203200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:38,924 - INFO - API请求耗时: 266ms
2025-06-08 20:52:38,924 - INFO - Response - Page 1
2025-06-08 20:52:38,924 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:38,924 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:38,924 - WARNING - 分段 21 查询返回空数据
2025-06-08 20:52:39,940 - INFO - 查询分段 22: 2025-02-12 至 2025-02-13
2025-06-08 20:52:39,940 - INFO - 查询日期范围: 2025-02-12 至 2025-02-13，使用分页查询，每页 100 条记录
2025-06-08 20:52:39,940 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:39,940 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:39,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1739289600000, 1739376000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:40,205 - INFO - API请求耗时: 266ms
2025-06-08 20:52:40,205 - INFO - Response - Page 1
2025-06-08 20:52:40,205 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:40,205 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:40,205 - WARNING - 分段 22 查询返回空数据
2025-06-08 20:52:41,205 - INFO - 查询分段 23: 2025-02-14 至 2025-02-15
2025-06-08 20:52:41,205 - INFO - 查询日期范围: 2025-02-14 至 2025-02-15，使用分页查询，每页 100 条记录
2025-06-08 20:52:41,205 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:41,205 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:41,205 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1739462400000, 1739548800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:41,438 - INFO - API请求耗时: 233ms
2025-06-08 20:52:41,438 - INFO - Response - Page 1
2025-06-08 20:52:41,438 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:41,438 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:41,438 - WARNING - 分段 23 查询返回空数据
2025-06-08 20:52:42,438 - INFO - 查询分段 24: 2025-02-16 至 2025-02-17
2025-06-08 20:52:42,438 - INFO - 查询日期范围: 2025-02-16 至 2025-02-17，使用分页查询，每页 100 条记录
2025-06-08 20:52:42,438 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:42,438 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:42,438 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1739635200000, 1739721600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:42,703 - INFO - API请求耗时: 266ms
2025-06-08 20:52:42,703 - INFO - Response - Page 1
2025-06-08 20:52:42,703 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:42,703 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:42,703 - WARNING - 分段 24 查询返回空数据
2025-06-08 20:52:43,703 - INFO - 查询分段 25: 2025-02-18 至 2025-02-19
2025-06-08 20:52:43,703 - INFO - 查询日期范围: 2025-02-18 至 2025-02-19，使用分页查询，每页 100 条记录
2025-06-08 20:52:43,703 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:43,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:43,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1739808000000, 1739894400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:43,968 - INFO - API请求耗时: 265ms
2025-06-08 20:52:43,968 - INFO - Response - Page 1
2025-06-08 20:52:43,968 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:43,968 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:43,968 - WARNING - 分段 25 查询返回空数据
2025-06-08 20:52:44,968 - INFO - 查询分段 26: 2025-02-20 至 2025-02-21
2025-06-08 20:52:44,968 - INFO - 查询日期范围: 2025-02-20 至 2025-02-21，使用分页查询，每页 100 条记录
2025-06-08 20:52:44,968 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:44,968 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:44,968 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1739980800000, 1740067200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:45,233 - INFO - API请求耗时: 266ms
2025-06-08 20:52:45,233 - INFO - Response - Page 1
2025-06-08 20:52:45,233 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:45,233 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:45,233 - WARNING - 分段 26 查询返回空数据
2025-06-08 20:52:46,248 - INFO - 查询分段 27: 2025-02-22 至 2025-02-23
2025-06-08 20:52:46,248 - INFO - 查询日期范围: 2025-02-22 至 2025-02-23，使用分页查询，每页 100 条记录
2025-06-08 20:52:46,248 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:46,248 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:46,248 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740153600000, 1740240000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:46,498 - INFO - API请求耗时: 250ms
2025-06-08 20:52:46,498 - INFO - Response - Page 1
2025-06-08 20:52:46,498 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:46,498 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:46,498 - WARNING - 分段 27 查询返回空数据
2025-06-08 20:52:47,514 - INFO - 查询分段 28: 2025-02-24 至 2025-02-25
2025-06-08 20:52:47,514 - INFO - 查询日期范围: 2025-02-24 至 2025-02-25，使用分页查询，每页 100 条记录
2025-06-08 20:52:47,514 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:47,514 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:47,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740326400000, 1740412800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:47,825 - INFO - API请求耗时: 311ms
2025-06-08 20:52:47,825 - INFO - Response - Page 1
2025-06-08 20:52:47,825 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:47,825 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:47,825 - WARNING - 分段 28 查询返回空数据
2025-06-08 20:52:48,825 - INFO - 查询分段 29: 2025-02-26 至 2025-02-27
2025-06-08 20:52:48,825 - INFO - 查询日期范围: 2025-02-26 至 2025-02-27，使用分页查询，每页 100 条记录
2025-06-08 20:52:48,825 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:48,825 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:48,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740499200000, 1740585600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:49,122 - INFO - API请求耗时: 297ms
2025-06-08 20:52:49,122 - INFO - Response - Page 1
2025-06-08 20:52:49,122 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:49,122 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:49,122 - WARNING - 分段 29 查询返回空数据
2025-06-08 20:52:50,135 - INFO - 查询分段 30: 2025-02-28 至 2025-03-01
2025-06-08 20:52:50,135 - INFO - 查询日期范围: 2025-02-28 至 2025-03-01，使用分页查询，每页 100 条记录
2025-06-08 20:52:50,135 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:50,135 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:50,135 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740672000000, 1740758400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:50,447 - INFO - API请求耗时: 311ms
2025-06-08 20:52:50,447 - INFO - Response - Page 1
2025-06-08 20:52:50,447 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:50,447 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:50,447 - WARNING - 分段 30 查询返回空数据
2025-06-08 20:52:51,460 - INFO - 查询分段 31: 2025-03-02 至 2025-03-03
2025-06-08 20:52:51,460 - INFO - 查询日期范围: 2025-03-02 至 2025-03-03，使用分页查询，每页 100 条记录
2025-06-08 20:52:51,460 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:51,460 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:51,460 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740844800000, 1740931200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:51,710 - INFO - API请求耗时: 250ms
2025-06-08 20:52:51,710 - INFO - Response - Page 1
2025-06-08 20:52:51,710 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:51,710 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:51,710 - WARNING - 分段 31 查询返回空数据
2025-06-08 20:52:52,723 - INFO - 查询分段 32: 2025-03-04 至 2025-03-05
2025-06-08 20:52:52,723 - INFO - 查询日期范围: 2025-03-04 至 2025-03-05，使用分页查询，每页 100 条记录
2025-06-08 20:52:52,723 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:52,723 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:52,723 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1741017600000, 1741104000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:53,004 - INFO - API请求耗时: 280ms
2025-06-08 20:52:53,004 - INFO - Response - Page 1
2025-06-08 20:52:53,004 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:53,004 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:53,004 - WARNING - 分段 32 查询返回空数据
2025-06-08 20:52:54,018 - INFO - 查询分段 33: 2025-03-06 至 2025-03-07
2025-06-08 20:52:54,018 - INFO - 查询日期范围: 2025-03-06 至 2025-03-07，使用分页查询，每页 100 条记录
2025-06-08 20:52:54,018 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:54,018 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:54,018 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1741190400000, 1741276800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:54,267 - INFO - API请求耗时: 249ms
2025-06-08 20:52:54,282 - INFO - Response - Page 1
2025-06-08 20:52:54,282 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:54,282 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:54,282 - WARNING - 分段 33 查询返回空数据
2025-06-08 20:52:55,297 - INFO - 查询分段 34: 2025-03-08 至 2025-03-09
2025-06-08 20:52:55,297 - INFO - 查询日期范围: 2025-03-08 至 2025-03-09，使用分页查询，每页 100 条记录
2025-06-08 20:52:55,297 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:55,297 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:55,297 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1741363200000, 1741449600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:55,515 - INFO - API请求耗时: 218ms
2025-06-08 20:52:55,515 - INFO - Response - Page 1
2025-06-08 20:52:55,515 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:55,515 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:55,515 - WARNING - 分段 34 查询返回空数据
2025-06-08 20:52:56,529 - INFO - 查询分段 35: 2025-03-10 至 2025-03-11
2025-06-08 20:52:56,529 - INFO - 查询日期范围: 2025-03-10 至 2025-03-11，使用分页查询，每页 100 条记录
2025-06-08 20:52:56,529 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:56,529 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:56,529 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1741536000000, 1741622400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:56,778 - INFO - API请求耗时: 249ms
2025-06-08 20:52:56,794 - INFO - Response - Page 1
2025-06-08 20:52:56,794 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:56,794 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:56,794 - WARNING - 分段 35 查询返回空数据
2025-06-08 20:52:57,806 - INFO - 查询分段 36: 2025-03-12 至 2025-03-13
2025-06-08 20:52:57,806 - INFO - 查询日期范围: 2025-03-12 至 2025-03-13，使用分页查询，每页 100 条记录
2025-06-08 20:52:57,806 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:57,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:57,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1741708800000, 1741795200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:58,039 - INFO - API请求耗时: 233ms
2025-06-08 20:52:58,039 - INFO - Response - Page 1
2025-06-08 20:52:58,039 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:58,039 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:58,039 - WARNING - 分段 36 查询返回空数据
2025-06-08 20:52:59,055 - INFO - 查询分段 37: 2025-03-14 至 2025-03-15
2025-06-08 20:52:59,055 - INFO - 查询日期范围: 2025-03-14 至 2025-03-15，使用分页查询，每页 100 条记录
2025-06-08 20:52:59,055 - INFO - Request Parameters - Page 1:
2025-06-08 20:52:59,055 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:52:59,055 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1741881600000, 1741968000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:52:59,319 - INFO - API请求耗时: 265ms
2025-06-08 20:52:59,335 - INFO - Response - Page 1
2025-06-08 20:52:59,335 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:52:59,335 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:52:59,335 - WARNING - 分段 37 查询返回空数据
2025-06-08 20:53:00,349 - INFO - 查询分段 38: 2025-03-16 至 2025-03-17
2025-06-08 20:53:00,349 - INFO - 查询日期范围: 2025-03-16 至 2025-03-17，使用分页查询，每页 100 条记录
2025-06-08 20:53:00,349 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:00,349 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:00,349 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742054400000, 1742140800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:00,583 - INFO - API请求耗时: 233ms
2025-06-08 20:53:00,583 - INFO - Response - Page 1
2025-06-08 20:53:00,583 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:00,583 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:00,583 - WARNING - 分段 38 查询返回空数据
2025-06-08 20:53:01,598 - INFO - 查询分段 39: 2025-03-18 至 2025-03-19
2025-06-08 20:53:01,598 - INFO - 查询日期范围: 2025-03-18 至 2025-03-19，使用分页查询，每页 100 条记录
2025-06-08 20:53:01,598 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:01,598 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:01,598 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742227200000, 1742313600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:01,801 - INFO - API请求耗时: 203ms
2025-06-08 20:53:01,801 - INFO - Response - Page 1
2025-06-08 20:53:01,801 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:01,801 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:01,801 - WARNING - 分段 39 查询返回空数据
2025-06-08 20:53:02,816 - INFO - 查询分段 40: 2025-03-20 至 2025-03-21
2025-06-08 20:53:02,816 - INFO - 查询日期范围: 2025-03-20 至 2025-03-21，使用分页查询，每页 100 条记录
2025-06-08 20:53:02,816 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:02,816 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:02,816 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742400000000, 1742486400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:03,066 - INFO - API请求耗时: 250ms
2025-06-08 20:53:03,066 - INFO - Response - Page 1
2025-06-08 20:53:03,066 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:03,066 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:03,066 - WARNING - 分段 40 查询返回空数据
2025-06-08 20:53:04,082 - INFO - 查询分段 41: 2025-03-22 至 2025-03-23
2025-06-08 20:53:04,082 - INFO - 查询日期范围: 2025-03-22 至 2025-03-23，使用分页查询，每页 100 条记录
2025-06-08 20:53:04,082 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:04,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:04,082 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742572800000, 1742659200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:04,377 - INFO - API请求耗时: 296ms
2025-06-08 20:53:04,377 - INFO - Response - Page 1
2025-06-08 20:53:04,377 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:04,377 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:04,377 - WARNING - 分段 41 查询返回空数据
2025-06-08 20:53:05,393 - INFO - 查询分段 42: 2025-03-24 至 2025-03-25
2025-06-08 20:53:05,393 - INFO - 查询日期范围: 2025-03-24 至 2025-03-25，使用分页查询，每页 100 条记录
2025-06-08 20:53:05,393 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:05,393 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:05,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742745600000, 1742832000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:05,657 - INFO - API请求耗时: 265ms
2025-06-08 20:53:05,657 - INFO - Response - Page 1
2025-06-08 20:53:05,657 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:05,673 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:05,673 - WARNING - 分段 42 查询返回空数据
2025-06-08 20:53:06,688 - INFO - 查询分段 43: 2025-03-26 至 2025-03-27
2025-06-08 20:53:06,688 - INFO - 查询日期范围: 2025-03-26 至 2025-03-27，使用分页查询，每页 100 条记录
2025-06-08 20:53:06,688 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:06,688 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:06,688 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1742918400000, 1743004800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:06,954 - INFO - API请求耗时: 266ms
2025-06-08 20:53:06,954 - INFO - Response - Page 1
2025-06-08 20:53:06,954 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:06,954 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:06,954 - WARNING - 分段 43 查询返回空数据
2025-06-08 20:53:07,969 - INFO - 查询分段 44: 2025-03-28 至 2025-03-29
2025-06-08 20:53:07,969 - INFO - 查询日期范围: 2025-03-28 至 2025-03-29，使用分页查询，每页 100 条记录
2025-06-08 20:53:07,969 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:07,969 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:07,969 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743091200000, 1743177600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:08,266 - INFO - API请求耗时: 297ms
2025-06-08 20:53:08,266 - INFO - Response - Page 1
2025-06-08 20:53:08,266 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:08,266 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:08,266 - WARNING - 分段 44 查询返回空数据
2025-06-08 20:53:09,281 - INFO - 查询分段 45: 2025-03-30 至 2025-03-31
2025-06-08 20:53:09,281 - INFO - 查询日期范围: 2025-03-30 至 2025-03-31，使用分页查询，每页 100 条记录
2025-06-08 20:53:09,281 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:09,281 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:09,281 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743264000000, 1743350400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:09,531 - INFO - API请求耗时: 250ms
2025-06-08 20:53:09,531 - INFO - Response - Page 1
2025-06-08 20:53:09,531 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:09,531 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:09,531 - WARNING - 分段 45 查询返回空数据
2025-06-08 20:53:10,547 - INFO - 查询分段 46: 2025-04-01 至 2025-04-02
2025-06-08 20:53:10,547 - INFO - 查询日期范围: 2025-04-01 至 2025-04-02，使用分页查询，每页 100 条记录
2025-06-08 20:53:10,547 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:10,547 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:10,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800000, 1743523200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:10,827 - INFO - API请求耗时: 280ms
2025-06-08 20:53:10,827 - INFO - Response - Page 1
2025-06-08 20:53:10,827 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:10,827 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:10,827 - WARNING - 分段 46 查询返回空数据
2025-06-08 20:53:11,832 - INFO - 查询分段 47: 2025-04-03 至 2025-04-04
2025-06-08 20:53:11,832 - INFO - 查询日期范围: 2025-04-03 至 2025-04-04，使用分页查询，每页 100 条记录
2025-06-08 20:53:11,832 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:11,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:11,832 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743609600000, 1743696000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:12,044 - INFO - API请求耗时: 213ms
2025-06-08 20:53:12,044 - INFO - Response - Page 1
2025-06-08 20:53:12,044 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:12,044 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:12,044 - WARNING - 分段 47 查询返回空数据
2025-06-08 20:53:13,048 - INFO - 查询分段 48: 2025-04-05 至 2025-04-06
2025-06-08 20:53:13,048 - INFO - 查询日期范围: 2025-04-05 至 2025-04-06，使用分页查询，每页 100 条记录
2025-06-08 20:53:13,048 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:13,048 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:13,048 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743782400000, 1743868800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:13,261 - INFO - API请求耗时: 213ms
2025-06-08 20:53:13,261 - INFO - Response - Page 1
2025-06-08 20:53:13,261 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:13,261 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:13,261 - WARNING - 分段 48 查询返回空数据
2025-06-08 20:53:14,264 - INFO - 查询分段 49: 2025-04-07 至 2025-04-08
2025-06-08 20:53:14,264 - INFO - 查询日期范围: 2025-04-07 至 2025-04-08，使用分页查询，每页 100 条记录
2025-06-08 20:53:14,264 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:14,264 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:14,264 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200000, 1744041600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:14,525 - INFO - API请求耗时: 262ms
2025-06-08 20:53:14,525 - INFO - Response - Page 1
2025-06-08 20:53:14,525 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:14,525 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:14,525 - WARNING - 分段 49 查询返回空数据
2025-06-08 20:53:15,541 - INFO - 查询分段 50: 2025-04-09 至 2025-04-10
2025-06-08 20:53:15,541 - INFO - 查询日期范围: 2025-04-09 至 2025-04-10，使用分页查询，每页 100 条记录
2025-06-08 20:53:15,541 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:15,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:15,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000000, 1744214400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:15,774 - INFO - API请求耗时: 233ms
2025-06-08 20:53:15,774 - INFO - Response - Page 1
2025-06-08 20:53:15,774 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:15,774 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:15,774 - WARNING - 分段 50 查询返回空数据
2025-06-08 20:53:16,789 - INFO - 查询分段 51: 2025-04-11 至 2025-04-12
2025-06-08 20:53:16,789 - INFO - 查询日期范围: 2025-04-11 至 2025-04-12，使用分页查询，每页 100 条记录
2025-06-08 20:53:16,789 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:16,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:16,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800000, 1744387200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:16,991 - INFO - API请求耗时: 202ms
2025-06-08 20:53:16,991 - INFO - Response - Page 1
2025-06-08 20:53:16,991 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:16,991 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:16,991 - WARNING - 分段 51 查询返回空数据
2025-06-08 20:53:18,007 - INFO - 查询分段 52: 2025-04-13 至 2025-04-14
2025-06-08 20:53:18,007 - INFO - 查询日期范围: 2025-04-13 至 2025-04-14，使用分页查询，每页 100 条记录
2025-06-08 20:53:18,007 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:18,007 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:18,007 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600000, 1744560000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:18,303 - INFO - API请求耗时: 296ms
2025-06-08 20:53:18,303 - INFO - Response - Page 1
2025-06-08 20:53:18,303 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:18,303 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:18,303 - WARNING - 分段 52 查询返回空数据
2025-06-08 20:53:19,315 - INFO - 查询分段 53: 2025-04-15 至 2025-04-16
2025-06-08 20:53:19,315 - INFO - 查询日期范围: 2025-04-15 至 2025-04-16，使用分页查询，每页 100 条记录
2025-06-08 20:53:19,315 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:19,315 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:19,315 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400000, 1744732800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:19,579 - INFO - API请求耗时: 264ms
2025-06-08 20:53:19,579 - INFO - Response - Page 1
2025-06-08 20:53:19,579 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:19,579 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:19,579 - WARNING - 分段 53 查询返回空数据
2025-06-08 20:53:20,592 - INFO - 查询分段 54: 2025-04-17 至 2025-04-18
2025-06-08 20:53:20,592 - INFO - 查询日期范围: 2025-04-17 至 2025-04-18，使用分页查询，每页 100 条记录
2025-06-08 20:53:20,592 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:20,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:20,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200000, 1744905600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:20,872 - INFO - API请求耗时: 280ms
2025-06-08 20:53:20,872 - INFO - Response - Page 1
2025-06-08 20:53:20,872 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:20,872 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:20,872 - WARNING - 分段 54 查询返回空数据
2025-06-08 20:53:21,887 - INFO - 查询分段 55: 2025-04-19 至 2025-04-20
2025-06-08 20:53:21,887 - INFO - 查询日期范围: 2025-04-19 至 2025-04-20，使用分页查询，每页 100 条记录
2025-06-08 20:53:21,887 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:21,887 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:21,887 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744992000000, 1745078400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:22,136 - INFO - API请求耗时: 249ms
2025-06-08 20:53:22,136 - INFO - Response - Page 1
2025-06-08 20:53:22,136 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:22,136 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:22,136 - WARNING - 分段 55 查询返回空数据
2025-06-08 20:53:23,135 - INFO - 查询分段 56: 2025-04-21 至 2025-04-22
2025-06-08 20:53:23,135 - INFO - 查询日期范围: 2025-04-21 至 2025-04-22，使用分页查询，每页 100 条记录
2025-06-08 20:53:23,135 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:23,135 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:23,135 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800000, 1745251200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:23,431 - INFO - API请求耗时: 296ms
2025-06-08 20:53:23,431 - INFO - Response - Page 1
2025-06-08 20:53:23,431 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:23,431 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:23,431 - WARNING - 分段 56 查询返回空数据
2025-06-08 20:53:24,445 - INFO - 查询分段 57: 2025-04-23 至 2025-04-24
2025-06-08 20:53:24,445 - INFO - 查询日期范围: 2025-04-23 至 2025-04-24，使用分页查询，每页 100 条记录
2025-06-08 20:53:24,445 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:24,445 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:24,445 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600000, 1745424000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:24,694 - INFO - API请求耗时: 249ms
2025-06-08 20:53:24,694 - INFO - Response - Page 1
2025-06-08 20:53:24,694 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:24,694 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:24,694 - WARNING - 分段 57 查询返回空数据
2025-06-08 20:53:25,709 - INFO - 查询分段 58: 2025-04-25 至 2025-04-26
2025-06-08 20:53:25,709 - INFO - 查询日期范围: 2025-04-25 至 2025-04-26，使用分页查询，每页 100 条记录
2025-06-08 20:53:25,709 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:25,709 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:25,709 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400000, 1745596800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:26,036 - INFO - API请求耗时: 327ms
2025-06-08 20:53:26,036 - INFO - Response - Page 1
2025-06-08 20:53:26,036 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:26,036 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:26,036 - WARNING - 分段 58 查询返回空数据
2025-06-08 20:53:27,050 - INFO - 查询分段 59: 2025-04-27 至 2025-04-28
2025-06-08 20:53:27,050 - INFO - 查询日期范围: 2025-04-27 至 2025-04-28，使用分页查询，每页 100 条记录
2025-06-08 20:53:27,050 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:27,050 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:27,050 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200000, 1745769600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:27,238 - INFO - API请求耗时: 187ms
2025-06-08 20:53:27,238 - INFO - Response - Page 1
2025-06-08 20:53:27,238 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:27,238 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:27,238 - WARNING - 分段 59 查询返回空数据
2025-06-08 20:53:28,252 - INFO - 查询分段 60: 2025-04-29 至 2025-04-30
2025-06-08 20:53:28,252 - INFO - 查询日期范围: 2025-04-29 至 2025-04-30，使用分页查询，每页 100 条记录
2025-06-08 20:53:28,252 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:28,252 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:28,252 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000000, 1745942400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:28,499 - INFO - API请求耗时: 247ms
2025-06-08 20:53:28,499 - INFO - Response - Page 1
2025-06-08 20:53:28,499 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:28,499 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:28,499 - WARNING - 分段 60 查询返回空数据
2025-06-08 20:53:29,513 - INFO - 查询分段 61: 2025-05-01 至 2025-05-02
2025-06-08 20:53:29,513 - INFO - 查询日期范围: 2025-05-01 至 2025-05-02，使用分页查询，每页 100 条记录
2025-06-08 20:53:29,513 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:29,513 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:29,513 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800000, 1746115200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:29,716 - INFO - API请求耗时: 202ms
2025-06-08 20:53:29,716 - INFO - Response - Page 1
2025-06-08 20:53:29,716 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:29,716 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:29,716 - WARNING - 分段 61 查询返回空数据
2025-06-08 20:53:30,731 - INFO - 查询分段 62: 2025-05-03 至 2025-05-04
2025-06-08 20:53:30,731 - INFO - 查询日期范围: 2025-05-03 至 2025-05-04，使用分页查询，每页 100 条记录
2025-06-08 20:53:30,731 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:30,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:30,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746201600000, 1746288000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:30,933 - INFO - API请求耗时: 202ms
2025-06-08 20:53:30,933 - INFO - Response - Page 1
2025-06-08 20:53:30,933 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:30,933 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:30,933 - WARNING - 分段 62 查询返回空数据
2025-06-08 20:53:31,933 - INFO - 查询分段 63: 2025-05-05 至 2025-05-06
2025-06-08 20:53:31,933 - INFO - 查询日期范围: 2025-05-05 至 2025-05-06，使用分页查询，每页 100 条记录
2025-06-08 20:53:31,933 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:31,933 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:31,933 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400000, 1746460800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:32,198 - INFO - API请求耗时: 266ms
2025-06-08 20:53:32,198 - INFO - Response - Page 1
2025-06-08 20:53:32,198 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:32,198 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:32,198 - WARNING - 分段 63 查询返回空数据
2025-06-08 20:53:33,214 - INFO - 查询分段 64: 2025-05-07 至 2025-05-08
2025-06-08 20:53:33,214 - INFO - 查询日期范围: 2025-05-07 至 2025-05-08，使用分页查询，每页 100 条记录
2025-06-08 20:53:33,214 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:33,214 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:33,214 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200000, 1746633600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:33,448 - INFO - API请求耗时: 234ms
2025-06-08 20:53:33,448 - INFO - Response - Page 1
2025-06-08 20:53:33,448 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:33,448 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:33,448 - WARNING - 分段 64 查询返回空数据
2025-06-08 20:53:34,463 - INFO - 查询分段 65: 2025-05-09 至 2025-05-10
2025-06-08 20:53:34,463 - INFO - 查询日期范围: 2025-05-09 至 2025-05-10，使用分页查询，每页 100 条记录
2025-06-08 20:53:34,463 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:34,463 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:34,463 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000000, 1746806400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:34,760 - INFO - API请求耗时: 297ms
2025-06-08 20:53:34,760 - INFO - Response - Page 1
2025-06-08 20:53:34,760 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:34,760 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:34,760 - WARNING - 分段 65 查询返回空数据
2025-06-08 20:53:35,775 - INFO - 查询分段 66: 2025-05-11 至 2025-05-12
2025-06-08 20:53:35,775 - INFO - 查询日期范围: 2025-05-11 至 2025-05-12，使用分页查询，每页 100 条记录
2025-06-08 20:53:35,775 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:35,775 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:35,775 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800000, 1746979200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:36,010 - INFO - API请求耗时: 234ms
2025-06-08 20:53:36,010 - INFO - Response - Page 1
2025-06-08 20:53:36,010 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:36,010 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:36,010 - WARNING - 分段 66 查询返回空数据
2025-06-08 20:53:37,010 - INFO - 查询分段 67: 2025-05-13 至 2025-05-14
2025-06-08 20:53:37,010 - INFO - 查询日期范围: 2025-05-13 至 2025-05-14，使用分页查询，每页 100 条记录
2025-06-08 20:53:37,010 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:37,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:37,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600000, 1747152000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:37,290 - INFO - API请求耗时: 280ms
2025-06-08 20:53:37,290 - INFO - Response - Page 1
2025-06-08 20:53:37,290 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:37,290 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:37,290 - WARNING - 分段 67 查询返回空数据
2025-06-08 20:53:38,305 - INFO - 查询分段 68: 2025-05-15 至 2025-05-16
2025-06-08 20:53:38,305 - INFO - 查询日期范围: 2025-05-15 至 2025-05-16，使用分页查询，每页 100 条记录
2025-06-08 20:53:38,305 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:38,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:38,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400000, 1747324800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:38,555 - INFO - API请求耗时: 250ms
2025-06-08 20:53:38,555 - INFO - Response - Page 1
2025-06-08 20:53:38,555 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:38,555 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:38,555 - WARNING - 分段 68 查询返回空数据
2025-06-08 20:53:39,570 - INFO - 查询分段 69: 2025-05-17 至 2025-05-18
2025-06-08 20:53:39,570 - INFO - 查询日期范围: 2025-05-17 至 2025-05-18，使用分页查询，每页 100 条记录
2025-06-08 20:53:39,570 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:39,570 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:39,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747411200000, 1747497600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:39,772 - INFO - API请求耗时: 202ms
2025-06-08 20:53:39,772 - INFO - Response - Page 1
2025-06-08 20:53:39,772 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:39,772 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:39,772 - WARNING - 分段 69 查询返回空数据
2025-06-08 20:53:40,772 - INFO - 查询分段 70: 2025-05-19 至 2025-05-20
2025-06-08 20:53:40,772 - INFO - 查询日期范围: 2025-05-19 至 2025-05-20，使用分页查询，每页 100 条记录
2025-06-08 20:53:40,772 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:40,772 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:40,772 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747584000000, 1747670400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:41,053 - INFO - API请求耗时: 281ms
2025-06-08 20:53:41,053 - INFO - Response - Page 1
2025-06-08 20:53:41,053 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:41,053 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:41,053 - WARNING - 分段 70 查询返回空数据
2025-06-08 20:53:42,053 - INFO - 查询分段 71: 2025-05-21 至 2025-05-22
2025-06-08 20:53:42,053 - INFO - 查询日期范围: 2025-05-21 至 2025-05-22，使用分页查询，每页 100 条记录
2025-06-08 20:53:42,053 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:42,053 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:42,053 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747756800000, 1747843200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:42,350 - INFO - API请求耗时: 297ms
2025-06-08 20:53:42,350 - INFO - Response - Page 1
2025-06-08 20:53:42,350 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:42,350 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:42,350 - WARNING - 分段 71 查询返回空数据
2025-06-08 20:53:43,364 - INFO - 查询分段 72: 2025-05-23 至 2025-05-24
2025-06-08 20:53:43,364 - INFO - 查询日期范围: 2025-05-23 至 2025-05-24，使用分页查询，每页 100 条记录
2025-06-08 20:53:43,364 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:43,364 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:43,364 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747929600000, 1748016000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:43,661 - INFO - API请求耗时: 297ms
2025-06-08 20:53:43,661 - INFO - Response - Page 1
2025-06-08 20:53:43,661 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:43,661 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:43,661 - WARNING - 分段 72 查询返回空数据
2025-06-08 20:53:44,675 - INFO - 查询分段 73: 2025-05-25 至 2025-05-26
2025-06-08 20:53:44,675 - INFO - 查询日期范围: 2025-05-25 至 2025-05-26，使用分页查询，每页 100 条记录
2025-06-08 20:53:44,675 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:44,675 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:44,675 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748102400000, 1748188800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:44,941 - INFO - API请求耗时: 266ms
2025-06-08 20:53:44,941 - INFO - Response - Page 1
2025-06-08 20:53:44,941 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:44,941 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:44,941 - WARNING - 分段 73 查询返回空数据
2025-06-08 20:53:45,955 - INFO - 查询分段 74: 2025-05-27 至 2025-05-28
2025-06-08 20:53:45,955 - INFO - 查询日期范围: 2025-05-27 至 2025-05-28，使用分页查询，每页 100 条记录
2025-06-08 20:53:45,955 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:45,955 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:45,955 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748275200000, 1748361600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:46,237 - INFO - API请求耗时: 281ms
2025-06-08 20:53:46,237 - INFO - Response - Page 1
2025-06-08 20:53:46,237 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:46,237 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:46,237 - WARNING - 分段 74 查询返回空数据
2025-06-08 20:53:47,251 - INFO - 查询分段 75: 2025-05-29 至 2025-05-30
2025-06-08 20:53:47,251 - INFO - 查询日期范围: 2025-05-29 至 2025-05-30，使用分页查询，每页 100 条记录
2025-06-08 20:53:47,251 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:47,251 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:47,251 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748448000000, 1748534400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:47,484 - INFO - API请求耗时: 233ms
2025-06-08 20:53:47,484 - INFO - Response - Page 1
2025-06-08 20:53:47,484 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:47,484 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:47,484 - WARNING - 分段 75 查询返回空数据
2025-06-08 20:53:48,515 - INFO - 查询分段 76: 2025-05-31 至 2025-06-01
2025-06-08 20:53:48,515 - INFO - 查询日期范围: 2025-05-31 至 2025-06-01，使用分页查询，每页 100 条记录
2025-06-08 20:53:48,515 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:48,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:48,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748620800000, 1748707200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:48,781 - INFO - API请求耗时: 266ms
2025-06-08 20:53:48,781 - INFO - Response - Page 1
2025-06-08 20:53:48,781 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:48,781 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:48,781 - WARNING - 分段 76 查询返回空数据
2025-06-08 20:53:49,796 - INFO - 查询分段 77: 2025-06-02 至 2025-06-03
2025-06-08 20:53:49,796 - INFO - 查询日期范围: 2025-06-02 至 2025-06-03，使用分页查询，每页 100 条记录
2025-06-08 20:53:49,796 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:49,796 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:49,796 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748793600000, 1748880000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:50,031 - INFO - API请求耗时: 234ms
2025-06-08 20:53:50,031 - INFO - Response - Page 1
2025-06-08 20:53:50,046 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:50,046 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:50,046 - WARNING - 分段 77 查询返回空数据
2025-06-08 20:53:51,046 - INFO - 查询分段 78: 2025-06-04 至 2025-06-05
2025-06-08 20:53:51,046 - INFO - 查询日期范围: 2025-06-04 至 2025-06-05，使用分页查询，每页 100 条记录
2025-06-08 20:53:51,046 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:51,046 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:51,046 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748966400000, 1749052800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:51,311 - INFO - API请求耗时: 266ms
2025-06-08 20:53:51,311 - INFO - Response - Page 1
2025-06-08 20:53:51,311 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:51,311 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:51,311 - WARNING - 分段 78 查询返回空数据
2025-06-08 20:53:52,311 - INFO - 查询分段 79: 2025-06-06 至 2025-06-07
2025-06-08 20:53:52,311 - INFO - 查询日期范围: 2025-06-06 至 2025-06-07，使用分页查询，每页 100 条记录
2025-06-08 20:53:52,311 - INFO - Request Parameters - Page 1:
2025-06-08 20:53:52,311 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 20:53:52,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749139200000, 1749225600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 20:53:52,513 - INFO - API请求耗时: 202ms
2025-06-08 20:53:52,513 - INFO - Response - Page 1
2025-06-08 20:53:52,513 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-08 20:53:52,513 - INFO - 查询完成，共获取到 0 条记录
2025-06-08 20:53:52,513 - WARNING - 分段 79 查询返回空数据
2025-06-08 20:53:53,528 - INFO - 宜搭每日表单数据查询完成，共 79 个分段，成功获取 0 条记录，失败 0 次
2025-06-08 20:53:53,528 - INFO - 成功获取宜搭日销售表单数据，共 0 条记录
2025-06-08 20:53:53,528 - INFO - 开始对比和同步日销售数据...
2025-06-08 20:53:53,528 - INFO - 成功创建宜搭日销售数据索引，共 0 条记录
2025-06-08 20:53:53,528 - INFO - 开始处理数衍数据，共 34112 条记录
2025-06-08 20:54:07,725 - INFO - 正在批量插入每日数据，批次 1/342，共 100 条记录
2025-06-08 20:54:08,147 - INFO - 批量插入每日数据成功，批次 1，100 条记录
2025-06-08 20:54:11,159 - INFO - 正在批量插入每日数据，批次 2/342，共 100 条记录
2025-06-08 20:54:11,643 - INFO - 批量插入每日数据成功，批次 2，100 条记录
2025-06-08 20:54:14,672 - INFO - 正在批量插入每日数据，批次 3/342，共 100 条记录
2025-06-08 20:54:15,046 - INFO - 批量插入每日数据成功，批次 3，100 条记录
2025-06-08 20:54:18,061 - INFO - 正在批量插入每日数据，批次 4/342，共 100 条记录
2025-06-08 20:54:18,530 - INFO - 批量插入每日数据成功，批次 4，100 条记录
2025-06-08 20:54:21,545 - INFO - 正在批量插入每日数据，批次 5/342，共 100 条记录
2025-06-08 20:54:21,951 - INFO - 批量插入每日数据成功，批次 5，100 条记录
2025-06-08 20:54:24,966 - INFO - 正在批量插入每日数据，批次 6/342，共 100 条记录
2025-06-08 20:54:25,356 - INFO - 批量插入每日数据成功，批次 6，100 条记录
2025-06-08 20:54:28,371 - INFO - 正在批量插入每日数据，批次 7/342，共 100 条记录
2025-06-08 20:54:28,855 - INFO - 批量插入每日数据成功，批次 7，100 条记录
2025-06-08 20:54:31,870 - INFO - 正在批量插入每日数据，批次 8/342，共 100 条记录
2025-06-08 20:54:32,245 - INFO - 批量插入每日数据成功，批次 8，100 条记录
2025-06-08 20:54:35,260 - INFO - 正在批量插入每日数据，批次 9/342，共 100 条记录
2025-06-08 20:54:35,760 - INFO - 批量插入每日数据成功，批次 9，100 条记录
2025-06-08 20:54:38,759 - INFO - 正在批量插入每日数据，批次 10/342，共 100 条记录
2025-06-08 20:54:39,195 - INFO - 批量插入每日数据成功，批次 10，100 条记录
2025-06-08 20:54:42,210 - INFO - 正在批量插入每日数据，批次 11/342，共 100 条记录
2025-06-08 20:54:42,741 - INFO - 批量插入每日数据成功，批次 11，100 条记录
2025-06-08 20:54:45,756 - INFO - 正在批量插入每日数据，批次 12/342，共 100 条记录
2025-06-08 20:54:46,209 - INFO - 批量插入每日数据成功，批次 12，100 条记录
2025-06-08 20:54:49,224 - INFO - 正在批量插入每日数据，批次 13/342，共 100 条记录
2025-06-08 20:54:49,708 - INFO - 批量插入每日数据成功，批次 13，100 条记录
2025-06-08 20:54:52,707 - INFO - 正在批量插入每日数据，批次 14/342，共 100 条记录
2025-06-08 20:54:53,192 - INFO - 批量插入每日数据成功，批次 14，100 条记录
2025-06-08 20:54:56,206 - INFO - 正在批量插入每日数据，批次 15/342，共 100 条记录
2025-06-08 20:54:56,738 - INFO - 批量插入每日数据成功，批次 15，100 条记录
2025-06-08 20:54:59,737 - INFO - 正在批量插入每日数据，批次 16/342，共 100 条记录
2025-06-08 20:55:00,237 - INFO - 批量插入每日数据成功，批次 16，100 条记录
2025-06-08 20:55:03,251 - INFO - 正在批量插入每日数据，批次 17/342，共 100 条记录
2025-06-08 20:55:03,704 - INFO - 批量插入每日数据成功，批次 17，100 条记录
2025-06-08 20:55:06,718 - INFO - 正在批量插入每日数据，批次 18/342，共 100 条记录
2025-06-08 20:55:07,140 - INFO - 批量插入每日数据成功，批次 18，100 条记录
2025-06-08 20:55:10,155 - INFO - 正在批量插入每日数据，批次 19/342，共 100 条记录
2025-06-08 20:55:10,608 - INFO - 批量插入每日数据成功，批次 19，100 条记录
2025-06-08 20:55:13,623 - INFO - 正在批量插入每日数据，批次 20/342，共 100 条记录
2025-06-08 20:55:13,998 - INFO - 批量插入每日数据成功，批次 20，100 条记录
2025-06-08 20:55:17,012 - INFO - 正在批量插入每日数据，批次 21/342，共 100 条记录
2025-06-08 20:55:17,402 - INFO - 批量插入每日数据成功，批次 21，100 条记录
2025-06-08 20:55:20,417 - INFO - 正在批量插入每日数据，批次 22/342，共 100 条记录
2025-06-08 20:55:20,761 - INFO - 批量插入每日数据成功，批次 22，100 条记录
2025-06-08 20:55:23,775 - INFO - 正在批量插入每日数据，批次 23/342，共 100 条记录
2025-06-08 20:55:24,166 - INFO - 批量插入每日数据成功，批次 23，100 条记录
2025-06-08 20:55:27,180 - INFO - 正在批量插入每日数据，批次 24/342，共 100 条记录
2025-06-08 20:55:27,742 - INFO - 批量插入每日数据成功，批次 24，100 条记录
2025-06-08 20:55:30,756 - INFO - 正在批量插入每日数据，批次 25/342，共 100 条记录
2025-06-08 20:55:31,178 - INFO - 批量插入每日数据成功，批次 25，100 条记录
2025-06-08 20:55:34,193 - INFO - 正在批量插入每日数据，批次 26/342，共 100 条记录
2025-06-08 20:55:34,785 - INFO - 批量插入每日数据成功，批次 26，100 条记录
2025-06-08 20:55:37,799 - INFO - 正在批量插入每日数据，批次 27/342，共 100 条记录
2025-06-08 20:55:38,329 - INFO - 批量插入每日数据成功，批次 27，100 条记录
2025-06-08 20:55:41,342 - INFO - 正在批量插入每日数据，批次 28/342，共 100 条记录
2025-06-08 20:55:41,779 - INFO - 批量插入每日数据成功，批次 28，100 条记录
2025-06-08 20:55:44,791 - INFO - 正在批量插入每日数据，批次 29/342，共 100 条记录
2025-06-08 20:55:45,181 - INFO - 批量插入每日数据成功，批次 29，100 条记录
2025-06-08 20:55:48,195 - INFO - 正在批量插入每日数据，批次 30/342，共 100 条记录
2025-06-08 20:55:48,709 - INFO - 批量插入每日数据成功，批次 30，100 条记录
2025-06-08 20:55:51,723 - INFO - 正在批量插入每日数据，批次 31/342，共 100 条记录
2025-06-08 20:55:52,144 - INFO - 批量插入每日数据成功，批次 31，100 条记录
2025-06-08 20:55:55,159 - INFO - 正在批量插入每日数据，批次 32/342，共 100 条记录
2025-06-08 20:55:55,611 - INFO - 批量插入每日数据成功，批次 32，100 条记录
2025-06-08 20:55:58,626 - INFO - 正在批量插入每日数据，批次 33/342，共 100 条记录
2025-06-08 20:55:59,031 - INFO - 批量插入每日数据成功，批次 33，100 条记录
2025-06-08 20:56:02,045 - INFO - 正在批量插入每日数据，批次 34/342，共 100 条记录
2025-06-08 20:56:02,387 - INFO - 批量插入每日数据成功，批次 34，100 条记录
2025-06-08 20:56:05,401 - INFO - 正在批量插入每日数据，批次 35/342，共 100 条记录
2025-06-08 20:56:05,869 - INFO - 批量插入每日数据成功，批次 35，100 条记录
2025-06-08 20:56:08,883 - INFO - 正在批量插入每日数据，批次 36/342，共 100 条记录
2025-06-08 20:56:09,366 - INFO - 批量插入每日数据成功，批次 36，100 条记录
2025-06-08 20:56:12,381 - INFO - 正在批量插入每日数据，批次 37/342，共 100 条记录
2025-06-08 20:56:12,755 - INFO - 批量插入每日数据成功，批次 37，100 条记录
2025-06-08 20:56:15,769 - INFO - 正在批量插入每日数据，批次 38/342，共 100 条记录
2025-06-08 20:56:16,237 - INFO - 批量插入每日数据成功，批次 38，100 条记录
2025-06-08 20:56:19,251 - INFO - 正在批量插入每日数据，批次 39/342，共 100 条记录
2025-06-08 20:56:19,642 - INFO - 批量插入每日数据成功，批次 39，100 条记录
2025-06-08 20:56:22,656 - INFO - 正在批量插入每日数据，批次 40/342，共 100 条记录
2025-06-08 20:56:23,093 - INFO - 批量插入每日数据成功，批次 40，100 条记录
2025-06-08 20:56:26,108 - INFO - 正在批量插入每日数据，批次 41/342，共 100 条记录
2025-06-08 20:56:26,686 - INFO - 批量插入每日数据成功，批次 41，100 条记录
2025-06-08 20:56:29,700 - INFO - 正在批量插入每日数据，批次 42/342，共 100 条记录
2025-06-08 20:56:30,153 - INFO - 批量插入每日数据成功，批次 42，100 条记录
2025-06-08 20:56:33,165 - INFO - 正在批量插入每日数据，批次 43/342，共 100 条记录
2025-06-08 20:56:33,634 - INFO - 批量插入每日数据成功，批次 43，100 条记录
2025-06-08 20:56:36,648 - INFO - 正在批量插入每日数据，批次 44/342，共 100 条记录
2025-06-08 20:56:37,023 - INFO - 批量插入每日数据成功，批次 44，100 条记录
2025-06-08 20:56:40,037 - INFO - 正在批量插入每日数据，批次 45/342，共 100 条记录
2025-06-08 20:56:40,568 - INFO - 批量插入每日数据成功，批次 45，100 条记录
2025-06-08 20:56:43,582 - INFO - 正在批量插入每日数据，批次 46/342，共 100 条记录
2025-06-08 20:56:43,987 - INFO - 批量插入每日数据成功，批次 46，100 条记录
2025-06-08 20:56:47,002 - INFO - 正在批量插入每日数据，批次 47/342，共 100 条记录
2025-06-08 20:56:47,609 - INFO - 批量插入每日数据成功，批次 47，100 条记录
2025-06-08 20:56:50,609 - INFO - 正在批量插入每日数据，批次 48/342，共 100 条记录
2025-06-08 20:56:50,982 - INFO - 批量插入每日数据成功，批次 48，100 条记录
2025-06-08 20:56:53,996 - INFO - 正在批量插入每日数据，批次 49/342，共 100 条记录
2025-06-08 20:56:54,401 - INFO - 批量插入每日数据成功，批次 49，100 条记录
2025-06-08 20:56:57,415 - INFO - 正在批量插入每日数据，批次 50/342，共 100 条记录
2025-06-08 20:56:57,789 - INFO - 批量插入每日数据成功，批次 50，100 条记录
2025-06-08 20:57:00,804 - INFO - 正在批量插入每日数据，批次 51/342，共 100 条记录
2025-06-08 20:57:01,193 - INFO - 批量插入每日数据成功，批次 51，100 条记录
2025-06-08 20:57:04,224 - INFO - 正在批量插入每日数据，批次 52/342，共 100 条记录
2025-06-08 20:57:04,738 - INFO - 批量插入每日数据成功，批次 52，100 条记录
2025-06-08 20:57:07,753 - INFO - 正在批量插入每日数据，批次 53/342，共 100 条记录
2025-06-08 20:57:08,159 - INFO - 批量插入每日数据成功，批次 53，100 条记录
2025-06-08 20:57:11,174 - INFO - 正在批量插入每日数据，批次 54/342，共 100 条记录
2025-06-08 20:57:11,596 - INFO - 批量插入每日数据成功，批次 54，100 条记录
2025-06-08 20:57:14,611 - INFO - 正在批量插入每日数据，批次 55/342，共 100 条记录
2025-06-08 20:57:15,064 - INFO - 批量插入每日数据成功，批次 55，100 条记录
2025-06-08 20:57:18,078 - INFO - 正在批量插入每日数据，批次 56/342，共 100 条记录
2025-06-08 20:57:18,655 - INFO - 批量插入每日数据成功，批次 56，100 条记录
2025-06-08 20:57:21,670 - INFO - 正在批量插入每日数据，批次 57/342，共 100 条记录
2025-06-08 20:57:22,123 - INFO - 批量插入每日数据成功，批次 57，100 条记录
2025-06-08 20:57:25,134 - INFO - 正在批量插入每日数据，批次 58/342，共 100 条记录
2025-06-08 20:57:25,586 - INFO - 批量插入每日数据成功，批次 58，100 条记录
2025-06-08 20:57:28,588 - INFO - 正在批量插入每日数据，批次 59/342，共 100 条记录
2025-06-08 20:57:28,980 - INFO - 批量插入每日数据成功，批次 59，100 条记录
2025-06-08 20:57:31,982 - INFO - 正在批量插入每日数据，批次 60/342，共 100 条记录
2025-06-08 20:57:32,361 - INFO - 批量插入每日数据成功，批次 60，100 条记录
2025-06-08 20:57:35,363 - INFO - 正在批量插入每日数据，批次 61/342，共 100 条记录
2025-06-08 20:57:35,832 - INFO - 批量插入每日数据成功，批次 61，100 条记录
2025-06-08 20:57:38,835 - INFO - 正在批量插入每日数据，批次 62/342，共 100 条记录
2025-06-08 20:57:39,412 - INFO - 批量插入每日数据成功，批次 62，100 条记录
2025-06-08 20:57:42,412 - INFO - 正在批量插入每日数据，批次 63/342，共 100 条记录
2025-06-08 20:57:42,878 - INFO - 批量插入每日数据成功，批次 63，100 条记录
2025-06-08 20:57:45,878 - INFO - 正在批量插入每日数据，批次 64/342，共 100 条记录
2025-06-08 20:57:46,393 - INFO - 批量插入每日数据成功，批次 64，100 条记录
2025-06-08 20:57:49,394 - INFO - 正在批量插入每日数据，批次 65/342，共 100 条记录
2025-06-08 20:57:49,882 - INFO - 批量插入每日数据成功，批次 65，100 条记录
2025-06-08 20:57:52,884 - INFO - 正在批量插入每日数据，批次 66/342，共 100 条记录
2025-06-08 20:57:53,323 - INFO - 批量插入每日数据成功，批次 66，100 条记录
2025-06-08 20:57:56,325 - INFO - 正在批量插入每日数据，批次 67/342，共 100 条记录
2025-06-08 20:57:56,759 - INFO - 批量插入每日数据成功，批次 67，100 条记录
2025-06-08 20:57:59,766 - INFO - 正在批量插入每日数据，批次 68/342，共 100 条记录
2025-06-08 20:58:00,238 - INFO - 批量插入每日数据成功，批次 68，100 条记录
2025-06-08 20:58:03,246 - INFO - 正在批量插入每日数据，批次 69/342，共 100 条记录
2025-06-08 20:58:04,389 - INFO - 批量插入每日数据成功，批次 69，100 条记录
2025-06-08 20:58:07,401 - INFO - 正在批量插入每日数据，批次 70/342，共 100 条记录
2025-06-08 20:58:07,855 - INFO - 批量插入每日数据成功，批次 70，100 条记录
2025-06-08 20:58:10,856 - INFO - 正在批量插入每日数据，批次 71/342，共 100 条记录
2025-06-08 20:58:11,291 - INFO - 批量插入每日数据成功，批次 71，100 条记录
2025-06-08 20:58:14,292 - INFO - 正在批量插入每日数据，批次 72/342，共 100 条记录
2025-06-08 20:58:14,760 - INFO - 批量插入每日数据成功，批次 72，100 条记录
2025-06-08 20:58:17,774 - INFO - 正在批量插入每日数据，批次 73/342，共 100 条记录
2025-06-08 20:58:18,241 - INFO - 批量插入每日数据成功，批次 73，100 条记录
2025-06-08 20:58:21,254 - INFO - 正在批量插入每日数据，批次 74/342，共 100 条记录
2025-06-08 20:58:21,737 - INFO - 批量插入每日数据成功，批次 74，100 条记录
2025-06-08 20:58:24,751 - INFO - 正在批量插入每日数据，批次 75/342，共 100 条记录
2025-06-08 20:58:25,267 - INFO - 批量插入每日数据成功，批次 75，100 条记录
2025-06-08 20:58:28,282 - INFO - 正在批量插入每日数据，批次 76/342，共 100 条记录
2025-06-08 20:58:28,625 - INFO - 批量插入每日数据成功，批次 76，100 条记录
2025-06-08 20:58:31,640 - INFO - 正在批量插入每日数据，批次 77/342，共 100 条记录
2025-06-08 20:58:32,078 - INFO - 批量插入每日数据成功，批次 77，100 条记录
2025-06-08 20:58:35,092 - INFO - 正在批量插入每日数据，批次 78/342，共 100 条记录
2025-06-08 20:58:35,655 - INFO - 批量插入每日数据成功，批次 78，100 条记录
2025-06-08 20:58:38,670 - INFO - 正在批量插入每日数据，批次 79/342，共 100 条记录
2025-06-08 20:58:39,044 - INFO - 批量插入每日数据成功，批次 79，100 条记录
2025-06-08 20:58:42,059 - INFO - 正在批量插入每日数据，批次 80/342，共 100 条记录
2025-06-08 20:58:42,450 - INFO - 批量插入每日数据成功，批次 80，100 条记录
2025-06-08 20:58:45,465 - INFO - 正在批量插入每日数据，批次 81/342，共 100 条记录
2025-06-08 20:58:45,840 - INFO - 批量插入每日数据成功，批次 81，100 条记录
2025-06-08 20:58:48,854 - INFO - 正在批量插入每日数据，批次 82/342，共 100 条记录
2025-06-08 20:58:49,338 - INFO - 批量插入每日数据成功，批次 82，100 条记录
2025-06-08 20:58:52,352 - INFO - 正在批量插入每日数据，批次 83/342，共 100 条记录
2025-06-08 20:58:52,821 - INFO - 批量插入每日数据成功，批次 83，100 条记录
2025-06-08 20:58:55,836 - INFO - 正在批量插入每日数据，批次 84/342，共 100 条记录
2025-06-08 20:58:56,336 - INFO - 批量插入每日数据成功，批次 84，100 条记录
2025-06-08 20:58:59,351 - INFO - 正在批量插入每日数据，批次 85/342，共 100 条记录
2025-06-08 20:58:59,882 - INFO - 批量插入每日数据成功，批次 85，100 条记录
2025-06-08 20:59:02,897 - INFO - 正在批量插入每日数据，批次 86/342，共 100 条记录
2025-06-08 20:59:03,350 - INFO - 批量插入每日数据成功，批次 86，100 条记录
2025-06-08 20:59:06,364 - INFO - 正在批量插入每日数据，批次 87/342，共 100 条记录
2025-06-08 20:59:06,755 - INFO - 批量插入每日数据成功，批次 87，100 条记录
2025-06-08 20:59:09,768 - INFO - 正在批量插入每日数据，批次 88/342，共 100 条记录
2025-06-08 20:59:10,174 - INFO - 批量插入每日数据成功，批次 88，100 条记录
2025-06-08 20:59:13,187 - INFO - 正在批量插入每日数据，批次 89/342，共 100 条记录
2025-06-08 20:59:13,640 - INFO - 批量插入每日数据成功，批次 89，100 条记录
2025-06-08 20:59:16,653 - INFO - 正在批量插入每日数据，批次 90/342，共 100 条记录
2025-06-08 20:59:17,106 - INFO - 批量插入每日数据成功，批次 90，100 条记录
2025-06-08 20:59:20,120 - INFO - 正在批量插入每日数据，批次 91/342，共 100 条记录
2025-06-08 20:59:20,525 - INFO - 批量插入每日数据成功，批次 91，100 条记录
2025-06-08 20:59:23,540 - INFO - 正在批量插入每日数据，批次 92/342，共 100 条记录
2025-06-08 20:59:23,978 - INFO - 批量插入每日数据成功，批次 92，100 条记录
2025-06-08 20:59:26,978 - INFO - 正在批量插入每日数据，批次 93/342，共 100 条记录
2025-06-08 20:59:27,338 - INFO - 批量插入每日数据成功，批次 93，100 条记录
2025-06-08 20:59:30,339 - INFO - 正在批量插入每日数据，批次 94/342，共 100 条记录
2025-06-08 20:59:30,777 - INFO - 批量插入每日数据成功，批次 94，100 条记录
2025-06-08 20:59:33,788 - INFO - 正在批量插入每日数据，批次 95/342，共 100 条记录
2025-06-08 20:59:34,258 - INFO - 批量插入每日数据成功，批次 95，100 条记录
2025-06-08 20:59:37,262 - INFO - 正在批量插入每日数据，批次 96/342，共 100 条记录
2025-06-08 20:59:37,634 - INFO - 批量插入每日数据成功，批次 96，100 条记录
2025-06-08 20:59:40,637 - INFO - 正在批量插入每日数据，批次 97/342，共 100 条记录
2025-06-08 20:59:41,025 - INFO - 批量插入每日数据成功，批次 97，100 条记录
2025-06-08 20:59:44,028 - INFO - 正在批量插入每日数据，批次 98/342，共 100 条记录
2025-06-08 20:59:44,405 - INFO - 批量插入每日数据成功，批次 98，100 条记录
2025-06-08 20:59:47,407 - INFO - 正在批量插入每日数据，批次 99/342，共 100 条记录
2025-06-08 20:59:47,914 - INFO - 批量插入每日数据成功，批次 99，100 条记录
2025-06-08 20:59:50,916 - INFO - 正在批量插入每日数据，批次 100/342，共 100 条记录
2025-06-08 20:59:51,323 - INFO - 批量插入每日数据成功，批次 100，100 条记录
2025-06-08 20:59:54,325 - INFO - 正在批量插入每日数据，批次 101/342，共 100 条记录
2025-06-08 20:59:54,749 - INFO - 批量插入每日数据成功，批次 101，100 条记录
2025-06-08 20:59:57,751 - INFO - 正在批量插入每日数据，批次 102/342，共 100 条记录
2025-06-08 20:59:58,209 - INFO - 批量插入每日数据成功，批次 102，100 条记录
2025-06-08 21:00:01,211 - INFO - 正在批量插入每日数据，批次 103/342，共 100 条记录
2025-06-08 21:00:01,678 - INFO - 批量插入每日数据成功，批次 103，100 条记录
2025-06-08 21:00:04,691 - INFO - 正在批量插入每日数据，批次 104/342，共 100 条记录
2025-06-08 21:00:05,151 - INFO - 批量插入每日数据成功，批次 104，100 条记录
2025-06-08 21:00:08,153 - INFO - 正在批量插入每日数据，批次 105/342，共 100 条记录
2025-06-08 21:00:08,617 - INFO - 批量插入每日数据成功，批次 105，100 条记录
2025-06-08 21:00:11,626 - INFO - 正在批量插入每日数据，批次 106/342，共 100 条记录
2025-06-08 21:00:11,982 - INFO - 批量插入每日数据成功，批次 106，100 条记录
2025-06-08 21:00:14,996 - INFO - 正在批量插入每日数据，批次 107/342，共 100 条记录
2025-06-08 21:00:15,448 - INFO - 批量插入每日数据成功，批次 107，100 条记录
2025-06-08 21:00:18,461 - INFO - 正在批量插入每日数据，批次 108/342，共 100 条记录
2025-06-08 21:00:18,928 - INFO - 批量插入每日数据成功，批次 108，100 条记录
2025-06-08 21:00:21,940 - INFO - 正在批量插入每日数据，批次 109/342，共 100 条记录
2025-06-08 21:00:22,344 - INFO - 批量插入每日数据成功，批次 109，100 条记录
2025-06-08 21:00:25,347 - INFO - 正在批量插入每日数据，批次 110/342，共 100 条记录
2025-06-08 21:00:25,771 - INFO - 批量插入每日数据成功，批次 110，100 条记录
2025-06-08 21:00:28,786 - INFO - 正在批量插入每日数据，批次 111/342，共 100 条记录
2025-06-08 21:00:29,335 - INFO - 批量插入每日数据成功，批次 111，100 条记录
2025-06-08 21:00:32,338 - INFO - 正在批量插入每日数据，批次 112/342，共 100 条记录
2025-06-08 21:00:32,726 - INFO - 批量插入每日数据成功，批次 112，100 条记录
2025-06-08 21:00:35,728 - INFO - 正在批量插入每日数据，批次 113/342，共 100 条记录
2025-06-08 21:00:36,230 - INFO - 批量插入每日数据成功，批次 113，100 条记录
2025-06-08 21:00:39,233 - INFO - 正在批量插入每日数据，批次 114/342，共 100 条记录
2025-06-08 21:00:39,636 - INFO - 批量插入每日数据成功，批次 114，100 条记录
2025-06-08 21:00:42,637 - INFO - 正在批量插入每日数据，批次 115/342，共 100 条记录
2025-06-08 21:00:43,147 - INFO - 批量插入每日数据成功，批次 115，100 条记录
2025-06-08 21:00:46,149 - INFO - 正在批量插入每日数据，批次 116/342，共 100 条记录
2025-06-08 21:00:46,670 - INFO - 批量插入每日数据成功，批次 116，100 条记录
2025-06-08 21:00:49,680 - INFO - 正在批量插入每日数据，批次 117/342，共 100 条记录
2025-06-08 21:00:50,039 - INFO - 批量插入每日数据成功，批次 117，100 条记录
2025-06-08 21:00:53,051 - INFO - 正在批量插入每日数据，批次 118/342，共 100 条记录
2025-06-08 21:00:53,568 - INFO - 批量插入每日数据成功，批次 118，100 条记录
2025-06-08 21:00:56,568 - INFO - 正在批量插入每日数据，批次 119/342，共 100 条记录
2025-06-08 21:00:57,069 - INFO - 批量插入每日数据成功，批次 119，100 条记录
2025-06-08 21:01:00,069 - INFO - 正在批量插入每日数据，批次 120/342，共 100 条记录
2025-06-08 21:01:00,445 - INFO - 批量插入每日数据成功，批次 120，100 条记录
2025-06-08 21:01:03,460 - INFO - 正在批量插入每日数据，批次 121/342，共 100 条记录
2025-06-08 21:01:03,803 - INFO - 批量插入每日数据成功，批次 121，100 条记录
2025-06-08 21:01:06,818 - INFO - 正在批量插入每日数据，批次 122/342，共 100 条记录
2025-06-08 21:01:07,365 - INFO - 批量插入每日数据成功，批次 122，100 条记录
2025-06-08 21:01:10,380 - INFO - 正在批量插入每日数据，批次 123/342，共 100 条记录
2025-06-08 21:01:10,959 - INFO - 批量插入每日数据成功，批次 123，100 条记录
2025-06-08 21:01:13,974 - INFO - 正在批量插入每日数据，批次 124/342，共 100 条记录
2025-06-08 21:01:14,427 - INFO - 批量插入每日数据成功，批次 124，100 条记录
2025-06-08 21:01:17,441 - INFO - 正在批量插入每日数据，批次 125/342，共 100 条记录
2025-06-08 21:01:17,910 - INFO - 批量插入每日数据成功，批次 125，100 条记录
2025-06-08 21:01:20,925 - INFO - 正在批量插入每日数据，批次 126/342，共 100 条记录
2025-06-08 21:01:21,362 - INFO - 批量插入每日数据成功，批次 126，100 条记录
2025-06-08 21:01:24,374 - INFO - 正在批量插入每日数据，批次 127/342，共 100 条记录
2025-06-08 21:01:25,000 - INFO - 批量插入每日数据成功，批次 127，100 条记录
2025-06-08 21:01:28,000 - INFO - 正在批量插入每日数据，批次 128/342，共 100 条记录
2025-06-08 21:01:28,531 - INFO - 批量插入每日数据成功，批次 128，100 条记录
2025-06-08 21:01:31,546 - INFO - 正在批量插入每日数据，批次 129/342，共 100 条记录
2025-06-08 21:01:31,970 - INFO - 批量插入每日数据成功，批次 129，100 条记录
2025-06-08 21:01:34,985 - INFO - 正在批量插入每日数据，批次 130/342，共 100 条记录
2025-06-08 21:01:35,345 - INFO - 批量插入每日数据成功，批次 130，100 条记录
2025-06-08 21:01:38,357 - INFO - 正在批量插入每日数据，批次 131/342，共 100 条记录
2025-06-08 21:01:38,842 - INFO - 批量插入每日数据成功，批次 131，100 条记录
2025-06-08 21:01:41,857 - INFO - 正在批量插入每日数据，批次 132/342，共 100 条记录
2025-06-08 21:01:42,249 - INFO - 批量插入每日数据成功，批次 132，100 条记录
2025-06-08 21:01:45,250 - INFO - 正在批量插入每日数据，批次 133/342，共 100 条记录
2025-06-08 21:01:45,718 - INFO - 批量插入每日数据成功，批次 133，100 条记录
2025-06-08 21:01:48,731 - INFO - 正在批量插入每日数据，批次 134/342，共 100 条记录
2025-06-08 21:01:49,121 - INFO - 批量插入每日数据成功，批次 134，100 条记录
2025-06-08 21:01:52,123 - INFO - 正在批量插入每日数据，批次 135/342，共 100 条记录
2025-06-08 21:01:52,637 - INFO - 批量插入每日数据成功，批次 135，100 条记录
2025-06-08 21:01:55,641 - INFO - 正在批量插入每日数据，批次 136/342，共 100 条记录
2025-06-08 21:01:56,033 - INFO - 批量插入每日数据成功，批次 136，100 条记录
2025-06-08 21:01:59,043 - INFO - 正在批量插入每日数据，批次 137/342，共 100 条记录
2025-06-08 21:01:59,448 - INFO - 批量插入每日数据成功，批次 137，100 条记录
2025-06-08 21:02:02,458 - INFO - 正在批量插入每日数据，批次 138/342，共 100 条记录
2025-06-08 21:02:02,877 - INFO - 批量插入每日数据成功，批次 138，100 条记录
2025-06-08 21:02:05,884 - INFO - 正在批量插入每日数据，批次 139/342，共 100 条记录
2025-06-08 21:02:06,310 - INFO - 批量插入每日数据成功，批次 139，100 条记录
2025-06-08 21:02:09,317 - INFO - 正在批量插入每日数据，批次 140/342，共 100 条记录
2025-06-08 21:02:09,792 - INFO - 批量插入每日数据成功，批次 140，100 条记录
2025-06-08 21:02:12,802 - INFO - 正在批量插入每日数据，批次 141/342，共 100 条记录
2025-06-08 21:02:13,426 - INFO - 批量插入每日数据成功，批次 141，100 条记录
2025-06-08 21:02:16,429 - INFO - 正在批量插入每日数据，批次 142/342，共 100 条记录
2025-06-08 21:02:16,896 - INFO - 批量插入每日数据成功，批次 142，100 条记录
2025-06-08 21:02:19,903 - INFO - 正在批量插入每日数据，批次 143/342，共 100 条记录
2025-06-08 21:02:20,388 - INFO - 批量插入每日数据成功，批次 143，100 条记录
2025-06-08 21:02:23,402 - INFO - 正在批量插入每日数据，批次 144/342，共 100 条记录
2025-06-08 21:02:23,833 - INFO - 批量插入每日数据成功，批次 144，100 条记录
2025-06-08 21:02:26,841 - INFO - 正在批量插入每日数据，批次 145/342，共 100 条记录
2025-06-08 21:02:27,266 - INFO - 批量插入每日数据成功，批次 145，100 条记录
2025-06-08 21:02:30,269 - INFO - 正在批量插入每日数据，批次 146/342，共 100 条记录
2025-06-08 21:02:30,771 - INFO - 批量插入每日数据成功，批次 146，100 条记录
2025-06-08 21:02:33,774 - INFO - 正在批量插入每日数据，批次 147/342，共 100 条记录
2025-06-08 21:02:34,171 - INFO - 批量插入每日数据成功，批次 147，100 条记录
2025-06-08 21:02:37,179 - INFO - 正在批量插入每日数据，批次 148/342，共 100 条记录
2025-06-08 21:02:37,700 - INFO - 批量插入每日数据成功，批次 148，100 条记录
2025-06-08 21:02:40,712 - INFO - 正在批量插入每日数据，批次 149/342，共 100 条记录
2025-06-08 21:02:41,156 - INFO - 批量插入每日数据成功，批次 149，100 条记录
2025-06-08 21:02:44,159 - INFO - 正在批量插入每日数据，批次 150/342，共 100 条记录
2025-06-08 21:02:44,533 - INFO - 批量插入每日数据成功，批次 150，100 条记录
2025-06-08 21:02:47,535 - INFO - 正在批量插入每日数据，批次 151/342，共 100 条记录
2025-06-08 21:02:48,031 - INFO - 批量插入每日数据成功，批次 151，100 条记录
2025-06-08 21:02:51,033 - INFO - 正在批量插入每日数据，批次 152/342，共 100 条记录
2025-06-08 21:02:51,432 - INFO - 批量插入每日数据成功，批次 152，100 条记录
2025-06-08 21:02:54,434 - INFO - 正在批量插入每日数据，批次 153/342，共 100 条记录
2025-06-08 21:02:54,832 - INFO - 批量插入每日数据成功，批次 153，100 条记录
2025-06-08 21:02:57,837 - INFO - 正在批量插入每日数据，批次 154/342，共 100 条记录
2025-06-08 21:02:58,296 - INFO - 批量插入每日数据成功，批次 154，100 条记录
2025-06-08 21:03:01,298 - INFO - 正在批量插入每日数据，批次 155/342，共 100 条记录
2025-06-08 21:03:01,819 - INFO - 批量插入每日数据成功，批次 155，100 条记录
2025-06-08 21:03:04,821 - INFO - 正在批量插入每日数据，批次 156/342，共 100 条记录
2025-06-08 21:03:05,290 - INFO - 批量插入每日数据成功，批次 156，100 条记录
2025-06-08 21:03:08,292 - INFO - 正在批量插入每日数据，批次 157/342，共 100 条记录
2025-06-08 21:03:08,784 - INFO - 批量插入每日数据成功，批次 157，100 条记录
2025-06-08 21:03:11,787 - INFO - 正在批量插入每日数据，批次 158/342，共 100 条记录
2025-06-08 21:03:12,247 - INFO - 批量插入每日数据成功，批次 158，100 条记录
2025-06-08 21:03:15,248 - INFO - 正在批量插入每日数据，批次 159/342，共 100 条记录
2025-06-08 21:03:15,653 - INFO - 批量插入每日数据成功，批次 159，100 条记录
2025-06-08 21:03:18,655 - INFO - 正在批量插入每日数据，批次 160/342，共 100 条记录
2025-06-08 21:03:19,072 - INFO - 批量插入每日数据成功，批次 160，100 条记录
2025-06-08 21:03:22,086 - INFO - 正在批量插入每日数据，批次 161/342，共 100 条记录
2025-06-08 21:03:22,591 - INFO - 批量插入每日数据成功，批次 161，100 条记录
2025-06-08 21:03:25,592 - INFO - 正在批量插入每日数据，批次 162/342，共 100 条记录
2025-06-08 21:03:26,062 - INFO - 批量插入每日数据成功，批次 162，100 条记录
2025-06-08 21:03:29,064 - INFO - 正在批量插入每日数据，批次 163/342，共 100 条记录
2025-06-08 21:03:29,549 - INFO - 批量插入每日数据成功，批次 163，100 条记录
2025-06-08 21:03:32,552 - INFO - 正在批量插入每日数据，批次 164/342，共 100 条记录
2025-06-08 21:03:33,046 - INFO - 批量插入每日数据成功，批次 164，100 条记录
2025-06-08 21:03:36,048 - INFO - 正在批量插入每日数据，批次 165/342，共 100 条记录
2025-06-08 21:03:36,404 - INFO - 批量插入每日数据成功，批次 165，100 条记录
2025-06-08 21:03:39,406 - INFO - 正在批量插入每日数据，批次 166/342，共 100 条记录
2025-06-08 21:03:39,919 - INFO - 批量插入每日数据成功，批次 166，100 条记录
2025-06-08 21:03:42,921 - INFO - 正在批量插入每日数据，批次 167/342，共 100 条记录
2025-06-08 21:03:43,400 - INFO - 批量插入每日数据成功，批次 167，100 条记录
2025-06-08 21:03:46,402 - INFO - 正在批量插入每日数据，批次 168/342，共 100 条记录
2025-06-08 21:03:46,867 - INFO - 批量插入每日数据成功，批次 168，100 条记录
2025-06-08 21:03:49,870 - INFO - 正在批量插入每日数据，批次 169/342，共 100 条记录
2025-06-08 21:03:50,408 - INFO - 批量插入每日数据成功，批次 169，100 条记录
2025-06-08 21:03:53,423 - INFO - 正在批量插入每日数据，批次 170/342，共 100 条记录
2025-06-08 21:03:53,847 - INFO - 批量插入每日数据成功，批次 170，100 条记录
2025-06-08 21:03:56,849 - INFO - 正在批量插入每日数据，批次 171/342，共 100 条记录
2025-06-08 21:03:57,250 - INFO - 批量插入每日数据成功，批次 171，100 条记录
2025-06-08 21:04:00,253 - INFO - 正在批量插入每日数据，批次 172/342，共 100 条记录
2025-06-08 21:04:00,783 - INFO - 批量插入每日数据成功，批次 172，100 条记录
2025-06-08 21:04:03,785 - INFO - 正在批量插入每日数据，批次 173/342，共 100 条记录
2025-06-08 21:04:04,277 - INFO - 批量插入每日数据成功，批次 173，100 条记录
2025-06-08 21:04:07,282 - INFO - 正在批量插入每日数据，批次 174/342，共 100 条记录
2025-06-08 21:04:07,744 - INFO - 批量插入每日数据成功，批次 174，100 条记录
2025-06-08 21:04:10,744 - INFO - 正在批量插入每日数据，批次 175/342，共 100 条记录
2025-06-08 21:04:11,180 - INFO - 批量插入每日数据成功，批次 175，100 条记录
2025-06-08 21:04:14,180 - INFO - 正在批量插入每日数据，批次 176/342，共 100 条记录
2025-06-08 21:04:14,575 - INFO - 批量插入每日数据成功，批次 176，100 条记录
2025-06-08 21:04:17,578 - INFO - 正在批量插入每日数据，批次 177/342，共 100 条记录
2025-06-08 21:04:18,033 - INFO - 批量插入每日数据成功，批次 177，100 条记录
2025-06-08 21:04:21,035 - INFO - 正在批量插入每日数据，批次 178/342，共 100 条记录
2025-06-08 21:04:21,554 - INFO - 批量插入每日数据成功，批次 178，100 条记录
2025-06-08 21:04:24,568 - INFO - 正在批量插入每日数据，批次 179/342，共 100 条记录
2025-06-08 21:04:25,010 - INFO - 批量插入每日数据成功，批次 179，100 条记录
2025-06-08 21:04:28,023 - INFO - 正在批量插入每日数据，批次 180/342，共 100 条记录
2025-06-08 21:04:28,461 - INFO - 批量插入每日数据成功，批次 180，100 条记录
2025-06-08 21:04:31,467 - INFO - 正在批量插入每日数据，批次 181/342，共 100 条记录
2025-06-08 21:04:31,898 - INFO - 批量插入每日数据成功，批次 181，100 条记录
2025-06-08 21:04:34,900 - INFO - 正在批量插入每日数据，批次 182/342，共 100 条记录
2025-06-08 21:04:35,275 - INFO - 批量插入每日数据成功，批次 182，100 条记录
2025-06-08 21:04:38,280 - INFO - 正在批量插入每日数据，批次 183/342，共 100 条记录
2025-06-08 21:04:38,749 - INFO - 批量插入每日数据成功，批次 183，100 条记录
2025-06-08 21:04:41,751 - INFO - 正在批量插入每日数据，批次 184/342，共 100 条记录
2025-06-08 21:04:42,119 - INFO - 批量插入每日数据成功，批次 184，100 条记录
2025-06-08 21:04:45,122 - INFO - 正在批量插入每日数据，批次 185/342，共 100 条记录
2025-06-08 21:04:45,631 - INFO - 批量插入每日数据成功，批次 185，100 条记录
2025-06-08 21:04:48,633 - INFO - 正在批量插入每日数据，批次 186/342，共 100 条记录
2025-06-08 21:04:49,170 - INFO - 批量插入每日数据成功，批次 186，100 条记录
2025-06-08 21:04:52,173 - INFO - 正在批量插入每日数据，批次 187/342，共 100 条记录
2025-06-08 21:04:52,596 - INFO - 批量插入每日数据成功，批次 187，100 条记录
2025-06-08 21:04:55,599 - INFO - 正在批量插入每日数据，批次 188/342，共 100 条记录
2025-06-08 21:04:55,979 - INFO - 批量插入每日数据成功，批次 188，100 条记录
2025-06-08 21:04:58,981 - INFO - 正在批量插入每日数据，批次 189/342，共 100 条记录
2025-06-08 21:04:59,515 - INFO - 批量插入每日数据成功，批次 189，100 条记录
2025-06-08 21:05:02,517 - INFO - 正在批量插入每日数据，批次 190/342，共 100 条记录
2025-06-08 21:05:02,919 - INFO - 批量插入每日数据成功，批次 190，100 条记录
2025-06-08 21:05:05,922 - INFO - 正在批量插入每日数据，批次 191/342，共 100 条记录
2025-06-08 21:05:06,465 - INFO - 批量插入每日数据成功，批次 191，100 条记录
2025-06-08 21:05:09,467 - INFO - 正在批量插入每日数据，批次 192/342，共 100 条记录
2025-06-08 21:05:09,857 - INFO - 批量插入每日数据成功，批次 192，100 条记录
2025-06-08 21:05:12,871 - INFO - 正在批量插入每日数据，批次 193/342，共 100 条记录
2025-06-08 21:05:13,293 - INFO - 批量插入每日数据成功，批次 193，100 条记录
2025-06-08 21:05:16,309 - INFO - 正在批量插入每日数据，批次 194/342，共 100 条记录
2025-06-08 21:05:16,871 - INFO - 批量插入每日数据成功，批次 194，100 条记录
2025-06-08 21:05:19,884 - INFO - 正在批量插入每日数据，批次 195/342，共 100 条记录
2025-06-08 21:05:20,322 - INFO - 批量插入每日数据成功，批次 195，100 条记录
2025-06-08 21:05:23,337 - INFO - 正在批量插入每日数据，批次 196/342，共 100 条记录
2025-06-08 21:05:23,758 - INFO - 批量插入每日数据成功，批次 196，100 条记录
2025-06-08 21:05:26,771 - INFO - 正在批量插入每日数据，批次 197/342，共 100 条记录
2025-06-08 21:05:27,161 - INFO - 批量插入每日数据成功，批次 197，100 条记录
2025-06-08 21:05:30,161 - INFO - 正在批量插入每日数据，批次 198/342，共 100 条记录
2025-06-08 21:05:30,646 - INFO - 批量插入每日数据成功，批次 198，100 条记录
2025-06-08 21:05:33,647 - INFO - 正在批量插入每日数据，批次 199/342，共 100 条记录
2025-06-08 21:05:34,069 - INFO - 批量插入每日数据成功，批次 199，100 条记录
2025-06-08 21:05:37,071 - INFO - 正在批量插入每日数据，批次 200/342，共 100 条记录
2025-06-08 21:05:37,433 - INFO - 批量插入每日数据成功，批次 200，100 条记录
2025-06-08 21:05:40,446 - INFO - 正在批量插入每日数据，批次 201/342，共 100 条记录
2025-06-08 21:05:40,928 - INFO - 批量插入每日数据成功，批次 201，100 条记录
2025-06-08 21:05:43,930 - INFO - 正在批量插入每日数据，批次 202/342，共 100 条记录
2025-06-08 21:05:44,358 - INFO - 批量插入每日数据成功，批次 202，100 条记录
2025-06-08 21:05:47,368 - INFO - 正在批量插入每日数据，批次 203/342，共 100 条记录
2025-06-08 21:05:47,791 - INFO - 批量插入每日数据成功，批次 203，100 条记录
2025-06-08 21:05:50,793 - INFO - 正在批量插入每日数据，批次 204/342，共 100 条记录
2025-06-08 21:05:51,239 - INFO - 批量插入每日数据成功，批次 204，100 条记录
2025-06-08 21:05:54,256 - INFO - 正在批量插入每日数据，批次 205/342，共 100 条记录
2025-06-08 21:05:54,635 - INFO - 批量插入每日数据成功，批次 205，100 条记录
2025-06-08 21:05:57,641 - INFO - 正在批量插入每日数据，批次 206/342，共 100 条记录
2025-06-08 21:05:58,110 - INFO - 批量插入每日数据成功，批次 206，100 条记录
2025-06-08 21:06:01,113 - INFO - 正在批量插入每日数据，批次 207/342，共 100 条记录
2025-06-08 21:06:01,628 - INFO - 批量插入每日数据成功，批次 207，100 条记录
2025-06-08 21:06:04,631 - INFO - 正在批量插入每日数据，批次 208/342，共 100 条记录
2025-06-08 21:06:05,146 - INFO - 批量插入每日数据成功，批次 208，100 条记录
2025-06-08 21:06:08,148 - INFO - 正在批量插入每日数据，批次 209/342，共 100 条记录
2025-06-08 21:06:08,629 - INFO - 批量插入每日数据成功，批次 209，100 条记录
2025-06-08 21:06:11,633 - INFO - 正在批量插入每日数据，批次 210/342，共 100 条记录
2025-06-08 21:06:12,102 - INFO - 批量插入每日数据成功，批次 210，100 条记录
2025-06-08 21:06:15,109 - INFO - 正在批量插入每日数据，批次 211/342，共 100 条记录
2025-06-08 21:06:15,513 - INFO - 批量插入每日数据成功，批次 211，100 条记录
2025-06-08 21:06:18,515 - INFO - 正在批量插入每日数据，批次 212/342，共 100 条记录
2025-06-08 21:06:19,086 - INFO - 批量插入每日数据成功，批次 212，100 条记录
2025-06-08 21:06:22,088 - INFO - 正在批量插入每日数据，批次 213/342，共 100 条记录
2025-06-08 21:06:22,607 - INFO - 批量插入每日数据成功，批次 213，100 条记录
2025-06-08 21:06:25,609 - INFO - 正在批量插入每日数据，批次 214/342，共 100 条记录
2025-06-08 21:06:26,164 - INFO - 批量插入每日数据成功，批次 214，100 条记录
2025-06-08 21:06:29,166 - INFO - 正在批量插入每日数据，批次 215/342，共 100 条记录
2025-06-08 21:06:29,646 - INFO - 批量插入每日数据成功，批次 215，100 条记录
2025-06-08 21:06:32,658 - INFO - 正在批量插入每日数据，批次 216/342，共 100 条记录
2025-06-08 21:06:33,171 - INFO - 批量插入每日数据成功，批次 216，100 条记录
2025-06-08 21:06:36,173 - INFO - 正在批量插入每日数据，批次 217/342，共 100 条记录
2025-06-08 21:06:36,607 - INFO - 批量插入每日数据成功，批次 217，100 条记录
2025-06-08 21:06:39,609 - INFO - 正在批量插入每日数据，批次 218/342，共 100 条记录
2025-06-08 21:06:40,093 - INFO - 批量插入每日数据成功，批次 218，100 条记录
2025-06-08 21:06:43,101 - INFO - 正在批量插入每日数据，批次 219/342，共 100 条记录
2025-06-08 21:06:43,616 - INFO - 批量插入每日数据成功，批次 219，100 条记录
2025-06-08 21:06:46,631 - INFO - 正在批量插入每日数据，批次 220/342，共 100 条记录
2025-06-08 21:06:47,036 - INFO - 批量插入每日数据成功，批次 220，100 条记录
2025-06-08 21:06:50,046 - INFO - 正在批量插入每日数据，批次 221/342，共 100 条记录
2025-06-08 21:06:50,483 - INFO - 批量插入每日数据成功，批次 221，100 条记录
2025-06-08 21:06:53,490 - INFO - 正在批量插入每日数据，批次 222/342，共 100 条记录
2025-06-08 21:06:53,921 - INFO - 批量插入每日数据成功，批次 222，100 条记录
2025-06-08 21:06:56,927 - INFO - 正在批量插入每日数据，批次 223/342，共 100 条记录
2025-06-08 21:06:57,350 - INFO - 批量插入每日数据成功，批次 223，100 条记录
2025-06-08 21:07:00,363 - INFO - 正在批量插入每日数据，批次 224/342，共 100 条记录
2025-06-08 21:07:00,879 - INFO - 批量插入每日数据成功，批次 224，100 条记录
2025-06-08 21:07:03,883 - INFO - 正在批量插入每日数据，批次 225/342，共 100 条记录
2025-06-08 21:07:04,235 - INFO - 批量插入每日数据成功，批次 225，100 条记录
2025-06-08 21:07:07,237 - INFO - 正在批量插入每日数据，批次 226/342，共 100 条记录
2025-06-08 21:07:07,615 - INFO - 批量插入每日数据成功，批次 226，100 条记录
2025-06-08 21:07:10,626 - INFO - 正在批量插入每日数据，批次 227/342，共 100 条记录
2025-06-08 21:07:11,052 - INFO - 批量插入每日数据成功，批次 227，100 条记录
2025-06-08 21:07:14,064 - INFO - 正在批量插入每日数据，批次 228/342，共 100 条记录
2025-06-08 21:07:14,479 - INFO - 批量插入每日数据成功，批次 228，100 条记录
2025-06-08 21:07:17,482 - INFO - 正在批量插入每日数据，批次 229/342，共 100 条记录
2025-06-08 21:07:17,868 - INFO - 批量插入每日数据成功，批次 229，100 条记录
2025-06-08 21:07:20,871 - INFO - 正在批量插入每日数据，批次 230/342，共 100 条记录
2025-06-08 21:07:21,342 - INFO - 批量插入每日数据成功，批次 230，100 条记录
2025-06-08 21:07:24,345 - INFO - 正在批量插入每日数据，批次 231/342，共 100 条记录
2025-06-08 21:07:24,766 - INFO - 批量插入每日数据成功，批次 231，100 条记录
2025-06-08 21:07:27,777 - INFO - 正在批量插入每日数据，批次 232/342，共 100 条记录
2025-06-08 21:07:28,175 - INFO - 批量插入每日数据成功，批次 232，100 条记录
2025-06-08 21:07:31,176 - INFO - 正在批量插入每日数据，批次 233/342，共 100 条记录
2025-06-08 21:07:31,582 - INFO - 批量插入每日数据成功，批次 233，100 条记录
2025-06-08 21:07:34,594 - INFO - 正在批量插入每日数据，批次 234/342，共 100 条记录
2025-06-08 21:07:34,954 - INFO - 批量插入每日数据成功，批次 234，100 条记录
2025-06-08 21:07:37,967 - INFO - 正在批量插入每日数据，批次 235/342，共 100 条记录
2025-06-08 21:07:38,390 - INFO - 批量插入每日数据成功，批次 235，100 条记录
2025-06-08 21:07:41,405 - INFO - 正在批量插入每日数据，批次 236/342，共 100 条记录
2025-06-08 21:07:41,843 - INFO - 批量插入每日数据成功，批次 236，100 条记录
2025-06-08 21:07:44,854 - INFO - 正在批量插入每日数据，批次 237/342，共 100 条记录
2025-06-08 21:07:45,229 - INFO - 批量插入每日数据成功，批次 237，100 条记录
2025-06-08 21:07:48,244 - INFO - 正在批量插入每日数据，批次 238/342，共 100 条记录
2025-06-08 21:07:48,698 - INFO - 批量插入每日数据成功，批次 238，100 条记录
2025-06-08 21:07:51,700 - INFO - 正在批量插入每日数据，批次 239/342，共 100 条记录
2025-06-08 21:07:52,141 - INFO - 批量插入每日数据成功，批次 239，100 条记录
2025-06-08 21:07:55,144 - INFO - 正在批量插入每日数据，批次 240/342，共 100 条记录
2025-06-08 21:07:55,683 - INFO - 批量插入每日数据成功，批次 240，100 条记录
2025-06-08 21:07:58,693 - INFO - 正在批量插入每日数据，批次 241/342，共 100 条记录
2025-06-08 21:07:59,115 - INFO - 批量插入每日数据成功，批次 241，100 条记录
2025-06-08 21:08:02,117 - INFO - 正在批量插入每日数据，批次 242/342，共 100 条记录
2025-06-08 21:08:02,602 - INFO - 批量插入每日数据成功，批次 242，100 条记录
2025-06-08 21:08:05,604 - INFO - 正在批量插入每日数据，批次 243/342，共 100 条记录
2025-06-08 21:08:06,119 - INFO - 批量插入每日数据成功，批次 243，100 条记录
2025-06-08 21:08:09,124 - INFO - 正在批量插入每日数据，批次 244/342，共 100 条记录
2025-06-08 21:08:09,643 - INFO - 批量插入每日数据成功，批次 244，100 条记录
2025-06-08 21:08:12,642 - INFO - 正在批量插入每日数据，批次 245/342，共 100 条记录
2025-06-08 21:08:13,077 - INFO - 批量插入每日数据成功，批次 245，100 条记录
2025-06-08 21:08:16,092 - INFO - 正在批量插入每日数据，批次 246/342，共 100 条记录
2025-06-08 21:08:16,481 - INFO - 批量插入每日数据成功，批次 246，100 条记录
2025-06-08 21:08:19,492 - INFO - 正在批量插入每日数据，批次 247/342，共 100 条记录
2025-06-08 21:08:19,836 - INFO - 批量插入每日数据成功，批次 247，100 条记录
2025-06-08 21:08:22,850 - INFO - 正在批量插入每日数据，批次 248/342，共 100 条记录
2025-06-08 21:08:23,366 - INFO - 批量插入每日数据成功，批次 248，100 条记录
2025-06-08 21:08:26,366 - INFO - 正在批量插入每日数据，批次 249/342，共 100 条记录
2025-06-08 21:08:26,725 - INFO - 批量插入每日数据成功，批次 249，100 条记录
2025-06-08 21:08:29,725 - INFO - 正在批量插入每日数据，批次 250/342，共 100 条记录
2025-06-08 21:08:30,100 - INFO - 批量插入每日数据成功，批次 250，100 条记录
2025-06-08 21:08:33,100 - INFO - 正在批量插入每日数据，批次 251/342，共 100 条记录
2025-06-08 21:08:33,491 - INFO - 批量插入每日数据成功，批次 251，100 条记录
2025-06-08 21:08:36,493 - INFO - 正在批量插入每日数据，批次 252/342，共 100 条记录
2025-06-08 21:08:36,964 - INFO - 批量插入每日数据成功，批次 252，100 条记录
2025-06-08 21:08:39,966 - INFO - 正在批量插入每日数据，批次 253/342，共 100 条记录
2025-06-08 21:08:40,433 - INFO - 批量插入每日数据成功，批次 253，100 条记录
2025-06-08 21:08:43,436 - INFO - 正在批量插入每日数据，批次 254/342，共 100 条记录
2025-06-08 21:08:43,802 - INFO - 批量插入每日数据成功，批次 254，100 条记录
2025-06-08 21:08:46,804 - INFO - 正在批量插入每日数据，批次 255/342，共 100 条记录
2025-06-08 21:08:47,252 - INFO - 批量插入每日数据成功，批次 255，100 条记录
2025-06-08 21:08:50,253 - INFO - 正在批量插入每日数据，批次 256/342，共 100 条记录
2025-06-08 21:08:50,732 - INFO - 批量插入每日数据成功，批次 256，100 条记录
2025-06-08 21:08:53,736 - INFO - 正在批量插入每日数据，批次 257/342，共 100 条记录
2025-06-08 21:08:54,187 - INFO - 批量插入每日数据成功，批次 257，100 条记录
2025-06-08 21:08:57,189 - INFO - 正在批量插入每日数据，批次 258/342，共 100 条记录
2025-06-08 21:08:57,597 - INFO - 批量插入每日数据成功，批次 258，100 条记录
2025-06-08 21:09:00,608 - INFO - 正在批量插入每日数据，批次 259/342，共 100 条记录
2025-06-08 21:09:01,158 - INFO - 批量插入每日数据成功，批次 259，100 条记录
2025-06-08 21:09:04,160 - INFO - 正在批量插入每日数据，批次 260/342，共 100 条记录
2025-06-08 21:09:04,556 - INFO - 批量插入每日数据成功，批次 260，100 条记录
2025-06-08 21:09:07,565 - INFO - 正在批量插入每日数据，批次 261/342，共 100 条记录
2025-06-08 21:09:08,016 - INFO - 批量插入每日数据成功，批次 261，100 条记录
2025-06-08 21:09:11,028 - INFO - 正在批量插入每日数据，批次 262/342，共 100 条记录
2025-06-08 21:09:11,462 - INFO - 批量插入每日数据成功，批次 262，100 条记录
2025-06-08 21:09:14,480 - INFO - 正在批量插入每日数据，批次 263/342，共 100 条记录
2025-06-08 21:09:14,898 - INFO - 批量插入每日数据成功，批次 263，100 条记录
2025-06-08 21:09:17,900 - INFO - 正在批量插入每日数据，批次 264/342，共 100 条记录
2025-06-08 21:09:18,473 - INFO - 批量插入每日数据成功，批次 264，100 条记录
2025-06-08 21:09:21,475 - INFO - 正在批量插入每日数据，批次 265/342，共 100 条记录
2025-06-08 21:09:22,083 - INFO - 批量插入每日数据成功，批次 265，100 条记录
2025-06-08 21:09:25,095 - INFO - 正在批量插入每日数据，批次 266/342，共 100 条记录
2025-06-08 21:09:25,483 - INFO - 批量插入每日数据成功，批次 266，100 条记录
2025-06-08 21:09:28,485 - INFO - 正在批量插入每日数据，批次 267/342，共 100 条记录
2025-06-08 21:09:29,090 - INFO - 批量插入每日数据成功，批次 267，100 条记录
2025-06-08 21:09:32,092 - INFO - 正在批量插入每日数据，批次 268/342，共 100 条记录
2025-06-08 21:09:32,575 - INFO - 批量插入每日数据成功，批次 268，100 条记录
2025-06-08 21:09:35,578 - INFO - 正在批量插入每日数据，批次 269/342，共 100 条记录
2025-06-08 21:09:36,142 - INFO - 批量插入每日数据成功，批次 269，100 条记录
2025-06-08 21:09:39,145 - INFO - 正在批量插入每日数据，批次 270/342，共 100 条记录
2025-06-08 21:09:39,655 - INFO - 批量插入每日数据成功，批次 270，100 条记录
2025-06-08 21:09:42,655 - INFO - 正在批量插入每日数据，批次 271/342，共 100 条记录
2025-06-08 21:09:43,104 - INFO - 批量插入每日数据成功，批次 271，100 条记录
2025-06-08 21:09:46,115 - INFO - 正在批量插入每日数据，批次 272/342，共 100 条记录
2025-06-08 21:09:46,703 - INFO - 批量插入每日数据成功，批次 272，100 条记录
2025-06-08 21:09:49,703 - INFO - 正在批量插入每日数据，批次 273/342，共 100 条记录
2025-06-08 21:09:50,108 - INFO - 批量插入每日数据成功，批次 273，100 条记录
2025-06-08 21:09:53,114 - INFO - 正在批量插入每日数据，批次 274/342，共 100 条记录
2025-06-08 21:09:53,532 - INFO - 批量插入每日数据成功，批次 274，100 条记录
2025-06-08 21:09:56,534 - INFO - 正在批量插入每日数据，批次 275/342，共 100 条记录
2025-06-08 21:09:57,003 - INFO - 批量插入每日数据成功，批次 275，100 条记录
2025-06-08 21:10:00,005 - INFO - 正在批量插入每日数据，批次 276/342，共 100 条记录
2025-06-08 21:10:00,449 - INFO - 批量插入每日数据成功，批次 276，100 条记录
2025-06-08 21:10:03,453 - INFO - 正在批量插入每日数据，批次 277/342，共 100 条记录
2025-06-08 21:10:03,893 - INFO - 批量插入每日数据成功，批次 277，100 条记录
2025-06-08 21:10:06,898 - INFO - 正在批量插入每日数据，批次 278/342，共 100 条记录
2025-06-08 21:10:07,393 - INFO - 批量插入每日数据成功，批次 278，100 条记录
2025-06-08 21:10:10,394 - INFO - 正在批量插入每日数据，批次 279/342，共 100 条记录
2025-06-08 21:10:10,988 - INFO - 批量插入每日数据成功，批次 279，100 条记录
2025-06-08 21:10:13,991 - INFO - 正在批量插入每日数据，批次 280/342，共 100 条记录
2025-06-08 21:10:14,374 - INFO - 批量插入每日数据成功，批次 280，100 条记录
2025-06-08 21:10:17,386 - INFO - 正在批量插入每日数据，批次 281/342，共 100 条记录
2025-06-08 21:10:17,791 - INFO - 批量插入每日数据成功，批次 281，100 条记录
2025-06-08 21:10:20,793 - INFO - 正在批量插入每日数据，批次 282/342，共 100 条记录
2025-06-08 21:10:21,134 - INFO - 批量插入每日数据成功，批次 282，100 条记录
2025-06-08 21:10:24,137 - INFO - 正在批量插入每日数据，批次 283/342，共 100 条记录
2025-06-08 21:10:24,565 - INFO - 批量插入每日数据成功，批次 283，100 条记录
2025-06-08 21:10:27,567 - INFO - 正在批量插入每日数据，批次 284/342，共 100 条记录
2025-06-08 21:10:27,952 - INFO - 批量插入每日数据成功，批次 284，100 条记录
2025-06-08 21:10:30,954 - INFO - 正在批量插入每日数据，批次 285/342，共 100 条记录
2025-06-08 21:10:31,392 - INFO - 批量插入每日数据成功，批次 285，100 条记录
2025-06-08 21:10:34,395 - INFO - 正在批量插入每日数据，批次 286/342，共 100 条记录
2025-06-08 21:10:34,791 - INFO - 批量插入每日数据成功，批次 286，100 条记录
2025-06-08 21:10:37,793 - INFO - 正在批量插入每日数据，批次 287/342，共 100 条记录
2025-06-08 21:10:38,307 - INFO - 批量插入每日数据成功，批次 287，100 条记录
2025-06-08 21:10:41,310 - INFO - 正在批量插入每日数据，批次 288/342，共 100 条记录
2025-06-08 21:10:41,821 - INFO - 批量插入每日数据成功，批次 288，100 条记录
2025-06-08 21:10:44,824 - INFO - 正在批量插入每日数据，批次 289/342，共 100 条记录
2025-06-08 21:10:45,298 - INFO - 批量插入每日数据成功，批次 289，100 条记录
2025-06-08 21:10:48,301 - INFO - 正在批量插入每日数据，批次 290/342，共 100 条记录
2025-06-08 21:10:48,879 - INFO - 批量插入每日数据成功，批次 290，100 条记录
2025-06-08 21:10:51,881 - INFO - 正在批量插入每日数据，批次 291/342，共 100 条记录
2025-06-08 21:10:52,379 - INFO - 批量插入每日数据成功，批次 291，100 条记录
2025-06-08 21:10:55,382 - INFO - 正在批量插入每日数据，批次 292/342，共 100 条记录
2025-06-08 21:10:55,936 - INFO - 批量插入每日数据成功，批次 292，100 条记录
2025-06-08 21:10:58,939 - INFO - 正在批量插入每日数据，批次 293/342，共 100 条记录
2025-06-08 21:10:59,363 - INFO - 批量插入每日数据成功，批次 293，100 条记录
2025-06-08 21:11:02,365 - INFO - 正在批量插入每日数据，批次 294/342，共 100 条记录
2025-06-08 21:11:02,823 - INFO - 批量插入每日数据成功，批次 294，100 条记录
2025-06-08 21:11:05,825 - INFO - 正在批量插入每日数据，批次 295/342，共 100 条记录
2025-06-08 21:11:06,341 - INFO - 批量插入每日数据成功，批次 295，100 条记录
2025-06-08 21:11:09,343 - INFO - 正在批量插入每日数据，批次 296/342，共 100 条记录
2025-06-08 21:11:09,805 - INFO - 批量插入每日数据成功，批次 296，100 条记录
2025-06-08 21:11:12,808 - INFO - 正在批量插入每日数据，批次 297/342，共 100 条记录
2025-06-08 21:11:13,233 - INFO - 批量插入每日数据成功，批次 297，100 条记录
2025-06-08 21:11:16,236 - INFO - 正在批量插入每日数据，批次 298/342，共 100 条记录
2025-06-08 21:11:16,679 - INFO - 批量插入每日数据成功，批次 298，100 条记录
2025-06-08 21:11:19,683 - INFO - 正在批量插入每日数据，批次 299/342，共 100 条记录
2025-06-08 21:11:20,357 - INFO - 批量插入每日数据成功，批次 299，100 条记录
2025-06-08 21:11:23,360 - INFO - 正在批量插入每日数据，批次 300/342，共 100 条记录
2025-06-08 21:11:23,836 - INFO - 批量插入每日数据成功，批次 300，100 条记录
2025-06-08 21:11:26,838 - INFO - 正在批量插入每日数据，批次 301/342，共 100 条记录
2025-06-08 21:11:27,250 - INFO - 批量插入每日数据成功，批次 301，100 条记录
2025-06-08 21:11:30,252 - INFO - 正在批量插入每日数据，批次 302/342，共 100 条记录
2025-06-08 21:11:30,730 - INFO - 批量插入每日数据成功，批次 302，100 条记录
2025-06-08 21:11:33,733 - INFO - 正在批量插入每日数据，批次 303/342，共 100 条记录
2025-06-08 21:11:34,236 - INFO - 批量插入每日数据成功，批次 303，100 条记录
2025-06-08 21:11:37,238 - INFO - 正在批量插入每日数据，批次 304/342，共 100 条记录
2025-06-08 21:11:37,617 - INFO - 批量插入每日数据成功，批次 304，100 条记录
2025-06-08 21:11:40,618 - INFO - 正在批量插入每日数据，批次 305/342，共 100 条记录
2025-06-08 21:11:41,150 - INFO - 批量插入每日数据成功，批次 305，100 条记录
2025-06-08 21:11:44,152 - INFO - 正在批量插入每日数据，批次 306/342，共 100 条记录
2025-06-08 21:11:44,592 - INFO - 批量插入每日数据成功，批次 306，100 条记录
2025-06-08 21:11:47,595 - INFO - 正在批量插入每日数据，批次 307/342，共 100 条记录
2025-06-08 21:11:48,031 - INFO - 批量插入每日数据成功，批次 307，100 条记录
2025-06-08 21:11:51,033 - INFO - 正在批量插入每日数据，批次 308/342，共 100 条记录
2025-06-08 21:11:51,453 - INFO - 批量插入每日数据成功，批次 308，100 条记录
2025-06-08 21:11:54,455 - INFO - 正在批量插入每日数据，批次 309/342，共 100 条记录
2025-06-08 21:11:54,893 - INFO - 批量插入每日数据成功，批次 309，100 条记录
2025-06-08 21:11:57,896 - INFO - 正在批量插入每日数据，批次 310/342，共 100 条记录
2025-06-08 21:11:58,364 - INFO - 批量插入每日数据成功，批次 310，100 条记录
2025-06-08 21:12:01,367 - INFO - 正在批量插入每日数据，批次 311/342，共 100 条记录
2025-06-08 21:12:01,760 - INFO - 批量插入每日数据成功，批次 311，100 条记录
2025-06-08 21:12:04,762 - INFO - 正在批量插入每日数据，批次 312/342，共 100 条记录
2025-06-08 21:12:05,340 - INFO - 批量插入每日数据成功，批次 312，100 条记录
2025-06-08 21:12:08,343 - INFO - 正在批量插入每日数据，批次 313/342，共 100 条记录
2025-06-08 21:12:08,760 - INFO - 批量插入每日数据成功，批次 313，100 条记录
2025-06-08 21:12:11,762 - INFO - 正在批量插入每日数据，批次 314/342，共 100 条记录
2025-06-08 21:12:12,255 - INFO - 批量插入每日数据成功，批次 314，100 条记录
2025-06-08 21:12:15,257 - INFO - 正在批量插入每日数据，批次 315/342，共 100 条记录
2025-06-08 21:12:15,783 - INFO - 批量插入每日数据成功，批次 315，100 条记录
2025-06-08 21:12:18,786 - INFO - 正在批量插入每日数据，批次 316/342，共 100 条记录
2025-06-08 21:12:19,170 - INFO - 批量插入每日数据成功，批次 316，100 条记录
2025-06-08 21:12:22,172 - INFO - 正在批量插入每日数据，批次 317/342，共 100 条记录
2025-06-08 21:12:22,634 - INFO - 批量插入每日数据成功，批次 317，100 条记录
2025-06-08 21:12:25,636 - INFO - 正在批量插入每日数据，批次 318/342，共 100 条记录
2025-06-08 21:12:26,271 - INFO - 批量插入每日数据成功，批次 318，100 条记录
2025-06-08 21:12:29,279 - INFO - 正在批量插入每日数据，批次 319/342，共 100 条记录
2025-06-08 21:12:29,714 - INFO - 批量插入每日数据成功，批次 319，100 条记录
2025-06-08 21:12:32,726 - INFO - 正在批量插入每日数据，批次 320/342，共 100 条记录
2025-06-08 21:12:33,161 - INFO - 批量插入每日数据成功，批次 320，100 条记录
2025-06-08 21:12:36,174 - INFO - 正在批量插入每日数据，批次 321/342，共 100 条记录
2025-06-08 21:12:36,672 - INFO - 批量插入每日数据成功，批次 321，100 条记录
2025-06-08 21:12:39,686 - INFO - 正在批量插入每日数据，批次 322/342，共 100 条记录
2025-06-08 21:12:40,232 - INFO - 批量插入每日数据成功，批次 322，100 条记录
2025-06-08 21:12:43,246 - INFO - 正在批量插入每日数据，批次 323/342，共 100 条记录
2025-06-08 21:12:43,745 - INFO - 批量插入每日数据成功，批次 323，100 条记录
2025-06-08 21:12:46,758 - INFO - 正在批量插入每日数据，批次 324/342，共 100 条记录
2025-06-08 21:12:47,118 - INFO - 批量插入每日数据成功，批次 324，100 条记录
2025-06-08 21:12:50,121 - INFO - 正在批量插入每日数据，批次 325/342，共 100 条记录
2025-06-08 21:12:50,662 - INFO - 批量插入每日数据成功，批次 325，100 条记录
2025-06-08 21:12:53,672 - INFO - 正在批量插入每日数据，批次 326/342，共 100 条记录
2025-06-08 21:12:54,097 - INFO - 批量插入每日数据成功，批次 326，100 条记录
2025-06-08 21:12:57,103 - INFO - 正在批量插入每日数据，批次 327/342，共 100 条记录
2025-06-08 21:12:57,565 - INFO - 批量插入每日数据成功，批次 327，100 条记录
2025-06-08 21:13:00,578 - INFO - 正在批量插入每日数据，批次 328/342，共 100 条记录
2025-06-08 21:13:00,989 - INFO - 批量插入每日数据成功，批次 328，100 条记录
2025-06-08 21:13:03,988 - INFO - 正在批量插入每日数据，批次 329/342，共 100 条记录
2025-06-08 21:13:04,502 - INFO - 批量插入每日数据成功，批次 329，100 条记录
2025-06-08 21:13:07,504 - INFO - 正在批量插入每日数据，批次 330/342，共 100 条记录
2025-06-08 21:13:07,981 - INFO - 批量插入每日数据成功，批次 330，100 条记录
2025-06-08 21:13:10,984 - INFO - 正在批量插入每日数据，批次 331/342，共 100 条记录
2025-06-08 21:13:11,407 - INFO - 批量插入每日数据成功，批次 331，100 条记录
2025-06-08 21:13:14,416 - INFO - 正在批量插入每日数据，批次 332/342，共 100 条记录
2025-06-08 21:13:14,871 - INFO - 批量插入每日数据成功，批次 332，100 条记录
2025-06-08 21:13:17,874 - INFO - 正在批量插入每日数据，批次 333/342，共 100 条记录
2025-06-08 21:13:18,296 - INFO - 批量插入每日数据成功，批次 333，100 条记录
2025-06-08 21:13:21,298 - INFO - 正在批量插入每日数据，批次 334/342，共 100 条记录
2025-06-08 21:13:21,890 - INFO - 批量插入每日数据成功，批次 334，100 条记录
2025-06-08 21:13:24,892 - INFO - 正在批量插入每日数据，批次 335/342，共 100 条记录
2025-06-08 21:13:25,350 - INFO - 批量插入每日数据成功，批次 335，100 条记录
2025-06-08 21:13:28,352 - INFO - 正在批量插入每日数据，批次 336/342，共 100 条记录
2025-06-08 21:13:28,731 - INFO - 批量插入每日数据成功，批次 336，100 条记录
2025-06-08 21:13:31,733 - INFO - 正在批量插入每日数据，批次 337/342，共 100 条记录
2025-06-08 21:13:32,098 - INFO - 批量插入每日数据成功，批次 337，100 条记录
2025-06-08 21:13:35,100 - INFO - 正在批量插入每日数据，批次 338/342，共 100 条记录
2025-06-08 21:13:35,612 - INFO - 批量插入每日数据成功，批次 338，100 条记录
2025-06-08 21:13:38,612 - INFO - 正在批量插入每日数据，批次 339/342，共 100 条记录
2025-06-08 21:13:39,029 - INFO - 批量插入每日数据成功，批次 339，100 条记录
2025-06-08 21:13:42,031 - INFO - 正在批量插入每日数据，批次 340/342，共 100 条记录
2025-06-08 21:13:42,472 - INFO - 批量插入每日数据成功，批次 340，100 条记录
2025-06-08 21:13:45,474 - INFO - 正在批量插入每日数据，批次 341/342，共 100 条记录
2025-06-08 21:13:45,893 - INFO - 批量插入每日数据成功，批次 341，100 条记录
2025-06-08 21:13:48,895 - INFO - 正在批量插入每日数据，批次 342/342，共 12 条记录
2025-06-08 21:13:49,119 - INFO - 批量插入每日数据成功，批次 342，12 条记录
2025-06-08 21:13:52,123 - INFO - 批量插入每日数据完成: 总计 34112 条，成功 34112 条，失败 0 条
2025-06-08 21:13:52,170 - INFO - 批量插入日销售数据完成，共 34112 条记录
2025-06-08 21:13:52,170 - INFO - 日销售数据同步完成！更新: 0 条，插入: 34112 条，错误: 0 条，跳过: 0 条
2025-06-08 21:13:52,170 - INFO - 正在获取宜搭月销售表单数据...
2025-06-08 21:13:52,171 - INFO - 开始获取宜搭月度表单数据，总时间范围: 2025-01-01 至 2025-06-30
2025-06-08 21:13:52,171 - INFO - 查询月度分段 1: 2025-01-01 至 2025-03-31
2025-06-08 21:13:52,171 - INFO - 查询日期范围: 2025-01-01 至 2025-03-31，使用分页查询，每页 100 条记录
2025-06-08 21:13:52,171 - INFO - Request Parameters - Page 1:
2025-06-08 21:13:52,172 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:13:52,172 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1743350400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:13:54,179 - INFO - API请求耗时: 2007ms
2025-06-08 21:13:54,179 - INFO - Response - Page 1
2025-06-08 21:13:54,179 - INFO - 第 1 页获取到 100 条记录
2025-06-08 21:13:54,688 - INFO - Request Parameters - Page 2:
2025-06-08 21:13:54,688 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:13:54,688 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1743350400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:13:55,314 - INFO - API请求耗时: 626ms
2025-06-08 21:13:55,315 - INFO - Response - Page 2
2025-06-08 21:13:55,315 - INFO - 第 2 页获取到 100 条记录
2025-06-08 21:13:55,815 - INFO - Request Parameters - Page 3:
2025-06-08 21:13:55,815 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:13:55,815 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1743350400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:13:56,404 - INFO - API请求耗时: 588ms
2025-06-08 21:13:56,404 - INFO - Response - Page 3
2025-06-08 21:13:56,404 - INFO - 第 3 页获取到 100 条记录
2025-06-08 21:13:56,907 - INFO - Request Parameters - Page 4:
2025-06-08 21:13:56,907 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:13:56,907 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1743350400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:13:57,619 - INFO - API请求耗时: 712ms
2025-06-08 21:13:57,619 - INFO - Response - Page 4
2025-06-08 21:13:57,620 - INFO - 第 4 页获取到 100 条记录
2025-06-08 21:13:58,120 - INFO - Request Parameters - Page 5:
2025-06-08 21:13:58,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:13:58,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1743350400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:13:58,621 - INFO - API请求耗时: 500ms
2025-06-08 21:13:58,621 - INFO - Response - Page 5
2025-06-08 21:13:58,622 - INFO - 第 5 页获取到 100 条记录
2025-06-08 21:13:59,122 - INFO - Request Parameters - Page 6:
2025-06-08 21:13:59,122 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:13:59,122 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1743350400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:13:59,907 - INFO - API请求耗时: 784ms
2025-06-08 21:13:59,907 - INFO - Response - Page 6
2025-06-08 21:13:59,908 - INFO - 第 6 页获取到 100 条记录
2025-06-08 21:14:00,409 - INFO - Request Parameters - Page 7:
2025-06-08 21:14:00,409 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:14:00,410 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1743350400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:14:00,926 - INFO - API请求耗时: 517ms
2025-06-08 21:14:00,926 - INFO - Response - Page 7
2025-06-08 21:14:00,926 - INFO - 第 7 页获取到 100 条记录
2025-06-08 21:14:01,427 - INFO - Request Parameters - Page 8:
2025-06-08 21:14:01,427 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:14:01,427 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 8, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1735660800000, 1743350400000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:14:01,797 - INFO - API请求耗时: 369ms
2025-06-08 21:14:01,797 - INFO - Response - Page 8
2025-06-08 21:14:01,798 - INFO - 第 8 页获取到 31 条记录
2025-06-08 21:14:01,798 - INFO - 查询完成，共获取到 731 条记录
2025-06-08 21:14:01,798 - INFO - 月度分段 1 查询成功，获取到 731 条记录
2025-06-08 21:14:02,799 - INFO - 查询月度分段 2: 2025-04-01 至 2025-06-30
2025-06-08 21:14:02,799 - INFO - 查询日期范围: 2025-04-01 至 2025-06-30，使用分页查询，每页 100 条记录
2025-06-08 21:14:02,799 - INFO - Request Parameters - Page 1:
2025-06-08 21:14:02,800 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:14:02,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:14:03,354 - INFO - API请求耗时: 554ms
2025-06-08 21:14:03,354 - INFO - Response - Page 1
2025-06-08 21:14:03,354 - INFO - 第 1 页获取到 100 条记录
2025-06-08 21:14:03,855 - INFO - Request Parameters - Page 2:
2025-06-08 21:14:03,855 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:14:03,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:14:04,337 - INFO - API请求耗时: 480ms
2025-06-08 21:14:04,337 - INFO - Response - Page 2
2025-06-08 21:14:04,337 - INFO - 第 2 页获取到 100 条记录
2025-06-08 21:14:04,837 - INFO - Request Parameters - Page 3:
2025-06-08 21:14:04,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:14:04,838 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:14:05,443 - INFO - API请求耗时: 604ms
2025-06-08 21:14:05,444 - INFO - Response - Page 3
2025-06-08 21:14:05,444 - INFO - 第 3 页获取到 100 条记录
2025-06-08 21:14:05,948 - INFO - Request Parameters - Page 4:
2025-06-08 21:14:05,948 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:14:05,948 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:14:06,460 - INFO - API请求耗时: 513ms
2025-06-08 21:14:06,460 - INFO - Response - Page 4
2025-06-08 21:14:06,460 - INFO - 第 4 页获取到 100 条记录
2025-06-08 21:14:06,976 - INFO - Request Parameters - Page 5:
2025-06-08 21:14:06,976 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:14:06,976 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:14:07,503 - INFO - API请求耗时: 527ms
2025-06-08 21:14:07,504 - INFO - Response - Page 5
2025-06-08 21:14:07,504 - INFO - 第 5 页获取到 100 条记录
2025-06-08 21:14:08,016 - INFO - Request Parameters - Page 6:
2025-06-08 21:14:08,016 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:14:08,016 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:14:08,546 - INFO - API请求耗时: 530ms
2025-06-08 21:14:08,546 - INFO - Response - Page 6
2025-06-08 21:14:08,546 - INFO - 第 6 页获取到 100 条记录
2025-06-08 21:14:09,047 - INFO - Request Parameters - Page 7:
2025-06-08 21:14:09,047 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-08 21:14:09,047 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743436800000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-08 21:14:09,531 - INFO - API请求耗时: 482ms
2025-06-08 21:14:09,531 - INFO - Response - Page 7
2025-06-08 21:14:09,531 - INFO - 第 7 页获取到 72 条记录
2025-06-08 21:14:09,532 - INFO - 查询完成，共获取到 672 条记录
2025-06-08 21:14:09,532 - INFO - 月度分段 2 查询成功，获取到 672 条记录
2025-06-08 21:14:10,542 - INFO - 宜搭月度表单数据查询完成，共 2 个分段，成功获取 1403 条记录，失败 0 次
2025-06-08 21:14:10,542 - INFO - 成功获取宜搭月销售表单数据，共 1403 条记录
2025-06-08 21:14:10,542 - INFO - 宜搭月销售数据项结构示例: ['formInstanceId', 'formData']
2025-06-08 21:14:10,542 - INFO - 正在从MySQL获取月度汇总数据...
2025-06-08 21:14:10,589 - INFO - 成功获取MySQL月度汇总数据，共 1403 条记录
2025-06-08 21:14:11,259 - INFO - 月度汇总数据已导出到Excel文件: logs/数衍平台月度数据_20250608.xlsx
2025-06-08 21:14:11,330 - INFO - 成功创建宜搭月销售数据索引，共 1403 条记录
2025-06-08 21:14:11,336 - WARNING - 时间戳字符串包含非数字字符: 2025-01
2025-06-08 21:14:11,818 - INFO - 更新表单数据成功: FINST-4OD66CC1B2WUB26YD9IZECBFC82620IVMXX9M0D
2025-06-08 21:14:11,818 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MO2IE70S367Q2OVAE57DLH001Q7I_2025-01, 变更字段: [{'field': 'shopEntityName', 'old_value': '椰客', 'new_value': '椰客椰子鸡'}]
2025-06-08 21:14:11,818 - WARNING - 时间戳字符串包含非数字字符: 2025-02
2025-06-08 21:14:12,261 - INFO - 更新表单数据成功: FINST-4OD66CC1B2WUB26YD9IZECBFC82620IVMXX9MAD
2025-06-08 21:14:12,261 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MO2IE70S367Q2OVAE57DLH001Q7I_2025-02, 变更字段: [{'field': 'shopEntityName', 'old_value': '椰客', 'new_value': '椰客椰子鸡'}]
2025-06-08 21:14:12,264 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-08 21:14:12,723 - INFO - 更新表单数据成功: FINST-4OD66CC1B2WUB26YD9IZECBFC82620IVMXX9MLD
2025-06-08 21:14:12,723 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_2025-03, 变更字段: [{'field': 'amount', 'old_value': 145584.6, 'new_value': 145585.3}]
2025-06-08 21:14:12,724 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-08 21:14:13,196 - INFO - 更新表单数据成功: FINST-4OD66CC1B2WUB26YD9IZECBFC82620IVMXX9MKD
2025-06-08 21:14:13,196 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MO2IE70S367Q2OVAE57DLH001Q7I_2025-03, 变更字段: [{'field': 'shopEntityName', 'old_value': '椰客', 'new_value': '椰客椰子鸡'}]
2025-06-08 21:14:13,201 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:13,583 - INFO - 更新表单数据成功: FINST-4OD66CC1B2WUB26YD9IZECBFC82621IVMXX9M3E
2025-06-08 21:14:13,583 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1I14PQ9FMDG1FG3MDGKU4P0I8N00178C_2025-04, 变更字段: [{'field': 'amount', 'old_value': 130146.49, 'new_value': 130150.75}]
2025-06-08 21:14:13,585 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:14,052 - INFO - 更新表单数据成功: FINST-4OD66CC1B2WUB26YD9IZECBFC82620IVMXX9MVD
2025-06-08 21:14:14,052 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_2025-04, 变更字段: [{'field': 'amount', 'old_value': 182939.3, 'new_value': 182939.4}]
2025-06-08 21:14:14,054 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:14,550 - INFO - 更新表单数据成功: FINST-4OD66CC1B2WUB26YD9IZECBFC82620IVMXX9MYD
2025-06-08 21:14:14,550 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_2025-04, 变更字段: [{'field': 'shopEntityName', 'old_value': 'SUPA FAMA 农场西餐', 'new_value': 'SUPA FAMA'}]
2025-06-08 21:14:14,552 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:15,031 - INFO - 更新表单数据成功: FINST-4OD66CC1B2WUB26YD9IZECBFC82621IVMXX9MZD
2025-06-08 21:14:15,031 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS_2025-04, 变更字段: [{'field': 'amount', 'old_value': 120735.78, 'new_value': 120737.59}]
2025-06-08 21:14:15,033 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:15,488 - INFO - 更新表单数据成功: FINST-4OD66CC1B2WUB26YD9IZECBFC82621IVMXX9M1E
2025-06-08 21:14:15,488 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ_2025-04, 变更字段: [{'field': 'amount', 'old_value': 827498.3099999999, 'new_value': 827498.41}]
2025-06-08 21:14:15,488 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:16,012 - INFO - 更新表单数据成功: FINST-4OD66CC1B2WUB26YD9IZECBFC82621IVMXX9M2E
2025-06-08 21:14:16,012 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1HRQIR9VACRH722I1UUTD5AEGF001EGK_2025-04, 变更字段: [{'field': 'amount', 'old_value': 124493.75, 'new_value': 124494.45}]
2025-06-08 21:14:16,012 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:14:16,540 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMLJ
2025-06-08 21:14:16,541 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_2025-05, 变更字段: [{'field': 'amount', 'old_value': 303699.8, 'new_value': 303701.9}]
2025-06-08 21:14:16,542 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:14:17,209 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMKJ
2025-06-08 21:14:17,210 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MO2IE70S367Q2OVAE57DLH001Q7I_2025-05, 变更字段: [{'field': 'amount', 'old_value': 6575.1, 'new_value': 6575.67}]
2025-06-08 21:14:17,210 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:14:17,641 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMTJ
2025-06-08 21:14:17,642 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1I14PQ9FMDG1FG3MDGKU4P0I8N00178C_2025-05, 变更字段: [{'field': 'amount', 'old_value': 229075.6, 'new_value': 229085.21}]
2025-06-08 21:14:17,643 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:14:18,165 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMRJ
2025-06-08 21:14:18,165 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ_2025-05, 变更字段: [{'field': 'amount', 'old_value': 769480.3, 'new_value': 769482.76}]
2025-06-08 21:14:18,167 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:14:18,589 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMSJ
2025-06-08 21:14:18,589 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1HRQIR9VACRH722I1UUTD5AEGF001EGK_2025-05, 变更字段: [{'field': 'amount', 'old_value': 26521.08, 'new_value': 26521.15}]
2025-06-08 21:14:18,589 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:14:19,066 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMPJ
2025-06-08 21:14:19,066 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS_2025-05, 变更字段: [{'field': 'amount', 'old_value': 92132.41, 'new_value': 92134.22}]
2025-06-08 21:14:19,068 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:14:19,476 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMOJ
2025-06-08 21:14:19,476 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_2025-05, 变更字段: [{'field': 'shopEntityName', 'old_value': 'SUPA FAMA 农场西餐', 'new_value': 'SUPA FAMA'}]
2025-06-08 21:14:19,512 - WARNING - 时间戳字符串包含非数字字符: 2025-01
2025-06-08 21:14:19,983 - INFO - 更新表单数据成功: FINST-4OD66CC1B2WUB26YD9IZECBFC82621IVMXX9MMF
2025-06-08 21:14:19,983 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_2025-01, 变更字段: [{'field': 'shopEntityName', 'old_value': '付先生在成都', 'new_value': '付掌柜在成都'}]
2025-06-08 21:14:19,994 - WARNING - 时间戳字符串包含非数字字符: 2025-02
2025-06-08 21:14:20,421 - INFO - 更新表单数据成功: FINST-RN766181H5VUOSQ29BNNDCKN1TOD3J4YMXX9MUD
2025-06-08 21:14:20,421 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_2025-02, 变更字段: [{'field': 'shopEntityName', 'old_value': '付先生在成都', 'new_value': '付掌柜在成都'}]
2025-06-08 21:14:20,429 - WARNING - 时间戳字符串包含非数字字符: 2025-02
2025-06-08 21:14:20,861 - INFO - 更新表单数据成功: FINST-RN766181H5VUOSQ29BNNDCKN1TOD3J4YMXX9MXD
2025-06-08 21:14:20,861 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVOR1PG7AOF730ABID5I1B7DD0016KT_2025-02, 变更字段: [{'field': 'shopEntityName', 'old_value': '米由米拌饭', 'new_value': '馨疆小馆'}]
2025-06-08 21:14:20,877 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-08 21:14:21,267 - INFO - 更新表单数据成功: FINST-MLF662B1FNVUWUUVAJ6XSDP7E8CS21U0NXX9MEG
2025-06-08 21:14:21,267 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDRLHCKFK97Q2OV4FVC7BS00148D_2025-03, 变更字段: [{'field': 'amount', 'old_value': 212608.16999999998, 'new_value': 212608.49}]
2025-06-08 21:14:21,270 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-08 21:14:21,763 - INFO - 更新表单数据成功: FINST-MLF662B1FNVUWUUVAJ6XSDP7E8CS21U0NXX9M3H
2025-06-08 21:14:21,764 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_2025-03, 变更字段: [{'field': 'shopEntityName', 'old_value': '付先生在成都', 'new_value': '付掌柜在成都'}]
2025-06-08 21:14:21,768 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-08 21:14:22,200 - INFO - 更新表单数据成功: FINST-MLF662B1FNVUWUUVAJ6XSDP7E8CS21U0NXX9MSG
2025-06-08 21:14:22,200 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE29HIJ7QK7Q2OV4FVC7F40014BL_2025-03, 变更字段: [{'field': 'amount', 'old_value': 153616.1, 'new_value': 153616.7}]
2025-06-08 21:14:22,211 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-08 21:14:22,661 - INFO - 更新表单数据成功: FINST-RN766181H5VUOSQ29BNNDCKN1TOD3J4YMXX9MKE
2025-06-08 21:14:22,661 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_2025-03, 变更字段: [{'field': 'amount', 'old_value': 23052.52, 'new_value': 23052.82}]
2025-06-08 21:14:22,661 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-08 21:14:23,154 - INFO - 更新表单数据成功: FINST-MLF662B1FNVUWUUVAJ6XSDP7E8CS22U0NXX9MKH
2025-06-08 21:14:23,154 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_2025-03, 变更字段: [{'field': 'amount', 'old_value': 52881.0, 'new_value': 52881.5}]
2025-06-08 21:14:23,159 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-08 21:14:23,597 - INFO - 更新表单数据成功: FINST-MLF662B1FNVUWUUVAJ6XSDP7E8CS21U0NXX9MBH
2025-06-08 21:14:23,597 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6692283A183D432BAE322E1032539CE8_2025-03, 变更字段: [{'field': 'shopEntityName', 'old_value': '鸟见', 'new_value': '鸟见炭火烧鸟'}]
2025-06-08 21:14:23,601 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:24,060 - INFO - 更新表单数据成功: FINST-Z7B66WA1GKVUDP8IB6DE66FONUG62SI3NXX9ML9
2025-06-08 21:14:24,060 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HHDS9O4DUBH2L6U1G19QP11V40018CI_2025-04, 变更字段: [{'field': 'amount', 'old_value': 51993.25, 'new_value': 51993.36}]
2025-06-08 21:14:24,060 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:24,546 - INFO - 更新表单数据成功: FINST-Z7B66WA1GKVUDP8IB6DE66FONUG62SI3NXX9MI9
2025-06-08 21:14:24,546 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9UCRQKEOIF52ASKKUBQUNH0018FA_2025-04, 变更字段: [{'field': 'amount', 'old_value': 314199.95, 'new_value': 314200.3}]
2025-06-08 21:14:24,552 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:24,990 - INFO - 更新表单数据成功: FINST-MLF662B1FNVUWUUVAJ6XSDP7E8CS22U0NXX9MHI
2025-06-08 21:14:24,991 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_2025-04, 变更字段: [{'field': 'amount', 'old_value': 111759.37, 'new_value': 111760.71}]
2025-06-08 21:14:24,993 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:25,420 - INFO - 更新表单数据成功: FINST-MLF662B1FNVUWUUVAJ6XSDP7E8CS22U0NXX9MDI
2025-06-08 21:14:25,421 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_2025-04, 变更字段: [{'field': 'amount', 'old_value': 106113.25, 'new_value': 106114.39}]
2025-06-08 21:14:25,423 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:25,914 - INFO - 更新表单数据成功: FINST-MLF662B1FNVUWUUVAJ6XSDP7E8CS22U0NXX9M9I
2025-06-08 21:14:25,915 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_2025-04, 变更字段: [{'field': 'amount', 'old_value': 35721.19, 'new_value': 35722.1}]
2025-06-08 21:14:25,917 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:26,405 - INFO - 更新表单数据成功: FINST-MLF662B1FNVUWUUVAJ6XSDP7E8CS22U0NXX9M6I
2025-06-08 21:14:26,405 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_2025-04, 变更字段: [{'field': 'amount', 'old_value': 37215.97, 'new_value': 37216.07}]
2025-06-08 21:14:26,409 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:26,792 - INFO - 更新表单数据成功: FINST-MLF662B1FNVUWUUVAJ6XSDP7E8CS22U0NXX9M1I
2025-06-08 21:14:26,792 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S_2025-04, 变更字段: [{'field': 'amount', 'old_value': 275477.43, 'new_value': 275478.13}]
2025-06-08 21:14:26,807 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:27,257 - INFO - 更新表单数据成功: FINST-MLF662B1FNVUWUUVAJ6XSDP7E8CS22U0NXX9MXH
2025-06-08 21:14:27,257 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCIR7D5JD7Q2OVBN4IS7U001D40_2025-04, 变更字段: [{'field': 'amount', 'old_value': 138771.9, 'new_value': 138774.0}]
2025-06-08 21:14:27,257 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:27,693 - INFO - 更新表单数据成功: FINST-MLF662B1FNVUWUUVAJ6XSDP7E8CS22U0NXX9MTH
2025-06-08 21:14:27,693 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTA575PTKJ7Q2OVBN4IS72001D34_2025-04, 变更字段: [{'field': 'amount', 'old_value': 188144.34, 'new_value': 188145.92}]
2025-06-08 21:14:27,696 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:28,100 - INFO - 更新表单数据成功: FINST-MLF662B1FNVUWUUVAJ6XSDP7E8CS22U0NXX9MRH
2025-06-08 21:14:28,100 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_2025-04, 变更字段: [{'field': 'amount', 'old_value': 17848.96, 'new_value': 17849.86}]
2025-06-08 21:14:28,104 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:28,609 - INFO - 更新表单数据成功: FINST-MLF662B1FNVUWUUVAJ6XSDP7E8CS22U0NXX9MMH
2025-06-08 21:14:28,609 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_2025-04, 变更字段: [{'field': 'amount', 'old_value': 102573.0, 'new_value': 102573.4}]
2025-06-08 21:14:28,613 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:29,008 - INFO - 更新表单数据成功: FINST-Z7B66WA1GKVUDP8IB6DE66FONUG62SI3NXX9MZ9
2025-06-08 21:14:29,009 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6B7571A27AF84C73B4FC04CCBDB83D9B_2025-04, 变更字段: [{'field': 'amount', 'old_value': 49546.229999999996, 'new_value': 49546.66}]
2025-06-08 21:14:29,015 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:29,393 - INFO - 更新表单数据成功: FINST-Z7B66WA1GKVUDP8IB6DE66FONUG62SI3NXX9M5A
2025-06-08 21:14:29,393 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_BF554F536BF14762AEB7110E7BD583B7_2025-04, 变更字段: [{'field': 'amount', 'old_value': 1216755.3, 'new_value': 1216755.32}]
2025-06-08 21:14:29,396 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:14:29,831 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMHK
2025-06-08 21:14:29,832 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDN6L4LMS87Q2OV4FVC79K001465_2025-05, 变更字段: [{'field': 'amount', 'old_value': 30024.01, 'new_value': 30024.7}]
2025-06-08 21:14:29,833 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:14:30,237 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMGK
2025-06-08 21:14:30,237 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_2025-05, 变更字段: [{'field': 'amount', 'old_value': 33117.43, 'new_value': 33122.48}, {'field': 'onlineAmount', 'old_value': 25971.62, 'new_value': 25971.63}]
2025-06-08 21:14:30,238 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:14:30,645 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMOK
2025-06-08 21:14:30,646 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSTUTUPTG7Q2OV4FVC7CG001491_2025-05, 变更字段: [{'field': 'amount', 'old_value': 150230.81, 'new_value': 150231.55}]
2025-06-08 21:14:30,648 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:14:31,023 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2L
2025-06-08 21:14:31,024 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9UCRQKEOIF52ASKKUBQUNH0018FA_2025-05, 变更字段: [{'field': 'amount', 'old_value': 242202.1, 'new_value': 242203.0}]
2025-06-08 21:14:31,026 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:14:31,398 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM5L
2025-06-08 21:14:31,398 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HHDS9O4DUBH2L6U1G19QP11V40018CI_2025-05, 变更字段: [{'field': 'amount', 'old_value': 48001.74, 'new_value': 48003.81}]
2025-06-08 21:14:31,398 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:14:31,832 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7L
2025-06-08 21:14:31,832 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HINIR4GO8E5NM5U25UDHUFEGO001L3K_2025-05, 变更字段: [{'field': 'amount', 'old_value': 61089.37, 'new_value': 61091.01}]
2025-06-08 21:14:31,832 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:14:32,402 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM9L
2025-06-08 21:14:32,402 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_2025-05, 变更字段: [{'field': 'amount', 'old_value': 256779.0, 'new_value': 256782.47}]
2025-06-08 21:14:32,402 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:14:32,867 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMCL
2025-06-08 21:14:32,867 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVORJ88U7D2IL1AIB692RTFU8001185_2025-05, 变更字段: [{'field': 'amount', 'old_value': 118799.86, 'new_value': 118802.88}, {'field': 'onlineAmount', 'old_value': 11445.01, 'new_value': 11445.02}]
2025-06-08 21:14:32,867 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:14:33,342 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMML
2025-06-08 21:14:33,343 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_BF554F536BF14762AEB7110E7BD583B7_2025-05, 变更字段: [{'field': 'amount', 'old_value': 1303875.52, 'new_value': 1303877.38}]
2025-06-08 21:14:33,344 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:14:33,833 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMEK
2025-06-08 21:14:33,833 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDKDOANT3H7Q2OV4FVC78800144P_2025-05, 变更字段: [{'field': 'amount', 'old_value': 205903.08, 'new_value': 205903.34}]
2025-06-08 21:14:33,835 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:14:34,332 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMNL
2025-06-08 21:14:34,332 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_2025-05, 变更字段: [{'field': 'amount', 'old_value': 41814.9, 'new_value': 41819.4}]
2025-06-08 21:14:34,335 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:14:34,903 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBK
2025-06-08 21:14:34,904 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTECQFMLUJ7Q2OVBN4IS8Q001D4S_2025-05, 变更字段: [{'field': 'amount', 'old_value': 402416.33, 'new_value': 402421.78}]
2025-06-08 21:14:34,908 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:14:35,383 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7K
2025-06-08 21:14:35,383 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCIR7D5JD7Q2OVBN4IS7U001D40_2025-05, 变更字段: [{'field': 'amount', 'old_value': 140079.7, 'new_value': 140085.6}]
2025-06-08 21:14:35,387 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:14:35,849 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1K
2025-06-08 21:14:35,849 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_2025-05, 变更字段: [{'field': 'amount', 'old_value': 16079.4, 'new_value': 16080.2}]
2025-06-08 21:14:35,849 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:14:36,296 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMQK
2025-06-08 21:14:36,296 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_2025-05, 变更字段: [{'field': 'amount', 'old_value': 116417.1, 'new_value': 116427.25}]
2025-06-08 21:14:36,297 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:14:36,713 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3K
2025-06-08 21:14:36,714 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTA575PTKJ7Q2OVBN4IS72001D34_2025-05, 变更字段: [{'field': 'amount', 'old_value': 186088.22, 'new_value': 186089.08}]
2025-06-08 21:14:36,716 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:14:37,217 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMWJ
2025-06-08 21:14:37,217 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_2025-05, 变更字段: [{'field': 'amount', 'old_value': 142662.1, 'new_value': 142663.8}]
2025-06-08 21:14:37,217 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:14:37,729 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMSK
2025-06-08 21:14:37,729 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV0D9P6J27Q2OV4FVC7DG0014A1_2025-05, 变更字段: [{'field': 'amount', 'old_value': 82368.69, 'new_value': 82369.3}]
2025-06-08 21:14:37,731 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:14:38,222 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMUK
2025-06-08 21:14:38,222 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVG061A0H7Q2OV4FVC7DO0014A9_2025-05, 变更字段: [{'field': 'amount', 'old_value': 84989.5, 'new_value': 84990.0}]
2025-06-08 21:14:38,225 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:14:38,609 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYK
2025-06-08 21:14:38,609 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE29HIJ7QK7Q2OV4FVC7F40014BL_2025-05, 变更字段: [{'field': 'amount', 'old_value': 117480.15, 'new_value': 117482.75}]
2025-06-08 21:14:38,610 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:14:38,973 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZK
2025-06-08 21:14:38,973 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE2TORA7KI7Q2OV4FVC7FC0014BT_2025-05, 变更字段: [{'field': 'amount', 'old_value': 84333.83, 'new_value': 84337.01}]
2025-06-08 21:14:38,973 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:14:39,430 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0L
2025-06-08 21:14:39,430 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9PGR3703D752ASKKUBQUM50018DU_2025-05, 变更字段: [{'field': 'amount', 'old_value': 209460.97, 'new_value': 209461.17}]
2025-06-08 21:14:39,432 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:14:39,923 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYJ
2025-06-08 21:14:39,923 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O_2025-05, 变更字段: [{'field': 'amount', 'old_value': -415293.63, 'new_value': -415295.33}]
2025-06-08 21:14:39,925 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:14:40,412 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMMK
2025-06-08 21:14:40,412 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_2025-05, 变更字段: [{'field': 'amount', 'old_value': 123349.45, 'new_value': 123356.18}]
2025-06-08 21:14:40,414 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:14:40,930 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMLK
2025-06-08 21:14:40,930 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDS537TI7U7Q2OV4FVC7C400148L_2025-05, 变更字段: [{'field': 'amount', 'old_value': 20377.99, 'new_value': 20378.49}]
2025-06-08 21:14:40,930 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:14:41,335 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMKK
2025-06-08 21:14:41,335 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDRLHCKFK97Q2OV4FVC7BS00148D_2025-05, 变更字段: [{'field': 'amount', 'old_value': 187875.24, 'new_value': 187876.76}]
2025-06-08 21:14:41,335 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:14:41,809 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMJK
2025-06-08 21:14:41,810 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_2025-05, 变更字段: [{'field': 'amount', 'old_value': 36862.96, 'new_value': 36868.16}]
2025-06-08 21:14:41,832 - WARNING - 时间戳字符串包含非数字字符: 2025-01
2025-06-08 21:14:42,375 - INFO - 更新表单数据成功: FINST-7PF66MD1UWWUTYXCANXWGCMSUYOT3O66NXX9M6
2025-06-08 21:14:42,375 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRR231GUB4QN7QBECDAL3H28001J69_2025-01, 变更字段: [{'field': 'shopEntityName', 'old_value': '好特卖', 'new_value': '好特卖HOT MAXX'}]
2025-06-08 21:14:42,375 - WARNING - 时间戳字符串包含非数字字符: 2025-01
2025-06-08 21:14:42,788 - INFO - 更新表单数据成功: FINST-Z7B66WA1GKVUDP8IB6DE66FONUG62TI3NXX9M5C
2025-06-08 21:14:42,788 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAIN5DMKK0I86N3H2U1VH001F8N_2025-01, 变更字段: [{'field': 'shopEntityName', 'old_value': '凑凑火锅·茶米茶', 'new_value': '湊湊火锅·茶米茶'}]
2025-06-08 21:14:42,792 - WARNING - 时间戳字符串包含非数字字符: 2025-01
2025-06-08 21:14:43,231 - INFO - 更新表单数据成功: FINST-Z7B66WA1GKVUDP8IB6DE66FONUG62TI3NXX9MXB
2025-06-08 21:14:43,231 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE3PJ2TE9A0I86N3H2U1S3001F59_2025-01, 变更字段: [{'field': 'shopEntityName', 'old_value': '拣·JAN', 'new_value': '悦汇城拣'}]
2025-06-08 21:14:43,246 - WARNING - 时间戳字符串包含非数字字符: 2025-01
2025-06-08 21:14:43,671 - INFO - 更新表单数据成功: FINST-Z7B66WA1GKVUDP8IB6DE66FONUG62TI3NXX9MTB
2025-06-08 21:14:43,672 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1T4E94FK0I86N3H2U1R9001F4F_2025-01, 变更字段: [{'field': 'shopEntityName', 'old_value': '都可茶饮', 'new_value': 'CoCo都可'}]
2025-06-08 21:14:43,679 - WARNING - 时间戳字符串包含非数字字符: 2025-01
2025-06-08 21:14:44,088 - INFO - 更新表单数据成功: FINST-Z7B66WA1GKVUDP8IB6DE66FONUG62TI3NXX9MEB
2025-06-08 21:14:44,088 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJGUIA2QR0I86N3H2U1JH001ESN_2025-01, 变更字段: [{'field': 'shopEntityName', 'old_value': 'EAMIEYZ', 'new_value': '依幂'}]
2025-06-08 21:14:44,100 - WARNING - 时间戳字符串包含非数字字符: 2025-01
2025-06-08 21:14:44,468 - INFO - 更新表单数据成功: FINST-Z7B66WA1GKVUDP8IB6DE66FONUG62SI3NXX9MKA
2025-06-08 21:14:44,468 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDS341MMSU0I86N3H2U10P001E9V_2025-01, 变更字段: [{'field': 'shopEntityName', 'old_value': '陳家生', 'new_value': '陈家生煎'}]
2025-06-08 21:14:44,468 - WARNING - 时间戳字符串包含非数字字符: 2025-01
2025-06-08 21:14:44,869 - INFO - 更新表单数据成功: FINST-Z7B66WA1GKVUDP8IB6DE66FONUG62SI3NXX9MHA
2025-06-08 21:14:44,869 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_2025-01, 变更字段: [{'field': 'shopEntityName', 'old_value': '茶理宜世', 'new_value': '悦汇城茶理宜世'}]
2025-06-08 21:14:44,875 - WARNING - 时间戳字符串包含非数字字符: 2025-02
2025-06-08 21:14:45,319 - INFO - 更新表单数据成功: FINST-7PF66MD1UWWUTYXCANXWGCMSUYOT3P66NXX9ML
2025-06-08 21:14:45,319 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_2025-02, 变更字段: [{'field': 'shopEntityName', 'old_value': '茶理宜世', 'new_value': '悦汇城茶理宜世'}]
2025-06-08 21:14:45,335 - WARNING - 时间戳字符串包含非数字字符: 2025-02
2025-06-08 21:14:45,702 - INFO - 更新表单数据成功: FINST-7PF66MD1UWWUTYXCANXWGCMSUYOT3P66NXX9MZ1
2025-06-08 21:14:45,702 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1T4E94FK0I86N3H2U1R9001F4F_2025-02, 变更字段: [{'field': 'shopEntityName', 'old_value': '都可茶饮', 'new_value': 'CoCo都可'}]
2025-06-08 21:14:45,706 - WARNING - 时间戳字符串包含非数字字符: 2025-02
2025-06-08 21:14:46,107 - INFO - 更新表单数据成功: FINST-7PF66MD1UWWUTYXCANXWGCMSUYOT3P66NXX9M32
2025-06-08 21:14:46,107 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE3PJ2TE9A0I86N3H2U1S3001F59_2025-02, 变更字段: [{'field': 'shopEntityName', 'old_value': '拣·JAN', 'new_value': '悦汇城拣'}]
2025-06-08 21:14:46,107 - WARNING - 时间戳字符串包含非数字字符: 2025-02
2025-06-08 21:14:46,542 - INFO - 更新表单数据成功: FINST-7PF66MD1UWWUTYXCANXWGCMSUYOT3P66NXX9MO
2025-06-08 21:14:46,542 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDS341MMSU0I86N3H2U10P001E9V_2025-02, 变更字段: [{'field': 'shopEntityName', 'old_value': '陳家生', 'new_value': '陈家生煎'}]
2025-06-08 21:14:46,542 - WARNING - 时间戳字符串包含非数字字符: 2025-02
2025-06-08 21:14:46,957 - INFO - 更新表单数据成功: FINST-7PF66MD1UWWUTYXCANXWGCMSUYOT3P66NXX9MB1
2025-06-08 21:14:46,957 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_2025-02, 变更字段: [{'field': 'amount', 'old_value': 72479.76, 'new_value': 72480.36}]
2025-06-08 21:14:46,973 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-08 21:14:47,393 - INFO - 更新表单数据成功: FINST-FD966QA1M5VUULMLBGN4Z7W18VYC23U8NXX9MQL
2025-06-08 21:14:47,393 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDS341MMSU0I86N3H2U10P001E9V_2025-03, 变更字段: [{'field': 'shopEntityName', 'old_value': '陳家生', 'new_value': '陈家生煎'}]
2025-06-08 21:14:47,393 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-08 21:14:47,876 - INFO - 更新表单数据成功: FINST-FD966QA1M5VUULMLBGN4Z7W18VYC23U8NXX9MNL
2025-06-08 21:14:47,876 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_2025-03, 变更字段: [{'field': 'shopEntityName', 'old_value': '茶理宜世', 'new_value': '悦汇城茶理宜世'}]
2025-06-08 21:14:47,892 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-08 21:14:48,311 - INFO - 更新表单数据成功: FINST-FD966QA1M5VUULMLBGN4Z7W18VYC23U8NXX9MJL
2025-06-08 21:14:48,311 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_2025-03, 变更字段: [{'field': 'amount', 'old_value': 365873.9, 'new_value': 365874.5}]
2025-06-08 21:14:48,311 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-08 21:14:48,732 - INFO - 更新表单数据成功: FINST-7PF66MD1UWWUTYXCANXWGCMSUYOT3P66NXX9MR2
2025-06-08 21:14:48,732 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDMFCJQF4F0I86N3H2U1UC001E7I_2025-03, 变更字段: [{'field': 'recommendAmount', 'old_value': 158461.38, 'new_value': 158734.08}, {'field': 'amount', 'old_value': 158460.65, 'new_value': 158734.08}, {'field': 'count', 'old_value': 1712, 'new_value': 1713}, {'field': 'instoreAmount', 'old_value': 114006.75, 'new_value': 114279.45}, {'field': 'instoreCount', 'old_value': 1155, 'new_value': 1156}]
2025-06-08 21:14:48,732 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-08 21:14:49,152 - INFO - 更新表单数据成功: FINST-FD966QA1M5VUULMLBGN4Z7W18VYC24U8NXX9MBN
2025-06-08 21:14:49,152 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_2025-03, 变更字段: [{'field': 'amount', 'old_value': 150036.2, 'new_value': 150037.3}]
2025-06-08 21:14:49,155 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-08 21:14:49,656 - INFO - 更新表单数据成功: FINST-FD966QA1M5VUULMLBGN4Z7W18VYC24U8NXX9M5N
2025-06-08 21:14:49,656 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_2025-03, 变更字段: [{'field': 'amount', 'old_value': 735054.68, 'new_value': 735054.96}]
2025-06-08 21:14:49,659 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-08 21:14:50,096 - INFO - 更新表单数据成功: FINST-FD966QA1M5VUULMLBGN4Z7W18VYC24U8NXX9M2N
2025-06-08 21:14:50,096 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE3PJ2TE9A0I86N3H2U1S3001F59_2025-03, 变更字段: [{'field': 'shopEntityName', 'old_value': '拣·JAN', 'new_value': '悦汇城拣'}]
2025-06-08 21:14:50,096 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-08 21:14:50,470 - INFO - 更新表单数据成功: FINST-FD966QA1M5VUULMLBGN4Z7W18VYC24U8NXX9MYM
2025-06-08 21:14:50,470 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1T4E94FK0I86N3H2U1R9001F4F_2025-03, 变更字段: [{'field': 'shopEntityName', 'old_value': '都可茶饮', 'new_value': 'CoCo都可'}]
2025-06-08 21:14:50,486 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-08 21:14:50,889 - INFO - 更新表单数据成功: FINST-FD966QA1M5VUULMLBGN4Z7W18VYC23U8NXX9MBM
2025-06-08 21:14:50,889 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_2025-03, 变更字段: [{'field': 'amount', 'old_value': 109259.4, 'new_value': 108222.3}, {'field': 'count', 'old_value': 119, 'new_value': 120}, {'field': 'instoreAmount', 'old_value': 109784.3, 'new_value': 108746.6}, {'field': 'instoreCount', 'old_value': 119, 'new_value': 120}]
2025-06-08 21:14:50,889 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:51,398 - INFO - 更新表单数据成功: FINST-FD966QA1M5VUULMLBGN4Z7W18VYC24U8NXX9MNN
2025-06-08 21:14:51,398 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDMFCJQF4F0I86N3H2U1UC001E7I_2025-04, 变更字段: [{'field': 'amount', 'old_value': 158217.86, 'new_value': 158219.51}]
2025-06-08 21:14:51,398 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:51,838 - INFO - 更新表单数据成功: FINST-8PF66V71F2WUF93GERNDRA2XP25A2BHBNXX9MPA
2025-06-08 21:14:51,839 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_2025-04, 变更字段: [{'field': 'amount', 'old_value': 143969.4, 'new_value': 143970.8}]
2025-06-08 21:14:51,840 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:52,320 - INFO - 更新表单数据成功: FINST-8PF66V71F2WUF93GERNDRA2XP25A2BHBNXX9MQA
2025-06-08 21:14:52,321 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEQ2M9E710I86N3H2U1H1001EQ7_2025-04, 变更字段: [{'field': 'amount', 'old_value': 134042.05, 'new_value': 134043.65}]
2025-06-08 21:14:52,324 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:52,778 - INFO - 更新表单数据成功: FINST-8PF66V71F2WUF93GERNDRA2XP25A2BHBNXX9MVA
2025-06-08 21:14:52,778 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_2025-04, 变更字段: [{'field': 'amount', 'old_value': 47348.73, 'new_value': 47349.3}]
2025-06-08 21:14:52,781 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:53,246 - INFO - 更新表单数据成功: FINST-FD966QA1M5VUULMLBGN4Z7W18VYC24U8NXX9M4O
2025-06-08 21:14:53,246 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVBEGSM760I86N3H2U12H001EBN_2025-04, 变更字段: [{'field': 'amount', 'old_value': 216817.27, 'new_value': 216817.57}]
2025-06-08 21:14:53,246 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:53,712 - INFO - 更新表单数据成功: FINST-8PF66V71F2WUF93GERNDRA2XP25A2BHBNXX9MJB
2025-06-08 21:14:53,712 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_2025-04, 变更字段: [{'field': 'amount', 'old_value': 488613.26, 'new_value': 488614.28}]
2025-06-08 21:14:53,712 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:54,161 - INFO - 更新表单数据成功: FINST-8PF66V71F2WUF93GERNDRA2XP25A2BHBNXX9M7B
2025-06-08 21:14:54,161 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_2025-04, 变更字段: [{'field': 'amount', 'old_value': 142923.28, 'new_value': 142926.56}]
2025-06-08 21:14:54,163 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:54,660 - INFO - 更新表单数据成功: FINST-8PF66V71F2WUF93GERNDRA2XP25A2BHBNXX9M4B
2025-06-08 21:14:54,660 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_2025-04, 变更字段: [{'field': 'amount', 'old_value': 35882.7, 'new_value': 35884.2}]
2025-06-08 21:14:54,663 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:55,252 - INFO - 更新表单数据成功: FINST-8PF66V71F2WUF93GERNDRA2XP25A2BHBNXX9M0B
2025-06-08 21:14:55,252 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_2025-04, 变更字段: [{'field': 'amount', 'old_value': 350761.68, 'new_value': 350763.83}]
2025-06-08 21:14:55,252 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:55,745 - INFO - 更新表单数据成功: FINST-8PF66V71F2WUF93GERNDRA2XP25A2CHBNXX9MOB
2025-06-08 21:14:55,745 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_2025-04, 变更字段: [{'field': 'amount', 'old_value': 119908.5, 'new_value': 119909.6}]
2025-06-08 21:14:55,747 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:56,261 - INFO - 更新表单数据成功: FINST-8PF66V71F2WUF93GERNDRA2XP25A2CHBNXX9MQB
2025-06-08 21:14:56,262 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_2025-04, 变更字段: [{'field': 'amount', 'old_value': 130433.81, 'new_value': 130434.12}]
2025-06-08 21:14:56,264 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:56,714 - INFO - 更新表单数据成功: FINST-8PF66V71F2WUF93GERNDRA2XP25A2CHBNXX9MSB
2025-06-08 21:14:56,715 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_2025-04, 变更字段: [{'field': 'amount', 'old_value': 180821.32, 'new_value': 180823.2}]
2025-06-08 21:14:56,716 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:57,141 - INFO - 更新表单数据成功: FINST-8PF66V71F2WUF93GERNDRA2XP25A2CHBNXX9MUB
2025-06-08 21:14:57,141 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRR231GUB4QN7QBECDAL3H28001J69_2025-04, 变更字段: [{'field': 'amount', 'old_value': 220272.84, 'new_value': 220273.14}]
2025-06-08 21:14:57,143 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:57,626 - INFO - 更新表单数据成功: FINST-8PF66V71F2WUF93GERNDRA2XP25A2CHBNXX9MWB
2025-06-08 21:14:57,626 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B358872E0DBE420182AF77D4C47644F6_2025-04, 变更字段: [{'field': 'amount', 'old_value': 155204.73, 'new_value': 155205.8}]
2025-06-08 21:14:57,627 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:58,117 - INFO - 更新表单数据成功: FINST-8PF66V71F2WUF93GERNDRA2XP25A2CHBNXX9MXB
2025-06-08 21:14:58,117 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_2025-04, 变更字段: [{'field': 'amount', 'old_value': 35036.88, 'new_value': 35037.65}]
2025-06-08 21:14:58,119 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:58,458 - INFO - 更新表单数据成功: FINST-8PF66V71F2WUF93GERNDRA2XP25A2CHBNXX9MZB
2025-06-08 21:14:58,458 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_EC9758A692DF47FBA8F7C97344079C9E_2025-04, 变更字段: [{'field': 'amount', 'old_value': 103157.51000000001, 'new_value': 103158.14}]
2025-06-08 21:14:58,460 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:58,855 - INFO - 更新表单数据成功: FINST-8PF66V71F2WUF93GERNDRA2XP25A2CHBNXX9MLB
2025-06-08 21:14:58,855 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAB2RFI0N0I86N3H2U1VD001F8J_2025-04, 变更字段: [{'field': 'amount', 'old_value': 153803.9, 'new_value': 153804.7}]
2025-06-08 21:14:58,857 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:59,248 - INFO - 更新表单数据成功: FINST-8PF66V71F2WUF93GERNDRA2XP25A2BHBNXX9MKB
2025-06-08 21:14:59,248 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEA4041D6F0I86N3H2U1V9001F8F_2025-04, 变更字段: [{'field': 'amount', 'old_value': 542236.72, 'new_value': 542237.11}]
2025-06-08 21:14:59,249 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:14:59,773 - INFO - 更新表单数据成功: FINST-8PF66V71F2WUF93GERNDRA2XP25A2BHBNXX9MIB
2025-06-08 21:14:59,774 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_2025-04, 变更字段: [{'field': 'amount', 'old_value': 731545.2, 'new_value': 731548.38}]
2025-06-08 21:14:59,777 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:15:00,297 - INFO - 更新表单数据成功: FINST-8PF66V71F2WUF93GERNDRA2XP25A2BHBNXX9MEB
2025-06-08 21:15:00,297 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_2025-04, 变更字段: [{'field': 'amount', 'old_value': 904314.12, 'new_value': 904316.37}]
2025-06-08 21:15:00,298 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:15:00,821 - INFO - 更新表单数据成功: FINST-8PF66V71F2WUF93GERNDRA2XP25A2BHBNXX9MDB
2025-06-08 21:15:00,821 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE2CVHLBFV0I86N3H2U1RH001F4N_2025-04, 变更字段: [{'field': 'amount', 'old_value': 382393.70999999996, 'new_value': 382393.93}]
2025-06-08 21:15:00,821 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:15:01,310 - INFO - 更新表单数据成功: FINST-8PF66V71F2WUF93GERNDRA2XP25A2BHBNXX9MCB
2025-06-08 21:15:01,311 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE25DAIM3B0I86N3H2U1RD001F4J_2025-04, 变更字段: [{'field': 'amount', 'old_value': 322386.65, 'new_value': 322387.15}]
2025-06-08 21:15:01,315 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:15:01,712 - INFO - 更新表单数据成功: FINST-FD966QA1M5VUULMLBGN4Z7W18VYC24U8NXX9M5O
2025-06-08 21:15:01,712 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVSS4V3460I86N3H2U12P001EBV_2025-04, 变更字段: [{'field': 'amount', 'old_value': 107062.11, 'new_value': 107063.0}]
2025-06-08 21:15:01,717 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:15:02,195 - INFO - 更新表单数据成功: FINST-FD966QA1M5VUULMLBGN4Z7W18VYC24U8NXX9M3O
2025-06-08 21:15:02,196 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_2025-04, 变更字段: [{'field': 'amount', 'old_value': 142368.19, 'new_value': 142371.09}]
2025-06-08 21:15:02,197 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:15:02,610 - INFO - 更新表单数据成功: FINST-FD966QA1M5VUULMLBGN4Z7W18VYC24U8NXX9M2O
2025-06-08 21:15:02,610 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_2025-04, 变更字段: [{'field': 'amount', 'old_value': 147140.09, 'new_value': 147144.14}]
2025-06-08 21:15:02,613 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:15:03,053 - INFO - 更新表单数据成功: FINST-FD966QA1M5VUULMLBGN4Z7W18VYC24U8NXX9MZN
2025-06-08 21:15:03,053 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_2025-04, 变更字段: [{'field': 'amount', 'old_value': 100674.45, 'new_value': 100675.47}]
2025-06-08 21:15:03,053 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:15:03,524 - INFO - 更新表单数据成功: FINST-FD966QA1M5VUULMLBGN4Z7W18VYC24U8NXX9MVN
2025-06-08 21:15:03,525 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_2025-04, 变更字段: [{'field': 'amount', 'old_value': 214947.93, 'new_value': 214948.98}]
2025-06-08 21:15:03,527 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:15:03,981 - INFO - 更新表单数据成功: FINST-FD966QA1M5VUULMLBGN4Z7W18VYC24U8NXX9MSN
2025-06-08 21:15:03,981 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_2025-04, 变更字段: [{'field': 'amount', 'old_value': 320645.97000000003, 'new_value': 320649.17}]
2025-06-08 21:15:03,982 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:15:04,419 - INFO - 更新表单数据成功: FINST-FD966QA1M5VUULMLBGN4Z7W18VYC24U8NXX9MRN
2025-06-08 21:15:04,419 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_2025-04, 变更字段: [{'field': 'amount', 'old_value': 338806.5, 'new_value': 338807.2}]
2025-06-08 21:15:04,420 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:15:04,839 - INFO - 更新表单数据成功: FINST-FD966QA1M5VUULMLBGN4Z7W18VYC24U8NXX9MQN
2025-06-08 21:15:04,840 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_2025-04, 变更字段: [{'field': 'amount', 'old_value': 141360.81, 'new_value': 141361.9}]
2025-06-08 21:15:04,842 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:05,256 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMJ71
2025-06-08 21:15:05,256 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_2025-05, 变更字段: [{'field': 'amount', 'old_value': 390264.6, 'new_value': 390267.94}]
2025-06-08 21:15:05,272 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:05,662 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMM71
2025-06-08 21:15:05,662 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_2025-05, 变更字段: [{'field': 'amount', 'old_value': 43310.4, 'new_value': 43319.84}]
2025-06-08 21:15:05,664 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:06,068 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMN71
2025-06-08 21:15:06,068 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_2025-05, 变更字段: [{'field': 'amount', 'old_value': 752660.08, 'new_value': 752665.56}]
2025-06-08 21:15:06,070 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:06,431 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMP71
2025-06-08 21:15:06,431 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_2025-05, 变更字段: [{'field': 'amount', 'old_value': 206490.73, 'new_value': 206498.08}]
2025-06-08 21:15:06,440 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:06,915 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMQ71
2025-06-08 21:15:06,916 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_2025-05, 变更字段: [{'field': 'amount', 'old_value': 136410.1, 'new_value': 136415.13}]
2025-06-08 21:15:06,918 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:07,286 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU71
2025-06-08 21:15:07,286 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE25DAIM3B0I86N3H2U1RD001F4J_2025-05, 变更字段: [{'field': 'amount', 'old_value': 399338.62, 'new_value': 399342.12}]
2025-06-08 21:15:07,287 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:07,643 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMV71
2025-06-08 21:15:07,643 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE2CVHLBFV0I86N3H2U1RH001F4N_2025-05, 变更字段: [{'field': 'amount', 'old_value': 477822.97, 'new_value': 477825.77}]
2025-06-08 21:15:07,644 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:08,096 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW71
2025-06-08 21:15:08,096 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_2025-05, 变更字段: [{'field': 'amount', 'old_value': 1314834.34, 'new_value': 1314841.16}]
2025-06-08 21:15:08,098 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:08,524 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMCC
2025-06-08 21:15:08,525 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE4GES1U770I86N3H2U1SF001F5L_2025-05, 变更字段: [{'field': 'amount', 'old_value': 944931.59, 'new_value': 944932.69}]
2025-06-08 21:15:08,526 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:09,000 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMZ71
2025-06-08 21:15:09,001 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_2025-05, 变更字段: [{'field': 'amount', 'old_value': 992932.08, 'new_value': 992941.37}]
2025-06-08 21:15:09,002 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:09,423 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM081
2025-06-08 21:15:09,423 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_2025-05, 变更字段: [{'field': 'amount', 'old_value': 570877.96, 'new_value': 570882.93}]
2025-06-08 21:15:09,425 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:09,870 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM281
2025-06-08 21:15:09,871 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAB2RFI0N0I86N3H2U1VD001F8J_2025-05, 变更字段: [{'field': 'amount', 'old_value': 145907.3, 'new_value': 145907.8}]
2025-06-08 21:15:09,872 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:10,245 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM381
2025-06-08 21:15:10,245 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAIN5DMKK0I86N3H2U1VH001F8N_2025-05, 变更字段: [{'field': 'amount', 'old_value': -333239.28, 'new_value': -333244.43}]
2025-06-08 21:15:10,245 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:10,709 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMVL
2025-06-08 21:15:10,709 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQQG9THS10I86N3H2U108001E9E_2025-05, 变更字段: [{'field': 'amount', 'old_value': 309664.87, 'new_value': 309666.98}]
2025-06-08 21:15:10,709 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:11,111 - INFO - 更新表单数据成功: FINST-RI766091Q8ZUDV4P6CERK73FVYBF3UQMQBAAMQC1
2025-06-08 21:15:11,111 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_2025-05, 变更字段: [{'field': 'amount', 'old_value': 202698.74, 'new_value': 202709.77}]
2025-06-08 21:15:11,113 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:11,606 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMTL
2025-06-08 21:15:11,606 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q_2025-05, 变更字段: [{'field': 'amount', 'old_value': 467545.75, 'new_value': 467546.73}]
2025-06-08 21:15:11,607 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:12,037 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMSL
2025-06-08 21:15:12,037 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_2025-05, 变更字段: [{'field': 'amount', 'old_value': 527586.55, 'new_value': 527596.1}]
2025-06-08 21:15:12,038 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:12,470 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMRL
2025-06-08 21:15:12,470 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_2025-05, 变更字段: [{'field': 'amount', 'old_value': 335079.4, 'new_value': 335082.1}]
2025-06-08 21:15:12,470 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:12,968 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMQL
2025-06-08 21:15:12,968 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_2025-05, 变更字段: [{'field': 'amount', 'old_value': 187504.68, 'new_value': 187507.53}]
2025-06-08 21:15:12,970 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:13,421 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMPL
2025-06-08 21:15:13,421 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q_2025-05, 变更字段: [{'field': 'amount', 'old_value': 35288.21, 'new_value': 35288.44}]
2025-06-08 21:15:13,437 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:13,934 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMOL
2025-06-08 21:15:13,934 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDMFCJQF4F0I86N3H2U1UC001E7I_2025-05, 变更字段: [{'field': 'amount', 'old_value': 229128.28, 'new_value': 229132.52}]
2025-06-08 21:15:13,949 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:14,461 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM3M
2025-06-08 21:15:14,462 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVBEGSM760I86N3H2U12H001EBN_2025-05, 变更字段: [{'field': 'amount', 'old_value': 143737.92, 'new_value': 143738.45}]
2025-06-08 21:15:14,464 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:14,891 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM181
2025-06-08 21:15:14,892 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEA4041D6F0I86N3H2U1V9001F8F_2025-05, 变更字段: [{'field': 'amount', 'old_value': 640400.28, 'new_value': 640404.33}]
2025-06-08 21:15:14,893 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:15,275 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM981
2025-06-08 21:15:15,275 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_2025-05, 变更字段: [{'field': 'amount', 'old_value': 172544.97, 'new_value': 172560.28}]
2025-06-08 21:15:15,275 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:15,655 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMB81
2025-06-08 21:15:15,655 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRR231GUB4QN7QBECDAL3H28001J69_2025-05, 变更字段: [{'field': 'amount', 'old_value': 197640.4, 'new_value': 197641.77}]
2025-06-08 21:15:15,656 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:16,049 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMC81
2025-06-08 21:15:16,049 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B358872E0DBE420182AF77D4C47644F6_2025-05, 变更字段: [{'field': 'amount', 'old_value': 192167.44, 'new_value': 192168.64}]
2025-06-08 21:15:16,049 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:16,522 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMD81
2025-06-08 21:15:16,522 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_2025-05, 变更字段: [{'field': 'amount', 'old_value': 38198.43, 'new_value': 38205.15}, {'field': 'onlineAmount', 'old_value': 20077.85, 'new_value': 20077.86}]
2025-06-08 21:15:16,523 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:16,957 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AME81
2025-06-08 21:15:16,958 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_E214E9E9A4534AE1943BBACB09056E2E_2025-05, 变更字段: [{'field': 'amount', 'old_value': 69539.4, 'new_value': 69541.7}]
2025-06-08 21:15:16,960 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:17,507 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM581
2025-06-08 21:15:17,507 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_2025-05, 变更字段: [{'field': 'amount', 'old_value': 211565.0, 'new_value': 211566.7}]
2025-06-08 21:15:17,510 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:17,916 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM781
2025-06-08 21:15:17,916 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_2025-05, 变更字段: [{'field': 'amount', 'old_value': 171089.59, 'new_value': 171092.9}]
2025-06-08 21:15:17,918 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:18,496 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AMH71
2025-06-08 21:15:18,496 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJP082LC00I86N3H2U1JM001ESS_2025-05, 变更字段: [{'field': 'amount', 'old_value': 29024.18, 'new_value': 29025.61}]
2025-06-08 21:15:18,498 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:18,974 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AME71
2025-06-08 21:15:18,974 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_2025-05, 变更字段: [{'field': 'amount', 'old_value': 61607.95, 'new_value': 61612.62}]
2025-06-08 21:15:18,977 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:19,388 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM971
2025-06-08 21:15:19,388 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEQ2M9E710I86N3H2U1H1001EQ7_2025-05, 变更字段: [{'field': 'amount', 'old_value': 65982.9, 'new_value': 65986.7}]
2025-06-08 21:15:19,390 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:19,920 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM871
2025-06-08 21:15:19,920 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_2025-05, 变更字段: [{'field': 'amount', 'old_value': 371698.2, 'new_value': 371705.66}]
2025-06-08 21:15:19,923 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:20,387 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMBM
2025-06-08 21:15:20,388 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9ETPVO71I0I86N3H2U18G001EHM_2025-05, 变更字段: [{'field': 'amount', 'old_value': 81467.5, 'new_value': 81469.7}]
2025-06-08 21:15:20,391 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:20,901 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM7M
2025-06-08 21:15:20,901 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_2025-05, 变更字段: [{'field': 'amount', 'old_value': 272690.86, 'new_value': 272691.46}]
2025-06-08 21:15:20,901 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:21,400 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM4M
2025-06-08 21:15:21,401 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVSS4V3460I86N3H2U12P001EBV_2025-05, 变更字段: [{'field': 'amount', 'old_value': 92207.51, 'new_value': 92211.6}]
2025-06-08 21:15:21,403 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:21,846 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM2M
2025-06-08 21:15:21,846 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_2025-05, 变更字段: [{'field': 'amount', 'old_value': 166431.48, 'new_value': 166439.2}]
2025-06-08 21:15:21,846 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:22,311 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM1M
2025-06-08 21:15:22,311 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_2025-05, 变更字段: [{'field': 'amount', 'old_value': 146943.84, 'new_value': 146955.11}]
2025-06-08 21:15:22,311 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:22,767 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AM0M
2025-06-08 21:15:22,768 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_2025-05, 变更字段: [{'field': 'amount', 'old_value': 213469.68, 'new_value': 213473.64}]
2025-06-08 21:15:22,769 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:23,205 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMZL
2025-06-08 21:15:23,206 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSK489TE20I86N3H2U114001EAA_2025-05, 变更字段: [{'field': 'amount', 'old_value': 135749.5, 'new_value': 135750.91}]
2025-06-08 21:15:23,206 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:23,687 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYL
2025-06-08 21:15:23,688 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_2025-05, 变更字段: [{'field': 'amount', 'old_value': 112261.62, 'new_value': 112267.12}]
2025-06-08 21:15:23,690 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:24,090 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMWL
2025-06-08 21:15:24,090 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDRR6FJ7A60I86N3H2U10L001E9R_2025-05, 变更字段: [{'field': 'amount', 'old_value': 98806.46, 'new_value': 98810.33}]
2025-06-08 21:15:24,099 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-08 21:15:24,613 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMBK
2025-06-08 21:15:24,614 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_2025-06, 变更字段: [{'field': 'amount', 'old_value': 46108.7, 'new_value': 46075.71}]
2025-06-08 21:15:24,629 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-08 21:15:25,079 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM2S
2025-06-08 21:15:25,079 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_2025-06, 变更字段: [{'field': 'amount', 'old_value': 13137.99, 'new_value': 13125.38}]
2025-06-08 21:15:25,089 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-08 21:15:25,499 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMPS
2025-06-08 21:15:25,500 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 41726.88, 'new_value': 41729.88}, {'field': 'amount', 'old_value': 41726.88, 'new_value': 41729.47}, {'field': 'count', 'old_value': 1934, 'new_value': 1935}, {'field': 'onlineAmount', 'old_value': 31577.31, 'new_value': 31580.31}, {'field': 'onlineCount', 'old_value': 1483, 'new_value': 1484}]
2025-06-08 21:15:25,520 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:15:26,057 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3N4ENXX9MY1
2025-06-08 21:15:26,058 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR_2025-04, 变更字段: [{'field': 'amount', 'old_value': 178959.77, 'new_value': 178960.4}]
2025-06-08 21:15:26,061 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:15:26,574 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3N4ENXX9M82
2025-06-08 21:15:26,574 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_2025-04, 变更字段: [{'field': 'amount', 'old_value': 115359.17, 'new_value': 115360.8}]
2025-06-08 21:15:26,576 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:15:27,028 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3N4ENXX9M62
2025-06-08 21:15:27,028 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_2025-04, 变更字段: [{'field': 'amount', 'old_value': 72800.56, 'new_value': 72802.65}]
2025-06-08 21:15:27,031 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:15:27,467 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3N4ENXX9M12
2025-06-08 21:15:27,468 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_2025-04, 变更字段: [{'field': 'amount', 'old_value': 411889.61, 'new_value': 411890.55}]
2025-06-08 21:15:27,470 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:27,888 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMP81
2025-06-08 21:15:27,888 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_2025-05, 变更字段: [{'field': 'amount', 'old_value': 154229.29, 'new_value': 154235.1}]
2025-06-08 21:15:27,890 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:28,351 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMO81
2025-06-08 21:15:28,352 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V_2025-05, 变更字段: [{'field': 'amount', 'old_value': 195863.07, 'new_value': 195863.47}]
2025-06-08 21:15:28,353 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:28,764 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMN81
2025-06-08 21:15:28,765 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_2025-05, 变更字段: [{'field': 'amount', 'old_value': 96703.86, 'new_value': 96717.99}]
2025-06-08 21:15:28,766 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:29,230 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMM81
2025-06-08 21:15:29,230 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_2025-05, 变更字段: [{'field': 'amount', 'old_value': 184859.75, 'new_value': 184862.54}]
2025-06-08 21:15:29,231 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:29,779 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AML81
2025-06-08 21:15:29,779 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_2025-05, 变更字段: [{'field': 'amount', 'old_value': 45884.6, 'new_value': 45888.6}]
2025-06-08 21:15:29,781 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:30,261 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMJ81
2025-06-08 21:15:30,262 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_2025-05, 变更字段: [{'field': 'amount', 'old_value': 614946.8, 'new_value': 614959.97}]
2025-06-08 21:15:30,263 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:30,674 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMI81
2025-06-08 21:15:30,674 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0BI1TJFBK3IKSIOCDI7LH001FSI_2025-05, 变更字段: [{'field': 'amount', 'old_value': 36425.06, 'new_value': 36425.86}]
2025-06-08 21:15:30,674 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:31,078 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMH81
2025-06-08 21:15:31,078 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR_2025-05, 变更字段: [{'field': 'amount', 'old_value': 232877.73, 'new_value': 232878.65}]
2025-06-08 21:15:31,078 - WARNING - 时间戳字符串包含非数字字符: 2025-01
2025-06-08 21:15:31,467 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3N4ENXX9MY2
2025-06-08 21:15:31,467 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_2025-01, 变更字段: [{'field': 'shopEntityName', 'old_value': '猴新奇', 'new_value': '多经- 猴新奇零食'}]
2025-06-08 21:15:31,467 - WARNING - 时间戳字符串包含非数字字符: 2025-01
2025-06-08 21:15:31,949 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3N4ENXX9M03
2025-06-08 21:15:31,949 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_2025-01, 变更字段: [{'field': 'shopEntityName', 'old_value': '酵社', 'new_value': '酵社面包'}]
2025-06-08 21:15:31,965 - WARNING - 时间戳字符串包含非数字字符: 2025-01
2025-06-08 21:15:32,441 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3N4ENXX9MG2
2025-06-08 21:15:32,441 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPK0KE3MN6E7AERKQ83JB001UND_2025-01, 变更字段: [{'field': 'amount', 'old_value': 214147.82, 'new_value': 214148.09}]
2025-06-08 21:15:32,443 - WARNING - 时间戳字符串包含非数字字符: 2025-01
2025-06-08 21:15:32,909 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3N4ENXX9MH2
2025-06-08 21:15:32,909 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-01, 变更字段: [{'field': 'shopEntityName', 'old_value': '泡鲜森', 'new_value': '泡鲜森·新食感汤泡饭'}]
2025-06-08 21:15:32,910 - WARNING - 时间戳字符串包含非数字字符: 2025-01
2025-06-08 21:15:33,384 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3N4ENXX9MI2
2025-06-08 21:15:33,384 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_2025-01, 变更字段: [{'field': 'shopEntityName', 'old_value': '歌志轩名古屋拉面', 'new_value': '歌志轩·名古屋拉面'}]
2025-06-08 21:15:33,386 - WARNING - 时间戳字符串包含非数字字符: 2025-01
2025-06-08 21:15:33,831 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3N4ENXX9ML2
2025-06-08 21:15:33,831 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_2025-01, 变更字段: [{'field': 'shopEntityName', 'old_value': '茶二', 'new_value': '茶二·现萃茶'}]
2025-06-08 21:15:33,831 - WARNING - 时间戳字符串包含非数字字符: 2025-01
2025-06-08 21:15:34,313 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3N4ENXX9MN2
2025-06-08 21:15:34,313 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_2025-01, 变更字段: [{'field': 'shopEntityName', 'old_value': '墨拿', 'new_value': 'mo labo coffee'}]
2025-06-08 21:15:34,313 - WARNING - 时间戳字符串包含非数字字符: 2025-01
2025-06-08 21:15:34,766 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3N4ENXX9MO2
2025-06-08 21:15:34,766 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_2025-01, 变更字段: [{'field': 'shopEntityName', 'old_value': '皮爷咖啡', 'new_value': "Peet's Coffee"}]
2025-06-08 21:15:34,766 - WARNING - 时间戳字符串包含非数字字符: 2025-01
2025-06-08 21:15:35,281 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3N4ENXX9MP2
2025-06-08 21:15:35,281 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_2025-01, 变更字段: [{'field': 'shopEntityName', 'old_value': '胖哥俩', 'new_value': '胖哥俩肉蟹煲'}]
2025-06-08 21:15:35,281 - WARNING - 时间戳字符串包含非数字字符: 2025-01
2025-06-08 21:15:35,748 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3N4ENXX9MW2
2025-06-08 21:15:35,748 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISLI3BD9P7AV8LHQQGIDR001EK4_2025-01, 变更字段: [{'field': 'shopEntityName', 'old_value': '半岛名轩', 'new_value': '半岛·优食坊蒸汽海鲜'}]
2025-06-08 21:15:35,764 - WARNING - 时间戳字符串包含非数字字符: 2025-01
2025-06-08 21:15:36,133 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3N4ENXX9MX2
2025-06-08 21:15:36,133 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_2025-01, 变更字段: [{'field': 'shopEntityName', 'old_value': '橘焱胡同烧肉', 'new_value': '橘焱胡同烤肉'}]
2025-06-08 21:15:36,136 - WARNING - 时间戳字符串包含非数字字符: 2025-02
2025-06-08 21:15:36,541 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9MF3
2025-06-08 21:15:36,541 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_2025-02, 变更字段: [{'field': 'shopEntityName', 'old_value': '皮爷咖啡', 'new_value': "Peet's Coffee"}]
2025-06-08 21:15:36,541 - WARNING - 时间戳字符串包含非数字字符: 2025-02
2025-06-08 21:15:36,913 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9ME3
2025-06-08 21:15:36,914 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_2025-02, 变更字段: [{'field': 'shopEntityName', 'old_value': '墨拿', 'new_value': 'mo labo coffee'}]
2025-06-08 21:15:36,915 - WARNING - 时间戳字符串包含非数字字符: 2025-02
2025-06-08 21:15:37,352 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9MC3
2025-06-08 21:15:37,353 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_2025-02, 变更字段: [{'field': 'shopEntityName', 'old_value': '茶二', 'new_value': '茶二·现萃茶'}]
2025-06-08 21:15:37,358 - WARNING - 时间戳字符串包含非数字字符: 2025-02
2025-06-08 21:15:37,768 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9MG3
2025-06-08 21:15:37,768 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_2025-02, 变更字段: [{'field': 'shopEntityName', 'old_value': '胖哥俩', 'new_value': '胖哥俩肉蟹煲'}]
2025-06-08 21:15:37,768 - WARNING - 时间戳字符串包含非数字字符: 2025-02
2025-06-08 21:15:38,299 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9M93
2025-06-08 21:15:38,299 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_2025-02, 变更字段: [{'field': 'shopEntityName', 'old_value': '歌志轩名古屋拉面', 'new_value': '歌志轩·名古屋拉面'}]
2025-06-08 21:15:38,301 - WARNING - 时间戳字符串包含非数字字符: 2025-02
2025-06-08 21:15:38,688 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9M83
2025-06-08 21:15:38,688 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-02, 变更字段: [{'field': 'shopEntityName', 'old_value': '泡鲜森', 'new_value': '泡鲜森·新食感汤泡饭'}]
2025-06-08 21:15:38,688 - WARNING - 时间戳字符串包含非数字字符: 2025-02
2025-06-08 21:15:39,077 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9MR3
2025-06-08 21:15:39,077 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_2025-02, 变更字段: [{'field': 'shopEntityName', 'old_value': '酵社', 'new_value': '酵社面包'}]
2025-06-08 21:15:39,077 - WARNING - 时间戳字符串包含非数字字符: 2025-02
2025-06-08 21:15:39,483 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9MP3
2025-06-08 21:15:39,498 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_2025-02, 变更字段: [{'field': 'shopEntityName', 'old_value': '猴新奇', 'new_value': '多经- 猴新奇零食'}]
2025-06-08 21:15:39,498 - WARNING - 时间戳字符串包含非数字字符: 2025-02
2025-06-08 21:15:40,184 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9MO3
2025-06-08 21:15:40,185 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_2025-02, 变更字段: [{'field': 'shopEntityName', 'old_value': '橘焱胡同烧肉', 'new_value': '橘焱胡同烤肉'}]
2025-06-08 21:15:40,186 - WARNING - 时间戳字符串包含非数字字符: 2025-02
2025-06-08 21:15:40,684 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9MN3
2025-06-08 21:15:40,684 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISLI3BD9P7AV8LHQQGIDR001EK4_2025-02, 变更字段: [{'field': 'shopEntityName', 'old_value': '半岛名轩', 'new_value': '半岛·优食坊蒸汽海鲜'}]
2025-06-08 21:15:40,688 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-08 21:15:41,095 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9MC4
2025-06-08 21:15:41,095 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_2025-03, 变更字段: [{'field': 'recommendAmount', 'old_value': 998246.5, 'new_value': 993281.53}, {'field': 'dailyBillAmount', 'old_value': 998246.5, 'new_value': 993281.53}, {'field': 'amount', 'old_value': 888592.74, 'new_value': 888593.18}]
2025-06-08 21:15:41,101 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-08 21:15:41,549 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9MD4
2025-06-08 21:15:41,549 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISLI3BD9P7AV8LHQQGIDR001EK4_2025-03, 变更字段: [{'field': 'shopEntityName', 'old_value': '半岛名轩', 'new_value': '半岛·优食坊蒸汽海鲜'}]
2025-06-08 21:15:41,550 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-08 21:15:42,000 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9ME4
2025-06-08 21:15:42,000 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_2025-03, 变更字段: [{'field': 'shopEntityName', 'old_value': '橘焱胡同烧肉', 'new_value': '橘焱胡同烤肉'}]
2025-06-08 21:15:42,000 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-08 21:15:42,500 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9MF4
2025-06-08 21:15:42,500 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_2025-03, 变更字段: [{'field': 'shopEntityName', 'old_value': '猴新奇', 'new_value': '多经- 猴新奇零食'}]
2025-06-08 21:15:42,502 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-08 21:15:42,979 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9MH4
2025-06-08 21:15:42,980 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_2025-03, 变更字段: [{'field': 'shopEntityName', 'old_value': '酵社', 'new_value': '酵社面包'}]
2025-06-08 21:15:42,982 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-08 21:15:43,451 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9M54
2025-06-08 21:15:43,451 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_2025-03, 变更字段: [{'field': 'shopEntityName', 'old_value': '墨拿', 'new_value': 'mo labo coffee'}]
2025-06-08 21:15:43,453 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-08 21:15:43,930 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9MY3
2025-06-08 21:15:43,930 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPK0KE3MN6E7AERKQ83JB001UND_2025-03, 变更字段: [{'field': 'amount', 'old_value': 200468.5, 'new_value': 200472.97}]
2025-06-08 21:15:43,932 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-08 21:15:44,328 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9MZ3
2025-06-08 21:15:44,328 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-03, 变更字段: [{'field': 'shopEntityName', 'old_value': '泡鲜森', 'new_value': '泡鲜森·新食感汤泡饭'}]
2025-06-08 21:15:44,328 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-08 21:15:44,748 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9M04
2025-06-08 21:15:44,748 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_2025-03, 变更字段: [{'field': 'shopEntityName', 'old_value': '歌志轩名古屋拉面', 'new_value': '歌志轩·名古屋拉面'}]
2025-06-08 21:15:44,763 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-08 21:15:45,228 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9M34
2025-06-08 21:15:45,228 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_2025-03, 变更字段: [{'field': 'shopEntityName', 'old_value': '茶二', 'new_value': '茶二·现萃茶'}]
2025-06-08 21:15:45,228 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-08 21:15:45,697 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9M64
2025-06-08 21:15:45,697 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_2025-03, 变更字段: [{'field': 'shopEntityName', 'old_value': '皮爷咖啡', 'new_value': "Peet's Coffee"}]
2025-06-08 21:15:45,697 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-08 21:15:46,147 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9M74
2025-06-08 21:15:46,148 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_2025-03, 变更字段: [{'field': 'shopEntityName', 'old_value': '胖哥俩', 'new_value': '胖哥俩肉蟹煲'}]
2025-06-08 21:15:46,150 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:15:46,572 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9ML4
2025-06-08 21:15:46,572 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPGJC2SUF6E7AERKQ83J8001UNA_2025-04, 变更字段: [{'field': 'amount', 'old_value': 359447.72, 'new_value': 359448.11}]
2025-06-08 21:15:46,572 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:15:47,061 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9MM4
2025-06-08 21:15:47,062 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPK0KE3MN6E7AERKQ83JB001UND_2025-04, 变更字段: [{'field': 'amount', 'old_value': 158267.0, 'new_value': 158282.85}]
2025-06-08 21:15:47,063 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:15:47,550 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9MN4
2025-06-08 21:15:47,550 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-04, 变更字段: [{'field': 'shopEntityName', 'old_value': '泡鲜森', 'new_value': '泡鲜森·新食感汤泡饭'}]
2025-06-08 21:15:47,550 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:15:48,012 - INFO - 更新表单数据成功: FINST-OLF665813NVUOH19CCHMM8B7MMTW2NSGNXX9MRA
2025-06-08 21:15:48,012 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_2025-04, 变更字段: [{'field': 'amount', 'old_value': 72753.89, 'new_value': 72755.24}, {'field': 'shopEntityName', 'old_value': '茶二', 'new_value': '茶二·现萃茶'}]
2025-06-08 21:15:48,014 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:15:48,420 - INFO - 更新表单数据成功: FINST-OLF665813NVUOH19CCHMM8B7MMTW2NSGNXX9MUA
2025-06-08 21:15:48,420 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_2025-04, 变更字段: [{'field': 'amount', 'old_value': 251609.22, 'new_value': 251609.38}, {'field': 'shopEntityName', 'old_value': '皮爷咖啡', 'new_value': "Peet's Coffee"}]
2025-06-08 21:15:48,420 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:15:48,833 - INFO - 更新表单数据成功: FINST-OLF665813NVUOH19CCHMM8B7MMTW2NSGNXX9MVA
2025-06-08 21:15:48,833 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_2025-04, 变更字段: [{'field': 'shopEntityName', 'old_value': '胖哥俩', 'new_value': '胖哥俩肉蟹煲'}]
2025-06-08 21:15:48,836 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:15:49,225 - INFO - 更新表单数据成功: FINST-OLF665813NVUOH19CCHMM8B7MMTW2NSGNXX9MYA
2025-06-08 21:15:49,225 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_2025-04, 变更字段: [{'field': 'amount', 'old_value': 822387.68, 'new_value': 822393.79}]
2025-06-08 21:15:49,225 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:15:49,706 - INFO - 更新表单数据成功: FINST-OLF665813NVUOH19CCHMM8B7MMTW2NSGNXX9M1B
2025-06-08 21:15:49,706 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISLI3BD9P7AV8LHQQGIDR001EK4_2025-04, 变更字段: [{'field': 'shopEntityName', 'old_value': '半岛名轩', 'new_value': '半岛·优食坊蒸汽海鲜'}]
2025-06-08 21:15:49,706 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:15:50,145 - INFO - 更新表单数据成功: FINST-OLF665813NVUOH19CCHMM8B7MMTW2NSGNXX9M2B
2025-06-08 21:15:50,145 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_2025-04, 变更字段: [{'field': 'amount', 'old_value': 235729.98, 'new_value': 235730.03}, {'field': 'shopEntityName', 'old_value': '橘焱胡同烧肉', 'new_value': '橘焱胡同烤肉'}]
2025-06-08 21:15:50,160 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:15:50,642 - INFO - 更新表单数据成功: FINST-OLF665813NVUOH19CCHMM8B7MMTW2NSGNXX9M3B
2025-06-08 21:15:50,642 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_2025-04, 变更字段: [{'field': 'shopEntityName', 'old_value': '猴新奇', 'new_value': '多经- 猴新奇零食'}]
2025-06-08 21:15:50,644 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:15:51,059 - INFO - 更新表单数据成功: FINST-OLF665813NVUOH19CCHMM8B7MMTW2NSGNXX9M5B
2025-06-08 21:15:51,060 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_2025-04, 变更字段: [{'field': 'amount', 'old_value': 449548.79, 'new_value': 449549.59}, {'field': 'shopEntityName', 'old_value': '酵社', 'new_value': '酵社面包'}]
2025-06-08 21:15:51,061 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:15:51,471 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9MI4
2025-06-08 21:15:51,471 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_2025-04, 变更字段: [{'field': 'amount', 'old_value': 47171.45, 'new_value': 47173.1}]
2025-06-08 21:15:51,473 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:15:51,883 - INFO - 更新表单数据成功: FINST-OLF665813NVUOH19CCHMM8B7MMTW2NSGNXX9MTA
2025-06-08 21:15:51,884 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_2025-04, 变更字段: [{'field': 'amount', 'old_value': 71937.27, 'new_value': 71945.09}, {'field': 'shopEntityName', 'old_value': '墨拿', 'new_value': 'mo labo coffee'}]
2025-06-08 21:15:51,885 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:52,266 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM491
2025-06-08 21:15:52,267 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_2025-05, 变更字段: [{'field': 'amount', 'old_value': 689122.02, 'new_value': 689128.61}]
2025-06-08 21:15:52,269 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:52,676 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM291
2025-06-08 21:15:52,676 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRGKS2JA27AV8LHQQGIDC001EJL_2025-05, 变更字段: [{'field': 'amount', 'old_value': 1327952.96, 'new_value': 1327953.46}]
2025-06-08 21:15:52,678 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:53,158 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMB91
2025-06-08 21:15:53,158 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_2025-05, 变更字段: [{'field': 'amount', 'old_value': 422561.68, 'new_value': 422564.02}, {'field': 'shopEntityName', 'old_value': '酵社', 'new_value': '酵社面包'}]
2025-06-08 21:15:53,159 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:53,573 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM091
2025-06-08 21:15:53,574 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_2025-05, 变更字段: [{'field': 'shopEntityName', 'old_value': '皮爷咖啡', 'new_value': "Peet's Coffee"}]
2025-06-08 21:15:53,575 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:53,987 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMZ81
2025-06-08 21:15:53,987 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_2025-05, 变更字段: [{'field': 'amount', 'old_value': 59478.19, 'new_value': 59479.09}, {'field': 'shopEntityName', 'old_value': '墨拿', 'new_value': 'mo labo coffee'}]
2025-06-08 21:15:53,987 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:54,453 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMX81
2025-06-08 21:15:54,453 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_2025-05, 变更字段: [{'field': 'amount', 'old_value': 49811.64, 'new_value': 49812.48}, {'field': 'shopEntityName', 'old_value': '茶二', 'new_value': '茶二·现萃茶'}]
2025-06-08 21:15:54,453 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:54,895 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMW81
2025-06-08 21:15:54,896 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQ716B84L7AV8LHQQGID0001EJ9_2025-05, 变更字段: [{'field': 'amount', 'old_value': 126467.24, 'new_value': 126468.33}]
2025-06-08 21:15:54,898 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:55,294 - INFO - 更新表单数据成功: FINST-VRA66VA1DNZUAADJA7KJPA7PQK7Q37OC8W8AM7W
2025-06-08 21:15:55,294 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-05, 变更字段: [{'field': 'amount', 'old_value': 7326.42, 'new_value': 7327.16}, {'field': 'shopEntityName', 'old_value': '泡鲜森', 'new_value': '泡鲜森·新食感汤泡饭'}]
2025-06-08 21:15:55,294 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:55,793 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMU81
2025-06-08 21:15:55,793 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPK0KE3MN6E7AERKQ83JB001UND_2025-05, 变更字段: [{'field': 'amount', 'old_value': 167110.49, 'new_value': 167116.36}]
2025-06-08 21:15:55,793 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:56,197 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM191
2025-06-08 21:15:56,197 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_2025-05, 变更字段: [{'field': 'amount', 'old_value': 811859.5, 'new_value': 811862.7}, {'field': 'shopEntityName', 'old_value': '胖哥俩', 'new_value': '胖哥俩肉蟹煲'}]
2025-06-08 21:15:56,197 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:56,617 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR81
2025-06-08 21:15:56,632 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIP3GSKTFR6E7AERKQ83J2001UN4_2025-05, 变更字段: [{'field': 'amount', 'old_value': 70181.16, 'new_value': 70183.76}]
2025-06-08 21:15:56,632 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:57,053 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMQ81
2025-06-08 21:15:57,053 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_2025-05, 变更字段: [{'field': 'amount', 'old_value': 34591.85, 'new_value': 34595.21}]
2025-06-08 21:15:57,053 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:57,474 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM991
2025-06-08 21:15:57,474 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_2025-05, 变更字段: [{'field': 'shopEntityName', 'old_value': '猴新奇', 'new_value': '多经- 猴新奇零食'}]
2025-06-08 21:15:57,474 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:57,881 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM891
2025-06-08 21:15:57,881 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_2025-05, 变更字段: [{'field': 'amount', 'old_value': 176357.75, 'new_value': 176359.46}, {'field': 'shopEntityName', 'old_value': '橘焱胡同烧肉', 'new_value': '橘焱胡同烤肉'}]
2025-06-08 21:15:57,883 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:58,359 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM791
2025-06-08 21:15:58,360 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISLI3BD9P7AV8LHQQGIDR001EK4_2025-05, 变更字段: [{'field': 'shopEntityName', 'old_value': '半岛名轩', 'new_value': '半岛·优食坊蒸汽海鲜'}]
2025-06-08 21:15:58,362 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:58,882 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM691
2025-06-08 21:15:58,883 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_2025-05, 变更字段: [{'field': 'amount', 'old_value': 940958.14, 'new_value': 940965.6}]
2025-06-08 21:15:58,884 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:15:59,358 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM591
2025-06-08 21:15:59,358 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS34G4B697AV8LHQQGIDL001EJU_2025-05, 变更字段: [{'field': 'amount', 'old_value': 924151.32, 'new_value': 924152.64}]
2025-06-08 21:15:59,384 - WARNING - 时间戳字符串包含非数字字符: 2025-01
2025-06-08 21:15:59,840 - INFO - 更新表单数据成功: FINST-OLF665813NVUOH19CCHMM8B7MMTW2OSGNXX9M9B
2025-06-08 21:15:59,840 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_2025-01, 变更字段: [{'field': 'shopEntityName', 'old_value': '湘颂·国藩家宴', 'new_value': '湘颂'}]
2025-06-08 21:15:59,840 - WARNING - 时间戳字符串包含非数字字符: 2025-02
2025-06-08 21:16:00,293 - INFO - 更新表单数据成功: FINST-OLF665813NVUOH19CCHMM8B7MMTW2OSGNXX9MJB
2025-06-08 21:16:00,293 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_2025-02, 变更字段: [{'field': 'shopEntityName', 'old_value': '湘颂·国藩家宴', 'new_value': '湘颂'}]
2025-06-08 21:16:00,293 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-08 21:16:00,855 - INFO - 更新表单数据成功: FINST-OLF665813NVUOH19CCHMM8B7MMTW2OSGNXX9MTB
2025-06-08 21:16:00,855 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_2025-03, 变更字段: [{'field': 'shopEntityName', 'old_value': '湘颂·国藩家宴', 'new_value': '湘颂'}]
2025-06-08 21:16:00,872 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:16:01,290 - INFO - 更新表单数据成功: FINST-OLF665813NVUOH19CCHMM8B7MMTW2OSGNXX9M7C
2025-06-08 21:16:01,290 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_2025-04, 变更字段: [{'field': 'amount', 'old_value': 689655.35, 'new_value': 689655.55}]
2025-06-08 21:16:01,290 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:16:01,836 - INFO - 更新表单数据成功: FINST-OLF665813NVUOH19CCHMM8B7MMTW2OSGNXX9M3C
2025-06-08 21:16:01,836 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_2025-04, 变更字段: [{'field': 'amount', 'old_value': 917363.0, 'new_value': 917365.1}]
2025-06-08 21:16:01,852 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:16:02,319 - INFO - 更新表单数据成功: FINST-OLF665813NVUOH19CCHMM8B7MMTW2OSGNXX9M1C
2025-06-08 21:16:02,319 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD0GVFB5C86AJB6QM8HA590011O7_2025-04, 变更字段: [{'field': 'amount', 'old_value': 866306.9, 'new_value': 866307.42}]
2025-06-08 21:16:02,335 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:16:02,786 - INFO - 更新表单数据成功: FINST-OLF665813NVUOH19CCHMM8B7MMTW2OSGNXX9M2C
2025-06-08 21:16:02,786 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD2F2MCTBD6AJB6QM8HA650011P3_2025-04, 变更字段: [{'field': 'amount', 'old_value': 1361724.38, 'new_value': 1361724.98}]
2025-06-08 21:16:02,786 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:16:03,223 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMH91
2025-06-08 21:16:03,223 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK_2025-05, 变更字段: [{'field': 'amount', 'old_value': 360297.67, 'new_value': 360298.46}]
2025-06-08 21:16:03,223 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:16:03,654 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMI91
2025-06-08 21:16:03,655 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2PLB45826AJB6QM8HA7Q0011QO_2025-05, 变更字段: [{'field': 'amount', 'old_value': 1288893.61, 'new_value': 1288894.26}]
2025-06-08 21:16:03,656 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:16:04,143 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMJ91
2025-06-08 21:16:04,143 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_2025-05, 变更字段: [{'field': 'amount', 'old_value': 857792.9, 'new_value': 857794.3}]
2025-06-08 21:16:04,143 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:16:04,548 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMF91
2025-06-08 21:16:04,548 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_2025-05, 变更字段: [{'field': 'amount', 'old_value': 991685.2, 'new_value': 991692.56}]
2025-06-08 21:16:04,548 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:16:05,063 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMD91
2025-06-08 21:16:05,063 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD0GVFB5C86AJB6QM8HA590011O7_2025-05, 变更字段: [{'field': 'amount', 'old_value': 872814.49, 'new_value': 872814.6}]
2025-06-08 21:16:05,063 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:16:05,452 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AME91
2025-06-08 21:16:05,452 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD2F2MCTBD6AJB6QM8HA650011P3_2025-05, 变更字段: [{'field': 'amount', 'old_value': 1539272.78, 'new_value': 1539274.13}]
2025-06-08 21:16:05,452 - WARNING - 时间戳字符串包含非数字字符: 2025-01
2025-06-08 21:16:05,895 - INFO - 更新表单数据成功: FINST-OLF665813NVUOH19CCHMM8B7MMTW2PSGNXX9MVC
2025-06-08 21:16:05,896 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_2025-01, 变更字段: [{'field': 'shopEntityName', 'old_value': '全食物种-A', 'new_value': '全食物种'}]
2025-06-08 21:16:05,903 - WARNING - 时间戳字符串包含非数字字符: 2025-01
2025-06-08 21:16:06,280 - INFO - 更新表单数据成功: FINST-OLF665813NVUOH19CCHMM8B7MMTW2OSGNXX9MLC
2025-06-08 21:16:06,280 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_2025-01, 变更字段: [{'field': 'shopEntityName', 'old_value': '恰饭英雄', 'new_value': '恰饭叔'}]
2025-06-08 21:16:06,282 - WARNING - 时间戳字符串包含非数字字符: 2025-02
2025-06-08 21:16:06,668 - INFO - 更新表单数据成功: FINST-OLF665813NVUOH19CCHMM8B7MMTW2PSGNXX9MCD
2025-06-08 21:16:06,668 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_2025-02, 变更字段: [{'field': 'shopEntityName', 'old_value': '恰饭英雄', 'new_value': '恰饭叔'}]
2025-06-08 21:16:06,683 - WARNING - 时间戳字符串包含非数字字符: 2025-02
2025-06-08 21:16:07,181 - INFO - 更新表单数据成功: FINST-X0G66U8100WULBDP7CXOPDDF85T830DJNXX9MB4
2025-06-08 21:16:07,181 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_2025-02, 变更字段: [{'field': 'shopEntityName', 'old_value': '全食物种-A', 'new_value': '全食物种'}]
2025-06-08 21:16:07,197 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-08 21:16:07,586 - INFO - 更新表单数据成功: FINST-X0G66U8100WULBDP7CXOPDDF85T830DJNXX9MS4
2025-06-08 21:16:07,601 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_2025-03, 变更字段: [{'field': 'shopEntityName', 'old_value': '恰饭英雄', 'new_value': '恰饭叔'}]
2025-06-08 21:16:07,601 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-08 21:16:08,130 - INFO - 更新表单数据成功: FINST-X0G66U8100WULBDP7CXOPDDF85T830DJNXX9M15
2025-06-08 21:16:08,130 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_2025-03, 变更字段: [{'field': 'shopEntityName', 'old_value': '全食物种-A', 'new_value': '全食物种'}]
2025-06-08 21:16:08,130 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:16:08,524 - INFO - 更新表单数据成功: FINST-X0G66U8100WULBDP7CXOPDDF85T831DJNXX9MI5
2025-06-08 21:16:08,524 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_2025-04, 变更字段: [{'field': 'shopEntityName', 'old_value': '恰饭英雄', 'new_value': '恰饭叔'}]
2025-06-08 21:16:08,524 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:16:08,951 - INFO - 更新表单数据成功: FINST-X0G66U8100WULBDP7CXOPDDF85T831DJNXX9MM5
2025-06-08 21:16:08,951 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_2025-04, 变更字段: [{'field': 'amount', 'old_value': 79108.49, 'new_value': 79110.39}]
2025-06-08 21:16:08,959 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-08 21:16:09,357 - INFO - 更新表单数据成功: FINST-X0G66U8100WULBDP7CXOPDDF85T831DJNXX9MB5
2025-06-08 21:16:09,372 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_2025-04, 变更字段: [{'field': 'amount', 'old_value': 233865.92, 'new_value': 233867.95}]
2025-06-08 21:16:09,377 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:16:09,729 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMQ91
2025-06-08 21:16:09,729 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_2025-05, 变更字段: [{'field': 'amount', 'old_value': 289874.36, 'new_value': 289877.67}]
2025-06-08 21:16:09,729 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:16:10,242 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMR91
2025-06-08 21:16:10,258 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMMCI75GD42F6DB81RH81001P49_2025-05, 变更字段: [{'field': 'amount', 'old_value': 60588.8, 'new_value': 60589.2}]
2025-06-08 21:16:10,258 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:16:10,652 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT91
2025-06-08 21:16:10,652 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_2025-05, 变更字段: [{'field': 'amount', 'old_value': 61829.11, 'new_value': 61830.01}, {'field': 'shopEntityName', 'old_value': '恰饭英雄', 'new_value': '恰饭叔'}]
2025-06-08 21:16:10,652 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:16:11,055 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMT7
2025-06-08 21:16:11,055 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_2025-05, 变更字段: [{'field': 'amount', 'old_value': 100045.0, 'new_value': 100046.8}, {'field': 'onlineAmount', 'old_value': 67046.53, 'new_value': 67046.55}]
2025-06-08 21:16:11,058 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:16:11,572 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM38
2025-06-08 21:16:11,572 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_A48FB8F8F66644F59454F3E73DFCEB92_2025-05, 变更字段: [{'field': 'amount', 'old_value': 304191.5, 'new_value': 304192.1}]
2025-06-08 21:16:11,572 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:16:11,976 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AM28
2025-06-08 21:16:11,976 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6FEB527E4B354363BD1420A3FF0FB3E3_2025-05, 变更字段: [{'field': 'amount', 'old_value': 80947.55, 'new_value': 80948.2}]
2025-06-08 21:16:11,977 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:16:12,405 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AML91
2025-06-08 21:16:12,405 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_2025-05, 变更字段: [{'field': 'amount', 'old_value': 177502.14, 'new_value': 177512.44}]
2025-06-08 21:16:12,406 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:16:12,911 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMM91
2025-06-08 21:16:12,911 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_2025-05, 变更字段: [{'field': 'amount', 'old_value': 249163.0, 'new_value': 249163.62}]
2025-06-08 21:16:12,911 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:16:13,347 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMEC
2025-06-08 21:16:13,347 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_7987833E6DE549FCBAC0AAF7A1D27E61_2025-05, 变更字段: [{'field': 'amount', 'old_value': 71788.32, 'new_value': 71789.62}]
2025-06-08 21:16:13,363 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:16:13,768 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMO91
2025-06-08 21:16:13,768 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_2025-05, 变更字段: [{'field': 'amount', 'old_value': 56587.9, 'new_value': 56591.1}]
2025-06-08 21:16:13,768 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:16:14,280 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMY7
2025-06-08 21:16:14,280 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_2025-05, 变更字段: [{'field': 'amount', 'old_value': 6113.76, 'new_value': 6115.39}]
2025-06-08 21:16:14,281 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-08 21:16:14,799 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMX7
2025-06-08 21:16:14,799 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_2025-05, 变更字段: [{'field': 'amount', 'old_value': 74740.0, 'new_value': 74740.7}]
2025-06-08 21:16:14,810 - INFO - 月销售数据同步完成！更新: 272 条，插入: 0 条，错误: 0 条，跳过: 1131 条
2025-06-08 21:16:14,811 - INFO - 综合数据同步流程完成！
2025-06-08 21:16:14,896 - INFO - 综合数据同步完成
2025-06-08 21:16:14,896 - INFO - MySQL数据库连接已关闭
2025-06-08 21:16:14,896 - INFO - ==================================================
2025-06-08 21:16:14,897 - INFO - 程序退出
2025-06-08 21:16:14,897 - INFO - ==================================================
