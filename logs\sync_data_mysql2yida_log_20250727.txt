2025-07-27 01:30:33,665 - INFO - 使用默认增量同步（当天更新数据）
2025-07-27 01:30:33,665 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-27 01:30:33,665 - INFO - 查询参数: ('2025-07-27',)
2025-07-27 01:30:33,821 - INFO - MySQL查询成功，增量数据（日期: 2025-07-27），共获取 5 条记录
2025-07-27 01:30:33,821 - INFO - 获取到 2 个日期需要处理: ['2025-07-26', '2025-07-27']
2025-07-27 01:30:33,821 - INFO - 开始处理日期: 2025-07-26
2025-07-27 01:30:33,821 - INFO - Request Parameters - Page 1:
2025-07-27 01:30:33,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 01:30:33,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 01:30:41,509 - INFO - Response - Page 1:
2025-07-27 01:30:41,509 - INFO - 第 1 页获取到 36 条记录
2025-07-27 01:30:42,024 - INFO - 查询完成，共获取到 36 条记录
2025-07-27 01:30:42,024 - INFO - 获取到 36 条表单数据
2025-07-27 01:30:42,024 - INFO - 当前日期 2025-07-26 有 3 条MySQL数据需要处理
2025-07-27 01:30:42,024 - INFO - 开始批量插入 3 条新记录
2025-07-27 01:30:42,180 - INFO - 批量插入响应状态码: 200
2025-07-27 01:30:42,180 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 26 Jul 2025 17:30:41 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '156', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '53DB38CE-B2BA-77C4-B11C-169D21821E9B', 'x-acs-trace-id': '2cc3e43d9943cb9b167a4decfac44688', 'etag': '17FfI/w7IaRG4XFMcjCp8wg6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-27 01:30:42,180 - INFO - 批量插入响应体: {'result': ['FINST-K8C66U61DDHXGRDZEZXBED67PYJC2QDJ0JKDM7F', 'FINST-K8C66U61DDHXGRDZEZXBED67PYJC2QDJ0JKDM8F', 'FINST-K8C66U61DDHXGRDZEZXBED67PYJC2QDJ0JKDM9F']}
2025-07-27 01:30:42,180 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-07-27 01:30:42,180 - INFO - 成功插入的数据ID: ['FINST-K8C66U61DDHXGRDZEZXBED67PYJC2QDJ0JKDM7F', 'FINST-K8C66U61DDHXGRDZEZXBED67PYJC2QDJ0JKDM8F', 'FINST-K8C66U61DDHXGRDZEZXBED67PYJC2QDJ0JKDM9F']
2025-07-27 01:30:47,196 - INFO - 批量插入完成，共 3 条记录
2025-07-27 01:30:47,196 - INFO - 日期 2025-07-26 处理完成 - 更新: 0 条，插入: 3 条，错误: 0 条
2025-07-27 01:30:47,196 - INFO - 开始处理日期: 2025-07-27
2025-07-27 01:30:47,196 - INFO - Request Parameters - Page 1:
2025-07-27 01:30:47,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 01:30:47,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 01:30:55,305 - ERROR - 处理日期 2025-07-27 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 50C86958-4EC0-75D2-A0B3-4BAB1E5887AF Response: {'code': 'ServiceUnavailable', 'requestid': '50C86958-4EC0-75D2-A0B3-4BAB1E5887AF', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 50C86958-4EC0-75D2-A0B3-4BAB1E5887AF)
2025-07-27 01:30:55,305 - INFO - 数据同步完成！更新: 0 条，插入: 3 条，错误: 1 条
2025-07-27 01:31:55,321 - INFO - 开始同步昨天与今天的销售数据: 2025-07-26 至 2025-07-27
2025-07-27 01:31:55,321 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-27 01:31:55,321 - INFO - 查询参数: ('2025-07-26', '2025-07-27')
2025-07-27 01:31:55,477 - INFO - MySQL查询成功，时间段: 2025-07-26 至 2025-07-27，共获取 108 条记录
2025-07-27 01:31:55,477 - INFO - 获取到 2 个日期需要处理: ['2025-07-26', '2025-07-27']
2025-07-27 01:31:55,477 - INFO - 开始处理日期: 2025-07-26
2025-07-27 01:31:55,477 - INFO - Request Parameters - Page 1:
2025-07-27 01:31:55,477 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 01:31:55,477 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 01:31:56,211 - INFO - Response - Page 1:
2025-07-27 01:31:56,211 - INFO - 第 1 页获取到 39 条记录
2025-07-27 01:31:56,711 - INFO - 查询完成，共获取到 39 条记录
2025-07-27 01:31:56,711 - INFO - 获取到 39 条表单数据
2025-07-27 01:31:56,711 - INFO - 当前日期 2025-07-26 有 103 条MySQL数据需要处理
2025-07-27 01:31:56,711 - INFO - 开始批量插入 64 条新记录
2025-07-27 01:31:56,961 - INFO - 批量插入响应状态码: 200
2025-07-27 01:31:56,961 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 26 Jul 2025 17:31:56 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0AE89A61-E4C6-7AF6-8FB9-663730876F3D', 'x-acs-trace-id': 'ae43f34342650e087df4edc151e383ff', 'etag': '25U01gxRfjlhtnIMRXdFggw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-27 01:31:56,961 - INFO - 批量插入响应体: {'result': ['FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMMA', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMNA', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMOA', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMPA', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMQA', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMRA', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMSA', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMTA', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMUA', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMVA', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMWA', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMXA', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMYA', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMZA', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDM0B', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDM1B', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDM2B', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDM3B', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDM4B', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDM5B', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDM6B', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDM7B', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDM8B', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDM9B', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMAB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMBB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMCB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMDB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMEB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMFB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMGB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMHB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMIB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMJB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMKB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMLB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMMB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMNB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMOB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMPB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMQB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMRB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMSB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMTB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMUB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMVB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMWB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMXB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMYB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMZB']}
2025-07-27 01:31:56,961 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-27 01:31:56,961 - INFO - 成功插入的数据ID: ['FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMMA', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMNA', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMOA', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMPA', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMQA', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMRA', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMSA', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMTA', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMUA', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMVA', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMWA', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMXA', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMYA', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMZA', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDM0B', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDM1B', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDM2B', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDM3B', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDM4B', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDM5B', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDM6B', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDM7B', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDM8B', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDM9B', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMAB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMBB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMCB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMDB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMEB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMFB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMGB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMHB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMIB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMJB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMKB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMLB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMMB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMNB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMOB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMPB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMQB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMRB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMSB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMTB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMUB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMVB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMWB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMXB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMYB', 'FINST-4OD66CC1HJHX44DT8ZARQ9PSF6G731352JKDMZB']
2025-07-27 01:32:02,133 - INFO - 批量插入响应状态码: 200
2025-07-27 01:32:02,133 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 26 Jul 2025 17:32:01 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '684', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D2978EFF-1295-7291-B4EB-7838EE68125F', 'x-acs-trace-id': 'ab3fe6fc45315ce4b18cb006e9711dbe', 'etag': '6slGS+9dTwCSZsv6FRRoPtQ4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-27 01:32:02,133 - INFO - 批量插入响应体: {'result': ['FINST-1MD668B1LJHXSI9NDTP7I5JLLZYD3V292JKDMEG', 'FINST-1MD668B1LJHXSI9NDTP7I5JLLZYD3V292JKDMFG', 'FINST-1MD668B1LJHXSI9NDTP7I5JLLZYD3V292JKDMGG', 'FINST-1MD668B1LJHXSI9NDTP7I5JLLZYD3V292JKDMHG', 'FINST-1MD668B1LJHXSI9NDTP7I5JLLZYD3V292JKDMIG', 'FINST-1MD668B1LJHXSI9NDTP7I5JLLZYD3V292JKDMJG', 'FINST-1MD668B1LJHXSI9NDTP7I5JLLZYD3V292JKDMKG', 'FINST-1MD668B1LJHXSI9NDTP7I5JLLZYD3V292JKDMLG', 'FINST-1MD668B1LJHXSI9NDTP7I5JLLZYD3V292JKDMMG', 'FINST-1MD668B1LJHXSI9NDTP7I5JLLZYD3V292JKDMNG', 'FINST-1MD668B1LJHXSI9NDTP7I5JLLZYD3V292JKDMOG', 'FINST-1MD668B1LJHXSI9NDTP7I5JLLZYD3V292JKDMPG', 'FINST-1MD668B1LJHXSI9NDTP7I5JLLZYD3V292JKDMQG', 'FINST-1MD668B1LJHXSI9NDTP7I5JLLZYD3V292JKDMRG']}
2025-07-27 01:32:02,133 - INFO - 批量插入表单数据成功，批次 2，共 14 条记录
2025-07-27 01:32:02,133 - INFO - 成功插入的数据ID: ['FINST-1MD668B1LJHXSI9NDTP7I5JLLZYD3V292JKDMEG', 'FINST-1MD668B1LJHXSI9NDTP7I5JLLZYD3V292JKDMFG', 'FINST-1MD668B1LJHXSI9NDTP7I5JLLZYD3V292JKDMGG', 'FINST-1MD668B1LJHXSI9NDTP7I5JLLZYD3V292JKDMHG', 'FINST-1MD668B1LJHXSI9NDTP7I5JLLZYD3V292JKDMIG', 'FINST-1MD668B1LJHXSI9NDTP7I5JLLZYD3V292JKDMJG', 'FINST-1MD668B1LJHXSI9NDTP7I5JLLZYD3V292JKDMKG', 'FINST-1MD668B1LJHXSI9NDTP7I5JLLZYD3V292JKDMLG', 'FINST-1MD668B1LJHXSI9NDTP7I5JLLZYD3V292JKDMMG', 'FINST-1MD668B1LJHXSI9NDTP7I5JLLZYD3V292JKDMNG', 'FINST-1MD668B1LJHXSI9NDTP7I5JLLZYD3V292JKDMOG', 'FINST-1MD668B1LJHXSI9NDTP7I5JLLZYD3V292JKDMPG', 'FINST-1MD668B1LJHXSI9NDTP7I5JLLZYD3V292JKDMQG', 'FINST-1MD668B1LJHXSI9NDTP7I5JLLZYD3V292JKDMRG']
2025-07-27 01:32:07,149 - INFO - 批量插入完成，共 64 条记录
2025-07-27 01:32:07,149 - INFO - 日期 2025-07-26 处理完成 - 更新: 0 条，插入: 64 条，错误: 0 条
2025-07-27 01:32:07,149 - INFO - 开始处理日期: 2025-07-27
2025-07-27 01:32:07,149 - INFO - Request Parameters - Page 1:
2025-07-27 01:32:07,149 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 01:32:07,149 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 01:32:07,649 - INFO - Response - Page 1:
2025-07-27 01:32:07,649 - INFO - 查询完成，共获取到 0 条记录
2025-07-27 01:32:07,649 - INFO - 获取到 0 条表单数据
2025-07-27 01:32:07,649 - INFO - 当前日期 2025-07-27 有 1 条MySQL数据需要处理
2025-07-27 01:32:07,649 - INFO - 开始批量插入 1 条新记录
2025-07-27 01:32:07,805 - INFO - 批量插入响应状态码: 200
2025-07-27 01:32:07,805 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 26 Jul 2025 17:32:07 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '12F8BC8D-67B2-7A31-8874-1D515FD0AF6A', 'x-acs-trace-id': '1661982fdb98406515f701130ece3b58', 'etag': '6xuop2m1kVoE3ESfB6T8fzQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-27 01:32:07,805 - INFO - 批量插入响应体: {'result': ['FINST-MRA66WC11JHX3AO28DRPLDIR08I53BGD2JKDMXK']}
2025-07-27 01:32:07,805 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-27 01:32:07,805 - INFO - 成功插入的数据ID: ['FINST-MRA66WC11JHX3AO28DRPLDIR08I53BGD2JKDMXK']
2025-07-27 01:32:12,820 - INFO - 批量插入完成，共 1 条记录
2025-07-27 01:32:12,820 - INFO - 日期 2025-07-27 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-27 01:32:12,820 - INFO - 数据同步完成！更新: 0 条，插入: 65 条，错误: 0 条
2025-07-27 01:32:12,820 - INFO - 同步完成
2025-07-27 04:30:33,717 - INFO - 使用默认增量同步（当天更新数据）
2025-07-27 04:30:33,717 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-27 04:30:33,717 - INFO - 查询参数: ('2025-07-27',)
2025-07-27 04:30:33,873 - INFO - MySQL查询成功，增量数据（日期: 2025-07-27），共获取 5 条记录
2025-07-27 04:30:33,873 - INFO - 获取到 2 个日期需要处理: ['2025-07-26', '2025-07-27']
2025-07-27 04:30:33,873 - INFO - 开始处理日期: 2025-07-26
2025-07-27 04:30:33,873 - INFO - Request Parameters - Page 1:
2025-07-27 04:30:33,873 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 04:30:33,873 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 04:30:41,420 - INFO - Response - Page 1:
2025-07-27 04:30:41,420 - INFO - 第 1 页获取到 50 条记录
2025-07-27 04:30:41,920 - INFO - Request Parameters - Page 2:
2025-07-27 04:30:41,920 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 04:30:41,920 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 04:30:50,029 - ERROR - 处理日期 2025-07-26 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: EB8BF477-9699-75FC-8165-E9D91C58874F Response: {'code': 'ServiceUnavailable', 'requestid': 'EB8BF477-9699-75FC-8165-E9D91C58874F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: EB8BF477-9699-75FC-8165-E9D91C58874F)
2025-07-27 04:30:50,029 - INFO - 开始处理日期: 2025-07-27
2025-07-27 04:30:50,029 - INFO - Request Parameters - Page 1:
2025-07-27 04:30:50,029 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 04:30:50,029 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 04:30:53,654 - INFO - Response - Page 1:
2025-07-27 04:30:53,654 - INFO - 第 1 页获取到 1 条记录
2025-07-27 04:30:54,170 - INFO - 查询完成，共获取到 1 条记录
2025-07-27 04:30:54,170 - INFO - 获取到 1 条表单数据
2025-07-27 04:30:54,170 - INFO - 当前日期 2025-07-27 有 1 条MySQL数据需要处理
2025-07-27 04:30:54,170 - INFO - 日期 2025-07-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-27 04:30:54,170 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-27 04:31:54,185 - INFO - 开始同步昨天与今天的销售数据: 2025-07-26 至 2025-07-27
2025-07-27 04:31:54,185 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-27 04:31:54,185 - INFO - 查询参数: ('2025-07-26', '2025-07-27')
2025-07-27 04:31:54,325 - INFO - MySQL查询成功，时间段: 2025-07-26 至 2025-07-27，共获取 108 条记录
2025-07-27 04:31:54,325 - INFO - 获取到 2 个日期需要处理: ['2025-07-26', '2025-07-27']
2025-07-27 04:31:54,325 - INFO - 开始处理日期: 2025-07-26
2025-07-27 04:31:54,325 - INFO - Request Parameters - Page 1:
2025-07-27 04:31:54,325 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 04:31:54,325 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 04:31:55,091 - INFO - Response - Page 1:
2025-07-27 04:31:55,091 - INFO - 第 1 页获取到 50 条记录
2025-07-27 04:31:55,607 - INFO - Request Parameters - Page 2:
2025-07-27 04:31:55,607 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 04:31:55,607 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 04:32:02,982 - INFO - Response - Page 2:
2025-07-27 04:32:02,982 - INFO - 第 2 页获取到 50 条记录
2025-07-27 04:32:03,482 - INFO - Request Parameters - Page 3:
2025-07-27 04:32:03,482 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 04:32:03,482 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 04:32:03,997 - INFO - Response - Page 3:
2025-07-27 04:32:03,997 - INFO - 第 3 页获取到 3 条记录
2025-07-27 04:32:04,497 - INFO - 查询完成，共获取到 103 条记录
2025-07-27 04:32:04,497 - INFO - 获取到 103 条表单数据
2025-07-27 04:32:04,497 - INFO - 当前日期 2025-07-26 有 103 条MySQL数据需要处理
2025-07-27 04:32:04,497 - INFO - 日期 2025-07-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-27 04:32:04,497 - INFO - 开始处理日期: 2025-07-27
2025-07-27 04:32:04,497 - INFO - Request Parameters - Page 1:
2025-07-27 04:32:04,497 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 04:32:04,497 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 04:32:04,997 - INFO - Response - Page 1:
2025-07-27 04:32:04,997 - INFO - 第 1 页获取到 1 条记录
2025-07-27 04:32:05,513 - INFO - 查询完成，共获取到 1 条记录
2025-07-27 04:32:05,513 - INFO - 获取到 1 条表单数据
2025-07-27 04:32:05,513 - INFO - 当前日期 2025-07-27 有 1 条MySQL数据需要处理
2025-07-27 04:32:05,513 - INFO - 日期 2025-07-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-27 04:32:05,513 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-27 04:32:05,513 - INFO - 同步完成
2025-07-27 07:30:33,502 - INFO - 使用默认增量同步（当天更新数据）
2025-07-27 07:30:33,502 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-27 07:30:33,502 - INFO - 查询参数: ('2025-07-27',)
2025-07-27 07:30:33,658 - INFO - MySQL查询成功，增量数据（日期: 2025-07-27），共获取 5 条记录
2025-07-27 07:30:33,658 - INFO - 获取到 2 个日期需要处理: ['2025-07-26', '2025-07-27']
2025-07-27 07:30:33,658 - INFO - 开始处理日期: 2025-07-26
2025-07-27 07:30:33,658 - INFO - Request Parameters - Page 1:
2025-07-27 07:30:33,658 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 07:30:33,658 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 07:30:41,783 - ERROR - 处理日期 2025-07-26 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2963543F-7C44-73D2-8B7C-A93E191B15B1 Response: {'code': 'ServiceUnavailable', 'requestid': '2963543F-7C44-73D2-8B7C-A93E191B15B1', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2963543F-7C44-73D2-8B7C-A93E191B15B1)
2025-07-27 07:30:41,783 - INFO - 开始处理日期: 2025-07-27
2025-07-27 07:30:41,783 - INFO - Request Parameters - Page 1:
2025-07-27 07:30:41,783 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 07:30:41,783 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 07:30:42,267 - INFO - Response - Page 1:
2025-07-27 07:30:42,267 - INFO - 第 1 页获取到 1 条记录
2025-07-27 07:30:42,783 - INFO - 查询完成，共获取到 1 条记录
2025-07-27 07:30:42,783 - INFO - 获取到 1 条表单数据
2025-07-27 07:30:42,783 - INFO - 当前日期 2025-07-27 有 1 条MySQL数据需要处理
2025-07-27 07:30:42,783 - INFO - 日期 2025-07-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-27 07:30:42,783 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-27 07:31:42,798 - INFO - 开始同步昨天与今天的销售数据: 2025-07-26 至 2025-07-27
2025-07-27 07:31:42,798 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-27 07:31:42,798 - INFO - 查询参数: ('2025-07-26', '2025-07-27')
2025-07-27 07:31:42,939 - INFO - MySQL查询成功，时间段: 2025-07-26 至 2025-07-27，共获取 108 条记录
2025-07-27 07:31:42,939 - INFO - 获取到 2 个日期需要处理: ['2025-07-26', '2025-07-27']
2025-07-27 07:31:42,954 - INFO - 开始处理日期: 2025-07-26
2025-07-27 07:31:42,954 - INFO - Request Parameters - Page 1:
2025-07-27 07:31:42,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 07:31:42,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 07:31:43,642 - INFO - Response - Page 1:
2025-07-27 07:31:43,642 - INFO - 第 1 页获取到 50 条记录
2025-07-27 07:31:44,157 - INFO - Request Parameters - Page 2:
2025-07-27 07:31:44,157 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 07:31:44,157 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 07:31:44,907 - INFO - Response - Page 2:
2025-07-27 07:31:44,907 - INFO - 第 2 页获取到 50 条记录
2025-07-27 07:31:45,407 - INFO - Request Parameters - Page 3:
2025-07-27 07:31:45,407 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 07:31:45,407 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 07:31:45,892 - INFO - Response - Page 3:
2025-07-27 07:31:45,892 - INFO - 第 3 页获取到 3 条记录
2025-07-27 07:31:46,407 - INFO - 查询完成，共获取到 103 条记录
2025-07-27 07:31:46,407 - INFO - 获取到 103 条表单数据
2025-07-27 07:31:46,407 - INFO - 当前日期 2025-07-26 有 103 条MySQL数据需要处理
2025-07-27 07:31:46,407 - INFO - 日期 2025-07-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-27 07:31:46,407 - INFO - 开始处理日期: 2025-07-27
2025-07-27 07:31:46,407 - INFO - Request Parameters - Page 1:
2025-07-27 07:31:46,407 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 07:31:46,407 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 07:31:46,861 - INFO - Response - Page 1:
2025-07-27 07:31:46,861 - INFO - 第 1 页获取到 1 条记录
2025-07-27 07:31:47,376 - INFO - 查询完成，共获取到 1 条记录
2025-07-27 07:31:47,376 - INFO - 获取到 1 条表单数据
2025-07-27 07:31:47,376 - INFO - 当前日期 2025-07-27 有 1 条MySQL数据需要处理
2025-07-27 07:31:47,376 - INFO - 日期 2025-07-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-27 07:31:47,376 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-27 07:31:47,376 - INFO - 同步完成
2025-07-27 10:30:33,834 - INFO - 使用默认增量同步（当天更新数据）
2025-07-27 10:30:33,850 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-27 10:30:33,850 - INFO - 查询参数: ('2025-07-27',)
2025-07-27 10:30:34,006 - INFO - MySQL查询成功，增量数据（日期: 2025-07-27），共获取 112 条记录
2025-07-27 10:30:34,006 - INFO - 获取到 3 个日期需要处理: ['2025-07-25', '2025-07-26', '2025-07-27']
2025-07-27 10:30:34,006 - INFO - 开始处理日期: 2025-07-25
2025-07-27 10:30:34,006 - INFO - Request Parameters - Page 1:
2025-07-27 10:30:34,006 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 10:30:34,006 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 10:30:42,115 - ERROR - 处理日期 2025-07-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D439FDD0-27F2-7029-A22E-BDFBF8909321 Response: {'code': 'ServiceUnavailable', 'requestid': 'D439FDD0-27F2-7029-A22E-BDFBF8909321', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D439FDD0-27F2-7029-A22E-BDFBF8909321)
2025-07-27 10:30:42,131 - INFO - 开始处理日期: 2025-07-26
2025-07-27 10:30:42,131 - INFO - Request Parameters - Page 1:
2025-07-27 10:30:42,131 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 10:30:42,131 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 10:30:50,240 - ERROR - 处理日期 2025-07-26 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B825BF51-AFCD-7F42-BAD2-61E8E5DD6A4B Response: {'code': 'ServiceUnavailable', 'requestid': 'B825BF51-AFCD-7F42-BAD2-61E8E5DD6A4B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B825BF51-AFCD-7F42-BAD2-61E8E5DD6A4B)
2025-07-27 10:30:50,240 - INFO - 开始处理日期: 2025-07-27
2025-07-27 10:30:50,240 - INFO - Request Parameters - Page 1:
2025-07-27 10:30:50,240 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 10:30:50,240 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 10:30:52,865 - INFO - Response - Page 1:
2025-07-27 10:30:52,865 - INFO - 第 1 页获取到 1 条记录
2025-07-27 10:30:53,381 - INFO - 查询完成，共获取到 1 条记录
2025-07-27 10:30:53,381 - INFO - 获取到 1 条表单数据
2025-07-27 10:30:53,381 - INFO - 当前日期 2025-07-27 有 5 条MySQL数据需要处理
2025-07-27 10:30:53,381 - INFO - 开始批量插入 4 条新记录
2025-07-27 10:30:53,568 - INFO - 批量插入响应状态码: 200
2025-07-27 10:30:53,568 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 27 Jul 2025 02:30:53 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '204', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'A5528BB8-5E09-78C6-97D9-C3FC2792232F', 'x-acs-trace-id': '3e91836689fc5ecd07723a5c0a07c73b', 'etag': '2sPDcu6Kst0k6BVG/QtpMWw4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-27 10:30:53,568 - INFO - 批量插入响应体: {'result': ['FINST-NWE664C1VIHX9LTA96OPA5L4N1022HC8B2LDMET', 'FINST-NWE664C1VIHX9LTA96OPA5L4N1022HC8B2LDMFT', 'FINST-NWE664C1VIHX9LTA96OPA5L4N1022HC8B2LDMGT', 'FINST-NWE664C1VIHX9LTA96OPA5L4N1022HC8B2LDMHT']}
2025-07-27 10:30:53,568 - INFO - 批量插入表单数据成功，批次 1，共 4 条记录
2025-07-27 10:30:53,568 - INFO - 成功插入的数据ID: ['FINST-NWE664C1VIHX9LTA96OPA5L4N1022HC8B2LDMET', 'FINST-NWE664C1VIHX9LTA96OPA5L4N1022HC8B2LDMFT', 'FINST-NWE664C1VIHX9LTA96OPA5L4N1022HC8B2LDMGT', 'FINST-NWE664C1VIHX9LTA96OPA5L4N1022HC8B2LDMHT']
2025-07-27 10:30:58,584 - INFO - 批量插入完成，共 4 条记录
2025-07-27 10:30:58,584 - INFO - 日期 2025-07-27 处理完成 - 更新: 0 条，插入: 4 条，错误: 0 条
2025-07-27 10:30:58,584 - INFO - 数据同步完成！更新: 0 条，插入: 4 条，错误: 2 条
2025-07-27 10:31:58,599 - INFO - 开始同步昨天与今天的销售数据: 2025-07-26 至 2025-07-27
2025-07-27 10:31:58,599 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-27 10:31:58,599 - INFO - 查询参数: ('2025-07-26', '2025-07-27')
2025-07-27 10:31:58,755 - INFO - MySQL查询成功，时间段: 2025-07-26 至 2025-07-27，共获取 385 条记录
2025-07-27 10:31:58,755 - INFO - 获取到 2 个日期需要处理: ['2025-07-26', '2025-07-27']
2025-07-27 10:31:58,755 - INFO - 开始处理日期: 2025-07-26
2025-07-27 10:31:58,755 - INFO - Request Parameters - Page 1:
2025-07-27 10:31:58,755 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 10:31:58,755 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 10:31:59,583 - INFO - Response - Page 1:
2025-07-27 10:31:59,583 - INFO - 第 1 页获取到 50 条记录
2025-07-27 10:32:00,083 - INFO - Request Parameters - Page 2:
2025-07-27 10:32:00,083 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 10:32:00,083 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 10:32:07,036 - INFO - Response - Page 2:
2025-07-27 10:32:07,036 - INFO - 第 2 页获取到 50 条记录
2025-07-27 10:32:07,552 - INFO - Request Parameters - Page 3:
2025-07-27 10:32:07,552 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 10:32:07,552 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 10:32:08,068 - INFO - Response - Page 3:
2025-07-27 10:32:08,068 - INFO - 第 3 页获取到 3 条记录
2025-07-27 10:32:08,583 - INFO - 查询完成，共获取到 103 条记录
2025-07-27 10:32:08,583 - INFO - 获取到 103 条表单数据
2025-07-27 10:32:08,583 - INFO - 当前日期 2025-07-26 有 369 条MySQL数据需要处理
2025-07-27 10:32:08,583 - INFO - 开始批量插入 266 条新记录
2025-07-27 10:32:08,849 - INFO - 批量插入响应状态码: 200
2025-07-27 10:32:08,849 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 27 Jul 2025 02:32:08 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2462', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'FA6ED648-4670-76FE-9DD5-8F1718E310C2', 'x-acs-trace-id': 'ad8bc5787d1c37b149ea8b63218bc796', 'etag': '2l2FIMANLDLsXRzyPrIvQBA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-27 10:32:08,849 - INFO - 批量插入响应体: {'result': ['FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDML31', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMM31', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMN31', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMO31', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMP31', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMQ31', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMR31', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMS31', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMT31', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMU31', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMV31', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMW31', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMX31', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMY31', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMZ31', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDM041', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDM141', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDM241', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDM341', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDM441', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDM541', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDM641', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDM741', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDM841', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDM941', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMA41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMB41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMC41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMD41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDME41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMF41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMG41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMH41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMI41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMJ41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMK41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDML41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMM41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMN41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMO41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMP41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMQ41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMR41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMS41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMT41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMU41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMV41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMW41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMX41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMY41']}
2025-07-27 10:32:08,849 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-27 10:32:08,849 - INFO - 成功插入的数据ID: ['FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDML31', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMM31', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMN31', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMO31', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMP31', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMQ31', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMR31', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMS31', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMT31', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMU31', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMV31', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMW31', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMX31', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMY31', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMZ31', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDM041', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDM141', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDM241', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDM341', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDM441', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDM541', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDM641', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDM741', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDM841', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDM941', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMA41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMB41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMC41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMD41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDME41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMF41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMG41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMH41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMI41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMJ41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMK41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDML41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMM41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMN41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMO41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMP41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMQ41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMR41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMS41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMT41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMU41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMV41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMW41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMX41', 'FINST-OY8665C1FDHXTEYS8ZH3N6JX9U8Q2JFUC2LDMY41']
2025-07-27 10:32:14,083 - INFO - 批量插入响应状态码: 200
2025-07-27 10:32:14,083 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 27 Jul 2025 02:32:13 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'CF1CFE9C-AD18-7808-851C-C511CB680854', 'x-acs-trace-id': '8876f9acbcafb13bb9aa3496952db281', 'etag': '2hB15kZSS8xrhiNBIsTxlkA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-27 10:32:14,083 - INFO - 批量插入响应体: {'result': ['FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMDN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMEN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMFN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMGN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMHN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMIN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMJN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMKN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMLN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMMN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMNN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMON', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMPN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMQN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMRN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMSN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMTN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMUN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMVN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMWN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMXN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMYN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMZN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDM0O', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDM1O', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDM2O', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDM3O', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDM4O', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDM5O', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDM6O', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDM7O', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDM8O', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDM9O', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMAO', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMBO', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMCO', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMDO', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMEO', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMFO', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMGO', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMHO', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMIO', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMJO', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMKO', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMLO', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMMO', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMNO', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMOO', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMPO', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMQO']}
2025-07-27 10:32:14,083 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-07-27 10:32:14,083 - INFO - 成功插入的数据ID: ['FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMDN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMEN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMFN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMGN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMHN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMIN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMJN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMKN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMLN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMMN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMNN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMON', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMPN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMQN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMRN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMSN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMTN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMUN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMVN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMWN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMXN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMYN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMZN', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDM0O', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDM1O', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDM2O', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDM3O', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDM4O', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDM5O', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDM6O', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDM7O', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDM8O', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDM9O', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMAO', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMBO', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMCO', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMDO', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMEO', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMFO', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMGO', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMHO', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMIO', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMJO', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMKO', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMLO', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMMO', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMNO', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMOO', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMPO', 'FINST-KLF66HD18JHXJUPX6EUY6D1YY3623OGYC2LDMQO']
2025-07-27 10:32:19,333 - INFO - 批量插入响应状态码: 200
2025-07-27 10:32:19,333 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 27 Jul 2025 02:32:19 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'C61BDA54-8E4C-7183-98B9-E781BF6DFA42', 'x-acs-trace-id': '8a05bc88b501fdfb3598e39d1f6d12b7', 'etag': '2+f/ip307dsJziRADffJu4w2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-27 10:32:19,333 - INFO - 批量插入响应体: {'result': ['FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM0M', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM1M', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM2M', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM3M', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM4M', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM5M', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM6M', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM7M', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM8M', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM9M', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMAM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMBM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMCM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMDM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMEM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMFM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMGM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMHM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMIM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMJM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMKM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMLM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMMM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMNM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMOM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMPM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMQM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMRM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMSM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMTM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMUM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMVM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMWM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMXM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMYM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMZM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM0N', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM1N', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM2N', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM3N', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM4N', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM5N', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM6N', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM7N', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM8N', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM9N', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMAN', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMBN', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMCN', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMDN']}
2025-07-27 10:32:19,333 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-07-27 10:32:19,333 - INFO - 成功插入的数据ID: ['FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM0M', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM1M', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM2M', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM3M', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM4M', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM5M', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM6M', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM7M', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM8M', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM9M', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMAM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMBM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMCM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMDM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMEM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMFM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMGM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMHM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMIM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMJM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMKM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMLM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMMM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMNM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMOM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMPM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMQM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMRM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMSM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMTM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMUM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMVM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMWM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMXM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMYM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMZM', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM0N', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM1N', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM2N', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM3N', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM4N', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM5N', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM6N', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM7N', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM8N', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDM9N', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMAN', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMBN', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMCN', 'FINST-1V966BA1AJHXMP5ZEIWWIBS4GEQT2EI2D2LDMDN']
2025-07-27 10:32:24,583 - INFO - 批量插入响应状态码: 200
2025-07-27 10:32:24,583 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 27 Jul 2025 02:32:24 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'DFE232F8-6F23-7DD0-9D6B-1CF5966B84ED', 'x-acs-trace-id': 'ae8a15a734a5fe56cc2e09db266d2e82', 'etag': '2MtZKL7DEuhn90DMKjodWhQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-27 10:32:24,583 - INFO - 批量插入响应体: {'result': ['FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDMO1', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDMP1', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDMQ1', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDMR1', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDMS1', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDMT1', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDMU1', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDMV1', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDMW1', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDMX1', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDMY1', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDMZ1', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDM02', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDM12', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDM22', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDM32', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDM42', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDM52', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDM62', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDM72', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDM82', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDM92', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMA2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMB2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMC2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMD2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDME2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMF2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMG2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMH2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMI2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMJ2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMK2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDML2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMM2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMN2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMO2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMP2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMQ2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMR2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMS2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMT2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMU2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMV2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMW2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMX2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMY2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMZ2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDM03', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDM13']}
2025-07-27 10:32:24,583 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-07-27 10:32:24,583 - INFO - 成功插入的数据ID: ['FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDMO1', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDMP1', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDMQ1', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDMR1', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDMS1', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDMT1', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDMU1', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDMV1', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDMW1', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDMX1', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDMY1', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDMZ1', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDM02', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDM12', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDM22', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDM32', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDM42', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDM52', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDM62', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDM72', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT25K6D2LDM82', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDM92', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMA2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMB2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMC2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMD2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDME2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMF2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMG2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMH2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMI2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMJ2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMK2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDML2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMM2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMN2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMO2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMP2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMQ2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMR2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMS2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMT2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMU2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMV2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMW2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMX2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMY2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDMZ2', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDM03', 'FINST-3Z966E91SIHXRY9L85P274OEGRBT26K6D2LDM13']
2025-07-27 10:32:29,818 - INFO - 批量插入响应状态码: 200
2025-07-27 10:32:29,818 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 27 Jul 2025 02:32:29 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0D525A08-E4FC-7F8B-AA0D-0CDC721B5AAD', 'x-acs-trace-id': 'fc610dff7e5414cca13bf1020774df89', 'etag': '2NKRKhuncjgSxljc+4Or6Bw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-27 10:32:29,818 - INFO - 批量插入响应体: {'result': ['FINST-TQB66671ZIHXSU17DWU8SDIGXTD92ZLAD2LDM1S', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD92ZLAD2LDM2S', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD92ZLAD2LDM3S', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD92ZLAD2LDM4S', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD92ZLAD2LDM5S', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD92ZLAD2LDM6S', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD92ZLAD2LDM7S', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD92ZLAD2LDM8S', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD92ZLAD2LDM9S', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD92ZLAD2LDMAS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD92ZLAD2LDMBS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD92ZLAD2LDMCS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD92ZLAD2LDMDS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMES', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMFS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMGS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMHS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMIS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMJS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMKS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMLS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMMS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMNS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMOS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMPS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMQS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMRS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMSS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMTS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMUS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMVS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMWS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMXS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMYS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMZS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDM0T', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDM1T', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDM2T', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDM3T', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDM4T', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDM5T', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDM6T', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDM7T', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDM8T', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDM9T', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMAT', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMBT', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMCT', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMDT', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMET']}
2025-07-27 10:32:29,818 - INFO - 批量插入表单数据成功，批次 5，共 50 条记录
2025-07-27 10:32:29,818 - INFO - 成功插入的数据ID: ['FINST-TQB66671ZIHXSU17DWU8SDIGXTD92ZLAD2LDM1S', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD92ZLAD2LDM2S', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD92ZLAD2LDM3S', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD92ZLAD2LDM4S', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD92ZLAD2LDM5S', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD92ZLAD2LDM6S', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD92ZLAD2LDM7S', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD92ZLAD2LDM8S', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD92ZLAD2LDM9S', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD92ZLAD2LDMAS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD92ZLAD2LDMBS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD92ZLAD2LDMCS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD92ZLAD2LDMDS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMES', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMFS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMGS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMHS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMIS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMJS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMKS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMLS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMMS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMNS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMOS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMPS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMQS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMRS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMSS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMTS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMUS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMVS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMWS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMXS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMYS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMZS', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDM0T', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDM1T', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDM2T', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDM3T', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDM4T', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDM5T', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDM6T', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDM7T', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDM8T', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDM9T', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMAT', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMBT', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMCT', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMDT', 'FINST-TQB66671ZIHXSU17DWU8SDIGXTD920MAD2LDMET']
2025-07-27 10:32:34,989 - INFO - 批量插入响应状态码: 200
2025-07-27 10:32:34,989 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 27 Jul 2025 02:32:34 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '780', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4DB5B02A-13D9-7E68-87DA-406BDDCC402E', 'x-acs-trace-id': '5e9208f87aa04c23c520beff58b8df6e', 'etag': '776Mnu6T7i57AMxhPlZd/bA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-27 10:32:34,989 - INFO - 批量插入响应体: {'result': ['FINST-CZD66191XKGXGIGNC8UQ7BL2KBYX2OLED2LDM5R', 'FINST-CZD66191XKGXGIGNC8UQ7BL2KBYX2OLED2LDM6R', 'FINST-CZD66191XKGXGIGNC8UQ7BL2KBYX2OLED2LDM7R', 'FINST-CZD66191XKGXGIGNC8UQ7BL2KBYX2OLED2LDM8R', 'FINST-CZD66191XKGXGIGNC8UQ7BL2KBYX2OLED2LDM9R', 'FINST-CZD66191XKGXGIGNC8UQ7BL2KBYX2OLED2LDMAR', 'FINST-CZD66191XKGXGIGNC8UQ7BL2KBYX2OLED2LDMBR', 'FINST-CZD66191XKGXGIGNC8UQ7BL2KBYX2OLED2LDMCR', 'FINST-CZD66191XKGXGIGNC8UQ7BL2KBYX2OLED2LDMDR', 'FINST-CZD66191XKGXGIGNC8UQ7BL2KBYX2OLED2LDMER', 'FINST-CZD66191XKGXGIGNC8UQ7BL2KBYX2OLED2LDMFR', 'FINST-CZD66191XKGXGIGNC8UQ7BL2KBYX2OLED2LDMGR', 'FINST-CZD66191XKGXGIGNC8UQ7BL2KBYX2OLED2LDMHR', 'FINST-CZD66191XKGXGIGNC8UQ7BL2KBYX2OLED2LDMIR', 'FINST-CZD66191XKGXGIGNC8UQ7BL2KBYX2OLED2LDMJR', 'FINST-CZD66191XKGXGIGNC8UQ7BL2KBYX2OLED2LDMKR']}
2025-07-27 10:32:34,989 - INFO - 批量插入表单数据成功，批次 6，共 16 条记录
2025-07-27 10:32:34,989 - INFO - 成功插入的数据ID: ['FINST-CZD66191XKGXGIGNC8UQ7BL2KBYX2OLED2LDM5R', 'FINST-CZD66191XKGXGIGNC8UQ7BL2KBYX2OLED2LDM6R', 'FINST-CZD66191XKGXGIGNC8UQ7BL2KBYX2OLED2LDM7R', 'FINST-CZD66191XKGXGIGNC8UQ7BL2KBYX2OLED2LDM8R', 'FINST-CZD66191XKGXGIGNC8UQ7BL2KBYX2OLED2LDM9R', 'FINST-CZD66191XKGXGIGNC8UQ7BL2KBYX2OLED2LDMAR', 'FINST-CZD66191XKGXGIGNC8UQ7BL2KBYX2OLED2LDMBR', 'FINST-CZD66191XKGXGIGNC8UQ7BL2KBYX2OLED2LDMCR', 'FINST-CZD66191XKGXGIGNC8UQ7BL2KBYX2OLED2LDMDR', 'FINST-CZD66191XKGXGIGNC8UQ7BL2KBYX2OLED2LDMER', 'FINST-CZD66191XKGXGIGNC8UQ7BL2KBYX2OLED2LDMFR', 'FINST-CZD66191XKGXGIGNC8UQ7BL2KBYX2OLED2LDMGR', 'FINST-CZD66191XKGXGIGNC8UQ7BL2KBYX2OLED2LDMHR', 'FINST-CZD66191XKGXGIGNC8UQ7BL2KBYX2OLED2LDMIR', 'FINST-CZD66191XKGXGIGNC8UQ7BL2KBYX2OLED2LDMJR', 'FINST-CZD66191XKGXGIGNC8UQ7BL2KBYX2OLED2LDMKR']
2025-07-27 10:32:40,005 - INFO - 批量插入完成，共 266 条记录
2025-07-27 10:32:40,005 - INFO - 日期 2025-07-26 处理完成 - 更新: 0 条，插入: 266 条，错误: 0 条
2025-07-27 10:32:40,005 - INFO - 开始处理日期: 2025-07-27
2025-07-27 10:32:40,005 - INFO - Request Parameters - Page 1:
2025-07-27 10:32:40,005 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 10:32:40,005 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 10:32:40,552 - INFO - Response - Page 1:
2025-07-27 10:32:40,552 - INFO - 第 1 页获取到 5 条记录
2025-07-27 10:32:41,052 - INFO - 查询完成，共获取到 5 条记录
2025-07-27 10:32:41,052 - INFO - 获取到 5 条表单数据
2025-07-27 10:32:41,052 - INFO - 当前日期 2025-07-27 有 5 条MySQL数据需要处理
2025-07-27 10:32:41,052 - INFO - 日期 2025-07-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-27 10:32:41,052 - INFO - 数据同步完成！更新: 0 条，插入: 266 条，错误: 0 条
2025-07-27 10:32:41,052 - INFO - 同步完成
2025-07-27 13:30:33,698 - INFO - 使用默认增量同步（当天更新数据）
2025-07-27 13:30:33,698 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-27 13:30:33,698 - INFO - 查询参数: ('2025-07-27',)
2025-07-27 13:30:33,854 - INFO - MySQL查询成功，增量数据（日期: 2025-07-27），共获取 157 条记录
2025-07-27 13:30:33,854 - INFO - 获取到 3 个日期需要处理: ['2025-07-25', '2025-07-26', '2025-07-27']
2025-07-27 13:30:33,854 - INFO - 开始处理日期: 2025-07-25
2025-07-27 13:30:33,854 - INFO - Request Parameters - Page 1:
2025-07-27 13:30:33,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 13:30:33,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 13:30:41,338 - INFO - Response - Page 1:
2025-07-27 13:30:41,338 - INFO - 第 1 页获取到 50 条记录
2025-07-27 13:30:41,854 - INFO - Request Parameters - Page 2:
2025-07-27 13:30:41,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 13:30:41,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 13:30:49,963 - ERROR - 处理日期 2025-07-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3AE41C15-4F44-71BC-9CCD-B13D8FE765D2 Response: {'code': 'ServiceUnavailable', 'requestid': '3AE41C15-4F44-71BC-9CCD-B13D8FE765D2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3AE41C15-4F44-71BC-9CCD-B13D8FE765D2)
2025-07-27 13:30:49,963 - INFO - 开始处理日期: 2025-07-26
2025-07-27 13:30:49,963 - INFO - Request Parameters - Page 1:
2025-07-27 13:30:49,963 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 13:30:49,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 13:30:53,432 - INFO - Response - Page 1:
2025-07-27 13:30:53,432 - INFO - 第 1 页获取到 50 条记录
2025-07-27 13:30:53,947 - INFO - Request Parameters - Page 2:
2025-07-27 13:30:53,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 13:30:53,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 13:30:54,713 - INFO - Response - Page 2:
2025-07-27 13:30:54,713 - INFO - 第 2 页获取到 50 条记录
2025-07-27 13:30:55,213 - INFO - Request Parameters - Page 3:
2025-07-27 13:30:55,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 13:30:55,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 13:30:55,901 - INFO - Response - Page 3:
2025-07-27 13:30:55,901 - INFO - 第 3 页获取到 50 条记录
2025-07-27 13:30:56,401 - INFO - Request Parameters - Page 4:
2025-07-27 13:30:56,401 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 13:30:56,401 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 13:30:57,088 - INFO - Response - Page 4:
2025-07-27 13:30:57,088 - INFO - 第 4 页获取到 50 条记录
2025-07-27 13:30:57,604 - INFO - Request Parameters - Page 5:
2025-07-27 13:30:57,604 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 13:30:57,604 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 13:30:58,307 - INFO - Response - Page 5:
2025-07-27 13:30:58,307 - INFO - 第 5 页获取到 50 条记录
2025-07-27 13:30:58,822 - INFO - Request Parameters - Page 6:
2025-07-27 13:30:58,822 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 13:30:58,822 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 13:30:59,572 - INFO - Response - Page 6:
2025-07-27 13:30:59,572 - INFO - 第 6 页获取到 50 条记录
2025-07-27 13:31:00,072 - INFO - Request Parameters - Page 7:
2025-07-27 13:31:00,072 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 13:31:00,072 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 13:31:00,791 - INFO - Response - Page 7:
2025-07-27 13:31:00,791 - INFO - 第 7 页获取到 50 条记录
2025-07-27 13:31:01,307 - INFO - Request Parameters - Page 8:
2025-07-27 13:31:01,307 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 13:31:01,307 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 13:31:01,916 - INFO - Response - Page 8:
2025-07-27 13:31:01,916 - INFO - 第 8 页获取到 19 条记录
2025-07-27 13:31:02,432 - INFO - 查询完成，共获取到 369 条记录
2025-07-27 13:31:02,432 - INFO - 获取到 369 条表单数据
2025-07-27 13:31:02,432 - INFO - 当前日期 2025-07-26 有 144 条MySQL数据需要处理
2025-07-27 13:31:02,432 - INFO - 开始批量插入 41 条新记录
2025-07-27 13:31:02,682 - INFO - 批量插入响应状态码: 200
2025-07-27 13:31:02,682 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 27 Jul 2025 05:31:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1980', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E64C6660-0828-7095-AA11-C3D3CB6EF7EB', 'x-acs-trace-id': '18f85f7f524d297aeec03531dda9daff', 'etag': '1qO79u8cRoiraTVqTyxU5KQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-27 13:31:02,682 - INFO - 批量插入响应体: {'result': ['FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMTR', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMUR', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMVR', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMWR', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMXR', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMYR', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMZR', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDM0S', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDM1S', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDM2S', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDM3S', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDM4S', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDM5S', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDM6S', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDM7S', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDM8S', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDM9S', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMAS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMBS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMCS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMDS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMES', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMFS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMGS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMHS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMIS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMJS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMKS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMLS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMMS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMNS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMOS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMPS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMQS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMRS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMSS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMTS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMUS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMVS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMWS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMXS']}
2025-07-27 13:31:02,682 - INFO - 批量插入表单数据成功，批次 1，共 41 条记录
2025-07-27 13:31:02,682 - INFO - 成功插入的数据ID: ['FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMTR', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMUR', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMVR', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMWR', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMXR', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMYR', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMZR', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDM0S', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDM1S', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDM2S', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDM3S', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDM4S', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDM5S', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDM6S', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDM7S', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDM8S', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDM9S', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMAS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMBS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMCS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMDS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMES', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMFS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMGS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMHS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMIS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMJS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMKS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMLS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMMS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMNS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMOS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMPS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMQS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMRS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMSS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMTS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMUS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMVS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMWS', 'FINST-LLF66FD1DJHXR8MZDMRIG9DJ6A5T3ARWQ8LDMXS']
2025-07-27 13:31:07,697 - INFO - 批量插入完成，共 41 条记录
2025-07-27 13:31:07,697 - INFO - 日期 2025-07-26 处理完成 - 更新: 0 条，插入: 41 条，错误: 0 条
2025-07-27 13:31:07,697 - INFO - 开始处理日期: 2025-07-27
2025-07-27 13:31:07,697 - INFO - Request Parameters - Page 1:
2025-07-27 13:31:07,697 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 13:31:07,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 13:31:08,244 - INFO - Response - Page 1:
2025-07-27 13:31:08,244 - INFO - 第 1 页获取到 5 条记录
2025-07-27 13:31:08,760 - INFO - 查询完成，共获取到 5 条记录
2025-07-27 13:31:08,760 - INFO - 获取到 5 条表单数据
2025-07-27 13:31:08,760 - INFO - 当前日期 2025-07-27 有 5 条MySQL数据需要处理
2025-07-27 13:31:08,760 - INFO - 日期 2025-07-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-27 13:31:08,760 - INFO - 数据同步完成！更新: 0 条，插入: 41 条，错误: 1 条
2025-07-27 13:32:08,775 - INFO - 开始同步昨天与今天的销售数据: 2025-07-26 至 2025-07-27
2025-07-27 13:32:08,775 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-27 13:32:08,775 - INFO - 查询参数: ('2025-07-26', '2025-07-27')
2025-07-27 13:32:08,931 - INFO - MySQL查询成功，时间段: 2025-07-26 至 2025-07-27，共获取 461 条记录
2025-07-27 13:32:08,931 - INFO - 获取到 2 个日期需要处理: ['2025-07-26', '2025-07-27']
2025-07-27 13:32:08,947 - INFO - 开始处理日期: 2025-07-26
2025-07-27 13:32:08,947 - INFO - Request Parameters - Page 1:
2025-07-27 13:32:08,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 13:32:08,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 13:32:09,713 - INFO - Response - Page 1:
2025-07-27 13:32:09,713 - INFO - 第 1 页获取到 50 条记录
2025-07-27 13:32:10,213 - INFO - Request Parameters - Page 2:
2025-07-27 13:32:10,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 13:32:10,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 13:32:10,900 - INFO - Response - Page 2:
2025-07-27 13:32:10,900 - INFO - 第 2 页获取到 50 条记录
2025-07-27 13:32:11,416 - INFO - Request Parameters - Page 3:
2025-07-27 13:32:11,416 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 13:32:11,416 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 13:32:12,134 - INFO - Response - Page 3:
2025-07-27 13:32:12,134 - INFO - 第 3 页获取到 50 条记录
2025-07-27 13:32:12,650 - INFO - Request Parameters - Page 4:
2025-07-27 13:32:12,650 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 13:32:12,650 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 13:32:13,369 - INFO - Response - Page 4:
2025-07-27 13:32:13,369 - INFO - 第 4 页获取到 50 条记录
2025-07-27 13:32:13,884 - INFO - Request Parameters - Page 5:
2025-07-27 13:32:13,884 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 13:32:13,884 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 13:32:14,634 - INFO - Response - Page 5:
2025-07-27 13:32:14,634 - INFO - 第 5 页获取到 50 条记录
2025-07-27 13:32:15,150 - INFO - Request Parameters - Page 6:
2025-07-27 13:32:15,150 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 13:32:15,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 13:32:15,837 - INFO - Response - Page 6:
2025-07-27 13:32:15,837 - INFO - 第 6 页获取到 50 条记录
2025-07-27 13:32:16,353 - INFO - Request Parameters - Page 7:
2025-07-27 13:32:16,353 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 13:32:16,353 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 13:32:17,087 - INFO - Response - Page 7:
2025-07-27 13:32:17,087 - INFO - 第 7 页获取到 50 条记录
2025-07-27 13:32:17,603 - INFO - Request Parameters - Page 8:
2025-07-27 13:32:17,603 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 13:32:17,603 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 13:32:18,322 - INFO - Response - Page 8:
2025-07-27 13:32:18,322 - INFO - 第 8 页获取到 50 条记录
2025-07-27 13:32:18,837 - INFO - Request Parameters - Page 9:
2025-07-27 13:32:18,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 13:32:18,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 13:32:19,431 - INFO - Response - Page 9:
2025-07-27 13:32:19,431 - INFO - 第 9 页获取到 10 条记录
2025-07-27 13:32:19,931 - INFO - 查询完成，共获取到 410 条记录
2025-07-27 13:32:19,931 - INFO - 获取到 410 条表单数据
2025-07-27 13:32:19,931 - INFO - 当前日期 2025-07-26 有 445 条MySQL数据需要处理
2025-07-27 13:32:19,947 - INFO - 开始批量插入 35 条新记录
2025-07-27 13:32:20,166 - INFO - 批量插入响应状态码: 200
2025-07-27 13:32:20,166 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 27 Jul 2025 05:32:20 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1692', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'AC90E22F-6CE1-746B-8C44-5BD9E7A71D0A', 'x-acs-trace-id': '58d386ebbf2d3c0dc092f745c9d4ec37', 'etag': '1ejGEj54pv6WpnQdSqjg0zg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-27 13:32:20,166 - INFO - 批量插入响应体: {'result': ['FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMHW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMIW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMJW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMKW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMLW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMMW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMNW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMOW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMPW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMQW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMRW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMSW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMTW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMUW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMVW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMWW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMXW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMYW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMZW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDM0X', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDM1X', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDM2X', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDM3X', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDM4X', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDM5X', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDM6X', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDM7X', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDM8X', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDM9X', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMAX', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMBX', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMCX', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMDX', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMEX', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMFX']}
2025-07-27 13:32:20,166 - INFO - 批量插入表单数据成功，批次 1，共 35 条记录
2025-07-27 13:32:20,166 - INFO - 成功插入的数据ID: ['FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMHW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMIW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMJW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMKW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMLW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMMW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMNW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMOW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMPW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMQW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMRW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMSW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMTW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMUW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMVW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMWW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMXW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMYW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMZW', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDM0X', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDM1X', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDM2X', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDM3X', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDM4X', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDM5X', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDM6X', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDM7X', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDM8X', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDM9X', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMAX', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMBX', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMCX', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMDX', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMEX', 'FINST-YWD66FA11JHX0SEZE7SS4DL3QMWI3OJKS8LDMFX']
2025-07-27 13:32:25,181 - INFO - 批量插入完成，共 35 条记录
2025-07-27 13:32:25,181 - INFO - 日期 2025-07-26 处理完成 - 更新: 0 条，插入: 35 条，错误: 0 条
2025-07-27 13:32:25,181 - INFO - 开始处理日期: 2025-07-27
2025-07-27 13:32:25,181 - INFO - Request Parameters - Page 1:
2025-07-27 13:32:25,181 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 13:32:25,181 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 13:32:25,759 - INFO - Response - Page 1:
2025-07-27 13:32:25,759 - INFO - 第 1 页获取到 5 条记录
2025-07-27 13:32:26,259 - INFO - 查询完成，共获取到 5 条记录
2025-07-27 13:32:26,259 - INFO - 获取到 5 条表单数据
2025-07-27 13:32:26,259 - INFO - 当前日期 2025-07-27 有 5 条MySQL数据需要处理
2025-07-27 13:32:26,259 - INFO - 日期 2025-07-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-27 13:32:26,259 - INFO - 数据同步完成！更新: 0 条，插入: 35 条，错误: 0 条
2025-07-27 13:32:26,259 - INFO - 同步完成
2025-07-27 16:30:33,843 - INFO - 使用默认增量同步（当天更新数据）
2025-07-27 16:30:33,843 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-27 16:30:33,843 - INFO - 查询参数: ('2025-07-27',)
2025-07-27 16:30:33,999 - INFO - MySQL查询成功，增量数据（日期: 2025-07-27），共获取 161 条记录
2025-07-27 16:30:33,999 - INFO - 获取到 3 个日期需要处理: ['2025-07-25', '2025-07-26', '2025-07-27']
2025-07-27 16:30:33,999 - INFO - 开始处理日期: 2025-07-25
2025-07-27 16:30:34,014 - INFO - Request Parameters - Page 1:
2025-07-27 16:30:34,014 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 16:30:34,014 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 16:30:42,139 - ERROR - 处理日期 2025-07-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E4E9C264-4A99-72D6-8CF8-************ Response: {'code': 'ServiceUnavailable', 'requestid': 'E4E9C264-4A99-72D6-8CF8-************', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E4E9C264-4A99-72D6-8CF8-************)
2025-07-27 16:30:42,139 - INFO - 开始处理日期: 2025-07-26
2025-07-27 16:30:42,139 - INFO - Request Parameters - Page 1:
2025-07-27 16:30:42,139 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 16:30:42,139 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 16:30:46,327 - INFO - Response - Page 1:
2025-07-27 16:30:46,327 - INFO - 第 1 页获取到 50 条记录
2025-07-27 16:30:46,843 - INFO - Request Parameters - Page 2:
2025-07-27 16:30:46,843 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 16:30:46,843 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 16:30:47,593 - INFO - Response - Page 2:
2025-07-27 16:30:47,593 - INFO - 第 2 页获取到 50 条记录
2025-07-27 16:30:48,093 - INFO - Request Parameters - Page 3:
2025-07-27 16:30:48,093 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 16:30:48,093 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 16:30:48,827 - INFO - Response - Page 3:
2025-07-27 16:30:48,827 - INFO - 第 3 页获取到 50 条记录
2025-07-27 16:30:49,327 - INFO - Request Parameters - Page 4:
2025-07-27 16:30:49,327 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 16:30:49,327 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 16:30:50,061 - INFO - Response - Page 4:
2025-07-27 16:30:50,061 - INFO - 第 4 页获取到 50 条记录
2025-07-27 16:30:50,577 - INFO - Request Parameters - Page 5:
2025-07-27 16:30:50,577 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 16:30:50,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 16:30:57,686 - INFO - Response - Page 5:
2025-07-27 16:30:57,686 - INFO - 第 5 页获取到 50 条记录
2025-07-27 16:30:58,202 - INFO - Request Parameters - Page 6:
2025-07-27 16:30:58,202 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 16:30:58,202 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 16:30:58,889 - INFO - Response - Page 6:
2025-07-27 16:30:58,889 - INFO - 第 6 页获取到 50 条记录
2025-07-27 16:30:59,405 - INFO - Request Parameters - Page 7:
2025-07-27 16:30:59,405 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 16:30:59,405 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 16:31:00,124 - INFO - Response - Page 7:
2025-07-27 16:31:00,124 - INFO - 第 7 页获取到 50 条记录
2025-07-27 16:31:00,639 - INFO - Request Parameters - Page 8:
2025-07-27 16:31:00,639 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 16:31:00,639 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 16:31:01,374 - INFO - Response - Page 8:
2025-07-27 16:31:01,374 - INFO - 第 8 页获取到 50 条记录
2025-07-27 16:31:01,874 - INFO - Request Parameters - Page 9:
2025-07-27 16:31:01,874 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 16:31:01,874 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 16:31:02,577 - INFO - Response - Page 9:
2025-07-27 16:31:02,577 - INFO - 第 9 页获取到 45 条记录
2025-07-27 16:31:03,092 - INFO - 查询完成，共获取到 445 条记录
2025-07-27 16:31:03,092 - INFO - 获取到 445 条表单数据
2025-07-27 16:31:03,092 - INFO - 当前日期 2025-07-26 有 147 条MySQL数据需要处理
2025-07-27 16:31:03,092 - INFO - 开始批量插入 3 条新记录
2025-07-27 16:31:03,264 - INFO - 批量插入响应状态码: 200
2025-07-27 16:31:03,264 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 27 Jul 2025 08:31:03 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '159', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '********-3E23-7439-A444-AE53274FC19C', 'x-acs-trace-id': 'c2ba97d2e6cc40a31e3aa9ad3f44816b', 'etag': '1fyQmC/4ZqBx4hSQrMPPsxg9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-27 16:31:03,280 - INFO - 批量插入响应体: {'result': ['FINST-80B662911JHXNWD7FV40MAFMRKP52RLE6FLDME11', 'FINST-80B662911JHXNWD7FV40MAFMRKP52RLE6FLDMF11', 'FINST-80B662911JHXNWD7FV40MAFMRKP52RLE6FLDMG11']}
2025-07-27 16:31:03,280 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-07-27 16:31:03,280 - INFO - 成功插入的数据ID: ['FINST-80B662911JHXNWD7FV40MAFMRKP52RLE6FLDME11', 'FINST-80B662911JHXNWD7FV40MAFMRKP52RLE6FLDMF11', 'FINST-80B662911JHXNWD7FV40MAFMRKP52RLE6FLDMG11']
2025-07-27 16:31:08,296 - INFO - 批量插入完成，共 3 条记录
2025-07-27 16:31:08,296 - INFO - 日期 2025-07-26 处理完成 - 更新: 0 条，插入: 3 条，错误: 0 条
2025-07-27 16:31:08,296 - INFO - 开始处理日期: 2025-07-27
2025-07-27 16:31:08,296 - INFO - Request Parameters - Page 1:
2025-07-27 16:31:08,296 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 16:31:08,296 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 16:31:08,827 - INFO - Response - Page 1:
2025-07-27 16:31:08,827 - INFO - 第 1 页获取到 5 条记录
2025-07-27 16:31:09,342 - INFO - 查询完成，共获取到 5 条记录
2025-07-27 16:31:09,342 - INFO - 获取到 5 条表单数据
2025-07-27 16:31:09,342 - INFO - 当前日期 2025-07-27 有 6 条MySQL数据需要处理
2025-07-27 16:31:09,342 - INFO - 开始批量插入 1 条新记录
2025-07-27 16:31:09,499 - INFO - 批量插入响应状态码: 200
2025-07-27 16:31:09,499 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 27 Jul 2025 08:31:09 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D3E7958E-1432-784D-9E50-99DCBC613519', 'x-acs-trace-id': 'e34cfea17a0e8206f7cae5ef3a49886f', 'etag': '6NdKulkitcLtmQyxUpbHR0A0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-27 16:31:09,499 - INFO - 批量插入响应体: {'result': ['FINST-3PF66X61AJHXPRWP76VH67SIR65M31FJ6FLDM5N']}
2025-07-27 16:31:09,499 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-27 16:31:09,499 - INFO - 成功插入的数据ID: ['FINST-3PF66X61AJHXPRWP76VH67SIR65M31FJ6FLDM5N']
2025-07-27 16:31:14,514 - INFO - 批量插入完成，共 1 条记录
2025-07-27 16:31:14,514 - INFO - 日期 2025-07-27 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-27 16:31:14,514 - INFO - 数据同步完成！更新: 0 条，插入: 4 条，错误: 1 条
2025-07-27 16:32:14,529 - INFO - 开始同步昨天与今天的销售数据: 2025-07-26 至 2025-07-27
2025-07-27 16:32:14,529 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-27 16:32:14,529 - INFO - 查询参数: ('2025-07-26', '2025-07-27')
2025-07-27 16:32:14,686 - INFO - MySQL查询成功，时间段: 2025-07-26 至 2025-07-27，共获取 482 条记录
2025-07-27 16:32:14,686 - INFO - 获取到 2 个日期需要处理: ['2025-07-26', '2025-07-27']
2025-07-27 16:32:14,701 - INFO - 开始处理日期: 2025-07-26
2025-07-27 16:32:14,701 - INFO - Request Parameters - Page 1:
2025-07-27 16:32:14,701 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 16:32:14,701 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 16:32:15,420 - INFO - Response - Page 1:
2025-07-27 16:32:15,420 - INFO - 第 1 页获取到 50 条记录
2025-07-27 16:32:15,936 - INFO - Request Parameters - Page 2:
2025-07-27 16:32:15,936 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 16:32:15,936 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 16:32:16,748 - INFO - Response - Page 2:
2025-07-27 16:32:16,748 - INFO - 第 2 页获取到 50 条记录
2025-07-27 16:32:17,264 - INFO - Request Parameters - Page 3:
2025-07-27 16:32:17,264 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 16:32:17,264 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 16:32:17,983 - INFO - Response - Page 3:
2025-07-27 16:32:17,983 - INFO - 第 3 页获取到 50 条记录
2025-07-27 16:32:18,498 - INFO - Request Parameters - Page 4:
2025-07-27 16:32:18,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 16:32:18,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 16:32:19,233 - INFO - Response - Page 4:
2025-07-27 16:32:19,233 - INFO - 第 4 页获取到 50 条记录
2025-07-27 16:32:19,733 - INFO - Request Parameters - Page 5:
2025-07-27 16:32:19,733 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 16:32:19,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 16:32:20,467 - INFO - Response - Page 5:
2025-07-27 16:32:20,467 - INFO - 第 5 页获取到 50 条记录
2025-07-27 16:32:20,983 - INFO - Request Parameters - Page 6:
2025-07-27 16:32:20,983 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 16:32:20,983 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 16:32:21,686 - INFO - Response - Page 6:
2025-07-27 16:32:21,686 - INFO - 第 6 页获取到 50 条记录
2025-07-27 16:32:22,201 - INFO - Request Parameters - Page 7:
2025-07-27 16:32:22,201 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 16:32:22,201 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 16:32:22,920 - INFO - Response - Page 7:
2025-07-27 16:32:22,920 - INFO - 第 7 页获取到 50 条记录
2025-07-27 16:32:23,420 - INFO - Request Parameters - Page 8:
2025-07-27 16:32:23,420 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 16:32:23,420 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 16:32:24,186 - INFO - Response - Page 8:
2025-07-27 16:32:24,186 - INFO - 第 8 页获取到 50 条记录
2025-07-27 16:32:24,686 - INFO - Request Parameters - Page 9:
2025-07-27 16:32:24,686 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 16:32:24,686 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 16:32:25,436 - INFO - Response - Page 9:
2025-07-27 16:32:25,436 - INFO - 第 9 页获取到 48 条记录
2025-07-27 16:32:25,951 - INFO - 查询完成，共获取到 448 条记录
2025-07-27 16:32:25,951 - INFO - 获取到 448 条表单数据
2025-07-27 16:32:25,951 - INFO - 当前日期 2025-07-26 有 462 条MySQL数据需要处理
2025-07-27 16:32:25,967 - INFO - 开始批量插入 14 条新记录
2025-07-27 16:32:26,139 - INFO - 批量插入响应状态码: 200
2025-07-27 16:32:26,139 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 27 Jul 2025 08:32:26 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '684', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '37BB71EF-5150-7D9A-AB52-9792F2D804C9', 'x-acs-trace-id': '2fcf8672d1fbb4569a1c50e62c57887d', 'etag': '68xZoScUyymHlGsZN05cUdw4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-27 16:32:26,139 - INFO - 批量插入响应体: {'result': ['FINST-1V966BA13JHXW3FJC1OOIA82JR0L3PJ68FLDMRJ', 'FINST-1V966BA13JHXW3FJC1OOIA82JR0L3PJ68FLDMSJ', 'FINST-1V966BA13JHXW3FJC1OOIA82JR0L3PJ68FLDMTJ', 'FINST-1V966BA13JHXW3FJC1OOIA82JR0L3PJ68FLDMUJ', 'FINST-1V966BA13JHXW3FJC1OOIA82JR0L3PJ68FLDMVJ', 'FINST-1V966BA13JHXW3FJC1OOIA82JR0L3PJ68FLDMWJ', 'FINST-1V966BA13JHXW3FJC1OOIA82JR0L3PJ68FLDMXJ', 'FINST-1V966BA13JHXW3FJC1OOIA82JR0L3PJ68FLDMYJ', 'FINST-1V966BA13JHXW3FJC1OOIA82JR0L3PJ68FLDMZJ', 'FINST-1V966BA13JHXW3FJC1OOIA82JR0L3PJ68FLDM0K', 'FINST-1V966BA13JHXW3FJC1OOIA82JR0L3PJ68FLDM1K', 'FINST-1V966BA13JHXW3FJC1OOIA82JR0L3PJ68FLDM2K', 'FINST-1V966BA13JHXW3FJC1OOIA82JR0L3PJ68FLDM3K', 'FINST-1V966BA13JHXW3FJC1OOIA82JR0L3PJ68FLDM4K']}
2025-07-27 16:32:26,139 - INFO - 批量插入表单数据成功，批次 1，共 14 条记录
2025-07-27 16:32:26,139 - INFO - 成功插入的数据ID: ['FINST-1V966BA13JHXW3FJC1OOIA82JR0L3PJ68FLDMRJ', 'FINST-1V966BA13JHXW3FJC1OOIA82JR0L3PJ68FLDMSJ', 'FINST-1V966BA13JHXW3FJC1OOIA82JR0L3PJ68FLDMTJ', 'FINST-1V966BA13JHXW3FJC1OOIA82JR0L3PJ68FLDMUJ', 'FINST-1V966BA13JHXW3FJC1OOIA82JR0L3PJ68FLDMVJ', 'FINST-1V966BA13JHXW3FJC1OOIA82JR0L3PJ68FLDMWJ', 'FINST-1V966BA13JHXW3FJC1OOIA82JR0L3PJ68FLDMXJ', 'FINST-1V966BA13JHXW3FJC1OOIA82JR0L3PJ68FLDMYJ', 'FINST-1V966BA13JHXW3FJC1OOIA82JR0L3PJ68FLDMZJ', 'FINST-1V966BA13JHXW3FJC1OOIA82JR0L3PJ68FLDM0K', 'FINST-1V966BA13JHXW3FJC1OOIA82JR0L3PJ68FLDM1K', 'FINST-1V966BA13JHXW3FJC1OOIA82JR0L3PJ68FLDM2K', 'FINST-1V966BA13JHXW3FJC1OOIA82JR0L3PJ68FLDM3K', 'FINST-1V966BA13JHXW3FJC1OOIA82JR0L3PJ68FLDM4K']
2025-07-27 16:32:31,154 - INFO - 批量插入完成，共 14 条记录
2025-07-27 16:32:31,154 - INFO - 日期 2025-07-26 处理完成 - 更新: 0 条，插入: 14 条，错误: 0 条
2025-07-27 16:32:31,154 - INFO - 开始处理日期: 2025-07-27
2025-07-27 16:32:31,154 - INFO - Request Parameters - Page 1:
2025-07-27 16:32:31,154 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 16:32:31,154 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 16:32:31,686 - INFO - Response - Page 1:
2025-07-27 16:32:31,686 - INFO - 第 1 页获取到 6 条记录
2025-07-27 16:32:32,186 - INFO - 查询完成，共获取到 6 条记录
2025-07-27 16:32:32,186 - INFO - 获取到 6 条表单数据
2025-07-27 16:32:32,186 - INFO - 当前日期 2025-07-27 有 6 条MySQL数据需要处理
2025-07-27 16:32:32,186 - INFO - 日期 2025-07-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-27 16:32:32,186 - INFO - 数据同步完成！更新: 0 条，插入: 14 条，错误: 0 条
2025-07-27 16:32:32,186 - INFO - 同步完成
2025-07-27 19:30:34,090 - INFO - 使用默认增量同步（当天更新数据）
2025-07-27 19:30:34,090 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-27 19:30:34,090 - INFO - 查询参数: ('2025-07-27',)
2025-07-27 19:30:34,247 - INFO - MySQL查询成功，增量数据（日期: 2025-07-27），共获取 162 条记录
2025-07-27 19:30:34,247 - INFO - 获取到 4 个日期需要处理: ['2025-07-24', '2025-07-25', '2025-07-26', '2025-07-27']
2025-07-27 19:30:34,247 - INFO - 开始处理日期: 2025-07-24
2025-07-27 19:30:34,247 - INFO - Request Parameters - Page 1:
2025-07-27 19:30:34,247 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 19:30:34,247 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 19:30:42,375 - ERROR - 处理日期 2025-07-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A2C06F6D-073D-7D86-9DBE-24774A9705E4 Response: {'code': 'ServiceUnavailable', 'requestid': 'A2C06F6D-073D-7D86-9DBE-24774A9705E4', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A2C06F6D-073D-7D86-9DBE-24774A9705E4)
2025-07-27 19:30:42,375 - INFO - 开始处理日期: 2025-07-25
2025-07-27 19:30:42,375 - INFO - Request Parameters - Page 1:
2025-07-27 19:30:42,375 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 19:30:42,375 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 19:30:45,376 - INFO - Response - Page 1:
2025-07-27 19:30:45,376 - INFO - 第 1 页获取到 50 条记录
2025-07-27 19:30:45,876 - INFO - Request Parameters - Page 2:
2025-07-27 19:30:45,876 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 19:30:45,876 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 19:30:46,549 - INFO - Response - Page 2:
2025-07-27 19:30:46,549 - INFO - 第 2 页获取到 50 条记录
2025-07-27 19:30:47,064 - INFO - Request Parameters - Page 3:
2025-07-27 19:30:47,064 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 19:30:47,064 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 19:30:47,815 - INFO - Response - Page 3:
2025-07-27 19:30:47,815 - INFO - 第 3 页获取到 50 条记录
2025-07-27 19:30:48,330 - INFO - Request Parameters - Page 4:
2025-07-27 19:30:48,330 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 19:30:48,330 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 19:30:49,065 - INFO - Response - Page 4:
2025-07-27 19:30:49,065 - INFO - 第 4 页获取到 50 条记录
2025-07-27 19:30:49,565 - INFO - Request Parameters - Page 5:
2025-07-27 19:30:49,565 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 19:30:49,565 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 19:30:50,284 - INFO - Response - Page 5:
2025-07-27 19:30:50,284 - INFO - 第 5 页获取到 50 条记录
2025-07-27 19:30:50,800 - INFO - Request Parameters - Page 6:
2025-07-27 19:30:50,800 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 19:30:50,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 19:30:51,566 - INFO - Response - Page 6:
2025-07-27 19:30:51,566 - INFO - 第 6 页获取到 50 条记录
2025-07-27 19:30:52,082 - INFO - Request Parameters - Page 7:
2025-07-27 19:30:52,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 19:30:52,082 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 19:30:52,817 - INFO - Response - Page 7:
2025-07-27 19:30:52,817 - INFO - 第 7 页获取到 50 条记录
2025-07-27 19:30:53,332 - INFO - Request Parameters - Page 8:
2025-07-27 19:30:53,332 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 19:30:53,332 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 19:31:00,304 - INFO - Response - Page 8:
2025-07-27 19:31:00,304 - INFO - 第 8 页获取到 50 条记录
2025-07-27 19:31:00,820 - INFO - Request Parameters - Page 9:
2025-07-27 19:31:00,820 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 19:31:00,820 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 19:31:01,680 - INFO - Response - Page 9:
2025-07-27 19:31:01,680 - INFO - 第 9 页获取到 40 条记录
2025-07-27 19:31:02,195 - INFO - 查询完成，共获取到 440 条记录
2025-07-27 19:31:02,195 - INFO - 获取到 440 条表单数据
2025-07-27 19:31:02,195 - INFO - 当前日期 2025-07-25 有 6 条MySQL数据需要处理
2025-07-27 19:31:02,195 - INFO - 开始批量插入 6 条新记录
2025-07-27 19:31:02,352 - INFO - 批量插入响应状态码: 200
2025-07-27 19:31:02,352 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 27 Jul 2025 11:31:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '300', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '06CB8D26-6403-7966-B585-0F8E194AF03C', 'x-acs-trace-id': '90af07fe714453fe195b2abbef18e456', 'etag': '3EUhzyPYNnGXq9LKOS5SiyQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-27 19:31:02,352 - INFO - 批量插入响应体: {'result': ['FINST-0P766Q81KJIXB3DFAYP9BBH13FPP2W0VLLLDMB4', 'FINST-0P766Q81KJIXB3DFAYP9BBH13FPP2W0VLLLDMC4', 'FINST-0P766Q81KJIXB3DFAYP9BBH13FPP2W0VLLLDMD4', 'FINST-0P766Q81KJIXB3DFAYP9BBH13FPP2W0VLLLDME4', 'FINST-0P766Q81KJIXB3DFAYP9BBH13FPP2W0VLLLDMF4', 'FINST-0P766Q81KJIXB3DFAYP9BBH13FPP2W0VLLLDMG4']}
2025-07-27 19:31:02,352 - INFO - 批量插入表单数据成功，批次 1，共 6 条记录
2025-07-27 19:31:02,352 - INFO - 成功插入的数据ID: ['FINST-0P766Q81KJIXB3DFAYP9BBH13FPP2W0VLLLDMB4', 'FINST-0P766Q81KJIXB3DFAYP9BBH13FPP2W0VLLLDMC4', 'FINST-0P766Q81KJIXB3DFAYP9BBH13FPP2W0VLLLDMD4', 'FINST-0P766Q81KJIXB3DFAYP9BBH13FPP2W0VLLLDME4', 'FINST-0P766Q81KJIXB3DFAYP9BBH13FPP2W0VLLLDMF4', 'FINST-0P766Q81KJIXB3DFAYP9BBH13FPP2W0VLLLDMG4']
2025-07-27 19:31:07,369 - INFO - 批量插入完成，共 6 条记录
2025-07-27 19:31:07,369 - INFO - 日期 2025-07-25 处理完成 - 更新: 0 条，插入: 6 条，错误: 0 条
2025-07-27 19:31:07,369 - INFO - 开始处理日期: 2025-07-26
2025-07-27 19:31:07,369 - INFO - Request Parameters - Page 1:
2025-07-27 19:31:07,369 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 19:31:07,369 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 19:31:08,088 - INFO - Response - Page 1:
2025-07-27 19:31:08,088 - INFO - 第 1 页获取到 50 条记录
2025-07-27 19:31:08,589 - INFO - Request Parameters - Page 2:
2025-07-27 19:31:08,589 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 19:31:08,589 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 19:31:09,292 - INFO - Response - Page 2:
2025-07-27 19:31:09,292 - INFO - 第 2 页获取到 50 条记录
2025-07-27 19:31:09,808 - INFO - Request Parameters - Page 3:
2025-07-27 19:31:09,808 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 19:31:09,808 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 19:31:10,589 - INFO - Response - Page 3:
2025-07-27 19:31:10,589 - INFO - 第 3 页获取到 50 条记录
2025-07-27 19:31:11,089 - INFO - Request Parameters - Page 4:
2025-07-27 19:31:11,089 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 19:31:11,089 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 19:31:11,840 - INFO - Response - Page 4:
2025-07-27 19:31:11,840 - INFO - 第 4 页获取到 50 条记录
2025-07-27 19:31:12,340 - INFO - Request Parameters - Page 5:
2025-07-27 19:31:12,340 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 19:31:12,340 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 19:31:13,075 - INFO - Response - Page 5:
2025-07-27 19:31:13,075 - INFO - 第 5 页获取到 50 条记录
2025-07-27 19:31:13,590 - INFO - Request Parameters - Page 6:
2025-07-27 19:31:13,590 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 19:31:13,590 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 19:31:14,310 - INFO - Response - Page 6:
2025-07-27 19:31:14,310 - INFO - 第 6 页获取到 50 条记录
2025-07-27 19:31:14,825 - INFO - Request Parameters - Page 7:
2025-07-27 19:31:14,825 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 19:31:14,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 19:31:15,560 - INFO - Response - Page 7:
2025-07-27 19:31:15,560 - INFO - 第 7 页获取到 50 条记录
2025-07-27 19:31:16,060 - INFO - Request Parameters - Page 8:
2025-07-27 19:31:16,060 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 19:31:16,060 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 19:31:16,779 - INFO - Response - Page 8:
2025-07-27 19:31:16,779 - INFO - 第 8 页获取到 50 条记录
2025-07-27 19:31:17,279 - INFO - Request Parameters - Page 9:
2025-07-27 19:31:17,279 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 19:31:17,279 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 19:31:17,998 - INFO - Response - Page 9:
2025-07-27 19:31:17,998 - INFO - 第 9 页获取到 50 条记录
2025-07-27 19:31:18,499 - INFO - Request Parameters - Page 10:
2025-07-27 19:31:18,499 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 19:31:18,499 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 19:31:19,077 - INFO - Response - Page 10:
2025-07-27 19:31:19,077 - INFO - 第 10 页获取到 12 条记录
2025-07-27 19:31:19,593 - INFO - 查询完成，共获取到 462 条记录
2025-07-27 19:31:19,593 - INFO - 获取到 462 条表单数据
2025-07-27 19:31:19,593 - INFO - 当前日期 2025-07-26 有 147 条MySQL数据需要处理
2025-07-27 19:31:19,593 - INFO - 日期 2025-07-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-27 19:31:19,593 - INFO - 开始处理日期: 2025-07-27
2025-07-27 19:31:19,593 - INFO - Request Parameters - Page 1:
2025-07-27 19:31:19,593 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 19:31:19,593 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 19:31:20,124 - INFO - Response - Page 1:
2025-07-27 19:31:20,124 - INFO - 第 1 页获取到 6 条记录
2025-07-27 19:31:20,625 - INFO - 查询完成，共获取到 6 条记录
2025-07-27 19:31:20,625 - INFO - 获取到 6 条表单数据
2025-07-27 19:31:20,625 - INFO - 当前日期 2025-07-27 有 6 条MySQL数据需要处理
2025-07-27 19:31:20,625 - INFO - 日期 2025-07-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-27 19:31:20,625 - INFO - 数据同步完成！更新: 0 条，插入: 6 条，错误: 1 条
2025-07-27 19:32:20,664 - INFO - 开始同步昨天与今天的销售数据: 2025-07-26 至 2025-07-27
2025-07-27 19:32:20,664 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-27 19:32:20,664 - INFO - 查询参数: ('2025-07-26', '2025-07-27')
2025-07-27 19:32:20,820 - INFO - MySQL查询成功，时间段: 2025-07-26 至 2025-07-27，共获取 482 条记录
2025-07-27 19:32:20,820 - INFO - 获取到 2 个日期需要处理: ['2025-07-26', '2025-07-27']
2025-07-27 19:32:20,836 - INFO - 开始处理日期: 2025-07-26
2025-07-27 19:32:20,836 - INFO - Request Parameters - Page 1:
2025-07-27 19:32:20,836 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 19:32:20,836 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 19:32:21,555 - INFO - Response - Page 1:
2025-07-27 19:32:21,555 - INFO - 第 1 页获取到 50 条记录
2025-07-27 19:32:22,071 - INFO - Request Parameters - Page 2:
2025-07-27 19:32:22,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 19:32:22,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 19:32:22,759 - INFO - Response - Page 2:
2025-07-27 19:32:22,759 - INFO - 第 2 页获取到 50 条记录
2025-07-27 19:32:23,274 - INFO - Request Parameters - Page 3:
2025-07-27 19:32:23,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 19:32:23,274 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 19:32:23,993 - INFO - Response - Page 3:
2025-07-27 19:32:23,993 - INFO - 第 3 页获取到 50 条记录
2025-07-27 19:32:24,509 - INFO - Request Parameters - Page 4:
2025-07-27 19:32:24,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 19:32:24,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 19:32:25,228 - INFO - Response - Page 4:
2025-07-27 19:32:25,228 - INFO - 第 4 页获取到 50 条记录
2025-07-27 19:32:25,728 - INFO - Request Parameters - Page 5:
2025-07-27 19:32:25,728 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 19:32:25,728 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 19:32:26,447 - INFO - Response - Page 5:
2025-07-27 19:32:26,447 - INFO - 第 5 页获取到 50 条记录
2025-07-27 19:32:26,963 - INFO - Request Parameters - Page 6:
2025-07-27 19:32:26,963 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 19:32:26,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 19:32:27,698 - INFO - Response - Page 6:
2025-07-27 19:32:27,698 - INFO - 第 6 页获取到 50 条记录
2025-07-27 19:32:28,214 - INFO - Request Parameters - Page 7:
2025-07-27 19:32:28,214 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 19:32:28,214 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 19:32:28,917 - INFO - Response - Page 7:
2025-07-27 19:32:28,917 - INFO - 第 7 页获取到 50 条记录
2025-07-27 19:32:29,433 - INFO - Request Parameters - Page 8:
2025-07-27 19:32:29,433 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 19:32:29,433 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 19:32:30,136 - INFO - Response - Page 8:
2025-07-27 19:32:30,136 - INFO - 第 8 页获取到 50 条记录
2025-07-27 19:32:30,652 - INFO - Request Parameters - Page 9:
2025-07-27 19:32:30,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 19:32:30,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 19:32:31,356 - INFO - Response - Page 9:
2025-07-27 19:32:31,356 - INFO - 第 9 页获取到 50 条记录
2025-07-27 19:32:31,871 - INFO - Request Parameters - Page 10:
2025-07-27 19:32:31,871 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 19:32:31,871 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 19:32:32,419 - INFO - Response - Page 10:
2025-07-27 19:32:32,419 - INFO - 第 10 页获取到 12 条记录
2025-07-27 19:32:32,919 - INFO - 查询完成，共获取到 462 条记录
2025-07-27 19:32:32,919 - INFO - 获取到 462 条表单数据
2025-07-27 19:32:32,919 - INFO - 当前日期 2025-07-26 有 462 条MySQL数据需要处理
2025-07-27 19:32:32,934 - INFO - 日期 2025-07-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-27 19:32:32,934 - INFO - 开始处理日期: 2025-07-27
2025-07-27 19:32:32,934 - INFO - Request Parameters - Page 1:
2025-07-27 19:32:32,934 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 19:32:32,934 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 19:32:33,466 - INFO - Response - Page 1:
2025-07-27 19:32:33,466 - INFO - 第 1 页获取到 6 条记录
2025-07-27 19:32:33,966 - INFO - 查询完成，共获取到 6 条记录
2025-07-27 19:32:33,966 - INFO - 获取到 6 条表单数据
2025-07-27 19:32:33,966 - INFO - 当前日期 2025-07-27 有 6 条MySQL数据需要处理
2025-07-27 19:32:33,966 - INFO - 日期 2025-07-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-27 19:32:33,966 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-27 19:32:33,966 - INFO - 同步完成
2025-07-27 22:30:34,105 - INFO - 使用默认增量同步（当天更新数据）
2025-07-27 22:30:34,105 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-27 22:30:34,105 - INFO - 查询参数: ('2025-07-27',)
2025-07-27 22:30:34,276 - INFO - MySQL查询成功，增量数据（日期: 2025-07-27），共获取 205 条记录
2025-07-27 22:30:34,276 - INFO - 获取到 4 个日期需要处理: ['2025-07-24', '2025-07-25', '2025-07-26', '2025-07-27']
2025-07-27 22:30:34,276 - INFO - 开始处理日期: 2025-07-24
2025-07-27 22:30:34,276 - INFO - Request Parameters - Page 1:
2025-07-27 22:30:34,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 22:30:34,276 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 22:30:42,405 - ERROR - 处理日期 2025-07-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: BE58D2F2-75AC-7E58-9046-2DCF1AAD547B Response: {'code': 'ServiceUnavailable', 'requestid': 'BE58D2F2-75AC-7E58-9046-2DCF1AAD547B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: BE58D2F2-75AC-7E58-9046-2DCF1AAD547B)
2025-07-27 22:30:42,405 - INFO - 开始处理日期: 2025-07-25
2025-07-27 22:30:42,405 - INFO - Request Parameters - Page 1:
2025-07-27 22:30:42,405 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 22:30:42,405 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 22:30:50,001 - INFO - Response - Page 1:
2025-07-27 22:30:50,001 - INFO - 第 1 页获取到 50 条记录
2025-07-27 22:30:50,502 - INFO - Request Parameters - Page 2:
2025-07-27 22:30:50,502 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 22:30:50,502 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 22:30:51,268 - INFO - Response - Page 2:
2025-07-27 22:30:51,268 - INFO - 第 2 页获取到 50 条记录
2025-07-27 22:30:51,783 - INFO - Request Parameters - Page 3:
2025-07-27 22:30:51,783 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 22:30:51,783 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 22:30:52,487 - INFO - Response - Page 3:
2025-07-27 22:30:52,487 - INFO - 第 3 页获取到 50 条记录
2025-07-27 22:30:53,003 - INFO - Request Parameters - Page 4:
2025-07-27 22:30:53,003 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 22:30:53,003 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 22:30:53,659 - INFO - Response - Page 4:
2025-07-27 22:30:53,659 - INFO - 第 4 页获取到 50 条记录
2025-07-27 22:30:54,159 - INFO - Request Parameters - Page 5:
2025-07-27 22:30:54,159 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 22:30:54,159 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 22:30:54,941 - INFO - Response - Page 5:
2025-07-27 22:30:54,941 - INFO - 第 5 页获取到 50 条记录
2025-07-27 22:30:55,457 - INFO - Request Parameters - Page 6:
2025-07-27 22:30:55,457 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 22:30:55,457 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 22:30:56,176 - INFO - Response - Page 6:
2025-07-27 22:30:56,176 - INFO - 第 6 页获取到 50 条记录
2025-07-27 22:30:56,692 - INFO - Request Parameters - Page 7:
2025-07-27 22:30:56,692 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 22:30:56,692 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 22:30:57,395 - INFO - Response - Page 7:
2025-07-27 22:30:57,395 - INFO - 第 7 页获取到 50 条记录
2025-07-27 22:30:57,895 - INFO - Request Parameters - Page 8:
2025-07-27 22:30:57,895 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 22:30:57,895 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 22:30:58,661 - INFO - Response - Page 8:
2025-07-27 22:30:58,661 - INFO - 第 8 页获取到 50 条记录
2025-07-27 22:30:59,161 - INFO - Request Parameters - Page 9:
2025-07-27 22:30:59,161 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 22:30:59,161 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753372800000, 1753459199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 22:30:59,833 - INFO - Response - Page 9:
2025-07-27 22:30:59,833 - INFO - 第 9 页获取到 46 条记录
2025-07-27 22:31:00,334 - INFO - 查询完成，共获取到 446 条记录
2025-07-27 22:31:00,334 - INFO - 获取到 446 条表单数据
2025-07-27 22:31:00,334 - INFO - 当前日期 2025-07-25 有 6 条MySQL数据需要处理
2025-07-27 22:31:00,334 - INFO - 日期 2025-07-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-27 22:31:00,334 - INFO - 开始处理日期: 2025-07-26
2025-07-27 22:31:00,334 - INFO - Request Parameters - Page 1:
2025-07-27 22:31:00,334 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 22:31:00,334 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 22:31:01,053 - INFO - Response - Page 1:
2025-07-27 22:31:01,053 - INFO - 第 1 页获取到 50 条记录
2025-07-27 22:31:01,553 - INFO - Request Parameters - Page 2:
2025-07-27 22:31:01,553 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 22:31:01,553 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 22:31:02,272 - INFO - Response - Page 2:
2025-07-27 22:31:02,272 - INFO - 第 2 页获取到 50 条记录
2025-07-27 22:31:02,772 - INFO - Request Parameters - Page 3:
2025-07-27 22:31:02,772 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 22:31:02,772 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 22:31:03,429 - INFO - Response - Page 3:
2025-07-27 22:31:03,429 - INFO - 第 3 页获取到 50 条记录
2025-07-27 22:31:03,944 - INFO - Request Parameters - Page 4:
2025-07-27 22:31:03,944 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 22:31:03,944 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 22:31:04,601 - INFO - Response - Page 4:
2025-07-27 22:31:04,601 - INFO - 第 4 页获取到 50 条记录
2025-07-27 22:31:05,101 - INFO - Request Parameters - Page 5:
2025-07-27 22:31:05,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 22:31:05,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 22:31:05,820 - INFO - Response - Page 5:
2025-07-27 22:31:05,820 - INFO - 第 5 页获取到 50 条记录
2025-07-27 22:31:06,336 - INFO - Request Parameters - Page 6:
2025-07-27 22:31:06,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 22:31:06,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 22:31:07,071 - INFO - Response - Page 6:
2025-07-27 22:31:07,071 - INFO - 第 6 页获取到 50 条记录
2025-07-27 22:31:07,571 - INFO - Request Parameters - Page 7:
2025-07-27 22:31:07,571 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 22:31:07,571 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 22:31:08,259 - INFO - Response - Page 7:
2025-07-27 22:31:08,259 - INFO - 第 7 页获取到 50 条记录
2025-07-27 22:31:08,775 - INFO - Request Parameters - Page 8:
2025-07-27 22:31:08,775 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 22:31:08,775 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 22:31:09,478 - INFO - Response - Page 8:
2025-07-27 22:31:09,478 - INFO - 第 8 页获取到 50 条记录
2025-07-27 22:31:09,978 - INFO - Request Parameters - Page 9:
2025-07-27 22:31:09,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 22:31:09,978 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 22:31:10,728 - INFO - Response - Page 9:
2025-07-27 22:31:10,728 - INFO - 第 9 页获取到 50 条记录
2025-07-27 22:31:11,229 - INFO - Request Parameters - Page 10:
2025-07-27 22:31:11,229 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 22:31:11,229 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 22:31:11,776 - INFO - Response - Page 10:
2025-07-27 22:31:11,776 - INFO - 第 10 页获取到 12 条记录
2025-07-27 22:31:12,292 - INFO - 查询完成，共获取到 462 条记录
2025-07-27 22:31:12,292 - INFO - 获取到 462 条表单数据
2025-07-27 22:31:12,292 - INFO - 当前日期 2025-07-26 有 147 条MySQL数据需要处理
2025-07-27 22:31:12,292 - INFO - 日期 2025-07-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-27 22:31:12,292 - INFO - 开始处理日期: 2025-07-27
2025-07-27 22:31:12,292 - INFO - Request Parameters - Page 1:
2025-07-27 22:31:12,292 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 22:31:12,292 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 22:31:12,823 - INFO - Response - Page 1:
2025-07-27 22:31:12,823 - INFO - 第 1 页获取到 6 条记录
2025-07-27 22:31:13,339 - INFO - 查询完成，共获取到 6 条记录
2025-07-27 22:31:13,339 - INFO - 获取到 6 条表单数据
2025-07-27 22:31:13,339 - INFO - 当前日期 2025-07-27 有 49 条MySQL数据需要处理
2025-07-27 22:31:13,339 - INFO - 开始批量插入 43 条新记录
2025-07-27 22:31:13,589 - INFO - 批量插入响应状态码: 200
2025-07-27 22:31:13,589 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 27 Jul 2025 14:31:09 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2076', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'A700D07C-45E8-78A6-99BA-93486838DD76', 'x-acs-trace-id': 'a97bb0ab4ac2db44b135a31a559b5cfc', 'etag': '26N0tkNst3CnWuyiZUFAzOQ6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-27 22:31:13,589 - INFO - 批量插入响应体: {'result': ['FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDM0X', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDM1X', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDM2X', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDM3X', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDM4X', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDM5X', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDM6X', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDM7X', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDM8X', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDM9X', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMAX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMBX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMCX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMDX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMEX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMFX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMGX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMHX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMIX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMJX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMKX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMLX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMMX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMNX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMOX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMPX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMQX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMRX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMSX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMTX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMUX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMVX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMWX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMXX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMYX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMZX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDM0Y', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDM1Y', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDM2Y', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDM3Y', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDM4Y', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDM5Y', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDM6Y']}
2025-07-27 22:31:13,589 - INFO - 批量插入表单数据成功，批次 1，共 43 条记录
2025-07-27 22:31:13,589 - INFO - 成功插入的数据ID: ['FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDM0X', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDM1X', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDM2X', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDM3X', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDM4X', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDM5X', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDM6X', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDM7X', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDM8X', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDM9X', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMAX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMBX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMCX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMDX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMEX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMFX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMGX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMHX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMIX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMJX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMKX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMLX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMMX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMNX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMOX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMPX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMQX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMRX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMSX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMTX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMUX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMVX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMWX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMXX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMYX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDMZX', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDM0Y', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDM1Y', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDM2Y', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDM3Y', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDM4Y', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDM5Y', 'FINST-VEC667D1GDHXYA06A9LXJ56NF8AO2GPH1SLDM6Y']
2025-07-27 22:31:18,607 - INFO - 批量插入完成，共 43 条记录
2025-07-27 22:31:18,607 - INFO - 日期 2025-07-27 处理完成 - 更新: 0 条，插入: 43 条，错误: 0 条
2025-07-27 22:31:18,607 - INFO - 数据同步完成！更新: 0 条，插入: 43 条，错误: 1 条
2025-07-27 22:32:18,646 - INFO - 开始同步昨天与今天的销售数据: 2025-07-26 至 2025-07-27
2025-07-27 22:32:18,646 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-27 22:32:18,646 - INFO - 查询参数: ('2025-07-26', '2025-07-27')
2025-07-27 22:32:18,818 - INFO - MySQL查询成功，时间段: 2025-07-26 至 2025-07-27，共获取 525 条记录
2025-07-27 22:32:18,818 - INFO - 获取到 2 个日期需要处理: ['2025-07-26', '2025-07-27']
2025-07-27 22:32:18,818 - INFO - 开始处理日期: 2025-07-26
2025-07-27 22:32:18,818 - INFO - Request Parameters - Page 1:
2025-07-27 22:32:18,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 22:32:18,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 22:32:19,537 - INFO - Response - Page 1:
2025-07-27 22:32:19,537 - INFO - 第 1 页获取到 50 条记录
2025-07-27 22:32:20,053 - INFO - Request Parameters - Page 2:
2025-07-27 22:32:20,053 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 22:32:20,053 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 22:32:20,787 - INFO - Response - Page 2:
2025-07-27 22:32:20,787 - INFO - 第 2 页获取到 50 条记录
2025-07-27 22:32:21,303 - INFO - Request Parameters - Page 3:
2025-07-27 22:32:21,303 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 22:32:21,303 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 22:32:22,007 - INFO - Response - Page 3:
2025-07-27 22:32:22,007 - INFO - 第 3 页获取到 50 条记录
2025-07-27 22:32:22,523 - INFO - Request Parameters - Page 4:
2025-07-27 22:32:22,523 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 22:32:22,523 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 22:32:23,257 - INFO - Response - Page 4:
2025-07-27 22:32:23,257 - INFO - 第 4 页获取到 50 条记录
2025-07-27 22:32:23,757 - INFO - Request Parameters - Page 5:
2025-07-27 22:32:23,757 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 22:32:23,757 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 22:32:24,461 - INFO - Response - Page 5:
2025-07-27 22:32:24,461 - INFO - 第 5 页获取到 50 条记录
2025-07-27 22:32:24,977 - INFO - Request Parameters - Page 6:
2025-07-27 22:32:24,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 22:32:24,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 22:32:25,680 - INFO - Response - Page 6:
2025-07-27 22:32:25,680 - INFO - 第 6 页获取到 50 条记录
2025-07-27 22:32:26,180 - INFO - Request Parameters - Page 7:
2025-07-27 22:32:26,180 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 22:32:26,180 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 22:32:26,884 - INFO - Response - Page 7:
2025-07-27 22:32:26,884 - INFO - 第 7 页获取到 50 条记录
2025-07-27 22:32:27,399 - INFO - Request Parameters - Page 8:
2025-07-27 22:32:27,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 22:32:27,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 22:32:28,087 - INFO - Response - Page 8:
2025-07-27 22:32:28,087 - INFO - 第 8 页获取到 50 条记录
2025-07-27 22:32:28,587 - INFO - Request Parameters - Page 9:
2025-07-27 22:32:28,587 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 22:32:28,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 22:32:29,400 - INFO - Response - Page 9:
2025-07-27 22:32:29,400 - INFO - 第 9 页获取到 50 条记录
2025-07-27 22:32:29,916 - INFO - Request Parameters - Page 10:
2025-07-27 22:32:29,916 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 22:32:29,916 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753459200000, 1753545599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 22:32:30,463 - INFO - Response - Page 10:
2025-07-27 22:32:30,463 - INFO - 第 10 页获取到 12 条记录
2025-07-27 22:32:30,963 - INFO - 查询完成，共获取到 462 条记录
2025-07-27 22:32:30,963 - INFO - 获取到 462 条表单数据
2025-07-27 22:32:30,963 - INFO - 当前日期 2025-07-26 有 462 条MySQL数据需要处理
2025-07-27 22:32:30,979 - INFO - 日期 2025-07-26 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-27 22:32:30,979 - INFO - 开始处理日期: 2025-07-27
2025-07-27 22:32:30,979 - INFO - Request Parameters - Page 1:
2025-07-27 22:32:30,979 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-27 22:32:30,979 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753545600000, 1753631999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-27 22:32:31,745 - INFO - Response - Page 1:
2025-07-27 22:32:31,745 - INFO - 第 1 页获取到 49 条记录
2025-07-27 22:32:32,261 - INFO - 查询完成，共获取到 49 条记录
2025-07-27 22:32:32,261 - INFO - 获取到 49 条表单数据
2025-07-27 22:32:32,261 - INFO - 当前日期 2025-07-27 有 49 条MySQL数据需要处理
2025-07-27 22:32:32,261 - INFO - 日期 2025-07-27 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-27 22:32:32,261 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-27 22:32:32,261 - INFO - 同步完成
