# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
#操作钉钉获取token
import os
import sys

from typing import List

from alibabacloud_dingtalk.oauth2_1_0.client import Client as dingtalkoauth2_1_0Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dingtalk.oauth2_1_0 import models as dingtalkoauth_2__1__0_models
from alibabacloud_tea_util.client import Client as UtilClient


class token:

    def __init__(self):
        access_token = ''

    @staticmethod
    def create_client() -> dingtalkoauth2_1_0Client:
        """
        使用 Token 初始化账号Client
        @return: Client
        @throws Exception
        """
        config = open_api_models.Config()
        config.protocol = 'https'
        config.region_id = 'central'
        return dingtalkoauth2_1_0Client(config)

    @staticmethod
    def get_token():
        client = token.create_client()
        get_access_token_request = dingtalkoauth_2__1__0_models.GetAccessTokenRequest(
            app_key='dingjr24s1lu7jk3xsgb',
            app_secret=
            'GWF7ysBn93WloqdN1pNl4FU4E0ZQKRPAr82TnjbPdFANtvAl4NBvE5oT83dYmont')
        try:
            rtn = client.get_access_token(get_access_token_request)
            access_token = rtn.body.access_token
            # 调用成功
            print('获取 access_token 成功', access_token)
        except Exception as err:
            if not UtilClient.empty(err.code) and not UtilClient.empty(
                    err.message):
                # err 中含有 code 和 message 属性，可帮助开发定位问题
                print('获取 access_token 失败', err.code, err.message)
        return access_token


if __name__ == '__main__':
    print(token.get_token())
