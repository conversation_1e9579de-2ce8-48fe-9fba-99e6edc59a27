2025-06-26 01:30:33,639 - INFO - 使用默认增量同步（当天更新数据）
2025-06-26 01:30:33,639 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-26 01:30:33,639 - INFO - 查询参数: ('2025-06-26',)
2025-06-26 01:30:33,717 - INFO - MySQL查询成功，增量数据（日期: 2025-06-26），共获取 0 条记录
2025-06-26 01:30:33,717 - ERROR - 未获取到MySQL数据
2025-06-26 01:31:33,732 - INFO - 开始同步昨天与今天的销售数据: 2025-06-25 至 2025-06-26
2025-06-26 01:31:33,732 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-26 01:31:33,732 - INFO - 查询参数: ('2025-06-25', '2025-06-26')
2025-06-26 01:31:33,857 - INFO - MySQL查询成功，时间段: 2025-06-25 至 2025-06-26，共获取 66 条记录
2025-06-26 01:31:33,857 - INFO - 获取到 1 个日期需要处理: ['2025-06-25']
2025-06-26 01:31:33,857 - INFO - 开始处理日期: 2025-06-25
2025-06-26 01:31:33,857 - INFO - Request Parameters - Page 1:
2025-06-26 01:31:33,857 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 01:31:33,857 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 01:31:41,982 - ERROR - 处理日期 2025-06-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B20E5B1E-FDF7-7FAB-8987-58A70AE0B9B2 Response: {'code': 'ServiceUnavailable', 'requestid': 'B20E5B1E-FDF7-7FAB-8987-58A70AE0B9B2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B20E5B1E-FDF7-7FAB-8987-58A70AE0B9B2)
2025-06-26 01:31:41,982 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-26 01:31:41,982 - INFO - 同步完成
2025-06-26 04:30:33,721 - INFO - 使用默认增量同步（当天更新数据）
2025-06-26 04:30:33,721 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-26 04:30:33,721 - INFO - 查询参数: ('2025-06-26',)
2025-06-26 04:30:33,862 - INFO - MySQL查询成功，增量数据（日期: 2025-06-26），共获取 1 条记录
2025-06-26 04:30:33,862 - INFO - 获取到 1 个日期需要处理: ['2025-06-25']
2025-06-26 04:30:33,862 - INFO - 开始处理日期: 2025-06-25
2025-06-26 04:30:33,862 - INFO - Request Parameters - Page 1:
2025-06-26 04:30:33,862 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 04:30:33,862 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 04:30:41,971 - ERROR - 处理日期 2025-06-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 0C9A57EA-A8C6-7193-B0A8-F115B85582A3 Response: {'code': 'ServiceUnavailable', 'requestid': '0C9A57EA-A8C6-7193-B0A8-F115B85582A3', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 0C9A57EA-A8C6-7193-B0A8-F115B85582A3)
2025-06-26 04:30:41,971 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-26 04:31:41,986 - INFO - 开始同步昨天与今天的销售数据: 2025-06-25 至 2025-06-26
2025-06-26 04:31:41,986 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-26 04:31:41,986 - INFO - 查询参数: ('2025-06-25', '2025-06-26')
2025-06-26 04:31:42,111 - INFO - MySQL查询成功，时间段: 2025-06-25 至 2025-06-26，共获取 67 条记录
2025-06-26 04:31:42,111 - INFO - 获取到 1 个日期需要处理: ['2025-06-25']
2025-06-26 04:31:42,111 - INFO - 开始处理日期: 2025-06-25
2025-06-26 04:31:42,111 - INFO - Request Parameters - Page 1:
2025-06-26 04:31:42,111 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 04:31:42,111 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 04:31:48,064 - INFO - Response - Page 1:
2025-06-26 04:31:48,064 - INFO - 第 1 页获取到 50 条记录
2025-06-26 04:31:48,580 - INFO - Request Parameters - Page 2:
2025-06-26 04:31:48,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 04:31:48,580 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 04:31:49,049 - INFO - Response - Page 2:
2025-06-26 04:31:49,049 - INFO - 第 2 页获取到 5 条记录
2025-06-26 04:31:49,564 - INFO - 查询完成，共获取到 55 条记录
2025-06-26 04:31:49,564 - INFO - 获取到 55 条表单数据
2025-06-26 04:31:49,564 - INFO - 当前日期 2025-06-25 有 64 条MySQL数据需要处理
2025-06-26 04:31:49,564 - INFO - 开始批量插入 9 条新记录
2025-06-26 04:31:49,736 - INFO - 批量插入响应状态码: 200
2025-06-26 04:31:49,736 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 25 Jun 2025 20:31:49 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '444', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F2C1A103-60C8-7DAD-ABC4-141A341D8026', 'x-acs-trace-id': '580e2faca06f832cf4e60f189d6670c5', 'etag': '4CUW6ncGWc9yJWQnrpYrT1Q4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-26 04:31:49,736 - INFO - 批量插入响应体: {'result': ['FINST-LFA66G91U6LWC7348DDHA9JPD6SZ2FJ2UECCMSG', 'FINST-LFA66G91U6LWC7348DDHA9JPD6SZ2FJ2UECCMTG', 'FINST-LFA66G91U6LWC7348DDHA9JPD6SZ2FJ2UECCMUG', 'FINST-LFA66G91U6LWC7348DDHA9JPD6SZ2FJ2UECCMVG', 'FINST-LFA66G91U6LWC7348DDHA9JPD6SZ2FJ2UECCMWG', 'FINST-LFA66G91U6LWC7348DDHA9JPD6SZ2FJ2UECCMXG', 'FINST-LFA66G91U6LWC7348DDHA9JPD6SZ2FJ2UECCMYG', 'FINST-LFA66G91U6LWC7348DDHA9JPD6SZ2FJ2UECCMZG', 'FINST-LFA66G91U6LWC7348DDHA9JPD6SZ2FJ2UECCM0H']}
2025-06-26 04:31:49,736 - INFO - 批量插入表单数据成功，批次 1，共 9 条记录
2025-06-26 04:31:49,736 - INFO - 成功插入的数据ID: ['FINST-LFA66G91U6LWC7348DDHA9JPD6SZ2FJ2UECCMSG', 'FINST-LFA66G91U6LWC7348DDHA9JPD6SZ2FJ2UECCMTG', 'FINST-LFA66G91U6LWC7348DDHA9JPD6SZ2FJ2UECCMUG', 'FINST-LFA66G91U6LWC7348DDHA9JPD6SZ2FJ2UECCMVG', 'FINST-LFA66G91U6LWC7348DDHA9JPD6SZ2FJ2UECCMWG', 'FINST-LFA66G91U6LWC7348DDHA9JPD6SZ2FJ2UECCMXG', 'FINST-LFA66G91U6LWC7348DDHA9JPD6SZ2FJ2UECCMYG', 'FINST-LFA66G91U6LWC7348DDHA9JPD6SZ2FJ2UECCMZG', 'FINST-LFA66G91U6LWC7348DDHA9JPD6SZ2FJ2UECCM0H']
2025-06-26 04:31:54,752 - INFO - 批量插入完成，共 9 条记录
2025-06-26 04:31:54,752 - INFO - 日期 2025-06-25 处理完成 - 更新: 0 条，插入: 9 条，错误: 0 条
2025-06-26 04:31:54,752 - INFO - 数据同步完成！更新: 0 条，插入: 9 条，错误: 0 条
2025-06-26 04:31:54,752 - INFO - 同步完成
2025-06-26 07:30:33,591 - INFO - 使用默认增量同步（当天更新数据）
2025-06-26 07:30:33,591 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-26 07:30:33,591 - INFO - 查询参数: ('2025-06-26',)
2025-06-26 07:30:33,731 - INFO - MySQL查询成功，增量数据（日期: 2025-06-26），共获取 1 条记录
2025-06-26 07:30:33,731 - INFO - 获取到 1 个日期需要处理: ['2025-06-25']
2025-06-26 07:30:33,731 - INFO - 开始处理日期: 2025-06-25
2025-06-26 07:30:33,731 - INFO - Request Parameters - Page 1:
2025-06-26 07:30:33,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 07:30:33,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 07:30:41,841 - ERROR - 处理日期 2025-06-25 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 9E9E744E-FC39-749D-BD61-4F1BDABD441E Response: {'code': 'ServiceUnavailable', 'requestid': '9E9E744E-FC39-749D-BD61-4F1BDABD441E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 9E9E744E-FC39-749D-BD61-4F1BDABD441E)
2025-06-26 07:30:41,841 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-06-26 07:31:41,856 - INFO - 开始同步昨天与今天的销售数据: 2025-06-25 至 2025-06-26
2025-06-26 07:31:41,856 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-26 07:31:41,856 - INFO - 查询参数: ('2025-06-25', '2025-06-26')
2025-06-26 07:31:41,981 - INFO - MySQL查询成功，时间段: 2025-06-25 至 2025-06-26，共获取 67 条记录
2025-06-26 07:31:41,981 - INFO - 获取到 1 个日期需要处理: ['2025-06-25']
2025-06-26 07:31:41,981 - INFO - 开始处理日期: 2025-06-25
2025-06-26 07:31:41,981 - INFO - Request Parameters - Page 1:
2025-06-26 07:31:41,981 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 07:31:41,981 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 07:31:42,700 - INFO - Response - Page 1:
2025-06-26 07:31:42,700 - INFO - 第 1 页获取到 50 条记录
2025-06-26 07:31:43,200 - INFO - Request Parameters - Page 2:
2025-06-26 07:31:43,200 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 07:31:43,200 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 07:31:48,997 - INFO - Response - Page 2:
2025-06-26 07:31:48,997 - INFO - 第 2 页获取到 14 条记录
2025-06-26 07:31:49,512 - INFO - 查询完成，共获取到 64 条记录
2025-06-26 07:31:49,512 - INFO - 获取到 64 条表单数据
2025-06-26 07:31:49,512 - INFO - 当前日期 2025-06-25 有 64 条MySQL数据需要处理
2025-06-26 07:31:49,512 - INFO - 日期 2025-06-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 07:31:49,512 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 07:31:49,512 - INFO - 同步完成
2025-06-26 10:30:34,210 - INFO - 使用默认增量同步（当天更新数据）
2025-06-26 10:30:34,210 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-26 10:30:34,210 - INFO - 查询参数: ('2025-06-26',)
2025-06-26 10:30:34,351 - INFO - MySQL查询成功，增量数据（日期: 2025-06-26），共获取 126 条记录
2025-06-26 10:30:34,351 - INFO - 获取到 2 个日期需要处理: ['2025-06-24', '2025-06-25']
2025-06-26 10:30:34,351 - INFO - 开始处理日期: 2025-06-24
2025-06-26 10:30:34,351 - INFO - Request Parameters - Page 1:
2025-06-26 10:30:34,351 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 10:30:34,351 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 10:30:41,132 - INFO - Response - Page 1:
2025-06-26 10:30:41,132 - INFO - 第 1 页获取到 50 条记录
2025-06-26 10:30:41,648 - INFO - Request Parameters - Page 2:
2025-06-26 10:30:41,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 10:30:41,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 10:30:49,773 - ERROR - 处理日期 2025-06-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 176E7DF0-238E-71BD-8BD2-B06A65A0437D Response: {'code': 'ServiceUnavailable', 'requestid': '176E7DF0-238E-71BD-8BD2-B06A65A0437D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 176E7DF0-238E-71BD-8BD2-B06A65A0437D)
2025-06-26 10:30:49,773 - INFO - 开始处理日期: 2025-06-25
2025-06-26 10:30:49,773 - INFO - Request Parameters - Page 1:
2025-06-26 10:30:49,773 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 10:30:49,773 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 10:30:51,445 - INFO - Response - Page 1:
2025-06-26 10:30:51,445 - INFO - 第 1 页获取到 50 条记录
2025-06-26 10:30:51,945 - INFO - Request Parameters - Page 2:
2025-06-26 10:30:51,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 10:30:51,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 10:30:52,476 - INFO - Response - Page 2:
2025-06-26 10:30:52,476 - INFO - 第 2 页获取到 14 条记录
2025-06-26 10:30:52,992 - INFO - 查询完成，共获取到 64 条记录
2025-06-26 10:30:52,992 - INFO - 获取到 64 条表单数据
2025-06-26 10:30:52,992 - INFO - 当前日期 2025-06-25 有 119 条MySQL数据需要处理
2025-06-26 10:30:52,992 - INFO - 开始批量插入 118 条新记录
2025-06-26 10:30:53,242 - INFO - 批量插入响应状态码: 200
2025-06-26 10:30:53,242 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 26 Jun 2025 02:30:53 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2396', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '757849DE-E4ED-7428-B9BF-E5691788C20B', 'x-acs-trace-id': '6208394f120e2c34ba54e2767b525e68', 'etag': '261Vr3fRAeKSnBGOp3KhlqA6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-26 10:30:53,242 - INFO - 批量插入响应体: {'result': ['FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR31QTNRCCMK', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR31QTNRCCML', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR31QTNRCCMM', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR31QTNRCCMN', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR31QTNRCCMO', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR31QTNRCCMP', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR31QTNRCCMQ', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR31QTNRCCMR', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR31QTNRCCMS', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR31QTNRCCMT', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR31QTNRCCMU', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR31QTNRCCMV', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR31QTNRCCMW', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR31QTNRCCMX', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR31QTNRCCMY', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMZ', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCM01', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCM11', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCM21', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCM31', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCM41', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCM51', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCM61', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCM71', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCM81', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCM91', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMA1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMB1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMC1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMD1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCME1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMF1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMG1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMH1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMI1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMJ1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMK1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCML1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMM1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMN1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMO1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMP1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMQ1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMR1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMS1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMT1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMU1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMV1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMW1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMX1']}
2025-06-26 10:30:53,242 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-26 10:30:53,242 - INFO - 成功插入的数据ID: ['FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR31QTNRCCMK', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR31QTNRCCML', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR31QTNRCCMM', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR31QTNRCCMN', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR31QTNRCCMO', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR31QTNRCCMP', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR31QTNRCCMQ', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR31QTNRCCMR', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR31QTNRCCMS', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR31QTNRCCMT', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR31QTNRCCMU', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR31QTNRCCMV', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR31QTNRCCMW', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR31QTNRCCMX', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR31QTNRCCMY', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMZ', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCM01', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCM11', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCM21', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCM31', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCM41', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCM51', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCM61', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCM71', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCM81', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCM91', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMA1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMB1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMC1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMD1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCME1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMF1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMG1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMH1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMI1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMJ1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMK1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCML1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMM1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMN1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMO1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMP1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMQ1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMR1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMS1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMT1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMU1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMV1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMW1', 'FINST-PGC66MB1WZLWOTJM7VFZK5NC1XDR32QTNRCCMX1']
2025-06-26 10:30:58,507 - INFO - 批量插入响应状态码: 200
2025-06-26 10:30:58,507 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 26 Jun 2025 02:30:58 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2382', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E3FA8194-50BF-70CC-A33E-4E60C4110E63', 'x-acs-trace-id': '7cff74b8044c360ee11e8fc0d09d1e59', 'etag': '23cAh1T0xUv51Cxyq5rXShw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-26 10:30:58,507 - INFO - 批量插入响应体: {'result': ['FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCM6', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCM7', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCM8', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCM9', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMA', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMB', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMC', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMD', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCME', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMF', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMG', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMH', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMI', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMJ', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMK', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCML', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMM', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMN', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMO', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMP', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMQ', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMR', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMS', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMT', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMU', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMV', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMW', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMX', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMY', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMZ', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCM01', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCM11', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCM21', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCM31', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCM41', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCM51', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCM61', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCM71', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCM81', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCM91', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMA1', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMB1', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMC1', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMD1', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCME1', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMF1', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMG1', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMH1', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMI1', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMJ1']}
2025-06-26 10:30:58,507 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-06-26 10:30:58,507 - INFO - 成功插入的数据ID: ['FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCM6', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCM7', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCM8', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCM9', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMA', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMB', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMC', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMD', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCME', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMF', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMG', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMH', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMI', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMJ', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMK', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCML', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMM', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMN', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMO', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMP', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMQ', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMR', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMS', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMT', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMU', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMV', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMW', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMX', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMY', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMZ', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCM01', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCM11', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCM21', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCM31', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCM41', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCM51', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCM61', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCM71', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCM81', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCM91', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMA1', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMB1', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMC1', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMD1', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCME1', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMF1', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMG1', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMH1', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMI1', 'FINST-E3G66QA180MW1EICFOEJJ7Y7WWG33KSXNRCCMJ1']
2025-06-26 10:31:03,679 - INFO - 批量插入响应状态码: 200
2025-06-26 10:31:03,679 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 26 Jun 2025 02:31:03 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '876', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '11C2DC4D-727F-75F5-A0A8-AC087C6E49EA', 'x-acs-trace-id': 'a5055e31cb211c2e82c1f731ae8c8091', 'etag': '8O44rXIxKcz92qGoWLSBAvQ6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-26 10:31:03,679 - INFO - 批量插入响应体: {'result': ['FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCMW2', 'FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCMX2', 'FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCMY2', 'FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCMZ2', 'FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCM03', 'FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCM13', 'FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCM23', 'FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCM33', 'FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCM43', 'FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCM53', 'FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCM63', 'FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCM73', 'FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCM83', 'FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCM93', 'FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCMA3', 'FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCMB3', 'FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCMC3', 'FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCMD3']}
2025-06-26 10:31:03,679 - INFO - 批量插入表单数据成功，批次 3，共 18 条记录
2025-06-26 10:31:03,679 - INFO - 成功插入的数据ID: ['FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCMW2', 'FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCMX2', 'FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCMY2', 'FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCMZ2', 'FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCM03', 'FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCM13', 'FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCM23', 'FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCM33', 'FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCM43', 'FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCM53', 'FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCM63', 'FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCM73', 'FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCM83', 'FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCM93', 'FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCMA3', 'FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCMB3', 'FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCMC3', 'FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCMD3']
2025-06-26 10:31:08,695 - INFO - 批量插入完成，共 118 条记录
2025-06-26 10:31:08,695 - INFO - 日期 2025-06-25 处理完成 - 更新: 0 条，插入: 118 条，错误: 0 条
2025-06-26 10:31:08,695 - INFO - 数据同步完成！更新: 0 条，插入: 118 条，错误: 1 条
2025-06-26 10:32:08,710 - INFO - 开始同步昨天与今天的销售数据: 2025-06-25 至 2025-06-26
2025-06-26 10:32:08,710 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-26 10:32:08,710 - INFO - 查询参数: ('2025-06-25', '2025-06-26')
2025-06-26 10:32:08,850 - INFO - MySQL查询成功，时间段: 2025-06-25 至 2025-06-26，共获取 427 条记录
2025-06-26 10:32:08,850 - INFO - 获取到 1 个日期需要处理: ['2025-06-25']
2025-06-26 10:32:08,850 - INFO - 开始处理日期: 2025-06-25
2025-06-26 10:32:08,850 - INFO - Request Parameters - Page 1:
2025-06-26 10:32:08,850 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 10:32:08,850 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 10:32:09,647 - INFO - Response - Page 1:
2025-06-26 10:32:09,647 - INFO - 第 1 页获取到 50 条记录
2025-06-26 10:32:10,147 - INFO - Request Parameters - Page 2:
2025-06-26 10:32:10,147 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 10:32:10,147 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 10:32:10,850 - INFO - Response - Page 2:
2025-06-26 10:32:10,850 - INFO - 第 2 页获取到 50 条记录
2025-06-26 10:32:11,350 - INFO - Request Parameters - Page 3:
2025-06-26 10:32:11,350 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 10:32:11,350 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 10:32:11,944 - INFO - Response - Page 3:
2025-06-26 10:32:11,944 - INFO - 第 3 页获取到 50 条记录
2025-06-26 10:32:12,444 - INFO - Request Parameters - Page 4:
2025-06-26 10:32:12,444 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 10:32:12,444 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 10:32:13,194 - INFO - Response - Page 4:
2025-06-26 10:32:13,194 - INFO - 第 4 页获取到 32 条记录
2025-06-26 10:32:13,710 - INFO - 查询完成，共获取到 182 条记录
2025-06-26 10:32:13,710 - INFO - 获取到 182 条表单数据
2025-06-26 10:32:13,710 - INFO - 当前日期 2025-06-25 有 418 条MySQL数据需要处理
2025-06-26 10:32:13,710 - INFO - 开始批量插入 236 条新记录
2025-06-26 10:32:13,975 - INFO - 批量插入响应状态码: 200
2025-06-26 10:32:13,975 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 26 Jun 2025 02:32:14 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '36EA63C6-7789-7853-BE0F-ED371F2F49BA', 'x-acs-trace-id': 'b910a413dcbffd8bc330d23222c12bce', 'etag': '24JOxXFg3c5UotclhojKWWQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-26 10:32:13,975 - INFO - 批量插入响应体: {'result': ['FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCM45', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCM55', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCM65', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCM75', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCM85', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCM95', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMA5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMB5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMC5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMD5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCME5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMF5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMG5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMH5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMI5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMJ5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMK5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCML5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMM5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMN5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMO5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMP5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMQ5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMR5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMS5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMT5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMU5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMV5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMW5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMX5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMY5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMZ5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCM06', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCM16', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCM26', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCM36', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCM46', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCM56', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCM66', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCM76', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCM86', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCM96', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMA6', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMB6', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMC6', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMD6', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCME6', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMF6', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMG6', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMH6']}
2025-06-26 10:32:13,975 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-06-26 10:32:13,975 - INFO - 成功插入的数据ID: ['FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCM45', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCM55', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCM65', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCM75', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCM85', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCM95', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMA5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMB5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMC5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMD5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCME5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMF5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMG5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMH5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMI5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMJ5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMK5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCML5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMM5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMN5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMO5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMP5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMQ5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMR5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMS5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMT5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMU5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMV5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMW5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMX5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMY5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMZ5', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCM06', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCM16', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCM26', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCM36', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCM46', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCM56', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCM66', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCM76', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCM86', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCM96', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMA6', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMB6', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMC6', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMD6', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCME6', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMF6', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMG6', 'FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCMH6']
2025-06-26 10:32:19,194 - INFO - 批量插入响应状态码: 200
2025-06-26 10:32:19,194 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 26 Jun 2025 02:32:19 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2396', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D1890618-90B4-7FAE-820A-7061F1C4C98D', 'x-acs-trace-id': 'b6508b5820e417522f434a38e1927373', 'etag': '2m5vB0z413U4s31yCx61teQ6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-26 10:32:19,194 - INFO - 批量插入响应体: {'result': ['FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMK', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCML', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMM', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMN', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMO', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMP', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMQ', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMR', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMS', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMT', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMU', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMV', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMW', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMX', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMY', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMZ', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCM01', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCM11', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCM21', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCM31', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCM41', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCM51', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCM61', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCM71', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCM81', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCM91', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMA1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMB1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMC1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMD1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCME1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMF1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMG1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMH1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMI1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMJ1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMK1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCML1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMM1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMN1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMO1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMP1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2V1OPRCCMQ1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2V1OPRCCMR1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2V1OPRCCMS1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2V1OPRCCMT1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2V1OPRCCMU1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2V1OPRCCMV1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2V1OPRCCMW1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2V1OPRCCMX1']}
2025-06-26 10:32:19,194 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-06-26 10:32:19,194 - INFO - 成功插入的数据ID: ['FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMK', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCML', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMM', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMN', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMO', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMP', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMQ', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMR', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMS', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMT', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMU', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMV', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMW', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMX', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMY', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMZ', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCM01', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCM11', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCM21', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCM31', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCM41', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCM51', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCM61', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCM71', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCM81', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCM91', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMA1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMB1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMC1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMD1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCME1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMF1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMG1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMH1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMI1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMJ1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMK1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCML1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMM1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMN1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMO1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2U1OPRCCMP1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2V1OPRCCMQ1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2V1OPRCCMR1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2V1OPRCCMS1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2V1OPRCCMT1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2V1OPRCCMU1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2V1OPRCCMV1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2V1OPRCCMW1', 'FINST-LLF66F71IZLW86CK853JXCKT9Q7O2V1OPRCCMX1']
2025-06-26 10:32:24,444 - INFO - 批量插入响应状态码: 200
2025-06-26 10:32:24,460 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 26 Jun 2025 02:32:24 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2391', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'CC38E512-BBB7-7E90-A079-248D24BB2DDD', 'x-acs-trace-id': '6fefcd4500e8d584da8a285f9ef23d9f', 'etag': '2ebCTEDiuKEthVJlHcWrazg1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-26 10:32:24,460 - INFO - 批量插入响应体: {'result': ['FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMF', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMG', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMH', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMI', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMJ', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMK', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCML', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMM', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMN', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMO', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMP', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMQ', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMR', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMS', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMT', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMU', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMV', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMW', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMX', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMY', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMZ', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCM01', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCM11', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCM21', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCM31', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCM41', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCM51', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCM61', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCM71', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCM81', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCM91', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMA1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMB1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMC1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMD1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCME1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMF1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMG1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMH1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMI1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMJ1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMK1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCML1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMM1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMN1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMO1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMP1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMQ1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMR1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMS1']}
2025-06-26 10:32:24,460 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-06-26 10:32:24,460 - INFO - 成功插入的数据ID: ['FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMF', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMG', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMH', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMI', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMJ', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMK', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCML', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMM', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMN', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMO', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMP', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMQ', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMR', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMS', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMT', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMU', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMV', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMW', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMX', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMY', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMZ', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCM01', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCM11', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCM21', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCM31', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCM41', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCM51', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCM61', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCM71', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCM81', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCM91', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMA1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMB1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMC1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMD1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCME1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMF1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMG1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMH1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMI1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMJ1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMK1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCML1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMM1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMN1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMO1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMP1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMQ1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMR1', 'FINST-OIF66RB10ZLWYI0MCQIHB75V4DPC2Q3SPRCCMS1']
2025-06-26 10:32:29,710 - INFO - 批量插入响应状态码: 200
2025-06-26 10:32:29,710 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 26 Jun 2025 02:32:29 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7EFE967E-16C9-7AE0-A986-9CC9DFC4CCA6', 'x-acs-trace-id': '60cbc4305c8c5f7889e961d59ec24929', 'etag': '2xuyrt8cuVM2fuaBMl0Y32A2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-26 10:32:29,710 - INFO - 批量插入响应体: {'result': ['FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMM7', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMN7', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMO7', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMP7', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMQ7', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMR7', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMS7', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMT7', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMU7', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMV7', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMW7', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMX7', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMY7', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMZ7', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCM08', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCM18', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCM28', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCM38', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCM48', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCM58', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCM68', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCM78', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCM88', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCM98', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMA8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMB8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMC8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMD8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCME8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMF8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMG8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMH8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMI8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMJ8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMK8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2W5WPRCCML8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2W5WPRCCMM8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2W5WPRCCMN8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2W5WPRCCMO8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2W5WPRCCMP8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2W5WPRCCMQ8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2W5WPRCCMR8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2W5WPRCCMS8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2W5WPRCCMT8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2W5WPRCCMU8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2W5WPRCCMV8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2W5WPRCCMW8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2W5WPRCCMX8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2W5WPRCCMY8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2W5WPRCCMZ8']}
2025-06-26 10:32:29,710 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-06-26 10:32:29,710 - INFO - 成功插入的数据ID: ['FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMM7', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMN7', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMO7', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMP7', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMQ7', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMR7', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMS7', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMT7', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMU7', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMV7', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMW7', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMX7', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMY7', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMZ7', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCM08', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCM18', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCM28', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCM38', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCM48', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCM58', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCM68', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCM78', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCM88', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCM98', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMA8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMB8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMC8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMD8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCME8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMF8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMG8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMH8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMI8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMJ8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2V5WPRCCMK8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2W5WPRCCML8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2W5WPRCCMM8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2W5WPRCCMN8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2W5WPRCCMO8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2W5WPRCCMP8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2W5WPRCCMQ8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2W5WPRCCMR8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2W5WPRCCMS8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2W5WPRCCMT8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2W5WPRCCMU8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2W5WPRCCMV8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2W5WPRCCMW8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2W5WPRCCMX8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2W5WPRCCMY8', 'FINST-AEF66BC1BZLWLKIX5W5E1AP09G5H2W5WPRCCMZ8']
2025-06-26 10:32:34,944 - INFO - 批量插入响应状态码: 200
2025-06-26 10:32:34,944 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 26 Jun 2025 02:32:35 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1727', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '88D05026-DE9C-711C-8FDD-703A625A460F', 'x-acs-trace-id': '6c6aebe929f0de492d6978cb43263c75', 'etag': '1SN8dOx6x0uneHZbl9Uz6aA7', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-26 10:32:34,944 - INFO - 批量插入响应体: {'result': ['FINST-AEF66BC16ZLWMA63A4AXH688WMME3A70QRCCMN', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3A70QRCCMO', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3A70QRCCMP', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3A70QRCCMQ', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3A70QRCCMR', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMS', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMT', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMU', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMV', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMW', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMX', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMY', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMZ', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCM01', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCM11', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCM21', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCM31', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCM41', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCM51', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCM61', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCM71', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCM81', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCM91', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMA1', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMB1', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMC1', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMD1', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCME1', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMF1', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMG1', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMH1', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMI1', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMJ1', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMK1', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCML1', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMM1']}
2025-06-26 10:32:34,944 - INFO - 批量插入表单数据成功，批次 5，共 36 条记录
2025-06-26 10:32:34,944 - INFO - 成功插入的数据ID: ['FINST-AEF66BC16ZLWMA63A4AXH688WMME3A70QRCCMN', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3A70QRCCMO', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3A70QRCCMP', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3A70QRCCMQ', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3A70QRCCMR', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMS', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMT', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMU', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMV', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMW', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMX', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMY', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMZ', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCM01', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCM11', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCM21', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCM31', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCM41', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCM51', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCM61', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCM71', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCM81', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCM91', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMA1', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMB1', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMC1', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMD1', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCME1', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMF1', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMG1', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMH1', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMI1', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMJ1', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMK1', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCML1', 'FINST-AEF66BC16ZLWMA63A4AXH688WMME3B70QRCCMM1']
2025-06-26 10:32:39,960 - INFO - 批量插入完成，共 236 条记录
2025-06-26 10:32:39,960 - INFO - 日期 2025-06-25 处理完成 - 更新: 0 条，插入: 236 条，错误: 0 条
2025-06-26 10:32:39,960 - INFO - 数据同步完成！更新: 0 条，插入: 236 条，错误: 0 条
2025-06-26 10:32:39,960 - INFO - 同步完成
2025-06-26 13:30:33,758 - INFO - 使用默认增量同步（当天更新数据）
2025-06-26 13:30:33,758 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-26 13:30:33,758 - INFO - 查询参数: ('2025-06-26',)
2025-06-26 13:30:33,899 - INFO - MySQL查询成功，增量数据（日期: 2025-06-26），共获取 139 条记录
2025-06-26 13:30:33,899 - INFO - 获取到 2 个日期需要处理: ['2025-06-24', '2025-06-25']
2025-06-26 13:30:33,899 - INFO - 开始处理日期: 2025-06-24
2025-06-26 13:30:33,914 - INFO - Request Parameters - Page 1:
2025-06-26 13:30:33,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 13:30:33,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 13:30:42,039 - ERROR - 处理日期 2025-06-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 68E14A37-B546-7AC5-A286-FC4F2510D0DB Response: {'code': 'ServiceUnavailable', 'requestid': '68E14A37-B546-7AC5-A286-FC4F2510D0DB', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 68E14A37-B546-7AC5-A286-FC4F2510D0DB)
2025-06-26 13:30:42,039 - INFO - 开始处理日期: 2025-06-25
2025-06-26 13:30:42,039 - INFO - Request Parameters - Page 1:
2025-06-26 13:30:42,039 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 13:30:42,039 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 13:30:47,961 - INFO - Response - Page 1:
2025-06-26 13:30:47,961 - INFO - 第 1 页获取到 50 条记录
2025-06-26 13:30:48,477 - INFO - Request Parameters - Page 2:
2025-06-26 13:30:48,477 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 13:30:48,477 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 13:30:49,164 - INFO - Response - Page 2:
2025-06-26 13:30:49,164 - INFO - 第 2 页获取到 50 条记录
2025-06-26 13:30:49,680 - INFO - Request Parameters - Page 3:
2025-06-26 13:30:49,680 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 13:30:49,680 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 13:30:50,430 - INFO - Response - Page 3:
2025-06-26 13:30:50,445 - INFO - 第 3 页获取到 50 条记录
2025-06-26 13:30:50,961 - INFO - Request Parameters - Page 4:
2025-06-26 13:30:50,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 13:30:50,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 13:30:51,742 - INFO - Response - Page 4:
2025-06-26 13:30:51,742 - INFO - 第 4 页获取到 50 条记录
2025-06-26 13:30:52,242 - INFO - Request Parameters - Page 5:
2025-06-26 13:30:52,242 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 13:30:52,242 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 13:30:52,898 - INFO - Response - Page 5:
2025-06-26 13:30:52,898 - INFO - 第 5 页获取到 50 条记录
2025-06-26 13:30:53,414 - INFO - Request Parameters - Page 6:
2025-06-26 13:30:53,414 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 13:30:53,414 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 13:30:54,102 - INFO - Response - Page 6:
2025-06-26 13:30:54,102 - INFO - 第 6 页获取到 50 条记录
2025-06-26 13:30:54,617 - INFO - Request Parameters - Page 7:
2025-06-26 13:30:54,617 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 13:30:54,617 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 13:30:55,352 - INFO - Response - Page 7:
2025-06-26 13:30:55,352 - INFO - 第 7 页获取到 50 条记录
2025-06-26 13:30:55,852 - INFO - Request Parameters - Page 8:
2025-06-26 13:30:55,852 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 13:30:55,852 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 13:30:56,461 - INFO - Response - Page 8:
2025-06-26 13:30:56,461 - INFO - 第 8 页获取到 50 条记录
2025-06-26 13:30:56,977 - INFO - Request Parameters - Page 9:
2025-06-26 13:30:56,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 13:30:56,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 13:30:57,602 - INFO - Response - Page 9:
2025-06-26 13:30:57,602 - INFO - 第 9 页获取到 18 条记录
2025-06-26 13:30:58,117 - INFO - 查询完成，共获取到 418 条记录
2025-06-26 13:30:58,117 - INFO - 获取到 418 条表单数据
2025-06-26 13:30:58,117 - INFO - 当前日期 2025-06-25 有 132 条MySQL数据需要处理
2025-06-26 13:30:58,117 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCM45
2025-06-26 13:30:58,586 - INFO - 更新表单数据成功: FINST-8PF66V71YQLWX99FCERAO6MSHFO32J0KPRCCM45
2025-06-26 13:30:58,586 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8775.0, 'new_value': 9514.0}, {'field': 'total_amount', 'old_value': 8775.0, 'new_value': 9514.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 38}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/82656f9d5d41477a864f707558a9a4d0.png?Expires=2066027201&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=enujc0ShoBXtSe%2BHULeJjUA%2FnnE%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/947d0df78e26493dab3ece9fd85fab96.png?Expires=2066198329&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=CTP8SfZpXK%2FXsywa1w5Nv2BkhqM%3D'}]
2025-06-26 13:30:58,586 - INFO - 开始更新记录 - 表单实例ID: FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCM43
2025-06-26 13:30:59,039 - INFO - 更新表单数据成功: FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCM43
2025-06-26 13:30:59,039 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1500.0, 'new_value': 2281.0}, {'field': 'total_amount', 'old_value': 1500.0, 'new_value': 2281.0}, {'field': 'order_count', 'old_value': 115, 'new_value': 142}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/c46743f82cb543a2b9e80979f1d473ea.jpg?Expires=2066198087&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=YTJrkl5fsoadjia0Fq2n%2FZwjdkc%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/3895a1cb53224cffa7c62af1daa6852a.jpg?Expires=2066198087&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=L0badkk7ZijzCp14gX1GyIUcSyM%3D'}]
2025-06-26 13:30:59,039 - INFO - 开始更新记录 - 表单实例ID: FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCM73
2025-06-26 13:30:59,477 - INFO - 更新表单数据成功: FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCM73
2025-06-26 13:30:59,477 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 1295.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1295.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 3}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/9a0f312cbfd748818c06867a29133653.jpg?Expires=2066198329&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=IaiDGs9MgSJR3B1jVuWZZj3pAts%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/c4be24c0d0f44a95a60a9bd30eb5331b.jpg?Expires=2066198087&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=uvry4W25W736dleeoymB3Tz%2BI64%3D'}]
2025-06-26 13:30:59,477 - INFO - 开始批量插入 12 条新记录
2025-06-26 13:30:59,648 - INFO - 批量插入响应状态码: 200
2025-06-26 13:30:59,648 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 26 Jun 2025 05:31:00 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '588', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7673C41E-DF74-7EE6-8276-5BA375D9A34A', 'x-acs-trace-id': '515e29c4716238c0d1ad4cefde04921d', 'etag': '5Srp2HZ6rK1FPF4SpOBZB1w8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-26 13:30:59,648 - INFO - 批量插入响应体: {'result': ['FINST-CPC66T91EOMWEBISEV3RDDL9WJBF222G3YCCM44', 'FINST-CPC66T91EOMWEBISEV3RDDL9WJBF222G3YCCM54', 'FINST-CPC66T91EOMWEBISEV3RDDL9WJBF222G3YCCM64', 'FINST-CPC66T91EOMWEBISEV3RDDL9WJBF222G3YCCM74', 'FINST-CPC66T91EOMWEBISEV3RDDL9WJBF222G3YCCM84', 'FINST-CPC66T91EOMWEBISEV3RDDL9WJBF222G3YCCM94', 'FINST-CPC66T91EOMWEBISEV3RDDL9WJBF222G3YCCMA4', 'FINST-CPC66T91EOMWEBISEV3RDDL9WJBF222G3YCCMB4', 'FINST-CPC66T91EOMWEBISEV3RDDL9WJBF222G3YCCMC4', 'FINST-CPC66T91EOMWEBISEV3RDDL9WJBF222G3YCCMD4', 'FINST-CPC66T91EOMWEBISEV3RDDL9WJBF222G3YCCME4', 'FINST-CPC66T91EOMWEBISEV3RDDL9WJBF222G3YCCMF4']}
2025-06-26 13:30:59,648 - INFO - 批量插入表单数据成功，批次 1，共 12 条记录
2025-06-26 13:30:59,648 - INFO - 成功插入的数据ID: ['FINST-CPC66T91EOMWEBISEV3RDDL9WJBF222G3YCCM44', 'FINST-CPC66T91EOMWEBISEV3RDDL9WJBF222G3YCCM54', 'FINST-CPC66T91EOMWEBISEV3RDDL9WJBF222G3YCCM64', 'FINST-CPC66T91EOMWEBISEV3RDDL9WJBF222G3YCCM74', 'FINST-CPC66T91EOMWEBISEV3RDDL9WJBF222G3YCCM84', 'FINST-CPC66T91EOMWEBISEV3RDDL9WJBF222G3YCCM94', 'FINST-CPC66T91EOMWEBISEV3RDDL9WJBF222G3YCCMA4', 'FINST-CPC66T91EOMWEBISEV3RDDL9WJBF222G3YCCMB4', 'FINST-CPC66T91EOMWEBISEV3RDDL9WJBF222G3YCCMC4', 'FINST-CPC66T91EOMWEBISEV3RDDL9WJBF222G3YCCMD4', 'FINST-CPC66T91EOMWEBISEV3RDDL9WJBF222G3YCCME4', 'FINST-CPC66T91EOMWEBISEV3RDDL9WJBF222G3YCCMF4']
2025-06-26 13:31:04,664 - INFO - 批量插入完成，共 12 条记录
2025-06-26 13:31:04,664 - INFO - 日期 2025-06-25 处理完成 - 更新: 3 条，插入: 12 条，错误: 0 条
2025-06-26 13:31:04,664 - INFO - 数据同步完成！更新: 3 条，插入: 12 条，错误: 1 条
2025-06-26 13:32:04,679 - INFO - 开始同步昨天与今天的销售数据: 2025-06-25 至 2025-06-26
2025-06-26 13:32:04,679 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-26 13:32:04,679 - INFO - 查询参数: ('2025-06-25', '2025-06-26')
2025-06-26 13:32:04,820 - INFO - MySQL查询成功，时间段: 2025-06-25 至 2025-06-26，共获取 485 条记录
2025-06-26 13:32:04,820 - INFO - 获取到 1 个日期需要处理: ['2025-06-25']
2025-06-26 13:32:04,820 - INFO - 开始处理日期: 2025-06-25
2025-06-26 13:32:04,820 - INFO - Request Parameters - Page 1:
2025-06-26 13:32:04,820 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 13:32:04,820 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 13:32:05,554 - INFO - Response - Page 1:
2025-06-26 13:32:05,554 - INFO - 第 1 页获取到 50 条记录
2025-06-26 13:32:06,070 - INFO - Request Parameters - Page 2:
2025-06-26 13:32:06,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 13:32:06,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 13:32:06,726 - INFO - Response - Page 2:
2025-06-26 13:32:06,726 - INFO - 第 2 页获取到 50 条记录
2025-06-26 13:32:07,226 - INFO - Request Parameters - Page 3:
2025-06-26 13:32:07,226 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 13:32:07,226 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 13:32:07,914 - INFO - Response - Page 3:
2025-06-26 13:32:07,914 - INFO - 第 3 页获取到 50 条记录
2025-06-26 13:32:08,429 - INFO - Request Parameters - Page 4:
2025-06-26 13:32:08,429 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 13:32:08,429 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 13:32:09,101 - INFO - Response - Page 4:
2025-06-26 13:32:09,101 - INFO - 第 4 页获取到 50 条记录
2025-06-26 13:32:09,617 - INFO - Request Parameters - Page 5:
2025-06-26 13:32:09,617 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 13:32:09,617 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 13:32:10,304 - INFO - Response - Page 5:
2025-06-26 13:32:10,304 - INFO - 第 5 页获取到 50 条记录
2025-06-26 13:32:10,804 - INFO - Request Parameters - Page 6:
2025-06-26 13:32:10,804 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 13:32:10,804 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 13:32:11,460 - INFO - Response - Page 6:
2025-06-26 13:32:11,460 - INFO - 第 6 页获取到 50 条记录
2025-06-26 13:32:11,960 - INFO - Request Parameters - Page 7:
2025-06-26 13:32:11,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 13:32:11,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 13:32:12,663 - INFO - Response - Page 7:
2025-06-26 13:32:12,663 - INFO - 第 7 页获取到 50 条记录
2025-06-26 13:32:13,163 - INFO - Request Parameters - Page 8:
2025-06-26 13:32:13,163 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 13:32:13,163 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 13:32:13,882 - INFO - Response - Page 8:
2025-06-26 13:32:13,882 - INFO - 第 8 页获取到 50 条记录
2025-06-26 13:32:14,382 - INFO - Request Parameters - Page 9:
2025-06-26 13:32:14,382 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 13:32:14,382 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 13:32:15,007 - INFO - Response - Page 9:
2025-06-26 13:32:15,007 - INFO - 第 9 页获取到 30 条记录
2025-06-26 13:32:15,523 - INFO - 查询完成，共获取到 430 条记录
2025-06-26 13:32:15,523 - INFO - 获取到 430 条表单数据
2025-06-26 13:32:15,523 - INFO - 当前日期 2025-06-25 有 476 条MySQL数据需要处理
2025-06-26 13:32:15,538 - INFO - 开始批量插入 46 条新记录
2025-06-26 13:32:15,804 - INFO - 批量插入响应状态码: 200
2025-06-26 13:32:15,804 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 26 Jun 2025 05:32:16 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2189', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '8EA2D3A1-B996-7ECE-896A-26D5A04F7866', 'x-acs-trace-id': '00e798a911491ac1cc17b89b5534b67f', 'etag': '2CRqcQNq2S62/cRSKtnte9g9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-26 13:32:15,804 - INFO - 批量插入响应体: {'result': ['FINST-DIC66I91FUMWJ8U37EGN27VPUP9F25T25YCCM5', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F25T25YCCM6', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F25T25YCCM7', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F25T25YCCM8', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F25T25YCCM9', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F25T25YCCMA', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F25T25YCCMB', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F25T25YCCMC', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F25T25YCCMD', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F25T25YCCME', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F25T25YCCMF', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F25T25YCCMG', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F25T25YCCMH', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMI', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMJ', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMK', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCML', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMM', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMN', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMO', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMP', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMQ', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMR', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMS', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMT', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMU', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMV', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMW', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMX', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMY', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMZ', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCM01', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCM11', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCM21', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCM31', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCM41', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCM51', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCM61', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCM71', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCM81', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCM91', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMA1', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMB1', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMC1', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMD1', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCME1']}
2025-06-26 13:32:15,804 - INFO - 批量插入表单数据成功，批次 1，共 46 条记录
2025-06-26 13:32:15,804 - INFO - 成功插入的数据ID: ['FINST-DIC66I91FUMWJ8U37EGN27VPUP9F25T25YCCM5', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F25T25YCCM6', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F25T25YCCM7', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F25T25YCCM8', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F25T25YCCM9', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F25T25YCCMA', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F25T25YCCMB', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F25T25YCCMC', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F25T25YCCMD', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F25T25YCCME', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F25T25YCCMF', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F25T25YCCMG', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F25T25YCCMH', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMI', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMJ', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMK', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCML', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMM', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMN', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMO', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMP', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMQ', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMR', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMS', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMT', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMU', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMV', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMW', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMX', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMY', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMZ', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCM01', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCM11', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCM21', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCM31', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCM41', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCM51', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCM61', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCM71', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCM81', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCM91', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMA1', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMB1', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMC1', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCMD1', 'FINST-DIC66I91FUMWJ8U37EGN27VPUP9F26T25YCCME1']
2025-06-26 13:32:20,820 - INFO - 批量插入完成，共 46 条记录
2025-06-26 13:32:20,820 - INFO - 日期 2025-06-25 处理完成 - 更新: 0 条，插入: 46 条，错误: 0 条
2025-06-26 13:32:20,820 - INFO - 数据同步完成！更新: 0 条，插入: 46 条，错误: 0 条
2025-06-26 13:32:20,820 - INFO - 同步完成
2025-06-26 16:30:34,115 - INFO - 使用默认增量同步（当天更新数据）
2025-06-26 16:30:34,115 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-26 16:30:34,115 - INFO - 查询参数: ('2025-06-26',)
2025-06-26 16:30:34,256 - INFO - MySQL查询成功，增量数据（日期: 2025-06-26），共获取 143 条记录
2025-06-26 16:30:34,256 - INFO - 获取到 2 个日期需要处理: ['2025-06-24', '2025-06-25']
2025-06-26 16:30:34,256 - INFO - 开始处理日期: 2025-06-24
2025-06-26 16:30:34,256 - INFO - Request Parameters - Page 1:
2025-06-26 16:30:34,256 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 16:30:34,256 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 16:30:40,444 - INFO - Response - Page 1:
2025-06-26 16:30:40,444 - INFO - 第 1 页获取到 50 条记录
2025-06-26 16:30:40,959 - INFO - Request Parameters - Page 2:
2025-06-26 16:30:40,959 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 16:30:40,959 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 16:30:49,068 - ERROR - 处理日期 2025-06-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 6A984C36-691E-787C-B79A-28F24F7D5902 Response: {'code': 'ServiceUnavailable', 'requestid': '6A984C36-691E-787C-B79A-28F24F7D5902', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 6A984C36-691E-787C-B79A-28F24F7D5902)
2025-06-26 16:30:49,068 - INFO - 开始处理日期: 2025-06-25
2025-06-26 16:30:49,068 - INFO - Request Parameters - Page 1:
2025-06-26 16:30:49,068 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 16:30:49,068 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 16:30:51,022 - INFO - Response - Page 1:
2025-06-26 16:30:51,022 - INFO - 第 1 页获取到 50 条记录
2025-06-26 16:30:51,537 - INFO - Request Parameters - Page 2:
2025-06-26 16:30:51,537 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 16:30:51,537 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 16:30:52,178 - INFO - Response - Page 2:
2025-06-26 16:30:52,178 - INFO - 第 2 页获取到 50 条记录
2025-06-26 16:30:52,693 - INFO - Request Parameters - Page 3:
2025-06-26 16:30:52,693 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 16:30:52,693 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 16:30:53,365 - INFO - Response - Page 3:
2025-06-26 16:30:53,365 - INFO - 第 3 页获取到 50 条记录
2025-06-26 16:30:53,865 - INFO - Request Parameters - Page 4:
2025-06-26 16:30:53,865 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 16:30:53,865 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 16:30:54,522 - INFO - Response - Page 4:
2025-06-26 16:30:54,522 - INFO - 第 4 页获取到 50 条记录
2025-06-26 16:30:55,037 - INFO - Request Parameters - Page 5:
2025-06-26 16:30:55,037 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 16:30:55,037 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 16:30:55,772 - INFO - Response - Page 5:
2025-06-26 16:30:55,772 - INFO - 第 5 页获取到 50 条记录
2025-06-26 16:30:56,287 - INFO - Request Parameters - Page 6:
2025-06-26 16:30:56,287 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 16:30:56,287 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 16:30:57,084 - INFO - Response - Page 6:
2025-06-26 16:30:57,084 - INFO - 第 6 页获取到 50 条记录
2025-06-26 16:30:57,600 - INFO - Request Parameters - Page 7:
2025-06-26 16:30:57,600 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 16:30:57,600 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 16:30:58,271 - INFO - Response - Page 7:
2025-06-26 16:30:58,271 - INFO - 第 7 页获取到 50 条记录
2025-06-26 16:30:58,771 - INFO - Request Parameters - Page 8:
2025-06-26 16:30:58,771 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 16:30:58,771 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 16:30:59,459 - INFO - Response - Page 8:
2025-06-26 16:30:59,459 - INFO - 第 8 页获取到 50 条记录
2025-06-26 16:30:59,975 - INFO - Request Parameters - Page 9:
2025-06-26 16:30:59,975 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 16:30:59,975 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 16:31:00,646 - INFO - Response - Page 9:
2025-06-26 16:31:00,646 - INFO - 第 9 页获取到 50 条记录
2025-06-26 16:31:01,146 - INFO - Request Parameters - Page 10:
2025-06-26 16:31:01,146 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 16:31:01,146 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 16:31:01,756 - INFO - Response - Page 10:
2025-06-26 16:31:01,756 - INFO - 第 10 页获取到 26 条记录
2025-06-26 16:31:02,271 - INFO - 查询完成，共获取到 476 条记录
2025-06-26 16:31:02,271 - INFO - 获取到 476 条表单数据
2025-06-26 16:31:02,271 - INFO - 当前日期 2025-06-25 有 135 条MySQL数据需要处理
2025-06-26 16:31:02,271 - INFO - 开始批量插入 3 条新记录
2025-06-26 16:31:02,443 - INFO - 批量插入响应状态码: 200
2025-06-26 16:31:02,443 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 26 Jun 2025 08:31:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '156', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5C19F812-DF3F-726A-9170-2DA6022AC949', 'x-acs-trace-id': '3e6e6ec9740fdbe1a7b28b50399c139f', 'etag': '167w2utwfe/yd3NrESKRRQQ6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-26 16:31:02,443 - INFO - 批量插入响应体: {'result': ['FINST-XMC66R91VXKWKL6F6IQDA4CKPSX332MZI4DCME9', 'FINST-XMC66R91VXKWKL6F6IQDA4CKPSX332MZI4DCMF9', 'FINST-XMC66R91VXKWKL6F6IQDA4CKPSX332MZI4DCMG9']}
2025-06-26 16:31:02,443 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-06-26 16:31:02,443 - INFO - 成功插入的数据ID: ['FINST-XMC66R91VXKWKL6F6IQDA4CKPSX332MZI4DCME9', 'FINST-XMC66R91VXKWKL6F6IQDA4CKPSX332MZI4DCMF9', 'FINST-XMC66R91VXKWKL6F6IQDA4CKPSX332MZI4DCMG9']
2025-06-26 16:31:07,459 - INFO - 批量插入完成，共 3 条记录
2025-06-26 16:31:07,459 - INFO - 日期 2025-06-25 处理完成 - 更新: 0 条，插入: 3 条，错误: 0 条
2025-06-26 16:31:07,459 - INFO - 数据同步完成！更新: 0 条，插入: 3 条，错误: 1 条
2025-06-26 16:32:07,474 - INFO - 开始同步昨天与今天的销售数据: 2025-06-25 至 2025-06-26
2025-06-26 16:32:07,474 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-26 16:32:07,474 - INFO - 查询参数: ('2025-06-25', '2025-06-26')
2025-06-26 16:32:07,615 - INFO - MySQL查询成功，时间段: 2025-06-25 至 2025-06-26，共获取 497 条记录
2025-06-26 16:32:07,615 - INFO - 获取到 1 个日期需要处理: ['2025-06-25']
2025-06-26 16:32:07,615 - INFO - 开始处理日期: 2025-06-25
2025-06-26 16:32:07,615 - INFO - Request Parameters - Page 1:
2025-06-26 16:32:07,615 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 16:32:07,615 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 16:32:08,334 - INFO - Response - Page 1:
2025-06-26 16:32:08,334 - INFO - 第 1 页获取到 50 条记录
2025-06-26 16:32:08,834 - INFO - Request Parameters - Page 2:
2025-06-26 16:32:08,834 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 16:32:08,834 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 16:32:09,537 - INFO - Response - Page 2:
2025-06-26 16:32:09,537 - INFO - 第 2 页获取到 50 条记录
2025-06-26 16:32:10,052 - INFO - Request Parameters - Page 3:
2025-06-26 16:32:10,052 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 16:32:10,052 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 16:32:10,724 - INFO - Response - Page 3:
2025-06-26 16:32:10,724 - INFO - 第 3 页获取到 50 条记录
2025-06-26 16:32:11,224 - INFO - Request Parameters - Page 4:
2025-06-26 16:32:11,224 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 16:32:11,224 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 16:32:11,943 - INFO - Response - Page 4:
2025-06-26 16:32:11,943 - INFO - 第 4 页获取到 50 条记录
2025-06-26 16:32:12,458 - INFO - Request Parameters - Page 5:
2025-06-26 16:32:12,458 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 16:32:12,458 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 16:32:13,177 - INFO - Response - Page 5:
2025-06-26 16:32:13,177 - INFO - 第 5 页获取到 50 条记录
2025-06-26 16:32:13,677 - INFO - Request Parameters - Page 6:
2025-06-26 16:32:13,677 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 16:32:13,677 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 16:32:14,490 - INFO - Response - Page 6:
2025-06-26 16:32:14,490 - INFO - 第 6 页获取到 50 条记录
2025-06-26 16:32:15,005 - INFO - Request Parameters - Page 7:
2025-06-26 16:32:15,005 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 16:32:15,005 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 16:32:15,740 - INFO - Response - Page 7:
2025-06-26 16:32:15,740 - INFO - 第 7 页获取到 50 条记录
2025-06-26 16:32:16,255 - INFO - Request Parameters - Page 8:
2025-06-26 16:32:16,255 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 16:32:16,255 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 16:32:16,927 - INFO - Response - Page 8:
2025-06-26 16:32:16,927 - INFO - 第 8 页获取到 50 条记录
2025-06-26 16:32:17,443 - INFO - Request Parameters - Page 9:
2025-06-26 16:32:17,443 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 16:32:17,443 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 16:32:18,255 - INFO - Response - Page 9:
2025-06-26 16:32:18,255 - INFO - 第 9 页获取到 50 条记录
2025-06-26 16:32:18,771 - INFO - Request Parameters - Page 10:
2025-06-26 16:32:18,771 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 16:32:18,771 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 16:32:19,396 - INFO - Response - Page 10:
2025-06-26 16:32:19,396 - INFO - 第 10 页获取到 29 条记录
2025-06-26 16:32:19,912 - INFO - 查询完成，共获取到 479 条记录
2025-06-26 16:32:19,912 - INFO - 获取到 479 条表单数据
2025-06-26 16:32:19,912 - INFO - 当前日期 2025-06-25 有 487 条MySQL数据需要处理
2025-06-26 16:32:19,927 - INFO - 开始批量插入 8 条新记录
2025-06-26 16:32:20,099 - INFO - 批量插入响应状态码: 200
2025-06-26 16:32:20,099 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 26 Jun 2025 08:32:20 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '396', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '9689F64D-5C6A-722B-AB93-61D42BAD78CF', 'x-acs-trace-id': 'bb07fe02301544d75f1eb5f832ff1eeb', 'etag': '39FqcaNun0yItQnnWwajZew6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-26 16:32:20,099 - INFO - 批量插入响应体: {'result': ['FINST-NU966I81UZLWWW1IFXJIA9P5JM4U3VINK4DCMSA', 'FINST-NU966I81UZLWWW1IFXJIA9P5JM4U3VINK4DCMTA', 'FINST-NU966I81UZLWWW1IFXJIA9P5JM4U3VINK4DCMUA', 'FINST-NU966I81UZLWWW1IFXJIA9P5JM4U3VINK4DCMVA', 'FINST-NU966I81UZLWWW1IFXJIA9P5JM4U3VINK4DCMWA', 'FINST-NU966I81UZLWWW1IFXJIA9P5JM4U3VINK4DCMXA', 'FINST-NU966I81UZLWWW1IFXJIA9P5JM4U3VINK4DCMYA', 'FINST-NU966I81UZLWWW1IFXJIA9P5JM4U3VINK4DCMZA']}
2025-06-26 16:32:20,099 - INFO - 批量插入表单数据成功，批次 1，共 8 条记录
2025-06-26 16:32:20,099 - INFO - 成功插入的数据ID: ['FINST-NU966I81UZLWWW1IFXJIA9P5JM4U3VINK4DCMSA', 'FINST-NU966I81UZLWWW1IFXJIA9P5JM4U3VINK4DCMTA', 'FINST-NU966I81UZLWWW1IFXJIA9P5JM4U3VINK4DCMUA', 'FINST-NU966I81UZLWWW1IFXJIA9P5JM4U3VINK4DCMVA', 'FINST-NU966I81UZLWWW1IFXJIA9P5JM4U3VINK4DCMWA', 'FINST-NU966I81UZLWWW1IFXJIA9P5JM4U3VINK4DCMXA', 'FINST-NU966I81UZLWWW1IFXJIA9P5JM4U3VINK4DCMYA', 'FINST-NU966I81UZLWWW1IFXJIA9P5JM4U3VINK4DCMZA']
2025-06-26 16:32:25,115 - INFO - 批量插入完成，共 8 条记录
2025-06-26 16:32:25,115 - INFO - 日期 2025-06-25 处理完成 - 更新: 0 条，插入: 8 条，错误: 0 条
2025-06-26 16:32:25,115 - INFO - 数据同步完成！更新: 0 条，插入: 8 条，错误: 0 条
2025-06-26 16:32:25,115 - INFO - 同步完成
2025-06-26 19:30:34,899 - INFO - 使用默认增量同步（当天更新数据）
2025-06-26 19:30:34,899 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-26 19:30:34,899 - INFO - 查询参数: ('2025-06-26',)
2025-06-26 19:30:35,039 - INFO - MySQL查询成功，增量数据（日期: 2025-06-26），共获取 158 条记录
2025-06-26 19:30:35,039 - INFO - 获取到 5 个日期需要处理: ['2025-06-14', '2025-06-21', '2025-06-23', '2025-06-24', '2025-06-25']
2025-06-26 19:30:35,039 - INFO - 开始处理日期: 2025-06-14
2025-06-26 19:30:35,039 - INFO - Request Parameters - Page 1:
2025-06-26 19:30:35,039 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:30:35,039 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:30:43,181 - ERROR - 处理日期 2025-06-14 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: BFF0E04D-07DA-7957-8464-D8CCDFBC8A10 Response: {'code': 'ServiceUnavailable', 'requestid': 'BFF0E04D-07DA-7957-8464-D8CCDFBC8A10', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: BFF0E04D-07DA-7957-8464-D8CCDFBC8A10)
2025-06-26 19:30:43,181 - INFO - 开始处理日期: 2025-06-21
2025-06-26 19:30:43,181 - INFO - Request Parameters - Page 1:
2025-06-26 19:30:43,181 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:30:43,181 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:30:48,994 - INFO - Response - Page 1:
2025-06-26 19:30:48,994 - INFO - 第 1 页获取到 50 条记录
2025-06-26 19:30:49,494 - INFO - Request Parameters - Page 2:
2025-06-26 19:30:49,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:30:49,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:30:50,182 - INFO - Response - Page 2:
2025-06-26 19:30:50,182 - INFO - 第 2 页获取到 50 条记录
2025-06-26 19:30:50,698 - INFO - Request Parameters - Page 3:
2025-06-26 19:30:50,698 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:30:50,698 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:30:51,323 - INFO - Response - Page 3:
2025-06-26 19:30:51,323 - INFO - 第 3 页获取到 50 条记录
2025-06-26 19:30:51,823 - INFO - Request Parameters - Page 4:
2025-06-26 19:30:51,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:30:51,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:30:52,573 - INFO - Response - Page 4:
2025-06-26 19:30:52,573 - INFO - 第 4 页获取到 50 条记录
2025-06-26 19:30:53,089 - INFO - Request Parameters - Page 5:
2025-06-26 19:30:53,089 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:30:53,089 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:30:53,776 - INFO - Response - Page 5:
2025-06-26 19:30:53,776 - INFO - 第 5 页获取到 50 条记录
2025-06-26 19:30:54,276 - INFO - Request Parameters - Page 6:
2025-06-26 19:30:54,276 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:30:54,276 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:30:54,933 - INFO - Response - Page 6:
2025-06-26 19:30:54,933 - INFO - 第 6 页获取到 50 条记录
2025-06-26 19:30:55,448 - INFO - Request Parameters - Page 7:
2025-06-26 19:30:55,448 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:30:55,448 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:30:56,152 - INFO - Response - Page 7:
2025-06-26 19:30:56,152 - INFO - 第 7 页获取到 50 条记录
2025-06-26 19:30:56,667 - INFO - Request Parameters - Page 8:
2025-06-26 19:30:56,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:30:56,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:30:57,339 - INFO - Response - Page 8:
2025-06-26 19:30:57,339 - INFO - 第 8 页获取到 50 条记录
2025-06-26 19:30:57,855 - INFO - Request Parameters - Page 9:
2025-06-26 19:30:57,855 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:30:57,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:30:58,543 - INFO - Response - Page 9:
2025-06-26 19:30:58,543 - INFO - 第 9 页获取到 50 条记录
2025-06-26 19:30:59,043 - INFO - Request Parameters - Page 10:
2025-06-26 19:30:59,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:30:59,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:30:59,683 - INFO - Response - Page 10:
2025-06-26 19:30:59,683 - INFO - 第 10 页获取到 50 条记录
2025-06-26 19:31:00,199 - INFO - Request Parameters - Page 11:
2025-06-26 19:31:00,199 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:31:00,199 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:31:00,793 - INFO - Response - Page 11:
2025-06-26 19:31:00,793 - INFO - 第 11 页获取到 19 条记录
2025-06-26 19:31:01,293 - INFO - 查询完成，共获取到 519 条记录
2025-06-26 19:31:01,293 - INFO - 获取到 519 条表单数据
2025-06-26 19:31:01,293 - INFO - 当前日期 2025-06-21 有 1 条MySQL数据需要处理
2025-06-26 19:31:01,293 - INFO - 开始批量插入 1 条新记录
2025-06-26 19:31:01,465 - INFO - 批量插入响应状态码: 200
2025-06-26 19:31:01,465 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 26 Jun 2025 11:31:01 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'AA29CC70-5D4B-7508-BB28-3CB64BD53C47', 'x-acs-trace-id': '413ec6d089ba5852122ede211df96b91', 'etag': '6O7aLb4/tAmMy/NdvH4uYkg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-26 19:31:01,465 - INFO - 批量插入响应体: {'result': ['FINST-K7666JC180MWBRCO7JD0GA91G9YL2GSFYADCMM2']}
2025-06-26 19:31:01,465 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-06-26 19:31:01,465 - INFO - 成功插入的数据ID: ['FINST-K7666JC180MWBRCO7JD0GA91G9YL2GSFYADCMM2']
2025-06-26 19:31:06,481 - INFO - 批量插入完成，共 1 条记录
2025-06-26 19:31:06,481 - INFO - 日期 2025-06-21 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-06-26 19:31:06,481 - INFO - 开始处理日期: 2025-06-23
2025-06-26 19:31:06,481 - INFO - Request Parameters - Page 1:
2025-06-26 19:31:06,481 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:31:06,481 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:31:07,122 - INFO - Response - Page 1:
2025-06-26 19:31:07,122 - INFO - 第 1 页获取到 50 条记录
2025-06-26 19:31:07,638 - INFO - Request Parameters - Page 2:
2025-06-26 19:31:07,638 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:31:07,638 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:31:08,247 - INFO - Response - Page 2:
2025-06-26 19:31:08,247 - INFO - 第 2 页获取到 50 条记录
2025-06-26 19:31:08,763 - INFO - Request Parameters - Page 3:
2025-06-26 19:31:08,763 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:31:08,763 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:31:09,404 - INFO - Response - Page 3:
2025-06-26 19:31:09,404 - INFO - 第 3 页获取到 50 条记录
2025-06-26 19:31:09,904 - INFO - Request Parameters - Page 4:
2025-06-26 19:31:09,904 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:31:09,904 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:31:10,560 - INFO - Response - Page 4:
2025-06-26 19:31:10,560 - INFO - 第 4 页获取到 50 条记录
2025-06-26 19:31:11,076 - INFO - Request Parameters - Page 5:
2025-06-26 19:31:11,076 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:31:11,076 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:31:11,716 - INFO - Response - Page 5:
2025-06-26 19:31:11,716 - INFO - 第 5 页获取到 50 条记录
2025-06-26 19:31:12,232 - INFO - Request Parameters - Page 6:
2025-06-26 19:31:12,232 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:31:12,232 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:31:12,904 - INFO - Response - Page 6:
2025-06-26 19:31:12,904 - INFO - 第 6 页获取到 50 条记录
2025-06-26 19:31:13,420 - INFO - Request Parameters - Page 7:
2025-06-26 19:31:13,420 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:31:13,420 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:31:14,139 - INFO - Response - Page 7:
2025-06-26 19:31:14,139 - INFO - 第 7 页获取到 50 条记录
2025-06-26 19:31:14,639 - INFO - Request Parameters - Page 8:
2025-06-26 19:31:14,639 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:31:14,639 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:31:15,248 - INFO - Response - Page 8:
2025-06-26 19:31:15,248 - INFO - 第 8 页获取到 50 条记录
2025-06-26 19:31:15,764 - INFO - Request Parameters - Page 9:
2025-06-26 19:31:15,764 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:31:15,764 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:31:16,452 - INFO - Response - Page 9:
2025-06-26 19:31:16,452 - INFO - 第 9 页获取到 50 条记录
2025-06-26 19:31:16,967 - INFO - Request Parameters - Page 10:
2025-06-26 19:31:16,967 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:31:16,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:31:17,717 - INFO - Response - Page 10:
2025-06-26 19:31:17,717 - INFO - 第 10 页获取到 50 条记录
2025-06-26 19:31:18,233 - INFO - Request Parameters - Page 11:
2025-06-26 19:31:18,233 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:31:18,233 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:31:18,874 - INFO - Response - Page 11:
2025-06-26 19:31:18,874 - INFO - 第 11 页获取到 45 条记录
2025-06-26 19:31:19,389 - INFO - 查询完成，共获取到 545 条记录
2025-06-26 19:31:19,389 - INFO - 获取到 545 条表单数据
2025-06-26 19:31:19,389 - INFO - 当前日期 2025-06-23 有 1 条MySQL数据需要处理
2025-06-26 19:31:19,389 - INFO - 开始更新记录 - 表单实例ID: FINST-SED66Q61FOKWU5R695LWW5LXIL3P2OH5UW9CM7
2025-06-26 19:31:19,811 - INFO - 更新表单数据成功: FINST-SED66Q61FOKWU5R695LWW5LXIL3P2OH5UW9CM7
2025-06-26 19:31:19,811 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35000.0, 'new_value': 39702.99}, {'field': 'total_amount', 'old_value': 35000.0, 'new_value': 39702.99}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/82a1e807a1dd48a2962ba9e57c11f06a.jpg?Expires=2066026428&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=rOw8AfvB1QQXKSIZBbPP5wCCJKE%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/c79b3df68ff84bb694bd1c9dc1366398.jpg?Expires=2066282901&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=Ll96ClrN%2F8bml3mYYXjmltGRkmE%3D'}]
2025-06-26 19:31:19,811 - INFO - 日期 2025-06-23 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-06-26 19:31:19,811 - INFO - 开始处理日期: 2025-06-24
2025-06-26 19:31:19,811 - INFO - Request Parameters - Page 1:
2025-06-26 19:31:19,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:31:19,811 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:31:20,561 - INFO - Response - Page 1:
2025-06-26 19:31:20,561 - INFO - 第 1 页获取到 50 条记录
2025-06-26 19:31:21,062 - INFO - Request Parameters - Page 2:
2025-06-26 19:31:21,062 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:31:21,062 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:31:21,687 - INFO - Response - Page 2:
2025-06-26 19:31:21,687 - INFO - 第 2 页获取到 50 条记录
2025-06-26 19:31:22,202 - INFO - Request Parameters - Page 3:
2025-06-26 19:31:22,202 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:31:22,202 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:31:22,843 - INFO - Response - Page 3:
2025-06-26 19:31:22,843 - INFO - 第 3 页获取到 50 条记录
2025-06-26 19:31:23,343 - INFO - Request Parameters - Page 4:
2025-06-26 19:31:23,343 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:31:23,343 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:31:24,031 - INFO - Response - Page 4:
2025-06-26 19:31:24,031 - INFO - 第 4 页获取到 50 条记录
2025-06-26 19:31:24,531 - INFO - Request Parameters - Page 5:
2025-06-26 19:31:24,531 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:31:24,531 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:31:25,140 - INFO - Response - Page 5:
2025-06-26 19:31:25,140 - INFO - 第 5 页获取到 50 条记录
2025-06-26 19:31:25,656 - INFO - Request Parameters - Page 6:
2025-06-26 19:31:25,656 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:31:25,656 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:31:26,297 - INFO - Response - Page 6:
2025-06-26 19:31:26,297 - INFO - 第 6 页获取到 50 条记录
2025-06-26 19:31:26,812 - INFO - Request Parameters - Page 7:
2025-06-26 19:31:26,812 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:31:26,812 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:31:27,438 - INFO - Response - Page 7:
2025-06-26 19:31:27,453 - INFO - 第 7 页获取到 50 条记录
2025-06-26 19:31:27,969 - INFO - Request Parameters - Page 8:
2025-06-26 19:31:27,969 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:31:27,969 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:31:28,610 - INFO - Response - Page 8:
2025-06-26 19:31:28,610 - INFO - 第 8 页获取到 50 条记录
2025-06-26 19:31:29,110 - INFO - Request Parameters - Page 9:
2025-06-26 19:31:29,110 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:31:29,110 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:31:29,766 - INFO - Response - Page 9:
2025-06-26 19:31:29,766 - INFO - 第 9 页获取到 50 条记录
2025-06-26 19:31:30,266 - INFO - Request Parameters - Page 10:
2025-06-26 19:31:30,266 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:31:30,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:31:30,876 - INFO - Response - Page 10:
2025-06-26 19:31:30,876 - INFO - 第 10 页获取到 42 条记录
2025-06-26 19:31:31,376 - INFO - 查询完成，共获取到 492 条记录
2025-06-26 19:31:31,376 - INFO - 获取到 492 条表单数据
2025-06-26 19:31:31,376 - INFO - 当前日期 2025-06-24 有 7 条MySQL数据需要处理
2025-06-26 19:31:31,376 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC1T6LW93LK79LAD9FOO0LZ29NCNIBCM35
2025-06-26 19:31:31,829 - INFO - 更新表单数据成功: FINST-ZNE66RC1T6LW93LK79LAD9FOO0LZ29NCNIBCM35
2025-06-26 19:31:31,829 - INFO - 更新记录成功，变更字段: [{'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/b220bee0627546df94d8fff8d15d2701.png?Expires=2066026428&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=RJ7u6420nlQkt36hvBcB5PvBOGM%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/4e4c4f76593c490a82cad2cb4f69b4a7.png?Expires=2066198087&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=h24XywBf1FeLc99Ly43ihH1%2Fo3Y%3D'}]
2025-06-26 19:31:31,829 - INFO - 开始批量插入 6 条新记录
2025-06-26 19:31:32,001 - INFO - 批量插入响应状态码: 200
2025-06-26 19:31:32,001 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 26 Jun 2025 11:31:31 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '300', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '1C5A0287-312D-7EEE-9449-1C67F732141C', 'x-acs-trace-id': '3bc372c21282b15fcf92dcc486d01f60', 'etag': '3DzaRZtK9AXq9xOtV89mU4Q0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-26 19:31:32,001 - INFO - 批量插入响应体: {'result': ['FINST-XL866HB1BZLWS1N992E89DWHKO313JC3ZADCMP5', 'FINST-XL866HB1BZLWS1N992E89DWHKO313JC3ZADCMQ5', 'FINST-XL866HB1BZLWS1N992E89DWHKO313JC3ZADCMR5', 'FINST-XL866HB1BZLWS1N992E89DWHKO313JC3ZADCMS5', 'FINST-XL866HB1BZLWS1N992E89DWHKO313JC3ZADCMT5', 'FINST-XL866HB1BZLWS1N992E89DWHKO313JC3ZADCMU5']}
2025-06-26 19:31:32,001 - INFO - 批量插入表单数据成功，批次 1，共 6 条记录
2025-06-26 19:31:32,001 - INFO - 成功插入的数据ID: ['FINST-XL866HB1BZLWS1N992E89DWHKO313JC3ZADCMP5', 'FINST-XL866HB1BZLWS1N992E89DWHKO313JC3ZADCMQ5', 'FINST-XL866HB1BZLWS1N992E89DWHKO313JC3ZADCMR5', 'FINST-XL866HB1BZLWS1N992E89DWHKO313JC3ZADCMS5', 'FINST-XL866HB1BZLWS1N992E89DWHKO313JC3ZADCMT5', 'FINST-XL866HB1BZLWS1N992E89DWHKO313JC3ZADCMU5']
2025-06-26 19:31:37,017 - INFO - 批量插入完成，共 6 条记录
2025-06-26 19:31:37,017 - INFO - 日期 2025-06-24 处理完成 - 更新: 1 条，插入: 6 条，错误: 0 条
2025-06-26 19:31:37,017 - INFO - 开始处理日期: 2025-06-25
2025-06-26 19:31:37,017 - INFO - Request Parameters - Page 1:
2025-06-26 19:31:37,017 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:31:37,017 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:31:37,783 - INFO - Response - Page 1:
2025-06-26 19:31:37,783 - INFO - 第 1 页获取到 50 条记录
2025-06-26 19:31:38,283 - INFO - Request Parameters - Page 2:
2025-06-26 19:31:38,283 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:31:38,283 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:31:38,939 - INFO - Response - Page 2:
2025-06-26 19:31:38,939 - INFO - 第 2 页获取到 50 条记录
2025-06-26 19:31:39,455 - INFO - Request Parameters - Page 3:
2025-06-26 19:31:39,455 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:31:39,455 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:31:40,064 - INFO - Response - Page 3:
2025-06-26 19:31:40,064 - INFO - 第 3 页获取到 50 条记录
2025-06-26 19:31:40,580 - INFO - Request Parameters - Page 4:
2025-06-26 19:31:40,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:31:40,580 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:31:41,221 - INFO - Response - Page 4:
2025-06-26 19:31:41,221 - INFO - 第 4 页获取到 50 条记录
2025-06-26 19:31:41,721 - INFO - Request Parameters - Page 5:
2025-06-26 19:31:41,721 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:31:41,721 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:31:42,455 - INFO - Response - Page 5:
2025-06-26 19:31:42,455 - INFO - 第 5 页获取到 50 条记录
2025-06-26 19:31:42,971 - INFO - Request Parameters - Page 6:
2025-06-26 19:31:42,971 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:31:42,971 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:31:43,627 - INFO - Response - Page 6:
2025-06-26 19:31:43,627 - INFO - 第 6 页获取到 50 条记录
2025-06-26 19:31:44,127 - INFO - Request Parameters - Page 7:
2025-06-26 19:31:44,127 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:31:44,127 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:31:44,815 - INFO - Response - Page 7:
2025-06-26 19:31:44,815 - INFO - 第 7 页获取到 50 条记录
2025-06-26 19:31:45,315 - INFO - Request Parameters - Page 8:
2025-06-26 19:31:45,315 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:31:45,315 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:31:46,018 - INFO - Response - Page 8:
2025-06-26 19:31:46,018 - INFO - 第 8 页获取到 50 条记录
2025-06-26 19:31:46,534 - INFO - Request Parameters - Page 9:
2025-06-26 19:31:46,534 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:31:46,534 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:31:47,175 - INFO - Response - Page 9:
2025-06-26 19:31:47,175 - INFO - 第 9 页获取到 50 条记录
2025-06-26 19:31:47,690 - INFO - Request Parameters - Page 10:
2025-06-26 19:31:47,690 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:31:47,690 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:31:48,269 - INFO - Response - Page 10:
2025-06-26 19:31:48,269 - INFO - 第 10 页获取到 37 条记录
2025-06-26 19:31:48,784 - INFO - 查询完成，共获取到 487 条记录
2025-06-26 19:31:48,784 - INFO - 获取到 487 条表单数据
2025-06-26 19:31:48,784 - INFO - 当前日期 2025-06-25 有 145 条MySQL数据需要处理
2025-06-26 19:31:48,784 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81UZLWWW1IFXJIA9P5JM4U3VINK4DCMSA
2025-06-26 19:31:49,222 - INFO - 更新表单数据成功: FINST-NU966I81UZLWWW1IFXJIA9P5JM4U3VINK4DCMSA
2025-06-26 19:31:49,222 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 325.92, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 5338.4, 'new_value': 5664.32}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-26 19:31:49,222 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81UZLWWW1IFXJIA9P5JM4U3VINK4DCMTA
2025-06-26 19:31:49,753 - INFO - 更新表单数据成功: FINST-NU966I81UZLWWW1IFXJIA9P5JM4U3VINK4DCMTA
2025-06-26 19:31:49,753 - INFO - 更新记录成功，变更字段: [{'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-26 19:31:49,753 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81UZLWWW1IFXJIA9P5JM4U3VINK4DCMUA
2025-06-26 19:31:50,238 - INFO - 更新表单数据成功: FINST-NU966I81UZLWWW1IFXJIA9P5JM4U3VINK4DCMUA
2025-06-26 19:31:50,238 - INFO - 更新记录成功，变更字段: [{'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-26 19:31:50,238 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81UZLWWW1IFXJIA9P5JM4U3VINK4DCMVA
2025-06-26 19:31:50,785 - INFO - 更新表单数据成功: FINST-NU966I81UZLWWW1IFXJIA9P5JM4U3VINK4DCMVA
2025-06-26 19:31:50,785 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29.9, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 100.0, 'new_value': 129.9}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-26 19:31:50,785 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81UZLWWW1IFXJIA9P5JM4U3VINK4DCMWA
2025-06-26 19:31:51,269 - INFO - 更新表单数据成功: FINST-NU966I81UZLWWW1IFXJIA9P5JM4U3VINK4DCMWA
2025-06-26 19:31:51,269 - INFO - 更新记录成功，变更字段: [{'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-26 19:31:51,269 - INFO - 开始更新记录 - 表单实例ID: FINST-NU966I81UZLWWW1IFXJIA9P5JM4U3VINK4DCMYA
2025-06-26 19:31:51,754 - INFO - 更新表单数据成功: FINST-NU966I81UZLWWW1IFXJIA9P5JM4U3VINK4DCMYA
2025-06-26 19:31:51,754 - INFO - 更新记录成功，变更字段: [{'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-26 19:31:51,754 - INFO - 开始更新记录 - 表单实例ID: FINST-XMC66R91VXKWKL6F6IQDA4CKPSX332MZI4DCME9
2025-06-26 19:31:52,207 - INFO - 更新表单数据成功: FINST-XMC66R91VXKWKL6F6IQDA4CKPSX332MZI4DCME9
2025-06-26 19:31:52,207 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8173.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 0.0, 'new_value': 8173.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-26 19:31:52,207 - INFO - 开始更新记录 - 表单实例ID: FINST-XMC66R91VXKWKL6F6IQDA4CKPSX332MZI4DCMF9
2025-06-26 19:31:52,769 - INFO - 更新表单数据成功: FINST-XMC66R91VXKWKL6F6IQDA4CKPSX332MZI4DCMF9
2025-06-26 19:31:52,769 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1068.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 15900.0, 'new_value': 16968.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-06-26 19:31:52,769 - INFO - 开始更新记录 - 表单实例ID: FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCMA3
2025-06-26 19:31:53,316 - INFO - 更新表单数据成功: FINST-0KG66Q71YOMWIYYTE2G244LCJ1JJ38S1ORCCMA3
2025-06-26 19:31:53,316 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28000.0, 'new_value': 42972.19}, {'field': 'total_amount', 'old_value': 28000.0, 'new_value': 42972.19}, {'field': 'order_count', 'old_value': 135, 'new_value': 119}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/04aa4b8212704f9eb51f3cd2c0f7536a.jpg?Expires=2066198087&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=L650tDHbn6YPaeyZuEVEdZpv8pg%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/9d71760ccd4841f0876006c624c625a0.png?Expires=2066282901&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=DjtccMPxp44WZq%2B8JI6f%2FEypoO4%3D'}]
2025-06-26 19:31:53,316 - INFO - 开始批量插入 4 条新记录
2025-06-26 19:31:53,457 - INFO - 批量插入响应状态码: 200
2025-06-26 19:31:53,457 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 26 Jun 2025 11:31:53 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '201', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F2146D3C-21AF-78BB-9231-0D8AF75C244C', 'x-acs-trace-id': '14506d9b5dce51207d8a5955a813d2e6', 'etag': '2N1PUWR5MUa1NbWFsasdtMw1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-26 19:31:53,457 - INFO - 批量插入响应体: {'result': ['FINST-W4G66DA1IZMW2610BPC9H7D1A8T72LWJZADCMX', 'FINST-W4G66DA1IZMW2610BPC9H7D1A8T72MWJZADCMY', 'FINST-W4G66DA1IZMW2610BPC9H7D1A8T72MWJZADCMZ', 'FINST-W4G66DA1IZMW2610BPC9H7D1A8T72MWJZADCM01']}
2025-06-26 19:31:53,457 - INFO - 批量插入表单数据成功，批次 1，共 4 条记录
2025-06-26 19:31:53,457 - INFO - 成功插入的数据ID: ['FINST-W4G66DA1IZMW2610BPC9H7D1A8T72LWJZADCMX', 'FINST-W4G66DA1IZMW2610BPC9H7D1A8T72MWJZADCMY', 'FINST-W4G66DA1IZMW2610BPC9H7D1A8T72MWJZADCMZ', 'FINST-W4G66DA1IZMW2610BPC9H7D1A8T72MWJZADCM01']
2025-06-26 19:31:58,473 - INFO - 批量插入完成，共 4 条记录
2025-06-26 19:31:58,473 - INFO - 日期 2025-06-25 处理完成 - 更新: 9 条，插入: 4 条，错误: 0 条
2025-06-26 19:31:58,473 - INFO - 数据同步完成！更新: 11 条，插入: 11 条，错误: 1 条
2025-06-26 19:32:58,498 - INFO - 开始同步昨天与今天的销售数据: 2025-06-25 至 2025-06-26
2025-06-26 19:32:58,498 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-26 19:32:58,498 - INFO - 查询参数: ('2025-06-25', '2025-06-26')
2025-06-26 19:32:58,638 - INFO - MySQL查询成功，时间段: 2025-06-25 至 2025-06-26，共获取 508 条记录
2025-06-26 19:32:58,638 - INFO - 获取到 1 个日期需要处理: ['2025-06-25']
2025-06-26 19:32:58,638 - INFO - 开始处理日期: 2025-06-25
2025-06-26 19:32:58,638 - INFO - Request Parameters - Page 1:
2025-06-26 19:32:58,638 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:32:58,638 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:32:59,404 - INFO - Response - Page 1:
2025-06-26 19:32:59,404 - INFO - 第 1 页获取到 50 条记录
2025-06-26 19:32:59,904 - INFO - Request Parameters - Page 2:
2025-06-26 19:32:59,904 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:32:59,904 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:33:00,576 - INFO - Response - Page 2:
2025-06-26 19:33:00,592 - INFO - 第 2 页获取到 50 条记录
2025-06-26 19:33:01,092 - INFO - Request Parameters - Page 3:
2025-06-26 19:33:01,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:33:01,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:33:01,779 - INFO - Response - Page 3:
2025-06-26 19:33:01,779 - INFO - 第 3 页获取到 50 条记录
2025-06-26 19:33:02,295 - INFO - Request Parameters - Page 4:
2025-06-26 19:33:02,295 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:33:02,295 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:33:02,936 - INFO - Response - Page 4:
2025-06-26 19:33:02,936 - INFO - 第 4 页获取到 50 条记录
2025-06-26 19:33:03,452 - INFO - Request Parameters - Page 5:
2025-06-26 19:33:03,452 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:33:03,452 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:33:04,061 - INFO - Response - Page 5:
2025-06-26 19:33:04,061 - INFO - 第 5 页获取到 50 条记录
2025-06-26 19:33:04,577 - INFO - Request Parameters - Page 6:
2025-06-26 19:33:04,577 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:33:04,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:33:05,249 - INFO - Response - Page 6:
2025-06-26 19:33:05,249 - INFO - 第 6 页获取到 50 条记录
2025-06-26 19:33:05,749 - INFO - Request Parameters - Page 7:
2025-06-26 19:33:05,749 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:33:05,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:33:06,374 - INFO - Response - Page 7:
2025-06-26 19:33:06,375 - INFO - 第 7 页获取到 50 条记录
2025-06-26 19:33:06,876 - INFO - Request Parameters - Page 8:
2025-06-26 19:33:06,876 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:33:06,876 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:33:07,501 - INFO - Response - Page 8:
2025-06-26 19:33:07,501 - INFO - 第 8 页获取到 50 条记录
2025-06-26 19:33:08,017 - INFO - Request Parameters - Page 9:
2025-06-26 19:33:08,017 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:33:08,017 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:33:08,642 - INFO - Response - Page 9:
2025-06-26 19:33:08,642 - INFO - 第 9 页获取到 50 条记录
2025-06-26 19:33:09,142 - INFO - Request Parameters - Page 10:
2025-06-26 19:33:09,142 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 19:33:09,142 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 19:33:09,798 - INFO - Response - Page 10:
2025-06-26 19:33:09,798 - INFO - 第 10 页获取到 41 条记录
2025-06-26 19:33:10,314 - INFO - 查询完成，共获取到 491 条记录
2025-06-26 19:33:10,314 - INFO - 获取到 491 条表单数据
2025-06-26 19:33:10,314 - INFO - 当前日期 2025-06-25 有 498 条MySQL数据需要处理
2025-06-26 19:33:10,330 - INFO - 开始批量插入 7 条新记录
2025-06-26 19:33:10,501 - INFO - 批量插入响应状态码: 200
2025-06-26 19:33:10,501 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 26 Jun 2025 11:33:10 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '348', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'AB8C3CCA-B43E-7FA8-82AA-7BFA98521E18', 'x-acs-trace-id': 'bd44e47b8c0a499d37f759f610c05564', 'etag': '3FKqO09ViZh9+Is220XFRzg8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-26 19:33:10,517 - INFO - 批量插入响应体: {'result': ['FINST-90E66JD1QZLWHXC16RQMV4F2YYMZ2DC71BDCML1', 'FINST-90E66JD1QZLWHXC16RQMV4F2YYMZ2DC71BDCMM1', 'FINST-90E66JD1QZLWHXC16RQMV4F2YYMZ2DC71BDCMN1', 'FINST-90E66JD1QZLWHXC16RQMV4F2YYMZ2DC71BDCMO1', 'FINST-90E66JD1QZLWHXC16RQMV4F2YYMZ2DC71BDCMP1', 'FINST-90E66JD1QZLWHXC16RQMV4F2YYMZ2DC71BDCMQ1', 'FINST-90E66JD1QZLWHXC16RQMV4F2YYMZ2DC71BDCMR1']}
2025-06-26 19:33:10,517 - INFO - 批量插入表单数据成功，批次 1，共 7 条记录
2025-06-26 19:33:10,517 - INFO - 成功插入的数据ID: ['FINST-90E66JD1QZLWHXC16RQMV4F2YYMZ2DC71BDCML1', 'FINST-90E66JD1QZLWHXC16RQMV4F2YYMZ2DC71BDCMM1', 'FINST-90E66JD1QZLWHXC16RQMV4F2YYMZ2DC71BDCMN1', 'FINST-90E66JD1QZLWHXC16RQMV4F2YYMZ2DC71BDCMO1', 'FINST-90E66JD1QZLWHXC16RQMV4F2YYMZ2DC71BDCMP1', 'FINST-90E66JD1QZLWHXC16RQMV4F2YYMZ2DC71BDCMQ1', 'FINST-90E66JD1QZLWHXC16RQMV4F2YYMZ2DC71BDCMR1']
2025-06-26 19:33:15,533 - INFO - 批量插入完成，共 7 条记录
2025-06-26 19:33:15,533 - INFO - 日期 2025-06-25 处理完成 - 更新: 0 条，插入: 7 条，错误: 0 条
2025-06-26 19:33:15,533 - INFO - 数据同步完成！更新: 0 条，插入: 7 条，错误: 0 条
2025-06-26 19:33:15,533 - INFO - 同步完成
2025-06-26 22:30:33,776 - INFO - 使用默认增量同步（当天更新数据）
2025-06-26 22:30:33,776 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-06-26 22:30:33,776 - INFO - 查询参数: ('2025-06-26',)
2025-06-26 22:30:33,917 - INFO - MySQL查询成功，增量数据（日期: 2025-06-26），共获取 190 条记录
2025-06-26 22:30:33,917 - INFO - 获取到 6 个日期需要处理: ['2025-06-14', '2025-06-21', '2025-06-23', '2025-06-24', '2025-06-25', '2025-06-26']
2025-06-26 22:30:33,917 - INFO - 开始处理日期: 2025-06-14
2025-06-26 22:30:33,932 - INFO - Request Parameters - Page 1:
2025-06-26 22:30:33,932 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:30:33,932 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1749830400000, 1749916799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:30:42,043 - ERROR - 处理日期 2025-06-14 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 80F270B3-C789-7ECB-A834-258035C8B84A Response: {'code': 'ServiceUnavailable', 'requestid': '80F270B3-C789-7ECB-A834-258035C8B84A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 80F270B3-C789-7ECB-A834-258035C8B84A)
2025-06-26 22:30:42,043 - INFO - 开始处理日期: 2025-06-21
2025-06-26 22:30:42,043 - INFO - Request Parameters - Page 1:
2025-06-26 22:30:42,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:30:42,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:30:42,793 - INFO - Response - Page 1:
2025-06-26 22:30:42,793 - INFO - 第 1 页获取到 50 条记录
2025-06-26 22:30:43,309 - INFO - Request Parameters - Page 2:
2025-06-26 22:30:43,309 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:30:43,309 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:30:43,996 - INFO - Response - Page 2:
2025-06-26 22:30:43,996 - INFO - 第 2 页获取到 50 条记录
2025-06-26 22:30:44,512 - INFO - Request Parameters - Page 3:
2025-06-26 22:30:44,512 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:30:44,512 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:30:48,856 - INFO - Response - Page 3:
2025-06-26 22:30:48,856 - INFO - 第 3 页获取到 50 条记录
2025-06-26 22:30:49,372 - INFO - Request Parameters - Page 4:
2025-06-26 22:30:49,372 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:30:49,372 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:30:50,075 - INFO - Response - Page 4:
2025-06-26 22:30:50,075 - INFO - 第 4 页获取到 50 条记录
2025-06-26 22:30:50,575 - INFO - Request Parameters - Page 5:
2025-06-26 22:30:50,575 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:30:50,575 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:30:51,247 - INFO - Response - Page 5:
2025-06-26 22:30:51,247 - INFO - 第 5 页获取到 50 条记录
2025-06-26 22:30:51,748 - INFO - Request Parameters - Page 6:
2025-06-26 22:30:51,748 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:30:51,748 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:30:52,451 - INFO - Response - Page 6:
2025-06-26 22:30:52,451 - INFO - 第 6 页获取到 50 条记录
2025-06-26 22:30:52,951 - INFO - Request Parameters - Page 7:
2025-06-26 22:30:52,951 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:30:52,951 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:30:53,607 - INFO - Response - Page 7:
2025-06-26 22:30:53,607 - INFO - 第 7 页获取到 50 条记录
2025-06-26 22:30:54,107 - INFO - Request Parameters - Page 8:
2025-06-26 22:30:54,107 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:30:54,107 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:30:54,779 - INFO - Response - Page 8:
2025-06-26 22:30:54,779 - INFO - 第 8 页获取到 50 条记录
2025-06-26 22:30:55,279 - INFO - Request Parameters - Page 9:
2025-06-26 22:30:55,279 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:30:55,279 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:30:55,936 - INFO - Response - Page 9:
2025-06-26 22:30:55,936 - INFO - 第 9 页获取到 50 条记录
2025-06-26 22:30:56,436 - INFO - Request Parameters - Page 10:
2025-06-26 22:30:56,436 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:30:56,436 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:30:57,108 - INFO - Response - Page 10:
2025-06-26 22:30:57,108 - INFO - 第 10 页获取到 50 条记录
2025-06-26 22:30:57,623 - INFO - Request Parameters - Page 11:
2025-06-26 22:30:57,623 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:30:57,623 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750435200000, 1750521599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:30:58,186 - INFO - Response - Page 11:
2025-06-26 22:30:58,186 - INFO - 第 11 页获取到 20 条记录
2025-06-26 22:30:58,686 - INFO - 查询完成，共获取到 520 条记录
2025-06-26 22:30:58,686 - INFO - 获取到 520 条表单数据
2025-06-26 22:30:58,686 - INFO - 当前日期 2025-06-21 有 1 条MySQL数据需要处理
2025-06-26 22:30:58,686 - INFO - 日期 2025-06-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 22:30:58,686 - INFO - 开始处理日期: 2025-06-23
2025-06-26 22:30:58,686 - INFO - Request Parameters - Page 1:
2025-06-26 22:30:58,686 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:30:58,686 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:30:59,436 - INFO - Response - Page 1:
2025-06-26 22:30:59,436 - INFO - 第 1 页获取到 50 条记录
2025-06-26 22:30:59,952 - INFO - Request Parameters - Page 2:
2025-06-26 22:30:59,952 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:30:59,952 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:31:00,593 - INFO - Response - Page 2:
2025-06-26 22:31:00,593 - INFO - 第 2 页获取到 50 条记录
2025-06-26 22:31:01,108 - INFO - Request Parameters - Page 3:
2025-06-26 22:31:01,108 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:31:01,108 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:31:01,812 - INFO - Response - Page 3:
2025-06-26 22:31:01,812 - INFO - 第 3 页获取到 50 条记录
2025-06-26 22:31:02,312 - INFO - Request Parameters - Page 4:
2025-06-26 22:31:02,312 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:31:02,312 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:31:03,265 - INFO - Response - Page 4:
2025-06-26 22:31:03,265 - INFO - 第 4 页获取到 50 条记录
2025-06-26 22:31:03,781 - INFO - Request Parameters - Page 5:
2025-06-26 22:31:03,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:31:03,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:31:04,437 - INFO - Response - Page 5:
2025-06-26 22:31:04,437 - INFO - 第 5 页获取到 50 条记录
2025-06-26 22:31:04,937 - INFO - Request Parameters - Page 6:
2025-06-26 22:31:04,937 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:31:04,937 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:31:05,671 - INFO - Response - Page 6:
2025-06-26 22:31:05,671 - INFO - 第 6 页获取到 50 条记录
2025-06-26 22:31:06,172 - INFO - Request Parameters - Page 7:
2025-06-26 22:31:06,172 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:31:06,172 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:31:06,875 - INFO - Response - Page 7:
2025-06-26 22:31:06,875 - INFO - 第 7 页获取到 50 条记录
2025-06-26 22:31:07,390 - INFO - Request Parameters - Page 8:
2025-06-26 22:31:07,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:31:07,390 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:31:08,094 - INFO - Response - Page 8:
2025-06-26 22:31:08,094 - INFO - 第 8 页获取到 50 条记录
2025-06-26 22:31:08,594 - INFO - Request Parameters - Page 9:
2025-06-26 22:31:08,594 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:31:08,594 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:31:09,266 - INFO - Response - Page 9:
2025-06-26 22:31:09,266 - INFO - 第 9 页获取到 50 条记录
2025-06-26 22:31:09,766 - INFO - Request Parameters - Page 10:
2025-06-26 22:31:09,766 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:31:09,766 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:31:10,438 - INFO - Response - Page 10:
2025-06-26 22:31:10,438 - INFO - 第 10 页获取到 50 条记录
2025-06-26 22:31:10,938 - INFO - Request Parameters - Page 11:
2025-06-26 22:31:10,938 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:31:10,938 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750608000000, 1750694399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:31:11,610 - INFO - Response - Page 11:
2025-06-26 22:31:11,610 - INFO - 第 11 页获取到 45 条记录
2025-06-26 22:31:12,110 - INFO - 查询完成，共获取到 545 条记录
2025-06-26 22:31:12,110 - INFO - 获取到 545 条表单数据
2025-06-26 22:31:12,110 - INFO - 当前日期 2025-06-23 有 1 条MySQL数据需要处理
2025-06-26 22:31:12,110 - INFO - 日期 2025-06-23 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 22:31:12,110 - INFO - 开始处理日期: 2025-06-24
2025-06-26 22:31:12,110 - INFO - Request Parameters - Page 1:
2025-06-26 22:31:12,110 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:31:12,110 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:31:12,782 - INFO - Response - Page 1:
2025-06-26 22:31:12,782 - INFO - 第 1 页获取到 50 条记录
2025-06-26 22:31:13,298 - INFO - Request Parameters - Page 2:
2025-06-26 22:31:13,298 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:31:13,298 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:31:13,938 - INFO - Response - Page 2:
2025-06-26 22:31:13,938 - INFO - 第 2 页获取到 50 条记录
2025-06-26 22:31:14,454 - INFO - Request Parameters - Page 3:
2025-06-26 22:31:14,454 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:31:14,454 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:31:15,126 - INFO - Response - Page 3:
2025-06-26 22:31:15,126 - INFO - 第 3 页获取到 50 条记录
2025-06-26 22:31:15,642 - INFO - Request Parameters - Page 4:
2025-06-26 22:31:15,642 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:31:15,642 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:31:16,314 - INFO - Response - Page 4:
2025-06-26 22:31:16,314 - INFO - 第 4 页获取到 50 条记录
2025-06-26 22:31:16,814 - INFO - Request Parameters - Page 5:
2025-06-26 22:31:16,814 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:31:16,814 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:31:17,423 - INFO - Response - Page 5:
2025-06-26 22:31:17,423 - INFO - 第 5 页获取到 50 条记录
2025-06-26 22:31:17,923 - INFO - Request Parameters - Page 6:
2025-06-26 22:31:17,923 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:31:17,923 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:31:18,626 - INFO - Response - Page 6:
2025-06-26 22:31:18,626 - INFO - 第 6 页获取到 50 条记录
2025-06-26 22:31:19,127 - INFO - Request Parameters - Page 7:
2025-06-26 22:31:19,127 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:31:19,127 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:31:19,767 - INFO - Response - Page 7:
2025-06-26 22:31:19,767 - INFO - 第 7 页获取到 50 条记录
2025-06-26 22:31:20,283 - INFO - Request Parameters - Page 8:
2025-06-26 22:31:20,283 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:31:20,283 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:31:20,939 - INFO - Response - Page 8:
2025-06-26 22:31:20,939 - INFO - 第 8 页获取到 50 条记录
2025-06-26 22:31:21,455 - INFO - Request Parameters - Page 9:
2025-06-26 22:31:21,455 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:31:21,455 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:31:22,096 - INFO - Response - Page 9:
2025-06-26 22:31:22,096 - INFO - 第 9 页获取到 50 条记录
2025-06-26 22:31:22,596 - INFO - Request Parameters - Page 10:
2025-06-26 22:31:22,596 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:31:22,596 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750694400000, 1750780799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:31:23,205 - INFO - Response - Page 10:
2025-06-26 22:31:23,205 - INFO - 第 10 页获取到 48 条记录
2025-06-26 22:31:23,705 - INFO - 查询完成，共获取到 498 条记录
2025-06-26 22:31:23,705 - INFO - 获取到 498 条表单数据
2025-06-26 22:31:23,705 - INFO - 当前日期 2025-06-24 有 7 条MySQL数据需要处理
2025-06-26 22:31:23,705 - INFO - 日期 2025-06-24 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 22:31:23,705 - INFO - 开始处理日期: 2025-06-25
2025-06-26 22:31:23,705 - INFO - Request Parameters - Page 1:
2025-06-26 22:31:23,705 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:31:23,705 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:31:24,393 - INFO - Response - Page 1:
2025-06-26 22:31:24,393 - INFO - 第 1 页获取到 50 条记录
2025-06-26 22:31:24,893 - INFO - Request Parameters - Page 2:
2025-06-26 22:31:24,893 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:31:24,893 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:31:25,612 - INFO - Response - Page 2:
2025-06-26 22:31:25,612 - INFO - 第 2 页获取到 50 条记录
2025-06-26 22:31:26,112 - INFO - Request Parameters - Page 3:
2025-06-26 22:31:26,112 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:31:26,112 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:31:26,784 - INFO - Response - Page 3:
2025-06-26 22:31:26,784 - INFO - 第 3 页获取到 50 条记录
2025-06-26 22:31:27,300 - INFO - Request Parameters - Page 4:
2025-06-26 22:31:27,300 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:31:27,300 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:31:27,956 - INFO - Response - Page 4:
2025-06-26 22:31:27,956 - INFO - 第 4 页获取到 50 条记录
2025-06-26 22:31:28,456 - INFO - Request Parameters - Page 5:
2025-06-26 22:31:28,456 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:31:28,456 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:31:29,128 - INFO - Response - Page 5:
2025-06-26 22:31:29,128 - INFO - 第 5 页获取到 50 条记录
2025-06-26 22:31:29,644 - INFO - Request Parameters - Page 6:
2025-06-26 22:31:29,644 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:31:29,644 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:31:30,347 - INFO - Response - Page 6:
2025-06-26 22:31:30,347 - INFO - 第 6 页获取到 50 条记录
2025-06-26 22:31:30,863 - INFO - Request Parameters - Page 7:
2025-06-26 22:31:30,863 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:31:30,863 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:31:31,519 - INFO - Response - Page 7:
2025-06-26 22:31:31,519 - INFO - 第 7 页获取到 50 条记录
2025-06-26 22:31:32,035 - INFO - Request Parameters - Page 8:
2025-06-26 22:31:32,035 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:31:32,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:31:32,675 - INFO - Response - Page 8:
2025-06-26 22:31:32,675 - INFO - 第 8 页获取到 50 条记录
2025-06-26 22:31:33,191 - INFO - Request Parameters - Page 9:
2025-06-26 22:31:33,191 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:31:33,191 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:31:33,801 - INFO - Response - Page 9:
2025-06-26 22:31:33,801 - INFO - 第 9 页获取到 50 条记录
2025-06-26 22:31:34,301 - INFO - Request Parameters - Page 10:
2025-06-26 22:31:34,301 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:31:34,301 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:31:35,066 - INFO - Response - Page 10:
2025-06-26 22:31:35,066 - INFO - 第 10 页获取到 48 条记录
2025-06-26 22:31:35,582 - INFO - 查询完成，共获取到 498 条记录
2025-06-26 22:31:35,582 - INFO - 获取到 498 条表单数据
2025-06-26 22:31:35,582 - INFO - 当前日期 2025-06-25 有 145 条MySQL数据需要处理
2025-06-26 22:31:35,582 - INFO - 日期 2025-06-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 22:31:35,582 - INFO - 开始处理日期: 2025-06-26
2025-06-26 22:31:35,582 - INFO - Request Parameters - Page 1:
2025-06-26 22:31:35,582 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:31:35,582 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:31:36,051 - INFO - Response - Page 1:
2025-06-26 22:31:36,051 - INFO - 查询完成，共获取到 0 条记录
2025-06-26 22:31:36,051 - INFO - 获取到 0 条表单数据
2025-06-26 22:31:36,051 - INFO - 当前日期 2025-06-26 有 31 条MySQL数据需要处理
2025-06-26 22:31:36,051 - INFO - 开始批量插入 31 条新记录
2025-06-26 22:31:36,285 - INFO - 批量插入响应状态码: 200
2025-06-26 22:31:36,285 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 26 Jun 2025 14:31:34 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1500', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '394C045B-DC9B-77B4-B3A5-836D55131715', 'x-acs-trace-id': '34e69a04b773d6e26f22f095b85db97c', 'etag': '1AwgS/aBXAEsezStCapFkeg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-26 22:31:36,285 - INFO - 批量插入响应体: {'result': ['FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCM13', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCM23', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCM33', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCM43', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCM53', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCM63', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCM73', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCM83', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCM93', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCMA3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCMB3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCMC3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCMD3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCME3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCMF3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCMG3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCMH3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCMI3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCMJ3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCMK3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCML3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCMM3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCMN3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCMO3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCMP3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCMQ3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCMR3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2IRMEHDCMS3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2IRMEHDCMT3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2IRMEHDCMU3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2IRMEHDCMV3']}
2025-06-26 22:31:36,285 - INFO - 批量插入表单数据成功，批次 1，共 31 条记录
2025-06-26 22:31:36,285 - INFO - 成功插入的数据ID: ['FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCM13', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCM23', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCM33', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCM43', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCM53', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCM63', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCM73', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCM83', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCM93', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCMA3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCMB3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCMC3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCMD3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCME3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCMF3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCMG3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCMH3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCMI3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCMJ3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCMK3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCML3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCMM3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCMN3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCMO3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCMP3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCMQ3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2HRMEHDCMR3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2IRMEHDCMS3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2IRMEHDCMT3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2IRMEHDCMU3', 'FINST-2PF66KD11VMWCPAZECM6D5EHQFBF2IRMEHDCMV3']
2025-06-26 22:31:41,302 - INFO - 批量插入完成，共 31 条记录
2025-06-26 22:31:41,302 - INFO - 日期 2025-06-26 处理完成 - 更新: 0 条，插入: 31 条，错误: 0 条
2025-06-26 22:31:41,302 - INFO - 数据同步完成！更新: 0 条，插入: 31 条，错误: 1 条
2025-06-26 22:32:41,326 - INFO - 开始同步昨天与今天的销售数据: 2025-06-25 至 2025-06-26
2025-06-26 22:32:41,326 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-06-26 22:32:41,326 - INFO - 查询参数: ('2025-06-25', '2025-06-26')
2025-06-26 22:32:41,467 - INFO - MySQL查询成功，时间段: 2025-06-25 至 2025-06-26，共获取 542 条记录
2025-06-26 22:32:41,467 - INFO - 获取到 2 个日期需要处理: ['2025-06-25', '2025-06-26']
2025-06-26 22:32:41,467 - INFO - 开始处理日期: 2025-06-25
2025-06-26 22:32:41,467 - INFO - Request Parameters - Page 1:
2025-06-26 22:32:41,467 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:32:41,467 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:32:42,170 - INFO - Response - Page 1:
2025-06-26 22:32:42,170 - INFO - 第 1 页获取到 50 条记录
2025-06-26 22:32:42,686 - INFO - Request Parameters - Page 2:
2025-06-26 22:32:42,686 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:32:42,686 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:32:43,342 - INFO - Response - Page 2:
2025-06-26 22:32:43,342 - INFO - 第 2 页获取到 50 条记录
2025-06-26 22:32:43,858 - INFO - Request Parameters - Page 3:
2025-06-26 22:32:43,858 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:32:43,858 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:32:44,530 - INFO - Response - Page 3:
2025-06-26 22:32:44,530 - INFO - 第 3 页获取到 50 条记录
2025-06-26 22:32:45,030 - INFO - Request Parameters - Page 4:
2025-06-26 22:32:45,030 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:32:45,030 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:32:45,717 - INFO - Response - Page 4:
2025-06-26 22:32:45,717 - INFO - 第 4 页获取到 50 条记录
2025-06-26 22:32:46,217 - INFO - Request Parameters - Page 5:
2025-06-26 22:32:46,217 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:32:46,217 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:32:46,874 - INFO - Response - Page 5:
2025-06-26 22:32:46,874 - INFO - 第 5 页获取到 50 条记录
2025-06-26 22:32:47,390 - INFO - Request Parameters - Page 6:
2025-06-26 22:32:47,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:32:47,390 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:32:48,077 - INFO - Response - Page 6:
2025-06-26 22:32:48,077 - INFO - 第 6 页获取到 50 条记录
2025-06-26 22:32:48,577 - INFO - Request Parameters - Page 7:
2025-06-26 22:32:48,577 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:32:48,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:32:49,249 - INFO - Response - Page 7:
2025-06-26 22:32:49,249 - INFO - 第 7 页获取到 50 条记录
2025-06-26 22:32:49,765 - INFO - Request Parameters - Page 8:
2025-06-26 22:32:49,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:32:49,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:32:50,421 - INFO - Response - Page 8:
2025-06-26 22:32:50,421 - INFO - 第 8 页获取到 50 条记录
2025-06-26 22:32:50,921 - INFO - Request Parameters - Page 9:
2025-06-26 22:32:50,921 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:32:50,921 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:32:51,531 - INFO - Response - Page 9:
2025-06-26 22:32:51,531 - INFO - 第 9 页获取到 50 条记录
2025-06-26 22:32:52,046 - INFO - Request Parameters - Page 10:
2025-06-26 22:32:52,046 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:32:52,046 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750780800000, 1750867199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:32:52,672 - INFO - Response - Page 10:
2025-06-26 22:32:52,672 - INFO - 第 10 页获取到 48 条记录
2025-06-26 22:32:53,172 - INFO - 查询完成，共获取到 498 条记录
2025-06-26 22:32:53,172 - INFO - 获取到 498 条表单数据
2025-06-26 22:32:53,172 - INFO - 当前日期 2025-06-25 有 498 条MySQL数据需要处理
2025-06-26 22:32:53,187 - INFO - 日期 2025-06-25 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-26 22:32:53,187 - INFO - 开始处理日期: 2025-06-26
2025-06-26 22:32:53,187 - INFO - Request Parameters - Page 1:
2025-06-26 22:32:53,187 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-26 22:32:53,187 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1750867200000, 1750953599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-26 22:32:53,906 - INFO - Response - Page 1:
2025-06-26 22:32:53,906 - INFO - 第 1 页获取到 31 条记录
2025-06-26 22:32:54,422 - INFO - 查询完成，共获取到 31 条记录
2025-06-26 22:32:54,422 - INFO - 获取到 31 条表单数据
2025-06-26 22:32:54,422 - INFO - 当前日期 2025-06-26 有 33 条MySQL数据需要处理
2025-06-26 22:32:54,422 - INFO - 开始批量插入 2 条新记录
2025-06-26 22:32:54,594 - INFO - 批量插入响应状态码: 200
2025-06-26 22:32:54,594 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 26 Jun 2025 14:32:52 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F703984F-74C2-71E8-B495-26219E2DCF1A', 'x-acs-trace-id': 'f8a22f5d434d3fec3ed72e60f9bd3b47', 'etag': '16AcP78stHBablojlF1OyKA8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-26 22:32:54,594 - INFO - 批量插入响应体: {'result': ['FINST-X3E66X814WMW3RJEFHQEJ7LW4DKK2A6BGHDCMF8', 'FINST-X3E66X814WMW3RJEFHQEJ7LW4DKK2B6BGHDCMG8']}
2025-06-26 22:32:54,594 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-06-26 22:32:54,594 - INFO - 成功插入的数据ID: ['FINST-X3E66X814WMW3RJEFHQEJ7LW4DKK2A6BGHDCMF8', 'FINST-X3E66X814WMW3RJEFHQEJ7LW4DKK2B6BGHDCMG8']
2025-06-26 22:32:59,610 - INFO - 批量插入完成，共 2 条记录
2025-06-26 22:32:59,610 - INFO - 日期 2025-06-26 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-06-26 22:32:59,610 - INFO - 数据同步完成！更新: 0 条，插入: 2 条，错误: 0 条
2025-06-26 22:32:59,610 - INFO - 同步完成
