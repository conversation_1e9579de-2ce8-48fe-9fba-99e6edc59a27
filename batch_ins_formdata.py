# -*- coding: utf-8 -*-
# This file is auto-generated, don't edit it. Thanks.
import sys

from typing import List

from alibabacloud_dingtalk.yida_1_0.client import Client as dingtalkyida_1_0Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dingtalk.yida_1_0 import models as dingtalkyida__1__0_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_util.client import Client as UtilClient


class Sample:
    def __init__(self):
        pass

    @staticmethod
    def create_client() -> dingtalkyida_1_0Client:
        """
        使用 Token 初始化账号Client
        @return: Client
        @throws Exception
        """
        config = open_api_models.Config()
        config.protocol = 'https'
        config.region_id = 'central'
        return dingtalkyida_1_0Client(config)

    @staticmethod
    def main(
        args: List[str],
    ) -> None:
        client = Sample.create_client()
        batch_save_form_data_headers = dingtalkyida__1__0_models.BatchSaveFormDataHeaders()
        batch_save_form_data_headers.x_acs_dingtalk_access_token = '<your access token>'
        batch_save_form_data_request = dingtalkyida__1__0_models.BatchSaveFormDataRequest(
            no_execute_expression=True,
            form_uuid='FORM-GX866MC1NC1VOFF6WVQW33FD16E23L3CPMKVKA',
            app_type='APP_XCE0EVXS6DYG3YDYC5RD',
            asynchronous_execution=True,
            system_token='09866181UTZVVD4R3DC955FNKIM52HVPU5WWK7',
            keep_running_after_exception=True,
            user_id='ding173982232112232',
            form_data_json_list=[
                '{"countrySelectField_l0c1cwiu":[{"value":"US"}],"addressField_l0c1cwiy":{"address":"111","regionIds":[460000,469027,469023401],"regionText":[{"en_US":"hai+nan+sheng","zh_CN":"海南省"},{"en_US":"cheng+mai+xian","zh_CN":"澄迈县"},{"en_US":"guo+ying+hong+gang+nong+chang","zh_CN":"国营红岗农场"}]}}'
            ]
        )
        try:
            client.batch_save_form_data_with_options(batch_save_form_data_request, batch_save_form_data_headers, util_models.RuntimeOptions())
        except Exception as err:
            if not UtilClient.empty(err.code) and not UtilClient.empty(err.message):
                # err 中含有 code 和 message 属性，可帮助开发定位问题
                pass

    @staticmethod
    async def main_async(
        args: List[str],
    ) -> None:
        client = Sample.create_client()
        batch_save_form_data_headers = dingtalkyida__1__0_models.BatchSaveFormDataHeaders()
        batch_save_form_data_headers.x_acs_dingtalk_access_token = '<your access token>'
        batch_save_form_data_request = dingtalkyida__1__0_models.BatchSaveFormDataRequest(
            no_execute_expression=True,
            form_uuid='FORM-GX866MC1NC1VOFF6WVQW33FD16E23L3CPMKVKA',
            app_type='APP_XCE0EVXS6DYG3YDYC5RD',
            asynchronous_execution=True,
            system_token='09866181UTZVVD4R3DC955FNKIM52HVPU5WWK7',
            keep_running_after_exception=True,
            user_id='ding173982232112232',
            form_data_json_list=[
                '{"countrySelectField_l0c1cwiu":[{"value":"US"}],"addressField_l0c1cwiy":{"address":"111","regionIds":[460000,469027,469023401],"regionText":[{"en_US":"hai+nan+sheng","zh_CN":"海南省"},{"en_US":"cheng+mai+xian","zh_CN":"澄迈县"},{"en_US":"guo+ying+hong+gang+nong+chang","zh_CN":"国营红岗农场"}]}}'
            ]
        )
        try:
            await client.batch_save_form_data_with_options_async(batch_save_form_data_request, batch_save_form_data_headers, util_models.RuntimeOptions())
        except Exception as err:
            if not UtilClient.empty(err.code) and not UtilClient.empty(err.message):
                # err 中含有 code 和 message 属性，可帮助开发定位问题
                pass


if __name__ == '__main__':
    Sample.main(sys.argv[1:])