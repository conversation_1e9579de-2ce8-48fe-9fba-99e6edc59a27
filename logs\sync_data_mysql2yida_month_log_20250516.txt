2025-05-16 00:00:03,523 - INFO - =================使用默认全量同步=============
2025-05-16 00:00:04,930 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-16 00:00:04,930 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-16 00:00:04,961 - INFO - 开始处理日期: 2025-01
2025-05-16 00:00:04,961 - INFO - Request Parameters - Page 1:
2025-05-16 00:00:04,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 00:00:04,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 00:00:05,899 - INFO - Response - Page 1:
2025-05-16 00:00:06,102 - INFO - 第 1 页获取到 100 条记录
2025-05-16 00:00:06,102 - INFO - Request Parameters - Page 2:
2025-05-16 00:00:06,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 00:00:06,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 00:00:06,946 - INFO - Response - Page 2:
2025-05-16 00:00:07,150 - INFO - 第 2 页获取到 100 条记录
2025-05-16 00:00:07,150 - INFO - Request Parameters - Page 3:
2025-05-16 00:00:07,150 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 00:00:07,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 00:00:07,603 - INFO - Response - Page 3:
2025-05-16 00:00:07,806 - INFO - 第 3 页获取到 100 条记录
2025-05-16 00:00:07,806 - INFO - Request Parameters - Page 4:
2025-05-16 00:00:07,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 00:00:07,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 00:00:08,291 - INFO - Response - Page 4:
2025-05-16 00:00:08,494 - INFO - 第 4 页获取到 100 条记录
2025-05-16 00:00:08,494 - INFO - Request Parameters - Page 5:
2025-05-16 00:00:08,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 00:00:08,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 00:00:09,119 - INFO - Response - Page 5:
2025-05-16 00:00:09,323 - INFO - 第 5 页获取到 100 条记录
2025-05-16 00:00:09,323 - INFO - Request Parameters - Page 6:
2025-05-16 00:00:09,323 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 00:00:09,323 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 00:00:09,792 - INFO - Response - Page 6:
2025-05-16 00:00:09,995 - INFO - 第 6 页获取到 100 条记录
2025-05-16 00:00:09,995 - INFO - Request Parameters - Page 7:
2025-05-16 00:00:09,995 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 00:00:09,995 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 00:00:10,464 - INFO - Response - Page 7:
2025-05-16 00:00:10,667 - INFO - 第 7 页获取到 82 条记录
2025-05-16 00:00:10,667 - INFO - 查询完成，共获取到 682 条记录
2025-05-16 00:00:10,667 - INFO - 获取到 682 条表单数据
2025-05-16 00:00:10,667 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-16 00:00:10,683 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-16 00:00:10,683 - INFO - 开始处理日期: 2025-02
2025-05-16 00:00:10,683 - INFO - Request Parameters - Page 1:
2025-05-16 00:00:10,683 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 00:00:10,683 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 00:00:11,183 - INFO - Response - Page 1:
2025-05-16 00:00:11,386 - INFO - 第 1 页获取到 100 条记录
2025-05-16 00:00:11,386 - INFO - Request Parameters - Page 2:
2025-05-16 00:00:11,386 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 00:00:11,386 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 00:00:11,871 - INFO - Response - Page 2:
2025-05-16 00:00:12,074 - INFO - 第 2 页获取到 100 条记录
2025-05-16 00:00:12,074 - INFO - Request Parameters - Page 3:
2025-05-16 00:00:12,074 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 00:00:12,074 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 00:00:12,574 - INFO - Response - Page 3:
2025-05-16 00:00:12,778 - INFO - 第 3 页获取到 100 条记录
2025-05-16 00:00:12,778 - INFO - Request Parameters - Page 4:
2025-05-16 00:00:12,778 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 00:00:12,778 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 00:00:13,262 - INFO - Response - Page 4:
2025-05-16 00:00:13,466 - INFO - 第 4 页获取到 100 条记录
2025-05-16 00:00:13,466 - INFO - Request Parameters - Page 5:
2025-05-16 00:00:13,466 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 00:00:13,466 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 00:00:13,950 - INFO - Response - Page 5:
2025-05-16 00:00:14,153 - INFO - 第 5 页获取到 100 条记录
2025-05-16 00:00:14,153 - INFO - Request Parameters - Page 6:
2025-05-16 00:00:14,153 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 00:00:14,153 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 00:00:14,669 - INFO - Response - Page 6:
2025-05-16 00:00:14,873 - INFO - 第 6 页获取到 100 条记录
2025-05-16 00:00:14,873 - INFO - Request Parameters - Page 7:
2025-05-16 00:00:14,873 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 00:00:14,873 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 00:00:15,310 - INFO - Response - Page 7:
2025-05-16 00:00:15,514 - INFO - 第 7 页获取到 70 条记录
2025-05-16 00:00:15,514 - INFO - 查询完成，共获取到 670 条记录
2025-05-16 00:00:15,514 - INFO - 获取到 670 条表单数据
2025-05-16 00:00:15,514 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-16 00:00:15,529 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-16 00:00:15,529 - INFO - 开始处理日期: 2025-03
2025-05-16 00:00:15,529 - INFO - Request Parameters - Page 1:
2025-05-16 00:00:15,529 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 00:00:15,529 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 00:00:15,983 - INFO - Response - Page 1:
2025-05-16 00:00:16,186 - INFO - 第 1 页获取到 100 条记录
2025-05-16 00:00:16,186 - INFO - Request Parameters - Page 2:
2025-05-16 00:00:16,186 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 00:00:16,186 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 00:00:16,717 - INFO - Response - Page 2:
2025-05-16 00:00:16,921 - INFO - 第 2 页获取到 100 条记录
2025-05-16 00:00:16,921 - INFO - Request Parameters - Page 3:
2025-05-16 00:00:16,921 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 00:00:16,921 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 00:00:17,452 - INFO - Response - Page 3:
2025-05-16 00:00:17,655 - INFO - 第 3 页获取到 100 条记录
2025-05-16 00:00:17,655 - INFO - Request Parameters - Page 4:
2025-05-16 00:00:17,655 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 00:00:17,655 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 00:00:18,109 - INFO - Response - Page 4:
2025-05-16 00:00:18,312 - INFO - 第 4 页获取到 100 条记录
2025-05-16 00:00:18,312 - INFO - Request Parameters - Page 5:
2025-05-16 00:00:18,312 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 00:00:18,312 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 00:00:18,734 - INFO - Response - Page 5:
2025-05-16 00:00:18,937 - INFO - 第 5 页获取到 100 条记录
2025-05-16 00:00:18,937 - INFO - Request Parameters - Page 6:
2025-05-16 00:00:18,937 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 00:00:18,937 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 00:00:19,453 - INFO - Response - Page 6:
2025-05-16 00:00:19,656 - INFO - 第 6 页获取到 100 条记录
2025-05-16 00:00:19,656 - INFO - Request Parameters - Page 7:
2025-05-16 00:00:19,656 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 00:00:19,656 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 00:00:20,125 - INFO - Response - Page 7:
2025-05-16 00:00:20,329 - INFO - 第 7 页获取到 61 条记录
2025-05-16 00:00:20,329 - INFO - 查询完成，共获取到 661 条记录
2025-05-16 00:00:20,329 - INFO - 获取到 661 条表单数据
2025-05-16 00:00:20,329 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-16 00:00:20,344 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-16 00:00:20,344 - INFO - 开始处理日期: 2025-04
2025-05-16 00:00:20,344 - INFO - Request Parameters - Page 1:
2025-05-16 00:00:20,344 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 00:00:20,344 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 00:00:20,876 - INFO - Response - Page 1:
2025-05-16 00:00:21,079 - INFO - 第 1 页获取到 100 条记录
2025-05-16 00:00:21,079 - INFO - Request Parameters - Page 2:
2025-05-16 00:00:21,079 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 00:00:21,079 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 00:00:21,548 - INFO - Response - Page 2:
2025-05-16 00:00:21,751 - INFO - 第 2 页获取到 100 条记录
2025-05-16 00:00:21,751 - INFO - Request Parameters - Page 3:
2025-05-16 00:00:21,751 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 00:00:21,751 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 00:00:22,252 - INFO - Response - Page 3:
2025-05-16 00:00:22,455 - INFO - 第 3 页获取到 100 条记录
2025-05-16 00:00:22,455 - INFO - Request Parameters - Page 4:
2025-05-16 00:00:22,455 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 00:00:22,455 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 00:00:22,971 - INFO - Response - Page 4:
2025-05-16 00:00:23,174 - INFO - 第 4 页获取到 100 条记录
2025-05-16 00:00:23,174 - INFO - Request Parameters - Page 5:
2025-05-16 00:00:23,174 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 00:00:23,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 00:00:23,643 - INFO - Response - Page 5:
2025-05-16 00:00:23,846 - INFO - 第 5 页获取到 100 条记录
2025-05-16 00:00:23,846 - INFO - Request Parameters - Page 6:
2025-05-16 00:00:23,846 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 00:00:23,846 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 00:00:24,378 - INFO - Response - Page 6:
2025-05-16 00:00:24,581 - INFO - 第 6 页获取到 100 条记录
2025-05-16 00:00:24,581 - INFO - Request Parameters - Page 7:
2025-05-16 00:00:24,581 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 00:00:24,581 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 00:00:25,003 - INFO - Response - Page 7:
2025-05-16 00:00:25,206 - INFO - 第 7 页获取到 54 条记录
2025-05-16 00:00:25,206 - INFO - 查询完成，共获取到 654 条记录
2025-05-16 00:00:25,206 - INFO - 获取到 654 条表单数据
2025-05-16 00:00:25,206 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-16 00:00:25,222 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M3B
2025-05-16 00:00:25,644 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M3B
2025-05-16 00:00:25,644 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 267852.0, 'new_value': 287617.93}, {'field': 'total_amount', 'old_value': 267852.0, 'new_value': 287617.93}]
2025-05-16 00:00:25,660 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MV1
2025-05-16 00:00:26,097 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MV1
2025-05-16 00:00:26,097 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 144147.0, 'new_value': 200543.07}, {'field': 'total_amount', 'old_value': 144147.0, 'new_value': 200543.07}]
2025-05-16 00:00:26,097 - INFO - 日期 2025-04 处理完成 - 更新: 2 条，插入: 0 条，错误: 0 条
2025-05-16 00:00:26,097 - INFO - 开始处理日期: 2025-05
2025-05-16 00:00:26,097 - INFO - Request Parameters - Page 1:
2025-05-16 00:00:26,097 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 00:00:26,097 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 00:00:26,582 - INFO - Response - Page 1:
2025-05-16 00:00:26,785 - INFO - 第 1 页获取到 100 条记录
2025-05-16 00:00:26,785 - INFO - Request Parameters - Page 2:
2025-05-16 00:00:26,785 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 00:00:26,785 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 00:00:27,270 - INFO - Response - Page 2:
2025-05-16 00:00:27,473 - INFO - 第 2 页获取到 100 条记录
2025-05-16 00:00:27,473 - INFO - Request Parameters - Page 3:
2025-05-16 00:00:27,473 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 00:00:27,473 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 00:00:27,880 - INFO - Response - Page 3:
2025-05-16 00:00:28,083 - INFO - 第 3 页获取到 100 条记录
2025-05-16 00:00:28,083 - INFO - Request Parameters - Page 4:
2025-05-16 00:00:28,083 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 00:00:28,083 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 00:00:28,599 - INFO - Response - Page 4:
2025-05-16 00:00:28,802 - INFO - 第 4 页获取到 100 条记录
2025-05-16 00:00:28,802 - INFO - Request Parameters - Page 5:
2025-05-16 00:00:28,802 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 00:00:28,802 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 00:00:29,333 - INFO - Response - Page 5:
2025-05-16 00:00:29,537 - INFO - 第 5 页获取到 100 条记录
2025-05-16 00:00:29,537 - INFO - Request Parameters - Page 6:
2025-05-16 00:00:29,537 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 00:00:29,537 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 00:00:30,037 - INFO - Response - Page 6:
2025-05-16 00:00:30,240 - INFO - 第 6 页获取到 100 条记录
2025-05-16 00:00:30,240 - INFO - Request Parameters - Page 7:
2025-05-16 00:00:30,240 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 00:00:30,240 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 00:00:30,584 - INFO - Response - Page 7:
2025-05-16 00:00:30,787 - INFO - 第 7 页获取到 25 条记录
2025-05-16 00:00:30,787 - INFO - 查询完成，共获取到 625 条记录
2025-05-16 00:00:30,787 - INFO - 获取到 625 条表单数据
2025-05-16 00:00:30,787 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-16 00:00:30,787 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-16 00:00:31,194 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-16 00:00:31,194 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 70239.0, 'new_value': 74368.0}, {'field': 'offline_amount', 'old_value': 74267.28, 'new_value': 81145.28}, {'field': 'total_amount', 'old_value': 144506.28, 'new_value': 155513.28}, {'field': 'order_count', 'old_value': 3092, 'new_value': 3325}]
2025-05-16 00:00:31,194 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-16 00:00:31,616 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-16 00:00:31,616 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13393.2, 'new_value': 14840.2}, {'field': 'offline_amount', 'old_value': 74364.66, 'new_value': 77119.84}, {'field': 'total_amount', 'old_value': 87757.86, 'new_value': 91960.04}, {'field': 'order_count', 'old_value': 148, 'new_value': 157}]
2025-05-16 00:00:31,616 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-16 00:00:32,085 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-16 00:00:32,085 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27345.45, 'new_value': 28780.58}, {'field': 'offline_amount', 'old_value': 389072.74, 'new_value': 418017.64}, {'field': 'total_amount', 'old_value': 416418.19, 'new_value': 446798.22}, {'field': 'order_count', 'old_value': 1686, 'new_value': 1841}]
2025-05-16 00:00:32,085 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-16 00:00:32,538 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-16 00:00:32,538 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1064804.23, 'new_value': 1125849.35}, {'field': 'total_amount', 'old_value': 1064804.23, 'new_value': 1125849.35}, {'field': 'order_count', 'old_value': 8267, 'new_value': 8839}]
2025-05-16 00:00:32,538 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-16 00:00:32,929 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-16 00:00:32,929 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13345.17, 'new_value': 14224.23}, {'field': 'total_amount', 'old_value': 14695.17, 'new_value': 15574.23}, {'field': 'order_count', 'old_value': 281, 'new_value': 293}]
2025-05-16 00:00:32,929 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-16 00:00:33,383 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-16 00:00:33,383 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 420987.88, 'new_value': 439102.5}, {'field': 'total_amount', 'old_value': 420987.88, 'new_value': 439102.5}, {'field': 'order_count', 'old_value': 2831, 'new_value': 2964}]
2025-05-16 00:00:33,383 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-16 00:00:33,805 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-16 00:00:33,805 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 122928.61, 'new_value': 129178.64}, {'field': 'offline_amount', 'old_value': 249946.92, 'new_value': 257946.92}, {'field': 'total_amount', 'old_value': 372875.53, 'new_value': 387125.56}, {'field': 'order_count', 'old_value': 831, 'new_value': 873}]
2025-05-16 00:00:33,820 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-05-16 00:00:34,289 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-05-16 00:00:34,289 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1542.96, 'new_value': 2376.62}, {'field': 'offline_amount', 'old_value': 14063.14, 'new_value': 17723.15}, {'field': 'total_amount', 'old_value': 15606.1, 'new_value': 20099.77}, {'field': 'order_count', 'old_value': 643, 'new_value': 809}]
2025-05-16 00:00:34,289 - INFO - 日期 2025-05 处理完成 - 更新: 8 条，插入: 0 条，错误: 0 条
2025-05-16 00:00:34,289 - INFO - 数据同步完成！更新: 10 条，插入: 0 条，错误: 0 条
2025-05-16 00:00:34,289 - INFO - =================同步完成====================
2025-05-16 03:00:03,170 - INFO - =================使用默认全量同步=============
2025-05-16 03:00:04,514 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-16 03:00:04,514 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-16 03:00:04,530 - INFO - 开始处理日期: 2025-01
2025-05-16 03:00:04,545 - INFO - Request Parameters - Page 1:
2025-05-16 03:00:04,545 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 03:00:04,545 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 03:00:05,608 - INFO - Response - Page 1:
2025-05-16 03:00:05,812 - INFO - 第 1 页获取到 100 条记录
2025-05-16 03:00:05,812 - INFO - Request Parameters - Page 2:
2025-05-16 03:00:05,812 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 03:00:05,812 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 03:00:06,515 - INFO - Response - Page 2:
2025-05-16 03:00:06,718 - INFO - 第 2 页获取到 100 条记录
2025-05-16 03:00:06,718 - INFO - Request Parameters - Page 3:
2025-05-16 03:00:06,718 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 03:00:06,718 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 03:00:07,140 - INFO - Response - Page 3:
2025-05-16 03:00:07,344 - INFO - 第 3 页获取到 100 条记录
2025-05-16 03:00:07,344 - INFO - Request Parameters - Page 4:
2025-05-16 03:00:07,344 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 03:00:07,344 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 03:00:07,891 - INFO - Response - Page 4:
2025-05-16 03:00:08,094 - INFO - 第 4 页获取到 100 条记录
2025-05-16 03:00:08,094 - INFO - Request Parameters - Page 5:
2025-05-16 03:00:08,094 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 03:00:08,094 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 03:00:08,579 - INFO - Response - Page 5:
2025-05-16 03:00:08,782 - INFO - 第 5 页获取到 100 条记录
2025-05-16 03:00:08,782 - INFO - Request Parameters - Page 6:
2025-05-16 03:00:08,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 03:00:08,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 03:00:09,204 - INFO - Response - Page 6:
2025-05-16 03:00:09,407 - INFO - 第 6 页获取到 100 条记录
2025-05-16 03:00:09,407 - INFO - Request Parameters - Page 7:
2025-05-16 03:00:09,407 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 03:00:09,407 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 03:00:09,923 - INFO - Response - Page 7:
2025-05-16 03:00:10,126 - INFO - 第 7 页获取到 82 条记录
2025-05-16 03:00:10,126 - INFO - 查询完成，共获取到 682 条记录
2025-05-16 03:00:10,126 - INFO - 获取到 682 条表单数据
2025-05-16 03:00:10,126 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-16 03:00:10,142 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-16 03:00:10,142 - INFO - 开始处理日期: 2025-02
2025-05-16 03:00:10,142 - INFO - Request Parameters - Page 1:
2025-05-16 03:00:10,142 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 03:00:10,142 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 03:00:10,674 - INFO - Response - Page 1:
2025-05-16 03:00:10,877 - INFO - 第 1 页获取到 100 条记录
2025-05-16 03:00:10,877 - INFO - Request Parameters - Page 2:
2025-05-16 03:00:10,877 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 03:00:10,877 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 03:00:11,299 - INFO - Response - Page 2:
2025-05-16 03:00:11,502 - INFO - 第 2 页获取到 100 条记录
2025-05-16 03:00:11,502 - INFO - Request Parameters - Page 3:
2025-05-16 03:00:11,502 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 03:00:11,502 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 03:00:12,018 - INFO - Response - Page 3:
2025-05-16 03:00:12,221 - INFO - 第 3 页获取到 100 条记录
2025-05-16 03:00:12,221 - INFO - Request Parameters - Page 4:
2025-05-16 03:00:12,221 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 03:00:12,221 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 03:00:12,675 - INFO - Response - Page 4:
2025-05-16 03:00:12,878 - INFO - 第 4 页获取到 100 条记录
2025-05-16 03:00:12,878 - INFO - Request Parameters - Page 5:
2025-05-16 03:00:12,878 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 03:00:12,878 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 03:00:13,347 - INFO - Response - Page 5:
2025-05-16 03:00:13,550 - INFO - 第 5 页获取到 100 条记录
2025-05-16 03:00:13,550 - INFO - Request Parameters - Page 6:
2025-05-16 03:00:13,550 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 03:00:13,550 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 03:00:14,035 - INFO - Response - Page 6:
2025-05-16 03:00:14,238 - INFO - 第 6 页获取到 100 条记录
2025-05-16 03:00:14,238 - INFO - Request Parameters - Page 7:
2025-05-16 03:00:14,238 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 03:00:14,238 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 03:00:14,676 - INFO - Response - Page 7:
2025-05-16 03:00:14,879 - INFO - 第 7 页获取到 70 条记录
2025-05-16 03:00:14,879 - INFO - 查询完成，共获取到 670 条记录
2025-05-16 03:00:14,879 - INFO - 获取到 670 条表单数据
2025-05-16 03:00:14,879 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-16 03:00:14,895 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-16 03:00:14,895 - INFO - 开始处理日期: 2025-03
2025-05-16 03:00:14,895 - INFO - Request Parameters - Page 1:
2025-05-16 03:00:14,895 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 03:00:14,895 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 03:00:15,395 - INFO - Response - Page 1:
2025-05-16 03:00:15,598 - INFO - 第 1 页获取到 100 条记录
2025-05-16 03:00:15,598 - INFO - Request Parameters - Page 2:
2025-05-16 03:00:15,598 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 03:00:15,598 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 03:00:16,098 - INFO - Response - Page 2:
2025-05-16 03:00:16,302 - INFO - 第 2 页获取到 100 条记录
2025-05-16 03:00:16,302 - INFO - Request Parameters - Page 3:
2025-05-16 03:00:16,302 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 03:00:16,302 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 03:00:16,771 - INFO - Response - Page 3:
2025-05-16 03:00:16,974 - INFO - 第 3 页获取到 100 条记录
2025-05-16 03:00:16,974 - INFO - Request Parameters - Page 4:
2025-05-16 03:00:16,974 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 03:00:16,974 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 03:00:17,443 - INFO - Response - Page 4:
2025-05-16 03:00:17,646 - INFO - 第 4 页获取到 100 条记录
2025-05-16 03:00:17,646 - INFO - Request Parameters - Page 5:
2025-05-16 03:00:17,646 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 03:00:17,646 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 03:00:18,115 - INFO - Response - Page 5:
2025-05-16 03:00:18,318 - INFO - 第 5 页获取到 100 条记录
2025-05-16 03:00:18,318 - INFO - Request Parameters - Page 6:
2025-05-16 03:00:18,318 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 03:00:18,318 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 03:00:18,834 - INFO - Response - Page 6:
2025-05-16 03:00:19,037 - INFO - 第 6 页获取到 100 条记录
2025-05-16 03:00:19,037 - INFO - Request Parameters - Page 7:
2025-05-16 03:00:19,037 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 03:00:19,037 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 03:00:19,428 - INFO - Response - Page 7:
2025-05-16 03:00:19,632 - INFO - 第 7 页获取到 61 条记录
2025-05-16 03:00:19,632 - INFO - 查询完成，共获取到 661 条记录
2025-05-16 03:00:19,632 - INFO - 获取到 661 条表单数据
2025-05-16 03:00:19,632 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-16 03:00:19,647 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-16 03:00:19,647 - INFO - 开始处理日期: 2025-04
2025-05-16 03:00:19,647 - INFO - Request Parameters - Page 1:
2025-05-16 03:00:19,647 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 03:00:19,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 03:00:20,288 - INFO - Response - Page 1:
2025-05-16 03:00:20,491 - INFO - 第 1 页获取到 100 条记录
2025-05-16 03:00:20,491 - INFO - Request Parameters - Page 2:
2025-05-16 03:00:20,491 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 03:00:20,491 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 03:00:20,960 - INFO - Response - Page 2:
2025-05-16 03:00:21,164 - INFO - 第 2 页获取到 100 条记录
2025-05-16 03:00:21,164 - INFO - Request Parameters - Page 3:
2025-05-16 03:00:21,164 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 03:00:21,164 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 03:00:21,695 - INFO - Response - Page 3:
2025-05-16 03:00:21,898 - INFO - 第 3 页获取到 100 条记录
2025-05-16 03:00:21,898 - INFO - Request Parameters - Page 4:
2025-05-16 03:00:21,898 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 03:00:21,898 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 03:00:22,383 - INFO - Response - Page 4:
2025-05-16 03:00:22,586 - INFO - 第 4 页获取到 100 条记录
2025-05-16 03:00:22,586 - INFO - Request Parameters - Page 5:
2025-05-16 03:00:22,586 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 03:00:22,586 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 03:00:23,087 - INFO - Response - Page 5:
2025-05-16 03:00:23,290 - INFO - 第 5 页获取到 100 条记录
2025-05-16 03:00:23,290 - INFO - Request Parameters - Page 6:
2025-05-16 03:00:23,290 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 03:00:23,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 03:00:23,712 - INFO - Response - Page 6:
2025-05-16 03:00:23,915 - INFO - 第 6 页获取到 100 条记录
2025-05-16 03:00:23,915 - INFO - Request Parameters - Page 7:
2025-05-16 03:00:23,915 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 03:00:23,915 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 03:00:24,384 - INFO - Response - Page 7:
2025-05-16 03:00:24,587 - INFO - 第 7 页获取到 54 条记录
2025-05-16 03:00:24,587 - INFO - 查询完成，共获取到 654 条记录
2025-05-16 03:00:24,587 - INFO - 获取到 654 条表单数据
2025-05-16 03:00:24,603 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-16 03:00:24,619 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-16 03:00:24,619 - INFO - 开始处理日期: 2025-05
2025-05-16 03:00:24,619 - INFO - Request Parameters - Page 1:
2025-05-16 03:00:24,619 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 03:00:24,619 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 03:00:25,119 - INFO - Response - Page 1:
2025-05-16 03:00:25,322 - INFO - 第 1 页获取到 100 条记录
2025-05-16 03:00:25,322 - INFO - Request Parameters - Page 2:
2025-05-16 03:00:25,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 03:00:25,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 03:00:25,807 - INFO - Response - Page 2:
2025-05-16 03:00:26,010 - INFO - 第 2 页获取到 100 条记录
2025-05-16 03:00:26,010 - INFO - Request Parameters - Page 3:
2025-05-16 03:00:26,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 03:00:26,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 03:00:26,542 - INFO - Response - Page 3:
2025-05-16 03:00:26,745 - INFO - 第 3 页获取到 100 条记录
2025-05-16 03:00:26,745 - INFO - Request Parameters - Page 4:
2025-05-16 03:00:26,745 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 03:00:26,745 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 03:00:27,214 - INFO - Response - Page 4:
2025-05-16 03:00:27,417 - INFO - 第 4 页获取到 100 条记录
2025-05-16 03:00:27,417 - INFO - Request Parameters - Page 5:
2025-05-16 03:00:27,417 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 03:00:27,417 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 03:00:27,933 - INFO - Response - Page 5:
2025-05-16 03:00:28,136 - INFO - 第 5 页获取到 100 条记录
2025-05-16 03:00:28,136 - INFO - Request Parameters - Page 6:
2025-05-16 03:00:28,136 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 03:00:28,136 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 03:00:28,636 - INFO - Response - Page 6:
2025-05-16 03:00:28,840 - INFO - 第 6 页获取到 100 条记录
2025-05-16 03:00:28,840 - INFO - Request Parameters - Page 7:
2025-05-16 03:00:28,840 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 03:00:28,840 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 03:00:29,168 - INFO - Response - Page 7:
2025-05-16 03:00:29,371 - INFO - 第 7 页获取到 25 条记录
2025-05-16 03:00:29,371 - INFO - 查询完成，共获取到 625 条记录
2025-05-16 03:00:29,371 - INFO - 获取到 625 条表单数据
2025-05-16 03:00:29,371 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-16 03:00:29,387 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-16 03:00:29,387 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-16 03:00:29,387 - INFO - =================同步完成====================
2025-05-16 06:00:03,028 - INFO - =================使用默认全量同步=============
2025-05-16 06:00:04,425 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-16 06:00:04,425 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-16 06:00:04,440 - INFO - 开始处理日期: 2025-01
2025-05-16 06:00:04,456 - INFO - Request Parameters - Page 1:
2025-05-16 06:00:04,456 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 06:00:04,456 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 06:00:05,617 - INFO - Response - Page 1:
2025-05-16 06:00:05,821 - INFO - 第 1 页获取到 100 条记录
2025-05-16 06:00:05,821 - INFO - Request Parameters - Page 2:
2025-05-16 06:00:05,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 06:00:05,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 06:00:06,292 - INFO - Response - Page 2:
2025-05-16 06:00:06,496 - INFO - 第 2 页获取到 100 条记录
2025-05-16 06:00:06,496 - INFO - Request Parameters - Page 3:
2025-05-16 06:00:06,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 06:00:06,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 06:00:07,108 - INFO - Response - Page 3:
2025-05-16 06:00:07,312 - INFO - 第 3 页获取到 100 条记录
2025-05-16 06:00:07,312 - INFO - Request Parameters - Page 4:
2025-05-16 06:00:07,312 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 06:00:07,312 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 06:00:07,830 - INFO - Response - Page 4:
2025-05-16 06:00:08,034 - INFO - 第 4 页获取到 100 条记录
2025-05-16 06:00:08,034 - INFO - Request Parameters - Page 5:
2025-05-16 06:00:08,034 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 06:00:08,034 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 06:00:08,489 - INFO - Response - Page 5:
2025-05-16 06:00:08,693 - INFO - 第 5 页获取到 100 条记录
2025-05-16 06:00:08,693 - INFO - Request Parameters - Page 6:
2025-05-16 06:00:08,693 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 06:00:08,693 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 06:00:09,180 - INFO - Response - Page 6:
2025-05-16 06:00:09,384 - INFO - 第 6 页获取到 100 条记录
2025-05-16 06:00:09,384 - INFO - Request Parameters - Page 7:
2025-05-16 06:00:09,384 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 06:00:09,384 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 06:00:09,902 - INFO - Response - Page 7:
2025-05-16 06:00:10,106 - INFO - 第 7 页获取到 82 条记录
2025-05-16 06:00:10,106 - INFO - 查询完成，共获取到 682 条记录
2025-05-16 06:00:10,106 - INFO - 获取到 682 条表单数据
2025-05-16 06:00:10,106 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-16 06:00:10,121 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-16 06:00:10,121 - INFO - 开始处理日期: 2025-02
2025-05-16 06:00:10,121 - INFO - Request Parameters - Page 1:
2025-05-16 06:00:10,121 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 06:00:10,121 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 06:00:10,608 - INFO - Response - Page 1:
2025-05-16 06:00:10,812 - INFO - 第 1 页获取到 100 条记录
2025-05-16 06:00:10,812 - INFO - Request Parameters - Page 2:
2025-05-16 06:00:10,812 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 06:00:10,812 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 06:00:11,283 - INFO - Response - Page 2:
2025-05-16 06:00:11,487 - INFO - 第 2 页获取到 100 条记录
2025-05-16 06:00:11,487 - INFO - Request Parameters - Page 3:
2025-05-16 06:00:11,487 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 06:00:11,487 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 06:00:11,895 - INFO - Response - Page 3:
2025-05-16 06:00:12,099 - INFO - 第 3 页获取到 100 条记录
2025-05-16 06:00:12,099 - INFO - Request Parameters - Page 4:
2025-05-16 06:00:12,099 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 06:00:12,099 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 06:00:12,538 - INFO - Response - Page 4:
2025-05-16 06:00:12,742 - INFO - 第 4 页获取到 100 条记录
2025-05-16 06:00:12,742 - INFO - Request Parameters - Page 5:
2025-05-16 06:00:12,742 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 06:00:12,742 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 06:00:13,228 - INFO - Response - Page 5:
2025-05-16 06:00:13,432 - INFO - 第 5 页获取到 100 条记录
2025-05-16 06:00:13,432 - INFO - Request Parameters - Page 6:
2025-05-16 06:00:13,432 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 06:00:13,432 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 06:00:13,950 - INFO - Response - Page 6:
2025-05-16 06:00:14,154 - INFO - 第 6 页获取到 100 条记录
2025-05-16 06:00:14,154 - INFO - Request Parameters - Page 7:
2025-05-16 06:00:14,154 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 06:00:14,154 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 06:00:14,641 - INFO - Response - Page 7:
2025-05-16 06:00:14,845 - INFO - 第 7 页获取到 70 条记录
2025-05-16 06:00:14,845 - INFO - 查询完成，共获取到 670 条记录
2025-05-16 06:00:14,845 - INFO - 获取到 670 条表单数据
2025-05-16 06:00:14,845 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-16 06:00:14,860 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-16 06:00:14,860 - INFO - 开始处理日期: 2025-03
2025-05-16 06:00:14,860 - INFO - Request Parameters - Page 1:
2025-05-16 06:00:14,860 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 06:00:14,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 06:00:15,347 - INFO - Response - Page 1:
2025-05-16 06:00:15,551 - INFO - 第 1 页获取到 100 条记录
2025-05-16 06:00:15,551 - INFO - Request Parameters - Page 2:
2025-05-16 06:00:15,551 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 06:00:15,551 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 06:00:16,053 - INFO - Response - Page 2:
2025-05-16 06:00:16,257 - INFO - 第 2 页获取到 100 条记录
2025-05-16 06:00:16,257 - INFO - Request Parameters - Page 3:
2025-05-16 06:00:16,257 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 06:00:16,257 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 06:00:16,806 - INFO - Response - Page 3:
2025-05-16 06:00:17,010 - INFO - 第 3 页获取到 100 条记录
2025-05-16 06:00:17,010 - INFO - Request Parameters - Page 4:
2025-05-16 06:00:17,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 06:00:17,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 06:00:17,481 - INFO - Response - Page 4:
2025-05-16 06:00:17,685 - INFO - 第 4 页获取到 100 条记录
2025-05-16 06:00:17,685 - INFO - Request Parameters - Page 5:
2025-05-16 06:00:17,685 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 06:00:17,685 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 06:00:18,124 - INFO - Response - Page 5:
2025-05-16 06:00:18,328 - INFO - 第 5 页获取到 100 条记录
2025-05-16 06:00:18,328 - INFO - Request Parameters - Page 6:
2025-05-16 06:00:18,328 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 06:00:18,328 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 06:00:18,814 - INFO - Response - Page 6:
2025-05-16 06:00:19,018 - INFO - 第 6 页获取到 100 条记录
2025-05-16 06:00:19,018 - INFO - Request Parameters - Page 7:
2025-05-16 06:00:19,018 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 06:00:19,018 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 06:00:19,489 - INFO - Response - Page 7:
2025-05-16 06:00:19,693 - INFO - 第 7 页获取到 61 条记录
2025-05-16 06:00:19,693 - INFO - 查询完成，共获取到 661 条记录
2025-05-16 06:00:19,693 - INFO - 获取到 661 条表单数据
2025-05-16 06:00:19,693 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-16 06:00:19,709 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-16 06:00:19,709 - INFO - 开始处理日期: 2025-04
2025-05-16 06:00:19,709 - INFO - Request Parameters - Page 1:
2025-05-16 06:00:19,709 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 06:00:19,709 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 06:00:20,195 - INFO - Response - Page 1:
2025-05-16 06:00:20,399 - INFO - 第 1 页获取到 100 条记录
2025-05-16 06:00:20,399 - INFO - Request Parameters - Page 2:
2025-05-16 06:00:20,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 06:00:20,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 06:00:20,886 - INFO - Response - Page 2:
2025-05-16 06:00:21,090 - INFO - 第 2 页获取到 100 条记录
2025-05-16 06:00:21,090 - INFO - Request Parameters - Page 3:
2025-05-16 06:00:21,090 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 06:00:21,090 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 06:00:21,639 - INFO - Response - Page 3:
2025-05-16 06:00:21,843 - INFO - 第 3 页获取到 100 条记录
2025-05-16 06:00:21,843 - INFO - Request Parameters - Page 4:
2025-05-16 06:00:21,843 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 06:00:21,843 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 06:00:22,376 - INFO - Response - Page 4:
2025-05-16 06:00:22,580 - INFO - 第 4 页获取到 100 条记录
2025-05-16 06:00:22,580 - INFO - Request Parameters - Page 5:
2025-05-16 06:00:22,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 06:00:22,580 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 06:00:23,114 - INFO - Response - Page 5:
2025-05-16 06:00:23,318 - INFO - 第 5 页获取到 100 条记录
2025-05-16 06:00:23,318 - INFO - Request Parameters - Page 6:
2025-05-16 06:00:23,318 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 06:00:23,318 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 06:00:23,804 - INFO - Response - Page 6:
2025-05-16 06:00:24,008 - INFO - 第 6 页获取到 100 条记录
2025-05-16 06:00:24,008 - INFO - Request Parameters - Page 7:
2025-05-16 06:00:24,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 06:00:24,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 06:00:24,369 - INFO - Response - Page 7:
2025-05-16 06:00:24,573 - INFO - 第 7 页获取到 54 条记录
2025-05-16 06:00:24,573 - INFO - 查询完成，共获取到 654 条记录
2025-05-16 06:00:24,573 - INFO - 获取到 654 条表单数据
2025-05-16 06:00:24,573 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-16 06:00:24,588 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-16 06:00:24,588 - INFO - 开始处理日期: 2025-05
2025-05-16 06:00:24,588 - INFO - Request Parameters - Page 1:
2025-05-16 06:00:24,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 06:00:24,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 06:00:25,059 - INFO - Response - Page 1:
2025-05-16 06:00:25,263 - INFO - 第 1 页获取到 100 条记录
2025-05-16 06:00:25,263 - INFO - Request Parameters - Page 2:
2025-05-16 06:00:25,263 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 06:00:25,263 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 06:00:25,749 - INFO - Response - Page 2:
2025-05-16 06:00:25,953 - INFO - 第 2 页获取到 100 条记录
2025-05-16 06:00:25,953 - INFO - Request Parameters - Page 3:
2025-05-16 06:00:25,953 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 06:00:25,953 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 06:00:26,487 - INFO - Response - Page 3:
2025-05-16 06:00:26,691 - INFO - 第 3 页获取到 100 条记录
2025-05-16 06:00:26,691 - INFO - Request Parameters - Page 4:
2025-05-16 06:00:26,691 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 06:00:26,691 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 06:00:27,146 - INFO - Response - Page 4:
2025-05-16 06:00:27,350 - INFO - 第 4 页获取到 100 条记录
2025-05-16 06:00:27,350 - INFO - Request Parameters - Page 5:
2025-05-16 06:00:27,350 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 06:00:27,350 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 06:00:27,930 - INFO - Response - Page 5:
2025-05-16 06:00:28,134 - INFO - 第 5 页获取到 100 条记录
2025-05-16 06:00:28,134 - INFO - Request Parameters - Page 6:
2025-05-16 06:00:28,134 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 06:00:28,134 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 06:00:28,636 - INFO - Response - Page 6:
2025-05-16 06:00:28,840 - INFO - 第 6 页获取到 100 条记录
2025-05-16 06:00:28,840 - INFO - Request Parameters - Page 7:
2025-05-16 06:00:28,840 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 06:00:28,840 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 06:00:29,154 - INFO - Response - Page 7:
2025-05-16 06:00:29,358 - INFO - 第 7 页获取到 25 条记录
2025-05-16 06:00:29,358 - INFO - 查询完成，共获取到 625 条记录
2025-05-16 06:00:29,358 - INFO - 获取到 625 条表单数据
2025-05-16 06:00:29,358 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-16 06:00:29,373 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-16 06:00:29,766 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-16 06:00:29,766 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47945.0, 'new_value': 50518.0}, {'field': 'total_amount', 'old_value': 49795.0, 'new_value': 52368.0}, {'field': 'order_count', 'old_value': 290, 'new_value': 305}]
2025-05-16 06:00:29,766 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-16 06:00:29,766 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-05-16 06:00:29,766 - INFO - =================同步完成====================
2025-05-16 09:00:01,879 - INFO - =================使用默认全量同步=============
2025-05-16 09:00:03,238 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-16 09:00:03,238 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-16 09:00:03,269 - INFO - 开始处理日期: 2025-01
2025-05-16 09:00:03,269 - INFO - Request Parameters - Page 1:
2025-05-16 09:00:03,269 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 09:00:03,269 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 09:00:04,176 - INFO - Response - Page 1:
2025-05-16 09:00:04,379 - INFO - 第 1 页获取到 100 条记录
2025-05-16 09:00:04,379 - INFO - Request Parameters - Page 2:
2025-05-16 09:00:04,379 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 09:00:04,379 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 09:00:04,863 - INFO - Response - Page 2:
2025-05-16 09:00:05,066 - INFO - 第 2 页获取到 100 条记录
2025-05-16 09:00:05,066 - INFO - Request Parameters - Page 3:
2025-05-16 09:00:05,066 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 09:00:05,066 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 09:00:05,660 - INFO - Response - Page 3:
2025-05-16 09:00:05,863 - INFO - 第 3 页获取到 100 条记录
2025-05-16 09:00:05,863 - INFO - Request Parameters - Page 4:
2025-05-16 09:00:05,863 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 09:00:05,863 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 09:00:06,754 - INFO - Response - Page 4:
2025-05-16 09:00:06,957 - INFO - 第 4 页获取到 100 条记录
2025-05-16 09:00:06,957 - INFO - Request Parameters - Page 5:
2025-05-16 09:00:06,957 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 09:00:06,957 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 09:00:07,535 - INFO - Response - Page 5:
2025-05-16 09:00:07,738 - INFO - 第 5 页获取到 100 条记录
2025-05-16 09:00:07,738 - INFO - Request Parameters - Page 6:
2025-05-16 09:00:07,738 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 09:00:07,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 09:00:08,269 - INFO - Response - Page 6:
2025-05-16 09:00:08,472 - INFO - 第 6 页获取到 100 条记录
2025-05-16 09:00:08,472 - INFO - Request Parameters - Page 7:
2025-05-16 09:00:08,472 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 09:00:08,472 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 09:00:08,988 - INFO - Response - Page 7:
2025-05-16 09:00:09,191 - INFO - 第 7 页获取到 82 条记录
2025-05-16 09:00:09,191 - INFO - 查询完成，共获取到 682 条记录
2025-05-16 09:00:09,191 - INFO - 获取到 682 条表单数据
2025-05-16 09:00:09,191 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-16 09:00:09,207 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-16 09:00:09,207 - INFO - 开始处理日期: 2025-02
2025-05-16 09:00:09,207 - INFO - Request Parameters - Page 1:
2025-05-16 09:00:09,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 09:00:09,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 09:00:09,769 - INFO - Response - Page 1:
2025-05-16 09:00:09,972 - INFO - 第 1 页获取到 100 条记录
2025-05-16 09:00:09,972 - INFO - Request Parameters - Page 2:
2025-05-16 09:00:09,972 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 09:00:09,972 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 09:00:10,457 - INFO - Response - Page 2:
2025-05-16 09:00:10,660 - INFO - 第 2 页获取到 100 条记录
2025-05-16 09:00:10,660 - INFO - Request Parameters - Page 3:
2025-05-16 09:00:10,660 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 09:00:10,660 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 09:00:11,207 - INFO - Response - Page 3:
2025-05-16 09:00:11,410 - INFO - 第 3 页获取到 100 条记录
2025-05-16 09:00:11,410 - INFO - Request Parameters - Page 4:
2025-05-16 09:00:11,410 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 09:00:11,410 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 09:00:12,004 - INFO - Response - Page 4:
2025-05-16 09:00:12,207 - INFO - 第 4 页获取到 100 条记录
2025-05-16 09:00:12,207 - INFO - Request Parameters - Page 5:
2025-05-16 09:00:12,207 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 09:00:12,207 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 09:00:12,754 - INFO - Response - Page 5:
2025-05-16 09:00:12,957 - INFO - 第 5 页获取到 100 条记录
2025-05-16 09:00:12,957 - INFO - Request Parameters - Page 6:
2025-05-16 09:00:12,957 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 09:00:12,957 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 09:00:13,426 - INFO - Response - Page 6:
2025-05-16 09:00:13,629 - INFO - 第 6 页获取到 100 条记录
2025-05-16 09:00:13,629 - INFO - Request Parameters - Page 7:
2025-05-16 09:00:13,629 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 09:00:13,629 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 09:00:14,051 - INFO - Response - Page 7:
2025-05-16 09:00:14,254 - INFO - 第 7 页获取到 70 条记录
2025-05-16 09:00:14,254 - INFO - 查询完成，共获取到 670 条记录
2025-05-16 09:00:14,254 - INFO - 获取到 670 条表单数据
2025-05-16 09:00:14,269 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-16 09:00:14,269 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-16 09:00:14,269 - INFO - 开始处理日期: 2025-03
2025-05-16 09:00:14,269 - INFO - Request Parameters - Page 1:
2025-05-16 09:00:14,269 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 09:00:14,269 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 09:00:14,785 - INFO - Response - Page 1:
2025-05-16 09:00:14,988 - INFO - 第 1 页获取到 100 条记录
2025-05-16 09:00:14,988 - INFO - Request Parameters - Page 2:
2025-05-16 09:00:14,988 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 09:00:14,988 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 09:00:15,551 - INFO - Response - Page 2:
2025-05-16 09:00:15,754 - INFO - 第 2 页获取到 100 条记录
2025-05-16 09:00:15,754 - INFO - Request Parameters - Page 3:
2025-05-16 09:00:15,754 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 09:00:15,754 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 09:00:16,285 - INFO - Response - Page 3:
2025-05-16 09:00:16,488 - INFO - 第 3 页获取到 100 条记录
2025-05-16 09:00:16,488 - INFO - Request Parameters - Page 4:
2025-05-16 09:00:16,488 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 09:00:16,488 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 09:00:16,957 - INFO - Response - Page 4:
2025-05-16 09:00:17,160 - INFO - 第 4 页获取到 100 条记录
2025-05-16 09:00:17,160 - INFO - Request Parameters - Page 5:
2025-05-16 09:00:17,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 09:00:17,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 09:00:17,629 - INFO - Response - Page 5:
2025-05-16 09:00:17,832 - INFO - 第 5 页获取到 100 条记录
2025-05-16 09:00:17,832 - INFO - Request Parameters - Page 6:
2025-05-16 09:00:17,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 09:00:17,832 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 09:00:18,379 - INFO - Response - Page 6:
2025-05-16 09:00:18,582 - INFO - 第 6 页获取到 100 条记录
2025-05-16 09:00:18,582 - INFO - Request Parameters - Page 7:
2025-05-16 09:00:18,582 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 09:00:18,582 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 09:00:19,019 - INFO - Response - Page 7:
2025-05-16 09:00:19,222 - INFO - 第 7 页获取到 61 条记录
2025-05-16 09:00:19,222 - INFO - 查询完成，共获取到 661 条记录
2025-05-16 09:00:19,222 - INFO - 获取到 661 条表单数据
2025-05-16 09:00:19,222 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-16 09:00:19,238 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-16 09:00:19,238 - INFO - 开始处理日期: 2025-04
2025-05-16 09:00:19,238 - INFO - Request Parameters - Page 1:
2025-05-16 09:00:19,238 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 09:00:19,238 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 09:00:19,754 - INFO - Response - Page 1:
2025-05-16 09:00:19,957 - INFO - 第 1 页获取到 100 条记录
2025-05-16 09:00:19,957 - INFO - Request Parameters - Page 2:
2025-05-16 09:00:19,957 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 09:00:19,957 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 09:00:20,504 - INFO - Response - Page 2:
2025-05-16 09:00:20,707 - INFO - 第 2 页获取到 100 条记录
2025-05-16 09:00:20,707 - INFO - Request Parameters - Page 3:
2025-05-16 09:00:20,707 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 09:00:20,707 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 09:00:21,222 - INFO - Response - Page 3:
2025-05-16 09:00:21,425 - INFO - 第 3 页获取到 100 条记录
2025-05-16 09:00:21,425 - INFO - Request Parameters - Page 4:
2025-05-16 09:00:21,425 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 09:00:21,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 09:00:21,926 - INFO - Response - Page 4:
2025-05-16 09:00:22,129 - INFO - 第 4 页获取到 100 条记录
2025-05-16 09:00:22,129 - INFO - Request Parameters - Page 5:
2025-05-16 09:00:22,129 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 09:00:22,129 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 09:00:22,629 - INFO - Response - Page 5:
2025-05-16 09:00:22,832 - INFO - 第 5 页获取到 100 条记录
2025-05-16 09:00:22,832 - INFO - Request Parameters - Page 6:
2025-05-16 09:00:22,832 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 09:00:22,832 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 09:00:23,332 - INFO - Response - Page 6:
2025-05-16 09:00:23,535 - INFO - 第 6 页获取到 100 条记录
2025-05-16 09:00:23,535 - INFO - Request Parameters - Page 7:
2025-05-16 09:00:23,535 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 09:00:23,535 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 09:00:24,035 - INFO - Response - Page 7:
2025-05-16 09:00:24,238 - INFO - 第 7 页获取到 54 条记录
2025-05-16 09:00:24,238 - INFO - 查询完成，共获取到 654 条记录
2025-05-16 09:00:24,238 - INFO - 获取到 654 条表单数据
2025-05-16 09:00:24,238 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-16 09:00:24,254 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-16 09:00:24,254 - INFO - 开始处理日期: 2025-05
2025-05-16 09:00:24,254 - INFO - Request Parameters - Page 1:
2025-05-16 09:00:24,254 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 09:00:24,254 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 09:00:24,816 - INFO - Response - Page 1:
2025-05-16 09:00:25,019 - INFO - 第 1 页获取到 100 条记录
2025-05-16 09:00:25,019 - INFO - Request Parameters - Page 2:
2025-05-16 09:00:25,019 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 09:00:25,019 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 09:00:25,504 - INFO - Response - Page 2:
2025-05-16 09:00:25,707 - INFO - 第 2 页获取到 100 条记录
2025-05-16 09:00:25,707 - INFO - Request Parameters - Page 3:
2025-05-16 09:00:25,707 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 09:00:25,707 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 09:00:26,254 - INFO - Response - Page 3:
2025-05-16 09:00:26,457 - INFO - 第 3 页获取到 100 条记录
2025-05-16 09:00:26,457 - INFO - Request Parameters - Page 4:
2025-05-16 09:00:26,457 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 09:00:26,457 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 09:00:26,925 - INFO - Response - Page 4:
2025-05-16 09:00:27,129 - INFO - 第 4 页获取到 100 条记录
2025-05-16 09:00:27,129 - INFO - Request Parameters - Page 5:
2025-05-16 09:00:27,129 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 09:00:27,129 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 09:00:27,769 - INFO - Response - Page 5:
2025-05-16 09:00:27,972 - INFO - 第 5 页获取到 100 条记录
2025-05-16 09:00:27,972 - INFO - Request Parameters - Page 6:
2025-05-16 09:00:27,972 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 09:00:27,972 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 09:00:28,644 - INFO - Response - Page 6:
2025-05-16 09:00:28,847 - INFO - 第 6 页获取到 100 条记录
2025-05-16 09:00:28,847 - INFO - Request Parameters - Page 7:
2025-05-16 09:00:28,847 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 09:00:28,847 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 09:00:29,176 - INFO - Response - Page 7:
2025-05-16 09:00:29,379 - INFO - 第 7 页获取到 25 条记录
2025-05-16 09:00:29,379 - INFO - 查询完成，共获取到 625 条记录
2025-05-16 09:00:29,379 - INFO - 获取到 625 条表单数据
2025-05-16 09:00:29,379 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-16 09:00:29,379 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2O
2025-05-16 09:00:29,847 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2O
2025-05-16 09:00:29,847 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 155600.0, 'new_value': 159800.0}, {'field': 'total_amount', 'old_value': 155600.0, 'new_value': 159800.0}]
2025-05-16 09:00:29,847 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-16 09:00:30,285 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-16 09:00:30,285 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1305.0, 'new_value': 1435.0}, {'field': 'offline_amount', 'old_value': 25510.0, 'new_value': 26150.0}, {'field': 'total_amount', 'old_value': 26815.0, 'new_value': 27585.0}, {'field': 'order_count', 'old_value': 340, 'new_value': 353}]
2025-05-16 09:00:30,285 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-16 09:00:30,769 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-16 09:00:30,769 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 202395.0, 'new_value': 223083.0}, {'field': 'total_amount', 'old_value': 202395.0, 'new_value': 223083.0}, {'field': 'order_count', 'old_value': 146, 'new_value': 161}]
2025-05-16 09:00:30,769 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-16 09:00:31,238 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-16 09:00:31,238 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 427618.98, 'new_value': 452097.98}, {'field': 'total_amount', 'old_value': 427618.98, 'new_value': 452097.98}, {'field': 'order_count', 'old_value': 1264, 'new_value': 1340}]
2025-05-16 09:00:31,238 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-16 09:00:31,675 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-16 09:00:31,675 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 172200.0, 'new_value': 187050.0}, {'field': 'total_amount', 'old_value': 172200.0, 'new_value': 187050.0}, {'field': 'order_count', 'old_value': 94, 'new_value': 104}]
2025-05-16 09:00:31,675 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMEC
2025-05-16 09:00:32,097 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMEC
2025-05-16 09:00:32,097 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81421.0, 'new_value': 83421.0}, {'field': 'total_amount', 'old_value': 89620.0, 'new_value': 91620.0}, {'field': 'order_count', 'old_value': 35, 'new_value': 36}]
2025-05-16 09:00:32,097 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-16 09:00:32,644 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-16 09:00:32,644 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57514.0, 'new_value': 60151.0}, {'field': 'total_amount', 'old_value': 57514.0, 'new_value': 60151.0}, {'field': 'order_count', 'old_value': 66, 'new_value': 69}]
2025-05-16 09:00:32,644 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-16 09:00:33,082 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-16 09:00:33,082 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25080.0, 'new_value': 28840.0}, {'field': 'total_amount', 'old_value': 29200.0, 'new_value': 32960.0}, {'field': 'order_count', 'old_value': 286, 'new_value': 316}]
2025-05-16 09:00:33,082 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMKC
2025-05-16 09:00:33,519 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMKC
2025-05-16 09:00:33,519 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24783.0, 'new_value': 27663.0}, {'field': 'total_amount', 'old_value': 24783.0, 'new_value': 27663.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-16 09:00:33,519 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMOC
2025-05-16 09:00:34,113 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMOC
2025-05-16 09:00:34,113 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1769300.0, 'new_value': 2004200.0}, {'field': 'total_amount', 'old_value': 1769300.0, 'new_value': 2004200.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-16 09:00:34,113 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-16 09:00:34,550 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-16 09:00:34,550 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97889.0, 'new_value': 97892.0}, {'field': 'total_amount', 'old_value': 97889.0, 'new_value': 97892.0}, {'field': 'order_count', 'old_value': 32, 'new_value': 11912}]
2025-05-16 09:00:34,550 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-16 09:00:35,004 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-16 09:00:35,004 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32482.39, 'new_value': 34863.89}, {'field': 'offline_amount', 'old_value': 64971.0, 'new_value': 69734.0}, {'field': 'total_amount', 'old_value': 97453.39, 'new_value': 104597.89}, {'field': 'order_count', 'old_value': 1186, 'new_value': 1267}]
2025-05-16 09:00:35,004 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-16 09:00:35,425 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-16 09:00:35,425 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13435.8, 'new_value': 14019.66}, {'field': 'offline_amount', 'old_value': 15549.81, 'new_value': 15954.51}, {'field': 'total_amount', 'old_value': 28985.61, 'new_value': 29974.17}, {'field': 'order_count', 'old_value': 1389, 'new_value': 1445}]
2025-05-16 09:00:35,425 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMSC
2025-05-16 09:00:35,847 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMSC
2025-05-16 09:00:35,847 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35360.0, 'new_value': 35860.0}, {'field': 'total_amount', 'old_value': 35360.0, 'new_value': 35860.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-05-16 09:00:35,847 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-16 09:00:36,332 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-16 09:00:36,332 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 196525.5, 'new_value': 199117.9}, {'field': 'total_amount', 'old_value': 311545.2, 'new_value': 314137.6}, {'field': 'order_count', 'old_value': 2074, 'new_value': 2167}]
2025-05-16 09:00:36,332 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-16 09:00:36,785 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-16 09:00:36,785 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57809.0, 'new_value': 62380.8}, {'field': 'total_amount', 'old_value': 57809.0, 'new_value': 62380.8}, {'field': 'order_count', 'old_value': 3101, 'new_value': 3388}]
2025-05-16 09:00:36,785 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-16 09:00:37,222 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-16 09:00:37,222 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81066.41, 'new_value': 84704.41}, {'field': 'total_amount', 'old_value': 81066.41, 'new_value': 84704.41}, {'field': 'order_count', 'old_value': 909, 'new_value': 955}]
2025-05-16 09:00:37,222 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-16 09:00:37,566 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-16 09:00:37,566 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5194.0, 'new_value': 5253.9}, {'field': 'offline_amount', 'old_value': 59982.7, 'new_value': 60256.7}, {'field': 'total_amount', 'old_value': 65176.7, 'new_value': 65510.6}, {'field': 'order_count', 'old_value': 1322, 'new_value': 1328}]
2025-05-16 09:00:37,566 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-16 09:00:38,004 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-16 09:00:38,004 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 454999.1, 'new_value': 488128.2}, {'field': 'total_amount', 'old_value': 456256.5, 'new_value': 489385.6}, {'field': 'order_count', 'old_value': 52, 'new_value': 54}]
2025-05-16 09:00:38,004 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-16 09:00:38,472 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-16 09:00:38,472 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31077.6, 'new_value': 31195.6}, {'field': 'total_amount', 'old_value': 45708.3, 'new_value': 45826.3}, {'field': 'order_count', 'old_value': 567, 'new_value': 568}]
2025-05-16 09:00:38,472 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-16 09:00:39,035 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-16 09:00:39,035 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4321.0, 'new_value': 4449.0}, {'field': 'offline_amount', 'old_value': 11310.0, 'new_value': 11409.0}, {'field': 'total_amount', 'old_value': 15631.0, 'new_value': 15858.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 47}]
2025-05-16 09:00:39,035 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-16 09:00:39,472 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-16 09:00:39,472 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120338.0, 'new_value': 122254.0}, {'field': 'total_amount', 'old_value': 120338.0, 'new_value': 122254.0}, {'field': 'order_count', 'old_value': 213, 'new_value': 219}]
2025-05-16 09:00:39,472 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-16 09:00:39,910 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-16 09:00:39,910 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 108046.56, 'new_value': 112558.38}, {'field': 'offline_amount', 'old_value': 18688.82, 'new_value': 19347.16}, {'field': 'total_amount', 'old_value': 126735.38, 'new_value': 131905.54}, {'field': 'order_count', 'old_value': 451, 'new_value': 472}]
2025-05-16 09:00:39,910 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-16 09:00:40,316 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-16 09:00:40,316 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 109237.0, 'new_value': 112282.0}, {'field': 'offline_amount', 'old_value': 38424.56, 'new_value': 39055.56}, {'field': 'total_amount', 'old_value': 147661.56, 'new_value': 151337.56}, {'field': 'order_count', 'old_value': 894, 'new_value': 918}]
2025-05-16 09:00:40,316 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-16 09:00:40,816 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-16 09:00:40,816 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4310.0, 'new_value': 4556.0}, {'field': 'total_amount', 'old_value': 5428.0, 'new_value': 5674.0}, {'field': 'order_count', 'old_value': 105, 'new_value': 110}]
2025-05-16 09:00:40,816 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-16 09:00:41,254 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-16 09:00:41,254 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50992.47, 'new_value': 55113.43}, {'field': 'total_amount', 'old_value': 50992.47, 'new_value': 55113.43}, {'field': 'order_count', 'old_value': 1344, 'new_value': 1468}]
2025-05-16 09:00:41,254 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-16 09:00:41,675 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-16 09:00:41,675 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 87158.0, 'new_value': 90001.0}, {'field': 'offline_amount', 'old_value': 39004.22, 'new_value': 41575.22}, {'field': 'total_amount', 'old_value': 126162.22, 'new_value': 131576.22}, {'field': 'order_count', 'old_value': 889, 'new_value': 935}]
2025-05-16 09:00:41,675 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-16 09:00:42,082 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-16 09:00:42,082 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6527.37, 'new_value': 6893.12}, {'field': 'offline_amount', 'old_value': 103413.67, 'new_value': 109241.47}, {'field': 'total_amount', 'old_value': 109941.04, 'new_value': 116134.59}, {'field': 'order_count', 'old_value': 1196, 'new_value': 1266}]
2025-05-16 09:00:42,082 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-16 09:00:42,488 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-16 09:00:42,488 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79468.0, 'new_value': 86512.0}, {'field': 'total_amount', 'old_value': 79468.0, 'new_value': 86512.0}, {'field': 'order_count', 'old_value': 1924, 'new_value': 2124}]
2025-05-16 09:00:42,488 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXD
2025-05-16 09:00:42,878 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXD
2025-05-16 09:00:42,878 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8209.65, 'new_value': 8285.65}, {'field': 'total_amount', 'old_value': 8209.65, 'new_value': 8285.65}, {'field': 'order_count', 'old_value': 135, 'new_value': 138}]
2025-05-16 09:00:42,878 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-16 09:00:43,347 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-16 09:00:43,347 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14043.51, 'new_value': 14585.78}, {'field': 'total_amount', 'old_value': 14043.51, 'new_value': 14585.78}, {'field': 'order_count', 'old_value': 64, 'new_value': 70}]
2025-05-16 09:00:43,347 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-16 09:00:43,816 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-16 09:00:43,816 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90767.0, 'new_value': 98772.0}, {'field': 'total_amount', 'old_value': 90767.0, 'new_value': 98772.0}, {'field': 'order_count', 'old_value': 3347, 'new_value': 3668}]
2025-05-16 09:00:43,816 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-16 09:00:44,285 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-16 09:00:44,285 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 409755.7, 'new_value': 430084.98}, {'field': 'total_amount', 'old_value': 409755.7, 'new_value': 430084.98}, {'field': 'order_count', 'old_value': 2970, 'new_value': 3161}]
2025-05-16 09:00:44,285 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-16 09:00:44,738 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-16 09:00:44,738 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 289866.0, 'new_value': 301552.0}, {'field': 'total_amount', 'old_value': 305424.0, 'new_value': 317110.0}, {'field': 'order_count', 'old_value': 52, 'new_value': 54}]
2025-05-16 09:00:44,738 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-16 09:00:45,175 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-16 09:00:45,175 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76588.86, 'new_value': 81997.41}, {'field': 'total_amount', 'old_value': 76588.86, 'new_value': 81997.41}, {'field': 'order_count', 'old_value': 2807, 'new_value': 2983}]
2025-05-16 09:00:45,175 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-05-16 09:00:45,613 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-05-16 09:00:45,613 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13390.28, 'new_value': 14158.28}, {'field': 'total_amount', 'old_value': 13390.28, 'new_value': 14158.28}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-05-16 09:00:45,613 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-16 09:00:46,128 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-16 09:00:46,128 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 219223.0, 'new_value': 223181.0}, {'field': 'total_amount', 'old_value': 219223.0, 'new_value': 223181.0}, {'field': 'order_count', 'old_value': 101, 'new_value': 108}]
2025-05-16 09:00:46,128 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-16 09:00:46,597 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-16 09:00:46,597 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47165.0, 'new_value': 48371.0}, {'field': 'total_amount', 'old_value': 47165.0, 'new_value': 48371.0}, {'field': 'order_count', 'old_value': 80, 'new_value': 84}]
2025-05-16 09:00:46,597 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-16 09:00:47,160 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-16 09:00:47,160 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41475.28, 'new_value': 44487.83}, {'field': 'offline_amount', 'old_value': 243760.3, 'new_value': 261521.3}, {'field': 'total_amount', 'old_value': 285235.58, 'new_value': 306009.13}, {'field': 'order_count', 'old_value': 458, 'new_value': 491}]
2025-05-16 09:00:47,160 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-16 09:00:47,597 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-16 09:00:47,597 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97760.42, 'new_value': 103524.39}, {'field': 'total_amount', 'old_value': 97760.42, 'new_value': 103524.39}, {'field': 'order_count', 'old_value': 523, 'new_value': 579}]
2025-05-16 09:00:47,597 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-16 09:00:48,050 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-16 09:00:48,050 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17599.55, 'new_value': 17645.15}, {'field': 'total_amount', 'old_value': 17665.1, 'new_value': 17710.7}, {'field': 'order_count', 'old_value': 174, 'new_value': 175}]
2025-05-16 09:00:48,050 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-05-16 09:00:48,566 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-05-16 09:00:48,566 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90882.0, 'new_value': 108880.0}, {'field': 'total_amount', 'old_value': 90882.0, 'new_value': 108880.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 26}]
2025-05-16 09:00:48,566 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-16 09:00:49,035 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-16 09:00:49,035 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1060487.1, 'new_value': 1100971.1}, {'field': 'total_amount', 'old_value': 1113932.2, 'new_value': 1154416.2}, {'field': 'order_count', 'old_value': 1908, 'new_value': 1994}]
2025-05-16 09:00:49,035 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-16 09:00:49,519 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-16 09:00:49,519 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25272.9, 'new_value': 26249.9}, {'field': 'total_amount', 'old_value': 25272.9, 'new_value': 26249.9}, {'field': 'order_count', 'old_value': 111, 'new_value': 114}]
2025-05-16 09:00:49,519 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-16 09:00:49,972 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-16 09:00:49,972 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29654.0, 'new_value': 31772.0}, {'field': 'total_amount', 'old_value': 58300.0, 'new_value': 60418.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 31}]
2025-05-16 09:00:49,972 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-16 09:00:50,378 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-16 09:00:50,378 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13695.0, 'new_value': 13869.0}, {'field': 'total_amount', 'old_value': 13695.0, 'new_value': 13869.0}, {'field': 'order_count', 'old_value': 236, 'new_value': 239}]
2025-05-16 09:00:50,378 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-16 09:00:50,847 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-16 09:00:50,847 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3705.86, 'new_value': 3976.05}, {'field': 'offline_amount', 'old_value': 9129.53, 'new_value': 9714.52}, {'field': 'total_amount', 'old_value': 12835.39, 'new_value': 13690.57}, {'field': 'order_count', 'old_value': 451, 'new_value': 478}]
2025-05-16 09:00:50,847 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-16 09:00:51,363 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-16 09:00:51,363 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 97219.38, 'new_value': 104036.78}, {'field': 'offline_amount', 'old_value': 80027.26, 'new_value': 85695.58}, {'field': 'total_amount', 'old_value': 177246.64, 'new_value': 189732.36}, {'field': 'order_count', 'old_value': 1516, 'new_value': 1648}]
2025-05-16 09:00:51,363 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-16 09:00:51,785 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-16 09:00:51,785 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39111.71, 'new_value': 42751.97}, {'field': 'total_amount', 'old_value': 39115.01, 'new_value': 42755.27}, {'field': 'order_count', 'old_value': 27, 'new_value': 29}]
2025-05-16 09:00:51,785 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-16 09:00:52,269 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-16 09:00:52,269 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 206071.9, 'new_value': 217093.9}, {'field': 'offline_amount', 'old_value': 50314.0, 'new_value': 50634.0}, {'field': 'total_amount', 'old_value': 256385.9, 'new_value': 267727.9}, {'field': 'order_count', 'old_value': 314, 'new_value': 330}]
2025-05-16 09:00:52,269 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-16 09:00:52,769 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-16 09:00:52,769 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19076.0, 'new_value': 19285.0}, {'field': 'total_amount', 'old_value': 19076.0, 'new_value': 19285.0}, {'field': 'order_count', 'old_value': 53, 'new_value': 54}]
2025-05-16 09:00:52,769 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-16 09:00:53,191 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-16 09:00:53,191 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49707.0, 'new_value': 52766.0}, {'field': 'total_amount', 'old_value': 49707.0, 'new_value': 52766.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 37}]
2025-05-16 09:00:53,191 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-16 09:00:53,628 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-16 09:00:53,628 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57553.19, 'new_value': 57653.19}, {'field': 'total_amount', 'old_value': 61322.29, 'new_value': 61422.29}, {'field': 'order_count', 'old_value': 316, 'new_value': 317}]
2025-05-16 09:00:53,628 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-16 09:00:54,050 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-16 09:00:54,050 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102984.0, 'new_value': 112802.0}, {'field': 'total_amount', 'old_value': 133141.0, 'new_value': 142959.0}, {'field': 'order_count', 'old_value': 2818, 'new_value': 3050}]
2025-05-16 09:00:54,050 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-16 09:00:54,503 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-16 09:00:54,503 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8554.58, 'new_value': 9212.31}, {'field': 'offline_amount', 'old_value': 156757.3, 'new_value': 163874.6}, {'field': 'total_amount', 'old_value': 165311.88, 'new_value': 173086.91}, {'field': 'order_count', 'old_value': 1173, 'new_value': 1232}]
2025-05-16 09:00:54,503 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-16 09:00:54,957 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-16 09:00:54,957 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 432423.04, 'new_value': 432463.04}, {'field': 'offline_amount', 'old_value': 143644.9, 'new_value': 144193.9}, {'field': 'total_amount', 'old_value': 576067.94, 'new_value': 576656.94}, {'field': 'order_count', 'old_value': 5440, 'new_value': 5451}]
2025-05-16 09:00:54,957 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-16 09:00:55,378 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-16 09:00:55,378 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25582.6, 'new_value': 25871.6}, {'field': 'total_amount', 'old_value': 26114.6, 'new_value': 26403.6}, {'field': 'order_count', 'old_value': 110, 'new_value': 113}]
2025-05-16 09:00:55,378 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-16 09:00:55,800 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-16 09:00:55,800 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48641.2, 'new_value': 50251.5}, {'field': 'total_amount', 'old_value': 51278.75, 'new_value': 52889.05}, {'field': 'order_count', 'old_value': 136, 'new_value': 145}]
2025-05-16 09:00:55,800 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-16 09:00:56,316 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-16 09:00:56,316 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43859.87, 'new_value': 45863.47}, {'field': 'total_amount', 'old_value': 43859.87, 'new_value': 45863.47}, {'field': 'order_count', 'old_value': 1199, 'new_value': 1252}]
2025-05-16 09:00:56,316 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-16 09:00:56,785 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-16 09:00:56,785 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 70894.18, 'new_value': 76194.3}, {'field': 'offline_amount', 'old_value': 245636.98, 'new_value': 259907.41}, {'field': 'total_amount', 'old_value': 316531.16, 'new_value': 336101.71}, {'field': 'order_count', 'old_value': 1843, 'new_value': 2024}]
2025-05-16 09:00:56,785 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-16 09:00:57,269 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-16 09:00:57,269 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 241518.42, 'new_value': 244842.36}, {'field': 'total_amount', 'old_value': 241518.42, 'new_value': 244842.36}, {'field': 'order_count', 'old_value': 359, 'new_value': 374}]
2025-05-16 09:00:57,269 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-16 09:00:57,707 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-16 09:00:57,707 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8691.96, 'new_value': 9401.03}, {'field': 'offline_amount', 'old_value': 255009.27, 'new_value': 264843.85}, {'field': 'total_amount', 'old_value': 263701.23, 'new_value': 274244.88}, {'field': 'order_count', 'old_value': 1044, 'new_value': 1092}]
2025-05-16 09:00:57,707 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-16 09:00:58,160 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-16 09:00:58,160 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47525.0, 'new_value': 51717.0}, {'field': 'offline_amount', 'old_value': 49388.46, 'new_value': 52123.46}, {'field': 'total_amount', 'old_value': 96913.46, 'new_value': 103840.46}, {'field': 'order_count', 'old_value': 115, 'new_value': 123}]
2025-05-16 09:00:58,160 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-16 09:00:58,613 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-16 09:00:58,613 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98060.8, 'new_value': 100294.1}, {'field': 'total_amount', 'old_value': 98060.8, 'new_value': 100294.1}, {'field': 'order_count', 'old_value': 219, 'new_value': 226}]
2025-05-16 09:00:58,613 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-16 09:00:59,082 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-16 09:00:59,082 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 129527.15, 'new_value': 131063.35}, {'field': 'offline_amount', 'old_value': 74595.28, 'new_value': 81007.61}, {'field': 'total_amount', 'old_value': 204122.43, 'new_value': 212070.96}, {'field': 'order_count', 'old_value': 774, 'new_value': 808}]
2025-05-16 09:00:59,082 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-16 09:00:59,503 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-16 09:00:59,503 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12384.01, 'new_value': 12824.61}, {'field': 'offline_amount', 'old_value': 166617.35, 'new_value': 171519.7}, {'field': 'total_amount', 'old_value': 179001.36, 'new_value': 184344.31}, {'field': 'order_count', 'old_value': 841, 'new_value': 873}]
2025-05-16 09:00:59,503 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-16 09:01:00,003 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-16 09:01:00,003 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28515.0, 'new_value': 29345.0}, {'field': 'total_amount', 'old_value': 28515.0, 'new_value': 29345.0}, {'field': 'order_count', 'old_value': 58, 'new_value': 60}]
2025-05-16 09:01:00,003 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-16 09:01:00,425 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-16 09:01:00,425 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4986.9, 'new_value': 5533.39}, {'field': 'offline_amount', 'old_value': 21290.0, 'new_value': 21905.0}, {'field': 'total_amount', 'old_value': 26276.9, 'new_value': 27438.39}, {'field': 'order_count', 'old_value': 123, 'new_value': 133}]
2025-05-16 09:01:00,425 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-16 09:01:00,941 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-16 09:01:00,941 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27968.6, 'new_value': 30204.75}, {'field': 'total_amount', 'old_value': 28165.4, 'new_value': 30401.55}, {'field': 'order_count', 'old_value': 248, 'new_value': 259}]
2025-05-16 09:01:00,941 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-16 09:01:01,425 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-16 09:01:01,425 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2901.0, 'new_value': 3249.0}, {'field': 'offline_amount', 'old_value': 13958.5, 'new_value': 14858.5}, {'field': 'total_amount', 'old_value': 16859.5, 'new_value': 18107.5}, {'field': 'order_count', 'old_value': 684, 'new_value': 739}]
2025-05-16 09:01:01,425 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-16 09:01:01,878 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-16 09:01:01,878 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61358.08, 'new_value': 63697.08}, {'field': 'total_amount', 'old_value': 61358.08, 'new_value': 63697.08}, {'field': 'order_count', 'old_value': 204, 'new_value': 206}]
2025-05-16 09:01:01,894 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-16 09:01:02,300 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-16 09:01:02,300 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 119575.6, 'new_value': 124725.5}, {'field': 'total_amount', 'old_value': 119575.6, 'new_value': 124725.5}, {'field': 'order_count', 'old_value': 431, 'new_value': 452}]
2025-05-16 09:01:02,300 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-16 09:01:02,691 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-16 09:01:02,691 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89407.35, 'new_value': 92817.45}, {'field': 'total_amount', 'old_value': 89407.35, 'new_value': 92817.45}, {'field': 'order_count', 'old_value': 433, 'new_value': 452}]
2025-05-16 09:01:02,691 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-16 09:01:03,113 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-16 09:01:03,113 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13041.93, 'new_value': 14134.17}, {'field': 'offline_amount', 'old_value': 23886.8, 'new_value': 25982.02}, {'field': 'total_amount', 'old_value': 36928.73, 'new_value': 40116.19}, {'field': 'order_count', 'old_value': 1329, 'new_value': 1442}]
2025-05-16 09:01:03,128 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-16 09:01:03,550 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-16 09:01:03,550 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39030.0, 'new_value': 40883.0}, {'field': 'total_amount', 'old_value': 41438.0, 'new_value': 43291.0}, {'field': 'order_count', 'old_value': 169, 'new_value': 181}]
2025-05-16 09:01:03,550 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-16 09:01:04,019 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-16 09:01:04,019 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14159.7, 'new_value': 14275.7}, {'field': 'offline_amount', 'old_value': 34705.84, 'new_value': 35628.84}, {'field': 'total_amount', 'old_value': 48865.54, 'new_value': 49904.54}, {'field': 'order_count', 'old_value': 545, 'new_value': 571}]
2025-05-16 09:01:04,019 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-16 09:01:04,550 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-16 09:01:04,550 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46630.55, 'new_value': 59717.86}, {'field': 'total_amount', 'old_value': 371942.39, 'new_value': 385029.7}, {'field': 'order_count', 'old_value': 1635, 'new_value': 1676}]
2025-05-16 09:01:04,550 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-16 09:01:05,003 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-16 09:01:05,003 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55664.5, 'new_value': 57369.6}, {'field': 'offline_amount', 'old_value': 72267.33, 'new_value': 77197.37}, {'field': 'total_amount', 'old_value': 127931.83, 'new_value': 134566.97}, {'field': 'order_count', 'old_value': 886, 'new_value': 933}]
2025-05-16 09:01:05,003 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-16 09:01:05,441 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-16 09:01:05,441 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10623.04, 'new_value': 11783.43}, {'field': 'offline_amount', 'old_value': 22339.36, 'new_value': 24251.62}, {'field': 'total_amount', 'old_value': 32962.4, 'new_value': 36035.05}, {'field': 'order_count', 'old_value': 1757, 'new_value': 1927}]
2025-05-16 09:01:05,441 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-16 09:01:05,847 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-16 09:01:05,847 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33212.0, 'new_value': 35150.0}, {'field': 'total_amount', 'old_value': 33561.0, 'new_value': 35499.0}, {'field': 'order_count', 'old_value': 56, 'new_value': 62}]
2025-05-16 09:01:05,847 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-16 09:01:06,363 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-16 09:01:06,363 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33334.0, 'new_value': 33810.0}, {'field': 'total_amount', 'old_value': 33334.0, 'new_value': 33810.0}, {'field': 'order_count', 'old_value': 227, 'new_value': 233}]
2025-05-16 09:01:06,363 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-16 09:01:06,863 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-16 09:01:06,863 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 258888.5, 'new_value': 270493.5}, {'field': 'total_amount', 'old_value': 299875.48, 'new_value': 311480.48}, {'field': 'order_count', 'old_value': 2304, 'new_value': 2414}]
2025-05-16 09:01:06,863 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-16 09:01:07,285 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-16 09:01:07,285 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70778.69, 'new_value': 73153.89}, {'field': 'total_amount', 'old_value': 70778.69, 'new_value': 73153.89}, {'field': 'order_count', 'old_value': 2044, 'new_value': 2130}]
2025-05-16 09:01:07,285 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0V
2025-05-16 09:01:07,706 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0V
2025-05-16 09:01:07,706 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17039.0, 'new_value': 20239.0}, {'field': 'total_amount', 'old_value': 17039.0, 'new_value': 20239.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-16 09:01:07,706 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-16 09:01:08,191 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-16 09:01:08,191 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 157752.73, 'new_value': 171825.59}, {'field': 'total_amount', 'old_value': 225706.76, 'new_value': 239779.62}, {'field': 'order_count', 'old_value': 2658, 'new_value': 2867}]
2025-05-16 09:01:08,191 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-16 09:01:08,691 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-16 09:01:08,691 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 240924.42, 'new_value': 245585.43}, {'field': 'total_amount', 'old_value': 240924.42, 'new_value': 245585.43}, {'field': 'order_count', 'old_value': 1129, 'new_value': 1172}]
2025-05-16 09:01:08,691 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-16 09:01:09,144 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-16 09:01:09,144 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28964.4, 'new_value': 30410.4}, {'field': 'total_amount', 'old_value': 28964.4, 'new_value': 30410.4}, {'field': 'order_count', 'old_value': 41, 'new_value': 45}]
2025-05-16 09:01:09,144 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-16 09:01:09,660 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-16 09:01:09,660 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36466.0, 'new_value': 36704.0}, {'field': 'total_amount', 'old_value': 43390.0, 'new_value': 43628.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 24}]
2025-05-16 09:01:09,660 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-16 09:01:10,191 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-16 09:01:10,191 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28040.6, 'new_value': 28431.4}, {'field': 'total_amount', 'old_value': 28561.8, 'new_value': 28952.6}, {'field': 'order_count', 'old_value': 89, 'new_value': 91}]
2025-05-16 09:01:10,191 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-16 09:01:10,660 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-16 09:01:10,660 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25957.55, 'new_value': 27422.8}, {'field': 'total_amount', 'old_value': 25957.55, 'new_value': 27422.8}, {'field': 'order_count', 'old_value': 1141, 'new_value': 1212}]
2025-05-16 09:01:10,660 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-16 09:01:11,113 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-16 09:01:11,113 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62860.65, 'new_value': 76799.15}, {'field': 'total_amount', 'old_value': 135586.35, 'new_value': 149524.85}, {'field': 'order_count', 'old_value': 3535, 'new_value': 3915}]
2025-05-16 09:01:11,113 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-16 09:01:11,519 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-16 09:01:11,519 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43894.49, 'new_value': 45893.21}, {'field': 'offline_amount', 'old_value': 150029.46, 'new_value': 156751.36}, {'field': 'total_amount', 'old_value': 193923.95, 'new_value': 202644.57}, {'field': 'order_count', 'old_value': 2644, 'new_value': 2716}]
2025-05-16 09:01:11,519 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-16 09:01:11,910 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-16 09:01:11,910 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19173.0, 'new_value': 20373.0}, {'field': 'total_amount', 'old_value': 19173.0, 'new_value': 20373.0}, {'field': 'order_count', 'old_value': 190, 'new_value': 200}]
2025-05-16 09:01:11,910 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-16 09:01:12,347 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-16 09:01:12,347 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30787.56, 'new_value': 32786.75}, {'field': 'offline_amount', 'old_value': 19099.19, 'new_value': 20276.77}, {'field': 'total_amount', 'old_value': 49886.75, 'new_value': 53063.52}, {'field': 'order_count', 'old_value': 2740, 'new_value': 2899}]
2025-05-16 09:01:12,347 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-16 09:01:12,863 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-16 09:01:12,863 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10367.18, 'new_value': 10702.18}, {'field': 'total_amount', 'old_value': 10367.18, 'new_value': 10702.18}, {'field': 'order_count', 'old_value': 96, 'new_value': 100}]
2025-05-16 09:01:12,863 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-16 09:01:13,300 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-16 09:01:13,300 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 234364.75, 'new_value': 246562.49}, {'field': 'total_amount', 'old_value': 234364.75, 'new_value': 246562.49}, {'field': 'order_count', 'old_value': 845, 'new_value': 888}]
2025-05-16 09:01:13,300 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-16 09:01:13,722 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-16 09:01:13,722 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 184281.0, 'new_value': 193545.0}, {'field': 'total_amount', 'old_value': 184281.0, 'new_value': 193545.0}, {'field': 'order_count', 'old_value': 4486, 'new_value': 4761}]
2025-05-16 09:01:13,722 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-16 09:01:14,175 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-16 09:01:14,175 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21955.59, 'new_value': 23691.93}, {'field': 'total_amount', 'old_value': 21955.59, 'new_value': 23691.93}, {'field': 'order_count', 'old_value': 2794, 'new_value': 3024}]
2025-05-16 09:01:14,175 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-16 09:01:14,613 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-16 09:01:14,613 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14631.57, 'new_value': 15471.26}, {'field': 'offline_amount', 'old_value': 18943.4, 'new_value': 19371.2}, {'field': 'total_amount', 'old_value': 33574.97, 'new_value': 34842.46}, {'field': 'order_count', 'old_value': 1476, 'new_value': 1539}]
2025-05-16 09:01:14,613 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-16 09:01:15,003 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-16 09:01:15,003 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43139.0, 'new_value': 46876.0}, {'field': 'total_amount', 'old_value': 48340.0, 'new_value': 52077.0}, {'field': 'order_count', 'old_value': 143, 'new_value': 152}]
2025-05-16 09:01:15,003 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-16 09:01:15,425 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-16 09:01:15,441 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 173016.6, 'new_value': 200804.6}, {'field': 'total_amount', 'old_value': 173016.6, 'new_value': 200804.6}, {'field': 'order_count', 'old_value': 36, 'new_value': 41}]
2025-05-16 09:01:15,441 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZM
2025-05-16 09:01:16,003 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZM
2025-05-16 09:01:16,003 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53572.0, 'new_value': 64930.0}, {'field': 'total_amount', 'old_value': 53572.0, 'new_value': 64930.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 13}]
2025-05-16 09:01:16,003 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-16 09:01:16,456 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-16 09:01:16,456 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10103.6, 'new_value': 10137.6}, {'field': 'total_amount', 'old_value': 46942.7, 'new_value': 46976.7}, {'field': 'order_count', 'old_value': 109, 'new_value': 110}]
2025-05-16 09:01:16,456 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-16 09:01:16,894 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-16 09:01:16,894 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 271343.96, 'new_value': 284733.96}, {'field': 'total_amount', 'old_value': 271343.96, 'new_value': 284733.96}, {'field': 'order_count', 'old_value': 1360, 'new_value': 1429}]
2025-05-16 09:01:16,894 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-16 09:01:17,331 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-16 09:01:17,331 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122872.0, 'new_value': 123987.0}, {'field': 'total_amount', 'old_value': 122872.0, 'new_value': 123987.0}, {'field': 'order_count', 'old_value': 68, 'new_value': 70}]
2025-05-16 09:01:17,331 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO
2025-05-16 09:01:17,785 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO
2025-05-16 09:01:17,785 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 766259.0, 'new_value': 813226.0}, {'field': 'total_amount', 'old_value': 766259.0, 'new_value': 813226.0}, {'field': 'order_count', 'old_value': 89, 'new_value': 95}]
2025-05-16 09:01:17,785 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-16 09:01:18,238 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-16 09:01:18,238 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30485.88, 'new_value': 32240.94}, {'field': 'offline_amount', 'old_value': 28903.59, 'new_value': 28937.69}, {'field': 'total_amount', 'old_value': 59389.47, 'new_value': 61178.63}, {'field': 'order_count', 'old_value': 194, 'new_value': 202}]
2025-05-16 09:01:18,238 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUO
2025-05-16 09:01:18,722 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUO
2025-05-16 09:01:18,722 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25500.0, 'new_value': 42300.0}, {'field': 'total_amount', 'old_value': 25500.0, 'new_value': 42300.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 5}]
2025-05-16 09:01:18,722 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-16 09:01:19,144 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-16 09:01:19,144 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 195370.52, 'new_value': 202269.52}, {'field': 'offline_amount', 'old_value': 5770.0, 'new_value': 5947.0}, {'field': 'total_amount', 'old_value': 201140.52, 'new_value': 208216.52}, {'field': 'order_count', 'old_value': 1576, 'new_value': 1656}]
2025-05-16 09:01:19,144 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-16 09:01:19,597 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-16 09:01:19,597 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13276.0, 'new_value': 13466.0}, {'field': 'total_amount', 'old_value': 13276.0, 'new_value': 13466.0}, {'field': 'order_count', 'old_value': 66, 'new_value': 67}]
2025-05-16 09:01:19,597 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-16 09:01:20,035 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-16 09:01:20,035 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11509.7, 'new_value': 11695.3}, {'field': 'total_amount', 'old_value': 11509.7, 'new_value': 11695.3}, {'field': 'order_count', 'old_value': 403, 'new_value': 413}]
2025-05-16 09:01:20,035 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-16 09:01:20,519 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-16 09:01:20,519 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7520.6, 'new_value': 7634.6}, {'field': 'total_amount', 'old_value': 7949.6, 'new_value': 8063.6}, {'field': 'order_count', 'old_value': 118, 'new_value': 120}]
2025-05-16 09:01:20,519 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-16 09:01:20,956 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-16 09:01:20,956 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4639.5, 'new_value': 5263.5}, {'field': 'offline_amount', 'old_value': 21559.1, 'new_value': 22146.3}, {'field': 'total_amount', 'old_value': 26198.6, 'new_value': 27409.8}, {'field': 'order_count', 'old_value': 295, 'new_value': 304}]
2025-05-16 09:01:20,956 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-16 09:01:21,347 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-16 09:01:21,347 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8247.0, 'new_value': 8370.0}, {'field': 'total_amount', 'old_value': 22250.0, 'new_value': 22373.0}, {'field': 'order_count', 'old_value': 51, 'new_value': 52}]
2025-05-16 09:01:21,347 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-16 09:01:21,831 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-16 09:01:21,831 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 432227.86, 'new_value': 458946.16}, {'field': 'total_amount', 'old_value': 432227.86, 'new_value': 458946.16}, {'field': 'order_count', 'old_value': 3129, 'new_value': 3333}]
2025-05-16 09:01:21,831 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-16 09:01:22,269 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-16 09:01:22,269 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 534772.0, 'new_value': 551776.0}, {'field': 'total_amount', 'old_value': 534772.0, 'new_value': 551776.0}, {'field': 'order_count', 'old_value': 2412, 'new_value': 2489}]
2025-05-16 09:01:22,269 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-16 09:01:22,691 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-16 09:01:22,691 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11689.0, 'new_value': 12688.0}, {'field': 'total_amount', 'old_value': 11689.0, 'new_value': 12688.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-05-16 09:01:22,691 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-16 09:01:23,097 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-16 09:01:23,097 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7822.2, 'new_value': 7926.0}, {'field': 'total_amount', 'old_value': 18322.2, 'new_value': 18426.0}, {'field': 'order_count', 'old_value': 122, 'new_value': 124}]
2025-05-16 09:01:23,097 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-16 09:01:23,628 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-16 09:01:23,628 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96568.0, 'new_value': 98906.0}, {'field': 'total_amount', 'old_value': 96568.0, 'new_value': 98906.0}, {'field': 'order_count', 'old_value': 3096, 'new_value': 3168}]
2025-05-16 09:01:23,628 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-16 09:01:24,081 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-16 09:01:24,081 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52423.43, 'new_value': 56209.61}, {'field': 'offline_amount', 'old_value': 62886.61, 'new_value': 65663.31}, {'field': 'total_amount', 'old_value': 115310.04, 'new_value': 121872.92}, {'field': 'order_count', 'old_value': 4640, 'new_value': 4929}]
2025-05-16 09:01:24,081 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-16 09:01:24,550 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-16 09:01:24,550 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 157963.06, 'new_value': 163431.8}, {'field': 'total_amount', 'old_value': 214389.24, 'new_value': 219857.98}, {'field': 'order_count', 'old_value': 377, 'new_value': 388}]
2025-05-16 09:01:24,550 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-16 09:01:25,050 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-16 09:01:25,050 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34774.0, 'new_value': 37056.0}, {'field': 'total_amount', 'old_value': 34774.0, 'new_value': 37056.0}, {'field': 'order_count', 'old_value': 71, 'new_value': 75}]
2025-05-16 09:01:25,050 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-16 09:01:25,503 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-16 09:01:25,503 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 65746.0, 'new_value': 68055.0}, {'field': 'offline_amount', 'old_value': 50158.36, 'new_value': 50564.76}, {'field': 'total_amount', 'old_value': 115904.36, 'new_value': 118619.76}, {'field': 'order_count', 'old_value': 761, 'new_value': 782}]
2025-05-16 09:01:25,503 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-16 09:01:25,909 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-16 09:01:25,909 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 208198.32, 'new_value': 215185.43}, {'field': 'offline_amount', 'old_value': 740435.55, 'new_value': 772439.29}, {'field': 'total_amount', 'old_value': 948633.87, 'new_value': 987624.72}, {'field': 'order_count', 'old_value': 4767, 'new_value': 4984}]
2025-05-16 09:01:25,909 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-16 09:01:26,347 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-16 09:01:26,347 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32945.23, 'new_value': 35290.23}, {'field': 'offline_amount', 'old_value': 28846.33, 'new_value': 29863.33}, {'field': 'total_amount', 'old_value': 61791.56, 'new_value': 65153.56}, {'field': 'order_count', 'old_value': 1236, 'new_value': 1301}]
2025-05-16 09:01:26,347 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCP
2025-05-16 09:01:26,769 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCP
2025-05-16 09:01:26,769 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91640.0, 'new_value': 115040.0}, {'field': 'total_amount', 'old_value': 91640.0, 'new_value': 115040.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 14}]
2025-05-16 09:01:26,769 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-16 09:01:27,206 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-16 09:01:27,206 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1480000.0, 'new_value': 1530000.0}, {'field': 'total_amount', 'old_value': 1480000.0, 'new_value': 1530000.0}, {'field': 'order_count', 'old_value': 271, 'new_value': 272}]
2025-05-16 09:01:27,206 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-16 09:01:27,628 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-16 09:01:27,628 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 153521.95, 'new_value': 160079.45}, {'field': 'total_amount', 'old_value': 153521.95, 'new_value': 160079.45}, {'field': 'order_count', 'old_value': 939, 'new_value': 983}]
2025-05-16 09:01:27,628 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-16 09:01:28,113 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-16 09:01:28,113 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 432086.8, 'new_value': 451937.71}, {'field': 'total_amount', 'old_value': 432086.8, 'new_value': 451937.71}, {'field': 'order_count', 'old_value': 4970, 'new_value': 5276}]
2025-05-16 09:01:28,113 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-16 09:01:28,534 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-16 09:01:28,534 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 145687.6, 'new_value': 147120.2}, {'field': 'offline_amount', 'old_value': 318709.0, 'new_value': 322977.0}, {'field': 'total_amount', 'old_value': 464396.6, 'new_value': 470097.2}, {'field': 'order_count', 'old_value': 3102, 'new_value': 3158}]
2025-05-16 09:01:28,534 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-16 09:01:28,988 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-16 09:01:28,988 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18465.98, 'new_value': 19875.41}, {'field': 'offline_amount', 'old_value': 214785.07, 'new_value': 221158.77}, {'field': 'total_amount', 'old_value': 233251.05, 'new_value': 241034.18}, {'field': 'order_count', 'old_value': 8885, 'new_value': 8946}]
2025-05-16 09:01:28,988 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-16 09:01:29,441 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-16 09:01:29,441 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 281376.0, 'new_value': 317699.0}, {'field': 'total_amount', 'old_value': 281376.0, 'new_value': 317699.0}, {'field': 'order_count', 'old_value': 265, 'new_value': 295}]
2025-05-16 09:01:29,441 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-16 09:01:29,878 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-16 09:01:29,878 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 158546.25, 'new_value': 161538.75}, {'field': 'offline_amount', 'old_value': 96584.11, 'new_value': 100704.84}, {'field': 'total_amount', 'old_value': 255130.36, 'new_value': 262243.59}, {'field': 'order_count', 'old_value': 2342, 'new_value': 2373}]
2025-05-16 09:01:29,878 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-16 09:01:30,363 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-16 09:01:30,363 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57170.6, 'new_value': 58471.5}, {'field': 'total_amount', 'old_value': 58699.4, 'new_value': 60000.3}, {'field': 'order_count', 'old_value': 350, 'new_value': 360}]
2025-05-16 09:01:30,363 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-16 09:01:30,863 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-16 09:01:30,863 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34664.0, 'new_value': 35840.0}, {'field': 'total_amount', 'old_value': 34664.0, 'new_value': 35840.0}, {'field': 'order_count', 'old_value': 74, 'new_value': 77}]
2025-05-16 09:01:30,863 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-16 09:01:31,425 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-16 09:01:31,425 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 109480.94, 'new_value': 118674.3}, {'field': 'offline_amount', 'old_value': 18557.3, 'new_value': 19063.1}, {'field': 'total_amount', 'old_value': 128038.24, 'new_value': 137737.4}, {'field': 'order_count', 'old_value': 6170, 'new_value': 6497}]
2025-05-16 09:01:31,425 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-16 09:01:31,878 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-16 09:01:31,878 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 445000.0, 'new_value': 450000.0}, {'field': 'total_amount', 'old_value': 445000.0, 'new_value': 450000.0}, {'field': 'order_count', 'old_value': 138, 'new_value': 139}]
2025-05-16 09:01:31,878 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-16 09:01:32,363 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-16 09:01:32,363 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 425000.0, 'new_value': 430000.0}, {'field': 'total_amount', 'old_value': 425000.0, 'new_value': 430000.0}, {'field': 'order_count', 'old_value': 137, 'new_value': 138}]
2025-05-16 09:01:32,363 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-16 09:01:32,738 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-16 09:01:32,738 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2598674.0, 'new_value': 2648674.0}, {'field': 'total_amount', 'old_value': 2598674.0, 'new_value': 2648674.0}, {'field': 'order_count', 'old_value': 291, 'new_value': 292}]
2025-05-16 09:01:32,738 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-16 09:01:33,191 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-16 09:01:33,191 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49934.0, 'new_value': 53918.0}, {'field': 'offline_amount', 'old_value': 608866.0, 'new_value': 650379.0}, {'field': 'total_amount', 'old_value': 658800.0, 'new_value': 704297.0}, {'field': 'order_count', 'old_value': 15633, 'new_value': 16834}]
2025-05-16 09:01:33,191 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-16 09:01:33,659 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-16 09:01:33,659 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 235271.92, 'new_value': 247791.08}, {'field': 'total_amount', 'old_value': 235271.92, 'new_value': 247791.08}, {'field': 'order_count', 'old_value': 793, 'new_value': 830}]
2025-05-16 09:01:33,659 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-16 09:01:34,097 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-16 09:01:34,097 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19629.0, 'new_value': 21131.0}, {'field': 'offline_amount', 'old_value': 113647.0, 'new_value': 121604.0}, {'field': 'total_amount', 'old_value': 133276.0, 'new_value': 142735.0}, {'field': 'order_count', 'old_value': 138, 'new_value': 146}]
2025-05-16 09:01:34,097 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-16 09:01:34,550 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-16 09:01:34,550 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21675.0, 'new_value': 23438.0}, {'field': 'total_amount', 'old_value': 21675.0, 'new_value': 23438.0}, {'field': 'order_count', 'old_value': 32, 'new_value': 35}]
2025-05-16 09:01:34,550 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-16 09:01:35,003 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-16 09:01:35,003 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12636.54, 'new_value': 13661.9}, {'field': 'offline_amount', 'old_value': 9946.4, 'new_value': 10578.4}, {'field': 'total_amount', 'old_value': 22582.94, 'new_value': 24240.3}, {'field': 'order_count', 'old_value': 1024, 'new_value': 1087}]
2025-05-16 09:01:35,003 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-16 09:01:35,472 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-16 09:01:35,472 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28298.6, 'new_value': 28465.5}, {'field': 'total_amount', 'old_value': 38053.1, 'new_value': 38220.0}, {'field': 'order_count', 'old_value': 427, 'new_value': 431}]
2025-05-16 09:01:35,472 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-16 09:01:35,909 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-16 09:01:35,909 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31449.1, 'new_value': 31579.1}, {'field': 'total_amount', 'old_value': 31877.1, 'new_value': 32007.1}, {'field': 'order_count', 'old_value': 9238, 'new_value': 9240}]
2025-05-16 09:01:35,909 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-16 09:01:36,394 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-16 09:01:36,394 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 103683.53, 'new_value': 107756.69}, {'field': 'total_amount', 'old_value': 103683.53, 'new_value': 107756.69}, {'field': 'order_count', 'old_value': 5219, 'new_value': 5487}]
2025-05-16 09:01:36,394 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-16 09:01:36,863 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-16 09:01:36,863 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92001.1, 'new_value': 99586.5}, {'field': 'total_amount', 'old_value': 92001.1, 'new_value': 99586.5}, {'field': 'order_count', 'old_value': 428, 'new_value': 459}]
2025-05-16 09:01:36,863 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-16 09:01:37,331 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-16 09:01:37,331 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92791.3, 'new_value': 96048.5}, {'field': 'total_amount', 'old_value': 92791.3, 'new_value': 96048.5}, {'field': 'order_count', 'old_value': 2556, 'new_value': 2647}]
2025-05-16 09:01:37,331 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-16 09:01:37,878 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-16 09:01:37,878 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 63404.3, 'new_value': 66187.7}, {'field': 'total_amount', 'old_value': 63404.3, 'new_value': 66187.7}, {'field': 'order_count', 'old_value': 298, 'new_value': 316}]
2025-05-16 09:01:37,878 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-16 09:01:38,253 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-16 09:01:38,253 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 212125.84, 'new_value': 215367.84}, {'field': 'total_amount', 'old_value': 212125.84, 'new_value': 215367.84}, {'field': 'order_count', 'old_value': 1106, 'new_value': 1129}]
2025-05-16 09:01:38,253 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-16 09:01:38,706 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-16 09:01:38,706 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27922.04, 'new_value': 30843.56}, {'field': 'offline_amount', 'old_value': 29808.29, 'new_value': 30667.69}, {'field': 'total_amount', 'old_value': 57730.33, 'new_value': 61511.25}, {'field': 'order_count', 'old_value': 4444, 'new_value': 4831}]
2025-05-16 09:01:38,706 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-16 09:01:39,097 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-16 09:01:39,097 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52255.56, 'new_value': 56231.56}, {'field': 'total_amount', 'old_value': 52255.56, 'new_value': 56231.56}, {'field': 'order_count', 'old_value': 2454, 'new_value': 2679}]
2025-05-16 09:01:39,097 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-16 09:01:39,706 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-16 09:01:39,706 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53223.62, 'new_value': 54433.35}, {'field': 'total_amount', 'old_value': 54484.05, 'new_value': 55693.78}, {'field': 'order_count', 'old_value': 251, 'new_value': 258}]
2025-05-16 09:01:39,706 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXP
2025-05-16 09:01:40,175 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXP
2025-05-16 09:01:40,175 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47640.0, 'new_value': 51240.0}, {'field': 'total_amount', 'old_value': 47640.0, 'new_value': 51240.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-05-16 09:01:40,175 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-16 09:01:40,628 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-16 09:01:40,628 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2698.0, 'new_value': 2772.0}, {'field': 'total_amount', 'old_value': 7147.0, 'new_value': 7221.0}, {'field': 'order_count', 'old_value': 66, 'new_value': 68}]
2025-05-16 09:01:40,628 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-16 09:01:41,191 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-16 09:01:41,191 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32207.48, 'new_value': 34357.4}, {'field': 'offline_amount', 'old_value': 27112.43, 'new_value': 30023.93}, {'field': 'total_amount', 'old_value': 59319.91, 'new_value': 64381.33}, {'field': 'order_count', 'old_value': 3084, 'new_value': 3345}]
2025-05-16 09:01:41,191 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-16 09:01:41,644 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-16 09:01:41,644 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 56993.71, 'new_value': 61483.02}, {'field': 'offline_amount', 'old_value': 64377.16, 'new_value': 68571.22}, {'field': 'total_amount', 'old_value': 121370.87, 'new_value': 130054.24}, {'field': 'order_count', 'old_value': 3038, 'new_value': 3266}]
2025-05-16 09:01:41,644 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-16 09:01:42,112 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-16 09:01:42,112 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 565860.0, 'new_value': 599420.0}, {'field': 'total_amount', 'old_value': 565860.0, 'new_value': 599420.0}, {'field': 'order_count', 'old_value': 662, 'new_value': 697}]
2025-05-16 09:01:42,112 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-16 09:01:42,581 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-16 09:01:42,581 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 111042.6, 'new_value': 113816.6}, {'field': 'total_amount', 'old_value': 116992.9, 'new_value': 119766.9}, {'field': 'order_count', 'old_value': 231, 'new_value': 236}]
2025-05-16 09:01:42,581 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-16 09:01:43,050 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-16 09:01:43,050 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24527.75, 'new_value': 25694.95}, {'field': 'offline_amount', 'old_value': 65035.0, 'new_value': 68739.0}, {'field': 'total_amount', 'old_value': 89562.75, 'new_value': 94433.95}, {'field': 'order_count', 'old_value': 952, 'new_value': 1010}]
2025-05-16 09:01:43,050 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-16 09:01:43,519 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-16 09:01:43,519 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 76742.0, 'new_value': 81025.0}, {'field': 'offline_amount', 'old_value': 56111.0, 'new_value': 59396.0}, {'field': 'total_amount', 'old_value': 132853.0, 'new_value': 140421.0}, {'field': 'order_count', 'old_value': 1639, 'new_value': 1752}]
2025-05-16 09:01:43,519 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-16 09:01:44,034 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-16 09:01:44,034 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5818.3, 'new_value': 5966.2}, {'field': 'offline_amount', 'old_value': 12246.45, 'new_value': 12669.35}, {'field': 'total_amount', 'old_value': 18064.75, 'new_value': 18635.55}, {'field': 'order_count', 'old_value': 188, 'new_value': 194}]
2025-05-16 09:01:44,034 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-16 09:01:44,472 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-16 09:01:44,472 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66137.0, 'new_value': 104927.0}, {'field': 'total_amount', 'old_value': 72339.5, 'new_value': 111129.5}, {'field': 'order_count', 'old_value': 39, 'new_value': 46}]
2025-05-16 09:01:44,472 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-16 09:01:44,972 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-16 09:01:44,972 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21490.6, 'new_value': 22818.6}, {'field': 'total_amount', 'old_value': 38824.12, 'new_value': 40152.12}, {'field': 'order_count', 'old_value': 157, 'new_value': 164}]
2025-05-16 09:01:44,972 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-16 09:01:45,409 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-16 09:01:45,409 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 128773.5, 'new_value': 134758.5}, {'field': 'total_amount', 'old_value': 128773.5, 'new_value': 134758.5}, {'field': 'order_count', 'old_value': 641, 'new_value': 669}]
2025-05-16 09:01:45,409 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-16 09:01:45,878 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-16 09:01:45,878 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26845.09, 'new_value': 28424.59}, {'field': 'total_amount', 'old_value': 30532.09, 'new_value': 32111.59}, {'field': 'order_count', 'old_value': 293, 'new_value': 307}]
2025-05-16 09:01:45,878 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-16 09:01:46,347 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-16 09:01:46,347 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98212.99, 'new_value': 102810.74}, {'field': 'total_amount', 'old_value': 98212.99, 'new_value': 102810.74}, {'field': 'order_count', 'old_value': 344, 'new_value': 359}]
2025-05-16 09:01:46,347 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-16 09:01:46,737 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-16 09:01:46,737 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 670704.0, 'new_value': 694826.0}, {'field': 'total_amount', 'old_value': 670704.0, 'new_value': 694826.0}, {'field': 'order_count', 'old_value': 2861, 'new_value': 2986}]
2025-05-16 09:01:46,737 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-16 09:01:47,300 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-16 09:01:47,300 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8688481.0, 'new_value': 8979198.0}, {'field': 'total_amount', 'old_value': 8688481.0, 'new_value': 8979198.0}, {'field': 'order_count', 'old_value': 25990, 'new_value': 27025}]
2025-05-16 09:01:47,300 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-16 09:01:47,753 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-16 09:01:47,753 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88881.53, 'new_value': 96303.23}, {'field': 'total_amount', 'old_value': 96321.17, 'new_value': 103742.87}, {'field': 'order_count', 'old_value': 6714, 'new_value': 7270}]
2025-05-16 09:01:47,753 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-16 09:01:48,300 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-16 09:01:48,300 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 140514.0, 'new_value': 143997.0}, {'field': 'total_amount', 'old_value': 140514.0, 'new_value': 143997.0}, {'field': 'order_count', 'old_value': 289, 'new_value': 299}]
2025-05-16 09:01:48,300 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-16 09:01:48,753 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-16 09:01:48,753 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 162055.82, 'new_value': 176104.74}, {'field': 'offline_amount', 'old_value': 127075.32, 'new_value': 134175.34}, {'field': 'total_amount', 'old_value': 289131.14, 'new_value': 310280.08}, {'field': 'order_count', 'old_value': 11483, 'new_value': 12314}]
2025-05-16 09:01:48,753 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-16 09:01:49,128 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-16 09:01:49,128 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 180521.0, 'new_value': 184619.0}, {'field': 'total_amount', 'old_value': 180521.0, 'new_value': 184619.0}, {'field': 'order_count', 'old_value': 204, 'new_value': 213}]
2025-05-16 09:01:49,128 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-16 09:01:49,581 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-16 09:01:49,581 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105854.05, 'new_value': 120049.78}, {'field': 'total_amount', 'old_value': 182844.75, 'new_value': 197040.48}, {'field': 'order_count', 'old_value': 188, 'new_value': 207}]
2025-05-16 09:01:49,581 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-16 09:01:50,050 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-16 09:01:50,050 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 101907.0, 'new_value': 108660.0}, {'field': 'offline_amount', 'old_value': 85034.0, 'new_value': 89321.0}, {'field': 'total_amount', 'old_value': 186941.0, 'new_value': 197981.0}, {'field': 'order_count', 'old_value': 561, 'new_value': 592}]
2025-05-16 09:01:50,050 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-16 09:01:50,566 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-16 09:01:50,566 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 463387.81, 'new_value': 473487.3}, {'field': 'total_amount', 'old_value': 463387.81, 'new_value': 473487.3}, {'field': 'order_count', 'old_value': 2430, 'new_value': 2528}]
2025-05-16 09:01:50,566 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-16 09:01:51,003 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-16 09:01:51,003 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83577.52, 'new_value': 87492.6}, {'field': 'total_amount', 'old_value': 83577.52, 'new_value': 87492.6}, {'field': 'order_count', 'old_value': 5717, 'new_value': 5980}]
2025-05-16 09:01:51,003 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-16 09:01:51,519 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-16 09:01:51,519 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 325648.0, 'new_value': 329038.0}, {'field': 'total_amount', 'old_value': 325648.0, 'new_value': 329038.0}, {'field': 'order_count', 'old_value': 7370, 'new_value': 7444}]
2025-05-16 09:01:51,519 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-16 09:01:51,878 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-16 09:01:51,878 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76712.0, 'new_value': 83043.0}, {'field': 'total_amount', 'old_value': 76712.0, 'new_value': 83043.0}, {'field': 'order_count', 'old_value': 5609, 'new_value': 6113}]
2025-05-16 09:01:51,878 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-16 09:01:52,378 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-16 09:01:52,378 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25739.0, 'new_value': 25951.0}, {'field': 'total_amount', 'old_value': 25739.0, 'new_value': 25951.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 28}]
2025-05-16 09:01:52,378 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-16 09:01:52,800 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-16 09:01:52,800 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 226.2, 'new_value': 253.0}, {'field': 'offline_amount', 'old_value': 49584.2, 'new_value': 49780.2}, {'field': 'total_amount', 'old_value': 49810.4, 'new_value': 50033.2}, {'field': 'order_count', 'old_value': 764, 'new_value': 768}]
2025-05-16 09:01:52,800 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-16 09:01:53,284 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-16 09:01:53,284 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12636.3, 'new_value': 13793.4}, {'field': 'offline_amount', 'old_value': 33275.4, 'new_value': 34730.4}, {'field': 'total_amount', 'old_value': 45911.7, 'new_value': 48523.8}, {'field': 'order_count', 'old_value': 1698, 'new_value': 1799}]
2025-05-16 09:01:53,284 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM6V
2025-05-16 09:01:53,722 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM6V
2025-05-16 09:01:53,722 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12880.0, 'new_value': 22780.0}, {'field': 'total_amount', 'old_value': 12880.0, 'new_value': 22780.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-16 09:01:53,722 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-16 09:01:54,191 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-16 09:01:54,191 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15028.29, 'new_value': 16098.29}, {'field': 'total_amount', 'old_value': 15028.29, 'new_value': 16098.29}, {'field': 'order_count', 'old_value': 61, 'new_value': 67}]
2025-05-16 09:01:54,191 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-16 09:01:54,612 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-16 09:01:54,612 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 179010.0, 'new_value': 192659.0}, {'field': 'total_amount', 'old_value': 179010.0, 'new_value': 192659.0}, {'field': 'order_count', 'old_value': 3898, 'new_value': 4180}]
2025-05-16 09:01:54,612 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMDV
2025-05-16 09:01:55,019 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMDV
2025-05-16 09:01:55,019 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22336.0, 'new_value': 22643.0}, {'field': 'total_amount', 'old_value': 22363.9, 'new_value': 22670.9}, {'field': 'order_count', 'old_value': 20, 'new_value': 22}]
2025-05-16 09:01:55,019 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-16 09:01:55,550 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-16 09:01:55,550 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16548.0, 'new_value': 18073.0}, {'field': 'total_amount', 'old_value': 16548.0, 'new_value': 18073.0}, {'field': 'order_count', 'old_value': 56, 'new_value': 61}]
2025-05-16 09:01:55,550 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-16 09:01:55,972 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-16 09:01:55,972 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95024.0, 'new_value': 103547.0}, {'field': 'total_amount', 'old_value': 95024.0, 'new_value': 103547.0}, {'field': 'order_count', 'old_value': 3304, 'new_value': 3623}]
2025-05-16 09:01:55,972 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-16 09:01:56,441 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-16 09:01:56,441 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 996.0, 'new_value': 4356.0}, {'field': 'offline_amount', 'old_value': 214512.0, 'new_value': 223832.0}, {'field': 'total_amount', 'old_value': 215508.0, 'new_value': 228188.0}, {'field': 'order_count', 'old_value': 55, 'new_value': 60}]
2025-05-16 09:01:56,441 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-16 09:01:56,847 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-16 09:01:56,847 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 184957.49, 'new_value': 194136.07}, {'field': 'total_amount', 'old_value': 184957.49, 'new_value': 194136.07}, {'field': 'order_count', 'old_value': 515, 'new_value': 538}]
2025-05-16 09:01:56,847 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-16 09:01:57,378 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-16 09:01:57,378 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81996.0, 'new_value': 88492.0}, {'field': 'total_amount', 'old_value': 81996.0, 'new_value': 88492.0}, {'field': 'order_count', 'old_value': 320, 'new_value': 348}]
2025-05-16 09:01:57,378 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-16 09:01:57,831 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-16 09:01:57,831 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27561.0, 'new_value': 30352.0}, {'field': 'total_amount', 'old_value': 27561.0, 'new_value': 30352.0}, {'field': 'order_count', 'old_value': 526, 'new_value': 580}]
2025-05-16 09:01:57,831 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-16 09:01:58,269 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-16 09:01:58,269 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15471.0, 'new_value': 18151.0}, {'field': 'offline_amount', 'old_value': 35549.0, 'new_value': 36371.0}, {'field': 'total_amount', 'old_value': 51020.0, 'new_value': 54522.0}, {'field': 'order_count', 'old_value': 72, 'new_value': 76}]
2025-05-16 09:01:58,269 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-16 09:01:58,691 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-16 09:01:58,691 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 101173.47, 'new_value': 107585.11}, {'field': 'total_amount', 'old_value': 101173.47, 'new_value': 107585.11}, {'field': 'order_count', 'old_value': 10536, 'new_value': 11290}]
2025-05-16 09:01:58,691 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-16 09:01:59,128 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-16 09:01:59,128 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16474.08, 'new_value': 19522.92}, {'field': 'total_amount', 'old_value': 16474.08, 'new_value': 19522.92}, {'field': 'order_count', 'old_value': 103, 'new_value': 122}]
2025-05-16 09:01:59,128 - INFO - 日期 2025-05 处理完成 - 更新: 196 条，插入: 0 条，错误: 0 条
2025-05-16 09:01:59,128 - INFO - 数据同步完成！更新: 196 条，插入: 0 条，错误: 0 条
2025-05-16 09:01:59,128 - INFO - =================同步完成====================
2025-05-16 12:00:01,860 - INFO - =================使用默认全量同步=============
2025-05-16 12:00:03,235 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-16 12:00:03,235 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-16 12:00:03,250 - INFO - 开始处理日期: 2025-01
2025-05-16 12:00:03,250 - INFO - Request Parameters - Page 1:
2025-05-16 12:00:03,250 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 12:00:03,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 12:00:04,469 - INFO - Response - Page 1:
2025-05-16 12:00:04,672 - INFO - 第 1 页获取到 100 条记录
2025-05-16 12:00:04,672 - INFO - Request Parameters - Page 2:
2025-05-16 12:00:04,672 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 12:00:04,672 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 12:00:05,188 - INFO - Response - Page 2:
2025-05-16 12:00:05,391 - INFO - 第 2 页获取到 100 条记录
2025-05-16 12:00:05,391 - INFO - Request Parameters - Page 3:
2025-05-16 12:00:05,391 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 12:00:05,391 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 12:00:05,907 - INFO - Response - Page 3:
2025-05-16 12:00:06,110 - INFO - 第 3 页获取到 100 条记录
2025-05-16 12:00:06,110 - INFO - Request Parameters - Page 4:
2025-05-16 12:00:06,110 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 12:00:06,110 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 12:00:06,563 - INFO - Response - Page 4:
2025-05-16 12:00:06,766 - INFO - 第 4 页获取到 100 条记录
2025-05-16 12:00:06,766 - INFO - Request Parameters - Page 5:
2025-05-16 12:00:06,766 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 12:00:06,766 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 12:00:07,297 - INFO - Response - Page 5:
2025-05-16 12:00:07,500 - INFO - 第 5 页获取到 100 条记录
2025-05-16 12:00:07,500 - INFO - Request Parameters - Page 6:
2025-05-16 12:00:07,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 12:00:07,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 12:00:07,985 - INFO - Response - Page 6:
2025-05-16 12:00:08,188 - INFO - 第 6 页获取到 100 条记录
2025-05-16 12:00:08,188 - INFO - Request Parameters - Page 7:
2025-05-16 12:00:08,188 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 12:00:08,188 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 12:00:08,672 - INFO - Response - Page 7:
2025-05-16 12:00:08,875 - INFO - 第 7 页获取到 82 条记录
2025-05-16 12:00:08,875 - INFO - 查询完成，共获取到 682 条记录
2025-05-16 12:00:08,875 - INFO - 获取到 682 条表单数据
2025-05-16 12:00:08,875 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-16 12:00:08,891 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-16 12:00:08,891 - INFO - 开始处理日期: 2025-02
2025-05-16 12:00:08,891 - INFO - Request Parameters - Page 1:
2025-05-16 12:00:08,891 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 12:00:08,891 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 12:00:09,407 - INFO - Response - Page 1:
2025-05-16 12:00:09,610 - INFO - 第 1 页获取到 100 条记录
2025-05-16 12:00:09,610 - INFO - Request Parameters - Page 2:
2025-05-16 12:00:09,610 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 12:00:09,610 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 12:00:10,094 - INFO - Response - Page 2:
2025-05-16 12:00:10,297 - INFO - 第 2 页获取到 100 条记录
2025-05-16 12:00:10,297 - INFO - Request Parameters - Page 3:
2025-05-16 12:00:10,297 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 12:00:10,297 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 12:00:10,797 - INFO - Response - Page 3:
2025-05-16 12:00:11,000 - INFO - 第 3 页获取到 100 条记录
2025-05-16 12:00:11,000 - INFO - Request Parameters - Page 4:
2025-05-16 12:00:11,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 12:00:11,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 12:00:11,532 - INFO - Response - Page 4:
2025-05-16 12:00:11,735 - INFO - 第 4 页获取到 100 条记录
2025-05-16 12:00:11,735 - INFO - Request Parameters - Page 5:
2025-05-16 12:00:11,735 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 12:00:11,735 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 12:00:12,204 - INFO - Response - Page 5:
2025-05-16 12:00:12,407 - INFO - 第 5 页获取到 100 条记录
2025-05-16 12:00:12,407 - INFO - Request Parameters - Page 6:
2025-05-16 12:00:12,407 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 12:00:12,407 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 12:00:12,860 - INFO - Response - Page 6:
2025-05-16 12:00:13,063 - INFO - 第 6 页获取到 100 条记录
2025-05-16 12:00:13,063 - INFO - Request Parameters - Page 7:
2025-05-16 12:00:13,063 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 12:00:13,063 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 12:00:13,563 - INFO - Response - Page 7:
2025-05-16 12:00:13,766 - INFO - 第 7 页获取到 70 条记录
2025-05-16 12:00:13,766 - INFO - 查询完成，共获取到 670 条记录
2025-05-16 12:00:13,766 - INFO - 获取到 670 条表单数据
2025-05-16 12:00:13,766 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-16 12:00:13,782 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-16 12:00:13,782 - INFO - 开始处理日期: 2025-03
2025-05-16 12:00:13,782 - INFO - Request Parameters - Page 1:
2025-05-16 12:00:13,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 12:00:13,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 12:00:14,329 - INFO - Response - Page 1:
2025-05-16 12:00:14,532 - INFO - 第 1 页获取到 100 条记录
2025-05-16 12:00:14,532 - INFO - Request Parameters - Page 2:
2025-05-16 12:00:14,532 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 12:00:14,532 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 12:00:14,969 - INFO - Response - Page 2:
2025-05-16 12:00:15,172 - INFO - 第 2 页获取到 100 条记录
2025-05-16 12:00:15,172 - INFO - Request Parameters - Page 3:
2025-05-16 12:00:15,172 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 12:00:15,172 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 12:00:15,797 - INFO - Response - Page 3:
2025-05-16 12:00:16,000 - INFO - 第 3 页获取到 100 条记录
2025-05-16 12:00:16,000 - INFO - Request Parameters - Page 4:
2025-05-16 12:00:16,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 12:00:16,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 12:00:16,579 - INFO - Response - Page 4:
2025-05-16 12:00:16,782 - INFO - 第 4 页获取到 100 条记录
2025-05-16 12:00:16,782 - INFO - Request Parameters - Page 5:
2025-05-16 12:00:16,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 12:00:16,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 12:00:17,235 - INFO - Response - Page 5:
2025-05-16 12:00:17,438 - INFO - 第 5 页获取到 100 条记录
2025-05-16 12:00:17,438 - INFO - Request Parameters - Page 6:
2025-05-16 12:00:17,438 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 12:00:17,438 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 12:00:17,891 - INFO - Response - Page 6:
2025-05-16 12:00:18,094 - INFO - 第 6 页获取到 100 条记录
2025-05-16 12:00:18,094 - INFO - Request Parameters - Page 7:
2025-05-16 12:00:18,094 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 12:00:18,094 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 12:00:18,625 - INFO - Response - Page 7:
2025-05-16 12:00:18,829 - INFO - 第 7 页获取到 61 条记录
2025-05-16 12:00:18,829 - INFO - 查询完成，共获取到 661 条记录
2025-05-16 12:00:18,829 - INFO - 获取到 661 条表单数据
2025-05-16 12:00:18,829 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-16 12:00:18,844 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-16 12:00:18,844 - INFO - 开始处理日期: 2025-04
2025-05-16 12:00:18,844 - INFO - Request Parameters - Page 1:
2025-05-16 12:00:18,844 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 12:00:18,844 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 12:00:19,375 - INFO - Response - Page 1:
2025-05-16 12:00:19,579 - INFO - 第 1 页获取到 100 条记录
2025-05-16 12:00:19,579 - INFO - Request Parameters - Page 2:
2025-05-16 12:00:19,579 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 12:00:19,579 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 12:00:20,110 - INFO - Response - Page 2:
2025-05-16 12:00:20,313 - INFO - 第 2 页获取到 100 条记录
2025-05-16 12:00:20,313 - INFO - Request Parameters - Page 3:
2025-05-16 12:00:20,313 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 12:00:20,313 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 12:00:20,797 - INFO - Response - Page 3:
2025-05-16 12:00:21,000 - INFO - 第 3 页获取到 100 条记录
2025-05-16 12:00:21,000 - INFO - Request Parameters - Page 4:
2025-05-16 12:00:21,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 12:00:21,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 12:00:21,485 - INFO - Response - Page 4:
2025-05-16 12:00:21,688 - INFO - 第 4 页获取到 100 条记录
2025-05-16 12:00:21,688 - INFO - Request Parameters - Page 5:
2025-05-16 12:00:21,688 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 12:00:21,688 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 12:00:22,500 - INFO - Response - Page 5:
2025-05-16 12:00:22,703 - INFO - 第 5 页获取到 100 条记录
2025-05-16 12:00:22,703 - INFO - Request Parameters - Page 6:
2025-05-16 12:00:22,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 12:00:22,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 12:00:23,235 - INFO - Response - Page 6:
2025-05-16 12:00:23,438 - INFO - 第 6 页获取到 100 条记录
2025-05-16 12:00:23,438 - INFO - Request Parameters - Page 7:
2025-05-16 12:00:23,438 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 12:00:23,438 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 12:00:23,860 - INFO - Response - Page 7:
2025-05-16 12:00:24,063 - INFO - 第 7 页获取到 54 条记录
2025-05-16 12:00:24,063 - INFO - 查询完成，共获取到 654 条记录
2025-05-16 12:00:24,063 - INFO - 获取到 654 条表单数据
2025-05-16 12:00:24,063 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-16 12:00:24,063 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MC3
2025-05-16 12:00:24,563 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MC3
2025-05-16 12:00:24,563 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35184.41, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 17570.68, 'new_value': 54572.01}, {'field': 'total_amount', 'old_value': 52755.09, 'new_value': 54572.01}]
2025-05-16 12:00:24,578 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MM6
2025-05-16 12:00:25,047 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MM6
2025-05-16 12:00:25,047 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69036.39, 'new_value': 66901.9}, {'field': 'total_amount', 'old_value': 69036.39, 'new_value': 66901.9}]
2025-05-16 12:00:25,047 - INFO - 日期 2025-04 处理完成 - 更新: 2 条，插入: 0 条，错误: 0 条
2025-05-16 12:00:25,047 - INFO - 开始处理日期: 2025-05
2025-05-16 12:00:25,047 - INFO - Request Parameters - Page 1:
2025-05-16 12:00:25,047 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 12:00:25,047 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 12:00:25,563 - INFO - Response - Page 1:
2025-05-16 12:00:25,766 - INFO - 第 1 页获取到 100 条记录
2025-05-16 12:00:25,766 - INFO - Request Parameters - Page 2:
2025-05-16 12:00:25,766 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 12:00:25,766 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 12:00:26,219 - INFO - Response - Page 2:
2025-05-16 12:00:26,422 - INFO - 第 2 页获取到 100 条记录
2025-05-16 12:00:26,422 - INFO - Request Parameters - Page 3:
2025-05-16 12:00:26,422 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 12:00:26,422 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 12:00:26,985 - INFO - Response - Page 3:
2025-05-16 12:00:27,188 - INFO - 第 3 页获取到 100 条记录
2025-05-16 12:00:27,188 - INFO - Request Parameters - Page 4:
2025-05-16 12:00:27,188 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 12:00:27,188 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 12:00:27,813 - INFO - Response - Page 4:
2025-05-16 12:00:28,016 - INFO - 第 4 页获取到 100 条记录
2025-05-16 12:00:28,016 - INFO - Request Parameters - Page 5:
2025-05-16 12:00:28,016 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 12:00:28,016 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 12:00:28,594 - INFO - Response - Page 5:
2025-05-16 12:00:28,797 - INFO - 第 5 页获取到 100 条记录
2025-05-16 12:00:28,797 - INFO - Request Parameters - Page 6:
2025-05-16 12:00:28,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 12:00:28,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 12:00:29,282 - INFO - Response - Page 6:
2025-05-16 12:00:29,485 - INFO - 第 6 页获取到 100 条记录
2025-05-16 12:00:29,485 - INFO - Request Parameters - Page 7:
2025-05-16 12:00:29,485 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 12:00:29,485 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 12:00:29,907 - INFO - Response - Page 7:
2025-05-16 12:00:30,110 - INFO - 第 7 页获取到 25 条记录
2025-05-16 12:00:30,110 - INFO - 查询完成，共获取到 625 条记录
2025-05-16 12:00:30,110 - INFO - 获取到 625 条表单数据
2025-05-16 12:00:30,110 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-16 12:00:30,110 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-16 12:00:30,516 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-16 12:00:30,516 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5800000.0, 'new_value': 6200000.0}, {'field': 'total_amount', 'old_value': 5900000.0, 'new_value': 6300000.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 30}]
2025-05-16 12:00:30,516 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-16 12:00:30,969 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-16 12:00:30,969 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22525.0, 'new_value': 26235.0}, {'field': 'total_amount', 'old_value': 24115.0, 'new_value': 27825.0}, {'field': 'order_count', 'old_value': 91, 'new_value': 105}]
2025-05-16 12:00:30,969 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9C
2025-05-16 12:00:31,438 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9C
2025-05-16 12:00:31,438 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 132441.0, 'new_value': 171250.0}, {'field': 'total_amount', 'old_value': 132441.0, 'new_value': 171250.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-16 12:00:31,438 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3QQFGZBAM8D1
2025-05-16 12:00:31,891 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3QQFGZBAM8D1
2025-05-16 12:00:31,891 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15265.7, 'new_value': 15708.7}, {'field': 'total_amount', 'old_value': 15265.7, 'new_value': 15708.7}, {'field': 'order_count', 'old_value': 102, 'new_value': 107}]
2025-05-16 12:00:31,891 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMOX
2025-05-16 12:00:32,313 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMOX
2025-05-16 12:00:32,313 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22205.88, 'new_value': 24717.41}, {'field': 'total_amount', 'old_value': 22205.88, 'new_value': 24717.41}, {'field': 'order_count', 'old_value': 4241, 'new_value': 4758}]
2025-05-16 12:00:32,313 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM1Q
2025-05-16 12:00:32,750 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM1Q
2025-05-16 12:00:32,750 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34891.7, 'new_value': 39158.39}, {'field': 'total_amount', 'old_value': 38366.73, 'new_value': 42633.42}, {'field': 'order_count', 'old_value': 1344, 'new_value': 1504}]
2025-05-16 12:00:32,766 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-16 12:00:33,250 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-16 12:00:33,250 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16852.92, 'new_value': 18159.71}, {'field': 'offline_amount', 'old_value': 8591.47, 'new_value': 9251.27}, {'field': 'total_amount', 'old_value': 25444.39, 'new_value': 27410.98}, {'field': 'order_count', 'old_value': 1303, 'new_value': 1406}]
2025-05-16 12:00:33,250 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-16 12:00:33,719 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-16 12:00:33,719 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34854.78, 'new_value': 37397.78}, {'field': 'total_amount', 'old_value': 34854.78, 'new_value': 37397.78}, {'field': 'order_count', 'old_value': 78, 'new_value': 80}]
2025-05-16 12:00:33,719 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-16 12:00:34,313 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-16 12:00:34,313 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 265190.0, 'new_value': 271371.0}, {'field': 'offline_amount', 'old_value': 146871.0, 'new_value': 172610.0}, {'field': 'total_amount', 'old_value': 412061.0, 'new_value': 443981.0}, {'field': 'order_count', 'old_value': 452, 'new_value': 484}]
2025-05-16 12:00:34,313 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGC
2025-05-16 12:00:34,672 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGC
2025-05-16 12:00:34,672 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29657.0, 'new_value': 36934.0}, {'field': 'total_amount', 'old_value': 29657.0, 'new_value': 36934.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-05-16 12:00:34,672 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMO3
2025-05-16 12:00:35,094 - INFO - 更新表单数据成功: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMO3
2025-05-16 12:00:35,094 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49447.58, 'new_value': 54162.49}, {'field': 'total_amount', 'old_value': 69840.76, 'new_value': 74555.67}, {'field': 'order_count', 'old_value': 1596, 'new_value': 1707}]
2025-05-16 12:00:35,094 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJC
2025-05-16 12:00:35,532 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJC
2025-05-16 12:00:35,532 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1030.9, 'new_value': 1096.9}, {'field': 'total_amount', 'old_value': 25851.7, 'new_value': 25917.7}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-05-16 12:00:35,532 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-16 12:00:36,110 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-16 12:00:36,110 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28938.8, 'new_value': 29248.0}, {'field': 'total_amount', 'old_value': 32898.8, 'new_value': 33208.0}, {'field': 'order_count', 'old_value': 242, 'new_value': 258}]
2025-05-16 12:00:36,110 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-16 12:00:36,563 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-16 12:00:36,563 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97892.0, 'new_value': 109769.0}, {'field': 'total_amount', 'old_value': 97892.0, 'new_value': 109769.0}, {'field': 'order_count', 'old_value': 11912, 'new_value': 35}]
2025-05-16 12:00:36,563 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-16 12:00:37,078 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-16 12:00:37,078 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 231658.48, 'new_value': 250844.48}, {'field': 'total_amount', 'old_value': 231658.48, 'new_value': 250844.48}, {'field': 'order_count', 'old_value': 254, 'new_value': 280}]
2025-05-16 12:00:37,078 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM4A
2025-05-16 12:00:37,657 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM4A
2025-05-16 12:00:37,657 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2568.66, 'new_value': 2696.53}, {'field': 'offline_amount', 'old_value': 49384.49, 'new_value': 52371.65}, {'field': 'total_amount', 'old_value': 51953.15, 'new_value': 55068.18}, {'field': 'order_count', 'old_value': 1987, 'new_value': 2122}]
2025-05-16 12:00:37,657 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-16 12:00:38,203 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-16 12:00:38,203 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 648829.0, 'new_value': 685508.0}, {'field': 'offline_amount', 'old_value': 185366.0, 'new_value': 195578.0}, {'field': 'total_amount', 'old_value': 834195.0, 'new_value': 881086.0}, {'field': 'order_count', 'old_value': 930, 'new_value': 999}]
2025-05-16 12:00:38,203 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6F
2025-05-16 12:00:38,610 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6F
2025-05-16 12:00:38,610 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7065.6, 'new_value': 8112.24}, {'field': 'offline_amount', 'old_value': 23389.59, 'new_value': 24673.89}, {'field': 'total_amount', 'old_value': 30455.19, 'new_value': 32786.13}, {'field': 'order_count', 'old_value': 541, 'new_value': 575}]
2025-05-16 12:00:38,610 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM2Q
2025-05-16 12:00:39,000 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM2Q
2025-05-16 12:00:39,000 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 151615.04, 'new_value': 160388.28}, {'field': 'offline_amount', 'old_value': 10792.15, 'new_value': 11424.15}, {'field': 'total_amount', 'old_value': 162407.19, 'new_value': 171812.43}, {'field': 'order_count', 'old_value': 3026, 'new_value': 3324}]
2025-05-16 12:00:39,016 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-16 12:00:39,500 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-16 12:00:39,500 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 178100.0, 'new_value': 182800.0}, {'field': 'total_amount', 'old_value': 178100.0, 'new_value': 182800.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-05-16 12:00:39,500 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-16 12:00:40,078 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-16 12:00:40,078 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 227420.89, 'new_value': 229220.89}, {'field': 'total_amount', 'old_value': 227420.89, 'new_value': 229220.89}, {'field': 'order_count', 'old_value': 40, 'new_value': 41}]
2025-05-16 12:00:40,078 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-16 12:00:40,578 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-16 12:00:40,578 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 185960.13, 'new_value': 188660.13}, {'field': 'total_amount', 'old_value': 225320.13, 'new_value': 228020.13}, {'field': 'order_count', 'old_value': 32, 'new_value': 33}]
2025-05-16 12:00:40,578 - INFO - 开始更新记录 - 表单实例ID: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMI
2025-05-16 12:00:41,031 - INFO - 更新表单数据成功: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMI
2025-05-16 12:00:41,031 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36561.0, 'new_value': 41587.0}, {'field': 'total_amount', 'old_value': 85956.0, 'new_value': 90982.0}, {'field': 'order_count', 'old_value': 1072, 'new_value': 1150}]
2025-05-16 12:00:41,031 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-16 12:00:41,422 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-16 12:00:41,422 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112560.57, 'new_value': 127041.72}, {'field': 'total_amount', 'old_value': 112560.57, 'new_value': 127041.72}, {'field': 'order_count', 'old_value': 142, 'new_value': 151}]
2025-05-16 12:00:41,422 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A
2025-05-16 12:00:41,922 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A
2025-05-16 12:00:41,922 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58735.21, 'new_value': 61587.83}, {'field': 'total_amount', 'old_value': 58735.21, 'new_value': 61587.83}, {'field': 'order_count', 'old_value': 2200, 'new_value': 2323}]
2025-05-16 12:00:41,922 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-05-16 12:00:42,391 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-05-16 12:00:42,391 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8545.0, 'new_value': 9139.0}, {'field': 'total_amount', 'old_value': 8545.0, 'new_value': 9139.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 24}]
2025-05-16 12:00:42,391 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-16 12:00:42,860 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-16 12:00:42,860 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55154.23, 'new_value': 60546.47}, {'field': 'offline_amount', 'old_value': 32483.06, 'new_value': 33175.26}, {'field': 'total_amount', 'old_value': 87637.29, 'new_value': 93721.73}, {'field': 'order_count', 'old_value': 5028, 'new_value': 5358}]
2025-05-16 12:00:42,860 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-16 12:00:43,360 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-16 12:00:43,360 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41636.0, 'new_value': 44510.0}, {'field': 'total_amount', 'old_value': 41636.0, 'new_value': 44510.0}, {'field': 'order_count', 'old_value': 360, 'new_value': 384}]
2025-05-16 12:00:43,360 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-16 12:00:43,797 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-16 12:00:43,797 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21389.18, 'new_value': 23970.7}, {'field': 'total_amount', 'old_value': 21389.18, 'new_value': 23970.7}, {'field': 'order_count', 'old_value': 1617, 'new_value': 1809}]
2025-05-16 12:00:43,797 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-16 12:00:44,297 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-16 12:00:44,297 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26603.0, 'new_value': 27440.0}, {'field': 'total_amount', 'old_value': 30179.0, 'new_value': 31016.0}, {'field': 'order_count', 'old_value': 127, 'new_value': 133}]
2025-05-16 12:00:44,297 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM7A
2025-05-16 12:00:44,719 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM7A
2025-05-16 12:00:44,719 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24522.79, 'new_value': 26502.31}, {'field': 'offline_amount', 'old_value': 30709.36, 'new_value': 32282.69}, {'field': 'total_amount', 'old_value': 55232.15, 'new_value': 58785.0}, {'field': 'order_count', 'old_value': 2775, 'new_value': 2953}]
2025-05-16 12:00:44,719 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-16 12:00:45,266 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-16 12:00:45,266 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44936.38, 'new_value': 45806.38}, {'field': 'total_amount', 'old_value': 44936.38, 'new_value': 45806.38}, {'field': 'order_count', 'old_value': 76, 'new_value': 77}]
2025-05-16 12:00:45,281 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMMD
2025-05-16 12:00:45,750 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMMD
2025-05-16 12:00:45,750 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4725.0, 'new_value': 6325.0}, {'field': 'total_amount', 'old_value': 4725.0, 'new_value': 6325.0}, {'field': 'order_count', 'old_value': 297, 'new_value': 298}]
2025-05-16 12:00:45,750 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-16 12:00:46,172 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-16 12:00:46,172 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106342.0, 'new_value': 108142.0}, {'field': 'total_amount', 'old_value': 106342.0, 'new_value': 108142.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 40}]
2025-05-16 12:00:46,172 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8F
2025-05-16 12:00:46,610 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8F
2025-05-16 12:00:46,610 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32120.93, 'new_value': 34389.0}, {'field': 'offline_amount', 'old_value': 314419.73, 'new_value': 334836.8}, {'field': 'total_amount', 'old_value': 346540.66, 'new_value': 369225.8}, {'field': 'order_count', 'old_value': 1072, 'new_value': 1150}]
2025-05-16 12:00:46,610 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM8A
2025-05-16 12:00:47,047 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM8A
2025-05-16 12:00:47,047 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5814.67, 'new_value': 6029.16}, {'field': 'offline_amount', 'old_value': 73344.51, 'new_value': 76687.68}, {'field': 'total_amount', 'old_value': 79159.18, 'new_value': 82716.84}, {'field': 'order_count', 'old_value': 1247, 'new_value': 1303}]
2025-05-16 12:00:47,047 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-16 12:00:47,531 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-16 12:00:47,531 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2.0, 'new_value': 11.9}, {'field': 'offline_amount', 'old_value': 29030.7, 'new_value': 29833.8}, {'field': 'total_amount', 'old_value': 29032.7, 'new_value': 29845.7}, {'field': 'order_count', 'old_value': 169, 'new_value': 174}]
2025-05-16 12:00:47,531 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-16 12:00:48,000 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-16 12:00:48,000 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45342.0, 'new_value': 53352.0}, {'field': 'offline_amount', 'old_value': 39942.0, 'new_value': 45828.0}, {'field': 'total_amount', 'old_value': 85284.0, 'new_value': 99180.0}, {'field': 'order_count', 'old_value': 32, 'new_value': 36}]
2025-05-16 12:00:48,000 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-16 12:00:48,500 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-16 12:00:48,500 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17447.0, 'new_value': 18084.0}, {'field': 'total_amount', 'old_value': 18647.0, 'new_value': 19284.0}, {'field': 'order_count', 'old_value': 70, 'new_value': 73}]
2025-05-16 12:00:48,500 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-16 12:00:48,969 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-16 12:00:48,969 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5180.61, 'new_value': 5222.38}, {'field': 'offline_amount', 'old_value': 58492.23, 'new_value': 60386.81}, {'field': 'total_amount', 'old_value': 63672.84, 'new_value': 65609.19}, {'field': 'order_count', 'old_value': 1501, 'new_value': 1559}]
2025-05-16 12:00:48,969 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-16 12:00:49,391 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-16 12:00:49,391 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 116568.0, 'new_value': 122904.0}, {'field': 'total_amount', 'old_value': 116568.0, 'new_value': 122904.0}, {'field': 'order_count', 'old_value': 548, 'new_value': 594}]
2025-05-16 12:00:49,391 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-16 12:00:49,844 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-16 12:00:49,844 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39875.0, 'new_value': 42273.0}, {'field': 'total_amount', 'old_value': 44371.32, 'new_value': 46769.32}, {'field': 'order_count', 'old_value': 434, 'new_value': 436}]
2025-05-16 12:00:49,844 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-16 12:00:50,297 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-16 12:00:50,297 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109859.0, 'new_value': 117164.43}, {'field': 'total_amount', 'old_value': 109859.0, 'new_value': 117164.43}, {'field': 'order_count', 'old_value': 346, 'new_value': 374}]
2025-05-16 12:00:50,297 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAM9D1
2025-05-16 12:00:50,719 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAM9D1
2025-05-16 12:00:50,719 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48112.15, 'new_value': 49833.59}, {'field': 'total_amount', 'old_value': 52921.1, 'new_value': 54642.54}, {'field': 'order_count', 'old_value': 2990, 'new_value': 3102}]
2025-05-16 12:00:50,719 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-16 12:00:51,328 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-16 12:00:51,328 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43604.43, 'new_value': 47334.82}, {'field': 'offline_amount', 'old_value': 24185.19, 'new_value': 25644.97}, {'field': 'total_amount', 'old_value': 67789.62, 'new_value': 72979.79}, {'field': 'order_count', 'old_value': 2289, 'new_value': 2489}]
2025-05-16 12:00:51,328 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-16 12:00:51,813 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-16 12:00:51,813 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47966.64, 'new_value': 50863.85}, {'field': 'offline_amount', 'old_value': 508984.31, 'new_value': 542424.51}, {'field': 'total_amount', 'old_value': 556950.95, 'new_value': 593288.36}, {'field': 'order_count', 'old_value': 1812, 'new_value': 1927}]
2025-05-16 12:00:51,813 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-16 12:00:52,235 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-16 12:00:52,235 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27029.14, 'new_value': 29197.57}, {'field': 'offline_amount', 'old_value': 66960.08, 'new_value': 70703.32}, {'field': 'total_amount', 'old_value': 93989.22, 'new_value': 99900.89}, {'field': 'order_count', 'old_value': 3195, 'new_value': 3423}]
2025-05-16 12:00:52,235 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-16 12:00:52,719 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-16 12:00:52,719 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 90553.0, 'new_value': 93152.0}, {'field': 'offline_amount', 'old_value': 37643.0, 'new_value': 37762.0}, {'field': 'total_amount', 'old_value': 128196.0, 'new_value': 130914.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 50}]
2025-05-16 12:00:52,719 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-05-16 12:00:53,203 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-05-16 12:00:53,203 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25437.44, 'new_value': 25675.4}, {'field': 'total_amount', 'old_value': 25437.44, 'new_value': 25675.4}, {'field': 'order_count', 'old_value': 25, 'new_value': 32}]
2025-05-16 12:00:53,203 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMAD1
2025-05-16 12:00:53,688 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMAD1
2025-05-16 12:00:53,688 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4172.0, 'new_value': 4231.0}, {'field': 'offline_amount', 'old_value': 9601.0, 'new_value': 10989.0}, {'field': 'total_amount', 'old_value': 13773.0, 'new_value': 15220.0}, {'field': 'order_count', 'old_value': 83, 'new_value': 89}]
2025-05-16 12:00:53,688 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCF
2025-05-16 12:00:54,078 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCF
2025-05-16 12:00:54,078 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 150656.96, 'new_value': 163902.03}, {'field': 'total_amount', 'old_value': 151413.96, 'new_value': 164659.03}, {'field': 'order_count', 'old_value': 1783, 'new_value': 1948}]
2025-05-16 12:00:54,078 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-16 12:00:54,563 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-16 12:00:54,563 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1688.84, 'new_value': 2300.07}, {'field': 'offline_amount', 'old_value': 80144.43, 'new_value': 83527.51}, {'field': 'total_amount', 'old_value': 81833.27, 'new_value': 85827.58}, {'field': 'order_count', 'old_value': 377, 'new_value': 398}]
2025-05-16 12:00:54,563 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM3Q
2025-05-16 12:00:55,016 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM3Q
2025-05-16 12:00:55,016 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 58281.41, 'new_value': 65838.58}, {'field': 'offline_amount', 'old_value': 51228.99, 'new_value': 56118.05}, {'field': 'total_amount', 'old_value': 109510.4, 'new_value': 121956.63}, {'field': 'order_count', 'old_value': 3842, 'new_value': 4328}]
2025-05-16 12:00:55,016 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-16 12:00:55,594 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-16 12:00:55,594 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 104606.41, 'new_value': 112071.05}, {'field': 'offline_amount', 'old_value': 282903.18, 'new_value': 291955.81}, {'field': 'total_amount', 'old_value': 387509.59, 'new_value': 404026.86}, {'field': 'order_count', 'old_value': 2663, 'new_value': 2795}]
2025-05-16 12:00:55,594 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-16 12:00:55,985 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-16 12:00:55,985 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18450.7, 'new_value': 19352.7}, {'field': 'total_amount', 'old_value': 18450.7, 'new_value': 19352.7}, {'field': 'order_count', 'old_value': 102, 'new_value': 106}]
2025-05-16 12:00:55,985 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-16 12:00:56,438 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-16 12:00:56,438 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3925.4, 'new_value': 4118.32}, {'field': 'offline_amount', 'old_value': 108230.03, 'new_value': 118185.9}, {'field': 'total_amount', 'old_value': 112155.43, 'new_value': 122304.22}, {'field': 'order_count', 'old_value': 746, 'new_value': 830}]
2025-05-16 12:00:56,438 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM9A
2025-05-16 12:00:56,969 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM9A
2025-05-16 12:00:56,969 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40205.63, 'new_value': 43041.98}, {'field': 'total_amount', 'old_value': 40205.63, 'new_value': 43041.98}, {'field': 'order_count', 'old_value': 1856, 'new_value': 1985}]
2025-05-16 12:00:56,969 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-16 12:00:57,406 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-16 12:00:57,406 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 85139.65, 'new_value': 95092.65}, {'field': 'offline_amount', 'old_value': 53101.0, 'new_value': 59091.0}, {'field': 'total_amount', 'old_value': 138240.65, 'new_value': 154183.65}, {'field': 'order_count', 'old_value': 735, 'new_value': 811}]
2025-05-16 12:00:57,406 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMFE
2025-05-16 12:00:57,860 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMFE
2025-05-16 12:00:57,860 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3437.0, 'new_value': 3576.0}, {'field': 'total_amount', 'old_value': 3437.0, 'new_value': 3576.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-16 12:00:57,860 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMBD1
2025-05-16 12:00:58,375 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMBD1
2025-05-16 12:00:58,375 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109073.0, 'new_value': 115498.0}, {'field': 'total_amount', 'old_value': 109073.0, 'new_value': 115498.0}, {'field': 'order_count', 'old_value': 9466, 'new_value': 9583}]
2025-05-16 12:00:58,375 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-16 12:00:58,828 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-16 12:00:58,828 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1.0, 'new_value': 2.0}, {'field': 'offline_amount', 'old_value': 79276.0, 'new_value': 83811.0}, {'field': 'total_amount', 'old_value': 79277.0, 'new_value': 83813.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 39}]
2025-05-16 12:00:58,828 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-16 12:00:59,313 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-16 12:00:59,313 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92357.0, 'new_value': 95802.0}, {'field': 'total_amount', 'old_value': 99932.8, 'new_value': 103377.8}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-05-16 12:00:59,313 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM4Q
2025-05-16 12:00:59,938 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM4Q
2025-05-16 12:00:59,938 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 116006.23, 'new_value': 127489.29}, {'field': 'total_amount', 'old_value': 122921.89, 'new_value': 134404.95}, {'field': 'order_count', 'old_value': 2483, 'new_value': 2746}]
2025-05-16 12:00:59,938 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-16 12:01:00,422 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-16 12:01:00,422 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23888.92, 'new_value': 25540.49}, {'field': 'offline_amount', 'old_value': 57143.57, 'new_value': 59046.58}, {'field': 'total_amount', 'old_value': 81032.49, 'new_value': 84587.07}, {'field': 'order_count', 'old_value': 2919, 'new_value': 3059}]
2025-05-16 12:01:00,422 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM5Q
2025-05-16 12:01:00,922 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM5Q
2025-05-16 12:01:00,922 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 268630.79, 'new_value': 287965.39}, {'field': 'total_amount', 'old_value': 268630.79, 'new_value': 287965.39}, {'field': 'order_count', 'old_value': 2686, 'new_value': 2883}]
2025-05-16 12:01:00,922 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-16 12:01:01,375 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-16 12:01:01,375 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49212.0, 'new_value': 63237.64}, {'field': 'total_amount', 'old_value': 54353.0, 'new_value': 68378.64}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-05-16 12:01:01,375 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-16 12:01:01,891 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-16 12:01:01,891 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26128.0, 'new_value': 26254.0}, {'field': 'offline_amount', 'old_value': 115282.0, 'new_value': 116776.0}, {'field': 'total_amount', 'old_value': 141410.0, 'new_value': 143030.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 52}]
2025-05-16 12:01:01,891 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-16 12:01:02,359 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-16 12:01:02,359 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 82199.5, 'new_value': 85274.11}, {'field': 'offline_amount', 'old_value': 75270.45, 'new_value': 78069.45}, {'field': 'total_amount', 'old_value': 157469.95, 'new_value': 163343.56}, {'field': 'order_count', 'old_value': 1563, 'new_value': 1628}]
2025-05-16 12:01:02,359 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-16 12:01:02,781 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-16 12:01:02,781 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85204.0, 'new_value': 90203.0}, {'field': 'total_amount', 'old_value': 108826.48, 'new_value': 113825.48}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-05-16 12:01:02,781 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-16 12:01:03,219 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-16 12:01:03,219 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5189.2, 'new_value': 5244.9}, {'field': 'offline_amount', 'old_value': 29376.0, 'new_value': 30176.0}, {'field': 'total_amount', 'old_value': 34565.2, 'new_value': 35420.9}, {'field': 'order_count', 'old_value': 39, 'new_value': 41}]
2025-05-16 12:01:03,219 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-16 12:01:03,734 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-16 12:01:03,734 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 216821.0, 'new_value': 387411.0}, {'field': 'total_amount', 'old_value': 216821.0, 'new_value': 387411.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 37}]
2025-05-16 12:01:03,734 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-16 12:01:04,250 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-16 12:01:04,250 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35060.0, 'new_value': 36478.0}, {'field': 'total_amount', 'old_value': 35408.0, 'new_value': 36826.0}, {'field': 'order_count', 'old_value': 71, 'new_value': 74}]
2025-05-16 12:01:04,250 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-16 12:01:04,797 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-16 12:01:04,797 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 485663.0, 'new_value': 528048.0}, {'field': 'total_amount', 'old_value': 485663.0, 'new_value': 528048.0}, {'field': 'order_count', 'old_value': 87, 'new_value': 93}]
2025-05-16 12:01:04,797 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-16 12:01:05,266 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-16 12:01:05,266 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 129742.45, 'new_value': 133843.3}, {'field': 'total_amount', 'old_value': 190605.05, 'new_value': 194705.9}, {'field': 'order_count', 'old_value': 2201, 'new_value': 2262}]
2025-05-16 12:01:05,266 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-16 12:01:05,734 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-16 12:01:05,734 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58764.5, 'new_value': 60203.27}, {'field': 'total_amount', 'old_value': 58764.5, 'new_value': 60203.27}, {'field': 'order_count', 'old_value': 2249, 'new_value': 2318}]
2025-05-16 12:01:05,734 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-16 12:01:06,234 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-16 12:01:06,234 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20027.26, 'new_value': 21094.26}, {'field': 'offline_amount', 'old_value': 336272.86, 'new_value': 347698.08}, {'field': 'total_amount', 'old_value': 356300.12, 'new_value': 368792.34}, {'field': 'order_count', 'old_value': 1965, 'new_value': 2071}]
2025-05-16 12:01:06,250 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-16 12:01:06,656 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-16 12:01:06,656 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47023.58, 'new_value': 52363.82}, {'field': 'total_amount', 'old_value': 47023.58, 'new_value': 52363.82}, {'field': 'order_count', 'old_value': 44, 'new_value': 50}]
2025-05-16 12:01:06,656 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-16 12:01:07,125 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-16 12:01:07,125 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44901.79, 'new_value': 50027.79}, {'field': 'offline_amount', 'old_value': 646556.38, 'new_value': 684173.6}, {'field': 'total_amount', 'old_value': 691458.17, 'new_value': 734201.39}, {'field': 'order_count', 'old_value': 5479, 'new_value': 5889}]
2025-05-16 12:01:07,125 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-16 12:01:07,656 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-16 12:01:07,656 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6142.04, 'new_value': 6577.24}, {'field': 'total_amount', 'old_value': 15232.26, 'new_value': 15667.46}, {'field': 'order_count', 'old_value': 63, 'new_value': 65}]
2025-05-16 12:01:07,656 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-16 12:01:08,094 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-16 12:01:08,094 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74746.2, 'new_value': 77759.87}, {'field': 'total_amount', 'old_value': 74746.2, 'new_value': 77759.87}, {'field': 'order_count', 'old_value': 480, 'new_value': 497}]
2025-05-16 12:01:08,094 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-16 12:01:08,563 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-16 12:01:08,563 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 50471.18, 'new_value': 53122.18}, {'field': 'total_amount', 'old_value': 50471.18, 'new_value': 53122.18}, {'field': 'order_count', 'old_value': 281, 'new_value': 298}]
2025-05-16 12:01:08,563 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM7
2025-05-16 12:01:08,953 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM7
2025-05-16 12:01:08,953 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1231.41, 'new_value': 1395.32}, {'field': 'offline_amount', 'old_value': 14335.34, 'new_value': 14662.94}, {'field': 'total_amount', 'old_value': 15566.75, 'new_value': 16058.26}, {'field': 'order_count', 'old_value': 705, 'new_value': 729}]
2025-05-16 12:01:08,953 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-16 12:01:09,422 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-16 12:01:09,422 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3228.0, 'new_value': 3878.0}, {'field': 'total_amount', 'old_value': 3228.0, 'new_value': 3878.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 21}]
2025-05-16 12:01:09,422 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-16 12:01:09,906 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-16 12:01:09,906 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62237.0, 'new_value': 65409.5}, {'field': 'total_amount', 'old_value': 62237.0, 'new_value': 65409.5}, {'field': 'order_count', 'old_value': 190, 'new_value': 201}]
2025-05-16 12:01:09,906 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMCD1
2025-05-16 12:01:10,359 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMCD1
2025-05-16 12:01:10,359 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7050.96, 'new_value': 7413.72}, {'field': 'offline_amount', 'old_value': 71109.23, 'new_value': 75184.36}, {'field': 'total_amount', 'old_value': 78160.19, 'new_value': 82598.08}, {'field': 'order_count', 'old_value': 1960, 'new_value': 2076}]
2025-05-16 12:01:10,359 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-16 12:01:10,797 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-16 12:01:10,797 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 92809.93, 'new_value': 99201.17}, {'field': 'offline_amount', 'old_value': 217466.76, 'new_value': 224513.67}, {'field': 'total_amount', 'old_value': 310276.69, 'new_value': 323714.84}, {'field': 'order_count', 'old_value': 8100, 'new_value': 8583}]
2025-05-16 12:01:10,797 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-16 12:01:11,328 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-16 12:01:11,328 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30216.0, 'new_value': 31549.0}, {'field': 'total_amount', 'old_value': 30216.0, 'new_value': 31549.0}, {'field': 'order_count', 'old_value': 77, 'new_value': 82}]
2025-05-16 12:01:11,328 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-16 12:01:11,875 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-16 12:01:11,875 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23616.0, 'new_value': 23795.0}, {'field': 'total_amount', 'old_value': 23616.0, 'new_value': 23795.0}, {'field': 'order_count', 'old_value': 72, 'new_value': 73}]
2025-05-16 12:01:11,875 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-16 12:01:12,313 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-16 12:01:12,313 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 718000.0, 'new_value': 753000.0}, {'field': 'total_amount', 'old_value': 718000.0, 'new_value': 753000.0}, {'field': 'order_count', 'old_value': 333, 'new_value': 334}]
2025-05-16 12:01:12,313 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-16 12:01:12,734 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-16 12:01:12,734 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16961.5, 'new_value': 18035.4}, {'field': 'offline_amount', 'old_value': 13480.7, 'new_value': 14266.7}, {'field': 'total_amount', 'old_value': 30442.2, 'new_value': 32302.1}, {'field': 'order_count', 'old_value': 163, 'new_value': 171}]
2025-05-16 12:01:12,734 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEF
2025-05-16 12:01:13,172 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEF
2025-05-16 12:01:13,172 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10669.34, 'new_value': 11251.08}, {'field': 'offline_amount', 'old_value': 18615.26, 'new_value': 20291.63}, {'field': 'total_amount', 'old_value': 29284.6, 'new_value': 31542.71}, {'field': 'order_count', 'old_value': 1221, 'new_value': 1326}]
2025-05-16 12:01:13,172 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM5H2
2025-05-16 12:01:13,594 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM5H2
2025-05-16 12:01:13,594 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 543573.16, 'new_value': 564039.16}, {'field': 'total_amount', 'old_value': 543573.16, 'new_value': 564039.16}, {'field': 'order_count', 'old_value': 2048, 'new_value': 2155}]
2025-05-16 12:01:13,594 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-16 12:01:14,047 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-16 12:01:14,047 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30796.0, 'new_value': 31509.0}, {'field': 'total_amount', 'old_value': 32312.0, 'new_value': 33025.0}, {'field': 'order_count', 'old_value': 128, 'new_value': 131}]
2025-05-16 12:01:14,047 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-16 12:01:14,531 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-16 12:01:14,531 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 199359.0, 'new_value': 203781.0}, {'field': 'total_amount', 'old_value': 199359.0, 'new_value': 203781.0}, {'field': 'order_count', 'old_value': 167, 'new_value': 172}]
2025-05-16 12:01:14,531 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-16 12:01:15,016 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-16 12:01:15,016 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15792.1, 'new_value': 16218.5}, {'field': 'offline_amount', 'old_value': 153417.6, 'new_value': 157850.8}, {'field': 'total_amount', 'old_value': 169209.7, 'new_value': 174069.3}, {'field': 'order_count', 'old_value': 1314, 'new_value': 1375}]
2025-05-16 12:01:15,016 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-16 12:01:15,469 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-16 12:01:15,469 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8582.61, 'new_value': 9113.62}, {'field': 'offline_amount', 'old_value': 146340.04, 'new_value': 155657.94}, {'field': 'total_amount', 'old_value': 154922.65, 'new_value': 164771.56}, {'field': 'order_count', 'old_value': 8416, 'new_value': 9052}]
2025-05-16 12:01:15,469 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-16 12:01:15,922 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-16 12:01:15,922 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 165828.11, 'new_value': 179146.76}, {'field': 'total_amount', 'old_value': 165828.11, 'new_value': 179146.76}, {'field': 'order_count', 'old_value': 4430, 'new_value': 4807}]
2025-05-16 12:01:15,922 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-16 12:01:16,359 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-16 12:01:16,359 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 191857.6, 'new_value': 200955.6}, {'field': 'total_amount', 'old_value': 191857.6, 'new_value': 200955.6}, {'field': 'order_count', 'old_value': 2164, 'new_value': 2223}]
2025-05-16 12:01:16,359 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-16 12:01:16,828 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-16 12:01:16,828 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28330.44, 'new_value': 31065.47}, {'field': 'offline_amount', 'old_value': 18064.0, 'new_value': 19311.0}, {'field': 'total_amount', 'old_value': 46394.44, 'new_value': 50376.47}, {'field': 'order_count', 'old_value': 589, 'new_value': 637}]
2025-05-16 12:01:16,828 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-16 12:01:17,297 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-16 12:01:17,297 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82888.75, 'new_value': 87888.75}, {'field': 'total_amount', 'old_value': 82888.75, 'new_value': 87888.75}, {'field': 'order_count', 'old_value': 7539, 'new_value': 7540}]
2025-05-16 12:01:17,297 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-16 12:01:17,719 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-16 12:01:17,719 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 166567.9, 'new_value': 170122.3}, {'field': 'total_amount', 'old_value': 166567.9, 'new_value': 170122.3}, {'field': 'order_count', 'old_value': 274, 'new_value': 282}]
2025-05-16 12:01:17,719 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTU
2025-05-16 12:01:18,203 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTU
2025-05-16 12:01:18,203 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44922.0, 'new_value': 49002.0}, {'field': 'total_amount', 'old_value': 44922.0, 'new_value': 49002.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-16 12:01:18,203 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-16 12:01:18,609 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-16 12:01:18,609 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 128632.0, 'new_value': 130433.0}, {'field': 'total_amount', 'old_value': 128632.0, 'new_value': 130433.0}, {'field': 'order_count', 'old_value': 85, 'new_value': 88}]
2025-05-16 12:01:18,609 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTL
2025-05-16 12:01:19,031 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTL
2025-05-16 12:01:19,031 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6509.0, 'new_value': 7507.0}, {'field': 'total_amount', 'old_value': 6509.0, 'new_value': 7507.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-05-16 12:01:19,031 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-16 12:01:19,484 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-16 12:01:19,484 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55010.17, 'new_value': 58363.09}, {'field': 'offline_amount', 'old_value': 40808.98, 'new_value': 43196.95}, {'field': 'total_amount', 'old_value': 95819.15, 'new_value': 101560.04}, {'field': 'order_count', 'old_value': 3928, 'new_value': 4192}]
2025-05-16 12:01:19,484 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-16 12:01:19,937 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-16 12:01:19,937 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 64635.07, 'new_value': 67400.19}, {'field': 'total_amount', 'old_value': 70246.59, 'new_value': 73011.71}, {'field': 'order_count', 'old_value': 6258, 'new_value': 6454}]
2025-05-16 12:01:19,937 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-16 12:01:20,391 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-16 12:01:20,391 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5144.75, 'new_value': 5340.92}, {'field': 'offline_amount', 'old_value': 51184.95, 'new_value': 53192.49}, {'field': 'total_amount', 'old_value': 56329.7, 'new_value': 58533.41}, {'field': 'order_count', 'old_value': 1676, 'new_value': 1724}]
2025-05-16 12:01:20,391 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-16 12:01:20,937 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-16 12:01:20,937 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78017.3, 'new_value': 79274.3}, {'field': 'total_amount', 'old_value': 78017.3, 'new_value': 79274.3}, {'field': 'order_count', 'old_value': 806, 'new_value': 817}]
2025-05-16 12:01:20,937 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-16 12:01:21,344 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-16 12:01:21,344 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64700.0, 'new_value': 68520.0}, {'field': 'total_amount', 'old_value': 64700.0, 'new_value': 68520.0}, {'field': 'order_count', 'old_value': 3319, 'new_value': 3445}]
2025-05-16 12:01:21,344 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM6Q
2025-05-16 12:01:21,797 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM6Q
2025-05-16 12:01:21,797 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24840.58, 'new_value': 26689.39}, {'field': 'offline_amount', 'old_value': 141167.29, 'new_value': 150290.49}, {'field': 'total_amount', 'old_value': 166007.87, 'new_value': 176979.88}, {'field': 'order_count', 'old_value': 5097, 'new_value': 5454}]
2025-05-16 12:01:21,797 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-16 12:01:22,219 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-16 12:01:22,219 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20988.1, 'new_value': 21196.0}, {'field': 'total_amount', 'old_value': 22775.0, 'new_value': 22982.9}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-05-16 12:01:22,219 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-16 12:01:22,625 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-16 12:01:22,641 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4290.85, 'new_value': 4636.79}, {'field': 'offline_amount', 'old_value': 7887.64, 'new_value': 8034.24}, {'field': 'total_amount', 'old_value': 12178.49, 'new_value': 12671.03}, {'field': 'order_count', 'old_value': 910, 'new_value': 953}]
2025-05-16 12:01:22,641 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-16 12:01:23,062 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-16 12:01:23,062 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3547856.0, 'new_value': 3737856.0}, {'field': 'total_amount', 'old_value': 3547856.0, 'new_value': 3737856.0}, {'field': 'order_count', 'old_value': 59695, 'new_value': 59696}]
2025-05-16 12:01:23,062 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-16 12:01:23,562 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-16 12:01:23,562 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50592.0, 'new_value': 51845.0}, {'field': 'total_amount', 'old_value': 50592.0, 'new_value': 51845.0}, {'field': 'order_count', 'old_value': 63, 'new_value': 65}]
2025-05-16 12:01:23,562 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYU
2025-05-16 12:01:24,047 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYU
2025-05-16 12:01:24,047 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21692.0, 'new_value': 23571.0}, {'field': 'total_amount', 'old_value': 21692.0, 'new_value': 23571.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-05-16 12:01:24,047 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-16 12:01:24,562 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-16 12:01:24,562 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29152.23, 'new_value': 30216.1}, {'field': 'offline_amount', 'old_value': 244467.82, 'new_value': 249848.56}, {'field': 'total_amount', 'old_value': 273620.05, 'new_value': 280064.66}, {'field': 'order_count', 'old_value': 2292, 'new_value': 2361}]
2025-05-16 12:01:24,562 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM8
2025-05-16 12:01:25,203 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM8
2025-05-16 12:01:25,203 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50530.74, 'new_value': 54030.74}, {'field': 'total_amount', 'old_value': 50530.74, 'new_value': 54030.74}, {'field': 'order_count', 'old_value': 1650, 'new_value': 1651}]
2025-05-16 12:01:25,203 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-16 12:01:25,672 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-16 12:01:25,672 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69336.0, 'new_value': 73336.0}, {'field': 'total_amount', 'old_value': 69336.0, 'new_value': 73336.0}, {'field': 'order_count', 'old_value': 2683, 'new_value': 2684}]
2025-05-16 12:01:25,672 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-16 12:01:26,094 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-16 12:01:26,094 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 51387.72, 'new_value': 55496.77}, {'field': 'offline_amount', 'old_value': 25448.2, 'new_value': 25999.94}, {'field': 'total_amount', 'old_value': 76835.92, 'new_value': 81496.71}, {'field': 'order_count', 'old_value': 4923, 'new_value': 5171}]
2025-05-16 12:01:26,094 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-16 12:01:26,609 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-16 12:01:26,609 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17524.07, 'new_value': 18624.07}, {'field': 'total_amount', 'old_value': 17524.07, 'new_value': 18624.07}, {'field': 'order_count', 'old_value': 1732, 'new_value': 1733}]
2025-05-16 12:01:26,609 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFF
2025-05-16 12:01:27,094 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFF
2025-05-16 12:01:27,094 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 93854.42, 'new_value': 96382.82}, {'field': 'total_amount', 'old_value': 93854.42, 'new_value': 96382.82}, {'field': 'order_count', 'old_value': 152, 'new_value': 160}]
2025-05-16 12:01:27,094 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-16 12:01:27,516 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-16 12:01:27,516 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 121620.0, 'new_value': 130164.0}, {'field': 'total_amount', 'old_value': 121620.0, 'new_value': 130164.0}, {'field': 'order_count', 'old_value': 10135, 'new_value': 10847}]
2025-05-16 12:01:27,516 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMBA
2025-05-16 12:01:28,016 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMBA
2025-05-16 12:01:28,016 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22030.8, 'new_value': 24198.8}, {'field': 'offline_amount', 'old_value': 18167.71, 'new_value': 18997.71}, {'field': 'total_amount', 'old_value': 40198.51, 'new_value': 43196.51}, {'field': 'order_count', 'old_value': 6363, 'new_value': 6569}]
2025-05-16 12:01:28,016 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-16 12:01:28,453 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-16 12:01:28,453 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68844.68, 'new_value': 71373.68}, {'field': 'total_amount', 'old_value': 68844.68, 'new_value': 71373.68}, {'field': 'order_count', 'old_value': 572, 'new_value': 597}]
2025-05-16 12:01:28,453 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-16 12:01:28,875 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-16 12:01:28,875 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52125.34, 'new_value': 56205.28}, {'field': 'offline_amount', 'old_value': 150830.52, 'new_value': 156317.16}, {'field': 'total_amount', 'old_value': 202955.86, 'new_value': 212522.44}, {'field': 'order_count', 'old_value': 6518, 'new_value': 6904}]
2025-05-16 12:01:28,875 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM9
2025-05-16 12:01:29,297 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM9
2025-05-16 12:01:29,297 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67365.1, 'new_value': 70631.06}, {'field': 'offline_amount', 'old_value': 182423.98, 'new_value': 191487.53}, {'field': 'total_amount', 'old_value': 249789.08, 'new_value': 262118.59}, {'field': 'order_count', 'old_value': 2818, 'new_value': 3055}]
2025-05-16 12:01:29,297 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-16 12:01:29,797 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-16 12:01:29,797 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4647200.0, 'new_value': 4692200.0}, {'field': 'total_amount', 'old_value': 4647200.0, 'new_value': 4692200.0}, {'field': 'order_count', 'old_value': 57, 'new_value': 58}]
2025-05-16 12:01:29,797 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMCA
2025-05-16 12:01:30,187 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMCA
2025-05-16 12:01:30,187 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27567.5, 'new_value': 29382.5}, {'field': 'total_amount', 'old_value': 27567.5, 'new_value': 29382.5}, {'field': 'order_count', 'old_value': 1376, 'new_value': 1473}]
2025-05-16 12:01:30,187 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-16 12:01:30,609 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-16 12:01:30,609 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 174800.96, 'new_value': 184749.33}, {'field': 'offline_amount', 'old_value': 10865.9, 'new_value': 11153.3}, {'field': 'total_amount', 'old_value': 185666.86, 'new_value': 195902.63}, {'field': 'order_count', 'old_value': 6906, 'new_value': 7305}]
2025-05-16 12:01:30,609 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-16 12:01:31,062 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-16 12:01:31,062 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63289.8, 'new_value': 71245.8}, {'field': 'total_amount', 'old_value': 79859.3, 'new_value': 87815.3}, {'field': 'order_count', 'old_value': 114, 'new_value': 117}]
2025-05-16 12:01:31,062 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMDA
2025-05-16 12:01:31,531 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMDA
2025-05-16 12:01:31,531 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9608.91, 'new_value': 10702.55}, {'field': 'offline_amount', 'old_value': 144460.96, 'new_value': 147837.76}, {'field': 'total_amount', 'old_value': 154069.87, 'new_value': 158540.31}, {'field': 'order_count', 'old_value': 2022, 'new_value': 2143}]
2025-05-16 12:01:31,531 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMA
2025-05-16 12:01:32,016 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMA
2025-05-16 12:01:32,016 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26894.0, 'new_value': 28590.0}, {'field': 'total_amount', 'old_value': 26894.0, 'new_value': 28590.0}, {'field': 'order_count', 'old_value': 139, 'new_value': 154}]
2025-05-16 12:01:32,016 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-16 12:01:32,516 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-16 12:01:32,516 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 188660.0, 'new_value': 193660.0}, {'field': 'total_amount', 'old_value': 197478.99, 'new_value': 202478.99}, {'field': 'order_count', 'old_value': 34, 'new_value': 35}]
2025-05-16 12:01:32,516 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-16 12:01:32,969 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-16 12:01:32,969 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67983.64, 'new_value': 72451.42}, {'field': 'offline_amount', 'old_value': 165405.55, 'new_value': 171152.35}, {'field': 'total_amount', 'old_value': 233389.19, 'new_value': 243603.77}, {'field': 'order_count', 'old_value': 2867, 'new_value': 3052}]
2025-05-16 12:01:32,969 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMM
2025-05-16 12:01:33,484 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMM
2025-05-16 12:01:33,484 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22997.0, 'new_value': 23926.0}, {'field': 'offline_amount', 'old_value': 144217.0, 'new_value': 160197.0}, {'field': 'total_amount', 'old_value': 167214.0, 'new_value': 184123.0}, {'field': 'order_count', 'old_value': 148, 'new_value': 164}]
2025-05-16 12:01:33,484 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-16 12:01:33,906 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-16 12:01:33,906 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 141948.0, 'new_value': 158266.0}, {'field': 'total_amount', 'old_value': 141948.0, 'new_value': 158266.0}, {'field': 'order_count', 'old_value': 66, 'new_value': 74}]
2025-05-16 12:01:33,906 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-16 12:01:34,375 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-16 12:01:34,375 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51929.2, 'new_value': 53801.5}, {'field': 'total_amount', 'old_value': 51929.2, 'new_value': 53801.5}, {'field': 'order_count', 'old_value': 391, 'new_value': 407}]
2025-05-16 12:01:34,375 - INFO - 开始更新记录 - 表单实例ID: FINST-8LC66GC10PZUU9MTETB405L5IF663D3CEZ6AMYB
2025-05-16 12:01:34,859 - INFO - 更新表单数据成功: FINST-8LC66GC10PZUU9MTETB405L5IF663D3CEZ6AMYB
2025-05-16 12:01:34,859 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 167273.85, 'new_value': 180377.73}, {'field': 'total_amount', 'old_value': 200354.06, 'new_value': 213457.94}, {'field': 'order_count', 'old_value': 8343, 'new_value': 8914}]
2025-05-16 12:01:34,859 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMDD1
2025-05-16 12:01:35,312 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMDD1
2025-05-16 12:01:35,312 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 226427.0, 'new_value': 250660.0}, {'field': 'total_amount', 'old_value': 226427.0, 'new_value': 250660.0}, {'field': 'order_count', 'old_value': 6134, 'new_value': 6824}]
2025-05-16 12:01:35,312 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-16 12:01:35,766 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-16 12:01:35,766 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 257666.2, 'new_value': 257866.2}, {'field': 'total_amount', 'old_value': 257666.2, 'new_value': 257866.2}, {'field': 'order_count', 'old_value': 499, 'new_value': 500}]
2025-05-16 12:01:35,766 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-16 12:01:36,203 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-16 12:01:36,203 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45340.2, 'new_value': 46404.2}, {'field': 'total_amount', 'old_value': 45340.2, 'new_value': 46404.2}, {'field': 'order_count', 'old_value': 123, 'new_value': 126}]
2025-05-16 12:01:36,203 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM7Q
2025-05-16 12:01:36,734 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM7Q
2025-05-16 12:01:36,734 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58645.4, 'new_value': 63573.74}, {'field': 'total_amount', 'old_value': 58645.4, 'new_value': 63573.74}, {'field': 'order_count', 'old_value': 1628, 'new_value': 1789}]
2025-05-16 12:01:36,734 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM
2025-05-16 12:01:37,234 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM
2025-05-16 12:01:37,234 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11948.0, 'new_value': 13102.0}, {'field': 'total_amount', 'old_value': 11948.0, 'new_value': 13102.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 23}]
2025-05-16 12:01:37,234 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-16 12:01:37,734 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-16 12:01:37,734 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24023.82, 'new_value': 25403.42}, {'field': 'total_amount', 'old_value': 24023.82, 'new_value': 25403.42}, {'field': 'order_count', 'old_value': 97, 'new_value': 103}]
2025-05-16 12:01:37,734 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM8Q
2025-05-16 12:01:38,187 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM8Q
2025-05-16 12:01:38,187 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106102.91, 'new_value': 117540.91}, {'field': 'total_amount', 'old_value': 106102.91, 'new_value': 117540.91}, {'field': 'order_count', 'old_value': 4433, 'new_value': 4944}]
2025-05-16 12:01:38,187 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-16 12:01:38,641 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-16 12:01:38,641 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42601.0, 'new_value': 46706.0}, {'field': 'total_amount', 'old_value': 47097.0, 'new_value': 51202.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 30}]
2025-05-16 12:01:38,641 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-16 12:01:39,078 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-16 12:01:39,078 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45674.0, 'new_value': 47674.0}, {'field': 'total_amount', 'old_value': 45674.0, 'new_value': 47674.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-05-16 12:01:39,078 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMFA
2025-05-16 12:01:39,484 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMFA
2025-05-16 12:01:39,484 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13208.72, 'new_value': 17394.71}, {'field': 'offline_amount', 'old_value': 87344.74, 'new_value': 89961.14}, {'field': 'total_amount', 'old_value': 100553.46, 'new_value': 107355.85}, {'field': 'order_count', 'old_value': 5720, 'new_value': 6103}]
2025-05-16 12:01:39,484 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-16 12:01:39,906 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-16 12:01:39,906 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19755.0, 'new_value': 20686.0}, {'field': 'total_amount', 'old_value': 19755.0, 'new_value': 20686.0}, {'field': 'order_count', 'old_value': 186, 'new_value': 193}]
2025-05-16 12:01:39,906 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-16 12:01:40,390 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-16 12:01:40,390 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 469246.7, 'new_value': 481165.7}, {'field': 'total_amount', 'old_value': 469246.7, 'new_value': 481165.7}, {'field': 'order_count', 'old_value': 1206, 'new_value': 1252}]
2025-05-16 12:01:40,390 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM9Q
2025-05-16 12:01:40,875 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM9Q
2025-05-16 12:01:40,875 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17494.55, 'new_value': 18752.54}, {'field': 'offline_amount', 'old_value': 35607.36, 'new_value': 38321.96}, {'field': 'total_amount', 'old_value': 53101.91, 'new_value': 57074.5}, {'field': 'order_count', 'old_value': 448, 'new_value': 480}]
2025-05-16 12:01:40,875 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-16 12:01:41,312 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-16 12:01:41,312 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 386083.0, 'new_value': 400977.0}, {'field': 'total_amount', 'old_value': 386083.0, 'new_value': 400977.0}, {'field': 'order_count', 'old_value': 54, 'new_value': 58}]
2025-05-16 12:01:41,312 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-16 12:01:41,765 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-16 12:01:41,765 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47713.35, 'new_value': 49841.13}, {'field': 'offline_amount', 'old_value': 78735.58, 'new_value': 80688.98}, {'field': 'total_amount', 'old_value': 126448.93, 'new_value': 130530.11}, {'field': 'order_count', 'old_value': 1247, 'new_value': 1285}]
2025-05-16 12:01:41,765 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-16 12:01:42,203 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-16 12:01:42,203 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 938296.03, 'new_value': 1015766.93}, {'field': 'total_amount', 'old_value': 1078659.33, 'new_value': 1156130.23}, {'field': 'order_count', 'old_value': 3796, 'new_value': 4074}]
2025-05-16 12:01:42,203 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-16 12:01:42,625 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-16 12:01:42,625 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17650.0, 'new_value': 18650.0}, {'field': 'total_amount', 'old_value': 19026.0, 'new_value': 20026.0}, {'field': 'order_count', 'old_value': 2079, 'new_value': 2080}]
2025-05-16 12:01:42,625 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-05-16 12:01:43,015 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-05-16 12:01:43,015 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5892.0, 'new_value': 7003.0}, {'field': 'total_amount', 'old_value': 5892.0, 'new_value': 7003.0}, {'field': 'order_count', 'old_value': 43, 'new_value': 52}]
2025-05-16 12:01:43,015 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-16 12:01:43,500 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-16 12:01:43,500 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5729.6, 'new_value': 6274.6}, {'field': 'total_amount', 'old_value': 6557.6, 'new_value': 7102.6}, {'field': 'order_count', 'old_value': 67, 'new_value': 72}]
2025-05-16 12:01:43,500 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-16 12:01:43,969 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-16 12:01:43,969 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 385435.0, 'new_value': 387381.0}, {'field': 'total_amount', 'old_value': 385875.0, 'new_value': 387821.0}, {'field': 'order_count', 'old_value': 167, 'new_value': 170}]
2025-05-16 12:01:43,969 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-16 12:01:44,375 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-16 12:01:44,375 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18731.0, 'new_value': 19863.0}, {'field': 'total_amount', 'old_value': 18731.0, 'new_value': 19863.0}, {'field': 'order_count', 'old_value': 81, 'new_value': 85}]
2025-05-16 12:01:44,375 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMB
2025-05-16 12:01:44,812 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMB
2025-05-16 12:01:44,812 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49310.0, 'new_value': 51630.0}, {'field': 'total_amount', 'old_value': 49310.0, 'new_value': 51630.0}, {'field': 'order_count', 'old_value': 344, 'new_value': 360}]
2025-05-16 12:01:44,812 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-16 12:01:45,265 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-16 12:01:45,265 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 58536.8, 'new_value': 59926.4}, {'field': 'offline_amount', 'old_value': 87547.4, 'new_value': 89550.1}, {'field': 'total_amount', 'old_value': 146084.2, 'new_value': 149476.5}, {'field': 'order_count', 'old_value': 2942, 'new_value': 3010}]
2025-05-16 12:01:45,265 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-16 12:01:45,719 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-16 12:01:45,719 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 284615.94, 'new_value': 296566.56}, {'field': 'total_amount', 'old_value': 284615.94, 'new_value': 296566.56}, {'field': 'order_count', 'old_value': 3830, 'new_value': 4019}]
2025-05-16 12:01:45,719 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-16 12:01:46,281 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-16 12:01:46,281 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6502.4, 'new_value': 6898.4}, {'field': 'offline_amount', 'old_value': 6169.9, 'new_value': 6499.9}, {'field': 'total_amount', 'old_value': 12672.3, 'new_value': 13398.3}, {'field': 'order_count', 'old_value': 56, 'new_value': 61}]
2025-05-16 12:01:46,281 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-16 12:01:46,734 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-16 12:01:46,734 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 119918.0, 'new_value': 123785.0}, {'field': 'total_amount', 'old_value': 119918.0, 'new_value': 123785.0}, {'field': 'order_count', 'old_value': 192, 'new_value': 197}]
2025-05-16 12:01:46,734 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-16 12:01:47,172 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-16 12:01:47,172 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 349992.7, 'new_value': 376925.04}, {'field': 'total_amount', 'old_value': 350519.71, 'new_value': 377452.05}, {'field': 'order_count', 'old_value': 822, 'new_value': 887}]
2025-05-16 12:01:47,172 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-16 12:01:47,625 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-16 12:01:47,625 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96750.0, 'new_value': 103010.0}, {'field': 'total_amount', 'old_value': 96750.0, 'new_value': 103010.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-05-16 12:01:47,625 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-16 12:01:48,078 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-16 12:01:48,078 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53617.96, 'new_value': 55683.62}, {'field': 'total_amount', 'old_value': 53617.96, 'new_value': 55683.62}, {'field': 'order_count', 'old_value': 1616, 'new_value': 1682}]
2025-05-16 12:01:48,078 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-16 12:01:48,547 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-16 12:01:48,547 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5509.4, 'new_value': 5788.46}, {'field': 'offline_amount', 'old_value': 17890.78, 'new_value': 18832.88}, {'field': 'total_amount', 'old_value': 23400.18, 'new_value': 24621.34}, {'field': 'order_count', 'old_value': 829, 'new_value': 869}]
2025-05-16 12:01:48,547 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-16 12:01:49,015 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-16 12:01:49,015 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 152855.0, 'new_value': 163239.0}, {'field': 'total_amount', 'old_value': 152855.0, 'new_value': 163239.0}, {'field': 'order_count', 'old_value': 35, 'new_value': 38}]
2025-05-16 12:01:49,015 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-16 12:01:49,469 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-16 12:01:49,469 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89598.12, 'new_value': 91791.54}, {'field': 'total_amount', 'old_value': 89598.12, 'new_value': 91791.54}, {'field': 'order_count', 'old_value': 2281, 'new_value': 2355}]
2025-05-16 12:01:49,469 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-16 12:01:49,890 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-16 12:01:49,890 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20023.28, 'new_value': 20719.09}, {'field': 'offline_amount', 'old_value': 200858.69, 'new_value': 206717.47}, {'field': 'total_amount', 'old_value': 220881.97, 'new_value': 227436.56}, {'field': 'order_count', 'old_value': 5133, 'new_value': 5273}]
2025-05-16 12:01:49,890 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-16 12:01:50,484 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-16 12:01:50,484 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 203648.11, 'new_value': 210991.41}, {'field': 'total_amount', 'old_value': 203648.11, 'new_value': 210991.41}, {'field': 'order_count', 'old_value': 1760, 'new_value': 1846}]
2025-05-16 12:01:50,484 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMP3
2025-05-16 12:01:50,953 - INFO - 更新表单数据成功: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMP3
2025-05-16 12:01:50,953 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5047.79, 'new_value': 5355.13}, {'field': 'offline_amount', 'old_value': 71098.1, 'new_value': 75552.5}, {'field': 'total_amount', 'old_value': 76145.89, 'new_value': 80907.63}, {'field': 'order_count', 'old_value': 4519, 'new_value': 4714}]
2025-05-16 12:01:50,953 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-16 12:01:51,515 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-16 12:01:51,515 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 279013.4, 'new_value': 289122.9}, {'field': 'total_amount', 'old_value': 279013.4, 'new_value': 289122.9}, {'field': 'order_count', 'old_value': 1378, 'new_value': 1427}]
2025-05-16 12:01:51,515 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-16 12:01:52,031 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-16 12:01:52,031 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 88163.25, 'new_value': 91508.5}, {'field': 'offline_amount', 'old_value': 31824.1, 'new_value': 33208.1}, {'field': 'total_amount', 'old_value': 119987.35, 'new_value': 124716.6}, {'field': 'order_count', 'old_value': 7554, 'new_value': 7839}]
2025-05-16 12:01:52,031 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMC
2025-05-16 12:01:52,469 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMC
2025-05-16 12:01:52,469 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10632.76, 'new_value': 10853.96}, {'field': 'offline_amount', 'old_value': 81935.17, 'new_value': 84528.95}, {'field': 'total_amount', 'old_value': 92567.93, 'new_value': 95382.91}, {'field': 'order_count', 'old_value': 2720, 'new_value': 2816}]
2025-05-16 12:01:52,469 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMEA
2025-05-16 12:01:52,890 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMEA
2025-05-16 12:01:52,890 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58486.1, 'new_value': 61245.3}, {'field': 'total_amount', 'old_value': 58486.1, 'new_value': 61245.3}, {'field': 'order_count', 'old_value': 2886, 'new_value': 3023}]
2025-05-16 12:01:52,890 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-16 12:01:53,250 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-16 12:01:53,250 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3027.0, 'new_value': 3778.0}, {'field': 'offline_amount', 'old_value': 20779.0, 'new_value': 21034.0}, {'field': 'total_amount', 'old_value': 23806.0, 'new_value': 24812.0}, {'field': 'order_count', 'old_value': 186, 'new_value': 194}]
2025-05-16 12:01:53,250 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-16 12:01:53,765 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-16 12:01:53,765 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32270.5, 'new_value': 39894.5}, {'field': 'total_amount', 'old_value': 119637.71, 'new_value': 127261.71}, {'field': 'order_count', 'old_value': 5225, 'new_value': 5570}]
2025-05-16 12:01:53,765 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-16 12:01:54,219 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-16 12:01:54,219 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 125483.0, 'new_value': 128907.0}, {'field': 'total_amount', 'old_value': 125483.0, 'new_value': 128907.0}, {'field': 'order_count', 'old_value': 142, 'new_value': 150}]
2025-05-16 12:01:54,219 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-16 12:01:54,672 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-16 12:01:54,672 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 445909.89, 'new_value': 458749.59}, {'field': 'total_amount', 'old_value': 445909.89, 'new_value': 458749.59}, {'field': 'order_count', 'old_value': 8868, 'new_value': 8991}]
2025-05-16 12:01:54,672 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-16 12:01:55,281 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-16 12:01:55,281 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48519.36, 'new_value': 52820.37}, {'field': 'offline_amount', 'old_value': 133771.4, 'new_value': 145520.93}, {'field': 'total_amount', 'old_value': 182290.76, 'new_value': 198341.3}, {'field': 'order_count', 'old_value': 8368, 'new_value': 9216}]
2025-05-16 12:01:55,281 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-16 12:01:55,765 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-16 12:01:55,765 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 163911.64, 'new_value': 173449.33}, {'field': 'total_amount', 'old_value': 163911.64, 'new_value': 173449.33}, {'field': 'order_count', 'old_value': 6841, 'new_value': 7264}]
2025-05-16 12:01:55,765 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-16 12:01:56,265 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-16 12:01:56,265 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 525226.0, 'new_value': 540226.0}, {'field': 'total_amount', 'old_value': 525226.0, 'new_value': 540226.0}, {'field': 'order_count', 'old_value': 1108, 'new_value': 1109}]
2025-05-16 12:01:56,265 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-16 12:01:56,844 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-16 12:01:56,844 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 137374.0, 'new_value': 141042.0}, {'field': 'total_amount', 'old_value': 137374.0, 'new_value': 141042.0}, {'field': 'order_count', 'old_value': 411, 'new_value': 425}]
2025-05-16 12:01:56,844 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-16 12:01:57,250 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-16 12:01:57,250 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35427.72, 'new_value': 37846.41}, {'field': 'offline_amount', 'old_value': 265472.66, 'new_value': 277793.72}, {'field': 'total_amount', 'old_value': 300900.38, 'new_value': 315640.13}, {'field': 'order_count', 'old_value': 1324, 'new_value': 1415}]
2025-05-16 12:01:57,250 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-16 12:01:57,672 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-16 12:01:57,672 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 448522.0, 'new_value': 463004.0}, {'field': 'total_amount', 'old_value': 448522.0, 'new_value': 463004.0}, {'field': 'order_count', 'old_value': 113, 'new_value': 117}]
2025-05-16 12:01:57,672 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-16 12:01:58,094 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-16 12:01:58,094 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 870.06, 'new_value': 984.15}, {'field': 'offline_amount', 'old_value': 16216.21, 'new_value': 16712.75}, {'field': 'total_amount', 'old_value': 17086.27, 'new_value': 17696.9}, {'field': 'order_count', 'old_value': 603, 'new_value': 621}]
2025-05-16 12:01:58,094 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-16 12:01:58,515 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-16 12:01:58,515 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4030.32, 'new_value': 4110.62}, {'field': 'offline_amount', 'old_value': 235876.54, 'new_value': 245115.64}, {'field': 'total_amount', 'old_value': 239906.86, 'new_value': 249226.26}, {'field': 'order_count', 'old_value': 10802, 'new_value': 11285}]
2025-05-16 12:01:58,515 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-16 12:01:58,890 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-16 12:01:58,890 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50113.0, 'new_value': 52413.0}, {'field': 'total_amount', 'old_value': 50113.0, 'new_value': 52413.0}, {'field': 'order_count', 'old_value': 129, 'new_value': 135}]
2025-05-16 12:01:58,890 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-16 12:01:59,312 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-16 12:01:59,312 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27952.42, 'new_value': 30046.32}, {'field': 'offline_amount', 'old_value': 196259.4, 'new_value': 204035.61}, {'field': 'total_amount', 'old_value': 224211.82, 'new_value': 234081.93}, {'field': 'order_count', 'old_value': 1380, 'new_value': 1449}]
2025-05-16 12:01:59,312 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-16 12:01:59,765 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-16 12:01:59,765 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 115629.55, 'new_value': 122275.55}, {'field': 'total_amount', 'old_value': 115629.55, 'new_value': 122275.55}, {'field': 'order_count', 'old_value': 635, 'new_value': 670}]
2025-05-16 12:01:59,765 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-16 12:02:00,172 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-16 12:02:00,172 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 56005.0, 'new_value': 57142.0}, {'field': 'total_amount', 'old_value': 56005.0, 'new_value': 57142.0}, {'field': 'order_count', 'old_value': 1662, 'new_value': 1695}]
2025-05-16 12:02:00,172 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-16 12:02:00,672 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-16 12:02:00,672 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52872.14, 'new_value': 57577.74}, {'field': 'total_amount', 'old_value': 92102.69, 'new_value': 96808.29}, {'field': 'order_count', 'old_value': 4223, 'new_value': 4444}]
2025-05-16 12:02:00,672 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-16 12:02:01,140 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-16 12:02:01,140 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22162.38, 'new_value': 22163.12}, {'field': 'offline_amount', 'old_value': 211870.91, 'new_value': 220760.28}, {'field': 'total_amount', 'old_value': 234033.29, 'new_value': 242923.4}, {'field': 'order_count', 'old_value': 9727, 'new_value': 10172}]
2025-05-16 12:02:01,140 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-16 12:02:01,609 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-16 12:02:01,609 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16156.72, 'new_value': 17604.89}, {'field': 'offline_amount', 'old_value': 144342.24, 'new_value': 148710.14}, {'field': 'total_amount', 'old_value': 160498.96, 'new_value': 166315.03}, {'field': 'order_count', 'old_value': 4955, 'new_value': 5159}]
2025-05-16 12:02:01,609 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-16 12:02:02,047 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-16 12:02:02,047 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 243616.26, 'new_value': 252625.91}, {'field': 'total_amount', 'old_value': 243616.26, 'new_value': 252625.91}, {'field': 'order_count', 'old_value': 1639, 'new_value': 1724}]
2025-05-16 12:02:02,047 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-16 12:02:02,484 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-16 12:02:02,484 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87820.7, 'new_value': 90547.5}, {'field': 'total_amount', 'old_value': 87820.7, 'new_value': 90547.5}, {'field': 'order_count', 'old_value': 162, 'new_value': 167}]
2025-05-16 12:02:02,484 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-16 12:02:02,922 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-16 12:02:02,922 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68723.0, 'new_value': 69917.0}, {'field': 'total_amount', 'old_value': 83927.0, 'new_value': 85121.0}, {'field': 'order_count', 'old_value': 1876, 'new_value': 1906}]
2025-05-16 12:02:02,922 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-16 12:02:03,375 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-16 12:02:03,375 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59528.0, 'new_value': 63028.0}, {'field': 'total_amount', 'old_value': 59528.0, 'new_value': 63028.0}, {'field': 'order_count', 'old_value': 395, 'new_value': 396}]
2025-05-16 12:02:03,375 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMCE
2025-05-16 12:02:03,812 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMCE
2025-05-16 12:02:03,812 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106096.37, 'new_value': 111251.18}, {'field': 'total_amount', 'old_value': 106096.37, 'new_value': 111251.18}, {'field': 'order_count', 'old_value': 1399, 'new_value': 1476}]
2025-05-16 12:02:03,812 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-16 12:02:04,234 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-16 12:02:04,234 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16705.0, 'new_value': 17101.0}, {'field': 'total_amount', 'old_value': 16705.0, 'new_value': 17101.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-05-16 12:02:04,234 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-16 12:02:04,765 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-16 12:02:04,765 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 56984.86, 'new_value': 63069.46}, {'field': 'offline_amount', 'old_value': 242800.28, 'new_value': 246075.6}, {'field': 'total_amount', 'old_value': 299785.14, 'new_value': 309145.06}, {'field': 'order_count', 'old_value': 2205, 'new_value': 2362}]
2025-05-16 12:02:04,765 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-16 12:02:05,203 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-16 12:02:05,203 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 28, 'new_value': 29}]
2025-05-16 12:02:05,203 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-16 12:02:05,656 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-16 12:02:05,656 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 192700.0, 'new_value': 216700.0}, {'field': 'total_amount', 'old_value': 192700.0, 'new_value': 216700.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-05-16 12:02:05,656 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-16 12:02:06,140 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-16 12:02:06,140 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 140491.14, 'new_value': 145339.17}, {'field': 'total_amount', 'old_value': 159664.57, 'new_value': 164512.6}, {'field': 'order_count', 'old_value': 3357, 'new_value': 3443}]
2025-05-16 12:02:06,140 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-16 12:02:06,672 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-16 12:02:06,672 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 220035.3, 'new_value': 227394.0}, {'field': 'total_amount', 'old_value': 220035.3, 'new_value': 227394.0}, {'field': 'order_count', 'old_value': 274, 'new_value': 282}]
2025-05-16 12:02:06,672 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMAQ
2025-05-16 12:02:07,156 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMAQ
2025-05-16 12:02:07,156 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52755.0, 'new_value': 59672.0}, {'field': 'total_amount', 'old_value': 52755.0, 'new_value': 59672.0}, {'field': 'order_count', 'old_value': 1244, 'new_value': 1414}]
2025-05-16 12:02:07,156 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-16 12:02:07,593 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-16 12:02:07,593 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 564394.0, 'new_value': 574226.0}, {'field': 'total_amount', 'old_value': 564394.0, 'new_value': 574226.0}, {'field': 'order_count', 'old_value': 663, 'new_value': 683}]
2025-05-16 12:02:07,593 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-16 12:02:08,078 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-16 12:02:08,078 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 406562.58, 'new_value': 423980.58}, {'field': 'total_amount', 'old_value': 406562.58, 'new_value': 423980.58}, {'field': 'order_count', 'old_value': 3088, 'new_value': 3254}]
2025-05-16 12:02:08,078 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-16 12:02:08,437 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-16 12:02:08,437 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14676.0, 'new_value': 16184.0}, {'field': 'total_amount', 'old_value': 14676.0, 'new_value': 16184.0}, {'field': 'order_count', 'old_value': 78, 'new_value': 85}]
2025-05-16 12:02:08,437 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-16 12:02:08,875 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-16 12:02:08,875 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 387623.0, 'new_value': 399320.0}, {'field': 'total_amount', 'old_value': 387623.0, 'new_value': 399320.0}, {'field': 'order_count', 'old_value': 42, 'new_value': 45}]
2025-05-16 12:02:08,875 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-16 12:02:09,312 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-16 12:02:09,312 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47238.97, 'new_value': 49363.43}, {'field': 'total_amount', 'old_value': 47238.97, 'new_value': 49363.43}, {'field': 'order_count', 'old_value': 2714, 'new_value': 2846}]
2025-05-16 12:02:09,312 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-16 12:02:09,797 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-16 12:02:09,797 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8513.45, 'new_value': 9192.45}, {'field': 'offline_amount', 'old_value': 156614.0, 'new_value': 167099.0}, {'field': 'total_amount', 'old_value': 165127.45, 'new_value': 176291.45}, {'field': 'order_count', 'old_value': 827, 'new_value': 892}]
2025-05-16 12:02:09,797 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-16 12:02:10,328 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-16 12:02:10,328 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 75488.8, 'new_value': 80788.4}, {'field': 'offline_amount', 'old_value': 63975.9, 'new_value': 67964.6}, {'field': 'total_amount', 'old_value': 139464.7, 'new_value': 148753.0}, {'field': 'order_count', 'old_value': 3287, 'new_value': 3502}]
2025-05-16 12:02:10,328 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-16 12:02:10,797 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-16 12:02:10,797 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-05-16 12:02:10,797 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-16 12:02:11,265 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-16 12:02:11,265 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 113413.0, 'new_value': 117881.3}, {'field': 'total_amount', 'old_value': 113413.0, 'new_value': 117881.3}, {'field': 'order_count', 'old_value': 1455, 'new_value': 1509}]
2025-05-16 12:02:11,265 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-16 12:02:11,687 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-16 12:02:11,687 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20251.2, 'new_value': 20882.2}, {'field': 'total_amount', 'old_value': 20251.2, 'new_value': 20882.2}, {'field': 'order_count', 'old_value': 117, 'new_value': 120}]
2025-05-16 12:02:11,687 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-16 12:02:12,156 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-16 12:02:12,156 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 170756.5, 'new_value': 188961.5}, {'field': 'total_amount', 'old_value': 170756.5, 'new_value': 188961.5}, {'field': 'order_count', 'old_value': 31, 'new_value': 34}]
2025-05-16 12:02:12,156 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-16 12:02:12,578 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-16 12:02:12,578 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24074.14, 'new_value': 25484.04}, {'field': 'offline_amount', 'old_value': 666478.09, 'new_value': 693864.1}, {'field': 'total_amount', 'old_value': 690552.23, 'new_value': 719348.14}, {'field': 'order_count', 'old_value': 3201, 'new_value': 3386}]
2025-05-16 12:02:12,578 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-16 12:02:13,031 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-16 12:02:13,031 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 183284.0, 'new_value': 196704.0}, {'field': 'total_amount', 'old_value': 197059.0, 'new_value': 210479.0}, {'field': 'order_count', 'old_value': 4098, 'new_value': 4448}]
2025-05-16 12:02:13,031 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-16 12:02:13,625 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-16 12:02:13,625 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44841.72, 'new_value': 47226.13}, {'field': 'offline_amount', 'old_value': 118679.38, 'new_value': 123378.72}, {'field': 'total_amount', 'old_value': 163521.1, 'new_value': 170604.85}, {'field': 'order_count', 'old_value': 2832, 'new_value': 3016}]
2025-05-16 12:02:13,625 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-16 12:02:14,093 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-16 12:02:14,093 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 171920.0, 'new_value': 172438.0}, {'field': 'total_amount', 'old_value': 176020.0, 'new_value': 176538.0}, {'field': 'order_count', 'old_value': 118, 'new_value': 121}]
2025-05-16 12:02:14,093 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-16 12:02:14,593 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-16 12:02:14,593 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33827.85, 'new_value': 34814.25}, {'field': 'total_amount', 'old_value': 33827.85, 'new_value': 34814.25}, {'field': 'order_count', 'old_value': 126, 'new_value': 130}]
2025-05-16 12:02:14,593 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-16 12:02:15,031 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-16 12:02:15,031 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 125509.07, 'new_value': 132036.81}, {'field': 'offline_amount', 'old_value': 453358.57, 'new_value': 477005.43}, {'field': 'total_amount', 'old_value': 578867.64, 'new_value': 609042.24}, {'field': 'order_count', 'old_value': 3250, 'new_value': 3462}]
2025-05-16 12:02:15,031 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-16 12:02:15,500 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-16 12:02:15,500 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 123119.0, 'new_value': 125315.0}, {'field': 'total_amount', 'old_value': 123119.0, 'new_value': 125315.0}, {'field': 'order_count', 'old_value': 2013, 'new_value': 2046}]
2025-05-16 12:02:15,500 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-16 12:02:16,062 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-16 12:02:16,062 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112932.61, 'new_value': 116389.24}, {'field': 'total_amount', 'old_value': 112932.61, 'new_value': 116389.24}, {'field': 'order_count', 'old_value': 4733, 'new_value': 4900}]
2025-05-16 12:02:16,062 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-16 12:02:16,484 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-16 12:02:16,484 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 135397.3, 'new_value': 142076.34}, {'field': 'total_amount', 'old_value': 135397.3, 'new_value': 142076.34}, {'field': 'order_count', 'old_value': 1011, 'new_value': 1078}]
2025-05-16 12:02:16,484 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-16 12:02:16,906 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-16 12:02:16,906 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21856.6, 'new_value': 24856.6}, {'field': 'offline_amount', 'old_value': 75653.07, 'new_value': 75960.91}, {'field': 'total_amount', 'old_value': 97509.67, 'new_value': 100817.51}, {'field': 'order_count', 'old_value': 2796, 'new_value': 2895}]
2025-05-16 12:02:16,906 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-16 12:02:17,359 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-16 12:02:17,359 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19960.66, 'new_value': 20960.66}, {'field': 'offline_amount', 'old_value': 29257.1, 'new_value': 31095.62}, {'field': 'total_amount', 'old_value': 49217.76, 'new_value': 52056.28}, {'field': 'order_count', 'old_value': 2234, 'new_value': 2378}]
2025-05-16 12:02:17,359 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-16 12:02:17,797 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-16 12:02:17,797 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 188043.0, 'new_value': 196424.0}, {'field': 'total_amount', 'old_value': 188043.0, 'new_value': 196424.0}, {'field': 'order_count', 'old_value': 280, 'new_value': 293}]
2025-05-16 12:02:17,797 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-16 12:02:18,218 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-16 12:02:18,218 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22988.0, 'new_value': 23594.0}, {'field': 'total_amount', 'old_value': 22988.0, 'new_value': 23594.0}, {'field': 'order_count', 'old_value': 123, 'new_value': 127}]
2025-05-16 12:02:18,218 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-16 12:02:18,687 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-16 12:02:18,687 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59652.14, 'new_value': 64397.07}, {'field': 'offline_amount', 'old_value': 122181.75, 'new_value': 128829.75}, {'field': 'total_amount', 'old_value': 181833.89, 'new_value': 193226.82}, {'field': 'order_count', 'old_value': 5721, 'new_value': 6130}]
2025-05-16 12:02:18,687 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-16 12:02:19,109 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-16 12:02:19,109 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57276.0, 'new_value': 61620.0}, {'field': 'total_amount', 'old_value': 57276.0, 'new_value': 61620.0}, {'field': 'order_count', 'old_value': 247, 'new_value': 267}]
2025-05-16 12:02:19,109 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMBQ
2025-05-16 12:02:19,593 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMBQ
2025-05-16 12:02:19,593 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72611.56, 'new_value': 81271.0}, {'field': 'total_amount', 'old_value': 72619.56, 'new_value': 81279.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-05-16 12:02:19,593 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-16 12:02:20,046 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-16 12:02:20,046 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 93960.6, 'new_value': 101071.51}, {'field': 'offline_amount', 'old_value': 30648.28, 'new_value': 31559.08}, {'field': 'total_amount', 'old_value': 124608.88, 'new_value': 132630.59}, {'field': 'order_count', 'old_value': 6987, 'new_value': 7437}]
2025-05-16 12:02:20,046 - INFO - 开始更新记录 - 表单实例ID: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMH
2025-05-16 12:02:20,468 - INFO - 更新表单数据成功: FINST-E3G66QA1FT5V4M1QBJUOG6Q0YPEX3FFMYJAAMH
2025-05-16 12:02:20,468 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 61699.0, 'new_value': 64526.3}, {'field': 'offline_amount', 'old_value': 21237.3, 'new_value': 22162.3}, {'field': 'total_amount', 'old_value': 82936.3, 'new_value': 86688.6}, {'field': 'order_count', 'old_value': 6869, 'new_value': 7167}]
2025-05-16 12:02:20,468 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-16 12:02:20,953 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-16 12:02:20,953 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 152932.58, 'new_value': 157539.19}, {'field': 'total_amount', 'old_value': 175419.98, 'new_value': 180026.59}, {'field': 'order_count', 'old_value': 956, 'new_value': 995}]
2025-05-16 12:02:20,953 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-16 12:02:21,437 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-16 12:02:21,437 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 89155.2, 'new_value': 93614.4}, {'field': 'offline_amount', 'old_value': 170293.63, 'new_value': 175150.43}, {'field': 'total_amount', 'old_value': 259448.83, 'new_value': 268764.83}, {'field': 'order_count', 'old_value': 2084, 'new_value': 2187}]
2025-05-16 12:02:21,437 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-16 12:02:22,062 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-16 12:02:22,062 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1304821.0, 'new_value': 1346797.0}, {'field': 'total_amount', 'old_value': 1304821.0, 'new_value': 1346797.0}, {'field': 'order_count', 'old_value': 4944, 'new_value': 5121}]
2025-05-16 12:02:22,062 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-16 12:02:22,515 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-16 12:02:22,515 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23857.43, 'new_value': 25943.28}, {'field': 'offline_amount', 'old_value': 16615.17, 'new_value': 17014.77}, {'field': 'total_amount', 'old_value': 40472.6, 'new_value': 42958.05}, {'field': 'order_count', 'old_value': 1749, 'new_value': 1859}]
2025-05-16 12:02:22,515 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-16 12:02:22,984 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-16 12:02:22,984 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9301.05, 'new_value': 9705.03}, {'field': 'offline_amount', 'old_value': 21986.0, 'new_value': 22648.0}, {'field': 'total_amount', 'old_value': 31287.05, 'new_value': 32353.03}, {'field': 'order_count', 'old_value': 1246, 'new_value': 1295}]
2025-05-16 12:02:22,984 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-16 12:02:23,468 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-16 12:02:23,468 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 235334.3, 'new_value': 252529.3}, {'field': 'total_amount', 'old_value': 235334.3, 'new_value': 252529.3}]
2025-05-16 12:02:23,468 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-16 12:02:23,968 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-16 12:02:23,968 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50000.0, 'new_value': 55862.0}, {'field': 'total_amount', 'old_value': 50000.0, 'new_value': 55862.0}, {'field': 'order_count', 'old_value': 1684, 'new_value': 1685}]
2025-05-16 12:02:23,968 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-16 12:02:24,468 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-16 12:02:24,468 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 401148.0, 'new_value': 426297.0}, {'field': 'total_amount', 'old_value': 401148.0, 'new_value': 426297.0}, {'field': 'order_count', 'old_value': 248, 'new_value': 260}]
2025-05-16 12:02:24,468 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-16 12:02:24,937 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-16 12:02:24,937 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 476835.92, 'new_value': 491325.18}, {'field': 'total_amount', 'old_value': 476835.92, 'new_value': 491325.18}, {'field': 'order_count', 'old_value': 3674, 'new_value': 3830}]
2025-05-16 12:02:24,937 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-16 12:02:25,468 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-16 12:02:25,468 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11013.1, 'new_value': 11556.3}, {'field': 'offline_amount', 'old_value': 106234.2, 'new_value': 111810.9}, {'field': 'total_amount', 'old_value': 117247.3, 'new_value': 123367.2}, {'field': 'order_count', 'old_value': 3613, 'new_value': 3882}]
2025-05-16 12:02:25,468 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-16 12:02:25,937 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-16 12:02:25,937 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 377286.0, 'new_value': 387033.0}, {'field': 'total_amount', 'old_value': 377286.0, 'new_value': 387033.0}, {'field': 'order_count', 'old_value': 2179, 'new_value': 2305}]
2025-05-16 12:02:25,937 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMED1
2025-05-16 12:02:26,484 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMED1
2025-05-16 12:02:26,484 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14637.54, 'new_value': 15544.17}, {'field': 'offline_amount', 'old_value': 16659.85, 'new_value': 17983.05}, {'field': 'total_amount', 'old_value': 31297.39, 'new_value': 33527.22}, {'field': 'order_count', 'old_value': 1479, 'new_value': 1592}]
2025-05-16 12:02:26,484 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-16 12:02:26,906 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-16 12:02:26,906 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2113.0, 'new_value': 2181.0}, {'field': 'offline_amount', 'old_value': 18678.6, 'new_value': 19174.6}, {'field': 'total_amount', 'old_value': 20791.6, 'new_value': 21355.6}, {'field': 'order_count', 'old_value': 761, 'new_value': 787}]
2025-05-16 12:02:26,906 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-16 12:02:27,375 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-16 12:02:27,375 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1223.0, 'new_value': 1286.0}, {'field': 'offline_amount', 'old_value': 35811.0, 'new_value': 36994.0}, {'field': 'total_amount', 'old_value': 37034.0, 'new_value': 38280.0}, {'field': 'order_count', 'old_value': 292, 'new_value': 303}]
2025-05-16 12:02:27,375 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-16 12:02:27,843 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-16 12:02:27,843 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12224.0, 'new_value': 12998.0}, {'field': 'total_amount', 'old_value': 15529.0, 'new_value': 16303.0}, {'field': 'order_count', 'old_value': 140, 'new_value': 146}]
2025-05-16 12:02:27,843 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFQ
2025-05-16 12:02:28,265 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFQ
2025-05-16 12:02:28,265 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12159.0, 'new_value': 12857.0}, {'field': 'total_amount', 'old_value': 12159.0, 'new_value': 12857.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-05-16 12:02:28,265 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-16 12:02:28,734 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-16 12:02:28,734 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42176.0, 'new_value': 43676.0}, {'field': 'offline_amount', 'old_value': 152038.0, 'new_value': 164170.0}, {'field': 'total_amount', 'old_value': 194214.0, 'new_value': 207846.0}, {'field': 'order_count', 'old_value': 858, 'new_value': 908}]
2025-05-16 12:02:28,734 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-16 12:02:29,234 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-16 12:02:29,234 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2260884.89, 'new_value': 2376797.48}, {'field': 'total_amount', 'old_value': 2260884.89, 'new_value': 2376797.48}, {'field': 'order_count', 'old_value': 3800, 'new_value': 4005}]
2025-05-16 12:02:29,234 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-16 12:02:29,703 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-16 12:02:29,703 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 178890.0, 'new_value': 194794.0}, {'field': 'total_amount', 'old_value': 178890.0, 'new_value': 194794.0}, {'field': 'order_count', 'old_value': 3797, 'new_value': 4157}]
2025-05-16 12:02:29,703 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-16 12:02:30,250 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-16 12:02:30,250 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28581.18, 'new_value': 29759.26}, {'field': 'offline_amount', 'old_value': 38781.94, 'new_value': 41550.43}, {'field': 'total_amount', 'old_value': 67363.12, 'new_value': 71309.69}, {'field': 'order_count', 'old_value': 1532, 'new_value': 1626}]
2025-05-16 12:02:30,250 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-16 12:02:30,734 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-16 12:02:30,734 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 218664.6, 'new_value': 221949.1}, {'field': 'total_amount', 'old_value': 218664.6, 'new_value': 221949.1}, {'field': 'order_count', 'old_value': 4722, 'new_value': 4790}]
2025-05-16 12:02:30,734 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-16 12:02:31,218 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-16 12:02:31,218 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32016.0, 'new_value': 33807.4}, {'field': 'total_amount', 'old_value': 32016.0, 'new_value': 33807.4}, {'field': 'order_count', 'old_value': 163, 'new_value': 173}]
2025-05-16 12:02:31,218 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-16 12:02:31,687 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-16 12:02:31,687 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60838.0, 'new_value': 63666.0}, {'field': 'total_amount', 'old_value': 60838.0, 'new_value': 63666.0}, {'field': 'order_count', 'old_value': 4177, 'new_value': 4385}]
2025-05-16 12:02:31,687 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMFD1
2025-05-16 12:02:32,156 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMFD1
2025-05-16 12:02:32,156 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2440.6, 'new_value': 2692.6}, {'field': 'offline_amount', 'old_value': 12646.0, 'new_value': 14246.0}, {'field': 'total_amount', 'old_value': 15086.6, 'new_value': 16938.6}, {'field': 'order_count', 'old_value': 368, 'new_value': 408}]
2025-05-16 12:02:32,156 - INFO - 开始更新记录 - 表单实例ID: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMGD1
2025-05-16 12:02:32,640 - INFO - 更新表单数据成功: FINST-S0E660A1W00V1UP0EWNLDC49M86F3RQFGZBAMGD1
2025-05-16 12:02:32,640 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 108905.73, 'new_value': 116114.71}, {'field': 'total_amount', 'old_value': 108905.73, 'new_value': 116114.71}, {'field': 'order_count', 'old_value': 7650, 'new_value': 8115}]
2025-05-16 12:02:32,640 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMMX
2025-05-16 12:02:33,125 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMMX
2025-05-16 12:02:33,125 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21161.58, 'new_value': 22784.18}, {'field': 'total_amount', 'old_value': 21161.58, 'new_value': 22784.18}, {'field': 'order_count', 'old_value': 986, 'new_value': 1070}]
2025-05-16 12:02:33,125 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-16 12:02:33,625 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-16 12:02:33,625 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 2383.05}, {'field': 'total_amount', 'old_value': 41068.94, 'new_value': 43451.99}, {'field': 'order_count', 'old_value': 667, 'new_value': 713}]
2025-05-16 12:02:33,625 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-16 12:02:34,093 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-16 12:02:34,093 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2800118.39, 'new_value': 2966105.12}, {'field': 'total_amount', 'old_value': 2800118.39, 'new_value': 2966105.12}, {'field': 'order_count', 'old_value': 58107, 'new_value': 61726}]
2025-05-16 12:02:34,093 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-16 12:02:34,531 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-16 12:02:34,531 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 343573.47, 'new_value': 355267.47}, {'field': 'total_amount', 'old_value': 343573.47, 'new_value': 355267.47}, {'field': 'order_count', 'old_value': 4338, 'new_value': 4444}]
2025-05-16 12:02:34,531 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-16 12:02:35,031 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-16 12:02:35,031 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104057.78, 'new_value': 113319.78}, {'field': 'total_amount', 'old_value': 104057.78, 'new_value': 113319.78}, {'field': 'order_count', 'old_value': 2045, 'new_value': 2185}]
2025-05-16 12:02:35,031 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMNX
2025-05-16 12:02:35,562 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMNX
2025-05-16 12:02:35,562 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46200.02, 'new_value': 49533.77}, {'field': 'total_amount', 'old_value': 46200.02, 'new_value': 49533.77}, {'field': 'order_count', 'old_value': 4515, 'new_value': 4887}]
2025-05-16 12:02:35,562 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-16 12:02:36,015 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-16 12:02:36,015 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 240025.0, 'new_value': 245205.0}, {'field': 'total_amount', 'old_value': 240025.0, 'new_value': 245205.0}, {'field': 'order_count', 'old_value': 52, 'new_value': 54}]
2025-05-16 12:02:36,015 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-16 12:02:36,453 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-16 12:02:36,453 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74710.4, 'new_value': 79086.8}, {'field': 'total_amount', 'old_value': 74710.4, 'new_value': 79086.8}, {'field': 'order_count', 'old_value': 1910, 'new_value': 2048}]
2025-05-16 12:02:36,453 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-16 12:02:36,953 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-16 12:02:36,953 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53611.93, 'new_value': 57546.63}, {'field': 'offline_amount', 'old_value': 262990.4, 'new_value': 274674.6}, {'field': 'total_amount', 'old_value': 316602.33, 'new_value': 332221.23}, {'field': 'order_count', 'old_value': 2052, 'new_value': 2197}]
2025-05-16 12:02:36,953 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-16 12:02:37,406 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-16 12:02:37,406 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31950.28, 'new_value': 34648.33}, {'field': 'total_amount', 'old_value': 50827.75, 'new_value': 53525.8}, {'field': 'order_count', 'old_value': 3290, 'new_value': 3487}]
2025-05-16 12:02:37,406 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-16 12:02:37,859 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-16 12:02:37,875 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53034.01, 'new_value': 59781.97}, {'field': 'total_amount', 'old_value': 83442.15, 'new_value': 90190.11}, {'field': 'order_count', 'old_value': 5444, 'new_value': 5895}]
2025-05-16 12:02:37,875 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-16 12:02:38,281 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-16 12:02:38,281 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 668368.99, 'new_value': 707721.16}, {'field': 'total_amount', 'old_value': 668368.99, 'new_value': 707721.16}, {'field': 'order_count', 'old_value': 2001, 'new_value': 2123}]
2025-05-16 12:02:38,281 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G
2025-05-16 12:02:38,765 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G
2025-05-16 12:02:38,765 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 4, 'new_value': 8}]
2025-05-16 12:02:38,765 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-16 12:02:39,296 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-16 12:02:39,296 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 406563.65, 'new_value': 428664.83}, {'field': 'total_amount', 'old_value': 406563.65, 'new_value': 428664.83}, {'field': 'order_count', 'old_value': 1925, 'new_value': 2050}]
2025-05-16 12:02:39,296 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6G
2025-05-16 12:02:39,718 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6G
2025-05-16 12:02:39,718 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 632082.7, 'new_value': 669731.08}, {'field': 'total_amount', 'old_value': 632082.7, 'new_value': 669731.08}, {'field': 'order_count', 'old_value': 2203, 'new_value': 2330}]
2025-05-16 12:02:39,718 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-16 12:02:40,203 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-16 12:02:40,203 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 466025.06, 'new_value': 484669.76}, {'field': 'total_amount', 'old_value': 466025.06, 'new_value': 484669.76}, {'field': 'order_count', 'old_value': 1504, 'new_value': 1551}]
2025-05-16 12:02:40,203 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8G
2025-05-16 12:02:40,671 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8G
2025-05-16 12:02:40,671 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24990.0, 'new_value': 35850.0}, {'field': 'total_amount', 'old_value': 24990.0, 'new_value': 35850.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 19}]
2025-05-16 12:02:40,671 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9G
2025-05-16 12:02:41,109 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9G
2025-05-16 12:02:41,109 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10375.0, 'new_value': 11519.0}, {'field': 'offline_amount', 'old_value': 12775.0, 'new_value': 13919.0}, {'field': 'total_amount', 'old_value': 23150.0, 'new_value': 25438.0}, {'field': 'order_count', 'old_value': 10413, 'new_value': 11557}]
2025-05-16 12:02:41,109 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAG
2025-05-16 12:02:41,640 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAG
2025-05-16 12:02:41,640 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24416.02, 'new_value': 25455.02}, {'field': 'total_amount', 'old_value': 25426.92, 'new_value': 26465.92}, {'field': 'order_count', 'old_value': 192, 'new_value': 203}]
2025-05-16 12:02:41,640 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-16 12:02:42,093 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-16 12:02:42,093 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67055.0, 'new_value': 74504.0}, {'field': 'total_amount', 'old_value': 67135.0, 'new_value': 74584.0}, {'field': 'order_count', 'old_value': 7010, 'new_value': 7874}]
2025-05-16 12:02:42,093 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-16 12:02:42,546 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-16 12:02:42,546 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9054.0, 'new_value': 12142.0}, {'field': 'offline_amount', 'old_value': 32766.0, 'new_value': 33109.0}, {'field': 'total_amount', 'old_value': 41820.0, 'new_value': 45251.0}, {'field': 'order_count', 'old_value': 59, 'new_value': 67}]
2025-05-16 12:02:42,546 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGG
2025-05-16 12:02:42,984 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGG
2025-05-16 12:02:42,984 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 69493.0, 'new_value': 78597.0}, {'field': 'offline_amount', 'old_value': 47081.0, 'new_value': 53083.0}, {'field': 'total_amount', 'old_value': 116574.0, 'new_value': 131680.0}, {'field': 'order_count', 'old_value': 4953, 'new_value': 5514}]
2025-05-16 12:02:42,984 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-16 12:02:43,437 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-16 12:02:43,437 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61476.0, 'new_value': 65145.0}, {'field': 'total_amount', 'old_value': 61476.0, 'new_value': 65145.0}, {'field': 'order_count', 'old_value': 325, 'new_value': 357}]
2025-05-16 12:02:43,437 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-16 12:02:43,999 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-16 12:02:43,999 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 126685.0, 'new_value': 139085.0}, {'field': 'total_amount', 'old_value': 126685.0, 'new_value': 139085.0}, {'field': 'order_count', 'old_value': 306, 'new_value': 336}]
2025-05-16 12:02:43,999 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-16 12:02:44,468 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-16 12:02:44,468 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100065.0, 'new_value': 110462.0}, {'field': 'total_amount', 'old_value': 100065.0, 'new_value': 110462.0}, {'field': 'order_count', 'old_value': 10550, 'new_value': 11725}]
2025-05-16 12:02:44,468 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-16 12:02:44,890 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-16 12:02:44,890 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77928.0, 'new_value': 84021.0}, {'field': 'total_amount', 'old_value': 77928.0, 'new_value': 84021.0}, {'field': 'order_count', 'old_value': 667, 'new_value': 715}]
2025-05-16 12:02:44,890 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-16 12:02:45,328 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-16 12:02:45,328 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94905.17, 'new_value': 96905.17}, {'field': 'total_amount', 'old_value': 94905.17, 'new_value': 96905.17}, {'field': 'order_count', 'old_value': 794, 'new_value': 795}]
2025-05-16 12:02:45,328 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSG
2025-05-16 12:02:45,765 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSG
2025-05-16 12:02:45,765 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2798.0, 'new_value': 3223.0}, {'field': 'total_amount', 'old_value': 2798.0, 'new_value': 3223.0}, {'field': 'order_count', 'old_value': 585, 'new_value': 586}]
2025-05-16 12:02:45,765 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-16 12:02:46,249 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-16 12:02:46,249 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30427.46, 'new_value': 32100.46}, {'field': 'total_amount', 'old_value': 30427.46, 'new_value': 32100.46}, {'field': 'order_count', 'old_value': 493, 'new_value': 533}]
2025-05-16 12:02:46,249 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-16 12:02:46,703 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-16 12:02:46,703 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 77821.69, 'new_value': 82619.89}, {'field': 'offline_amount', 'old_value': 389790.95, 'new_value': 419006.13}, {'field': 'total_amount', 'old_value': 467612.64, 'new_value': 501626.02}, {'field': 'order_count', 'old_value': 1054, 'new_value': 1131}]
2025-05-16 12:02:46,703 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-16 12:02:47,203 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-16 12:02:47,203 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41912.6, 'new_value': 44927.6}, {'field': 'total_amount', 'old_value': 41912.6, 'new_value': 44927.6}, {'field': 'order_count', 'old_value': 26, 'new_value': 29}]
2025-05-16 12:02:47,203 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-16 12:02:47,624 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-16 12:02:47,624 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48098.73, 'new_value': 50969.4}, {'field': 'offline_amount', 'old_value': 451882.86, 'new_value': 492659.89}, {'field': 'total_amount', 'old_value': 498107.26, 'new_value': 541754.96}, {'field': 'order_count', 'old_value': 2433, 'new_value': 2613}]
2025-05-16 12:02:47,624 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-16 12:02:48,156 - INFO - 更新表单数据成功: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-16 12:02:48,156 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59309.0, 'new_value': 65599.0}, {'field': 'total_amount', 'old_value': 59309.0, 'new_value': 65599.0}, {'field': 'order_count', 'old_value': 200, 'new_value': 224}]
2025-05-16 12:02:48,156 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-05-16 12:02:48,640 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-05-16 12:02:48,640 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47182.0, 'new_value': 50242.0}, {'field': 'total_amount', 'old_value': 52500.0, 'new_value': 55560.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 27}]
2025-05-16 12:02:48,640 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-16 12:02:49,140 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-16 12:02:49,140 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9437183.74, 'new_value': 10767479.46}, {'field': 'total_amount', 'old_value': 9437183.74, 'new_value': 10767479.46}, {'field': 'order_count', 'old_value': 13, 'new_value': 15}]
2025-05-16 12:02:49,140 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMOE
2025-05-16 12:02:49,656 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMOE
2025-05-16 12:02:49,656 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27846.36, 'new_value': 29890.36}, {'field': 'total_amount', 'old_value': 31845.36, 'new_value': 33889.36}, {'field': 'order_count', 'old_value': 2121, 'new_value': 2247}]
2025-05-16 12:02:49,656 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-16 12:02:50,187 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-16 12:02:50,203 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13427.0, 'new_value': 14111.0}, {'field': 'total_amount', 'old_value': 13427.0, 'new_value': 14111.0}, {'field': 'order_count', 'old_value': 79, 'new_value': 82}]
2025-05-16 12:02:50,203 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-16 12:02:50,703 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-16 12:02:50,703 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20701.41, 'new_value': 22073.49}, {'field': 'total_amount', 'old_value': 20701.41, 'new_value': 22073.49}, {'field': 'order_count', 'old_value': 805, 'new_value': 898}]
2025-05-16 12:02:50,703 - INFO - 日期 2025-05 处理完成 - 更新: 300 条，插入: 0 条，错误: 0 条
2025-05-16 12:02:50,703 - INFO - 数据同步完成！更新: 302 条，插入: 0 条，错误: 0 条
2025-05-16 12:02:50,703 - INFO - =================同步完成====================
2025-05-16 15:00:01,855 - INFO - =================使用默认全量同步=============
2025-05-16 15:00:03,230 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-16 15:00:03,230 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-16 15:00:03,262 - INFO - 开始处理日期: 2025-01
2025-05-16 15:00:03,262 - INFO - Request Parameters - Page 1:
2025-05-16 15:00:03,262 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 15:00:03,262 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 15:00:04,309 - INFO - Response - Page 1:
2025-05-16 15:00:04,512 - INFO - 第 1 页获取到 100 条记录
2025-05-16 15:00:04,512 - INFO - Request Parameters - Page 2:
2025-05-16 15:00:04,512 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 15:00:04,512 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 15:00:05,277 - INFO - Response - Page 2:
2025-05-16 15:00:05,480 - INFO - 第 2 页获取到 100 条记录
2025-05-16 15:00:05,480 - INFO - Request Parameters - Page 3:
2025-05-16 15:00:05,480 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 15:00:05,480 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 15:00:05,918 - INFO - Response - Page 3:
2025-05-16 15:00:06,121 - INFO - 第 3 页获取到 100 条记录
2025-05-16 15:00:06,121 - INFO - Request Parameters - Page 4:
2025-05-16 15:00:06,121 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 15:00:06,121 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 15:00:06,668 - INFO - Response - Page 4:
2025-05-16 15:00:06,871 - INFO - 第 4 页获取到 100 条记录
2025-05-16 15:00:06,871 - INFO - Request Parameters - Page 5:
2025-05-16 15:00:06,871 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 15:00:06,871 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 15:00:07,387 - INFO - Response - Page 5:
2025-05-16 15:00:07,590 - INFO - 第 5 页获取到 100 条记录
2025-05-16 15:00:07,590 - INFO - Request Parameters - Page 6:
2025-05-16 15:00:07,590 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 15:00:07,590 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 15:00:08,059 - INFO - Response - Page 6:
2025-05-16 15:00:08,262 - INFO - 第 6 页获取到 100 条记录
2025-05-16 15:00:08,262 - INFO - Request Parameters - Page 7:
2025-05-16 15:00:08,262 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 15:00:08,262 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 15:00:08,762 - INFO - Response - Page 7:
2025-05-16 15:00:08,965 - INFO - 第 7 页获取到 82 条记录
2025-05-16 15:00:08,965 - INFO - 查询完成，共获取到 682 条记录
2025-05-16 15:00:08,965 - INFO - 获取到 682 条表单数据
2025-05-16 15:00:08,965 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-16 15:00:08,980 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-16 15:00:08,980 - INFO - 开始处理日期: 2025-02
2025-05-16 15:00:08,980 - INFO - Request Parameters - Page 1:
2025-05-16 15:00:08,980 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 15:00:08,980 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 15:00:09,559 - INFO - Response - Page 1:
2025-05-16 15:00:09,762 - INFO - 第 1 页获取到 100 条记录
2025-05-16 15:00:09,762 - INFO - Request Parameters - Page 2:
2025-05-16 15:00:09,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 15:00:09,762 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 15:00:10,355 - INFO - Response - Page 2:
2025-05-16 15:00:10,559 - INFO - 第 2 页获取到 100 条记录
2025-05-16 15:00:10,559 - INFO - Request Parameters - Page 3:
2025-05-16 15:00:10,559 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 15:00:10,559 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 15:00:11,058 - INFO - Response - Page 3:
2025-05-16 15:00:11,262 - INFO - 第 3 页获取到 100 条记录
2025-05-16 15:00:11,262 - INFO - Request Parameters - Page 4:
2025-05-16 15:00:11,262 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 15:00:11,262 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 15:00:11,808 - INFO - Response - Page 4:
2025-05-16 15:00:12,012 - INFO - 第 4 页获取到 100 条记录
2025-05-16 15:00:12,012 - INFO - Request Parameters - Page 5:
2025-05-16 15:00:12,012 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 15:00:12,012 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 15:00:12,558 - INFO - Response - Page 5:
2025-05-16 15:00:12,762 - INFO - 第 5 页获取到 100 条记录
2025-05-16 15:00:12,762 - INFO - Request Parameters - Page 6:
2025-05-16 15:00:12,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 15:00:12,762 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 15:00:13,293 - INFO - Response - Page 6:
2025-05-16 15:00:13,496 - INFO - 第 6 页获取到 100 条记录
2025-05-16 15:00:13,496 - INFO - Request Parameters - Page 7:
2025-05-16 15:00:13,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 15:00:13,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 15:00:13,980 - INFO - Response - Page 7:
2025-05-16 15:00:14,183 - INFO - 第 7 页获取到 70 条记录
2025-05-16 15:00:14,183 - INFO - 查询完成，共获取到 670 条记录
2025-05-16 15:00:14,183 - INFO - 获取到 670 条表单数据
2025-05-16 15:00:14,183 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-16 15:00:14,199 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-16 15:00:14,199 - INFO - 开始处理日期: 2025-03
2025-05-16 15:00:14,199 - INFO - Request Parameters - Page 1:
2025-05-16 15:00:14,199 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 15:00:14,199 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 15:00:14,762 - INFO - Response - Page 1:
2025-05-16 15:00:14,965 - INFO - 第 1 页获取到 100 条记录
2025-05-16 15:00:14,965 - INFO - Request Parameters - Page 2:
2025-05-16 15:00:14,965 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 15:00:14,965 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 15:00:15,387 - INFO - Response - Page 2:
2025-05-16 15:00:15,590 - INFO - 第 2 页获取到 100 条记录
2025-05-16 15:00:15,590 - INFO - Request Parameters - Page 3:
2025-05-16 15:00:15,590 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 15:00:15,590 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 15:00:16,168 - INFO - Response - Page 3:
2025-05-16 15:00:16,371 - INFO - 第 3 页获取到 100 条记录
2025-05-16 15:00:16,371 - INFO - Request Parameters - Page 4:
2025-05-16 15:00:16,371 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 15:00:16,371 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 15:00:16,840 - INFO - Response - Page 4:
2025-05-16 15:00:17,043 - INFO - 第 4 页获取到 100 条记录
2025-05-16 15:00:17,043 - INFO - Request Parameters - Page 5:
2025-05-16 15:00:17,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 15:00:17,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 15:00:17,590 - INFO - Response - Page 5:
2025-05-16 15:00:17,793 - INFO - 第 5 页获取到 100 条记录
2025-05-16 15:00:17,793 - INFO - Request Parameters - Page 6:
2025-05-16 15:00:17,793 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 15:00:17,793 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 15:00:18,308 - INFO - Response - Page 6:
2025-05-16 15:00:18,512 - INFO - 第 6 页获取到 100 条记录
2025-05-16 15:00:18,512 - INFO - Request Parameters - Page 7:
2025-05-16 15:00:18,512 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 15:00:18,512 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 15:00:19,012 - INFO - Response - Page 7:
2025-05-16 15:00:19,215 - INFO - 第 7 页获取到 61 条记录
2025-05-16 15:00:19,215 - INFO - 查询完成，共获取到 661 条记录
2025-05-16 15:00:19,215 - INFO - 获取到 661 条表单数据
2025-05-16 15:00:19,215 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-16 15:00:19,230 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-16 15:00:19,230 - INFO - 开始处理日期: 2025-04
2025-05-16 15:00:19,230 - INFO - Request Parameters - Page 1:
2025-05-16 15:00:19,230 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 15:00:19,230 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 15:00:19,715 - INFO - Response - Page 1:
2025-05-16 15:00:19,918 - INFO - 第 1 页获取到 100 条记录
2025-05-16 15:00:19,918 - INFO - Request Parameters - Page 2:
2025-05-16 15:00:19,918 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 15:00:19,918 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 15:00:20,449 - INFO - Response - Page 2:
2025-05-16 15:00:20,652 - INFO - 第 2 页获取到 100 条记录
2025-05-16 15:00:20,652 - INFO - Request Parameters - Page 3:
2025-05-16 15:00:20,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 15:00:20,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 15:00:21,262 - INFO - Response - Page 3:
2025-05-16 15:00:21,465 - INFO - 第 3 页获取到 100 条记录
2025-05-16 15:00:21,465 - INFO - Request Parameters - Page 4:
2025-05-16 15:00:21,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 15:00:21,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 15:00:22,090 - INFO - Response - Page 4:
2025-05-16 15:00:22,293 - INFO - 第 4 页获取到 100 条记录
2025-05-16 15:00:22,293 - INFO - Request Parameters - Page 5:
2025-05-16 15:00:22,293 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 15:00:22,293 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 15:00:22,746 - INFO - Response - Page 5:
2025-05-16 15:00:22,949 - INFO - 第 5 页获取到 100 条记录
2025-05-16 15:00:22,949 - INFO - Request Parameters - Page 6:
2025-05-16 15:00:22,949 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 15:00:22,949 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 15:00:23,449 - INFO - Response - Page 6:
2025-05-16 15:00:23,652 - INFO - 第 6 页获取到 100 条记录
2025-05-16 15:00:23,652 - INFO - Request Parameters - Page 7:
2025-05-16 15:00:23,652 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 15:00:23,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 15:00:24,058 - INFO - Response - Page 7:
2025-05-16 15:00:24,262 - INFO - 第 7 页获取到 54 条记录
2025-05-16 15:00:24,262 - INFO - 查询完成，共获取到 654 条记录
2025-05-16 15:00:24,262 - INFO - 获取到 654 条表单数据
2025-05-16 15:00:24,262 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-16 15:00:24,277 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MX1
2025-05-16 15:00:24,777 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MX1
2025-05-16 15:00:24,777 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 178288.0, 'new_value': 192614.0}, {'field': 'total_amount', 'old_value': 187695.0, 'new_value': 202021.0}]
2025-05-16 15:00:24,777 - INFO - 日期 2025-04 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-16 15:00:24,777 - INFO - 开始处理日期: 2025-05
2025-05-16 15:00:24,777 - INFO - Request Parameters - Page 1:
2025-05-16 15:00:24,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 15:00:24,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 15:00:25,324 - INFO - Response - Page 1:
2025-05-16 15:00:25,527 - INFO - 第 1 页获取到 100 条记录
2025-05-16 15:00:25,527 - INFO - Request Parameters - Page 2:
2025-05-16 15:00:25,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 15:00:25,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 15:00:26,012 - INFO - Response - Page 2:
2025-05-16 15:00:26,215 - INFO - 第 2 页获取到 100 条记录
2025-05-16 15:00:26,215 - INFO - Request Parameters - Page 3:
2025-05-16 15:00:26,215 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 15:00:26,215 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 15:00:26,746 - INFO - Response - Page 3:
2025-05-16 15:00:26,949 - INFO - 第 3 页获取到 100 条记录
2025-05-16 15:00:26,949 - INFO - Request Parameters - Page 4:
2025-05-16 15:00:26,949 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 15:00:26,949 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 15:00:27,902 - INFO - Response - Page 4:
2025-05-16 15:00:28,105 - INFO - 第 4 页获取到 100 条记录
2025-05-16 15:00:28,105 - INFO - Request Parameters - Page 5:
2025-05-16 15:00:28,105 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 15:00:28,105 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 15:00:28,637 - INFO - Response - Page 5:
2025-05-16 15:00:28,840 - INFO - 第 5 页获取到 100 条记录
2025-05-16 15:00:28,840 - INFO - Request Parameters - Page 6:
2025-05-16 15:00:28,840 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 15:00:28,840 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 15:00:29,387 - INFO - Response - Page 6:
2025-05-16 15:00:29,590 - INFO - 第 6 页获取到 100 条记录
2025-05-16 15:00:29,590 - INFO - Request Parameters - Page 7:
2025-05-16 15:00:29,590 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 15:00:29,590 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 15:00:29,980 - INFO - Response - Page 7:
2025-05-16 15:00:30,183 - INFO - 第 7 页获取到 25 条记录
2025-05-16 15:00:30,183 - INFO - 查询完成，共获取到 625 条记录
2025-05-16 15:00:30,183 - INFO - 获取到 625 条表单数据
2025-05-16 15:00:30,183 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-16 15:00:30,183 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-16 15:00:30,621 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-16 15:00:30,621 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100245.47, 'new_value': 110905.53}, {'field': 'total_amount', 'old_value': 100245.47, 'new_value': 110905.53}, {'field': 'order_count', 'old_value': 4153, 'new_value': 4477}]
2025-05-16 15:00:30,636 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-16 15:00:31,246 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-16 15:00:31,246 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17546.83, 'new_value': 18805.83}, {'field': 'total_amount', 'old_value': 17546.83, 'new_value': 18805.83}, {'field': 'order_count', 'old_value': 652, 'new_value': 703}]
2025-05-16 15:00:31,246 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-16 15:00:31,699 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-16 15:00:31,699 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 56070.2, 'new_value': 60429.3}, {'field': 'total_amount', 'old_value': 63299.27, 'new_value': 67658.37}, {'field': 'order_count', 'old_value': 354, 'new_value': 384}]
2025-05-16 15:00:31,715 - INFO - 日期 2025-05 处理完成 - 更新: 3 条，插入: 0 条，错误: 0 条
2025-05-16 15:00:31,715 - INFO - 数据同步完成！更新: 4 条，插入: 0 条，错误: 0 条
2025-05-16 15:00:31,715 - INFO - =================同步完成====================
2025-05-16 18:00:01,988 - INFO - =================使用默认全量同步=============
2025-05-16 18:00:03,363 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-16 18:00:03,363 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-16 18:00:03,394 - INFO - 开始处理日期: 2025-01
2025-05-16 18:00:03,394 - INFO - Request Parameters - Page 1:
2025-05-16 18:00:03,410 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 18:00:03,410 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 18:00:04,535 - INFO - Response - Page 1:
2025-05-16 18:00:04,738 - INFO - 第 1 页获取到 100 条记录
2025-05-16 18:00:04,738 - INFO - Request Parameters - Page 2:
2025-05-16 18:00:04,738 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 18:00:04,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 18:00:05,535 - INFO - Response - Page 2:
2025-05-16 18:00:05,738 - INFO - 第 2 页获取到 100 条记录
2025-05-16 18:00:05,738 - INFO - Request Parameters - Page 3:
2025-05-16 18:00:05,738 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 18:00:05,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 18:00:06,301 - INFO - Response - Page 3:
2025-05-16 18:00:06,504 - INFO - 第 3 页获取到 100 条记录
2025-05-16 18:00:06,504 - INFO - Request Parameters - Page 4:
2025-05-16 18:00:06,504 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 18:00:06,504 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 18:00:07,113 - INFO - Response - Page 4:
2025-05-16 18:00:07,316 - INFO - 第 4 页获取到 100 条记录
2025-05-16 18:00:07,316 - INFO - Request Parameters - Page 5:
2025-05-16 18:00:07,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 18:00:07,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 18:00:07,879 - INFO - Response - Page 5:
2025-05-16 18:00:08,082 - INFO - 第 5 页获取到 100 条记录
2025-05-16 18:00:08,082 - INFO - Request Parameters - Page 6:
2025-05-16 18:00:08,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 18:00:08,082 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 18:00:08,598 - INFO - Response - Page 6:
2025-05-16 18:00:08,801 - INFO - 第 6 页获取到 100 条记录
2025-05-16 18:00:08,801 - INFO - Request Parameters - Page 7:
2025-05-16 18:00:08,801 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 18:00:08,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 18:00:09,394 - INFO - Response - Page 7:
2025-05-16 18:00:09,598 - INFO - 第 7 页获取到 82 条记录
2025-05-16 18:00:09,598 - INFO - 查询完成，共获取到 682 条记录
2025-05-16 18:00:09,598 - INFO - 获取到 682 条表单数据
2025-05-16 18:00:09,598 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-16 18:00:09,613 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-16 18:00:09,613 - INFO - 开始处理日期: 2025-02
2025-05-16 18:00:09,613 - INFO - Request Parameters - Page 1:
2025-05-16 18:00:09,613 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 18:00:09,613 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 18:00:10,176 - INFO - Response - Page 1:
2025-05-16 18:00:10,379 - INFO - 第 1 页获取到 100 条记录
2025-05-16 18:00:10,379 - INFO - Request Parameters - Page 2:
2025-05-16 18:00:10,379 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 18:00:10,379 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 18:00:10,988 - INFO - Response - Page 2:
2025-05-16 18:00:11,191 - INFO - 第 2 页获取到 100 条记录
2025-05-16 18:00:11,191 - INFO - Request Parameters - Page 3:
2025-05-16 18:00:11,191 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 18:00:11,191 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 18:00:11,785 - INFO - Response - Page 3:
2025-05-16 18:00:11,988 - INFO - 第 3 页获取到 100 条记录
2025-05-16 18:00:11,988 - INFO - Request Parameters - Page 4:
2025-05-16 18:00:11,988 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 18:00:11,988 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 18:00:12,582 - INFO - Response - Page 4:
2025-05-16 18:00:12,785 - INFO - 第 4 页获取到 100 条记录
2025-05-16 18:00:12,785 - INFO - Request Parameters - Page 5:
2025-05-16 18:00:12,785 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 18:00:12,785 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 18:00:13,348 - INFO - Response - Page 5:
2025-05-16 18:00:13,551 - INFO - 第 5 页获取到 100 条记录
2025-05-16 18:00:13,551 - INFO - Request Parameters - Page 6:
2025-05-16 18:00:13,551 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 18:00:13,551 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 18:00:14,051 - INFO - Response - Page 6:
2025-05-16 18:00:14,254 - INFO - 第 6 页获取到 100 条记录
2025-05-16 18:00:14,254 - INFO - Request Parameters - Page 7:
2025-05-16 18:00:14,254 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 18:00:14,254 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 18:00:14,769 - INFO - Response - Page 7:
2025-05-16 18:00:14,973 - INFO - 第 7 页获取到 70 条记录
2025-05-16 18:00:14,973 - INFO - 查询完成，共获取到 670 条记录
2025-05-16 18:00:14,973 - INFO - 获取到 670 条表单数据
2025-05-16 18:00:14,973 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-16 18:00:14,988 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-16 18:00:14,988 - INFO - 开始处理日期: 2025-03
2025-05-16 18:00:14,988 - INFO - Request Parameters - Page 1:
2025-05-16 18:00:14,988 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 18:00:14,988 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 18:00:15,566 - INFO - Response - Page 1:
2025-05-16 18:00:15,769 - INFO - 第 1 页获取到 100 条记录
2025-05-16 18:00:15,769 - INFO - Request Parameters - Page 2:
2025-05-16 18:00:15,769 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 18:00:15,769 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 18:00:16,316 - INFO - Response - Page 2:
2025-05-16 18:00:16,519 - INFO - 第 2 页获取到 100 条记录
2025-05-16 18:00:16,519 - INFO - Request Parameters - Page 3:
2025-05-16 18:00:16,519 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 18:00:16,519 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 18:00:17,082 - INFO - Response - Page 3:
2025-05-16 18:00:17,285 - INFO - 第 3 页获取到 100 条记录
2025-05-16 18:00:17,285 - INFO - Request Parameters - Page 4:
2025-05-16 18:00:17,285 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 18:00:17,285 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 18:00:17,910 - INFO - Response - Page 4:
2025-05-16 18:00:18,113 - INFO - 第 4 页获取到 100 条记录
2025-05-16 18:00:18,113 - INFO - Request Parameters - Page 5:
2025-05-16 18:00:18,113 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 18:00:18,113 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 18:00:18,754 - INFO - Response - Page 5:
2025-05-16 18:00:18,957 - INFO - 第 5 页获取到 100 条记录
2025-05-16 18:00:18,957 - INFO - Request Parameters - Page 6:
2025-05-16 18:00:18,957 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 18:00:18,957 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 18:00:19,504 - INFO - Response - Page 6:
2025-05-16 18:00:19,707 - INFO - 第 6 页获取到 100 条记录
2025-05-16 18:00:19,707 - INFO - Request Parameters - Page 7:
2025-05-16 18:00:19,707 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 18:00:19,707 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 18:00:20,176 - INFO - Response - Page 7:
2025-05-16 18:00:20,379 - INFO - 第 7 页获取到 61 条记录
2025-05-16 18:00:20,379 - INFO - 查询完成，共获取到 661 条记录
2025-05-16 18:00:20,379 - INFO - 获取到 661 条表单数据
2025-05-16 18:00:20,379 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-16 18:00:20,394 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-16 18:00:20,394 - INFO - 开始处理日期: 2025-04
2025-05-16 18:00:20,394 - INFO - Request Parameters - Page 1:
2025-05-16 18:00:20,394 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 18:00:20,394 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 18:00:20,973 - INFO - Response - Page 1:
2025-05-16 18:00:21,176 - INFO - 第 1 页获取到 100 条记录
2025-05-16 18:00:21,176 - INFO - Request Parameters - Page 2:
2025-05-16 18:00:21,176 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 18:00:21,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 18:00:21,723 - INFO - Response - Page 2:
2025-05-16 18:00:21,926 - INFO - 第 2 页获取到 100 条记录
2025-05-16 18:00:21,926 - INFO - Request Parameters - Page 3:
2025-05-16 18:00:21,926 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 18:00:21,926 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 18:00:22,519 - INFO - Response - Page 3:
2025-05-16 18:00:22,722 - INFO - 第 3 页获取到 100 条记录
2025-05-16 18:00:22,722 - INFO - Request Parameters - Page 4:
2025-05-16 18:00:22,722 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 18:00:22,722 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 18:00:23,332 - INFO - Response - Page 4:
2025-05-16 18:00:23,535 - INFO - 第 4 页获取到 100 条记录
2025-05-16 18:00:23,535 - INFO - Request Parameters - Page 5:
2025-05-16 18:00:23,535 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 18:00:23,535 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 18:00:24,066 - INFO - Response - Page 5:
2025-05-16 18:00:24,269 - INFO - 第 5 页获取到 100 条记录
2025-05-16 18:00:24,269 - INFO - Request Parameters - Page 6:
2025-05-16 18:00:24,269 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 18:00:24,269 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 18:00:24,847 - INFO - Response - Page 6:
2025-05-16 18:00:25,051 - INFO - 第 6 页获取到 100 条记录
2025-05-16 18:00:25,051 - INFO - Request Parameters - Page 7:
2025-05-16 18:00:25,051 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 18:00:25,051 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 18:00:25,457 - INFO - Response - Page 7:
2025-05-16 18:00:25,660 - INFO - 第 7 页获取到 54 条记录
2025-05-16 18:00:25,660 - INFO - 查询完成，共获取到 654 条记录
2025-05-16 18:00:25,660 - INFO - 获取到 654 条表单数据
2025-05-16 18:00:25,660 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-16 18:00:25,660 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M54
2025-05-16 18:00:26,160 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M54
2025-05-16 18:00:26,176 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 3, 'new_value': 2}]
2025-05-16 18:00:26,176 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MGB
2025-05-16 18:00:26,722 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MGB
2025-05-16 18:00:26,722 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87582.0, 'new_value': 91118.0}, {'field': 'total_amount', 'old_value': 90163.0, 'new_value': 93699.0}]
2025-05-16 18:00:26,722 - INFO - 日期 2025-04 处理完成 - 更新: 2 条，插入: 0 条，错误: 0 条
2025-05-16 18:00:26,722 - INFO - 开始处理日期: 2025-05
2025-05-16 18:00:26,722 - INFO - Request Parameters - Page 1:
2025-05-16 18:00:26,722 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 18:00:26,722 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 18:00:27,347 - INFO - Response - Page 1:
2025-05-16 18:00:27,551 - INFO - 第 1 页获取到 100 条记录
2025-05-16 18:00:27,551 - INFO - Request Parameters - Page 2:
2025-05-16 18:00:27,551 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 18:00:27,551 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 18:00:28,129 - INFO - Response - Page 2:
2025-05-16 18:00:28,332 - INFO - 第 2 页获取到 100 条记录
2025-05-16 18:00:28,332 - INFO - Request Parameters - Page 3:
2025-05-16 18:00:28,332 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 18:00:28,332 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 18:00:28,832 - INFO - Response - Page 3:
2025-05-16 18:00:29,035 - INFO - 第 3 页获取到 100 条记录
2025-05-16 18:00:29,035 - INFO - Request Parameters - Page 4:
2025-05-16 18:00:29,035 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 18:00:29,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 18:00:29,535 - INFO - Response - Page 4:
2025-05-16 18:00:29,738 - INFO - 第 4 页获取到 100 条记录
2025-05-16 18:00:29,738 - INFO - Request Parameters - Page 5:
2025-05-16 18:00:29,738 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 18:00:29,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 18:00:30,301 - INFO - Response - Page 5:
2025-05-16 18:00:30,504 - INFO - 第 5 页获取到 100 条记录
2025-05-16 18:00:30,504 - INFO - Request Parameters - Page 6:
2025-05-16 18:00:30,504 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 18:00:30,504 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 18:00:31,035 - INFO - Response - Page 6:
2025-05-16 18:00:31,238 - INFO - 第 6 页获取到 100 条记录
2025-05-16 18:00:31,238 - INFO - Request Parameters - Page 7:
2025-05-16 18:00:31,238 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 18:00:31,238 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 18:00:31,582 - INFO - Response - Page 7:
2025-05-16 18:00:31,785 - INFO - 第 7 页获取到 25 条记录
2025-05-16 18:00:31,785 - INFO - 查询完成，共获取到 625 条记录
2025-05-16 18:00:31,785 - INFO - 获取到 625 条表单数据
2025-05-16 18:00:31,785 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-16 18:00:31,785 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-16 18:00:32,222 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-16 18:00:32,222 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13670.0, 'new_value': 14017.0}, {'field': 'total_amount', 'old_value': 13670.0, 'new_value': 14017.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 31}]
2025-05-16 18:00:32,222 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-16 18:00:32,707 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-16 18:00:32,707 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 753000.0, 'new_value': 758000.0}, {'field': 'total_amount', 'old_value': 753000.0, 'new_value': 758000.0}]
2025-05-16 18:00:32,707 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDL
2025-05-16 18:00:33,129 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDL
2025-05-16 18:00:33,129 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40827.0, 'new_value': 53627.0}, {'field': 'total_amount', 'old_value': 40827.0, 'new_value': 53627.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-05-16 18:00:33,129 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-16 18:00:33,629 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-16 18:00:33,629 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26490.0, 'new_value': 29970.0}, {'field': 'total_amount', 'old_value': 26490.0, 'new_value': 29970.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 16}]
2025-05-16 18:00:33,629 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-16 18:00:34,269 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-16 18:00:34,269 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87888.75, 'new_value': 89017.08}, {'field': 'total_amount', 'old_value': 87888.75, 'new_value': 89017.08}, {'field': 'order_count', 'old_value': 7540, 'new_value': 8061}]
2025-05-16 18:00:34,269 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-16 18:00:34,738 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-16 18:00:34,738 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3737856.0, 'new_value': 3757767.0}, {'field': 'total_amount', 'old_value': 3737856.0, 'new_value': 3757767.0}, {'field': 'order_count', 'old_value': 59696, 'new_value': 63688}]
2025-05-16 18:00:34,738 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-16 18:00:35,176 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-16 18:00:35,176 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31955.0, 'new_value': 32819.0}, {'field': 'total_amount', 'old_value': 31955.0, 'new_value': 32819.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 32}]
2025-05-16 18:00:35,176 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM8
2025-05-16 18:00:35,597 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM8
2025-05-16 18:00:35,597 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54030.74, 'new_value': 53716.74}, {'field': 'total_amount', 'old_value': 54030.74, 'new_value': 53716.74}, {'field': 'order_count', 'old_value': 1651, 'new_value': 1698}]
2025-05-16 18:00:35,597 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-16 18:00:36,051 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-16 18:00:36,051 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73336.0, 'new_value': 73439.0}, {'field': 'total_amount', 'old_value': 73336.0, 'new_value': 73439.0}, {'field': 'order_count', 'old_value': 2684, 'new_value': 2825}]
2025-05-16 18:00:36,051 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-16 18:00:36,472 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-16 18:00:36,472 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18624.07, 'new_value': 18710.07}, {'field': 'total_amount', 'old_value': 18624.07, 'new_value': 18710.07}, {'field': 'order_count', 'old_value': 1733, 'new_value': 1845}]
2025-05-16 18:00:36,472 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEM
2025-05-16 18:00:36,941 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEM
2025-05-16 18:00:36,941 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26768.7, 'new_value': 27413.6}, {'field': 'total_amount', 'old_value': 26768.7, 'new_value': 27413.6}, {'field': 'order_count', 'old_value': 288, 'new_value': 293}]
2025-05-16 18:00:36,941 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-16 18:00:37,426 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-16 18:00:37,426 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4692200.0, 'new_value': 4728900.0}, {'field': 'total_amount', 'old_value': 4692200.0, 'new_value': 4728900.0}]
2025-05-16 18:00:37,426 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-16 18:00:37,894 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-16 18:00:37,894 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 193660.0, 'new_value': 192773.0}, {'field': 'total_amount', 'old_value': 202478.99, 'new_value': 201591.99}]
2025-05-16 18:00:37,894 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-16 18:00:38,363 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-16 18:00:38,363 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 257866.2, 'new_value': 274295.1}, {'field': 'total_amount', 'old_value': 257866.2, 'new_value': 274295.1}, {'field': 'order_count', 'old_value': 500, 'new_value': 516}]
2025-05-16 18:00:38,363 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-16 18:00:38,879 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-16 18:00:38,879 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47674.0, 'new_value': 50442.0}, {'field': 'total_amount', 'old_value': 47674.0, 'new_value': 50442.0}]
2025-05-16 18:00:38,879 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-16 18:00:39,332 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-16 18:00:39,332 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18650.0, 'new_value': 19085.0}, {'field': 'total_amount', 'old_value': 20026.0, 'new_value': 20461.0}, {'field': 'order_count', 'old_value': 2080, 'new_value': 2207}]
2025-05-16 18:00:39,347 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-16 18:00:39,769 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-16 18:00:39,769 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 540226.0, 'new_value': 562195.0}, {'field': 'total_amount', 'old_value': 540226.0, 'new_value': 562195.0}, {'field': 'order_count', 'old_value': 1109, 'new_value': 1194}]
2025-05-16 18:00:39,769 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4E
2025-05-16 18:00:40,426 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4E
2025-05-16 18:00:40,426 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53484.0, 'new_value': 55364.0}, {'field': 'total_amount', 'old_value': 53484.0, 'new_value': 55364.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-05-16 18:00:40,426 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-16 18:00:40,894 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-16 18:00:40,894 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63028.0, 'new_value': 62862.0}, {'field': 'total_amount', 'old_value': 63028.0, 'new_value': 62862.0}, {'field': 'order_count', 'old_value': 396, 'new_value': 421}]
2025-05-16 18:00:40,894 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMEE
2025-05-16 18:00:41,316 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMEE
2025-05-16 18:00:41,316 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6818.0, 'new_value': 7117.0}, {'field': 'total_amount', 'old_value': 6818.0, 'new_value': 7117.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 23}]
2025-05-16 18:00:41,316 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMD
2025-05-16 18:00:41,847 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMD
2025-05-16 18:00:41,847 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1697.0, 'new_value': 2096.0}, {'field': 'total_amount', 'old_value': 7295.0, 'new_value': 7694.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-05-16 18:00:41,847 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-16 18:00:42,379 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-16 18:00:42,379 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96905.17, 'new_value': 101479.17}, {'field': 'total_amount', 'old_value': 96905.17, 'new_value': 101479.17}, {'field': 'order_count', 'old_value': 795, 'new_value': 844}]
2025-05-16 18:00:42,379 - INFO - 日期 2025-05 处理完成 - 更新: 22 条，插入: 0 条，错误: 0 条
2025-05-16 18:00:42,379 - INFO - 数据同步完成！更新: 24 条，插入: 0 条，错误: 0 条
2025-05-16 18:00:42,379 - INFO - =================同步完成====================
2025-05-16 21:00:01,972 - INFO - =================使用默认全量同步=============
2025-05-16 21:00:03,318 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-16 21:00:03,319 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-16 21:00:03,345 - INFO - 开始处理日期: 2025-01
2025-05-16 21:00:03,348 - INFO - Request Parameters - Page 1:
2025-05-16 21:00:03,348 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 21:00:03,348 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 21:00:04,513 - INFO - Response - Page 1:
2025-05-16 21:00:04,713 - INFO - 第 1 页获取到 100 条记录
2025-05-16 21:00:04,713 - INFO - Request Parameters - Page 2:
2025-05-16 21:00:04,713 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 21:00:04,713 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 21:00:05,269 - INFO - Response - Page 2:
2025-05-16 21:00:05,469 - INFO - 第 2 页获取到 100 条记录
2025-05-16 21:00:05,469 - INFO - Request Parameters - Page 3:
2025-05-16 21:00:05,469 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 21:00:05,469 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 21:00:05,970 - INFO - Response - Page 3:
2025-05-16 21:00:06,170 - INFO - 第 3 页获取到 100 条记录
2025-05-16 21:00:06,170 - INFO - Request Parameters - Page 4:
2025-05-16 21:00:06,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 21:00:06,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 21:00:06,699 - INFO - Response - Page 4:
2025-05-16 21:00:06,899 - INFO - 第 4 页获取到 100 条记录
2025-05-16 21:00:06,899 - INFO - Request Parameters - Page 5:
2025-05-16 21:00:06,899 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 21:00:06,899 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 21:00:07,396 - INFO - Response - Page 5:
2025-05-16 21:00:07,596 - INFO - 第 5 页获取到 100 条记录
2025-05-16 21:00:07,596 - INFO - Request Parameters - Page 6:
2025-05-16 21:00:07,596 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 21:00:07,596 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 21:00:08,083 - INFO - Response - Page 6:
2025-05-16 21:00:08,284 - INFO - 第 6 页获取到 100 条记录
2025-05-16 21:00:08,284 - INFO - Request Parameters - Page 7:
2025-05-16 21:00:08,284 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 21:00:08,284 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 21:00:08,700 - INFO - Response - Page 7:
2025-05-16 21:00:08,902 - INFO - 第 7 页获取到 82 条记录
2025-05-16 21:00:08,902 - INFO - 查询完成，共获取到 682 条记录
2025-05-16 21:00:08,902 - INFO - 获取到 682 条表单数据
2025-05-16 21:00:08,913 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-16 21:00:08,925 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-16 21:00:08,925 - INFO - 开始处理日期: 2025-02
2025-05-16 21:00:08,926 - INFO - Request Parameters - Page 1:
2025-05-16 21:00:08,926 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 21:00:08,926 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 21:00:09,526 - INFO - Response - Page 1:
2025-05-16 21:00:09,726 - INFO - 第 1 页获取到 100 条记录
2025-05-16 21:00:09,726 - INFO - Request Parameters - Page 2:
2025-05-16 21:00:09,726 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 21:00:09,726 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 21:00:10,307 - INFO - Response - Page 2:
2025-05-16 21:00:10,508 - INFO - 第 2 页获取到 100 条记录
2025-05-16 21:00:10,508 - INFO - Request Parameters - Page 3:
2025-05-16 21:00:10,508 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 21:00:10,508 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 21:00:10,968 - INFO - Response - Page 3:
2025-05-16 21:00:11,168 - INFO - 第 3 页获取到 100 条记录
2025-05-16 21:00:11,168 - INFO - Request Parameters - Page 4:
2025-05-16 21:00:11,168 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 21:00:11,168 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 21:00:11,701 - INFO - Response - Page 4:
2025-05-16 21:00:11,903 - INFO - 第 4 页获取到 100 条记录
2025-05-16 21:00:11,903 - INFO - Request Parameters - Page 5:
2025-05-16 21:00:11,903 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 21:00:11,903 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 21:00:12,409 - INFO - Response - Page 5:
2025-05-16 21:00:12,610 - INFO - 第 5 页获取到 100 条记录
2025-05-16 21:00:12,610 - INFO - Request Parameters - Page 6:
2025-05-16 21:00:12,610 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 21:00:12,610 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 21:00:13,140 - INFO - Response - Page 6:
2025-05-16 21:00:13,340 - INFO - 第 6 页获取到 100 条记录
2025-05-16 21:00:13,340 - INFO - Request Parameters - Page 7:
2025-05-16 21:00:13,340 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 21:00:13,340 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 21:00:13,794 - INFO - Response - Page 7:
2025-05-16 21:00:13,994 - INFO - 第 7 页获取到 70 条记录
2025-05-16 21:00:13,994 - INFO - 查询完成，共获取到 670 条记录
2025-05-16 21:00:13,995 - INFO - 获取到 670 条表单数据
2025-05-16 21:00:14,006 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-16 21:00:14,018 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-16 21:00:14,018 - INFO - 开始处理日期: 2025-03
2025-05-16 21:00:14,019 - INFO - Request Parameters - Page 1:
2025-05-16 21:00:14,019 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 21:00:14,019 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 21:00:14,525 - INFO - Response - Page 1:
2025-05-16 21:00:14,725 - INFO - 第 1 页获取到 100 条记录
2025-05-16 21:00:14,725 - INFO - Request Parameters - Page 2:
2025-05-16 21:00:14,725 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 21:00:14,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 21:00:15,265 - INFO - Response - Page 2:
2025-05-16 21:00:15,466 - INFO - 第 2 页获取到 100 条记录
2025-05-16 21:00:15,466 - INFO - Request Parameters - Page 3:
2025-05-16 21:00:15,466 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 21:00:15,466 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 21:00:15,939 - INFO - Response - Page 3:
2025-05-16 21:00:16,139 - INFO - 第 3 页获取到 100 条记录
2025-05-16 21:00:16,139 - INFO - Request Parameters - Page 4:
2025-05-16 21:00:16,139 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 21:00:16,140 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 21:00:16,722 - INFO - Response - Page 4:
2025-05-16 21:00:16,923 - INFO - 第 4 页获取到 100 条记录
2025-05-16 21:00:16,923 - INFO - Request Parameters - Page 5:
2025-05-16 21:00:16,923 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 21:00:16,923 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 21:00:17,375 - INFO - Response - Page 5:
2025-05-16 21:00:17,575 - INFO - 第 5 页获取到 100 条记录
2025-05-16 21:00:17,575 - INFO - Request Parameters - Page 6:
2025-05-16 21:00:17,575 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 21:00:17,575 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 21:00:18,041 - INFO - Response - Page 6:
2025-05-16 21:00:18,241 - INFO - 第 6 页获取到 100 条记录
2025-05-16 21:00:18,241 - INFO - Request Parameters - Page 7:
2025-05-16 21:00:18,241 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 21:00:18,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 21:00:18,680 - INFO - Response - Page 7:
2025-05-16 21:00:18,881 - INFO - 第 7 页获取到 61 条记录
2025-05-16 21:00:18,881 - INFO - 查询完成，共获取到 661 条记录
2025-05-16 21:00:18,881 - INFO - 获取到 661 条表单数据
2025-05-16 21:00:18,894 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-16 21:00:18,906 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-16 21:00:18,906 - INFO - 开始处理日期: 2025-04
2025-05-16 21:00:18,906 - INFO - Request Parameters - Page 1:
2025-05-16 21:00:18,906 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 21:00:18,906 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 21:00:19,457 - INFO - Response - Page 1:
2025-05-16 21:00:19,658 - INFO - 第 1 页获取到 100 条记录
2025-05-16 21:00:19,658 - INFO - Request Parameters - Page 2:
2025-05-16 21:00:19,658 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 21:00:19,658 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 21:00:20,162 - INFO - Response - Page 2:
2025-05-16 21:00:20,363 - INFO - 第 2 页获取到 100 条记录
2025-05-16 21:00:20,363 - INFO - Request Parameters - Page 3:
2025-05-16 21:00:20,363 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 21:00:20,363 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 21:00:20,986 - INFO - Response - Page 3:
2025-05-16 21:00:21,186 - INFO - 第 3 页获取到 100 条记录
2025-05-16 21:00:21,186 - INFO - Request Parameters - Page 4:
2025-05-16 21:00:21,186 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 21:00:21,186 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 21:00:21,679 - INFO - Response - Page 4:
2025-05-16 21:00:21,880 - INFO - 第 4 页获取到 100 条记录
2025-05-16 21:00:21,880 - INFO - Request Parameters - Page 5:
2025-05-16 21:00:21,880 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 21:00:21,880 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 21:00:22,341 - INFO - Response - Page 5:
2025-05-16 21:00:22,541 - INFO - 第 5 页获取到 100 条记录
2025-05-16 21:00:22,541 - INFO - Request Parameters - Page 6:
2025-05-16 21:00:22,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 21:00:22,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 21:00:23,256 - INFO - Response - Page 6:
2025-05-16 21:00:23,456 - INFO - 第 6 页获取到 100 条记录
2025-05-16 21:00:23,456 - INFO - Request Parameters - Page 7:
2025-05-16 21:00:23,456 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 21:00:23,456 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 21:00:23,865 - INFO - Response - Page 7:
2025-05-16 21:00:24,066 - INFO - 第 7 页获取到 54 条记录
2025-05-16 21:00:24,066 - INFO - 查询完成，共获取到 654 条记录
2025-05-16 21:00:24,067 - INFO - 获取到 654 条表单数据
2025-05-16 21:00:24,079 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-16 21:00:24,088 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MX1
2025-05-16 21:00:24,557 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MX1
2025-05-16 21:00:24,557 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4867940.0, 'new_value': 1890000.0}, {'field': 'offline_amount', 'old_value': 2463451.0, 'new_value': 3404991.0}, {'field': 'total_amount', 'old_value': 7331391.0, 'new_value': 5294991.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 21}]
2025-05-16 21:00:24,560 - INFO - 日期 2025-04 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-16 21:00:24,561 - INFO - 开始处理日期: 2025-05
2025-05-16 21:00:24,561 - INFO - Request Parameters - Page 1:
2025-05-16 21:00:24,561 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 21:00:24,561 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 21:00:25,056 - INFO - Response - Page 1:
2025-05-16 21:00:25,256 - INFO - 第 1 页获取到 100 条记录
2025-05-16 21:00:25,256 - INFO - Request Parameters - Page 2:
2025-05-16 21:00:25,256 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 21:00:25,256 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 21:00:25,792 - INFO - Response - Page 2:
2025-05-16 21:00:25,992 - INFO - 第 2 页获取到 100 条记录
2025-05-16 21:00:25,992 - INFO - Request Parameters - Page 3:
2025-05-16 21:00:25,992 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 21:00:25,992 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 21:00:26,504 - INFO - Response - Page 3:
2025-05-16 21:00:26,704 - INFO - 第 3 页获取到 100 条记录
2025-05-16 21:00:26,704 - INFO - Request Parameters - Page 4:
2025-05-16 21:00:26,704 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 21:00:26,704 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 21:00:27,236 - INFO - Response - Page 4:
2025-05-16 21:00:27,436 - INFO - 第 4 页获取到 100 条记录
2025-05-16 21:00:27,436 - INFO - Request Parameters - Page 5:
2025-05-16 21:00:27,436 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 21:00:27,436 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 21:00:27,854 - INFO - Response - Page 5:
2025-05-16 21:00:28,054 - INFO - 第 5 页获取到 100 条记录
2025-05-16 21:00:28,054 - INFO - Request Parameters - Page 6:
2025-05-16 21:00:28,054 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 21:00:28,054 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 21:00:28,541 - INFO - Response - Page 6:
2025-05-16 21:00:28,741 - INFO - 第 6 页获取到 100 条记录
2025-05-16 21:00:28,741 - INFO - Request Parameters - Page 7:
2025-05-16 21:00:28,741 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-16 21:00:28,741 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-16 21:00:29,091 - INFO - Response - Page 7:
2025-05-16 21:00:29,291 - INFO - 第 7 页获取到 25 条记录
2025-05-16 21:00:29,291 - INFO - 查询完成，共获取到 625 条记录
2025-05-16 21:00:29,291 - INFO - 获取到 625 条表单数据
2025-05-16 21:00:29,302 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-16 21:00:29,313 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-16 21:00:29,314 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-05-16 21:00:29,315 - INFO - =================同步完成====================
