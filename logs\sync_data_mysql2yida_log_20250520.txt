2025-05-20 00:30:33,060 - INFO - 使用默认增量同步（当天更新数据）
2025-05-20 00:30:33,061 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 00:30:33,062 - INFO - 查询参数: ('2025-05-20',)
2025-05-20 00:30:33,123 - INFO - MySQL查询成功，增量数据（日期: 2025-05-20），共获取 0 条记录
2025-05-20 00:30:33,124 - ERROR - 未获取到MySQL数据
2025-05-20 00:31:33,124 - INFO - 开始同步昨天与今天的销售数据: 2025-05-19 至 2025-05-20
2025-05-20 00:31:33,124 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 00:31:33,124 - INFO - 查询参数: ('2025-05-19', '2025-05-20')
2025-05-20 00:31:33,184 - INFO - MySQL查询成功，时间段: 2025-05-19 至 2025-05-20，共获取 38 条记录
2025-05-20 00:31:33,185 - INFO - 获取到 1 个日期需要处理: ['2025-05-19']
2025-05-20 00:31:33,185 - INFO - 开始处理日期: 2025-05-19
2025-05-20 00:31:33,188 - INFO - Request Parameters - Page 1:
2025-05-20 00:31:33,189 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 00:31:33,189 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 00:31:41,296 - ERROR - 处理日期 2025-05-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3F602105-108F-73B4-9082-8CD941C25307 Response: {'code': 'ServiceUnavailable', 'requestid': '3F602105-108F-73B4-9082-8CD941C25307', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3F602105-108F-73B4-9082-8CD941C25307)
2025-05-20 00:31:41,296 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-20 00:31:41,296 - INFO - 同步完成
2025-05-20 01:30:33,155 - INFO - 使用默认增量同步（当天更新数据）
2025-05-20 01:30:33,156 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 01:30:33,156 - INFO - 查询参数: ('2025-05-20',)
2025-05-20 01:30:33,217 - INFO - MySQL查询成功，增量数据（日期: 2025-05-20），共获取 0 条记录
2025-05-20 01:30:33,217 - ERROR - 未获取到MySQL数据
2025-05-20 01:31:33,217 - INFO - 开始同步昨天与今天的销售数据: 2025-05-19 至 2025-05-20
2025-05-20 01:31:33,217 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 01:31:33,217 - INFO - 查询参数: ('2025-05-19', '2025-05-20')
2025-05-20 01:31:33,277 - INFO - MySQL查询成功，时间段: 2025-05-19 至 2025-05-20，共获取 38 条记录
2025-05-20 01:31:33,278 - INFO - 获取到 1 个日期需要处理: ['2025-05-19']
2025-05-20 01:31:33,278 - INFO - 开始处理日期: 2025-05-19
2025-05-20 01:31:33,281 - INFO - Request Parameters - Page 1:
2025-05-20 01:31:33,281 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 01:31:33,281 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 01:31:41,407 - ERROR - 处理日期 2025-05-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3684ECD5-2BC1-7B3A-BA51-5336B58C524C Response: {'code': 'ServiceUnavailable', 'requestid': '3684ECD5-2BC1-7B3A-BA51-5336B58C524C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3684ECD5-2BC1-7B3A-BA51-5336B58C524C)
2025-05-20 01:31:41,408 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-20 01:31:41,408 - INFO - 同步完成
2025-05-20 02:30:33,112 - INFO - 使用默认增量同步（当天更新数据）
2025-05-20 02:30:33,112 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 02:30:33,113 - INFO - 查询参数: ('2025-05-20',)
2025-05-20 02:30:33,175 - INFO - MySQL查询成功，增量数据（日期: 2025-05-20），共获取 0 条记录
2025-05-20 02:30:33,175 - ERROR - 未获取到MySQL数据
2025-05-20 02:31:33,176 - INFO - 开始同步昨天与今天的销售数据: 2025-05-19 至 2025-05-20
2025-05-20 02:31:33,176 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 02:31:33,176 - INFO - 查询参数: ('2025-05-19', '2025-05-20')
2025-05-20 02:31:33,235 - INFO - MySQL查询成功，时间段: 2025-05-19 至 2025-05-20，共获取 38 条记录
2025-05-20 02:31:33,236 - INFO - 获取到 1 个日期需要处理: ['2025-05-19']
2025-05-20 02:31:33,236 - INFO - 开始处理日期: 2025-05-19
2025-05-20 02:31:33,239 - INFO - Request Parameters - Page 1:
2025-05-20 02:31:33,239 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 02:31:33,239 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 02:31:41,357 - ERROR - 处理日期 2025-05-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 4E58D038-DB06-71DE-8546-EB260AAC11BF Response: {'code': 'ServiceUnavailable', 'requestid': '4E58D038-DB06-71DE-8546-EB260AAC11BF', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 4E58D038-DB06-71DE-8546-EB260AAC11BF)
2025-05-20 02:31:41,357 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-20 02:31:41,357 - INFO - 同步完成
2025-05-20 03:30:33,159 - INFO - 使用默认增量同步（当天更新数据）
2025-05-20 03:30:33,160 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 03:30:33,160 - INFO - 查询参数: ('2025-05-20',)
2025-05-20 03:30:33,221 - INFO - MySQL查询成功，增量数据（日期: 2025-05-20），共获取 3 条记录
2025-05-20 03:30:33,222 - INFO - 获取到 1 个日期需要处理: ['2025-05-19']
2025-05-20 03:30:33,222 - INFO - 开始处理日期: 2025-05-19
2025-05-20 03:30:33,225 - INFO - Request Parameters - Page 1:
2025-05-20 03:30:33,225 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:30:33,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:30:41,341 - ERROR - 处理日期 2025-05-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: CA7C0747-EBF5-7792-BB41-FC037D78DF71 Response: {'code': 'ServiceUnavailable', 'requestid': 'CA7C0747-EBF5-7792-BB41-FC037D78DF71', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: CA7C0747-EBF5-7792-BB41-FC037D78DF71)
2025-05-20 03:30:41,341 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-20 03:31:41,342 - INFO - 开始同步昨天与今天的销售数据: 2025-05-19 至 2025-05-20
2025-05-20 03:31:41,342 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 03:31:41,342 - INFO - 查询参数: ('2025-05-19', '2025-05-20')
2025-05-20 03:31:41,402 - INFO - MySQL查询成功，时间段: 2025-05-19 至 2025-05-20，共获取 41 条记录
2025-05-20 03:31:41,402 - INFO - 获取到 1 个日期需要处理: ['2025-05-19']
2025-05-20 03:31:41,403 - INFO - 开始处理日期: 2025-05-19
2025-05-20 03:31:41,403 - INFO - Request Parameters - Page 1:
2025-05-20 03:31:41,403 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 03:31:41,403 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 03:31:42,045 - INFO - Response - Page 1:
2025-05-20 03:31:42,046 - INFO - 第 1 页获取到 14 条记录
2025-05-20 03:31:42,246 - INFO - 查询完成，共获取到 14 条记录
2025-05-20 03:31:42,246 - INFO - 获取到 14 条表单数据
2025-05-20 03:31:42,246 - INFO - 当前日期 2025-05-19 有 41 条MySQL数据需要处理
2025-05-20 03:31:42,247 - INFO - 开始批量插入 27 条新记录
2025-05-20 03:31:42,412 - INFO - 批量插入响应状态码: 200
2025-05-20 03:31:42,412 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 19 May 2025 19:31:42 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1308', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '672C5433-C13A-7B56-B8F6-2E6E0E0908E4', 'x-acs-trace-id': '31e101599ad15d7a699b695fd33d25ff', 'etag': '1WYhHxBGPKVW+BYYMWvIGNg8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-20 03:31:42,412 - INFO - 批量插入响应体: {'result': ['FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMP5', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMQ5', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMR5', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMS5', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMT5', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMU5', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMV5', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMW5', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMX5', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMY5', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMZ5', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAM06', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAM16', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAM26', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAM36', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAM46', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAM56', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAM66', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAM76', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAM86', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAM96', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMA6', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMB6', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMC6', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMD6', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAME6', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMF6']}
2025-05-20 03:31:42,412 - INFO - 批量插入表单数据成功，批次 1，共 27 条记录
2025-05-20 03:31:42,412 - INFO - 成功插入的数据ID: ['FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMP5', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMQ5', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMR5', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMS5', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMT5', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMU5', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMV5', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMW5', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMX5', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMY5', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMZ5', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAM06', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAM16', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAM26', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAM36', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAM46', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAM56', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAM66', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAM76', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAM86', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAM96', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMA6', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMB6', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMC6', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMD6', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAME6', 'FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMF6']
2025-05-20 03:31:47,412 - INFO - 批量插入完成，共 27 条记录
2025-05-20 03:31:47,412 - INFO - 日期 2025-05-19 处理完成 - 更新: 0 条，插入: 27 条，错误: 0 条
2025-05-20 03:31:47,412 - INFO - 数据同步完成！更新: 0 条，插入: 27 条，错误: 0 条
2025-05-20 03:31:47,412 - INFO - 同步完成
2025-05-20 04:30:33,249 - INFO - 使用默认增量同步（当天更新数据）
2025-05-20 04:30:33,250 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 04:30:33,250 - INFO - 查询参数: ('2025-05-20',)
2025-05-20 04:30:33,311 - INFO - MySQL查询成功，增量数据（日期: 2025-05-20），共获取 3 条记录
2025-05-20 04:30:33,311 - INFO - 获取到 1 个日期需要处理: ['2025-05-19']
2025-05-20 04:30:33,311 - INFO - 开始处理日期: 2025-05-19
2025-05-20 04:30:33,314 - INFO - Request Parameters - Page 1:
2025-05-20 04:30:33,314 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 04:30:33,314 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 04:30:41,434 - ERROR - 处理日期 2025-05-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 0959AFA0-5848-76B8-83C1-0C5AA242FF23 Response: {'code': 'ServiceUnavailable', 'requestid': '0959AFA0-5848-76B8-83C1-0C5AA242FF23', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 0959AFA0-5848-76B8-83C1-0C5AA242FF23)
2025-05-20 04:30:41,435 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-20 04:31:41,436 - INFO - 开始同步昨天与今天的销售数据: 2025-05-19 至 2025-05-20
2025-05-20 04:31:41,436 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 04:31:41,436 - INFO - 查询参数: ('2025-05-19', '2025-05-20')
2025-05-20 04:31:41,496 - INFO - MySQL查询成功，时间段: 2025-05-19 至 2025-05-20，共获取 41 条记录
2025-05-20 04:31:41,496 - INFO - 获取到 1 个日期需要处理: ['2025-05-19']
2025-05-20 04:31:41,497 - INFO - 开始处理日期: 2025-05-19
2025-05-20 04:31:41,497 - INFO - Request Parameters - Page 1:
2025-05-20 04:31:41,497 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 04:31:41,497 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 04:31:49,612 - ERROR - 处理日期 2025-05-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 82B7D848-FFFF-7F19-B00D-3639C2CF56B3 Response: {'code': 'ServiceUnavailable', 'requestid': '82B7D848-FFFF-7F19-B00D-3639C2CF56B3', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 82B7D848-FFFF-7F19-B00D-3639C2CF56B3)
2025-05-20 04:31:49,612 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-20 04:31:49,612 - INFO - 同步完成
2025-05-20 05:30:33,257 - INFO - 使用默认增量同步（当天更新数据）
2025-05-20 05:30:33,257 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 05:30:33,257 - INFO - 查询参数: ('2025-05-20',)
2025-05-20 05:30:33,318 - INFO - MySQL查询成功，增量数据（日期: 2025-05-20），共获取 3 条记录
2025-05-20 05:30:33,318 - INFO - 获取到 1 个日期需要处理: ['2025-05-19']
2025-05-20 05:30:33,318 - INFO - 开始处理日期: 2025-05-19
2025-05-20 05:30:33,321 - INFO - Request Parameters - Page 1:
2025-05-20 05:30:33,321 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 05:30:33,321 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 05:30:41,428 - ERROR - 处理日期 2025-05-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 6F18133F-A07C-740F-9D43-462B1D6E1743 Response: {'code': 'ServiceUnavailable', 'requestid': '6F18133F-A07C-740F-9D43-462B1D6E1743', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 6F18133F-A07C-740F-9D43-462B1D6E1743)
2025-05-20 05:30:41,428 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-20 05:31:41,429 - INFO - 开始同步昨天与今天的销售数据: 2025-05-19 至 2025-05-20
2025-05-20 05:31:41,429 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 05:31:41,429 - INFO - 查询参数: ('2025-05-19', '2025-05-20')
2025-05-20 05:31:41,491 - INFO - MySQL查询成功，时间段: 2025-05-19 至 2025-05-20，共获取 41 条记录
2025-05-20 05:31:41,492 - INFO - 获取到 1 个日期需要处理: ['2025-05-19']
2025-05-20 05:31:41,492 - INFO - 开始处理日期: 2025-05-19
2025-05-20 05:31:41,492 - INFO - Request Parameters - Page 1:
2025-05-20 05:31:41,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 05:31:41,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 05:31:49,615 - ERROR - 处理日期 2025-05-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3D39CA1D-8409-7F32-A620-A415DB01D53D Response: {'code': 'ServiceUnavailable', 'requestid': '3D39CA1D-8409-7F32-A620-A415DB01D53D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3D39CA1D-8409-7F32-A620-A415DB01D53D)
2025-05-20 05:31:49,615 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-20 05:31:49,615 - INFO - 同步完成
2025-05-20 06:30:33,289 - INFO - 使用默认增量同步（当天更新数据）
2025-05-20 06:30:33,290 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 06:30:33,290 - INFO - 查询参数: ('2025-05-20',)
2025-05-20 06:30:33,352 - INFO - MySQL查询成功，增量数据（日期: 2025-05-20），共获取 3 条记录
2025-05-20 06:30:33,352 - INFO - 获取到 1 个日期需要处理: ['2025-05-19']
2025-05-20 06:30:33,352 - INFO - 开始处理日期: 2025-05-19
2025-05-20 06:30:33,355 - INFO - Request Parameters - Page 1:
2025-05-20 06:30:33,355 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:30:33,355 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:30:41,474 - ERROR - 处理日期 2025-05-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 6CC28676-3C72-778E-8B75-A8B5A00B26FD Response: {'code': 'ServiceUnavailable', 'requestid': '6CC28676-3C72-778E-8B75-A8B5A00B26FD', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 6CC28676-3C72-778E-8B75-A8B5A00B26FD)
2025-05-20 06:30:41,474 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-20 06:31:41,474 - INFO - 开始同步昨天与今天的销售数据: 2025-05-19 至 2025-05-20
2025-05-20 06:31:41,474 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 06:31:41,474 - INFO - 查询参数: ('2025-05-19', '2025-05-20')
2025-05-20 06:31:41,536 - INFO - MySQL查询成功，时间段: 2025-05-19 至 2025-05-20，共获取 41 条记录
2025-05-20 06:31:41,536 - INFO - 获取到 1 个日期需要处理: ['2025-05-19']
2025-05-20 06:31:41,537 - INFO - 开始处理日期: 2025-05-19
2025-05-20 06:31:41,537 - INFO - Request Parameters - Page 1:
2025-05-20 06:31:41,537 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 06:31:41,537 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 06:31:42,308 - INFO - Response - Page 1:
2025-05-20 06:31:42,308 - INFO - 第 1 页获取到 41 条记录
2025-05-20 06:31:42,509 - INFO - 查询完成，共获取到 41 条记录
2025-05-20 06:31:42,509 - INFO - 获取到 41 条表单数据
2025-05-20 06:31:42,509 - INFO - 当前日期 2025-05-19 有 41 条MySQL数据需要处理
2025-05-20 06:31:42,510 - INFO - 日期 2025-05-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 06:31:42,510 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 06:31:42,510 - INFO - 同步完成
2025-05-20 07:30:33,344 - INFO - 使用默认增量同步（当天更新数据）
2025-05-20 07:30:33,344 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 07:30:33,345 - INFO - 查询参数: ('2025-05-20',)
2025-05-20 07:30:33,407 - INFO - MySQL查询成功，增量数据（日期: 2025-05-20），共获取 3 条记录
2025-05-20 07:30:33,407 - INFO - 获取到 1 个日期需要处理: ['2025-05-19']
2025-05-20 07:30:33,407 - INFO - 开始处理日期: 2025-05-19
2025-05-20 07:30:33,410 - INFO - Request Parameters - Page 1:
2025-05-20 07:30:33,410 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 07:30:33,410 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 07:30:41,531 - ERROR - 处理日期 2025-05-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 08695DB8-63E1-70DA-8543-02860365EE50 Response: {'code': 'ServiceUnavailable', 'requestid': '08695DB8-63E1-70DA-8543-02860365EE50', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 08695DB8-63E1-70DA-8543-02860365EE50)
2025-05-20 07:30:41,531 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-20 07:31:41,531 - INFO - 开始同步昨天与今天的销售数据: 2025-05-19 至 2025-05-20
2025-05-20 07:31:41,531 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 07:31:41,531 - INFO - 查询参数: ('2025-05-19', '2025-05-20')
2025-05-20 07:31:41,593 - INFO - MySQL查询成功，时间段: 2025-05-19 至 2025-05-20，共获取 41 条记录
2025-05-20 07:31:41,593 - INFO - 获取到 1 个日期需要处理: ['2025-05-19']
2025-05-20 07:31:41,593 - INFO - 开始处理日期: 2025-05-19
2025-05-20 07:31:41,594 - INFO - Request Parameters - Page 1:
2025-05-20 07:31:41,594 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 07:31:41,594 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 07:31:49,716 - ERROR - 处理日期 2025-05-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 4D59DB0A-6B00-7D4C-8828-6F80EB9457CE Response: {'code': 'ServiceUnavailable', 'requestid': '4D59DB0A-6B00-7D4C-8828-6F80EB9457CE', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 4D59DB0A-6B00-7D4C-8828-6F80EB9457CE)
2025-05-20 07:31:49,716 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-20 07:31:49,716 - INFO - 同步完成
2025-05-20 08:30:33,356 - INFO - 使用默认增量同步（当天更新数据）
2025-05-20 08:30:33,356 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 08:30:33,356 - INFO - 查询参数: ('2025-05-20',)
2025-05-20 08:30:33,419 - INFO - MySQL查询成功，增量数据（日期: 2025-05-20），共获取 13 条记录
2025-05-20 08:30:33,419 - INFO - 获取到 1 个日期需要处理: ['2025-05-19']
2025-05-20 08:30:33,420 - INFO - 开始处理日期: 2025-05-19
2025-05-20 08:30:33,422 - INFO - Request Parameters - Page 1:
2025-05-20 08:30:33,423 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:30:33,423 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:30:41,545 - ERROR - 处理日期 2025-05-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: DFDE5375-F707-7C08-9180-E3F8FD90D5C0 Response: {'code': 'ServiceUnavailable', 'requestid': 'DFDE5375-F707-7C08-9180-E3F8FD90D5C0', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: DFDE5375-F707-7C08-9180-E3F8FD90D5C0)
2025-05-20 08:30:41,546 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-20 08:31:41,546 - INFO - 开始同步昨天与今天的销售数据: 2025-05-19 至 2025-05-20
2025-05-20 08:31:41,546 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 08:31:41,546 - INFO - 查询参数: ('2025-05-19', '2025-05-20')
2025-05-20 08:31:41,608 - INFO - MySQL查询成功，时间段: 2025-05-19 至 2025-05-20，共获取 68 条记录
2025-05-20 08:31:41,608 - INFO - 获取到 1 个日期需要处理: ['2025-05-19']
2025-05-20 08:31:41,609 - INFO - 开始处理日期: 2025-05-19
2025-05-20 08:31:41,609 - INFO - Request Parameters - Page 1:
2025-05-20 08:31:41,609 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 08:31:41,609 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 08:31:42,346 - INFO - Response - Page 1:
2025-05-20 08:31:42,347 - INFO - 第 1 页获取到 41 条记录
2025-05-20 08:31:42,547 - INFO - 查询完成，共获取到 41 条记录
2025-05-20 08:31:42,547 - INFO - 获取到 41 条表单数据
2025-05-20 08:31:42,548 - INFO - 当前日期 2025-05-19 有 68 条MySQL数据需要处理
2025-05-20 08:31:42,549 - INFO - 开始批量插入 27 条新记录
2025-05-20 08:31:42,717 - INFO - 批量插入响应状态码: 200
2025-05-20 08:31:42,718 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 20 May 2025 00:31:42 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1308', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0D895256-022B-79CA-84E5-38AE37DD5C59', 'x-acs-trace-id': '9657dbe283942fd0cef744d2260cd809', 'etag': '1Z8h4vhR5fFclQBt8Mp9siw8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-20 08:31:42,718 - INFO - 批量插入响应体: {'result': ['FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2MM14SVAMZ8', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAM09', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAM19', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAM29', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAM39', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAM49', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAM59', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAM69', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAM79', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAM89', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAM99', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAMA9', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAMB9', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAMC9', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAMD9', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAME9', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAMF9', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAMG9', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAMH9', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAMI9', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAMJ9', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAMK9', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAML9', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAMM9', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAMN9', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAMO9', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAMP9']}
2025-05-20 08:31:42,718 - INFO - 批量插入表单数据成功，批次 1，共 27 条记录
2025-05-20 08:31:42,718 - INFO - 成功插入的数据ID: ['FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2MM14SVAMZ8', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAM09', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAM19', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAM29', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAM39', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAM49', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAM59', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAM69', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAM79', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAM89', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAM99', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAMA9', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAMB9', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAMC9', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAMD9', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAME9', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAMF9', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAMG9', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAMH9', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAMI9', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAMJ9', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAMK9', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAML9', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAMM9', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAMN9', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAMO9', 'FINST-RDC668B1ZCKV1YCZDLK1VCHZXPEQ2NM14SVAMP9']
2025-05-20 08:31:47,719 - INFO - 批量插入完成，共 27 条记录
2025-05-20 08:31:47,719 - INFO - 日期 2025-05-19 处理完成 - 更新: 0 条，插入: 27 条，错误: 0 条
2025-05-20 08:31:47,719 - INFO - 数据同步完成！更新: 0 条，插入: 27 条，错误: 0 条
2025-05-20 08:31:47,719 - INFO - 同步完成
2025-05-20 09:30:33,472 - INFO - 使用默认增量同步（当天更新数据）
2025-05-20 09:30:33,472 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 09:30:33,472 - INFO - 查询参数: ('2025-05-20',)
2025-05-20 09:30:33,539 - INFO - MySQL查询成功，增量数据（日期: 2025-05-20），共获取 118 条记录
2025-05-20 09:30:33,539 - INFO - 获取到 4 个日期需要处理: ['2025-05-16', '2025-05-17', '2025-05-18', '2025-05-19']
2025-05-20 09:30:33,540 - INFO - 开始处理日期: 2025-05-16
2025-05-20 09:30:33,543 - INFO - Request Parameters - Page 1:
2025-05-20 09:30:33,543 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:30:33,543 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:30:41,670 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 22C6F253-A8A6-7EA6-AD56-3F7168951990 Response: {'code': 'ServiceUnavailable', 'requestid': '22C6F253-A8A6-7EA6-AD56-3F7168951990', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 22C6F253-A8A6-7EA6-AD56-3F7168951990)
2025-05-20 09:30:41,670 - INFO - 开始处理日期: 2025-05-17
2025-05-20 09:30:41,670 - INFO - Request Parameters - Page 1:
2025-05-20 09:30:41,670 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:30:41,670 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:30:49,790 - ERROR - 处理日期 2025-05-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 472D98C1-33C8-74B0-A415-23CF4737AF37 Response: {'code': 'ServiceUnavailable', 'requestid': '472D98C1-33C8-74B0-A415-23CF4737AF37', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 472D98C1-33C8-74B0-A415-23CF4737AF37)
2025-05-20 09:30:49,791 - INFO - 开始处理日期: 2025-05-18
2025-05-20 09:30:49,791 - INFO - Request Parameters - Page 1:
2025-05-20 09:30:49,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:30:49,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:30:57,902 - ERROR - 处理日期 2025-05-18 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 99AA070D-**************-006D79305262 Response: {'code': 'ServiceUnavailable', 'requestid': '99AA070D-**************-006D79305262', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 99AA070D-**************-006D79305262)
2025-05-20 09:30:57,903 - INFO - 开始处理日期: 2025-05-19
2025-05-20 09:30:57,903 - INFO - Request Parameters - Page 1:
2025-05-20 09:30:57,903 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:30:57,903 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:30:58,722 - INFO - Response - Page 1:
2025-05-20 09:30:58,722 - INFO - 第 1 页获取到 68 条记录
2025-05-20 09:30:58,922 - INFO - 查询完成，共获取到 68 条记录
2025-05-20 09:30:58,922 - INFO - 获取到 68 条表单数据
2025-05-20 09:30:58,923 - INFO - 当前日期 2025-05-19 有 115 条MySQL数据需要处理
2025-05-20 09:30:58,925 - INFO - 开始批量插入 102 条新记录
2025-05-20 09:30:59,240 - INFO - 批量插入响应状态码: 200
2025-05-20 09:30:59,240 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 20 May 2025 01:30:59 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '05101C28-53F9-7D05-A12F-E5A4E7F4D1DF', 'x-acs-trace-id': 'c8a7a057b8e0d0b8b9c8bc9c032cf560', 'etag': '4y2OzcuUhG1SXTZBRfyGNbQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-20 09:30:59,240 - INFO - 批量插入响应体: {'result': ['FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM9B', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMAB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMBB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMCB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMDB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMEB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMFB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMGB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMHB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMIB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMJB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMKB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMLB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMMB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMNB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMOB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMPB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMQB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMRB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMSB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMTB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMUB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMVB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMWB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMXB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMYB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMZB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM0C', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM1C', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM2C', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM3C', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM4C', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM5C', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM6C', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM7C', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM8C', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM9C', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMAC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMBC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMCC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMDC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMEC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMFC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMGC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMHC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMIC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMJC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMKC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMLC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMMC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMNC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMOC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMPC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMQC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMRC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMSC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMTC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMUC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMVC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMWC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMXC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMYC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMZC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM0D', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM1D', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM2D', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM3D', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM4D', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM5D', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM6D', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM7D', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM8D', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM9D', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMAD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMBD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMCD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMDD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMED', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMFD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMGD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMHD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMID', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMJD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMKD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMLD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMMD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMND', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMOD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMPD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMQD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMRD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMSD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMTD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMUD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMVD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O29V98UVAMWD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O29V98UVAMXD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O29V98UVAMYD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O29V98UVAMZD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O29V98UVAM0E']}
2025-05-20 09:30:59,240 - INFO - 批量插入表单数据成功，批次 1，共 100 条记录
2025-05-20 09:30:59,241 - INFO - 成功插入的数据ID: ['FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM9B', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMAB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMBB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMCB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMDB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMEB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMFB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMGB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMHB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMIB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMJB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMKB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMLB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMMB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMNB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMOB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMPB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMQB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMRB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMSB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMTB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMUB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMVB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMWB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMXB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMYB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMZB', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM0C', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM1C', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM2C', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM3C', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM4C', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM5C', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM6C', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM7C', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM8C', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM9C', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMAC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMBC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMCC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMDC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMEC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMFC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMGC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMHC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMIC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMJC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMKC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMLC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMMC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMNC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMOC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMPC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMQC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMRC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMSC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMTC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMUC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMVC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMWC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMXC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMYC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMZC', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM0D', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM1D', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM2D', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM3D', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM4D', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM5D', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM6D', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM7D', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM8D', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAM9D', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMAD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMBD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMCD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMDD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMED', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMFD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMGD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMHD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMID', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMJD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMKD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMLD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMMD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMND', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMOD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMPD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMQD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMRD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMSD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMTD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMUD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMVD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O29V98UVAMWD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O29V98UVAMXD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O29V98UVAMYD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O29V98UVAMZD', 'FINST-07E66I912IJV5PVT9I0IZ7FR3W9O29V98UVAM0E']
2025-05-20 09:31:04,395 - INFO - 批量插入响应状态码: 200
2025-05-20 09:31:04,395 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 20 May 2025 01:31:04 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '8818567B-E138-7C72-93B1-73BFD25A7B85', 'x-acs-trace-id': 'ee9c714acefea47d2fc2c0d7407afc5c', 'etag': '1Pj8VcQY39bcFqH6gNGuHAg8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-20 09:31:04,395 - INFO - 批量插入响应体: {'result': ['FINST-X0G66U81QCKVHSTM7E61360FEOVP3RUD8UVAM1F', 'FINST-X0G66U81QCKVHSTM7E61360FEOVP3RUD8UVAM2F']}
2025-05-20 09:31:04,395 - INFO - 批量插入表单数据成功，批次 2，共 2 条记录
2025-05-20 09:31:04,395 - INFO - 成功插入的数据ID: ['FINST-X0G66U81QCKVHSTM7E61360FEOVP3RUD8UVAM1F', 'FINST-X0G66U81QCKVHSTM7E61360FEOVP3RUD8UVAM2F']
2025-05-20 09:31:09,396 - INFO - 批量插入完成，共 102 条记录
2025-05-20 09:31:09,396 - INFO - 日期 2025-05-19 处理完成 - 更新: 0 条，插入: 102 条，错误: 0 条
2025-05-20 09:31:09,396 - INFO - 数据同步完成！更新: 0 条，插入: 102 条，错误: 3 条
2025-05-20 09:32:09,396 - INFO - 开始同步昨天与今天的销售数据: 2025-05-19 至 2025-05-20
2025-05-20 09:32:09,396 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 09:32:09,396 - INFO - 查询参数: ('2025-05-19', '2025-05-20')
2025-05-20 09:32:09,473 - INFO - MySQL查询成功，时间段: 2025-05-19 至 2025-05-20，共获取 454 条记录
2025-05-20 09:32:09,473 - INFO - 获取到 1 个日期需要处理: ['2025-05-19']
2025-05-20 09:32:09,477 - INFO - 开始处理日期: 2025-05-19
2025-05-20 09:32:09,478 - INFO - Request Parameters - Page 1:
2025-05-20 09:32:09,478 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:32:09,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:32:10,319 - INFO - Response - Page 1:
2025-05-20 09:32:10,319 - INFO - 第 1 页获取到 100 条记录
2025-05-20 09:32:10,520 - INFO - Request Parameters - Page 2:
2025-05-20 09:32:10,520 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 09:32:10,520 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 09:32:11,385 - INFO - Response - Page 2:
2025-05-20 09:32:11,385 - INFO - 第 2 页获取到 70 条记录
2025-05-20 09:32:11,585 - INFO - 查询完成，共获取到 170 条记录
2025-05-20 09:32:11,585 - INFO - 获取到 170 条表单数据
2025-05-20 09:32:11,588 - INFO - 当前日期 2025-05-19 有 454 条MySQL数据需要处理
2025-05-20 09:32:11,594 - INFO - 开始批量插入 284 条新记录
2025-05-20 09:32:11,873 - INFO - 批量插入响应状态码: 200
2025-05-20 09:32:11,873 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 20 May 2025 01:32:12 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4790', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '72997B3B-0D5C-700E-A905-910FA7A3F881', 'x-acs-trace-id': '8595526ba0ac9b113d70f54e5e41112b', 'etag': '4R5DkViMAtO0WC0XF/W3Dmg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-20 09:32:11,873 - INFO - 批量插入响应体: {'result': ['FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAME', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMF', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMG', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMH', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMI', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMJ', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMK', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAML', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMM', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMN', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMO', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMP', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMQ', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMR', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMS', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMT', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMU', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMV', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMW', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMX', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMY', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMZ', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAM01', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAM11', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAM21', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAM31', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAM41', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAM51', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAM61', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAM71', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAM81', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAM91', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMA1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMB1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMC1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMD1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAME1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMF1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMG1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMH1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMI1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMJ1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMK1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAML1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMM1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMN1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMO1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMP1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMQ1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMR1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMS1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMT1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMU1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMV1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMW1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMX1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMY1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMZ1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAM02', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAM12', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAM22', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAM32', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAM42', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAM52', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAM62', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAM72', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAM82', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAM92', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMA2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMB2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMC2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMD2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAME2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMF2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMG2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMH2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMI2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMJ2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMK2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAML2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMM2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMN2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMO2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMP2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMQ2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMR2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMS2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMT2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMU2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMV2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMW2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMX2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMY2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMZ2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAM03', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAM13', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAM23', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAM33', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAM43', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAM53']}
2025-05-20 09:32:11,873 - INFO - 批量插入表单数据成功，批次 1，共 100 条记录
2025-05-20 09:32:11,874 - INFO - 成功插入的数据ID: ['FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAME', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMF', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMG', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMH', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMI', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMJ', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMK', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAML', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMM', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMN', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMO', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMP', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMQ', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMR', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMS', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMT', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMU', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMV', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMW', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMX', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMY', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMZ', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAM01', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAM11', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAM21', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAM31', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAM41', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAM51', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAM61', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAM71', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAM81', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAM91', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMA1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMB1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMC1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMD1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAME1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMF1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMG1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMH1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMI1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMJ1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMK1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAML1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMM1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMN1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMO1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMP1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMQ1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMR1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMS1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMT1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMU1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMV1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMW1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMX1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMY1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMZ1', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAM02', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAM12', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAM22', 'FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAM32', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAM42', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAM52', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAM62', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAM72', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAM82', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAM92', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMA2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMB2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMC2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMD2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAME2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMF2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMG2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMH2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMI2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMJ2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMK2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAML2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMM2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMN2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMO2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMP2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMQ2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMR2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMS2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMT2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMU2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMV2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMW2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMX2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMY2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAMZ2', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAM03', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAM13', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAM23', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAM33', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAM43', 'FINST-2PF662C1DBKVAY62F93K79SFGVA322XT9UVAM53']
2025-05-20 09:32:17,160 - INFO - 批量插入响应状态码: 200
2025-05-20 09:32:17,161 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 20 May 2025 01:32:17 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D1A0257F-87AF-7521-826D-7BF1F877D1E1', 'x-acs-trace-id': '30b263505583afb18af2852c011e008a', 'etag': '4i16AnuFLaj2fPbU0EunITQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-20 09:32:17,161 - INFO - 批量插入响应体: {'result': ['FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMVA', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMWA', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMXA', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMYA', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMZA', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAM0B', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAM1B', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAM2B', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAM3B', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAM4B', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAM5B', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAM6B', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAM7B', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAM8B', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAM9B', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMAB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMBB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMCB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMDB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMEB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMFB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMGB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMHB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMIB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMJB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMKB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMLB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMMB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMNB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMOB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMPB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMQB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMRB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMSB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMTB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMUB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMVB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMWB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMXB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMYB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMZB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAM0C', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAM1C', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAM2C', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAM3C', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAM4C', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAM5C', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAM6C', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAM7C', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAM8C', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAM9C', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMAC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMBC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMCC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMDC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMEC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMFC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMGC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMHC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMIC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMJC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMKC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMLC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMMC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMNC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMOC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMPC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMQC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMRC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMSC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMTC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMUC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMVC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMWC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMXC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMYC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMZC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAM0D', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAM1D', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAM2D', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAM3D', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAM4D', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAM5D', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAM6D', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAM7D', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAM8D', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAM9D', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMAD', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMBD', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMCD', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMDD', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMED', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMFD', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMGD', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMHD', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMID', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMJD', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMKD', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMLD', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMMD']}
2025-05-20 09:32:17,161 - INFO - 批量插入表单数据成功，批次 2，共 100 条记录
2025-05-20 09:32:17,161 - INFO - 成功插入的数据ID: ['FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMVA', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMWA', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMXA', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMYA', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMZA', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAM0B', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAM1B', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAM2B', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAM3B', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAM4B', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAM5B', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAM6B', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAM7B', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAM8B', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAM9B', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMAB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMBB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMCB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMDB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMEB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMFB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMGB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMHB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMIB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMJB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMKB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMLB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMMB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMNB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMOB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMPB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMQB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMRB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMSB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMTB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMUB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMVB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMWB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMXB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMYB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAMZB', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAM0C', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAM1C', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAM2C', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAM3C', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAM4C', 'FINST-RI766091IEKVUPRE9P29V4Q671C92XZX9UVAM5C', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAM6C', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAM7C', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAM8C', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAM9C', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMAC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMBC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMCC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMDC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMEC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMFC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMGC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMHC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMIC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMJC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMKC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMLC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMMC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMNC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMOC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMPC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMQC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMRC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMSC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMTC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMUC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMVC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMWC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMXC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMYC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMZC', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAM0D', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAM1D', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAM2D', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAM3D', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAM4D', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAM5D', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAM6D', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAM7D', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAM8D', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAM9D', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMAD', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMBD', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMCD', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMDD', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMED', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMFD', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMGD', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMHD', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMID', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMJD', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMKD', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMLD', 'FINST-RI766091IEKVUPRE9P29V4Q671C92YZX9UVAMMD']
2025-05-20 09:32:22,391 - INFO - 批量插入响应状态码: 200
2025-05-20 09:32:22,391 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 20 May 2025 01:32:22 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4044', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'AC69A3F5-AC08-7743-A569-5366833AC294', 'x-acs-trace-id': 'aef53f7aae01b0dc5602c1cb4d65e532', 'etag': '42fL4frIyb+KSfvn25SwePA4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-20 09:32:22,391 - INFO - 批量插入响应体: {'result': ['FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMF8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMG8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMH8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMI8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMJ8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMK8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAML8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMM8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMN8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMO8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMP8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMQ8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMR8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMS8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMT8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMU8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMV8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMW8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMX8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMY8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMZ8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM09', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM19', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM29', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM39', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM49', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM59', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM69', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM79', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM89', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM99', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMA9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMB9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMC9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMD9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAME9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMF9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMG9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMH9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMI9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMJ9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMK9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAML9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMM9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMN9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMO9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMP9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMQ9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMR9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMS9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMT9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMU9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMV9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMW9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMX9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMY9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMZ9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM0A', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM1A', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM2A', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM3A', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM4A', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM5A', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM6A', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM7A', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM8A', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM9A', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMAA', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMBA', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMCA', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMDA', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMEA', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMFA', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMGA', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMHA', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMIA', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMJA', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMKA', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMLA', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMMA', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMNA', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMOA', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMPA', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMQA']}
2025-05-20 09:32:22,391 - INFO - 批量插入表单数据成功，批次 3，共 84 条记录
2025-05-20 09:32:22,392 - INFO - 成功插入的数据ID: ['FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMF8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMG8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMH8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMI8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMJ8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMK8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAML8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMM8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMN8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMO8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMP8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMQ8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMR8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMS8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMT8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMU8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMV8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMW8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMX8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMY8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMZ8', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM09', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM19', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM29', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM39', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM49', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM59', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM69', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM79', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM89', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM99', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMA9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMB9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMC9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMD9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAME9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMF9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMG9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMH9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMI9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMJ9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMK9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAML9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMM9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMN9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMO9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMP9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMQ9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMR9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMS9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMT9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMU9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMV9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMW9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMX9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMY9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMZ9', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM0A', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM1A', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM2A', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM3A', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM4A', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM5A', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM6A', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM7A', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM8A', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAM9A', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMAA', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMBA', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMCA', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMDA', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMEA', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMFA', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMGA', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMHA', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMIA', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMJA', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMKA', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMLA', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMMA', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMNA', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMOA', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMPA', 'FINST-WBF66B81PDKVVLEEFMG5MDEVKDDD2112AUVAMQA']
2025-05-20 09:32:27,393 - INFO - 批量插入完成，共 284 条记录
2025-05-20 09:32:27,393 - INFO - 日期 2025-05-19 处理完成 - 更新: 0 条，插入: 284 条，错误: 0 条
2025-05-20 09:32:27,393 - INFO - 数据同步完成！更新: 0 条，插入: 284 条，错误: 0 条
2025-05-20 09:32:27,394 - INFO - 同步完成
2025-05-20 10:30:33,477 - INFO - 使用默认增量同步（当天更新数据）
2025-05-20 10:30:33,477 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 10:30:33,477 - INFO - 查询参数: ('2025-05-20',)
2025-05-20 10:30:33,548 - INFO - MySQL查询成功，增量数据（日期: 2025-05-20），共获取 197 条记录
2025-05-20 10:30:33,549 - INFO - 获取到 5 个日期需要处理: ['2025-05-16', '2025-05-17', '2025-05-18', '2025-05-19', '2025-05-20']
2025-05-20 10:30:33,550 - INFO - 开始处理日期: 2025-05-16
2025-05-20 10:30:33,553 - INFO - Request Parameters - Page 1:
2025-05-20 10:30:33,553 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 10:30:33,553 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 10:30:41,683 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 187BD218-3512-7F5D-906D-5072708EBC1E Response: {'code': 'ServiceUnavailable', 'requestid': '187BD218-3512-7F5D-906D-5072708EBC1E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 187BD218-3512-7F5D-906D-5072708EBC1E)
2025-05-20 10:30:41,683 - INFO - 开始处理日期: 2025-05-17
2025-05-20 10:30:41,683 - INFO - Request Parameters - Page 1:
2025-05-20 10:30:41,683 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 10:30:41,683 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 10:30:49,799 - ERROR - 处理日期 2025-05-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: DD36382E-E2D6-7655-9155-1F3215B61012 Response: {'code': 'ServiceUnavailable', 'requestid': 'DD36382E-E2D6-7655-9155-1F3215B61012', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: DD36382E-E2D6-7655-9155-1F3215B61012)
2025-05-20 10:30:49,799 - INFO - 开始处理日期: 2025-05-18
2025-05-20 10:30:49,800 - INFO - Request Parameters - Page 1:
2025-05-20 10:30:49,800 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 10:30:49,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 10:30:57,907 - ERROR - 处理日期 2025-05-18 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 7BD87459-329E-752D-8BC7-49ECBA12EAE6 Response: {'code': 'ServiceUnavailable', 'requestid': '7BD87459-329E-752D-8BC7-49ECBA12EAE6', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 7BD87459-329E-752D-8BC7-49ECBA12EAE6)
2025-05-20 10:30:57,907 - INFO - 开始处理日期: 2025-05-19
2025-05-20 10:30:57,908 - INFO - Request Parameters - Page 1:
2025-05-20 10:30:57,908 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 10:30:57,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 10:31:04,012 - INFO - Response - Page 1:
2025-05-20 10:31:04,012 - INFO - 第 1 页获取到 100 条记录
2025-05-20 10:31:04,212 - INFO - Request Parameters - Page 2:
2025-05-20 10:31:04,212 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 10:31:04,212 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 10:31:05,217 - INFO - Response - Page 2:
2025-05-20 10:31:05,217 - INFO - 第 2 页获取到 100 条记录
2025-05-20 10:31:05,417 - INFO - Request Parameters - Page 3:
2025-05-20 10:31:05,417 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 10:31:05,417 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 10:31:06,309 - INFO - Response - Page 3:
2025-05-20 10:31:06,309 - INFO - 第 3 页获取到 100 条记录
2025-05-20 10:31:06,510 - INFO - Request Parameters - Page 4:
2025-05-20 10:31:06,510 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 10:31:06,510 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 10:31:07,320 - INFO - Response - Page 4:
2025-05-20 10:31:07,321 - INFO - 第 4 页获取到 100 条记录
2025-05-20 10:31:07,521 - INFO - Request Parameters - Page 5:
2025-05-20 10:31:07,521 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 10:31:07,521 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 10:31:08,304 - INFO - Response - Page 5:
2025-05-20 10:31:08,304 - INFO - 第 5 页获取到 54 条记录
2025-05-20 10:31:08,505 - INFO - 查询完成，共获取到 454 条记录
2025-05-20 10:31:08,505 - INFO - 获取到 454 条表单数据
2025-05-20 10:31:08,511 - INFO - 当前日期 2025-05-19 有 192 条MySQL数据需要处理
2025-05-20 10:31:08,514 - INFO - 开始批量插入 77 条新记录
2025-05-20 10:31:08,767 - INFO - 批量插入响应状态码: 200
2025-05-20 10:31:08,767 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 20 May 2025 02:31:09 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '3708', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '83A329E9-0FE0-70E1-9041-36DD639CF97B', 'x-acs-trace-id': '9d60da4b9f7d37218b423e7467d0c28a', 'etag': '38vhHyUcfRK8A4DcBv8Ap5g8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-20 10:31:08,767 - INFO - 批量插入响应体: {'result': ['FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMNJ', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMOJ', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMPJ', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMQJ', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMRJ', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMSJ', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMTJ', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMUJ', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMVJ', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMWJ', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMXJ', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMYJ', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMZJ', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM0K', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM1K', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM2K', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM3K', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM4K', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM5K', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM6K', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM7K', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM8K', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM9K', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMAK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMBK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMCK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMDK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMEK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMFK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMGK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMHK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMIK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMJK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMKK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMLK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMMK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMNK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMOK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMPK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMQK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMRK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMSK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMTK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMUK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMVK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMWK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMXK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMYK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMZK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM0L', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM1L', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM2L', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM3L', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM4L', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM5L', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM6L', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM7L', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM8L', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM9L', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMAL', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMBL', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMCL', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMDL', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMEL', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMFL', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMGL', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMHL', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMIL', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMJL', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMKL', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMLL', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMML', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMNL', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMOL', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMPL', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMQL', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMRL']}
2025-05-20 10:31:08,767 - INFO - 批量插入表单数据成功，批次 1，共 77 条记录
2025-05-20 10:31:08,767 - INFO - 成功插入的数据ID: ['FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMNJ', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMOJ', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMPJ', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMQJ', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMRJ', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMSJ', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMTJ', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMUJ', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMVJ', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMWJ', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMXJ', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMYJ', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMZJ', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM0K', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM1K', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM2K', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM3K', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM4K', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM5K', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM6K', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM7K', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM8K', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM9K', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMAK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMBK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMCK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMDK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMEK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMFK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMGK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMHK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMIK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMJK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMKK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMLK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMMK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMNK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMOK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMPK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMQK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMRK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMSK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMTK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMUK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMVK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMWK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMXK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMYK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMZK', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM0L', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM1L', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM2L', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM3L', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM4L', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM5L', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM6L', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM7L', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM8L', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM9L', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMAL', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMBL', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMCL', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMDL', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMEL', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMFL', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMGL', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMHL', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMIL', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMJL', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMKL', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMLL', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMML', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMNL', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMOL', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMPL', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMQL', 'FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMRL']
2025-05-20 10:31:13,769 - INFO - 批量插入完成，共 77 条记录
2025-05-20 10:31:13,769 - INFO - 日期 2025-05-19 处理完成 - 更新: 0 条，插入: 77 条，错误: 0 条
2025-05-20 10:31:13,769 - INFO - 开始处理日期: 2025-05-20
2025-05-20 10:31:13,769 - INFO - Request Parameters - Page 1:
2025-05-20 10:31:13,769 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 10:31:13,769 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 10:31:14,258 - INFO - Response - Page 1:
2025-05-20 10:31:14,258 - INFO - 查询完成，共获取到 0 条记录
2025-05-20 10:31:14,258 - INFO - 获取到 0 条表单数据
2025-05-20 10:31:14,258 - INFO - 当前日期 2025-05-20 有 2 条MySQL数据需要处理
2025-05-20 10:31:14,258 - INFO - 开始批量插入 2 条新记录
2025-05-20 10:31:14,409 - INFO - 批量插入响应状态码: 200
2025-05-20 10:31:14,409 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 20 May 2025 02:31:14 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5423A978-A40F-7935-981A-73DAABCA3841', 'x-acs-trace-id': '0790a821d9b0d5c69b4ca17a239a3b9a', 'etag': '1fi8alXqI35YMYQZ0Jvgs6A8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-20 10:31:14,409 - INFO - 批量插入响应体: {'result': ['FINST-3PF66E9106JVQK84AQ9X987X8JII2JDRDWVAMFV', 'FINST-3PF66E9106JVQK84AQ9X987X8JII2JDRDWVAMGV']}
2025-05-20 10:31:14,409 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-05-20 10:31:14,409 - INFO - 成功插入的数据ID: ['FINST-3PF66E9106JVQK84AQ9X987X8JII2JDRDWVAMFV', 'FINST-3PF66E9106JVQK84AQ9X987X8JII2JDRDWVAMGV']
2025-05-20 10:31:19,410 - INFO - 批量插入完成，共 2 条记录
2025-05-20 10:31:19,410 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-05-20 10:31:19,410 - INFO - 数据同步完成！更新: 0 条，插入: 79 条，错误: 3 条
2025-05-20 10:32:19,411 - INFO - 开始同步昨天与今天的销售数据: 2025-05-19 至 2025-05-20
2025-05-20 10:32:19,411 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 10:32:19,411 - INFO - 查询参数: ('2025-05-19', '2025-05-20')
2025-05-20 10:32:19,490 - INFO - MySQL查询成功，时间段: 2025-05-19 至 2025-05-20，共获取 552 条记录
2025-05-20 10:32:19,491 - INFO - 获取到 2 个日期需要处理: ['2025-05-19', '2025-05-20']
2025-05-20 10:32:19,495 - INFO - 开始处理日期: 2025-05-19
2025-05-20 10:32:19,496 - INFO - Request Parameters - Page 1:
2025-05-20 10:32:19,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 10:32:19,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 10:32:20,389 - INFO - Response - Page 1:
2025-05-20 10:32:20,389 - INFO - 第 1 页获取到 100 条记录
2025-05-20 10:32:20,589 - INFO - Request Parameters - Page 2:
2025-05-20 10:32:20,589 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 10:32:20,589 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 10:32:21,452 - INFO - Response - Page 2:
2025-05-20 10:32:21,452 - INFO - 第 2 页获取到 100 条记录
2025-05-20 10:32:21,653 - INFO - Request Parameters - Page 3:
2025-05-20 10:32:21,653 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 10:32:21,653 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 10:32:22,429 - INFO - Response - Page 3:
2025-05-20 10:32:22,430 - INFO - 第 3 页获取到 100 条记录
2025-05-20 10:32:22,630 - INFO - Request Parameters - Page 4:
2025-05-20 10:32:22,630 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 10:32:22,630 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 10:32:23,410 - INFO - Response - Page 4:
2025-05-20 10:32:23,410 - INFO - 第 4 页获取到 100 条记录
2025-05-20 10:32:23,611 - INFO - Request Parameters - Page 5:
2025-05-20 10:32:23,611 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 10:32:23,611 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 10:32:24,409 - INFO - Response - Page 5:
2025-05-20 10:32:24,409 - INFO - 第 5 页获取到 100 条记录
2025-05-20 10:32:24,610 - INFO - Request Parameters - Page 6:
2025-05-20 10:32:24,610 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 10:32:24,610 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 10:32:25,315 - INFO - Response - Page 6:
2025-05-20 10:32:25,316 - INFO - 第 6 页获取到 31 条记录
2025-05-20 10:32:25,516 - INFO - 查询完成，共获取到 531 条记录
2025-05-20 10:32:25,516 - INFO - 获取到 531 条表单数据
2025-05-20 10:32:25,525 - INFO - 当前日期 2025-05-19 有 550 条MySQL数据需要处理
2025-05-20 10:32:25,525 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMI
2025-05-20 10:32:26,037 - INFO - 更新表单数据成功: FINST-2PF662C1DBKVAY62F93K79SFGVA321XT9UVAMI
2025-05-20 10:32:26,037 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3.0, 'new_value': 9864.0}, {'field': 'total_amount', 'old_value': 3.0, 'new_value': 9864.0}]
2025-05-20 10:32:26,047 - INFO - 开始批量插入 19 条新记录
2025-05-20 10:32:26,219 - INFO - 批量插入响应状态码: 200
2025-05-20 10:32:26,219 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 20 May 2025 02:32:26 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '924', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F073ADEC-77D0-797B-839C-A9F5D702EDDA', 'x-acs-trace-id': '234b0651c9f6a53db0255a4b4ee5c9b7', 'etag': '9n1PQVG6VujNgbBkOke0i/g4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-20 10:32:26,219 - INFO - 批量插入响应体: {'result': ['FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAMO6', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAMP6', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAMQ6', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAMR6', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAMS6', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAMT6', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAMU6', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAMV6', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAMW6', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAMX6', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAMY6', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAMZ6', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAM07', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAM17', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAM27', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAM37', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAM47', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAM57', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAM67']}
2025-05-20 10:32:26,219 - INFO - 批量插入表单数据成功，批次 1，共 19 条记录
2025-05-20 10:32:26,219 - INFO - 成功插入的数据ID: ['FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAMO6', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAMP6', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAMQ6', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAMR6', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAMS6', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAMT6', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAMU6', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAMV6', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAMW6', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAMX6', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAMY6', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAMZ6', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAM07', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAM17', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAM27', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAM37', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAM47', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAM57', 'FINST-2PF66KD1FDKVKDTDB25EC584GHBB37SAFWVAM67']
2025-05-20 10:32:31,220 - INFO - 批量插入完成，共 19 条记录
2025-05-20 10:32:31,220 - INFO - 日期 2025-05-19 处理完成 - 更新: 1 条，插入: 19 条，错误: 0 条
2025-05-20 10:32:31,220 - INFO - 开始处理日期: 2025-05-20
2025-05-20 10:32:31,220 - INFO - Request Parameters - Page 1:
2025-05-20 10:32:31,220 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 10:32:31,220 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 10:32:31,745 - INFO - Response - Page 1:
2025-05-20 10:32:31,745 - INFO - 第 1 页获取到 2 条记录
2025-05-20 10:32:31,945 - INFO - 查询完成，共获取到 2 条记录
2025-05-20 10:32:31,945 - INFO - 获取到 2 条表单数据
2025-05-20 10:32:31,946 - INFO - 当前日期 2025-05-20 有 2 条MySQL数据需要处理
2025-05-20 10:32:31,946 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 10:32:31,946 - INFO - 数据同步完成！更新: 1 条，插入: 19 条，错误: 0 条
2025-05-20 10:32:31,946 - INFO - 同步完成
2025-05-20 11:30:33,468 - INFO - 使用默认增量同步（当天更新数据）
2025-05-20 11:30:33,468 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 11:30:33,469 - INFO - 查询参数: ('2025-05-20',)
2025-05-20 11:30:33,540 - INFO - MySQL查询成功，增量数据（日期: 2025-05-20），共获取 207 条记录
2025-05-20 11:30:33,540 - INFO - 获取到 5 个日期需要处理: ['2025-05-16', '2025-05-17', '2025-05-18', '2025-05-19', '2025-05-20']
2025-05-20 11:30:33,542 - INFO - 开始处理日期: 2025-05-16
2025-05-20 11:30:33,544 - INFO - Request Parameters - Page 1:
2025-05-20 11:30:33,544 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 11:30:33,545 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 11:30:41,654 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 1127CCCB-A324-75EA-BE9E-386D683D3F9B Response: {'code': 'ServiceUnavailable', 'requestid': '1127CCCB-A324-75EA-BE9E-386D683D3F9B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 1127CCCB-A324-75EA-BE9E-386D683D3F9B)
2025-05-20 11:30:41,654 - INFO - 开始处理日期: 2025-05-17
2025-05-20 11:30:41,655 - INFO - Request Parameters - Page 1:
2025-05-20 11:30:41,655 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 11:30:41,655 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 11:30:49,761 - ERROR - 处理日期 2025-05-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 4A852488-CF04-7898-A684-968A740EC3CE Response: {'code': 'ServiceUnavailable', 'requestid': '4A852488-CF04-7898-A684-968A740EC3CE', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 4A852488-CF04-7898-A684-968A740EC3CE)
2025-05-20 11:30:49,761 - INFO - 开始处理日期: 2025-05-18
2025-05-20 11:30:49,761 - INFO - Request Parameters - Page 1:
2025-05-20 11:30:49,761 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 11:30:49,761 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 11:30:57,876 - ERROR - 处理日期 2025-05-18 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 547F044A-CEF4-7AD5-AB7A-7ECF2639E908 Response: {'code': 'ServiceUnavailable', 'requestid': '547F044A-CEF4-7AD5-AB7A-7ECF2639E908', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 547F044A-CEF4-7AD5-AB7A-7ECF2639E908)
2025-05-20 11:30:57,876 - INFO - 开始处理日期: 2025-05-19
2025-05-20 11:30:57,877 - INFO - Request Parameters - Page 1:
2025-05-20 11:30:57,877 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 11:30:57,877 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 11:30:59,555 - INFO - Response - Page 1:
2025-05-20 11:30:59,555 - INFO - 第 1 页获取到 100 条记录
2025-05-20 11:30:59,755 - INFO - Request Parameters - Page 2:
2025-05-20 11:30:59,755 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 11:30:59,755 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 11:31:00,559 - INFO - Response - Page 2:
2025-05-20 11:31:00,559 - INFO - 第 2 页获取到 100 条记录
2025-05-20 11:31:00,759 - INFO - Request Parameters - Page 3:
2025-05-20 11:31:00,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 11:31:00,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 11:31:01,882 - INFO - Response - Page 3:
2025-05-20 11:31:01,882 - INFO - 第 3 页获取到 100 条记录
2025-05-20 11:31:02,082 - INFO - Request Parameters - Page 4:
2025-05-20 11:31:02,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 11:31:02,082 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 11:31:03,016 - INFO - Response - Page 4:
2025-05-20 11:31:03,016 - INFO - 第 4 页获取到 100 条记录
2025-05-20 11:31:03,216 - INFO - Request Parameters - Page 5:
2025-05-20 11:31:03,216 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 11:31:03,216 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 11:31:04,062 - INFO - Response - Page 5:
2025-05-20 11:31:04,062 - INFO - 第 5 页获取到 100 条记录
2025-05-20 11:31:04,262 - INFO - Request Parameters - Page 6:
2025-05-20 11:31:04,262 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 11:31:04,262 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 11:31:05,020 - INFO - Response - Page 6:
2025-05-20 11:31:05,021 - INFO - 第 6 页获取到 50 条记录
2025-05-20 11:31:05,221 - INFO - 查询完成，共获取到 550 条记录
2025-05-20 11:31:05,221 - INFO - 获取到 550 条表单数据
2025-05-20 11:31:05,231 - INFO - 当前日期 2025-05-19 有 202 条MySQL数据需要处理
2025-05-20 11:31:05,231 - INFO - 开始更新记录 - 表单实例ID: FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMS5
2025-05-20 11:31:05,786 - INFO - 更新表单数据成功: FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMS5
2025-05-20 11:31:05,786 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 30000.0}, {'field': 'total_amount', 'old_value': 7800.0, 'new_value': 37800.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 7}]
2025-05-20 11:31:05,787 - INFO - 开始更新记录 - 表单实例ID: FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMV5
2025-05-20 11:31:06,253 - INFO - 更新表单数据成功: FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMV5
2025-05-20 11:31:06,254 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 21800.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 21800.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 3}]
2025-05-20 11:31:06,254 - INFO - 开始更新记录 - 表单实例ID: FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMW5
2025-05-20 11:31:06,700 - INFO - 更新表单数据成功: FINST-Z7B66WA1SBKVUCSY73Z0X5HETK3V2OE8EHVAMW5
2025-05-20 11:31:06,700 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37458.0, 'new_value': 57458.0}, {'field': 'total_amount', 'old_value': 47681.0, 'new_value': 67681.0}, {'field': 'order_count', 'old_value': 54, 'new_value': 58}]
2025-05-20 11:31:06,704 - INFO - 开始批量插入 5 条新记录
2025-05-20 11:31:06,851 - INFO - 批量插入响应状态码: 200
2025-05-20 11:31:06,852 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 20 May 2025 03:31:07 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '252', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E3E25090-E9B8-76ED-8F92-5C62F4B6A36A', 'x-acs-trace-id': '92e05b6076e3203ffcad4c3e2bb51eb8', 'etag': '2P9LiEMVzhi25uSiJExgFtg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-20 11:31:06,852 - INFO - 批量插入响应体: {'result': ['FINST-3PF66271WDKVW4D399UFOBGGLJ6K38CRIYVAMW6', 'FINST-3PF66271WDKVW4D399UFOBGGLJ6K38CRIYVAMX6', 'FINST-3PF66271WDKVW4D399UFOBGGLJ6K38CRIYVAMY6', 'FINST-3PF66271WDKVW4D399UFOBGGLJ6K38CRIYVAMZ6', 'FINST-3PF66271WDKVW4D399UFOBGGLJ6K38CRIYVAM07']}
2025-05-20 11:31:06,852 - INFO - 批量插入表单数据成功，批次 1，共 5 条记录
2025-05-20 11:31:06,852 - INFO - 成功插入的数据ID: ['FINST-3PF66271WDKVW4D399UFOBGGLJ6K38CRIYVAMW6', 'FINST-3PF66271WDKVW4D399UFOBGGLJ6K38CRIYVAMX6', 'FINST-3PF66271WDKVW4D399UFOBGGLJ6K38CRIYVAMY6', 'FINST-3PF66271WDKVW4D399UFOBGGLJ6K38CRIYVAMZ6', 'FINST-3PF66271WDKVW4D399UFOBGGLJ6K38CRIYVAM07']
2025-05-20 11:31:11,853 - INFO - 批量插入完成，共 5 条记录
2025-05-20 11:31:11,853 - INFO - 日期 2025-05-19 处理完成 - 更新: 3 条，插入: 5 条，错误: 0 条
2025-05-20 11:31:11,853 - INFO - 开始处理日期: 2025-05-20
2025-05-20 11:31:11,853 - INFO - Request Parameters - Page 1:
2025-05-20 11:31:11,853 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 11:31:11,853 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 11:31:12,359 - INFO - Response - Page 1:
2025-05-20 11:31:12,359 - INFO - 第 1 页获取到 2 条记录
2025-05-20 11:31:12,559 - INFO - 查询完成，共获取到 2 条记录
2025-05-20 11:31:12,559 - INFO - 获取到 2 条表单数据
2025-05-20 11:31:12,560 - INFO - 当前日期 2025-05-20 有 2 条MySQL数据需要处理
2025-05-20 11:31:12,560 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 11:31:12,560 - INFO - 数据同步完成！更新: 3 条，插入: 5 条，错误: 3 条
2025-05-20 11:32:12,560 - INFO - 开始同步昨天与今天的销售数据: 2025-05-19 至 2025-05-20
2025-05-20 11:32:12,560 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 11:32:12,560 - INFO - 查询参数: ('2025-05-19', '2025-05-20')
2025-05-20 11:32:12,640 - INFO - MySQL查询成功，时间段: 2025-05-19 至 2025-05-20，共获取 557 条记录
2025-05-20 11:32:12,640 - INFO - 获取到 2 个日期需要处理: ['2025-05-19', '2025-05-20']
2025-05-20 11:32:12,644 - INFO - 开始处理日期: 2025-05-19
2025-05-20 11:32:12,645 - INFO - Request Parameters - Page 1:
2025-05-20 11:32:12,645 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 11:32:12,645 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 11:32:13,582 - INFO - Response - Page 1:
2025-05-20 11:32:13,582 - INFO - 第 1 页获取到 100 条记录
2025-05-20 11:32:13,782 - INFO - Request Parameters - Page 2:
2025-05-20 11:32:13,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 11:32:13,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 11:32:14,581 - INFO - Response - Page 2:
2025-05-20 11:32:14,581 - INFO - 第 2 页获取到 100 条记录
2025-05-20 11:32:14,781 - INFO - Request Parameters - Page 3:
2025-05-20 11:32:14,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 11:32:14,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 11:32:15,580 - INFO - Response - Page 3:
2025-05-20 11:32:15,581 - INFO - 第 3 页获取到 100 条记录
2025-05-20 11:32:15,781 - INFO - Request Parameters - Page 4:
2025-05-20 11:32:15,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 11:32:15,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 11:32:16,530 - INFO - Response - Page 4:
2025-05-20 11:32:16,530 - INFO - 第 4 页获取到 100 条记录
2025-05-20 11:32:16,730 - INFO - Request Parameters - Page 5:
2025-05-20 11:32:16,730 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 11:32:16,730 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 11:32:17,498 - INFO - Response - Page 5:
2025-05-20 11:32:17,499 - INFO - 第 5 页获取到 100 条记录
2025-05-20 11:32:17,699 - INFO - Request Parameters - Page 6:
2025-05-20 11:32:17,699 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 11:32:17,699 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 11:32:18,767 - INFO - Response - Page 6:
2025-05-20 11:32:18,767 - INFO - 第 6 页获取到 55 条记录
2025-05-20 11:32:18,967 - INFO - 查询完成，共获取到 555 条记录
2025-05-20 11:32:18,967 - INFO - 获取到 555 条表单数据
2025-05-20 11:32:18,976 - INFO - 当前日期 2025-05-19 有 555 条MySQL数据需要处理
2025-05-20 11:32:18,986 - INFO - 日期 2025-05-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 11:32:18,986 - INFO - 开始处理日期: 2025-05-20
2025-05-20 11:32:18,986 - INFO - Request Parameters - Page 1:
2025-05-20 11:32:18,986 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 11:32:18,986 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 11:32:19,535 - INFO - Response - Page 1:
2025-05-20 11:32:19,536 - INFO - 第 1 页获取到 2 条记录
2025-05-20 11:32:19,736 - INFO - 查询完成，共获取到 2 条记录
2025-05-20 11:32:19,736 - INFO - 获取到 2 条表单数据
2025-05-20 11:32:19,737 - INFO - 当前日期 2025-05-20 有 2 条MySQL数据需要处理
2025-05-20 11:32:19,737 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 11:32:19,737 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 11:32:19,737 - INFO - 同步完成
2025-05-20 12:30:34,137 - INFO - 使用默认增量同步（当天更新数据）
2025-05-20 12:30:34,137 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 12:30:34,137 - INFO - 查询参数: ('2025-05-20',)
2025-05-20 12:30:34,209 - INFO - MySQL查询成功，增量数据（日期: 2025-05-20），共获取 209 条记录
2025-05-20 12:30:34,209 - INFO - 获取到 5 个日期需要处理: ['2025-05-16', '2025-05-17', '2025-05-18', '2025-05-19', '2025-05-20']
2025-05-20 12:30:34,211 - INFO - 开始处理日期: 2025-05-16
2025-05-20 12:30:34,213 - INFO - Request Parameters - Page 1:
2025-05-20 12:30:34,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:30:34,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:30:42,320 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: DE4846FF-2E42-7897-BD53-1C12E5E14283 Response: {'code': 'ServiceUnavailable', 'requestid': 'DE4846FF-2E42-7897-BD53-1C12E5E14283', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: DE4846FF-2E42-7897-BD53-1C12E5E14283)
2025-05-20 12:30:42,321 - INFO - 开始处理日期: 2025-05-17
2025-05-20 12:30:42,321 - INFO - Request Parameters - Page 1:
2025-05-20 12:30:42,321 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:30:42,321 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:30:50,426 - ERROR - 处理日期 2025-05-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 996DEB3E-0DFB-712E-91BB-DD11045E579A Response: {'code': 'ServiceUnavailable', 'requestid': '996DEB3E-0DFB-712E-91BB-DD11045E579A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 996DEB3E-0DFB-712E-91BB-DD11045E579A)
2025-05-20 12:30:50,426 - INFO - 开始处理日期: 2025-05-18
2025-05-20 12:30:50,426 - INFO - Request Parameters - Page 1:
2025-05-20 12:30:50,426 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:30:50,426 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:30:51,303 - INFO - Response - Page 1:
2025-05-20 12:30:51,303 - INFO - 第 1 页获取到 100 条记录
2025-05-20 12:30:51,503 - INFO - Request Parameters - Page 2:
2025-05-20 12:30:51,503 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:30:51,503 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:30:52,361 - INFO - Response - Page 2:
2025-05-20 12:30:52,361 - INFO - 第 2 页获取到 100 条记录
2025-05-20 12:30:52,561 - INFO - Request Parameters - Page 3:
2025-05-20 12:30:52,561 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:30:52,561 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:30:53,373 - INFO - Response - Page 3:
2025-05-20 12:30:53,373 - INFO - 第 3 页获取到 100 条记录
2025-05-20 12:30:53,573 - INFO - Request Parameters - Page 4:
2025-05-20 12:30:53,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:30:53,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:30:59,368 - INFO - Response - Page 4:
2025-05-20 12:30:59,368 - INFO - 第 4 页获取到 100 条记录
2025-05-20 12:30:59,568 - INFO - Request Parameters - Page 5:
2025-05-20 12:30:59,568 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:30:59,568 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:31:00,364 - INFO - Response - Page 5:
2025-05-20 12:31:00,364 - INFO - 第 5 页获取到 100 条记录
2025-05-20 12:31:00,564 - INFO - Request Parameters - Page 6:
2025-05-20 12:31:00,564 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:31:00,564 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:31:01,834 - INFO - Response - Page 6:
2025-05-20 12:31:01,835 - INFO - 第 6 页获取到 100 条记录
2025-05-20 12:31:02,036 - INFO - Request Parameters - Page 7:
2025-05-20 12:31:02,036 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:31:02,036 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:31:02,626 - INFO - Response - Page 7:
2025-05-20 12:31:02,626 - INFO - 第 7 页获取到 13 条记录
2025-05-20 12:31:02,827 - INFO - 查询完成，共获取到 613 条记录
2025-05-20 12:31:02,827 - INFO - 获取到 613 条表单数据
2025-05-20 12:31:02,836 - INFO - 当前日期 2025-05-18 有 1 条MySQL数据需要处理
2025-05-20 12:31:02,836 - INFO - 开始更新记录 - 表单实例ID: FINST-HXD667B1QIJVRN4T8POJZ9TVTS7L21E0YGUAMGD
2025-05-20 12:31:03,331 - INFO - 更新表单数据成功: FINST-HXD667B1QIJVRN4T8POJZ9TVTS7L21E0YGUAMGD
2025-05-20 12:31:03,331 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 1447.0}, {'field': 'offline_amount', 'old_value': 1200.0, 'new_value': 480.0}, {'field': 'total_amount', 'old_value': 1200.0, 'new_value': 1927.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 10}]
2025-05-20 12:31:03,331 - INFO - 日期 2025-05-18 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-20 12:31:03,332 - INFO - 开始处理日期: 2025-05-19
2025-05-20 12:31:03,332 - INFO - Request Parameters - Page 1:
2025-05-20 12:31:03,332 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:31:03,332 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:31:04,126 - INFO - Response - Page 1:
2025-05-20 12:31:04,127 - INFO - 第 1 页获取到 100 条记录
2025-05-20 12:31:04,328 - INFO - Request Parameters - Page 2:
2025-05-20 12:31:04,328 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:31:04,328 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:31:05,189 - INFO - Response - Page 2:
2025-05-20 12:31:05,189 - INFO - 第 2 页获取到 100 条记录
2025-05-20 12:31:05,389 - INFO - Request Parameters - Page 3:
2025-05-20 12:31:05,389 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:31:05,389 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:31:06,286 - INFO - Response - Page 3:
2025-05-20 12:31:06,287 - INFO - 第 3 页获取到 100 条记录
2025-05-20 12:31:06,487 - INFO - Request Parameters - Page 4:
2025-05-20 12:31:06,487 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:31:06,487 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:31:07,418 - INFO - Response - Page 4:
2025-05-20 12:31:07,419 - INFO - 第 4 页获取到 100 条记录
2025-05-20 12:31:07,620 - INFO - Request Parameters - Page 5:
2025-05-20 12:31:07,620 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:31:07,620 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:31:08,420 - INFO - Response - Page 5:
2025-05-20 12:31:08,420 - INFO - 第 5 页获取到 100 条记录
2025-05-20 12:31:08,620 - INFO - Request Parameters - Page 6:
2025-05-20 12:31:08,620 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:31:08,620 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:31:09,368 - INFO - Response - Page 6:
2025-05-20 12:31:09,368 - INFO - 第 6 页获取到 55 条记录
2025-05-20 12:31:09,568 - INFO - 查询完成，共获取到 555 条记录
2025-05-20 12:31:09,568 - INFO - 获取到 555 条表单数据
2025-05-20 12:31:09,578 - INFO - 当前日期 2025-05-19 有 204 条MySQL数据需要处理
2025-05-20 12:31:09,582 - INFO - 开始批量插入 2 条新记录
2025-05-20 12:31:09,719 - INFO - 批量插入响应状态码: 200
2025-05-20 12:31:09,719 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 20 May 2025 04:31:10 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'A7650DCF-B188-7ABA-86F3-760EA7D05AE3', 'x-acs-trace-id': 'f0cd67d73c2cb4ab7fc6c64a7b97d300', 'etag': '1UPIcNYX3AFgi4IeuzSa/Fw8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-20 12:31:09,719 - INFO - 批量插入响应体: {'result': ['FINST-XBF66071ABJVBKX8FZA5FCENTY362KCZN0WAMPF', 'FINST-XBF66071ABJVBKX8FZA5FCENTY362KCZN0WAMQF']}
2025-05-20 12:31:09,719 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-05-20 12:31:09,719 - INFO - 成功插入的数据ID: ['FINST-XBF66071ABJVBKX8FZA5FCENTY362KCZN0WAMPF', 'FINST-XBF66071ABJVBKX8FZA5FCENTY362KCZN0WAMQF']
2025-05-20 12:31:14,720 - INFO - 批量插入完成，共 2 条记录
2025-05-20 12:31:14,720 - INFO - 日期 2025-05-19 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-05-20 12:31:14,720 - INFO - 开始处理日期: 2025-05-20
2025-05-20 12:31:14,720 - INFO - Request Parameters - Page 1:
2025-05-20 12:31:14,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:31:14,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:31:15,235 - INFO - Response - Page 1:
2025-05-20 12:31:15,236 - INFO - 第 1 页获取到 2 条记录
2025-05-20 12:31:15,436 - INFO - 查询完成，共获取到 2 条记录
2025-05-20 12:31:15,436 - INFO - 获取到 2 条表单数据
2025-05-20 12:31:15,437 - INFO - 当前日期 2025-05-20 有 2 条MySQL数据需要处理
2025-05-20 12:31:15,437 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 12:31:15,437 - INFO - 数据同步完成！更新: 1 条，插入: 2 条，错误: 2 条
2025-05-20 12:32:15,437 - INFO - 开始同步昨天与今天的销售数据: 2025-05-19 至 2025-05-20
2025-05-20 12:32:15,437 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 12:32:15,437 - INFO - 查询参数: ('2025-05-19', '2025-05-20')
2025-05-20 12:32:15,517 - INFO - MySQL查询成功，时间段: 2025-05-19 至 2025-05-20，共获取 560 条记录
2025-05-20 12:32:15,517 - INFO - 获取到 2 个日期需要处理: ['2025-05-19', '2025-05-20']
2025-05-20 12:32:15,521 - INFO - 开始处理日期: 2025-05-19
2025-05-20 12:32:15,521 - INFO - Request Parameters - Page 1:
2025-05-20 12:32:15,521 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:32:15,521 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:32:16,572 - INFO - Response - Page 1:
2025-05-20 12:32:16,572 - INFO - 第 1 页获取到 100 条记录
2025-05-20 12:32:16,773 - INFO - Request Parameters - Page 2:
2025-05-20 12:32:16,773 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:32:16,773 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:32:17,870 - INFO - Response - Page 2:
2025-05-20 12:32:17,870 - INFO - 第 2 页获取到 100 条记录
2025-05-20 12:32:18,070 - INFO - Request Parameters - Page 3:
2025-05-20 12:32:18,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:32:18,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:32:18,858 - INFO - Response - Page 3:
2025-05-20 12:32:18,858 - INFO - 第 3 页获取到 100 条记录
2025-05-20 12:32:19,058 - INFO - Request Parameters - Page 4:
2025-05-20 12:32:19,058 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:32:19,058 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:32:19,855 - INFO - Response - Page 4:
2025-05-20 12:32:19,855 - INFO - 第 4 页获取到 100 条记录
2025-05-20 12:32:20,056 - INFO - Request Parameters - Page 5:
2025-05-20 12:32:20,056 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:32:20,056 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:32:20,919 - INFO - Response - Page 5:
2025-05-20 12:32:20,919 - INFO - 第 5 页获取到 100 条记录
2025-05-20 12:32:21,119 - INFO - Request Parameters - Page 6:
2025-05-20 12:32:21,119 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:32:21,119 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:32:21,848 - INFO - Response - Page 6:
2025-05-20 12:32:21,848 - INFO - 第 6 页获取到 57 条记录
2025-05-20 12:32:22,048 - INFO - 查询完成，共获取到 557 条记录
2025-05-20 12:32:22,048 - INFO - 获取到 557 条表单数据
2025-05-20 12:32:22,057 - INFO - 当前日期 2025-05-19 有 558 条MySQL数据需要处理
2025-05-20 12:32:22,066 - INFO - 开始批量插入 1 条新记录
2025-05-20 12:32:22,213 - INFO - 批量插入响应状态码: 200
2025-05-20 12:32:22,213 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 20 May 2025 04:32:22 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5F8967B9-DFEB-75CF-AA24-7C0D50D499E2', 'x-acs-trace-id': 'b3ac71ad571c4df146feda01f5d3fc39', 'etag': '6PQLRxuO+Xb68wDPu+Z0mtg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-20 12:32:22,213 - INFO - 批量插入响应体: {'result': ['FINST-PPA66671UDKV44QMAESZI9JDOGIQ29AJP0WAMD1']}
2025-05-20 12:32:22,213 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-20 12:32:22,213 - INFO - 成功插入的数据ID: ['FINST-PPA66671UDKV44QMAESZI9JDOGIQ29AJP0WAMD1']
2025-05-20 12:32:27,214 - INFO - 批量插入完成，共 1 条记录
2025-05-20 12:32:27,214 - INFO - 日期 2025-05-19 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-05-20 12:32:27,214 - INFO - 开始处理日期: 2025-05-20
2025-05-20 12:32:27,214 - INFO - Request Parameters - Page 1:
2025-05-20 12:32:27,214 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 12:32:27,214 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 12:32:27,724 - INFO - Response - Page 1:
2025-05-20 12:32:27,725 - INFO - 第 1 页获取到 2 条记录
2025-05-20 12:32:27,926 - INFO - 查询完成，共获取到 2 条记录
2025-05-20 12:32:27,926 - INFO - 获取到 2 条表单数据
2025-05-20 12:32:27,927 - INFO - 当前日期 2025-05-20 有 2 条MySQL数据需要处理
2025-05-20 12:32:27,927 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 12:32:27,927 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 0 条
2025-05-20 12:32:27,927 - INFO - 同步完成
2025-05-20 13:30:33,858 - INFO - 使用默认增量同步（当天更新数据）
2025-05-20 13:30:33,859 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 13:30:33,859 - INFO - 查询参数: ('2025-05-20',)
2025-05-20 13:30:33,935 - INFO - MySQL查询成功，增量数据（日期: 2025-05-20），共获取 210 条记录
2025-05-20 13:30:33,936 - INFO - 获取到 5 个日期需要处理: ['2025-05-16', '2025-05-17', '2025-05-18', '2025-05-19', '2025-05-20']
2025-05-20 13:30:33,939 - INFO - 开始处理日期: 2025-05-16
2025-05-20 13:30:33,942 - INFO - Request Parameters - Page 1:
2025-05-20 13:30:33,942 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 13:30:33,942 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 13:30:42,051 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5F188BFE-3624-7B73-BAC0-EAB4750D182F Response: {'code': 'ServiceUnavailable', 'requestid': '5F188BFE-3624-7B73-BAC0-EAB4750D182F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 5F188BFE-3624-7B73-BAC0-EAB4750D182F)
2025-05-20 13:30:42,051 - INFO - 开始处理日期: 2025-05-17
2025-05-20 13:30:42,051 - INFO - Request Parameters - Page 1:
2025-05-20 13:30:42,051 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 13:30:42,051 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 13:30:50,171 - ERROR - 处理日期 2025-05-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F531F68B-9554-7718-8A64-8EA9C31C9055 Response: {'code': 'ServiceUnavailable', 'requestid': 'F531F68B-9554-7718-8A64-8EA9C31C9055', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F531F68B-9554-7718-8A64-8EA9C31C9055)
2025-05-20 13:30:50,171 - INFO - 开始处理日期: 2025-05-18
2025-05-20 13:30:50,171 - INFO - Request Parameters - Page 1:
2025-05-20 13:30:50,171 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 13:30:50,171 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 13:30:58,426 - INFO - Response - Page 1:
2025-05-20 13:30:58,426 - INFO - 第 1 页获取到 100 条记录
2025-05-20 13:30:58,626 - INFO - Request Parameters - Page 2:
2025-05-20 13:30:58,626 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 13:30:58,626 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 13:30:59,480 - INFO - Response - Page 2:
2025-05-20 13:30:59,481 - INFO - 第 2 页获取到 100 条记录
2025-05-20 13:30:59,681 - INFO - Request Parameters - Page 3:
2025-05-20 13:30:59,681 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 13:30:59,681 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 13:31:00,477 - INFO - Response - Page 3:
2025-05-20 13:31:00,477 - INFO - 第 3 页获取到 100 条记录
2025-05-20 13:31:00,677 - INFO - Request Parameters - Page 4:
2025-05-20 13:31:00,677 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 13:31:00,677 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 13:31:01,457 - INFO - Response - Page 4:
2025-05-20 13:31:01,458 - INFO - 第 4 页获取到 100 条记录
2025-05-20 13:31:01,659 - INFO - Request Parameters - Page 5:
2025-05-20 13:31:01,659 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 13:31:01,659 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 13:31:02,522 - INFO - Response - Page 5:
2025-05-20 13:31:02,522 - INFO - 第 5 页获取到 100 条记录
2025-05-20 13:31:02,722 - INFO - Request Parameters - Page 6:
2025-05-20 13:31:02,722 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 13:31:02,722 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 13:31:03,648 - INFO - Response - Page 6:
2025-05-20 13:31:03,648 - INFO - 第 6 页获取到 100 条记录
2025-05-20 13:31:03,848 - INFO - Request Parameters - Page 7:
2025-05-20 13:31:03,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 13:31:03,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 13:31:04,464 - INFO - Response - Page 7:
2025-05-20 13:31:04,465 - INFO - 第 7 页获取到 13 条记录
2025-05-20 13:31:04,665 - INFO - 查询完成，共获取到 613 条记录
2025-05-20 13:31:04,665 - INFO - 获取到 613 条表单数据
2025-05-20 13:31:04,675 - INFO - 当前日期 2025-05-18 有 1 条MySQL数据需要处理
2025-05-20 13:31:04,675 - INFO - 日期 2025-05-18 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 13:31:04,675 - INFO - 开始处理日期: 2025-05-19
2025-05-20 13:31:04,675 - INFO - Request Parameters - Page 1:
2025-05-20 13:31:04,675 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 13:31:04,676 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 13:31:05,523 - INFO - Response - Page 1:
2025-05-20 13:31:05,523 - INFO - 第 1 页获取到 100 条记录
2025-05-20 13:31:05,723 - INFO - Request Parameters - Page 2:
2025-05-20 13:31:05,723 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 13:31:05,723 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 13:31:06,505 - INFO - Response - Page 2:
2025-05-20 13:31:06,505 - INFO - 第 2 页获取到 100 条记录
2025-05-20 13:31:06,705 - INFO - Request Parameters - Page 3:
2025-05-20 13:31:06,705 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 13:31:06,705 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 13:31:07,490 - INFO - Response - Page 3:
2025-05-20 13:31:07,490 - INFO - 第 3 页获取到 100 条记录
2025-05-20 13:31:07,691 - INFO - Request Parameters - Page 4:
2025-05-20 13:31:07,691 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 13:31:07,691 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 13:31:08,503 - INFO - Response - Page 4:
2025-05-20 13:31:08,503 - INFO - 第 4 页获取到 100 条记录
2025-05-20 13:31:08,703 - INFO - Request Parameters - Page 5:
2025-05-20 13:31:08,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 13:31:08,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 13:31:09,523 - INFO - Response - Page 5:
2025-05-20 13:31:09,523 - INFO - 第 5 页获取到 100 条记录
2025-05-20 13:31:09,723 - INFO - Request Parameters - Page 6:
2025-05-20 13:31:09,723 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 13:31:09,723 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 13:31:10,528 - INFO - Response - Page 6:
2025-05-20 13:31:10,528 - INFO - 第 6 页获取到 58 条记录
2025-05-20 13:31:10,728 - INFO - 查询完成，共获取到 558 条记录
2025-05-20 13:31:10,728 - INFO - 获取到 558 条表单数据
2025-05-20 13:31:10,739 - INFO - 当前日期 2025-05-19 有 205 条MySQL数据需要处理
2025-05-20 13:31:10,741 - INFO - 日期 2025-05-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 13:31:10,742 - INFO - 开始处理日期: 2025-05-20
2025-05-20 13:31:10,742 - INFO - Request Parameters - Page 1:
2025-05-20 13:31:10,742 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 13:31:10,742 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 13:31:11,227 - INFO - Response - Page 1:
2025-05-20 13:31:11,227 - INFO - 第 1 页获取到 2 条记录
2025-05-20 13:31:11,427 - INFO - 查询完成，共获取到 2 条记录
2025-05-20 13:31:11,427 - INFO - 获取到 2 条表单数据
2025-05-20 13:31:11,428 - INFO - 当前日期 2025-05-20 有 2 条MySQL数据需要处理
2025-05-20 13:31:11,428 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 13:31:11,428 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-20 13:32:11,429 - INFO - 开始同步昨天与今天的销售数据: 2025-05-19 至 2025-05-20
2025-05-20 13:32:11,429 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 13:32:11,429 - INFO - 查询参数: ('2025-05-19', '2025-05-20')
2025-05-20 13:32:11,509 - INFO - MySQL查询成功，时间段: 2025-05-19 至 2025-05-20，共获取 560 条记录
2025-05-20 13:32:11,509 - INFO - 获取到 2 个日期需要处理: ['2025-05-19', '2025-05-20']
2025-05-20 13:32:11,513 - INFO - 开始处理日期: 2025-05-19
2025-05-20 13:32:11,514 - INFO - Request Parameters - Page 1:
2025-05-20 13:32:11,514 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 13:32:11,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 13:32:12,383 - INFO - Response - Page 1:
2025-05-20 13:32:12,384 - INFO - 第 1 页获取到 100 条记录
2025-05-20 13:32:12,584 - INFO - Request Parameters - Page 2:
2025-05-20 13:32:12,584 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 13:32:12,584 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 13:32:13,401 - INFO - Response - Page 2:
2025-05-20 13:32:13,401 - INFO - 第 2 页获取到 100 条记录
2025-05-20 13:32:13,601 - INFO - Request Parameters - Page 3:
2025-05-20 13:32:13,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 13:32:13,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 13:32:14,384 - INFO - Response - Page 3:
2025-05-20 13:32:14,384 - INFO - 第 3 页获取到 100 条记录
2025-05-20 13:32:14,584 - INFO - Request Parameters - Page 4:
2025-05-20 13:32:14,584 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 13:32:14,584 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 13:32:15,406 - INFO - Response - Page 4:
2025-05-20 13:32:15,406 - INFO - 第 4 页获取到 100 条记录
2025-05-20 13:32:15,606 - INFO - Request Parameters - Page 5:
2025-05-20 13:32:15,606 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 13:32:15,606 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 13:32:16,542 - INFO - Response - Page 5:
2025-05-20 13:32:16,542 - INFO - 第 5 页获取到 100 条记录
2025-05-20 13:32:16,742 - INFO - Request Parameters - Page 6:
2025-05-20 13:32:16,742 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 13:32:16,742 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 13:32:17,520 - INFO - Response - Page 6:
2025-05-20 13:32:17,520 - INFO - 第 6 页获取到 58 条记录
2025-05-20 13:32:17,721 - INFO - 查询完成，共获取到 558 条记录
2025-05-20 13:32:17,721 - INFO - 获取到 558 条表单数据
2025-05-20 13:32:17,730 - INFO - 当前日期 2025-05-19 有 558 条MySQL数据需要处理
2025-05-20 13:32:17,740 - INFO - 日期 2025-05-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 13:32:17,740 - INFO - 开始处理日期: 2025-05-20
2025-05-20 13:32:17,740 - INFO - Request Parameters - Page 1:
2025-05-20 13:32:17,740 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 13:32:17,740 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 13:32:18,212 - INFO - Response - Page 1:
2025-05-20 13:32:18,212 - INFO - 第 1 页获取到 2 条记录
2025-05-20 13:32:18,412 - INFO - 查询完成，共获取到 2 条记录
2025-05-20 13:32:18,412 - INFO - 获取到 2 条表单数据
2025-05-20 13:32:18,413 - INFO - 当前日期 2025-05-20 有 2 条MySQL数据需要处理
2025-05-20 13:32:18,413 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 13:32:18,413 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 13:32:18,414 - INFO - 同步完成
2025-05-20 14:30:34,199 - INFO - 使用默认增量同步（当天更新数据）
2025-05-20 14:30:34,200 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 14:30:34,200 - INFO - 查询参数: ('2025-05-20',)
2025-05-20 14:30:34,275 - INFO - MySQL查询成功，增量数据（日期: 2025-05-20），共获取 214 条记录
2025-05-20 14:30:34,275 - INFO - 获取到 5 个日期需要处理: ['2025-05-16', '2025-05-17', '2025-05-18', '2025-05-19', '2025-05-20']
2025-05-20 14:30:34,277 - INFO - 开始处理日期: 2025-05-16
2025-05-20 14:30:34,280 - INFO - Request Parameters - Page 1:
2025-05-20 14:30:34,280 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 14:30:34,280 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 14:30:42,414 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 9B9069B8-BF26-72FB-8873-1FB1BF526D9B Response: {'code': 'ServiceUnavailable', 'requestid': '9B9069B8-BF26-72FB-8873-1FB1BF526D9B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 9B9069B8-BF26-72FB-8873-1FB1BF526D9B)
2025-05-20 14:30:42,414 - INFO - 开始处理日期: 2025-05-17
2025-05-20 14:30:42,414 - INFO - Request Parameters - Page 1:
2025-05-20 14:30:42,415 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 14:30:42,415 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 14:30:50,525 - ERROR - 处理日期 2025-05-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 1065BD14-F193-7AF4-BCC7-EEC76B58CF55 Response: {'code': 'ServiceUnavailable', 'requestid': '1065BD14-F193-7AF4-BCC7-EEC76B58CF55', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 1065BD14-F193-7AF4-BCC7-EEC76B58CF55)
2025-05-20 14:30:50,525 - INFO - 开始处理日期: 2025-05-18
2025-05-20 14:30:50,525 - INFO - Request Parameters - Page 1:
2025-05-20 14:30:50,525 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 14:30:50,525 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 14:30:52,891 - INFO - Response - Page 1:
2025-05-20 14:30:52,892 - INFO - 第 1 页获取到 100 条记录
2025-05-20 14:30:53,092 - INFO - Request Parameters - Page 2:
2025-05-20 14:30:53,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 14:30:53,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 14:30:53,926 - INFO - Response - Page 2:
2025-05-20 14:30:53,927 - INFO - 第 2 页获取到 100 条记录
2025-05-20 14:30:54,127 - INFO - Request Parameters - Page 3:
2025-05-20 14:30:54,127 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 14:30:54,127 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 14:30:54,959 - INFO - Response - Page 3:
2025-05-20 14:30:54,959 - INFO - 第 3 页获取到 100 条记录
2025-05-20 14:30:55,159 - INFO - Request Parameters - Page 4:
2025-05-20 14:30:55,159 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 14:30:55,159 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 14:30:56,017 - INFO - Response - Page 4:
2025-05-20 14:30:56,017 - INFO - 第 4 页获取到 100 条记录
2025-05-20 14:30:56,217 - INFO - Request Parameters - Page 5:
2025-05-20 14:30:56,217 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 14:30:56,217 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 14:30:57,199 - INFO - Response - Page 5:
2025-05-20 14:30:57,199 - INFO - 第 5 页获取到 100 条记录
2025-05-20 14:30:57,399 - INFO - Request Parameters - Page 6:
2025-05-20 14:30:57,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 14:30:57,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 14:30:58,202 - INFO - Response - Page 6:
2025-05-20 14:30:58,203 - INFO - 第 6 页获取到 100 条记录
2025-05-20 14:30:58,403 - INFO - Request Parameters - Page 7:
2025-05-20 14:30:58,403 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 14:30:58,403 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 14:30:59,000 - INFO - Response - Page 7:
2025-05-20 14:30:59,001 - INFO - 第 7 页获取到 13 条记录
2025-05-20 14:30:59,201 - INFO - 查询完成，共获取到 613 条记录
2025-05-20 14:30:59,201 - INFO - 获取到 613 条表单数据
2025-05-20 14:30:59,211 - INFO - 当前日期 2025-05-18 有 1 条MySQL数据需要处理
2025-05-20 14:30:59,211 - INFO - 日期 2025-05-18 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 14:30:59,211 - INFO - 开始处理日期: 2025-05-19
2025-05-20 14:30:59,211 - INFO - Request Parameters - Page 1:
2025-05-20 14:30:59,211 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 14:30:59,211 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 14:31:00,105 - INFO - Response - Page 1:
2025-05-20 14:31:00,105 - INFO - 第 1 页获取到 100 条记录
2025-05-20 14:31:00,305 - INFO - Request Parameters - Page 2:
2025-05-20 14:31:00,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 14:31:00,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 14:31:01,128 - INFO - Response - Page 2:
2025-05-20 14:31:01,129 - INFO - 第 2 页获取到 100 条记录
2025-05-20 14:31:01,329 - INFO - Request Parameters - Page 3:
2025-05-20 14:31:01,329 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 14:31:01,329 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 14:31:02,198 - INFO - Response - Page 3:
2025-05-20 14:31:02,198 - INFO - 第 3 页获取到 100 条记录
2025-05-20 14:31:02,398 - INFO - Request Parameters - Page 4:
2025-05-20 14:31:02,398 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 14:31:02,398 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 14:31:03,258 - INFO - Response - Page 4:
2025-05-20 14:31:03,258 - INFO - 第 4 页获取到 100 条记录
2025-05-20 14:31:03,458 - INFO - Request Parameters - Page 5:
2025-05-20 14:31:03,458 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 14:31:03,458 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 14:31:04,223 - INFO - Response - Page 5:
2025-05-20 14:31:04,223 - INFO - 第 5 页获取到 100 条记录
2025-05-20 14:31:04,423 - INFO - Request Parameters - Page 6:
2025-05-20 14:31:04,423 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 14:31:04,423 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 14:31:05,181 - INFO - Response - Page 6:
2025-05-20 14:31:05,181 - INFO - 第 6 页获取到 58 条记录
2025-05-20 14:31:05,381 - INFO - 查询完成，共获取到 558 条记录
2025-05-20 14:31:05,381 - INFO - 获取到 558 条表单数据
2025-05-20 14:31:05,392 - INFO - 当前日期 2025-05-19 有 209 条MySQL数据需要处理
2025-05-20 14:31:05,396 - INFO - 开始批量插入 4 条新记录
2025-05-20 14:31:05,579 - INFO - 批量插入响应状态码: 200
2025-05-20 14:31:05,580 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 20 May 2025 06:31:05 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '204', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0627DE71-3580-7FAD-BC93-0F49AD756524', 'x-acs-trace-id': '1d2be71da5227b15ab4c366ebccae3b5', 'etag': '2EYh6mB4kfgngJYeAH0U6GA4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-20 14:31:05,580 - INFO - 批量插入响应体: {'result': ['FINST-WBF66B81WDKVIAT69CAVW7DYYCU53WQ7Y4WAMA2', 'FINST-WBF66B81WDKVIAT69CAVW7DYYCU53WQ7Y4WAMB2', 'FINST-WBF66B81WDKVIAT69CAVW7DYYCU53WQ7Y4WAMC2', 'FINST-WBF66B81WDKVIAT69CAVW7DYYCU53WQ7Y4WAMD2']}
2025-05-20 14:31:05,580 - INFO - 批量插入表单数据成功，批次 1，共 4 条记录
2025-05-20 14:31:05,580 - INFO - 成功插入的数据ID: ['FINST-WBF66B81WDKVIAT69CAVW7DYYCU53WQ7Y4WAMA2', 'FINST-WBF66B81WDKVIAT69CAVW7DYYCU53WQ7Y4WAMB2', 'FINST-WBF66B81WDKVIAT69CAVW7DYYCU53WQ7Y4WAMC2', 'FINST-WBF66B81WDKVIAT69CAVW7DYYCU53WQ7Y4WAMD2']
2025-05-20 14:31:10,581 - INFO - 批量插入完成，共 4 条记录
2025-05-20 14:31:10,581 - INFO - 日期 2025-05-19 处理完成 - 更新: 0 条，插入: 4 条，错误: 0 条
2025-05-20 14:31:10,581 - INFO - 开始处理日期: 2025-05-20
2025-05-20 14:31:10,581 - INFO - Request Parameters - Page 1:
2025-05-20 14:31:10,581 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 14:31:10,581 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 14:31:11,065 - INFO - Response - Page 1:
2025-05-20 14:31:11,065 - INFO - 第 1 页获取到 2 条记录
2025-05-20 14:31:11,266 - INFO - 查询完成，共获取到 2 条记录
2025-05-20 14:31:11,266 - INFO - 获取到 2 条表单数据
2025-05-20 14:31:11,267 - INFO - 当前日期 2025-05-20 有 2 条MySQL数据需要处理
2025-05-20 14:31:11,267 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 14:31:11,267 - INFO - 数据同步完成！更新: 0 条，插入: 4 条，错误: 2 条
2025-05-20 14:32:11,267 - INFO - 开始同步昨天与今天的销售数据: 2025-05-19 至 2025-05-20
2025-05-20 14:32:11,267 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 14:32:11,267 - INFO - 查询参数: ('2025-05-19', '2025-05-20')
2025-05-20 14:32:11,348 - INFO - MySQL查询成功，时间段: 2025-05-19 至 2025-05-20，共获取 573 条记录
2025-05-20 14:32:11,348 - INFO - 获取到 2 个日期需要处理: ['2025-05-19', '2025-05-20']
2025-05-20 14:32:11,353 - INFO - 开始处理日期: 2025-05-19
2025-05-20 14:32:11,353 - INFO - Request Parameters - Page 1:
2025-05-20 14:32:11,353 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 14:32:11,353 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 14:32:12,171 - INFO - Response - Page 1:
2025-05-20 14:32:12,171 - INFO - 第 1 页获取到 100 条记录
2025-05-20 14:32:12,372 - INFO - Request Parameters - Page 2:
2025-05-20 14:32:12,372 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 14:32:12,372 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 14:32:13,240 - INFO - Response - Page 2:
2025-05-20 14:32:13,241 - INFO - 第 2 页获取到 100 条记录
2025-05-20 14:32:13,441 - INFO - Request Parameters - Page 3:
2025-05-20 14:32:13,441 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 14:32:13,441 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 14:32:14,257 - INFO - Response - Page 3:
2025-05-20 14:32:14,257 - INFO - 第 3 页获取到 100 条记录
2025-05-20 14:32:14,458 - INFO - Request Parameters - Page 4:
2025-05-20 14:32:14,458 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 14:32:14,458 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 14:32:15,286 - INFO - Response - Page 4:
2025-05-20 14:32:15,286 - INFO - 第 4 页获取到 100 条记录
2025-05-20 14:32:15,486 - INFO - Request Parameters - Page 5:
2025-05-20 14:32:15,486 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 14:32:15,486 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 14:32:16,442 - INFO - Response - Page 5:
2025-05-20 14:32:16,442 - INFO - 第 5 页获取到 100 条记录
2025-05-20 14:32:16,642 - INFO - Request Parameters - Page 6:
2025-05-20 14:32:16,642 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 14:32:16,642 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 14:32:17,396 - INFO - Response - Page 6:
2025-05-20 14:32:17,396 - INFO - 第 6 页获取到 62 条记录
2025-05-20 14:32:17,596 - INFO - 查询完成，共获取到 562 条记录
2025-05-20 14:32:17,596 - INFO - 获取到 562 条表单数据
2025-05-20 14:32:17,605 - INFO - 当前日期 2025-05-19 有 571 条MySQL数据需要处理
2025-05-20 14:32:17,615 - INFO - 开始批量插入 9 条新记录
2025-05-20 14:32:17,777 - INFO - 批量插入响应状态码: 200
2025-05-20 14:32:17,777 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 20 May 2025 06:32:18 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '444', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '122ACDA6-E521-7019-9EA9-C9EAA4FB747A', 'x-acs-trace-id': '1b0948bf8b1ce7c3212aa4098b5f48b5', 'etag': '4SqWCOaivU/gVb5BZJnbgYw4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-20 14:32:17,777 - INFO - 批量插入响应体: {'result': ['FINST-XBF660710DKV5X3S6JTK1514VDFZ17GRZ4WAMGC', 'FINST-XBF660710DKV5X3S6JTK1514VDFZ17GRZ4WAMHC', 'FINST-XBF660710DKV5X3S6JTK1514VDFZ17GRZ4WAMIC', 'FINST-XBF660710DKV5X3S6JTK1514VDFZ17GRZ4WAMJC', 'FINST-XBF660710DKV5X3S6JTK1514VDFZ17GRZ4WAMKC', 'FINST-XBF660710DKV5X3S6JTK1514VDFZ17GRZ4WAMLC', 'FINST-XBF660710DKV5X3S6JTK1514VDFZ17GRZ4WAMMC', 'FINST-XBF660710DKV5X3S6JTK1514VDFZ17GRZ4WAMNC', 'FINST-XBF660710DKV5X3S6JTK1514VDFZ17GRZ4WAMOC']}
2025-05-20 14:32:17,777 - INFO - 批量插入表单数据成功，批次 1，共 9 条记录
2025-05-20 14:32:17,777 - INFO - 成功插入的数据ID: ['FINST-XBF660710DKV5X3S6JTK1514VDFZ17GRZ4WAMGC', 'FINST-XBF660710DKV5X3S6JTK1514VDFZ17GRZ4WAMHC', 'FINST-XBF660710DKV5X3S6JTK1514VDFZ17GRZ4WAMIC', 'FINST-XBF660710DKV5X3S6JTK1514VDFZ17GRZ4WAMJC', 'FINST-XBF660710DKV5X3S6JTK1514VDFZ17GRZ4WAMKC', 'FINST-XBF660710DKV5X3S6JTK1514VDFZ17GRZ4WAMLC', 'FINST-XBF660710DKV5X3S6JTK1514VDFZ17GRZ4WAMMC', 'FINST-XBF660710DKV5X3S6JTK1514VDFZ17GRZ4WAMNC', 'FINST-XBF660710DKV5X3S6JTK1514VDFZ17GRZ4WAMOC']
2025-05-20 14:32:22,778 - INFO - 批量插入完成，共 9 条记录
2025-05-20 14:32:22,778 - INFO - 日期 2025-05-19 处理完成 - 更新: 0 条，插入: 9 条，错误: 0 条
2025-05-20 14:32:22,778 - INFO - 开始处理日期: 2025-05-20
2025-05-20 14:32:22,778 - INFO - Request Parameters - Page 1:
2025-05-20 14:32:22,778 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 14:32:22,778 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 14:32:23,288 - INFO - Response - Page 1:
2025-05-20 14:32:23,288 - INFO - 第 1 页获取到 2 条记录
2025-05-20 14:32:23,488 - INFO - 查询完成，共获取到 2 条记录
2025-05-20 14:32:23,488 - INFO - 获取到 2 条表单数据
2025-05-20 14:32:23,489 - INFO - 当前日期 2025-05-20 有 2 条MySQL数据需要处理
2025-05-20 14:32:23,489 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 14:32:23,489 - INFO - 数据同步完成！更新: 0 条，插入: 9 条，错误: 0 条
2025-05-20 14:32:23,489 - INFO - 同步完成
2025-05-20 15:30:33,575 - INFO - 使用默认增量同步（当天更新数据）
2025-05-20 15:30:33,575 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 15:30:33,575 - INFO - 查询参数: ('2025-05-20',)
2025-05-20 15:30:33,652 - INFO - MySQL查询成功，增量数据（日期: 2025-05-20），共获取 222 条记录
2025-05-20 15:30:33,652 - INFO - 获取到 5 个日期需要处理: ['2025-05-16', '2025-05-17', '2025-05-18', '2025-05-19', '2025-05-20']
2025-05-20 15:30:33,653 - INFO - 开始处理日期: 2025-05-16
2025-05-20 15:30:33,656 - INFO - Request Parameters - Page 1:
2025-05-20 15:30:33,656 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:30:33,656 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:30:41,777 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 441C9193-C7EA-7B2D-AF7F-31283B1EA4CC Response: {'code': 'ServiceUnavailable', 'requestid': '441C9193-C7EA-7B2D-AF7F-31283B1EA4CC', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 441C9193-C7EA-7B2D-AF7F-31283B1EA4CC)
2025-05-20 15:30:41,777 - INFO - 开始处理日期: 2025-05-17
2025-05-20 15:30:41,777 - INFO - Request Parameters - Page 1:
2025-05-20 15:30:41,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:30:41,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:30:49,893 - ERROR - 处理日期 2025-05-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: FB6D6312-61F5-731F-BC74-25BDD93533DB Response: {'code': 'ServiceUnavailable', 'requestid': 'FB6D6312-61F5-731F-BC74-25BDD93533DB', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: FB6D6312-61F5-731F-BC74-25BDD93533DB)
2025-05-20 15:30:49,894 - INFO - 开始处理日期: 2025-05-18
2025-05-20 15:30:49,894 - INFO - Request Parameters - Page 1:
2025-05-20 15:30:49,894 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:30:49,894 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:30:58,003 - ERROR - 处理日期 2025-05-18 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F114B4DE-9994-7F72-A566-48780886FE6F Response: {'code': 'ServiceUnavailable', 'requestid': 'F114B4DE-9994-7F72-A566-48780886FE6F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F114B4DE-9994-7F72-A566-48780886FE6F)
2025-05-20 15:30:58,003 - INFO - 开始处理日期: 2025-05-19
2025-05-20 15:30:58,003 - INFO - Request Parameters - Page 1:
2025-05-20 15:30:58,003 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:30:58,003 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:30:58,904 - INFO - Response - Page 1:
2025-05-20 15:30:58,904 - INFO - 第 1 页获取到 100 条记录
2025-05-20 15:30:59,104 - INFO - Request Parameters - Page 2:
2025-05-20 15:30:59,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:30:59,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:30:59,928 - INFO - Response - Page 2:
2025-05-20 15:30:59,928 - INFO - 第 2 页获取到 100 条记录
2025-05-20 15:31:00,128 - INFO - Request Parameters - Page 3:
2025-05-20 15:31:00,128 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:31:00,128 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:31:01,069 - INFO - Response - Page 3:
2025-05-20 15:31:01,069 - INFO - 第 3 页获取到 100 条记录
2025-05-20 15:31:01,269 - INFO - Request Parameters - Page 4:
2025-05-20 15:31:01,269 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:31:01,269 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:31:02,075 - INFO - Response - Page 4:
2025-05-20 15:31:02,075 - INFO - 第 4 页获取到 100 条记录
2025-05-20 15:31:02,275 - INFO - Request Parameters - Page 5:
2025-05-20 15:31:02,275 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:31:02,275 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:31:03,075 - INFO - Response - Page 5:
2025-05-20 15:31:03,075 - INFO - 第 5 页获取到 100 条记录
2025-05-20 15:31:03,275 - INFO - Request Parameters - Page 6:
2025-05-20 15:31:03,275 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:31:03,275 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:31:03,983 - INFO - Response - Page 6:
2025-05-20 15:31:03,984 - INFO - 第 6 页获取到 71 条记录
2025-05-20 15:31:04,185 - INFO - 查询完成，共获取到 571 条记录
2025-05-20 15:31:04,185 - INFO - 获取到 571 条表单数据
2025-05-20 15:31:04,194 - INFO - 当前日期 2025-05-19 有 217 条MySQL数据需要处理
2025-05-20 15:31:04,198 - INFO - 开始批量插入 8 条新记录
2025-05-20 15:31:04,368 - INFO - 批量插入响应状态码: 200
2025-05-20 15:31:04,369 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 20 May 2025 07:31:04 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '388', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7B1A5942-3C0F-71BA-90B3-FE3DB5EBAE85', 'x-acs-trace-id': '3a0ff484698dfa11efff033f83337de9', 'etag': '32ENZN9bJr5HZXCJxwP8SRg8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-20 15:31:04,369 - INFO - 批量插入响应体: {'result': ['FINST-LFA66G91YALVZB7Y98U6YCO3PG272ULC37WAM0', 'FINST-LFA66G91YALVZB7Y98U6YCO3PG272ULC37WAM1', 'FINST-LFA66G91YALVZB7Y98U6YCO3PG272ULC37WAM2', 'FINST-LFA66G91YALVZB7Y98U6YCO3PG272ULC37WAM3', 'FINST-LFA66G91YALVZB7Y98U6YCO3PG272ULC37WAM4', 'FINST-LFA66G91YALVZB7Y98U6YCO3PG272ULC37WAM5', 'FINST-LFA66G91YALVZB7Y98U6YCO3PG272ULC37WAM6', 'FINST-LFA66G91YALVZB7Y98U6YCO3PG272ULC37WAM7']}
2025-05-20 15:31:04,369 - INFO - 批量插入表单数据成功，批次 1，共 8 条记录
2025-05-20 15:31:04,369 - INFO - 成功插入的数据ID: ['FINST-LFA66G91YALVZB7Y98U6YCO3PG272ULC37WAM0', 'FINST-LFA66G91YALVZB7Y98U6YCO3PG272ULC37WAM1', 'FINST-LFA66G91YALVZB7Y98U6YCO3PG272ULC37WAM2', 'FINST-LFA66G91YALVZB7Y98U6YCO3PG272ULC37WAM3', 'FINST-LFA66G91YALVZB7Y98U6YCO3PG272ULC37WAM4', 'FINST-LFA66G91YALVZB7Y98U6YCO3PG272ULC37WAM5', 'FINST-LFA66G91YALVZB7Y98U6YCO3PG272ULC37WAM6', 'FINST-LFA66G91YALVZB7Y98U6YCO3PG272ULC37WAM7']
2025-05-20 15:31:09,370 - INFO - 批量插入完成，共 8 条记录
2025-05-20 15:31:09,370 - INFO - 日期 2025-05-19 处理完成 - 更新: 0 条，插入: 8 条，错误: 0 条
2025-05-20 15:31:09,370 - INFO - 开始处理日期: 2025-05-20
2025-05-20 15:31:09,370 - INFO - Request Parameters - Page 1:
2025-05-20 15:31:09,370 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:31:09,370 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:31:09,970 - INFO - Response - Page 1:
2025-05-20 15:31:09,970 - INFO - 第 1 页获取到 2 条记录
2025-05-20 15:31:10,170 - INFO - 查询完成，共获取到 2 条记录
2025-05-20 15:31:10,170 - INFO - 获取到 2 条表单数据
2025-05-20 15:31:10,171 - INFO - 当前日期 2025-05-20 有 2 条MySQL数据需要处理
2025-05-20 15:31:10,171 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 15:31:10,171 - INFO - 数据同步完成！更新: 0 条，插入: 8 条，错误: 3 条
2025-05-20 15:32:10,171 - INFO - 开始同步昨天与今天的销售数据: 2025-05-19 至 2025-05-20
2025-05-20 15:32:10,171 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 15:32:10,171 - INFO - 查询参数: ('2025-05-19', '2025-05-20')
2025-05-20 15:32:10,255 - INFO - MySQL查询成功，时间段: 2025-05-19 至 2025-05-20，共获取 603 条记录
2025-05-20 15:32:10,255 - INFO - 获取到 2 个日期需要处理: ['2025-05-19', '2025-05-20']
2025-05-20 15:32:10,261 - INFO - 开始处理日期: 2025-05-19
2025-05-20 15:32:10,261 - INFO - Request Parameters - Page 1:
2025-05-20 15:32:10,261 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:32:10,261 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:32:11,179 - INFO - Response - Page 1:
2025-05-20 15:32:11,179 - INFO - 第 1 页获取到 100 条记录
2025-05-20 15:32:11,379 - INFO - Request Parameters - Page 2:
2025-05-20 15:32:11,379 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:32:11,379 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:32:12,227 - INFO - Response - Page 2:
2025-05-20 15:32:12,227 - INFO - 第 2 页获取到 100 条记录
2025-05-20 15:32:12,427 - INFO - Request Parameters - Page 3:
2025-05-20 15:32:12,427 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:32:12,427 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:32:13,184 - INFO - Response - Page 3:
2025-05-20 15:32:13,185 - INFO - 第 3 页获取到 100 条记录
2025-05-20 15:32:13,385 - INFO - Request Parameters - Page 4:
2025-05-20 15:32:13,385 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:32:13,385 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:32:14,237 - INFO - Response - Page 4:
2025-05-20 15:32:14,238 - INFO - 第 4 页获取到 100 条记录
2025-05-20 15:32:14,438 - INFO - Request Parameters - Page 5:
2025-05-20 15:32:14,438 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:32:14,438 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:32:15,269 - INFO - Response - Page 5:
2025-05-20 15:32:15,269 - INFO - 第 5 页获取到 100 条记录
2025-05-20 15:32:15,469 - INFO - Request Parameters - Page 6:
2025-05-20 15:32:15,469 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:32:15,469 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:32:16,256 - INFO - Response - Page 6:
2025-05-20 15:32:16,256 - INFO - 第 6 页获取到 79 条记录
2025-05-20 15:32:16,457 - INFO - 查询完成，共获取到 579 条记录
2025-05-20 15:32:16,457 - INFO - 获取到 579 条表单数据
2025-05-20 15:32:16,466 - INFO - 当前日期 2025-05-19 有 601 条MySQL数据需要处理
2025-05-20 15:32:16,477 - INFO - 开始批量插入 22 条新记录
2025-05-20 15:32:16,649 - INFO - 批量插入响应状态码: 200
2025-05-20 15:32:16,649 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 20 May 2025 07:32:17 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1068', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7EEE505D-D308-7B95-BDE8-34F7FFDE10A8', 'x-acs-trace-id': '61e11e38f63324d8762a812e041e327f', 'etag': '1MfiM9qSORT9rZtNF4mTMfQ8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-20 15:32:16,649 - INFO - 批量插入响应体: {'result': ['FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMC1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMD1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAME1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMF1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMG1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMH1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMI1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMJ1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMK1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAML1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMM1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMN1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMO1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMP1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMQ1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMR1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMS1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMT1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMU1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMV1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMW1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMX1']}
2025-05-20 15:32:16,649 - INFO - 批量插入表单数据成功，批次 1，共 22 条记录
2025-05-20 15:32:16,649 - INFO - 成功插入的数据ID: ['FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMC1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMD1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAME1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMF1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMG1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMH1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMI1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMJ1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMK1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAML1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMM1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMN1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMO1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMP1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMQ1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMR1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMS1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMT1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMU1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMV1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMW1', 'FINST-QVA66B81EBKVOC828S43KBGXBCBD39DW47WAMX1']
2025-05-20 15:32:21,650 - INFO - 批量插入完成，共 22 条记录
2025-05-20 15:32:21,650 - INFO - 日期 2025-05-19 处理完成 - 更新: 0 条，插入: 22 条，错误: 0 条
2025-05-20 15:32:21,650 - INFO - 开始处理日期: 2025-05-20
2025-05-20 15:32:21,650 - INFO - Request Parameters - Page 1:
2025-05-20 15:32:21,650 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 15:32:21,650 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 15:32:22,156 - INFO - Response - Page 1:
2025-05-20 15:32:22,156 - INFO - 第 1 页获取到 2 条记录
2025-05-20 15:32:22,356 - INFO - 查询完成，共获取到 2 条记录
2025-05-20 15:32:22,356 - INFO - 获取到 2 条表单数据
2025-05-20 15:32:22,357 - INFO - 当前日期 2025-05-20 有 2 条MySQL数据需要处理
2025-05-20 15:32:22,357 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 15:32:22,357 - INFO - 数据同步完成！更新: 0 条，插入: 22 条，错误: 0 条
2025-05-20 15:32:22,357 - INFO - 同步完成
2025-05-20 16:30:34,259 - INFO - 使用默认增量同步（当天更新数据）
2025-05-20 16:30:34,259 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 16:30:34,260 - INFO - 查询参数: ('2025-05-20',)
2025-05-20 16:30:34,331 - INFO - MySQL查询成功，增量数据（日期: 2025-05-20），共获取 225 条记录
2025-05-20 16:30:34,332 - INFO - 获取到 5 个日期需要处理: ['2025-05-16', '2025-05-17', '2025-05-18', '2025-05-19', '2025-05-20']
2025-05-20 16:30:34,333 - INFO - 开始处理日期: 2025-05-16
2025-05-20 16:30:34,336 - INFO - Request Parameters - Page 1:
2025-05-20 16:30:34,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 16:30:34,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 16:30:42,456 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 4EF6EE9B-ED0C-7543-A9C0-05C930CDDE95 Response: {'code': 'ServiceUnavailable', 'requestid': '4EF6EE9B-ED0C-7543-A9C0-05C930CDDE95', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 4EF6EE9B-ED0C-7543-A9C0-05C930CDDE95)
2025-05-20 16:30:42,456 - INFO - 开始处理日期: 2025-05-17
2025-05-20 16:30:42,456 - INFO - Request Parameters - Page 1:
2025-05-20 16:30:42,456 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 16:30:42,456 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 16:30:50,570 - ERROR - 处理日期 2025-05-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E3AE0BFD-3952-7B14-B625-BE8190611452 Response: {'code': 'ServiceUnavailable', 'requestid': 'E3AE0BFD-3952-7B14-B625-BE8190611452', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E3AE0BFD-3952-7B14-B625-BE8190611452)
2025-05-20 16:30:50,570 - INFO - 开始处理日期: 2025-05-18
2025-05-20 16:30:50,570 - INFO - Request Parameters - Page 1:
2025-05-20 16:30:50,570 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 16:30:50,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 16:30:53,166 - INFO - Response - Page 1:
2025-05-20 16:30:53,166 - INFO - 第 1 页获取到 100 条记录
2025-05-20 16:30:53,366 - INFO - Request Parameters - Page 2:
2025-05-20 16:30:53,366 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 16:30:53,366 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 16:30:54,202 - INFO - Response - Page 2:
2025-05-20 16:30:54,203 - INFO - 第 2 页获取到 100 条记录
2025-05-20 16:30:54,403 - INFO - Request Parameters - Page 3:
2025-05-20 16:30:54,403 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 16:30:54,403 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 16:30:55,279 - INFO - Response - Page 3:
2025-05-20 16:30:55,279 - INFO - 第 3 页获取到 100 条记录
2025-05-20 16:30:55,480 - INFO - Request Parameters - Page 4:
2025-05-20 16:30:55,480 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 16:30:55,480 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 16:30:56,259 - INFO - Response - Page 4:
2025-05-20 16:30:56,260 - INFO - 第 4 页获取到 100 条记录
2025-05-20 16:30:56,460 - INFO - Request Parameters - Page 5:
2025-05-20 16:30:56,460 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 16:30:56,460 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 16:30:57,392 - INFO - Response - Page 5:
2025-05-20 16:30:57,393 - INFO - 第 5 页获取到 100 条记录
2025-05-20 16:30:57,593 - INFO - Request Parameters - Page 6:
2025-05-20 16:30:57,593 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 16:30:57,593 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 16:30:58,489 - INFO - Response - Page 6:
2025-05-20 16:30:58,489 - INFO - 第 6 页获取到 100 条记录
2025-05-20 16:30:58,690 - INFO - Request Parameters - Page 7:
2025-05-20 16:30:58,690 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 16:30:58,690 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 16:30:59,309 - INFO - Response - Page 7:
2025-05-20 16:30:59,310 - INFO - 第 7 页获取到 13 条记录
2025-05-20 16:30:59,510 - INFO - 查询完成，共获取到 613 条记录
2025-05-20 16:30:59,510 - INFO - 获取到 613 条表单数据
2025-05-20 16:30:59,520 - INFO - 当前日期 2025-05-18 有 1 条MySQL数据需要处理
2025-05-20 16:30:59,520 - INFO - 日期 2025-05-18 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 16:30:59,520 - INFO - 开始处理日期: 2025-05-19
2025-05-20 16:30:59,521 - INFO - Request Parameters - Page 1:
2025-05-20 16:30:59,521 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 16:30:59,521 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 16:31:00,429 - INFO - Response - Page 1:
2025-05-20 16:31:00,429 - INFO - 第 1 页获取到 100 条记录
2025-05-20 16:31:00,630 - INFO - Request Parameters - Page 2:
2025-05-20 16:31:00,630 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 16:31:00,630 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 16:31:01,507 - INFO - Response - Page 2:
2025-05-20 16:31:01,507 - INFO - 第 2 页获取到 100 条记录
2025-05-20 16:31:01,707 - INFO - Request Parameters - Page 3:
2025-05-20 16:31:01,707 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 16:31:01,707 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 16:31:02,590 - INFO - Response - Page 3:
2025-05-20 16:31:02,590 - INFO - 第 3 页获取到 100 条记录
2025-05-20 16:31:02,791 - INFO - Request Parameters - Page 4:
2025-05-20 16:31:02,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 16:31:02,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 16:31:03,634 - INFO - Response - Page 4:
2025-05-20 16:31:03,635 - INFO - 第 4 页获取到 100 条记录
2025-05-20 16:31:03,835 - INFO - Request Parameters - Page 5:
2025-05-20 16:31:03,835 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 16:31:03,835 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 16:31:04,626 - INFO - Response - Page 5:
2025-05-20 16:31:04,626 - INFO - 第 5 页获取到 100 条记录
2025-05-20 16:31:04,826 - INFO - Request Parameters - Page 6:
2025-05-20 16:31:04,826 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 16:31:04,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 16:31:05,653 - INFO - Response - Page 6:
2025-05-20 16:31:05,653 - INFO - 第 6 页获取到 100 条记录
2025-05-20 16:31:05,854 - INFO - Request Parameters - Page 7:
2025-05-20 16:31:05,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 16:31:05,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 16:31:06,383 - INFO - Response - Page 7:
2025-05-20 16:31:06,383 - INFO - 第 7 页获取到 1 条记录
2025-05-20 16:31:06,583 - INFO - 查询完成，共获取到 601 条记录
2025-05-20 16:31:06,583 - INFO - 获取到 601 条表单数据
2025-05-20 16:31:06,594 - INFO - 当前日期 2025-05-19 有 219 条MySQL数据需要处理
2025-05-20 16:31:06,598 - INFO - 开始更新记录 - 表单实例ID: FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM8L
2025-05-20 16:31:07,141 - INFO - 更新表单数据成功: FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM8L
2025-05-20 16:31:07,141 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3316.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 3316.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 42}]
2025-05-20 16:31:07,142 - INFO - 开始更新记录 - 表单实例ID: FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM9L
2025-05-20 16:31:07,589 - INFO - 更新表单数据成功: FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAM9L
2025-05-20 16:31:07,589 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2000.0, 'new_value': 22552.0}, {'field': 'total_amount', 'old_value': 2000.0, 'new_value': 22552.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-20 16:31:07,589 - INFO - 开始更新记录 - 表单实例ID: FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMAL
2025-05-20 16:31:08,083 - INFO - 更新表单数据成功: FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMAL
2025-05-20 16:31:08,084 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3000.0, 'new_value': 6180.0}, {'field': 'total_amount', 'old_value': 3000.0, 'new_value': 6180.0}]
2025-05-20 16:31:08,084 - INFO - 开始更新记录 - 表单实例ID: FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMBL
2025-05-20 16:31:08,619 - INFO - 更新表单数据成功: FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMBL
2025-05-20 16:31:08,619 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 900.0, 'new_value': 1178.0}, {'field': 'total_amount', 'old_value': 900.0, 'new_value': 1178.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 108}]
2025-05-20 16:31:08,619 - INFO - 开始更新记录 - 表单实例ID: FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMCL
2025-05-20 16:31:09,030 - INFO - 更新表单数据成功: FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMCL
2025-05-20 16:31:09,030 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20000.0, 'new_value': 5293.54}, {'field': 'total_amount', 'old_value': 20000.0, 'new_value': 5293.54}, {'field': 'order_count', 'old_value': 1, 'new_value': 22}]
2025-05-20 16:31:09,030 - INFO - 开始更新记录 - 表单实例ID: FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMDL
2025-05-20 16:31:09,459 - INFO - 更新表单数据成功: FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMDL
2025-05-20 16:31:09,459 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1000.0, 'new_value': 6698.0}, {'field': 'total_amount', 'old_value': 1000.0, 'new_value': 6698.0}]
2025-05-20 16:31:09,459 - INFO - 开始更新记录 - 表单实例ID: FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMEL
2025-05-20 16:31:09,886 - INFO - 更新表单数据成功: FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMEL
2025-05-20 16:31:09,886 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35000.0, 'new_value': 49000.0}, {'field': 'total_amount', 'old_value': 35000.0, 'new_value': 49000.0}]
2025-05-20 16:31:09,886 - INFO - 开始更新记录 - 表单实例ID: FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMFL
2025-05-20 16:31:10,405 - INFO - 更新表单数据成功: FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMFL
2025-05-20 16:31:10,406 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4500.0, 'new_value': 8538.68}, {'field': 'total_amount', 'old_value': 4500.0, 'new_value': 8538.68}, {'field': 'order_count', 'old_value': 1, 'new_value': 86}]
2025-05-20 16:31:10,406 - INFO - 开始更新记录 - 表单实例ID: FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMGL
2025-05-20 16:31:10,956 - INFO - 更新表单数据成功: FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMGL
2025-05-20 16:31:10,957 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2500.0, 'new_value': 1096.0}, {'field': 'total_amount', 'old_value': 2500.0, 'new_value': 1096.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 13}]
2025-05-20 16:31:10,957 - INFO - 开始更新记录 - 表单实例ID: FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMHL
2025-05-20 16:31:11,445 - INFO - 更新表单数据成功: FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMHL
2025-05-20 16:31:11,445 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4000.0, 'new_value': 2237.0}, {'field': 'total_amount', 'old_value': 4000.0, 'new_value': 2237.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 19}]
2025-05-20 16:31:11,445 - INFO - 开始更新记录 - 表单实例ID: FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMIL
2025-05-20 16:31:11,903 - INFO - 更新表单数据成功: FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMIL
2025-05-20 16:31:11,903 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3300.0, 'new_value': 2485.0}, {'field': 'total_amount', 'old_value': 3300.0, 'new_value': 2485.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 114}]
2025-05-20 16:31:11,903 - INFO - 开始更新记录 - 表单实例ID: FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMJL
2025-05-20 16:31:12,405 - INFO - 更新表单数据成功: FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMJL
2025-05-20 16:31:12,405 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2200.0, 'new_value': 2179.85}, {'field': 'total_amount', 'old_value': 2200.0, 'new_value': 2179.85}, {'field': 'order_count', 'old_value': 1, 'new_value': 26}]
2025-05-20 16:31:12,406 - INFO - 开始更新记录 - 表单实例ID: FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMKL
2025-05-20 16:31:12,856 - INFO - 更新表单数据成功: FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMKL
2025-05-20 16:31:12,856 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1200.0, 'new_value': 1374.0}, {'field': 'total_amount', 'old_value': 1200.0, 'new_value': 1374.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 126}]
2025-05-20 16:31:12,856 - INFO - 开始更新记录 - 表单实例ID: FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMLL
2025-05-20 16:31:13,434 - INFO - 更新表单数据成功: FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMLL
2025-05-20 16:31:13,434 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 200000.0, 'new_value': 217068.0}, {'field': 'total_amount', 'old_value': 200000.0, 'new_value': 217068.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 3778}]
2025-05-20 16:31:13,434 - INFO - 开始更新记录 - 表单实例ID: FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMNL
2025-05-20 16:31:13,881 - INFO - 更新表单数据成功: FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMNL
2025-05-20 16:31:13,881 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3500.0, 'new_value': 6495.28}, {'field': 'total_amount', 'old_value': 3500.0, 'new_value': 6495.28}, {'field': 'order_count', 'old_value': 1, 'new_value': 300}]
2025-05-20 16:31:13,881 - INFO - 开始更新记录 - 表单实例ID: FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMOL
2025-05-20 16:31:14,429 - INFO - 更新表单数据成功: FINST-BCC66FB1FIJVJLV59U0OK4B25FJ23L0NDWVAMOL
2025-05-20 16:31:14,429 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 297.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 297.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-20 16:31:14,430 - INFO - 开始批量插入 2 条新记录
2025-05-20 16:31:14,590 - INFO - 批量插入响应状态码: 200
2025-05-20 16:31:14,590 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 20 May 2025 08:31:14 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2D27C8A0-B743-7F91-8622-051A7E200EC4', 'x-acs-trace-id': 'f6490f764d9a7248bf0c14aa97bcb51a', 'etag': '1NrEFwrBUGi/vuBgegpkDvw8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-20 16:31:14,590 - INFO - 批量插入响应体: {'result': ['FINST-COC668A1TDJVKB7GAT86CBLKR45D2BAQ89WAMYF', 'FINST-COC668A1TDJVKB7GAT86CBLKR45D2BAQ89WAMZF']}
2025-05-20 16:31:14,590 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-05-20 16:31:14,590 - INFO - 成功插入的数据ID: ['FINST-COC668A1TDJVKB7GAT86CBLKR45D2BAQ89WAMYF', 'FINST-COC668A1TDJVKB7GAT86CBLKR45D2BAQ89WAMZF']
2025-05-20 16:31:19,591 - INFO - 批量插入完成，共 2 条记录
2025-05-20 16:31:19,591 - INFO - 日期 2025-05-19 处理完成 - 更新: 16 条，插入: 2 条，错误: 0 条
2025-05-20 16:31:19,591 - INFO - 开始处理日期: 2025-05-20
2025-05-20 16:31:19,591 - INFO - Request Parameters - Page 1:
2025-05-20 16:31:19,591 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 16:31:19,591 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 16:31:20,152 - INFO - Response - Page 1:
2025-05-20 16:31:20,153 - INFO - 第 1 页获取到 2 条记录
2025-05-20 16:31:20,354 - INFO - 查询完成，共获取到 2 条记录
2025-05-20 16:31:20,354 - INFO - 获取到 2 条表单数据
2025-05-20 16:31:20,354 - INFO - 当前日期 2025-05-20 有 3 条MySQL数据需要处理
2025-05-20 16:31:20,354 - INFO - 开始批量插入 1 条新记录
2025-05-20 16:31:20,522 - INFO - 批量插入响应状态码: 200
2025-05-20 16:31:20,522 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 20 May 2025 08:31:20 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'DFA8C1E2-15FC-7431-BD56-A622AB50136C', 'x-acs-trace-id': '0fa384afac7f5c96b8f0e1652a7909ac', 'etag': '6U59/SmZ+t4qjyE0TnGGI2Q0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-20 16:31:20,522 - INFO - 批量插入响应体: {'result': ['FINST-XL666BD18EKV9NL195WISA1ORHTC32VU89WAM98']}
2025-05-20 16:31:20,522 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-20 16:31:20,522 - INFO - 成功插入的数据ID: ['FINST-XL666BD18EKV9NL195WISA1ORHTC32VU89WAM98']
2025-05-20 16:31:25,523 - INFO - 批量插入完成，共 1 条记录
2025-05-20 16:31:25,523 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-05-20 16:31:25,523 - INFO - 数据同步完成！更新: 16 条，插入: 3 条，错误: 2 条
2025-05-20 16:32:25,524 - INFO - 开始同步昨天与今天的销售数据: 2025-05-19 至 2025-05-20
2025-05-20 16:32:25,524 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 16:32:25,524 - INFO - 查询参数: ('2025-05-19', '2025-05-20')
2025-05-20 16:32:25,609 - INFO - MySQL查询成功，时间段: 2025-05-19 至 2025-05-20，共获取 606 条记录
2025-05-20 16:32:25,610 - INFO - 获取到 2 个日期需要处理: ['2025-05-19', '2025-05-20']
2025-05-20 16:32:25,615 - INFO - 开始处理日期: 2025-05-19
2025-05-20 16:32:25,615 - INFO - Request Parameters - Page 1:
2025-05-20 16:32:25,615 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 16:32:25,615 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 16:32:26,839 - INFO - Response - Page 1:
2025-05-20 16:32:26,839 - INFO - 第 1 页获取到 100 条记录
2025-05-20 16:32:27,040 - INFO - Request Parameters - Page 2:
2025-05-20 16:32:27,040 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 16:32:27,040 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 16:32:27,845 - INFO - Response - Page 2:
2025-05-20 16:32:27,846 - INFO - 第 2 页获取到 100 条记录
2025-05-20 16:32:28,046 - INFO - Request Parameters - Page 3:
2025-05-20 16:32:28,046 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 16:32:28,046 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 16:32:29,058 - INFO - Response - Page 3:
2025-05-20 16:32:29,058 - INFO - 第 3 页获取到 100 条记录
2025-05-20 16:32:29,258 - INFO - Request Parameters - Page 4:
2025-05-20 16:32:29,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 16:32:29,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 16:32:30,123 - INFO - Response - Page 4:
2025-05-20 16:32:30,123 - INFO - 第 4 页获取到 100 条记录
2025-05-20 16:32:30,323 - INFO - Request Parameters - Page 5:
2025-05-20 16:32:30,323 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 16:32:30,323 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 16:32:31,125 - INFO - Response - Page 5:
2025-05-20 16:32:31,125 - INFO - 第 5 页获取到 100 条记录
2025-05-20 16:32:31,325 - INFO - Request Parameters - Page 6:
2025-05-20 16:32:31,325 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 16:32:31,325 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 16:32:32,131 - INFO - Response - Page 6:
2025-05-20 16:32:32,131 - INFO - 第 6 页获取到 100 条记录
2025-05-20 16:32:32,332 - INFO - Request Parameters - Page 7:
2025-05-20 16:32:32,332 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 16:32:32,332 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 16:32:32,827 - INFO - Response - Page 7:
2025-05-20 16:32:32,827 - INFO - 第 7 页获取到 3 条记录
2025-05-20 16:32:33,027 - INFO - 查询完成，共获取到 603 条记录
2025-05-20 16:32:33,027 - INFO - 获取到 603 条表单数据
2025-05-20 16:32:33,037 - INFO - 当前日期 2025-05-19 有 603 条MySQL数据需要处理
2025-05-20 16:32:33,050 - INFO - 日期 2025-05-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 16:32:33,050 - INFO - 开始处理日期: 2025-05-20
2025-05-20 16:32:33,050 - INFO - Request Parameters - Page 1:
2025-05-20 16:32:33,050 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 16:32:33,051 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 16:32:33,578 - INFO - Response - Page 1:
2025-05-20 16:32:33,578 - INFO - 第 1 页获取到 3 条记录
2025-05-20 16:32:33,778 - INFO - 查询完成，共获取到 3 条记录
2025-05-20 16:32:33,778 - INFO - 获取到 3 条表单数据
2025-05-20 16:32:33,779 - INFO - 当前日期 2025-05-20 有 3 条MySQL数据需要处理
2025-05-20 16:32:33,779 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 16:32:33,779 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 16:32:33,780 - INFO - 同步完成
2025-05-20 17:30:34,185 - INFO - 使用默认增量同步（当天更新数据）
2025-05-20 17:30:34,185 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 17:30:34,185 - INFO - 查询参数: ('2025-05-20',)
2025-05-20 17:30:34,257 - INFO - MySQL查询成功，增量数据（日期: 2025-05-20），共获取 226 条记录
2025-05-20 17:30:34,257 - INFO - 获取到 5 个日期需要处理: ['2025-05-16', '2025-05-17', '2025-05-18', '2025-05-19', '2025-05-20']
2025-05-20 17:30:34,259 - INFO - 开始处理日期: 2025-05-16
2025-05-20 17:30:34,262 - INFO - Request Parameters - Page 1:
2025-05-20 17:30:34,262 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 17:30:34,262 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 17:30:42,393 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3D92BECD-9C43-7FE5-ADCB-DF0F3FA0D757 Response: {'code': 'ServiceUnavailable', 'requestid': '3D92BECD-9C43-7FE5-ADCB-DF0F3FA0D757', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3D92BECD-9C43-7FE5-ADCB-DF0F3FA0D757)
2025-05-20 17:30:42,393 - INFO - 开始处理日期: 2025-05-17
2025-05-20 17:30:42,393 - INFO - Request Parameters - Page 1:
2025-05-20 17:30:42,393 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 17:30:42,394 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 17:30:50,516 - ERROR - 处理日期 2025-05-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 4AAE4FA1-09F3-7A30-B968-B196CEA5A380 Response: {'code': 'ServiceUnavailable', 'requestid': '4AAE4FA1-09F3-7A30-B968-B196CEA5A380', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 4AAE4FA1-09F3-7A30-B968-B196CEA5A380)
2025-05-20 17:30:50,516 - INFO - 开始处理日期: 2025-05-18
2025-05-20 17:30:50,516 - INFO - Request Parameters - Page 1:
2025-05-20 17:30:50,516 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 17:30:50,516 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 17:30:58,644 - ERROR - 处理日期 2025-05-18 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: ********-6138-7D46-814D-AFF965105BD3 Response: {'code': 'ServiceUnavailable', 'requestid': '********-6138-7D46-814D-AFF965105BD3', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: ********-6138-7D46-814D-AFF965105BD3)
2025-05-20 17:30:58,644 - INFO - 开始处理日期: 2025-05-19
2025-05-20 17:30:58,645 - INFO - Request Parameters - Page 1:
2025-05-20 17:30:58,645 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 17:30:58,645 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 17:30:59,492 - INFO - Response - Page 1:
2025-05-20 17:30:59,493 - INFO - 第 1 页获取到 100 条记录
2025-05-20 17:30:59,693 - INFO - Request Parameters - Page 2:
2025-05-20 17:30:59,693 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 17:30:59,693 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 17:31:00,559 - INFO - Response - Page 2:
2025-05-20 17:31:00,559 - INFO - 第 2 页获取到 100 条记录
2025-05-20 17:31:00,759 - INFO - Request Parameters - Page 3:
2025-05-20 17:31:00,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 17:31:00,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 17:31:01,684 - INFO - Response - Page 3:
2025-05-20 17:31:01,685 - INFO - 第 3 页获取到 100 条记录
2025-05-20 17:31:01,885 - INFO - Request Parameters - Page 4:
2025-05-20 17:31:01,885 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 17:31:01,885 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 17:31:02,815 - INFO - Response - Page 4:
2025-05-20 17:31:02,816 - INFO - 第 4 页获取到 100 条记录
2025-05-20 17:31:03,016 - INFO - Request Parameters - Page 5:
2025-05-20 17:31:03,016 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 17:31:03,016 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 17:31:03,837 - INFO - Response - Page 5:
2025-05-20 17:31:03,837 - INFO - 第 5 页获取到 100 条记录
2025-05-20 17:31:04,037 - INFO - Request Parameters - Page 6:
2025-05-20 17:31:04,037 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 17:31:04,037 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 17:31:04,901 - INFO - Response - Page 6:
2025-05-20 17:31:04,901 - INFO - 第 6 页获取到 100 条记录
2025-05-20 17:31:05,101 - INFO - Request Parameters - Page 7:
2025-05-20 17:31:05,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 17:31:05,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 17:31:05,730 - INFO - Response - Page 7:
2025-05-20 17:31:05,730 - INFO - 第 7 页获取到 3 条记录
2025-05-20 17:31:05,930 - INFO - 查询完成，共获取到 603 条记录
2025-05-20 17:31:05,930 - INFO - 获取到 603 条表单数据
2025-05-20 17:31:05,940 - INFO - 当前日期 2025-05-19 有 219 条MySQL数据需要处理
2025-05-20 17:31:05,942 - INFO - 开始更新记录 - 表单实例ID: FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMTD
2025-05-20 17:31:06,450 - INFO - 更新表单数据成功: FINST-07E66I912IJV5PVT9I0IZ7FR3W9O28V98UVAMTD
2025-05-20 17:31:06,450 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4409.0, 'new_value': 3472.0}, {'field': 'total_amount', 'old_value': 4409.0, 'new_value': 3472.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 34}]
2025-05-20 17:31:06,452 - INFO - 日期 2025-05-19 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-20 17:31:06,452 - INFO - 开始处理日期: 2025-05-20
2025-05-20 17:31:06,452 - INFO - Request Parameters - Page 1:
2025-05-20 17:31:06,452 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 17:31:06,452 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 17:31:06,998 - INFO - Response - Page 1:
2025-05-20 17:31:06,998 - INFO - 第 1 页获取到 3 条记录
2025-05-20 17:31:07,199 - INFO - 查询完成，共获取到 3 条记录
2025-05-20 17:31:07,199 - INFO - 获取到 3 条表单数据
2025-05-20 17:31:07,200 - INFO - 当前日期 2025-05-20 有 3 条MySQL数据需要处理
2025-05-20 17:31:07,200 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 17:31:07,200 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 3 条
2025-05-20 17:32:07,200 - INFO - 开始同步昨天与今天的销售数据: 2025-05-19 至 2025-05-20
2025-05-20 17:32:07,200 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 17:32:07,200 - INFO - 查询参数: ('2025-05-19', '2025-05-20')
2025-05-20 17:32:07,304 - INFO - MySQL查询成功，时间段: 2025-05-19 至 2025-05-20，共获取 606 条记录
2025-05-20 17:32:07,304 - INFO - 获取到 2 个日期需要处理: ['2025-05-19', '2025-05-20']
2025-05-20 17:32:07,309 - INFO - 开始处理日期: 2025-05-19
2025-05-20 17:32:07,309 - INFO - Request Parameters - Page 1:
2025-05-20 17:32:07,309 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 17:32:07,309 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 17:32:08,152 - INFO - Response - Page 1:
2025-05-20 17:32:08,153 - INFO - 第 1 页获取到 100 条记录
2025-05-20 17:32:08,353 - INFO - Request Parameters - Page 2:
2025-05-20 17:32:08,353 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 17:32:08,353 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 17:32:09,152 - INFO - Response - Page 2:
2025-05-20 17:32:09,152 - INFO - 第 2 页获取到 100 条记录
2025-05-20 17:32:09,352 - INFO - Request Parameters - Page 3:
2025-05-20 17:32:09,352 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 17:32:09,352 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 17:32:10,208 - INFO - Response - Page 3:
2025-05-20 17:32:10,208 - INFO - 第 3 页获取到 100 条记录
2025-05-20 17:32:10,408 - INFO - Request Parameters - Page 4:
2025-05-20 17:32:10,408 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 17:32:10,408 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 17:32:11,183 - INFO - Response - Page 4:
2025-05-20 17:32:11,183 - INFO - 第 4 页获取到 100 条记录
2025-05-20 17:32:11,385 - INFO - Request Parameters - Page 5:
2025-05-20 17:32:11,385 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 17:32:11,385 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 17:32:12,234 - INFO - Response - Page 5:
2025-05-20 17:32:12,234 - INFO - 第 5 页获取到 100 条记录
2025-05-20 17:32:12,434 - INFO - Request Parameters - Page 6:
2025-05-20 17:32:12,434 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 17:32:12,434 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 17:32:13,231 - INFO - Response - Page 6:
2025-05-20 17:32:13,231 - INFO - 第 6 页获取到 100 条记录
2025-05-20 17:32:13,431 - INFO - Request Parameters - Page 7:
2025-05-20 17:32:13,431 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 17:32:13,431 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 17:32:13,921 - INFO - Response - Page 7:
2025-05-20 17:32:13,922 - INFO - 第 7 页获取到 3 条记录
2025-05-20 17:32:14,122 - INFO - 查询完成，共获取到 603 条记录
2025-05-20 17:32:14,122 - INFO - 获取到 603 条表单数据
2025-05-20 17:32:14,132 - INFO - 当前日期 2025-05-19 有 603 条MySQL数据需要处理
2025-05-20 17:32:14,147 - INFO - 日期 2025-05-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 17:32:14,147 - INFO - 开始处理日期: 2025-05-20
2025-05-20 17:32:14,147 - INFO - Request Parameters - Page 1:
2025-05-20 17:32:14,147 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 17:32:14,147 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 17:32:14,672 - INFO - Response - Page 1:
2025-05-20 17:32:14,672 - INFO - 第 1 页获取到 3 条记录
2025-05-20 17:32:14,872 - INFO - 查询完成，共获取到 3 条记录
2025-05-20 17:32:14,872 - INFO - 获取到 3 条表单数据
2025-05-20 17:32:14,873 - INFO - 当前日期 2025-05-20 有 3 条MySQL数据需要处理
2025-05-20 17:32:14,873 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 17:32:14,873 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 17:32:14,874 - INFO - 同步完成
2025-05-20 18:30:34,280 - INFO - 使用默认增量同步（当天更新数据）
2025-05-20 18:30:34,280 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 18:30:34,281 - INFO - 查询参数: ('2025-05-20',)
2025-05-20 18:30:34,353 - INFO - MySQL查询成功，增量数据（日期: 2025-05-20），共获取 226 条记录
2025-05-20 18:30:34,353 - INFO - 获取到 5 个日期需要处理: ['2025-05-16', '2025-05-17', '2025-05-18', '2025-05-19', '2025-05-20']
2025-05-20 18:30:34,355 - INFO - 开始处理日期: 2025-05-16
2025-05-20 18:30:34,358 - INFO - Request Parameters - Page 1:
2025-05-20 18:30:34,358 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:30:34,358 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:30:42,490 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: ********-8762-742A-B8E0-48A3A82335EA Response: {'code': 'ServiceUnavailable', 'requestid': '********-8762-742A-B8E0-48A3A82335EA', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: ********-8762-742A-B8E0-48A3A82335EA)
2025-05-20 18:30:42,491 - INFO - 开始处理日期: 2025-05-17
2025-05-20 18:30:42,491 - INFO - Request Parameters - Page 1:
2025-05-20 18:30:42,491 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:30:42,491 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:30:44,881 - INFO - Response - Page 1:
2025-05-20 18:30:44,882 - INFO - 第 1 页获取到 100 条记录
2025-05-20 18:30:45,082 - INFO - Request Parameters - Page 2:
2025-05-20 18:30:45,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:30:45,082 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:30:45,965 - INFO - Response - Page 2:
2025-05-20 18:30:45,965 - INFO - 第 2 页获取到 100 条记录
2025-05-20 18:30:46,167 - INFO - Request Parameters - Page 3:
2025-05-20 18:30:46,167 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:30:46,167 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:30:54,287 - ERROR - 处理日期 2025-05-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 9D65E72E-0882-7BBE-A86E-A3A94A7F4AED Response: {'code': 'ServiceUnavailable', 'requestid': '9D65E72E-0882-7BBE-A86E-A3A94A7F4AED', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 9D65E72E-0882-7BBE-A86E-A3A94A7F4AED)
2025-05-20 18:30:54,288 - INFO - 开始处理日期: 2025-05-18
2025-05-20 18:30:54,288 - INFO - Request Parameters - Page 1:
2025-05-20 18:30:54,288 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:30:54,288 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:31:02,417 - ERROR - 处理日期 2025-05-18 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: AE52B30D-2F72-7F59-917B-F7A8368A6628 Response: {'code': 'ServiceUnavailable', 'requestid': 'AE52B30D-2F72-7F59-917B-F7A8368A6628', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: AE52B30D-2F72-7F59-917B-F7A8368A6628)
2025-05-20 18:31:02,418 - INFO - 开始处理日期: 2025-05-19
2025-05-20 18:31:02,418 - INFO - Request Parameters - Page 1:
2025-05-20 18:31:02,418 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:31:02,418 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:31:03,282 - INFO - Response - Page 1:
2025-05-20 18:31:03,282 - INFO - 第 1 页获取到 100 条记录
2025-05-20 18:31:03,482 - INFO - Request Parameters - Page 2:
2025-05-20 18:31:03,482 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:31:03,482 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:31:04,322 - INFO - Response - Page 2:
2025-05-20 18:31:04,322 - INFO - 第 2 页获取到 100 条记录
2025-05-20 18:31:04,522 - INFO - Request Parameters - Page 3:
2025-05-20 18:31:04,522 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:31:04,522 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:31:05,430 - INFO - Response - Page 3:
2025-05-20 18:31:05,430 - INFO - 第 3 页获取到 100 条记录
2025-05-20 18:31:05,630 - INFO - Request Parameters - Page 4:
2025-05-20 18:31:05,630 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:31:05,630 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:31:06,430 - INFO - Response - Page 4:
2025-05-20 18:31:06,430 - INFO - 第 4 页获取到 100 条记录
2025-05-20 18:31:06,630 - INFO - Request Parameters - Page 5:
2025-05-20 18:31:06,630 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:31:06,630 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:31:07,539 - INFO - Response - Page 5:
2025-05-20 18:31:07,540 - INFO - 第 5 页获取到 100 条记录
2025-05-20 18:31:07,740 - INFO - Request Parameters - Page 6:
2025-05-20 18:31:07,740 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:31:07,740 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:31:08,629 - INFO - Response - Page 6:
2025-05-20 18:31:08,630 - INFO - 第 6 页获取到 100 条记录
2025-05-20 18:31:08,831 - INFO - Request Parameters - Page 7:
2025-05-20 18:31:08,831 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:31:08,831 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:31:09,362 - INFO - Response - Page 7:
2025-05-20 18:31:09,362 - INFO - 第 7 页获取到 3 条记录
2025-05-20 18:31:09,562 - INFO - 查询完成，共获取到 603 条记录
2025-05-20 18:31:09,562 - INFO - 获取到 603 条表单数据
2025-05-20 18:31:09,572 - INFO - 当前日期 2025-05-19 有 219 条MySQL数据需要处理
2025-05-20 18:31:09,576 - INFO - 日期 2025-05-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 18:31:09,576 - INFO - 开始处理日期: 2025-05-20
2025-05-20 18:31:09,576 - INFO - Request Parameters - Page 1:
2025-05-20 18:31:09,577 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:31:09,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:31:10,108 - INFO - Response - Page 1:
2025-05-20 18:31:10,108 - INFO - 第 1 页获取到 3 条记录
2025-05-20 18:31:10,308 - INFO - 查询完成，共获取到 3 条记录
2025-05-20 18:31:10,308 - INFO - 获取到 3 条表单数据
2025-05-20 18:31:10,309 - INFO - 当前日期 2025-05-20 有 3 条MySQL数据需要处理
2025-05-20 18:31:10,309 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 18:31:10,309 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 3 条
2025-05-20 18:32:10,310 - INFO - 开始同步昨天与今天的销售数据: 2025-05-19 至 2025-05-20
2025-05-20 18:32:10,310 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 18:32:10,310 - INFO - 查询参数: ('2025-05-19', '2025-05-20')
2025-05-20 18:32:10,393 - INFO - MySQL查询成功，时间段: 2025-05-19 至 2025-05-20，共获取 606 条记录
2025-05-20 18:32:10,393 - INFO - 获取到 2 个日期需要处理: ['2025-05-19', '2025-05-20']
2025-05-20 18:32:10,398 - INFO - 开始处理日期: 2025-05-19
2025-05-20 18:32:10,398 - INFO - Request Parameters - Page 1:
2025-05-20 18:32:10,398 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:32:10,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:32:11,259 - INFO - Response - Page 1:
2025-05-20 18:32:11,259 - INFO - 第 1 页获取到 100 条记录
2025-05-20 18:32:11,460 - INFO - Request Parameters - Page 2:
2025-05-20 18:32:11,460 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:32:11,460 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:32:12,328 - INFO - Response - Page 2:
2025-05-20 18:32:12,328 - INFO - 第 2 页获取到 100 条记录
2025-05-20 18:32:12,528 - INFO - Request Parameters - Page 3:
2025-05-20 18:32:12,528 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:32:12,528 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:32:13,391 - INFO - Response - Page 3:
2025-05-20 18:32:13,391 - INFO - 第 3 页获取到 100 条记录
2025-05-20 18:32:13,591 - INFO - Request Parameters - Page 4:
2025-05-20 18:32:13,591 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:32:13,591 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:32:14,450 - INFO - Response - Page 4:
2025-05-20 18:32:14,451 - INFO - 第 4 页获取到 100 条记录
2025-05-20 18:32:14,651 - INFO - Request Parameters - Page 5:
2025-05-20 18:32:14,651 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:32:14,651 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:32:15,471 - INFO - Response - Page 5:
2025-05-20 18:32:15,471 - INFO - 第 5 页获取到 100 条记录
2025-05-20 18:32:15,672 - INFO - Request Parameters - Page 6:
2025-05-20 18:32:15,672 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:32:15,672 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:32:16,555 - INFO - Response - Page 6:
2025-05-20 18:32:16,555 - INFO - 第 6 页获取到 100 条记录
2025-05-20 18:32:16,755 - INFO - Request Parameters - Page 7:
2025-05-20 18:32:16,755 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:32:16,755 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:32:17,248 - INFO - Response - Page 7:
2025-05-20 18:32:17,249 - INFO - 第 7 页获取到 3 条记录
2025-05-20 18:32:17,449 - INFO - 查询完成，共获取到 603 条记录
2025-05-20 18:32:17,449 - INFO - 获取到 603 条表单数据
2025-05-20 18:32:17,459 - INFO - 当前日期 2025-05-19 有 603 条MySQL数据需要处理
2025-05-20 18:32:17,470 - INFO - 日期 2025-05-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 18:32:17,470 - INFO - 开始处理日期: 2025-05-20
2025-05-20 18:32:17,470 - INFO - Request Parameters - Page 1:
2025-05-20 18:32:17,471 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 18:32:17,471 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 18:32:17,992 - INFO - Response - Page 1:
2025-05-20 18:32:17,992 - INFO - 第 1 页获取到 3 条记录
2025-05-20 18:32:18,192 - INFO - 查询完成，共获取到 3 条记录
2025-05-20 18:32:18,192 - INFO - 获取到 3 条表单数据
2025-05-20 18:32:18,193 - INFO - 当前日期 2025-05-20 有 3 条MySQL数据需要处理
2025-05-20 18:32:18,193 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 18:32:18,193 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 18:32:18,193 - INFO - 同步完成
2025-05-20 19:30:34,769 - INFO - 使用默认增量同步（当天更新数据）
2025-05-20 19:30:34,769 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 19:30:34,769 - INFO - 查询参数: ('2025-05-20',)
2025-05-20 19:30:34,842 - INFO - MySQL查询成功，增量数据（日期: 2025-05-20），共获取 226 条记录
2025-05-20 19:30:34,842 - INFO - 获取到 5 个日期需要处理: ['2025-05-16', '2025-05-17', '2025-05-18', '2025-05-19', '2025-05-20']
2025-05-20 19:30:34,844 - INFO - 开始处理日期: 2025-05-16
2025-05-20 19:30:34,846 - INFO - Request Parameters - Page 1:
2025-05-20 19:30:34,847 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 19:30:34,847 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 19:30:43,001 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F40433FA-7FF7-7249-98E7-B0EF3AE62A03 Response: {'code': 'ServiceUnavailable', 'requestid': 'F40433FA-7FF7-7249-98E7-B0EF3AE62A03', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F40433FA-7FF7-7249-98E7-B0EF3AE62A03)
2025-05-20 19:30:43,002 - INFO - 开始处理日期: 2025-05-17
2025-05-20 19:30:43,002 - INFO - Request Parameters - Page 1:
2025-05-20 19:30:43,002 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 19:30:43,002 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 19:30:50,479 - INFO - Response - Page 1:
2025-05-20 19:30:50,479 - INFO - 第 1 页获取到 100 条记录
2025-05-20 19:30:50,680 - INFO - Request Parameters - Page 2:
2025-05-20 19:30:50,680 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 19:30:50,680 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 19:30:51,533 - INFO - Response - Page 2:
2025-05-20 19:30:51,533 - INFO - 第 2 页获取到 100 条记录
2025-05-20 19:30:51,734 - INFO - Request Parameters - Page 3:
2025-05-20 19:30:51,734 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 19:30:51,734 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 19:30:52,582 - INFO - Response - Page 3:
2025-05-20 19:30:52,582 - INFO - 第 3 页获取到 100 条记录
2025-05-20 19:30:52,782 - INFO - Request Parameters - Page 4:
2025-05-20 19:30:52,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 19:30:52,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 19:31:00,889 - ERROR - 处理日期 2025-05-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: FC29447B-6A77-7929-AF68-F61E3B47EAFE Response: {'code': 'ServiceUnavailable', 'requestid': 'FC29447B-6A77-7929-AF68-F61E3B47EAFE', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: FC29447B-6A77-7929-AF68-F61E3B47EAFE)
2025-05-20 19:31:00,889 - INFO - 开始处理日期: 2025-05-18
2025-05-20 19:31:00,890 - INFO - Request Parameters - Page 1:
2025-05-20 19:31:00,890 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 19:31:00,890 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 19:31:03,087 - INFO - Response - Page 1:
2025-05-20 19:31:03,087 - INFO - 第 1 页获取到 100 条记录
2025-05-20 19:31:03,287 - INFO - Request Parameters - Page 2:
2025-05-20 19:31:03,287 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 19:31:03,287 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 19:31:04,099 - INFO - Response - Page 2:
2025-05-20 19:31:04,099 - INFO - 第 2 页获取到 100 条记录
2025-05-20 19:31:04,299 - INFO - Request Parameters - Page 3:
2025-05-20 19:31:04,299 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 19:31:04,299 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 19:31:05,096 - INFO - Response - Page 3:
2025-05-20 19:31:05,096 - INFO - 第 3 页获取到 100 条记录
2025-05-20 19:31:05,297 - INFO - Request Parameters - Page 4:
2025-05-20 19:31:05,297 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 19:31:05,297 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 19:31:06,136 - INFO - Response - Page 4:
2025-05-20 19:31:06,136 - INFO - 第 4 页获取到 100 条记录
2025-05-20 19:31:06,338 - INFO - Request Parameters - Page 5:
2025-05-20 19:31:06,338 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 19:31:06,338 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 19:31:07,129 - INFO - Response - Page 5:
2025-05-20 19:31:07,129 - INFO - 第 5 页获取到 100 条记录
2025-05-20 19:31:07,329 - INFO - Request Parameters - Page 6:
2025-05-20 19:31:07,329 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 19:31:07,329 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 19:31:08,145 - INFO - Response - Page 6:
2025-05-20 19:31:08,145 - INFO - 第 6 页获取到 100 条记录
2025-05-20 19:31:08,347 - INFO - Request Parameters - Page 7:
2025-05-20 19:31:08,347 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 19:31:08,347 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 19:31:08,959 - INFO - Response - Page 7:
2025-05-20 19:31:08,959 - INFO - 第 7 页获取到 13 条记录
2025-05-20 19:31:09,159 - INFO - 查询完成，共获取到 613 条记录
2025-05-20 19:31:09,159 - INFO - 获取到 613 条表单数据
2025-05-20 19:31:09,169 - INFO - 当前日期 2025-05-18 有 1 条MySQL数据需要处理
2025-05-20 19:31:09,169 - INFO - 日期 2025-05-18 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 19:31:09,169 - INFO - 开始处理日期: 2025-05-19
2025-05-20 19:31:09,169 - INFO - Request Parameters - Page 1:
2025-05-20 19:31:09,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 19:31:09,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 19:31:09,998 - INFO - Response - Page 1:
2025-05-20 19:31:09,998 - INFO - 第 1 页获取到 100 条记录
2025-05-20 19:31:10,198 - INFO - Request Parameters - Page 2:
2025-05-20 19:31:10,198 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 19:31:10,198 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 19:31:11,021 - INFO - Response - Page 2:
2025-05-20 19:31:11,021 - INFO - 第 2 页获取到 100 条记录
2025-05-20 19:31:11,221 - INFO - Request Parameters - Page 3:
2025-05-20 19:31:11,221 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 19:31:11,221 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 19:31:12,008 - INFO - Response - Page 3:
2025-05-20 19:31:12,008 - INFO - 第 3 页获取到 100 条记录
2025-05-20 19:31:12,209 - INFO - Request Parameters - Page 4:
2025-05-20 19:31:12,209 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 19:31:12,209 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 19:31:13,025 - INFO - Response - Page 4:
2025-05-20 19:31:13,026 - INFO - 第 4 页获取到 100 条记录
2025-05-20 19:31:13,226 - INFO - Request Parameters - Page 5:
2025-05-20 19:31:13,226 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 19:31:13,226 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 19:31:13,996 - INFO - Response - Page 5:
2025-05-20 19:31:13,996 - INFO - 第 5 页获取到 100 条记录
2025-05-20 19:31:14,196 - INFO - Request Parameters - Page 6:
2025-05-20 19:31:14,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 19:31:14,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 19:31:15,044 - INFO - Response - Page 6:
2025-05-20 19:31:15,044 - INFO - 第 6 页获取到 100 条记录
2025-05-20 19:31:15,245 - INFO - Request Parameters - Page 7:
2025-05-20 19:31:15,245 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 19:31:15,245 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 19:31:15,763 - INFO - Response - Page 7:
2025-05-20 19:31:15,763 - INFO - 第 7 页获取到 3 条记录
2025-05-20 19:31:15,965 - INFO - 查询完成，共获取到 603 条记录
2025-05-20 19:31:15,965 - INFO - 获取到 603 条表单数据
2025-05-20 19:31:15,974 - INFO - 当前日期 2025-05-19 有 219 条MySQL数据需要处理
2025-05-20 19:31:15,978 - INFO - 日期 2025-05-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 19:31:15,978 - INFO - 开始处理日期: 2025-05-20
2025-05-20 19:31:15,978 - INFO - Request Parameters - Page 1:
2025-05-20 19:31:15,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 19:31:15,979 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 19:31:16,493 - INFO - Response - Page 1:
2025-05-20 19:31:16,493 - INFO - 第 1 页获取到 3 条记录
2025-05-20 19:31:16,693 - INFO - 查询完成，共获取到 3 条记录
2025-05-20 19:31:16,693 - INFO - 获取到 3 条表单数据
2025-05-20 19:31:16,694 - INFO - 当前日期 2025-05-20 有 3 条MySQL数据需要处理
2025-05-20 19:31:16,694 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 19:31:16,694 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-20 19:32:16,701 - INFO - 开始同步昨天与今天的销售数据: 2025-05-19 至 2025-05-20
2025-05-20 19:32:16,701 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 19:32:16,701 - INFO - 查询参数: ('2025-05-19', '2025-05-20')
2025-05-20 19:32:16,782 - INFO - MySQL查询成功，时间段: 2025-05-19 至 2025-05-20，共获取 606 条记录
2025-05-20 19:32:16,783 - INFO - 获取到 2 个日期需要处理: ['2025-05-19', '2025-05-20']
2025-05-20 19:32:16,788 - INFO - 开始处理日期: 2025-05-19
2025-05-20 19:32:16,788 - INFO - Request Parameters - Page 1:
2025-05-20 19:32:16,788 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 19:32:16,788 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 19:32:17,618 - INFO - Response - Page 1:
2025-05-20 19:32:17,618 - INFO - 第 1 页获取到 100 条记录
2025-05-20 19:32:17,819 - INFO - Request Parameters - Page 2:
2025-05-20 19:32:17,819 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 19:32:17,819 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 19:32:18,728 - INFO - Response - Page 2:
2025-05-20 19:32:18,728 - INFO - 第 2 页获取到 100 条记录
2025-05-20 19:32:18,928 - INFO - Request Parameters - Page 3:
2025-05-20 19:32:18,928 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 19:32:18,928 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 19:32:19,750 - INFO - Response - Page 3:
2025-05-20 19:32:19,751 - INFO - 第 3 页获取到 100 条记录
2025-05-20 19:32:19,952 - INFO - Request Parameters - Page 4:
2025-05-20 19:32:19,952 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 19:32:19,952 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 19:32:20,735 - INFO - Response - Page 4:
2025-05-20 19:32:20,735 - INFO - 第 4 页获取到 100 条记录
2025-05-20 19:32:20,935 - INFO - Request Parameters - Page 5:
2025-05-20 19:32:20,935 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 19:32:20,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 19:32:21,747 - INFO - Response - Page 5:
2025-05-20 19:32:21,747 - INFO - 第 5 页获取到 100 条记录
2025-05-20 19:32:21,947 - INFO - Request Parameters - Page 6:
2025-05-20 19:32:21,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 19:32:21,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 19:32:22,783 - INFO - Response - Page 6:
2025-05-20 19:32:22,783 - INFO - 第 6 页获取到 100 条记录
2025-05-20 19:32:22,983 - INFO - Request Parameters - Page 7:
2025-05-20 19:32:22,983 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 19:32:22,983 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 19:32:23,496 - INFO - Response - Page 7:
2025-05-20 19:32:23,497 - INFO - 第 7 页获取到 3 条记录
2025-05-20 19:32:23,697 - INFO - 查询完成，共获取到 603 条记录
2025-05-20 19:32:23,697 - INFO - 获取到 603 条表单数据
2025-05-20 19:32:23,714 - INFO - 当前日期 2025-05-19 有 603 条MySQL数据需要处理
2025-05-20 19:32:23,731 - INFO - 日期 2025-05-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 19:32:23,731 - INFO - 开始处理日期: 2025-05-20
2025-05-20 19:32:23,731 - INFO - Request Parameters - Page 1:
2025-05-20 19:32:23,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 19:32:23,732 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 19:32:24,252 - INFO - Response - Page 1:
2025-05-20 19:32:24,252 - INFO - 第 1 页获取到 3 条记录
2025-05-20 19:32:24,452 - INFO - 查询完成，共获取到 3 条记录
2025-05-20 19:32:24,452 - INFO - 获取到 3 条表单数据
2025-05-20 19:32:24,453 - INFO - 当前日期 2025-05-20 有 3 条MySQL数据需要处理
2025-05-20 19:32:24,453 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 19:32:24,453 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 19:32:24,454 - INFO - 同步完成
2025-05-20 20:30:34,575 - INFO - 使用默认增量同步（当天更新数据）
2025-05-20 20:30:34,576 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 20:30:34,576 - INFO - 查询参数: ('2025-05-20',)
2025-05-20 20:30:34,651 - INFO - MySQL查询成功，增量数据（日期: 2025-05-20），共获取 227 条记录
2025-05-20 20:30:34,652 - INFO - 获取到 5 个日期需要处理: ['2025-05-16', '2025-05-17', '2025-05-18', '2025-05-19', '2025-05-20']
2025-05-20 20:30:34,655 - INFO - 开始处理日期: 2025-05-16
2025-05-20 20:30:34,658 - INFO - Request Parameters - Page 1:
2025-05-20 20:30:34,658 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 20:30:34,658 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 20:30:42,778 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C7B36CCC-7A59-721A-B373-FA2E7963AF63 Response: {'code': 'ServiceUnavailable', 'requestid': 'C7B36CCC-7A59-721A-B373-FA2E7963AF63', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C7B36CCC-7A59-721A-B373-FA2E7963AF63)
2025-05-20 20:30:42,778 - INFO - 开始处理日期: 2025-05-17
2025-05-20 20:30:42,778 - INFO - Request Parameters - Page 1:
2025-05-20 20:30:42,779 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 20:30:42,779 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 20:30:50,894 - ERROR - 处理日期 2025-05-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E2643D6C-9ABE-725C-BE28-86D5AD16BB53 Response: {'code': 'ServiceUnavailable', 'requestid': 'E2643D6C-9ABE-725C-BE28-86D5AD16BB53', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E2643D6C-9ABE-725C-BE28-86D5AD16BB53)
2025-05-20 20:30:50,894 - INFO - 开始处理日期: 2025-05-18
2025-05-20 20:30:50,894 - INFO - Request Parameters - Page 1:
2025-05-20 20:30:50,894 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 20:30:50,894 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 20:30:58,453 - INFO - Response - Page 1:
2025-05-20 20:30:58,453 - INFO - 第 1 页获取到 100 条记录
2025-05-20 20:30:58,654 - INFO - Request Parameters - Page 2:
2025-05-20 20:30:58,654 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 20:30:58,654 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 20:30:59,526 - INFO - Response - Page 2:
2025-05-20 20:30:59,527 - INFO - 第 2 页获取到 100 条记录
2025-05-20 20:30:59,727 - INFO - Request Parameters - Page 3:
2025-05-20 20:30:59,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 20:30:59,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 20:31:00,577 - INFO - Response - Page 3:
2025-05-20 20:31:00,578 - INFO - 第 3 页获取到 100 条记录
2025-05-20 20:31:00,778 - INFO - Request Parameters - Page 4:
2025-05-20 20:31:00,778 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 20:31:00,778 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 20:31:01,686 - INFO - Response - Page 4:
2025-05-20 20:31:01,687 - INFO - 第 4 页获取到 100 条记录
2025-05-20 20:31:01,887 - INFO - Request Parameters - Page 5:
2025-05-20 20:31:01,887 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 20:31:01,887 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 20:31:02,737 - INFO - Response - Page 5:
2025-05-20 20:31:02,737 - INFO - 第 5 页获取到 100 条记录
2025-05-20 20:31:02,937 - INFO - Request Parameters - Page 6:
2025-05-20 20:31:02,937 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 20:31:02,937 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 20:31:03,768 - INFO - Response - Page 6:
2025-05-20 20:31:03,768 - INFO - 第 6 页获取到 100 条记录
2025-05-20 20:31:03,968 - INFO - Request Parameters - Page 7:
2025-05-20 20:31:03,968 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 20:31:03,968 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 20:31:04,580 - INFO - Response - Page 7:
2025-05-20 20:31:04,580 - INFO - 第 7 页获取到 13 条记录
2025-05-20 20:31:04,780 - INFO - 查询完成，共获取到 613 条记录
2025-05-20 20:31:04,780 - INFO - 获取到 613 条表单数据
2025-05-20 20:31:04,790 - INFO - 当前日期 2025-05-18 有 1 条MySQL数据需要处理
2025-05-20 20:31:04,790 - INFO - 日期 2025-05-18 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 20:31:04,790 - INFO - 开始处理日期: 2025-05-19
2025-05-20 20:31:04,790 - INFO - Request Parameters - Page 1:
2025-05-20 20:31:04,790 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 20:31:04,790 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 20:31:05,530 - INFO - Response - Page 1:
2025-05-20 20:31:05,530 - INFO - 第 1 页获取到 100 条记录
2025-05-20 20:31:05,731 - INFO - Request Parameters - Page 2:
2025-05-20 20:31:05,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 20:31:05,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 20:31:06,653 - INFO - Response - Page 2:
2025-05-20 20:31:06,654 - INFO - 第 2 页获取到 100 条记录
2025-05-20 20:31:06,854 - INFO - Request Parameters - Page 3:
2025-05-20 20:31:06,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 20:31:06,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 20:31:07,670 - INFO - Response - Page 3:
2025-05-20 20:31:07,670 - INFO - 第 3 页获取到 100 条记录
2025-05-20 20:31:07,870 - INFO - Request Parameters - Page 4:
2025-05-20 20:31:07,870 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 20:31:07,870 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 20:31:08,683 - INFO - Response - Page 4:
2025-05-20 20:31:08,683 - INFO - 第 4 页获取到 100 条记录
2025-05-20 20:31:08,883 - INFO - Request Parameters - Page 5:
2025-05-20 20:31:08,883 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 20:31:08,883 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 20:31:09,683 - INFO - Response - Page 5:
2025-05-20 20:31:09,683 - INFO - 第 5 页获取到 100 条记录
2025-05-20 20:31:09,883 - INFO - Request Parameters - Page 6:
2025-05-20 20:31:09,883 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 20:31:09,883 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 20:31:10,649 - INFO - Response - Page 6:
2025-05-20 20:31:10,649 - INFO - 第 6 页获取到 100 条记录
2025-05-20 20:31:10,849 - INFO - Request Parameters - Page 7:
2025-05-20 20:31:10,849 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 20:31:10,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 20:31:11,373 - INFO - Response - Page 7:
2025-05-20 20:31:11,373 - INFO - 第 7 页获取到 3 条记录
2025-05-20 20:31:11,573 - INFO - 查询完成，共获取到 603 条记录
2025-05-20 20:31:11,573 - INFO - 获取到 603 条表单数据
2025-05-20 20:31:11,583 - INFO - 当前日期 2025-05-19 有 219 条MySQL数据需要处理
2025-05-20 20:31:11,587 - INFO - 日期 2025-05-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 20:31:11,587 - INFO - 开始处理日期: 2025-05-20
2025-05-20 20:31:11,587 - INFO - Request Parameters - Page 1:
2025-05-20 20:31:11,587 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 20:31:11,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 20:31:12,093 - INFO - Response - Page 1:
2025-05-20 20:31:12,093 - INFO - 第 1 页获取到 3 条记录
2025-05-20 20:31:12,293 - INFO - 查询完成，共获取到 3 条记录
2025-05-20 20:31:12,293 - INFO - 获取到 3 条表单数据
2025-05-20 20:31:12,294 - INFO - 当前日期 2025-05-20 有 4 条MySQL数据需要处理
2025-05-20 20:31:12,294 - INFO - 开始批量插入 1 条新记录
2025-05-20 20:31:12,463 - INFO - 批量插入响应状态码: 200
2025-05-20 20:31:12,463 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 20 May 2025 12:31:12 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'BB6C1C2A-5CE8-76BE-A589-D6CB8F6BB36C', 'x-acs-trace-id': 'afef07c2d4951dd586827fd1ae3f0219', 'etag': '6Cs+XBPwUjDpVt5O7r9HKtQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-20 20:31:12,463 - INFO - 批量插入响应体: {'result': ['FINST-7PF66CC1W0HV8RB0D2QJG53JR8VM314BTHWAMX7']}
2025-05-20 20:31:12,463 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-20 20:31:12,463 - INFO - 成功插入的数据ID: ['FINST-7PF66CC1W0HV8RB0D2QJG53JR8VM314BTHWAMX7']
2025-05-20 20:31:17,464 - INFO - 批量插入完成，共 1 条记录
2025-05-20 20:31:17,464 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-05-20 20:31:17,464 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 2 条
2025-05-20 20:32:17,471 - INFO - 开始同步昨天与今天的销售数据: 2025-05-19 至 2025-05-20
2025-05-20 20:32:17,471 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 20:32:17,471 - INFO - 查询参数: ('2025-05-19', '2025-05-20')
2025-05-20 20:32:17,553 - INFO - MySQL查询成功，时间段: 2025-05-19 至 2025-05-20，共获取 607 条记录
2025-05-20 20:32:17,553 - INFO - 获取到 2 个日期需要处理: ['2025-05-19', '2025-05-20']
2025-05-20 20:32:17,558 - INFO - 开始处理日期: 2025-05-19
2025-05-20 20:32:17,559 - INFO - Request Parameters - Page 1:
2025-05-20 20:32:17,559 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 20:32:17,559 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 20:32:18,487 - INFO - Response - Page 1:
2025-05-20 20:32:18,487 - INFO - 第 1 页获取到 100 条记录
2025-05-20 20:32:18,687 - INFO - Request Parameters - Page 2:
2025-05-20 20:32:18,687 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 20:32:18,687 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 20:32:19,439 - INFO - Response - Page 2:
2025-05-20 20:32:19,439 - INFO - 第 2 页获取到 100 条记录
2025-05-20 20:32:19,640 - INFO - Request Parameters - Page 3:
2025-05-20 20:32:19,640 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 20:32:19,640 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 20:32:20,500 - INFO - Response - Page 3:
2025-05-20 20:32:20,500 - INFO - 第 3 页获取到 100 条记录
2025-05-20 20:32:20,701 - INFO - Request Parameters - Page 4:
2025-05-20 20:32:20,701 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 20:32:20,701 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 20:32:21,533 - INFO - Response - Page 4:
2025-05-20 20:32:21,534 - INFO - 第 4 页获取到 100 条记录
2025-05-20 20:32:21,734 - INFO - Request Parameters - Page 5:
2025-05-20 20:32:21,734 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 20:32:21,734 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 20:32:22,529 - INFO - Response - Page 5:
2025-05-20 20:32:22,530 - INFO - 第 5 页获取到 100 条记录
2025-05-20 20:32:22,731 - INFO - Request Parameters - Page 6:
2025-05-20 20:32:22,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 20:32:22,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 20:32:23,522 - INFO - Response - Page 6:
2025-05-20 20:32:23,522 - INFO - 第 6 页获取到 100 条记录
2025-05-20 20:32:23,722 - INFO - Request Parameters - Page 7:
2025-05-20 20:32:23,722 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 20:32:23,722 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 20:32:24,240 - INFO - Response - Page 7:
2025-05-20 20:32:24,240 - INFO - 第 7 页获取到 3 条记录
2025-05-20 20:32:24,440 - INFO - 查询完成，共获取到 603 条记录
2025-05-20 20:32:24,440 - INFO - 获取到 603 条表单数据
2025-05-20 20:32:24,450 - INFO - 当前日期 2025-05-19 有 603 条MySQL数据需要处理
2025-05-20 20:32:24,459 - INFO - 日期 2025-05-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 20:32:24,459 - INFO - 开始处理日期: 2025-05-20
2025-05-20 20:32:24,459 - INFO - Request Parameters - Page 1:
2025-05-20 20:32:24,459 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 20:32:24,459 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 20:32:25,019 - INFO - Response - Page 1:
2025-05-20 20:32:25,020 - INFO - 第 1 页获取到 4 条记录
2025-05-20 20:32:25,220 - INFO - 查询完成，共获取到 4 条记录
2025-05-20 20:32:25,220 - INFO - 获取到 4 条表单数据
2025-05-20 20:32:25,221 - INFO - 当前日期 2025-05-20 有 4 条MySQL数据需要处理
2025-05-20 20:32:25,221 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 20:32:25,222 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 20:32:25,222 - INFO - 同步完成
2025-05-20 21:30:34,448 - INFO - 使用默认增量同步（当天更新数据）
2025-05-20 21:30:34,449 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 21:30:34,449 - INFO - 查询参数: ('2025-05-20',)
2025-05-20 21:30:34,520 - INFO - MySQL查询成功，增量数据（日期: 2025-05-20），共获取 228 条记录
2025-05-20 21:30:34,520 - INFO - 获取到 5 个日期需要处理: ['2025-05-16', '2025-05-17', '2025-05-18', '2025-05-19', '2025-05-20']
2025-05-20 21:30:34,522 - INFO - 开始处理日期: 2025-05-16
2025-05-20 21:30:34,526 - INFO - Request Parameters - Page 1:
2025-05-20 21:30:34,526 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:30:34,526 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:30:42,645 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 6338C328-D20B-7FC4-8A03-1CD7E4A24D1E Response: {'code': 'ServiceUnavailable', 'requestid': '6338C328-D20B-7FC4-8A03-1CD7E4A24D1E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 6338C328-D20B-7FC4-8A03-1CD7E4A24D1E)
2025-05-20 21:30:42,646 - INFO - 开始处理日期: 2025-05-17
2025-05-20 21:30:42,646 - INFO - Request Parameters - Page 1:
2025-05-20 21:30:42,646 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:30:42,646 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:30:50,765 - ERROR - 处理日期 2025-05-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: DE5AA38D-1C2F-7DE2-BA6E-D156285DE012 Response: {'code': 'ServiceUnavailable', 'requestid': 'DE5AA38D-1C2F-7DE2-BA6E-D156285DE012', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: DE5AA38D-1C2F-7DE2-BA6E-D156285DE012)
2025-05-20 21:30:50,765 - INFO - 开始处理日期: 2025-05-18
2025-05-20 21:30:50,765 - INFO - Request Parameters - Page 1:
2025-05-20 21:30:50,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:30:50,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:30:58,892 - ERROR - 处理日期 2025-05-18 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: AA3CC15D-E655-788B-BD40-071A70478C12 Response: {'code': 'ServiceUnavailable', 'requestid': 'AA3CC15D-E655-788B-BD40-071A70478C12', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: AA3CC15D-E655-788B-BD40-071A70478C12)
2025-05-20 21:30:58,892 - INFO - 开始处理日期: 2025-05-19
2025-05-20 21:30:58,893 - INFO - Request Parameters - Page 1:
2025-05-20 21:30:58,893 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:30:58,893 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:30:59,841 - INFO - Response - Page 1:
2025-05-20 21:30:59,842 - INFO - 第 1 页获取到 100 条记录
2025-05-20 21:31:00,042 - INFO - Request Parameters - Page 2:
2025-05-20 21:31:00,042 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:31:00,042 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:31:00,814 - INFO - Response - Page 2:
2025-05-20 21:31:00,815 - INFO - 第 2 页获取到 100 条记录
2025-05-20 21:31:01,015 - INFO - Request Parameters - Page 3:
2025-05-20 21:31:01,015 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:31:01,015 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:31:01,830 - INFO - Response - Page 3:
2025-05-20 21:31:01,830 - INFO - 第 3 页获取到 100 条记录
2025-05-20 21:31:02,031 - INFO - Request Parameters - Page 4:
2025-05-20 21:31:02,031 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:31:02,031 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:31:02,878 - INFO - Response - Page 4:
2025-05-20 21:31:02,879 - INFO - 第 4 页获取到 100 条记录
2025-05-20 21:31:03,079 - INFO - Request Parameters - Page 5:
2025-05-20 21:31:03,079 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:31:03,079 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:31:03,919 - INFO - Response - Page 5:
2025-05-20 21:31:03,919 - INFO - 第 5 页获取到 100 条记录
2025-05-20 21:31:04,120 - INFO - Request Parameters - Page 6:
2025-05-20 21:31:04,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:31:04,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:31:04,913 - INFO - Response - Page 6:
2025-05-20 21:31:04,913 - INFO - 第 6 页获取到 100 条记录
2025-05-20 21:31:05,113 - INFO - Request Parameters - Page 7:
2025-05-20 21:31:05,113 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:31:05,113 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:31:05,641 - INFO - Response - Page 7:
2025-05-20 21:31:05,641 - INFO - 第 7 页获取到 3 条记录
2025-05-20 21:31:05,841 - INFO - 查询完成，共获取到 603 条记录
2025-05-20 21:31:05,841 - INFO - 获取到 603 条表单数据
2025-05-20 21:31:05,851 - INFO - 当前日期 2025-05-19 有 219 条MySQL数据需要处理
2025-05-20 21:31:05,855 - INFO - 日期 2025-05-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 21:31:05,856 - INFO - 开始处理日期: 2025-05-20
2025-05-20 21:31:05,856 - INFO - Request Parameters - Page 1:
2025-05-20 21:31:05,856 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:31:05,856 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:31:06,435 - INFO - Response - Page 1:
2025-05-20 21:31:06,435 - INFO - 第 1 页获取到 4 条记录
2025-05-20 21:31:06,635 - INFO - 查询完成，共获取到 4 条记录
2025-05-20 21:31:06,635 - INFO - 获取到 4 条表单数据
2025-05-20 21:31:06,636 - INFO - 当前日期 2025-05-20 有 5 条MySQL数据需要处理
2025-05-20 21:31:06,636 - INFO - 开始批量插入 1 条新记录
2025-05-20 21:31:06,813 - INFO - 批量插入响应状态码: 200
2025-05-20 21:31:06,813 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 20 May 2025 13:31:06 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5E767F37-B9FB-7193-A408-A831955F5F0F', 'x-acs-trace-id': '4275b9c0453db8e9269cdd05e271e3ef', 'etag': '6ruBBKxKn7el4MBfLVM/o1g0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-20 21:31:06,813 - INFO - 批量插入响应体: {'result': ['FINST-ME9666C1RAKVDSTBF1CKIDPQ7E0Y2W8CYJWAM13']}
2025-05-20 21:31:06,813 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-20 21:31:06,814 - INFO - 成功插入的数据ID: ['FINST-ME9666C1RAKVDSTBF1CKIDPQ7E0Y2W8CYJWAM13']
2025-05-20 21:31:11,816 - INFO - 批量插入完成，共 1 条记录
2025-05-20 21:31:11,816 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-05-20 21:31:11,816 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 3 条
2025-05-20 21:32:11,823 - INFO - 开始同步昨天与今天的销售数据: 2025-05-19 至 2025-05-20
2025-05-20 21:32:11,823 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 21:32:11,823 - INFO - 查询参数: ('2025-05-19', '2025-05-20')
2025-05-20 21:32:11,922 - INFO - MySQL查询成功，时间段: 2025-05-19 至 2025-05-20，共获取 609 条记录
2025-05-20 21:32:11,922 - INFO - 获取到 2 个日期需要处理: ['2025-05-19', '2025-05-20']
2025-05-20 21:32:11,927 - INFO - 开始处理日期: 2025-05-19
2025-05-20 21:32:11,927 - INFO - Request Parameters - Page 1:
2025-05-20 21:32:11,927 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:32:11,927 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:32:12,747 - INFO - Response - Page 1:
2025-05-20 21:32:12,747 - INFO - 第 1 页获取到 100 条记录
2025-05-20 21:32:12,947 - INFO - Request Parameters - Page 2:
2025-05-20 21:32:12,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:32:12,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:32:13,790 - INFO - Response - Page 2:
2025-05-20 21:32:13,791 - INFO - 第 2 页获取到 100 条记录
2025-05-20 21:32:13,991 - INFO - Request Parameters - Page 3:
2025-05-20 21:32:13,991 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:32:13,991 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:32:15,436 - INFO - Response - Page 3:
2025-05-20 21:32:15,436 - INFO - 第 3 页获取到 100 条记录
2025-05-20 21:32:15,637 - INFO - Request Parameters - Page 4:
2025-05-20 21:32:15,637 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:32:15,637 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:32:16,488 - INFO - Response - Page 4:
2025-05-20 21:32:16,488 - INFO - 第 4 页获取到 100 条记录
2025-05-20 21:32:16,689 - INFO - Request Parameters - Page 5:
2025-05-20 21:32:16,689 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:32:16,689 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:32:17,569 - INFO - Response - Page 5:
2025-05-20 21:32:17,569 - INFO - 第 5 页获取到 100 条记录
2025-05-20 21:32:17,769 - INFO - Request Parameters - Page 6:
2025-05-20 21:32:17,769 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:32:17,769 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:32:18,547 - INFO - Response - Page 6:
2025-05-20 21:32:18,547 - INFO - 第 6 页获取到 100 条记录
2025-05-20 21:32:18,748 - INFO - Request Parameters - Page 7:
2025-05-20 21:32:18,748 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:32:18,748 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:32:19,241 - INFO - Response - Page 7:
2025-05-20 21:32:19,242 - INFO - 第 7 页获取到 3 条记录
2025-05-20 21:32:19,442 - INFO - 查询完成，共获取到 603 条记录
2025-05-20 21:32:19,442 - INFO - 获取到 603 条表单数据
2025-05-20 21:32:19,453 - INFO - 当前日期 2025-05-19 有 603 条MySQL数据需要处理
2025-05-20 21:32:19,463 - INFO - 日期 2025-05-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 21:32:19,463 - INFO - 开始处理日期: 2025-05-20
2025-05-20 21:32:19,464 - INFO - Request Parameters - Page 1:
2025-05-20 21:32:19,464 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 21:32:19,464 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 21:32:20,061 - INFO - Response - Page 1:
2025-05-20 21:32:20,061 - INFO - 第 1 页获取到 5 条记录
2025-05-20 21:32:20,262 - INFO - 查询完成，共获取到 5 条记录
2025-05-20 21:32:20,262 - INFO - 获取到 5 条表单数据
2025-05-20 21:32:20,263 - INFO - 当前日期 2025-05-20 有 6 条MySQL数据需要处理
2025-05-20 21:32:20,263 - INFO - 开始批量插入 1 条新记录
2025-05-20 21:32:20,413 - INFO - 批量插入响应状态码: 200
2025-05-20 21:32:20,413 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 20 May 2025 13:32:19 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '1AA0DD8B-E99D-78E7-BD72-40D64623D2E1', 'x-acs-trace-id': '42468684983d8d314068520eb3b46b45', 'etag': '6hIMDzVBh5cY4W1keUXZ5yA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-20 21:32:20,413 - INFO - 批量插入响应体: {'result': ['FINST-3PF66X61G8LVEATJEMG9H8F6OZ0S291XZJWAMK4']}
2025-05-20 21:32:20,413 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-20 21:32:20,413 - INFO - 成功插入的数据ID: ['FINST-3PF66X61G8LVEATJEMG9H8F6OZ0S291XZJWAMK4']
2025-05-20 21:32:25,414 - INFO - 批量插入完成，共 1 条记录
2025-05-20 21:32:25,414 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-05-20 21:32:25,414 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 0 条
2025-05-20 21:32:25,414 - INFO - 同步完成
2025-05-20 22:30:33,750 - INFO - 使用默认增量同步（当天更新数据）
2025-05-20 22:30:33,750 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 22:30:33,750 - INFO - 查询参数: ('2025-05-20',)
2025-05-20 22:30:33,825 - INFO - MySQL查询成功，增量数据（日期: 2025-05-20），共获取 249 条记录
2025-05-20 22:30:33,825 - INFO - 获取到 5 个日期需要处理: ['2025-05-16', '2025-05-17', '2025-05-18', '2025-05-19', '2025-05-20']
2025-05-20 22:30:33,827 - INFO - 开始处理日期: 2025-05-16
2025-05-20 22:30:33,830 - INFO - Request Parameters - Page 1:
2025-05-20 22:30:33,830 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 22:30:33,830 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 22:30:41,956 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 823355EE-71B2-7B6D-B2C7-A3DF5ADFCC0D Response: {'code': 'ServiceUnavailable', 'requestid': '823355EE-71B2-7B6D-B2C7-A3DF5ADFCC0D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 823355EE-71B2-7B6D-B2C7-A3DF5ADFCC0D)
2025-05-20 22:30:41,956 - INFO - 开始处理日期: 2025-05-17
2025-05-20 22:30:41,957 - INFO - Request Parameters - Page 1:
2025-05-20 22:30:41,957 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 22:30:41,957 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 22:30:50,082 - ERROR - 处理日期 2025-05-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B752010E-E957-762E-930C-04351713E1E8 Response: {'code': 'ServiceUnavailable', 'requestid': 'B752010E-E957-762E-930C-04351713E1E8', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B752010E-E957-762E-930C-04351713E1E8)
2025-05-20 22:30:50,082 - INFO - 开始处理日期: 2025-05-18
2025-05-20 22:30:50,082 - INFO - Request Parameters - Page 1:
2025-05-20 22:30:50,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 22:30:50,083 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 22:30:50,928 - INFO - Response - Page 1:
2025-05-20 22:30:50,929 - INFO - 第 1 页获取到 100 条记录
2025-05-20 22:30:51,129 - INFO - Request Parameters - Page 2:
2025-05-20 22:30:51,129 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 22:30:51,129 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 22:30:51,946 - INFO - Response - Page 2:
2025-05-20 22:30:51,946 - INFO - 第 2 页获取到 100 条记录
2025-05-20 22:30:52,147 - INFO - Request Parameters - Page 3:
2025-05-20 22:30:52,147 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 22:30:52,147 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 22:30:52,961 - INFO - Response - Page 3:
2025-05-20 22:30:52,961 - INFO - 第 3 页获取到 100 条记录
2025-05-20 22:30:53,162 - INFO - Request Parameters - Page 4:
2025-05-20 22:30:53,162 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 22:30:53,162 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 22:30:54,110 - INFO - Response - Page 4:
2025-05-20 22:30:54,110 - INFO - 第 4 页获取到 100 条记录
2025-05-20 22:30:54,310 - INFO - Request Parameters - Page 5:
2025-05-20 22:30:54,310 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 22:30:54,310 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 22:30:55,104 - INFO - Response - Page 5:
2025-05-20 22:30:55,104 - INFO - 第 5 页获取到 100 条记录
2025-05-20 22:30:55,304 - INFO - Request Parameters - Page 6:
2025-05-20 22:30:55,304 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 22:30:55,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 22:30:56,125 - INFO - Response - Page 6:
2025-05-20 22:30:56,125 - INFO - 第 6 页获取到 100 条记录
2025-05-20 22:30:56,325 - INFO - Request Parameters - Page 7:
2025-05-20 22:30:56,325 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 22:30:56,325 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 22:30:56,902 - INFO - Response - Page 7:
2025-05-20 22:30:56,902 - INFO - 第 7 页获取到 13 条记录
2025-05-20 22:30:57,102 - INFO - 查询完成，共获取到 613 条记录
2025-05-20 22:30:57,102 - INFO - 获取到 613 条表单数据
2025-05-20 22:30:57,112 - INFO - 当前日期 2025-05-18 有 1 条MySQL数据需要处理
2025-05-20 22:30:57,112 - INFO - 日期 2025-05-18 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 22:30:57,112 - INFO - 开始处理日期: 2025-05-19
2025-05-20 22:30:57,112 - INFO - Request Parameters - Page 1:
2025-05-20 22:30:57,112 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 22:30:57,112 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 22:30:58,125 - INFO - Response - Page 1:
2025-05-20 22:30:58,125 - INFO - 第 1 页获取到 100 条记录
2025-05-20 22:30:58,325 - INFO - Request Parameters - Page 2:
2025-05-20 22:30:58,325 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 22:30:58,325 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 22:30:59,100 - INFO - Response - Page 2:
2025-05-20 22:30:59,101 - INFO - 第 2 页获取到 100 条记录
2025-05-20 22:30:59,301 - INFO - Request Parameters - Page 3:
2025-05-20 22:30:59,301 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 22:30:59,301 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 22:31:00,037 - INFO - Response - Page 3:
2025-05-20 22:31:00,038 - INFO - 第 3 页获取到 100 条记录
2025-05-20 22:31:00,239 - INFO - Request Parameters - Page 4:
2025-05-20 22:31:00,239 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 22:31:00,239 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 22:31:01,063 - INFO - Response - Page 4:
2025-05-20 22:31:01,063 - INFO - 第 4 页获取到 100 条记录
2025-05-20 22:31:01,263 - INFO - Request Parameters - Page 5:
2025-05-20 22:31:01,263 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 22:31:01,263 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 22:31:02,040 - INFO - Response - Page 5:
2025-05-20 22:31:02,040 - INFO - 第 5 页获取到 100 条记录
2025-05-20 22:31:02,241 - INFO - Request Parameters - Page 6:
2025-05-20 22:31:02,241 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 22:31:02,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 22:31:03,037 - INFO - Response - Page 6:
2025-05-20 22:31:03,038 - INFO - 第 6 页获取到 100 条记录
2025-05-20 22:31:03,238 - INFO - Request Parameters - Page 7:
2025-05-20 22:31:03,238 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 22:31:03,238 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 22:31:03,752 - INFO - Response - Page 7:
2025-05-20 22:31:03,752 - INFO - 第 7 页获取到 3 条记录
2025-05-20 22:31:03,952 - INFO - 查询完成，共获取到 603 条记录
2025-05-20 22:31:03,952 - INFO - 获取到 603 条表单数据
2025-05-20 22:31:03,962 - INFO - 当前日期 2025-05-19 有 219 条MySQL数据需要处理
2025-05-20 22:31:03,965 - INFO - 日期 2025-05-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 22:31:03,965 - INFO - 开始处理日期: 2025-05-20
2025-05-20 22:31:03,965 - INFO - Request Parameters - Page 1:
2025-05-20 22:31:03,965 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 22:31:03,965 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 22:31:04,511 - INFO - Response - Page 1:
2025-05-20 22:31:04,511 - INFO - 第 1 页获取到 6 条记录
2025-05-20 22:31:04,713 - INFO - 查询完成，共获取到 6 条记录
2025-05-20 22:31:04,713 - INFO - 获取到 6 条表单数据
2025-05-20 22:31:04,714 - INFO - 当前日期 2025-05-20 有 26 条MySQL数据需要处理
2025-05-20 22:31:04,715 - INFO - 开始批量插入 20 条新记录
2025-05-20 22:31:04,886 - INFO - 批量插入响应状态码: 200
2025-05-20 22:31:04,886 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 20 May 2025 14:31:03 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '992', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'C2E0978B-1BE6-7A5A-9ECE-CF9A0E7C7919', 'x-acs-trace-id': 'fccaf14594522f6835f5cbe93bfd800f', 'etag': '9ThwTnnGnexCJ8n08ur7JaA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-20 22:31:04,886 - INFO - 批量插入响应体: {'result': ['FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAMV71', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAMW71', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAMX71', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAMY71', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAMZ71', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAM081', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAM181', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAM281', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAM381', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAM481', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAM581', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAM681', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAM781', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAM881', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAM981', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAMA81', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAMB81', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAMC81', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAMD81', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAME81']}
2025-05-20 22:31:04,886 - INFO - 批量插入表单数据成功，批次 1，共 20 条记录
2025-05-20 22:31:04,886 - INFO - 成功插入的数据ID: ['FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAMV71', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAMW71', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAMX71', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAMY71', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAMZ71', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAM081', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAM181', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAM281', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAM381', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAM481', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAM581', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAM681', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAM781', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAM881', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAM981', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAMA81', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAMB81', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAMC81', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAMD81', 'FINST-L8D665C10CHV9KLZCDJSO9AV6I1T339G3MWAME81']
2025-05-20 22:31:09,888 - INFO - 批量插入完成，共 20 条记录
2025-05-20 22:31:09,888 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 20 条，错误: 0 条
2025-05-20 22:31:09,888 - INFO - 数据同步完成！更新: 0 条，插入: 20 条，错误: 2 条
2025-05-20 22:32:09,895 - INFO - 开始同步昨天与今天的销售数据: 2025-05-19 至 2025-05-20
2025-05-20 22:32:09,895 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 22:32:09,895 - INFO - 查询参数: ('2025-05-19', '2025-05-20')
2025-05-20 22:32:09,978 - INFO - MySQL查询成功，时间段: 2025-05-19 至 2025-05-20，共获取 629 条记录
2025-05-20 22:32:09,978 - INFO - 获取到 2 个日期需要处理: ['2025-05-19', '2025-05-20']
2025-05-20 22:32:09,983 - INFO - 开始处理日期: 2025-05-19
2025-05-20 22:32:09,984 - INFO - Request Parameters - Page 1:
2025-05-20 22:32:09,984 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 22:32:09,984 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 22:32:10,809 - INFO - Response - Page 1:
2025-05-20 22:32:10,810 - INFO - 第 1 页获取到 100 条记录
2025-05-20 22:32:11,010 - INFO - Request Parameters - Page 2:
2025-05-20 22:32:11,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 22:32:11,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 22:32:11,838 - INFO - Response - Page 2:
2025-05-20 22:32:11,838 - INFO - 第 2 页获取到 100 条记录
2025-05-20 22:32:12,038 - INFO - Request Parameters - Page 3:
2025-05-20 22:32:12,038 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 22:32:12,038 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 22:32:12,793 - INFO - Response - Page 3:
2025-05-20 22:32:12,794 - INFO - 第 3 页获取到 100 条记录
2025-05-20 22:32:12,994 - INFO - Request Parameters - Page 4:
2025-05-20 22:32:12,994 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 22:32:12,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 22:32:13,813 - INFO - Response - Page 4:
2025-05-20 22:32:13,813 - INFO - 第 4 页获取到 100 条记录
2025-05-20 22:32:14,014 - INFO - Request Parameters - Page 5:
2025-05-20 22:32:14,014 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 22:32:14,014 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 22:32:14,751 - INFO - Response - Page 5:
2025-05-20 22:32:14,751 - INFO - 第 5 页获取到 100 条记录
2025-05-20 22:32:14,951 - INFO - Request Parameters - Page 6:
2025-05-20 22:32:14,951 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 22:32:14,951 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 22:32:15,839 - INFO - Response - Page 6:
2025-05-20 22:32:15,840 - INFO - 第 6 页获取到 100 条记录
2025-05-20 22:32:16,040 - INFO - Request Parameters - Page 7:
2025-05-20 22:32:16,040 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 22:32:16,040 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 22:32:16,573 - INFO - Response - Page 7:
2025-05-20 22:32:16,574 - INFO - 第 7 页获取到 3 条记录
2025-05-20 22:32:16,775 - INFO - 查询完成，共获取到 603 条记录
2025-05-20 22:32:16,775 - INFO - 获取到 603 条表单数据
2025-05-20 22:32:16,785 - INFO - 当前日期 2025-05-19 有 603 条MySQL数据需要处理
2025-05-20 22:32:16,795 - INFO - 日期 2025-05-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 22:32:16,795 - INFO - 开始处理日期: 2025-05-20
2025-05-20 22:32:16,796 - INFO - Request Parameters - Page 1:
2025-05-20 22:32:16,796 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 22:32:16,796 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 22:32:17,419 - INFO - Response - Page 1:
2025-05-20 22:32:17,419 - INFO - 第 1 页获取到 26 条记录
2025-05-20 22:32:17,619 - INFO - 查询完成，共获取到 26 条记录
2025-05-20 22:32:17,619 - INFO - 获取到 26 条表单数据
2025-05-20 22:32:17,621 - INFO - 当前日期 2025-05-20 有 26 条MySQL数据需要处理
2025-05-20 22:32:17,621 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 22:32:17,621 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 22:32:17,621 - INFO - 同步完成
2025-05-20 23:30:56,989 - INFO - 使用默认增量同步（当天更新数据）
2025-05-20 23:30:56,989 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 23:30:56,989 - INFO - 查询参数: ('2025-05-20',)
2025-05-20 23:30:57,064 - INFO - MySQL查询成功，增量数据（日期: 2025-05-20），共获取 252 条记录
2025-05-20 23:30:57,064 - INFO - 获取到 5 个日期需要处理: ['2025-05-16', '2025-05-17', '2025-05-18', '2025-05-19', '2025-05-20']
2025-05-20 23:30:57,067 - INFO - 开始处理日期: 2025-05-16
2025-05-20 23:30:57,070 - INFO - Request Parameters - Page 1:
2025-05-20 23:30:57,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 23:30:57,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747324800000, 1747411199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 23:31:05,204 - ERROR - 处理日期 2025-05-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C63275ED-AF20-7BEF-B695-9C087E94CA66 Response: {'code': 'ServiceUnavailable', 'requestid': 'C63275ED-AF20-7BEF-B695-9C087E94CA66', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C63275ED-AF20-7BEF-B695-9C087E94CA66)
2025-05-20 23:31:05,204 - INFO - 开始处理日期: 2025-05-17
2025-05-20 23:31:05,204 - INFO - Request Parameters - Page 1:
2025-05-20 23:31:05,204 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 23:31:05,204 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747411200000, 1747497599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 23:31:13,313 - ERROR - 处理日期 2025-05-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C901B8C4-A9F2-7ADA-A4B1-A1CCBE744B16 Response: {'code': 'ServiceUnavailable', 'requestid': 'C901B8C4-A9F2-7ADA-A4B1-A1CCBE744B16', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C901B8C4-A9F2-7ADA-A4B1-A1CCBE744B16)
2025-05-20 23:31:13,313 - INFO - 开始处理日期: 2025-05-18
2025-05-20 23:31:13,313 - INFO - Request Parameters - Page 1:
2025-05-20 23:31:13,313 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 23:31:13,313 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 23:31:14,238 - INFO - Response - Page 1:
2025-05-20 23:31:14,238 - INFO - 第 1 页获取到 100 条记录
2025-05-20 23:31:14,439 - INFO - Request Parameters - Page 2:
2025-05-20 23:31:14,439 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 23:31:14,439 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 23:31:20,390 - INFO - Response - Page 2:
2025-05-20 23:31:20,390 - INFO - 第 2 页获取到 100 条记录
2025-05-20 23:31:20,590 - INFO - Request Parameters - Page 3:
2025-05-20 23:31:20,590 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 23:31:20,590 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 23:31:21,358 - INFO - Response - Page 3:
2025-05-20 23:31:21,358 - INFO - 第 3 页获取到 100 条记录
2025-05-20 23:31:21,558 - INFO - Request Parameters - Page 4:
2025-05-20 23:31:21,558 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 23:31:21,558 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 23:31:22,434 - INFO - Response - Page 4:
2025-05-20 23:31:22,434 - INFO - 第 4 页获取到 100 条记录
2025-05-20 23:31:22,634 - INFO - Request Parameters - Page 5:
2025-05-20 23:31:22,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 23:31:22,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 23:31:23,465 - INFO - Response - Page 5:
2025-05-20 23:31:23,465 - INFO - 第 5 页获取到 100 条记录
2025-05-20 23:31:23,666 - INFO - Request Parameters - Page 6:
2025-05-20 23:31:23,666 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 23:31:23,666 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 23:31:24,458 - INFO - Response - Page 6:
2025-05-20 23:31:24,459 - INFO - 第 6 页获取到 100 条记录
2025-05-20 23:31:24,659 - INFO - Request Parameters - Page 7:
2025-05-20 23:31:24,659 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 23:31:24,659 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747497600000, 1747583999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 23:31:25,259 - INFO - Response - Page 7:
2025-05-20 23:31:25,259 - INFO - 第 7 页获取到 13 条记录
2025-05-20 23:31:25,459 - INFO - 查询完成，共获取到 613 条记录
2025-05-20 23:31:25,459 - INFO - 获取到 613 条表单数据
2025-05-20 23:31:25,468 - INFO - 当前日期 2025-05-18 有 1 条MySQL数据需要处理
2025-05-20 23:31:25,468 - INFO - 日期 2025-05-18 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 23:31:25,468 - INFO - 开始处理日期: 2025-05-19
2025-05-20 23:31:25,468 - INFO - Request Parameters - Page 1:
2025-05-20 23:31:25,468 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 23:31:25,468 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 23:31:26,319 - INFO - Response - Page 1:
2025-05-20 23:31:26,319 - INFO - 第 1 页获取到 100 条记录
2025-05-20 23:31:26,519 - INFO - Request Parameters - Page 2:
2025-05-20 23:31:26,519 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 23:31:26,519 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 23:31:27,418 - INFO - Response - Page 2:
2025-05-20 23:31:27,419 - INFO - 第 2 页获取到 100 条记录
2025-05-20 23:31:27,619 - INFO - Request Parameters - Page 3:
2025-05-20 23:31:27,619 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 23:31:27,619 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 23:31:28,344 - INFO - Response - Page 3:
2025-05-20 23:31:28,345 - INFO - 第 3 页获取到 100 条记录
2025-05-20 23:31:28,545 - INFO - Request Parameters - Page 4:
2025-05-20 23:31:28,545 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 23:31:28,545 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 23:31:29,541 - INFO - Response - Page 4:
2025-05-20 23:31:29,541 - INFO - 第 4 页获取到 100 条记录
2025-05-20 23:31:29,741 - INFO - Request Parameters - Page 5:
2025-05-20 23:31:29,741 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 23:31:29,741 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 23:31:30,525 - INFO - Response - Page 5:
2025-05-20 23:31:30,525 - INFO - 第 5 页获取到 100 条记录
2025-05-20 23:31:30,726 - INFO - Request Parameters - Page 6:
2025-05-20 23:31:30,726 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 23:31:30,726 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 23:31:31,514 - INFO - Response - Page 6:
2025-05-20 23:31:31,514 - INFO - 第 6 页获取到 100 条记录
2025-05-20 23:31:31,714 - INFO - Request Parameters - Page 7:
2025-05-20 23:31:31,714 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 23:31:31,714 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 23:31:32,198 - INFO - Response - Page 7:
2025-05-20 23:31:32,199 - INFO - 第 7 页获取到 3 条记录
2025-05-20 23:31:32,399 - INFO - 查询完成，共获取到 603 条记录
2025-05-20 23:31:32,399 - INFO - 获取到 603 条表单数据
2025-05-20 23:31:32,409 - INFO - 当前日期 2025-05-19 有 219 条MySQL数据需要处理
2025-05-20 23:31:32,413 - INFO - 日期 2025-05-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 23:31:32,413 - INFO - 开始处理日期: 2025-05-20
2025-05-20 23:31:32,414 - INFO - Request Parameters - Page 1:
2025-05-20 23:31:32,414 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 23:31:32,414 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 23:31:33,073 - INFO - Response - Page 1:
2025-05-20 23:31:33,073 - INFO - 第 1 页获取到 26 条记录
2025-05-20 23:31:33,274 - INFO - 查询完成，共获取到 26 条记录
2025-05-20 23:31:33,274 - INFO - 获取到 26 条表单数据
2025-05-20 23:31:33,276 - INFO - 当前日期 2025-05-20 有 29 条MySQL数据需要处理
2025-05-20 23:31:33,276 - INFO - 开始批量插入 3 条新记录
2025-05-20 23:31:33,427 - INFO - 批量插入响应状态码: 200
2025-05-20 23:31:33,427 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 20 May 2025 15:31:33 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '156', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '1B9FDC7C-C772-7016-8509-FBBC619DBC24', 'x-acs-trace-id': 'a2dcdf019806e9a888b66a644297d953', 'etag': '1I3WF2xSb6tgnL/1HltiCJQ6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-20 23:31:33,427 - INFO - 批量插入响应体: {'result': ['FINST-6IF66PC16ALVX8H4CJZNHAF71S1A28V89OWAME7', 'FINST-6IF66PC16ALVX8H4CJZNHAF71S1A28V89OWAMF7', 'FINST-6IF66PC16ALVX8H4CJZNHAF71S1A28V89OWAMG7']}
2025-05-20 23:31:33,427 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-05-20 23:31:33,427 - INFO - 成功插入的数据ID: ['FINST-6IF66PC16ALVX8H4CJZNHAF71S1A28V89OWAME7', 'FINST-6IF66PC16ALVX8H4CJZNHAF71S1A28V89OWAMF7', 'FINST-6IF66PC16ALVX8H4CJZNHAF71S1A28V89OWAMG7']
2025-05-20 23:31:38,427 - INFO - 批量插入完成，共 3 条记录
2025-05-20 23:31:38,427 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 3 条，错误: 0 条
2025-05-20 23:31:38,427 - INFO - 数据同步完成！更新: 0 条，插入: 3 条，错误: 2 条
2025-05-20 23:32:38,432 - INFO - 开始同步昨天与今天的销售数据: 2025-05-19 至 2025-05-20
2025-05-20 23:32:38,432 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-20 23:32:38,432 - INFO - 查询参数: ('2025-05-19', '2025-05-20')
2025-05-20 23:32:38,516 - INFO - MySQL查询成功，时间段: 2025-05-19 至 2025-05-20，共获取 632 条记录
2025-05-20 23:32:38,517 - INFO - 获取到 2 个日期需要处理: ['2025-05-19', '2025-05-20']
2025-05-20 23:32:38,522 - INFO - 开始处理日期: 2025-05-19
2025-05-20 23:32:38,522 - INFO - Request Parameters - Page 1:
2025-05-20 23:32:38,523 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 23:32:38,523 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 23:32:39,599 - INFO - Response - Page 1:
2025-05-20 23:32:39,599 - INFO - 第 1 页获取到 100 条记录
2025-05-20 23:32:39,799 - INFO - Request Parameters - Page 2:
2025-05-20 23:32:39,799 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 23:32:39,799 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 23:32:40,621 - INFO - Response - Page 2:
2025-05-20 23:32:40,621 - INFO - 第 2 页获取到 100 条记录
2025-05-20 23:32:40,821 - INFO - Request Parameters - Page 3:
2025-05-20 23:32:40,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 23:32:40,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 23:32:41,616 - INFO - Response - Page 3:
2025-05-20 23:32:41,616 - INFO - 第 3 页获取到 100 条记录
2025-05-20 23:32:41,816 - INFO - Request Parameters - Page 4:
2025-05-20 23:32:41,816 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 23:32:41,816 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 23:32:42,589 - INFO - Response - Page 4:
2025-05-20 23:32:42,589 - INFO - 第 4 页获取到 100 条记录
2025-05-20 23:32:42,789 - INFO - Request Parameters - Page 5:
2025-05-20 23:32:42,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 23:32:42,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 23:32:43,594 - INFO - Response - Page 5:
2025-05-20 23:32:43,594 - INFO - 第 5 页获取到 100 条记录
2025-05-20 23:32:43,795 - INFO - Request Parameters - Page 6:
2025-05-20 23:32:43,795 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 23:32:43,795 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 23:32:44,690 - INFO - Response - Page 6:
2025-05-20 23:32:44,691 - INFO - 第 6 页获取到 100 条记录
2025-05-20 23:32:44,892 - INFO - Request Parameters - Page 7:
2025-05-20 23:32:44,892 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 23:32:44,892 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 23:32:45,370 - INFO - Response - Page 7:
2025-05-20 23:32:45,370 - INFO - 第 7 页获取到 3 条记录
2025-05-20 23:32:45,570 - INFO - 查询完成，共获取到 603 条记录
2025-05-20 23:32:45,570 - INFO - 获取到 603 条表单数据
2025-05-20 23:32:45,581 - INFO - 当前日期 2025-05-19 有 603 条MySQL数据需要处理
2025-05-20 23:32:45,591 - INFO - 日期 2025-05-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 23:32:45,591 - INFO - 开始处理日期: 2025-05-20
2025-05-20 23:32:45,591 - INFO - Request Parameters - Page 1:
2025-05-20 23:32:45,591 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-20 23:32:45,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-20 23:32:46,263 - INFO - Response - Page 1:
2025-05-20 23:32:46,263 - INFO - 第 1 页获取到 29 条记录
2025-05-20 23:32:46,463 - INFO - 查询完成，共获取到 29 条记录
2025-05-20 23:32:46,463 - INFO - 获取到 29 条表单数据
2025-05-20 23:32:46,464 - INFO - 当前日期 2025-05-20 有 29 条MySQL数据需要处理
2025-05-20 23:32:46,465 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 23:32:46,465 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-20 23:32:46,466 - INFO - 同步完成
