2025-05-21 00:30:34,416 - INFO - 使用默认增量同步（当天更新数据）
2025-05-21 00:30:34,416 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 00:30:34,418 - INFO - 查询参数: ('2025-05-21',)
2025-05-21 00:30:34,479 - INFO - MySQL查询成功，增量数据（日期: 2025-05-21），共获取 0 条记录
2025-05-21 00:30:34,479 - ERROR - 未获取到MySQL数据
2025-05-21 00:31:34,484 - INFO - 开始同步昨天与今天的销售数据: 2025-05-20 至 2025-05-21
2025-05-21 00:31:34,484 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 00:31:34,484 - INFO - 查询参数: ('2025-05-20', '2025-05-21')
2025-05-21 00:31:34,544 - INFO - MySQL查询成功，时间段: 2025-05-20 至 2025-05-21，共获取 29 条记录
2025-05-21 00:31:34,544 - INFO - 获取到 1 个日期需要处理: ['2025-05-20']
2025-05-21 00:31:34,544 - INFO - 开始处理日期: 2025-05-20
2025-05-21 00:31:34,547 - INFO - Request Parameters - Page 1:
2025-05-21 00:31:34,548 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 00:31:34,548 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 00:31:42,680 - ERROR - 处理日期 2025-05-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 96E41F49-BBD4-7E4A-B4B7-055AF2723C0E Response: {'code': 'ServiceUnavailable', 'requestid': '96E41F49-BBD4-7E4A-B4B7-055AF2723C0E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 96E41F49-BBD4-7E4A-B4B7-055AF2723C0E)
2025-05-21 00:31:42,680 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-21 00:31:42,680 - INFO - 同步完成
2025-05-21 01:30:34,290 - INFO - 使用默认增量同步（当天更新数据）
2025-05-21 01:30:34,290 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 01:30:34,290 - INFO - 查询参数: ('2025-05-21',)
2025-05-21 01:30:34,351 - INFO - MySQL查询成功，增量数据（日期: 2025-05-21），共获取 0 条记录
2025-05-21 01:30:34,351 - ERROR - 未获取到MySQL数据
2025-05-21 01:31:34,356 - INFO - 开始同步昨天与今天的销售数据: 2025-05-20 至 2025-05-21
2025-05-21 01:31:34,356 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 01:31:34,356 - INFO - 查询参数: ('2025-05-20', '2025-05-21')
2025-05-21 01:31:34,415 - INFO - MySQL查询成功，时间段: 2025-05-20 至 2025-05-21，共获取 29 条记录
2025-05-21 01:31:34,416 - INFO - 获取到 1 个日期需要处理: ['2025-05-20']
2025-05-21 01:31:34,416 - INFO - 开始处理日期: 2025-05-20
2025-05-21 01:31:34,419 - INFO - Request Parameters - Page 1:
2025-05-21 01:31:34,419 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 01:31:34,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 01:31:42,535 - ERROR - 处理日期 2025-05-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: ED1C5CA3-5F67-786C-8112-F2BAFDA0DE97 Response: {'code': 'ServiceUnavailable', 'requestid': 'ED1C5CA3-5F67-786C-8112-F2BAFDA0DE97', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: ED1C5CA3-5F67-786C-8112-F2BAFDA0DE97)
2025-05-21 01:31:42,536 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-21 01:31:42,536 - INFO - 同步完成
2025-05-21 02:30:34,250 - INFO - 使用默认增量同步（当天更新数据）
2025-05-21 02:30:34,250 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 02:30:34,250 - INFO - 查询参数: ('2025-05-21',)
2025-05-21 02:30:34,312 - INFO - MySQL查询成功，增量数据（日期: 2025-05-21），共获取 0 条记录
2025-05-21 02:30:34,312 - ERROR - 未获取到MySQL数据
2025-05-21 02:31:34,316 - INFO - 开始同步昨天与今天的销售数据: 2025-05-20 至 2025-05-21
2025-05-21 02:31:34,316 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 02:31:34,316 - INFO - 查询参数: ('2025-05-20', '2025-05-21')
2025-05-21 02:31:34,377 - INFO - MySQL查询成功，时间段: 2025-05-20 至 2025-05-21，共获取 29 条记录
2025-05-21 02:31:34,377 - INFO - 获取到 1 个日期需要处理: ['2025-05-20']
2025-05-21 02:31:34,378 - INFO - 开始处理日期: 2025-05-20
2025-05-21 02:31:34,380 - INFO - Request Parameters - Page 1:
2025-05-21 02:31:34,380 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 02:31:34,380 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 02:31:42,515 - ERROR - 处理日期 2025-05-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 949BF651-0C47-7BCF-B667-645580EE7098 Response: {'code': 'ServiceUnavailable', 'requestid': '949BF651-0C47-7BCF-B667-645580EE7098', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 949BF651-0C47-7BCF-B667-645580EE7098)
2025-05-21 02:31:42,516 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-21 02:31:42,516 - INFO - 同步完成
2025-05-21 03:30:34,342 - INFO - 使用默认增量同步（当天更新数据）
2025-05-21 03:30:34,343 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 03:30:34,343 - INFO - 查询参数: ('2025-05-21',)
2025-05-21 03:30:34,405 - INFO - MySQL查询成功，增量数据（日期: 2025-05-21），共获取 0 条记录
2025-05-21 03:30:34,405 - ERROR - 未获取到MySQL数据
2025-05-21 03:31:34,409 - INFO - 开始同步昨天与今天的销售数据: 2025-05-20 至 2025-05-21
2025-05-21 03:31:34,409 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 03:31:34,409 - INFO - 查询参数: ('2025-05-20', '2025-05-21')
2025-05-21 03:31:34,469 - INFO - MySQL查询成功，时间段: 2025-05-20 至 2025-05-21，共获取 29 条记录
2025-05-21 03:31:34,469 - INFO - 获取到 1 个日期需要处理: ['2025-05-20']
2025-05-21 03:31:34,470 - INFO - 开始处理日期: 2025-05-20
2025-05-21 03:31:34,473 - INFO - Request Parameters - Page 1:
2025-05-21 03:31:34,473 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 03:31:34,473 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 03:31:42,589 - ERROR - 处理日期 2025-05-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 50184F3A-5B89-76DD-8B76-4FBE6647D19C Response: {'code': 'ServiceUnavailable', 'requestid': '50184F3A-5B89-76DD-8B76-4FBE6647D19C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 50184F3A-5B89-76DD-8B76-4FBE6647D19C)
2025-05-21 03:31:42,589 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-21 03:31:42,589 - INFO - 同步完成
2025-05-21 04:30:34,107 - INFO - 使用默认增量同步（当天更新数据）
2025-05-21 04:30:34,107 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 04:30:34,107 - INFO - 查询参数: ('2025-05-21',)
2025-05-21 04:30:34,169 - INFO - MySQL查询成功，增量数据（日期: 2025-05-21），共获取 1 条记录
2025-05-21 04:30:34,169 - INFO - 获取到 1 个日期需要处理: ['2025-05-20']
2025-05-21 04:30:34,169 - INFO - 开始处理日期: 2025-05-20
2025-05-21 04:30:34,171 - INFO - Request Parameters - Page 1:
2025-05-21 04:30:34,171 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 04:30:34,171 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 04:30:42,290 - ERROR - 处理日期 2025-05-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 23BD4E85-F600-7F8B-816F-901392ECE886 Response: {'code': 'ServiceUnavailable', 'requestid': '23BD4E85-F600-7F8B-816F-901392ECE886', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 23BD4E85-F600-7F8B-816F-901392ECE886)
2025-05-21 04:30:42,290 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-21 04:31:42,295 - INFO - 开始同步昨天与今天的销售数据: 2025-05-20 至 2025-05-21
2025-05-21 04:31:42,295 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 04:31:42,295 - INFO - 查询参数: ('2025-05-20', '2025-05-21')
2025-05-21 04:31:42,355 - INFO - MySQL查询成功，时间段: 2025-05-20 至 2025-05-21，共获取 30 条记录
2025-05-21 04:31:42,356 - INFO - 获取到 1 个日期需要处理: ['2025-05-20']
2025-05-21 04:31:42,356 - INFO - 开始处理日期: 2025-05-20
2025-05-21 04:31:42,356 - INFO - Request Parameters - Page 1:
2025-05-21 04:31:42,356 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 04:31:42,356 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 04:31:43,053 - INFO - Response - Page 1:
2025-05-21 04:31:43,053 - INFO - 第 1 页获取到 29 条记录
2025-05-21 04:31:43,255 - INFO - 查询完成，共获取到 29 条记录
2025-05-21 04:31:43,255 - INFO - 获取到 29 条表单数据
2025-05-21 04:31:43,256 - INFO - 当前日期 2025-05-20 有 30 条MySQL数据需要处理
2025-05-21 04:31:43,256 - INFO - 开始批量插入 1 条新记录
2025-05-21 04:31:43,421 - INFO - 批量插入响应状态码: 200
2025-05-21 04:31:43,422 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 20 May 2025 20:31:42 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '9CDB9AD0-E6F8-7739-9671-E979B3BE8C2C', 'x-acs-trace-id': '1fdf3ed41532c631776f0d4b5e35ce91', 'etag': '6RKib4qdskCO7pMCO23iKvg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-21 04:31:43,422 - INFO - 批量插入响应体: {'result': ['FINST-4OD66CC1N9LVVBHYAJTLTCOYSTHO2XN8ZYWAME5']}
2025-05-21 04:31:43,422 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-21 04:31:43,422 - INFO - 成功插入的数据ID: ['FINST-4OD66CC1N9LVVBHYAJTLTCOYSTHO2XN8ZYWAME5']
2025-05-21 04:31:48,423 - INFO - 批量插入完成，共 1 条记录
2025-05-21 04:31:48,423 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-05-21 04:31:48,423 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 0 条
2025-05-21 04:31:48,423 - INFO - 同步完成
2025-05-21 05:30:34,286 - INFO - 使用默认增量同步（当天更新数据）
2025-05-21 05:30:34,286 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 05:30:34,287 - INFO - 查询参数: ('2025-05-21',)
2025-05-21 05:30:34,348 - INFO - MySQL查询成功，增量数据（日期: 2025-05-21），共获取 1 条记录
2025-05-21 05:30:34,348 - INFO - 获取到 1 个日期需要处理: ['2025-05-20']
2025-05-21 05:30:34,348 - INFO - 开始处理日期: 2025-05-20
2025-05-21 05:30:34,351 - INFO - Request Parameters - Page 1:
2025-05-21 05:30:34,351 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 05:30:34,351 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 05:30:42,468 - ERROR - 处理日期 2025-05-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F4524DC8-E6C6-7305-9E67-46992C3E56A8 Response: {'code': 'ServiceUnavailable', 'requestid': 'F4524DC8-E6C6-7305-9E67-46992C3E56A8', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F4524DC8-E6C6-7305-9E67-46992C3E56A8)
2025-05-21 05:30:42,468 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-21 05:31:42,556 - INFO - 开始同步昨天与今天的销售数据: 2025-05-20 至 2025-05-21
2025-05-21 05:31:42,556 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 05:31:42,556 - INFO - 查询参数: ('2025-05-20', '2025-05-21')
2025-05-21 05:31:42,617 - INFO - MySQL查询成功，时间段: 2025-05-20 至 2025-05-21，共获取 30 条记录
2025-05-21 05:31:42,617 - INFO - 获取到 1 个日期需要处理: ['2025-05-20']
2025-05-21 05:31:42,617 - INFO - 开始处理日期: 2025-05-20
2025-05-21 05:31:42,617 - INFO - Request Parameters - Page 1:
2025-05-21 05:31:42,618 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 05:31:42,618 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 05:31:43,278 - INFO - Response - Page 1:
2025-05-21 05:31:43,278 - INFO - 第 1 页获取到 30 条记录
2025-05-21 05:31:43,479 - INFO - 查询完成，共获取到 30 条记录
2025-05-21 05:31:43,479 - INFO - 获取到 30 条表单数据
2025-05-21 05:31:43,480 - INFO - 当前日期 2025-05-20 有 30 条MySQL数据需要处理
2025-05-21 05:31:43,480 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 05:31:43,480 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 05:31:43,480 - INFO - 同步完成
2025-05-21 06:30:34,207 - INFO - 使用默认增量同步（当天更新数据）
2025-05-21 06:30:34,207 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 06:30:34,207 - INFO - 查询参数: ('2025-05-21',)
2025-05-21 06:30:34,269 - INFO - MySQL查询成功，增量数据（日期: 2025-05-21），共获取 1 条记录
2025-05-21 06:30:34,269 - INFO - 获取到 1 个日期需要处理: ['2025-05-20']
2025-05-21 06:30:34,269 - INFO - 开始处理日期: 2025-05-20
2025-05-21 06:30:34,272 - INFO - Request Parameters - Page 1:
2025-05-21 06:30:34,272 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:30:34,272 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:30:42,392 - ERROR - 处理日期 2025-05-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 05684B7C-FB94-7936-B030-F27AB3205D63 Response: {'code': 'ServiceUnavailable', 'requestid': '05684B7C-FB94-7936-B030-F27AB3205D63', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 05684B7C-FB94-7936-B030-F27AB3205D63)
2025-05-21 06:30:42,392 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-21 06:31:42,393 - INFO - 开始同步昨天与今天的销售数据: 2025-05-20 至 2025-05-21
2025-05-21 06:31:42,393 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 06:31:42,393 - INFO - 查询参数: ('2025-05-20', '2025-05-21')
2025-05-21 06:31:42,453 - INFO - MySQL查询成功，时间段: 2025-05-20 至 2025-05-21，共获取 30 条记录
2025-05-21 06:31:42,454 - INFO - 获取到 1 个日期需要处理: ['2025-05-20']
2025-05-21 06:31:42,454 - INFO - 开始处理日期: 2025-05-20
2025-05-21 06:31:42,454 - INFO - Request Parameters - Page 1:
2025-05-21 06:31:42,454 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 06:31:42,454 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 06:31:43,169 - INFO - Response - Page 1:
2025-05-21 06:31:43,170 - INFO - 第 1 页获取到 30 条记录
2025-05-21 06:31:43,370 - INFO - 查询完成，共获取到 30 条记录
2025-05-21 06:31:43,370 - INFO - 获取到 30 条表单数据
2025-05-21 06:31:43,371 - INFO - 当前日期 2025-05-20 有 30 条MySQL数据需要处理
2025-05-21 06:31:43,371 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 06:31:43,371 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 06:31:43,371 - INFO - 同步完成
2025-05-21 07:30:34,201 - INFO - 使用默认增量同步（当天更新数据）
2025-05-21 07:30:34,201 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 07:30:34,201 - INFO - 查询参数: ('2025-05-21',)
2025-05-21 07:30:34,263 - INFO - MySQL查询成功，增量数据（日期: 2025-05-21），共获取 1 条记录
2025-05-21 07:30:34,263 - INFO - 获取到 1 个日期需要处理: ['2025-05-20']
2025-05-21 07:30:34,263 - INFO - 开始处理日期: 2025-05-20
2025-05-21 07:30:34,266 - INFO - Request Parameters - Page 1:
2025-05-21 07:30:34,266 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 07:30:34,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 07:30:42,379 - ERROR - 处理日期 2025-05-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F49261C0-328D-76C4-BAAF-B7A7EEFD3FA8 Response: {'code': 'ServiceUnavailable', 'requestid': 'F49261C0-328D-76C4-BAAF-B7A7EEFD3FA8', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F49261C0-328D-76C4-BAAF-B7A7EEFD3FA8)
2025-05-21 07:30:42,379 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-21 07:31:42,380 - INFO - 开始同步昨天与今天的销售数据: 2025-05-20 至 2025-05-21
2025-05-21 07:31:42,380 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 07:31:42,380 - INFO - 查询参数: ('2025-05-20', '2025-05-21')
2025-05-21 07:31:42,444 - INFO - MySQL查询成功，时间段: 2025-05-20 至 2025-05-21，共获取 30 条记录
2025-05-21 07:31:42,444 - INFO - 获取到 1 个日期需要处理: ['2025-05-20']
2025-05-21 07:31:42,444 - INFO - 开始处理日期: 2025-05-20
2025-05-21 07:31:42,444 - INFO - Request Parameters - Page 1:
2025-05-21 07:31:42,444 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 07:31:42,444 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 07:31:43,145 - INFO - Response - Page 1:
2025-05-21 07:31:43,145 - INFO - 第 1 页获取到 30 条记录
2025-05-21 07:31:43,345 - INFO - 查询完成，共获取到 30 条记录
2025-05-21 07:31:43,345 - INFO - 获取到 30 条表单数据
2025-05-21 07:31:43,346 - INFO - 当前日期 2025-05-20 有 30 条MySQL数据需要处理
2025-05-21 07:31:43,346 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 07:31:43,346 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 07:31:43,346 - INFO - 同步完成
2025-05-21 08:30:34,275 - INFO - 使用默认增量同步（当天更新数据）
2025-05-21 08:30:34,275 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 08:30:34,275 - INFO - 查询参数: ('2025-05-21',)
2025-05-21 08:30:34,337 - INFO - MySQL查询成功，增量数据（日期: 2025-05-21），共获取 11 条记录
2025-05-21 08:30:34,337 - INFO - 获取到 1 个日期需要处理: ['2025-05-20']
2025-05-21 08:30:34,338 - INFO - 开始处理日期: 2025-05-20
2025-05-21 08:30:34,340 - INFO - Request Parameters - Page 1:
2025-05-21 08:30:34,340 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:30:34,340 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:30:42,447 - ERROR - 处理日期 2025-05-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: ********-2607-7E14-B277-C72F9DF350B3 Response: {'code': 'ServiceUnavailable', 'requestid': '********-2607-7E14-B277-C72F9DF350B3', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: ********-2607-7E14-B277-C72F9DF350B3)
2025-05-21 08:30:42,447 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-21 08:31:42,447 - INFO - 开始同步昨天与今天的销售数据: 2025-05-20 至 2025-05-21
2025-05-21 08:31:42,447 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 08:31:42,447 - INFO - 查询参数: ('2025-05-20', '2025-05-21')
2025-05-21 08:31:42,512 - INFO - MySQL查询成功，时间段: 2025-05-20 至 2025-05-21，共获取 91 条记录
2025-05-21 08:31:42,512 - INFO - 获取到 1 个日期需要处理: ['2025-05-20']
2025-05-21 08:31:42,513 - INFO - 开始处理日期: 2025-05-20
2025-05-21 08:31:42,513 - INFO - Request Parameters - Page 1:
2025-05-21 08:31:42,513 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 08:31:42,513 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 08:31:50,621 - ERROR - 处理日期 2025-05-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 1898B1CE-84DA-746D-AB2D-E6F82A69C798 Response: {'code': 'ServiceUnavailable', 'requestid': '1898B1CE-84DA-746D-AB2D-E6F82A69C798', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 1898B1CE-84DA-746D-AB2D-E6F82A69C798)
2025-05-21 08:31:50,621 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-21 08:31:50,621 - INFO - 同步完成
2025-05-21 09:30:34,287 - INFO - 使用默认增量同步（当天更新数据）
2025-05-21 09:30:34,287 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 09:30:34,287 - INFO - 查询参数: ('2025-05-21',)
2025-05-21 09:30:34,354 - INFO - MySQL查询成功，增量数据（日期: 2025-05-21），共获取 104 条记录
2025-05-21 09:30:34,355 - INFO - 获取到 1 个日期需要处理: ['2025-05-20']
2025-05-21 09:30:34,356 - INFO - 开始处理日期: 2025-05-20
2025-05-21 09:30:34,360 - INFO - Request Parameters - Page 1:
2025-05-21 09:30:34,360 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:30:34,360 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:30:42,478 - ERROR - 处理日期 2025-05-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 390B8134-DFD3-70FA-B8D2-378EDD98A627 Response: {'code': 'ServiceUnavailable', 'requestid': '390B8134-DFD3-70FA-B8D2-378EDD98A627', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 390B8134-DFD3-70FA-B8D2-378EDD98A627)
2025-05-21 09:30:42,478 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-21 09:31:42,478 - INFO - 开始同步昨天与今天的销售数据: 2025-05-20 至 2025-05-21
2025-05-21 09:31:42,479 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 09:31:42,479 - INFO - 查询参数: ('2025-05-20', '2025-05-21')
2025-05-21 09:31:42,554 - INFO - MySQL查询成功，时间段: 2025-05-20 至 2025-05-21，共获取 447 条记录
2025-05-21 09:31:42,554 - INFO - 获取到 1 个日期需要处理: ['2025-05-20']
2025-05-21 09:31:42,558 - INFO - 开始处理日期: 2025-05-20
2025-05-21 09:31:42,558 - INFO - Request Parameters - Page 1:
2025-05-21 09:31:42,558 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 09:31:42,558 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 09:31:43,242 - INFO - Response - Page 1:
2025-05-21 09:31:43,242 - INFO - 第 1 页获取到 30 条记录
2025-05-21 09:31:43,442 - INFO - 查询完成，共获取到 30 条记录
2025-05-21 09:31:43,442 - INFO - 获取到 30 条表单数据
2025-05-21 09:31:43,443 - INFO - 当前日期 2025-05-20 有 447 条MySQL数据需要处理
2025-05-21 09:31:43,447 - INFO - 开始批量插入 417 条新记录
2025-05-21 09:31:43,753 - INFO - 批量插入响应状态码: 200
2025-05-21 09:31:43,753 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 21 May 2025 01:31:47 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4779', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '966BDAFC-9D89-7C8F-BC82-D2F280744A6C', 'x-acs-trace-id': '70d657d92893e617efaaa101b72fecb7', 'etag': '4fLTOsfC5xD6D+2g3EaAajw9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-21 09:31:43,753 - INFO - 批量插入响应体: {'result': ['FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM3', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM4', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM5', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM6', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM7', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM8', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM9', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMA', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMB', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMC', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMD', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAME', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMF', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMG', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMH', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMI', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMJ', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMK', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAML', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMM', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMN', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMO', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMP', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMQ', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMR', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMS', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMT', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMU', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMV', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMW', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMX', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMY', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMZ', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM01', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM11', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM21', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM31', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM41', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM51', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM61', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM71', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM81', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM91', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMA1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMB1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMC1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMD1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAME1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMF1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMG1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMH1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMI1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMJ1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMK1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAML1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMM1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMN1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMO1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMP1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMQ1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMR1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMS1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMT1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMU1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMV1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMW1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMX1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMY1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMZ1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM02', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM12', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM22', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM32', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM42', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM52', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM62', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM72', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM82', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM92', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMA2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMB2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMC2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMD2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAME2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMF2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMG2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMH2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMI2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMJ2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMK2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAML2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMM2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMN2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMO2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMP2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMQ2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMR2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMS2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMT2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMU2']}
2025-05-21 09:31:43,754 - INFO - 批量插入表单数据成功，批次 1，共 100 条记录
2025-05-21 09:31:43,754 - INFO - 成功插入的数据ID: ['FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM3', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM4', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM5', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM6', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM7', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM8', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM9', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMA', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMB', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMC', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMD', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAME', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMF', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMG', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMH', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMI', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMJ', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMK', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAML', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMM', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMN', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMO', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMP', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMQ', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMR', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMS', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMT', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMU', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMV', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMW', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMX', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMY', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMZ', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM01', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM11', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM21', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM31', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM41', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM51', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM61', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM71', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM81', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM91', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMA1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMB1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMC1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMD1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAME1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMF1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMG1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMH1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMI1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMJ1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMK1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAML1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMM1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMN1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMO1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMP1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMQ1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMR1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMS1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMT1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMU1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMV1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMW1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMX1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMY1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMZ1', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM02', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM12', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM22', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM32', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM42', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM52', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM62', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM72', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM82', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAM92', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMA2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMB2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMC2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMD2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAME2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMF2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMG2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMH2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMI2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMJ2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMK2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAML2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMM2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMN2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMO2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMP2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMQ2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMR2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMS2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMT2', 'FINST-VRA66VA1FALVHTVC9SJFU6YQPOLF3HF5P9XAMU2']
2025-05-21 09:31:49,001 - INFO - 批量插入响应状态码: 200
2025-05-21 09:31:49,001 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 21 May 2025 01:31:52 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '312358EF-4003-76A2-B3BF-EA1A9D4B917E', 'x-acs-trace-id': 'c1e5f43a2d440e4bd15e26195f8e9466', 'etag': '4B6O5+ciTF3kb6oywnOK2sA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-21 09:31:49,001 - INFO - 批量插入响应体: {'result': ['FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMDG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMEG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMFG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMGG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMHG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMIG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMJG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMKG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMLG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMMG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMNG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMOG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMPG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMQG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMRG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMSG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMTG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMUG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMVG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMWG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMXG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMYG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMZG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM0H', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM1H', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM2H', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM3H', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM4H', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM5H', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM6H', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM7H', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM8H', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM9H', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMAH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMBH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMCH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMDH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMEH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMFH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMGH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMHH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMIH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMJH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMKH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMLH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMMH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMNH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMOH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMPH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMQH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMRH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMSH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMTH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMUH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMVH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMWH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMXH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMYH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMZH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM0I', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM1I', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM2I', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM3I', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM4I', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM5I', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM6I', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM7I', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM8I', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM9I', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMAI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMBI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMCI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMDI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMEI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMFI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMGI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMHI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMII', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMJI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMKI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMLI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMMI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMNI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMOI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMPI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMQI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMRI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMSI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMTI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMUI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMVI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMWI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMXI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMYI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMZI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM0J', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM1J', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM2J', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13GH9P9XAM3J', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13GH9P9XAM4J']}
2025-05-21 09:31:49,001 - INFO - 批量插入表单数据成功，批次 2，共 100 条记录
2025-05-21 09:31:49,001 - INFO - 成功插入的数据ID: ['FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMDG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMEG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMFG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMGG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMHG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMIG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMJG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMKG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMLG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMMG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMNG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMOG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMPG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMQG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMRG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMSG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMTG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMUG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMVG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMWG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMXG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMYG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMZG', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM0H', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM1H', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM2H', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM3H', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM4H', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM5H', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM6H', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM7H', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM8H', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM9H', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMAH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMBH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMCH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMDH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMEH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMFH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMGH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMHH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMIH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMJH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMKH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMLH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMMH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMNH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMOH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMPH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMQH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMRH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMSH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMTH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMUH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMVH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMWH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMXH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMYH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMZH', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM0I', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM1I', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM2I', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM3I', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM4I', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM5I', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM6I', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM7I', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM8I', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM9I', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMAI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMBI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMCI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMDI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMEI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMFI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMGI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMHI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMII', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMJI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMKI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMLI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMMI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMNI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMOI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMPI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMQI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMRI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMSI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMTI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMUI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMVI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMWI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMXI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMYI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAMZI', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM0J', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM1J', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13FH9P9XAM2J', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13GH9P9XAM3J', 'FINST-OIF66RB1TBHVJBI38V2I59CML0O13GH9P9XAM4J']
2025-05-21 09:31:54,271 - INFO - 批量插入响应状态码: 200
2025-05-21 09:31:54,271 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 21 May 2025 01:31:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D99A8ADA-E3F7-75A7-8882-74F9FF2DE05B', 'x-acs-trace-id': '031971b99777ca0739cb6ec406cebad1', 'etag': '4OQlElFtQ8pKCM0jzEVvICQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-21 09:31:54,272 - INFO - 批量插入响应体: {'result': ['FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMO2', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMP2', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMQ2', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMR2', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMS2', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMT2', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMU2', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMV2', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMW2', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMX2', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMY2', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMZ2', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM03', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM13', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM23', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM33', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM43', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM53', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM63', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM73', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM83', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM93', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMA3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMB3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMC3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMD3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAME3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMF3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMG3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMH3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMI3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMJ3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMK3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAML3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMM3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMN3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMO3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMP3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMQ3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMR3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMS3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMT3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMU3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMV3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMW3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMX3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMY3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMZ3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM04', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM14', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM24', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM34', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM44', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM54', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM64', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM74', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM84', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM94', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMA4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMB4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMC4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMD4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAME4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMF4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMG4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMH4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMI4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMJ4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMK4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAML4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMM4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMN4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMO4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMP4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMQ4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMR4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMS4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMT4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMU4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMV4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMW4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMX4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMY4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMZ4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM05', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM15', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM25', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM35', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM45', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM55', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM65', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM75', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM85', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM95', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMA5', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMB5', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMC5', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMD5', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAME5', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMF5']}
2025-05-21 09:31:54,272 - INFO - 批量插入表单数据成功，批次 3，共 100 条记录
2025-05-21 09:31:54,272 - INFO - 成功插入的数据ID: ['FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMO2', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMP2', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMQ2', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMR2', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMS2', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMT2', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMU2', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMV2', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMW2', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMX2', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMY2', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMZ2', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM03', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM13', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM23', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM33', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM43', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM53', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM63', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM73', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM83', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM93', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMA3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMB3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMC3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMD3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAME3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMF3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMG3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMH3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMI3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMJ3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMK3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAML3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMM3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMN3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMO3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMP3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMQ3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMR3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMS3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMT3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMU3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMV3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMW3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMX3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMY3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMZ3', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM04', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM14', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM24', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM34', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM44', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM54', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM64', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM74', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM84', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM94', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMA4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMB4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMC4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMD4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAME4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMF4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMG4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMH4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMI4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMJ4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMK4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAML4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMM4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMN4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMO4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMP4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMQ4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMR4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMS4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMT4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMU4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMV4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMW4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMX4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMY4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMZ4', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM05', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM15', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM25', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM35', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM45', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM55', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM65', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM75', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM85', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAM95', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMA5', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMB5', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMC5', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMD5', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAME5', 'FINST-GNC66E91D1LV5SOO6JA9Q5MBN6VB2WJDP9XAMF5']
2025-05-21 09:31:59,533 - INFO - 批量插入响应状态码: 200
2025-05-21 09:31:59,533 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 21 May 2025 01:32:03 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'DEDDED98-0B4E-7FE2-924E-9A1D64BB9BCF', 'x-acs-trace-id': 'c80aaf5befd2ec1d09c932611f8fec77', 'etag': '4zFbo6wFjZ18lL58azoCS1g2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-21 09:31:59,533 - INFO - 批量插入响应体: {'result': ['FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMQ8', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMR8', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMS8', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMT8', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMU8', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMV8', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMW8', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMX8', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMY8', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMZ8', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM09', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM19', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM29', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM39', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM49', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM59', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM69', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM79', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM89', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM99', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMA9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMB9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMC9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMD9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAME9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMF9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMG9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMH9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMI9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMJ9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMK9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAML9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMM9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMN9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMO9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMP9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMQ9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMR9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMS9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMT9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMU9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMV9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMW9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMX9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMY9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMZ9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM0A', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM1A', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM2A', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM3A', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM4A', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM5A', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM6A', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM7A', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM8A', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM9A', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMAA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMBA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMCA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMDA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMEA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMFA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMGA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMHA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMIA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMJA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMKA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMLA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMMA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMNA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMOA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMPA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMQA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMRA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMSA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMTA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMUA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMVA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMWA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMXA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMYA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMZA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM0B', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM1B', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM2B', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM3B', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM4B', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM5B', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM6B', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM7B', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM8B', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM9B', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMAB', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMBB', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMCB', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMDB', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMEB', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMFB', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMGB', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMHB']}
2025-05-21 09:31:59,533 - INFO - 批量插入表单数据成功，批次 4，共 100 条记录
2025-05-21 09:31:59,534 - INFO - 成功插入的数据ID: ['FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMQ8', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMR8', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMS8', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMT8', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMU8', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMV8', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMW8', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMX8', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMY8', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMZ8', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM09', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM19', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM29', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM39', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM49', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM59', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM69', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM79', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM89', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM99', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMA9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMB9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMC9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMD9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAME9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMF9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMG9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMH9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMI9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMJ9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMK9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAML9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMM9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMN9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMO9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMP9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMQ9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMR9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMS9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMT9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMU9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMV9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMW9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMX9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMY9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMZ9', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM0A', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM1A', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM2A', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM3A', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM4A', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM5A', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM6A', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM7A', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM8A', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM9A', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMAA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMBA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMCA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMDA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMEA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMFA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMGA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMHA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMIA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMJA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMKA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMLA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMMA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMNA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMOA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMPA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMQA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMRA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMSA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMTA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMUA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMVA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMWA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMXA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMYA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMZA', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM0B', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM1B', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM2B', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM3B', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM4B', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM5B', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM6B', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM7B', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM8B', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAM9B', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMAB', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMBB', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMCB', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMDB', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMEB', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMFB', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMGB', 'FINST-FQD66YB1IBLVB22BAH2PI5456P8C3XLHP9XAMHB']
2025-05-21 09:32:04,708 - INFO - 批量插入响应状态码: 200
2025-05-21 09:32:04,708 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 21 May 2025 01:32:08 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '828', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '1D626B2F-C05D-765B-B773-87D5641A0338', 'x-acs-trace-id': 'ecd93f797412a4cd9ea5af55529d3a3d', 'etag': '8jbvfIagMceaMEWUfte3Cgw8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-21 09:32:04,709 - INFO - 批量插入响应体: {'result': ['FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAMV3', 'FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAMW3', 'FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAMX3', 'FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAMY3', 'FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAMZ3', 'FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAM04', 'FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAM14', 'FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAM24', 'FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAM34', 'FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAM44', 'FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAM54', 'FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAM64', 'FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAM74', 'FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAM84', 'FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAM94', 'FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAMA4', 'FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAMB4']}
2025-05-21 09:32:04,709 - INFO - 批量插入表单数据成功，批次 5，共 17 条记录
2025-05-21 09:32:04,709 - INFO - 成功插入的数据ID: ['FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAMV3', 'FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAMW3', 'FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAMX3', 'FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAMY3', 'FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAMZ3', 'FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAM04', 'FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAM14', 'FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAM24', 'FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAM34', 'FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAM44', 'FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAM54', 'FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAM64', 'FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAM74', 'FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAM84', 'FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAM94', 'FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAMA4', 'FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAMB4']
2025-05-21 09:32:09,710 - INFO - 批量插入完成，共 417 条记录
2025-05-21 09:32:09,710 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 417 条，错误: 0 条
2025-05-21 09:32:09,710 - INFO - 数据同步完成！更新: 0 条，插入: 417 条，错误: 0 条
2025-05-21 09:32:09,710 - INFO - 同步完成
2025-05-21 10:30:34,077 - INFO - 使用默认增量同步（当天更新数据）
2025-05-21 10:30:34,078 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 10:30:34,078 - INFO - 查询参数: ('2025-05-21',)
2025-05-21 10:30:34,150 - INFO - MySQL查询成功，增量数据（日期: 2025-05-21），共获取 188 条记录
2025-05-21 10:30:34,151 - INFO - 获取到 2 个日期需要处理: ['2025-05-19', '2025-05-20']
2025-05-21 10:30:34,151 - INFO - 开始处理日期: 2025-05-19
2025-05-21 10:30:34,154 - INFO - Request Parameters - Page 1:
2025-05-21 10:30:34,155 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 10:30:34,155 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 10:30:42,274 - ERROR - 处理日期 2025-05-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 0C707B93-C9DF-7740-9EA2-996043EC9EE2 Response: {'code': 'ServiceUnavailable', 'requestid': '0C707B93-C9DF-7740-9EA2-996043EC9EE2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 0C707B93-C9DF-7740-9EA2-996043EC9EE2)
2025-05-21 10:30:42,274 - INFO - 开始处理日期: 2025-05-20
2025-05-21 10:30:42,274 - INFO - Request Parameters - Page 1:
2025-05-21 10:30:42,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 10:30:42,274 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 10:30:50,397 - ERROR - 处理日期 2025-05-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 23C28D6C-D1FE-714E-AC1C-23B2EAEC93D0 Response: {'code': 'ServiceUnavailable', 'requestid': '23C28D6C-D1FE-714E-AC1C-23B2EAEC93D0', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 23C28D6C-D1FE-714E-AC1C-23B2EAEC93D0)
2025-05-21 10:30:50,398 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-21 10:31:50,399 - INFO - 开始同步昨天与今天的销售数据: 2025-05-20 至 2025-05-21
2025-05-21 10:31:50,399 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 10:31:50,399 - INFO - 查询参数: ('2025-05-20', '2025-05-21')
2025-05-21 10:31:50,478 - INFO - MySQL查询成功，时间段: 2025-05-20 至 2025-05-21，共获取 530 条记录
2025-05-21 10:31:50,479 - INFO - 获取到 1 个日期需要处理: ['2025-05-20']
2025-05-21 10:31:50,483 - INFO - 开始处理日期: 2025-05-20
2025-05-21 10:31:50,484 - INFO - Request Parameters - Page 1:
2025-05-21 10:31:50,484 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 10:31:50,484 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 10:31:51,439 - INFO - Response - Page 1:
2025-05-21 10:31:51,439 - INFO - 第 1 页获取到 100 条记录
2025-05-21 10:31:51,639 - INFO - Request Parameters - Page 2:
2025-05-21 10:31:51,639 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 10:31:51,639 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 10:31:52,542 - INFO - Response - Page 2:
2025-05-21 10:31:52,543 - INFO - 第 2 页获取到 100 条记录
2025-05-21 10:31:52,743 - INFO - Request Parameters - Page 3:
2025-05-21 10:31:52,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 10:31:52,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 10:31:53,618 - INFO - Response - Page 3:
2025-05-21 10:31:53,618 - INFO - 第 3 页获取到 100 条记录
2025-05-21 10:31:53,818 - INFO - Request Parameters - Page 4:
2025-05-21 10:31:53,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 10:31:53,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 10:31:54,641 - INFO - Response - Page 4:
2025-05-21 10:31:54,641 - INFO - 第 4 页获取到 100 条记录
2025-05-21 10:31:54,842 - INFO - Request Parameters - Page 5:
2025-05-21 10:31:54,842 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 10:31:54,842 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 10:31:55,581 - INFO - Response - Page 5:
2025-05-21 10:31:55,581 - INFO - 第 5 页获取到 47 条记录
2025-05-21 10:31:55,782 - INFO - 查询完成，共获取到 447 条记录
2025-05-21 10:31:55,782 - INFO - 获取到 447 条表单数据
2025-05-21 10:31:55,790 - INFO - 当前日期 2025-05-20 有 530 条MySQL数据需要处理
2025-05-21 10:31:55,798 - INFO - 开始批量插入 83 条新记录
2025-05-21 10:31:56,068 - INFO - 批量插入响应状态码: 200
2025-05-21 10:31:56,068 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 21 May 2025 02:31:59 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '3996', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'FFAE0EDD-25C2-75A6-9A96-AACDB6F85700', 'x-acs-trace-id': '395d7f1ebee5fa884df9d22b671946b4', 'etag': '33YmY26z/gZxE2qBGhVJDhQ6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-21 10:31:56,068 - INFO - 批量插入响应体: {'result': ['FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMAB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMBB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMCB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMDB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMEB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMFB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMGB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMHB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMIB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMJB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMKB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMLB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMMB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMNB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMOB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMPB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMQB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMRB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMSB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMTB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMUB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMVB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMWB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMXB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMYB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMZB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAM0C', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAM1C', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAM2C', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAM3C', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAM4C', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAM5C', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAM6C', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAM7C', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAM8C', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAM9C', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMAC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMBC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMCC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMDC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMEC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMFC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMGC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMHC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMIC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMJC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMKC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMLC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMMC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMNC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMOC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMPC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMQC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMRC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMSC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMTC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMUC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMVC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMWC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMXC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMYC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMZC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAM0D', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAM1D', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAM2D', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAM3D', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAM4D', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAM5D', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAM6D', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAM7D', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAM8D', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAM9D', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMAD', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMBD', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMCD', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMDD', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMED', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMFD', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMGD', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMHD', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMID', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMJD', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMKD']}
2025-05-21 10:31:56,068 - INFO - 批量插入表单数据成功，批次 1，共 83 条记录
2025-05-21 10:31:56,068 - INFO - 成功插入的数据ID: ['FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMAB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMBB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMCB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMDB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMEB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMFB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMGB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMHB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMIB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMJB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMKB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMLB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMMB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMNB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMOB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMPB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMQB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMRB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMSB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMTB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMUB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMVB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMWB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMXB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMYB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMZB', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAM0C', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAM1C', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAM2C', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAM3C', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAM4C', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAM5C', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAM6C', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAM7C', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAM8C', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAM9C', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMAC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMBC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMCC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMDC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMEC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMFC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMGC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMHC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMIC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMJC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMKC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMLC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMMC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMNC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMOC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMPC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMQC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMRC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMSC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMTC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMUC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMVC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMWC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMXC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMYC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMZC', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAM0D', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAM1D', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAM2D', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAM3D', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAM4D', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAM5D', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAM6D', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAM7D', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAM8D', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAM9D', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMAD', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMBD', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMCD', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMDD', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMED', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMFD', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMGD', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMHD', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMID', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMJD', 'FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMKD']
2025-05-21 10:32:01,069 - INFO - 批量插入完成，共 83 条记录
2025-05-21 10:32:01,069 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 83 条，错误: 0 条
2025-05-21 10:32:01,069 - INFO - 数据同步完成！更新: 0 条，插入: 83 条，错误: 0 条
2025-05-21 10:32:01,069 - INFO - 同步完成
2025-05-21 11:30:34,247 - INFO - 使用默认增量同步（当天更新数据）
2025-05-21 11:30:34,248 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 11:30:34,248 - INFO - 查询参数: ('2025-05-21',)
2025-05-21 11:30:34,320 - INFO - MySQL查询成功，增量数据（日期: 2025-05-21），共获取 198 条记录
2025-05-21 11:30:34,320 - INFO - 获取到 2 个日期需要处理: ['2025-05-19', '2025-05-20']
2025-05-21 11:30:34,321 - INFO - 开始处理日期: 2025-05-19
2025-05-21 11:30:34,324 - INFO - Request Parameters - Page 1:
2025-05-21 11:30:34,324 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 11:30:34,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 11:30:42,467 - ERROR - 处理日期 2025-05-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 67C1FA34-12B4-764A-B498-056824B6343B Response: {'code': 'ServiceUnavailable', 'requestid': '67C1FA34-12B4-764A-B498-056824B6343B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 67C1FA34-12B4-764A-B498-056824B6343B)
2025-05-21 11:30:42,467 - INFO - 开始处理日期: 2025-05-20
2025-05-21 11:30:42,467 - INFO - Request Parameters - Page 1:
2025-05-21 11:30:42,467 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 11:30:42,467 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 11:30:50,576 - ERROR - 处理日期 2025-05-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: DE7FA118-407E-753C-8663-7C7C752D24EE Response: {'code': 'ServiceUnavailable', 'requestid': 'DE7FA118-407E-753C-8663-7C7C752D24EE', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: DE7FA118-407E-753C-8663-7C7C752D24EE)
2025-05-21 11:30:50,576 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-21 11:31:50,577 - INFO - 开始同步昨天与今天的销售数据: 2025-05-20 至 2025-05-21
2025-05-21 11:31:50,577 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 11:31:50,577 - INFO - 查询参数: ('2025-05-20', '2025-05-21')
2025-05-21 11:31:50,658 - INFO - MySQL查询成功，时间段: 2025-05-20 至 2025-05-21，共获取 559 条记录
2025-05-21 11:31:50,659 - INFO - 获取到 1 个日期需要处理: ['2025-05-20']
2025-05-21 11:31:50,663 - INFO - 开始处理日期: 2025-05-20
2025-05-21 11:31:50,664 - INFO - Request Parameters - Page 1:
2025-05-21 11:31:50,664 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 11:31:50,664 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 11:31:51,580 - INFO - Response - Page 1:
2025-05-21 11:31:51,581 - INFO - 第 1 页获取到 100 条记录
2025-05-21 11:31:51,782 - INFO - Request Parameters - Page 2:
2025-05-21 11:31:51,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 11:31:51,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 11:31:52,630 - INFO - Response - Page 2:
2025-05-21 11:31:52,631 - INFO - 第 2 页获取到 100 条记录
2025-05-21 11:31:52,831 - INFO - Request Parameters - Page 3:
2025-05-21 11:31:52,831 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 11:31:52,831 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 11:31:53,734 - INFO - Response - Page 3:
2025-05-21 11:31:53,735 - INFO - 第 3 页获取到 100 条记录
2025-05-21 11:31:53,935 - INFO - Request Parameters - Page 4:
2025-05-21 11:31:53,935 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 11:31:53,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 11:31:54,967 - INFO - Response - Page 4:
2025-05-21 11:31:54,968 - INFO - 第 4 页获取到 100 条记录
2025-05-21 11:31:55,168 - INFO - Request Parameters - Page 5:
2025-05-21 11:31:55,168 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 11:31:55,168 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 11:31:56,027 - INFO - Response - Page 5:
2025-05-21 11:31:56,027 - INFO - 第 5 页获取到 100 条记录
2025-05-21 11:31:56,227 - INFO - Request Parameters - Page 6:
2025-05-21 11:31:56,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 11:31:56,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 11:31:56,892 - INFO - Response - Page 6:
2025-05-21 11:31:56,892 - INFO - 第 6 页获取到 30 条记录
2025-05-21 11:31:57,092 - INFO - 查询完成，共获取到 530 条记录
2025-05-21 11:31:57,092 - INFO - 获取到 530 条表单数据
2025-05-21 11:31:57,101 - INFO - 当前日期 2025-05-20 有 559 条MySQL数据需要处理
2025-05-21 11:31:57,111 - INFO - 开始更新记录 - 表单实例ID: FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAM84
2025-05-21 11:31:57,629 - INFO - 更新表单数据成功: FINST-CPC66T91NALVB6VQFOXXV707ZH0B3PLLP9XAM84
2025-05-21 11:31:57,629 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3011.0, 'new_value': 3947.0}, {'field': 'total_amount', 'old_value': 3011.0, 'new_value': 3947.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 26}]
2025-05-21 11:31:57,631 - INFO - 开始批量插入 29 条新记录
2025-05-21 11:31:57,835 - INFO - 批量插入响应状态码: 200
2025-05-21 11:31:57,835 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 21 May 2025 03:32:01 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1402', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7DD73555-4366-7E47-B0F4-2F2826BBBAB9', 'x-acs-trace-id': '47e7aa1a8193b1a309752a5706787da5', 'etag': '1WEFmMfRjdvNDvMIE0PNH5A2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-21 11:31:57,835 - INFO - 批量插入响应体: {'result': ['FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAMY', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAMZ', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAM01', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAM11', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAM21', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAM31', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAM41', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAM51', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAM61', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAM71', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAM81', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAM91', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAMA1', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAMB1', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAMC1', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAMD1', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAME1', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAMF1', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAMG1', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAMH1', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAMI1', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAMJ1', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAMK1', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAML1', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAMM1', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAMN1', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAMO1', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAMP1', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAMQ1']}
2025-05-21 11:31:57,836 - INFO - 批量插入表单数据成功，批次 1，共 29 条记录
2025-05-21 11:31:57,836 - INFO - 成功插入的数据ID: ['FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAMY', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAMZ', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAM01', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAM11', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAM21', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAM31', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAM41', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAM51', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAM61', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAM71', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAM81', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAM91', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAMA1', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAMB1', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAMC1', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAMD1', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAME1', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAMF1', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAMG1', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAMH1', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAMI1', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAMJ1', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAMK1', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAML1', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAMM1', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAMN1', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAMO1', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAMP1', 'FINST-737662B1VCKV3GDZ9VU3NAHHDRHY28WRZDXAMQ1']
2025-05-21 11:32:02,837 - INFO - 批量插入完成，共 29 条记录
2025-05-21 11:32:02,837 - INFO - 日期 2025-05-20 处理完成 - 更新: 1 条，插入: 29 条，错误: 0 条
2025-05-21 11:32:02,837 - INFO - 数据同步完成！更新: 1 条，插入: 29 条，错误: 0 条
2025-05-21 11:32:02,838 - INFO - 同步完成
2025-05-21 12:30:35,240 - INFO - 使用默认增量同步（当天更新数据）
2025-05-21 12:30:35,240 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 12:30:35,240 - INFO - 查询参数: ('2025-05-21',)
2025-05-21 12:30:35,311 - INFO - MySQL查询成功，增量数据（日期: 2025-05-21），共获取 199 条记录
2025-05-21 12:30:35,311 - INFO - 获取到 2 个日期需要处理: ['2025-05-19', '2025-05-20']
2025-05-21 12:30:35,313 - INFO - 开始处理日期: 2025-05-19
2025-05-21 12:30:35,316 - INFO - Request Parameters - Page 1:
2025-05-21 12:30:35,316 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:30:35,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:30:43,422 - ERROR - 处理日期 2025-05-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F8AEF1DA-16AD-7DE0-84D6-82F88557B702 Response: {'code': 'ServiceUnavailable', 'requestid': 'F8AEF1DA-16AD-7DE0-84D6-82F88557B702', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F8AEF1DA-16AD-7DE0-84D6-82F88557B702)
2025-05-21 12:30:43,422 - INFO - 开始处理日期: 2025-05-20
2025-05-21 12:30:43,423 - INFO - Request Parameters - Page 1:
2025-05-21 12:30:43,423 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:30:43,423 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:30:51,524 - ERROR - 处理日期 2025-05-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 83C715E4-B63A-7CA4-A7A4-89B51D784FAC Response: {'code': 'ServiceUnavailable', 'requestid': '83C715E4-B63A-7CA4-A7A4-89B51D784FAC', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 83C715E4-B63A-7CA4-A7A4-89B51D784FAC)
2025-05-21 12:30:51,524 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-21 12:31:51,526 - INFO - 开始同步昨天与今天的销售数据: 2025-05-20 至 2025-05-21
2025-05-21 12:31:51,526 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 12:31:51,526 - INFO - 查询参数: ('2025-05-20', '2025-05-21')
2025-05-21 12:31:51,606 - INFO - MySQL查询成功，时间段: 2025-05-20 至 2025-05-21，共获取 560 条记录
2025-05-21 12:31:51,607 - INFO - 获取到 1 个日期需要处理: ['2025-05-20']
2025-05-21 12:31:51,611 - INFO - 开始处理日期: 2025-05-20
2025-05-21 12:31:51,612 - INFO - Request Parameters - Page 1:
2025-05-21 12:31:51,612 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:31:51,612 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:31:52,493 - INFO - Response - Page 1:
2025-05-21 12:31:52,493 - INFO - 第 1 页获取到 100 条记录
2025-05-21 12:31:52,694 - INFO - Request Parameters - Page 2:
2025-05-21 12:31:52,694 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:31:52,694 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:31:53,542 - INFO - Response - Page 2:
2025-05-21 12:31:53,542 - INFO - 第 2 页获取到 100 条记录
2025-05-21 12:31:53,742 - INFO - Request Parameters - Page 3:
2025-05-21 12:31:53,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:31:53,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:31:54,564 - INFO - Response - Page 3:
2025-05-21 12:31:54,564 - INFO - 第 3 页获取到 100 条记录
2025-05-21 12:31:54,765 - INFO - Request Parameters - Page 4:
2025-05-21 12:31:54,765 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:31:54,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:31:55,572 - INFO - Response - Page 4:
2025-05-21 12:31:55,572 - INFO - 第 4 页获取到 100 条记录
2025-05-21 12:31:55,774 - INFO - Request Parameters - Page 5:
2025-05-21 12:31:55,774 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:31:55,774 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:31:56,573 - INFO - Response - Page 5:
2025-05-21 12:31:56,573 - INFO - 第 5 页获取到 100 条记录
2025-05-21 12:31:56,774 - INFO - Request Parameters - Page 6:
2025-05-21 12:31:56,774 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 12:31:56,774 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 12:31:57,509 - INFO - Response - Page 6:
2025-05-21 12:31:57,510 - INFO - 第 6 页获取到 59 条记录
2025-05-21 12:31:57,710 - INFO - 查询完成，共获取到 559 条记录
2025-05-21 12:31:57,710 - INFO - 获取到 559 条表单数据
2025-05-21 12:31:57,721 - INFO - 当前日期 2025-05-20 有 560 条MySQL数据需要处理
2025-05-21 12:31:57,732 - INFO - 开始批量插入 1 条新记录
2025-05-21 12:31:57,891 - INFO - 批量插入响应状态码: 200
2025-05-21 12:31:57,891 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 21 May 2025 04:32:01 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'EE5F1AC9-75D5-7856-8730-A9C80A634C38', 'x-acs-trace-id': '4961f5b8a5c946ae00267854deb25052', 'etag': '5WHe4rlVSBeBD9aPgaeyUOw9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-21 12:31:57,892 - INFO - 批量插入响应体: {'result': ['FINST-2FD66I714BLVTXRL6NBUK5DMJ8HG2FQX4GXAMW']}
2025-05-21 12:31:57,892 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-21 12:31:57,892 - INFO - 成功插入的数据ID: ['FINST-2FD66I714BLVTXRL6NBUK5DMJ8HG2FQX4GXAMW']
2025-05-21 12:32:02,893 - INFO - 批量插入完成，共 1 条记录
2025-05-21 12:32:02,893 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-05-21 12:32:02,893 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 0 条
2025-05-21 12:32:02,894 - INFO - 同步完成
2025-05-21 13:30:34,178 - INFO - 使用默认增量同步（当天更新数据）
2025-05-21 13:30:34,179 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 13:30:34,179 - INFO - 查询参数: ('2025-05-21',)
2025-05-21 13:30:34,251 - INFO - MySQL查询成功，增量数据（日期: 2025-05-21），共获取 199 条记录
2025-05-21 13:30:34,251 - INFO - 获取到 2 个日期需要处理: ['2025-05-19', '2025-05-20']
2025-05-21 13:30:34,252 - INFO - 开始处理日期: 2025-05-19
2025-05-21 13:30:34,255 - INFO - Request Parameters - Page 1:
2025-05-21 13:30:34,255 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 13:30:34,255 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 13:30:42,378 - ERROR - 处理日期 2025-05-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: BE3A33B5-4E19-7556-ACD5-29C697066485 Response: {'code': 'ServiceUnavailable', 'requestid': 'BE3A33B5-4E19-7556-ACD5-29C697066485', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: BE3A33B5-4E19-7556-ACD5-29C697066485)
2025-05-21 13:30:42,378 - INFO - 开始处理日期: 2025-05-20
2025-05-21 13:30:42,378 - INFO - Request Parameters - Page 1:
2025-05-21 13:30:42,378 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 13:30:42,378 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 13:30:50,507 - ERROR - 处理日期 2025-05-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 50062D26-9DB2-7AD8-B63C-814C594845B4 Response: {'code': 'ServiceUnavailable', 'requestid': '50062D26-9DB2-7AD8-B63C-814C594845B4', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 50062D26-9DB2-7AD8-B63C-814C594845B4)
2025-05-21 13:30:50,507 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-21 13:31:50,508 - INFO - 开始同步昨天与今天的销售数据: 2025-05-20 至 2025-05-21
2025-05-21 13:31:50,508 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 13:31:50,508 - INFO - 查询参数: ('2025-05-20', '2025-05-21')
2025-05-21 13:31:50,589 - INFO - MySQL查询成功，时间段: 2025-05-20 至 2025-05-21，共获取 560 条记录
2025-05-21 13:31:50,589 - INFO - 获取到 1 个日期需要处理: ['2025-05-20']
2025-05-21 13:31:50,594 - INFO - 开始处理日期: 2025-05-20
2025-05-21 13:31:50,594 - INFO - Request Parameters - Page 1:
2025-05-21 13:31:50,594 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 13:31:50,595 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 13:31:58,714 - ERROR - 处理日期 2025-05-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 7AD94530-0E45-7565-B42E-30ED23A3EC7A Response: {'code': 'ServiceUnavailable', 'requestid': '7AD94530-0E45-7565-B42E-30ED23A3EC7A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 7AD94530-0E45-7565-B42E-30ED23A3EC7A)
2025-05-21 13:31:58,715 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-21 13:31:58,715 - INFO - 同步完成
2025-05-21 14:30:33,944 - INFO - 使用默认增量同步（当天更新数据）
2025-05-21 14:30:33,944 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 14:30:33,944 - INFO - 查询参数: ('2025-05-21',)
2025-05-21 14:30:34,016 - INFO - MySQL查询成功，增量数据（日期: 2025-05-21），共获取 200 条记录
2025-05-21 14:30:34,016 - INFO - 获取到 2 个日期需要处理: ['2025-05-19', '2025-05-20']
2025-05-21 14:30:34,018 - INFO - 开始处理日期: 2025-05-19
2025-05-21 14:30:34,021 - INFO - Request Parameters - Page 1:
2025-05-21 14:30:34,021 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 14:30:34,021 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 14:30:42,155 - ERROR - 处理日期 2025-05-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 8998FB2D-C938-73F2-8471-7AB43142E040 Response: {'code': 'ServiceUnavailable', 'requestid': '8998FB2D-C938-73F2-8471-7AB43142E040', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 8998FB2D-C938-73F2-8471-7AB43142E040)
2025-05-21 14:30:42,155 - INFO - 开始处理日期: 2025-05-20
2025-05-21 14:30:42,155 - INFO - Request Parameters - Page 1:
2025-05-21 14:30:42,155 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 14:30:42,155 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 14:30:50,262 - ERROR - 处理日期 2025-05-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 88AF3469-A17C-7135-A20A-78A6393A0CBA Response: {'code': 'ServiceUnavailable', 'requestid': '88AF3469-A17C-7135-A20A-78A6393A0CBA', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 88AF3469-A17C-7135-A20A-78A6393A0CBA)
2025-05-21 14:30:50,262 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-21 14:31:50,263 - INFO - 开始同步昨天与今天的销售数据: 2025-05-20 至 2025-05-21
2025-05-21 14:31:50,263 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 14:31:50,263 - INFO - 查询参数: ('2025-05-20', '2025-05-21')
2025-05-21 14:31:50,344 - INFO - MySQL查询成功，时间段: 2025-05-20 至 2025-05-21，共获取 563 条记录
2025-05-21 14:31:50,345 - INFO - 获取到 1 个日期需要处理: ['2025-05-20']
2025-05-21 14:31:50,350 - INFO - 开始处理日期: 2025-05-20
2025-05-21 14:31:50,350 - INFO - Request Parameters - Page 1:
2025-05-21 14:31:50,350 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 14:31:50,350 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 14:31:51,357 - INFO - Response - Page 1:
2025-05-21 14:31:51,357 - INFO - 第 1 页获取到 100 条记录
2025-05-21 14:31:51,559 - INFO - Request Parameters - Page 2:
2025-05-21 14:31:51,559 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 14:31:51,559 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 14:31:52,373 - INFO - Response - Page 2:
2025-05-21 14:31:52,373 - INFO - 第 2 页获取到 100 条记录
2025-05-21 14:31:52,573 - INFO - Request Parameters - Page 3:
2025-05-21 14:31:52,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 14:31:52,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 14:31:53,430 - INFO - Response - Page 3:
2025-05-21 14:31:53,430 - INFO - 第 3 页获取到 100 条记录
2025-05-21 14:31:53,630 - INFO - Request Parameters - Page 4:
2025-05-21 14:31:53,630 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 14:31:53,630 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 14:31:54,499 - INFO - Response - Page 4:
2025-05-21 14:31:54,499 - INFO - 第 4 页获取到 100 条记录
2025-05-21 14:31:54,699 - INFO - Request Parameters - Page 5:
2025-05-21 14:31:54,699 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 14:31:54,699 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 14:31:55,497 - INFO - Response - Page 5:
2025-05-21 14:31:55,497 - INFO - 第 5 页获取到 100 条记录
2025-05-21 14:31:55,698 - INFO - Request Parameters - Page 6:
2025-05-21 14:31:55,698 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 14:31:55,698 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 14:31:56,439 - INFO - Response - Page 6:
2025-05-21 14:31:56,439 - INFO - 第 6 页获取到 60 条记录
2025-05-21 14:31:56,640 - INFO - 查询完成，共获取到 560 条记录
2025-05-21 14:31:56,640 - INFO - 获取到 560 条表单数据
2025-05-21 14:31:56,649 - INFO - 当前日期 2025-05-20 有 563 条MySQL数据需要处理
2025-05-21 14:31:56,658 - INFO - 开始批量插入 3 条新记录
2025-05-21 14:31:56,807 - INFO - 批量插入响应状态码: 200
2025-05-21 14:31:56,807 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 21 May 2025 06:32:00 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '156', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4B029043-54D4-7A41-A128-1582425CA175', 'x-acs-trace-id': '088c919331677d670b62fb0c818a96f4', 'etag': '1LQQbt4uIo8KdclWuMlufLg6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-21 14:31:56,807 - INFO - 批量插入响应体: {'result': ['FINST-KLF66WC1X9LVRR1J9DPRX4ABZP952NH8FKXAMD4', 'FINST-KLF66WC1X9LVRR1J9DPRX4ABZP952NH8FKXAME4', 'FINST-KLF66WC1X9LVRR1J9DPRX4ABZP952NH8FKXAMF4']}
2025-05-21 14:31:56,808 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-05-21 14:31:56,808 - INFO - 成功插入的数据ID: ['FINST-KLF66WC1X9LVRR1J9DPRX4ABZP952NH8FKXAMD4', 'FINST-KLF66WC1X9LVRR1J9DPRX4ABZP952NH8FKXAME4', 'FINST-KLF66WC1X9LVRR1J9DPRX4ABZP952NH8FKXAMF4']
2025-05-21 14:32:01,808 - INFO - 批量插入完成，共 3 条记录
2025-05-21 14:32:01,808 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 3 条，错误: 0 条
2025-05-21 14:32:01,808 - INFO - 数据同步完成！更新: 0 条，插入: 3 条，错误: 0 条
2025-05-21 14:32:01,809 - INFO - 同步完成
2025-05-21 15:30:34,251 - INFO - 使用默认增量同步（当天更新数据）
2025-05-21 15:30:34,252 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 15:30:34,252 - INFO - 查询参数: ('2025-05-21',)
2025-05-21 15:30:34,324 - INFO - MySQL查询成功，增量数据（日期: 2025-05-21），共获取 206 条记录
2025-05-21 15:30:34,324 - INFO - 获取到 3 个日期需要处理: ['2025-05-19', '2025-05-20', '2025-05-21']
2025-05-21 15:30:34,326 - INFO - 开始处理日期: 2025-05-19
2025-05-21 15:30:34,329 - INFO - Request Parameters - Page 1:
2025-05-21 15:30:34,329 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:30:34,329 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:30:42,443 - ERROR - 处理日期 2025-05-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 263D084E-897F-74FE-B9E0-7F0DEC2C2509 Response: {'code': 'ServiceUnavailable', 'requestid': '263D084E-897F-74FE-B9E0-7F0DEC2C2509', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 263D084E-897F-74FE-B9E0-7F0DEC2C2509)
2025-05-21 15:30:42,443 - INFO - 开始处理日期: 2025-05-20
2025-05-21 15:30:42,444 - INFO - Request Parameters - Page 1:
2025-05-21 15:30:42,444 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:30:42,444 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:30:50,573 - ERROR - 处理日期 2025-05-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: ACB9C10E-8E08-7D74-8631-75CDF35A35B2 Response: {'code': 'ServiceUnavailable', 'requestid': 'ACB9C10E-8E08-7D74-8631-75CDF35A35B2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: ACB9C10E-8E08-7D74-8631-75CDF35A35B2)
2025-05-21 15:30:50,573 - INFO - 开始处理日期: 2025-05-21
2025-05-21 15:30:50,573 - INFO - Request Parameters - Page 1:
2025-05-21 15:30:50,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:30:50,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:30:53,521 - INFO - Response - Page 1:
2025-05-21 15:30:53,521 - INFO - 查询完成，共获取到 0 条记录
2025-05-21 15:30:53,521 - INFO - 获取到 0 条表单数据
2025-05-21 15:30:53,521 - INFO - 当前日期 2025-05-21 有 5 条MySQL数据需要处理
2025-05-21 15:30:53,521 - INFO - 开始批量插入 5 条新记录
2025-05-21 15:30:53,680 - INFO - 批量插入响应状态码: 200
2025-05-21 15:30:53,680 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 21 May 2025 07:30:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '252', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B0B8796A-23A5-71DA-8968-3D560066C393', 'x-acs-trace-id': 'e9d16862b4e12496ba389ff7e3530f58', 'etag': '2HXVwcvg6gt9lws0TEBhNnA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-21 15:30:53,680 - INFO - 批量插入响应体: {'result': ['FINST-7PF66H7144MVD9WJAI22GBVSJEHF3LK1JMXAMT3', 'FINST-7PF66H7144MVD9WJAI22GBVSJEHF3LK1JMXAMU3', 'FINST-7PF66H7144MVD9WJAI22GBVSJEHF3LK1JMXAMV3', 'FINST-7PF66H7144MVD9WJAI22GBVSJEHF3LK1JMXAMW3', 'FINST-7PF66H7144MVD9WJAI22GBVSJEHF3LK1JMXAMX3']}
2025-05-21 15:30:53,680 - INFO - 批量插入表单数据成功，批次 1，共 5 条记录
2025-05-21 15:30:53,681 - INFO - 成功插入的数据ID: ['FINST-7PF66H7144MVD9WJAI22GBVSJEHF3LK1JMXAMT3', 'FINST-7PF66H7144MVD9WJAI22GBVSJEHF3LK1JMXAMU3', 'FINST-7PF66H7144MVD9WJAI22GBVSJEHF3LK1JMXAMV3', 'FINST-7PF66H7144MVD9WJAI22GBVSJEHF3LK1JMXAMW3', 'FINST-7PF66H7144MVD9WJAI22GBVSJEHF3LK1JMXAMX3']
2025-05-21 15:30:58,682 - INFO - 批量插入完成，共 5 条记录
2025-05-21 15:30:58,682 - INFO - 日期 2025-05-21 处理完成 - 更新: 0 条，插入: 5 条，错误: 0 条
2025-05-21 15:30:58,682 - INFO - 数据同步完成！更新: 0 条，插入: 5 条，错误: 2 条
2025-05-21 15:31:58,683 - INFO - 开始同步昨天与今天的销售数据: 2025-05-20 至 2025-05-21
2025-05-21 15:31:58,683 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 15:31:58,683 - INFO - 查询参数: ('2025-05-20', '2025-05-21')
2025-05-21 15:31:58,764 - INFO - MySQL查询成功，时间段: 2025-05-20 至 2025-05-21，共获取 569 条记录
2025-05-21 15:31:58,764 - INFO - 获取到 2 个日期需要处理: ['2025-05-20', '2025-05-21']
2025-05-21 15:31:58,769 - INFO - 开始处理日期: 2025-05-20
2025-05-21 15:31:58,769 - INFO - Request Parameters - Page 1:
2025-05-21 15:31:58,769 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:31:58,769 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:31:59,713 - INFO - Response - Page 1:
2025-05-21 15:31:59,714 - INFO - 第 1 页获取到 100 条记录
2025-05-21 15:31:59,914 - INFO - Request Parameters - Page 2:
2025-05-21 15:31:59,914 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:31:59,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:32:00,756 - INFO - Response - Page 2:
2025-05-21 15:32:00,756 - INFO - 第 2 页获取到 100 条记录
2025-05-21 15:32:00,956 - INFO - Request Parameters - Page 3:
2025-05-21 15:32:00,956 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:32:00,956 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:32:01,819 - INFO - Response - Page 3:
2025-05-21 15:32:01,819 - INFO - 第 3 页获取到 100 条记录
2025-05-21 15:32:02,020 - INFO - Request Parameters - Page 4:
2025-05-21 15:32:02,020 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:32:02,020 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:32:02,843 - INFO - Response - Page 4:
2025-05-21 15:32:02,844 - INFO - 第 4 页获取到 100 条记录
2025-05-21 15:32:03,044 - INFO - Request Parameters - Page 5:
2025-05-21 15:32:03,044 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:32:03,044 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:32:03,857 - INFO - Response - Page 5:
2025-05-21 15:32:03,857 - INFO - 第 5 页获取到 100 条记录
2025-05-21 15:32:04,058 - INFO - Request Parameters - Page 6:
2025-05-21 15:32:04,058 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:32:04,058 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:32:04,972 - INFO - Response - Page 6:
2025-05-21 15:32:04,972 - INFO - 第 6 页获取到 63 条记录
2025-05-21 15:32:05,172 - INFO - 查询完成，共获取到 563 条记录
2025-05-21 15:32:05,172 - INFO - 获取到 563 条表单数据
2025-05-21 15:32:05,181 - INFO - 当前日期 2025-05-20 有 564 条MySQL数据需要处理
2025-05-21 15:32:05,190 - INFO - 开始批量插入 1 条新记录
2025-05-21 15:32:05,360 - INFO - 批量插入响应状态码: 200
2025-05-21 15:32:05,360 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 21 May 2025 07:32:09 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '256B48FA-306D-799E-9DD6-DE750DEF00FB', 'x-acs-trace-id': 'de8e67cc47ef6e016d534b373e9efd12', 'etag': '6DDeQHmfE9tM1832DuqChNw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-21 15:32:05,360 - INFO - 批量插入响应体: {'result': ['FINST-68E66TC11XGV79JRFFXKC6ZSU2GE3QVKKMXAM8D']}
2025-05-21 15:32:05,360 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-21 15:32:05,361 - INFO - 成功插入的数据ID: ['FINST-68E66TC11XGV79JRFFXKC6ZSU2GE3QVKKMXAM8D']
2025-05-21 15:32:10,362 - INFO - 批量插入完成，共 1 条记录
2025-05-21 15:32:10,362 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-05-21 15:32:10,362 - INFO - 开始处理日期: 2025-05-21
2025-05-21 15:32:10,362 - INFO - Request Parameters - Page 1:
2025-05-21 15:32:10,362 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 15:32:10,362 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 15:32:10,931 - INFO - Response - Page 1:
2025-05-21 15:32:10,931 - INFO - 第 1 页获取到 5 条记录
2025-05-21 15:32:11,131 - INFO - 查询完成，共获取到 5 条记录
2025-05-21 15:32:11,131 - INFO - 获取到 5 条表单数据
2025-05-21 15:32:11,132 - INFO - 当前日期 2025-05-21 有 5 条MySQL数据需要处理
2025-05-21 15:32:11,132 - INFO - 日期 2025-05-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 15:32:11,132 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 0 条
2025-05-21 15:32:11,132 - INFO - 同步完成
2025-05-21 16:30:34,294 - INFO - 使用默认增量同步（当天更新数据）
2025-05-21 16:30:34,294 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 16:30:34,294 - INFO - 查询参数: ('2025-05-21',)
2025-05-21 16:30:34,371 - INFO - MySQL查询成功，增量数据（日期: 2025-05-21），共获取 217 条记录
2025-05-21 16:30:34,372 - INFO - 获取到 3 个日期需要处理: ['2025-05-19', '2025-05-20', '2025-05-21']
2025-05-21 16:30:34,374 - INFO - 开始处理日期: 2025-05-19
2025-05-21 16:30:34,377 - INFO - Request Parameters - Page 1:
2025-05-21 16:30:34,377 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 16:30:34,377 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 16:30:42,483 - ERROR - 处理日期 2025-05-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A5EDFA5F-9C29-7952-A15A-4CD345921392 Response: {'code': 'ServiceUnavailable', 'requestid': 'A5EDFA5F-9C29-7952-A15A-4CD345921392', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A5EDFA5F-9C29-7952-A15A-4CD345921392)
2025-05-21 16:30:42,499 - INFO - 开始处理日期: 2025-05-20
2025-05-21 16:30:42,499 - INFO - Request Parameters - Page 1:
2025-05-21 16:30:42,499 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 16:30:42,499 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 16:30:50,608 - ERROR - 处理日期 2025-05-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C52DFACB-A9E2-7575-9BA8-2B492A00F129 Response: {'code': 'ServiceUnavailable', 'requestid': 'C52DFACB-A9E2-7575-9BA8-2B492A00F129', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C52DFACB-A9E2-7575-9BA8-2B492A00F129)
2025-05-21 16:30:50,608 - INFO - 开始处理日期: 2025-05-21
2025-05-21 16:30:50,608 - INFO - Request Parameters - Page 1:
2025-05-21 16:30:50,608 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 16:30:50,608 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 16:30:52,874 - INFO - Response - Page 1:
2025-05-21 16:30:52,874 - INFO - 第 1 页获取到 5 条记录
2025-05-21 16:30:53,077 - INFO - 查询完成，共获取到 5 条记录
2025-05-21 16:30:53,077 - INFO - 获取到 5 条表单数据
2025-05-21 16:30:53,077 - INFO - 当前日期 2025-05-21 有 5 条MySQL数据需要处理
2025-05-21 16:30:53,077 - INFO - 日期 2025-05-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 16:30:53,077 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-21 16:31:53,092 - INFO - 开始同步昨天与今天的销售数据: 2025-05-20 至 2025-05-21
2025-05-21 16:31:53,092 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 16:31:53,092 - INFO - 查询参数: ('2025-05-20', '2025-05-21')
2025-05-21 16:31:53,170 - INFO - MySQL查询成功，时间段: 2025-05-20 至 2025-05-21，共获取 601 条记录
2025-05-21 16:31:53,170 - INFO - 获取到 2 个日期需要处理: ['2025-05-20', '2025-05-21']
2025-05-21 16:31:53,170 - INFO - 开始处理日期: 2025-05-20
2025-05-21 16:31:53,170 - INFO - Request Parameters - Page 1:
2025-05-21 16:31:53,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 16:31:53,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 16:31:54,100 - INFO - Response - Page 1:
2025-05-21 16:31:54,100 - INFO - 第 1 页获取到 100 条记录
2025-05-21 16:31:54,307 - INFO - Request Parameters - Page 2:
2025-05-21 16:31:54,307 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 16:31:54,307 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 16:31:55,081 - INFO - Response - Page 2:
2025-05-21 16:31:55,081 - INFO - 第 2 页获取到 100 条记录
2025-05-21 16:31:55,286 - INFO - Request Parameters - Page 3:
2025-05-21 16:31:55,286 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 16:31:55,286 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 16:31:56,244 - INFO - Response - Page 3:
2025-05-21 16:31:56,244 - INFO - 第 3 页获取到 100 条记录
2025-05-21 16:31:56,445 - INFO - Request Parameters - Page 4:
2025-05-21 16:31:56,445 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 16:31:56,445 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 16:31:57,344 - INFO - Response - Page 4:
2025-05-21 16:31:57,344 - INFO - 第 4 页获取到 100 条记录
2025-05-21 16:31:57,545 - INFO - Request Parameters - Page 5:
2025-05-21 16:31:57,545 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 16:31:57,545 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 16:31:58,387 - INFO - Response - Page 5:
2025-05-21 16:31:58,387 - INFO - 第 5 页获取到 100 条记录
2025-05-21 16:31:58,587 - INFO - Request Parameters - Page 6:
2025-05-21 16:31:58,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 16:31:58,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 16:31:59,378 - INFO - Response - Page 6:
2025-05-21 16:31:59,379 - INFO - 第 6 页获取到 64 条记录
2025-05-21 16:31:59,584 - INFO - 查询完成，共获取到 564 条记录
2025-05-21 16:31:59,584 - INFO - 获取到 564 条表单数据
2025-05-21 16:31:59,584 - INFO - 当前日期 2025-05-20 有 596 条MySQL数据需要处理
2025-05-21 16:31:59,600 - INFO - 开始更新记录 - 表单实例ID: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMHC
2025-05-21 16:32:00,037 - INFO - 更新表单数据成功: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMHC
2025-05-21 16:32:00,037 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 4190.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 4190.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 52}]
2025-05-21 16:32:00,037 - INFO - 开始更新记录 - 表单实例ID: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMIC
2025-05-21 16:32:00,506 - INFO - 更新表单数据成功: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMIC
2025-05-21 16:32:00,506 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10000.0, 'new_value': 10580.0}, {'field': 'total_amount', 'old_value': 10000.0, 'new_value': 10580.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 2}]
2025-05-21 16:32:00,506 - INFO - 开始更新记录 - 表单实例ID: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMJC
2025-05-21 16:32:00,959 - INFO - 更新表单数据成功: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMJC
2025-05-21 16:32:00,959 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 900.0, 'new_value': 1018.0}, {'field': 'total_amount', 'old_value': 900.0, 'new_value': 1018.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 96}]
2025-05-21 16:32:00,959 - INFO - 开始更新记录 - 表单实例ID: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMKC
2025-05-21 16:32:01,412 - INFO - 更新表单数据成功: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMKC
2025-05-21 16:32:01,412 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15000.0, 'new_value': 19860.51}, {'field': 'total_amount', 'old_value': 15000.0, 'new_value': 19860.51}, {'field': 'order_count', 'old_value': 1, 'new_value': 30}]
2025-05-21 16:32:01,412 - INFO - 开始更新记录 - 表单实例ID: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMLC
2025-05-21 16:32:01,960 - INFO - 更新表单数据成功: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMLC
2025-05-21 16:32:01,960 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37000.0, 'new_value': 62000.0}, {'field': 'total_amount', 'old_value': 37000.0, 'new_value': 62000.0}]
2025-05-21 16:32:01,960 - INFO - 开始更新记录 - 表单实例ID: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMMC
2025-05-21 16:32:02,425 - INFO - 更新表单数据成功: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMMC
2025-05-21 16:32:02,425 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4200.0, 'new_value': 10207.67}, {'field': 'total_amount', 'old_value': 4200.0, 'new_value': 10207.67}, {'field': 'order_count', 'old_value': 1, 'new_value': 88}]
2025-05-21 16:32:02,425 - INFO - 开始更新记录 - 表单实例ID: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMPC
2025-05-21 16:32:02,962 - INFO - 更新表单数据成功: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMPC
2025-05-21 16:32:02,962 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 399.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 399.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-21 16:32:02,963 - INFO - 开始更新记录 - 表单实例ID: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMRC
2025-05-21 16:32:03,395 - INFO - 更新表单数据成功: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW39QKUBXAMRC
2025-05-21 16:32:03,395 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1800.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1800.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-21 16:32:03,396 - INFO - 开始更新记录 - 表单实例ID: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMTC
2025-05-21 16:32:03,868 - INFO - 更新表单数据成功: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMTC
2025-05-21 16:32:03,868 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4500.0, 'new_value': 3196.0}, {'field': 'total_amount', 'old_value': 4500.0, 'new_value': 3196.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 115}]
2025-05-21 16:32:03,868 - INFO - 开始更新记录 - 表单实例ID: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMUC
2025-05-21 16:32:04,430 - INFO - 更新表单数据成功: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMUC
2025-05-21 16:32:04,430 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 6587.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 6587.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 43}]
2025-05-21 16:32:04,430 - INFO - 开始更新记录 - 表单实例ID: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMXC
2025-05-21 16:32:04,853 - INFO - 更新表单数据成功: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMXC
2025-05-21 16:32:04,854 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 4820.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 4820.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-21 16:32:04,854 - INFO - 开始更新记录 - 表单实例ID: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMZC
2025-05-21 16:32:05,349 - INFO - 更新表单数据成功: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAMZC
2025-05-21 16:32:05,349 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35000.0, 'new_value': 45000.0}, {'field': 'total_amount', 'old_value': 35000.0, 'new_value': 45000.0}]
2025-05-21 16:32:05,350 - INFO - 开始更新记录 - 表单实例ID: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAM1D
2025-05-21 16:32:05,965 - INFO - 更新表单数据成功: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAM1D
2025-05-21 16:32:05,966 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1200.0, 'new_value': 1315.0}, {'field': 'total_amount', 'old_value': 1200.0, 'new_value': 1315.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 127}]
2025-05-21 16:32:05,966 - INFO - 开始更新记录 - 表单实例ID: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAM2D
2025-05-21 16:32:06,375 - INFO - 更新表单数据成功: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAM2D
2025-05-21 16:32:06,375 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1795.72}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1795.72}, {'field': 'order_count', 'old_value': 0, 'new_value': 83}]
2025-05-21 16:32:06,375 - INFO - 开始更新记录 - 表单实例ID: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAM3D
2025-05-21 16:32:06,923 - INFO - 更新表单数据成功: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAM3D
2025-05-21 16:32:06,923 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 529.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 529.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 16}]
2025-05-21 16:32:06,923 - INFO - 开始更新记录 - 表单实例ID: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAM4D
2025-05-21 16:32:07,380 - INFO - 更新表单数据成功: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAM4D
2025-05-21 16:32:07,380 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1288.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1288.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-21 16:32:07,381 - INFO - 开始更新记录 - 表单实例ID: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAM7D
2025-05-21 16:32:07,850 - INFO - 更新表单数据成功: FINST-KLF66WC13ALV1CDY7F93O5E4TGFW3AQKUBXAM7D
2025-05-21 16:32:07,850 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1393.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1393.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-05-21 16:32:07,850 - INFO - 开始批量插入 32 条新记录
2025-05-21 16:32:08,044 - INFO - 批量插入响应状态码: 200
2025-05-21 16:32:08,044 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 21 May 2025 08:32:11 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1531', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '94C5384C-61F6-762F-8F9B-7BD90D2DC9A1', 'x-acs-trace-id': '459b9df74b3c26e508caf926e49a7371', 'etag': '19KBRZYz10cLiiPiRgFEyNg1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-21 16:32:08,044 - INFO - 批量插入响应体: {'result': ['FINST-1MD668B173MV1I6KFLEON89917VF31RSPOXAMJ', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMK', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAML', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMM', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMN', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMO', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMP', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMQ', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMR', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMS', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMT', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMU', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMV', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMW', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMX', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMY', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMZ', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAM01', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAM11', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAM21', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAM31', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAM41', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAM51', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAM61', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAM71', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAM81', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAM91', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMA1', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMB1', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMC1', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMD1', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAME1']}
2025-05-21 16:32:08,044 - INFO - 批量插入表单数据成功，批次 1，共 32 条记录
2025-05-21 16:32:08,044 - INFO - 成功插入的数据ID: ['FINST-1MD668B173MV1I6KFLEON89917VF31RSPOXAMJ', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMK', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAML', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMM', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMN', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMO', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMP', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMQ', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMR', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMS', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMT', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMU', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMV', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMW', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMX', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMY', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMZ', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAM01', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAM11', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAM21', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAM31', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAM41', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAM51', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAM61', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAM71', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAM81', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAM91', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMA1', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMB1', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMC1', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAMD1', 'FINST-1MD668B173MV1I6KFLEON89917VF32RSPOXAME1']
2025-05-21 16:32:13,049 - INFO - 批量插入完成，共 32 条记录
2025-05-21 16:32:13,049 - INFO - 日期 2025-05-20 处理完成 - 更新: 17 条，插入: 32 条，错误: 0 条
2025-05-21 16:32:13,049 - INFO - 开始处理日期: 2025-05-21
2025-05-21 16:32:13,049 - INFO - Request Parameters - Page 1:
2025-05-21 16:32:13,049 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 16:32:13,049 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 16:32:13,586 - INFO - Response - Page 1:
2025-05-21 16:32:13,586 - INFO - 第 1 页获取到 5 条记录
2025-05-21 16:32:13,786 - INFO - 查询完成，共获取到 5 条记录
2025-05-21 16:32:13,786 - INFO - 获取到 5 条表单数据
2025-05-21 16:32:13,787 - INFO - 当前日期 2025-05-21 有 5 条MySQL数据需要处理
2025-05-21 16:32:13,787 - INFO - 日期 2025-05-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 16:32:13,787 - INFO - 数据同步完成！更新: 17 条，插入: 32 条，错误: 0 条
2025-05-21 16:32:13,787 - INFO - 同步完成
2025-05-21 17:30:34,263 - INFO - 使用默认增量同步（当天更新数据）
2025-05-21 17:30:34,264 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 17:30:34,264 - INFO - 查询参数: ('2025-05-21',)
2025-05-21 17:30:34,331 - INFO - MySQL查询成功，增量数据（日期: 2025-05-21），共获取 217 条记录
2025-05-21 17:30:34,331 - INFO - 获取到 3 个日期需要处理: ['2025-05-19', '2025-05-20', '2025-05-21']
2025-05-21 17:30:34,331 - INFO - 开始处理日期: 2025-05-19
2025-05-21 17:30:34,343 - INFO - Request Parameters - Page 1:
2025-05-21 17:30:34,343 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 17:30:34,343 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 17:30:42,473 - ERROR - 处理日期 2025-05-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 8D36F572-22D1-7387-AA70-BD442E8953A0 Response: {'code': 'ServiceUnavailable', 'requestid': '8D36F572-22D1-7387-AA70-BD442E8953A0', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 8D36F572-22D1-7387-AA70-BD442E8953A0)
2025-05-21 17:30:42,473 - INFO - 开始处理日期: 2025-05-20
2025-05-21 17:30:42,473 - INFO - Request Parameters - Page 1:
2025-05-21 17:30:42,473 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 17:30:42,473 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 17:30:50,587 - ERROR - 处理日期 2025-05-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5A6E7F48-8B88-738D-A419-CDAB1CD966A1 Response: {'code': 'ServiceUnavailable', 'requestid': '5A6E7F48-8B88-738D-A419-CDAB1CD966A1', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 5A6E7F48-8B88-738D-A419-CDAB1CD966A1)
2025-05-21 17:30:50,587 - INFO - 开始处理日期: 2025-05-21
2025-05-21 17:30:50,587 - INFO - Request Parameters - Page 1:
2025-05-21 17:30:50,587 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 17:30:50,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 17:30:58,717 - ERROR - 处理日期 2025-05-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: CE5F7E2D-D3E7-7E9D-A613-CBC1778B2872 Response: {'code': 'ServiceUnavailable', 'requestid': 'CE5F7E2D-D3E7-7E9D-A613-CBC1778B2872', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: CE5F7E2D-D3E7-7E9D-A613-CBC1778B2872)
2025-05-21 17:30:58,717 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 3 条
2025-05-21 17:31:58,718 - INFO - 开始同步昨天与今天的销售数据: 2025-05-20 至 2025-05-21
2025-05-21 17:31:58,718 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 17:31:58,718 - INFO - 查询参数: ('2025-05-20', '2025-05-21')
2025-05-21 17:31:58,796 - INFO - MySQL查询成功，时间段: 2025-05-20 至 2025-05-21，共获取 601 条记录
2025-05-21 17:31:58,812 - INFO - 获取到 2 个日期需要处理: ['2025-05-20', '2025-05-21']
2025-05-21 17:31:58,812 - INFO - 开始处理日期: 2025-05-20
2025-05-21 17:31:58,812 - INFO - Request Parameters - Page 1:
2025-05-21 17:31:58,812 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 17:31:58,812 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 17:32:06,921 - ERROR - 处理日期 2025-05-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 48B4043F-0651-7CC9-A049-E710A9414393 Response: {'code': 'ServiceUnavailable', 'requestid': '48B4043F-0651-7CC9-A049-E710A9414393', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 48B4043F-0651-7CC9-A049-E710A9414393)
2025-05-21 17:32:06,921 - INFO - 开始处理日期: 2025-05-21
2025-05-21 17:32:06,921 - INFO - Request Parameters - Page 1:
2025-05-21 17:32:06,921 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 17:32:06,921 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 17:32:08,812 - INFO - Response - Page 1:
2025-05-21 17:32:08,812 - INFO - 第 1 页获取到 5 条记录
2025-05-21 17:32:09,015 - INFO - 查询完成，共获取到 5 条记录
2025-05-21 17:32:09,015 - INFO - 获取到 5 条表单数据
2025-05-21 17:32:09,015 - INFO - 当前日期 2025-05-21 有 5 条MySQL数据需要处理
2025-05-21 17:32:09,015 - INFO - 日期 2025-05-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 17:32:09,015 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-21 17:32:09,015 - INFO - 同步完成
2025-05-21 18:30:34,197 - INFO - 使用默认增量同步（当天更新数据）
2025-05-21 18:30:34,197 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 18:30:34,197 - INFO - 查询参数: ('2025-05-21',)
2025-05-21 18:30:34,275 - INFO - MySQL查询成功，增量数据（日期: 2025-05-21），共获取 217 条记录
2025-05-21 18:30:34,275 - INFO - 获取到 3 个日期需要处理: ['2025-05-19', '2025-05-20', '2025-05-21']
2025-05-21 18:30:34,275 - INFO - 开始处理日期: 2025-05-19
2025-05-21 18:30:34,275 - INFO - Request Parameters - Page 1:
2025-05-21 18:30:34,275 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:30:34,275 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:30:42,416 - ERROR - 处理日期 2025-05-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 247CCA64-8155-73D8-A2E1-A6CF86DE3B27 Response: {'code': 'ServiceUnavailable', 'requestid': '247CCA64-8155-73D8-A2E1-A6CF86DE3B27', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 247CCA64-8155-73D8-A2E1-A6CF86DE3B27)
2025-05-21 18:30:42,416 - INFO - 开始处理日期: 2025-05-20
2025-05-21 18:30:42,416 - INFO - Request Parameters - Page 1:
2025-05-21 18:30:42,416 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:30:42,416 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:30:50,525 - ERROR - 处理日期 2025-05-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: AA70C62D-1258-76D6-AD38-44E05A06C117 Response: {'code': 'ServiceUnavailable', 'requestid': 'AA70C62D-1258-76D6-AD38-44E05A06C117', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: AA70C62D-1258-76D6-AD38-44E05A06C117)
2025-05-21 18:30:50,525 - INFO - 开始处理日期: 2025-05-21
2025-05-21 18:30:50,525 - INFO - Request Parameters - Page 1:
2025-05-21 18:30:50,525 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:30:50,525 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:30:52,432 - INFO - Response - Page 1:
2025-05-21 18:30:52,432 - INFO - 第 1 页获取到 5 条记录
2025-05-21 18:30:52,635 - INFO - 查询完成，共获取到 5 条记录
2025-05-21 18:30:52,635 - INFO - 获取到 5 条表单数据
2025-05-21 18:30:52,635 - INFO - 当前日期 2025-05-21 有 5 条MySQL数据需要处理
2025-05-21 18:30:52,635 - INFO - 日期 2025-05-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 18:30:52,635 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-21 18:31:52,650 - INFO - 开始同步昨天与今天的销售数据: 2025-05-20 至 2025-05-21
2025-05-21 18:31:52,650 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 18:31:52,650 - INFO - 查询参数: ('2025-05-20', '2025-05-21')
2025-05-21 18:31:52,728 - INFO - MySQL查询成功，时间段: 2025-05-20 至 2025-05-21，共获取 601 条记录
2025-05-21 18:31:52,728 - INFO - 获取到 2 个日期需要处理: ['2025-05-20', '2025-05-21']
2025-05-21 18:31:52,728 - INFO - 开始处理日期: 2025-05-20
2025-05-21 18:31:52,728 - INFO - Request Parameters - Page 1:
2025-05-21 18:31:52,728 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:31:52,728 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:31:53,650 - INFO - Response - Page 1:
2025-05-21 18:31:53,650 - INFO - 第 1 页获取到 100 条记录
2025-05-21 18:31:53,853 - INFO - Request Parameters - Page 2:
2025-05-21 18:31:53,853 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:31:53,853 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:31:54,697 - INFO - Response - Page 2:
2025-05-21 18:31:54,697 - INFO - 第 2 页获取到 100 条记录
2025-05-21 18:31:54,900 - INFO - Request Parameters - Page 3:
2025-05-21 18:31:54,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:31:54,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:31:55,666 - INFO - Response - Page 3:
2025-05-21 18:31:55,666 - INFO - 第 3 页获取到 100 条记录
2025-05-21 18:31:55,869 - INFO - Request Parameters - Page 4:
2025-05-21 18:31:55,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:31:55,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:31:56,681 - INFO - Response - Page 4:
2025-05-21 18:31:56,681 - INFO - 第 4 页获取到 100 条记录
2025-05-21 18:31:56,884 - INFO - Request Parameters - Page 5:
2025-05-21 18:31:56,884 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:31:56,884 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:31:57,697 - INFO - Response - Page 5:
2025-05-21 18:31:57,697 - INFO - 第 5 页获取到 100 条记录
2025-05-21 18:31:57,900 - INFO - Request Parameters - Page 6:
2025-05-21 18:31:57,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:31:57,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:31:58,775 - INFO - Response - Page 6:
2025-05-21 18:31:58,775 - INFO - 第 6 页获取到 96 条记录
2025-05-21 18:31:58,978 - INFO - 查询完成，共获取到 596 条记录
2025-05-21 18:31:58,978 - INFO - 获取到 596 条表单数据
2025-05-21 18:31:58,978 - INFO - 当前日期 2025-05-20 有 596 条MySQL数据需要处理
2025-05-21 18:31:58,994 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 18:31:58,994 - INFO - 开始处理日期: 2025-05-21
2025-05-21 18:31:58,994 - INFO - Request Parameters - Page 1:
2025-05-21 18:31:58,994 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 18:31:58,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 18:31:59,587 - INFO - Response - Page 1:
2025-05-21 18:31:59,587 - INFO - 第 1 页获取到 5 条记录
2025-05-21 18:31:59,791 - INFO - 查询完成，共获取到 5 条记录
2025-05-21 18:31:59,791 - INFO - 获取到 5 条表单数据
2025-05-21 18:31:59,791 - INFO - 当前日期 2025-05-21 有 5 条MySQL数据需要处理
2025-05-21 18:31:59,791 - INFO - 日期 2025-05-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 18:31:59,791 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 18:31:59,791 - INFO - 同步完成
2025-05-21 19:30:34,281 - INFO - 使用默认增量同步（当天更新数据）
2025-05-21 19:30:34,281 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 19:30:34,281 - INFO - 查询参数: ('2025-05-21',)
2025-05-21 19:30:34,359 - INFO - MySQL查询成功，增量数据（日期: 2025-05-21），共获取 217 条记录
2025-05-21 19:30:34,359 - INFO - 获取到 3 个日期需要处理: ['2025-05-19', '2025-05-20', '2025-05-21']
2025-05-21 19:30:34,359 - INFO - 开始处理日期: 2025-05-19
2025-05-21 19:30:34,359 - INFO - Request Parameters - Page 1:
2025-05-21 19:30:34,359 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 19:30:34,359 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 19:30:42,483 - ERROR - 处理日期 2025-05-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: BDFC3C77-7823-7AB7-B942-8A80177FCD82 Response: {'code': 'ServiceUnavailable', 'requestid': 'BDFC3C77-7823-7AB7-B942-8A80177FCD82', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: BDFC3C77-7823-7AB7-B942-8A80177FCD82)
2025-05-21 19:30:42,483 - INFO - 开始处理日期: 2025-05-20
2025-05-21 19:30:42,483 - INFO - Request Parameters - Page 1:
2025-05-21 19:30:42,483 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 19:30:42,483 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 19:30:50,592 - ERROR - 处理日期 2025-05-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: EC8C4AA7-B342-7EBA-83FF-6B46D00DD1C4 Response: {'code': 'ServiceUnavailable', 'requestid': 'EC8C4AA7-B342-7EBA-83FF-6B46D00DD1C4', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: EC8C4AA7-B342-7EBA-83FF-6B46D00DD1C4)
2025-05-21 19:30:50,592 - INFO - 开始处理日期: 2025-05-21
2025-05-21 19:30:50,592 - INFO - Request Parameters - Page 1:
2025-05-21 19:30:50,592 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 19:30:50,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 19:30:58,091 - INFO - Response - Page 1:
2025-05-21 19:30:58,091 - INFO - 第 1 页获取到 5 条记录
2025-05-21 19:30:58,294 - INFO - 查询完成，共获取到 5 条记录
2025-05-21 19:30:58,294 - INFO - 获取到 5 条表单数据
2025-05-21 19:30:58,294 - INFO - 当前日期 2025-05-21 有 5 条MySQL数据需要处理
2025-05-21 19:30:58,294 - INFO - 日期 2025-05-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 19:30:58,294 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-21 19:31:58,303 - INFO - 开始同步昨天与今天的销售数据: 2025-05-20 至 2025-05-21
2025-05-21 19:31:58,303 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 19:31:58,303 - INFO - 查询参数: ('2025-05-20', '2025-05-21')
2025-05-21 19:31:58,381 - INFO - MySQL查询成功，时间段: 2025-05-20 至 2025-05-21，共获取 601 条记录
2025-05-21 19:31:58,381 - INFO - 获取到 2 个日期需要处理: ['2025-05-20', '2025-05-21']
2025-05-21 19:31:58,381 - INFO - 开始处理日期: 2025-05-20
2025-05-21 19:31:58,381 - INFO - Request Parameters - Page 1:
2025-05-21 19:31:58,381 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 19:31:58,381 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 19:31:59,287 - INFO - Response - Page 1:
2025-05-21 19:31:59,287 - INFO - 第 1 页获取到 100 条记录
2025-05-21 19:31:59,490 - INFO - Request Parameters - Page 2:
2025-05-21 19:31:59,490 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 19:31:59,490 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 19:32:00,380 - INFO - Response - Page 2:
2025-05-21 19:32:00,380 - INFO - 第 2 页获取到 100 条记录
2025-05-21 19:32:00,584 - INFO - Request Parameters - Page 3:
2025-05-21 19:32:00,584 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 19:32:00,584 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 19:32:01,365 - INFO - Response - Page 3:
2025-05-21 19:32:01,365 - INFO - 第 3 页获取到 100 条记录
2025-05-21 19:32:01,568 - INFO - Request Parameters - Page 4:
2025-05-21 19:32:01,568 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 19:32:01,568 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 19:32:02,396 - INFO - Response - Page 4:
2025-05-21 19:32:02,396 - INFO - 第 4 页获取到 100 条记录
2025-05-21 19:32:02,599 - INFO - Request Parameters - Page 5:
2025-05-21 19:32:02,599 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 19:32:02,599 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 19:32:03,474 - INFO - Response - Page 5:
2025-05-21 19:32:03,474 - INFO - 第 5 页获取到 100 条记录
2025-05-21 19:32:03,677 - INFO - Request Parameters - Page 6:
2025-05-21 19:32:03,677 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 19:32:03,677 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 19:32:04,552 - INFO - Response - Page 6:
2025-05-21 19:32:04,552 - INFO - 第 6 页获取到 96 条记录
2025-05-21 19:32:04,755 - INFO - 查询完成，共获取到 596 条记录
2025-05-21 19:32:04,755 - INFO - 获取到 596 条表单数据
2025-05-21 19:32:04,755 - INFO - 当前日期 2025-05-20 有 596 条MySQL数据需要处理
2025-05-21 19:32:04,771 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 19:32:04,771 - INFO - 开始处理日期: 2025-05-21
2025-05-21 19:32:04,771 - INFO - Request Parameters - Page 1:
2025-05-21 19:32:04,771 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 19:32:04,771 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 19:32:05,349 - INFO - Response - Page 1:
2025-05-21 19:32:05,349 - INFO - 第 1 页获取到 5 条记录
2025-05-21 19:32:05,552 - INFO - 查询完成，共获取到 5 条记录
2025-05-21 19:32:05,552 - INFO - 获取到 5 条表单数据
2025-05-21 19:32:05,552 - INFO - 当前日期 2025-05-21 有 5 条MySQL数据需要处理
2025-05-21 19:32:05,552 - INFO - 日期 2025-05-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 19:32:05,552 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 19:32:05,552 - INFO - 同步完成
2025-05-21 20:30:33,235 - INFO - 使用默认增量同步（当天更新数据）
2025-05-21 20:30:33,235 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 20:30:33,235 - INFO - 查询参数: ('2025-05-21',)
2025-05-21 20:30:33,297 - INFO - MySQL查询成功，增量数据（日期: 2025-05-21），共获取 217 条记录
2025-05-21 20:30:33,297 - INFO - 获取到 3 个日期需要处理: ['2025-05-19', '2025-05-20', '2025-05-21']
2025-05-21 20:30:33,313 - INFO - 开始处理日期: 2025-05-19
2025-05-21 20:30:33,313 - INFO - Request Parameters - Page 1:
2025-05-21 20:30:33,313 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 20:30:33,313 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 20:30:41,406 - ERROR - 处理日期 2025-05-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 7BB9F850-F74F-7343-A5E5-1D68C0B2B072 Response: {'code': 'ServiceUnavailable', 'requestid': '7BB9F850-F74F-7343-A5E5-1D68C0B2B072', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 7BB9F850-F74F-7343-A5E5-1D68C0B2B072)
2025-05-21 20:30:41,406 - INFO - 开始处理日期: 2025-05-20
2025-05-21 20:30:41,406 - INFO - Request Parameters - Page 1:
2025-05-21 20:30:41,406 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 20:30:41,406 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 20:30:43,937 - INFO - Response - Page 1:
2025-05-21 20:30:43,937 - INFO - 第 1 页获取到 100 条记录
2025-05-21 20:30:44,140 - INFO - Request Parameters - Page 2:
2025-05-21 20:30:44,140 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 20:30:44,140 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 20:30:52,264 - ERROR - 处理日期 2025-05-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 6F37D350-7BBA-7715-9159-C15B4AC64FFB Response: {'code': 'ServiceUnavailable', 'requestid': '6F37D350-7BBA-7715-9159-C15B4AC64FFB', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 6F37D350-7BBA-7715-9159-C15B4AC64FFB)
2025-05-21 20:30:52,264 - INFO - 开始处理日期: 2025-05-21
2025-05-21 20:30:52,264 - INFO - Request Parameters - Page 1:
2025-05-21 20:30:52,264 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 20:30:52,264 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 20:30:59,544 - INFO - Response - Page 1:
2025-05-21 20:30:59,544 - INFO - 第 1 页获取到 5 条记录
2025-05-21 20:30:59,747 - INFO - 查询完成，共获取到 5 条记录
2025-05-21 20:30:59,747 - INFO - 获取到 5 条表单数据
2025-05-21 20:30:59,747 - INFO - 当前日期 2025-05-21 有 5 条MySQL数据需要处理
2025-05-21 20:30:59,747 - INFO - 日期 2025-05-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 20:30:59,747 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-21 20:31:59,756 - INFO - 开始同步昨天与今天的销售数据: 2025-05-20 至 2025-05-21
2025-05-21 20:31:59,756 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 20:31:59,756 - INFO - 查询参数: ('2025-05-20', '2025-05-21')
2025-05-21 20:31:59,834 - INFO - MySQL查询成功，时间段: 2025-05-20 至 2025-05-21，共获取 601 条记录
2025-05-21 20:31:59,834 - INFO - 获取到 2 个日期需要处理: ['2025-05-20', '2025-05-21']
2025-05-21 20:31:59,834 - INFO - 开始处理日期: 2025-05-20
2025-05-21 20:31:59,834 - INFO - Request Parameters - Page 1:
2025-05-21 20:31:59,834 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 20:31:59,834 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 20:32:00,897 - INFO - Response - Page 1:
2025-05-21 20:32:00,897 - INFO - 第 1 页获取到 100 条记录
2025-05-21 20:32:01,100 - INFO - Request Parameters - Page 2:
2025-05-21 20:32:01,100 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 20:32:01,100 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 20:32:01,959 - INFO - Response - Page 2:
2025-05-21 20:32:01,959 - INFO - 第 2 页获取到 100 条记录
2025-05-21 20:32:02,162 - INFO - Request Parameters - Page 3:
2025-05-21 20:32:02,162 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 20:32:02,162 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 20:32:03,068 - INFO - Response - Page 3:
2025-05-21 20:32:03,068 - INFO - 第 3 页获取到 100 条记录
2025-05-21 20:32:03,271 - INFO - Request Parameters - Page 4:
2025-05-21 20:32:03,271 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 20:32:03,271 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 20:32:04,115 - INFO - Response - Page 4:
2025-05-21 20:32:04,115 - INFO - 第 4 页获取到 100 条记录
2025-05-21 20:32:04,318 - INFO - Request Parameters - Page 5:
2025-05-21 20:32:04,318 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 20:32:04,318 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 20:32:05,099 - INFO - Response - Page 5:
2025-05-21 20:32:05,099 - INFO - 第 5 页获取到 100 条记录
2025-05-21 20:32:05,302 - INFO - Request Parameters - Page 6:
2025-05-21 20:32:05,302 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 20:32:05,302 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 20:32:06,099 - INFO - Response - Page 6:
2025-05-21 20:32:06,099 - INFO - 第 6 页获取到 96 条记录
2025-05-21 20:32:06,302 - INFO - 查询完成，共获取到 596 条记录
2025-05-21 20:32:06,302 - INFO - 获取到 596 条表单数据
2025-05-21 20:32:06,302 - INFO - 当前日期 2025-05-20 有 596 条MySQL数据需要处理
2025-05-21 20:32:06,318 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 20:32:06,318 - INFO - 开始处理日期: 2025-05-21
2025-05-21 20:32:06,318 - INFO - Request Parameters - Page 1:
2025-05-21 20:32:06,318 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 20:32:06,318 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 20:32:06,849 - INFO - Response - Page 1:
2025-05-21 20:32:06,849 - INFO - 第 1 页获取到 5 条记录
2025-05-21 20:32:07,052 - INFO - 查询完成，共获取到 5 条记录
2025-05-21 20:32:07,052 - INFO - 获取到 5 条表单数据
2025-05-21 20:32:07,052 - INFO - 当前日期 2025-05-21 有 5 条MySQL数据需要处理
2025-05-21 20:32:07,052 - INFO - 日期 2025-05-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 20:32:07,052 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 20:32:07,052 - INFO - 同步完成
2025-05-21 21:30:33,366 - INFO - 使用默认增量同步（当天更新数据）
2025-05-21 21:30:33,366 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 21:30:33,366 - INFO - 查询参数: ('2025-05-21',)
2025-05-21 21:30:33,429 - INFO - MySQL查询成功，增量数据（日期: 2025-05-21），共获取 218 条记录
2025-05-21 21:30:33,429 - INFO - 获取到 3 个日期需要处理: ['2025-05-19', '2025-05-20', '2025-05-21']
2025-05-21 21:30:33,429 - INFO - 开始处理日期: 2025-05-19
2025-05-21 21:30:33,444 - INFO - Request Parameters - Page 1:
2025-05-21 21:30:33,444 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:30:33,444 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:30:41,553 - ERROR - 处理日期 2025-05-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D6D89516-B566-72E7-87A4-13B4A7F10202 Response: {'code': 'ServiceUnavailable', 'requestid': 'D6D89516-B566-72E7-87A4-13B4A7F10202', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D6D89516-B566-72E7-87A4-13B4A7F10202)
2025-05-21 21:30:41,553 - INFO - 开始处理日期: 2025-05-20
2025-05-21 21:30:41,553 - INFO - Request Parameters - Page 1:
2025-05-21 21:30:41,553 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:30:41,553 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:30:49,318 - INFO - Response - Page 1:
2025-05-21 21:30:49,318 - INFO - 第 1 页获取到 100 条记录
2025-05-21 21:30:49,521 - INFO - Request Parameters - Page 2:
2025-05-21 21:30:49,521 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:30:49,521 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:30:50,302 - INFO - Response - Page 2:
2025-05-21 21:30:50,302 - INFO - 第 2 页获取到 100 条记录
2025-05-21 21:30:50,505 - INFO - Request Parameters - Page 3:
2025-05-21 21:30:50,505 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:30:50,505 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:30:51,286 - INFO - Response - Page 3:
2025-05-21 21:30:51,286 - INFO - 第 3 页获取到 100 条记录
2025-05-21 21:30:51,489 - INFO - Request Parameters - Page 4:
2025-05-21 21:30:51,489 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:30:51,489 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:30:52,255 - INFO - Response - Page 4:
2025-05-21 21:30:52,255 - INFO - 第 4 页获取到 100 条记录
2025-05-21 21:30:52,458 - INFO - Request Parameters - Page 5:
2025-05-21 21:30:52,458 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:30:52,458 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:30:53,395 - INFO - Response - Page 5:
2025-05-21 21:30:53,395 - INFO - 第 5 页获取到 100 条记录
2025-05-21 21:30:53,598 - INFO - Request Parameters - Page 6:
2025-05-21 21:30:53,598 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:30:53,598 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:30:54,411 - INFO - Response - Page 6:
2025-05-21 21:30:54,411 - INFO - 第 6 页获取到 96 条记录
2025-05-21 21:30:54,614 - INFO - 查询完成，共获取到 596 条记录
2025-05-21 21:30:54,614 - INFO - 获取到 596 条表单数据
2025-05-21 21:30:54,614 - INFO - 当前日期 2025-05-20 有 210 条MySQL数据需要处理
2025-05-21 21:30:54,614 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 21:30:54,614 - INFO - 开始处理日期: 2025-05-21
2025-05-21 21:30:54,614 - INFO - Request Parameters - Page 1:
2025-05-21 21:30:54,614 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:30:54,614 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:30:55,192 - INFO - Response - Page 1:
2025-05-21 21:30:55,192 - INFO - 第 1 页获取到 5 条记录
2025-05-21 21:30:55,395 - INFO - 查询完成，共获取到 5 条记录
2025-05-21 21:30:55,395 - INFO - 获取到 5 条表单数据
2025-05-21 21:30:55,395 - INFO - 当前日期 2025-05-21 有 6 条MySQL数据需要处理
2025-05-21 21:30:55,395 - INFO - 开始批量插入 1 条新记录
2025-05-21 21:30:55,536 - INFO - 批量插入响应状态码: 200
2025-05-21 21:30:55,536 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 21 May 2025 13:30:56 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '8D6B38ED-6ADC-7A55-B98E-5B4AACFD4756', 'x-acs-trace-id': '5d96fd69d8f56c76599e962d1ff20712', 'etag': '65jQoFOlB9e0J+cCIP0ln8Q0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-21 21:30:55,536 - INFO - 批量插入响应体: {'result': ['FINST-X8D66N81O1KVDJMK8XCFM5YJ23YC2RIZDZXAM15']}
2025-05-21 21:30:55,536 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-21 21:30:55,536 - INFO - 成功插入的数据ID: ['FINST-X8D66N81O1KVDJMK8XCFM5YJ23YC2RIZDZXAM15']
2025-05-21 21:31:00,551 - INFO - 批量插入完成，共 1 条记录
2025-05-21 21:31:00,551 - INFO - 日期 2025-05-21 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-05-21 21:31:00,551 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 1 条
2025-05-21 21:32:00,559 - INFO - 开始同步昨天与今天的销售数据: 2025-05-20 至 2025-05-21
2025-05-21 21:32:00,559 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 21:32:00,559 - INFO - 查询参数: ('2025-05-20', '2025-05-21')
2025-05-21 21:32:00,653 - INFO - MySQL查询成功，时间段: 2025-05-20 至 2025-05-21，共获取 602 条记录
2025-05-21 21:32:00,653 - INFO - 获取到 2 个日期需要处理: ['2025-05-20', '2025-05-21']
2025-05-21 21:32:00,669 - INFO - 开始处理日期: 2025-05-20
2025-05-21 21:32:00,669 - INFO - Request Parameters - Page 1:
2025-05-21 21:32:00,669 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:32:00,669 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:32:08,777 - ERROR - 处理日期 2025-05-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A7085E89-5DBC-7D16-96C0-B9A3E77D7B0B Response: {'code': 'ServiceUnavailable', 'requestid': 'A7085E89-5DBC-7D16-96C0-B9A3E77D7B0B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A7085E89-5DBC-7D16-96C0-B9A3E77D7B0B)
2025-05-21 21:32:08,777 - INFO - 开始处理日期: 2025-05-21
2025-05-21 21:32:08,777 - INFO - Request Parameters - Page 1:
2025-05-21 21:32:08,777 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 21:32:08,777 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 21:32:09,324 - INFO - Response - Page 1:
2025-05-21 21:32:09,324 - INFO - 第 1 页获取到 6 条记录
2025-05-21 21:32:09,527 - INFO - 查询完成，共获取到 6 条记录
2025-05-21 21:32:09,527 - INFO - 获取到 6 条表单数据
2025-05-21 21:32:09,527 - INFO - 当前日期 2025-05-21 有 6 条MySQL数据需要处理
2025-05-21 21:32:09,527 - INFO - 日期 2025-05-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 21:32:09,527 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-21 21:32:09,527 - INFO - 同步完成
2025-05-21 22:30:33,248 - INFO - 使用默认增量同步（当天更新数据）
2025-05-21 22:30:33,248 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 22:30:33,248 - INFO - 查询参数: ('2025-05-21',)
2025-05-21 22:30:33,310 - INFO - MySQL查询成功，增量数据（日期: 2025-05-21），共获取 226 条记录
2025-05-21 22:30:33,310 - INFO - 获取到 3 个日期需要处理: ['2025-05-19', '2025-05-20', '2025-05-21']
2025-05-21 22:30:33,326 - INFO - 开始处理日期: 2025-05-19
2025-05-21 22:30:33,326 - INFO - Request Parameters - Page 1:
2025-05-21 22:30:33,326 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 22:30:33,326 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 22:30:41,450 - ERROR - 处理日期 2025-05-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: BA7F3C9A-4FD5-7253-A8DE-457DE3044FEF Response: {'code': 'ServiceUnavailable', 'requestid': 'BA7F3C9A-4FD5-7253-A8DE-457DE3044FEF', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: BA7F3C9A-4FD5-7253-A8DE-457DE3044FEF)
2025-05-21 22:30:41,450 - INFO - 开始处理日期: 2025-05-20
2025-05-21 22:30:41,450 - INFO - Request Parameters - Page 1:
2025-05-21 22:30:41,450 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 22:30:41,450 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 22:30:49,559 - ERROR - 处理日期 2025-05-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 43376774-5E70-7D18-BB86-2ABA8C7E575C Response: {'code': 'ServiceUnavailable', 'requestid': '43376774-5E70-7D18-BB86-2ABA8C7E575C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 43376774-5E70-7D18-BB86-2ABA8C7E575C)
2025-05-21 22:30:49,559 - INFO - 开始处理日期: 2025-05-21
2025-05-21 22:30:49,559 - INFO - Request Parameters - Page 1:
2025-05-21 22:30:49,559 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 22:30:49,559 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 22:30:50,121 - INFO - Response - Page 1:
2025-05-21 22:30:50,121 - INFO - 第 1 页获取到 6 条记录
2025-05-21 22:30:50,324 - INFO - 查询完成，共获取到 6 条记录
2025-05-21 22:30:50,324 - INFO - 获取到 6 条表单数据
2025-05-21 22:30:50,324 - INFO - 当前日期 2025-05-21 有 14 条MySQL数据需要处理
2025-05-21 22:30:50,324 - INFO - 开始批量插入 8 条新记录
2025-05-21 22:30:50,465 - INFO - 批量插入响应状态码: 200
2025-05-21 22:30:50,465 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 21 May 2025 14:30:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '404', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '9F3E29E7-B69C-76BC-8411-C192DF6AB86A', 'x-acs-trace-id': 'e031616743232773dc6f1a45caa01caf', 'etag': '4z9I9MjjHxi39/lDVpof1XA4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-21 22:30:50,465 - INFO - 批量插入响应体: {'result': ['FINST-YPE66RB1EDKVFLP37STVA41QUQAG2HP1J1YAMQ11', 'FINST-YPE66RB1EDKVFLP37STVA41QUQAG2HP1J1YAMR11', 'FINST-YPE66RB1EDKVFLP37STVA41QUQAG2HP1J1YAMS11', 'FINST-YPE66RB1EDKVFLP37STVA41QUQAG2HP1J1YAMT11', 'FINST-YPE66RB1EDKVFLP37STVA41QUQAG2HP1J1YAMU11', 'FINST-YPE66RB1EDKVFLP37STVA41QUQAG2HP1J1YAMV11', 'FINST-YPE66RB1EDKVFLP37STVA41QUQAG2HP1J1YAMW11', 'FINST-YPE66RB1EDKVFLP37STVA41QUQAG2HP1J1YAMX11']}
2025-05-21 22:30:50,465 - INFO - 批量插入表单数据成功，批次 1，共 8 条记录
2025-05-21 22:30:50,465 - INFO - 成功插入的数据ID: ['FINST-YPE66RB1EDKVFLP37STVA41QUQAG2HP1J1YAMQ11', 'FINST-YPE66RB1EDKVFLP37STVA41QUQAG2HP1J1YAMR11', 'FINST-YPE66RB1EDKVFLP37STVA41QUQAG2HP1J1YAMS11', 'FINST-YPE66RB1EDKVFLP37STVA41QUQAG2HP1J1YAMT11', 'FINST-YPE66RB1EDKVFLP37STVA41QUQAG2HP1J1YAMU11', 'FINST-YPE66RB1EDKVFLP37STVA41QUQAG2HP1J1YAMV11', 'FINST-YPE66RB1EDKVFLP37STVA41QUQAG2HP1J1YAMW11', 'FINST-YPE66RB1EDKVFLP37STVA41QUQAG2HP1J1YAMX11']
2025-05-21 22:30:55,480 - INFO - 批量插入完成，共 8 条记录
2025-05-21 22:30:55,480 - INFO - 日期 2025-05-21 处理完成 - 更新: 0 条，插入: 8 条，错误: 0 条
2025-05-21 22:30:55,480 - INFO - 数据同步完成！更新: 0 条，插入: 8 条，错误: 2 条
2025-05-21 22:31:55,488 - INFO - 开始同步昨天与今天的销售数据: 2025-05-20 至 2025-05-21
2025-05-21 22:31:55,488 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 22:31:55,488 - INFO - 查询参数: ('2025-05-20', '2025-05-21')
2025-05-21 22:31:55,567 - INFO - MySQL查询成功，时间段: 2025-05-20 至 2025-05-21，共获取 610 条记录
2025-05-21 22:31:55,567 - INFO - 获取到 2 个日期需要处理: ['2025-05-20', '2025-05-21']
2025-05-21 22:31:55,567 - INFO - 开始处理日期: 2025-05-20
2025-05-21 22:31:55,567 - INFO - Request Parameters - Page 1:
2025-05-21 22:31:55,567 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 22:31:55,567 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 22:31:56,473 - INFO - Response - Page 1:
2025-05-21 22:31:56,473 - INFO - 第 1 页获取到 100 条记录
2025-05-21 22:31:56,676 - INFO - Request Parameters - Page 2:
2025-05-21 22:31:56,676 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 22:31:56,676 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 22:31:57,457 - INFO - Response - Page 2:
2025-05-21 22:31:57,457 - INFO - 第 2 页获取到 100 条记录
2025-05-21 22:31:57,660 - INFO - Request Parameters - Page 3:
2025-05-21 22:31:57,660 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 22:31:57,660 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 22:31:58,441 - INFO - Response - Page 3:
2025-05-21 22:31:58,441 - INFO - 第 3 页获取到 100 条记录
2025-05-21 22:31:58,644 - INFO - Request Parameters - Page 4:
2025-05-21 22:31:58,644 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 22:31:58,644 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 22:31:59,425 - INFO - Response - Page 4:
2025-05-21 22:31:59,425 - INFO - 第 4 页获取到 100 条记录
2025-05-21 22:31:59,629 - INFO - Request Parameters - Page 5:
2025-05-21 22:31:59,629 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 22:31:59,629 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 22:32:00,410 - INFO - Response - Page 5:
2025-05-21 22:32:00,410 - INFO - 第 5 页获取到 100 条记录
2025-05-21 22:32:00,613 - INFO - Request Parameters - Page 6:
2025-05-21 22:32:00,613 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 22:32:00,613 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 22:32:01,566 - INFO - Response - Page 6:
2025-05-21 22:32:01,566 - INFO - 第 6 页获取到 96 条记录
2025-05-21 22:32:01,769 - INFO - 查询完成，共获取到 596 条记录
2025-05-21 22:32:01,769 - INFO - 获取到 596 条表单数据
2025-05-21 22:32:01,769 - INFO - 当前日期 2025-05-20 有 596 条MySQL数据需要处理
2025-05-21 22:32:01,785 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 22:32:01,785 - INFO - 开始处理日期: 2025-05-21
2025-05-21 22:32:01,785 - INFO - Request Parameters - Page 1:
2025-05-21 22:32:01,785 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 22:32:01,785 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 22:32:02,347 - INFO - Response - Page 1:
2025-05-21 22:32:02,347 - INFO - 第 1 页获取到 14 条记录
2025-05-21 22:32:02,550 - INFO - 查询完成，共获取到 14 条记录
2025-05-21 22:32:02,550 - INFO - 获取到 14 条表单数据
2025-05-21 22:32:02,550 - INFO - 当前日期 2025-05-21 有 14 条MySQL数据需要处理
2025-05-21 22:32:02,550 - INFO - 日期 2025-05-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 22:32:02,550 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 22:32:02,550 - INFO - 同步完成
2025-05-21 23:30:35,429 - INFO - 使用默认增量同步（当天更新数据）
2025-05-21 23:30:35,429 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 23:30:35,429 - INFO - 查询参数: ('2025-05-21',)
2025-05-21 23:30:35,507 - INFO - MySQL查询成功，增量数据（日期: 2025-05-21），共获取 228 条记录
2025-05-21 23:30:35,507 - INFO - 获取到 3 个日期需要处理: ['2025-05-19', '2025-05-20', '2025-05-21']
2025-05-21 23:30:35,507 - INFO - 开始处理日期: 2025-05-19
2025-05-21 23:30:35,507 - INFO - Request Parameters - Page 1:
2025-05-21 23:30:35,507 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 23:30:35,507 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747584000000, 1747670399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 23:30:43,616 - ERROR - 处理日期 2025-05-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A79EB598-38CD-7498-AAC7-9C316A6372CE Response: {'code': 'ServiceUnavailable', 'requestid': 'A79EB598-38CD-7498-AAC7-9C316A6372CE', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A79EB598-38CD-7498-AAC7-9C316A6372CE)
2025-05-21 23:30:43,616 - INFO - 开始处理日期: 2025-05-20
2025-05-21 23:30:43,616 - INFO - Request Parameters - Page 1:
2025-05-21 23:30:43,616 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 23:30:43,616 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 23:30:51,740 - ERROR - 处理日期 2025-05-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 01E199E1-E200-7AEE-BC87-47CF6FCEC417 Response: {'code': 'ServiceUnavailable', 'requestid': '01E199E1-E200-7AEE-BC87-47CF6FCEC417', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 01E199E1-E200-7AEE-BC87-47CF6FCEC417)
2025-05-21 23:30:51,740 - INFO - 开始处理日期: 2025-05-21
2025-05-21 23:30:51,740 - INFO - Request Parameters - Page 1:
2025-05-21 23:30:51,740 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 23:30:51,740 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 23:30:52,318 - INFO - Response - Page 1:
2025-05-21 23:30:52,318 - INFO - 第 1 页获取到 14 条记录
2025-05-21 23:30:52,521 - INFO - 查询完成，共获取到 14 条记录
2025-05-21 23:30:52,521 - INFO - 获取到 14 条表单数据
2025-05-21 23:30:52,521 - INFO - 当前日期 2025-05-21 有 16 条MySQL数据需要处理
2025-05-21 23:30:52,521 - INFO - 开始批量插入 2 条新记录
2025-05-21 23:30:52,693 - INFO - 批量插入响应状态码: 200
2025-05-21 23:30:52,693 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 21 May 2025 15:30:52 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '944C441B-3D28-7129-9A78-91EF19270BC5', 'x-acs-trace-id': 'd45e75d369485a42b66d765511cf2b8e', 'etag': '11RhvLJxeQYEbYV+a+DYCtQ8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-21 23:30:52,693 - INFO - 批量插入响应体: {'result': ['FINST-3RE66ZB1AZHVDMBT7W8D09W3Z38M38A8O3YAMBQ', 'FINST-3RE66ZB1AZHVDMBT7W8D09W3Z38M38A8O3YAMCQ']}
2025-05-21 23:30:52,693 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-05-21 23:30:52,693 - INFO - 成功插入的数据ID: ['FINST-3RE66ZB1AZHVDMBT7W8D09W3Z38M38A8O3YAMBQ', 'FINST-3RE66ZB1AZHVDMBT7W8D09W3Z38M38A8O3YAMCQ']
2025-05-21 23:30:57,709 - INFO - 批量插入完成，共 2 条记录
2025-05-21 23:30:57,709 - INFO - 日期 2025-05-21 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-05-21 23:30:57,709 - INFO - 数据同步完成！更新: 0 条，插入: 2 条，错误: 2 条
2025-05-21 23:31:57,720 - INFO - 开始同步昨天与今天的销售数据: 2025-05-20 至 2025-05-21
2025-05-21 23:31:57,720 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
                    and c.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-21 23:31:57,720 - INFO - 查询参数: ('2025-05-20', '2025-05-21')
2025-05-21 23:31:57,798 - INFO - MySQL查询成功，时间段: 2025-05-20 至 2025-05-21，共获取 612 条记录
2025-05-21 23:31:57,798 - INFO - 获取到 2 个日期需要处理: ['2025-05-20', '2025-05-21']
2025-05-21 23:31:57,798 - INFO - 开始处理日期: 2025-05-20
2025-05-21 23:31:57,798 - INFO - Request Parameters - Page 1:
2025-05-21 23:31:57,798 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 23:31:57,798 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 23:31:58,658 - INFO - Response - Page 1:
2025-05-21 23:31:58,658 - INFO - 第 1 页获取到 100 条记录
2025-05-21 23:31:58,861 - INFO - Request Parameters - Page 2:
2025-05-21 23:31:58,861 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 23:31:58,861 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 23:31:59,642 - INFO - Response - Page 2:
2025-05-21 23:31:59,642 - INFO - 第 2 页获取到 100 条记录
2025-05-21 23:31:59,845 - INFO - Request Parameters - Page 3:
2025-05-21 23:31:59,845 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 23:31:59,845 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 23:32:00,720 - INFO - Response - Page 3:
2025-05-21 23:32:00,720 - INFO - 第 3 页获取到 100 条记录
2025-05-21 23:32:00,923 - INFO - Request Parameters - Page 4:
2025-05-21 23:32:00,923 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 23:32:00,923 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 23:32:01,751 - INFO - Response - Page 4:
2025-05-21 23:32:01,751 - INFO - 第 4 页获取到 100 条记录
2025-05-21 23:32:01,954 - INFO - Request Parameters - Page 5:
2025-05-21 23:32:01,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 23:32:01,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 23:32:02,767 - INFO - Response - Page 5:
2025-05-21 23:32:02,767 - INFO - 第 5 页获取到 100 条记录
2025-05-21 23:32:02,970 - INFO - Request Parameters - Page 6:
2025-05-21 23:32:02,970 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 23:32:02,970 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747670400000, 1747756799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 23:32:03,782 - INFO - Response - Page 6:
2025-05-21 23:32:03,782 - INFO - 第 6 页获取到 96 条记录
2025-05-21 23:32:03,986 - INFO - 查询完成，共获取到 596 条记录
2025-05-21 23:32:03,986 - INFO - 获取到 596 条表单数据
2025-05-21 23:32:03,986 - INFO - 当前日期 2025-05-20 有 596 条MySQL数据需要处理
2025-05-21 23:32:04,001 - INFO - 日期 2025-05-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 23:32:04,001 - INFO - 开始处理日期: 2025-05-21
2025-05-21 23:32:04,001 - INFO - Request Parameters - Page 1:
2025-05-21 23:32:04,001 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-21 23:32:04,001 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1747756800000, 1747843199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-21 23:32:04,611 - INFO - Response - Page 1:
2025-05-21 23:32:04,611 - INFO - 第 1 页获取到 16 条记录
2025-05-21 23:32:04,814 - INFO - 查询完成，共获取到 16 条记录
2025-05-21 23:32:04,814 - INFO - 获取到 16 条表单数据
2025-05-21 23:32:04,814 - INFO - 当前日期 2025-05-21 有 16 条MySQL数据需要处理
2025-05-21 23:32:04,814 - INFO - 日期 2025-05-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 23:32:04,814 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-21 23:32:04,814 - INFO - 同步完成
