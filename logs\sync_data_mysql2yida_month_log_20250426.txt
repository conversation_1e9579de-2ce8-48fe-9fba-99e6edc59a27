2025-04-26 16:49:51,813 - INFO - =================使用默认全量同步=============
2025-04-26 16:49:52,921 - INFO - MySQL查询成功，共获取 2638 条记录
2025-04-26 16:49:52,921 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-04-26 16:49:52,952 - INFO - 开始处理日期: 2025-01
2025-04-26 16:49:52,952 - INFO - Request Parameters - Page 1:
2025-04-26 16:49:52,952 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 16:49:52,952 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 16:49:53,828 - INFO - Response - Page 1:
2025-04-26 16:49:54,031 - INFO - 第 1 页获取到 100 条记录
2025-04-26 16:49:54,031 - INFO - Request Parameters - Page 2:
2025-04-26 16:49:54,031 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 16:49:54,031 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 16:49:54,576 - INFO - Response - Page 2:
2025-04-26 16:49:54,778 - INFO - 第 2 页获取到 100 条记录
2025-04-26 16:49:54,779 - INFO - Request Parameters - Page 3:
2025-04-26 16:49:54,779 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 16:49:54,779 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 16:49:55,294 - INFO - Response - Page 3:
2025-04-26 16:49:55,494 - INFO - 第 3 页获取到 100 条记录
2025-04-26 16:49:55,494 - INFO - Request Parameters - Page 4:
2025-04-26 16:49:55,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 16:49:55,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 16:49:56,122 - INFO - Response - Page 4:
2025-04-26 16:49:56,323 - INFO - 第 4 页获取到 100 条记录
2025-04-26 16:49:56,323 - INFO - Request Parameters - Page 5:
2025-04-26 16:49:56,323 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 16:49:56,323 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 16:49:56,764 - INFO - Response - Page 5:
2025-04-26 16:49:56,973 - INFO - 第 5 页获取到 100 条记录
2025-04-26 16:49:56,973 - INFO - Request Parameters - Page 6:
2025-04-26 16:49:56,973 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 16:49:56,973 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 16:49:57,442 - INFO - Response - Page 6:
2025-04-26 16:49:57,643 - INFO - 第 6 页获取到 100 条记录
2025-04-26 16:49:57,643 - INFO - Request Parameters - Page 7:
2025-04-26 16:49:57,643 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 16:49:57,643 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 16:49:58,085 - INFO - Response - Page 7:
2025-04-26 16:49:58,288 - INFO - 第 7 页获取到 82 条记录
2025-04-26 16:49:58,288 - INFO - 查询完成，共获取到 682 条记录
2025-04-26 16:49:58,288 - INFO - 获取到 682 条表单数据
2025-04-26 16:49:58,288 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-04-26 16:49:58,304 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-26 16:49:58,304 - INFO - 开始处理日期: 2025-02
2025-04-26 16:49:58,304 - INFO - Request Parameters - Page 1:
2025-04-26 16:49:58,304 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 16:49:58,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 16:49:58,863 - INFO - Response - Page 1:
2025-04-26 16:49:59,064 - INFO - 第 1 页获取到 100 条记录
2025-04-26 16:49:59,064 - INFO - Request Parameters - Page 2:
2025-04-26 16:49:59,064 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 16:49:59,064 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 16:49:59,550 - INFO - Response - Page 2:
2025-04-26 16:49:59,750 - INFO - 第 2 页获取到 100 条记录
2025-04-26 16:49:59,750 - INFO - Request Parameters - Page 3:
2025-04-26 16:49:59,750 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 16:49:59,750 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 16:50:00,344 - INFO - Response - Page 3:
2025-04-26 16:50:00,552 - INFO - 第 3 页获取到 100 条记录
2025-04-26 16:50:00,552 - INFO - Request Parameters - Page 4:
2025-04-26 16:50:00,552 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 16:50:00,552 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 16:50:01,135 - INFO - Response - Page 4:
2025-04-26 16:50:01,336 - INFO - 第 4 页获取到 100 条记录
2025-04-26 16:50:01,336 - INFO - Request Parameters - Page 5:
2025-04-26 16:50:01,336 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 16:50:01,336 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 16:50:01,819 - INFO - Response - Page 5:
2025-04-26 16:50:02,020 - INFO - 第 5 页获取到 100 条记录
2025-04-26 16:50:02,020 - INFO - Request Parameters - Page 6:
2025-04-26 16:50:02,020 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 16:50:02,020 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 16:50:02,491 - INFO - Response - Page 6:
2025-04-26 16:50:02,691 - INFO - 第 6 页获取到 100 条记录
2025-04-26 16:50:02,691 - INFO - Request Parameters - Page 7:
2025-04-26 16:50:02,691 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 16:50:02,691 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 16:50:03,146 - INFO - Response - Page 7:
2025-04-26 16:50:03,346 - INFO - 第 7 页获取到 70 条记录
2025-04-26 16:50:03,346 - INFO - 查询完成，共获取到 670 条记录
2025-04-26 16:50:03,346 - INFO - 获取到 670 条表单数据
2025-04-26 16:50:03,359 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-04-26 16:50:03,372 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-26 16:50:03,372 - INFO - 开始处理日期: 2025-03
2025-04-26 16:50:03,372 - INFO - Request Parameters - Page 1:
2025-04-26 16:50:03,372 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 16:50:03,372 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 16:50:03,885 - INFO - Response - Page 1:
2025-04-26 16:50:04,085 - INFO - 第 1 页获取到 100 条记录
2025-04-26 16:50:04,085 - INFO - Request Parameters - Page 2:
2025-04-26 16:50:04,085 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 16:50:04,085 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 16:50:04,581 - INFO - Response - Page 2:
2025-04-26 16:50:04,781 - INFO - 第 2 页获取到 100 条记录
2025-04-26 16:50:04,781 - INFO - Request Parameters - Page 3:
2025-04-26 16:50:04,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 16:50:04,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 16:50:05,286 - INFO - Response - Page 3:
2025-04-26 16:50:05,487 - INFO - 第 3 页获取到 100 条记录
2025-04-26 16:50:05,487 - INFO - Request Parameters - Page 4:
2025-04-26 16:50:05,487 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 16:50:05,487 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 16:50:05,975 - INFO - Response - Page 4:
2025-04-26 16:50:06,191 - INFO - 第 4 页获取到 100 条记录
2025-04-26 16:50:06,191 - INFO - Request Parameters - Page 5:
2025-04-26 16:50:06,191 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 16:50:06,191 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 16:50:06,675 - INFO - Response - Page 5:
2025-04-26 16:50:06,879 - INFO - 第 5 页获取到 100 条记录
2025-04-26 16:50:06,879 - INFO - Request Parameters - Page 6:
2025-04-26 16:50:06,879 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 16:50:06,879 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 16:50:07,363 - INFO - Response - Page 6:
2025-04-26 16:50:07,564 - INFO - 第 6 页获取到 100 条记录
2025-04-26 16:50:07,564 - INFO - Request Parameters - Page 7:
2025-04-26 16:50:07,564 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 16:50:07,564 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 16:50:07,996 - INFO - Response - Page 7:
2025-04-26 16:50:08,204 - INFO - 第 7 页获取到 61 条记录
2025-04-26 16:50:08,204 - INFO - 查询完成，共获取到 661 条记录
2025-04-26 16:50:08,204 - INFO - 获取到 661 条表单数据
2025-04-26 16:50:08,215 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-04-26 16:50:08,226 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-26 16:50:08,226 - INFO - 开始处理日期: 2025-04
2025-04-26 16:50:08,227 - INFO - Request Parameters - Page 1:
2025-04-26 16:50:08,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 16:50:08,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 16:50:08,721 - INFO - Response - Page 1:
2025-04-26 16:50:08,921 - INFO - 第 1 页获取到 100 条记录
2025-04-26 16:50:08,921 - INFO - Request Parameters - Page 2:
2025-04-26 16:50:08,921 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 16:50:08,921 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 16:50:09,417 - INFO - Response - Page 2:
2025-04-26 16:50:09,617 - INFO - 第 2 页获取到 100 条记录
2025-04-26 16:50:09,617 - INFO - Request Parameters - Page 3:
2025-04-26 16:50:09,617 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 16:50:09,617 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 16:50:10,194 - INFO - Response - Page 3:
2025-04-26 16:50:10,397 - INFO - 第 3 页获取到 100 条记录
2025-04-26 16:50:10,397 - INFO - Request Parameters - Page 4:
2025-04-26 16:50:10,397 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 16:50:10,397 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 16:50:10,910 - INFO - Response - Page 4:
2025-04-26 16:50:11,110 - INFO - 第 4 页获取到 100 条记录
2025-04-26 16:50:11,110 - INFO - Request Parameters - Page 5:
2025-04-26 16:50:11,110 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 16:50:11,110 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 16:50:11,607 - INFO - Response - Page 5:
2025-04-26 16:50:11,808 - INFO - 第 5 页获取到 100 条记录
2025-04-26 16:50:11,808 - INFO - Request Parameters - Page 6:
2025-04-26 16:50:11,808 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 16:50:11,808 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 16:50:12,310 - INFO - Response - Page 6:
2025-04-26 16:50:12,511 - INFO - 第 6 页获取到 100 条记录
2025-04-26 16:50:12,511 - INFO - Request Parameters - Page 7:
2025-04-26 16:50:12,511 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 16:50:12,511 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 16:50:12,846 - INFO - Response - Page 7:
2025-04-26 16:50:13,047 - INFO - 第 7 页获取到 25 条记录
2025-04-26 16:50:13,047 - INFO - 查询完成，共获取到 625 条记录
2025-04-26 16:50:13,047 - INFO - 获取到 625 条表单数据
2025-04-26 16:50:13,058 - INFO - 当前日期 2025-04 有 625 条MySQL数据需要处理
2025-04-26 16:50:13,059 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M53
2025-04-26 16:50:13,555 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9M53
2025-04-26 16:50:13,555 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13102.0, 'new_value': 14917.0}, {'field': 'total_amount', 'old_value': 13102.0, 'new_value': 14917.0}, {'field': 'order_count', 'old_value': 2642, 'new_value': 2912}]
2025-04-26 16:50:13,555 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M44
2025-04-26 16:50:14,133 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M44
2025-04-26 16:50:14,133 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15021.6, 'new_value': 16798.0}, {'field': 'total_amount', 'old_value': 15122.6, 'new_value': 16899.0}, {'field': 'order_count', 'old_value': 1228, 'new_value': 1364}]
2025-04-26 16:50:14,133 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M94
2025-04-26 16:50:14,604 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M94
2025-04-26 16:50:14,604 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31375.71, 'new_value': 33808.45}, {'field': 'offline_amount', 'old_value': 103702.31, 'new_value': 113404.41}, {'field': 'total_amount', 'old_value': 135078.02, 'new_value': 147212.86}, {'field': 'order_count', 'old_value': 1837, 'new_value': 2005}]
2025-04-26 16:50:14,604 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MC4
2025-04-26 16:50:15,096 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MC4
2025-04-26 16:50:15,096 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73297.15, 'new_value': 80551.23}, {'field': 'total_amount', 'old_value': 73297.15, 'new_value': 80551.23}, {'field': 'order_count', 'old_value': 3836, 'new_value': 4216}]
2025-04-26 16:50:15,096 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M05
2025-04-26 16:50:15,581 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M05
2025-04-26 16:50:15,581 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53458.0, 'new_value': 56948.0}, {'field': 'total_amount', 'old_value': 53458.0, 'new_value': 56948.0}, {'field': 'order_count', 'old_value': 807, 'new_value': 851}]
2025-04-26 16:50:15,581 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M45
2025-04-26 16:50:15,972 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M45
2025-04-26 16:50:15,972 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3040.0, 'new_value': 3342.0}, {'field': 'offline_amount', 'old_value': 41471.0, 'new_value': 43709.0}, {'field': 'total_amount', 'old_value': 44511.0, 'new_value': 47051.0}, {'field': 'order_count', 'old_value': 490, 'new_value': 532}]
2025-04-26 16:50:15,972 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MM5
2025-04-26 16:50:16,378 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MM5
2025-04-26 16:50:16,378 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38433.8, 'new_value': 40294.8}, {'field': 'total_amount', 'old_value': 38433.8, 'new_value': 40294.8}, {'field': 'order_count', 'old_value': 200, 'new_value': 215}]
2025-04-26 16:50:16,378 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MA9
2025-04-26 16:50:16,738 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E29GVFMW9MA9
2025-04-26 16:50:16,738 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55561.0, 'new_value': 58158.0}, {'field': 'offline_amount', 'old_value': 179569.45, 'new_value': 185286.45}, {'field': 'total_amount', 'old_value': 235130.45, 'new_value': 243444.45}, {'field': 'order_count', 'old_value': 1631, 'new_value': 1684}]
2025-04-26 16:50:16,738 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MY9
2025-04-26 16:50:17,239 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MY9
2025-04-26 16:50:17,239 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31319.99, 'new_value': 60202.99}, {'field': 'total_amount', 'old_value': 642824.06, 'new_value': 671707.06}, {'field': 'order_count', 'old_value': 2158, 'new_value': 2234}]
2025-04-26 16:50:17,239 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M36
2025-04-26 16:50:17,739 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M36
2025-04-26 16:50:17,739 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 188812.5, 'new_value': 198080.5}, {'field': 'total_amount', 'old_value': 292990.5, 'new_value': 302258.5}, {'field': 'order_count', 'old_value': 61, 'new_value': 62}]
2025-04-26 16:50:17,739 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M38
2025-04-26 16:50:18,177 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M38
2025-04-26 16:50:18,177 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49634.5, 'new_value': 51693.2}, {'field': 'total_amount', 'old_value': 53382.5, 'new_value': 55441.2}, {'field': 'order_count', 'old_value': 54, 'new_value': 58}]
2025-04-26 16:50:18,177 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MVH
2025-04-26 16:50:18,662 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MVH
2025-04-26 16:50:18,662 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63349.84, 'new_value': 69393.84}, {'field': 'total_amount', 'old_value': 63349.84, 'new_value': 69393.84}, {'field': 'order_count', 'old_value': 3429, 'new_value': 3782}]
2025-04-26 16:50:18,662 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MII
2025-04-26 16:50:19,164 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MII
2025-04-26 16:50:19,164 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11822.4, 'new_value': 12593.04}, {'field': 'offline_amount', 'old_value': 63602.67, 'new_value': 69886.9}, {'field': 'total_amount', 'old_value': 75425.07, 'new_value': 82479.94}, {'field': 'order_count', 'old_value': 1941, 'new_value': 2123}]
2025-04-26 16:50:19,166 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MH1
2025-04-26 16:50:19,624 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MH1
2025-04-26 16:50:19,624 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55194.0, 'new_value': 60563.0}, {'field': 'total_amount', 'old_value': 55194.0, 'new_value': 60563.0}, {'field': 'order_count', 'old_value': 11923, 'new_value': 13071}]
2025-04-26 16:50:19,624 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MI1
2025-04-26 16:50:20,125 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9MI1
2025-04-26 16:50:20,125 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82797.0, 'new_value': 90850.0}, {'field': 'total_amount', 'old_value': 82797.0, 'new_value': 90850.0}, {'field': 'order_count', 'old_value': 11923, 'new_value': 13071}]
2025-04-26 16:50:20,125 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MF2
2025-04-26 16:50:20,625 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MF2
2025-04-26 16:50:20,625 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96968.0, 'new_value': 107769.0}, {'field': 'total_amount', 'old_value': 96968.0, 'new_value': 107769.0}, {'field': 'order_count', 'old_value': 259, 'new_value': 287}]
2025-04-26 16:50:20,641 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MQ2
2025-04-26 16:50:21,114 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2OQ7GMW9MQ2
2025-04-26 16:50:21,115 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24582.06, 'new_value': 26758.32}, {'field': 'offline_amount', 'old_value': 23793.88, 'new_value': 25877.12}, {'field': 'total_amount', 'old_value': 48375.94, 'new_value': 52635.44}, {'field': 'order_count', 'old_value': 2647, 'new_value': 2883}]
2025-04-26 16:50:21,115 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M46
2025-04-26 16:50:21,624 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M46
2025-04-26 16:50:21,625 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13258.31, 'new_value': 13595.11}, {'field': 'offline_amount', 'old_value': 109860.65, 'new_value': 119038.35}, {'field': 'total_amount', 'old_value': 123118.96, 'new_value': 132633.46}, {'field': 'order_count', 'old_value': 1827, 'new_value': 2015}]
2025-04-26 16:50:21,626 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M77
2025-04-26 16:50:22,076 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M77
2025-04-26 16:50:22,076 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27518.0, 'new_value': 28523.0}, {'field': 'total_amount', 'old_value': 27518.0, 'new_value': 28523.0}, {'field': 'order_count', 'old_value': 2565, 'new_value': 2663}]
2025-04-26 16:50:22,077 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MP7
2025-04-26 16:50:22,504 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MP7
2025-04-26 16:50:22,504 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86481.0, 'new_value': 94369.0}, {'field': 'total_amount', 'old_value': 86481.0, 'new_value': 94369.0}, {'field': 'order_count', 'old_value': 598, 'new_value': 627}]
2025-04-26 16:50:22,504 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MU7
2025-04-26 16:50:23,007 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MU7
2025-04-26 16:50:23,007 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 518000.0, 'new_value': 524000.0}, {'field': 'total_amount', 'old_value': 518000.0, 'new_value': 524000.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-04-26 16:50:23,007 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MV7
2025-04-26 16:50:23,461 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MV7
2025-04-26 16:50:23,462 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 149548.0, 'new_value': 172928.0}, {'field': 'total_amount', 'old_value': 149548.0, 'new_value': 172928.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 20}]
2025-04-26 16:50:23,462 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MW7
2025-04-26 16:50:23,961 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MW7
2025-04-26 16:50:23,961 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2120.0, 'new_value': 3100.0}, {'field': 'total_amount', 'old_value': 2120.0, 'new_value': 3100.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-04-26 16:50:23,961 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MX7
2025-04-26 16:50:24,431 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MX7
2025-04-26 16:50:24,431 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 222444.0, 'new_value': 246444.0}, {'field': 'total_amount', 'old_value': 222444.0, 'new_value': 246444.0}, {'field': 'order_count', 'old_value': 619, 'new_value': 620}]
2025-04-26 16:50:24,431 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MY7
2025-04-26 16:50:24,861 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MY7
2025-04-26 16:50:24,861 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12662.3, 'new_value': 13016.9}, {'field': 'total_amount', 'old_value': 12662.3, 'new_value': 13016.9}, {'field': 'order_count', 'old_value': 41, 'new_value': 43}]
2025-04-26 16:50:24,861 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M68
2025-04-26 16:50:25,291 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M68
2025-04-26 16:50:25,291 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12978.0, 'new_value': 14778.0}, {'field': 'total_amount', 'old_value': 12978.0, 'new_value': 14778.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-04-26 16:50:25,291 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M78
2025-04-26 16:50:25,723 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M78
2025-04-26 16:50:25,723 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41421.8, 'new_value': 44014.4}, {'field': 'total_amount', 'old_value': 41421.8, 'new_value': 44014.4}, {'field': 'order_count', 'old_value': 438, 'new_value': 463}]
2025-04-26 16:50:25,723 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M88
2025-04-26 16:50:26,183 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M88
2025-04-26 16:50:26,183 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109760.0, 'new_value': 113868.0}, {'field': 'total_amount', 'old_value': 109760.0, 'new_value': 113868.0}, {'field': 'order_count', 'old_value': 3876, 'new_value': 4064}]
2025-04-26 16:50:26,183 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M98
2025-04-26 16:50:26,617 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9M98
2025-04-26 16:50:26,617 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 155067.4, 'new_value': 161161.4}, {'field': 'total_amount', 'old_value': 161462.4, 'new_value': 167556.4}, {'field': 'order_count', 'old_value': 965, 'new_value': 966}]
2025-04-26 16:50:26,617 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MA8
2025-04-26 16:50:27,059 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MA8
2025-04-26 16:50:27,059 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63862.0, 'new_value': 74414.0}, {'field': 'total_amount', 'old_value': 63862.0, 'new_value': 74414.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 19}]
2025-04-26 16:50:27,060 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MD8
2025-04-26 16:50:27,515 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MD8
2025-04-26 16:50:27,515 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51018.0, 'new_value': 56898.0}, {'field': 'total_amount', 'old_value': 51019.0, 'new_value': 56899.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-04-26 16:50:27,515 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ML8
2025-04-26 16:50:28,006 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9ML8
2025-04-26 16:50:28,006 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 140481.49, 'new_value': 145979.49}, {'field': 'total_amount', 'old_value': 140481.49, 'new_value': 145979.49}, {'field': 'order_count', 'old_value': 12218, 'new_value': 12442}]
2025-04-26 16:50:28,006 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MO8
2025-04-26 16:50:28,486 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MO8
2025-04-26 16:50:28,486 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47214.0, 'new_value': 52357.0}, {'field': 'total_amount', 'old_value': 47214.0, 'new_value': 52357.0}, {'field': 'order_count', 'old_value': 47, 'new_value': 52}]
2025-04-26 16:50:28,487 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M22
2025-04-26 16:50:28,917 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M22
2025-04-26 16:50:28,917 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109178.0, 'new_value': 115131.0}, {'field': 'total_amount', 'old_value': 109178.0, 'new_value': 115131.0}, {'field': 'order_count', 'old_value': 380, 'new_value': 399}]
2025-04-26 16:50:28,917 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M42
2025-04-26 16:50:29,372 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9M42
2025-04-26 16:50:29,372 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11179.33, 'new_value': 11599.33}, {'field': 'total_amount', 'old_value': 11179.33, 'new_value': 11599.33}, {'field': 'order_count', 'old_value': 299, 'new_value': 304}]
2025-04-26 16:50:29,372 - INFO - 日期 2025-04 处理完成 - 更新: 35 条，插入: 0 条，错误: 0 条
2025-04-26 16:50:29,372 - INFO - 数据同步完成！更新: 35 条，插入: 0 条，错误: 0 条
2025-04-26 16:50:29,374 - INFO - =================同步完成====================
2025-04-26 18:00:03,166 - INFO - =================使用默认全量同步=============
2025-04-26 18:00:04,292 - INFO - MySQL查询成功，共获取 2638 条记录
2025-04-26 18:00:04,292 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-04-26 18:00:04,308 - INFO - 开始处理日期: 2025-01
2025-04-26 18:00:04,308 - INFO - Request Parameters - Page 1:
2025-04-26 18:00:04,308 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 18:00:04,308 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 18:00:05,293 - INFO - Response - Page 1:
2025-04-26 18:00:05,496 - INFO - 第 1 页获取到 100 条记录
2025-04-26 18:00:05,496 - INFO - Request Parameters - Page 2:
2025-04-26 18:00:05,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 18:00:05,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 18:00:06,403 - INFO - Response - Page 2:
2025-04-26 18:00:06,607 - INFO - 第 2 页获取到 100 条记录
2025-04-26 18:00:06,607 - INFO - Request Parameters - Page 3:
2025-04-26 18:00:06,607 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 18:00:06,607 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 18:00:07,138 - INFO - Response - Page 3:
2025-04-26 18:00:07,342 - INFO - 第 3 页获取到 100 条记录
2025-04-26 18:00:07,342 - INFO - Request Parameters - Page 4:
2025-04-26 18:00:07,342 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 18:00:07,342 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 18:00:07,905 - INFO - Response - Page 4:
2025-04-26 18:00:08,108 - INFO - 第 4 页获取到 100 条记录
2025-04-26 18:00:08,108 - INFO - Request Parameters - Page 5:
2025-04-26 18:00:08,108 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 18:00:08,108 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 18:00:08,624 - INFO - Response - Page 5:
2025-04-26 18:00:08,827 - INFO - 第 5 页获取到 100 条记录
2025-04-26 18:00:08,827 - INFO - Request Parameters - Page 6:
2025-04-26 18:00:08,827 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 18:00:08,827 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 18:00:09,296 - INFO - Response - Page 6:
2025-04-26 18:00:09,500 - INFO - 第 6 页获取到 100 条记录
2025-04-26 18:00:09,500 - INFO - Request Parameters - Page 7:
2025-04-26 18:00:09,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 18:00:09,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 18:00:09,953 - INFO - Response - Page 7:
2025-04-26 18:00:10,157 - INFO - 第 7 页获取到 82 条记录
2025-04-26 18:00:10,157 - INFO - 查询完成，共获取到 682 条记录
2025-04-26 18:00:10,157 - INFO - 获取到 682 条表单数据
2025-04-26 18:00:10,157 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-04-26 18:00:10,172 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-26 18:00:10,172 - INFO - 开始处理日期: 2025-02
2025-04-26 18:00:10,172 - INFO - Request Parameters - Page 1:
2025-04-26 18:00:10,172 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 18:00:10,172 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 18:00:10,626 - INFO - Response - Page 1:
2025-04-26 18:00:10,829 - INFO - 第 1 页获取到 100 条记录
2025-04-26 18:00:10,829 - INFO - Request Parameters - Page 2:
2025-04-26 18:00:10,829 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 18:00:10,829 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 18:00:11,314 - INFO - Response - Page 2:
2025-04-26 18:00:11,517 - INFO - 第 2 页获取到 100 条记录
2025-04-26 18:00:11,517 - INFO - Request Parameters - Page 3:
2025-04-26 18:00:11,517 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 18:00:11,517 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 18:00:12,018 - INFO - Response - Page 3:
2025-04-26 18:00:12,221 - INFO - 第 3 页获取到 100 条记录
2025-04-26 18:00:12,221 - INFO - Request Parameters - Page 4:
2025-04-26 18:00:12,221 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 18:00:12,221 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 18:00:12,628 - INFO - Response - Page 4:
2025-04-26 18:00:12,831 - INFO - 第 4 页获取到 100 条记录
2025-04-26 18:00:12,831 - INFO - Request Parameters - Page 5:
2025-04-26 18:00:12,831 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 18:00:12,831 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 18:00:13,331 - INFO - Response - Page 5:
2025-04-26 18:00:13,535 - INFO - 第 5 页获取到 100 条记录
2025-04-26 18:00:13,535 - INFO - Request Parameters - Page 6:
2025-04-26 18:00:13,535 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 18:00:13,535 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 18:00:13,988 - INFO - Response - Page 6:
2025-04-26 18:00:14,191 - INFO - 第 6 页获取到 100 条记录
2025-04-26 18:00:14,191 - INFO - Request Parameters - Page 7:
2025-04-26 18:00:14,191 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 18:00:14,191 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 18:00:14,629 - INFO - Response - Page 7:
2025-04-26 18:00:14,833 - INFO - 第 7 页获取到 70 条记录
2025-04-26 18:00:14,833 - INFO - 查询完成，共获取到 670 条记录
2025-04-26 18:00:14,833 - INFO - 获取到 670 条表单数据
2025-04-26 18:00:14,833 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-04-26 18:00:14,848 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-26 18:00:14,848 - INFO - 开始处理日期: 2025-03
2025-04-26 18:00:14,848 - INFO - Request Parameters - Page 1:
2025-04-26 18:00:14,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 18:00:14,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 18:00:15,364 - INFO - Response - Page 1:
2025-04-26 18:00:15,568 - INFO - 第 1 页获取到 100 条记录
2025-04-26 18:00:15,568 - INFO - Request Parameters - Page 2:
2025-04-26 18:00:15,568 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 18:00:15,568 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 18:00:15,990 - INFO - Response - Page 2:
2025-04-26 18:00:16,193 - INFO - 第 2 页获取到 100 条记录
2025-04-26 18:00:16,193 - INFO - Request Parameters - Page 3:
2025-04-26 18:00:16,193 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 18:00:16,193 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 18:00:16,756 - INFO - Response - Page 3:
2025-04-26 18:00:16,960 - INFO - 第 3 页获取到 100 条记录
2025-04-26 18:00:16,960 - INFO - Request Parameters - Page 4:
2025-04-26 18:00:16,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 18:00:16,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 18:00:17,382 - INFO - Response - Page 4:
2025-04-26 18:00:17,601 - INFO - 第 4 页获取到 100 条记录
2025-04-26 18:00:17,601 - INFO - Request Parameters - Page 5:
2025-04-26 18:00:17,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 18:00:17,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 18:00:18,086 - INFO - Response - Page 5:
2025-04-26 18:00:18,289 - INFO - 第 5 页获取到 100 条记录
2025-04-26 18:00:18,289 - INFO - Request Parameters - Page 6:
2025-04-26 18:00:18,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 18:00:18,289 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 18:00:18,774 - INFO - Response - Page 6:
2025-04-26 18:00:18,977 - INFO - 第 6 页获取到 100 条记录
2025-04-26 18:00:18,977 - INFO - Request Parameters - Page 7:
2025-04-26 18:00:18,977 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 18:00:18,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 18:00:19,431 - INFO - Response - Page 7:
2025-04-26 18:00:19,634 - INFO - 第 7 页获取到 61 条记录
2025-04-26 18:00:19,634 - INFO - 查询完成，共获取到 661 条记录
2025-04-26 18:00:19,634 - INFO - 获取到 661 条表单数据
2025-04-26 18:00:19,634 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-04-26 18:00:19,650 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-26 18:00:19,650 - INFO - 开始处理日期: 2025-04
2025-04-26 18:00:19,650 - INFO - Request Parameters - Page 1:
2025-04-26 18:00:19,650 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 18:00:19,650 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 18:00:20,166 - INFO - Response - Page 1:
2025-04-26 18:00:20,369 - INFO - 第 1 页获取到 100 条记录
2025-04-26 18:00:20,369 - INFO - Request Parameters - Page 2:
2025-04-26 18:00:20,369 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 18:00:20,369 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 18:00:20,807 - INFO - Response - Page 2:
2025-04-26 18:00:21,010 - INFO - 第 2 页获取到 100 条记录
2025-04-26 18:00:21,010 - INFO - Request Parameters - Page 3:
2025-04-26 18:00:21,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 18:00:21,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 18:00:21,495 - INFO - Response - Page 3:
2025-04-26 18:00:21,698 - INFO - 第 3 页获取到 100 条记录
2025-04-26 18:00:21,698 - INFO - Request Parameters - Page 4:
2025-04-26 18:00:21,698 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 18:00:21,698 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 18:00:22,183 - INFO - Response - Page 4:
2025-04-26 18:00:22,386 - INFO - 第 4 页获取到 100 条记录
2025-04-26 18:00:22,386 - INFO - Request Parameters - Page 5:
2025-04-26 18:00:22,386 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 18:00:22,386 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 18:00:23,387 - INFO - Response - Page 5:
2025-04-26 18:00:23,591 - INFO - 第 5 页获取到 100 条记录
2025-04-26 18:00:23,591 - INFO - Request Parameters - Page 6:
2025-04-26 18:00:23,591 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 18:00:23,591 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 18:00:24,107 - INFO - Response - Page 6:
2025-04-26 18:00:24,310 - INFO - 第 6 页获取到 100 条记录
2025-04-26 18:00:24,310 - INFO - Request Parameters - Page 7:
2025-04-26 18:00:24,310 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 18:00:24,310 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 18:00:24,670 - INFO - Response - Page 7:
2025-04-26 18:00:24,873 - INFO - 第 7 页获取到 25 条记录
2025-04-26 18:00:24,873 - INFO - 查询完成，共获取到 625 条记录
2025-04-26 18:00:24,873 - INFO - 获取到 625 条表单数据
2025-04-26 18:00:24,873 - INFO - 当前日期 2025-04 有 625 条MySQL数据需要处理
2025-04-26 18:00:24,873 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MK3
2025-04-26 18:00:25,373 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43RDRFMW9MK3
2025-04-26 18:00:25,389 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68500.0, 'new_value': 71140.0}, {'field': 'total_amount', 'old_value': 68500.0, 'new_value': 71140.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-04-26 18:00:25,389 - INFO - 日期 2025-04 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-04-26 18:00:25,389 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-04-26 18:00:25,389 - INFO - =================同步完成====================
2025-04-26 21:00:03,468 - INFO - =================使用默认全量同步=============
2025-04-26 21:00:04,563 - INFO - MySQL查询成功，共获取 2638 条记录
2025-04-26 21:00:04,563 - INFO - 获取到 4 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04']
2025-04-26 21:00:04,594 - INFO - 开始处理日期: 2025-01
2025-04-26 21:00:04,594 - INFO - Request Parameters - Page 1:
2025-04-26 21:00:04,594 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 21:00:04,594 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 21:00:05,407 - INFO - Response - Page 1:
2025-04-26 21:00:05,611 - INFO - 第 1 页获取到 100 条记录
2025-04-26 21:00:05,611 - INFO - Request Parameters - Page 2:
2025-04-26 21:00:05,611 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 21:00:05,611 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 21:00:06,127 - INFO - Response - Page 2:
2025-04-26 21:00:06,330 - INFO - 第 2 页获取到 100 条记录
2025-04-26 21:00:06,330 - INFO - Request Parameters - Page 3:
2025-04-26 21:00:06,330 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 21:00:06,330 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 21:00:06,846 - INFO - Response - Page 3:
2025-04-26 21:00:07,050 - INFO - 第 3 页获取到 100 条记录
2025-04-26 21:00:07,050 - INFO - Request Parameters - Page 4:
2025-04-26 21:00:07,050 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 21:00:07,050 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 21:00:07,534 - INFO - Response - Page 4:
2025-04-26 21:00:07,738 - INFO - 第 4 页获取到 100 条记录
2025-04-26 21:00:07,738 - INFO - Request Parameters - Page 5:
2025-04-26 21:00:07,738 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 21:00:07,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 21:00:08,207 - INFO - Response - Page 5:
2025-04-26 21:00:08,410 - INFO - 第 5 页获取到 100 条记录
2025-04-26 21:00:08,410 - INFO - Request Parameters - Page 6:
2025-04-26 21:00:08,410 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 21:00:08,410 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 21:00:08,958 - INFO - Response - Page 6:
2025-04-26 21:00:09,161 - INFO - 第 6 页获取到 100 条记录
2025-04-26 21:00:09,161 - INFO - Request Parameters - Page 7:
2025-04-26 21:00:09,161 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 21:00:09,161 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 21:00:09,630 - INFO - Response - Page 7:
2025-04-26 21:00:09,833 - INFO - 第 7 页获取到 82 条记录
2025-04-26 21:00:09,833 - INFO - 查询完成，共获取到 682 条记录
2025-04-26 21:00:09,833 - INFO - 获取到 682 条表单数据
2025-04-26 21:00:09,833 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-04-26 21:00:09,849 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-26 21:00:09,849 - INFO - 开始处理日期: 2025-02
2025-04-26 21:00:09,849 - INFO - Request Parameters - Page 1:
2025-04-26 21:00:09,849 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 21:00:09,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 21:00:10,334 - INFO - Response - Page 1:
2025-04-26 21:00:10,537 - INFO - 第 1 页获取到 100 条记录
2025-04-26 21:00:10,537 - INFO - Request Parameters - Page 2:
2025-04-26 21:00:10,537 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 21:00:10,537 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 21:00:11,006 - INFO - Response - Page 2:
2025-04-26 21:00:11,210 - INFO - 第 2 页获取到 100 条记录
2025-04-26 21:00:11,210 - INFO - Request Parameters - Page 3:
2025-04-26 21:00:11,210 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 21:00:11,210 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 21:00:11,726 - INFO - Response - Page 3:
2025-04-26 21:00:11,929 - INFO - 第 3 页获取到 100 条记录
2025-04-26 21:00:11,929 - INFO - Request Parameters - Page 4:
2025-04-26 21:00:11,929 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 21:00:11,929 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 21:00:12,398 - INFO - Response - Page 4:
2025-04-26 21:00:12,601 - INFO - 第 4 页获取到 100 条记录
2025-04-26 21:00:12,601 - INFO - Request Parameters - Page 5:
2025-04-26 21:00:12,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 21:00:12,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 21:00:13,086 - INFO - Response - Page 5:
2025-04-26 21:00:13,290 - INFO - 第 5 页获取到 100 条记录
2025-04-26 21:00:13,290 - INFO - Request Parameters - Page 6:
2025-04-26 21:00:13,290 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 21:00:13,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 21:00:13,774 - INFO - Response - Page 6:
2025-04-26 21:00:13,978 - INFO - 第 6 页获取到 100 条记录
2025-04-26 21:00:13,978 - INFO - Request Parameters - Page 7:
2025-04-26 21:00:13,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 21:00:13,978 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 21:00:14,416 - INFO - Response - Page 7:
2025-04-26 21:00:14,619 - INFO - 第 7 页获取到 70 条记录
2025-04-26 21:00:14,619 - INFO - 查询完成，共获取到 670 条记录
2025-04-26 21:00:14,619 - INFO - 获取到 670 条表单数据
2025-04-26 21:00:14,619 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-04-26 21:00:14,635 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-26 21:00:14,635 - INFO - 开始处理日期: 2025-03
2025-04-26 21:00:14,635 - INFO - Request Parameters - Page 1:
2025-04-26 21:00:14,635 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 21:00:14,635 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 21:00:15,119 - INFO - Response - Page 1:
2025-04-26 21:00:15,323 - INFO - 第 1 页获取到 100 条记录
2025-04-26 21:00:15,323 - INFO - Request Parameters - Page 2:
2025-04-26 21:00:15,323 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 21:00:15,323 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 21:00:15,808 - INFO - Response - Page 2:
2025-04-26 21:00:16,011 - INFO - 第 2 页获取到 100 条记录
2025-04-26 21:00:16,011 - INFO - Request Parameters - Page 3:
2025-04-26 21:00:16,011 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 21:00:16,011 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 21:00:16,480 - INFO - Response - Page 3:
2025-04-26 21:00:16,683 - INFO - 第 3 页获取到 100 条记录
2025-04-26 21:00:16,683 - INFO - Request Parameters - Page 4:
2025-04-26 21:00:16,683 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 21:00:16,683 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 21:00:17,121 - INFO - Response - Page 4:
2025-04-26 21:00:17,324 - INFO - 第 4 页获取到 100 条记录
2025-04-26 21:00:17,324 - INFO - Request Parameters - Page 5:
2025-04-26 21:00:17,324 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 21:00:17,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 21:00:17,794 - INFO - Response - Page 5:
2025-04-26 21:00:17,997 - INFO - 第 5 页获取到 100 条记录
2025-04-26 21:00:17,997 - INFO - Request Parameters - Page 6:
2025-04-26 21:00:17,997 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 21:00:17,997 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 21:00:18,529 - INFO - Response - Page 6:
2025-04-26 21:00:18,732 - INFO - 第 6 页获取到 100 条记录
2025-04-26 21:00:18,732 - INFO - Request Parameters - Page 7:
2025-04-26 21:00:18,732 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 21:00:18,732 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 21:00:19,186 - INFO - Response - Page 7:
2025-04-26 21:00:19,389 - INFO - 第 7 页获取到 61 条记录
2025-04-26 21:00:19,389 - INFO - 查询完成，共获取到 661 条记录
2025-04-26 21:00:19,389 - INFO - 获取到 661 条表单数据
2025-04-26 21:00:19,389 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-04-26 21:00:19,404 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-26 21:00:19,404 - INFO - 开始处理日期: 2025-04
2025-04-26 21:00:19,404 - INFO - Request Parameters - Page 1:
2025-04-26 21:00:19,404 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 21:00:19,404 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 21:00:19,827 - INFO - Response - Page 1:
2025-04-26 21:00:20,030 - INFO - 第 1 页获取到 100 条记录
2025-04-26 21:00:20,030 - INFO - Request Parameters - Page 2:
2025-04-26 21:00:20,030 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 21:00:20,030 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 21:00:20,546 - INFO - Response - Page 2:
2025-04-26 21:00:20,749 - INFO - 第 2 页获取到 100 条记录
2025-04-26 21:00:20,749 - INFO - Request Parameters - Page 3:
2025-04-26 21:00:20,749 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 21:00:20,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 21:00:21,187 - INFO - Response - Page 3:
2025-04-26 21:00:21,391 - INFO - 第 3 页获取到 100 条记录
2025-04-26 21:00:21,391 - INFO - Request Parameters - Page 4:
2025-04-26 21:00:21,391 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 21:00:21,391 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 21:00:21,860 - INFO - Response - Page 4:
2025-04-26 21:00:22,063 - INFO - 第 4 页获取到 100 条记录
2025-04-26 21:00:22,063 - INFO - Request Parameters - Page 5:
2025-04-26 21:00:22,063 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 21:00:22,063 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 21:00:22,532 - INFO - Response - Page 5:
2025-04-26 21:00:22,736 - INFO - 第 5 页获取到 100 条记录
2025-04-26 21:00:22,736 - INFO - Request Parameters - Page 6:
2025-04-26 21:00:22,736 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 21:00:22,736 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 21:00:23,158 - INFO - Response - Page 6:
2025-04-26 21:00:23,361 - INFO - 第 6 页获取到 100 条记录
2025-04-26 21:00:23,361 - INFO - Request Parameters - Page 7:
2025-04-26 21:00:23,361 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-26 21:00:23,361 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-26 21:00:23,705 - INFO - Response - Page 7:
2025-04-26 21:00:23,909 - INFO - 第 7 页获取到 25 条记录
2025-04-26 21:00:23,909 - INFO - 查询完成，共获取到 625 条记录
2025-04-26 21:00:23,909 - INFO - 获取到 625 条表单数据
2025-04-26 21:00:23,909 - INFO - 当前日期 2025-04 有 625 条MySQL数据需要处理
2025-04-26 21:00:23,924 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M4J
2025-04-26 21:00:24,393 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9M4J
2025-04-26 21:00:24,393 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 89521.0, 'new_value': 92655.0}, {'field': 'offline_amount', 'old_value': 107251.0, 'new_value': 111364.0}, {'field': 'total_amount', 'old_value': 196772.0, 'new_value': 204019.0}, {'field': 'order_count', 'old_value': 5004, 'new_value': 5205}]
2025-04-26 21:00:24,393 - INFO - 日期 2025-04 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-04-26 21:00:24,393 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-04-26 21:00:24,393 - INFO - =================同步完成====================
