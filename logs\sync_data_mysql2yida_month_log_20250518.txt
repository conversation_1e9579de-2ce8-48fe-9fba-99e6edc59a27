2025-05-18 00:00:01,996 - INFO - =================使用默认全量同步=============
2025-05-18 00:00:03,390 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-18 00:00:03,391 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-18 00:00:03,420 - INFO - 开始处理日期: 2025-01
2025-05-18 00:00:03,423 - INFO - Request Parameters - Page 1:
2025-05-18 00:00:03,423 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 00:00:03,423 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 00:00:04,783 - INFO - Response - Page 1:
2025-05-18 00:00:04,983 - INFO - 第 1 页获取到 100 条记录
2025-05-18 00:00:04,983 - INFO - Request Parameters - Page 2:
2025-05-18 00:00:04,983 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 00:00:04,983 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 00:00:05,580 - INFO - Response - Page 2:
2025-05-18 00:00:05,781 - INFO - 第 2 页获取到 100 条记录
2025-05-18 00:00:05,781 - INFO - Request Parameters - Page 3:
2025-05-18 00:00:05,781 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 00:00:05,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 00:00:06,463 - INFO - Response - Page 3:
2025-05-18 00:00:06,663 - INFO - 第 3 页获取到 100 条记录
2025-05-18 00:00:06,663 - INFO - Request Parameters - Page 4:
2025-05-18 00:00:06,663 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 00:00:06,663 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 00:00:07,354 - INFO - Response - Page 4:
2025-05-18 00:00:07,554 - INFO - 第 4 页获取到 100 条记录
2025-05-18 00:00:07,554 - INFO - Request Parameters - Page 5:
2025-05-18 00:00:07,554 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 00:00:07,554 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 00:00:08,047 - INFO - Response - Page 5:
2025-05-18 00:00:08,248 - INFO - 第 5 页获取到 100 条记录
2025-05-18 00:00:08,248 - INFO - Request Parameters - Page 6:
2025-05-18 00:00:08,248 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 00:00:08,249 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 00:00:08,752 - INFO - Response - Page 6:
2025-05-18 00:00:08,952 - INFO - 第 6 页获取到 100 条记录
2025-05-18 00:00:08,952 - INFO - Request Parameters - Page 7:
2025-05-18 00:00:08,952 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 00:00:08,952 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 00:00:09,402 - INFO - Response - Page 7:
2025-05-18 00:00:09,603 - INFO - 第 7 页获取到 82 条记录
2025-05-18 00:00:09,603 - INFO - 查询完成，共获取到 682 条记录
2025-05-18 00:00:09,603 - INFO - 获取到 682 条表单数据
2025-05-18 00:00:09,614 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-18 00:00:09,625 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-18 00:00:09,626 - INFO - 开始处理日期: 2025-02
2025-05-18 00:00:09,626 - INFO - Request Parameters - Page 1:
2025-05-18 00:00:09,626 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 00:00:09,626 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 00:00:10,099 - INFO - Response - Page 1:
2025-05-18 00:00:10,300 - INFO - 第 1 页获取到 100 条记录
2025-05-18 00:00:10,300 - INFO - Request Parameters - Page 2:
2025-05-18 00:00:10,300 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 00:00:10,300 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 00:00:10,907 - INFO - Response - Page 2:
2025-05-18 00:00:11,107 - INFO - 第 2 页获取到 100 条记录
2025-05-18 00:00:11,107 - INFO - Request Parameters - Page 3:
2025-05-18 00:00:11,107 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 00:00:11,107 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 00:00:11,546 - INFO - Response - Page 3:
2025-05-18 00:00:11,746 - INFO - 第 3 页获取到 100 条记录
2025-05-18 00:00:11,746 - INFO - Request Parameters - Page 4:
2025-05-18 00:00:11,746 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 00:00:11,746 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 00:00:12,312 - INFO - Response - Page 4:
2025-05-18 00:00:12,512 - INFO - 第 4 页获取到 100 条记录
2025-05-18 00:00:12,512 - INFO - Request Parameters - Page 5:
2025-05-18 00:00:12,512 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 00:00:12,512 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 00:00:12,936 - INFO - Response - Page 5:
2025-05-18 00:00:13,136 - INFO - 第 5 页获取到 100 条记录
2025-05-18 00:00:13,136 - INFO - Request Parameters - Page 6:
2025-05-18 00:00:13,136 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 00:00:13,136 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 00:00:13,655 - INFO - Response - Page 6:
2025-05-18 00:00:13,855 - INFO - 第 6 页获取到 100 条记录
2025-05-18 00:00:13,855 - INFO - Request Parameters - Page 7:
2025-05-18 00:00:13,855 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 00:00:13,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 00:00:14,354 - INFO - Response - Page 7:
2025-05-18 00:00:14,555 - INFO - 第 7 页获取到 70 条记录
2025-05-18 00:00:14,555 - INFO - 查询完成，共获取到 670 条记录
2025-05-18 00:00:14,555 - INFO - 获取到 670 条表单数据
2025-05-18 00:00:14,567 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-18 00:00:14,579 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-18 00:00:14,579 - INFO - 开始处理日期: 2025-03
2025-05-18 00:00:14,580 - INFO - Request Parameters - Page 1:
2025-05-18 00:00:14,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 00:00:14,580 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 00:00:15,089 - INFO - Response - Page 1:
2025-05-18 00:00:15,290 - INFO - 第 1 页获取到 100 条记录
2025-05-18 00:00:15,290 - INFO - Request Parameters - Page 2:
2025-05-18 00:00:15,290 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 00:00:15,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 00:00:15,767 - INFO - Response - Page 2:
2025-05-18 00:00:15,967 - INFO - 第 2 页获取到 100 条记录
2025-05-18 00:00:15,967 - INFO - Request Parameters - Page 3:
2025-05-18 00:00:15,967 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 00:00:15,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 00:00:16,442 - INFO - Response - Page 3:
2025-05-18 00:00:16,643 - INFO - 第 3 页获取到 100 条记录
2025-05-18 00:00:16,643 - INFO - Request Parameters - Page 4:
2025-05-18 00:00:16,643 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 00:00:16,643 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 00:00:17,189 - INFO - Response - Page 4:
2025-05-18 00:00:17,390 - INFO - 第 4 页获取到 100 条记录
2025-05-18 00:00:17,390 - INFO - Request Parameters - Page 5:
2025-05-18 00:00:17,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 00:00:17,390 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 00:00:17,852 - INFO - Response - Page 5:
2025-05-18 00:00:18,052 - INFO - 第 5 页获取到 100 条记录
2025-05-18 00:00:18,052 - INFO - Request Parameters - Page 6:
2025-05-18 00:00:18,052 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 00:00:18,052 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 00:00:18,549 - INFO - Response - Page 6:
2025-05-18 00:00:18,749 - INFO - 第 6 页获取到 100 条记录
2025-05-18 00:00:18,749 - INFO - Request Parameters - Page 7:
2025-05-18 00:00:18,749 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 00:00:18,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 00:00:19,224 - INFO - Response - Page 7:
2025-05-18 00:00:19,425 - INFO - 第 7 页获取到 61 条记录
2025-05-18 00:00:19,425 - INFO - 查询完成，共获取到 661 条记录
2025-05-18 00:00:19,425 - INFO - 获取到 661 条表单数据
2025-05-18 00:00:19,437 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-18 00:00:19,450 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-18 00:00:19,450 - INFO - 开始处理日期: 2025-04
2025-05-18 00:00:19,450 - INFO - Request Parameters - Page 1:
2025-05-18 00:00:19,450 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 00:00:19,450 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 00:00:19,964 - INFO - Response - Page 1:
2025-05-18 00:00:20,164 - INFO - 第 1 页获取到 100 条记录
2025-05-18 00:00:20,164 - INFO - Request Parameters - Page 2:
2025-05-18 00:00:20,164 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 00:00:20,164 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 00:00:20,770 - INFO - Response - Page 2:
2025-05-18 00:00:20,970 - INFO - 第 2 页获取到 100 条记录
2025-05-18 00:00:20,970 - INFO - Request Parameters - Page 3:
2025-05-18 00:00:20,970 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 00:00:20,970 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 00:00:21,451 - INFO - Response - Page 3:
2025-05-18 00:00:21,651 - INFO - 第 3 页获取到 100 条记录
2025-05-18 00:00:21,651 - INFO - Request Parameters - Page 4:
2025-05-18 00:00:21,651 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 00:00:21,651 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 00:00:22,184 - INFO - Response - Page 4:
2025-05-18 00:00:22,384 - INFO - 第 4 页获取到 100 条记录
2025-05-18 00:00:22,384 - INFO - Request Parameters - Page 5:
2025-05-18 00:00:22,384 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 00:00:22,384 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 00:00:22,852 - INFO - Response - Page 5:
2025-05-18 00:00:23,052 - INFO - 第 5 页获取到 100 条记录
2025-05-18 00:00:23,052 - INFO - Request Parameters - Page 6:
2025-05-18 00:00:23,052 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 00:00:23,052 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 00:00:23,555 - INFO - Response - Page 6:
2025-05-18 00:00:23,755 - INFO - 第 6 页获取到 100 条记录
2025-05-18 00:00:23,755 - INFO - Request Parameters - Page 7:
2025-05-18 00:00:23,755 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 00:00:23,755 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 00:00:24,150 - INFO - Response - Page 7:
2025-05-18 00:00:24,351 - INFO - 第 7 页获取到 54 条记录
2025-05-18 00:00:24,351 - INFO - 查询完成，共获取到 654 条记录
2025-05-18 00:00:24,351 - INFO - 获取到 654 条表单数据
2025-05-18 00:00:24,363 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-18 00:00:24,375 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-18 00:00:24,375 - INFO - 开始处理日期: 2025-05
2025-05-18 00:00:24,375 - INFO - Request Parameters - Page 1:
2025-05-18 00:00:24,375 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 00:00:24,375 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 00:00:24,886 - INFO - Response - Page 1:
2025-05-18 00:00:25,086 - INFO - 第 1 页获取到 100 条记录
2025-05-18 00:00:25,086 - INFO - Request Parameters - Page 2:
2025-05-18 00:00:25,086 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 00:00:25,086 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 00:00:25,583 - INFO - Response - Page 2:
2025-05-18 00:00:25,783 - INFO - 第 2 页获取到 100 条记录
2025-05-18 00:00:25,783 - INFO - Request Parameters - Page 3:
2025-05-18 00:00:25,783 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 00:00:25,783 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 00:00:26,253 - INFO - Response - Page 3:
2025-05-18 00:00:26,453 - INFO - 第 3 页获取到 100 条记录
2025-05-18 00:00:26,453 - INFO - Request Parameters - Page 4:
2025-05-18 00:00:26,453 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 00:00:26,453 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 00:00:26,981 - INFO - Response - Page 4:
2025-05-18 00:00:27,182 - INFO - 第 4 页获取到 100 条记录
2025-05-18 00:00:27,182 - INFO - Request Parameters - Page 5:
2025-05-18 00:00:27,182 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 00:00:27,182 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 00:00:27,636 - INFO - Response - Page 5:
2025-05-18 00:00:27,837 - INFO - 第 5 页获取到 100 条记录
2025-05-18 00:00:27,837 - INFO - Request Parameters - Page 6:
2025-05-18 00:00:27,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 00:00:27,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 00:00:28,366 - INFO - Response - Page 6:
2025-05-18 00:00:28,566 - INFO - 第 6 页获取到 100 条记录
2025-05-18 00:00:28,566 - INFO - Request Parameters - Page 7:
2025-05-18 00:00:28,566 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 00:00:28,566 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 00:00:28,920 - INFO - Response - Page 7:
2025-05-18 00:00:29,120 - INFO - 第 7 页获取到 25 条记录
2025-05-18 00:00:29,120 - INFO - 查询完成，共获取到 625 条记录
2025-05-18 00:00:29,120 - INFO - 获取到 625 条表单数据
2025-05-18 00:00:29,132 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-18 00:00:29,133 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-18 00:00:29,545 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-18 00:00:29,545 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41314.78, 'new_value': 41699.78}, {'field': 'total_amount', 'old_value': 41314.78, 'new_value': 41699.78}, {'field': 'order_count', 'old_value': 83, 'new_value': 88}]
2025-05-18 00:00:29,546 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-18 00:00:30,041 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-18 00:00:30,042 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 79656.0, 'new_value': 82719.0}, {'field': 'offline_amount', 'old_value': 85339.28, 'new_value': 89714.28}, {'field': 'total_amount', 'old_value': 164995.28, 'new_value': 172433.28}, {'field': 'order_count', 'old_value': 3535, 'new_value': 3693}]
2025-05-18 00:00:30,042 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-18 00:00:30,433 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-18 00:00:30,434 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30480.0, 'new_value': 32900.0}, {'field': 'total_amount', 'old_value': 34600.0, 'new_value': 37020.0}, {'field': 'order_count', 'old_value': 334, 'new_value': 358}]
2025-05-18 00:00:30,434 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMMC
2025-05-18 00:00:30,807 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMMC
2025-05-18 00:00:30,807 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26505.0, 'new_value': 35107.0}, {'field': 'total_amount', 'old_value': 26505.0, 'new_value': 35107.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-05-18 00:00:30,807 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-18 00:00:31,263 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-18 00:00:31,263 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29367.7, 'new_value': 32347.7}, {'field': 'total_amount', 'old_value': 33327.7, 'new_value': 36307.7}, {'field': 'order_count', 'old_value': 270, 'new_value': 278}]
2025-05-18 00:00:31,264 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-18 00:00:31,705 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-18 00:00:31,706 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5253.9, 'new_value': 5855.25}, {'field': 'offline_amount', 'old_value': 60498.7, 'new_value': 66697.7}, {'field': 'total_amount', 'old_value': 65752.6, 'new_value': 72552.95}, {'field': 'order_count', 'old_value': 1498, 'new_value': 1612}]
2025-05-18 00:00:31,706 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-18 00:00:32,081 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM8D
2025-05-18 00:00:32,082 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14630.7, 'new_value': 15100.7}, {'field': 'offline_amount', 'old_value': 31195.6, 'new_value': 36583.6}, {'field': 'total_amount', 'old_value': 45826.3, 'new_value': 51684.3}, {'field': 'order_count', 'old_value': 568, 'new_value': 599}]
2025-05-18 00:00:32,082 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-18 00:00:32,485 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-18 00:00:32,486 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16466.2, 'new_value': 19929.2}, {'field': 'offline_amount', 'old_value': 94926.84, 'new_value': 99521.78}, {'field': 'total_amount', 'old_value': 111393.04, 'new_value': 119450.98}, {'field': 'order_count', 'old_value': 175, 'new_value': 187}]
2025-05-18 00:00:32,486 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-05-18 00:00:32,860 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMDD
2025-05-18 00:00:32,860 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9139.0, 'new_value': 9619.0}, {'field': 'total_amount', 'old_value': 9139.0, 'new_value': 9619.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-05-18 00:00:32,861 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-18 00:00:33,306 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-18 00:00:33,307 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31912.36, 'new_value': 36156.85}, {'field': 'offline_amount', 'old_value': 456247.24, 'new_value': 487614.44}, {'field': 'total_amount', 'old_value': 488159.6, 'new_value': 523771.29}, {'field': 'order_count', 'old_value': 1994, 'new_value': 2144}]
2025-05-18 00:00:33,307 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXD
2025-05-18 00:00:33,670 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXD
2025-05-18 00:00:33,670 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8521.65, 'new_value': 9127.32}, {'field': 'total_amount', 'old_value': 8521.65, 'new_value': 9127.32}, {'field': 'order_count', 'old_value': 141, 'new_value': 202}]
2025-05-18 00:00:33,671 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-18 00:00:34,091 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-18 00:00:34,091 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 120467.88, 'new_value': 129908.8}, {'field': 'offline_amount', 'old_value': 302628.26, 'new_value': 325746.92}, {'field': 'total_amount', 'old_value': 423096.14, 'new_value': 455655.72}, {'field': 'order_count', 'old_value': 2951, 'new_value': 3262}]
2025-05-18 00:00:34,091 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-18 00:00:34,416 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-18 00:00:34,416 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20315.7, 'new_value': 21222.7}, {'field': 'total_amount', 'old_value': 20315.7, 'new_value': 21222.7}, {'field': 'order_count', 'old_value': 111, 'new_value': 116}]
2025-05-18 00:00:34,416 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMFE
2025-05-18 00:00:34,811 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMFE
2025-05-18 00:00:34,811 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3576.0, 'new_value': 4219.0}, {'field': 'total_amount', 'old_value': 3576.0, 'new_value': 4219.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 13}]
2025-05-18 00:00:34,811 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-18 00:00:35,199 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-18 00:00:35,199 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26734.9, 'new_value': 29307.9}, {'field': 'total_amount', 'old_value': 26734.9, 'new_value': 29307.9}, {'field': 'order_count', 'old_value': 116, 'new_value': 131}]
2025-05-18 00:00:35,200 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-18 00:00:35,631 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-18 00:00:35,631 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86599.0, 'new_value': 95424.0}, {'field': 'total_amount', 'old_value': 86601.0, 'new_value': 95426.0}, {'field': 'order_count', 'old_value': 40, 'new_value': 41}]
2025-05-18 00:00:35,631 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-18 00:00:36,126 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-18 00:00:36,126 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27238.96, 'new_value': 29926.32}, {'field': 'offline_amount', 'old_value': 61238.27, 'new_value': 66684.9}, {'field': 'total_amount', 'old_value': 88477.23, 'new_value': 96611.22}, {'field': 'order_count', 'old_value': 3222, 'new_value': 3514}]
2025-05-18 00:00:36,126 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-18 00:00:36,587 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-18 00:00:36,587 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57683.09, 'new_value': 60472.79}, {'field': 'total_amount', 'old_value': 61452.19, 'new_value': 64241.89}, {'field': 'order_count', 'old_value': 323, 'new_value': 353}]
2025-05-18 00:00:36,587 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-18 00:00:37,048 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-18 00:00:37,048 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63237.64, 'new_value': 69276.64}, {'field': 'total_amount', 'old_value': 68577.64, 'new_value': 74616.64}, {'field': 'order_count', 'old_value': 22, 'new_value': 23}]
2025-05-18 00:00:37,048 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-18 00:00:37,436 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWE
2025-05-18 00:00:37,436 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26831.6, 'new_value': 31017.6}, {'field': 'offline_amount', 'old_value': 535.0, 'new_value': 541.0}, {'field': 'total_amount', 'old_value': 27366.6, 'new_value': 31558.6}, {'field': 'order_count', 'old_value': 115, 'new_value': 121}]
2025-05-18 00:00:37,436 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-18 00:00:37,809 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-18 00:00:37,809 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5502.8, 'new_value': 5700.8}, {'field': 'offline_amount', 'old_value': 32175.0, 'new_value': 35395.0}, {'field': 'total_amount', 'old_value': 37677.8, 'new_value': 41095.8}, {'field': 'order_count', 'old_value': 43, 'new_value': 47}]
2025-05-18 00:00:37,809 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-18 00:00:38,275 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-18 00:00:38,275 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104825.5, 'new_value': 114576.6}, {'field': 'total_amount', 'old_value': 104825.5, 'new_value': 114576.6}, {'field': 'order_count', 'old_value': 238, 'new_value': 265}]
2025-05-18 00:00:38,275 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-18 00:00:38,737 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-18 00:00:38,737 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 135190.22, 'new_value': 155094.32}, {'field': 'total_amount', 'old_value': 203861.04, 'new_value': 223765.14}, {'field': 'order_count', 'old_value': 2356, 'new_value': 2504}]
2025-05-18 00:00:38,738 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-18 00:00:39,154 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-18 00:00:39,154 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63268.43, 'new_value': 70214.1}, {'field': 'total_amount', 'old_value': 63268.43, 'new_value': 70214.1}, {'field': 'order_count', 'old_value': 2439, 'new_value': 2703}]
2025-05-18 00:00:39,154 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-18 00:00:39,586 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-18 00:00:39,586 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6577.24, 'new_value': 7405.81}, {'field': 'total_amount', 'old_value': 15964.06, 'new_value': 16792.63}, {'field': 'order_count', 'old_value': 67, 'new_value': 72}]
2025-05-18 00:00:39,586 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-18 00:00:39,980 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSK
2025-05-18 00:00:39,980 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30037.0, 'new_value': 34063.0}, {'field': 'total_amount', 'old_value': 30037.0, 'new_value': 34063.0}, {'field': 'order_count', 'old_value': 61, 'new_value': 65}]
2025-05-18 00:00:39,981 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-18 00:00:40,383 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVK
2025-05-18 00:00:40,383 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40483.0, 'new_value': 44613.0}, {'field': 'total_amount', 'old_value': 40483.0, 'new_value': 44613.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 27}]
2025-05-18 00:00:40,384 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-18 00:00:40,810 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-18 00:00:40,811 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68861.1, 'new_value': 75640.3}, {'field': 'total_amount', 'old_value': 68861.1, 'new_value': 75640.3}, {'field': 'order_count', 'old_value': 214, 'new_value': 236}]
2025-05-18 00:00:40,811 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-18 00:00:41,161 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-18 00:00:41,161 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 106111.1, 'new_value': 116539.21}, {'field': 'offline_amount', 'old_value': 234449.55, 'new_value': 253996.45}, {'field': 'total_amount', 'old_value': 340560.65, 'new_value': 370535.66}, {'field': 'order_count', 'old_value': 9139, 'new_value': 9985}]
2025-05-18 00:00:41,162 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-18 00:00:41,520 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-18 00:00:41,521 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32605.0, 'new_value': 34228.0}, {'field': 'total_amount', 'old_value': 32605.0, 'new_value': 34228.0}, {'field': 'order_count', 'old_value': 85, 'new_value': 90}]
2025-05-18 00:00:41,521 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-18 00:00:41,876 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-18 00:00:41,877 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23954.0, 'new_value': 25471.0}, {'field': 'total_amount', 'old_value': 23954.0, 'new_value': 25471.0}, {'field': 'order_count', 'old_value': 74, 'new_value': 79}]
2025-05-18 00:00:41,877 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMCL
2025-05-18 00:00:42,312 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMCL
2025-05-18 00:00:42,312 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46893.0, 'new_value': 52892.0}, {'field': 'total_amount', 'old_value': 46893.0, 'new_value': 52892.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-18 00:00:42,312 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-18 00:00:42,780 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-18 00:00:42,780 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33129.0, 'new_value': 35824.0}, {'field': 'total_amount', 'old_value': 34645.0, 'new_value': 37340.0}, {'field': 'order_count', 'old_value': 139, 'new_value': 150}]
2025-05-18 00:00:42,781 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-05-18 00:00:43,149 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-05-18 00:00:43,149 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4427.0, 'new_value': 5297.0}, {'field': 'total_amount', 'old_value': 4427.0, 'new_value': 5297.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 25}]
2025-05-18 00:00:43,150 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-18 00:00:43,518 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-18 00:00:43,519 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1186209.24, 'new_value': 1263612.2}, {'field': 'total_amount', 'old_value': 1186209.24, 'new_value': 1263612.2}, {'field': 'order_count', 'old_value': 9384, 'new_value': 10037}]
2025-05-18 00:00:43,520 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTL
2025-05-18 00:00:43,893 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTL
2025-05-18 00:00:43,893 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7507.0, 'new_value': 8737.0}, {'field': 'total_amount', 'old_value': 7507.0, 'new_value': 8737.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-05-18 00:00:43,894 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-18 00:00:44,304 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-18 00:00:44,305 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21729.9, 'new_value': 22479.9}, {'field': 'total_amount', 'old_value': 23516.8, 'new_value': 24266.8}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-05-18 00:00:44,305 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-18 00:00:44,657 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-18 00:00:44,657 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5274.47, 'new_value': 5850.99}, {'field': 'offline_amount', 'old_value': 8312.34, 'new_value': 8887.37}, {'field': 'total_amount', 'old_value': 13586.81, 'new_value': 14738.36}, {'field': 'order_count', 'old_value': 1021, 'new_value': 1137}]
2025-05-18 00:00:44,658 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-18 00:00:45,046 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-18 00:00:45,046 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 58125.03, 'new_value': 61389.96}, {'field': 'offline_amount', 'old_value': 27297.14, 'new_value': 28877.04}, {'field': 'total_amount', 'old_value': 85422.17, 'new_value': 90267.0}, {'field': 'order_count', 'old_value': 5423, 'new_value': 5739}]
2025-05-18 00:00:45,047 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-18 00:00:45,410 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-18 00:00:45,410 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 60960.61, 'new_value': 65508.61}, {'field': 'offline_amount', 'old_value': 163660.19, 'new_value': 178223.19}, {'field': 'total_amount', 'old_value': 224620.8, 'new_value': 243731.8}, {'field': 'order_count', 'old_value': 7348, 'new_value': 7968}]
2025-05-18 00:00:45,410 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBM
2025-05-18 00:00:45,843 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBM
2025-05-18 00:00:45,843 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35192.0, 'new_value': 41990.0}, {'field': 'total_amount', 'old_value': 35192.0, 'new_value': 41990.0}, {'field': 'order_count', 'old_value': 17600, 'new_value': 17601}]
2025-05-18 00:00:45,844 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-18 00:00:46,274 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-18 00:00:46,274 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77845.8, 'new_value': 84067.4}, {'field': 'total_amount', 'old_value': 94415.3, 'new_value': 100636.9}, {'field': 'order_count', 'old_value': 123, 'new_value': 135}]
2025-05-18 00:00:46,274 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-18 00:00:46,705 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-18 00:00:46,705 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 78016.46, 'new_value': 86876.7}, {'field': 'offline_amount', 'old_value': 177386.72, 'new_value': 193236.74}, {'field': 'total_amount', 'old_value': 255403.18, 'new_value': 280113.44}, {'field': 'order_count', 'old_value': 3237, 'new_value': 3523}]
2025-05-18 00:00:46,705 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-18 00:00:47,066 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-18 00:00:47,067 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55601.9, 'new_value': 59797.1}, {'field': 'total_amount', 'old_value': 55601.9, 'new_value': 59797.1}, {'field': 'order_count', 'old_value': 417, 'new_value': 447}]
2025-05-18 00:00:47,067 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-18 00:00:47,478 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-18 00:00:47,478 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14864.63, 'new_value': 16496.14}, {'field': 'total_amount', 'old_value': 16214.63, 'new_value': 17846.14}, {'field': 'order_count', 'old_value': 306, 'new_value': 329}]
2025-05-18 00:00:47,478 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM
2025-05-18 00:00:47,943 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM
2025-05-18 00:00:47,943 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13102.0, 'new_value': 13585.0}, {'field': 'total_amount', 'old_value': 13102.0, 'new_value': 13585.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 24}]
2025-05-18 00:00:47,944 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-18 00:00:48,394 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-18 00:00:48,394 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26163.21, 'new_value': 29050.27}, {'field': 'total_amount', 'old_value': 26163.21, 'new_value': 29050.27}, {'field': 'order_count', 'old_value': 106, 'new_value': 118}]
2025-05-18 00:00:48,394 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-18 00:00:48,747 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-18 00:00:48,747 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15349.95, 'new_value': 17108.41}, {'field': 'offline_amount', 'old_value': 63415.0, 'new_value': 80062.0}, {'field': 'total_amount', 'old_value': 78764.95, 'new_value': 97170.41}, {'field': 'order_count', 'old_value': 72, 'new_value': 80}]
2025-05-18 00:00:48,748 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-18 00:00:49,093 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-18 00:00:49,094 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53449.68, 'new_value': 58130.82}, {'field': 'offline_amount', 'old_value': 83756.08, 'new_value': 93155.09}, {'field': 'total_amount', 'old_value': 137205.76, 'new_value': 151285.91}, {'field': 'order_count', 'old_value': 1353, 'new_value': 1494}]
2025-05-18 00:00:49,094 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-18 00:00:49,482 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-18 00:00:49,483 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6523.6, 'new_value': 7207.6}, {'field': 'offline_amount', 'old_value': 828.0, 'new_value': 2006.0}, {'field': 'total_amount', 'old_value': 7351.6, 'new_value': 9213.6}, {'field': 'order_count', 'old_value': 75, 'new_value': 85}]
2025-05-18 00:00:49,483 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-18 00:00:49,898 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-18 00:00:49,899 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20387.0, 'new_value': 20652.0}, {'field': 'total_amount', 'old_value': 20387.0, 'new_value': 20652.0}, {'field': 'order_count', 'old_value': 89, 'new_value': 90}]
2025-05-18 00:00:49,899 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-18 00:00:50,282 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-18 00:00:50,282 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 311724.79, 'new_value': 340829.97}, {'field': 'total_amount', 'old_value': 311724.79, 'new_value': 340829.97}, {'field': 'order_count', 'old_value': 4252, 'new_value': 4613}]
2025-05-18 00:00:50,282 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-18 00:00:50,667 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-18 00:00:50,667 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6898.4, 'new_value': 7645.4}, {'field': 'offline_amount', 'old_value': 17003.9, 'new_value': 17122.9}, {'field': 'total_amount', 'old_value': 23902.3, 'new_value': 24768.3}, {'field': 'order_count', 'old_value': 65, 'new_value': 70}]
2025-05-18 00:00:50,668 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-18 00:00:51,117 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-18 00:00:51,118 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58245.02, 'new_value': 63195.32}, {'field': 'total_amount', 'old_value': 58245.02, 'new_value': 63195.32}, {'field': 'order_count', 'old_value': 1768, 'new_value': 1924}]
2025-05-18 00:00:51,118 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-18 00:00:51,519 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-18 00:00:51,519 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 95615.11, 'new_value': 107425.68}, {'field': 'total_amount', 'old_value': 95615.11, 'new_value': 107425.68}, {'field': 'order_count', 'old_value': 2462, 'new_value': 2744}]
2025-05-18 00:00:51,519 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-18 00:00:51,913 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-18 00:00:51,913 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21909.35, 'new_value': 23250.59}, {'field': 'offline_amount', 'old_value': 215858.59, 'new_value': 234583.0}, {'field': 'total_amount', 'old_value': 237767.94, 'new_value': 257833.59}, {'field': 'order_count', 'old_value': 5489, 'new_value': 6028}]
2025-05-18 00:00:51,914 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-18 00:00:52,296 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMMD
2025-05-18 00:00:52,296 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53671.0, 'new_value': 57164.0}, {'field': 'total_amount', 'old_value': 53671.0, 'new_value': 57164.0}, {'field': 'order_count', 'old_value': 256, 'new_value': 274}]
2025-05-18 00:00:52,296 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-18 00:00:52,822 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-18 00:00:52,823 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 299554.7, 'new_value': 318200.7}, {'field': 'total_amount', 'old_value': 299554.7, 'new_value': 318200.7}, {'field': 'order_count', 'old_value': 1488, 'new_value': 1582}]
2025-05-18 00:00:52,823 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-18 00:00:53,238 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPD
2025-05-18 00:00:53,238 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9271.6, 'new_value': 10286.6}, {'field': 'total_amount', 'old_value': 9700.6, 'new_value': 10715.6}, {'field': 'order_count', 'old_value': 121, 'new_value': 140}]
2025-05-18 00:00:53,238 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-18 00:00:53,662 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-18 00:00:53,662 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4158.0, 'new_value': 5130.0}, {'field': 'offline_amount', 'old_value': 21553.0, 'new_value': 23018.0}, {'field': 'total_amount', 'old_value': 25711.0, 'new_value': 28148.0}, {'field': 'order_count', 'old_value': 201, 'new_value': 220}]
2025-05-18 00:00:53,662 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-18 00:00:54,067 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-18 00:00:54,067 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 132364.0, 'new_value': 146813.0}, {'field': 'total_amount', 'old_value': 132364.0, 'new_value': 146813.0}, {'field': 'order_count', 'old_value': 151, 'new_value': 169}]
2025-05-18 00:00:54,067 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-18 00:00:54,423 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-18 00:00:54,424 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 475901.84, 'new_value': 523910.3}, {'field': 'total_amount', 'old_value': 475901.84, 'new_value': 523910.3}, {'field': 'order_count', 'old_value': 9337, 'new_value': 10228}]
2025-05-18 00:00:54,424 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-18 00:00:54,801 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-18 00:00:54,802 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 184881.79, 'new_value': 198282.61}, {'field': 'total_amount', 'old_value': 184881.79, 'new_value': 198282.61}, {'field': 'order_count', 'old_value': 7724, 'new_value': 8306}]
2025-05-18 00:00:54,802 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-18 00:00:55,143 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-18 00:00:55,144 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 143972.0, 'new_value': 154148.0}, {'field': 'total_amount', 'old_value': 143972.0, 'new_value': 154148.0}, {'field': 'order_count', 'old_value': 434, 'new_value': 466}]
2025-05-18 00:00:55,144 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-18 00:00:55,587 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-18 00:00:55,587 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39932.58, 'new_value': 42407.54}, {'field': 'offline_amount', 'old_value': 290646.94, 'new_value': 314869.9}, {'field': 'total_amount', 'old_value': 330579.52, 'new_value': 357277.44}, {'field': 'order_count', 'old_value': 1501, 'new_value': 1646}]
2025-05-18 00:00:55,587 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-18 00:00:55,971 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-18 00:00:55,972 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 465865.89, 'new_value': 508992.36}, {'field': 'total_amount', 'old_value': 465865.89, 'new_value': 508992.36}, {'field': 'order_count', 'old_value': 3168, 'new_value': 3478}]
2025-05-18 00:00:55,972 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-18 00:00:56,443 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-18 00:00:56,443 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41729.8, 'new_value': 43865.77}, {'field': 'offline_amount', 'old_value': 60119.73, 'new_value': 64776.37}, {'field': 'total_amount', 'old_value': 101849.53, 'new_value': 108642.14}, {'field': 'order_count', 'old_value': 4690, 'new_value': 5022}]
2025-05-18 00:00:56,444 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-18 00:00:56,884 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-18 00:00:56,884 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 99744.0, 'new_value': 107053.0}, {'field': 'total_amount', 'old_value': 99744.0, 'new_value': 107053.0}, {'field': 'order_count', 'old_value': 3194, 'new_value': 3427}]
2025-05-18 00:00:56,884 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-18 00:00:57,261 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-18 00:00:57,261 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 171592.66, 'new_value': 171692.66}, {'field': 'offline_amount', 'old_value': 56426.18, 'new_value': 77199.38}, {'field': 'total_amount', 'old_value': 228018.84, 'new_value': 248892.04}, {'field': 'order_count', 'old_value': 400, 'new_value': 440}]
2025-05-18 00:00:57,261 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-18 00:00:57,715 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-18 00:00:57,715 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 154364.71, 'new_value': 169278.86}, {'field': 'total_amount', 'old_value': 173538.14, 'new_value': 188452.29}, {'field': 'order_count', 'old_value': 3576, 'new_value': 3915}]
2025-05-18 00:00:57,716 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCP
2025-05-18 00:00:58,181 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCP
2025-05-18 00:00:58,181 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 135100.0, 'new_value': 140400.0}, {'field': 'total_amount', 'old_value': 135100.0, 'new_value': 140400.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-05-18 00:00:58,182 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-18 00:00:58,555 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-18 00:00:58,555 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22935.7, 'new_value': 24628.7}, {'field': 'total_amount', 'old_value': 22935.7, 'new_value': 24628.7}, {'field': 'order_count', 'old_value': 130, 'new_value': 138}]
2025-05-18 00:00:58,556 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-18 00:00:59,012 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-18 00:00:59,013 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21960.66, 'new_value': 24403.31}, {'field': 'offline_amount', 'old_value': 33033.54, 'new_value': 35033.54}, {'field': 'total_amount', 'old_value': 54994.2, 'new_value': 59436.85}, {'field': 'order_count', 'old_value': 2536, 'new_value': 2770}]
2025-05-18 00:00:59,013 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-18 00:00:59,432 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-18 00:00:59,432 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27919.79, 'new_value': 29828.4}, {'field': 'offline_amount', 'old_value': 17708.7, 'new_value': 18680.42}, {'field': 'total_amount', 'old_value': 45628.49, 'new_value': 48508.82}, {'field': 'order_count', 'old_value': 1973, 'new_value': 2097}]
2025-05-18 00:00:59,433 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXP
2025-05-18 00:00:59,789 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXP
2025-05-18 00:00:59,789 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54240.0, 'new_value': 85560.0}, {'field': 'total_amount', 'old_value': 54240.0, 'new_value': 85560.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 24}]
2025-05-18 00:00:59,790 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-18 00:01:00,220 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-18 00:01:00,220 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51039.2, 'new_value': 58003.2}, {'field': 'total_amount', 'old_value': 51292.2, 'new_value': 58256.2}, {'field': 'order_count', 'old_value': 783, 'new_value': 863}]
2025-05-18 00:01:00,220 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM6V
2025-05-18 00:01:00,609 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM6V
2025-05-18 00:01:00,609 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33880.0, 'new_value': 43780.0}, {'field': 'total_amount', 'old_value': 33880.0, 'new_value': 43780.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 7}]
2025-05-18 00:01:00,610 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AM9C
2025-05-18 00:01:01,094 - INFO - 更新表单数据成功: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AM9C
2025-05-18 00:01:01,094 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4224.6, 'new_value': 4471.6}, {'field': 'total_amount', 'old_value': 4224.6, 'new_value': 4471.6}, {'field': 'order_count', 'old_value': 12, 'new_value': 14}]
2025-05-18 00:01:01,095 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-18 00:01:01,498 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-18 00:01:01,499 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23902.83, 'new_value': 27756.8}, {'field': 'total_amount', 'old_value': 23902.83, 'new_value': 27756.8}, {'field': 'order_count', 'old_value': 1020, 'new_value': 1259}]
2025-05-18 00:01:01,499 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-05-18 00:01:01,858 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-05-18 00:01:01,858 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2591.12, 'new_value': 2686.92}, {'field': 'offline_amount', 'old_value': 21428.06, 'new_value': 23595.61}, {'field': 'total_amount', 'old_value': 24019.18, 'new_value': 26282.53}, {'field': 'order_count', 'old_value': 947, 'new_value': 1038}]
2025-05-18 00:01:01,858 - INFO - 日期 2025-05 处理完成 - 更新: 80 条，插入: 0 条，错误: 0 条
2025-05-18 00:01:01,858 - INFO - 数据同步完成！更新: 80 条，插入: 0 条，错误: 0 条
2025-05-18 00:01:01,860 - INFO - =================同步完成====================
2025-05-18 03:00:02,092 - INFO - =================使用默认全量同步=============
2025-05-18 03:00:03,474 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-18 03:00:03,474 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-18 03:00:03,501 - INFO - 开始处理日期: 2025-01
2025-05-18 03:00:03,504 - INFO - Request Parameters - Page 1:
2025-05-18 03:00:03,505 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 03:00:03,505 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 03:00:05,281 - INFO - Response - Page 1:
2025-05-18 03:00:05,482 - INFO - 第 1 页获取到 100 条记录
2025-05-18 03:00:05,482 - INFO - Request Parameters - Page 2:
2025-05-18 03:00:05,482 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 03:00:05,482 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 03:00:06,019 - INFO - Response - Page 2:
2025-05-18 03:00:06,220 - INFO - 第 2 页获取到 100 条记录
2025-05-18 03:00:06,220 - INFO - Request Parameters - Page 3:
2025-05-18 03:00:06,220 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 03:00:06,220 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 03:00:06,707 - INFO - Response - Page 3:
2025-05-18 03:00:06,907 - INFO - 第 3 页获取到 100 条记录
2025-05-18 03:00:06,907 - INFO - Request Parameters - Page 4:
2025-05-18 03:00:06,907 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 03:00:06,907 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 03:00:07,415 - INFO - Response - Page 4:
2025-05-18 03:00:07,615 - INFO - 第 4 页获取到 100 条记录
2025-05-18 03:00:07,615 - INFO - Request Parameters - Page 5:
2025-05-18 03:00:07,615 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 03:00:07,615 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 03:00:08,128 - INFO - Response - Page 5:
2025-05-18 03:00:08,328 - INFO - 第 5 页获取到 100 条记录
2025-05-18 03:00:08,328 - INFO - Request Parameters - Page 6:
2025-05-18 03:00:08,328 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 03:00:08,328 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 03:00:08,825 - INFO - Response - Page 6:
2025-05-18 03:00:09,026 - INFO - 第 6 页获取到 100 条记录
2025-05-18 03:00:09,026 - INFO - Request Parameters - Page 7:
2025-05-18 03:00:09,026 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 03:00:09,026 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 03:00:09,500 - INFO - Response - Page 7:
2025-05-18 03:00:09,700 - INFO - 第 7 页获取到 82 条记录
2025-05-18 03:00:09,700 - INFO - 查询完成，共获取到 682 条记录
2025-05-18 03:00:09,700 - INFO - 获取到 682 条表单数据
2025-05-18 03:00:09,711 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-18 03:00:09,723 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-18 03:00:09,723 - INFO - 开始处理日期: 2025-02
2025-05-18 03:00:09,723 - INFO - Request Parameters - Page 1:
2025-05-18 03:00:09,724 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 03:00:09,724 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 03:00:10,221 - INFO - Response - Page 1:
2025-05-18 03:00:10,421 - INFO - 第 1 页获取到 100 条记录
2025-05-18 03:00:10,421 - INFO - Request Parameters - Page 2:
2025-05-18 03:00:10,421 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 03:00:10,421 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 03:00:10,944 - INFO - Response - Page 2:
2025-05-18 03:00:11,144 - INFO - 第 2 页获取到 100 条记录
2025-05-18 03:00:11,144 - INFO - Request Parameters - Page 3:
2025-05-18 03:00:11,144 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 03:00:11,144 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 03:00:11,624 - INFO - Response - Page 3:
2025-05-18 03:00:11,824 - INFO - 第 3 页获取到 100 条记录
2025-05-18 03:00:11,824 - INFO - Request Parameters - Page 4:
2025-05-18 03:00:11,824 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 03:00:11,824 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 03:00:12,441 - INFO - Response - Page 4:
2025-05-18 03:00:12,642 - INFO - 第 4 页获取到 100 条记录
2025-05-18 03:00:12,642 - INFO - Request Parameters - Page 5:
2025-05-18 03:00:12,642 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 03:00:12,642 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 03:00:13,281 - INFO - Response - Page 5:
2025-05-18 03:00:13,481 - INFO - 第 5 页获取到 100 条记录
2025-05-18 03:00:13,481 - INFO - Request Parameters - Page 6:
2025-05-18 03:00:13,481 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 03:00:13,481 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 03:00:13,983 - INFO - Response - Page 6:
2025-05-18 03:00:14,184 - INFO - 第 6 页获取到 100 条记录
2025-05-18 03:00:14,184 - INFO - Request Parameters - Page 7:
2025-05-18 03:00:14,184 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 03:00:14,184 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 03:00:14,692 - INFO - Response - Page 7:
2025-05-18 03:00:14,893 - INFO - 第 7 页获取到 70 条记录
2025-05-18 03:00:14,893 - INFO - 查询完成，共获取到 670 条记录
2025-05-18 03:00:14,893 - INFO - 获取到 670 条表单数据
2025-05-18 03:00:14,905 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-18 03:00:14,917 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-18 03:00:14,917 - INFO - 开始处理日期: 2025-03
2025-05-18 03:00:14,917 - INFO - Request Parameters - Page 1:
2025-05-18 03:00:14,917 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 03:00:14,917 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 03:00:15,433 - INFO - Response - Page 1:
2025-05-18 03:00:15,634 - INFO - 第 1 页获取到 100 条记录
2025-05-18 03:00:15,634 - INFO - Request Parameters - Page 2:
2025-05-18 03:00:15,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 03:00:15,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 03:00:16,078 - INFO - Response - Page 2:
2025-05-18 03:00:16,278 - INFO - 第 2 页获取到 100 条记录
2025-05-18 03:00:16,278 - INFO - Request Parameters - Page 3:
2025-05-18 03:00:16,278 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 03:00:16,278 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 03:00:16,726 - INFO - Response - Page 3:
2025-05-18 03:00:16,927 - INFO - 第 3 页获取到 100 条记录
2025-05-18 03:00:16,927 - INFO - Request Parameters - Page 4:
2025-05-18 03:00:16,927 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 03:00:16,927 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 03:00:17,414 - INFO - Response - Page 4:
2025-05-18 03:00:17,614 - INFO - 第 4 页获取到 100 条记录
2025-05-18 03:00:17,614 - INFO - Request Parameters - Page 5:
2025-05-18 03:00:17,614 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 03:00:17,614 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 03:00:18,104 - INFO - Response - Page 5:
2025-05-18 03:00:18,306 - INFO - 第 5 页获取到 100 条记录
2025-05-18 03:00:18,306 - INFO - Request Parameters - Page 6:
2025-05-18 03:00:18,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 03:00:18,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 03:00:18,747 - INFO - Response - Page 6:
2025-05-18 03:00:18,947 - INFO - 第 6 页获取到 100 条记录
2025-05-18 03:00:18,947 - INFO - Request Parameters - Page 7:
2025-05-18 03:00:18,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 03:00:18,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 03:00:19,437 - INFO - Response - Page 7:
2025-05-18 03:00:19,638 - INFO - 第 7 页获取到 61 条记录
2025-05-18 03:00:19,638 - INFO - 查询完成，共获取到 661 条记录
2025-05-18 03:00:19,638 - INFO - 获取到 661 条表单数据
2025-05-18 03:00:19,649 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-18 03:00:19,661 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-18 03:00:19,661 - INFO - 开始处理日期: 2025-04
2025-05-18 03:00:19,661 - INFO - Request Parameters - Page 1:
2025-05-18 03:00:19,661 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 03:00:19,662 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 03:00:20,144 - INFO - Response - Page 1:
2025-05-18 03:00:20,345 - INFO - 第 1 页获取到 100 条记录
2025-05-18 03:00:20,345 - INFO - Request Parameters - Page 2:
2025-05-18 03:00:20,345 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 03:00:20,345 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 03:00:20,851 - INFO - Response - Page 2:
2025-05-18 03:00:21,052 - INFO - 第 2 页获取到 100 条记录
2025-05-18 03:00:21,052 - INFO - Request Parameters - Page 3:
2025-05-18 03:00:21,052 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 03:00:21,052 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 03:00:21,533 - INFO - Response - Page 3:
2025-05-18 03:00:21,733 - INFO - 第 3 页获取到 100 条记录
2025-05-18 03:00:21,733 - INFO - Request Parameters - Page 4:
2025-05-18 03:00:21,733 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 03:00:21,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 03:00:22,258 - INFO - Response - Page 4:
2025-05-18 03:00:22,458 - INFO - 第 4 页获取到 100 条记录
2025-05-18 03:00:22,458 - INFO - Request Parameters - Page 5:
2025-05-18 03:00:22,458 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 03:00:22,458 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 03:00:22,939 - INFO - Response - Page 5:
2025-05-18 03:00:23,140 - INFO - 第 5 页获取到 100 条记录
2025-05-18 03:00:23,140 - INFO - Request Parameters - Page 6:
2025-05-18 03:00:23,140 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 03:00:23,140 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 03:00:23,657 - INFO - Response - Page 6:
2025-05-18 03:00:23,857 - INFO - 第 6 页获取到 100 条记录
2025-05-18 03:00:23,857 - INFO - Request Parameters - Page 7:
2025-05-18 03:00:23,857 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 03:00:23,857 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 03:00:24,241 - INFO - Response - Page 7:
2025-05-18 03:00:24,441 - INFO - 第 7 页获取到 54 条记录
2025-05-18 03:00:24,441 - INFO - 查询完成，共获取到 654 条记录
2025-05-18 03:00:24,441 - INFO - 获取到 654 条表单数据
2025-05-18 03:00:24,453 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-18 03:00:24,464 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-18 03:00:24,465 - INFO - 开始处理日期: 2025-05
2025-05-18 03:00:24,465 - INFO - Request Parameters - Page 1:
2025-05-18 03:00:24,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 03:00:24,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 03:00:25,002 - INFO - Response - Page 1:
2025-05-18 03:00:25,202 - INFO - 第 1 页获取到 100 条记录
2025-05-18 03:00:25,202 - INFO - Request Parameters - Page 2:
2025-05-18 03:00:25,202 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 03:00:25,202 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 03:00:25,678 - INFO - Response - Page 2:
2025-05-18 03:00:25,878 - INFO - 第 2 页获取到 100 条记录
2025-05-18 03:00:25,878 - INFO - Request Parameters - Page 3:
2025-05-18 03:00:25,878 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 03:00:25,878 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 03:00:26,411 - INFO - Response - Page 3:
2025-05-18 03:00:26,611 - INFO - 第 3 页获取到 100 条记录
2025-05-18 03:00:26,611 - INFO - Request Parameters - Page 4:
2025-05-18 03:00:26,611 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 03:00:26,611 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 03:00:27,168 - INFO - Response - Page 4:
2025-05-18 03:00:27,368 - INFO - 第 4 页获取到 100 条记录
2025-05-18 03:00:27,368 - INFO - Request Parameters - Page 5:
2025-05-18 03:00:27,368 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 03:00:27,368 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 03:00:27,901 - INFO - Response - Page 5:
2025-05-18 03:00:28,101 - INFO - 第 5 页获取到 100 条记录
2025-05-18 03:00:28,101 - INFO - Request Parameters - Page 6:
2025-05-18 03:00:28,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 03:00:28,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 03:00:28,591 - INFO - Response - Page 6:
2025-05-18 03:00:28,792 - INFO - 第 6 页获取到 100 条记录
2025-05-18 03:00:28,792 - INFO - Request Parameters - Page 7:
2025-05-18 03:00:28,792 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 03:00:28,792 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 03:00:29,126 - INFO - Response - Page 7:
2025-05-18 03:00:29,327 - INFO - 第 7 页获取到 25 条记录
2025-05-18 03:00:29,327 - INFO - 查询完成，共获取到 625 条记录
2025-05-18 03:00:29,327 - INFO - 获取到 625 条表单数据
2025-05-18 03:00:29,339 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-18 03:00:29,348 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-18 03:00:29,811 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-18 03:00:29,811 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 224290.0, 'new_value': 226566.0}, {'field': 'total_amount', 'old_value': 228646.0, 'new_value': 230922.0}, {'field': 'order_count', 'old_value': 62, 'new_value': 65}]
2025-05-18 03:00:29,812 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-18 03:00:29,812 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-05-18 03:00:29,814 - INFO - =================同步完成====================
2025-05-18 06:00:02,001 - INFO - =================使用默认全量同步=============
2025-05-18 06:00:03,395 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-18 06:00:03,396 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-18 06:00:03,422 - INFO - 开始处理日期: 2025-01
2025-05-18 06:00:03,425 - INFO - Request Parameters - Page 1:
2025-05-18 06:00:03,425 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 06:00:03,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 06:00:04,753 - INFO - Response - Page 1:
2025-05-18 06:00:04,953 - INFO - 第 1 页获取到 100 条记录
2025-05-18 06:00:04,953 - INFO - Request Parameters - Page 2:
2025-05-18 06:00:04,953 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 06:00:04,953 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 06:00:05,575 - INFO - Response - Page 2:
2025-05-18 06:00:05,776 - INFO - 第 2 页获取到 100 条记录
2025-05-18 06:00:05,776 - INFO - Request Parameters - Page 3:
2025-05-18 06:00:05,776 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 06:00:05,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 06:00:06,324 - INFO - Response - Page 3:
2025-05-18 06:00:06,524 - INFO - 第 3 页获取到 100 条记录
2025-05-18 06:00:06,524 - INFO - Request Parameters - Page 4:
2025-05-18 06:00:06,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 06:00:06,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 06:00:07,100 - INFO - Response - Page 4:
2025-05-18 06:00:07,301 - INFO - 第 4 页获取到 100 条记录
2025-05-18 06:00:07,301 - INFO - Request Parameters - Page 5:
2025-05-18 06:00:07,301 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 06:00:07,301 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 06:00:07,815 - INFO - Response - Page 5:
2025-05-18 06:00:08,016 - INFO - 第 5 页获取到 100 条记录
2025-05-18 06:00:08,016 - INFO - Request Parameters - Page 6:
2025-05-18 06:00:08,016 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 06:00:08,016 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 06:00:08,623 - INFO - Response - Page 6:
2025-05-18 06:00:08,823 - INFO - 第 6 页获取到 100 条记录
2025-05-18 06:00:08,823 - INFO - Request Parameters - Page 7:
2025-05-18 06:00:08,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 06:00:08,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 06:00:09,370 - INFO - Response - Page 7:
2025-05-18 06:00:09,570 - INFO - 第 7 页获取到 82 条记录
2025-05-18 06:00:09,570 - INFO - 查询完成，共获取到 682 条记录
2025-05-18 06:00:09,570 - INFO - 获取到 682 条表单数据
2025-05-18 06:00:09,582 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-18 06:00:09,594 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-18 06:00:09,594 - INFO - 开始处理日期: 2025-02
2025-05-18 06:00:09,595 - INFO - Request Parameters - Page 1:
2025-05-18 06:00:09,595 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 06:00:09,595 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 06:00:10,106 - INFO - Response - Page 1:
2025-05-18 06:00:10,308 - INFO - 第 1 页获取到 100 条记录
2025-05-18 06:00:10,308 - INFO - Request Parameters - Page 2:
2025-05-18 06:00:10,308 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 06:00:10,308 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 06:00:10,756 - INFO - Response - Page 2:
2025-05-18 06:00:10,956 - INFO - 第 2 页获取到 100 条记录
2025-05-18 06:00:10,956 - INFO - Request Parameters - Page 3:
2025-05-18 06:00:10,956 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 06:00:10,956 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 06:00:11,409 - INFO - Response - Page 3:
2025-05-18 06:00:11,609 - INFO - 第 3 页获取到 100 条记录
2025-05-18 06:00:11,609 - INFO - Request Parameters - Page 4:
2025-05-18 06:00:11,609 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 06:00:11,609 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 06:00:12,097 - INFO - Response - Page 4:
2025-05-18 06:00:12,297 - INFO - 第 4 页获取到 100 条记录
2025-05-18 06:00:12,297 - INFO - Request Parameters - Page 5:
2025-05-18 06:00:12,297 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 06:00:12,297 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 06:00:12,834 - INFO - Response - Page 5:
2025-05-18 06:00:13,034 - INFO - 第 5 页获取到 100 条记录
2025-05-18 06:00:13,034 - INFO - Request Parameters - Page 6:
2025-05-18 06:00:13,034 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 06:00:13,034 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 06:00:13,758 - INFO - Response - Page 6:
2025-05-18 06:00:13,959 - INFO - 第 6 页获取到 100 条记录
2025-05-18 06:00:13,959 - INFO - Request Parameters - Page 7:
2025-05-18 06:00:13,959 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 06:00:13,959 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 06:00:14,393 - INFO - Response - Page 7:
2025-05-18 06:00:14,594 - INFO - 第 7 页获取到 70 条记录
2025-05-18 06:00:14,594 - INFO - 查询完成，共获取到 670 条记录
2025-05-18 06:00:14,594 - INFO - 获取到 670 条表单数据
2025-05-18 06:00:14,607 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-18 06:00:14,621 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-18 06:00:14,621 - INFO - 开始处理日期: 2025-03
2025-05-18 06:00:14,622 - INFO - Request Parameters - Page 1:
2025-05-18 06:00:14,622 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 06:00:14,622 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 06:00:15,101 - INFO - Response - Page 1:
2025-05-18 06:00:15,302 - INFO - 第 1 页获取到 100 条记录
2025-05-18 06:00:15,302 - INFO - Request Parameters - Page 2:
2025-05-18 06:00:15,302 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 06:00:15,302 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 06:00:15,876 - INFO - Response - Page 2:
2025-05-18 06:00:16,076 - INFO - 第 2 页获取到 100 条记录
2025-05-18 06:00:16,076 - INFO - Request Parameters - Page 3:
2025-05-18 06:00:16,076 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 06:00:16,076 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 06:00:16,655 - INFO - Response - Page 3:
2025-05-18 06:00:16,855 - INFO - 第 3 页获取到 100 条记录
2025-05-18 06:00:16,855 - INFO - Request Parameters - Page 4:
2025-05-18 06:00:16,855 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 06:00:16,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 06:00:17,329 - INFO - Response - Page 4:
2025-05-18 06:00:17,530 - INFO - 第 4 页获取到 100 条记录
2025-05-18 06:00:17,530 - INFO - Request Parameters - Page 5:
2025-05-18 06:00:17,530 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 06:00:17,530 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 06:00:18,098 - INFO - Response - Page 5:
2025-05-18 06:00:18,298 - INFO - 第 5 页获取到 100 条记录
2025-05-18 06:00:18,298 - INFO - Request Parameters - Page 6:
2025-05-18 06:00:18,298 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 06:00:18,298 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 06:00:18,844 - INFO - Response - Page 6:
2025-05-18 06:00:19,044 - INFO - 第 6 页获取到 100 条记录
2025-05-18 06:00:19,044 - INFO - Request Parameters - Page 7:
2025-05-18 06:00:19,044 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 06:00:19,044 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 06:00:19,526 - INFO - Response - Page 7:
2025-05-18 06:00:19,726 - INFO - 第 7 页获取到 61 条记录
2025-05-18 06:00:19,726 - INFO - 查询完成，共获取到 661 条记录
2025-05-18 06:00:19,726 - INFO - 获取到 661 条表单数据
2025-05-18 06:00:19,740 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-18 06:00:19,753 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-18 06:00:19,753 - INFO - 开始处理日期: 2025-04
2025-05-18 06:00:19,753 - INFO - Request Parameters - Page 1:
2025-05-18 06:00:19,753 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 06:00:19,753 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 06:00:20,237 - INFO - Response - Page 1:
2025-05-18 06:00:20,437 - INFO - 第 1 页获取到 100 条记录
2025-05-18 06:00:20,437 - INFO - Request Parameters - Page 2:
2025-05-18 06:00:20,437 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 06:00:20,437 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 06:00:21,034 - INFO - Response - Page 2:
2025-05-18 06:00:21,234 - INFO - 第 2 页获取到 100 条记录
2025-05-18 06:00:21,234 - INFO - Request Parameters - Page 3:
2025-05-18 06:00:21,234 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 06:00:21,234 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 06:00:21,719 - INFO - Response - Page 3:
2025-05-18 06:00:21,919 - INFO - 第 3 页获取到 100 条记录
2025-05-18 06:00:21,919 - INFO - Request Parameters - Page 4:
2025-05-18 06:00:21,919 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 06:00:21,919 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 06:00:22,516 - INFO - Response - Page 4:
2025-05-18 06:00:22,716 - INFO - 第 4 页获取到 100 条记录
2025-05-18 06:00:22,716 - INFO - Request Parameters - Page 5:
2025-05-18 06:00:22,716 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 06:00:22,716 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 06:00:23,195 - INFO - Response - Page 5:
2025-05-18 06:00:23,395 - INFO - 第 5 页获取到 100 条记录
2025-05-18 06:00:23,395 - INFO - Request Parameters - Page 6:
2025-05-18 06:00:23,395 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 06:00:23,395 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 06:00:23,857 - INFO - Response - Page 6:
2025-05-18 06:00:24,057 - INFO - 第 6 页获取到 100 条记录
2025-05-18 06:00:24,057 - INFO - Request Parameters - Page 7:
2025-05-18 06:00:24,057 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 06:00:24,057 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 06:00:24,476 - INFO - Response - Page 7:
2025-05-18 06:00:24,677 - INFO - 第 7 页获取到 54 条记录
2025-05-18 06:00:24,677 - INFO - 查询完成，共获取到 654 条记录
2025-05-18 06:00:24,678 - INFO - 获取到 654 条表单数据
2025-05-18 06:00:24,690 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-18 06:00:24,703 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-18 06:00:24,703 - INFO - 开始处理日期: 2025-05
2025-05-18 06:00:24,703 - INFO - Request Parameters - Page 1:
2025-05-18 06:00:24,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 06:00:24,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 06:00:25,248 - INFO - Response - Page 1:
2025-05-18 06:00:25,449 - INFO - 第 1 页获取到 100 条记录
2025-05-18 06:00:25,449 - INFO - Request Parameters - Page 2:
2025-05-18 06:00:25,449 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 06:00:25,449 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 06:00:25,889 - INFO - Response - Page 2:
2025-05-18 06:00:26,090 - INFO - 第 2 页获取到 100 条记录
2025-05-18 06:00:26,090 - INFO - Request Parameters - Page 3:
2025-05-18 06:00:26,090 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 06:00:26,090 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 06:00:26,552 - INFO - Response - Page 3:
2025-05-18 06:00:26,752 - INFO - 第 3 页获取到 100 条记录
2025-05-18 06:00:26,752 - INFO - Request Parameters - Page 4:
2025-05-18 06:00:26,752 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 06:00:26,752 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 06:00:27,265 - INFO - Response - Page 4:
2025-05-18 06:00:27,466 - INFO - 第 4 页获取到 100 条记录
2025-05-18 06:00:27,466 - INFO - Request Parameters - Page 5:
2025-05-18 06:00:27,466 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 06:00:27,466 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 06:00:27,967 - INFO - Response - Page 5:
2025-05-18 06:00:28,167 - INFO - 第 5 页获取到 100 条记录
2025-05-18 06:00:28,167 - INFO - Request Parameters - Page 6:
2025-05-18 06:00:28,167 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 06:00:28,167 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 06:00:28,629 - INFO - Response - Page 6:
2025-05-18 06:00:28,829 - INFO - 第 6 页获取到 100 条记录
2025-05-18 06:00:28,829 - INFO - Request Parameters - Page 7:
2025-05-18 06:00:28,829 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 06:00:28,829 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 06:00:29,193 - INFO - Response - Page 7:
2025-05-18 06:00:29,393 - INFO - 第 7 页获取到 25 条记录
2025-05-18 06:00:29,393 - INFO - 查询完成，共获取到 625 条记录
2025-05-18 06:00:29,393 - INFO - 获取到 625 条表单数据
2025-05-18 06:00:29,406 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-18 06:00:29,418 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-18 06:00:29,418 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-18 06:00:29,420 - INFO - =================同步完成====================
2025-05-18 09:00:01,995 - INFO - =================使用默认全量同步=============
2025-05-18 09:00:03,376 - INFO - MySQL查询成功，共获取 3292 条记录
2025-05-18 09:00:03,377 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-18 09:00:03,408 - INFO - 开始处理日期: 2025-01
2025-05-18 09:00:03,412 - INFO - Request Parameters - Page 1:
2025-05-18 09:00:03,412 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 09:00:03,412 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 09:00:04,735 - INFO - Response - Page 1:
2025-05-18 09:00:04,935 - INFO - 第 1 页获取到 100 条记录
2025-05-18 09:00:04,935 - INFO - Request Parameters - Page 2:
2025-05-18 09:00:04,935 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 09:00:04,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 09:00:05,510 - INFO - Response - Page 2:
2025-05-18 09:00:05,710 - INFO - 第 2 页获取到 100 条记录
2025-05-18 09:00:05,710 - INFO - Request Parameters - Page 3:
2025-05-18 09:00:05,710 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 09:00:05,710 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 09:00:06,162 - INFO - Response - Page 3:
2025-05-18 09:00:06,362 - INFO - 第 3 页获取到 100 条记录
2025-05-18 09:00:06,362 - INFO - Request Parameters - Page 4:
2025-05-18 09:00:06,362 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 09:00:06,362 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 09:00:06,865 - INFO - Response - Page 4:
2025-05-18 09:00:07,065 - INFO - 第 4 页获取到 100 条记录
2025-05-18 09:00:07,065 - INFO - Request Parameters - Page 5:
2025-05-18 09:00:07,065 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 09:00:07,065 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 09:00:07,561 - INFO - Response - Page 5:
2025-05-18 09:00:07,763 - INFO - 第 5 页获取到 100 条记录
2025-05-18 09:00:07,763 - INFO - Request Parameters - Page 6:
2025-05-18 09:00:07,763 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 09:00:07,763 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 09:00:08,265 - INFO - Response - Page 6:
2025-05-18 09:00:08,465 - INFO - 第 6 页获取到 100 条记录
2025-05-18 09:00:08,465 - INFO - Request Parameters - Page 7:
2025-05-18 09:00:08,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 09:00:08,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 09:00:08,923 - INFO - Response - Page 7:
2025-05-18 09:00:09,124 - INFO - 第 7 页获取到 82 条记录
2025-05-18 09:00:09,124 - INFO - 查询完成，共获取到 682 条记录
2025-05-18 09:00:09,124 - INFO - 获取到 682 条表单数据
2025-05-18 09:00:09,137 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-18 09:00:09,148 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-18 09:00:09,149 - INFO - 开始处理日期: 2025-02
2025-05-18 09:00:09,149 - INFO - Request Parameters - Page 1:
2025-05-18 09:00:09,149 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 09:00:09,149 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 09:00:09,658 - INFO - Response - Page 1:
2025-05-18 09:00:09,858 - INFO - 第 1 页获取到 100 条记录
2025-05-18 09:00:09,858 - INFO - Request Parameters - Page 2:
2025-05-18 09:00:09,858 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 09:00:09,858 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 09:00:10,444 - INFO - Response - Page 2:
2025-05-18 09:00:10,644 - INFO - 第 2 页获取到 100 条记录
2025-05-18 09:00:10,644 - INFO - Request Parameters - Page 3:
2025-05-18 09:00:10,644 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 09:00:10,644 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 09:00:11,277 - INFO - Response - Page 3:
2025-05-18 09:00:11,477 - INFO - 第 3 页获取到 100 条记录
2025-05-18 09:00:11,477 - INFO - Request Parameters - Page 4:
2025-05-18 09:00:11,477 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 09:00:11,477 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 09:00:11,976 - INFO - Response - Page 4:
2025-05-18 09:00:12,177 - INFO - 第 4 页获取到 100 条记录
2025-05-18 09:00:12,177 - INFO - Request Parameters - Page 5:
2025-05-18 09:00:12,177 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 09:00:12,177 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 09:00:12,737 - INFO - Response - Page 5:
2025-05-18 09:00:12,937 - INFO - 第 5 页获取到 100 条记录
2025-05-18 09:00:12,937 - INFO - Request Parameters - Page 6:
2025-05-18 09:00:12,937 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 09:00:12,937 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 09:00:13,447 - INFO - Response - Page 6:
2025-05-18 09:00:13,647 - INFO - 第 6 页获取到 100 条记录
2025-05-18 09:00:13,647 - INFO - Request Parameters - Page 7:
2025-05-18 09:00:13,647 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 09:00:13,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 09:00:14,170 - INFO - Response - Page 7:
2025-05-18 09:00:14,371 - INFO - 第 7 页获取到 70 条记录
2025-05-18 09:00:14,371 - INFO - 查询完成，共获取到 670 条记录
2025-05-18 09:00:14,371 - INFO - 获取到 670 条表单数据
2025-05-18 09:00:14,384 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-18 09:00:14,395 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-18 09:00:14,395 - INFO - 开始处理日期: 2025-03
2025-05-18 09:00:14,395 - INFO - Request Parameters - Page 1:
2025-05-18 09:00:14,395 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 09:00:14,395 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 09:00:14,945 - INFO - Response - Page 1:
2025-05-18 09:00:15,145 - INFO - 第 1 页获取到 100 条记录
2025-05-18 09:00:15,145 - INFO - Request Parameters - Page 2:
2025-05-18 09:00:15,145 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 09:00:15,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 09:00:15,679 - INFO - Response - Page 2:
2025-05-18 09:00:15,879 - INFO - 第 2 页获取到 100 条记录
2025-05-18 09:00:15,879 - INFO - Request Parameters - Page 3:
2025-05-18 09:00:15,879 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 09:00:15,879 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 09:00:16,355 - INFO - Response - Page 3:
2025-05-18 09:00:16,557 - INFO - 第 3 页获取到 100 条记录
2025-05-18 09:00:16,557 - INFO - Request Parameters - Page 4:
2025-05-18 09:00:16,557 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 09:00:16,557 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 09:00:17,105 - INFO - Response - Page 4:
2025-05-18 09:00:17,306 - INFO - 第 4 页获取到 100 条记录
2025-05-18 09:00:17,306 - INFO - Request Parameters - Page 5:
2025-05-18 09:00:17,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 09:00:17,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 09:00:17,846 - INFO - Response - Page 5:
2025-05-18 09:00:18,046 - INFO - 第 5 页获取到 100 条记录
2025-05-18 09:00:18,046 - INFO - Request Parameters - Page 6:
2025-05-18 09:00:18,046 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 09:00:18,046 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 09:00:18,546 - INFO - Response - Page 6:
2025-05-18 09:00:18,746 - INFO - 第 6 页获取到 100 条记录
2025-05-18 09:00:18,746 - INFO - Request Parameters - Page 7:
2025-05-18 09:00:18,746 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 09:00:18,746 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 09:00:19,159 - INFO - Response - Page 7:
2025-05-18 09:00:19,360 - INFO - 第 7 页获取到 61 条记录
2025-05-18 09:00:19,360 - INFO - 查询完成，共获取到 661 条记录
2025-05-18 09:00:19,360 - INFO - 获取到 661 条表单数据
2025-05-18 09:00:19,373 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-18 09:00:19,384 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-18 09:00:19,384 - INFO - 开始处理日期: 2025-04
2025-05-18 09:00:19,384 - INFO - Request Parameters - Page 1:
2025-05-18 09:00:19,384 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 09:00:19,384 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 09:00:19,971 - INFO - Response - Page 1:
2025-05-18 09:00:20,171 - INFO - 第 1 页获取到 100 条记录
2025-05-18 09:00:20,171 - INFO - Request Parameters - Page 2:
2025-05-18 09:00:20,171 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 09:00:20,171 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 09:00:20,721 - INFO - Response - Page 2:
2025-05-18 09:00:20,922 - INFO - 第 2 页获取到 100 条记录
2025-05-18 09:00:20,922 - INFO - Request Parameters - Page 3:
2025-05-18 09:00:20,922 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 09:00:20,922 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 09:00:21,355 - INFO - Response - Page 3:
2025-05-18 09:00:21,555 - INFO - 第 3 页获取到 100 条记录
2025-05-18 09:00:21,555 - INFO - Request Parameters - Page 4:
2025-05-18 09:00:21,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 09:00:21,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 09:00:22,120 - INFO - Response - Page 4:
2025-05-18 09:00:22,320 - INFO - 第 4 页获取到 100 条记录
2025-05-18 09:00:22,320 - INFO - Request Parameters - Page 5:
2025-05-18 09:00:22,320 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 09:00:22,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 09:00:22,839 - INFO - Response - Page 5:
2025-05-18 09:00:23,039 - INFO - 第 5 页获取到 100 条记录
2025-05-18 09:00:23,039 - INFO - Request Parameters - Page 6:
2025-05-18 09:00:23,039 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 09:00:23,039 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 09:00:23,556 - INFO - Response - Page 6:
2025-05-18 09:00:23,758 - INFO - 第 6 页获取到 100 条记录
2025-05-18 09:00:23,758 - INFO - Request Parameters - Page 7:
2025-05-18 09:00:23,758 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 09:00:23,758 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 09:00:24,140 - INFO - Response - Page 7:
2025-05-18 09:00:24,341 - INFO - 第 7 页获取到 54 条记录
2025-05-18 09:00:24,341 - INFO - 查询完成，共获取到 654 条记录
2025-05-18 09:00:24,341 - INFO - 获取到 654 条表单数据
2025-05-18 09:00:24,353 - INFO - 当前日期 2025-04 有 654 条MySQL数据需要处理
2025-05-18 09:00:24,364 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-18 09:00:24,364 - INFO - 开始处理日期: 2025-05
2025-05-18 09:00:24,364 - INFO - Request Parameters - Page 1:
2025-05-18 09:00:24,364 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 09:00:24,364 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 09:00:24,889 - INFO - Response - Page 1:
2025-05-18 09:00:25,089 - INFO - 第 1 页获取到 100 条记录
2025-05-18 09:00:25,089 - INFO - Request Parameters - Page 2:
2025-05-18 09:00:25,089 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 09:00:25,089 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 09:00:25,725 - INFO - Response - Page 2:
2025-05-18 09:00:25,926 - INFO - 第 2 页获取到 100 条记录
2025-05-18 09:00:25,926 - INFO - Request Parameters - Page 3:
2025-05-18 09:00:25,926 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 09:00:25,926 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 09:00:26,400 - INFO - Response - Page 3:
2025-05-18 09:00:26,600 - INFO - 第 3 页获取到 100 条记录
2025-05-18 09:00:26,600 - INFO - Request Parameters - Page 4:
2025-05-18 09:00:26,600 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 09:00:26,600 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 09:00:27,064 - INFO - Response - Page 4:
2025-05-18 09:00:27,265 - INFO - 第 4 页获取到 100 条记录
2025-05-18 09:00:27,265 - INFO - Request Parameters - Page 5:
2025-05-18 09:00:27,265 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 09:00:27,265 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 09:00:27,752 - INFO - Response - Page 5:
2025-05-18 09:00:27,952 - INFO - 第 5 页获取到 100 条记录
2025-05-18 09:00:27,952 - INFO - Request Parameters - Page 6:
2025-05-18 09:00:27,952 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 09:00:27,952 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 09:00:28,515 - INFO - Response - Page 6:
2025-05-18 09:00:28,717 - INFO - 第 6 页获取到 100 条记录
2025-05-18 09:00:28,717 - INFO - Request Parameters - Page 7:
2025-05-18 09:00:28,717 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 09:00:28,717 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 09:00:29,030 - INFO - Response - Page 7:
2025-05-18 09:00:29,230 - INFO - 第 7 页获取到 25 条记录
2025-05-18 09:00:29,230 - INFO - 查询完成，共获取到 625 条记录
2025-05-18 09:00:29,230 - INFO - 获取到 625 条表单数据
2025-05-18 09:00:29,242 - INFO - 当前日期 2025-05 有 625 条MySQL数据需要处理
2025-05-18 09:00:29,242 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2O
2025-05-18 09:00:29,758 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2O
2025-05-18 09:00:29,758 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 171800.0, 'new_value': 207600.0}, {'field': 'total_amount', 'old_value': 171800.0, 'new_value': 207600.0}]
2025-05-18 09:00:29,758 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-18 09:00:30,159 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-18 09:00:30,159 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1435.0, 'new_value': 1525.0}, {'field': 'offline_amount', 'old_value': 26950.0, 'new_value': 27980.0}, {'field': 'total_amount', 'old_value': 28385.0, 'new_value': 29505.0}, {'field': 'order_count', 'old_value': 366, 'new_value': 382}]
2025-05-18 09:00:30,160 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-18 09:00:30,663 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-18 09:00:30,663 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 242953.0, 'new_value': 260312.0}, {'field': 'total_amount', 'old_value': 242953.0, 'new_value': 260312.0}, {'field': 'order_count', 'old_value': 177, 'new_value': 190}]
2025-05-18 09:00:30,663 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-18 09:00:31,087 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-18 09:00:31,087 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6400000.0, 'new_value': 6800000.0}, {'field': 'total_amount', 'old_value': 6500000.0, 'new_value': 6900000.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 33}]
2025-05-18 09:00:31,087 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-18 09:00:31,547 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-18 09:00:31,547 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 205030.0, 'new_value': 221530.0}, {'field': 'total_amount', 'old_value': 205030.0, 'new_value': 221530.0}, {'field': 'order_count', 'old_value': 115, 'new_value': 125}]
2025-05-18 09:00:31,548 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-18 09:00:32,033 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-18 09:00:32,033 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63747.0, 'new_value': 68828.0}, {'field': 'total_amount', 'old_value': 63747.0, 'new_value': 68828.0}, {'field': 'order_count', 'old_value': 72, 'new_value': 78}]
2025-05-18 09:00:32,034 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMLC
2025-05-18 09:00:32,506 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMLC
2025-05-18 09:00:32,507 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 19498.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 19498.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 2}]
2025-05-18 09:00:32,507 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMOC
2025-05-18 09:00:32,935 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMOC
2025-05-18 09:00:32,935 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2199100.0, 'new_value': 2419000.0}, {'field': 'total_amount', 'old_value': 2199100.0, 'new_value': 2419000.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-05-18 09:00:32,935 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-18 09:00:33,418 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-18 09:00:33,419 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 114769.0, 'new_value': 119769.0}, {'field': 'total_amount', 'old_value': 114769.0, 'new_value': 119769.0}, {'field': 'order_count', 'old_value': 36, 'new_value': 37}]
2025-05-18 09:00:33,419 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-18 09:00:33,846 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-18 09:00:33,847 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36917.42, 'new_value': 39102.0}, {'field': 'offline_amount', 'old_value': 73888.45, 'new_value': 76993.45}, {'field': 'total_amount', 'old_value': 110805.87, 'new_value': 116095.45}, {'field': 'order_count', 'old_value': 1326, 'new_value': 1381}]
2025-05-18 09:00:33,847 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-18 09:00:34,303 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-18 09:00:34,303 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14714.29, 'new_value': 15479.95}, {'field': 'offline_amount', 'old_value': 16765.96, 'new_value': 18324.25}, {'field': 'total_amount', 'old_value': 31480.25, 'new_value': 33804.2}, {'field': 'order_count', 'old_value': 1527, 'new_value': 1631}]
2025-05-18 09:00:34,303 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-18 09:00:34,737 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-18 09:00:34,737 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 209135.7, 'new_value': 228500.4}, {'field': 'total_amount', 'old_value': 324155.4, 'new_value': 343520.1}, {'field': 'order_count', 'old_value': 2255, 'new_value': 2454}]
2025-05-18 09:00:34,737 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-18 09:00:35,168 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-18 09:00:35,168 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66100.2, 'new_value': 70159.5}, {'field': 'total_amount', 'old_value': 66100.2, 'new_value': 70159.5}, {'field': 'order_count', 'old_value': 3610, 'new_value': 3817}]
2025-05-18 09:00:35,168 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMWC
2025-05-18 09:00:35,635 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMWC
2025-05-18 09:00:35,636 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 4500.0}, {'field': 'offline_amount', 'old_value': 33680.0, 'new_value': 50480.0}, {'field': 'total_amount', 'old_value': 33680.0, 'new_value': 54980.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 12}]
2025-05-18 09:00:35,636 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-18 09:00:36,132 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-18 09:00:36,132 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91227.46, 'new_value': 99899.79}, {'field': 'total_amount', 'old_value': 91227.46, 'new_value': 99899.79}, {'field': 'order_count', 'old_value': 1040, 'new_value': 1125}]
2025-05-18 09:00:36,132 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-18 09:00:36,520 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-18 09:00:36,520 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4449.0, 'new_value': 5394.0}, {'field': 'total_amount', 'old_value': 17863.0, 'new_value': 18808.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 57}]
2025-05-18 09:00:36,521 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-18 09:00:36,913 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-18 09:00:36,913 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 130432.0, 'new_value': 139782.0}, {'field': 'total_amount', 'old_value': 130432.0, 'new_value': 139782.0}, {'field': 'order_count', 'old_value': 237, 'new_value': 254}]
2025-05-18 09:00:36,913 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-18 09:00:37,345 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-18 09:00:37,346 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 118139.74, 'new_value': 128066.57}, {'field': 'offline_amount', 'old_value': 19827.73, 'new_value': 21888.09}, {'field': 'total_amount', 'old_value': 137967.47, 'new_value': 149954.66}, {'field': 'order_count', 'old_value': 496, 'new_value': 542}]
2025-05-18 09:00:37,346 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-18 09:00:37,799 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-18 09:00:37,799 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 116798.0, 'new_value': 126794.0}, {'field': 'offline_amount', 'old_value': 42296.52, 'new_value': 46506.42}, {'field': 'total_amount', 'old_value': 159094.52, 'new_value': 173300.42}, {'field': 'order_count', 'old_value': 972, 'new_value': 1060}]
2025-05-18 09:00:37,800 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-18 09:00:38,216 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-18 09:00:38,217 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1118.0, 'new_value': 1375.0}, {'field': 'offline_amount', 'old_value': 4714.0, 'new_value': 5512.0}, {'field': 'total_amount', 'old_value': 5832.0, 'new_value': 6887.0}, {'field': 'order_count', 'old_value': 115, 'new_value': 138}]
2025-05-18 09:00:38,217 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-18 09:00:38,643 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-18 09:00:38,643 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58474.21, 'new_value': 61602.56}, {'field': 'total_amount', 'old_value': 58474.21, 'new_value': 61602.56}, {'field': 'order_count', 'old_value': 1550, 'new_value': 1641}]
2025-05-18 09:00:38,643 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-18 09:00:39,093 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMND
2025-05-18 09:00:39,093 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 110714.1, 'new_value': 112215.1}, {'field': 'total_amount', 'old_value': 110714.1, 'new_value': 112215.1}, {'field': 'order_count', 'old_value': 46, 'new_value': 47}]
2025-05-18 09:00:39,093 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-18 09:00:39,520 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-18 09:00:39,521 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 98220.0, 'new_value': 108750.0}, {'field': 'offline_amount', 'old_value': 43825.12, 'new_value': 48717.92}, {'field': 'total_amount', 'old_value': 142045.12, 'new_value': 157467.92}, {'field': 'order_count', 'old_value': 1009, 'new_value': 1108}]
2025-05-18 09:00:39,521 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-18 09:00:39,972 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-18 09:00:39,972 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7101.49, 'new_value': 7790.61}, {'field': 'offline_amount', 'old_value': 117015.72, 'new_value': 125613.74}, {'field': 'total_amount', 'old_value': 124117.21, 'new_value': 133404.35}, {'field': 'order_count', 'old_value': 1346, 'new_value': 1437}]
2025-05-18 09:00:39,973 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-18 09:00:40,430 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-18 09:00:40,431 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42273.0, 'new_value': 47565.0}, {'field': 'total_amount', 'old_value': 46769.32, 'new_value': 52061.32}, {'field': 'order_count', 'old_value': 436, 'new_value': 441}]
2025-05-18 09:00:40,431 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-18 09:00:40,878 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-18 09:00:40,879 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91958.0, 'new_value': 98238.0}, {'field': 'total_amount', 'old_value': 91958.0, 'new_value': 98238.0}, {'field': 'order_count', 'old_value': 2267, 'new_value': 2424}]
2025-05-18 09:00:40,879 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-18 09:00:41,365 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-18 09:00:41,366 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15124.78, 'new_value': 15735.19}, {'field': 'total_amount', 'old_value': 15124.78, 'new_value': 15735.19}, {'field': 'order_count', 'old_value': 72, 'new_value': 78}]
2025-05-18 09:00:41,366 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-18 09:00:41,785 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-18 09:00:41,785 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107239.0, 'new_value': 113682.0}, {'field': 'total_amount', 'old_value': 107239.0, 'new_value': 113682.0}, {'field': 'order_count', 'old_value': 3992, 'new_value': 4213}]
2025-05-18 09:00:41,785 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-18 09:00:42,262 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-18 09:00:42,262 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 304293.0, 'new_value': 315887.0}, {'field': 'total_amount', 'old_value': 320070.0, 'new_value': 331664.0}, {'field': 'order_count', 'old_value': 55, 'new_value': 58}]
2025-05-18 09:00:42,263 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-18 09:00:42,723 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-18 09:00:42,723 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87654.48, 'new_value': 92974.48}, {'field': 'total_amount', 'old_value': 87654.48, 'new_value': 92974.48}, {'field': 'order_count', 'old_value': 3213, 'new_value': 3423}]
2025-05-18 09:00:42,723 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-18 09:00:43,207 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-18 09:00:43,208 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30824.96, 'new_value': 32647.11}, {'field': 'offline_amount', 'old_value': 73510.25, 'new_value': 78511.19}, {'field': 'total_amount', 'old_value': 104335.21, 'new_value': 111158.3}, {'field': 'order_count', 'old_value': 3606, 'new_value': 3834}]
2025-05-18 09:00:43,208 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-05-18 09:00:43,639 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-05-18 09:00:43,639 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14158.28, 'new_value': 14940.08}, {'field': 'total_amount', 'old_value': 14158.28, 'new_value': 14940.08}, {'field': 'order_count', 'old_value': 25, 'new_value': 27}]
2025-05-18 09:00:43,639 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-18 09:00:44,078 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-18 09:00:44,078 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 224829.0, 'new_value': 232955.0}, {'field': 'total_amount', 'old_value': 224829.0, 'new_value': 232955.0}, {'field': 'order_count', 'old_value': 116, 'new_value': 126}]
2025-05-18 09:00:44,078 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-18 09:00:44,580 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-18 09:00:44,580 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48569.0, 'new_value': 48975.0}, {'field': 'total_amount', 'old_value': 48569.0, 'new_value': 48975.0}, {'field': 'order_count', 'old_value': 86, 'new_value': 88}]
2025-05-18 09:00:44,580 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-18 09:00:45,014 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-18 09:00:45,014 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44487.83, 'new_value': 50475.43}, {'field': 'offline_amount', 'old_value': 303850.3, 'new_value': 332557.3}, {'field': 'total_amount', 'old_value': 348338.13, 'new_value': 383032.73}, {'field': 'order_count', 'old_value': 526, 'new_value': 569}]
2025-05-18 09:00:45,014 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-18 09:00:45,543 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-18 09:00:45,543 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18298.3, 'new_value': 20727.23}, {'field': 'total_amount', 'old_value': 18363.85, 'new_value': 20792.78}, {'field': 'order_count', 'old_value': 176, 'new_value': 186}]
2025-05-18 09:00:45,543 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-05-18 09:00:46,042 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMDE
2025-05-18 09:00:46,042 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117093.0, 'new_value': 118792.0}, {'field': 'total_amount', 'old_value': 117093.0, 'new_value': 118792.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 29}]
2025-05-18 09:00:46,042 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-18 09:00:46,517 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-18 09:00:46,517 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1154222.9, 'new_value': 1227982.6}, {'field': 'total_amount', 'old_value': 1207668.0, 'new_value': 1281427.7}, {'field': 'order_count', 'old_value': 2092, 'new_value': 2253}]
2025-05-18 09:00:46,518 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-18 09:00:46,999 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-18 09:00:46,999 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34800.0, 'new_value': 38886.0}, {'field': 'total_amount', 'old_value': 63446.0, 'new_value': 67532.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 34}]
2025-05-18 09:00:46,999 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-18 09:00:47,449 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMIE
2025-05-18 09:00:47,450 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13927.0, 'new_value': 16131.0}, {'field': 'total_amount', 'old_value': 13927.0, 'new_value': 16131.0}, {'field': 'order_count', 'old_value': 240, 'new_value': 278}]
2025-05-18 09:00:47,450 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-18 09:00:47,924 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-18 09:00:47,924 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4180.85, 'new_value': 4521.85}, {'field': 'offline_amount', 'old_value': 10421.52, 'new_value': 11184.41}, {'field': 'total_amount', 'old_value': 14602.37, 'new_value': 15706.26}, {'field': 'order_count', 'old_value': 510, 'new_value': 551}]
2025-05-18 09:00:47,925 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-18 09:00:48,370 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-18 09:00:48,370 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 113334.95, 'new_value': 121814.75}, {'field': 'offline_amount', 'old_value': 92090.36, 'new_value': 98245.54}, {'field': 'total_amount', 'old_value': 205425.31, 'new_value': 220060.29}, {'field': 'order_count', 'old_value': 1786, 'new_value': 1908}]
2025-05-18 09:00:48,371 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-18 09:00:48,820 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-18 09:00:48,820 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54962.63, 'new_value': 57086.13}, {'field': 'total_amount', 'old_value': 54965.93, 'new_value': 57089.43}, {'field': 'order_count', 'old_value': 32, 'new_value': 33}]
2025-05-18 09:00:48,820 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-18 09:00:49,275 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-18 09:00:49,275 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 230084.4, 'new_value': 245136.6}, {'field': 'offline_amount', 'old_value': 53004.0, 'new_value': 56843.8}, {'field': 'total_amount', 'old_value': 283088.4, 'new_value': 301980.4}, {'field': 'order_count', 'old_value': 351, 'new_value': 382}]
2025-05-18 09:00:49,276 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-18 09:00:49,747 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-18 09:00:49,747 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20305.0, 'new_value': 20850.0}, {'field': 'total_amount', 'old_value': 20305.0, 'new_value': 20850.0}, {'field': 'order_count', 'old_value': 57, 'new_value': 60}]
2025-05-18 09:00:49,747 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-18 09:00:50,204 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-18 09:00:50,204 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54642.0, 'new_value': 55130.0}, {'field': 'total_amount', 'old_value': 54642.0, 'new_value': 55130.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 40}]
2025-05-18 09:00:50,205 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-18 09:00:50,660 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-18 09:00:50,660 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52148.0, 'new_value': 54148.0}, {'field': 'offline_amount', 'old_value': 2637.55, 'new_value': 3527.05}, {'field': 'total_amount', 'old_value': 54785.55, 'new_value': 57675.05}, {'field': 'order_count', 'old_value': 151, 'new_value': 168}]
2025-05-18 09:00:50,660 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-18 09:00:51,192 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-18 09:00:51,192 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97510.0, 'new_value': 113509.0}, {'field': 'total_amount', 'old_value': 121132.48, 'new_value': 137131.48}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-05-18 09:00:51,192 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-18 09:00:51,600 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-18 09:00:51,600 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47687.57, 'new_value': 51311.15}, {'field': 'total_amount', 'old_value': 47687.57, 'new_value': 51311.15}, {'field': 'order_count', 'old_value': 1306, 'new_value': 1413}]
2025-05-18 09:00:51,600 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-18 09:00:52,036 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-18 09:00:52,036 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 267022.83, 'new_value': 297566.72}, {'field': 'total_amount', 'old_value': 267022.83, 'new_value': 297566.72}, {'field': 'order_count', 'old_value': 398, 'new_value': 416}]
2025-05-18 09:00:52,036 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-18 09:00:52,552 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-18 09:00:52,552 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37533.0, 'new_value': 42392.0}, {'field': 'total_amount', 'old_value': 37881.0, 'new_value': 42740.0}, {'field': 'order_count', 'old_value': 76, 'new_value': 84}]
2025-05-18 09:00:52,552 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-18 09:00:52,987 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-18 09:00:52,987 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9668.97, 'new_value': 10826.23}, {'field': 'offline_amount', 'old_value': 274217.36, 'new_value': 293839.82}, {'field': 'total_amount', 'old_value': 283886.33, 'new_value': 304666.05}, {'field': 'order_count', 'old_value': 1143, 'new_value': 1236}]
2025-05-18 09:00:52,987 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-18 09:00:53,393 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-18 09:00:53,394 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 567461.0, 'new_value': 598201.0}, {'field': 'total_amount', 'old_value': 567461.0, 'new_value': 598201.0}, {'field': 'order_count', 'old_value': 99, 'new_value': 105}]
2025-05-18 09:00:53,394 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-18 09:00:53,861 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-18 09:00:53,861 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59352.0, 'new_value': 62350.0}, {'field': 'offline_amount', 'old_value': 53429.46, 'new_value': 54338.46}, {'field': 'total_amount', 'old_value': 112781.46, 'new_value': 116688.46}, {'field': 'order_count', 'old_value': 135, 'new_value': 142}]
2025-05-18 09:00:53,861 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-18 09:00:54,286 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-18 09:00:54,286 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 137813.14, 'new_value': 152348.03}, {'field': 'offline_amount', 'old_value': 82642.01, 'new_value': 86649.51}, {'field': 'total_amount', 'old_value': 220455.15, 'new_value': 238997.54}, {'field': 'order_count', 'old_value': 833, 'new_value': 897}]
2025-05-18 09:00:54,286 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-18 09:00:54,762 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-18 09:00:54,762 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7993.0, 'new_value': 8092.0}, {'field': 'total_amount', 'old_value': 7993.0, 'new_value': 8092.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-05-18 09:00:54,762 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-18 09:00:55,222 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-18 09:00:55,222 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13375.35, 'new_value': 14650.52}, {'field': 'offline_amount', 'old_value': 180972.21, 'new_value': 199569.0}, {'field': 'total_amount', 'old_value': 194347.56, 'new_value': 214219.52}, {'field': 'order_count', 'old_value': 922, 'new_value': 1022}]
2025-05-18 09:00:55,222 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPK
2025-05-18 09:00:55,671 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPK
2025-05-18 09:00:55,672 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28448.0, 'new_value': 40008.0}, {'field': 'total_amount', 'old_value': 28448.0, 'new_value': 40008.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-05-18 09:00:55,672 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-18 09:00:56,068 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-18 09:00:56,068 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22522.26, 'new_value': 24861.04}, {'field': 'offline_amount', 'old_value': 364394.45, 'new_value': 394698.73}, {'field': 'total_amount', 'old_value': 386916.71, 'new_value': 419559.77}, {'field': 'order_count', 'old_value': 2213, 'new_value': 2347}]
2025-05-18 09:00:56,069 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKU
2025-05-18 09:00:56,600 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKU
2025-05-18 09:00:56,600 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10000.0, 'new_value': 11800.0}, {'field': 'total_amount', 'old_value': 10000.0, 'new_value': 11800.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-05-18 09:00:56,600 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-18 09:00:57,035 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-18 09:00:57,035 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5723.35, 'new_value': 5994.01}, {'field': 'offline_amount', 'old_value': 22481.0, 'new_value': 23057.0}, {'field': 'total_amount', 'old_value': 28204.35, 'new_value': 29051.01}, {'field': 'order_count', 'old_value': 139, 'new_value': 148}]
2025-05-18 09:00:57,036 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-18 09:00:57,513 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-18 09:00:57,513 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3410.0, 'new_value': 3630.0}, {'field': 'offline_amount', 'old_value': 15648.9, 'new_value': 17092.1}, {'field': 'total_amount', 'old_value': 19058.9, 'new_value': 20722.1}, {'field': 'order_count', 'old_value': 771, 'new_value': 837}]
2025-05-18 09:00:57,513 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-18 09:00:57,986 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-18 09:00:57,987 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67116.82, 'new_value': 71396.12}, {'field': 'total_amount', 'old_value': 67116.82, 'new_value': 71396.12}, {'field': 'order_count', 'old_value': 216, 'new_value': 240}]
2025-05-18 09:00:57,987 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9L
2025-05-18 09:00:58,419 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9L
2025-05-18 09:00:58,419 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52484.0, 'new_value': 58747.0}, {'field': 'total_amount', 'old_value': 52484.0, 'new_value': 58747.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 22}]
2025-05-18 09:00:58,419 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-18 09:00:58,870 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-18 09:00:58,870 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 134655.4, 'new_value': 144579.4}, {'field': 'total_amount', 'old_value': 134655.4, 'new_value': 144579.4}, {'field': 'order_count', 'old_value': 487, 'new_value': 528}]
2025-05-18 09:00:58,870 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-18 09:00:59,458 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-18 09:00:59,458 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 212175.0, 'new_value': 224777.0}, {'field': 'total_amount', 'old_value': 212175.0, 'new_value': 224777.0}, {'field': 'order_count', 'old_value': 179, 'new_value': 189}]
2025-05-18 09:00:59,458 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-18 09:00:59,861 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-18 09:00:59,861 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 210076.3, 'new_value': 226883.4}, {'field': 'total_amount', 'old_value': 210076.3, 'new_value': 226883.4}, {'field': 'order_count', 'old_value': 2314, 'new_value': 2505}]
2025-05-18 09:00:59,861 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-18 09:01:00,328 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-18 09:01:00,328 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98005.56, 'new_value': 105174.94}, {'field': 'total_amount', 'old_value': 98005.56, 'new_value': 105174.94}, {'field': 'order_count', 'old_value': 478, 'new_value': 514}]
2025-05-18 09:01:00,328 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-18 09:01:00,789 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-18 09:01:00,789 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14987.57, 'new_value': 15786.82}, {'field': 'offline_amount', 'old_value': 27418.02, 'new_value': 28654.02}, {'field': 'total_amount', 'old_value': 42405.59, 'new_value': 44440.84}, {'field': 'order_count', 'old_value': 1523, 'new_value': 1594}]
2025-05-18 09:01:00,789 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-18 09:01:01,262 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-18 09:01:01,262 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44353.0, 'new_value': 46969.0}, {'field': 'total_amount', 'old_value': 46761.0, 'new_value': 49377.0}, {'field': 'order_count', 'old_value': 195, 'new_value': 206}]
2025-05-18 09:01:01,262 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-18 09:01:01,846 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-18 09:01:01,846 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15293.7, 'new_value': 16533.7}, {'field': 'offline_amount', 'old_value': 37153.34, 'new_value': 39815.34}, {'field': 'total_amount', 'old_value': 52447.04, 'new_value': 56349.04}, {'field': 'order_count', 'old_value': 602, 'new_value': 648}]
2025-05-18 09:01:01,846 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-18 09:01:02,290 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-18 09:01:02,291 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 176277.9, 'new_value': 187447.2}, {'field': 'total_amount', 'old_value': 176277.9, 'new_value': 187447.2}, {'field': 'order_count', 'old_value': 298, 'new_value': 318}]
2025-05-18 09:01:02,291 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-18 09:01:02,763 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-18 09:01:02,763 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 341311.84, 'new_value': 371950.83}, {'field': 'total_amount', 'old_value': 405896.0, 'new_value': 436534.99}, {'field': 'order_count', 'old_value': 1844, 'new_value': 1944}]
2025-05-18 09:01:02,763 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTU
2025-05-18 09:01:03,185 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTU
2025-05-18 09:01:03,185 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64601.0, 'new_value': 96129.0}, {'field': 'total_amount', 'old_value': 64601.0, 'new_value': 96129.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 18}]
2025-05-18 09:01:03,185 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-18 09:01:03,541 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-18 09:01:03,541 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 131418.0, 'new_value': 139015.0}, {'field': 'total_amount', 'old_value': 131418.0, 'new_value': 139015.0}, {'field': 'order_count', 'old_value': 89, 'new_value': 93}]
2025-05-18 09:01:03,542 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-18 09:01:04,002 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-18 09:01:04,002 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 60572.9, 'new_value': 65746.3}, {'field': 'offline_amount', 'old_value': 83184.75, 'new_value': 90574.17}, {'field': 'total_amount', 'old_value': 143757.65, 'new_value': 156320.47}, {'field': 'order_count', 'old_value': 956, 'new_value': 1042}]
2025-05-18 09:01:04,002 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-18 09:01:04,551 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-18 09:01:04,551 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 70603.19, 'new_value': 75797.19}, {'field': 'total_amount', 'old_value': 76214.71, 'new_value': 81408.71}, {'field': 'order_count', 'old_value': 6750, 'new_value': 7223}]
2025-05-18 09:01:04,551 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-18 09:01:05,004 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-18 09:01:05,004 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12724.53, 'new_value': 13662.04}, {'field': 'offline_amount', 'old_value': 25891.68, 'new_value': 27777.46}, {'field': 'total_amount', 'old_value': 38616.21, 'new_value': 41439.5}, {'field': 'order_count', 'old_value': 2076, 'new_value': 2213}]
2025-05-18 09:01:05,004 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-18 09:01:05,520 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-18 09:01:05,520 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39099.0, 'new_value': 39253.0}, {'field': 'total_amount', 'old_value': 39448.0, 'new_value': 39602.0}, {'field': 'order_count', 'old_value': 67, 'new_value': 68}]
2025-05-18 09:01:05,520 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-18 09:01:06,052 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-18 09:01:06,053 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53816.0, 'new_value': 58042.0}, {'field': 'total_amount', 'old_value': 53816.0, 'new_value': 58042.0}, {'field': 'order_count', 'old_value': 68, 'new_value': 74}]
2025-05-18 09:01:06,053 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYU
2025-05-18 09:01:06,515 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYU
2025-05-18 09:01:06,515 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23571.0, 'new_value': 28337.0}, {'field': 'total_amount', 'old_value': 23571.0, 'new_value': 28337.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-05-18 09:01:06,515 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-18 09:01:07,095 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-18 09:01:07,095 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77557.61, 'new_value': 84712.91}, {'field': 'total_amount', 'old_value': 77557.61, 'new_value': 84712.91}, {'field': 'order_count', 'old_value': 2247, 'new_value': 2463}]
2025-05-18 09:01:07,095 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-18 09:01:07,548 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-18 09:01:07,548 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 76179.23, 'new_value': 83465.17}, {'field': 'offline_amount', 'old_value': 180741.79, 'new_value': 192614.87}, {'field': 'total_amount', 'old_value': 256921.02, 'new_value': 276080.04}, {'field': 'order_count', 'old_value': 3028, 'new_value': 3222}]
2025-05-18 09:01:07,549 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO
2025-05-18 09:01:08,008 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO
2025-05-18 09:01:08,008 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19207.58, 'new_value': 19823.18}, {'field': 'total_amount', 'old_value': 19207.58, 'new_value': 19823.18}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-05-18 09:01:08,008 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-18 09:01:08,469 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-18 09:01:08,469 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 257175.66, 'new_value': 277669.53}, {'field': 'total_amount', 'old_value': 257175.66, 'new_value': 277669.53}, {'field': 'order_count', 'old_value': 1240, 'new_value': 1333}]
2025-05-18 09:01:08,470 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-18 09:01:08,847 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-18 09:01:08,847 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 139956.0, 'new_value': 149952.0}, {'field': 'total_amount', 'old_value': 139956.0, 'new_value': 149952.0}, {'field': 'order_count', 'old_value': 11663, 'new_value': 12496}]
2025-05-18 09:01:08,847 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCO
2025-05-18 09:01:09,289 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCO
2025-05-18 09:01:09,290 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8436.0, 'new_value': 9326.0}, {'field': 'total_amount', 'old_value': 8436.0, 'new_value': 9326.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-05-18 09:01:09,290 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-18 09:01:09,710 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-18 09:01:09,710 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31577.4, 'new_value': 33144.4}, {'field': 'total_amount', 'old_value': 31577.4, 'new_value': 33144.4}, {'field': 'order_count', 'old_value': 46, 'new_value': 48}]
2025-05-18 09:01:09,710 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-18 09:01:10,084 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-18 09:01:10,084 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29403.5, 'new_value': 31770.55}, {'field': 'total_amount', 'old_value': 29403.5, 'new_value': 31770.55}, {'field': 'order_count', 'old_value': 1304, 'new_value': 1401}]
2025-05-18 09:01:10,084 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-18 09:01:10,516 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-18 09:01:10,516 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84799.95, 'new_value': 104396.45}, {'field': 'total_amount', 'old_value': 157525.65, 'new_value': 177122.15}, {'field': 'order_count', 'old_value': 4153, 'new_value': 4547}]
2025-05-18 09:01:10,516 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-18 09:01:10,960 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-18 09:01:10,961 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 48426.92, 'new_value': 51634.22}, {'field': 'offline_amount', 'old_value': 163723.45, 'new_value': 180338.52}, {'field': 'total_amount', 'old_value': 212150.37, 'new_value': 231972.74}, {'field': 'order_count', 'old_value': 2790, 'new_value': 3011}]
2025-05-18 09:01:10,961 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-18 09:01:11,470 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-18 09:01:11,470 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48364.2, 'new_value': 50820.0}, {'field': 'total_amount', 'old_value': 48364.2, 'new_value': 50820.0}, {'field': 'order_count', 'old_value': 130, 'new_value': 134}]
2025-05-18 09:01:11,471 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-18 09:01:11,953 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-18 09:01:11,953 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11067.18, 'new_value': 14645.18}, {'field': 'total_amount', 'old_value': 11067.18, 'new_value': 14645.18}, {'field': 'order_count', 'old_value': 103, 'new_value': 119}]
2025-05-18 09:01:11,953 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-18 09:01:12,435 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-18 09:01:12,435 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 494907.7, 'new_value': 517381.0}, {'field': 'total_amount', 'old_value': 494907.7, 'new_value': 517381.0}, {'field': 'order_count', 'old_value': 1298, 'new_value': 1356}]
2025-05-18 09:01:12,436 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-18 09:01:12,865 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-18 09:01:12,866 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 260873.76, 'new_value': 286243.63}, {'field': 'total_amount', 'old_value': 260873.76, 'new_value': 286243.63}, {'field': 'order_count', 'old_value': 938, 'new_value': 1030}]
2025-05-18 09:01:12,866 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-18 09:01:13,332 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-18 09:01:13,333 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25047.67, 'new_value': 26826.52}, {'field': 'total_amount', 'old_value': 25047.67, 'new_value': 26826.52}, {'field': 'order_count', 'old_value': 3204, 'new_value': 3429}]
2025-05-18 09:01:13,333 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-18 09:01:13,777 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-18 09:01:13,777 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 477213.0, 'new_value': 484159.0}, {'field': 'total_amount', 'old_value': 477213.0, 'new_value': 484159.0}, {'field': 'order_count', 'old_value': 64, 'new_value': 67}]
2025-05-18 09:01:13,777 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-18 09:01:14,162 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-18 09:01:14,162 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16650.45, 'new_value': 17706.94}, {'field': 'offline_amount', 'old_value': 20909.77, 'new_value': 22828.34}, {'field': 'total_amount', 'old_value': 37560.22, 'new_value': 40535.28}, {'field': 'order_count', 'old_value': 1673, 'new_value': 1812}]
2025-05-18 09:01:14,162 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-18 09:01:14,622 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-18 09:01:14,622 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51694.0, 'new_value': 54046.0}, {'field': 'total_amount', 'old_value': 56895.0, 'new_value': 59247.0}, {'field': 'order_count', 'old_value': 163, 'new_value': 170}]
2025-05-18 09:01:14,623 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-18 09:01:15,120 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-18 09:01:15,120 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 207766.6, 'new_value': 208549.6}, {'field': 'total_amount', 'old_value': 207766.6, 'new_value': 208549.6}, {'field': 'order_count', 'old_value': 46, 'new_value': 51}]
2025-05-18 09:01:15,121 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-18 09:01:15,576 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-18 09:01:15,576 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 298234.88, 'new_value': 320768.2}, {'field': 'total_amount', 'old_value': 298234.88, 'new_value': 320768.2}, {'field': 'order_count', 'old_value': 1499, 'new_value': 1613}]
2025-05-18 09:01:15,577 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-18 09:01:16,029 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-18 09:01:16,029 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 403322.0, 'new_value': 419562.0}, {'field': 'total_amount', 'old_value': 403762.0, 'new_value': 420002.0}, {'field': 'order_count', 'old_value': 175, 'new_value': 189}]
2025-05-18 09:01:16,029 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-18 09:01:16,480 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-18 09:01:16,480 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 125016.0, 'new_value': 126715.0}, {'field': 'total_amount', 'old_value': 125016.0, 'new_value': 126715.0}, {'field': 'order_count', 'old_value': 72, 'new_value': 73}]
2025-05-18 09:01:16,481 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-18 09:01:16,965 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-18 09:01:16,965 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 128185.0, 'new_value': 138288.0}, {'field': 'total_amount', 'old_value': 128185.0, 'new_value': 138288.0}, {'field': 'order_count', 'old_value': 203, 'new_value': 214}]
2025-05-18 09:01:16,965 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-18 09:01:17,389 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-18 09:01:17,389 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109270.0, 'new_value': 118130.0}, {'field': 'total_amount', 'old_value': 109271.0, 'new_value': 118131.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-05-18 09:01:17,390 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO
2025-05-18 09:01:17,834 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO
2025-05-18 09:01:17,834 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 854412.0, 'new_value': 893330.0}, {'field': 'total_amount', 'old_value': 854412.0, 'new_value': 893330.0}, {'field': 'order_count', 'old_value': 100, 'new_value': 105}]
2025-05-18 09:01:17,834 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-18 09:01:18,292 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-18 09:01:18,293 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32274.44, 'new_value': 32663.04}, {'field': 'offline_amount', 'old_value': 31996.39, 'new_value': 36910.38}, {'field': 'total_amount', 'old_value': 64270.83, 'new_value': 69573.42}, {'field': 'order_count', 'old_value': 217, 'new_value': 229}]
2025-05-18 09:01:18,293 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-18 09:01:18,809 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-18 09:01:18,809 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 169712.0, 'new_value': 186119.0}, {'field': 'total_amount', 'old_value': 169712.0, 'new_value': 186119.0}, {'field': 'order_count', 'old_value': 40, 'new_value': 43}]
2025-05-18 09:01:18,810 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUO
2025-05-18 09:01:19,249 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUO
2025-05-18 09:01:19,249 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46800.0, 'new_value': 54000.0}, {'field': 'total_amount', 'old_value': 46800.0, 'new_value': 54000.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 8}]
2025-05-18 09:01:19,249 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-18 09:01:19,668 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-18 09:01:19,668 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 211875.32, 'new_value': 226018.52}, {'field': 'offline_amount', 'old_value': 6044.5, 'new_value': 6122.5}, {'field': 'total_amount', 'old_value': 217919.82, 'new_value': 232141.02}, {'field': 'order_count', 'old_value': 1767, 'new_value': 1948}]
2025-05-18 09:01:19,668 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-18 09:01:20,124 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-18 09:01:20,124 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14119.0, 'new_value': 15084.0}, {'field': 'total_amount', 'old_value': 14119.0, 'new_value': 15084.0}, {'field': 'order_count', 'old_value': 71, 'new_value': 77}]
2025-05-18 09:01:20,124 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-18 09:01:20,662 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-18 09:01:20,663 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12629.5, 'new_value': 17526.4}, {'field': 'total_amount', 'old_value': 12629.5, 'new_value': 17526.4}, {'field': 'order_count', 'old_value': 449, 'new_value': 487}]
2025-05-18 09:01:20,663 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-18 09:01:21,112 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-18 09:01:21,113 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5346.1, 'new_value': 5629.1}, {'field': 'offline_amount', 'old_value': 23273.8, 'new_value': 26977.1}, {'field': 'total_amount', 'old_value': 28619.9, 'new_value': 32606.2}, {'field': 'order_count', 'old_value': 315, 'new_value': 360}]
2025-05-18 09:01:21,113 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-18 09:01:21,540 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-18 09:01:21,540 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9245.0, 'new_value': 11078.0}, {'field': 'total_amount', 'old_value': 23503.0, 'new_value': 25336.0}, {'field': 'order_count', 'old_value': 63, 'new_value': 71}]
2025-05-18 09:01:21,540 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-18 09:01:21,990 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-18 09:01:21,990 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19478.73, 'new_value': 21249.55}, {'field': 'total_amount', 'old_value': 19478.73, 'new_value': 21249.55}, {'field': 'order_count', 'old_value': 736, 'new_value': 788}]
2025-05-18 09:01:21,990 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-18 09:01:22,419 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-18 09:01:22,419 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 475244.0, 'new_value': 518413.0}, {'field': 'total_amount', 'old_value': 475244.0, 'new_value': 518413.0}, {'field': 'order_count', 'old_value': 119, 'new_value': 125}]
2025-05-18 09:01:22,419 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-18 09:01:22,840 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-18 09:01:22,840 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 572017.0, 'new_value': 619478.0}, {'field': 'total_amount', 'old_value': 572017.0, 'new_value': 619478.0}, {'field': 'order_count', 'old_value': 2587, 'new_value': 2772}]
2025-05-18 09:01:22,840 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-18 09:01:23,348 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-18 09:01:23,348 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13687.0, 'new_value': 14686.0}, {'field': 'total_amount', 'old_value': 13687.0, 'new_value': 14686.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-18 09:01:23,349 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-18 09:01:23,826 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-18 09:01:23,827 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54998.0, 'new_value': 58508.0}, {'field': 'total_amount', 'old_value': 54998.0, 'new_value': 58508.0}, {'field': 'order_count', 'old_value': 140, 'new_value': 148}]
2025-05-18 09:01:23,827 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-18 09:01:24,251 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLV
2025-05-18 09:01:24,251 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8096.7, 'new_value': 8297.4}, {'field': 'total_amount', 'old_value': 18596.7, 'new_value': 18797.4}, {'field': 'order_count', 'old_value': 127, 'new_value': 130}]
2025-05-18 09:01:24,252 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-18 09:01:24,659 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-18 09:01:24,659 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94447.5, 'new_value': 96665.0}, {'field': 'total_amount', 'old_value': 94447.5, 'new_value': 96665.0}, {'field': 'order_count', 'old_value': 176, 'new_value': 182}]
2025-05-18 09:01:24,660 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-18 09:01:25,057 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-18 09:01:25,057 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 60614.62, 'new_value': 64879.47}, {'field': 'offline_amount', 'old_value': 71697.04, 'new_value': 76977.04}, {'field': 'total_amount', 'old_value': 132311.66, 'new_value': 141856.51}, {'field': 'order_count', 'old_value': 5338, 'new_value': 5716}]
2025-05-18 09:01:25,057 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-18 09:01:25,455 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-18 09:01:25,456 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38181.0, 'new_value': 42168.0}, {'field': 'total_amount', 'old_value': 38181.0, 'new_value': 42168.0}, {'field': 'order_count', 'old_value': 79, 'new_value': 87}]
2025-05-18 09:01:25,456 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-18 09:01:25,804 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-18 09:01:25,804 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 70986.0, 'new_value': 78593.0}, {'field': 'offline_amount', 'old_value': 52922.66, 'new_value': 56643.96}, {'field': 'total_amount', 'old_value': 123908.66, 'new_value': 135236.96}, {'field': 'order_count', 'old_value': 818, 'new_value': 898}]
2025-05-18 09:01:25,804 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-18 09:01:26,240 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-18 09:01:26,240 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 244077.2, 'new_value': 260510.2}, {'field': 'total_amount', 'old_value': 244077.2, 'new_value': 260510.2}, {'field': 'order_count', 'old_value': 302, 'new_value': 321}]
2025-05-18 09:01:26,241 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-18 09:01:26,693 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-18 09:01:26,694 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 605174.0, 'new_value': 652405.0}, {'field': 'total_amount', 'old_value': 605174.0, 'new_value': 652405.0}, {'field': 'order_count', 'old_value': 713, 'new_value': 754}]
2025-05-18 09:01:26,694 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-18 09:01:27,165 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-18 09:01:27,166 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 426617.0, 'new_value': 453114.0}, {'field': 'total_amount', 'old_value': 426617.0, 'new_value': 453114.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 51}]
2025-05-18 09:01:27,166 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-18 09:01:27,595 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-18 09:01:27,595 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 224058.11, 'new_value': 239013.34}, {'field': 'offline_amount', 'old_value': 818088.59, 'new_value': 896007.1}, {'field': 'total_amount', 'old_value': 1042146.7, 'new_value': 1135020.44}, {'field': 'order_count', 'old_value': 5273, 'new_value': 5694}]
2025-05-18 09:01:27,596 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9P
2025-05-18 09:01:28,027 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9P
2025-05-18 09:01:28,028 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23545.0, 'new_value': 27275.0}, {'field': 'total_amount', 'old_value': 23545.0, 'new_value': 27275.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 42}]
2025-05-18 09:01:28,028 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-18 09:01:28,478 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-18 09:01:28,478 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37259.45, 'new_value': 40554.45}, {'field': 'offline_amount', 'old_value': 30962.33, 'new_value': 33131.33}, {'field': 'total_amount', 'old_value': 68221.78, 'new_value': 73685.78}, {'field': 'order_count', 'old_value': 1367, 'new_value': 1460}]
2025-05-18 09:01:28,479 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-18 09:01:28,943 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-18 09:01:28,943 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 85741.9, 'new_value': 92191.5}, {'field': 'offline_amount', 'old_value': 72299.4, 'new_value': 77545.9}, {'field': 'total_amount', 'old_value': 158041.3, 'new_value': 169737.4}, {'field': 'order_count', 'old_value': 3717, 'new_value': 4006}]
2025-05-18 09:01:28,943 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-18 09:01:29,486 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMNE
2025-05-18 09:01:29,486 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1580000.0, 'new_value': 1680000.0}, {'field': 'total_amount', 'old_value': 1580000.0, 'new_value': 1680000.0}, {'field': 'order_count', 'old_value': 273, 'new_value': 274}]
2025-05-18 09:01:29,486 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-18 09:01:29,956 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-18 09:01:29,957 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 167666.45, 'new_value': 186584.55}, {'field': 'total_amount', 'old_value': 167666.45, 'new_value': 186584.55}, {'field': 'order_count', 'old_value': 1039, 'new_value': 1140}]
2025-05-18 09:01:29,957 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-18 09:01:30,351 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-18 09:01:30,351 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 140685.98, 'new_value': 153305.61}, {'field': 'offline_amount', 'old_value': 499502.98, 'new_value': 540040.59}, {'field': 'total_amount', 'old_value': 640188.96, 'new_value': 693346.2}, {'field': 'order_count', 'old_value': 3656, 'new_value': 3971}]
2025-05-18 09:01:30,351 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-18 09:01:30,825 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-18 09:01:30,825 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20793.38, 'new_value': 22593.4}, {'field': 'offline_amount', 'old_value': 228273.37, 'new_value': 249866.3}, {'field': 'total_amount', 'old_value': 249066.75, 'new_value': 272459.7}, {'field': 'order_count', 'old_value': 9011, 'new_value': 9155}]
2025-05-18 09:01:30,825 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-18 09:01:31,280 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-18 09:01:31,280 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 340234.0, 'new_value': 351521.0}, {'field': 'total_amount', 'old_value': 340234.0, 'new_value': 351521.0}, {'field': 'order_count', 'old_value': 317, 'new_value': 331}]
2025-05-18 09:01:31,280 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-18 09:01:31,729 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-18 09:01:31,729 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 168431.15, 'new_value': 182826.03}, {'field': 'offline_amount', 'old_value': 105254.44, 'new_value': 111075.16}, {'field': 'total_amount', 'old_value': 273685.59, 'new_value': 293901.19}, {'field': 'order_count', 'old_value': 2503, 'new_value': 2569}]
2025-05-18 09:01:31,730 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-18 09:01:32,193 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYE
2025-05-18 09:01:32,194 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 455000.0, 'new_value': 465000.0}, {'field': 'total_amount', 'old_value': 455000.0, 'new_value': 465000.0}, {'field': 'order_count', 'old_value': 140, 'new_value': 141}]
2025-05-18 09:01:32,194 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-18 09:01:32,666 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZE
2025-05-18 09:01:32,666 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 435000.0, 'new_value': 445000.0}, {'field': 'total_amount', 'old_value': 435000.0, 'new_value': 445000.0}, {'field': 'order_count', 'old_value': 139, 'new_value': 140}]
2025-05-18 09:01:32,666 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-18 09:01:33,111 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0F
2025-05-18 09:01:33,111 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2698674.0, 'new_value': 2798674.0}, {'field': 'total_amount', 'old_value': 2698674.0, 'new_value': 2798674.0}, {'field': 'order_count', 'old_value': 293, 'new_value': 294}]
2025-05-18 09:01:33,111 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-18 09:01:33,627 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-18 09:01:33,627 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57659.0, 'new_value': 61794.0}, {'field': 'offline_amount', 'old_value': 693838.0, 'new_value': 742103.0}, {'field': 'total_amount', 'old_value': 751497.0, 'new_value': 803897.0}, {'field': 'order_count', 'old_value': 18177, 'new_value': 19490}]
2025-05-18 09:01:33,628 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-18 09:01:34,048 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-18 09:01:34,049 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 265403.04, 'new_value': 288114.11}, {'field': 'total_amount', 'old_value': 265403.04, 'new_value': 288114.11}, {'field': 'order_count', 'old_value': 875, 'new_value': 950}]
2025-05-18 09:01:34,049 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-18 09:01:34,489 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-18 09:01:34,489 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24303.0, 'new_value': 25363.0}, {'field': 'offline_amount', 'old_value': 129210.0, 'new_value': 143161.0}, {'field': 'total_amount', 'old_value': 153513.0, 'new_value': 168524.0}, {'field': 'order_count', 'old_value': 158, 'new_value': 171}]
2025-05-18 09:01:34,489 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-18 09:01:34,899 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-18 09:01:34,899 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24272.0, 'new_value': 28368.0}, {'field': 'total_amount', 'old_value': 24272.0, 'new_value': 28368.0}, {'field': 'order_count', 'old_value': 37, 'new_value': 40}]
2025-05-18 09:01:34,900 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-18 09:01:35,413 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-18 09:01:35,413 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31581.1, 'new_value': 50397.1}, {'field': 'total_amount', 'old_value': 32009.1, 'new_value': 50825.1}, {'field': 'order_count', 'old_value': 16269, 'new_value': 16274}]
2025-05-18 09:01:35,414 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-18 09:01:35,832 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-18 09:01:35,832 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 115724.19, 'new_value': 123531.89}, {'field': 'total_amount', 'old_value': 115724.19, 'new_value': 123531.89}, {'field': 'order_count', 'old_value': 5918, 'new_value': 6350}]
2025-05-18 09:01:35,832 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-18 09:01:36,313 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-18 09:01:36,313 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104490.4, 'new_value': 113018.2}, {'field': 'total_amount', 'old_value': 104490.4, 'new_value': 113018.2}, {'field': 'order_count', 'old_value': 482, 'new_value': 520}]
2025-05-18 09:01:36,313 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-18 09:01:36,785 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-18 09:01:36,785 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98108.9, 'new_value': 103870.0}, {'field': 'total_amount', 'old_value': 98108.9, 'new_value': 103870.0}, {'field': 'order_count', 'old_value': 2702, 'new_value': 2865}]
2025-05-18 09:01:36,785 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-18 09:01:37,194 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-18 09:01:37,194 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1388971.0, 'new_value': 1461663.0}, {'field': 'total_amount', 'old_value': 1388971.0, 'new_value': 1461663.0}, {'field': 'order_count', 'old_value': 5288, 'new_value': 5592}]
2025-05-18 09:01:37,194 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-18 09:01:37,735 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-18 09:01:37,735 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 69384.3, 'new_value': 74669.7}, {'field': 'total_amount', 'old_value': 69384.3, 'new_value': 74669.7}, {'field': 'order_count', 'old_value': 333, 'new_value': 358}]
2025-05-18 09:01:37,735 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-18 09:01:38,129 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-18 09:01:38,129 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 221485.92, 'new_value': 234529.66}, {'field': 'total_amount', 'old_value': 221485.92, 'new_value': 234529.66}, {'field': 'order_count', 'old_value': 1168, 'new_value': 1238}]
2025-05-18 09:01:38,129 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-18 09:01:38,583 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-18 09:01:38,583 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34191.07, 'new_value': 38091.54}, {'field': 'offline_amount', 'old_value': 31760.79, 'new_value': 33906.74}, {'field': 'total_amount', 'old_value': 65951.86, 'new_value': 71998.28}, {'field': 'order_count', 'old_value': 5275, 'new_value': 5870}]
2025-05-18 09:01:38,583 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-18 09:01:39,005 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-18 09:01:39,006 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 252569.2, 'new_value': 253766.1}, {'field': 'total_amount', 'old_value': 252569.2, 'new_value': 253766.1}]
2025-05-18 09:01:39,006 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-18 09:01:39,388 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-18 09:01:39,388 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60860.56, 'new_value': 66477.56}, {'field': 'total_amount', 'old_value': 60860.56, 'new_value': 66477.56}, {'field': 'order_count', 'old_value': 3024, 'new_value': 3367}]
2025-05-18 09:01:39,388 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-18 09:01:39,841 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-18 09:01:39,841 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60772.97, 'new_value': 64751.83}, {'field': 'total_amount', 'old_value': 62033.4, 'new_value': 66012.26}, {'field': 'order_count', 'old_value': 286, 'new_value': 300}]
2025-05-18 09:01:39,841 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-18 09:01:40,247 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-18 09:01:40,248 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 397777.0, 'new_value': 412854.0}, {'field': 'total_amount', 'old_value': 397777.0, 'new_value': 412854.0}, {'field': 'order_count', 'old_value': 55, 'new_value': 58}]
2025-05-18 09:01:40,248 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-18 09:01:40,653 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-18 09:01:40,653 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 439198.0, 'new_value': 467284.0}, {'field': 'total_amount', 'old_value': 439198.0, 'new_value': 467284.0}, {'field': 'order_count', 'old_value': 292, 'new_value': 341}]
2025-05-18 09:01:40,653 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-18 09:01:41,059 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-18 09:01:41,059 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 513491.43, 'new_value': 555433.23}, {'field': 'total_amount', 'old_value': 513491.43, 'new_value': 555433.23}, {'field': 'order_count', 'old_value': 4026, 'new_value': 4343}]
2025-05-18 09:01:41,059 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-18 09:01:41,420 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-18 09:01:41,420 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4539.0, 'new_value': 5239.0}, {'field': 'offline_amount', 'old_value': 2842.0, 'new_value': 3372.0}, {'field': 'total_amount', 'old_value': 7381.0, 'new_value': 8611.0}, {'field': 'order_count', 'old_value': 70, 'new_value': 82}]
2025-05-18 09:01:41,420 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-18 09:01:41,794 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-18 09:01:41,794 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36273.67, 'new_value': 37972.77}, {'field': 'offline_amount', 'old_value': 32346.57, 'new_value': 34182.32}, {'field': 'total_amount', 'old_value': 68620.24, 'new_value': 72155.09}, {'field': 'order_count', 'old_value': 3556, 'new_value': 3721}]
2025-05-18 09:01:41,794 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-18 09:01:42,258 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-18 09:01:42,258 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 66220.95, 'new_value': 69847.83}, {'field': 'offline_amount', 'old_value': 72782.59, 'new_value': 77648.66}, {'field': 'total_amount', 'old_value': 139003.54, 'new_value': 147496.49}, {'field': 'order_count', 'old_value': 3487, 'new_value': 3700}]
2025-05-18 09:01:42,259 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-18 09:01:42,717 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-18 09:01:42,717 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 648404.0, 'new_value': 691093.0}, {'field': 'total_amount', 'old_value': 648404.0, 'new_value': 691093.0}, {'field': 'order_count', 'old_value': 749, 'new_value': 801}]
2025-05-18 09:01:42,717 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-18 09:01:43,161 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-18 09:01:43,161 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 130244.6, 'new_value': 139904.6}, {'field': 'total_amount', 'old_value': 136194.9, 'new_value': 145854.9}, {'field': 'order_count', 'old_value': 262, 'new_value': 281}]
2025-05-18 09:01:43,161 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6Q
2025-05-18 09:01:43,603 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6Q
2025-05-18 09:01:43,603 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72390.0, 'new_value': 80120.0}, {'field': 'total_amount', 'old_value': 72390.0, 'new_value': 80120.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 40}]
2025-05-18 09:01:43,603 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-18 09:01:44,093 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-18 09:01:44,093 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28260.15, 'new_value': 30236.55}, {'field': 'offline_amount', 'old_value': 71052.0, 'new_value': 78477.0}, {'field': 'total_amount', 'old_value': 99312.15, 'new_value': 108713.55}, {'field': 'order_count', 'old_value': 1103, 'new_value': 1223}]
2025-05-18 09:01:44,093 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-18 09:01:44,512 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-18 09:01:44,512 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 86446.0, 'new_value': 93584.0}, {'field': 'offline_amount', 'old_value': 62198.0, 'new_value': 68040.0}, {'field': 'total_amount', 'old_value': 148644.0, 'new_value': 161624.0}, {'field': 'order_count', 'old_value': 1870, 'new_value': 2033}]
2025-05-18 09:01:44,512 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-18 09:01:44,951 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-18 09:01:44,951 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6124.0, 'new_value': 6351.7}, {'field': 'offline_amount', 'old_value': 13600.15, 'new_value': 14998.95}, {'field': 'total_amount', 'old_value': 19724.15, 'new_value': 21350.65}, {'field': 'order_count', 'old_value': 206, 'new_value': 227}]
2025-05-18 09:01:44,951 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-18 09:01:45,350 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-18 09:01:45,351 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6500.42, 'new_value': 7008.42}, {'field': 'offline_amount', 'old_value': 105275.0, 'new_value': 107435.0}, {'field': 'total_amount', 'old_value': 111775.42, 'new_value': 114443.42}, {'field': 'order_count', 'old_value': 50, 'new_value': 52}]
2025-05-18 09:01:45,351 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-18 09:01:45,862 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-18 09:01:45,862 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20198.42, 'new_value': 23389.82}, {'field': 'total_amount', 'old_value': 43017.02, 'new_value': 46208.42}, {'field': 'order_count', 'old_value': 180, 'new_value': 194}]
2025-05-18 09:01:45,862 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-18 09:01:46,275 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-18 09:01:46,275 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 143706.5, 'new_value': 155621.5}, {'field': 'total_amount', 'old_value': 143706.5, 'new_value': 155621.5}, {'field': 'order_count', 'old_value': 713, 'new_value': 765}]
2025-05-18 09:01:46,275 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-18 09:01:46,672 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-18 09:01:46,672 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3687.0, 'new_value': 4005.0}, {'field': 'offline_amount', 'old_value': 32885.1, 'new_value': 36187.1}, {'field': 'total_amount', 'old_value': 36572.1, 'new_value': 40192.1}, {'field': 'order_count', 'old_value': 325, 'new_value': 349}]
2025-05-18 09:01:46,672 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-18 09:01:47,155 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-18 09:01:47,155 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 114215.05, 'new_value': 130140.45}, {'field': 'total_amount', 'old_value': 114215.05, 'new_value': 130140.45}, {'field': 'order_count', 'old_value': 403, 'new_value': 467}]
2025-05-18 09:01:47,155 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFQ
2025-05-18 09:01:47,603 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFQ
2025-05-18 09:01:47,603 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13156.0, 'new_value': 13754.0}, {'field': 'total_amount', 'old_value': 13156.0, 'new_value': 13754.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 23}]
2025-05-18 09:01:47,604 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-18 09:01:48,074 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-18 09:01:48,074 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 157155.0, 'new_value': 166595.0}, {'field': 'total_amount', 'old_value': 157155.0, 'new_value': 166595.0}, {'field': 'order_count', 'old_value': 329, 'new_value': 359}]
2025-05-18 09:01:48,074 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-18 09:01:48,549 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-18 09:01:48,549 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 192714.0, 'new_value': 209566.0}, {'field': 'total_amount', 'old_value': 192714.0, 'new_value': 209566.0}, {'field': 'order_count', 'old_value': 228, 'new_value': 248}]
2025-05-18 09:01:48,549 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-18 09:01:48,989 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-18 09:01:48,989 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120049.78, 'new_value': 133429.78}, {'field': 'total_amount', 'old_value': 211058.48, 'new_value': 224438.48}, {'field': 'order_count', 'old_value': 207, 'new_value': 227}]
2025-05-18 09:01:48,989 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-18 09:01:49,441 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-18 09:01:49,441 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 127581.0, 'new_value': 146350.0}, {'field': 'offline_amount', 'old_value': 101869.0, 'new_value': 119514.0}, {'field': 'total_amount', 'old_value': 229450.0, 'new_value': 265864.0}, {'field': 'order_count', 'old_value': 636, 'new_value': 707}]
2025-05-18 09:01:49,441 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-18 09:01:49,798 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-18 09:01:49,798 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 483867.14, 'new_value': 513563.79}, {'field': 'total_amount', 'old_value': 483867.14, 'new_value': 513563.79}, {'field': 'order_count', 'old_value': 2635, 'new_value': 2834}]
2025-05-18 09:01:49,798 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-18 09:01:50,274 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-18 09:01:50,274 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 91843.43, 'new_value': 100314.47}, {'field': 'total_amount', 'old_value': 91843.43, 'new_value': 100314.47}, {'field': 'order_count', 'old_value': 6283, 'new_value': 6880}]
2025-05-18 09:01:50,274 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-18 09:01:50,717 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-18 09:01:50,717 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 335262.0, 'new_value': 351998.0}, {'field': 'total_amount', 'old_value': 335262.0, 'new_value': 351998.0}, {'field': 'order_count', 'old_value': 7589, 'new_value': 7973}]
2025-05-18 09:01:50,718 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-18 09:01:51,146 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-18 09:01:51,146 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66729.0, 'new_value': 72002.0}, {'field': 'total_amount', 'old_value': 66729.0, 'new_value': 72002.0}, {'field': 'order_count', 'old_value': 4573, 'new_value': 4895}]
2025-05-18 09:01:51,146 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-18 09:01:51,571 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-18 09:01:51,571 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 90048.0, 'new_value': 95540.0}, {'field': 'total_amount', 'old_value': 90048.0, 'new_value': 95540.0}, {'field': 'order_count', 'old_value': 6670, 'new_value': 7029}]
2025-05-18 09:01:51,571 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-18 09:01:52,029 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-18 09:01:52,029 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29524.0, 'new_value': 29542.0}, {'field': 'total_amount', 'old_value': 29524.0, 'new_value': 29542.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 32}]
2025-05-18 09:01:52,030 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-18 09:01:52,501 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-18 09:01:52,502 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14535.2, 'new_value': 15307.0}, {'field': 'offline_amount', 'old_value': 37531.4, 'new_value': 41244.0}, {'field': 'total_amount', 'old_value': 52066.6, 'new_value': 56551.0}, {'field': 'order_count', 'old_value': 1942, 'new_value': 2097}]
2025-05-18 09:01:52,502 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-18 09:01:52,946 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-18 09:01:52,946 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3147849.32, 'new_value': 3445268.51}, {'field': 'total_amount', 'old_value': 3147849.32, 'new_value': 3445268.51}, {'field': 'order_count', 'old_value': 65545, 'new_value': 70969}]
2025-05-18 09:01:52,946 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-18 09:01:53,392 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-18 09:01:53,392 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 207417.0, 'new_value': 223142.0}, {'field': 'total_amount', 'old_value': 207417.0, 'new_value': 223142.0}, {'field': 'order_count', 'old_value': 4506, 'new_value': 4852}]
2025-05-18 09:01:53,393 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G
2025-05-18 09:01:53,894 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G
2025-05-18 09:01:53,894 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 11, 'new_value': 14}]
2025-05-18 09:01:53,894 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-18 09:01:54,358 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-18 09:01:54,358 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97177.0, 'new_value': 104357.0}, {'field': 'total_amount', 'old_value': 97177.0, 'new_value': 104357.0}, {'field': 'order_count', 'old_value': 379, 'new_value': 401}]
2025-05-18 09:01:54,358 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-18 09:01:54,836 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-18 09:01:54,836 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32863.0, 'new_value': 34662.0}, {'field': 'total_amount', 'old_value': 32863.0, 'new_value': 34662.0}, {'field': 'order_count', 'old_value': 635, 'new_value': 667}]
2025-05-18 09:01:54,836 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-18 09:01:55,286 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-18 09:01:55,286 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21831.0, 'new_value': 27791.0}, {'field': 'offline_amount', 'old_value': 36414.4, 'new_value': 36894.4}, {'field': 'total_amount', 'old_value': 58245.4, 'new_value': 64685.4}, {'field': 'order_count', 'old_value': 79, 'new_value': 85}]
2025-05-18 09:01:55,287 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMLX
2025-05-18 09:01:55,705 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMLX
2025-05-18 09:01:55,706 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5800.0, 'new_value': 6800.0}, {'field': 'total_amount', 'old_value': 7580.95, 'new_value': 8580.95}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-05-18 09:01:55,706 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-18 09:01:56,126 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-18 09:01:56,126 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26602.64, 'new_value': 35331.69}, {'field': 'total_amount', 'old_value': 30291.64, 'new_value': 39020.69}, {'field': 'order_count', 'old_value': 156, 'new_value': 192}]
2025-05-18 09:01:56,126 - INFO - 日期 2025-05 处理完成 - 更新: 192 条，插入: 0 条，错误: 0 条
2025-05-18 09:01:56,126 - INFO - 数据同步完成！更新: 192 条，插入: 0 条，错误: 0 条
2025-05-18 09:01:56,129 - INFO - =================同步完成====================
2025-05-18 12:00:01,998 - INFO - =================使用默认全量同步=============
2025-05-18 12:00:03,456 - INFO - MySQL查询成功，共获取 3295 条记录
2025-05-18 12:00:03,457 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-18 12:00:03,485 - INFO - 开始处理日期: 2025-01
2025-05-18 12:00:03,488 - INFO - Request Parameters - Page 1:
2025-05-18 12:00:03,489 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 12:00:03,489 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 12:00:04,866 - INFO - Response - Page 1:
2025-05-18 12:00:05,068 - INFO - 第 1 页获取到 100 条记录
2025-05-18 12:00:05,068 - INFO - Request Parameters - Page 2:
2025-05-18 12:00:05,068 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 12:00:05,068 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 12:00:05,633 - INFO - Response - Page 2:
2025-05-18 12:00:05,833 - INFO - 第 2 页获取到 100 条记录
2025-05-18 12:00:05,833 - INFO - Request Parameters - Page 3:
2025-05-18 12:00:05,833 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 12:00:05,833 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 12:00:06,478 - INFO - Response - Page 3:
2025-05-18 12:00:06,678 - INFO - 第 3 页获取到 100 条记录
2025-05-18 12:00:06,678 - INFO - Request Parameters - Page 4:
2025-05-18 12:00:06,678 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 12:00:06,678 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 12:00:07,163 - INFO - Response - Page 4:
2025-05-18 12:00:07,364 - INFO - 第 4 页获取到 100 条记录
2025-05-18 12:00:07,364 - INFO - Request Parameters - Page 5:
2025-05-18 12:00:07,364 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 12:00:07,364 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 12:00:07,883 - INFO - Response - Page 5:
2025-05-18 12:00:08,084 - INFO - 第 5 页获取到 100 条记录
2025-05-18 12:00:08,084 - INFO - Request Parameters - Page 6:
2025-05-18 12:00:08,084 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 12:00:08,084 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 12:00:08,622 - INFO - Response - Page 6:
2025-05-18 12:00:08,823 - INFO - 第 6 页获取到 100 条记录
2025-05-18 12:00:08,823 - INFO - Request Parameters - Page 7:
2025-05-18 12:00:08,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 12:00:08,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 12:00:09,355 - INFO - Response - Page 7:
2025-05-18 12:00:09,556 - INFO - 第 7 页获取到 82 条记录
2025-05-18 12:00:09,556 - INFO - 查询完成，共获取到 682 条记录
2025-05-18 12:00:09,556 - INFO - 获取到 682 条表单数据
2025-05-18 12:00:09,569 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-18 12:00:09,580 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-18 12:00:09,580 - INFO - 开始处理日期: 2025-02
2025-05-18 12:00:09,581 - INFO - Request Parameters - Page 1:
2025-05-18 12:00:09,581 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 12:00:09,581 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 12:00:10,094 - INFO - Response - Page 1:
2025-05-18 12:00:10,295 - INFO - 第 1 页获取到 100 条记录
2025-05-18 12:00:10,295 - INFO - Request Parameters - Page 2:
2025-05-18 12:00:10,295 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 12:00:10,295 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 12:00:10,812 - INFO - Response - Page 2:
2025-05-18 12:00:11,012 - INFO - 第 2 页获取到 100 条记录
2025-05-18 12:00:11,012 - INFO - Request Parameters - Page 3:
2025-05-18 12:00:11,012 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 12:00:11,012 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 12:00:11,515 - INFO - Response - Page 3:
2025-05-18 12:00:11,715 - INFO - 第 3 页获取到 100 条记录
2025-05-18 12:00:11,715 - INFO - Request Parameters - Page 4:
2025-05-18 12:00:11,715 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 12:00:11,715 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 12:00:12,182 - INFO - Response - Page 4:
2025-05-18 12:00:12,382 - INFO - 第 4 页获取到 100 条记录
2025-05-18 12:00:12,382 - INFO - Request Parameters - Page 5:
2025-05-18 12:00:12,383 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 12:00:12,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 12:00:12,851 - INFO - Response - Page 5:
2025-05-18 12:00:13,052 - INFO - 第 5 页获取到 100 条记录
2025-05-18 12:00:13,052 - INFO - Request Parameters - Page 6:
2025-05-18 12:00:13,052 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 12:00:13,053 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 12:00:13,572 - INFO - Response - Page 6:
2025-05-18 12:00:13,773 - INFO - 第 6 页获取到 100 条记录
2025-05-18 12:00:13,773 - INFO - Request Parameters - Page 7:
2025-05-18 12:00:13,773 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 12:00:13,773 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 12:00:14,188 - INFO - Response - Page 7:
2025-05-18 12:00:14,388 - INFO - 第 7 页获取到 70 条记录
2025-05-18 12:00:14,388 - INFO - 查询完成，共获取到 670 条记录
2025-05-18 12:00:14,388 - INFO - 获取到 670 条表单数据
2025-05-18 12:00:14,403 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-18 12:00:14,415 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-18 12:00:14,415 - INFO - 开始处理日期: 2025-03
2025-05-18 12:00:14,416 - INFO - Request Parameters - Page 1:
2025-05-18 12:00:14,416 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 12:00:14,416 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 12:00:14,907 - INFO - Response - Page 1:
2025-05-18 12:00:15,109 - INFO - 第 1 页获取到 100 条记录
2025-05-18 12:00:15,109 - INFO - Request Parameters - Page 2:
2025-05-18 12:00:15,109 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 12:00:15,109 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 12:00:15,595 - INFO - Response - Page 2:
2025-05-18 12:00:15,796 - INFO - 第 2 页获取到 100 条记录
2025-05-18 12:00:15,796 - INFO - Request Parameters - Page 3:
2025-05-18 12:00:15,796 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 12:00:15,796 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 12:00:16,378 - INFO - Response - Page 3:
2025-05-18 12:00:16,578 - INFO - 第 3 页获取到 100 条记录
2025-05-18 12:00:16,578 - INFO - Request Parameters - Page 4:
2025-05-18 12:00:16,578 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 12:00:16,578 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 12:00:17,119 - INFO - Response - Page 4:
2025-05-18 12:00:17,319 - INFO - 第 4 页获取到 100 条记录
2025-05-18 12:00:17,319 - INFO - Request Parameters - Page 5:
2025-05-18 12:00:17,320 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 12:00:17,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 12:00:17,842 - INFO - Response - Page 5:
2025-05-18 12:00:18,042 - INFO - 第 5 页获取到 100 条记录
2025-05-18 12:00:18,042 - INFO - Request Parameters - Page 6:
2025-05-18 12:00:18,042 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 12:00:18,042 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 12:00:18,899 - INFO - Response - Page 6:
2025-05-18 12:00:19,101 - INFO - 第 6 页获取到 100 条记录
2025-05-18 12:00:19,101 - INFO - Request Parameters - Page 7:
2025-05-18 12:00:19,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 12:00:19,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 12:00:19,702 - INFO - Response - Page 7:
2025-05-18 12:00:19,902 - INFO - 第 7 页获取到 61 条记录
2025-05-18 12:00:19,902 - INFO - 查询完成，共获取到 661 条记录
2025-05-18 12:00:19,902 - INFO - 获取到 661 条表单数据
2025-05-18 12:00:19,914 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-18 12:00:19,926 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-18 12:00:19,926 - INFO - 开始处理日期: 2025-04
2025-05-18 12:00:19,926 - INFO - Request Parameters - Page 1:
2025-05-18 12:00:19,926 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 12:00:19,927 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 12:00:20,440 - INFO - Response - Page 1:
2025-05-18 12:00:20,640 - INFO - 第 1 页获取到 100 条记录
2025-05-18 12:00:20,640 - INFO - Request Parameters - Page 2:
2025-05-18 12:00:20,640 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 12:00:20,640 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 12:00:21,128 - INFO - Response - Page 2:
2025-05-18 12:00:21,328 - INFO - 第 2 页获取到 100 条记录
2025-05-18 12:00:21,328 - INFO - Request Parameters - Page 3:
2025-05-18 12:00:21,328 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 12:00:21,328 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 12:00:21,828 - INFO - Response - Page 3:
2025-05-18 12:00:22,028 - INFO - 第 3 页获取到 100 条记录
2025-05-18 12:00:22,028 - INFO - Request Parameters - Page 4:
2025-05-18 12:00:22,028 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 12:00:22,028 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 12:00:22,538 - INFO - Response - Page 4:
2025-05-18 12:00:22,738 - INFO - 第 4 页获取到 100 条记录
2025-05-18 12:00:22,738 - INFO - Request Parameters - Page 5:
2025-05-18 12:00:22,738 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 12:00:22,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 12:00:23,221 - INFO - Response - Page 5:
2025-05-18 12:00:23,421 - INFO - 第 5 页获取到 100 条记录
2025-05-18 12:00:23,421 - INFO - Request Parameters - Page 6:
2025-05-18 12:00:23,421 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 12:00:23,421 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 12:00:23,955 - INFO - Response - Page 6:
2025-05-18 12:00:24,156 - INFO - 第 6 页获取到 100 条记录
2025-05-18 12:00:24,156 - INFO - Request Parameters - Page 7:
2025-05-18 12:00:24,156 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 12:00:24,156 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 12:00:24,513 - INFO - Response - Page 7:
2025-05-18 12:00:24,713 - INFO - 第 7 页获取到 54 条记录
2025-05-18 12:00:24,713 - INFO - 查询完成，共获取到 654 条记录
2025-05-18 12:00:24,713 - INFO - 获取到 654 条表单数据
2025-05-18 12:00:24,725 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-18 12:00:24,736 - INFO - 开始批量插入 2 条新记录
2025-05-18 12:00:24,906 - INFO - 批量插入响应状态码: 200
2025-05-18 12:00:24,907 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 18 May 2025 04:00:25 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'C296732C-FF56-71D6-B1D6-E744A3A31FD1', 'x-acs-trace-id': 'dc246481f8548993050fab9d31df46d7', 'etag': '1d68dynTT9Dq0aWVxcfkYoQ8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-18 12:00:24,907 - INFO - 批量插入响应体: {'result': ['FINST-OIF66BA14FHVTLV67N1YIBLSJNEK2BJQO4TAM6S', 'FINST-OIF66BA14FHVTLV67N1YIBLSJNEK2BJQO4TAM7S']}
2025-05-18 12:00:24,907 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-05-18 12:00:24,907 - INFO - 成功插入的数据ID: ['FINST-OIF66BA14FHVTLV67N1YIBLSJNEK2BJQO4TAM6S', 'FINST-OIF66BA14FHVTLV67N1YIBLSJNEK2BJQO4TAM7S']
2025-05-18 12:00:27,908 - INFO - 批量插入完成，共 2 条记录
2025-05-18 12:00:27,908 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-05-18 12:00:27,908 - INFO - 开始处理日期: 2025-05
2025-05-18 12:00:27,908 - INFO - Request Parameters - Page 1:
2025-05-18 12:00:27,909 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 12:00:27,909 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 12:00:28,479 - INFO - Response - Page 1:
2025-05-18 12:00:28,679 - INFO - 第 1 页获取到 100 条记录
2025-05-18 12:00:28,679 - INFO - Request Parameters - Page 2:
2025-05-18 12:00:28,679 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 12:00:28,679 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 12:00:29,132 - INFO - Response - Page 2:
2025-05-18 12:00:29,332 - INFO - 第 2 页获取到 100 条记录
2025-05-18 12:00:29,332 - INFO - Request Parameters - Page 3:
2025-05-18 12:00:29,332 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 12:00:29,332 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 12:00:29,821 - INFO - Response - Page 3:
2025-05-18 12:00:30,022 - INFO - 第 3 页获取到 100 条记录
2025-05-18 12:00:30,022 - INFO - Request Parameters - Page 4:
2025-05-18 12:00:30,022 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 12:00:30,022 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 12:00:30,516 - INFO - Response - Page 4:
2025-05-18 12:00:30,717 - INFO - 第 4 页获取到 100 条记录
2025-05-18 12:00:30,717 - INFO - Request Parameters - Page 5:
2025-05-18 12:00:30,717 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 12:00:30,717 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 12:00:31,291 - INFO - Response - Page 5:
2025-05-18 12:00:31,491 - INFO - 第 5 页获取到 100 条记录
2025-05-18 12:00:31,491 - INFO - Request Parameters - Page 6:
2025-05-18 12:00:31,491 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 12:00:31,491 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 12:00:31,956 - INFO - Response - Page 6:
2025-05-18 12:00:32,156 - INFO - 第 6 页获取到 100 条记录
2025-05-18 12:00:32,156 - INFO - Request Parameters - Page 7:
2025-05-18 12:00:32,156 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 12:00:32,156 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 12:00:32,520 - INFO - Response - Page 7:
2025-05-18 12:00:32,720 - INFO - 第 7 页获取到 25 条记录
2025-05-18 12:00:32,720 - INFO - 查询完成，共获取到 625 条记录
2025-05-18 12:00:32,720 - INFO - 获取到 625 条表单数据
2025-05-18 12:00:32,732 - INFO - 当前日期 2025-05 有 626 条MySQL数据需要处理
2025-05-18 12:00:32,732 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-18 12:00:33,189 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-18 12:00:33,189 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28620.0, 'new_value': 29680.0}, {'field': 'total_amount', 'old_value': 30210.0, 'new_value': 31270.0}, {'field': 'order_count', 'old_value': 114, 'new_value': 118}]
2025-05-18 12:00:33,189 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9C
2025-05-18 12:00:33,672 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9C
2025-05-18 12:00:33,672 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 171250.0, 'new_value': 208035.0}, {'field': 'total_amount', 'old_value': 171250.0, 'new_value': 208035.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-05-18 12:00:33,673 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-18 12:00:34,105 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-18 12:00:34,105 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 475998.98, 'new_value': 506687.98}, {'field': 'total_amount', 'old_value': 475998.98, 'new_value': 506687.98}, {'field': 'order_count', 'old_value': 1427, 'new_value': 1483}]
2025-05-18 12:00:34,105 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1LOZUQDCV8H1MR4A1E45C35KQ175AMO4
2025-05-18 12:00:34,573 - INFO - 更新表单数据成功: FINST-K7666JC1LOZUQDCV8H1MR4A1E45C35KQ175AMO4
2025-05-18 12:00:34,573 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19719.0, 'new_value': 32967.0}, {'field': 'total_amount', 'old_value': 19719.0, 'new_value': 32967.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 7}]
2025-05-18 12:00:34,573 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-18 12:00:35,016 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-18 12:00:35,017 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12347.0, 'new_value': 13203.0}, {'field': 'total_amount', 'old_value': 15244.0, 'new_value': 16100.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 19}]
2025-05-18 12:00:35,017 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM1Q
2025-05-18 12:00:35,480 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM1Q
2025-05-18 12:00:35,481 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3475.03, 'new_value': 4588.72}, {'field': 'total_amount', 'old_value': 42633.42, 'new_value': 43747.11}, {'field': 'order_count', 'old_value': 1504, 'new_value': 1538}]
2025-05-18 12:00:35,481 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-18 12:00:35,951 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-18 12:00:35,951 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19392.33, 'new_value': 20494.33}, {'field': 'offline_amount', 'old_value': 10196.03, 'new_value': 10792.03}, {'field': 'total_amount', 'old_value': 29588.36, 'new_value': 31286.36}, {'field': 'order_count', 'old_value': 1507, 'new_value': 1594}]
2025-05-18 12:00:35,952 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-18 12:00:36,405 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-18 12:00:36,405 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47178.0, 'new_value': 51228.0}, {'field': 'total_amount', 'old_value': 47178.0, 'new_value': 51228.0}, {'field': 'order_count', 'old_value': 82, 'new_value': 87}]
2025-05-18 12:00:36,406 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-18 12:00:36,799 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-18 12:00:36,799 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 269027.48, 'new_value': 284806.48}, {'field': 'total_amount', 'old_value': 269027.48, 'new_value': 284806.48}, {'field': 'order_count', 'old_value': 305, 'new_value': 324}]
2025-05-18 12:00:36,799 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM4A
2025-05-18 12:00:37,273 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM4A
2025-05-18 12:00:37,273 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2696.53, 'new_value': 2794.59}, {'field': 'offline_amount', 'old_value': 52371.65, 'new_value': 57113.81}, {'field': 'total_amount', 'old_value': 55068.18, 'new_value': 59908.4}, {'field': 'order_count', 'old_value': 2122, 'new_value': 2283}]
2025-05-18 12:00:37,273 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM2Q
2025-05-18 12:00:37,688 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM2Q
2025-05-18 12:00:37,688 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 160388.28, 'new_value': 165814.51}, {'field': 'offline_amount', 'old_value': 11424.15, 'new_value': 11976.15}, {'field': 'total_amount', 'old_value': 171812.43, 'new_value': 177790.66}, {'field': 'order_count', 'old_value': 3324, 'new_value': 3520}]
2025-05-18 12:00:37,688 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-18 12:00:38,098 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-18 12:00:38,098 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 507627.5, 'new_value': 594427.5}, {'field': 'total_amount', 'old_value': 508884.9, 'new_value': 595684.9}, {'field': 'order_count', 'old_value': 56, 'new_value': 66}]
2025-05-18 12:00:38,098 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCD
2025-05-18 12:00:38,616 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCD
2025-05-18 12:00:38,617 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19532.5, 'new_value': 19808.5}, {'field': 'total_amount', 'old_value': 19532.5, 'new_value': 19808.5}, {'field': 'order_count', 'old_value': 13, 'new_value': 15}]
2025-05-18 12:00:38,617 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-18 12:00:39,058 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-18 12:00:39,059 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 130804.62, 'new_value': 132377.5}, {'field': 'total_amount', 'old_value': 130804.62, 'new_value': 132377.5}, {'field': 'order_count', 'old_value': 159, 'new_value': 164}]
2025-05-18 12:00:39,059 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A
2025-05-18 12:00:39,437 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A
2025-05-18 12:00:39,438 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61587.83, 'new_value': 66218.17}, {'field': 'total_amount', 'old_value': 61587.83, 'new_value': 66218.17}, {'field': 'order_count', 'old_value': 2323, 'new_value': 2489}]
2025-05-18 12:00:39,438 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-18 12:00:39,862 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-18 12:00:39,863 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 60546.47, 'new_value': 71003.88}, {'field': 'offline_amount', 'old_value': 37360.26, 'new_value': 35046.58}, {'field': 'total_amount', 'old_value': 97906.73, 'new_value': 106050.46}, {'field': 'order_count', 'old_value': 5473, 'new_value': 6018}]
2025-05-18 12:00:39,863 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-18 12:00:40,276 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-18 12:00:40,276 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48566.0, 'new_value': 53560.0}, {'field': 'total_amount', 'old_value': 48566.0, 'new_value': 53560.0}, {'field': 'order_count', 'old_value': 416, 'new_value': 460}]
2025-05-18 12:00:40,277 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM7A
2025-05-18 12:00:40,713 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM7A
2025-05-18 12:00:40,714 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26502.31, 'new_value': 29210.46}, {'field': 'offline_amount', 'old_value': 32282.69, 'new_value': 35403.29}, {'field': 'total_amount', 'old_value': 58785.0, 'new_value': 64613.75}, {'field': 'order_count', 'old_value': 2953, 'new_value': 3289}]
2025-05-18 12:00:40,714 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM8A
2025-05-18 12:00:41,137 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM8A
2025-05-18 12:00:41,137 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6029.16, 'new_value': 6327.59}, {'field': 'offline_amount', 'old_value': 76687.68, 'new_value': 83014.55}, {'field': 'total_amount', 'old_value': 82716.84, 'new_value': 89342.14}, {'field': 'order_count', 'old_value': 1303, 'new_value': 1410}]
2025-05-18 12:00:41,137 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-18 12:00:41,580 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-18 12:00:41,580 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57186.0, 'new_value': 61020.0}, {'field': 'total_amount', 'old_value': 103014.0, 'new_value': 106848.0}, {'field': 'order_count', 'old_value': 37, 'new_value': 38}]
2025-05-18 12:00:41,580 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-18 12:00:42,100 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-18 12:00:42,100 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1200.0, 'new_value': 1300.0}, {'field': 'offline_amount', 'old_value': 19383.0, 'new_value': 21644.0}, {'field': 'total_amount', 'old_value': 20583.0, 'new_value': 22944.0}, {'field': 'order_count', 'old_value': 76, 'new_value': 81}]
2025-05-18 12:00:42,101 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-18 12:00:42,577 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-18 12:00:42,577 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5406.06, 'new_value': 5957.46}, {'field': 'offline_amount', 'old_value': 63777.27, 'new_value': 72936.41}, {'field': 'total_amount', 'old_value': 69183.33, 'new_value': 78893.87}, {'field': 'order_count', 'old_value': 1647, 'new_value': 1854}]
2025-05-18 12:00:42,578 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-18 12:00:43,005 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-18 12:00:43,006 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 131751.0, 'new_value': 143961.0}, {'field': 'total_amount', 'old_value': 131751.0, 'new_value': 143961.0}, {'field': 'order_count', 'old_value': 654, 'new_value': 709}]
2025-05-18 12:00:43,006 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-18 12:00:43,433 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-18 12:00:43,433 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 126654.26, 'new_value': 139906.06}, {'field': 'total_amount', 'old_value': 126654.26, 'new_value': 139906.06}, {'field': 'order_count', 'old_value': 406, 'new_value': 451}]
2025-05-18 12:00:43,433 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-18 12:00:43,985 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-18 12:00:43,985 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 55069.22, 'new_value': 59153.38}, {'field': 'offline_amount', 'old_value': 580414.83, 'new_value': 619817.83}, {'field': 'total_amount', 'old_value': 635484.05, 'new_value': 678971.21}, {'field': 'order_count', 'old_value': 2064, 'new_value': 2205}]
2025-05-18 12:00:43,986 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-18 12:00:44,388 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-18 12:00:44,389 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14500.0, 'new_value': 16955.0}, {'field': 'total_amount', 'old_value': 14500.0, 'new_value': 16955.0}, {'field': 'order_count', 'old_value': 32, 'new_value': 37}]
2025-05-18 12:00:44,389 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-18 12:00:44,906 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-18 12:00:44,906 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 454135.69, 'new_value': 485857.82}, {'field': 'total_amount', 'old_value': 454135.69, 'new_value': 485857.82}, {'field': 'order_count', 'old_value': 3161, 'new_value': 3408}]
2025-05-18 12:00:44,906 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1U
2025-05-18 12:00:45,366 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1U
2025-05-18 12:00:45,366 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58484.0, 'new_value': 62483.0}, {'field': 'total_amount', 'old_value': 58484.0, 'new_value': 62483.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-05-18 12:00:45,366 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-18 12:00:45,797 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-18 12:00:45,797 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112249.5, 'new_value': 123635.16}, {'field': 'total_amount', 'old_value': 112249.5, 'new_value': 123635.16}, {'field': 'order_count', 'old_value': 639, 'new_value': 694}]
2025-05-18 12:00:45,797 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM3Q
2025-05-18 12:00:46,276 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM3Q
2025-05-18 12:00:46,276 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 65838.58, 'new_value': 69190.96}, {'field': 'offline_amount', 'old_value': 56118.05, 'new_value': 58829.18}, {'field': 'total_amount', 'old_value': 121956.63, 'new_value': 128020.14}, {'field': 'order_count', 'old_value': 4328, 'new_value': 4541}]
2025-05-18 12:00:46,276 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-18 12:00:46,734 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-18 12:00:46,734 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4308.23, 'new_value': 4759.88}, {'field': 'offline_amount', 'old_value': 128688.55, 'new_value': 136663.05}, {'field': 'total_amount', 'old_value': 132996.78, 'new_value': 141422.93}, {'field': 'order_count', 'old_value': 872, 'new_value': 926}]
2025-05-18 12:00:46,734 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM9A
2025-05-18 12:00:47,190 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM9A
2025-05-18 12:00:47,190 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43041.98, 'new_value': 46388.95}, {'field': 'total_amount', 'old_value': 43041.98, 'new_value': 46388.95}, {'field': 'order_count', 'old_value': 1985, 'new_value': 2124}]
2025-05-18 12:00:47,190 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-18 12:00:47,691 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-18 12:00:47,692 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 109095.65, 'new_value': 126708.65}, {'field': 'offline_amount', 'old_value': 66316.0, 'new_value': 73343.0}, {'field': 'total_amount', 'old_value': 175411.65, 'new_value': 200051.65}, {'field': 'order_count', 'old_value': 886, 'new_value': 1012}]
2025-05-18 12:00:47,692 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMME
2025-05-18 12:00:48,092 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMME
2025-05-18 12:00:48,093 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15496.0, 'new_value': 29446.0}, {'field': 'total_amount', 'old_value': 15496.0, 'new_value': 29446.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 6}]
2025-05-18 12:00:48,093 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-18 12:00:48,574 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-18 12:00:48,575 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107782.0, 'new_value': 118811.0}, {'field': 'total_amount', 'old_value': 115357.8, 'new_value': 126386.8}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-05-18 12:00:48,575 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM4Q
2025-05-18 12:00:49,061 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM4Q
2025-05-18 12:00:49,061 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 127489.29, 'new_value': 136957.18}, {'field': 'total_amount', 'old_value': 134404.95, 'new_value': 143872.84}, {'field': 'order_count', 'old_value': 2746, 'new_value': 2936}]
2025-05-18 12:00:49,061 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM5Q
2025-05-18 12:00:49,456 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM5Q
2025-05-18 12:00:49,456 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 287965.39, 'new_value': 318982.55}, {'field': 'total_amount', 'old_value': 287965.39, 'new_value': 318982.55}, {'field': 'order_count', 'old_value': 2883, 'new_value': 3196}]
2025-05-18 12:00:49,457 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-18 12:00:49,835 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-18 12:00:49,836 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30157.0, 'new_value': 35565.0}, {'field': 'offline_amount', 'old_value': 122668.0, 'new_value': 126765.0}, {'field': 'total_amount', 'old_value': 152825.0, 'new_value': 162330.0}, {'field': 'order_count', 'old_value': 3255, 'new_value': 3461}]
2025-05-18 12:00:49,836 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-18 12:00:50,336 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-18 12:00:50,336 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 89291.74, 'new_value': 100309.08}, {'field': 'offline_amount', 'old_value': 80327.45, 'new_value': 88696.45}, {'field': 'total_amount', 'old_value': 169619.19, 'new_value': 189005.53}, {'field': 'order_count', 'old_value': 1698, 'new_value': 1872}]
2025-05-18 12:00:50,336 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-18 12:00:50,789 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-18 12:00:50,789 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 434907.04, 'new_value': 456477.04}, {'field': 'offline_amount', 'old_value': 144283.9, 'new_value': 171924.9}, {'field': 'total_amount', 'old_value': 579190.94, 'new_value': 628401.94}, {'field': 'order_count', 'old_value': 5482, 'new_value': 5833}]
2025-05-18 12:00:50,790 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-18 12:00:51,226 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-18 12:00:51,226 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 387411.0, 'new_value': 442911.0}, {'field': 'total_amount', 'old_value': 387411.0, 'new_value': 442911.0}, {'field': 'order_count', 'old_value': 37, 'new_value': 44}]
2025-05-18 12:00:51,226 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-18 12:00:51,657 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-18 12:00:51,658 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 80494.27, 'new_value': 85372.92}, {'field': 'offline_amount', 'old_value': 280694.21, 'new_value': 298720.79}, {'field': 'total_amount', 'old_value': 361188.48, 'new_value': 384093.71}, {'field': 'order_count', 'old_value': 2102, 'new_value': 2304}]
2025-05-18 12:00:51,658 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-18 12:00:52,084 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-18 12:00:52,084 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56005.86, 'new_value': 59144.38}, {'field': 'total_amount', 'old_value': 56005.86, 'new_value': 59144.38}, {'field': 'order_count', 'old_value': 54, 'new_value': 57}]
2025-05-18 12:00:52,084 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-18 12:00:52,534 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-18 12:00:52,534 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 54588.1, 'new_value': 57609.1}, {'field': 'offline_amount', 'old_value': 732880.34, 'new_value': 786616.52}, {'field': 'total_amount', 'old_value': 787468.44, 'new_value': 844225.62}, {'field': 'order_count', 'old_value': 6309, 'new_value': 6732}]
2025-05-18 12:00:52,534 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-18 12:00:52,952 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-18 12:00:52,952 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82046.1, 'new_value': 87124.34}, {'field': 'total_amount', 'old_value': 82046.1, 'new_value': 87124.34}, {'field': 'order_count', 'old_value': 534, 'new_value': 571}]
2025-05-18 12:00:52,953 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-18 12:00:53,395 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-18 12:00:53,395 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57880.52, 'new_value': 62796.75}, {'field': 'total_amount', 'old_value': 57880.52, 'new_value': 62796.75}, {'field': 'order_count', 'old_value': 322, 'new_value': 347}]
2025-05-18 12:00:53,395 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-18 12:00:53,977 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-18 12:00:53,978 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4268.0, 'new_value': 4558.0}, {'field': 'total_amount', 'old_value': 4268.0, 'new_value': 4558.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 26}]
2025-05-18 12:00:53,978 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-18 12:00:54,452 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-18 12:00:54,452 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31391.25, 'new_value': 34221.95}, {'field': 'total_amount', 'old_value': 31588.05, 'new_value': 34418.75}, {'field': 'order_count', 'old_value': 269, 'new_value': 288}]
2025-05-18 12:00:54,453 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-18 12:00:54,961 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-18 12:00:54,962 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16218.5, 'new_value': 19436.2}, {'field': 'offline_amount', 'old_value': 157850.8, 'new_value': 173129.7}, {'field': 'total_amount', 'old_value': 174069.3, 'new_value': 192565.9}, {'field': 'order_count', 'old_value': 1375, 'new_value': 1516}]
2025-05-18 12:00:54,962 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-18 12:00:55,466 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-18 12:00:55,466 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9714.22, 'new_value': 10810.36}, {'field': 'offline_amount', 'old_value': 168993.84, 'new_value': 185089.84}, {'field': 'total_amount', 'old_value': 178708.06, 'new_value': 195900.2}, {'field': 'order_count', 'old_value': 9794, 'new_value': 10683}]
2025-05-18 12:00:55,466 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-18 12:00:55,934 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-18 12:00:55,934 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 192243.84, 'new_value': 203762.46}, {'field': 'total_amount', 'old_value': 192243.84, 'new_value': 203762.46}, {'field': 'order_count', 'old_value': 5155, 'new_value': 5481}]
2025-05-18 12:00:55,934 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-18 12:00:56,349 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-18 12:00:56,350 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32376.82, 'new_value': 34730.49}, {'field': 'offline_amount', 'old_value': 20323.0, 'new_value': 21984.0}, {'field': 'total_amount', 'old_value': 52699.82, 'new_value': 56714.49}, {'field': 'order_count', 'old_value': 663, 'new_value': 711}]
2025-05-18 12:00:56,350 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-18 12:00:56,893 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-18 12:00:56,893 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5736.97, 'new_value': 6546.77}, {'field': 'offline_amount', 'old_value': 55781.4, 'new_value': 62817.76}, {'field': 'total_amount', 'old_value': 61518.37, 'new_value': 69364.53}, {'field': 'order_count', 'old_value': 1805, 'new_value': 1965}]
2025-05-18 12:00:56,894 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-18 12:00:57,474 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-18 12:00:57,474 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81488.2, 'new_value': 89722.1}, {'field': 'total_amount', 'old_value': 81488.2, 'new_value': 89722.1}, {'field': 'order_count', 'old_value': 834, 'new_value': 915}]
2025-05-18 12:00:57,474 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM6Q
2025-05-18 12:00:57,956 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM6Q
2025-05-18 12:00:57,956 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26689.39, 'new_value': 27534.93}, {'field': 'offline_amount', 'old_value': 150290.49, 'new_value': 165004.36}, {'field': 'total_amount', 'old_value': 176979.88, 'new_value': 192539.29}, {'field': 'order_count', 'old_value': 5454, 'new_value': 5931}]
2025-05-18 12:00:57,957 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-18 12:00:58,349 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-18 12:00:58,349 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34459.0, 'new_value': 37102.0}, {'field': 'total_amount', 'old_value': 34459.0, 'new_value': 37102.0}, {'field': 'order_count', 'old_value': 239, 'new_value': 261}]
2025-05-18 12:00:58,349 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-18 12:00:58,796 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-18 12:00:58,797 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31294.69, 'new_value': 34832.67}, {'field': 'offline_amount', 'old_value': 260063.11, 'new_value': 288087.64}, {'field': 'total_amount', 'old_value': 291357.8, 'new_value': 322920.31}, {'field': 'order_count', 'old_value': 2465, 'new_value': 2707}]
2025-05-18 12:00:58,797 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-18 12:00:59,208 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-18 12:00:59,208 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 288547.5, 'new_value': 323747.5}, {'field': 'total_amount', 'old_value': 329534.48, 'new_value': 364734.48}, {'field': 'order_count', 'old_value': 2578, 'new_value': 2831}]
2025-05-18 12:00:59,208 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-18 12:00:59,641 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1M
2025-05-18 12:00:59,641 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34791.0, 'new_value': 42321.0}, {'field': 'total_amount', 'old_value': 34791.0, 'new_value': 42321.0}, {'field': 'order_count', 'old_value': 34, 'new_value': 42}]
2025-05-18 12:00:59,641 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0V
2025-05-18 12:01:00,247 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0V
2025-05-18 12:01:00,247 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20239.0, 'new_value': 30430.2}, {'field': 'total_amount', 'old_value': 20239.0, 'new_value': 30430.2}, {'field': 'order_count', 'old_value': 11, 'new_value': 15}]
2025-05-18 12:01:00,247 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6M
2025-05-18 12:01:00,648 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6M
2025-05-18 12:01:00,648 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36580.0, 'new_value': 66580.0}, {'field': 'total_amount', 'old_value': 36580.0, 'new_value': 66580.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 7}]
2025-05-18 12:01:00,648 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMBA
2025-05-18 12:01:01,140 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMBA
2025-05-18 12:01:01,141 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24198.8, 'new_value': 26563.3}, {'field': 'offline_amount', 'old_value': 18997.71, 'new_value': 20833.64}, {'field': 'total_amount', 'old_value': 43196.51, 'new_value': 47396.94}, {'field': 'order_count', 'old_value': 6569, 'new_value': 6888}]
2025-05-18 12:01:01,141 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-18 12:01:01,716 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-18 12:01:01,717 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74921.98, 'new_value': 78392.98}, {'field': 'total_amount', 'old_value': 74921.98, 'new_value': 78392.98}, {'field': 'order_count', 'old_value': 634, 'new_value': 676}]
2025-05-18 12:01:01,717 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDM
2025-05-18 12:01:02,171 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDM
2025-05-18 12:01:02,171 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 225751.0, 'new_value': 356839.0}, {'field': 'total_amount', 'old_value': 225751.0, 'new_value': 356839.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 15}]
2025-05-18 12:01:02,171 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-18 12:01:02,646 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-18 12:01:02,646 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37950.0, 'new_value': 39498.0}, {'field': 'total_amount', 'old_value': 44874.0, 'new_value': 46422.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 32}]
2025-05-18 12:01:02,646 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMCA
2025-05-18 12:01:03,163 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMCA
2025-05-18 12:01:03,163 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29382.5, 'new_value': 31499.5}, {'field': 'total_amount', 'old_value': 29382.5, 'new_value': 31499.5}, {'field': 'order_count', 'old_value': 1473, 'new_value': 1588}]
2025-05-18 12:01:03,163 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-18 12:01:03,657 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-18 12:01:03,657 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 521.2, 'new_value': 907.2}, {'field': 'offline_amount', 'old_value': 29509.3, 'new_value': 31184.5}, {'field': 'total_amount', 'old_value': 30030.5, 'new_value': 32091.7}, {'field': 'order_count', 'old_value': 93, 'new_value': 101}]
2025-05-18 12:01:03,657 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-18 12:01:04,141 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-18 12:01:04,141 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 194801.43, 'new_value': 211165.7}, {'field': 'offline_amount', 'old_value': 11731.5, 'new_value': 12640.7}, {'field': 'total_amount', 'old_value': 206532.93, 'new_value': 223806.4}, {'field': 'order_count', 'old_value': 7706, 'new_value': 8433}]
2025-05-18 12:01:04,141 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMDA
2025-05-18 12:01:04,525 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMDA
2025-05-18 12:01:04,525 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10702.55, 'new_value': 14260.61}, {'field': 'offline_amount', 'old_value': 147837.76, 'new_value': 164065.34}, {'field': 'total_amount', 'old_value': 158540.31, 'new_value': 178325.95}, {'field': 'order_count', 'old_value': 2143, 'new_value': 2507}]
2025-05-18 12:01:04,525 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-18 12:01:04,940 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-18 12:01:04,940 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 164652.0, 'new_value': 187346.0}, {'field': 'total_amount', 'old_value': 164652.0, 'new_value': 187346.0}, {'field': 'order_count', 'old_value': 77, 'new_value': 87}]
2025-05-18 12:01:04,940 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM7Q
2025-05-18 12:01:05,553 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM7Q
2025-05-18 12:01:05,553 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63573.74, 'new_value': 68323.04}, {'field': 'total_amount', 'old_value': 63573.74, 'new_value': 68323.04}, {'field': 'order_count', 'old_value': 1789, 'new_value': 1928}]
2025-05-18 12:01:05,553 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-18 12:01:06,019 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-18 12:01:06,020 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21310.0, 'new_value': 22516.0}, {'field': 'total_amount', 'old_value': 21310.0, 'new_value': 22516.0}, {'field': 'order_count', 'old_value': 208, 'new_value': 218}]
2025-05-18 12:01:06,020 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM8Q
2025-05-18 12:01:06,498 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM8Q
2025-05-18 12:01:06,498 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117540.91, 'new_value': 123758.91}, {'field': 'total_amount', 'old_value': 117540.91, 'new_value': 123758.91}, {'field': 'order_count', 'old_value': 4944, 'new_value': 5165}]
2025-05-18 12:01:06,498 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-18 12:01:07,008 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-18 12:01:07,008 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34936.23, 'new_value': 37592.17}, {'field': 'offline_amount', 'old_value': 21934.52, 'new_value': 23291.87}, {'field': 'total_amount', 'old_value': 56870.75, 'new_value': 60884.04}, {'field': 'order_count', 'old_value': 3100, 'new_value': 3318}]
2025-05-18 12:01:07,008 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-18 12:01:07,434 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-18 12:01:07,434 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50002.0, 'new_value': 53298.0}, {'field': 'total_amount', 'old_value': 54498.0, 'new_value': 57794.0}, {'field': 'order_count', 'old_value': 32, 'new_value': 34}]
2025-05-18 12:01:07,434 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-18 12:01:07,846 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-18 12:01:07,847 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22042.0, 'new_value': 23438.0}, {'field': 'total_amount', 'old_value': 22042.0, 'new_value': 23438.0}, {'field': 'order_count', 'old_value': 201, 'new_value': 209}]
2025-05-18 12:01:07,847 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-18 12:01:08,298 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-18 12:01:08,298 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 206463.2, 'new_value': 219953.7}, {'field': 'total_amount', 'old_value': 206463.2, 'new_value': 219953.7}, {'field': 'order_count', 'old_value': 5104, 'new_value': 5428}]
2025-05-18 12:01:08,298 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM9Q
2025-05-18 12:01:08,777 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM9Q
2025-05-18 12:01:08,777 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18752.54, 'new_value': 20045.02}, {'field': 'offline_amount', 'old_value': 38321.96, 'new_value': 41611.16}, {'field': 'total_amount', 'old_value': 57074.5, 'new_value': 61656.18}, {'field': 'order_count', 'old_value': 480, 'new_value': 519}]
2025-05-18 12:01:08,777 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-18 12:01:09,287 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-18 12:01:09,287 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1082324.93, 'new_value': 1157715.19}, {'field': 'offline_amount', 'old_value': 145157.3, 'new_value': 147580.3}, {'field': 'total_amount', 'old_value': 1227482.23, 'new_value': 1305295.49}, {'field': 'order_count', 'old_value': 4238, 'new_value': 4500}]
2025-05-18 12:01:09,288 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZM
2025-05-18 12:01:09,720 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZM
2025-05-18 12:01:09,720 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64930.0, 'new_value': 76288.0}, {'field': 'total_amount', 'old_value': 64930.0, 'new_value': 76288.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 15}]
2025-05-18 12:01:09,720 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-18 12:01:10,168 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-18 12:01:10,168 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10728.6, 'new_value': 10946.2}, {'field': 'offline_amount', 'old_value': 36839.1, 'new_value': 37021.1}, {'field': 'total_amount', 'old_value': 47567.7, 'new_value': 47967.3}, {'field': 'order_count', 'old_value': 118, 'new_value': 125}]
2025-05-18 12:01:10,168 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-18 12:01:10,654 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-18 12:01:10,655 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 62685.4, 'new_value': 70360.7}, {'field': 'offline_amount', 'old_value': 91833.7, 'new_value': 104217.9}, {'field': 'total_amount', 'old_value': 154519.1, 'new_value': 174578.6}, {'field': 'order_count', 'old_value': 3111, 'new_value': 3513}]
2025-05-18 12:01:10,655 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-18 12:01:11,109 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-18 12:01:11,109 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 376925.04, 'new_value': 404150.64}, {'field': 'total_amount', 'old_value': 377452.05, 'new_value': 404677.65}, {'field': 'order_count', 'old_value': 887, 'new_value': 960}]
2025-05-18 12:01:11,109 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-18 12:01:11,530 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-18 12:01:11,530 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6222.9, 'new_value': 6616.4}, {'field': 'offline_amount', 'old_value': 19946.18, 'new_value': 21990.38}, {'field': 'total_amount', 'old_value': 26169.08, 'new_value': 28606.78}, {'field': 'order_count', 'old_value': 917, 'new_value': 1001}]
2025-05-18 12:01:11,530 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-18 12:01:12,037 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-18 12:01:12,037 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 220462.86, 'new_value': 236214.06}, {'field': 'total_amount', 'old_value': 220462.86, 'new_value': 236214.06}, {'field': 'order_count', 'old_value': 1953, 'new_value': 2136}]
2025-05-18 12:01:12,038 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-18 12:01:12,403 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-18 12:01:12,403 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 95158.75, 'new_value': 100192.78}, {'field': 'offline_amount', 'old_value': 35296.75, 'new_value': 38614.74}, {'field': 'total_amount', 'old_value': 130455.5, 'new_value': 138807.52}, {'field': 'order_count', 'old_value': 8141, 'new_value': 8603}]
2025-05-18 12:01:12,403 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-18 12:01:12,907 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-18 12:01:12,907 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 486206.89, 'new_value': 518233.42}, {'field': 'total_amount', 'old_value': 486206.89, 'new_value': 518233.42}, {'field': 'order_count', 'old_value': 3555, 'new_value': 3779}]
2025-05-18 12:01:12,908 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-18 12:01:13,500 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-18 12:01:13,500 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1080.31, 'new_value': 1208.74}, {'field': 'offline_amount', 'old_value': 17106.31, 'new_value': 18028.9}, {'field': 'total_amount', 'old_value': 18186.62, 'new_value': 19237.64}, {'field': 'order_count', 'old_value': 640, 'new_value': 677}]
2025-05-18 12:01:13,501 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-18 12:01:13,993 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-18 12:01:13,993 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4338.98, 'new_value': 4636.29}, {'field': 'offline_amount', 'old_value': 256776.04, 'new_value': 258761.44}, {'field': 'total_amount', 'old_value': 261115.02, 'new_value': 263397.73}, {'field': 'order_count', 'old_value': 11849, 'new_value': 12886}]
2025-05-18 12:01:13,993 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-18 12:01:14,449 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-18 12:01:14,449 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30046.32, 'new_value': 32211.62}, {'field': 'offline_amount', 'old_value': 204035.61, 'new_value': 224323.98}, {'field': 'total_amount', 'old_value': 234081.93, 'new_value': 256535.6}, {'field': 'order_count', 'old_value': 1449, 'new_value': 1601}]
2025-05-18 12:01:14,449 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-18 12:01:14,941 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-18 12:01:14,941 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 128466.55, 'new_value': 141853.55}, {'field': 'total_amount', 'old_value': 128466.55, 'new_value': 141853.55}, {'field': 'order_count', 'old_value': 703, 'new_value': 777}]
2025-05-18 12:01:14,941 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-18 12:01:15,330 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-18 12:01:15,331 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 58512.0, 'new_value': 60723.0}, {'field': 'total_amount', 'old_value': 58512.0, 'new_value': 60723.0}, {'field': 'order_count', 'old_value': 1734, 'new_value': 1799}]
2025-05-18 12:01:15,331 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-18 12:01:15,736 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-18 12:01:15,736 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 232797.28, 'new_value': 257657.07}, {'field': 'total_amount', 'old_value': 254960.4, 'new_value': 279820.19}, {'field': 'order_count', 'old_value': 10716, 'new_value': 11784}]
2025-05-18 12:01:15,736 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-18 12:01:16,228 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-18 12:01:16,228 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18786.45, 'new_value': 20021.02}, {'field': 'offline_amount', 'old_value': 154840.34, 'new_value': 168731.04}, {'field': 'total_amount', 'old_value': 173626.79, 'new_value': 188752.06}, {'field': 'order_count', 'old_value': 5398, 'new_value': 5883}]
2025-05-18 12:01:16,229 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-18 12:01:16,649 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-18 12:01:16,649 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17251.0, 'new_value': 17677.0}, {'field': 'total_amount', 'old_value': 17251.0, 'new_value': 17677.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-05-18 12:01:16,650 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-18 12:01:17,088 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-18 12:01:17,089 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 70532.01, 'new_value': 79386.91}, {'field': 'offline_amount', 'old_value': 251672.85, 'new_value': 271889.59}, {'field': 'total_amount', 'old_value': 322204.86, 'new_value': 351276.5}, {'field': 'order_count', 'old_value': 2529, 'new_value': 2823}]
2025-05-18 12:01:17,089 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-18 12:01:17,548 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-18 12:01:17,549 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 158162.46, 'new_value': 175251.46}, {'field': 'total_amount', 'old_value': 158162.46, 'new_value': 175251.46}, {'field': 'order_count', 'old_value': 29, 'new_value': 31}]
2025-05-18 12:01:17,549 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMAQ
2025-05-18 12:01:18,023 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMAQ
2025-05-18 12:01:18,023 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59672.0, 'new_value': 63293.0}, {'field': 'total_amount', 'old_value': 59672.0, 'new_value': 63293.0}, {'field': 'order_count', 'old_value': 1414, 'new_value': 1498}]
2025-05-18 12:01:18,023 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-18 12:01:18,448 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-18 12:01:18,448 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 447328.58, 'new_value': 480726.58}, {'field': 'total_amount', 'old_value': 447328.58, 'new_value': 480726.58}, {'field': 'order_count', 'old_value': 3470, 'new_value': 3722}]
2025-05-18 12:01:18,448 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-18 12:01:18,944 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-18 12:01:18,944 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17884.0, 'new_value': 19472.0}, {'field': 'total_amount', 'old_value': 17884.0, 'new_value': 19472.0}, {'field': 'order_count', 'old_value': 95, 'new_value': 104}]
2025-05-18 12:01:18,945 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-18 12:01:19,397 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-18 12:01:19,397 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52077.78, 'new_value': 55848.27}, {'field': 'total_amount', 'old_value': 52077.78, 'new_value': 55848.27}, {'field': 'order_count', 'old_value': 3003, 'new_value': 3205}]
2025-05-18 12:01:19,398 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBP
2025-05-18 12:01:19,811 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBP
2025-05-18 12:01:19,811 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39000.0, 'new_value': 44000.0}, {'field': 'total_amount', 'old_value': 39000.0, 'new_value': 44000.0}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-05-18 12:01:19,811 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-18 12:01:20,264 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-18 12:01:20,264 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9875.45, 'new_value': 10558.45}, {'field': 'offline_amount', 'old_value': 178466.0, 'new_value': 189833.0}, {'field': 'total_amount', 'old_value': 188341.45, 'new_value': 200391.45}, {'field': 'order_count', 'old_value': 980, 'new_value': 1068}]
2025-05-18 12:01:20,264 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-18 12:01:20,719 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-18 12:01:20,719 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-05-18 12:01:20,720 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-18 12:01:21,121 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-18 12:01:21,121 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124909.2, 'new_value': 136713.2}, {'field': 'total_amount', 'old_value': 124909.2, 'new_value': 136713.2}, {'field': 'order_count', 'old_value': 1599, 'new_value': 1741}]
2025-05-18 12:01:21,121 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-18 12:01:21,527 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-18 12:01:21,527 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70814.83, 'new_value': 73108.83}, {'field': 'total_amount', 'old_value': 70814.83, 'new_value': 73108.83}, {'field': 'order_count', 'old_value': 89, 'new_value': 92}]
2025-05-18 12:01:21,527 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-18 12:01:21,967 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-18 12:01:21,967 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26688.74, 'new_value': 28583.14}, {'field': 'offline_amount', 'old_value': 734481.37, 'new_value': 786501.98}, {'field': 'total_amount', 'old_value': 761170.11, 'new_value': 815085.12}, {'field': 'order_count', 'old_value': 3623, 'new_value': 3878}]
2025-05-18 12:01:21,968 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPE
2025-05-18 12:01:22,444 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPE
2025-05-18 12:01:22,444 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7274.0, 'new_value': 8374.0}, {'field': 'offline_amount', 'old_value': 5131.0, 'new_value': 5830.0}, {'field': 'total_amount', 'old_value': 12405.0, 'new_value': 14204.0}, {'field': 'order_count', 'old_value': 47, 'new_value': 52}]
2025-05-18 12:01:22,445 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-18 12:01:22,898 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-18 12:01:22,899 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49603.61, 'new_value': 63463.21}, {'field': 'offline_amount', 'old_value': 129125.18, 'new_value': 132530.2}, {'field': 'total_amount', 'old_value': 178728.79, 'new_value': 195993.41}, {'field': 'order_count', 'old_value': 3231, 'new_value': 3554}]
2025-05-18 12:01:22,899 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-18 12:01:23,446 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-18 12:01:23,446 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 475981.14, 'new_value': 518980.13}, {'field': 'total_amount', 'old_value': 475981.14, 'new_value': 518980.13}, {'field': 'order_count', 'old_value': 5591, 'new_value': 6039}]
2025-05-18 12:01:23,446 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-18 12:01:23,855 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-18 12:01:23,855 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 327809.0, 'new_value': 366495.2}, {'field': 'total_amount', 'old_value': 478941.8, 'new_value': 517628.0}, {'field': 'order_count', 'old_value': 3253, 'new_value': 3291}]
2025-05-18 12:01:23,855 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-18 12:01:24,330 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-18 12:01:24,331 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 129961.0, 'new_value': 141866.0}, {'field': 'total_amount', 'old_value': 129961.0, 'new_value': 141866.0}, {'field': 'order_count', 'old_value': 2116, 'new_value': 2320}]
2025-05-18 12:01:24,331 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-18 12:01:24,930 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-18 12:01:24,930 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 121304.91, 'new_value': 131932.9}, {'field': 'total_amount', 'old_value': 121304.91, 'new_value': 131932.9}, {'field': 'order_count', 'old_value': 5118, 'new_value': 5536}]
2025-05-18 12:01:24,931 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-18 12:01:25,384 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-18 12:01:25,385 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 150049.77, 'new_value': 164835.76}, {'field': 'total_amount', 'old_value': 150049.77, 'new_value': 164835.76}, {'field': 'order_count', 'old_value': 1146, 'new_value': 1246}]
2025-05-18 12:01:25,385 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-18 12:01:25,831 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-18 12:01:25,831 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1528.8, 'new_value': 1728.8}, {'field': 'offline_amount', 'old_value': 61571.0, 'new_value': 65314.9}, {'field': 'total_amount', 'old_value': 63099.8, 'new_value': 67043.7}, {'field': 'order_count', 'old_value': 376, 'new_value': 404}]
2025-05-18 12:01:25,831 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-18 12:01:26,408 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-18 12:01:26,408 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75960.91, 'new_value': 83495.7}, {'field': 'total_amount', 'old_value': 105741.51, 'new_value': 113276.3}, {'field': 'order_count', 'old_value': 3044, 'new_value': 3255}]
2025-05-18 12:01:26,408 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-18 12:01:26,907 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-18 12:01:26,908 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36716.0, 'new_value': 39928.0}, {'field': 'total_amount', 'old_value': 36716.0, 'new_value': 39928.0}, {'field': 'order_count', 'old_value': 80, 'new_value': 89}]
2025-05-18 12:01:26,908 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-18 12:01:27,549 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-18 12:01:27,549 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 126527.3, 'new_value': 135819.3}, {'field': 'offline_amount', 'old_value': 19415.1, 'new_value': 20265.1}, {'field': 'total_amount', 'old_value': 145942.4, 'new_value': 156084.4}, {'field': 'order_count', 'old_value': 6997, 'new_value': 7687}]
2025-05-18 12:01:27,549 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-18 12:01:27,974 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-18 12:01:27,974 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 209336.0, 'new_value': 214858.0}, {'field': 'total_amount', 'old_value': 209336.0, 'new_value': 214858.0}, {'field': 'order_count', 'old_value': 311, 'new_value': 322}]
2025-05-18 12:01:27,974 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-18 12:01:28,437 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-18 12:01:28,438 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23594.0, 'new_value': 24444.0}, {'field': 'total_amount', 'old_value': 23594.0, 'new_value': 24444.0}, {'field': 'order_count', 'old_value': 127, 'new_value': 132}]
2025-05-18 12:01:28,438 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-18 12:01:28,805 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-18 12:01:28,805 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9754.5, 'new_value': 12533.8}, {'field': 'offline_amount', 'old_value': 29102.5, 'new_value': 34701.8}, {'field': 'total_amount', 'old_value': 38857.0, 'new_value': 47235.6}, {'field': 'order_count', 'old_value': 439, 'new_value': 488}]
2025-05-18 12:01:28,805 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-18 12:01:29,257 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-18 12:01:29,258 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 70402.94, 'new_value': 75839.24}, {'field': 'offline_amount', 'old_value': 136204.35, 'new_value': 148651.88}, {'field': 'total_amount', 'old_value': 206607.29, 'new_value': 224491.12}, {'field': 'order_count', 'old_value': 6562, 'new_value': 7130}]
2025-05-18 12:01:29,258 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-18 12:01:29,635 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-18 12:01:29,635 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66014.0, 'new_value': 71934.0}, {'field': 'total_amount', 'old_value': 66014.0, 'new_value': 71934.0}, {'field': 'order_count', 'old_value': 287, 'new_value': 312}]
2025-05-18 12:01:29,635 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPF
2025-05-18 12:01:30,093 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPF
2025-05-18 12:01:30,093 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97643.0, 'new_value': 98093.0}, {'field': 'total_amount', 'old_value': 97643.0, 'new_value': 98093.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-05-18 12:01:30,093 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMBQ
2025-05-18 12:01:30,521 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMBQ
2025-05-18 12:01:30,522 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81271.0, 'new_value': 89464.72}, {'field': 'total_amount', 'old_value': 81279.0, 'new_value': 89472.72}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-05-18 12:01:30,522 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-18 12:01:30,950 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-18 12:01:30,950 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 111268.45, 'new_value': 119358.68}, {'field': 'offline_amount', 'old_value': 32951.09, 'new_value': 34945.75}, {'field': 'total_amount', 'old_value': 144219.54, 'new_value': 154304.43}, {'field': 'order_count', 'old_value': 8117, 'new_value': 8706}]
2025-05-18 12:01:30,951 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-18 12:01:31,397 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-18 12:01:31,398 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 166997.39, 'new_value': 191329.69}, {'field': 'total_amount', 'old_value': 189484.79, 'new_value': 213817.09}, {'field': 'order_count', 'old_value': 1047, 'new_value': 1138}]
2025-05-18 12:01:31,398 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-18 12:01:31,870 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-18 12:01:31,870 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 134293.8, 'new_value': 152723.14}, {'field': 'offline_amount', 'old_value': 265946.92, 'new_value': 285946.92}, {'field': 'total_amount', 'old_value': 400240.72, 'new_value': 438670.06}, {'field': 'order_count', 'old_value': 928, 'new_value': 1070}]
2025-05-18 12:01:31,870 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-18 12:01:32,301 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-18 12:01:32,301 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 123531.89, 'new_value': 131339.59}, {'field': 'total_amount', 'old_value': 123531.89, 'new_value': 131339.59}, {'field': 'order_count', 'old_value': 6350, 'new_value': 6773}]
2025-05-18 12:01:32,301 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-18 12:01:32,768 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-18 12:01:32,769 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6506.0, 'new_value': 7858.0}, {'field': 'total_amount', 'old_value': 14193.0, 'new_value': 15545.0}, {'field': 'order_count', 'old_value': 71, 'new_value': 78}]
2025-05-18 12:01:32,769 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-18 12:01:33,225 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-18 12:01:33,225 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 101775.37, 'new_value': 108529.37}, {'field': 'offline_amount', 'old_value': 186091.85, 'new_value': 205268.01}, {'field': 'total_amount', 'old_value': 287867.22, 'new_value': 313797.38}, {'field': 'order_count', 'old_value': 2367, 'new_value': 2557}]
2025-05-18 12:01:33,225 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-18 12:01:33,658 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-18 12:01:33,658 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10420.56, 'new_value': 10764.14}, {'field': 'offline_amount', 'old_value': 23407.4, 'new_value': 25371.0}, {'field': 'total_amount', 'old_value': 33827.96, 'new_value': 36135.14}, {'field': 'order_count', 'old_value': 1357, 'new_value': 1441}]
2025-05-18 12:01:33,658 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUP
2025-05-18 12:01:34,132 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUP
2025-05-18 12:01:34,132 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18300.0, 'new_value': 23300.0}, {'field': 'total_amount', 'old_value': 18300.0, 'new_value': 23300.0}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-05-18 12:01:34,132 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-18 12:01:34,551 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-18 12:01:34,551 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61236.0, 'new_value': 65086.0}, {'field': 'total_amount', 'old_value': 61236.0, 'new_value': 65086.0}, {'field': 'order_count', 'old_value': 1686, 'new_value': 1687}]
2025-05-18 12:01:34,552 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-18 12:01:35,022 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-18 12:01:35,022 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 398504.0, 'new_value': 409970.0}, {'field': 'total_amount', 'old_value': 398504.0, 'new_value': 409970.0}, {'field': 'order_count', 'old_value': 2416, 'new_value': 2553}]
2025-05-18 12:01:35,023 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-18 12:01:35,457 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-18 12:01:35,457 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2244.0, 'new_value': 2447.0}, {'field': 'offline_amount', 'old_value': 19625.6, 'new_value': 22193.4}, {'field': 'total_amount', 'old_value': 21869.6, 'new_value': 24640.4}, {'field': 'order_count', 'old_value': 809, 'new_value': 895}]
2025-05-18 12:01:35,457 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-18 12:01:35,913 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-18 12:01:35,913 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 63274.88, 'new_value': 70783.08}, {'field': 'total_amount', 'old_value': 70503.95, 'new_value': 78012.15}, {'field': 'order_count', 'old_value': 408, 'new_value': 439}]
2025-05-18 12:01:35,913 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-18 12:01:36,344 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-18 12:01:36,344 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1344.0, 'new_value': 1659.0}, {'field': 'offline_amount', 'old_value': 38408.0, 'new_value': 40235.0}, {'field': 'total_amount', 'old_value': 39752.0, 'new_value': 41894.0}, {'field': 'order_count', 'old_value': 316, 'new_value': 336}]
2025-05-18 12:01:36,344 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-18 12:01:36,756 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-18 12:01:36,756 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3403.0, 'new_value': 3593.0}, {'field': 'offline_amount', 'old_value': 13683.0, 'new_value': 14263.0}, {'field': 'total_amount', 'old_value': 17086.0, 'new_value': 17856.0}, {'field': 'order_count', 'old_value': 151, 'new_value': 156}]
2025-05-18 12:01:36,756 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-18 12:01:37,260 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-18 12:01:37,261 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45554.0, 'new_value': 48948.0}, {'field': 'offline_amount', 'old_value': 174869.0, 'new_value': 188169.0}, {'field': 'total_amount', 'old_value': 220423.0, 'new_value': 237117.0}, {'field': 'order_count', 'old_value': 958, 'new_value': 1018}]
2025-05-18 12:01:37,261 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-18 12:01:37,695 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-18 12:01:37,695 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 194324.71, 'new_value': 208194.86}, {'field': 'offline_amount', 'old_value': 141371.59, 'new_value': 150660.35}, {'field': 'total_amount', 'old_value': 335696.3, 'new_value': 358855.21}, {'field': 'order_count', 'old_value': 13355, 'new_value': 14323}]
2025-05-18 12:01:37,695 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-18 12:01:38,145 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-18 12:01:38,145 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 234417.3, 'new_value': 248163.4}, {'field': 'total_amount', 'old_value': 234417.3, 'new_value': 248163.4}, {'field': 'order_count', 'old_value': 5069, 'new_value': 5372}]
2025-05-18 12:01:38,145 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-18 12:01:38,652 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-18 12:01:38,652 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36176.4, 'new_value': 38329.4}, {'field': 'total_amount', 'old_value': 36176.4, 'new_value': 38329.4}, {'field': 'order_count', 'old_value': 186, 'new_value': 197}]
2025-05-18 12:01:38,653 - INFO - 开始更新记录 - 表单实例ID: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMMX
2025-05-18 12:01:39,181 - INFO - 更新表单数据成功: FINST-8PF66V71N10VB3JV6BZ3H5QQWG6F24ARNI7AMMX
2025-05-18 12:01:39,182 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22784.18, 'new_value': 24071.74}, {'field': 'total_amount', 'old_value': 22784.18, 'new_value': 24071.74}, {'field': 'order_count', 'old_value': 1070, 'new_value': 1106}]
2025-05-18 12:01:39,182 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-18 12:01:39,627 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-18 12:01:39,627 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41068.94, 'new_value': 44027.17}, {'field': 'total_amount', 'old_value': 46428.91, 'new_value': 49387.14}, {'field': 'order_count', 'old_value': 738, 'new_value': 787}]
2025-05-18 12:01:39,627 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-18 12:01:40,061 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-18 12:01:40,061 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16672.62, 'new_value': 18857.62}, {'field': 'total_amount', 'old_value': 16672.62, 'new_value': 18857.62}, {'field': 'order_count', 'old_value': 70, 'new_value': 82}]
2025-05-18 12:01:40,061 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-18 12:01:40,521 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-18 12:01:40,521 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 370823.47, 'new_value': 396023.47}, {'field': 'total_amount', 'old_value': 370823.47, 'new_value': 396023.47}, {'field': 'order_count', 'old_value': 4569, 'new_value': 4694}]
2025-05-18 12:01:40,521 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-18 12:01:40,970 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-18 12:01:40,970 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 121995.67, 'new_value': 131283.14}, {'field': 'total_amount', 'old_value': 121995.67, 'new_value': 131283.14}, {'field': 'order_count', 'old_value': 2334, 'new_value': 2511}]
2025-05-18 12:01:40,971 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-18 12:01:41,474 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-18 12:01:41,474 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 249675.0, 'new_value': 263751.0}, {'field': 'total_amount', 'old_value': 249675.0, 'new_value': 263751.0}, {'field': 'order_count', 'old_value': 56, 'new_value': 62}]
2025-05-18 12:01:41,475 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-18 12:01:42,006 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-18 12:01:42,006 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85933.8, 'new_value': 93340.3}, {'field': 'total_amount', 'old_value': 85933.8, 'new_value': 93340.3}, {'field': 'order_count', 'old_value': 2173, 'new_value': 2329}]
2025-05-18 12:01:42,006 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-18 12:01:42,388 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-18 12:01:42,388 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19526.0, 'new_value': 20844.0}, {'field': 'total_amount', 'old_value': 19526.0, 'new_value': 20844.0}, {'field': 'order_count', 'old_value': 67, 'new_value': 71}]
2025-05-18 12:01:42,388 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-18 12:01:42,852 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-18 12:01:42,852 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 61293.41, 'new_value': 67440.29}, {'field': 'offline_amount', 'old_value': 285240.2, 'new_value': 305973.3}, {'field': 'total_amount', 'old_value': 346533.61, 'new_value': 373413.59}, {'field': 'order_count', 'old_value': 2317, 'new_value': 2517}]
2025-05-18 12:01:42,852 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-18 12:01:43,283 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-18 12:01:43,283 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54168.0, 'new_value': 57498.0}, {'field': 'total_amount', 'old_value': 56018.0, 'new_value': 59348.0}, {'field': 'order_count', 'old_value': 325, 'new_value': 345}]
2025-05-18 12:01:43,283 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-18 12:01:43,780 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-18 12:01:43,780 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23293.63, 'new_value': 27442.72}, {'field': 'total_amount', 'old_value': 57941.96, 'new_value': 62091.05}, {'field': 'order_count', 'old_value': 3782, 'new_value': 4056}]
2025-05-18 12:01:43,780 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-18 12:01:44,243 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-18 12:01:44,244 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38284.48, 'new_value': 46416.81}, {'field': 'total_amount', 'old_value': 98066.45, 'new_value': 106198.78}, {'field': 'order_count', 'old_value': 6414, 'new_value': 6937}]
2025-05-18 12:01:44,244 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-18 12:01:44,677 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-18 12:01:44,678 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 758299.85, 'new_value': 813452.4}, {'field': 'total_amount', 'old_value': 758299.85, 'new_value': 813452.4}, {'field': 'order_count', 'old_value': 2262, 'new_value': 2382}]
2025-05-18 12:01:44,678 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-18 12:01:45,118 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-18 12:01:45,118 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 111238.0, 'new_value': 117595.0}, {'field': 'total_amount', 'old_value': 111238.0, 'new_value': 117595.0}, {'field': 'order_count', 'old_value': 3911, 'new_value': 4131}]
2025-05-18 12:01:45,118 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-18 12:01:45,605 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-18 12:01:45,605 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 460940.54, 'new_value': 501262.22}, {'field': 'total_amount', 'old_value': 460940.54, 'new_value': 501262.22}, {'field': 'order_count', 'old_value': 2223, 'new_value': 2454}]
2025-05-18 12:01:45,605 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMLE
2025-05-18 12:01:46,117 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMLE
2025-05-18 12:01:46,117 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42000.0, 'new_value': 56000.0}, {'field': 'total_amount', 'old_value': 42000.0, 'new_value': 56000.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 8}]
2025-05-18 12:01:46,117 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-18 12:01:46,547 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-18 12:01:46,547 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 507733.96, 'new_value': 540564.66}, {'field': 'total_amount', 'old_value': 507733.96, 'new_value': 540564.66}, {'field': 'order_count', 'old_value': 1617, 'new_value': 1685}]
2025-05-18 12:01:46,547 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-18 12:01:46,999 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-18 12:01:46,999 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 204040.08, 'new_value': 221332.27}, {'field': 'total_amount', 'old_value': 204040.08, 'new_value': 221332.27}, {'field': 'order_count', 'old_value': 568, 'new_value': 613}]
2025-05-18 12:01:46,999 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-18 12:01:47,473 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-18 12:01:47,474 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82279.0, 'new_value': 85225.0}, {'field': 'total_amount', 'old_value': 82359.0, 'new_value': 85305.0}, {'field': 'order_count', 'old_value': 7875, 'new_value': 8133}]
2025-05-18 12:01:47,474 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-18 12:01:47,916 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-18 12:01:47,916 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71117.0, 'new_value': 77771.0}, {'field': 'total_amount', 'old_value': 71117.0, 'new_value': 77771.0}, {'field': 'order_count', 'old_value': 358, 'new_value': 382}]
2025-05-18 12:01:47,916 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-18 12:01:48,405 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-18 12:01:48,406 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 139085.0, 'new_value': 150385.0}, {'field': 'total_amount', 'old_value': 139085.0, 'new_value': 150385.0}, {'field': 'order_count', 'old_value': 336, 'new_value': 365}]
2025-05-18 12:01:48,406 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-18 12:01:48,868 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-18 12:01:48,868 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122185.0, 'new_value': 126761.0}, {'field': 'total_amount', 'old_value': 122185.0, 'new_value': 126761.0}, {'field': 'order_count', 'old_value': 12930, 'new_value': 13338}]
2025-05-18 12:01:48,868 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-18 12:01:49,305 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-18 12:01:49,305 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86744.0, 'new_value': 91595.0}, {'field': 'total_amount', 'old_value': 86744.0, 'new_value': 91595.0}, {'field': 'order_count', 'old_value': 757, 'new_value': 808}]
2025-05-18 12:01:49,305 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQG
2025-05-18 12:01:49,844 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQG
2025-05-18 12:01:49,844 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69486.0, 'new_value': 81182.0}, {'field': 'total_amount', 'old_value': 69487.0, 'new_value': 81183.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 42}]
2025-05-18 12:01:49,844 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTG
2025-05-18 12:01:50,252 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTG
2025-05-18 12:01:50,252 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29039.0, 'new_value': 29136.0}, {'field': 'total_amount', 'old_value': 29039.0, 'new_value': 29136.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-05-18 12:01:50,252 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-18 12:01:50,697 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-18 12:01:50,697 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33496.06, 'new_value': 35446.06}, {'field': 'total_amount', 'old_value': 33496.06, 'new_value': 35446.06}, {'field': 'order_count', 'old_value': 565, 'new_value': 594}]
2025-05-18 12:01:50,697 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-18 12:01:51,159 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-18 12:01:51,159 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44927.6, 'new_value': 56191.6}, {'field': 'total_amount', 'old_value': 44927.6, 'new_value': 56191.6}, {'field': 'order_count', 'old_value': 29, 'new_value': 34}]
2025-05-18 12:01:51,159 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-18 12:01:51,615 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-18 12:01:51,616 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 54896.8, 'new_value': 57753.31}, {'field': 'offline_amount', 'old_value': 540486.67, 'new_value': 574064.3}, {'field': 'total_amount', 'old_value': 593509.14, 'new_value': 629943.28}, {'field': 'order_count', 'old_value': 2829, 'new_value': 2997}]
2025-05-18 12:01:51,616 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-05-18 12:01:52,106 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-05-18 12:01:52,106 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50242.0, 'new_value': 53394.0}, {'field': 'total_amount', 'old_value': 55560.0, 'new_value': 58712.0}, {'field': 'order_count', 'old_value': 27, 'new_value': 30}]
2025-05-18 12:01:52,106 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-18 12:01:52,565 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-18 12:01:52,565 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14477.0, 'new_value': 14853.0}, {'field': 'total_amount', 'old_value': 14477.0, 'new_value': 14853.0}, {'field': 'order_count', 'old_value': 85, 'new_value': 90}]
2025-05-18 12:01:52,565 - INFO - 开始批量插入 1 条新记录
2025-05-18 12:01:52,737 - INFO - 批量插入响应状态码: 200
2025-05-18 12:01:52,737 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 18 May 2025 04:01:53 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '142941C3-DCAF-7D3F-B300-1D7B64F71909', 'x-acs-trace-id': 'fd65136a151b8fc57b7ca6ba5091d185', 'etag': '6C1IGWU9ZuHDmNMBvUuISPg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-05-18 12:01:52,737 - INFO - 批量插入响应体: {'result': ['FINST-YJ866471ACHVV5H1ENMQ7732GXOW3MAMQ4TAMUC']}
2025-05-18 12:01:52,737 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-05-18 12:01:52,737 - INFO - 成功插入的数据ID: ['FINST-YJ866471ACHVV5H1ENMQ7732GXOW3MAMQ4TAMUC']
2025-05-18 12:01:55,737 - INFO - 批量插入完成，共 1 条记录
2025-05-18 12:01:55,737 - INFO - 日期 2025-05 处理完成 - 更新: 173 条，插入: 1 条，错误: 0 条
2025-05-18 12:01:55,737 - INFO - 数据同步完成！更新: 173 条，插入: 3 条，错误: 0 条
2025-05-18 12:01:55,740 - INFO - =================同步完成====================
2025-05-18 15:00:02,011 - INFO - =================使用默认全量同步=============
2025-05-18 15:00:03,411 - INFO - MySQL查询成功，共获取 3295 条记录
2025-05-18 15:00:03,411 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-18 15:00:03,439 - INFO - 开始处理日期: 2025-01
2025-05-18 15:00:03,442 - INFO - Request Parameters - Page 1:
2025-05-18 15:00:03,442 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 15:00:03,442 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 15:00:04,430 - INFO - Response - Page 1:
2025-05-18 15:00:04,631 - INFO - 第 1 页获取到 100 条记录
2025-05-18 15:00:04,631 - INFO - Request Parameters - Page 2:
2025-05-18 15:00:04,631 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 15:00:04,631 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 15:00:05,610 - INFO - Response - Page 2:
2025-05-18 15:00:05,810 - INFO - 第 2 页获取到 100 条记录
2025-05-18 15:00:05,810 - INFO - Request Parameters - Page 3:
2025-05-18 15:00:05,810 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 15:00:05,810 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 15:00:06,297 - INFO - Response - Page 3:
2025-05-18 15:00:06,497 - INFO - 第 3 页获取到 100 条记录
2025-05-18 15:00:06,497 - INFO - Request Parameters - Page 4:
2025-05-18 15:00:06,497 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 15:00:06,497 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 15:00:07,039 - INFO - Response - Page 4:
2025-05-18 15:00:07,241 - INFO - 第 4 页获取到 100 条记录
2025-05-18 15:00:07,241 - INFO - Request Parameters - Page 5:
2025-05-18 15:00:07,241 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 15:00:07,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 15:00:07,833 - INFO - Response - Page 5:
2025-05-18 15:00:08,034 - INFO - 第 5 页获取到 100 条记录
2025-05-18 15:00:08,034 - INFO - Request Parameters - Page 6:
2025-05-18 15:00:08,034 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 15:00:08,034 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 15:00:08,516 - INFO - Response - Page 6:
2025-05-18 15:00:08,716 - INFO - 第 6 页获取到 100 条记录
2025-05-18 15:00:08,716 - INFO - Request Parameters - Page 7:
2025-05-18 15:00:08,716 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 15:00:08,716 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 15:00:09,157 - INFO - Response - Page 7:
2025-05-18 15:00:09,357 - INFO - 第 7 页获取到 82 条记录
2025-05-18 15:00:09,357 - INFO - 查询完成，共获取到 682 条记录
2025-05-18 15:00:09,357 - INFO - 获取到 682 条表单数据
2025-05-18 15:00:09,368 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-18 15:00:09,379 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-18 15:00:09,380 - INFO - 开始处理日期: 2025-02
2025-05-18 15:00:09,380 - INFO - Request Parameters - Page 1:
2025-05-18 15:00:09,380 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 15:00:09,380 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 15:00:09,898 - INFO - Response - Page 1:
2025-05-18 15:00:10,098 - INFO - 第 1 页获取到 100 条记录
2025-05-18 15:00:10,098 - INFO - Request Parameters - Page 2:
2025-05-18 15:00:10,098 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 15:00:10,098 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 15:00:10,618 - INFO - Response - Page 2:
2025-05-18 15:00:10,818 - INFO - 第 2 页获取到 100 条记录
2025-05-18 15:00:10,818 - INFO - Request Parameters - Page 3:
2025-05-18 15:00:10,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 15:00:10,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 15:00:11,326 - INFO - Response - Page 3:
2025-05-18 15:00:11,527 - INFO - 第 3 页获取到 100 条记录
2025-05-18 15:00:11,527 - INFO - Request Parameters - Page 4:
2025-05-18 15:00:11,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 15:00:11,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 15:00:12,001 - INFO - Response - Page 4:
2025-05-18 15:00:12,201 - INFO - 第 4 页获取到 100 条记录
2025-05-18 15:00:12,201 - INFO - Request Parameters - Page 5:
2025-05-18 15:00:12,201 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 15:00:12,201 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 15:00:12,925 - INFO - Response - Page 5:
2025-05-18 15:00:13,125 - INFO - 第 5 页获取到 100 条记录
2025-05-18 15:00:13,125 - INFO - Request Parameters - Page 6:
2025-05-18 15:00:13,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 15:00:13,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 15:00:13,660 - INFO - Response - Page 6:
2025-05-18 15:00:13,861 - INFO - 第 6 页获取到 100 条记录
2025-05-18 15:00:13,861 - INFO - Request Parameters - Page 7:
2025-05-18 15:00:13,861 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 15:00:13,861 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 15:00:14,268 - INFO - Response - Page 7:
2025-05-18 15:00:14,468 - INFO - 第 7 页获取到 70 条记录
2025-05-18 15:00:14,468 - INFO - 查询完成，共获取到 670 条记录
2025-05-18 15:00:14,468 - INFO - 获取到 670 条表单数据
2025-05-18 15:00:14,480 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-18 15:00:14,491 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-18 15:00:14,492 - INFO - 开始处理日期: 2025-03
2025-05-18 15:00:14,492 - INFO - Request Parameters - Page 1:
2025-05-18 15:00:14,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 15:00:14,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 15:00:15,035 - INFO - Response - Page 1:
2025-05-18 15:00:15,235 - INFO - 第 1 页获取到 100 条记录
2025-05-18 15:00:15,235 - INFO - Request Parameters - Page 2:
2025-05-18 15:00:15,235 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 15:00:15,235 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 15:00:15,690 - INFO - Response - Page 2:
2025-05-18 15:00:15,890 - INFO - 第 2 页获取到 100 条记录
2025-05-18 15:00:15,890 - INFO - Request Parameters - Page 3:
2025-05-18 15:00:15,890 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 15:00:15,890 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 15:00:16,396 - INFO - Response - Page 3:
2025-05-18 15:00:16,596 - INFO - 第 3 页获取到 100 条记录
2025-05-18 15:00:16,596 - INFO - Request Parameters - Page 4:
2025-05-18 15:00:16,596 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 15:00:16,596 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 15:00:17,103 - INFO - Response - Page 4:
2025-05-18 15:00:17,303 - INFO - 第 4 页获取到 100 条记录
2025-05-18 15:00:17,303 - INFO - Request Parameters - Page 5:
2025-05-18 15:00:17,303 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 15:00:17,303 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 15:00:17,814 - INFO - Response - Page 5:
2025-05-18 15:00:18,014 - INFO - 第 5 页获取到 100 条记录
2025-05-18 15:00:18,014 - INFO - Request Parameters - Page 6:
2025-05-18 15:00:18,014 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 15:00:18,014 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 15:00:18,548 - INFO - Response - Page 6:
2025-05-18 15:00:18,749 - INFO - 第 6 页获取到 100 条记录
2025-05-18 15:00:18,749 - INFO - Request Parameters - Page 7:
2025-05-18 15:00:18,749 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 15:00:18,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 15:00:19,160 - INFO - Response - Page 7:
2025-05-18 15:00:19,361 - INFO - 第 7 页获取到 61 条记录
2025-05-18 15:00:19,361 - INFO - 查询完成，共获取到 661 条记录
2025-05-18 15:00:19,361 - INFO - 获取到 661 条表单数据
2025-05-18 15:00:19,372 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-18 15:00:19,384 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-18 15:00:19,384 - INFO - 开始处理日期: 2025-04
2025-05-18 15:00:19,384 - INFO - Request Parameters - Page 1:
2025-05-18 15:00:19,384 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 15:00:19,384 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 15:00:19,927 - INFO - Response - Page 1:
2025-05-18 15:00:20,127 - INFO - 第 1 页获取到 100 条记录
2025-05-18 15:00:20,127 - INFO - Request Parameters - Page 2:
2025-05-18 15:00:20,127 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 15:00:20,127 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 15:00:20,572 - INFO - Response - Page 2:
2025-05-18 15:00:20,772 - INFO - 第 2 页获取到 100 条记录
2025-05-18 15:00:20,772 - INFO - Request Parameters - Page 3:
2025-05-18 15:00:20,772 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 15:00:20,772 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 15:00:21,252 - INFO - Response - Page 3:
2025-05-18 15:00:21,452 - INFO - 第 3 页获取到 100 条记录
2025-05-18 15:00:21,452 - INFO - Request Parameters - Page 4:
2025-05-18 15:00:21,452 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 15:00:21,452 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 15:00:21,950 - INFO - Response - Page 4:
2025-05-18 15:00:22,151 - INFO - 第 4 页获取到 100 条记录
2025-05-18 15:00:22,151 - INFO - Request Parameters - Page 5:
2025-05-18 15:00:22,151 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 15:00:22,151 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 15:00:22,598 - INFO - Response - Page 5:
2025-05-18 15:00:22,798 - INFO - 第 5 页获取到 100 条记录
2025-05-18 15:00:22,798 - INFO - Request Parameters - Page 6:
2025-05-18 15:00:22,798 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 15:00:22,799 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 15:00:23,356 - INFO - Response - Page 6:
2025-05-18 15:00:23,557 - INFO - 第 6 页获取到 100 条记录
2025-05-18 15:00:23,557 - INFO - Request Parameters - Page 7:
2025-05-18 15:00:23,557 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 15:00:23,557 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 15:00:24,001 - INFO - Response - Page 7:
2025-05-18 15:00:24,201 - INFO - 第 7 页获取到 56 条记录
2025-05-18 15:00:24,201 - INFO - 查询完成，共获取到 656 条记录
2025-05-18 15:00:24,201 - INFO - 获取到 656 条表单数据
2025-05-18 15:00:24,213 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-18 15:00:24,225 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-18 15:00:24,225 - INFO - 开始处理日期: 2025-05
2025-05-18 15:00:24,225 - INFO - Request Parameters - Page 1:
2025-05-18 15:00:24,225 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 15:00:24,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 15:00:24,693 - INFO - Response - Page 1:
2025-05-18 15:00:24,893 - INFO - 第 1 页获取到 100 条记录
2025-05-18 15:00:24,893 - INFO - Request Parameters - Page 2:
2025-05-18 15:00:24,893 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 15:00:24,893 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 15:00:25,489 - INFO - Response - Page 2:
2025-05-18 15:00:25,691 - INFO - 第 2 页获取到 100 条记录
2025-05-18 15:00:25,691 - INFO - Request Parameters - Page 3:
2025-05-18 15:00:25,691 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 15:00:25,691 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 15:00:26,198 - INFO - Response - Page 3:
2025-05-18 15:00:26,398 - INFO - 第 3 页获取到 100 条记录
2025-05-18 15:00:26,398 - INFO - Request Parameters - Page 4:
2025-05-18 15:00:26,398 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 15:00:26,398 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 15:00:26,896 - INFO - Response - Page 4:
2025-05-18 15:00:27,096 - INFO - 第 4 页获取到 100 条记录
2025-05-18 15:00:27,096 - INFO - Request Parameters - Page 5:
2025-05-18 15:00:27,096 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 15:00:27,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 15:00:27,601 - INFO - Response - Page 5:
2025-05-18 15:00:27,801 - INFO - 第 5 页获取到 100 条记录
2025-05-18 15:00:27,801 - INFO - Request Parameters - Page 6:
2025-05-18 15:00:27,801 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 15:00:27,802 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 15:00:28,292 - INFO - Response - Page 6:
2025-05-18 15:00:28,493 - INFO - 第 6 页获取到 100 条记录
2025-05-18 15:00:28,493 - INFO - Request Parameters - Page 7:
2025-05-18 15:00:28,493 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 15:00:28,493 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 15:00:28,867 - INFO - Response - Page 7:
2025-05-18 15:00:29,068 - INFO - 第 7 页获取到 26 条记录
2025-05-18 15:00:29,068 - INFO - 查询完成，共获取到 626 条记录
2025-05-18 15:00:29,068 - INFO - 获取到 626 条表单数据
2025-05-18 15:00:29,079 - INFO - 当前日期 2025-05 有 626 条MySQL数据需要处理
2025-05-18 15:00:29,079 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-18 15:00:29,564 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-18 15:00:29,564 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 110905.53, 'new_value': 118826.89}, {'field': 'total_amount', 'old_value': 110905.53, 'new_value': 118826.89}, {'field': 'order_count', 'old_value': 4477, 'new_value': 4761}]
2025-05-18 15:00:29,564 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-18 15:00:30,020 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-18 15:00:30,020 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 285951.0, 'new_value': 299540.0}, {'field': 'offline_amount', 'old_value': 183050.0, 'new_value': 191655.0}, {'field': 'total_amount', 'old_value': 469001.0, 'new_value': 491195.0}, {'field': 'order_count', 'old_value': 517, 'new_value': 546}]
2025-05-18 15:00:30,023 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-18 15:00:30,498 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8U
2025-05-18 15:00:30,498 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9659.71, 'new_value': 10197.64}, {'field': 'offline_amount', 'old_value': 175585.9, 'new_value': 195106.1}, {'field': 'total_amount', 'old_value': 185245.61, 'new_value': 205303.74}, {'field': 'order_count', 'old_value': 1313, 'new_value': 1433}]
2025-05-18 15:00:30,498 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-18 15:00:30,922 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-18 15:00:30,922 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26254.0, 'new_value': 26384.0}, {'field': 'offline_amount', 'old_value': 135599.0, 'new_value': 177075.0}, {'field': 'total_amount', 'old_value': 161853.0, 'new_value': 203459.0}, {'field': 'order_count', 'old_value': 54, 'new_value': 66}]
2025-05-18 15:00:30,928 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-18 15:00:31,402 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-18 15:00:31,402 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13661.9, 'new_value': 14514.15}, {'field': 'offline_amount', 'old_value': 10578.4, 'new_value': 11115.36}, {'field': 'total_amount', 'old_value': 24240.3, 'new_value': 25629.51}, {'field': 'order_count', 'old_value': 1087, 'new_value': 1139}]
2025-05-18 15:00:31,404 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-18 15:00:31,848 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-18 15:00:31,848 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 150385.0, 'new_value': 163985.0}, {'field': 'total_amount', 'old_value': 150385.0, 'new_value': 163985.0}, {'field': 'order_count', 'old_value': 365, 'new_value': 397}]
2025-05-18 15:00:31,848 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-18 15:00:32,360 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-18 15:00:32,360 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35446.06, 'new_value': 37314.76}, {'field': 'total_amount', 'old_value': 35446.06, 'new_value': 37314.76}, {'field': 'order_count', 'old_value': 594, 'new_value': 623}]
2025-05-18 15:00:32,360 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-18 15:00:32,766 - INFO - 更新表单数据成功: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-18 15:00:32,766 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69716.0, 'new_value': 73748.0}, {'field': 'total_amount', 'old_value': 69716.0, 'new_value': 73748.0}, {'field': 'order_count', 'old_value': 237, 'new_value': 251}]
2025-05-18 15:00:32,766 - INFO - 日期 2025-05 处理完成 - 更新: 8 条，插入: 0 条，错误: 0 条
2025-05-18 15:00:32,766 - INFO - 数据同步完成！更新: 8 条，插入: 0 条，错误: 0 条
2025-05-18 15:00:32,768 - INFO - =================同步完成====================
2025-05-18 18:00:02,134 - INFO - =================使用默认全量同步=============
2025-05-18 18:00:03,526 - INFO - MySQL查询成功，共获取 3295 条记录
2025-05-18 18:00:03,527 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-18 18:00:03,553 - INFO - 开始处理日期: 2025-01
2025-05-18 18:00:03,556 - INFO - Request Parameters - Page 1:
2025-05-18 18:00:03,556 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 18:00:03,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 18:00:04,676 - INFO - Response - Page 1:
2025-05-18 18:00:04,876 - INFO - 第 1 页获取到 100 条记录
2025-05-18 18:00:04,876 - INFO - Request Parameters - Page 2:
2025-05-18 18:00:04,876 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 18:00:04,876 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 18:00:05,414 - INFO - Response - Page 2:
2025-05-18 18:00:05,614 - INFO - 第 2 页获取到 100 条记录
2025-05-18 18:00:05,614 - INFO - Request Parameters - Page 3:
2025-05-18 18:00:05,614 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 18:00:05,614 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 18:00:06,347 - INFO - Response - Page 3:
2025-05-18 18:00:06,548 - INFO - 第 3 页获取到 100 条记录
2025-05-18 18:00:06,548 - INFO - Request Parameters - Page 4:
2025-05-18 18:00:06,548 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 18:00:06,548 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 18:00:07,188 - INFO - Response - Page 4:
2025-05-18 18:00:07,388 - INFO - 第 4 页获取到 100 条记录
2025-05-18 18:00:07,388 - INFO - Request Parameters - Page 5:
2025-05-18 18:00:07,388 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 18:00:07,388 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 18:00:07,945 - INFO - Response - Page 5:
2025-05-18 18:00:08,145 - INFO - 第 5 页获取到 100 条记录
2025-05-18 18:00:08,145 - INFO - Request Parameters - Page 6:
2025-05-18 18:00:08,145 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 18:00:08,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 18:00:08,611 - INFO - Response - Page 6:
2025-05-18 18:00:08,812 - INFO - 第 6 页获取到 100 条记录
2025-05-18 18:00:08,812 - INFO - Request Parameters - Page 7:
2025-05-18 18:00:08,812 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 18:00:08,812 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 18:00:09,332 - INFO - Response - Page 7:
2025-05-18 18:00:09,532 - INFO - 第 7 页获取到 82 条记录
2025-05-18 18:00:09,532 - INFO - 查询完成，共获取到 682 条记录
2025-05-18 18:00:09,532 - INFO - 获取到 682 条表单数据
2025-05-18 18:00:09,551 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-18 18:00:09,565 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-18 18:00:09,565 - INFO - 开始处理日期: 2025-02
2025-05-18 18:00:09,566 - INFO - Request Parameters - Page 1:
2025-05-18 18:00:09,566 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 18:00:09,566 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 18:00:10,146 - INFO - Response - Page 1:
2025-05-18 18:00:10,346 - INFO - 第 1 页获取到 100 条记录
2025-05-18 18:00:10,346 - INFO - Request Parameters - Page 2:
2025-05-18 18:00:10,346 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 18:00:10,346 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 18:00:10,881 - INFO - Response - Page 2:
2025-05-18 18:00:11,082 - INFO - 第 2 页获取到 100 条记录
2025-05-18 18:00:11,082 - INFO - Request Parameters - Page 3:
2025-05-18 18:00:11,082 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 18:00:11,082 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 18:00:11,584 - INFO - Response - Page 3:
2025-05-18 18:00:11,785 - INFO - 第 3 页获取到 100 条记录
2025-05-18 18:00:11,785 - INFO - Request Parameters - Page 4:
2025-05-18 18:00:11,785 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 18:00:11,785 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 18:00:12,334 - INFO - Response - Page 4:
2025-05-18 18:00:12,535 - INFO - 第 4 页获取到 100 条记录
2025-05-18 18:00:12,535 - INFO - Request Parameters - Page 5:
2025-05-18 18:00:12,535 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 18:00:12,535 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 18:00:13,093 - INFO - Response - Page 5:
2025-05-18 18:00:13,293 - INFO - 第 5 页获取到 100 条记录
2025-05-18 18:00:13,293 - INFO - Request Parameters - Page 6:
2025-05-18 18:00:13,293 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 18:00:13,293 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 18:00:13,880 - INFO - Response - Page 6:
2025-05-18 18:00:14,081 - INFO - 第 6 页获取到 100 条记录
2025-05-18 18:00:14,081 - INFO - Request Parameters - Page 7:
2025-05-18 18:00:14,081 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 18:00:14,081 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 18:00:14,527 - INFO - Response - Page 7:
2025-05-18 18:00:14,727 - INFO - 第 7 页获取到 70 条记录
2025-05-18 18:00:14,727 - INFO - 查询完成，共获取到 670 条记录
2025-05-18 18:00:14,727 - INFO - 获取到 670 条表单数据
2025-05-18 18:00:14,741 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-18 18:00:14,753 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-18 18:00:14,753 - INFO - 开始处理日期: 2025-03
2025-05-18 18:00:14,753 - INFO - Request Parameters - Page 1:
2025-05-18 18:00:14,754 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 18:00:14,754 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 18:00:15,300 - INFO - Response - Page 1:
2025-05-18 18:00:15,500 - INFO - 第 1 页获取到 100 条记录
2025-05-18 18:00:15,500 - INFO - Request Parameters - Page 2:
2025-05-18 18:00:15,500 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 18:00:15,500 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 18:00:16,013 - INFO - Response - Page 2:
2025-05-18 18:00:16,213 - INFO - 第 2 页获取到 100 条记录
2025-05-18 18:00:16,213 - INFO - Request Parameters - Page 3:
2025-05-18 18:00:16,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 18:00:16,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 18:00:16,722 - INFO - Response - Page 3:
2025-05-18 18:00:16,923 - INFO - 第 3 页获取到 100 条记录
2025-05-18 18:00:16,923 - INFO - Request Parameters - Page 4:
2025-05-18 18:00:16,923 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 18:00:16,923 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 18:00:17,415 - INFO - Response - Page 4:
2025-05-18 18:00:17,617 - INFO - 第 4 页获取到 100 条记录
2025-05-18 18:00:17,617 - INFO - Request Parameters - Page 5:
2025-05-18 18:00:17,617 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 18:00:17,617 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 18:00:18,173 - INFO - Response - Page 5:
2025-05-18 18:00:18,374 - INFO - 第 5 页获取到 100 条记录
2025-05-18 18:00:18,374 - INFO - Request Parameters - Page 6:
2025-05-18 18:00:18,374 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 18:00:18,374 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 18:00:18,835 - INFO - Response - Page 6:
2025-05-18 18:00:19,036 - INFO - 第 6 页获取到 100 条记录
2025-05-18 18:00:19,036 - INFO - Request Parameters - Page 7:
2025-05-18 18:00:19,036 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 18:00:19,036 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 18:00:19,467 - INFO - Response - Page 7:
2025-05-18 18:00:19,667 - INFO - 第 7 页获取到 61 条记录
2025-05-18 18:00:19,668 - INFO - 查询完成，共获取到 661 条记录
2025-05-18 18:00:19,668 - INFO - 获取到 661 条表单数据
2025-05-18 18:00:19,680 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-18 18:00:19,694 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-18 18:00:19,694 - INFO - 开始处理日期: 2025-04
2025-05-18 18:00:19,694 - INFO - Request Parameters - Page 1:
2025-05-18 18:00:19,694 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 18:00:19,694 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 18:00:20,288 - INFO - Response - Page 1:
2025-05-18 18:00:20,489 - INFO - 第 1 页获取到 100 条记录
2025-05-18 18:00:20,489 - INFO - Request Parameters - Page 2:
2025-05-18 18:00:20,489 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 18:00:20,489 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 18:00:21,013 - INFO - Response - Page 2:
2025-05-18 18:00:21,213 - INFO - 第 2 页获取到 100 条记录
2025-05-18 18:00:21,213 - INFO - Request Parameters - Page 3:
2025-05-18 18:00:21,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 18:00:21,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 18:00:21,685 - INFO - Response - Page 3:
2025-05-18 18:00:21,885 - INFO - 第 3 页获取到 100 条记录
2025-05-18 18:00:21,885 - INFO - Request Parameters - Page 4:
2025-05-18 18:00:21,885 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 18:00:21,885 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 18:00:22,430 - INFO - Response - Page 4:
2025-05-18 18:00:22,630 - INFO - 第 4 页获取到 100 条记录
2025-05-18 18:00:22,630 - INFO - Request Parameters - Page 5:
2025-05-18 18:00:22,630 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 18:00:22,630 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 18:00:23,121 - INFO - Response - Page 5:
2025-05-18 18:00:23,322 - INFO - 第 5 页获取到 100 条记录
2025-05-18 18:00:23,322 - INFO - Request Parameters - Page 6:
2025-05-18 18:00:23,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 18:00:23,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 18:00:23,921 - INFO - Response - Page 6:
2025-05-18 18:00:24,121 - INFO - 第 6 页获取到 100 条记录
2025-05-18 18:00:24,121 - INFO - Request Parameters - Page 7:
2025-05-18 18:00:24,121 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 18:00:24,121 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 18:00:24,519 - INFO - Response - Page 7:
2025-05-18 18:00:24,720 - INFO - 第 7 页获取到 56 条记录
2025-05-18 18:00:24,720 - INFO - 查询完成，共获取到 656 条记录
2025-05-18 18:00:24,721 - INFO - 获取到 656 条表单数据
2025-05-18 18:00:24,734 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-18 18:00:24,735 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MX4
2025-05-18 18:00:25,234 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MX4
2025-05-18 18:00:25,234 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 736361.52, 'new_value': 737319.13}, {'field': 'total_amount', 'old_value': 736361.52, 'new_value': 737319.13}]
2025-05-18 18:00:25,235 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MA5
2025-05-18 18:00:25,667 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9MA5
2025-05-18 18:00:25,668 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 240284.89, 'new_value': 240584.95}, {'field': 'total_amount', 'old_value': 240284.89, 'new_value': 240584.95}]
2025-05-18 18:00:25,669 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MY9
2025-05-18 18:00:26,070 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9MY9
2025-05-18 18:00:26,070 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 611504.07, 'new_value': 472239.4}, {'field': 'offline_amount', 'old_value': 188469.15, 'new_value': 325057.41}, {'field': 'total_amount', 'old_value': 799973.22, 'new_value': 797296.81}]
2025-05-18 18:00:26,070 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M9A
2025-05-18 18:00:26,554 - INFO - 更新表单数据成功: FINST-K7666JC1G9VUKOV48KV9H6GZ0I0E2AGVFMW9M9A
2025-05-18 18:00:26,555 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20724.27, 'new_value': 18198.93}, {'field': 'offline_amount', 'old_value': 386254.5, 'new_value': 388825.84}, {'field': 'total_amount', 'old_value': 406978.77, 'new_value': 407024.77}]
2025-05-18 18:00:26,558 - INFO - 开始更新记录 - 表单实例ID: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M48
2025-05-18 18:00:27,058 - INFO - 更新表单数据成功: FINST-80B66291TKVUKRJOB0P6D8SV65GK2HJZFMW9M48
2025-05-18 18:00:27,059 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 173253.49, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 478079.19, 'new_value': 642010.0}, {'field': 'total_amount', 'old_value': 651332.68, 'new_value': 642010.0}]
2025-05-18 18:00:27,059 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MBH
2025-05-18 18:00:27,554 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MBH
2025-05-18 18:00:27,554 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 668542.19, 'new_value': 668537.19}, {'field': 'total_amount', 'old_value': 668542.19, 'new_value': 668537.19}]
2025-05-18 18:00:27,555 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MCI
2025-05-18 18:00:27,975 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MCI
2025-05-18 18:00:27,975 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 922632.28, 'new_value': 924888.35}, {'field': 'total_amount', 'old_value': 922632.28, 'new_value': 924888.35}]
2025-05-18 18:00:27,976 - INFO - 开始更新记录 - 表单实例ID: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MLJ
2025-05-18 18:00:28,393 - INFO - 更新表单数据成功: FINST-1OC66A91T6VUSGJSCIJIX7ZRH1BP3HN3GMW9MLJ
2025-05-18 18:00:28,393 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 242883.13, 'new_value': 118002.36}, {'field': 'offline_amount', 'old_value': 44925.57, 'new_value': 169626.01}, {'field': 'total_amount', 'old_value': 287808.7, 'new_value': 287628.37}]
2025-05-18 18:00:28,395 - INFO - 开始更新记录 - 表单实例ID: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M71
2025-05-18 18:00:28,820 - INFO - 更新表单数据成功: FINST-2XF66ID1KVVUAC3S96DE5BKSFCBH2NQ7GMW9M71
2025-05-18 18:00:28,820 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 101100.04, 'new_value': 11044.8}, {'field': 'offline_amount', 'old_value': 287461.71, 'new_value': 377663.71}, {'field': 'total_amount', 'old_value': 388561.75, 'new_value': 388708.51}]
2025-05-18 18:00:28,822 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MA7
2025-05-18 18:00:29,239 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MA7
2025-05-18 18:00:29,239 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3982348.72, 'new_value': 3982209.31}, {'field': 'total_amount', 'old_value': 3982348.72, 'new_value': 3982209.31}]
2025-05-18 18:00:29,240 - INFO - 开始更新记录 - 表单实例ID: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MG7
2025-05-18 18:00:29,614 - INFO - 更新表单数据成功: FINST-EEC66XC19KVUP6E07PSH0CZCJY6F30UBGMW9MG7
2025-05-18 18:00:29,614 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 93791.0, 'new_value': 78945.0}, {'field': 'offline_amount', 'old_value': 161832.0, 'new_value': 176384.0}, {'field': 'total_amount', 'old_value': 255623.0, 'new_value': 255329.0}]
2025-05-18 18:00:29,615 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MW1
2025-05-18 18:00:30,004 - INFO - 更新表单数据成功: FINST-V7966QC1PQVUM0VU80F2U9VU32PH3AVFGMW9MW1
2025-05-18 18:00:30,004 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 343209.03, 'new_value': 142729.96}, {'field': 'offline_amount', 'old_value': 286299.77, 'new_value': 475936.42}, {'field': 'total_amount', 'old_value': 629508.8, 'new_value': 618666.38}]
2025-05-18 18:00:30,006 - INFO - 日期 2025-04 处理完成 - 更新: 12 条，插入: 0 条，错误: 0 条
2025-05-18 18:00:30,006 - INFO - 开始处理日期: 2025-05
2025-05-18 18:00:30,006 - INFO - Request Parameters - Page 1:
2025-05-18 18:00:30,006 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 18:00:30,006 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 18:00:30,459 - INFO - Response - Page 1:
2025-05-18 18:00:30,660 - INFO - 第 1 页获取到 100 条记录
2025-05-18 18:00:30,660 - INFO - Request Parameters - Page 2:
2025-05-18 18:00:30,660 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 18:00:30,660 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 18:00:31,259 - INFO - Response - Page 2:
2025-05-18 18:00:31,459 - INFO - 第 2 页获取到 100 条记录
2025-05-18 18:00:31,459 - INFO - Request Parameters - Page 3:
2025-05-18 18:00:31,459 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 18:00:31,459 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 18:00:31,950 - INFO - Response - Page 3:
2025-05-18 18:00:32,150 - INFO - 第 3 页获取到 100 条记录
2025-05-18 18:00:32,150 - INFO - Request Parameters - Page 4:
2025-05-18 18:00:32,150 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 18:00:32,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 18:00:32,677 - INFO - Response - Page 4:
2025-05-18 18:00:32,877 - INFO - 第 4 页获取到 100 条记录
2025-05-18 18:00:32,877 - INFO - Request Parameters - Page 5:
2025-05-18 18:00:32,877 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 18:00:32,877 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 18:00:33,412 - INFO - Response - Page 5:
2025-05-18 18:00:33,614 - INFO - 第 5 页获取到 100 条记录
2025-05-18 18:00:33,614 - INFO - Request Parameters - Page 6:
2025-05-18 18:00:33,614 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 18:00:33,614 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 18:00:34,137 - INFO - Response - Page 6:
2025-05-18 18:00:34,337 - INFO - 第 6 页获取到 100 条记录
2025-05-18 18:00:34,337 - INFO - Request Parameters - Page 7:
2025-05-18 18:00:34,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 18:00:34,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 18:00:34,694 - INFO - Response - Page 7:
2025-05-18 18:00:34,894 - INFO - 第 7 页获取到 26 条记录
2025-05-18 18:00:34,894 - INFO - 查询完成，共获取到 626 条记录
2025-05-18 18:00:34,894 - INFO - 获取到 626 条表单数据
2025-05-18 18:00:34,906 - INFO - 当前日期 2025-05 有 626 条MySQL数据需要处理
2025-05-18 18:00:34,906 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAC
2025-05-18 18:00:35,290 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAC
2025-05-18 18:00:35,290 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51220.0, 'new_value': 82920.0}, {'field': 'total_amount', 'old_value': 51220.0, 'new_value': 82920.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 7}]
2025-05-18 18:00:35,291 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMYC
2025-05-18 18:00:35,685 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMYC
2025-05-18 18:00:35,686 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6319.0, 'new_value': 11599.0}, {'field': 'total_amount', 'old_value': 6319.0, 'new_value': 11599.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-05-18 18:00:35,689 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-18 18:00:36,124 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-18 18:00:36,124 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18984.6, 'new_value': 19929.5}, {'field': 'offline_amount', 'old_value': 14963.6, 'new_value': 15480.6}, {'field': 'total_amount', 'old_value': 33948.2, 'new_value': 35410.1}, {'field': 'order_count', 'old_value': 179, 'new_value': 188}]
2025-05-18 18:00:36,130 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-18 18:00:36,560 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-18 18:00:36,560 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 725113.0, 'new_value': 773003.0}, {'field': 'total_amount', 'old_value': 725113.0, 'new_value': 773003.0}, {'field': 'order_count', 'old_value': 3121, 'new_value': 3364}]
2025-05-18 18:00:36,560 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-18 18:00:36,913 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-18 18:00:36,913 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9254598.0, 'new_value': 9780789.0}, {'field': 'total_amount', 'old_value': 9254598.0, 'new_value': 9780789.0}, {'field': 'order_count', 'old_value': 27976, 'new_value': 29799}]
2025-05-18 18:00:36,913 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-18 18:00:37,350 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-18 18:00:37,350 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2495066.65, 'new_value': 2648665.06}, {'field': 'total_amount', 'old_value': 2495066.65, 'new_value': 2648665.06}, {'field': 'order_count', 'old_value': 4230, 'new_value': 4516}]
2025-05-18 18:00:37,350 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-18 18:00:37,853 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-18 18:00:37,853 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96303.23, 'new_value': 104228.56}, {'field': 'total_amount', 'old_value': 103742.87, 'new_value': 111668.2}, {'field': 'order_count', 'old_value': 7270, 'new_value': 7804}]
2025-05-18 18:00:37,853 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-18 18:00:38,408 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-18 18:00:38,408 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 194794.0, 'new_value': 208221.0}, {'field': 'total_amount', 'old_value': 194794.0, 'new_value': 208221.0}, {'field': 'order_count', 'old_value': 4157, 'new_value': 4469}]
2025-05-18 18:00:38,408 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-18 18:00:38,797 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-18 18:00:38,797 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41550.43, 'new_value': 47267.43}, {'field': 'total_amount', 'old_value': 71309.69, 'new_value': 77026.69}, {'field': 'order_count', 'old_value': 1626, 'new_value': 1627}]
2025-05-18 18:00:38,798 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-18 18:00:39,387 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-18 18:00:39,387 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35013.0, 'new_value': 38285.0}, {'field': 'total_amount', 'old_value': 47155.0, 'new_value': 50427.0}, {'field': 'order_count', 'old_value': 68, 'new_value': 69}]
2025-05-18 18:00:39,388 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-18 18:00:39,799 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-18 18:00:39,800 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 116527.04, 'new_value': 125967.04}, {'field': 'total_amount', 'old_value': 116527.04, 'new_value': 125967.04}, {'field': 'order_count', 'old_value': 12054, 'new_value': 12993}]
2025-05-18 18:00:39,800 - INFO - 日期 2025-05 处理完成 - 更新: 11 条，插入: 0 条，错误: 0 条
2025-05-18 18:00:39,800 - INFO - 数据同步完成！更新: 23 条，插入: 0 条，错误: 0 条
2025-05-18 18:00:39,802 - INFO - =================同步完成====================
2025-05-18 21:00:02,088 - INFO - =================使用默认全量同步=============
2025-05-18 21:00:03,488 - INFO - MySQL查询成功，共获取 3295 条记录
2025-05-18 21:00:03,489 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-18 21:00:03,515 - INFO - 开始处理日期: 2025-01
2025-05-18 21:00:03,519 - INFO - Request Parameters - Page 1:
2025-05-18 21:00:03,519 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 21:00:03,519 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 21:00:04,413 - INFO - Response - Page 1:
2025-05-18 21:00:04,613 - INFO - 第 1 页获取到 100 条记录
2025-05-18 21:00:04,613 - INFO - Request Parameters - Page 2:
2025-05-18 21:00:04,613 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 21:00:04,613 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 21:00:05,510 - INFO - Response - Page 2:
2025-05-18 21:00:05,711 - INFO - 第 2 页获取到 100 条记录
2025-05-18 21:00:05,711 - INFO - Request Parameters - Page 3:
2025-05-18 21:00:05,711 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 21:00:05,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 21:00:06,262 - INFO - Response - Page 3:
2025-05-18 21:00:06,463 - INFO - 第 3 页获取到 100 条记录
2025-05-18 21:00:06,463 - INFO - Request Parameters - Page 4:
2025-05-18 21:00:06,463 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 21:00:06,463 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 21:00:06,955 - INFO - Response - Page 4:
2025-05-18 21:00:07,155 - INFO - 第 4 页获取到 100 条记录
2025-05-18 21:00:07,155 - INFO - Request Parameters - Page 5:
2025-05-18 21:00:07,155 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 21:00:07,155 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 21:00:07,614 - INFO - Response - Page 5:
2025-05-18 21:00:07,815 - INFO - 第 5 页获取到 100 条记录
2025-05-18 21:00:07,815 - INFO - Request Parameters - Page 6:
2025-05-18 21:00:07,815 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 21:00:07,815 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 21:00:08,382 - INFO - Response - Page 6:
2025-05-18 21:00:08,582 - INFO - 第 6 页获取到 100 条记录
2025-05-18 21:00:08,582 - INFO - Request Parameters - Page 7:
2025-05-18 21:00:08,582 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 21:00:08,582 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 21:00:09,001 - INFO - Response - Page 7:
2025-05-18 21:00:09,202 - INFO - 第 7 页获取到 82 条记录
2025-05-18 21:00:09,202 - INFO - 查询完成，共获取到 682 条记录
2025-05-18 21:00:09,202 - INFO - 获取到 682 条表单数据
2025-05-18 21:00:09,214 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-18 21:00:09,226 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-18 21:00:09,226 - INFO - 开始处理日期: 2025-02
2025-05-18 21:00:09,226 - INFO - Request Parameters - Page 1:
2025-05-18 21:00:09,226 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 21:00:09,226 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 21:00:09,715 - INFO - Response - Page 1:
2025-05-18 21:00:09,915 - INFO - 第 1 页获取到 100 条记录
2025-05-18 21:00:09,915 - INFO - Request Parameters - Page 2:
2025-05-18 21:00:09,915 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 21:00:09,915 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 21:00:10,375 - INFO - Response - Page 2:
2025-05-18 21:00:10,575 - INFO - 第 2 页获取到 100 条记录
2025-05-18 21:00:10,576 - INFO - Request Parameters - Page 3:
2025-05-18 21:00:10,576 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 21:00:10,576 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 21:00:11,017 - INFO - Response - Page 3:
2025-05-18 21:00:11,217 - INFO - 第 3 页获取到 100 条记录
2025-05-18 21:00:11,217 - INFO - Request Parameters - Page 4:
2025-05-18 21:00:11,217 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 21:00:11,217 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 21:00:11,693 - INFO - Response - Page 4:
2025-05-18 21:00:11,893 - INFO - 第 4 页获取到 100 条记录
2025-05-18 21:00:11,893 - INFO - Request Parameters - Page 5:
2025-05-18 21:00:11,893 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 21:00:11,893 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 21:00:12,398 - INFO - Response - Page 5:
2025-05-18 21:00:12,598 - INFO - 第 5 页获取到 100 条记录
2025-05-18 21:00:12,598 - INFO - Request Parameters - Page 6:
2025-05-18 21:00:12,599 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 21:00:12,599 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 21:00:13,166 - INFO - Response - Page 6:
2025-05-18 21:00:13,366 - INFO - 第 6 页获取到 100 条记录
2025-05-18 21:00:13,366 - INFO - Request Parameters - Page 7:
2025-05-18 21:00:13,366 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 21:00:13,366 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 21:00:13,814 - INFO - Response - Page 7:
2025-05-18 21:00:14,015 - INFO - 第 7 页获取到 70 条记录
2025-05-18 21:00:14,015 - INFO - 查询完成，共获取到 670 条记录
2025-05-18 21:00:14,015 - INFO - 获取到 670 条表单数据
2025-05-18 21:00:14,027 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-18 21:00:14,039 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-18 21:00:14,039 - INFO - 开始处理日期: 2025-03
2025-05-18 21:00:14,039 - INFO - Request Parameters - Page 1:
2025-05-18 21:00:14,039 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 21:00:14,040 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 21:00:14,603 - INFO - Response - Page 1:
2025-05-18 21:00:14,803 - INFO - 第 1 页获取到 100 条记录
2025-05-18 21:00:14,803 - INFO - Request Parameters - Page 2:
2025-05-18 21:00:14,803 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 21:00:14,803 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 21:00:15,425 - INFO - Response - Page 2:
2025-05-18 21:00:15,625 - INFO - 第 2 页获取到 100 条记录
2025-05-18 21:00:15,625 - INFO - Request Parameters - Page 3:
2025-05-18 21:00:15,625 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 21:00:15,625 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 21:00:16,127 - INFO - Response - Page 3:
2025-05-18 21:00:16,327 - INFO - 第 3 页获取到 100 条记录
2025-05-18 21:00:16,327 - INFO - Request Parameters - Page 4:
2025-05-18 21:00:16,327 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 21:00:16,327 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 21:00:16,778 - INFO - Response - Page 4:
2025-05-18 21:00:16,979 - INFO - 第 4 页获取到 100 条记录
2025-05-18 21:00:16,979 - INFO - Request Parameters - Page 5:
2025-05-18 21:00:16,979 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 21:00:16,979 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 21:00:17,429 - INFO - Response - Page 5:
2025-05-18 21:00:17,629 - INFO - 第 5 页获取到 100 条记录
2025-05-18 21:00:17,629 - INFO - Request Parameters - Page 6:
2025-05-18 21:00:17,629 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 21:00:17,629 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 21:00:18,074 - INFO - Response - Page 6:
2025-05-18 21:00:18,274 - INFO - 第 6 页获取到 100 条记录
2025-05-18 21:00:18,274 - INFO - Request Parameters - Page 7:
2025-05-18 21:00:18,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 21:00:18,274 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 21:00:18,722 - INFO - Response - Page 7:
2025-05-18 21:00:18,923 - INFO - 第 7 页获取到 61 条记录
2025-05-18 21:00:18,923 - INFO - 查询完成，共获取到 661 条记录
2025-05-18 21:00:18,923 - INFO - 获取到 661 条表单数据
2025-05-18 21:00:18,936 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-18 21:00:18,947 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-18 21:00:18,947 - INFO - 开始处理日期: 2025-04
2025-05-18 21:00:18,948 - INFO - Request Parameters - Page 1:
2025-05-18 21:00:18,948 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 21:00:18,948 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 21:00:19,677 - INFO - Response - Page 1:
2025-05-18 21:00:19,877 - INFO - 第 1 页获取到 100 条记录
2025-05-18 21:00:19,877 - INFO - Request Parameters - Page 2:
2025-05-18 21:00:19,877 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 21:00:19,877 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 21:00:20,371 - INFO - Response - Page 2:
2025-05-18 21:00:20,572 - INFO - 第 2 页获取到 100 条记录
2025-05-18 21:00:20,572 - INFO - Request Parameters - Page 3:
2025-05-18 21:00:20,572 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 21:00:20,572 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 21:00:21,029 - INFO - Response - Page 3:
2025-05-18 21:00:21,229 - INFO - 第 3 页获取到 100 条记录
2025-05-18 21:00:21,229 - INFO - Request Parameters - Page 4:
2025-05-18 21:00:21,229 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 21:00:21,229 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 21:00:21,701 - INFO - Response - Page 4:
2025-05-18 21:00:21,902 - INFO - 第 4 页获取到 100 条记录
2025-05-18 21:00:21,902 - INFO - Request Parameters - Page 5:
2025-05-18 21:00:21,902 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 21:00:21,902 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 21:00:22,390 - INFO - Response - Page 5:
2025-05-18 21:00:22,590 - INFO - 第 5 页获取到 100 条记录
2025-05-18 21:00:22,590 - INFO - Request Parameters - Page 6:
2025-05-18 21:00:22,590 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 21:00:22,590 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 21:00:23,101 - INFO - Response - Page 6:
2025-05-18 21:00:23,301 - INFO - 第 6 页获取到 100 条记录
2025-05-18 21:00:23,301 - INFO - Request Parameters - Page 7:
2025-05-18 21:00:23,301 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 21:00:23,301 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 21:00:23,874 - INFO - Response - Page 7:
2025-05-18 21:00:24,074 - INFO - 第 7 页获取到 56 条记录
2025-05-18 21:00:24,074 - INFO - 查询完成，共获取到 656 条记录
2025-05-18 21:00:24,074 - INFO - 获取到 656 条表单数据
2025-05-18 21:00:24,086 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-18 21:00:24,097 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-18 21:00:24,097 - INFO - 开始处理日期: 2025-05
2025-05-18 21:00:24,098 - INFO - Request Parameters - Page 1:
2025-05-18 21:00:24,098 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 21:00:24,098 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 21:00:24,538 - INFO - Response - Page 1:
2025-05-18 21:00:24,739 - INFO - 第 1 页获取到 100 条记录
2025-05-18 21:00:24,739 - INFO - Request Parameters - Page 2:
2025-05-18 21:00:24,739 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 21:00:24,740 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 21:00:25,292 - INFO - Response - Page 2:
2025-05-18 21:00:25,493 - INFO - 第 2 页获取到 100 条记录
2025-05-18 21:00:25,493 - INFO - Request Parameters - Page 3:
2025-05-18 21:00:25,493 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 21:00:25,493 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 21:00:25,979 - INFO - Response - Page 3:
2025-05-18 21:00:26,180 - INFO - 第 3 页获取到 100 条记录
2025-05-18 21:00:26,180 - INFO - Request Parameters - Page 4:
2025-05-18 21:00:26,180 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 21:00:26,180 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 21:00:26,656 - INFO - Response - Page 4:
2025-05-18 21:00:26,856 - INFO - 第 4 页获取到 100 条记录
2025-05-18 21:00:26,856 - INFO - Request Parameters - Page 5:
2025-05-18 21:00:26,856 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 21:00:26,856 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 21:00:27,377 - INFO - Response - Page 5:
2025-05-18 21:00:27,578 - INFO - 第 5 页获取到 100 条记录
2025-05-18 21:00:27,578 - INFO - Request Parameters - Page 6:
2025-05-18 21:00:27,578 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 21:00:27,578 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 21:00:28,021 - INFO - Response - Page 6:
2025-05-18 21:00:28,222 - INFO - 第 6 页获取到 100 条记录
2025-05-18 21:00:28,222 - INFO - Request Parameters - Page 7:
2025-05-18 21:00:28,222 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-18 21:00:28,222 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-18 21:00:28,639 - INFO - Response - Page 7:
2025-05-18 21:00:28,840 - INFO - 第 7 页获取到 26 条记录
2025-05-18 21:00:28,840 - INFO - 查询完成，共获取到 626 条记录
2025-05-18 21:00:28,840 - INFO - 获取到 626 条表单数据
2025-05-18 21:00:28,852 - INFO - 当前日期 2025-05 有 626 条MySQL数据需要处理
2025-05-18 21:00:28,855 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM6H2
2025-05-18 21:00:29,208 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM6H2
2025-05-18 21:00:29,208 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9083.5, 'new_value': 9249.5}, {'field': 'total_amount', 'old_value': 9793.5, 'new_value': 9959.5}, {'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-05-18 21:00:29,209 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0L
2025-05-18 21:00:29,579 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0L
2025-05-18 21:00:29,580 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 864564.94, 'new_value': 873664.94}, {'field': 'total_amount', 'old_value': 864564.94, 'new_value': 873664.94}, {'field': 'order_count', 'old_value': 536, 'new_value': 540}]
2025-05-18 21:00:29,580 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4L
2025-05-18 21:00:29,980 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4L
2025-05-18 21:00:29,980 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1776.2, 'new_value': 2991.1}, {'field': 'total_amount', 'old_value': 1776.2, 'new_value': 2991.1}, {'field': 'order_count', 'old_value': 163, 'new_value': 175}]
2025-05-18 21:00:29,981 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBL
2025-05-18 21:00:30,403 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMBL
2025-05-18 21:00:30,404 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30229.0, 'new_value': 32109.0}, {'field': 'total_amount', 'old_value': 30229.0, 'new_value': 32109.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-05-18 21:00:30,404 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGL
2025-05-18 21:00:30,832 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGL
2025-05-18 21:00:30,832 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28145.8, 'new_value': 30705.8}, {'field': 'total_amount', 'old_value': 28145.8, 'new_value': 30705.8}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-05-18 21:00:30,833 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-18 21:00:31,292 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-18 21:00:31,292 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4225877.0, 'new_value': 4342220.0}, {'field': 'total_amount', 'old_value': 4225877.0, 'new_value': 4342220.0}, {'field': 'order_count', 'old_value': 71950, 'new_value': 73130}]
2025-05-18 21:00:31,293 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM8
2025-05-18 21:00:31,692 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM8
2025-05-18 21:00:31,693 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57885.74, 'new_value': 63795.74}, {'field': 'total_amount', 'old_value': 57885.74, 'new_value': 63795.74}, {'field': 'order_count', 'old_value': 1762, 'new_value': 1853}]
2025-05-18 21:00:31,693 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-18 21:00:32,181 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-18 21:00:32,181 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77758.0, 'new_value': 83731.0}, {'field': 'total_amount', 'old_value': 77758.0, 'new_value': 83731.0}, {'field': 'order_count', 'old_value': 2940, 'new_value': 3098}]
2025-05-18 21:00:32,182 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7M
2025-05-18 21:00:32,628 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7M
2025-05-18 21:00:32,628 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40000.0, 'new_value': 42640.0}, {'field': 'total_amount', 'old_value': 40000.0, 'new_value': 42640.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-05-18 21:00:32,629 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-18 21:00:33,052 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-18 21:00:33,052 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 281818.03, 'new_value': 291522.26}, {'field': 'total_amount', 'old_value': 281818.03, 'new_value': 291522.26}, {'field': 'order_count', 'old_value': 541, 'new_value': 567}]
2025-05-18 21:00:33,053 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-18 21:00:33,457 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-18 21:00:33,457 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50492.0, 'new_value': 58188.0}, {'field': 'total_amount', 'old_value': 50492.0, 'new_value': 58188.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 23}]
2025-05-18 21:00:33,458 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-18 21:00:33,908 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-18 21:00:33,908 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 562195.0, 'new_value': 598763.0}, {'field': 'total_amount', 'old_value': 562195.0, 'new_value': 598763.0}, {'field': 'order_count', 'old_value': 1194, 'new_value': 1272}]
2025-05-18 21:00:33,909 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-18 21:00:34,415 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-18 21:00:34,415 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 216700.0, 'new_value': 459700.0}, {'field': 'total_amount', 'old_value': 216700.0, 'new_value': 459700.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 46}]
2025-05-18 21:00:34,417 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMD
2025-05-18 21:00:34,827 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMD
2025-05-18 21:00:34,827 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2096.0, 'new_value': 2795.0}, {'field': 'total_amount', 'old_value': 7694.0, 'new_value': 8393.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 21}]
2025-05-18 21:00:34,830 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-18 21:00:35,351 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-18 21:00:35,351 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 109131.17, 'new_value': 116987.17}, {'field': 'total_amount', 'old_value': 109131.17, 'new_value': 116987.17}, {'field': 'order_count', 'old_value': 900, 'new_value': 956}]
2025-05-18 21:00:35,351 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRG
2025-05-18 21:00:35,736 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRG
2025-05-18 21:00:35,737 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5420.0, 'new_value': 9420.0}, {'field': 'total_amount', 'old_value': 5420.0, 'new_value': 9420.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-05-18 21:00:35,737 - INFO - 日期 2025-05 处理完成 - 更新: 16 条，插入: 0 条，错误: 0 条
2025-05-18 21:00:35,737 - INFO - 数据同步完成！更新: 16 条，插入: 0 条，错误: 0 条
2025-05-18 21:00:35,739 - INFO - =================同步完成====================
