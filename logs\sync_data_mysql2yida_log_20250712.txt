2025-07-12 01:30:33,696 - INFO - 使用默认增量同步（当天更新数据）
2025-07-12 01:30:33,696 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-12 01:30:33,696 - INFO - 查询参数: ('2025-07-12',)
2025-07-12 01:30:33,790 - INFO - MySQL查询成功，增量数据（日期: 2025-07-12），共获取 0 条记录
2025-07-12 01:30:33,790 - ERROR - 未获取到MySQL数据
2025-07-12 01:31:33,805 - INFO - 开始同步昨天与今天的销售数据: 2025-07-11 至 2025-07-12
2025-07-12 01:31:33,805 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-12 01:31:33,805 - INFO - 查询参数: ('2025-07-11', '2025-07-12')
2025-07-12 01:31:33,946 - INFO - MySQL查询成功，时间段: 2025-07-11 至 2025-07-12，共获取 87 条记录
2025-07-12 01:31:33,946 - INFO - 获取到 1 个日期需要处理: ['2025-07-11']
2025-07-12 01:31:33,946 - INFO - 开始处理日期: 2025-07-11
2025-07-12 01:31:33,946 - INFO - Request Parameters - Page 1:
2025-07-12 01:31:33,946 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 01:31:33,946 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 01:31:41,743 - INFO - Response - Page 1:
2025-07-12 01:31:41,743 - INFO - 第 1 页获取到 50 条记录
2025-07-12 01:31:42,258 - INFO - Request Parameters - Page 2:
2025-07-12 01:31:42,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 01:31:42,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 01:31:50,352 - ERROR - 处理日期 2025-07-11 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 67F62036-22F3-74EE-954F-6313E0952958 Response: {'code': 'ServiceUnavailable', 'requestid': '67F62036-22F3-74EE-954F-6313E0952958', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 67F62036-22F3-74EE-954F-6313E0952958)
2025-07-12 01:31:50,352 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-12 01:31:50,352 - INFO - 同步完成
2025-07-12 04:30:33,531 - INFO - 使用默认增量同步（当天更新数据）
2025-07-12 04:30:33,531 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-12 04:30:33,531 - INFO - 查询参数: ('2025-07-12',)
2025-07-12 04:30:33,671 - INFO - MySQL查询成功，增量数据（日期: 2025-07-12），共获取 10 条记录
2025-07-12 04:30:33,671 - INFO - 获取到 1 个日期需要处理: ['2025-07-11']
2025-07-12 04:30:33,671 - INFO - 开始处理日期: 2025-07-11
2025-07-12 04:30:33,671 - INFO - Request Parameters - Page 1:
2025-07-12 04:30:33,671 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 04:30:33,671 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 04:30:41,796 - ERROR - 处理日期 2025-07-11 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B60A78FD-F6C8-7F92-AD20-4F87CC88B29E Response: {'code': 'ServiceUnavailable', 'requestid': 'B60A78FD-F6C8-7F92-AD20-4F87CC88B29E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B60A78FD-F6C8-7F92-AD20-4F87CC88B29E)
2025-07-12 04:30:41,796 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-12 04:31:41,811 - INFO - 开始同步昨天与今天的销售数据: 2025-07-11 至 2025-07-12
2025-07-12 04:31:41,811 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-12 04:31:41,811 - INFO - 查询参数: ('2025-07-11', '2025-07-12')
2025-07-12 04:31:41,952 - INFO - MySQL查询成功，时间段: 2025-07-11 至 2025-07-12，共获取 112 条记录
2025-07-12 04:31:41,952 - INFO - 获取到 1 个日期需要处理: ['2025-07-11']
2025-07-12 04:31:41,952 - INFO - 开始处理日期: 2025-07-11
2025-07-12 04:31:41,952 - INFO - Request Parameters - Page 1:
2025-07-12 04:31:41,952 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 04:31:41,952 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 04:31:42,749 - INFO - Response - Page 1:
2025-07-12 04:31:42,749 - INFO - 第 1 页获取到 50 条记录
2025-07-12 04:31:43,249 - INFO - Request Parameters - Page 2:
2025-07-12 04:31:43,249 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 04:31:43,249 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 04:31:43,858 - INFO - Response - Page 2:
2025-07-12 04:31:43,858 - INFO - 第 2 页获取到 28 条记录
2025-07-12 04:31:44,358 - INFO - 查询完成，共获取到 78 条记录
2025-07-12 04:31:44,358 - INFO - 获取到 78 条表单数据
2025-07-12 04:31:44,358 - INFO - 当前日期 2025-07-11 有 107 条MySQL数据需要处理
2025-07-12 04:31:44,358 - INFO - 开始批量插入 29 条新记录
2025-07-12 04:31:44,608 - INFO - 批量插入响应状态码: 200
2025-07-12 04:31:44,608 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 11 Jul 2025 20:31:44 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1404', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E0E1D747-5166-74AA-BB31-DAEC08B81118', 'x-acs-trace-id': 'a05c68730f95e4d2e5dcd2c0c5f52ca5', 'etag': '10waTZklnlTQ65dDPlu7CpQ4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-12 04:31:44,608 - INFO - 批量插入响应体: {'result': ['FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMWC', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMXC', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMYC', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMZC', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCM0D', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCM1D', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCM2D', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCM3D', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCM4D', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCM5D', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCM6D', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCM7D', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCM8D', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCM9D', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMAD', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMBD', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMCD', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMDD', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMED', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMFD', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMGD', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMHD', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMID', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMJD', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMKD', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMLD', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMMD', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMND', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMOD']}
2025-07-12 04:31:44,608 - INFO - 批量插入表单数据成功，批次 1，共 29 条记录
2025-07-12 04:31:44,608 - INFO - 成功插入的数据ID: ['FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMWC', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMXC', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMYC', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMZC', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCM0D', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCM1D', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCM2D', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCM3D', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCM4D', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCM5D', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCM6D', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCM7D', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCM8D', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCM9D', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMAD', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMBD', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMCD', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMDD', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMED', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMFD', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMGD', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMHD', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMID', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMJD', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMKD', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMLD', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMMD', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMND', 'FINST-NDC66NB1CA2XFA1MFT3BH5KR5DFQ3DXKV9ZCMOD']
2025-07-12 04:31:49,624 - INFO - 批量插入完成，共 29 条记录
2025-07-12 04:31:49,624 - INFO - 日期 2025-07-11 处理完成 - 更新: 0 条，插入: 29 条，错误: 0 条
2025-07-12 04:31:49,624 - INFO - 数据同步完成！更新: 0 条，插入: 29 条，错误: 0 条
2025-07-12 04:31:49,624 - INFO - 同步完成
2025-07-12 07:30:33,849 - INFO - 使用默认增量同步（当天更新数据）
2025-07-12 07:30:33,849 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-12 07:30:33,849 - INFO - 查询参数: ('2025-07-12',)
2025-07-12 07:30:34,006 - INFO - MySQL查询成功，增量数据（日期: 2025-07-12），共获取 10 条记录
2025-07-12 07:30:34,006 - INFO - 获取到 1 个日期需要处理: ['2025-07-11']
2025-07-12 07:30:34,006 - INFO - 开始处理日期: 2025-07-11
2025-07-12 07:30:34,006 - INFO - Request Parameters - Page 1:
2025-07-12 07:30:34,006 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 07:30:34,006 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 07:30:42,131 - ERROR - 处理日期 2025-07-11 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C8637F92-FD7B-7E04-A5F5-118BC2EDA127 Response: {'code': 'ServiceUnavailable', 'requestid': 'C8637F92-FD7B-7E04-A5F5-118BC2EDA127', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C8637F92-FD7B-7E04-A5F5-118BC2EDA127)
2025-07-12 07:30:42,131 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-12 07:31:42,146 - INFO - 开始同步昨天与今天的销售数据: 2025-07-11 至 2025-07-12
2025-07-12 07:31:42,146 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-12 07:31:42,146 - INFO - 查询参数: ('2025-07-11', '2025-07-12')
2025-07-12 07:31:42,286 - INFO - MySQL查询成功，时间段: 2025-07-11 至 2025-07-12，共获取 112 条记录
2025-07-12 07:31:42,286 - INFO - 获取到 1 个日期需要处理: ['2025-07-11']
2025-07-12 07:31:42,286 - INFO - 开始处理日期: 2025-07-11
2025-07-12 07:31:42,286 - INFO - Request Parameters - Page 1:
2025-07-12 07:31:42,286 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 07:31:42,286 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 07:31:43,099 - INFO - Response - Page 1:
2025-07-12 07:31:43,099 - INFO - 第 1 页获取到 50 条记录
2025-07-12 07:31:43,599 - INFO - Request Parameters - Page 2:
2025-07-12 07:31:43,599 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 07:31:43,599 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 07:31:44,255 - INFO - Response - Page 2:
2025-07-12 07:31:44,255 - INFO - 第 2 页获取到 50 条记录
2025-07-12 07:31:44,755 - INFO - Request Parameters - Page 3:
2025-07-12 07:31:44,755 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 07:31:44,755 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 07:31:45,302 - INFO - Response - Page 3:
2025-07-12 07:31:45,302 - INFO - 第 3 页获取到 7 条记录
2025-07-12 07:31:45,818 - INFO - 查询完成，共获取到 107 条记录
2025-07-12 07:31:45,818 - INFO - 获取到 107 条表单数据
2025-07-12 07:31:45,818 - INFO - 当前日期 2025-07-11 有 107 条MySQL数据需要处理
2025-07-12 07:31:45,818 - INFO - 日期 2025-07-11 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 07:31:45,818 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 07:31:45,818 - INFO - 同步完成
2025-07-12 10:30:33,824 - INFO - 使用默认增量同步（当天更新数据）
2025-07-12 10:30:33,824 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-12 10:30:33,824 - INFO - 查询参数: ('2025-07-12',)
2025-07-12 10:30:33,981 - INFO - MySQL查询成功，增量数据（日期: 2025-07-12），共获取 142 条记录
2025-07-12 10:30:33,981 - INFO - 获取到 1 个日期需要处理: ['2025-07-11']
2025-07-12 10:30:33,981 - INFO - 开始处理日期: 2025-07-11
2025-07-12 10:30:33,981 - INFO - Request Parameters - Page 1:
2025-07-12 10:30:33,981 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 10:30:33,981 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 10:30:41,543 - INFO - Response - Page 1:
2025-07-12 10:30:41,543 - INFO - 第 1 页获取到 50 条记录
2025-07-12 10:30:42,043 - INFO - Request Parameters - Page 2:
2025-07-12 10:30:42,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 10:30:42,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 10:30:42,793 - INFO - Response - Page 2:
2025-07-12 10:30:42,793 - INFO - 第 2 页获取到 50 条记录
2025-07-12 10:30:43,293 - INFO - Request Parameters - Page 3:
2025-07-12 10:30:43,293 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 10:30:43,293 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 10:30:51,387 - ERROR - 处理日期 2025-07-11 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: ********-4000-7DF9-8A49-10CA103122C8 Response: {'code': 'ServiceUnavailable', 'requestid': '********-4000-7DF9-8A49-10CA103122C8', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: ********-4000-7DF9-8A49-10CA103122C8)
2025-07-12 10:30:51,387 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-12 10:31:51,402 - INFO - 开始同步昨天与今天的销售数据: 2025-07-11 至 2025-07-12
2025-07-12 10:31:51,402 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-12 10:31:51,402 - INFO - 查询参数: ('2025-07-11', '2025-07-12')
2025-07-12 10:31:51,558 - INFO - MySQL查询成功，时间段: 2025-07-11 至 2025-07-12，共获取 400 条记录
2025-07-12 10:31:51,558 - INFO - 获取到 1 个日期需要处理: ['2025-07-11']
2025-07-12 10:31:51,558 - INFO - 开始处理日期: 2025-07-11
2025-07-12 10:31:51,558 - INFO - Request Parameters - Page 1:
2025-07-12 10:31:51,558 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 10:31:51,558 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 10:31:52,293 - INFO - Response - Page 1:
2025-07-12 10:31:52,293 - INFO - 第 1 页获取到 50 条记录
2025-07-12 10:31:52,808 - INFO - Request Parameters - Page 2:
2025-07-12 10:31:52,808 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 10:31:52,808 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 10:31:53,496 - INFO - Response - Page 2:
2025-07-12 10:31:53,496 - INFO - 第 2 页获取到 50 条记录
2025-07-12 10:31:53,996 - INFO - Request Parameters - Page 3:
2025-07-12 10:31:53,996 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 10:31:53,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 10:31:54,496 - INFO - Response - Page 3:
2025-07-12 10:31:54,496 - INFO - 第 3 页获取到 7 条记录
2025-07-12 10:31:55,011 - INFO - 查询完成，共获取到 107 条记录
2025-07-12 10:31:55,011 - INFO - 获取到 107 条表单数据
2025-07-12 10:31:55,011 - INFO - 当前日期 2025-07-11 有 388 条MySQL数据需要处理
2025-07-12 10:31:55,011 - INFO - 开始批量插入 281 条新记录
2025-07-12 10:31:55,261 - INFO - 批量插入响应状态码: 200
2025-07-12 10:31:55,261 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 12 Jul 2025 02:31:55 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2462', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '25F81935-8CC5-77DE-A26C-9AB3D7D575E0', 'x-acs-trace-id': '8e2065f599f04175781f8d28e9cd7ca2', 'etag': '2YefoJ1TkY5tZws2Lb7wa8g2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-12 10:31:55,261 - INFO - 批量插入响应体: {'result': ['FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3IXRQMZCM832', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3IXRQMZCM932', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3IXRQMZCMA32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3IXRQMZCMB32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMC32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMD32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCME32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMF32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMG32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMH32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMI32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMJ32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMK32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCML32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMM32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMN32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMO32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMP32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMQ32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMR32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMS32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMT32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMU32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMV32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMW32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMX32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMY32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMZ32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCM042', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCM142', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCM242', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCM342', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCM442', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCM542', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCM642', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCM742', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCM842', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCM942', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMA42', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMB42', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMC42', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMD42', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCME42', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMF42', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMG42', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMH42', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMI42', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMJ42', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMK42', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCML42']}
2025-07-12 10:31:55,261 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-12 10:31:55,261 - INFO - 成功插入的数据ID: ['FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3IXRQMZCM832', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3IXRQMZCM932', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3IXRQMZCMA32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3IXRQMZCMB32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMC32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMD32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCME32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMF32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMG32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMH32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMI32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMJ32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMK32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCML32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMM32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMN32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMO32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMP32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMQ32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMR32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMS32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMT32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMU32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMV32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMW32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMX32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMY32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMZ32', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCM042', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCM142', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCM242', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCM342', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCM442', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCM542', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCM642', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCM742', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCM842', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCM942', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMA42', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMB42', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMC42', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMD42', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCME42', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMF42', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMG42', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMH42', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMI42', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMJ42', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCMK42', 'FINST-LLF66FD1W41X3EZ19NB0YAZH1HTH3JXRQMZCML42']
2025-07-12 10:32:00,527 - INFO - 批量插入响应状态码: 200
2025-07-12 10:32:00,527 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 12 Jul 2025 02:32:00 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '349A6069-C58B-757C-BEDF-F80453CC98C3', 'x-acs-trace-id': '686a366f68dde7a7c803d3f9b41aade0', 'etag': '2spCW6kAHkw+r0t+IMEsJxg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-12 10:32:00,527 - INFO - 批量插入响应体: {'result': ['FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCM75', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCM85', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCM95', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMA5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMB5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMC5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMD5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCME5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMF5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMG5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMH5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMI5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMJ5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMK5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCML5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMM5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMN5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMO5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMP5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMQ5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMR5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMS5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMT5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMU5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMV5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMW5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMX5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMY5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMZ5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCM06', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCM16', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCM26', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCM36', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCM46', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCM56', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCM66', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCM76', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCM86', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCM96', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMA6', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMB6', 'FINST-B9C660C1051XDUNODXLL44INELT22UZVQMZCMC6', 'FINST-B9C660C1051XDUNODXLL44INELT22UZVQMZCMD6', 'FINST-B9C660C1051XDUNODXLL44INELT22UZVQMZCME6', 'FINST-B9C660C1051XDUNODXLL44INELT22UZVQMZCMF6', 'FINST-B9C660C1051XDUNODXLL44INELT22UZVQMZCMG6', 'FINST-B9C660C1051XDUNODXLL44INELT22UZVQMZCMH6', 'FINST-B9C660C1051XDUNODXLL44INELT22UZVQMZCMI6', 'FINST-B9C660C1051XDUNODXLL44INELT22UZVQMZCMJ6', 'FINST-B9C660C1051XDUNODXLL44INELT22UZVQMZCMK6']}
2025-07-12 10:32:00,527 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-07-12 10:32:00,527 - INFO - 成功插入的数据ID: ['FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCM75', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCM85', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCM95', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMA5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMB5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMC5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMD5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCME5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMF5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMG5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMH5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMI5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMJ5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMK5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCML5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMM5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMN5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMO5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMP5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMQ5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMR5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMS5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMT5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMU5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMV5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMW5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMX5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMY5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMZ5', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCM06', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCM16', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCM26', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCM36', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCM46', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCM56', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCM66', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCM76', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCM86', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCM96', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMA6', 'FINST-B9C660C1051XDUNODXLL44INELT22TZVQMZCMB6', 'FINST-B9C660C1051XDUNODXLL44INELT22UZVQMZCMC6', 'FINST-B9C660C1051XDUNODXLL44INELT22UZVQMZCMD6', 'FINST-B9C660C1051XDUNODXLL44INELT22UZVQMZCME6', 'FINST-B9C660C1051XDUNODXLL44INELT22UZVQMZCMF6', 'FINST-B9C660C1051XDUNODXLL44INELT22UZVQMZCMG6', 'FINST-B9C660C1051XDUNODXLL44INELT22UZVQMZCMH6', 'FINST-B9C660C1051XDUNODXLL44INELT22UZVQMZCMI6', 'FINST-B9C660C1051XDUNODXLL44INELT22UZVQMZCMJ6', 'FINST-B9C660C1051XDUNODXLL44INELT22UZVQMZCMK6']
2025-07-12 10:32:05,793 - INFO - 批量插入响应状态码: 200
2025-07-12 10:32:05,793 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 12 Jul 2025 02:32:05 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2462', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '419AF308-339D-76EF-87C1-625B28AFBB75', 'x-acs-trace-id': '003dca10d73256d3e45b4556b884e188', 'etag': '2vVrObGUr8ezBmWcqgGgTIA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-12 10:32:05,793 - INFO - 批量插入响应体: {'result': ['FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCM6W1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCM7W1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCM8W1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCM9W1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMAW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMBW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMCW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMDW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMEW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMFW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMGW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMHW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMIW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMJW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMKW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMLW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMMW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMNW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMOW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMPW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMQW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMRW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMSW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMTW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMUW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMVW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMWW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMXW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMYW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMZW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCM0X1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCM1X1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCM2X1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCM3X1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCM4X1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCM5X1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCM6X1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCM7X1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCM8X1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCM9X1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMAX1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMBX1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMCX1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMDX1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMEX1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMFX1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMGX1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMHX1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMIX1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMJX1']}
2025-07-12 10:32:05,793 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-07-12 10:32:05,793 - INFO - 成功插入的数据ID: ['FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCM6W1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCM7W1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCM8W1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCM9W1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMAW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMBW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMCW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMDW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMEW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMFW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMGW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMHW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMIW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMJW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMKW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMLW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMMW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMNW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMOW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMPW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMQW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMRW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMSW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMTW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMUW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMVW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMWW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMXW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMYW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMZW1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCM0X1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCM1X1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCM2X1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCM3X1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCM4X1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCM5X1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCM6X1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCM7X1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCM8X1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCM9X1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMAX1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMBX1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMCX1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMDX1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMEX1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMFX1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMGX1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMHX1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMIX1', 'FINST-OIF66BA1F3YWRPID9MKSE7NUNYJB3120RMZCMJX1']
2025-07-12 10:32:11,011 - INFO - 批量插入响应状态码: 200
2025-07-12 10:32:11,011 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 12 Jul 2025 02:32:10 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '646FEB63-96C5-7AEA-8588-02033C42AF03', 'x-acs-trace-id': '8da1ca0861f455e08a4e8cab5c000490', 'etag': '2NWf1vLa71FY9ElHJI2kiAA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-12 10:32:11,011 - INFO - 批量插入响应体: {'result': ['FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMTV', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMUV', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMVV', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMWV', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMXV', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMYV', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMZV', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCM0W', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCM1W', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCM2W', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCM3W', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCM4W', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCM5W', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCM6W', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCM7W', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCM8W', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCM9W', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMAW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMBW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMCW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMDW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMEW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMFW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMGW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMHW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMIW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMJW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMKW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMLW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMMW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMNW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMOW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMPW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMQW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMRW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMSW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMTW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMUW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMVW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMWW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMXW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMYW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMZW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCM0X', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCM1X', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCM2X', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCM3X', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCM4X', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCM5X', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCM6X']}
2025-07-12 10:32:11,011 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-07-12 10:32:11,011 - INFO - 成功插入的数据ID: ['FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMTV', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMUV', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMVV', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMWV', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMXV', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMYV', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMZV', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCM0W', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCM1W', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCM2W', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCM3W', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCM4W', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCM5W', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCM6W', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCM7W', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCM8W', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCM9W', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMAW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMBW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMCW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMDW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMEW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMFW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMGW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMHW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMIW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMJW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMKW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMLW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMMW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMNW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMOW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMPW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMQW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMRW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMSW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMTW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMUW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMVW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMWW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMXW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMYW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCMZW', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCM0X', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCM1X', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCM2X', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCM3X', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCM4X', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCM5X', 'FINST-FQD66YB1G42X684PDQX4LBOOMXMC2934RMZCM6X']
2025-07-12 10:32:16,293 - INFO - 批量插入响应状态码: 200
2025-07-12 10:32:16,293 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 12 Jul 2025 02:32:16 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '274CABFE-A88F-76BF-A3FB-21754EC9B488', 'x-acs-trace-id': '5c16504f78e0b719e5bff630693e6ad5', 'etag': '2Z1cRJZ0oiPwEl1Wvh+PfvA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-12 10:32:16,293 - INFO - 批量插入响应体: {'result': ['FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCMU3', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCMV3', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCMW3', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCMX3', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCMY3', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCMZ3', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCM04', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCM14', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCM24', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCM34', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCM44', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCM54', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCM64', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCM74', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCM84', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCM94', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCMA4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCMB4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMC4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMD4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCME4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMF4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMG4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMH4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMI4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMJ4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMK4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCML4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMM4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMN4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMO4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMP4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMQ4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMR4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMS4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMT4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMU4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMV4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMW4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMX4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMY4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMZ4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCM05', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCM15', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCM25', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCM35', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCM45', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCM55', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCM65', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCM75']}
2025-07-12 10:32:16,293 - INFO - 批量插入表单数据成功，批次 5，共 50 条记录
2025-07-12 10:32:16,293 - INFO - 成功插入的数据ID: ['FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCMU3', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCMV3', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCMW3', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCMX3', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCMY3', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCMZ3', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCM04', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCM14', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCM24', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCM34', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCM44', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCM54', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCM64', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCM74', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCM84', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCM94', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCMA4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3H58RMZCMB4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMC4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMD4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCME4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMF4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMG4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMH4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMI4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMJ4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMK4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCML4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMM4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMN4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMO4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMP4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMQ4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMR4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMS4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMT4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMU4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMV4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMW4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMX4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMY4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMZ4', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCM05', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCM15', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCM25', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCM35', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCM45', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCM55', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCM65', 'FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCM75']
2025-07-12 10:32:21,542 - INFO - 批量插入响应状态码: 200
2025-07-12 10:32:21,542 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 12 Jul 2025 02:32:21 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1531', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B2437388-F439-74F9-9C70-2EB554CE94A9', 'x-acs-trace-id': '6add11b2ffe7a70c9bf61d780705cfc6', 'etag': '1BKH771ktS6aL+LzIkFttFw1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-12 10:32:21,542 - INFO - 批量插入响应体: {'result': ['FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCM801', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCM901', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMA01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMB01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMC01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMD01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCME01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMF01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMG01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMH01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMI01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMJ01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMK01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCML01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMM01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMN01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMO01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMP01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMQ01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMR01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMS01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMT01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMU01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMV01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMW01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMX01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMY01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMZ01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCM011', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCM111', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCM211']}
2025-07-12 10:32:21,542 - INFO - 批量插入表单数据成功，批次 6，共 31 条记录
2025-07-12 10:32:21,542 - INFO - 成功插入的数据ID: ['FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCM801', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCM901', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMA01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMB01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMC01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMD01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCME01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMF01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMG01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMH01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMI01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMJ01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMK01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCML01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMM01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMN01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMO01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMP01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMQ01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMR01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMS01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMT01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMU01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMV01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMW01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMX01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMY01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMZ01', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCM011', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCM111', 'FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCM211']
2025-07-12 10:32:26,558 - INFO - 批量插入完成，共 281 条记录
2025-07-12 10:32:26,558 - INFO - 日期 2025-07-11 处理完成 - 更新: 0 条，插入: 281 条，错误: 0 条
2025-07-12 10:32:26,558 - INFO - 数据同步完成！更新: 0 条，插入: 281 条，错误: 0 条
2025-07-12 10:32:26,558 - INFO - 同步完成
2025-07-12 13:30:33,799 - INFO - 使用默认增量同步（当天更新数据）
2025-07-12 13:30:33,799 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-12 13:30:33,799 - INFO - 查询参数: ('2025-07-12',)
2025-07-12 13:30:33,956 - INFO - MySQL查询成功，增量数据（日期: 2025-07-12），共获取 167 条记录
2025-07-12 13:30:33,956 - INFO - 获取到 1 个日期需要处理: ['2025-07-11']
2025-07-12 13:30:33,956 - INFO - 开始处理日期: 2025-07-11
2025-07-12 13:30:33,956 - INFO - Request Parameters - Page 1:
2025-07-12 13:30:33,956 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 13:30:33,956 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 13:30:41,909 - INFO - Response - Page 1:
2025-07-12 13:30:41,909 - INFO - 第 1 页获取到 50 条记录
2025-07-12 13:30:42,409 - INFO - Request Parameters - Page 2:
2025-07-12 13:30:42,409 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 13:30:42,409 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 13:30:50,518 - ERROR - 处理日期 2025-07-11 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F6260C41-1C53-7889-BD1D-FC479A97F687 Response: {'code': 'ServiceUnavailable', 'requestid': 'F6260C41-1C53-7889-BD1D-FC479A97F687', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F6260C41-1C53-7889-BD1D-FC479A97F687)
2025-07-12 13:30:50,518 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-12 13:31:50,533 - INFO - 开始同步昨天与今天的销售数据: 2025-07-11 至 2025-07-12
2025-07-12 13:31:50,533 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-12 13:31:50,533 - INFO - 查询参数: ('2025-07-11', '2025-07-12')
2025-07-12 13:31:50,674 - INFO - MySQL查询成功，时间段: 2025-07-11 至 2025-07-12，共获取 460 条记录
2025-07-12 13:31:50,674 - INFO - 获取到 1 个日期需要处理: ['2025-07-11']
2025-07-12 13:31:50,689 - INFO - 开始处理日期: 2025-07-11
2025-07-12 13:31:50,689 - INFO - Request Parameters - Page 1:
2025-07-12 13:31:50,689 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 13:31:50,689 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 13:31:51,502 - INFO - Response - Page 1:
2025-07-12 13:31:51,502 - INFO - 第 1 页获取到 50 条记录
2025-07-12 13:31:52,018 - INFO - Request Parameters - Page 2:
2025-07-12 13:31:52,018 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 13:31:52,018 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 13:31:52,689 - INFO - Response - Page 2:
2025-07-12 13:31:52,689 - INFO - 第 2 页获取到 50 条记录
2025-07-12 13:31:53,205 - INFO - Request Parameters - Page 3:
2025-07-12 13:31:53,205 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 13:31:53,205 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 13:31:53,939 - INFO - Response - Page 3:
2025-07-12 13:31:53,939 - INFO - 第 3 页获取到 50 条记录
2025-07-12 13:31:54,439 - INFO - Request Parameters - Page 4:
2025-07-12 13:31:54,439 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 13:31:54,439 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 13:31:55,205 - INFO - Response - Page 4:
2025-07-12 13:31:55,205 - INFO - 第 4 页获取到 50 条记录
2025-07-12 13:31:55,721 - INFO - Request Parameters - Page 5:
2025-07-12 13:31:55,721 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 13:31:55,721 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 13:31:56,408 - INFO - Response - Page 5:
2025-07-12 13:31:56,408 - INFO - 第 5 页获取到 50 条记录
2025-07-12 13:31:56,924 - INFO - Request Parameters - Page 6:
2025-07-12 13:31:56,924 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 13:31:56,924 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 13:31:57,596 - INFO - Response - Page 6:
2025-07-12 13:31:57,596 - INFO - 第 6 页获取到 50 条记录
2025-07-12 13:31:58,111 - INFO - Request Parameters - Page 7:
2025-07-12 13:31:58,111 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 13:31:58,111 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 13:31:58,799 - INFO - Response - Page 7:
2025-07-12 13:31:58,799 - INFO - 第 7 页获取到 50 条记录
2025-07-12 13:31:59,314 - INFO - Request Parameters - Page 8:
2025-07-12 13:31:59,314 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 13:31:59,314 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 13:31:59,986 - INFO - Response - Page 8:
2025-07-12 13:31:59,986 - INFO - 第 8 页获取到 38 条记录
2025-07-12 13:32:00,486 - INFO - 查询完成，共获取到 388 条记录
2025-07-12 13:32:00,486 - INFO - 获取到 388 条表单数据
2025-07-12 13:32:00,486 - INFO - 当前日期 2025-07-11 有 448 条MySQL数据需要处理
2025-07-12 13:32:00,502 - INFO - 开始更新记录 - 表单实例ID: FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCM211
2025-07-12 13:32:01,143 - INFO - 更新表单数据成功: FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCM211
2025-07-12 13:32:01,143 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29800.0, 'new_value': 59800.0}, {'field': 'total_amount', 'old_value': 29800.0, 'new_value': 59800.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 4}]
2025-07-12 13:32:01,143 - INFO - 开始更新记录 - 表单实例ID: FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMU01
2025-07-12 13:32:01,705 - INFO - 更新表单数据成功: FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMU01
2025-07-12 13:32:01,705 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 547.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 547.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 6}]
2025-07-12 13:32:01,705 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMM4
2025-07-12 13:32:02,205 - INFO - 更新表单数据成功: FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMM4
2025-07-12 13:32:02,205 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-07-12 13:32:02,205 - INFO - 开始更新记录 - 表单实例ID: FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCM901
2025-07-12 13:32:02,768 - INFO - 更新表单数据成功: FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCM901
2025-07-12 13:32:02,768 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2193.08}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2193.08}, {'field': 'order_count', 'old_value': 0, 'new_value': 92}]
2025-07-12 13:32:02,768 - INFO - 开始更新记录 - 表单实例ID: FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCM801
2025-07-12 13:32:03,361 - INFO - 更新表单数据成功: FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCM801
2025-07-12 13:32:03,361 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3301.52}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 3301.52}, {'field': 'order_count', 'old_value': 0, 'new_value': 146}]
2025-07-12 13:32:03,361 - INFO - 开始更新记录 - 表单实例ID: FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCME01
2025-07-12 13:32:03,830 - INFO - 更新表单数据成功: FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCME01
2025-07-12 13:32:03,830 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 300.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 300.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-07-12 13:32:03,830 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCML4
2025-07-12 13:32:04,377 - INFO - 更新表单数据成功: FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCML4
2025-07-12 13:32:04,377 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 6796.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 6796.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-07-12 13:32:04,377 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCM65
2025-07-12 13:32:04,892 - INFO - 更新表单数据成功: FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCM65
2025-07-12 13:32:04,892 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2390.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2390.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 211}]
2025-07-12 13:32:04,892 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMY4
2025-07-12 13:32:05,471 - INFO - 更新表单数据成功: FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMY4
2025-07-12 13:32:05,471 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2990.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2990.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-07-12 13:32:05,471 - INFO - 开始更新记录 - 表单实例ID: FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMJ01
2025-07-12 13:32:06,002 - INFO - 更新表单数据成功: FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMJ01
2025-07-12 13:32:06,002 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 23000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 23000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 2}]
2025-07-12 13:32:06,002 - INFO - 开始批量插入 60 条新记录
2025-07-12 13:32:06,252 - INFO - 批量插入响应状态码: 200
2025-07-12 13:32:06,252 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 12 Jul 2025 05:32:06 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '8574E32E-448E-7CDA-ACE5-EBD2B0A0737C', 'x-acs-trace-id': '337419fb98cb3d4ca21dfcab052896ba', 'etag': '2Ng1MJp9wAHq9bOOte2IITA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-12 13:32:06,252 - INFO - 批量插入响应体: {'result': ['FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMHD', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMID', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMJD', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMKD', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMLD', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMMD', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMND', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMOD', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMPD', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMQD', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMRD', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMSD', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMTD', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMUD', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMVD', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMWD', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMXD', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMYD', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMZD', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCM0E', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCM1E', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCM2E', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCM3E', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCM4E', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCM5E', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCM6E', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCM7E', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCM8E', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCM9E', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMAE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMBE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMCE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMDE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMEE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMFE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMGE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMHE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMIE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMJE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMKE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMLE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMME', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMNE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMOE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMPE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMQE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMRE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMSE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3YSH6TZCMTE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3YSH6TZCMUE']}
2025-07-12 13:32:06,252 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-12 13:32:06,252 - INFO - 成功插入的数据ID: ['FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMHD', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMID', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMJD', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMKD', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMLD', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMMD', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMND', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMOD', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMPD', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMQD', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMRD', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMSD', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMTD', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMUD', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMVD', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMWD', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMXD', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMYD', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMZD', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCM0E', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCM1E', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCM2E', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCM3E', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCM4E', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCM5E', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCM6E', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCM7E', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCM8E', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCM9E', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMAE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMBE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMCE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMDE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMEE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMFE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMGE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMHE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMIE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMJE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMKE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMLE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMME', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMNE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMOE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMPE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMQE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMRE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3XSH6TZCMSE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3YSH6TZCMTE', 'FINST-MRA66WC1C41X2KB19VMHMBCBVERU3YSH6TZCMUE']
2025-07-12 13:32:11,424 - INFO - 批量插入响应状态码: 200
2025-07-12 13:32:11,424 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 12 Jul 2025 05:32:11 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '492', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '298595AE-EF1C-7C56-A88C-49062F8F0030', 'x-acs-trace-id': 'c54c1ddbeab2f09d3d6aa1bad72cf747', 'etag': '4+dBqwwgNVxszJ8t85R+TBg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-12 13:32:11,424 - INFO - 批量插入响应体: {'result': ['FINST-CK766D711E2XTL7WAOARTDEFME0T2JSL6TZCMN8', 'FINST-CK766D711E2XTL7WAOARTDEFME0T2JSL6TZCMO8', 'FINST-CK766D711E2XTL7WAOARTDEFME0T2JSL6TZCMP8', 'FINST-CK766D711E2XTL7WAOARTDEFME0T2JSL6TZCMQ8', 'FINST-CK766D711E2XTL7WAOARTDEFME0T2JSL6TZCMR8', 'FINST-CK766D711E2XTL7WAOARTDEFME0T2JSL6TZCMS8', 'FINST-CK766D711E2XTL7WAOARTDEFME0T2JSL6TZCMT8', 'FINST-CK766D711E2XTL7WAOARTDEFME0T2JSL6TZCMU8', 'FINST-CK766D711E2XTL7WAOARTDEFME0T2JSL6TZCMV8', 'FINST-CK766D711E2XTL7WAOARTDEFME0T2JSL6TZCMW8']}
2025-07-12 13:32:11,424 - INFO - 批量插入表单数据成功，批次 2，共 10 条记录
2025-07-12 13:32:11,424 - INFO - 成功插入的数据ID: ['FINST-CK766D711E2XTL7WAOARTDEFME0T2JSL6TZCMN8', 'FINST-CK766D711E2XTL7WAOARTDEFME0T2JSL6TZCMO8', 'FINST-CK766D711E2XTL7WAOARTDEFME0T2JSL6TZCMP8', 'FINST-CK766D711E2XTL7WAOARTDEFME0T2JSL6TZCMQ8', 'FINST-CK766D711E2XTL7WAOARTDEFME0T2JSL6TZCMR8', 'FINST-CK766D711E2XTL7WAOARTDEFME0T2JSL6TZCMS8', 'FINST-CK766D711E2XTL7WAOARTDEFME0T2JSL6TZCMT8', 'FINST-CK766D711E2XTL7WAOARTDEFME0T2JSL6TZCMU8', 'FINST-CK766D711E2XTL7WAOARTDEFME0T2JSL6TZCMV8', 'FINST-CK766D711E2XTL7WAOARTDEFME0T2JSL6TZCMW8']
2025-07-12 13:32:16,439 - INFO - 批量插入完成，共 60 条记录
2025-07-12 13:32:16,439 - INFO - 日期 2025-07-11 处理完成 - 更新: 10 条，插入: 60 条，错误: 0 条
2025-07-12 13:32:16,439 - INFO - 数据同步完成！更新: 10 条，插入: 60 条，错误: 0 条
2025-07-12 13:32:16,439 - INFO - 同步完成
2025-07-12 16:30:33,977 - INFO - 使用默认增量同步（当天更新数据）
2025-07-12 16:30:33,977 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-12 16:30:33,977 - INFO - 查询参数: ('2025-07-12',)
2025-07-12 16:30:34,133 - INFO - MySQL查询成功，增量数据（日期: 2025-07-12），共获取 170 条记录
2025-07-12 16:30:34,133 - INFO - 获取到 1 个日期需要处理: ['2025-07-11']
2025-07-12 16:30:34,133 - INFO - 开始处理日期: 2025-07-11
2025-07-12 16:30:34,133 - INFO - Request Parameters - Page 1:
2025-07-12 16:30:34,133 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 16:30:34,133 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 16:30:42,243 - ERROR - 处理日期 2025-07-11 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 757A1DE1-A3D3-74CD-8A2E-9210ADB4767D Response: {'code': 'ServiceUnavailable', 'requestid': '757A1DE1-A3D3-74CD-8A2E-9210ADB4767D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 757A1DE1-A3D3-74CD-8A2E-9210ADB4767D)
2025-07-12 16:30:42,243 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-12 16:31:42,258 - INFO - 开始同步昨天与今天的销售数据: 2025-07-11 至 2025-07-12
2025-07-12 16:31:42,258 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-12 16:31:42,258 - INFO - 查询参数: ('2025-07-11', '2025-07-12')
2025-07-12 16:31:42,414 - INFO - MySQL查询成功，时间段: 2025-07-11 至 2025-07-12，共获取 463 条记录
2025-07-12 16:31:42,414 - INFO - 获取到 1 个日期需要处理: ['2025-07-11']
2025-07-12 16:31:42,414 - INFO - 开始处理日期: 2025-07-11
2025-07-12 16:31:42,414 - INFO - Request Parameters - Page 1:
2025-07-12 16:31:42,414 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 16:31:42,414 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 16:31:49,742 - INFO - Response - Page 1:
2025-07-12 16:31:49,742 - INFO - 第 1 页获取到 50 条记录
2025-07-12 16:31:50,242 - INFO - Request Parameters - Page 2:
2025-07-12 16:31:50,242 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 16:31:50,242 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 16:31:50,930 - INFO - Response - Page 2:
2025-07-12 16:31:50,930 - INFO - 第 2 页获取到 50 条记录
2025-07-12 16:31:51,445 - INFO - Request Parameters - Page 3:
2025-07-12 16:31:51,445 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 16:31:51,445 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 16:31:52,242 - INFO - Response - Page 3:
2025-07-12 16:31:52,242 - INFO - 第 3 页获取到 50 条记录
2025-07-12 16:31:52,742 - INFO - Request Parameters - Page 4:
2025-07-12 16:31:52,742 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 16:31:52,742 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 16:31:53,445 - INFO - Response - Page 4:
2025-07-12 16:31:53,445 - INFO - 第 4 页获取到 50 条记录
2025-07-12 16:31:53,945 - INFO - Request Parameters - Page 5:
2025-07-12 16:31:53,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 16:31:53,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 16:31:54,648 - INFO - Response - Page 5:
2025-07-12 16:31:54,648 - INFO - 第 5 页获取到 50 条记录
2025-07-12 16:31:55,148 - INFO - Request Parameters - Page 6:
2025-07-12 16:31:55,148 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 16:31:55,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 16:31:55,914 - INFO - Response - Page 6:
2025-07-12 16:31:55,914 - INFO - 第 6 页获取到 50 条记录
2025-07-12 16:31:56,414 - INFO - Request Parameters - Page 7:
2025-07-12 16:31:56,414 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 16:31:56,414 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 16:31:57,086 - INFO - Response - Page 7:
2025-07-12 16:31:57,086 - INFO - 第 7 页获取到 50 条记录
2025-07-12 16:31:57,586 - INFO - Request Parameters - Page 8:
2025-07-12 16:31:57,586 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 16:31:57,586 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 16:31:58,273 - INFO - Response - Page 8:
2025-07-12 16:31:58,273 - INFO - 第 8 页获取到 50 条记录
2025-07-12 16:31:58,789 - INFO - Request Parameters - Page 9:
2025-07-12 16:31:58,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 16:31:58,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 16:31:59,508 - INFO - Response - Page 9:
2025-07-12 16:31:59,508 - INFO - 第 9 页获取到 48 条记录
2025-07-12 16:32:00,008 - INFO - 查询完成，共获取到 448 条记录
2025-07-12 16:32:00,008 - INFO - 获取到 448 条表单数据
2025-07-12 16:32:00,008 - INFO - 当前日期 2025-07-11 有 451 条MySQL数据需要处理
2025-07-12 16:32:00,023 - INFO - 开始更新记录 - 表单实例ID: FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMC01
2025-07-12 16:32:00,648 - INFO - 更新表单数据成功: FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMC01
2025-07-12 16:32:00,648 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 4311.55}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 4311.55}, {'field': 'order_count', 'old_value': 0, 'new_value': 120}]
2025-07-12 16:32:00,648 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMT4
2025-07-12 16:32:01,289 - INFO - 更新表单数据成功: FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMT4
2025-07-12 16:32:01,289 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3820.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 3820.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 22}]
2025-07-12 16:32:01,289 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCM75
2025-07-12 16:32:02,008 - INFO - 更新表单数据成功: FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCM75
2025-07-12 16:32:02,008 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1198.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1198.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 2}]
2025-07-12 16:32:02,008 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMK4
2025-07-12 16:32:02,695 - INFO - 更新表单数据成功: FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMK4
2025-07-12 16:32:02,695 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17845.0, 'new_value': 12300.0}, {'field': 'total_amount', 'old_value': 17845.0, 'new_value': 12300.0}, {'field': 'order_count', 'old_value': 154, 'new_value': 410}]
2025-07-12 16:32:02,695 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMU4
2025-07-12 16:32:03,273 - INFO - 更新表单数据成功: FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMU4
2025-07-12 16:32:03,273 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 5500.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 5500.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 60}]
2025-07-12 16:32:03,273 - INFO - 开始更新记录 - 表单实例ID: FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMO01
2025-07-12 16:32:03,836 - INFO - 更新表单数据成功: FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMO01
2025-07-12 16:32:03,836 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 13844.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 13844.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 2}]
2025-07-12 16:32:03,836 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMX4
2025-07-12 16:32:04,461 - INFO - 更新表单数据成功: FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMX4
2025-07-12 16:32:04,461 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 699.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 699.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-07-12 16:32:04,461 - INFO - 开始更新记录 - 表单实例ID: FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMF01
2025-07-12 16:32:05,039 - INFO - 更新表单数据成功: FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMF01
2025-07-12 16:32:05,039 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 12020.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 12020.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 4}]
2025-07-12 16:32:05,039 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCM05
2025-07-12 16:32:05,555 - INFO - 更新表单数据成功: FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCM05
2025-07-12 16:32:05,555 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 5000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 5000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-07-12 16:32:05,555 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMZ4
2025-07-12 16:32:06,133 - INFO - 更新表单数据成功: FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMZ4
2025-07-12 16:32:06,133 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2880.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2880.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-07-12 16:32:06,133 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMP4
2025-07-12 16:32:06,711 - INFO - 更新表单数据成功: FINST-W3B66L71BZ1XY51HBR3QY66R5QMS3I58RMZCMP4
2025-07-12 16:32:06,711 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 19878.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 19878.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 56}]
2025-07-12 16:32:06,711 - INFO - 开始更新记录 - 表单实例ID: FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMA01
2025-07-12 16:32:07,305 - INFO - 更新表单数据成功: FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMA01
2025-07-12 16:32:07,305 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 455.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 455.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 10}]
2025-07-12 16:32:07,305 - INFO - 开始更新记录 - 表单实例ID: FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMT01
2025-07-12 16:32:07,930 - INFO - 更新表单数据成功: FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMT01
2025-07-12 16:32:07,930 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 188.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 188.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-07-12 16:32:07,930 - INFO - 开始更新记录 - 表单实例ID: FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMH01
2025-07-12 16:32:08,445 - INFO - 更新表单数据成功: FINST-1V966BA1L41XCKB7D1LGCDNAFC5Z2I7CRMZCMH01
2025-07-12 16:32:08,445 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1200.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1200.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-07-12 16:32:08,445 - INFO - 开始批量插入 3 条新记录
2025-07-12 16:32:08,633 - INFO - 批量插入响应状态码: 200
2025-07-12 16:32:08,633 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 12 Jul 2025 08:32:08 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '159', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'ED2C8E75-D7DF-7496-B990-91137519112D', 'x-acs-trace-id': '0c82b8ead05d14d4615e960ec577dbfc', 'etag': '1zIwdn+JoyUhmiKjm2f/hcw9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-12 16:32:08,633 - INFO - 批量插入响应体: {'result': ['FINST-AJF66F71N8ZWU8GU5TFY6D5JTEZK2T01MZZCMDS1', 'FINST-AJF66F71N8ZWU8GU5TFY6D5JTEZK2T01MZZCMES1', 'FINST-AJF66F71N8ZWU8GU5TFY6D5JTEZK2T01MZZCMFS1']}
2025-07-12 16:32:08,633 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-07-12 16:32:08,633 - INFO - 成功插入的数据ID: ['FINST-AJF66F71N8ZWU8GU5TFY6D5JTEZK2T01MZZCMDS1', 'FINST-AJF66F71N8ZWU8GU5TFY6D5JTEZK2T01MZZCMES1', 'FINST-AJF66F71N8ZWU8GU5TFY6D5JTEZK2T01MZZCMFS1']
2025-07-12 16:32:13,648 - INFO - 批量插入完成，共 3 条记录
2025-07-12 16:32:13,648 - INFO - 日期 2025-07-11 处理完成 - 更新: 14 条，插入: 3 条，错误: 0 条
2025-07-12 16:32:13,648 - INFO - 数据同步完成！更新: 14 条，插入: 3 条，错误: 0 条
2025-07-12 16:32:13,648 - INFO - 同步完成
2025-07-12 19:30:34,504 - INFO - 使用默认增量同步（当天更新数据）
2025-07-12 19:30:34,504 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-12 19:30:34,504 - INFO - 查询参数: ('2025-07-12',)
2025-07-12 19:30:34,660 - INFO - MySQL查询成功，增量数据（日期: 2025-07-12），共获取 172 条记录
2025-07-12 19:30:34,660 - INFO - 获取到 2 个日期需要处理: ['2025-07-11', '2025-07-12']
2025-07-12 19:30:34,660 - INFO - 开始处理日期: 2025-07-11
2025-07-12 19:30:34,660 - INFO - Request Parameters - Page 1:
2025-07-12 19:30:34,660 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 19:30:34,660 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 19:30:41,960 - INFO - Response - Page 1:
2025-07-12 19:30:41,960 - INFO - 第 1 页获取到 50 条记录
2025-07-12 19:30:42,475 - INFO - Request Parameters - Page 2:
2025-07-12 19:30:42,475 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 19:30:42,475 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 19:30:43,195 - INFO - Response - Page 2:
2025-07-12 19:30:43,195 - INFO - 第 2 页获取到 50 条记录
2025-07-12 19:30:43,695 - INFO - Request Parameters - Page 3:
2025-07-12 19:30:43,695 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 19:30:43,695 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 19:30:51,792 - ERROR - 处理日期 2025-07-11 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E38B140D-2E1F-77E2-94C6-32A7782C781A Response: {'code': 'ServiceUnavailable', 'requestid': 'E38B140D-2E1F-77E2-94C6-32A7782C781A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E38B140D-2E1F-77E2-94C6-32A7782C781A)
2025-07-12 19:30:51,792 - INFO - 开始处理日期: 2025-07-12
2025-07-12 19:30:51,792 - INFO - Request Parameters - Page 1:
2025-07-12 19:30:51,792 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 19:30:51,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 19:30:52,229 - INFO - Response - Page 1:
2025-07-12 19:30:52,229 - INFO - 查询完成，共获取到 0 条记录
2025-07-12 19:30:52,229 - INFO - 获取到 0 条表单数据
2025-07-12 19:30:52,229 - INFO - 当前日期 2025-07-12 有 1 条MySQL数据需要处理
2025-07-12 19:30:52,229 - INFO - 开始批量插入 1 条新记录
2025-07-12 19:30:52,401 - INFO - 批量插入响应状态码: 200
2025-07-12 19:30:52,401 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 12 Jul 2025 11:30:52 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '61', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '1233060A-74E6-7141-8D82-C5D8F450F4C4', 'x-acs-trace-id': 'bbc1b85fa7d154df27ee10a502c899ac', 'etag': '64z162K02i/hyNk35VWoFMA1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-12 19:30:52,401 - INFO - 批量插入响应体: {'result': ['FINST-34B66L91DF1XKFHA8BL88CEJ8C0M3ACVZ50DMXR1']}
2025-07-12 19:30:52,401 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-12 19:30:52,401 - INFO - 成功插入的数据ID: ['FINST-34B66L91DF1XKFHA8BL88CEJ8C0M3ACVZ50DMXR1']
2025-07-12 19:30:57,419 - INFO - 批量插入完成，共 1 条记录
2025-07-12 19:30:57,419 - INFO - 日期 2025-07-12 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-12 19:30:57,419 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 1 条
2025-07-12 19:31:57,458 - INFO - 开始同步昨天与今天的销售数据: 2025-07-11 至 2025-07-12
2025-07-12 19:31:57,458 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-12 19:31:57,458 - INFO - 查询参数: ('2025-07-11', '2025-07-12')
2025-07-12 19:31:57,615 - INFO - MySQL查询成功，时间段: 2025-07-11 至 2025-07-12，共获取 465 条记录
2025-07-12 19:31:57,615 - INFO - 获取到 2 个日期需要处理: ['2025-07-11', '2025-07-12']
2025-07-12 19:31:57,615 - INFO - 开始处理日期: 2025-07-11
2025-07-12 19:31:57,615 - INFO - Request Parameters - Page 1:
2025-07-12 19:31:57,615 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 19:31:57,615 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 19:31:58,381 - INFO - Response - Page 1:
2025-07-12 19:31:58,381 - INFO - 第 1 页获取到 50 条记录
2025-07-12 19:31:58,896 - INFO - Request Parameters - Page 2:
2025-07-12 19:31:58,896 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 19:31:58,896 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 19:31:59,600 - INFO - Response - Page 2:
2025-07-12 19:31:59,600 - INFO - 第 2 页获取到 50 条记录
2025-07-12 19:32:00,116 - INFO - Request Parameters - Page 3:
2025-07-12 19:32:00,116 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 19:32:00,116 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 19:32:00,882 - INFO - Response - Page 3:
2025-07-12 19:32:00,882 - INFO - 第 3 页获取到 50 条记录
2025-07-12 19:32:01,397 - INFO - Request Parameters - Page 4:
2025-07-12 19:32:01,397 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 19:32:01,397 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 19:32:02,085 - INFO - Response - Page 4:
2025-07-12 19:32:02,085 - INFO - 第 4 页获取到 50 条记录
2025-07-12 19:32:02,601 - INFO - Request Parameters - Page 5:
2025-07-12 19:32:02,601 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 19:32:02,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 19:32:03,289 - INFO - Response - Page 5:
2025-07-12 19:32:03,289 - INFO - 第 5 页获取到 50 条记录
2025-07-12 19:32:03,805 - INFO - Request Parameters - Page 6:
2025-07-12 19:32:03,805 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 19:32:03,805 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 19:32:04,555 - INFO - Response - Page 6:
2025-07-12 19:32:04,555 - INFO - 第 6 页获取到 50 条记录
2025-07-12 19:32:05,071 - INFO - Request Parameters - Page 7:
2025-07-12 19:32:05,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 19:32:05,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 19:32:05,759 - INFO - Response - Page 7:
2025-07-12 19:32:05,759 - INFO - 第 7 页获取到 50 条记录
2025-07-12 19:32:06,274 - INFO - Request Parameters - Page 8:
2025-07-12 19:32:06,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 19:32:06,274 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 19:32:07,040 - INFO - Response - Page 8:
2025-07-12 19:32:07,040 - INFO - 第 8 页获取到 50 条记录
2025-07-12 19:32:07,556 - INFO - Request Parameters - Page 9:
2025-07-12 19:32:07,556 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 19:32:07,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 19:32:08,275 - INFO - Response - Page 9:
2025-07-12 19:32:08,275 - INFO - 第 9 页获取到 50 条记录
2025-07-12 19:32:08,791 - INFO - Request Parameters - Page 10:
2025-07-12 19:32:08,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 19:32:08,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 19:32:09,244 - INFO - Response - Page 10:
2025-07-12 19:32:09,244 - INFO - 第 10 页获取到 1 条记录
2025-07-12 19:32:09,760 - INFO - 查询完成，共获取到 451 条记录
2025-07-12 19:32:09,760 - INFO - 获取到 451 条表单数据
2025-07-12 19:32:09,760 - INFO - 当前日期 2025-07-11 有 452 条MySQL数据需要处理
2025-07-12 19:32:09,776 - INFO - 开始批量插入 1 条新记录
2025-07-12 19:32:09,948 - INFO - 批量插入响应状态码: 200
2025-07-12 19:32:09,948 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 12 Jul 2025 11:32:09 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F12BEDD7-C4D6-7076-B39F-5583BB506573', 'x-acs-trace-id': 'b90ca90efd1a655aae5543186650ca19', 'etag': '6X4juDb/clPWOzBLYUaoYJQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-12 19:32:09,948 - INFO - 批量插入响应体: {'result': ['FINST-2TG66D91XA2X6QVH9E1RC7153DJ13O5J160DMCX']}
2025-07-12 19:32:09,948 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-12 19:32:09,948 - INFO - 成功插入的数据ID: ['FINST-2TG66D91XA2X6QVH9E1RC7153DJ13O5J160DMCX']
2025-07-12 19:32:14,965 - INFO - 批量插入完成，共 1 条记录
2025-07-12 19:32:14,965 - INFO - 日期 2025-07-11 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-12 19:32:14,965 - INFO - 开始处理日期: 2025-07-12
2025-07-12 19:32:14,965 - INFO - Request Parameters - Page 1:
2025-07-12 19:32:14,965 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 19:32:14,965 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 19:32:15,403 - INFO - Response - Page 1:
2025-07-12 19:32:15,403 - INFO - 第 1 页获取到 1 条记录
2025-07-12 19:32:15,919 - INFO - 查询完成，共获取到 1 条记录
2025-07-12 19:32:15,919 - INFO - 获取到 1 条表单数据
2025-07-12 19:32:15,919 - INFO - 当前日期 2025-07-12 有 1 条MySQL数据需要处理
2025-07-12 19:32:15,919 - INFO - 日期 2025-07-12 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 19:32:15,919 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 0 条
2025-07-12 19:32:15,919 - INFO - 同步完成
2025-07-12 22:30:35,426 - INFO - 使用默认增量同步（当天更新数据）
2025-07-12 22:30:35,426 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-12 22:30:35,426 - INFO - 查询参数: ('2025-07-12',)
2025-07-12 22:30:35,582 - INFO - MySQL查询成功，增量数据（日期: 2025-07-12），共获取 219 条记录
2025-07-12 22:30:35,582 - INFO - 获取到 2 个日期需要处理: ['2025-07-11', '2025-07-12']
2025-07-12 22:30:35,582 - INFO - 开始处理日期: 2025-07-11
2025-07-12 22:30:35,582 - INFO - Request Parameters - Page 1:
2025-07-12 22:30:35,582 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 22:30:35,582 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 22:30:43,710 - ERROR - 处理日期 2025-07-11 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E71CB89D-02DD-7C01-868C-0CE74C3B105C Response: {'code': 'ServiceUnavailable', 'requestid': 'E71CB89D-02DD-7C01-868C-0CE74C3B105C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E71CB89D-02DD-7C01-868C-0CE74C3B105C)
2025-07-12 22:30:43,710 - INFO - 开始处理日期: 2025-07-12
2025-07-12 22:30:43,710 - INFO - Request Parameters - Page 1:
2025-07-12 22:30:43,710 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 22:30:43,710 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 22:30:50,166 - INFO - Response - Page 1:
2025-07-12 22:30:50,166 - INFO - 第 1 页获取到 1 条记录
2025-07-12 22:30:50,682 - INFO - 查询完成，共获取到 1 条记录
2025-07-12 22:30:50,682 - INFO - 获取到 1 条表单数据
2025-07-12 22:30:50,682 - INFO - 当前日期 2025-07-12 有 45 条MySQL数据需要处理
2025-07-12 22:30:50,682 - INFO - 开始批量插入 44 条新记录
2025-07-12 22:30:50,916 - INFO - 批量插入响应状态码: 200
2025-07-12 22:30:50,916 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 12 Jul 2025 14:30:46 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2168', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3C10F704-F1AF-7935-83ED-1EB0CF137958', 'x-acs-trace-id': '9d3bf9b78a06ce7075b989dedabdae3d', 'etag': '29/3EYr4BIUfhx+RRv5qLdg8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-12 22:30:50,916 - INFO - 批量插入响应体: {'result': ['FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMJN1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMKN1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMLN1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMMN1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMNN1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMON1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMPN1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMQN1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMRN1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMSN1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMTN1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMUN1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMVN1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMWN1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMXN1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMYN1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMZN1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DM0O1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DM1O1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DM2O1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DM3O1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DM4O1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DM5O1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DM6O1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DM7O1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DM8O1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DM9O1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMAO1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMBO1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2J88FC0DMCO1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2J88FC0DMDO1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2J88FC0DMEO1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2J88FC0DMFO1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2J88FC0DMGO1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2J88FC0DMHO1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2J88FC0DMIO1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2J88FC0DMJO1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2J88FC0DMKO1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2J88FC0DMLO1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2J88FC0DMMO1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2J88FC0DMNO1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2J88FC0DMOO1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2J88FC0DMPO1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2J88FC0DMQO1']}
2025-07-12 22:30:50,916 - INFO - 批量插入表单数据成功，批次 1，共 44 条记录
2025-07-12 22:30:50,916 - INFO - 成功插入的数据ID: ['FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMJN1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMKN1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMLN1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMMN1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMNN1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMON1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMPN1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMQN1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMRN1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMSN1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMTN1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMUN1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMVN1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMWN1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMXN1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMYN1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMZN1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DM0O1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DM1O1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DM2O1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DM3O1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DM4O1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DM5O1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DM6O1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DM7O1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DM8O1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DM9O1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMAO1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2I88FC0DMBO1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2J88FC0DMCO1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2J88FC0DMDO1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2J88FC0DMEO1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2J88FC0DMFO1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2J88FC0DMGO1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2J88FC0DMHO1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2J88FC0DMIO1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2J88FC0DMJO1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2J88FC0DMKO1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2J88FC0DMLO1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2J88FC0DMMO1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2J88FC0DMNO1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2J88FC0DMOO1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2J88FC0DMPO1', 'FINST-2FD66I71S41XF8JP9CHYF57BEM5C2J88FC0DMQO1']
2025-07-12 22:30:55,934 - INFO - 批量插入完成，共 44 条记录
2025-07-12 22:30:55,934 - INFO - 日期 2025-07-12 处理完成 - 更新: 0 条，插入: 44 条，错误: 0 条
2025-07-12 22:30:55,934 - INFO - 数据同步完成！更新: 0 条，插入: 44 条，错误: 1 条
2025-07-12 22:31:55,973 - INFO - 开始同步昨天与今天的销售数据: 2025-07-11 至 2025-07-12
2025-07-12 22:31:55,973 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-12 22:31:55,973 - INFO - 查询参数: ('2025-07-11', '2025-07-12')
2025-07-12 22:31:56,130 - INFO - MySQL查询成功，时间段: 2025-07-11 至 2025-07-12，共获取 512 条记录
2025-07-12 22:31:56,130 - INFO - 获取到 2 个日期需要处理: ['2025-07-11', '2025-07-12']
2025-07-12 22:31:56,130 - INFO - 开始处理日期: 2025-07-11
2025-07-12 22:31:56,130 - INFO - Request Parameters - Page 1:
2025-07-12 22:31:56,130 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 22:31:56,130 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 22:31:56,943 - INFO - Response - Page 1:
2025-07-12 22:31:56,943 - INFO - 第 1 页获取到 50 条记录
2025-07-12 22:31:57,443 - INFO - Request Parameters - Page 2:
2025-07-12 22:31:57,443 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 22:31:57,443 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 22:31:58,131 - INFO - Response - Page 2:
2025-07-12 22:31:58,131 - INFO - 第 2 页获取到 50 条记录
2025-07-12 22:31:58,631 - INFO - Request Parameters - Page 3:
2025-07-12 22:31:58,631 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 22:31:58,631 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 22:31:59,303 - INFO - Response - Page 3:
2025-07-12 22:31:59,303 - INFO - 第 3 页获取到 50 条记录
2025-07-12 22:31:59,803 - INFO - Request Parameters - Page 4:
2025-07-12 22:31:59,803 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 22:31:59,803 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 22:32:00,491 - INFO - Response - Page 4:
2025-07-12 22:32:00,491 - INFO - 第 4 页获取到 50 条记录
2025-07-12 22:32:00,991 - INFO - Request Parameters - Page 5:
2025-07-12 22:32:00,991 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 22:32:00,991 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 22:32:01,694 - INFO - Response - Page 5:
2025-07-12 22:32:01,694 - INFO - 第 5 页获取到 50 条记录
2025-07-12 22:32:02,210 - INFO - Request Parameters - Page 6:
2025-07-12 22:32:02,210 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 22:32:02,210 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 22:32:02,992 - INFO - Response - Page 6:
2025-07-12 22:32:02,992 - INFO - 第 6 页获取到 50 条记录
2025-07-12 22:32:03,492 - INFO - Request Parameters - Page 7:
2025-07-12 22:32:03,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 22:32:03,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 22:32:04,149 - INFO - Response - Page 7:
2025-07-12 22:32:04,149 - INFO - 第 7 页获取到 50 条记录
2025-07-12 22:32:04,649 - INFO - Request Parameters - Page 8:
2025-07-12 22:32:04,649 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 22:32:04,649 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 22:32:05,321 - INFO - Response - Page 8:
2025-07-12 22:32:05,321 - INFO - 第 8 页获取到 50 条记录
2025-07-12 22:32:05,837 - INFO - Request Parameters - Page 9:
2025-07-12 22:32:05,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 22:32:05,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 22:32:06,571 - INFO - Response - Page 9:
2025-07-12 22:32:06,571 - INFO - 第 9 页获取到 50 条记录
2025-07-12 22:32:07,087 - INFO - Request Parameters - Page 10:
2025-07-12 22:32:07,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 22:32:07,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752163200000, 1752249599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 22:32:07,572 - INFO - Response - Page 10:
2025-07-12 22:32:07,572 - INFO - 第 10 页获取到 2 条记录
2025-07-12 22:32:08,088 - INFO - 查询完成，共获取到 452 条记录
2025-07-12 22:32:08,088 - INFO - 获取到 452 条表单数据
2025-07-12 22:32:08,088 - INFO - 当前日期 2025-07-11 有 452 条MySQL数据需要处理
2025-07-12 22:32:08,103 - INFO - 日期 2025-07-11 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 22:32:08,103 - INFO - 开始处理日期: 2025-07-12
2025-07-12 22:32:08,103 - INFO - Request Parameters - Page 1:
2025-07-12 22:32:08,103 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-12 22:32:08,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752249600000, 1752335999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-12 22:32:08,760 - INFO - Response - Page 1:
2025-07-12 22:32:08,760 - INFO - 第 1 页获取到 45 条记录
2025-07-12 22:32:09,260 - INFO - 查询完成，共获取到 45 条记录
2025-07-12 22:32:09,260 - INFO - 获取到 45 条表单数据
2025-07-12 22:32:09,260 - INFO - 当前日期 2025-07-12 有 45 条MySQL数据需要处理
2025-07-12 22:32:09,260 - INFO - 日期 2025-07-12 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 22:32:09,260 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-12 22:32:09,260 - INFO - 同步完成
