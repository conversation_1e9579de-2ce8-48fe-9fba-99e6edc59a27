2025-04-30 00:30:34,197 - INFO - 使用默认增量同步（当天更新数据）
2025-04-30 00:30:34,197 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 00:30:34,197 - INFO - 查询参数: ('2025-04-30',)
2025-04-30 00:30:34,244 - INFO - MySQL查询成功，增量数据（日期: 2025-04-30），共获取 0 条记录
2025-04-30 00:30:34,244 - ERROR - 未获取到MySQL数据
2025-04-30 00:31:34,316 - INFO - 开始同步昨天与今天的销售数据: 20250429 至 20250430
2025-04-30 00:31:34,316 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 00:31:34,316 - INFO - 查询参数: ('20250429', '20250430')
2025-04-30 00:31:34,363 - INFO - MySQL查询成功，时间段: 20250429 至 20250430，共获取 0 条记录
2025-04-30 00:31:34,363 - ERROR - 未获取到MySQL数据
2025-04-30 00:31:34,363 - INFO - 同步完成
2025-04-30 01:30:34,481 - INFO - 使用默认增量同步（当天更新数据）
2025-04-30 01:30:34,481 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 01:30:34,481 - INFO - 查询参数: ('2025-04-30',)
2025-04-30 01:30:34,528 - INFO - MySQL查询成功，增量数据（日期: 2025-04-30），共获取 0 条记录
2025-04-30 01:30:34,528 - ERROR - 未获取到MySQL数据
2025-04-30 01:31:34,600 - INFO - 开始同步昨天与今天的销售数据: 20250429 至 20250430
2025-04-30 01:31:34,600 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 01:31:34,600 - INFO - 查询参数: ('20250429', '20250430')
2025-04-30 01:31:34,647 - INFO - MySQL查询成功，时间段: 20250429 至 20250430，共获取 0 条记录
2025-04-30 01:31:34,647 - ERROR - 未获取到MySQL数据
2025-04-30 01:31:34,647 - INFO - 同步完成
2025-04-30 02:30:34,530 - INFO - 使用默认增量同步（当天更新数据）
2025-04-30 02:30:34,530 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 02:30:34,530 - INFO - 查询参数: ('2025-04-30',)
2025-04-30 02:30:34,593 - INFO - MySQL查询成功，增量数据（日期: 2025-04-30），共获取 0 条记录
2025-04-30 02:30:34,593 - ERROR - 未获取到MySQL数据
2025-04-30 02:31:34,665 - INFO - 开始同步昨天与今天的销售数据: 20250429 至 20250430
2025-04-30 02:31:34,665 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 02:31:34,665 - INFO - 查询参数: ('20250429', '20250430')
2025-04-30 02:31:34,712 - INFO - MySQL查询成功，时间段: 20250429 至 20250430，共获取 0 条记录
2025-04-30 02:31:34,712 - ERROR - 未获取到MySQL数据
2025-04-30 02:31:34,712 - INFO - 同步完成
2025-04-30 03:30:34,361 - INFO - 使用默认增量同步（当天更新数据）
2025-04-30 03:30:34,361 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 03:30:34,361 - INFO - 查询参数: ('2025-04-30',)
2025-04-30 03:30:34,423 - INFO - MySQL查询成功，增量数据（日期: 2025-04-30），共获取 0 条记录
2025-04-30 03:30:34,423 - ERROR - 未获取到MySQL数据
2025-04-30 03:31:34,495 - INFO - 开始同步昨天与今天的销售数据: 20250429 至 20250430
2025-04-30 03:31:34,495 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 03:31:34,495 - INFO - 查询参数: ('20250429', '20250430')
2025-04-30 03:31:34,542 - INFO - MySQL查询成功，时间段: 20250429 至 20250430，共获取 0 条记录
2025-04-30 03:31:34,542 - ERROR - 未获取到MySQL数据
2025-04-30 03:31:34,542 - INFO - 同步完成
2025-04-30 04:30:34,363 - INFO - 使用默认增量同步（当天更新数据）
2025-04-30 04:30:34,363 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 04:30:34,363 - INFO - 查询参数: ('2025-04-30',)
2025-04-30 04:30:34,410 - INFO - MySQL查询成功，增量数据（日期: 2025-04-30），共获取 0 条记录
2025-04-30 04:30:34,410 - ERROR - 未获取到MySQL数据
2025-04-30 04:31:34,482 - INFO - 开始同步昨天与今天的销售数据: 20250429 至 20250430
2025-04-30 04:31:34,482 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 04:31:34,482 - INFO - 查询参数: ('20250429', '20250430')
2025-04-30 04:31:34,529 - INFO - MySQL查询成功，时间段: 20250429 至 20250430，共获取 0 条记录
2025-04-30 04:31:34,529 - ERROR - 未获取到MySQL数据
2025-04-30 04:31:34,529 - INFO - 同步完成
2025-04-30 05:30:34,459 - INFO - 使用默认增量同步（当天更新数据）
2025-04-30 05:30:34,459 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 05:30:34,459 - INFO - 查询参数: ('2025-04-30',)
2025-04-30 05:30:34,522 - INFO - MySQL查询成功，增量数据（日期: 2025-04-30），共获取 1 条记录
2025-04-30 05:30:34,522 - INFO - 获取到 1 个日期需要处理: ['2025-04-29']
2025-04-30 05:30:34,522 - INFO - 开始处理日期: 2025-04-29
2025-04-30 05:30:34,522 - INFO - Request Parameters - Page 1:
2025-04-30 05:30:34,522 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 05:30:34,522 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 05:30:42,638 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: EC5FF452-EA9B-7633-A51B-EC99B85A3816 Response: {'code': 'ServiceUnavailable', 'requestid': 'EC5FF452-EA9B-7633-A51B-EC99B85A3816', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: EC5FF452-EA9B-7633-A51B-EC99B85A3816)
2025-04-30 05:30:42,638 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-04-30 05:31:42,726 - INFO - 开始同步昨天与今天的销售数据: 20250429 至 20250430
2025-04-30 05:31:42,726 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 05:31:42,726 - INFO - 查询参数: ('20250429', '20250430')
2025-04-30 05:31:42,773 - INFO - MySQL查询成功，时间段: 20250429 至 20250430，共获取 0 条记录
2025-04-30 05:31:42,773 - ERROR - 未获取到MySQL数据
2025-04-30 05:31:42,773 - INFO - 同步完成
2025-04-30 06:30:34,352 - INFO - 使用默认增量同步（当天更新数据）
2025-04-30 06:30:34,352 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 06:30:34,352 - INFO - 查询参数: ('2025-04-30',)
2025-04-30 06:30:34,399 - INFO - MySQL查询成功，增量数据（日期: 2025-04-30），共获取 1 条记录
2025-04-30 06:30:34,399 - INFO - 获取到 1 个日期需要处理: ['2025-04-29']
2025-04-30 06:30:34,399 - INFO - 开始处理日期: 2025-04-29
2025-04-30 06:30:34,399 - INFO - Request Parameters - Page 1:
2025-04-30 06:30:34,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 06:30:34,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 06:30:42,531 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2F64FCC7-1DB3-78B3-B886-31AEE2821DA2 Response: {'code': 'ServiceUnavailable', 'requestid': '2F64FCC7-1DB3-78B3-B886-31AEE2821DA2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2F64FCC7-1DB3-78B3-B886-31AEE2821DA2)
2025-04-30 06:30:42,531 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-04-30 06:31:42,603 - INFO - 开始同步昨天与今天的销售数据: 20250429 至 20250430
2025-04-30 06:31:42,603 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 06:31:42,603 - INFO - 查询参数: ('20250429', '20250430')
2025-04-30 06:31:42,650 - INFO - MySQL查询成功，时间段: 20250429 至 20250430，共获取 0 条记录
2025-04-30 06:31:42,650 - ERROR - 未获取到MySQL数据
2025-04-30 06:31:42,650 - INFO - 同步完成
2025-04-30 07:30:34,510 - INFO - 使用默认增量同步（当天更新数据）
2025-04-30 07:30:34,510 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 07:30:34,510 - INFO - 查询参数: ('2025-04-30',)
2025-04-30 07:30:34,573 - INFO - MySQL查询成功，增量数据（日期: 2025-04-30），共获取 2 条记录
2025-04-30 07:30:34,573 - INFO - 获取到 1 个日期需要处理: ['2025-04-29']
2025-04-30 07:30:34,573 - INFO - 开始处理日期: 2025-04-29
2025-04-30 07:30:34,573 - INFO - Request Parameters - Page 1:
2025-04-30 07:30:34,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 07:30:34,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 07:30:42,705 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 795D2257-76D7-7286-BCB1-51DA39CA1EB8 Response: {'code': 'ServiceUnavailable', 'requestid': '795D2257-76D7-7286-BCB1-51DA39CA1EB8', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 795D2257-76D7-7286-BCB1-51DA39CA1EB8)
2025-04-30 07:30:42,705 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-04-30 07:31:42,777 - INFO - 开始同步昨天与今天的销售数据: 20250429 至 20250430
2025-04-30 07:31:42,777 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 07:31:42,777 - INFO - 查询参数: ('20250429', '20250430')
2025-04-30 07:31:42,824 - INFO - MySQL查询成功，时间段: 20250429 至 20250430，共获取 0 条记录
2025-04-30 07:31:42,824 - ERROR - 未获取到MySQL数据
2025-04-30 07:31:42,824 - INFO - 同步完成
2025-04-30 08:30:34,419 - INFO - 使用默认增量同步（当天更新数据）
2025-04-30 08:30:34,419 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 08:30:34,419 - INFO - 查询参数: ('2025-04-30',)
2025-04-30 08:30:34,466 - INFO - MySQL查询成功，增量数据（日期: 2025-04-30），共获取 12 条记录
2025-04-30 08:30:34,466 - INFO - 获取到 1 个日期需要处理: ['2025-04-29']
2025-04-30 08:30:34,466 - INFO - 开始处理日期: 2025-04-29
2025-04-30 08:30:34,466 - INFO - Request Parameters - Page 1:
2025-04-30 08:30:34,466 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 08:30:34,466 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 08:30:42,614 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 93424B64-7FA6-7A15-A0D3-4F011DB75678 Response: {'code': 'ServiceUnavailable', 'requestid': '93424B64-7FA6-7A15-A0D3-4F011DB75678', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 93424B64-7FA6-7A15-A0D3-4F011DB75678)
2025-04-30 08:30:42,614 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-04-30 08:31:42,686 - INFO - 开始同步昨天与今天的销售数据: 20250429 至 20250430
2025-04-30 08:31:42,686 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 08:31:42,686 - INFO - 查询参数: ('20250429', '20250430')
2025-04-30 08:31:42,732 - INFO - MySQL查询成功，时间段: 20250429 至 20250430，共获取 0 条记录
2025-04-30 08:31:42,732 - ERROR - 未获取到MySQL数据
2025-04-30 08:31:42,732 - INFO - 同步完成
2025-04-30 09:30:34,327 - INFO - 使用默认增量同步（当天更新数据）
2025-04-30 09:30:34,327 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 09:30:34,327 - INFO - 查询参数: ('2025-04-30',)
2025-04-30 09:30:34,374 - INFO - MySQL查询成功，增量数据（日期: 2025-04-30），共获取 89 条记录
2025-04-30 09:30:34,374 - INFO - 获取到 1 个日期需要处理: ['2025-04-29']
2025-04-30 09:30:34,374 - INFO - 开始处理日期: 2025-04-29
2025-04-30 09:30:34,390 - INFO - Request Parameters - Page 1:
2025-04-30 09:30:34,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 09:30:34,390 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 09:30:42,522 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A34A5CB3-1386-7CF8-AC00-98C30D9D44C6 Response: {'code': 'ServiceUnavailable', 'requestid': 'A34A5CB3-1386-7CF8-AC00-98C30D9D44C6', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A34A5CB3-1386-7CF8-AC00-98C30D9D44C6)
2025-04-30 09:30:42,522 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-04-30 09:31:42,594 - INFO - 开始同步昨天与今天的销售数据: 20250429 至 20250430
2025-04-30 09:31:42,594 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 09:31:42,594 - INFO - 查询参数: ('20250429', '20250430')
2025-04-30 09:31:42,641 - INFO - MySQL查询成功，时间段: 20250429 至 20250430，共获取 0 条记录
2025-04-30 09:31:42,641 - ERROR - 未获取到MySQL数据
2025-04-30 09:31:42,641 - INFO - 同步完成
2025-04-30 10:30:34,313 - INFO - 使用默认增量同步（当天更新数据）
2025-04-30 10:30:34,313 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 10:30:34,313 - INFO - 查询参数: ('2025-04-30',)
2025-04-30 10:30:34,360 - INFO - MySQL查询成功，增量数据（日期: 2025-04-30），共获取 141 条记录
2025-04-30 10:30:34,360 - INFO - 获取到 2 个日期需要处理: ['2025-04-29', '2025-04-30']
2025-04-30 10:30:34,360 - INFO - 开始处理日期: 2025-04-29
2025-04-30 10:30:34,376 - INFO - Request Parameters - Page 1:
2025-04-30 10:30:34,376 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 10:30:34,376 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 10:30:42,493 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D82B9EAD-ADB4-751D-949D-E2F182D009D2 Response: {'code': 'ServiceUnavailable', 'requestid': 'D82B9EAD-ADB4-751D-949D-E2F182D009D2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D82B9EAD-ADB4-751D-949D-E2F182D009D2)
2025-04-30 10:30:42,493 - INFO - 开始处理日期: 2025-04-30
2025-04-30 10:30:42,493 - INFO - Request Parameters - Page 1:
2025-04-30 10:30:42,493 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 10:30:42,493 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 10:30:50,641 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 353F89E0-892B-7A77-B1A3-D09136EABEEA Response: {'code': 'ServiceUnavailable', 'requestid': '353F89E0-892B-7A77-B1A3-D09136EABEEA', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 353F89E0-892B-7A77-B1A3-D09136EABEEA)
2025-04-30 10:30:50,641 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-04-30 10:31:50,713 - INFO - 开始同步昨天与今天的销售数据: 20250429 至 20250430
2025-04-30 10:31:50,713 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 10:31:50,713 - INFO - 查询参数: ('20250429', '20250430')
2025-04-30 10:31:50,760 - INFO - MySQL查询成功，时间段: 20250429 至 20250430，共获取 0 条记录
2025-04-30 10:31:50,760 - ERROR - 未获取到MySQL数据
2025-04-30 10:31:50,760 - INFO - 同步完成
2025-04-30 11:30:34,316 - INFO - 使用默认增量同步（当天更新数据）
2025-04-30 11:30:34,316 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 11:30:34,316 - INFO - 查询参数: ('2025-04-30',)
2025-04-30 11:30:34,363 - INFO - MySQL查询成功，增量数据（日期: 2025-04-30），共获取 149 条记录
2025-04-30 11:30:34,363 - INFO - 获取到 2 个日期需要处理: ['2025-04-29', '2025-04-30']
2025-04-30 11:30:34,378 - INFO - 开始处理日期: 2025-04-29
2025-04-30 11:30:34,378 - INFO - Request Parameters - Page 1:
2025-04-30 11:30:34,378 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 11:30:34,378 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 11:30:42,511 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 80DA1B51-AECF-7F9A-A88B-B9B917B71CFF Response: {'code': 'ServiceUnavailable', 'requestid': '80DA1B51-AECF-7F9A-A88B-B9B917B71CFF', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 80DA1B51-AECF-7F9A-A88B-B9B917B71CFF)
2025-04-30 11:30:42,511 - INFO - 开始处理日期: 2025-04-30
2025-04-30 11:30:42,511 - INFO - Request Parameters - Page 1:
2025-04-30 11:30:42,511 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 11:30:42,511 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 11:30:42,667 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 65F262DA-2416-78E2-9511-97ED5C1504BF Response: {'requestid': '65F262DA-2416-78E2-9511-97ED5C1504BF', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 65F262DA-2416-78E2-9511-97ED5C1504BF)
2025-04-30 11:30:42,667 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-04-30 11:31:42,739 - INFO - 开始同步昨天与今天的销售数据: 20250429 至 20250430
2025-04-30 11:31:42,739 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 11:31:42,739 - INFO - 查询参数: ('20250429', '20250430')
2025-04-30 11:31:42,786 - INFO - MySQL查询成功，时间段: 20250429 至 20250430，共获取 0 条记录
2025-04-30 11:31:42,786 - ERROR - 未获取到MySQL数据
2025-04-30 11:31:42,786 - INFO - 同步完成
2025-04-30 12:30:34,506 - INFO - 使用默认增量同步（当天更新数据）
2025-04-30 12:30:34,521 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 12:30:34,521 - INFO - 查询参数: ('2025-04-30',)
2025-04-30 12:30:34,568 - INFO - MySQL查询成功，增量数据（日期: 2025-04-30），共获取 153 条记录
2025-04-30 12:30:34,568 - INFO - 获取到 2 个日期需要处理: ['2025-04-29', '2025-04-30']
2025-04-30 12:30:34,568 - INFO - 开始处理日期: 2025-04-29
2025-04-30 12:30:34,584 - INFO - Request Parameters - Page 1:
2025-04-30 12:30:34,584 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 12:30:34,584 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 12:30:42,701 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 6FCB578B-0F30-754D-BBD7-9DE96CCD71A1 Response: {'code': 'ServiceUnavailable', 'requestid': '6FCB578B-0F30-754D-BBD7-9DE96CCD71A1', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 6FCB578B-0F30-754D-BBD7-9DE96CCD71A1)
2025-04-30 12:30:42,701 - INFO - 开始处理日期: 2025-04-30
2025-04-30 12:30:42,701 - INFO - Request Parameters - Page 1:
2025-04-30 12:30:42,701 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 12:30:42,701 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 12:30:42,857 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 9210AA0A-C42C-79D6-99B7-5EA18FAFAF03 Response: {'requestid': '9210AA0A-C42C-79D6-99B7-5EA18FAFAF03', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 9210AA0A-C42C-79D6-99B7-5EA18FAFAF03)
2025-04-30 12:30:42,857 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-04-30 12:31:42,929 - INFO - 开始同步昨天与今天的销售数据: 20250429 至 20250430
2025-04-30 12:31:42,929 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 12:31:42,929 - INFO - 查询参数: ('20250429', '20250430')
2025-04-30 12:31:42,976 - INFO - MySQL查询成功，时间段: 20250429 至 20250430，共获取 0 条记录
2025-04-30 12:31:42,976 - ERROR - 未获取到MySQL数据
2025-04-30 12:31:42,976 - INFO - 同步完成
2025-04-30 13:30:34,492 - INFO - 使用默认增量同步（当天更新数据）
2025-04-30 13:30:34,492 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 13:30:34,492 - INFO - 查询参数: ('2025-04-30',)
2025-04-30 13:30:34,555 - INFO - MySQL查询成功，增量数据（日期: 2025-04-30），共获取 153 条记录
2025-04-30 13:30:34,555 - INFO - 获取到 2 个日期需要处理: ['2025-04-29', '2025-04-30']
2025-04-30 13:30:34,555 - INFO - 开始处理日期: 2025-04-29
2025-04-30 13:30:34,555 - INFO - Request Parameters - Page 1:
2025-04-30 13:30:34,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 13:30:34,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 13:30:42,703 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 0F3E0924-F1BC-718B-95D4-70CAB0CC4260 Response: {'code': 'ServiceUnavailable', 'requestid': '0F3E0924-F1BC-718B-95D4-70CAB0CC4260', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 0F3E0924-F1BC-718B-95D4-70CAB0CC4260)
2025-04-30 13:30:42,703 - INFO - 开始处理日期: 2025-04-30
2025-04-30 13:30:42,703 - INFO - Request Parameters - Page 1:
2025-04-30 13:30:42,703 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 13:30:42,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 13:30:50,836 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A3A38D33-92C1-7EE1-A6E9-3C7BF8AD689A Response: {'code': 'ServiceUnavailable', 'requestid': 'A3A38D33-92C1-7EE1-A6E9-3C7BF8AD689A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A3A38D33-92C1-7EE1-A6E9-3C7BF8AD689A)
2025-04-30 13:30:50,836 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-04-30 13:31:50,907 - INFO - 开始同步昨天与今天的销售数据: 20250429 至 20250430
2025-04-30 13:31:50,907 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 13:31:50,907 - INFO - 查询参数: ('20250429', '20250430')
2025-04-30 13:31:50,954 - INFO - MySQL查询成功，时间段: 20250429 至 20250430，共获取 0 条记录
2025-04-30 13:31:50,954 - ERROR - 未获取到MySQL数据
2025-04-30 13:31:50,954 - INFO - 同步完成
2025-04-30 14:30:33,877 - INFO - 使用默认增量同步（当天更新数据）
2025-04-30 14:30:33,893 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 14:30:33,893 - INFO - 查询参数: ('2025-04-30',)
2025-04-30 14:30:33,940 - INFO - MySQL查询成功，增量数据（日期: 2025-04-30），共获取 160 条记录
2025-04-30 14:30:33,940 - INFO - 获取到 2 个日期需要处理: ['2025-04-29', '2025-04-30']
2025-04-30 14:30:33,955 - INFO - 开始处理日期: 2025-04-29
2025-04-30 14:30:33,955 - INFO - Request Parameters - Page 1:
2025-04-30 14:30:33,955 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 14:30:33,955 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 14:30:42,080 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 99981F36-EECD-749E-9B68-DE07873856BA Response: {'code': 'ServiceUnavailable', 'requestid': '99981F36-EECD-749E-9B68-DE07873856BA', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 99981F36-EECD-749E-9B68-DE07873856BA)
2025-04-30 14:30:42,080 - INFO - 开始处理日期: 2025-04-30
2025-04-30 14:30:42,080 - INFO - Request Parameters - Page 1:
2025-04-30 14:30:42,080 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 14:30:42,080 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 14:30:42,252 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 7DD5007F-F8CF-76C0-A762-4BAC8F6AC647 Response: {'requestid': '7DD5007F-F8CF-76C0-A762-4BAC8F6AC647', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 7DD5007F-F8CF-76C0-A762-4BAC8F6AC647)
2025-04-30 14:30:42,252 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-04-30 14:31:42,268 - INFO - 开始同步昨天与今天的销售数据: 20250429 至 20250430
2025-04-30 14:31:42,268 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 14:31:42,268 - INFO - 查询参数: ('20250429', '20250430')
2025-04-30 14:31:42,314 - INFO - MySQL查询成功，时间段: 20250429 至 20250430，共获取 0 条记录
2025-04-30 14:31:42,314 - ERROR - 未获取到MySQL数据
2025-04-30 14:31:42,314 - INFO - 同步完成
2025-04-30 15:30:33,918 - INFO - 使用默认增量同步（当天更新数据）
2025-04-30 15:30:33,918 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 15:30:33,918 - INFO - 查询参数: ('2025-04-30',)
2025-04-30 15:30:33,980 - INFO - MySQL查询成功，增量数据（日期: 2025-04-30），共获取 163 条记录
2025-04-30 15:30:33,980 - INFO - 获取到 2 个日期需要处理: ['2025-04-29', '2025-04-30']
2025-04-30 15:30:33,980 - INFO - 开始处理日期: 2025-04-29
2025-04-30 15:30:33,980 - INFO - Request Parameters - Page 1:
2025-04-30 15:30:33,980 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 15:30:33,980 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 15:30:42,105 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: ********-7C18-76C0-97FC-E17538D0E817 Response: {'code': 'ServiceUnavailable', 'requestid': '********-7C18-76C0-97FC-E17538D0E817', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: ********-7C18-76C0-97FC-E17538D0E817)
2025-04-30 15:30:42,105 - INFO - 开始处理日期: 2025-04-30
2025-04-30 15:30:42,105 - INFO - Request Parameters - Page 1:
2025-04-30 15:30:42,105 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 15:30:42,105 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 15:30:50,214 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: DEE24E71-643C-7B36-B69D-0BE0D3D680F2 Response: {'code': 'ServiceUnavailable', 'requestid': 'DEE24E71-643C-7B36-B69D-0BE0D3D680F2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: DEE24E71-643C-7B36-B69D-0BE0D3D680F2)
2025-04-30 15:30:50,214 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-04-30 15:31:50,230 - INFO - 开始同步昨天与今天的销售数据: 20250429 至 20250430
2025-04-30 15:31:50,230 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 15:31:50,230 - INFO - 查询参数: ('20250429', '20250430')
2025-04-30 15:31:50,277 - INFO - MySQL查询成功，时间段: 20250429 至 20250430，共获取 0 条记录
2025-04-30 15:31:50,277 - ERROR - 未获取到MySQL数据
2025-04-30 15:31:50,277 - INFO - 同步完成
2025-04-30 16:30:33,740 - INFO - 使用默认增量同步（当天更新数据）
2025-04-30 16:30:33,740 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 16:30:33,740 - INFO - 查询参数: ('2025-04-30',)
2025-04-30 16:30:33,802 - INFO - MySQL查询成功，增量数据（日期: 2025-04-30），共获取 164 条记录
2025-04-30 16:30:33,802 - INFO - 获取到 2 个日期需要处理: ['2025-04-29', '2025-04-30']
2025-04-30 16:30:33,802 - INFO - 开始处理日期: 2025-04-29
2025-04-30 16:30:33,818 - INFO - Request Parameters - Page 1:
2025-04-30 16:30:33,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 16:30:33,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 16:30:41,927 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 373ABEC5-C683-7DD9-BFA4-B8262825F541 Response: {'code': 'ServiceUnavailable', 'requestid': '373ABEC5-C683-7DD9-BFA4-B8262825F541', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 373ABEC5-C683-7DD9-BFA4-B8262825F541)
2025-04-30 16:30:41,927 - INFO - 开始处理日期: 2025-04-30
2025-04-30 16:30:41,927 - INFO - Request Parameters - Page 1:
2025-04-30 16:30:41,927 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 16:30:41,927 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 16:30:42,099 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 928D010D-7C96-7BA1-AC05-D9CB6462CEB0 Response: {'requestid': '928D010D-7C96-7BA1-AC05-D9CB6462CEB0', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 928D010D-7C96-7BA1-AC05-D9CB6462CEB0)
2025-04-30 16:30:42,099 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-04-30 16:31:42,114 - INFO - 开始同步昨天与今天的销售数据: 20250429 至 20250430
2025-04-30 16:31:42,114 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 16:31:42,114 - INFO - 查询参数: ('20250429', '20250430')
2025-04-30 16:31:42,161 - INFO - MySQL查询成功，时间段: 20250429 至 20250430，共获取 0 条记录
2025-04-30 16:31:42,161 - ERROR - 未获取到MySQL数据
2025-04-30 16:31:42,161 - INFO - 同步完成
2025-04-30 17:30:33,781 - INFO - 使用默认增量同步（当天更新数据）
2025-04-30 17:30:33,781 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 17:30:33,781 - INFO - 查询参数: ('2025-04-30',)
2025-04-30 17:30:33,843 - INFO - MySQL查询成功，增量数据（日期: 2025-04-30），共获取 164 条记录
2025-04-30 17:30:33,843 - INFO - 获取到 2 个日期需要处理: ['2025-04-29', '2025-04-30']
2025-04-30 17:30:33,843 - INFO - 开始处理日期: 2025-04-29
2025-04-30 17:30:33,843 - INFO - Request Parameters - Page 1:
2025-04-30 17:30:33,843 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 17:30:33,843 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 17:30:41,968 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: BE4824F3-E8AC-79F4-805E-2CDEB1ACF098 Response: {'code': 'ServiceUnavailable', 'requestid': 'BE4824F3-E8AC-79F4-805E-2CDEB1ACF098', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: BE4824F3-E8AC-79F4-805E-2CDEB1ACF098)
2025-04-30 17:30:41,968 - INFO - 开始处理日期: 2025-04-30
2025-04-30 17:30:41,968 - INFO - Request Parameters - Page 1:
2025-04-30 17:30:41,968 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 17:30:41,968 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 17:30:42,109 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: EF99F1CE-75B0-75D4-BB42-D4ECE7928DBC Response: {'requestid': 'EF99F1CE-75B0-75D4-BB42-D4ECE7928DBC', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: EF99F1CE-75B0-75D4-BB42-D4ECE7928DBC)
2025-04-30 17:30:42,109 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-04-30 17:31:42,124 - INFO - 开始同步昨天与今天的销售数据: 20250429 至 20250430
2025-04-30 17:31:42,124 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 17:31:42,124 - INFO - 查询参数: ('20250429', '20250430')
2025-04-30 17:31:42,171 - INFO - MySQL查询成功，时间段: 20250429 至 20250430，共获取 0 条记录
2025-04-30 17:31:42,171 - ERROR - 未获取到MySQL数据
2025-04-30 17:31:42,171 - INFO - 同步完成
2025-04-30 18:30:33,774 - INFO - 使用默认增量同步（当天更新数据）
2025-04-30 18:30:33,774 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 18:30:33,774 - INFO - 查询参数: ('2025-04-30',)
2025-04-30 18:30:33,837 - INFO - MySQL查询成功，增量数据（日期: 2025-04-30），共获取 171 条记录
2025-04-30 18:30:33,837 - INFO - 获取到 4 个日期需要处理: ['2025-04-04', '2025-04-05', '2025-04-29', '2025-04-30']
2025-04-30 18:30:33,837 - INFO - 开始处理日期: 2025-04-04
2025-04-30 18:30:33,837 - INFO - Request Parameters - Page 1:
2025-04-30 18:30:33,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 18:30:33,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1743696000000, 1743782399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 18:30:41,962 - ERROR - 处理日期 2025-04-04 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 0F51D12E-7D67-74B4-B614-9EE06C3579C3 Response: {'code': 'ServiceUnavailable', 'requestid': '0F51D12E-7D67-74B4-B614-9EE06C3579C3', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 0F51D12E-7D67-74B4-B614-9EE06C3579C3)
2025-04-30 18:30:41,962 - INFO - 开始处理日期: 2025-04-05
2025-04-30 18:30:41,962 - INFO - Request Parameters - Page 1:
2025-04-30 18:30:41,962 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 18:30:41,962 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1743782400000, 1743868799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 18:30:42,118 - ERROR - 处理日期 2025-04-05 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 228D460E-2AF3-745D-A51A-A65EFB9075B5 Response: {'requestid': '228D460E-2AF3-745D-A51A-A65EFB9075B5', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 228D460E-2AF3-745D-A51A-A65EFB9075B5)
2025-04-30 18:30:42,118 - INFO - 开始处理日期: 2025-04-29
2025-04-30 18:30:42,134 - INFO - Request Parameters - Page 1:
2025-04-30 18:30:42,134 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 18:30:42,134 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 18:30:42,274 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 75D2098C-5087-79A0-A3F9-18643F02E0CF Response: {'requestid': '75D2098C-5087-79A0-A3F9-18643F02E0CF', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 75D2098C-5087-79A0-A3F9-18643F02E0CF)
2025-04-30 18:30:42,290 - INFO - 开始处理日期: 2025-04-30
2025-04-30 18:30:42,290 - INFO - Request Parameters - Page 1:
2025-04-30 18:30:42,290 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 18:30:42,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 18:30:42,430 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: FC2F695A-06F4-7145-80A3-0F241AD52AB3 Response: {'requestid': 'FC2F695A-06F4-7145-80A3-0F241AD52AB3', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: FC2F695A-06F4-7145-80A3-0F241AD52AB3)
2025-04-30 18:30:42,430 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 4 条
2025-04-30 18:31:42,446 - INFO - 开始同步昨天与今天的销售数据: 20250429 至 20250430
2025-04-30 18:31:42,446 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 18:31:42,446 - INFO - 查询参数: ('20250429', '20250430')
2025-04-30 18:31:42,493 - INFO - MySQL查询成功，时间段: 20250429 至 20250430，共获取 0 条记录
2025-04-30 18:31:42,493 - ERROR - 未获取到MySQL数据
2025-04-30 18:31:42,493 - INFO - 同步完成
2025-04-30 19:30:33,862 - INFO - 使用默认增量同步（当天更新数据）
2025-04-30 19:30:33,862 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 19:30:33,862 - INFO - 查询参数: ('2025-04-30',)
2025-04-30 19:30:33,924 - INFO - MySQL查询成功，增量数据（日期: 2025-04-30），共获取 172 条记录
2025-04-30 19:30:33,924 - INFO - 获取到 4 个日期需要处理: ['2025-04-04', '2025-04-05', '2025-04-29', '2025-04-30']
2025-04-30 19:30:33,924 - INFO - 开始处理日期: 2025-04-04
2025-04-30 19:30:33,924 - INFO - Request Parameters - Page 1:
2025-04-30 19:30:33,924 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 19:30:33,924 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1743696000000, 1743782399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 19:30:42,034 - ERROR - 处理日期 2025-04-04 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: CFF44D60-DC38-7AFF-A73B-7C81BF609A99 Response: {'code': 'ServiceUnavailable', 'requestid': 'CFF44D60-DC38-7AFF-A73B-7C81BF609A99', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: CFF44D60-DC38-7AFF-A73B-7C81BF609A99)
2025-04-30 19:30:42,034 - INFO - 开始处理日期: 2025-04-05
2025-04-30 19:30:42,034 - INFO - Request Parameters - Page 1:
2025-04-30 19:30:42,034 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 19:30:42,034 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1743782400000, 1743868799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 19:30:42,174 - ERROR - 处理日期 2025-04-05 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 253B8F4F-09DF-74FC-9AF2-E4A67DCF884C Response: {'requestid': '253B8F4F-09DF-74FC-9AF2-E4A67DCF884C', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 253B8F4F-09DF-74FC-9AF2-E4A67DCF884C)
2025-04-30 19:30:42,174 - INFO - 开始处理日期: 2025-04-29
2025-04-30 19:30:42,174 - INFO - Request Parameters - Page 1:
2025-04-30 19:30:42,174 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 19:30:42,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 19:30:42,330 - ERROR - 处理日期 2025-04-29 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: DF73A91F-9F7E-758D-B554-AC004250EA49 Response: {'requestid': 'DF73A91F-9F7E-758D-B554-AC004250EA49', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: DF73A91F-9F7E-758D-B554-AC004250EA49)
2025-04-30 19:30:42,330 - INFO - 开始处理日期: 2025-04-30
2025-04-30 19:30:42,330 - INFO - Request Parameters - Page 1:
2025-04-30 19:30:42,330 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 19:30:42,330 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 19:30:42,518 - ERROR - 处理日期 2025-04-30 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: FB3922AA-4C31-78FC-85EA-DEB547C23B5D Response: {'requestid': 'FB3922AA-4C31-78FC-85EA-DEB547C23B5D', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: FB3922AA-4C31-78FC-85EA-DEB547C23B5D)
2025-04-30 19:30:42,518 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 4 条
2025-04-30 19:31:42,533 - INFO - 开始同步昨天与今天的销售数据: 20250429 至 20250430
2025-04-30 19:31:42,533 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 19:31:42,533 - INFO - 查询参数: ('20250429', '20250430')
2025-04-30 19:31:42,580 - INFO - MySQL查询成功，时间段: 20250429 至 20250430，共获取 0 条记录
2025-04-30 19:31:42,580 - ERROR - 未获取到MySQL数据
2025-04-30 19:31:42,580 - INFO - 同步完成
2025-04-30 20:30:33,699 - INFO - 使用默认增量同步（当天更新数据）
2025-04-30 20:30:33,699 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 20:30:33,699 - INFO - 查询参数: ('2025-04-30',)
2025-04-30 20:30:33,762 - INFO - MySQL查询成功，增量数据（日期: 2025-04-30），共获取 172 条记录
2025-04-30 20:30:33,762 - INFO - 获取到 4 个日期需要处理: ['2025-04-04', '2025-04-05', '2025-04-29', '2025-04-30']
2025-04-30 20:30:33,762 - INFO - 开始处理日期: 2025-04-04
2025-04-30 20:30:33,762 - INFO - Request Parameters - Page 1:
2025-04-30 20:30:33,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 20:30:33,762 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1743696000000, 1743782399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 20:30:41,887 - ERROR - 处理日期 2025-04-04 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 9A75BBB9-B8F5-7188-9997-CDF3C26DBE2E Response: {'code': 'ServiceUnavailable', 'requestid': '9A75BBB9-B8F5-7188-9997-CDF3C26DBE2E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 9A75BBB9-B8F5-7188-9997-CDF3C26DBE2E)
2025-04-30 20:30:41,887 - INFO - 开始处理日期: 2025-04-05
2025-04-30 20:30:41,887 - INFO - Request Parameters - Page 1:
2025-04-30 20:30:41,887 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 20:30:41,887 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1743782400000, 1743868799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 20:30:49,996 - ERROR - 处理日期 2025-04-05 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 4239D27E-FCD8-75D2-86E0-A7218678C801 Response: {'code': 'ServiceUnavailable', 'requestid': '4239D27E-FCD8-75D2-86E0-A7218678C801', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 4239D27E-FCD8-75D2-86E0-A7218678C801)
2025-04-30 20:30:49,996 - INFO - 开始处理日期: 2025-04-29
2025-04-30 20:30:49,996 - INFO - Request Parameters - Page 1:
2025-04-30 20:30:49,996 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 20:30:49,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 20:30:50,730 - INFO - Response - Page 1:
2025-04-30 20:30:50,730 - INFO - 第 1 页获取到 87 条记录
2025-04-30 20:30:50,933 - INFO - 查询完成，共获取到 87 条记录
2025-04-30 20:30:50,933 - INFO - 获取到 87 条表单数据
2025-04-30 20:30:50,933 - INFO - 当前日期 2025-04-29 有 167 条MySQL数据需要处理
2025-04-30 20:30:50,933 - INFO - 开始批量插入 167 条新记录
2025-04-30 20:30:51,246 - INFO - 批量插入响应状态码: 200
2025-04-30 20:30:51,246 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 30 Apr 2025 12:30:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'BF1EBA55-CAF0-7F05-B011-9C6295317F21', 'x-acs-trace-id': 'fa47d8a00414419010293217bb535105', 'etag': '4LJ4ecPGx6Qhura+3rTgWIw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-04-30 20:30:51,246 - INFO - 批量插入响应体: {'result': ['FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMV9', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMW9', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMX9', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMY9', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMZ9', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM0A', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM1A', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM2A', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM3A', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM4A', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM5A', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM6A', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM7A', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM8A', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM9A', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMAA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMBA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMCA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMDA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMEA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMFA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMGA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMHA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMIA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMJA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMKA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMLA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMMA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMNA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMOA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMPA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMQA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMRA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMSA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMTA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMUA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMVA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMWA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMXA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMYA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMZA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM0B', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM1B', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM2B', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM3B', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM4B', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM5B', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM6B', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM7B', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM8B', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM9B', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMAB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMBB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMCB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMDB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMEB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMFB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMGB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMHB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMIB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMJB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMKB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMLB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMMB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMNB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMOB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMPB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMQB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMRB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMSB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMTB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMUB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMVB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMWB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMXB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMYB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMZB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM0C', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM1C', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM2C', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM3C', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM4C', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM5C', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM6C', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM7C', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM8C', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM9C', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMAC', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMBC', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMCC', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMDC', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMEC', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMFC', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMGC', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMHC', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMIC', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMJC', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2WPTZW3AMKC', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2WPTZW3AMLC', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2WPTZW3AMMC']}
2025-04-30 20:30:51,246 - INFO - 批量插入表单数据成功，批次 1，共 100 条记录
2025-04-30 20:30:51,246 - INFO - 成功插入的数据ID: ['FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMV9', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMW9', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMX9', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMY9', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMZ9', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM0A', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM1A', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM2A', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM3A', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM4A', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM5A', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM6A', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM7A', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM8A', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM9A', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMAA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMBA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMCA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMDA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMEA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMFA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMGA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMHA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMIA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMJA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMKA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMLA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMMA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMNA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMOA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMPA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMQA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMRA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMSA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMTA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMUA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMVA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMWA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMXA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMYA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMZA', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM0B', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM1B', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM2B', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM3B', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM4B', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM5B', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM6B', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM7B', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM8B', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM9B', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMAB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMBB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMCB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMDB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMEB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMFB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMGB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMHB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMIB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMJB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMKB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMLB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMMB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMNB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMOB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMPB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMQB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMRB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMSB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMTB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMUB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMVB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMWB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMXB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMYB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMZB', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM0C', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM1C', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM2C', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM3C', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM4C', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM5C', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM6C', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM7C', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM8C', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AM9C', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMAC', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMBC', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMCC', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMDC', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMEC', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMFC', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMGC', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMHC', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMIC', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2VPTZW3AMJC', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2WPTZW3AMKC', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2WPTZW3AMLC', 'FINST-6PF6669183ZU250F8J56EB0ZG8CN2WPTZW3AMMC']
2025-04-30 20:30:56,480 - INFO - 批量插入响应状态码: 200
2025-04-30 20:30:56,480 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 30 Apr 2025 12:30:56 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '3228', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F17EFB85-DAA9-7544-A21C-31BAF12B2E79', 'x-acs-trace-id': '99623886af167a64ca7d3f7428c2677d', 'etag': '3MmtpTHVkLUUOSqNiR6/lvA8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-04-30 20:30:56,480 - INFO - 批量插入响应体: {'result': ['FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2ARXZW3AMQG', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2ARXZW3AMRG', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2ARXZW3AMSG', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2ARXZW3AMTG', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2ARXZW3AMUG', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2ARXZW3AMVG', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2ARXZW3AMWG', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2ARXZW3AMXG', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2ARXZW3AMYG', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2ARXZW3AMZG', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2ARXZW3AM0H', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2ARXZW3AM1H', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2ARXZW3AM2H', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2ARXZW3AM3H', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AM4H', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AM5H', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AM6H', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AM7H', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AM8H', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AM9H', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMAH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMBH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMCH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMDH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMEH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMFH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMGH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMHH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMIH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMJH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMKH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMLH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMMH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMNH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMOH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMPH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMQH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMRH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMSH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMTH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMUH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMVH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMWH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMXH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMYH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMZH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AM0I', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AM1I', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AM2I', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AM3I', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AM4I', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AM5I', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AM6I', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AM7I', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AM8I', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AM9I', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMAI', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMBI', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMCI', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMDI', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMEI', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMFI', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMGI', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMHI', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMII', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMJI', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMKI']}
2025-04-30 20:30:56,480 - INFO - 批量插入表单数据成功，批次 2，共 67 条记录
2025-04-30 20:30:56,480 - INFO - 成功插入的数据ID: ['FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2ARXZW3AMQG', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2ARXZW3AMRG', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2ARXZW3AMSG', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2ARXZW3AMTG', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2ARXZW3AMUG', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2ARXZW3AMVG', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2ARXZW3AMWG', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2ARXZW3AMXG', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2ARXZW3AMYG', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2ARXZW3AMZG', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2ARXZW3AM0H', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2ARXZW3AM1H', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2ARXZW3AM2H', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2ARXZW3AM3H', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AM4H', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AM5H', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AM6H', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AM7H', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AM8H', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AM9H', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMAH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMBH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMCH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMDH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMEH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMFH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMGH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMHH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMIH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMJH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMKH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMLH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMMH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMNH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMOH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMPH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMQH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMRH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMSH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMTH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMUH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMVH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMWH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMXH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMYH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMZH', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AM0I', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AM1I', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AM2I', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AM3I', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AM4I', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AM5I', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AM6I', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AM7I', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AM8I', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AM9I', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMAI', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMBI', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMCI', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMDI', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMEI', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMFI', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMGI', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMHI', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMII', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMJI', 'FINST-K7666JC1UAZUFOCSF3SZ1B2JJPLD2BRXZW3AMKI']
2025-04-30 20:31:01,496 - INFO - 批量插入完成，共 167 条记录
2025-04-30 20:31:01,496 - INFO - 日期 2025-04-29 处理完成 - 更新: 0 条，插入: 167 条，错误: 0 条
2025-04-30 20:31:01,496 - INFO - 开始处理日期: 2025-04-30
2025-04-30 20:31:01,496 - INFO - Request Parameters - Page 1:
2025-04-30 20:31:01,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 20:31:01,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 20:31:02,012 - INFO - Response - Page 1:
2025-04-30 20:31:02,012 - INFO - 查询完成，共获取到 0 条记录
2025-04-30 20:31:02,012 - INFO - 获取到 0 条表单数据
2025-04-30 20:31:02,012 - INFO - 当前日期 2025-04-30 有 3 条MySQL数据需要处理
2025-04-30 20:31:02,012 - INFO - 开始批量插入 3 条新记录
2025-04-30 20:31:02,183 - INFO - 批量插入响应状态码: 200
2025-04-30 20:31:02,183 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 30 Apr 2025 12:31:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '156', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6E8E158A-EFF7-7FE6-A924-370460FA63F4', 'x-acs-trace-id': 'ff5ede8b556eedcb2d4b626c1728d626', 'etag': '1MciY7b7w2eVD7ePssrCopg6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-04-30 20:31:02,183 - INFO - 批量插入响应体: {'result': ['FINST-7PF66BA1ANZUGSBABB1FIACT0EJR3N520X3AMS7', 'FINST-7PF66BA1ANZUGSBABB1FIACT0EJR3N520X3AMT7', 'FINST-7PF66BA1ANZUGSBABB1FIACT0EJR3N520X3AMU7']}
2025-04-30 20:31:02,183 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-04-30 20:31:02,183 - INFO - 成功插入的数据ID: ['FINST-7PF66BA1ANZUGSBABB1FIACT0EJR3N520X3AMS7', 'FINST-7PF66BA1ANZUGSBABB1FIACT0EJR3N520X3AMT7', 'FINST-7PF66BA1ANZUGSBABB1FIACT0EJR3N520X3AMU7']
2025-04-30 20:31:07,199 - INFO - 批量插入完成，共 3 条记录
2025-04-30 20:31:07,199 - INFO - 日期 2025-04-30 处理完成 - 更新: 0 条，插入: 3 条，错误: 0 条
2025-04-30 20:31:07,199 - INFO - 数据同步完成！更新: 0 条，插入: 170 条，错误: 2 条
2025-04-30 20:32:07,214 - INFO - 开始同步昨天与今天的销售数据: 20250429 至 20250430
2025-04-30 20:32:07,214 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 20:32:07,214 - INFO - 查询参数: ('20250429', '20250430')
2025-04-30 20:32:07,261 - INFO - MySQL查询成功，时间段: 20250429 至 20250430，共获取 0 条记录
2025-04-30 20:32:07,261 - ERROR - 未获取到MySQL数据
2025-04-30 20:32:07,261 - INFO - 同步完成
2025-04-30 21:30:33,708 - INFO - 使用默认增量同步（当天更新数据）
2025-04-30 21:30:33,708 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 21:30:33,708 - INFO - 查询参数: ('2025-04-30',)
2025-04-30 21:30:33,771 - INFO - MySQL查询成功，增量数据（日期: 2025-04-30），共获取 174 条记录
2025-04-30 21:30:33,771 - INFO - 获取到 4 个日期需要处理: ['2025-04-04', '2025-04-05', '2025-04-29', '2025-04-30']
2025-04-30 21:30:33,771 - INFO - 开始处理日期: 2025-04-04
2025-04-30 21:30:33,771 - INFO - Request Parameters - Page 1:
2025-04-30 21:30:33,771 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 21:30:33,771 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1743696000000, 1743782399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 21:30:41,896 - ERROR - 处理日期 2025-04-04 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 9B35BA57-1486-7C5A-BAA3-D47D1DDAB0D2 Response: {'code': 'ServiceUnavailable', 'requestid': '9B35BA57-1486-7C5A-BAA3-D47D1DDAB0D2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 9B35BA57-1486-7C5A-BAA3-D47D1DDAB0D2)
2025-04-30 21:30:41,896 - INFO - 开始处理日期: 2025-04-05
2025-04-30 21:30:41,896 - INFO - Request Parameters - Page 1:
2025-04-30 21:30:41,896 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 21:30:41,896 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1743782400000, 1743868799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 21:30:50,021 - ERROR - 处理日期 2025-04-05 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 4F920990-0BB9-7B74-AA56-355CFAD4EB83 Response: {'code': 'ServiceUnavailable', 'requestid': '4F920990-0BB9-7B74-AA56-355CFAD4EB83', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 4F920990-0BB9-7B74-AA56-355CFAD4EB83)
2025-04-30 21:30:50,021 - INFO - 开始处理日期: 2025-04-29
2025-04-30 21:30:50,021 - INFO - Request Parameters - Page 1:
2025-04-30 21:30:50,021 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 21:30:50,021 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 21:30:50,802 - INFO - Response - Page 1:
2025-04-30 21:30:50,802 - INFO - 第 1 页获取到 100 条记录
2025-04-30 21:30:51,005 - INFO - Request Parameters - Page 2:
2025-04-30 21:30:51,005 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 21:30:51,005 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 21:30:51,693 - INFO - Response - Page 2:
2025-04-30 21:30:51,693 - INFO - 第 2 页获取到 100 条记录
2025-04-30 21:30:51,896 - INFO - Request Parameters - Page 3:
2025-04-30 21:30:51,896 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 21:30:51,896 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 21:30:52,490 - INFO - Response - Page 3:
2025-04-30 21:30:52,490 - INFO - 第 3 页获取到 54 条记录
2025-04-30 21:30:52,693 - INFO - 查询完成，共获取到 254 条记录
2025-04-30 21:30:52,693 - INFO - 获取到 254 条表单数据
2025-04-30 21:30:52,693 - INFO - 当前日期 2025-04-29 有 167 条MySQL数据需要处理
2025-04-30 21:30:52,693 - INFO - 日期 2025-04-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-30 21:30:52,693 - INFO - 开始处理日期: 2025-04-30
2025-04-30 21:30:52,693 - INFO - Request Parameters - Page 1:
2025-04-30 21:30:52,693 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 21:30:52,693 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 21:30:53,130 - INFO - Response - Page 1:
2025-04-30 21:30:53,130 - INFO - 第 1 页获取到 3 条记录
2025-04-30 21:30:53,333 - INFO - 查询完成，共获取到 3 条记录
2025-04-30 21:30:53,333 - INFO - 获取到 3 条表单数据
2025-04-30 21:30:53,333 - INFO - 当前日期 2025-04-30 有 5 条MySQL数据需要处理
2025-04-30 21:30:53,333 - INFO - 开始批量插入 2 条新记录
2025-04-30 21:30:53,490 - INFO - 批量插入响应状态码: 200
2025-04-30 21:30:53,490 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 30 Apr 2025 13:30:53 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '106', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '8FE48844-63DB-7674-9A80-E8F1F0432741', 'x-acs-trace-id': '12b5acb82b1059548ad2a12aaa03d1df', 'etag': '1BLXLfqU24ycbb5GN+3tZ0g6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-04-30 21:30:53,490 - INFO - 批量插入响应体: {'result': ['FINST-2PF66KD1I21VR164BI2VWAFXEQXQ30915Z3AMA', 'FINST-2PF66KD1I21VR164BI2VWAFXEQXQ30915Z3AMB']}
2025-04-30 21:30:53,490 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-04-30 21:30:53,490 - INFO - 成功插入的数据ID: ['FINST-2PF66KD1I21VR164BI2VWAFXEQXQ30915Z3AMA', 'FINST-2PF66KD1I21VR164BI2VWAFXEQXQ30915Z3AMB']
2025-04-30 21:30:58,505 - INFO - 批量插入完成，共 2 条记录
2025-04-30 21:30:58,505 - INFO - 日期 2025-04-30 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-04-30 21:30:58,505 - INFO - 数据同步完成！更新: 0 条，插入: 2 条，错误: 2 条
2025-04-30 21:31:58,520 - INFO - 开始同步昨天与今天的销售数据: 20250429 至 20250430
2025-04-30 21:31:58,520 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 21:31:58,520 - INFO - 查询参数: ('20250429', '20250430')
2025-04-30 21:31:58,567 - INFO - MySQL查询成功，时间段: 20250429 至 20250430，共获取 0 条记录
2025-04-30 21:31:58,567 - ERROR - 未获取到MySQL数据
2025-04-30 21:31:58,567 - INFO - 同步完成
2025-04-30 22:30:33,733 - INFO - 使用默认增量同步（当天更新数据）
2025-04-30 22:30:33,733 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 22:30:33,733 - INFO - 查询参数: ('2025-04-30',)
2025-04-30 22:30:33,796 - INFO - MySQL查询成功，增量数据（日期: 2025-04-30），共获取 208 条记录
2025-04-30 22:30:33,796 - INFO - 获取到 4 个日期需要处理: ['2025-04-04', '2025-04-05', '2025-04-29', '2025-04-30']
2025-04-30 22:30:33,796 - INFO - 开始处理日期: 2025-04-04
2025-04-30 22:30:33,811 - INFO - Request Parameters - Page 1:
2025-04-30 22:30:33,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 22:30:33,811 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1743696000000, 1743782399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 22:30:41,921 - ERROR - 处理日期 2025-04-04 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 41F16CDF-1228-7A85-A1B9-48F00F816D87 Response: {'code': 'ServiceUnavailable', 'requestid': '41F16CDF-1228-7A85-A1B9-48F00F816D87', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 41F16CDF-1228-7A85-A1B9-48F00F816D87)
2025-04-30 22:30:41,936 - INFO - 开始处理日期: 2025-04-05
2025-04-30 22:30:41,936 - INFO - Request Parameters - Page 1:
2025-04-30 22:30:41,936 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 22:30:41,936 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1743782400000, 1743868799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 22:30:50,061 - ERROR - 处理日期 2025-04-05 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 7CC715EE-401C-7C9E-9A45-7C0EEB20FD0E Response: {'code': 'ServiceUnavailable', 'requestid': '7CC715EE-401C-7C9E-9A45-7C0EEB20FD0E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 7CC715EE-401C-7C9E-9A45-7C0EEB20FD0E)
2025-04-30 22:30:50,061 - INFO - 开始处理日期: 2025-04-29
2025-04-30 22:30:50,061 - INFO - Request Parameters - Page 1:
2025-04-30 22:30:50,061 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 22:30:50,061 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 22:30:54,249 - INFO - Response - Page 1:
2025-04-30 22:30:54,249 - INFO - 第 1 页获取到 100 条记录
2025-04-30 22:30:54,452 - INFO - Request Parameters - Page 2:
2025-04-30 22:30:54,452 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 22:30:54,452 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 22:30:55,139 - INFO - Response - Page 2:
2025-04-30 22:30:55,139 - INFO - 第 2 页获取到 100 条记录
2025-04-30 22:30:55,342 - INFO - Request Parameters - Page 3:
2025-04-30 22:30:55,342 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 22:30:55,342 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 22:30:55,952 - INFO - Response - Page 3:
2025-04-30 22:30:55,952 - INFO - 第 3 页获取到 54 条记录
2025-04-30 22:30:56,155 - INFO - 查询完成，共获取到 254 条记录
2025-04-30 22:30:56,155 - INFO - 获取到 254 条表单数据
2025-04-30 22:30:56,155 - INFO - 当前日期 2025-04-29 有 167 条MySQL数据需要处理
2025-04-30 22:30:56,155 - INFO - 日期 2025-04-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-30 22:30:56,155 - INFO - 开始处理日期: 2025-04-30
2025-04-30 22:30:56,155 - INFO - Request Parameters - Page 1:
2025-04-30 22:30:56,155 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 22:30:56,155 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 22:30:56,671 - INFO - Response - Page 1:
2025-04-30 22:30:56,671 - INFO - 第 1 页获取到 5 条记录
2025-04-30 22:30:56,874 - INFO - 查询完成，共获取到 5 条记录
2025-04-30 22:30:56,874 - INFO - 获取到 5 条表单数据
2025-04-30 22:30:56,874 - INFO - 当前日期 2025-04-30 有 39 条MySQL数据需要处理
2025-04-30 22:30:56,874 - INFO - 开始批量插入 34 条新记录
2025-04-30 22:30:57,092 - INFO - 批量插入响应状态码: 200
2025-04-30 22:30:57,092 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 30 Apr 2025 14:30:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1644', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F2D1966E-3CB4-7B13-B757-78763099813B', 'x-acs-trace-id': '06dde0690fb6c37b81e61cb7fbb5bf72', 'etag': '1RZlKigu2t435AabRN0s6hw4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-04-30 22:30:57,092 - INFO - 批量插入响应体: {'result': ['FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMY7', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMZ7', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AM08', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AM18', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AM28', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AM38', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AM48', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AM58', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AM68', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AM78', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AM88', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AM98', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMA8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMB8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMC8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMD8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AME8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMF8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMG8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMH8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMI8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMJ8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMK8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AML8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMM8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMN8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMO8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMP8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMQ8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMR8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMS8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMT8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMU8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMV8']}
2025-04-30 22:30:57,092 - INFO - 批量插入表单数据成功，批次 1，共 34 条记录
2025-04-30 22:30:57,092 - INFO - 成功插入的数据ID: ['FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMY7', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMZ7', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AM08', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AM18', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AM28', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AM38', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AM48', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AM58', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AM68', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AM78', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AM88', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AM98', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMA8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMB8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMC8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMD8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AME8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMF8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMG8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMH8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMI8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMJ8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMK8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AML8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMM8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMN8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMO8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMP8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMQ8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMR8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMS8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMT8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMU8', 'FINST-I6E66WA1HJZUBXP662RTD8YAUJZB2JT9A14AMV8']
2025-04-30 22:31:02,108 - INFO - 批量插入完成，共 34 条记录
2025-04-30 22:31:02,108 - INFO - 日期 2025-04-30 处理完成 - 更新: 0 条，插入: 34 条，错误: 0 条
2025-04-30 22:31:02,108 - INFO - 数据同步完成！更新: 0 条，插入: 34 条，错误: 2 条
2025-04-30 22:32:02,123 - INFO - 开始同步昨天与今天的销售数据: 20250429 至 20250430
2025-04-30 22:32:02,123 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 22:32:02,123 - INFO - 查询参数: ('20250429', '20250430')
2025-04-30 22:32:02,170 - INFO - MySQL查询成功，时间段: 20250429 至 20250430，共获取 0 条记录
2025-04-30 22:32:02,170 - ERROR - 未获取到MySQL数据
2025-04-30 22:32:02,170 - INFO - 同步完成
2025-04-30 23:30:34,525 - INFO - 使用默认增量同步（当天更新数据）
2025-04-30 23:30:34,525 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 23:30:34,525 - INFO - 查询参数: ('2025-04-30',)
2025-04-30 23:30:34,588 - INFO - MySQL查询成功，增量数据（日期: 2025-04-30），共获取 280 条记录
2025-04-30 23:30:34,588 - INFO - 获取到 4 个日期需要处理: ['2025-04-04', '2025-04-05', '2025-04-29', '2025-04-30']
2025-04-30 23:30:34,588 - INFO - 开始处理日期: 2025-04-04
2025-04-30 23:30:34,588 - INFO - Request Parameters - Page 1:
2025-04-30 23:30:34,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 23:30:34,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1743696000000, 1743782399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 23:30:42,721 - ERROR - 处理日期 2025-04-04 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 4475E4C4-DAD9-724E-8539-6AD9DB46B627 Response: {'code': 'ServiceUnavailable', 'requestid': '4475E4C4-DAD9-724E-8539-6AD9DB46B627', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 4475E4C4-DAD9-724E-8539-6AD9DB46B627)
2025-04-30 23:30:42,721 - INFO - 开始处理日期: 2025-04-05
2025-04-30 23:30:42,721 - INFO - Request Parameters - Page 1:
2025-04-30 23:30:42,721 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 23:30:42,721 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1743782400000, 1743868799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 23:30:50,838 - ERROR - 处理日期 2025-04-05 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D9EB59C0-ACD9-7E17-9DA3-2BD105C722B4 Response: {'code': 'ServiceUnavailable', 'requestid': 'D9EB59C0-ACD9-7E17-9DA3-2BD105C722B4', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D9EB59C0-ACD9-7E17-9DA3-2BD105C722B4)
2025-04-30 23:30:50,838 - INFO - 开始处理日期: 2025-04-29
2025-04-30 23:30:50,838 - INFO - Request Parameters - Page 1:
2025-04-30 23:30:50,838 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 23:30:50,838 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 23:30:51,589 - INFO - Response - Page 1:
2025-04-30 23:30:51,589 - INFO - 第 1 页获取到 100 条记录
2025-04-30 23:30:51,792 - INFO - Request Parameters - Page 2:
2025-04-30 23:30:51,792 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 23:30:51,792 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 23:30:52,731 - INFO - Response - Page 2:
2025-04-30 23:30:52,731 - INFO - 第 2 页获取到 100 条记录
2025-04-30 23:30:52,934 - INFO - Request Parameters - Page 3:
2025-04-30 23:30:52,934 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 23:30:52,934 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745856000000, 1745942399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 23:30:53,638 - INFO - Response - Page 3:
2025-04-30 23:30:53,638 - INFO - 第 3 页获取到 54 条记录
2025-04-30 23:30:53,841 - INFO - 查询完成，共获取到 254 条记录
2025-04-30 23:30:53,841 - INFO - 获取到 254 条表单数据
2025-04-30 23:30:53,841 - INFO - 当前日期 2025-04-29 有 167 条MySQL数据需要处理
2025-04-30 23:30:53,841 - INFO - 日期 2025-04-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-04-30 23:30:53,841 - INFO - 开始处理日期: 2025-04-30
2025-04-30 23:30:53,841 - INFO - Request Parameters - Page 1:
2025-04-30 23:30:53,841 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-04-30 23:30:53,841 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745942400000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-04-30 23:30:54,436 - INFO - Response - Page 1:
2025-04-30 23:30:54,436 - INFO - 第 1 页获取到 39 条记录
2025-04-30 23:30:54,639 - INFO - 查询完成，共获取到 39 条记录
2025-04-30 23:30:54,639 - INFO - 获取到 39 条表单数据
2025-04-30 23:30:54,639 - INFO - 当前日期 2025-04-30 有 111 条MySQL数据需要处理
2025-04-30 23:30:54,639 - INFO - 开始批量插入 72 条新记录
2025-04-30 23:30:54,889 - INFO - 批量插入响应状态码: 200
2025-04-30 23:30:54,889 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 30 Apr 2025 15:30:53 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '3468', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '718BD485-D6E0-73D1-896E-03B7DC6284AE', 'x-acs-trace-id': '7fa2347d4007c4196cb3f047b9062206', 'etag': '3lnnjbLaOxEVBu5zI/0K8iw8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-04-30 23:30:54,889 - INFO - 批量插入响应体: {'result': ['FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM82', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM92', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMA2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMB2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMC2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMD2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AME2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMF2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMG2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMH2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMI2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMJ2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMK2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AML2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMM2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMN2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMO2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMP2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMQ2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMR2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMS2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMT2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMU2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMV2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMW2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMX2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMY2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMZ2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM03', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM13', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM23', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM33', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM43', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM53', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM63', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM73', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM83', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM93', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMA3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMB3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMC3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMD3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AME3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMF3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMG3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMH3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMI3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMJ3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMK3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AML3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMM3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMN3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMO3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMP3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMQ3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMR3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMS3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMT3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMU3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMV3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMW3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMX3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMY3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMZ3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM04', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM14', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM24', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM34', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM44', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM54', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM64', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM74']}
2025-04-30 23:30:54,889 - INFO - 批量插入表单数据成功，批次 1，共 72 条记录
2025-04-30 23:30:54,889 - INFO - 成功插入的数据ID: ['FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM82', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM92', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMA2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMB2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMC2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMD2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AME2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMF2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMG2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMH2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMI2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMJ2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMK2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AML2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMM2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMN2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMO2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMP2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMQ2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMR2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMS2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMT2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMU2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMV2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMW2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMX2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMY2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMZ2', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM03', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM13', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM23', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM33', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM43', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM53', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM63', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM73', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM83', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM93', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMA3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMB3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMC3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMD3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AME3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMF3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMG3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMH3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMI3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMJ3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMK3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AML3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMM3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMN3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMO3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMP3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMQ3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMR3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMS3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMT3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMU3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMV3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMW3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMX3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMY3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AMZ3', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM04', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM14', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM24', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM34', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM44', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM54', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM64', 'FINST-X8D66N81W30VBRTW90PXF9ZUC1WD2WNCF34AM74']
2025-04-30 23:30:59,910 - INFO - 批量插入完成，共 72 条记录
2025-04-30 23:30:59,910 - INFO - 日期 2025-04-30 处理完成 - 更新: 0 条，插入: 72 条，错误: 0 条
2025-04-30 23:30:59,910 - INFO - 数据同步完成！更新: 0 条，插入: 72 条，错误: 2 条
2025-04-30 23:31:59,984 - INFO - 开始同步昨天与今天的销售数据: 20250429 至 20250430
2025-04-30 23:31:59,984 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-04-30 23:31:59,984 - INFO - 查询参数: ('20250429', '20250430')
2025-04-30 23:32:00,031 - INFO - MySQL查询成功，时间段: 20250429 至 20250430，共获取 0 条记录
2025-04-30 23:32:00,031 - ERROR - 未获取到MySQL数据
2025-04-30 23:32:00,031 - INFO - 同步完成
