2025-07-22 01:30:33,853 - INFO - 使用默认增量同步（当天更新数据）
2025-07-22 01:30:33,853 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-22 01:30:33,853 - INFO - 查询参数: ('2025-07-22',)
2025-07-22 01:30:33,931 - INFO - MySQL查询成功，增量数据（日期: 2025-07-22），共获取 0 条记录
2025-07-22 01:30:33,946 - ERROR - 未获取到MySQL数据
2025-07-22 01:31:33,962 - INFO - 开始同步昨天与今天的销售数据: 2025-07-21 至 2025-07-22
2025-07-22 01:31:33,962 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-22 01:31:33,962 - INFO - 查询参数: ('2025-07-21', '2025-07-22')
2025-07-22 01:31:34,102 - INFO - MySQL查询成功，时间段: 2025-07-21 至 2025-07-22，共获取 63 条记录
2025-07-22 01:31:34,102 - INFO - 获取到 1 个日期需要处理: ['2025-07-21']
2025-07-22 01:31:34,102 - INFO - 开始处理日期: 2025-07-21
2025-07-22 01:31:34,118 - INFO - Request Parameters - Page 1:
2025-07-22 01:31:34,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 01:31:34,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 01:31:41,852 - INFO - Response - Page 1:
2025-07-22 01:31:41,852 - INFO - 第 1 页获取到 50 条记录
2025-07-22 01:31:42,352 - INFO - Request Parameters - Page 2:
2025-07-22 01:31:42,352 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 01:31:42,352 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 01:31:50,462 - ERROR - 处理日期 2025-07-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 92A69E66-F0D1-7383-94C0-AF8134E2E4D9 Response: {'code': 'ServiceUnavailable', 'requestid': '92A69E66-F0D1-7383-94C0-AF8134E2E4D9', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 92A69E66-F0D1-7383-94C0-AF8134E2E4D9)
2025-07-22 01:31:50,462 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-22 01:31:50,462 - INFO - 同步完成
2025-07-22 04:30:33,843 - INFO - 使用默认增量同步（当天更新数据）
2025-07-22 04:30:33,843 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-22 04:30:33,843 - INFO - 查询参数: ('2025-07-22',)
2025-07-22 04:30:33,999 - INFO - MySQL查询成功，增量数据（日期: 2025-07-22），共获取 1 条记录
2025-07-22 04:30:33,999 - INFO - 获取到 1 个日期需要处理: ['2025-07-21']
2025-07-22 04:30:33,999 - INFO - 开始处理日期: 2025-07-21
2025-07-22 04:30:33,999 - INFO - Request Parameters - Page 1:
2025-07-22 04:30:33,999 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 04:30:33,999 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 04:30:42,124 - ERROR - 处理日期 2025-07-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C158A321-C19B-713F-ADB6-CAA05B65906C Response: {'code': 'ServiceUnavailable', 'requestid': 'C158A321-C19B-713F-ADB6-CAA05B65906C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C158A321-C19B-713F-ADB6-CAA05B65906C)
2025-07-22 04:30:42,124 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-22 04:31:42,140 - INFO - 开始同步昨天与今天的销售数据: 2025-07-21 至 2025-07-22
2025-07-22 04:31:42,140 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-22 04:31:42,140 - INFO - 查询参数: ('2025-07-21', '2025-07-22')
2025-07-22 04:31:42,280 - INFO - MySQL查询成功，时间段: 2025-07-21 至 2025-07-22，共获取 64 条记录
2025-07-22 04:31:42,280 - INFO - 获取到 1 个日期需要处理: ['2025-07-21']
2025-07-22 04:31:42,280 - INFO - 开始处理日期: 2025-07-21
2025-07-22 04:31:42,280 - INFO - Request Parameters - Page 1:
2025-07-22 04:31:42,280 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 04:31:42,280 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 04:31:49,718 - INFO - Response - Page 1:
2025-07-22 04:31:49,718 - INFO - 第 1 页获取到 50 条记录
2025-07-22 04:31:50,218 - INFO - Request Parameters - Page 2:
2025-07-22 04:31:50,218 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 04:31:50,218 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 04:31:50,686 - INFO - Response - Page 2:
2025-07-22 04:31:50,686 - INFO - 第 2 页获取到 2 条记录
2025-07-22 04:31:51,202 - INFO - 查询完成，共获取到 52 条记录
2025-07-22 04:31:51,202 - INFO - 获取到 52 条表单数据
2025-07-22 04:31:51,202 - INFO - 当前日期 2025-07-21 有 63 条MySQL数据需要处理
2025-07-22 04:31:51,202 - INFO - 开始批量插入 11 条新记录
2025-07-22 04:31:51,374 - INFO - 批量插入响应状态码: 200
2025-07-22 04:31:51,374 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 21 Jul 2025 20:31:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '540', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3E49043D-3E07-7633-93DE-5E407ED01DD8', 'x-acs-trace-id': 'c1eb2363c68d4a8c7f38ffef6ff71273', 'etag': '5qhUhKSBrYwimTWUSi0JQqA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-22 04:31:51,374 - INFO - 批量插入响应体: {'result': ['FINST-RN7661810ECX9TS9BT13N65LF20N3PT8AKDDM47', 'FINST-RN7661810ECX9TS9BT13N65LF20N3PT8AKDDM57', 'FINST-RN7661810ECX9TS9BT13N65LF20N3PT8AKDDM67', 'FINST-RN7661810ECX9TS9BT13N65LF20N3PT8AKDDM77', 'FINST-RN7661810ECX9TS9BT13N65LF20N3PT8AKDDM87', 'FINST-RN7661810ECX9TS9BT13N65LF20N3PT8AKDDM97', 'FINST-RN7661810ECX9TS9BT13N65LF20N3PT8AKDDMA7', 'FINST-RN7661810ECX9TS9BT13N65LF20N3PT8AKDDMB7', 'FINST-RN7661810ECX9TS9BT13N65LF20N3PT8AKDDMC7', 'FINST-RN7661810ECX9TS9BT13N65LF20N3PT8AKDDMD7', 'FINST-RN7661810ECX9TS9BT13N65LF20N3PT8AKDDME7']}
2025-07-22 04:31:51,374 - INFO - 批量插入表单数据成功，批次 1，共 11 条记录
2025-07-22 04:31:51,374 - INFO - 成功插入的数据ID: ['FINST-RN7661810ECX9TS9BT13N65LF20N3PT8AKDDM47', 'FINST-RN7661810ECX9TS9BT13N65LF20N3PT8AKDDM57', 'FINST-RN7661810ECX9TS9BT13N65LF20N3PT8AKDDM67', 'FINST-RN7661810ECX9TS9BT13N65LF20N3PT8AKDDM77', 'FINST-RN7661810ECX9TS9BT13N65LF20N3PT8AKDDM87', 'FINST-RN7661810ECX9TS9BT13N65LF20N3PT8AKDDM97', 'FINST-RN7661810ECX9TS9BT13N65LF20N3PT8AKDDMA7', 'FINST-RN7661810ECX9TS9BT13N65LF20N3PT8AKDDMB7', 'FINST-RN7661810ECX9TS9BT13N65LF20N3PT8AKDDMC7', 'FINST-RN7661810ECX9TS9BT13N65LF20N3PT8AKDDMD7', 'FINST-RN7661810ECX9TS9BT13N65LF20N3PT8AKDDME7']
2025-07-22 04:31:56,390 - INFO - 批量插入完成，共 11 条记录
2025-07-22 04:31:56,390 - INFO - 日期 2025-07-21 处理完成 - 更新: 0 条，插入: 11 条，错误: 0 条
2025-07-22 04:31:56,390 - INFO - 数据同步完成！更新: 0 条，插入: 11 条，错误: 0 条
2025-07-22 04:31:56,390 - INFO - 同步完成
2025-07-22 07:30:33,708 - INFO - 使用默认增量同步（当天更新数据）
2025-07-22 07:30:33,708 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-22 07:30:33,708 - INFO - 查询参数: ('2025-07-22',)
2025-07-22 07:30:33,864 - INFO - MySQL查询成功，增量数据（日期: 2025-07-22），共获取 4 条记录
2025-07-22 07:30:33,864 - INFO - 获取到 1 个日期需要处理: ['2025-07-21']
2025-07-22 07:30:33,864 - INFO - 开始处理日期: 2025-07-21
2025-07-22 07:30:33,864 - INFO - Request Parameters - Page 1:
2025-07-22 07:30:33,864 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 07:30:33,864 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 07:30:41,114 - INFO - Response - Page 1:
2025-07-22 07:30:41,114 - INFO - 第 1 页获取到 50 条记录
2025-07-22 07:30:41,614 - INFO - Request Parameters - Page 2:
2025-07-22 07:30:41,614 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 07:30:41,614 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 07:30:49,739 - ERROR - 处理日期 2025-07-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 409C6200-740F-7128-A1D8-89C38A2E34BC Response: {'code': 'ServiceUnavailable', 'requestid': '409C6200-740F-7128-A1D8-89C38A2E34BC', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 409C6200-740F-7128-A1D8-89C38A2E34BC)
2025-07-22 07:30:49,739 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-22 07:31:49,754 - INFO - 开始同步昨天与今天的销售数据: 2025-07-21 至 2025-07-22
2025-07-22 07:31:49,754 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-22 07:31:49,754 - INFO - 查询参数: ('2025-07-21', '2025-07-22')
2025-07-22 07:31:49,895 - INFO - MySQL查询成功，时间段: 2025-07-21 至 2025-07-22，共获取 82 条记录
2025-07-22 07:31:49,895 - INFO - 获取到 1 个日期需要处理: ['2025-07-21']
2025-07-22 07:31:49,895 - INFO - 开始处理日期: 2025-07-21
2025-07-22 07:31:49,895 - INFO - Request Parameters - Page 1:
2025-07-22 07:31:49,895 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 07:31:49,895 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 07:31:58,020 - ERROR - 处理日期 2025-07-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: EA9FF54B-CD4A-7916-AB52-87FE248F2D67 Response: {'code': 'ServiceUnavailable', 'requestid': 'EA9FF54B-CD4A-7916-AB52-87FE248F2D67', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: EA9FF54B-CD4A-7916-AB52-87FE248F2D67)
2025-07-22 07:31:58,020 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-07-22 07:31:58,020 - INFO - 同步完成
2025-07-22 10:30:33,816 - INFO - 使用默认增量同步（当天更新数据）
2025-07-22 10:30:33,816 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-22 10:30:33,816 - INFO - 查询参数: ('2025-07-22',)
2025-07-22 10:30:33,988 - INFO - MySQL查询成功，增量数据（日期: 2025-07-22），共获取 184 条记录
2025-07-22 10:30:33,988 - INFO - 获取到 5 个日期需要处理: ['2025-07-07', '2025-07-10', '2025-07-18', '2025-07-21', '2025-07-22']
2025-07-22 10:30:33,988 - INFO - 开始处理日期: 2025-07-07
2025-07-22 10:30:33,988 - INFO - Request Parameters - Page 1:
2025-07-22 10:30:33,988 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 10:30:33,988 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751817600000, 1751903999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 10:30:42,097 - ERROR - 处理日期 2025-07-07 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E3F7C7AD-263E-7C1E-8CCC-92FA56B0B136 Response: {'code': 'ServiceUnavailable', 'requestid': 'E3F7C7AD-263E-7C1E-8CCC-92FA56B0B136', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E3F7C7AD-263E-7C1E-8CCC-92FA56B0B136)
2025-07-22 10:30:42,097 - INFO - 开始处理日期: 2025-07-10
2025-07-22 10:30:42,097 - INFO - Request Parameters - Page 1:
2025-07-22 10:30:42,097 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 10:30:42,097 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 10:30:50,222 - ERROR - 处理日期 2025-07-10 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3623064D-A595-7F45-BE36-09A4FEE3B075 Response: {'code': 'ServiceUnavailable', 'requestid': '3623064D-A595-7F45-BE36-09A4FEE3B075', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3623064D-A595-7F45-BE36-09A4FEE3B075)
2025-07-22 10:30:50,222 - INFO - 开始处理日期: 2025-07-18
2025-07-22 10:30:50,222 - INFO - Request Parameters - Page 1:
2025-07-22 10:30:50,222 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 10:30:50,222 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 10:30:54,284 - INFO - Response - Page 1:
2025-07-22 10:30:54,284 - INFO - 第 1 页获取到 50 条记录
2025-07-22 10:30:54,800 - INFO - Request Parameters - Page 2:
2025-07-22 10:30:54,800 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 10:30:54,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 10:30:55,612 - INFO - Response - Page 2:
2025-07-22 10:30:55,612 - INFO - 第 2 页获取到 50 条记录
2025-07-22 10:30:56,112 - INFO - Request Parameters - Page 3:
2025-07-22 10:30:56,112 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 10:30:56,112 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 10:30:56,894 - INFO - Response - Page 3:
2025-07-22 10:30:56,894 - INFO - 第 3 页获取到 50 条记录
2025-07-22 10:30:57,394 - INFO - Request Parameters - Page 4:
2025-07-22 10:30:57,394 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 10:30:57,394 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 10:30:58,159 - INFO - Response - Page 4:
2025-07-22 10:30:58,159 - INFO - 第 4 页获取到 50 条记录
2025-07-22 10:30:58,659 - INFO - Request Parameters - Page 5:
2025-07-22 10:30:58,659 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 10:30:58,659 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 10:30:59,347 - INFO - Response - Page 5:
2025-07-22 10:30:59,347 - INFO - 第 5 页获取到 50 条记录
2025-07-22 10:30:59,862 - INFO - Request Parameters - Page 6:
2025-07-22 10:30:59,862 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 10:30:59,862 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 10:31:00,566 - INFO - Response - Page 6:
2025-07-22 10:31:00,566 - INFO - 第 6 页获取到 50 条记录
2025-07-22 10:31:01,066 - INFO - Request Parameters - Page 7:
2025-07-22 10:31:01,066 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 10:31:01,066 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 10:31:01,862 - INFO - Response - Page 7:
2025-07-22 10:31:01,862 - INFO - 第 7 页获取到 50 条记录
2025-07-22 10:31:02,362 - INFO - Request Parameters - Page 8:
2025-07-22 10:31:02,362 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 10:31:02,362 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 10:31:03,097 - INFO - Response - Page 8:
2025-07-22 10:31:03,097 - INFO - 第 8 页获取到 50 条记录
2025-07-22 10:31:03,612 - INFO - Request Parameters - Page 9:
2025-07-22 10:31:03,612 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 10:31:03,612 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 10:31:04,331 - INFO - Response - Page 9:
2025-07-22 10:31:04,331 - INFO - 第 9 页获取到 50 条记录
2025-07-22 10:31:04,847 - INFO - Request Parameters - Page 10:
2025-07-22 10:31:04,847 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 10:31:04,847 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 10:31:05,503 - INFO - Response - Page 10:
2025-07-22 10:31:05,503 - INFO - 第 10 页获取到 17 条记录
2025-07-22 10:31:06,003 - INFO - 查询完成，共获取到 467 条记录
2025-07-22 10:31:06,003 - INFO - 获取到 467 条表单数据
2025-07-22 10:31:06,003 - INFO - 当前日期 2025-07-18 有 1 条MySQL数据需要处理
2025-07-22 10:31:06,003 - INFO - 开始批量插入 1 条新记录
2025-07-22 10:31:06,175 - INFO - 批量插入响应状态码: 200
2025-07-22 10:31:06,175 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 22 Jul 2025 02:31:06 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'DF990A0C-C17F-7C2D-AA21-8F2A30DA957F', 'x-acs-trace-id': 'c7c6ce3c82569aaade71d28824f4e37c', 'etag': '5aegvCDC7S85+KhTpwA72Kw9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-22 10:31:06,175 - INFO - 批量插入响应体: {'result': ['FINST-RI766091U5DXKQCFB9SW774O5AN62DQ84XDDMC']}
2025-07-22 10:31:06,175 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-22 10:31:06,175 - INFO - 成功插入的数据ID: ['FINST-RI766091U5DXKQCFB9SW774O5AN62DQ84XDDMC']
2025-07-22 10:31:11,190 - INFO - 批量插入完成，共 1 条记录
2025-07-22 10:31:11,190 - INFO - 日期 2025-07-18 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-22 10:31:11,190 - INFO - 开始处理日期: 2025-07-21
2025-07-22 10:31:11,190 - INFO - Request Parameters - Page 1:
2025-07-22 10:31:11,190 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 10:31:11,190 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 10:31:11,987 - INFO - Response - Page 1:
2025-07-22 10:31:11,987 - INFO - 第 1 页获取到 50 条记录
2025-07-22 10:31:12,487 - INFO - Request Parameters - Page 2:
2025-07-22 10:31:12,487 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 10:31:12,487 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 10:31:13,144 - INFO - Response - Page 2:
2025-07-22 10:31:13,144 - INFO - 第 2 页获取到 13 条记录
2025-07-22 10:31:13,644 - INFO - 查询完成，共获取到 63 条记录
2025-07-22 10:31:13,644 - INFO - 获取到 63 条表单数据
2025-07-22 10:31:13,644 - INFO - 当前日期 2025-07-21 有 172 条MySQL数据需要处理
2025-07-22 10:31:13,644 - INFO - 开始更新记录 - 表单实例ID: FINST-IQG66AD1B4CX5SLF7Q0U3A2I6CDK2PZVNHCDMC
2025-07-22 10:31:14,128 - INFO - 更新表单数据成功: FINST-IQG66AD1B4CX5SLF7Q0U3A2I6CDK2PZVNHCDMC
2025-07-22 10:31:14,128 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 675.08, 'new_value': 682.03}, {'field': 'offline_amount', 'old_value': 11800.0, 'new_value': 10494.7}, {'field': 'total_amount', 'old_value': 12475.08, 'new_value': 11176.73}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-07-22 10:31:14,128 - INFO - 开始批量插入 170 条新记录
2025-07-22 10:31:14,378 - INFO - 批量插入响应状态码: 200
2025-07-22 10:31:14,378 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 22 Jul 2025 02:31:14 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7A51E965-2F5A-74F8-A54A-B6306616AB48', 'x-acs-trace-id': 'a73139b5221eb0a54a736fdcd89bc256', 'etag': '2Q9wf5/TwsBXyqZxKifTypA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-22 10:31:14,378 - INFO - 批量插入响应体: {'result': ['FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMEV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMFV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMGV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMHV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMIV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMJV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMKV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMLV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMMV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMNV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMOV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMPV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMQV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMRV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMSV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMTV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMUV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMVV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMWV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMXV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMYV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMZV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDM0W', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDM1W', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDM2W', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDM3W', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDM4W', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDM5W', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDM6W', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDM7W', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDM8W', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDM9W', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMAW', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMBW', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMCW', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMDW', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMEW', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMFW', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMGW', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMHW', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMIW', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMJW', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMKW', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMLW', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMMW', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMNW', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMOW', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMPW', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMQW', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMRW']}
2025-07-22 10:31:14,378 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-22 10:31:14,378 - INFO - 成功插入的数据ID: ['FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMEV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMFV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMGV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMHV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMIV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMJV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMKV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMLV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMMV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMNV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMOV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMPV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMQV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMRV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMSV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMTV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMUV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMVV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMWV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMXV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMYV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMZV', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDM0W', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDM1W', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDM2W', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDM3W', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDM4W', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDM5W', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDM6W', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDM7W', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDM8W', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDM9W', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMAW', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMBW', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMCW', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMDW', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMEW', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMFW', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMGW', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMHW', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMIW', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMJW', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMKW', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMLW', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMMW', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMNW', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMOW', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMPW', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMQW', 'FINST-1OC66A91RHAX1OU96Z9QCDDEE3T5202F4XDDMRW']
2025-07-22 10:31:19,644 - INFO - 批量插入响应状态码: 200
2025-07-22 10:31:19,644 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 22 Jul 2025 02:31:19 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'DA43149D-B165-7B73-BD31-DFCA7CDC2B19', 'x-acs-trace-id': 'e17efd9980b055c764fe82e195b4119c', 'etag': '2Wlb1re4eFLBeLElr4izZNg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-22 10:31:19,644 - INFO - 批量插入响应体: {'result': ['FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM02', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM12', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM22', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM32', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM42', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM52', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM62', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM72', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM82', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM92', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMA2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMB2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMC2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMD2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDME2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMF2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMG2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMH2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMI2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMJ2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMK2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDML2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMM2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMN2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMO2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMP2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMQ2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMR2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMS2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMT2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMU2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMV2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMW2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMX2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMY2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMZ2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM03', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM13', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM23', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM33', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM43', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM53', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM63', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM73', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM83', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM93', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMA3', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMB3', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMC3', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMD3']}
2025-07-22 10:31:19,644 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-07-22 10:31:19,644 - INFO - 成功插入的数据ID: ['FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM02', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM12', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM22', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM32', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM42', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM52', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM62', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM72', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM82', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM92', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMA2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMB2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMC2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMD2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDME2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMF2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMG2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMH2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMI2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMJ2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMK2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDML2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMM2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMN2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMO2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMP2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMQ2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMR2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMS2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMT2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMU2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMV2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMW2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMX2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMY2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMZ2', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM03', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM13', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM23', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM33', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM43', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM53', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM63', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM73', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM83', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDM93', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMA3', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMB3', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMC3', 'FINST-V4G66WC1U2CXTZX9CG4Q78PQDDKZ124J4XDDMD3']
2025-07-22 10:31:24,909 - INFO - 批量插入响应状态码: 200
2025-07-22 10:31:24,909 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 22 Jul 2025 02:31:24 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2409', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '110BC8B0-7F3B-7F21-A16B-20A6FFC3A17F', 'x-acs-trace-id': '30e09ffe3a072b03c0e3a649e61694e6', 'etag': '2kDsdPK7ObNnryXAjRov4eQ9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-22 10:31:24,909 - INFO - 批量插入响应体: {'result': ['FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMX', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMY', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMZ', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM01', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM11', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM21', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM31', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM41', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM51', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM61', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM71', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM81', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM91', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMA1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMB1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMC1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMD1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDME1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMF1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMG1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMH1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMI1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMJ1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMK1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDML1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMM1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMN1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMO1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMP1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMQ1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMR1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMS1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMT1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMU1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMV1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMW1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMX1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMY1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMZ1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM02', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM12', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM22', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM32', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM42', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM52', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM62', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM72', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM82', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM92', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMA2']}
2025-07-22 10:31:24,909 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-07-22 10:31:24,909 - INFO - 成功插入的数据ID: ['FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMX', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMY', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMZ', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM01', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM11', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM21', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM31', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM41', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM51', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM61', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM71', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM81', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM91', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMA1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMB1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMC1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMD1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDME1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMF1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMG1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMH1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMI1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMJ1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMK1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDML1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMM1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMN1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMO1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMP1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMQ1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMR1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMS1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMT1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMU1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMV1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMW1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMX1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMY1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMZ1', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM02', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM12', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM22', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM32', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM42', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM52', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM62', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM72', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM82', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM92', 'FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMA2']
2025-07-22 10:31:30,143 - INFO - 批量插入响应状态码: 200
2025-07-22 10:31:30,143 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 22 Jul 2025 02:31:30 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '952', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0B11CE98-4012-7584-A6A3-641287F4661C', 'x-acs-trace-id': '6384f120aada98ae2b501c0daf2434b2', 'etag': '9J8VazUcs0MLyP1LgSwYsdg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-22 10:31:30,143 - INFO - 批量插入响应体: {'result': ['FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDM5', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDM6', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDM7', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDM8', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDM9', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDMA', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDMB', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDMC', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDMD', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDME', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDMF', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDMG', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDMH', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDMI', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDMJ', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDMK', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDML', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDMM', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDMN', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDMO']}
2025-07-22 10:31:30,143 - INFO - 批量插入表单数据成功，批次 4，共 20 条记录
2025-07-22 10:31:30,143 - INFO - 成功插入的数据ID: ['FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDM5', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDM6', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDM7', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDM8', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDM9', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDMA', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDMB', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDMC', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDMD', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDME', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDMF', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDMG', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDMH', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDMI', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDMJ', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDMK', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDML', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDMM', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDMN', 'FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDMO']
2025-07-22 10:31:35,159 - INFO - 批量插入完成，共 170 条记录
2025-07-22 10:31:35,159 - INFO - 日期 2025-07-21 处理完成 - 更新: 1 条，插入: 170 条，错误: 0 条
2025-07-22 10:31:35,159 - INFO - 开始处理日期: 2025-07-22
2025-07-22 10:31:35,159 - INFO - Request Parameters - Page 1:
2025-07-22 10:31:35,159 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 10:31:35,159 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 10:31:35,659 - INFO - Response - Page 1:
2025-07-22 10:31:35,659 - INFO - 查询完成，共获取到 0 条记录
2025-07-22 10:31:35,659 - INFO - 获取到 0 条表单数据
2025-07-22 10:31:35,659 - INFO - 当前日期 2025-07-22 有 1 条MySQL数据需要处理
2025-07-22 10:31:35,659 - INFO - 开始批量插入 1 条新记录
2025-07-22 10:31:35,831 - INFO - 批量插入响应状态码: 200
2025-07-22 10:31:35,831 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 22 Jul 2025 02:31:35 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '59', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '94800DA1-01B4-70B8-B033-4DF82B696FB6', 'x-acs-trace-id': 'a3bb91f8202a47698c27415f77040840', 'etag': '5x4w1caYHwfUo5VzFi1Sa8Q9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-22 10:31:35,831 - INFO - 批量插入响应体: {'result': ['FINST-V7966QC197CXAJGFER6O547I09S320MV4XDDMB']}
2025-07-22 10:31:35,831 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-07-22 10:31:35,831 - INFO - 成功插入的数据ID: ['FINST-V7966QC197CXAJGFER6O547I09S320MV4XDDMB']
2025-07-22 10:31:40,847 - INFO - 批量插入完成，共 1 条记录
2025-07-22 10:31:40,847 - INFO - 日期 2025-07-22 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-07-22 10:31:40,847 - INFO - 数据同步完成！更新: 1 条，插入: 172 条，错误: 2 条
2025-07-22 10:32:40,862 - INFO - 开始同步昨天与今天的销售数据: 2025-07-21 至 2025-07-22
2025-07-22 10:32:40,862 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-22 10:32:40,862 - INFO - 查询参数: ('2025-07-21', '2025-07-22')
2025-07-22 10:32:41,034 - INFO - MySQL查询成功，时间段: 2025-07-21 至 2025-07-22，共获取 486 条记录
2025-07-22 10:32:41,034 - INFO - 获取到 2 个日期需要处理: ['2025-07-21', '2025-07-22']
2025-07-22 10:32:41,034 - INFO - 开始处理日期: 2025-07-21
2025-07-22 10:32:41,034 - INFO - Request Parameters - Page 1:
2025-07-22 10:32:41,034 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 10:32:41,034 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 10:32:41,846 - INFO - Response - Page 1:
2025-07-22 10:32:41,846 - INFO - 第 1 页获取到 50 条记录
2025-07-22 10:32:42,346 - INFO - Request Parameters - Page 2:
2025-07-22 10:32:42,346 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 10:32:42,346 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 10:32:43,112 - INFO - Response - Page 2:
2025-07-22 10:32:43,112 - INFO - 第 2 页获取到 50 条记录
2025-07-22 10:32:43,627 - INFO - Request Parameters - Page 3:
2025-07-22 10:32:43,627 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 10:32:43,627 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 10:32:44,252 - INFO - Response - Page 3:
2025-07-22 10:32:44,252 - INFO - 第 3 页获取到 50 条记录
2025-07-22 10:32:44,768 - INFO - Request Parameters - Page 4:
2025-07-22 10:32:44,768 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 10:32:44,768 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 10:32:45,455 - INFO - Response - Page 4:
2025-07-22 10:32:45,455 - INFO - 第 4 页获取到 50 条记录
2025-07-22 10:32:45,971 - INFO - Request Parameters - Page 5:
2025-07-22 10:32:45,971 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 10:32:45,971 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 10:32:47,049 - INFO - Response - Page 5:
2025-07-22 10:32:47,049 - INFO - 第 5 页获取到 33 条记录
2025-07-22 10:32:47,565 - INFO - 查询完成，共获取到 233 条记录
2025-07-22 10:32:47,565 - INFO - 获取到 233 条表单数据
2025-07-22 10:32:47,565 - INFO - 当前日期 2025-07-21 有 476 条MySQL数据需要处理
2025-07-22 10:32:47,565 - INFO - 开始批量插入 243 条新记录
2025-07-22 10:32:47,799 - INFO - 批量插入响应状态码: 200
2025-07-22 10:32:47,799 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 22 Jul 2025 02:32:47 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2392', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'FC86010B-DE7B-7A26-A69E-1E5EC6C749C4', 'x-acs-trace-id': 'de731524464359293b79a18343d6531e', 'etag': '246zA1bVAM71l9Z7dvrPdCw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-22 10:32:47,799 - INFO - 批量插入响应体: {'result': ['FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMG', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMH', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMI', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMJ', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMK', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDML', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMM', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMN', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMO', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMP', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMQ', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMR', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMS', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMT', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMU', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMV', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMW', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMX', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMY', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMZ', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDM01', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDM11', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDM21', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDM31', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDM41', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDM51', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDM61', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDM71', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDM81', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDM91', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMA1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMB1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMC1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMD1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDME1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMF1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMG1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMH1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMI1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMJ1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMK1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDML1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMM1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMN1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMO1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMP1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMQ1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMR1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMS1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMT1']}
2025-07-22 10:32:47,799 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-22 10:32:47,815 - INFO - 成功插入的数据ID: ['FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMG', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMH', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMI', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMJ', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMK', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDML', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMM', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMN', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMO', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMP', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMQ', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMR', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMS', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMT', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMU', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMV', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMW', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMX', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMY', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMZ', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDM01', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDM11', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDM21', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDM31', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDM41', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDM51', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDM61', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDM71', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDM81', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDM91', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMA1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMB1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMC1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMD1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDME1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMF1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMG1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMH1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMI1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMJ1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMK1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDML1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMM1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMN1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMO1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMP1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMQ1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMR1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMS1', 'FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMT1']
2025-07-22 10:32:53,065 - INFO - 批量插入响应状态码: 200
2025-07-22 10:32:53,065 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 22 Jul 2025 02:32:52 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2391', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4AF3A63F-B572-7E55-9576-150BCBBA6712', 'x-acs-trace-id': '92e79fc4ee4be133fb16b549590cc9a0', 'etag': '2+GfWljn9rbvFu56zzMxUDA1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-22 10:32:53,065 - INFO - 批量插入响应体: {'result': ['FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMF', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMG', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMH', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMI', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMJ', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMK', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDML', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMM', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMN', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMO', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMP', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMQ', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMR', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMS', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMT', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMU', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMV', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMW', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMX', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMY', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMZ', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDM01', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDM11', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDM21', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDM31', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDM41', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDM51', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDM61', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDM71', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDM81', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDM91', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMA1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMB1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMC1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMD1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDME1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMF1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMG1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMH1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMI1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMJ1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMK1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDML1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMM1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMN1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMO1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMP1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMQ1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMR1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMS1']}
2025-07-22 10:32:53,065 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-07-22 10:32:53,065 - INFO - 成功插入的数据ID: ['FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMF', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMG', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMH', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMI', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMJ', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMK', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDML', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMM', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMN', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMO', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMP', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMQ', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMR', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMS', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMT', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMU', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMV', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMW', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMX', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMY', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMZ', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDM01', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDM11', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDM21', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDM31', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDM41', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDM51', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDM61', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDM71', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDM81', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDM91', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMA1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMB1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMC1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMD1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDME1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMF1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMG1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMH1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMI1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMJ1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMK1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDML1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMM1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMN1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMO1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMP1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMQ1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMR1', 'FINST-3Z966E91N4DX39KBAYOWJC7PJJNO297J6XDDMS1']
2025-07-22 10:32:58,299 - INFO - 批量插入响应状态码: 200
2025-07-22 10:32:58,299 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 22 Jul 2025 02:32:58 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '98A17A56-34DC-7698-87AA-055C2D1C32B2', 'x-acs-trace-id': 'efc75f9ed2bdafa2d99294d0a2ab61c3', 'etag': '2b/bjXFihAXrCVlelQTEiUg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-22 10:32:58,299 - INFO - 批量插入响应体: {'result': ['FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMEC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMFC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMGC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMHC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMIC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMJC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMKC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMLC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMMC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMNC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMOC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMPC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMQC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMRC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMSC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMTC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMUC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMVC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMWC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMXC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMYC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMZC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDM0D', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDM1D', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDM2D', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDM3D', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDM4D', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDM5D', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDM6D', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDM7D', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDM8D', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDM9D', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMAD', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMBD', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMCD', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMDD', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMED', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMFD', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMGD', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMHD', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMID', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMJD', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMKD', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMLD', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMMD', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMND', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMOD', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMPD', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMQD', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMRD']}
2025-07-22 10:32:58,299 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-07-22 10:32:58,299 - INFO - 成功插入的数据ID: ['FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMEC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMFC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMGC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMHC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMIC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMJC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMKC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMLC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMMC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMNC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMOC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMPC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMQC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMRC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMSC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMTC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMUC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMVC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMWC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Y8N6XDDMXC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMYC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMZC', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDM0D', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDM1D', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDM2D', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDM3D', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDM4D', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDM5D', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDM6D', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDM7D', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDM8D', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDM9D', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMAD', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMBD', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMCD', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMDD', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMED', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMFD', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMGD', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMHD', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMID', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMJD', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMKD', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMLD', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMMD', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMND', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMOD', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMPD', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMQD', 'FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMRD']
2025-07-22 10:33:03,549 - INFO - 批量插入响应状态码: 200
2025-07-22 10:33:03,549 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 22 Jul 2025 02:33:03 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4C1FEDAF-A47E-74F0-B8ED-DBFDD10F1CBF', 'x-acs-trace-id': 'da83b18c0b33118fbf26e898c1f1be50', 'etag': '2Nk7tWp6EzbMZtCabQwaR/g2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-22 10:33:03,549 - INFO - 批量插入响应体: {'result': ['FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMHH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMIH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMJH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMKH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMLH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMMH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMNH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMOH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMPH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMQH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMRH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMSH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMTH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMUH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMVH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMWH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMXH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMYH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMZH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDM0I', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDM1I', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDM2I', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDM3I', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDM4I', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDM5I', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDM6I', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDM7I', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDM8I', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDM9I', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMAI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMBI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMCI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMDI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMEI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMFI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMGI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMHI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMII', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMJI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMKI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMLI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMMI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMNI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMOI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMPI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMQI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMRI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMSI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMTI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMUI']}
2025-07-22 10:33:03,549 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-07-22 10:33:03,549 - INFO - 成功插入的数据ID: ['FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMHH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMIH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMJH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMKH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMLH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMMH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMNH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMOH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMPH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMQH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMRH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMSH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMTH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMUH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMVH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMWH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMXH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMYH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMZH', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDM0I', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDM1I', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDM2I', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDM3I', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDM4I', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDM5I', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDM6I', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDM7I', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDM8I', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDM9I', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMAI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMBI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMCI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMDI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMEI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMFI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMGI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMHI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMII', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMJI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMKI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMLI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMMI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMNI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMOI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMPI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMQI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMRI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMSI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMTI', 'FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMUI']
2025-07-22 10:33:08,846 - INFO - 批量插入响应状态码: 200
2025-07-22 10:33:08,846 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 22 Jul 2025 02:33:08 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2076', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '14F374C9-D3DD-7865-8BCE-4B074AAEFF2D', 'x-acs-trace-id': '5d83205c6d3372664d942bb69fc47b65', 'etag': '2he5+Wb5p3XJz9RTktYFhgw6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-22 10:33:08,846 - INFO - 批量插入响应体: {'result': ['FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMZB', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDM0C', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDM1C', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDM2C', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDM3C', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDM4C', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDM5C', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDM6C', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDM7C', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDM8C', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDM9C', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMAC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMBC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMCC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMDC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMEC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMFC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMGC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMHC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMIC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMJC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMKC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMLC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMMC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMNC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMOC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMPC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMQC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMRC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMSC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMTC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMUC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMVC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMWC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMXC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMYC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMZC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDM0D', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDM1D', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDM2D', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDM3D', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDM4D', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDM5D']}
2025-07-22 10:33:08,846 - INFO - 批量插入表单数据成功，批次 5，共 43 条记录
2025-07-22 10:33:08,862 - INFO - 成功插入的数据ID: ['FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMZB', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDM0C', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDM1C', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDM2C', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDM3C', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDM4C', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDM5C', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDM6C', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDM7C', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDM8C', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDM9C', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMAC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMBC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMCC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMDC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMEC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMFC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMGC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMHC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMIC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMJC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMKC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMLC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMMC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMNC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMOC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMPC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMQC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMRC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMSC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMTC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMUC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMVC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMWC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMXC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMYC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDMZC', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDM0D', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDM1D', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDM2D', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDM3D', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDM4D', 'FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDM5D']
2025-07-22 10:33:13,877 - INFO - 批量插入完成，共 243 条记录
2025-07-22 10:33:13,877 - INFO - 日期 2025-07-21 处理完成 - 更新: 0 条，插入: 243 条，错误: 0 条
2025-07-22 10:33:13,877 - INFO - 开始处理日期: 2025-07-22
2025-07-22 10:33:13,877 - INFO - Request Parameters - Page 1:
2025-07-22 10:33:13,877 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 10:33:13,877 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 10:33:14,330 - INFO - Response - Page 1:
2025-07-22 10:33:14,330 - INFO - 第 1 页获取到 1 条记录
2025-07-22 10:33:14,846 - INFO - 查询完成，共获取到 1 条记录
2025-07-22 10:33:14,846 - INFO - 获取到 1 条表单数据
2025-07-22 10:33:14,846 - INFO - 当前日期 2025-07-22 有 1 条MySQL数据需要处理
2025-07-22 10:33:14,846 - INFO - 日期 2025-07-22 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-22 10:33:14,846 - INFO - 数据同步完成！更新: 0 条，插入: 243 条，错误: 0 条
2025-07-22 10:33:14,846 - INFO - 同步完成
2025-07-22 13:30:33,829 - INFO - 使用默认增量同步（当天更新数据）
2025-07-22 13:30:33,829 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-22 13:30:33,829 - INFO - 查询参数: ('2025-07-22',)
2025-07-22 13:30:34,001 - INFO - MySQL查询成功，增量数据（日期: 2025-07-22），共获取 189 条记录
2025-07-22 13:30:34,001 - INFO - 获取到 5 个日期需要处理: ['2025-07-07', '2025-07-10', '2025-07-18', '2025-07-21', '2025-07-22']
2025-07-22 13:30:34,001 - INFO - 开始处理日期: 2025-07-07
2025-07-22 13:30:34,001 - INFO - Request Parameters - Page 1:
2025-07-22 13:30:34,001 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:30:34,001 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751817600000, 1751903999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:30:42,126 - ERROR - 处理日期 2025-07-07 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 6CF5C933-B178-77CE-964C-19BFA060D2F5 Response: {'code': 'ServiceUnavailable', 'requestid': '6CF5C933-B178-77CE-964C-19BFA060D2F5', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 6CF5C933-B178-77CE-964C-19BFA060D2F5)
2025-07-22 13:30:42,126 - INFO - 开始处理日期: 2025-07-10
2025-07-22 13:30:42,126 - INFO - Request Parameters - Page 1:
2025-07-22 13:30:42,126 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:30:42,126 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:30:49,314 - INFO - Response - Page 1:
2025-07-22 13:30:49,329 - INFO - 第 1 页获取到 50 条记录
2025-07-22 13:30:49,845 - INFO - Request Parameters - Page 2:
2025-07-22 13:30:49,845 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:30:49,845 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:30:50,532 - INFO - Response - Page 2:
2025-07-22 13:30:50,532 - INFO - 第 2 页获取到 50 条记录
2025-07-22 13:30:51,048 - INFO - Request Parameters - Page 3:
2025-07-22 13:30:51,048 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:30:51,048 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:30:51,782 - INFO - Response - Page 3:
2025-07-22 13:30:51,782 - INFO - 第 3 页获取到 50 条记录
2025-07-22 13:30:52,282 - INFO - Request Parameters - Page 4:
2025-07-22 13:30:52,282 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:30:52,282 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:30:52,986 - INFO - Response - Page 4:
2025-07-22 13:30:52,986 - INFO - 第 4 页获取到 50 条记录
2025-07-22 13:30:53,501 - INFO - Request Parameters - Page 5:
2025-07-22 13:30:53,501 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:30:53,501 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:30:54,251 - INFO - Response - Page 5:
2025-07-22 13:30:54,251 - INFO - 第 5 页获取到 50 条记录
2025-07-22 13:30:54,767 - INFO - Request Parameters - Page 6:
2025-07-22 13:30:54,767 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:30:54,767 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:30:55,470 - INFO - Response - Page 6:
2025-07-22 13:30:55,470 - INFO - 第 6 页获取到 50 条记录
2025-07-22 13:30:55,986 - INFO - Request Parameters - Page 7:
2025-07-22 13:30:55,986 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:30:55,986 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:30:56,704 - INFO - Response - Page 7:
2025-07-22 13:30:56,704 - INFO - 第 7 页获取到 50 条记录
2025-07-22 13:30:57,220 - INFO - Request Parameters - Page 8:
2025-07-22 13:30:57,220 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:30:57,220 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:30:57,954 - INFO - Response - Page 8:
2025-07-22 13:30:57,954 - INFO - 第 8 页获取到 50 条记录
2025-07-22 13:30:58,470 - INFO - Request Parameters - Page 9:
2025-07-22 13:30:58,470 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:30:58,470 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:30:59,189 - INFO - Response - Page 9:
2025-07-22 13:30:59,189 - INFO - 第 9 页获取到 50 条记录
2025-07-22 13:30:59,704 - INFO - Request Parameters - Page 10:
2025-07-22 13:30:59,704 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:30:59,704 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:31:00,689 - INFO - Response - Page 10:
2025-07-22 13:31:00,689 - INFO - 第 10 页获取到 50 条记录
2025-07-22 13:31:01,189 - INFO - Request Parameters - Page 11:
2025-07-22 13:31:01,189 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:31:01,189 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:31:01,923 - INFO - Response - Page 11:
2025-07-22 13:31:01,923 - INFO - 第 11 页获取到 41 条记录
2025-07-22 13:31:02,423 - INFO - 查询完成，共获取到 541 条记录
2025-07-22 13:31:02,423 - INFO - 获取到 541 条表单数据
2025-07-22 13:31:02,423 - INFO - 当前日期 2025-07-10 有 1 条MySQL数据需要处理
2025-07-22 13:31:02,423 - INFO - 开始更新记录 - 表单实例ID: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMUW
2025-07-22 13:31:03,017 - INFO - 更新表单数据成功: FINST-1PF66VA1970XKHNB6A3FN5KFNGBM381JB7YCMUW
2025-07-22 13:31:03,017 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2045.11}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2045.11}, {'field': 'order_count', 'old_value': 0, 'new_value': 80}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/8f0c243bab1a48a98151b72cc1c5c807.png?Expires=2067326195&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=HGYWudxA2qGBoJ%2Fj8Sk5g9ghvPI%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/cc578c192e914d4eb3a8e0f91498a35c.jpg?Expires=2067580947&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=uB3s8vRMAY7CUFFPw0Vq6rNH%2BcQ%3D'}]
2025-07-22 13:31:03,017 - INFO - 日期 2025-07-10 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-07-22 13:31:03,017 - INFO - 开始处理日期: 2025-07-18
2025-07-22 13:31:03,017 - INFO - Request Parameters - Page 1:
2025-07-22 13:31:03,017 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:31:03,017 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:31:03,704 - INFO - Response - Page 1:
2025-07-22 13:31:03,704 - INFO - 第 1 页获取到 50 条记录
2025-07-22 13:31:04,220 - INFO - Request Parameters - Page 2:
2025-07-22 13:31:04,220 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:31:04,220 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:31:04,970 - INFO - Response - Page 2:
2025-07-22 13:31:04,970 - INFO - 第 2 页获取到 50 条记录
2025-07-22 13:31:05,486 - INFO - Request Parameters - Page 3:
2025-07-22 13:31:05,486 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:31:05,486 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:31:06,376 - INFO - Response - Page 3:
2025-07-22 13:31:06,376 - INFO - 第 3 页获取到 50 条记录
2025-07-22 13:31:06,892 - INFO - Request Parameters - Page 4:
2025-07-22 13:31:06,892 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:31:06,892 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:31:07,626 - INFO - Response - Page 4:
2025-07-22 13:31:07,626 - INFO - 第 4 页获取到 50 条记录
2025-07-22 13:31:08,142 - INFO - Request Parameters - Page 5:
2025-07-22 13:31:08,142 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:31:08,142 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:31:08,876 - INFO - Response - Page 5:
2025-07-22 13:31:08,876 - INFO - 第 5 页获取到 50 条记录
2025-07-22 13:31:09,392 - INFO - Request Parameters - Page 6:
2025-07-22 13:31:09,392 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:31:09,392 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:31:10,157 - INFO - Response - Page 6:
2025-07-22 13:31:10,157 - INFO - 第 6 页获取到 50 条记录
2025-07-22 13:31:10,673 - INFO - Request Parameters - Page 7:
2025-07-22 13:31:10,673 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:31:10,673 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:31:11,517 - INFO - Response - Page 7:
2025-07-22 13:31:11,517 - INFO - 第 7 页获取到 50 条记录
2025-07-22 13:31:12,032 - INFO - Request Parameters - Page 8:
2025-07-22 13:31:12,032 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:31:12,032 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:31:12,814 - INFO - Response - Page 8:
2025-07-22 13:31:12,814 - INFO - 第 8 页获取到 50 条记录
2025-07-22 13:31:13,314 - INFO - Request Parameters - Page 9:
2025-07-22 13:31:13,314 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:31:13,314 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:31:14,064 - INFO - Response - Page 9:
2025-07-22 13:31:14,064 - INFO - 第 9 页获取到 50 条记录
2025-07-22 13:31:14,564 - INFO - Request Parameters - Page 10:
2025-07-22 13:31:14,564 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:31:14,564 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:31:15,204 - INFO - Response - Page 10:
2025-07-22 13:31:15,204 - INFO - 第 10 页获取到 18 条记录
2025-07-22 13:31:15,720 - INFO - 查询完成，共获取到 468 条记录
2025-07-22 13:31:15,720 - INFO - 获取到 468 条表单数据
2025-07-22 13:31:15,720 - INFO - 当前日期 2025-07-18 有 1 条MySQL数据需要处理
2025-07-22 13:31:15,720 - INFO - 日期 2025-07-18 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-22 13:31:15,720 - INFO - 开始处理日期: 2025-07-21
2025-07-22 13:31:15,720 - INFO - Request Parameters - Page 1:
2025-07-22 13:31:15,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:31:15,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:31:16,439 - INFO - Response - Page 1:
2025-07-22 13:31:16,439 - INFO - 第 1 页获取到 50 条记录
2025-07-22 13:31:16,954 - INFO - Request Parameters - Page 2:
2025-07-22 13:31:16,954 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:31:16,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:31:17,657 - INFO - Response - Page 2:
2025-07-22 13:31:17,657 - INFO - 第 2 页获取到 50 条记录
2025-07-22 13:31:18,173 - INFO - Request Parameters - Page 3:
2025-07-22 13:31:18,173 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:31:18,173 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:31:18,954 - INFO - Response - Page 3:
2025-07-22 13:31:18,954 - INFO - 第 3 页获取到 50 条记录
2025-07-22 13:31:19,454 - INFO - Request Parameters - Page 4:
2025-07-22 13:31:19,454 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:31:19,454 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:31:20,220 - INFO - Response - Page 4:
2025-07-22 13:31:20,220 - INFO - 第 4 页获取到 50 条记录
2025-07-22 13:31:20,720 - INFO - Request Parameters - Page 5:
2025-07-22 13:31:20,720 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:31:20,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:31:21,360 - INFO - Response - Page 5:
2025-07-22 13:31:21,360 - INFO - 第 5 页获取到 50 条记录
2025-07-22 13:31:21,876 - INFO - Request Parameters - Page 6:
2025-07-22 13:31:21,876 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:31:21,876 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:31:22,657 - INFO - Response - Page 6:
2025-07-22 13:31:22,657 - INFO - 第 6 页获取到 50 条记录
2025-07-22 13:31:23,173 - INFO - Request Parameters - Page 7:
2025-07-22 13:31:23,173 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:31:23,173 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:31:23,813 - INFO - Response - Page 7:
2025-07-22 13:31:23,813 - INFO - 第 7 页获取到 50 条记录
2025-07-22 13:31:24,314 - INFO - Request Parameters - Page 8:
2025-07-22 13:31:24,314 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:31:24,314 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:31:25,032 - INFO - Response - Page 8:
2025-07-22 13:31:25,032 - INFO - 第 8 页获取到 50 条记录
2025-07-22 13:31:25,548 - INFO - Request Parameters - Page 9:
2025-07-22 13:31:25,548 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:31:25,548 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:31:26,267 - INFO - Response - Page 9:
2025-07-22 13:31:26,267 - INFO - 第 9 页获取到 50 条记录
2025-07-22 13:31:26,782 - INFO - Request Parameters - Page 10:
2025-07-22 13:31:26,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:31:26,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:31:27,485 - INFO - Response - Page 10:
2025-07-22 13:31:27,485 - INFO - 第 10 页获取到 26 条记录
2025-07-22 13:31:28,001 - INFO - 查询完成，共获取到 476 条记录
2025-07-22 13:31:28,001 - INFO - 获取到 476 条表单数据
2025-07-22 13:31:28,001 - INFO - 当前日期 2025-07-21 有 177 条MySQL数据需要处理
2025-07-22 13:31:28,001 - INFO - 开始更新记录 - 表单实例ID: FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDML
2025-07-22 13:31:28,579 - INFO - 更新表单数据成功: FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDML
2025-07-22 13:31:28,579 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 9045.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 9045.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 35}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/26139526d48d4173a1f29d672dadb69b.png?Expires=2067580947&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=7e1ac1PsaxgjEkcBFBEzwNe3%2BmQ%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/372b3f69cc5d44a2961d881794b1acb3.jpg?Expires=2067581145&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=7%2BqThnkLw%2BiIqlGQENIN0kgD77Q%3D'}]
2025-07-22 13:31:28,579 - INFO - 开始更新记录 - 表单实例ID: FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDM6C
2025-07-22 13:31:29,157 - INFO - 更新表单数据成功: FINST-DIC66I91TGCXHL706C3HH9F2PW4X3XDV6XDDM6C
2025-07-22 13:31:29,157 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2398.0, 'new_value': 2402.59}, {'field': 'total_amount', 'old_value': 8423.83, 'new_value': 8428.42}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-07-22 13:31:29,157 - INFO - 开始更新记录 - 表单实例ID: FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMRH
2025-07-22 13:31:29,626 - INFO - 更新表单数据成功: FINST-R8666Q71B4AXOO5R94WVJC0A84L42VAR6XDDMRH
2025-07-22 13:31:29,626 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2034.1, 'new_value': 2034.01}, {'field': 'total_amount', 'old_value': 5836.36, 'new_value': 5836.27}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-07-22 13:31:29,626 - INFO - 开始更新记录 - 表单实例ID: FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMLD
2025-07-22 13:31:30,157 - INFO - 更新表单数据成功: FINST-RN76618174CX20FOEG5FVAX7QJ1M3Z8N6XDDMLD
2025-07-22 13:31:30,157 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10147.71, 'new_value': 10141.71}, {'field': 'total_amount', 'old_value': 11398.77, 'new_value': 11392.77}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-07-22 13:31:30,157 - INFO - 开始批量插入 2 条新记录
2025-07-22 13:31:30,313 - INFO - 批量插入响应状态码: 200
2025-07-22 13:31:30,313 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 22 Jul 2025 05:31:30 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4B04C028-A90A-7C00-9A4E-674FF2867ADA', 'x-acs-trace-id': 'e2eba6d6be6cbafbdc0852c7a019b9a3', 'etag': '1cj1qkUuLlNfJEaIr1lqKOQ8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-22 13:31:30,313 - INFO - 批量插入响应体: {'result': ['FINST-L5766E71A7DXBZGNC7M1PC188YWN2UQ8K3EDM81', 'FINST-L5766E71A7DXBZGNC7M1PC188YWN2UQ8K3EDM91']}
2025-07-22 13:31:30,313 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-07-22 13:31:30,313 - INFO - 成功插入的数据ID: ['FINST-L5766E71A7DXBZGNC7M1PC188YWN2UQ8K3EDM81', 'FINST-L5766E71A7DXBZGNC7M1PC188YWN2UQ8K3EDM91']
2025-07-22 13:31:35,329 - INFO - 批量插入完成，共 2 条记录
2025-07-22 13:31:35,329 - INFO - 日期 2025-07-21 处理完成 - 更新: 4 条，插入: 2 条，错误: 0 条
2025-07-22 13:31:35,329 - INFO - 开始处理日期: 2025-07-22
2025-07-22 13:31:35,329 - INFO - Request Parameters - Page 1:
2025-07-22 13:31:35,329 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:31:35,329 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:31:35,829 - INFO - Response - Page 1:
2025-07-22 13:31:35,829 - INFO - 第 1 页获取到 1 条记录
2025-07-22 13:31:36,345 - INFO - 查询完成，共获取到 1 条记录
2025-07-22 13:31:36,345 - INFO - 获取到 1 条表单数据
2025-07-22 13:31:36,345 - INFO - 当前日期 2025-07-22 有 1 条MySQL数据需要处理
2025-07-22 13:31:36,345 - INFO - 日期 2025-07-22 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-22 13:31:36,345 - INFO - 数据同步完成！更新: 5 条，插入: 2 条，错误: 1 条
2025-07-22 13:32:36,360 - INFO - 开始同步昨天与今天的销售数据: 2025-07-21 至 2025-07-22
2025-07-22 13:32:36,360 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-22 13:32:36,360 - INFO - 查询参数: ('2025-07-21', '2025-07-22')
2025-07-22 13:32:36,516 - INFO - MySQL查询成功，时间段: 2025-07-21 至 2025-07-22，共获取 488 条记录
2025-07-22 13:32:36,516 - INFO - 获取到 2 个日期需要处理: ['2025-07-21', '2025-07-22']
2025-07-22 13:32:36,516 - INFO - 开始处理日期: 2025-07-21
2025-07-22 13:32:36,516 - INFO - Request Parameters - Page 1:
2025-07-22 13:32:36,516 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:32:36,516 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:32:37,344 - INFO - Response - Page 1:
2025-07-22 13:32:37,344 - INFO - 第 1 页获取到 50 条记录
2025-07-22 13:32:37,860 - INFO - Request Parameters - Page 2:
2025-07-22 13:32:37,860 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:32:37,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:32:38,610 - INFO - Response - Page 2:
2025-07-22 13:32:38,610 - INFO - 第 2 页获取到 50 条记录
2025-07-22 13:32:39,110 - INFO - Request Parameters - Page 3:
2025-07-22 13:32:39,110 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:32:39,110 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:32:39,751 - INFO - Response - Page 3:
2025-07-22 13:32:39,751 - INFO - 第 3 页获取到 50 条记录
2025-07-22 13:32:40,266 - INFO - Request Parameters - Page 4:
2025-07-22 13:32:40,266 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:32:40,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:32:41,016 - INFO - Response - Page 4:
2025-07-22 13:32:41,016 - INFO - 第 4 页获取到 50 条记录
2025-07-22 13:32:41,532 - INFO - Request Parameters - Page 5:
2025-07-22 13:32:41,532 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:32:41,532 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:32:42,329 - INFO - Response - Page 5:
2025-07-22 13:32:42,329 - INFO - 第 5 页获取到 50 条记录
2025-07-22 13:32:42,844 - INFO - Request Parameters - Page 6:
2025-07-22 13:32:42,844 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:32:42,844 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:32:43,579 - INFO - Response - Page 6:
2025-07-22 13:32:43,579 - INFO - 第 6 页获取到 50 条记录
2025-07-22 13:32:44,094 - INFO - Request Parameters - Page 7:
2025-07-22 13:32:44,094 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:32:44,094 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:32:44,922 - INFO - Response - Page 7:
2025-07-22 13:32:44,922 - INFO - 第 7 页获取到 50 条记录
2025-07-22 13:32:45,438 - INFO - Request Parameters - Page 8:
2025-07-22 13:32:45,438 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:32:45,438 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:32:46,141 - INFO - Response - Page 8:
2025-07-22 13:32:46,141 - INFO - 第 8 页获取到 50 条记录
2025-07-22 13:32:46,657 - INFO - Request Parameters - Page 9:
2025-07-22 13:32:46,657 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:32:46,657 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:32:47,360 - INFO - Response - Page 9:
2025-07-22 13:32:47,360 - INFO - 第 9 页获取到 50 条记录
2025-07-22 13:32:47,875 - INFO - Request Parameters - Page 10:
2025-07-22 13:32:47,875 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:32:47,875 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:32:48,485 - INFO - Response - Page 10:
2025-07-22 13:32:48,485 - INFO - 第 10 页获取到 28 条记录
2025-07-22 13:32:49,000 - INFO - 查询完成，共获取到 478 条记录
2025-07-22 13:32:49,000 - INFO - 获取到 478 条表单数据
2025-07-22 13:32:49,000 - INFO - 当前日期 2025-07-21 有 478 条MySQL数据需要处理
2025-07-22 13:32:49,016 - INFO - 日期 2025-07-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-22 13:32:49,016 - INFO - 开始处理日期: 2025-07-22
2025-07-22 13:32:49,016 - INFO - Request Parameters - Page 1:
2025-07-22 13:32:49,016 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 13:32:49,016 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 13:32:49,500 - INFO - Response - Page 1:
2025-07-22 13:32:49,500 - INFO - 第 1 页获取到 1 条记录
2025-07-22 13:32:50,016 - INFO - 查询完成，共获取到 1 条记录
2025-07-22 13:32:50,016 - INFO - 获取到 1 条表单数据
2025-07-22 13:32:50,016 - INFO - 当前日期 2025-07-22 有 1 条MySQL数据需要处理
2025-07-22 13:32:50,016 - INFO - 日期 2025-07-22 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-22 13:32:50,016 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-22 13:32:50,016 - INFO - 同步完成
2025-07-22 16:30:33,858 - INFO - 使用默认增量同步（当天更新数据）
2025-07-22 16:30:33,858 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-22 16:30:33,858 - INFO - 查询参数: ('2025-07-22',)
2025-07-22 16:30:34,014 - INFO - MySQL查询成功，增量数据（日期: 2025-07-22），共获取 211 条记录
2025-07-22 16:30:34,014 - INFO - 获取到 6 个日期需要处理: ['2025-06-30', '2025-07-07', '2025-07-10', '2025-07-18', '2025-07-21', '2025-07-22']
2025-07-22 16:30:34,014 - INFO - 开始处理日期: 2025-06-30
2025-07-22 16:30:34,014 - INFO - Request Parameters - Page 1:
2025-07-22 16:30:34,014 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:30:34,014 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:30:41,983 - INFO - Response - Page 1:
2025-07-22 16:30:41,983 - INFO - 第 1 页获取到 50 条记录
2025-07-22 16:30:42,499 - INFO - Request Parameters - Page 2:
2025-07-22 16:30:42,499 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:30:42,499 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:30:50,639 - ERROR - 处理日期 2025-06-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: BEA6164F-FFB9-78CC-A4FC-8955A43EC718 Response: {'code': 'ServiceUnavailable', 'requestid': 'BEA6164F-FFB9-78CC-A4FC-8955A43EC718', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: BEA6164F-FFB9-78CC-A4FC-8955A43EC718)
2025-07-22 16:30:50,639 - INFO - 开始处理日期: 2025-07-07
2025-07-22 16:30:50,639 - INFO - Request Parameters - Page 1:
2025-07-22 16:30:50,639 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:30:50,639 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751817600000, 1751903999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:30:55,233 - INFO - Response - Page 1:
2025-07-22 16:30:55,233 - INFO - 第 1 页获取到 50 条记录
2025-07-22 16:30:55,733 - INFO - Request Parameters - Page 2:
2025-07-22 16:30:55,733 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:30:55,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751817600000, 1751903999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:30:56,483 - INFO - Response - Page 2:
2025-07-22 16:30:56,483 - INFO - 第 2 页获取到 50 条记录
2025-07-22 16:30:56,983 - INFO - Request Parameters - Page 3:
2025-07-22 16:30:56,983 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:30:56,983 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751817600000, 1751903999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:30:57,655 - INFO - Response - Page 3:
2025-07-22 16:30:57,655 - INFO - 第 3 页获取到 50 条记录
2025-07-22 16:30:58,155 - INFO - Request Parameters - Page 4:
2025-07-22 16:30:58,155 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:30:58,155 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751817600000, 1751903999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:30:58,905 - INFO - Response - Page 4:
2025-07-22 16:30:58,905 - INFO - 第 4 页获取到 50 条记录
2025-07-22 16:30:59,420 - INFO - Request Parameters - Page 5:
2025-07-22 16:30:59,420 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:30:59,420 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751817600000, 1751903999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:00,202 - INFO - Response - Page 5:
2025-07-22 16:31:00,202 - INFO - 第 5 页获取到 50 条记录
2025-07-22 16:31:00,702 - INFO - Request Parameters - Page 6:
2025-07-22 16:31:00,702 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:00,702 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751817600000, 1751903999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:01,623 - INFO - Response - Page 6:
2025-07-22 16:31:01,623 - INFO - 第 6 页获取到 50 条记录
2025-07-22 16:31:02,123 - INFO - Request Parameters - Page 7:
2025-07-22 16:31:02,123 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:02,123 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751817600000, 1751903999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:02,889 - INFO - Response - Page 7:
2025-07-22 16:31:02,889 - INFO - 第 7 页获取到 50 条记录
2025-07-22 16:31:03,405 - INFO - Request Parameters - Page 8:
2025-07-22 16:31:03,405 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:03,405 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751817600000, 1751903999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:04,186 - INFO - Response - Page 8:
2025-07-22 16:31:04,186 - INFO - 第 8 页获取到 50 条记录
2025-07-22 16:31:04,702 - INFO - Request Parameters - Page 9:
2025-07-22 16:31:04,702 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:04,702 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751817600000, 1751903999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:05,452 - INFO - Response - Page 9:
2025-07-22 16:31:05,452 - INFO - 第 9 页获取到 50 条记录
2025-07-22 16:31:05,967 - INFO - Request Parameters - Page 10:
2025-07-22 16:31:05,967 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:05,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751817600000, 1751903999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:06,670 - INFO - Response - Page 10:
2025-07-22 16:31:06,670 - INFO - 第 10 页获取到 50 条记录
2025-07-22 16:31:07,170 - INFO - Request Parameters - Page 11:
2025-07-22 16:31:07,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:07,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751817600000, 1751903999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:07,905 - INFO - Response - Page 11:
2025-07-22 16:31:07,905 - INFO - 第 11 页获取到 25 条记录
2025-07-22 16:31:08,420 - INFO - 查询完成，共获取到 525 条记录
2025-07-22 16:31:08,420 - INFO - 获取到 525 条表单数据
2025-07-22 16:31:08,420 - INFO - 当前日期 2025-07-07 有 6 条MySQL数据需要处理
2025-07-22 16:31:08,420 - INFO - 开始更新记录 - 表单实例ID: FINST-HXD667B1LVYW361S67Z4SALPSJOV2MD6K2WCMZE
2025-07-22 16:31:09,014 - INFO - 更新表单数据成功: FINST-HXD667B1LVYW361S67Z4SALPSJOV2MD6K2WCMZE
2025-07-22 16:31:09,014 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3636.65}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 3636.65}, {'field': 'order_count', 'old_value': 0, 'new_value': 141}]
2025-07-22 16:31:09,014 - INFO - 开始批量插入 5 条新记录
2025-07-22 16:31:09,170 - INFO - 批量插入响应状态码: 200
2025-07-22 16:31:09,170 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 22 Jul 2025 08:31:09 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '247', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'FC81BAB5-71C9-7CB7-9262-39CDE246AB73', 'x-acs-trace-id': '200e3c73bb0ec65ad4a3059a2f197f46', 'etag': '2e3GkxwZ/BG6scognoNxyUg7', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-22 16:31:09,170 - INFO - 批量插入响应体: {'result': ['FINST-LLF66FD176DXNKR1BE3RT7UNJKIK2GT9Z9EDMI', 'FINST-LLF66FD176DXNKR1BE3RT7UNJKIK2GT9Z9EDMJ', 'FINST-LLF66FD176DXNKR1BE3RT7UNJKIK2GT9Z9EDMK', 'FINST-LLF66FD176DXNKR1BE3RT7UNJKIK2GT9Z9EDML', 'FINST-LLF66FD176DXNKR1BE3RT7UNJKIK2GT9Z9EDMM']}
2025-07-22 16:31:09,170 - INFO - 批量插入表单数据成功，批次 1，共 5 条记录
2025-07-22 16:31:09,170 - INFO - 成功插入的数据ID: ['FINST-LLF66FD176DXNKR1BE3RT7UNJKIK2GT9Z9EDMI', 'FINST-LLF66FD176DXNKR1BE3RT7UNJKIK2GT9Z9EDMJ', 'FINST-LLF66FD176DXNKR1BE3RT7UNJKIK2GT9Z9EDMK', 'FINST-LLF66FD176DXNKR1BE3RT7UNJKIK2GT9Z9EDML', 'FINST-LLF66FD176DXNKR1BE3RT7UNJKIK2GT9Z9EDMM']
2025-07-22 16:31:14,186 - INFO - 批量插入完成，共 5 条记录
2025-07-22 16:31:14,186 - INFO - 日期 2025-07-07 处理完成 - 更新: 1 条，插入: 5 条，错误: 0 条
2025-07-22 16:31:14,186 - INFO - 开始处理日期: 2025-07-10
2025-07-22 16:31:14,186 - INFO - Request Parameters - Page 1:
2025-07-22 16:31:14,186 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:14,186 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:14,936 - INFO - Response - Page 1:
2025-07-22 16:31:14,936 - INFO - 第 1 页获取到 50 条记录
2025-07-22 16:31:15,451 - INFO - Request Parameters - Page 2:
2025-07-22 16:31:15,451 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:15,451 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:16,233 - INFO - Response - Page 2:
2025-07-22 16:31:16,233 - INFO - 第 2 页获取到 50 条记录
2025-07-22 16:31:16,748 - INFO - Request Parameters - Page 3:
2025-07-22 16:31:16,748 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:16,748 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:17,451 - INFO - Response - Page 3:
2025-07-22 16:31:17,451 - INFO - 第 3 页获取到 50 条记录
2025-07-22 16:31:17,967 - INFO - Request Parameters - Page 4:
2025-07-22 16:31:17,967 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:17,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:18,655 - INFO - Response - Page 4:
2025-07-22 16:31:18,655 - INFO - 第 4 页获取到 50 条记录
2025-07-22 16:31:19,170 - INFO - Request Parameters - Page 5:
2025-07-22 16:31:19,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:19,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:19,951 - INFO - Response - Page 5:
2025-07-22 16:31:19,951 - INFO - 第 5 页获取到 50 条记录
2025-07-22 16:31:20,451 - INFO - Request Parameters - Page 6:
2025-07-22 16:31:20,451 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:20,451 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:21,233 - INFO - Response - Page 6:
2025-07-22 16:31:21,233 - INFO - 第 6 页获取到 50 条记录
2025-07-22 16:31:21,733 - INFO - Request Parameters - Page 7:
2025-07-22 16:31:21,733 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:21,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:22,592 - INFO - Response - Page 7:
2025-07-22 16:31:22,592 - INFO - 第 7 页获取到 50 条记录
2025-07-22 16:31:23,108 - INFO - Request Parameters - Page 8:
2025-07-22 16:31:23,108 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:23,108 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:23,889 - INFO - Response - Page 8:
2025-07-22 16:31:23,889 - INFO - 第 8 页获取到 50 条记录
2025-07-22 16:31:24,405 - INFO - Request Parameters - Page 9:
2025-07-22 16:31:24,405 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:24,405 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:25,155 - INFO - Response - Page 9:
2025-07-22 16:31:25,155 - INFO - 第 9 页获取到 50 条记录
2025-07-22 16:31:25,670 - INFO - Request Parameters - Page 10:
2025-07-22 16:31:25,670 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:25,670 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:26,389 - INFO - Response - Page 10:
2025-07-22 16:31:26,389 - INFO - 第 10 页获取到 50 条记录
2025-07-22 16:31:26,905 - INFO - Request Parameters - Page 11:
2025-07-22 16:31:26,905 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:26,905 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:27,623 - INFO - Response - Page 11:
2025-07-22 16:31:27,623 - INFO - 第 11 页获取到 41 条记录
2025-07-22 16:31:28,139 - INFO - 查询完成，共获取到 541 条记录
2025-07-22 16:31:28,139 - INFO - 获取到 541 条表单数据
2025-07-22 16:31:28,139 - INFO - 当前日期 2025-07-10 有 1 条MySQL数据需要处理
2025-07-22 16:31:28,139 - INFO - 日期 2025-07-10 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-22 16:31:28,139 - INFO - 开始处理日期: 2025-07-18
2025-07-22 16:31:28,139 - INFO - Request Parameters - Page 1:
2025-07-22 16:31:28,139 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:28,139 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:28,905 - INFO - Response - Page 1:
2025-07-22 16:31:28,905 - INFO - 第 1 页获取到 50 条记录
2025-07-22 16:31:29,405 - INFO - Request Parameters - Page 2:
2025-07-22 16:31:29,405 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:29,405 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:30,139 - INFO - Response - Page 2:
2025-07-22 16:31:30,139 - INFO - 第 2 页获取到 50 条记录
2025-07-22 16:31:30,655 - INFO - Request Parameters - Page 3:
2025-07-22 16:31:30,655 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:30,655 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:31,389 - INFO - Response - Page 3:
2025-07-22 16:31:31,389 - INFO - 第 3 页获取到 50 条记录
2025-07-22 16:31:31,905 - INFO - Request Parameters - Page 4:
2025-07-22 16:31:31,905 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:31,905 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:32,592 - INFO - Response - Page 4:
2025-07-22 16:31:32,592 - INFO - 第 4 页获取到 50 条记录
2025-07-22 16:31:33,092 - INFO - Request Parameters - Page 5:
2025-07-22 16:31:33,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:33,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:33,826 - INFO - Response - Page 5:
2025-07-22 16:31:33,826 - INFO - 第 5 页获取到 50 条记录
2025-07-22 16:31:34,342 - INFO - Request Parameters - Page 6:
2025-07-22 16:31:34,342 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:34,342 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:35,092 - INFO - Response - Page 6:
2025-07-22 16:31:35,092 - INFO - 第 6 页获取到 50 条记录
2025-07-22 16:31:35,608 - INFO - Request Parameters - Page 7:
2025-07-22 16:31:35,608 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:35,608 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:36,326 - INFO - Response - Page 7:
2025-07-22 16:31:36,326 - INFO - 第 7 页获取到 50 条记录
2025-07-22 16:31:36,842 - INFO - Request Parameters - Page 8:
2025-07-22 16:31:36,842 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:36,842 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:37,608 - INFO - Response - Page 8:
2025-07-22 16:31:37,608 - INFO - 第 8 页获取到 50 条记录
2025-07-22 16:31:38,108 - INFO - Request Parameters - Page 9:
2025-07-22 16:31:38,108 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:38,108 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:39,029 - INFO - Response - Page 9:
2025-07-22 16:31:39,029 - INFO - 第 9 页获取到 50 条记录
2025-07-22 16:31:39,529 - INFO - Request Parameters - Page 10:
2025-07-22 16:31:39,529 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:39,529 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:40,201 - INFO - Response - Page 10:
2025-07-22 16:31:40,201 - INFO - 第 10 页获取到 18 条记录
2025-07-22 16:31:40,717 - INFO - 查询完成，共获取到 468 条记录
2025-07-22 16:31:40,717 - INFO - 获取到 468 条表单数据
2025-07-22 16:31:40,717 - INFO - 当前日期 2025-07-18 有 1 条MySQL数据需要处理
2025-07-22 16:31:40,717 - INFO - 日期 2025-07-18 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-22 16:31:40,717 - INFO - 开始处理日期: 2025-07-21
2025-07-22 16:31:40,717 - INFO - Request Parameters - Page 1:
2025-07-22 16:31:40,717 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:40,717 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:41,498 - INFO - Response - Page 1:
2025-07-22 16:31:41,498 - INFO - 第 1 页获取到 50 条记录
2025-07-22 16:31:41,998 - INFO - Request Parameters - Page 2:
2025-07-22 16:31:41,998 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:41,998 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:42,733 - INFO - Response - Page 2:
2025-07-22 16:31:42,733 - INFO - 第 2 页获取到 50 条记录
2025-07-22 16:31:43,233 - INFO - Request Parameters - Page 3:
2025-07-22 16:31:43,233 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:43,233 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:43,998 - INFO - Response - Page 3:
2025-07-22 16:31:43,998 - INFO - 第 3 页获取到 50 条记录
2025-07-22 16:31:44,498 - INFO - Request Parameters - Page 4:
2025-07-22 16:31:44,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:44,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:45,233 - INFO - Response - Page 4:
2025-07-22 16:31:45,233 - INFO - 第 4 页获取到 50 条记录
2025-07-22 16:31:45,733 - INFO - Request Parameters - Page 5:
2025-07-22 16:31:45,733 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:45,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:46,576 - INFO - Response - Page 5:
2025-07-22 16:31:46,576 - INFO - 第 5 页获取到 50 条记录
2025-07-22 16:31:47,092 - INFO - Request Parameters - Page 6:
2025-07-22 16:31:47,092 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:47,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:47,873 - INFO - Response - Page 6:
2025-07-22 16:31:47,873 - INFO - 第 6 页获取到 50 条记录
2025-07-22 16:31:48,389 - INFO - Request Parameters - Page 7:
2025-07-22 16:31:48,389 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:48,389 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:49,108 - INFO - Response - Page 7:
2025-07-22 16:31:49,108 - INFO - 第 7 页获取到 50 条记录
2025-07-22 16:31:49,623 - INFO - Request Parameters - Page 8:
2025-07-22 16:31:49,623 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:49,623 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:50,451 - INFO - Response - Page 8:
2025-07-22 16:31:50,451 - INFO - 第 8 页获取到 50 条记录
2025-07-22 16:31:50,967 - INFO - Request Parameters - Page 9:
2025-07-22 16:31:50,967 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:50,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:51,686 - INFO - Response - Page 9:
2025-07-22 16:31:51,686 - INFO - 第 9 页获取到 50 条记录
2025-07-22 16:31:52,201 - INFO - Request Parameters - Page 10:
2025-07-22 16:31:52,201 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:31:52,201 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:31:52,889 - INFO - Response - Page 10:
2025-07-22 16:31:52,889 - INFO - 第 10 页获取到 28 条记录
2025-07-22 16:31:53,404 - INFO - 查询完成，共获取到 478 条记录
2025-07-22 16:31:53,404 - INFO - 获取到 478 条表单数据
2025-07-22 16:31:53,404 - INFO - 当前日期 2025-07-21 有 194 条MySQL数据需要处理
2025-07-22 16:31:53,404 - INFO - 开始更新记录 - 表单实例ID: FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMU1
2025-07-22 16:31:53,982 - INFO - 更新表单数据成功: FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMU1
2025-07-22 16:31:53,982 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 9436.43}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 9436.43}, {'field': 'order_count', 'old_value': 0, 'new_value': 456}]
2025-07-22 16:31:53,982 - INFO - 开始更新记录 - 表单实例ID: FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMR1
2025-07-22 16:31:54,576 - INFO - 更新表单数据成功: FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMR1
2025-07-22 16:31:54,576 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1790.42}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1790.42}, {'field': 'order_count', 'old_value': 0, 'new_value': 72}]
2025-07-22 16:31:54,576 - INFO - 开始更新记录 - 表单实例ID: FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMW1
2025-07-22 16:31:55,389 - INFO - 更新表单数据成功: FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMW1
2025-07-22 16:31:55,389 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 6260.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 6260.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 580}]
2025-07-22 16:31:55,389 - INFO - 开始更新记录 - 表单实例ID: FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMO1
2025-07-22 16:31:55,920 - INFO - 更新表单数据成功: FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMO1
2025-07-22 16:31:55,920 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 250693.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 250693.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 4239}]
2025-07-22 16:31:55,920 - INFO - 开始更新记录 - 表单实例ID: FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMP1
2025-07-22 16:31:56,482 - INFO - 更新表单数据成功: FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMP1
2025-07-22 16:31:56,482 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 199.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 199.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-07-22 16:31:56,482 - INFO - 开始更新记录 - 表单实例ID: FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMC1
2025-07-22 16:31:57,186 - INFO - 更新表单数据成功: FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMC1
2025-07-22 16:31:57,186 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2838.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2838.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 19}]
2025-07-22 16:31:57,186 - INFO - 开始更新记录 - 表单实例ID: FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMG
2025-07-22 16:31:57,686 - INFO - 更新表单数据成功: FINST-OPC666D1Q4DXA9TJCZ19Y8HPZIXA2F5F6XDDMG
2025-07-22 16:31:57,686 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14986.22, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 11231.9, 'new_value': 12116.16}, {'field': 'total_amount', 'old_value': 26218.12, 'new_value': 12116.16}, {'field': 'order_count', 'old_value': 85, 'new_value': 39}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/1ea2fd1dbbd8408fa1f20f9a8898b3b5.png?Expires=2067580947&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=Pk8UkuRuVydn6mRlNF8uQNOW1fU%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/dff3397f7cc84a41bf295aaee44ae7e4.jpg?Expires=2067580947&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=PPzFySTpkc9P7Zo3lNnrDJrp6zk%3D'}]
2025-07-22 16:31:57,686 - INFO - 开始更新记录 - 表单实例ID: FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMT1
2025-07-22 16:31:58,264 - INFO - 更新表单数据成功: FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMT1
2025-07-22 16:31:58,264 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8800.0, 'new_value': 12168.0}, {'field': 'total_amount', 'old_value': 8800.0, 'new_value': 12168.0}, {'field': 'order_count', 'old_value': 520, 'new_value': 1014}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/7d23e7c8554f45abaffeb9eec9011c34.jpg?Expires=2067580947&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=7YQg1GdX41n2HNxx0HDTaROOQfo%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/e76a5eb808d2472a8d34ddd0a44c3b85.png?Expires=2067581145&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=zAky2otX%2BF1pHPPoUFRdiLDWpDg%3D'}]
2025-07-22 16:31:58,264 - INFO - 开始批量插入 16 条新记录
2025-07-22 16:31:58,436 - INFO - 批量插入响应状态码: 200
2025-07-22 16:31:58,436 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 22 Jul 2025 08:31:58 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '775', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5FC52DF5-1B53-73E6-9B26-47D34A45CB36', 'x-acs-trace-id': '5cd0a9fc1f1f140b7e2ea7b16a4fde1b', 'etag': '7PZoi0SlM0Ff1bNEzT83pKw5', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-22 16:31:58,436 - INFO - 批量插入响应体: {'result': ['FINST-737662B1K3DX53JN7TFYECFCI5CC20UB0AEDMV', 'FINST-737662B1K3DX53JN7TFYECFCI5CC20UB0AEDMW', 'FINST-737662B1K3DX53JN7TFYECFCI5CC20UB0AEDMX', 'FINST-737662B1K3DX53JN7TFYECFCI5CC20UB0AEDMY', 'FINST-737662B1K3DX53JN7TFYECFCI5CC20UB0AEDMZ', 'FINST-737662B1K3DX53JN7TFYECFCI5CC20UB0AEDM01', 'FINST-737662B1K3DX53JN7TFYECFCI5CC20UB0AEDM11', 'FINST-737662B1K3DX53JN7TFYECFCI5CC20UB0AEDM21', 'FINST-737662B1K3DX53JN7TFYECFCI5CC20UB0AEDM31', 'FINST-737662B1K3DX53JN7TFYECFCI5CC20UB0AEDM41', 'FINST-737662B1K3DX53JN7TFYECFCI5CC20UB0AEDM51', 'FINST-737662B1K3DX53JN7TFYECFCI5CC20UB0AEDM61', 'FINST-737662B1K3DX53JN7TFYECFCI5CC20UB0AEDM71', 'FINST-737662B1K3DX53JN7TFYECFCI5CC20UB0AEDM81', 'FINST-737662B1K3DX53JN7TFYECFCI5CC20UB0AEDM91', 'FINST-737662B1K3DX53JN7TFYECFCI5CC20UB0AEDMA1']}
2025-07-22 16:31:58,436 - INFO - 批量插入表单数据成功，批次 1，共 16 条记录
2025-07-22 16:31:58,436 - INFO - 成功插入的数据ID: ['FINST-737662B1K3DX53JN7TFYECFCI5CC20UB0AEDMV', 'FINST-737662B1K3DX53JN7TFYECFCI5CC20UB0AEDMW', 'FINST-737662B1K3DX53JN7TFYECFCI5CC20UB0AEDMX', 'FINST-737662B1K3DX53JN7TFYECFCI5CC20UB0AEDMY', 'FINST-737662B1K3DX53JN7TFYECFCI5CC20UB0AEDMZ', 'FINST-737662B1K3DX53JN7TFYECFCI5CC20UB0AEDM01', 'FINST-737662B1K3DX53JN7TFYECFCI5CC20UB0AEDM11', 'FINST-737662B1K3DX53JN7TFYECFCI5CC20UB0AEDM21', 'FINST-737662B1K3DX53JN7TFYECFCI5CC20UB0AEDM31', 'FINST-737662B1K3DX53JN7TFYECFCI5CC20UB0AEDM41', 'FINST-737662B1K3DX53JN7TFYECFCI5CC20UB0AEDM51', 'FINST-737662B1K3DX53JN7TFYECFCI5CC20UB0AEDM61', 'FINST-737662B1K3DX53JN7TFYECFCI5CC20UB0AEDM71', 'FINST-737662B1K3DX53JN7TFYECFCI5CC20UB0AEDM81', 'FINST-737662B1K3DX53JN7TFYECFCI5CC20UB0AEDM91', 'FINST-737662B1K3DX53JN7TFYECFCI5CC20UB0AEDMA1']
2025-07-22 16:32:03,451 - INFO - 批量插入完成，共 16 条记录
2025-07-22 16:32:03,451 - INFO - 日期 2025-07-21 处理完成 - 更新: 8 条，插入: 16 条，错误: 0 条
2025-07-22 16:32:03,451 - INFO - 开始处理日期: 2025-07-22
2025-07-22 16:32:03,451 - INFO - Request Parameters - Page 1:
2025-07-22 16:32:03,451 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:32:03,451 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:32:03,951 - INFO - Response - Page 1:
2025-07-22 16:32:03,951 - INFO - 第 1 页获取到 1 条记录
2025-07-22 16:32:04,451 - INFO - 查询完成，共获取到 1 条记录
2025-07-22 16:32:04,451 - INFO - 获取到 1 条表单数据
2025-07-22 16:32:04,451 - INFO - 当前日期 2025-07-22 有 3 条MySQL数据需要处理
2025-07-22 16:32:04,451 - INFO - 开始批量插入 2 条新记录
2025-07-22 16:32:04,623 - INFO - 批量插入响应状态码: 200
2025-07-22 16:32:04,623 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 22 Jul 2025 08:32:04 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2CA6D5C0-4CFF-7AC0-B946-FB543CA6303F', 'x-acs-trace-id': 'd69d524dadc94fa45700b85d9fa4c8bf', 'etag': '1Qc+tzmJFsFJHKqcQwJVygA8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-22 16:32:04,623 - INFO - 批量插入响应体: {'result': ['FINST-N3G66S81FHAXD67QD25VTDXIFTCI2SLG0AEDM6T', 'FINST-N3G66S81FHAXD67QD25VTDXIFTCI2SLG0AEDM7T']}
2025-07-22 16:32:04,623 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-07-22 16:32:04,623 - INFO - 成功插入的数据ID: ['FINST-N3G66S81FHAXD67QD25VTDXIFTCI2SLG0AEDM6T', 'FINST-N3G66S81FHAXD67QD25VTDXIFTCI2SLG0AEDM7T']
2025-07-22 16:32:09,639 - INFO - 批量插入完成，共 2 条记录
2025-07-22 16:32:09,639 - INFO - 日期 2025-07-22 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-07-22 16:32:09,639 - INFO - 数据同步完成！更新: 9 条，插入: 23 条，错误: 1 条
2025-07-22 16:33:09,654 - INFO - 开始同步昨天与今天的销售数据: 2025-07-21 至 2025-07-22
2025-07-22 16:33:09,654 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-22 16:33:09,654 - INFO - 查询参数: ('2025-07-21', '2025-07-22')
2025-07-22 16:33:09,810 - INFO - MySQL查询成功，时间段: 2025-07-21 至 2025-07-22，共获取 547 条记录
2025-07-22 16:33:09,810 - INFO - 获取到 2 个日期需要处理: ['2025-07-21', '2025-07-22']
2025-07-22 16:33:09,826 - INFO - 开始处理日期: 2025-07-21
2025-07-22 16:33:09,826 - INFO - Request Parameters - Page 1:
2025-07-22 16:33:09,826 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:33:09,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:33:10,638 - INFO - Response - Page 1:
2025-07-22 16:33:10,638 - INFO - 第 1 页获取到 50 条记录
2025-07-22 16:33:11,138 - INFO - Request Parameters - Page 2:
2025-07-22 16:33:11,138 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:33:11,138 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:33:11,888 - INFO - Response - Page 2:
2025-07-22 16:33:11,888 - INFO - 第 2 页获取到 50 条记录
2025-07-22 16:33:12,388 - INFO - Request Parameters - Page 3:
2025-07-22 16:33:12,388 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:33:12,388 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:33:13,138 - INFO - Response - Page 3:
2025-07-22 16:33:13,138 - INFO - 第 3 页获取到 50 条记录
2025-07-22 16:33:13,654 - INFO - Request Parameters - Page 4:
2025-07-22 16:33:13,654 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:33:13,654 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:33:14,341 - INFO - Response - Page 4:
2025-07-22 16:33:14,341 - INFO - 第 4 页获取到 50 条记录
2025-07-22 16:33:14,857 - INFO - Request Parameters - Page 5:
2025-07-22 16:33:14,857 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:33:14,857 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:33:15,716 - INFO - Response - Page 5:
2025-07-22 16:33:15,716 - INFO - 第 5 页获取到 50 条记录
2025-07-22 16:33:16,232 - INFO - Request Parameters - Page 6:
2025-07-22 16:33:16,232 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:33:16,232 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:33:16,998 - INFO - Response - Page 6:
2025-07-22 16:33:16,998 - INFO - 第 6 页获取到 50 条记录
2025-07-22 16:33:17,498 - INFO - Request Parameters - Page 7:
2025-07-22 16:33:17,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:33:17,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:33:18,263 - INFO - Response - Page 7:
2025-07-22 16:33:18,263 - INFO - 第 7 页获取到 50 条记录
2025-07-22 16:33:18,779 - INFO - Request Parameters - Page 8:
2025-07-22 16:33:18,779 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:33:18,779 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:33:19,591 - INFO - Response - Page 8:
2025-07-22 16:33:19,591 - INFO - 第 8 页获取到 50 条记录
2025-07-22 16:33:20,091 - INFO - Request Parameters - Page 9:
2025-07-22 16:33:20,091 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:33:20,091 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:33:20,779 - INFO - Response - Page 9:
2025-07-22 16:33:20,779 - INFO - 第 9 页获取到 50 条记录
2025-07-22 16:33:21,294 - INFO - Request Parameters - Page 10:
2025-07-22 16:33:21,294 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:33:21,294 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:33:22,013 - INFO - Response - Page 10:
2025-07-22 16:33:22,013 - INFO - 第 10 页获取到 44 条记录
2025-07-22 16:33:22,529 - INFO - 查询完成，共获取到 494 条记录
2025-07-22 16:33:22,529 - INFO - 获取到 494 条表单数据
2025-07-22 16:33:22,529 - INFO - 当前日期 2025-07-21 有 529 条MySQL数据需要处理
2025-07-22 16:33:22,544 - INFO - 开始批量插入 35 条新记录
2025-07-22 16:33:22,763 - INFO - 批量插入响应状态码: 200
2025-07-22 16:33:22,763 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 22 Jul 2025 08:33:22 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1692', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '15F306AC-F9FC-7B39-965A-06D2B7698B58', 'x-acs-trace-id': '7b7aa436bef8d4c256540eba3814b6bd', 'etag': '14Mc3VVQgCXGHuoVer+sN6Q2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-22 16:33:22,763 - INFO - 批量插入响应体: {'result': ['FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDM61', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDM71', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDM81', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDM91', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMA1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMB1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMC1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMD1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDME1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMF1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMG1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMH1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMI1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMJ1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMK1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDML1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMM1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMN1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMO1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMP1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMQ1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMR1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMS1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMT1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMU1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMV1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMW1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMX1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMY1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMZ1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDM02', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDM12', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDM22', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDM32', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDM42']}
2025-07-22 16:33:22,763 - INFO - 批量插入表单数据成功，批次 1，共 35 条记录
2025-07-22 16:33:22,763 - INFO - 成功插入的数据ID: ['FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDM61', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDM71', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDM81', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDM91', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMA1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMB1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMC1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMD1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDME1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMF1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMG1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMH1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMI1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMJ1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMK1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDML1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMM1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMN1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMO1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMP1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMQ1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMR1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMS1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMT1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMU1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMV1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMW1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMX1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMY1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDMZ1', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDM02', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDM12', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDM22', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDM32', 'FINST-WBF66B81L7DXK2VV91V9GB83RUDW3HW42AEDM42']
2025-07-22 16:33:27,779 - INFO - 批量插入完成，共 35 条记录
2025-07-22 16:33:27,779 - INFO - 日期 2025-07-21 处理完成 - 更新: 0 条，插入: 35 条，错误: 0 条
2025-07-22 16:33:27,779 - INFO - 开始处理日期: 2025-07-22
2025-07-22 16:33:27,779 - INFO - Request Parameters - Page 1:
2025-07-22 16:33:27,779 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 16:33:27,779 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 16:33:28,232 - INFO - Response - Page 1:
2025-07-22 16:33:28,232 - INFO - 第 1 页获取到 3 条记录
2025-07-22 16:33:28,747 - INFO - 查询完成，共获取到 3 条记录
2025-07-22 16:33:28,747 - INFO - 获取到 3 条表单数据
2025-07-22 16:33:28,747 - INFO - 当前日期 2025-07-22 有 3 条MySQL数据需要处理
2025-07-22 16:33:28,747 - INFO - 日期 2025-07-22 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-22 16:33:28,747 - INFO - 数据同步完成！更新: 0 条，插入: 35 条，错误: 0 条
2025-07-22 16:33:28,747 - INFO - 同步完成
2025-07-22 19:30:34,192 - INFO - 使用默认增量同步（当天更新数据）
2025-07-22 19:30:34,192 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-22 19:30:34,192 - INFO - 查询参数: ('2025-07-22',)
2025-07-22 19:30:34,348 - INFO - MySQL查询成功，增量数据（日期: 2025-07-22），共获取 211 条记录
2025-07-22 19:30:34,348 - INFO - 获取到 6 个日期需要处理: ['2025-06-30', '2025-07-07', '2025-07-10', '2025-07-18', '2025-07-21', '2025-07-22']
2025-07-22 19:30:34,363 - INFO - 开始处理日期: 2025-06-30
2025-07-22 19:30:34,363 - INFO - Request Parameters - Page 1:
2025-07-22 19:30:34,363 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:30:34,363 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:30:42,492 - ERROR - 处理日期 2025-06-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 7F0D9C76-D45A-7E84-9302-DBF19741B86E Response: {'code': 'ServiceUnavailable', 'requestid': '7F0D9C76-D45A-7E84-9302-DBF19741B86E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 7F0D9C76-D45A-7E84-9302-DBF19741B86E)
2025-07-22 19:30:42,492 - INFO - 开始处理日期: 2025-07-07
2025-07-22 19:30:42,492 - INFO - Request Parameters - Page 1:
2025-07-22 19:30:42,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:30:42,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751817600000, 1751903999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:30:50,604 - ERROR - 处理日期 2025-07-07 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 8514031A-A6B1-70F3-99EE-F8DFB3EB8B53 Response: {'code': 'ServiceUnavailable', 'requestid': '8514031A-A6B1-70F3-99EE-F8DFB3EB8B53', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 8514031A-A6B1-70F3-99EE-F8DFB3EB8B53)
2025-07-22 19:30:50,604 - INFO - 开始处理日期: 2025-07-10
2025-07-22 19:30:50,604 - INFO - Request Parameters - Page 1:
2025-07-22 19:30:50,604 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:30:50,604 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:30:52,621 - INFO - Response - Page 1:
2025-07-22 19:30:52,621 - INFO - 第 1 页获取到 50 条记录
2025-07-22 19:30:53,137 - INFO - Request Parameters - Page 2:
2025-07-22 19:30:53,137 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:30:53,137 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:30:53,856 - INFO - Response - Page 2:
2025-07-22 19:30:53,856 - INFO - 第 2 页获取到 50 条记录
2025-07-22 19:30:54,356 - INFO - Request Parameters - Page 3:
2025-07-22 19:30:54,356 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:30:54,356 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:30:55,028 - INFO - Response - Page 3:
2025-07-22 19:30:55,028 - INFO - 第 3 页获取到 50 条记录
2025-07-22 19:30:55,528 - INFO - Request Parameters - Page 4:
2025-07-22 19:30:55,528 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:30:55,528 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:30:56,247 - INFO - Response - Page 4:
2025-07-22 19:30:56,247 - INFO - 第 4 页获取到 50 条记录
2025-07-22 19:30:56,747 - INFO - Request Parameters - Page 5:
2025-07-22 19:30:56,747 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:30:56,747 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:30:57,435 - INFO - Response - Page 5:
2025-07-22 19:30:57,435 - INFO - 第 5 页获取到 50 条记录
2025-07-22 19:30:57,951 - INFO - Request Parameters - Page 6:
2025-07-22 19:30:57,951 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:30:57,951 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:30:58,686 - INFO - Response - Page 6:
2025-07-22 19:30:58,686 - INFO - 第 6 页获取到 50 条记录
2025-07-22 19:30:59,201 - INFO - Request Parameters - Page 7:
2025-07-22 19:30:59,201 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:30:59,201 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:30:59,905 - INFO - Response - Page 7:
2025-07-22 19:30:59,905 - INFO - 第 7 页获取到 50 条记录
2025-07-22 19:31:00,405 - INFO - Request Parameters - Page 8:
2025-07-22 19:31:00,405 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:31:00,405 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:31:01,187 - INFO - Response - Page 8:
2025-07-22 19:31:01,187 - INFO - 第 8 页获取到 50 条记录
2025-07-22 19:31:01,702 - INFO - Request Parameters - Page 9:
2025-07-22 19:31:01,702 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:31:01,702 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:31:02,406 - INFO - Response - Page 9:
2025-07-22 19:31:02,406 - INFO - 第 9 页获取到 50 条记录
2025-07-22 19:31:02,922 - INFO - Request Parameters - Page 10:
2025-07-22 19:31:02,922 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:31:02,922 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:31:03,609 - INFO - Response - Page 10:
2025-07-22 19:31:03,609 - INFO - 第 10 页获取到 50 条记录
2025-07-22 19:31:04,125 - INFO - Request Parameters - Page 11:
2025-07-22 19:31:04,125 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:31:04,125 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:31:04,829 - INFO - Response - Page 11:
2025-07-22 19:31:04,829 - INFO - 第 11 页获取到 41 条记录
2025-07-22 19:31:05,344 - INFO - 查询完成，共获取到 541 条记录
2025-07-22 19:31:05,344 - INFO - 获取到 541 条表单数据
2025-07-22 19:31:05,344 - INFO - 当前日期 2025-07-10 有 1 条MySQL数据需要处理
2025-07-22 19:31:05,344 - INFO - 日期 2025-07-10 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-22 19:31:05,344 - INFO - 开始处理日期: 2025-07-18
2025-07-22 19:31:05,344 - INFO - Request Parameters - Page 1:
2025-07-22 19:31:05,344 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:31:05,344 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:31:06,079 - INFO - Response - Page 1:
2025-07-22 19:31:06,079 - INFO - 第 1 页获取到 50 条记录
2025-07-22 19:31:06,579 - INFO - Request Parameters - Page 2:
2025-07-22 19:31:06,579 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:31:06,579 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:31:07,298 - INFO - Response - Page 2:
2025-07-22 19:31:07,298 - INFO - 第 2 页获取到 50 条记录
2025-07-22 19:31:07,814 - INFO - Request Parameters - Page 3:
2025-07-22 19:31:07,814 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:31:07,814 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:31:08,533 - INFO - Response - Page 3:
2025-07-22 19:31:08,533 - INFO - 第 3 页获取到 50 条记录
2025-07-22 19:31:09,049 - INFO - Request Parameters - Page 4:
2025-07-22 19:31:09,049 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:31:09,049 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:31:09,815 - INFO - Response - Page 4:
2025-07-22 19:31:09,815 - INFO - 第 4 页获取到 50 条记录
2025-07-22 19:31:10,315 - INFO - Request Parameters - Page 5:
2025-07-22 19:31:10,315 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:31:10,315 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:31:11,034 - INFO - Response - Page 5:
2025-07-22 19:31:11,034 - INFO - 第 5 页获取到 50 条记录
2025-07-22 19:31:11,550 - INFO - Request Parameters - Page 6:
2025-07-22 19:31:11,550 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:31:11,550 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:31:12,285 - INFO - Response - Page 6:
2025-07-22 19:31:12,285 - INFO - 第 6 页获取到 50 条记录
2025-07-22 19:31:12,785 - INFO - Request Parameters - Page 7:
2025-07-22 19:31:12,785 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:31:12,785 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:31:13,488 - INFO - Response - Page 7:
2025-07-22 19:31:13,488 - INFO - 第 7 页获取到 50 条记录
2025-07-22 19:31:13,989 - INFO - Request Parameters - Page 8:
2025-07-22 19:31:13,989 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:31:13,989 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:31:14,723 - INFO - Response - Page 8:
2025-07-22 19:31:14,723 - INFO - 第 8 页获取到 50 条记录
2025-07-22 19:31:15,239 - INFO - Request Parameters - Page 9:
2025-07-22 19:31:15,239 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:31:15,239 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:31:15,989 - INFO - Response - Page 9:
2025-07-22 19:31:15,989 - INFO - 第 9 页获取到 50 条记录
2025-07-22 19:31:16,505 - INFO - Request Parameters - Page 10:
2025-07-22 19:31:16,505 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:31:16,505 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:31:17,115 - INFO - Response - Page 10:
2025-07-22 19:31:17,115 - INFO - 第 10 页获取到 18 条记录
2025-07-22 19:31:17,615 - INFO - 查询完成，共获取到 468 条记录
2025-07-22 19:31:17,615 - INFO - 获取到 468 条表单数据
2025-07-22 19:31:17,615 - INFO - 当前日期 2025-07-18 有 1 条MySQL数据需要处理
2025-07-22 19:31:17,615 - INFO - 日期 2025-07-18 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-22 19:31:17,615 - INFO - 开始处理日期: 2025-07-21
2025-07-22 19:31:17,615 - INFO - Request Parameters - Page 1:
2025-07-22 19:31:17,615 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:31:17,615 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:31:18,334 - INFO - Response - Page 1:
2025-07-22 19:31:18,334 - INFO - 第 1 页获取到 50 条记录
2025-07-22 19:31:18,834 - INFO - Request Parameters - Page 2:
2025-07-22 19:31:18,834 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:31:18,834 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:31:19,569 - INFO - Response - Page 2:
2025-07-22 19:31:19,569 - INFO - 第 2 页获取到 50 条记录
2025-07-22 19:31:20,085 - INFO - Request Parameters - Page 3:
2025-07-22 19:31:20,085 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:31:20,085 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:31:20,804 - INFO - Response - Page 3:
2025-07-22 19:31:20,804 - INFO - 第 3 页获取到 50 条记录
2025-07-22 19:31:21,304 - INFO - Request Parameters - Page 4:
2025-07-22 19:31:21,304 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:31:21,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:31:22,023 - INFO - Response - Page 4:
2025-07-22 19:31:22,023 - INFO - 第 4 页获取到 50 条记录
2025-07-22 19:31:22,523 - INFO - Request Parameters - Page 5:
2025-07-22 19:31:22,523 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:31:22,523 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:31:23,180 - INFO - Response - Page 5:
2025-07-22 19:31:23,180 - INFO - 第 5 页获取到 50 条记录
2025-07-22 19:31:23,680 - INFO - Request Parameters - Page 6:
2025-07-22 19:31:23,680 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:31:23,680 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:31:24,508 - INFO - Response - Page 6:
2025-07-22 19:31:24,508 - INFO - 第 6 页获取到 50 条记录
2025-07-22 19:31:25,024 - INFO - Request Parameters - Page 7:
2025-07-22 19:31:25,024 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:31:25,024 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:31:25,712 - INFO - Response - Page 7:
2025-07-22 19:31:25,712 - INFO - 第 7 页获取到 50 条记录
2025-07-22 19:31:26,212 - INFO - Request Parameters - Page 8:
2025-07-22 19:31:26,212 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:31:26,212 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:31:26,869 - INFO - Response - Page 8:
2025-07-22 19:31:26,869 - INFO - 第 8 页获取到 50 条记录
2025-07-22 19:31:27,384 - INFO - Request Parameters - Page 9:
2025-07-22 19:31:27,384 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:31:27,384 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:31:28,104 - INFO - Response - Page 9:
2025-07-22 19:31:28,104 - INFO - 第 9 页获取到 50 条记录
2025-07-22 19:31:28,619 - INFO - Request Parameters - Page 10:
2025-07-22 19:31:28,619 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:31:28,619 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:31:29,307 - INFO - Response - Page 10:
2025-07-22 19:31:29,307 - INFO - 第 10 页获取到 50 条记录
2025-07-22 19:31:29,823 - INFO - Request Parameters - Page 11:
2025-07-22 19:31:29,823 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:31:29,823 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:31:30,479 - INFO - Response - Page 11:
2025-07-22 19:31:30,479 - INFO - 第 11 页获取到 29 条记录
2025-07-22 19:31:30,980 - INFO - 查询完成，共获取到 529 条记录
2025-07-22 19:31:30,980 - INFO - 获取到 529 条表单数据
2025-07-22 19:31:30,980 - INFO - 当前日期 2025-07-21 有 194 条MySQL数据需要处理
2025-07-22 19:31:30,980 - INFO - 开始更新记录 - 表单实例ID: FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDM9
2025-07-22 19:31:31,605 - INFO - 更新表单数据成功: FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDM9
2025-07-22 19:31:31,605 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 274.5}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 274.5}, {'field': 'order_count', 'old_value': 0, 'new_value': 30}]
2025-07-22 19:31:31,605 - INFO - 开始更新记录 - 表单实例ID: FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMD1
2025-07-22 19:31:32,308 - INFO - 更新表单数据成功: FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMD1
2025-07-22 19:31:32,308 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 4600.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 4600.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 50}]
2025-07-22 19:31:32,308 - INFO - 开始更新记录 - 表单实例ID: FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMS1
2025-07-22 19:31:32,902 - INFO - 更新表单数据成功: FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMS1
2025-07-22 19:31:32,902 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 728.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 728.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 17}]
2025-07-22 19:31:32,902 - INFO - 开始更新记录 - 表单实例ID: FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMQ1
2025-07-22 19:31:33,449 - INFO - 更新表单数据成功: FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDMQ1
2025-07-22 19:31:33,449 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3679.25}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 3679.25}, {'field': 'order_count', 'old_value': 0, 'new_value': 173}]
2025-07-22 19:31:33,449 - INFO - 开始更新记录 - 表单实例ID: FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM61
2025-07-22 19:31:34,231 - INFO - 更新表单数据成功: FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM61
2025-07-22 19:31:34,231 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 12879.51}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 12879.51}, {'field': 'order_count', 'old_value': 0, 'new_value': 128}]
2025-07-22 19:31:34,231 - INFO - 开始更新记录 - 表单实例ID: FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDM5
2025-07-22 19:31:34,794 - INFO - 更新表单数据成功: FINST-8P666U9116DXYIANDBOHV5M5WHXW3Q7R4XDDM5
2025-07-22 19:31:34,794 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5100.0, 'new_value': 5228.0}, {'field': 'total_amount', 'old_value': 5100.0, 'new_value': 5228.0}]
2025-07-22 19:31:34,794 - INFO - 日期 2025-07-21 处理完成 - 更新: 6 条，插入: 0 条，错误: 0 条
2025-07-22 19:31:34,794 - INFO - 开始处理日期: 2025-07-22
2025-07-22 19:31:34,794 - INFO - Request Parameters - Page 1:
2025-07-22 19:31:34,794 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:31:34,794 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:31:35,278 - INFO - Response - Page 1:
2025-07-22 19:31:35,278 - INFO - 第 1 页获取到 3 条记录
2025-07-22 19:31:35,794 - INFO - 查询完成，共获取到 3 条记录
2025-07-22 19:31:35,794 - INFO - 获取到 3 条表单数据
2025-07-22 19:31:35,794 - INFO - 当前日期 2025-07-22 有 3 条MySQL数据需要处理
2025-07-22 19:31:35,794 - INFO - 日期 2025-07-22 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-22 19:31:35,794 - INFO - 数据同步完成！更新: 6 条，插入: 0 条，错误: 2 条
2025-07-22 19:32:35,834 - INFO - 开始同步昨天与今天的销售数据: 2025-07-21 至 2025-07-22
2025-07-22 19:32:35,834 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-22 19:32:35,834 - INFO - 查询参数: ('2025-07-21', '2025-07-22')
2025-07-22 19:32:35,990 - INFO - MySQL查询成功，时间段: 2025-07-21 至 2025-07-22，共获取 547 条记录
2025-07-22 19:32:35,990 - INFO - 获取到 2 个日期需要处理: ['2025-07-21', '2025-07-22']
2025-07-22 19:32:36,005 - INFO - 开始处理日期: 2025-07-21
2025-07-22 19:32:36,005 - INFO - Request Parameters - Page 1:
2025-07-22 19:32:36,005 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:32:36,005 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:32:36,725 - INFO - Response - Page 1:
2025-07-22 19:32:36,725 - INFO - 第 1 页获取到 50 条记录
2025-07-22 19:32:37,240 - INFO - Request Parameters - Page 2:
2025-07-22 19:32:37,240 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:32:37,240 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:32:37,991 - INFO - Response - Page 2:
2025-07-22 19:32:37,991 - INFO - 第 2 页获取到 50 条记录
2025-07-22 19:32:38,506 - INFO - Request Parameters - Page 3:
2025-07-22 19:32:38,506 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:32:38,506 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:32:39,194 - INFO - Response - Page 3:
2025-07-22 19:32:39,194 - INFO - 第 3 页获取到 50 条记录
2025-07-22 19:32:39,710 - INFO - Request Parameters - Page 4:
2025-07-22 19:32:39,710 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:32:39,710 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:32:40,429 - INFO - Response - Page 4:
2025-07-22 19:32:40,429 - INFO - 第 4 页获取到 50 条记录
2025-07-22 19:32:40,945 - INFO - Request Parameters - Page 5:
2025-07-22 19:32:40,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:32:40,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:32:41,742 - INFO - Response - Page 5:
2025-07-22 19:32:41,742 - INFO - 第 5 页获取到 50 条记录
2025-07-22 19:32:42,242 - INFO - Request Parameters - Page 6:
2025-07-22 19:32:42,242 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:32:42,242 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:32:42,930 - INFO - Response - Page 6:
2025-07-22 19:32:42,930 - INFO - 第 6 页获取到 50 条记录
2025-07-22 19:32:43,446 - INFO - Request Parameters - Page 7:
2025-07-22 19:32:43,446 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:32:43,446 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:32:44,165 - INFO - Response - Page 7:
2025-07-22 19:32:44,165 - INFO - 第 7 页获取到 50 条记录
2025-07-22 19:32:44,665 - INFO - Request Parameters - Page 8:
2025-07-22 19:32:44,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:32:44,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:32:45,431 - INFO - Response - Page 8:
2025-07-22 19:32:45,431 - INFO - 第 8 页获取到 50 条记录
2025-07-22 19:32:45,931 - INFO - Request Parameters - Page 9:
2025-07-22 19:32:45,931 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:32:45,931 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:32:46,619 - INFO - Response - Page 9:
2025-07-22 19:32:46,619 - INFO - 第 9 页获取到 50 条记录
2025-07-22 19:32:47,135 - INFO - Request Parameters - Page 10:
2025-07-22 19:32:47,135 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:32:47,135 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:32:47,854 - INFO - Response - Page 10:
2025-07-22 19:32:47,854 - INFO - 第 10 页获取到 50 条记录
2025-07-22 19:32:48,354 - INFO - Request Parameters - Page 11:
2025-07-22 19:32:48,354 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:32:48,354 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:32:48,979 - INFO - Response - Page 11:
2025-07-22 19:32:48,979 - INFO - 第 11 页获取到 29 条记录
2025-07-22 19:32:49,480 - INFO - 查询完成，共获取到 529 条记录
2025-07-22 19:32:49,480 - INFO - 获取到 529 条表单数据
2025-07-22 19:32:49,480 - INFO - 当前日期 2025-07-21 有 529 条MySQL数据需要处理
2025-07-22 19:32:49,495 - INFO - 日期 2025-07-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-22 19:32:49,495 - INFO - 开始处理日期: 2025-07-22
2025-07-22 19:32:49,495 - INFO - Request Parameters - Page 1:
2025-07-22 19:32:49,495 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 19:32:49,495 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 19:32:50,011 - INFO - Response - Page 1:
2025-07-22 19:32:50,011 - INFO - 第 1 页获取到 3 条记录
2025-07-22 19:32:50,511 - INFO - 查询完成，共获取到 3 条记录
2025-07-22 19:32:50,511 - INFO - 获取到 3 条表单数据
2025-07-22 19:32:50,511 - INFO - 当前日期 2025-07-22 有 3 条MySQL数据需要处理
2025-07-22 19:32:50,511 - INFO - 日期 2025-07-22 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-22 19:32:50,511 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-07-22 19:32:50,511 - INFO - 同步完成
2025-07-22 22:30:33,238 - INFO - 使用默认增量同步（当天更新数据）
2025-07-22 22:30:33,238 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-07-22 22:30:33,238 - INFO - 查询参数: ('2025-07-22',)
2025-07-22 22:30:33,410 - INFO - MySQL查询成功，增量数据（日期: 2025-07-22），共获取 275 条记录
2025-07-22 22:30:33,410 - INFO - 获取到 6 个日期需要处理: ['2025-06-30', '2025-07-07', '2025-07-10', '2025-07-18', '2025-07-21', '2025-07-22']
2025-07-22 22:30:33,410 - INFO - 开始处理日期: 2025-06-30
2025-07-22 22:30:33,410 - INFO - Request Parameters - Page 1:
2025-07-22 22:30:33,410 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:30:33,410 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751212800000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:30:41,538 - ERROR - 处理日期 2025-06-30 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 223809F9-B44B-71AA-87B4-2CADC9D73D9C Response: {'code': 'ServiceUnavailable', 'requestid': '223809F9-B44B-71AA-87B4-2CADC9D73D9C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 223809F9-B44B-71AA-87B4-2CADC9D73D9C)
2025-07-22 22:30:41,538 - INFO - 开始处理日期: 2025-07-07
2025-07-22 22:30:41,538 - INFO - Request Parameters - Page 1:
2025-07-22 22:30:41,538 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:30:41,538 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1751817600000, 1751903999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:30:49,651 - ERROR - 处理日期 2025-07-07 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: CA0870FF-DC11-7464-A57C-7D880F31BA2D Response: {'code': 'ServiceUnavailable', 'requestid': 'CA0870FF-DC11-7464-A57C-7D880F31BA2D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: CA0870FF-DC11-7464-A57C-7D880F31BA2D)
2025-07-22 22:30:49,651 - INFO - 开始处理日期: 2025-07-10
2025-07-22 22:30:49,651 - INFO - Request Parameters - Page 1:
2025-07-22 22:30:49,651 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:30:49,651 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:30:51,995 - INFO - Response - Page 1:
2025-07-22 22:30:51,995 - INFO - 第 1 页获取到 50 条记录
2025-07-22 22:30:52,511 - INFO - Request Parameters - Page 2:
2025-07-22 22:30:52,511 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:30:52,511 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:30:53,246 - INFO - Response - Page 2:
2025-07-22 22:30:53,246 - INFO - 第 2 页获取到 50 条记录
2025-07-22 22:30:53,762 - INFO - Request Parameters - Page 3:
2025-07-22 22:30:53,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:30:53,762 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:30:54,403 - INFO - Response - Page 3:
2025-07-22 22:30:54,403 - INFO - 第 3 页获取到 50 条记录
2025-07-22 22:30:54,918 - INFO - Request Parameters - Page 4:
2025-07-22 22:30:54,918 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:30:54,918 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:30:55,637 - INFO - Response - Page 4:
2025-07-22 22:30:55,637 - INFO - 第 4 页获取到 50 条记录
2025-07-22 22:30:56,153 - INFO - Request Parameters - Page 5:
2025-07-22 22:30:56,153 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:30:56,153 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:30:56,872 - INFO - Response - Page 5:
2025-07-22 22:30:56,872 - INFO - 第 5 页获取到 50 条记录
2025-07-22 22:30:57,388 - INFO - Request Parameters - Page 6:
2025-07-22 22:30:57,388 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:30:57,388 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:30:58,029 - INFO - Response - Page 6:
2025-07-22 22:30:58,029 - INFO - 第 6 页获取到 50 条记录
2025-07-22 22:30:58,545 - INFO - Request Parameters - Page 7:
2025-07-22 22:30:58,545 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:30:58,545 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:30:59,311 - INFO - Response - Page 7:
2025-07-22 22:30:59,311 - INFO - 第 7 页获取到 50 条记录
2025-07-22 22:30:59,811 - INFO - Request Parameters - Page 8:
2025-07-22 22:30:59,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:30:59,811 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:31:00,499 - INFO - Response - Page 8:
2025-07-22 22:31:00,499 - INFO - 第 8 页获取到 50 条记录
2025-07-22 22:31:01,015 - INFO - Request Parameters - Page 9:
2025-07-22 22:31:01,015 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:31:01,015 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:31:01,734 - INFO - Response - Page 9:
2025-07-22 22:31:01,734 - INFO - 第 9 页获取到 50 条记录
2025-07-22 22:31:02,234 - INFO - Request Parameters - Page 10:
2025-07-22 22:31:02,234 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:31:02,234 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:31:03,000 - INFO - Response - Page 10:
2025-07-22 22:31:03,000 - INFO - 第 10 页获取到 50 条记录
2025-07-22 22:31:03,516 - INFO - Request Parameters - Page 11:
2025-07-22 22:31:03,516 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:31:03,516 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752076800000, 1752163199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:31:04,219 - INFO - Response - Page 11:
2025-07-22 22:31:04,219 - INFO - 第 11 页获取到 41 条记录
2025-07-22 22:31:04,735 - INFO - 查询完成，共获取到 541 条记录
2025-07-22 22:31:04,735 - INFO - 获取到 541 条表单数据
2025-07-22 22:31:04,735 - INFO - 当前日期 2025-07-10 有 1 条MySQL数据需要处理
2025-07-22 22:31:04,735 - INFO - 日期 2025-07-10 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-22 22:31:04,735 - INFO - 开始处理日期: 2025-07-18
2025-07-22 22:31:04,735 - INFO - Request Parameters - Page 1:
2025-07-22 22:31:04,735 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:31:04,735 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:31:05,438 - INFO - Response - Page 1:
2025-07-22 22:31:05,438 - INFO - 第 1 页获取到 50 条记录
2025-07-22 22:31:05,938 - INFO - Request Parameters - Page 2:
2025-07-22 22:31:05,938 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:31:05,938 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:31:06,642 - INFO - Response - Page 2:
2025-07-22 22:31:06,642 - INFO - 第 2 页获取到 50 条记录
2025-07-22 22:31:07,158 - INFO - Request Parameters - Page 3:
2025-07-22 22:31:07,158 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:31:07,158 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:31:07,924 - INFO - Response - Page 3:
2025-07-22 22:31:07,924 - INFO - 第 3 页获取到 50 条记录
2025-07-22 22:31:08,439 - INFO - Request Parameters - Page 4:
2025-07-22 22:31:08,439 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:31:08,439 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:31:09,127 - INFO - Response - Page 4:
2025-07-22 22:31:09,127 - INFO - 第 4 页获取到 50 条记录
2025-07-22 22:31:09,627 - INFO - Request Parameters - Page 5:
2025-07-22 22:31:09,627 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:31:09,627 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:31:10,346 - INFO - Response - Page 5:
2025-07-22 22:31:10,346 - INFO - 第 5 页获取到 50 条记录
2025-07-22 22:31:10,862 - INFO - Request Parameters - Page 6:
2025-07-22 22:31:10,862 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:31:10,862 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:31:11,597 - INFO - Response - Page 6:
2025-07-22 22:31:11,597 - INFO - 第 6 页获取到 50 条记录
2025-07-22 22:31:12,113 - INFO - Request Parameters - Page 7:
2025-07-22 22:31:12,113 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:31:12,113 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:31:12,879 - INFO - Response - Page 7:
2025-07-22 22:31:12,879 - INFO - 第 7 页获取到 50 条记录
2025-07-22 22:31:13,394 - INFO - Request Parameters - Page 8:
2025-07-22 22:31:13,394 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:31:13,394 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:31:14,098 - INFO - Response - Page 8:
2025-07-22 22:31:14,098 - INFO - 第 8 页获取到 50 条记录
2025-07-22 22:31:14,598 - INFO - Request Parameters - Page 9:
2025-07-22 22:31:14,598 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:31:14,598 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:31:15,270 - INFO - Response - Page 9:
2025-07-22 22:31:15,270 - INFO - 第 9 页获取到 50 条记录
2025-07-22 22:31:15,786 - INFO - Request Parameters - Page 10:
2025-07-22 22:31:15,786 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:31:15,786 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:31:16,411 - INFO - Response - Page 10:
2025-07-22 22:31:16,411 - INFO - 第 10 页获取到 18 条记录
2025-07-22 22:31:16,912 - INFO - 查询完成，共获取到 468 条记录
2025-07-22 22:31:16,912 - INFO - 获取到 468 条表单数据
2025-07-22 22:31:16,912 - INFO - 当前日期 2025-07-18 有 1 条MySQL数据需要处理
2025-07-22 22:31:16,912 - INFO - 日期 2025-07-18 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-22 22:31:16,912 - INFO - 开始处理日期: 2025-07-21
2025-07-22 22:31:16,912 - INFO - Request Parameters - Page 1:
2025-07-22 22:31:16,912 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:31:16,912 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:31:17,662 - INFO - Response - Page 1:
2025-07-22 22:31:17,662 - INFO - 第 1 页获取到 50 条记录
2025-07-22 22:31:18,162 - INFO - Request Parameters - Page 2:
2025-07-22 22:31:18,162 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:31:18,162 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:31:18,819 - INFO - Response - Page 2:
2025-07-22 22:31:18,819 - INFO - 第 2 页获取到 50 条记录
2025-07-22 22:31:19,319 - INFO - Request Parameters - Page 3:
2025-07-22 22:31:19,319 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:31:19,319 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:31:20,022 - INFO - Response - Page 3:
2025-07-22 22:31:20,022 - INFO - 第 3 页获取到 50 条记录
2025-07-22 22:31:20,522 - INFO - Request Parameters - Page 4:
2025-07-22 22:31:20,522 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:31:20,522 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:31:21,241 - INFO - Response - Page 4:
2025-07-22 22:31:21,241 - INFO - 第 4 页获取到 50 条记录
2025-07-22 22:31:21,757 - INFO - Request Parameters - Page 5:
2025-07-22 22:31:21,757 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:31:21,757 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:31:22,429 - INFO - Response - Page 5:
2025-07-22 22:31:22,429 - INFO - 第 5 页获取到 50 条记录
2025-07-22 22:31:22,945 - INFO - Request Parameters - Page 6:
2025-07-22 22:31:22,945 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:31:22,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:31:23,602 - INFO - Response - Page 6:
2025-07-22 22:31:23,602 - INFO - 第 6 页获取到 50 条记录
2025-07-22 22:31:24,102 - INFO - Request Parameters - Page 7:
2025-07-22 22:31:24,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:31:24,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:31:24,805 - INFO - Response - Page 7:
2025-07-22 22:31:24,805 - INFO - 第 7 页获取到 50 条记录
2025-07-22 22:31:25,321 - INFO - Request Parameters - Page 8:
2025-07-22 22:31:25,321 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:31:25,321 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:31:26,056 - INFO - Response - Page 8:
2025-07-22 22:31:26,056 - INFO - 第 8 页获取到 50 条记录
2025-07-22 22:31:26,556 - INFO - Request Parameters - Page 9:
2025-07-22 22:31:26,556 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:31:26,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:31:27,244 - INFO - Response - Page 9:
2025-07-22 22:31:27,244 - INFO - 第 9 页获取到 50 条记录
2025-07-22 22:31:27,760 - INFO - Request Parameters - Page 10:
2025-07-22 22:31:27,760 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:31:27,760 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:31:28,463 - INFO - Response - Page 10:
2025-07-22 22:31:28,463 - INFO - 第 10 页获取到 50 条记录
2025-07-22 22:31:28,979 - INFO - Request Parameters - Page 11:
2025-07-22 22:31:28,979 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:31:28,979 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:31:29,588 - INFO - Response - Page 11:
2025-07-22 22:31:29,588 - INFO - 第 11 页获取到 29 条记录
2025-07-22 22:31:30,104 - INFO - 查询完成，共获取到 529 条记录
2025-07-22 22:31:30,104 - INFO - 获取到 529 条表单数据
2025-07-22 22:31:30,104 - INFO - 当前日期 2025-07-21 有 194 条MySQL数据需要处理
2025-07-22 22:31:30,104 - INFO - 日期 2025-07-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-22 22:31:30,104 - INFO - 开始处理日期: 2025-07-22
2025-07-22 22:31:30,104 - INFO - Request Parameters - Page 1:
2025-07-22 22:31:30,104 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:31:30,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:31:30,636 - INFO - Response - Page 1:
2025-07-22 22:31:30,636 - INFO - 第 1 页获取到 3 条记录
2025-07-22 22:31:31,136 - INFO - 查询完成，共获取到 3 条记录
2025-07-22 22:31:31,136 - INFO - 获取到 3 条表单数据
2025-07-22 22:31:31,136 - INFO - 当前日期 2025-07-22 有 66 条MySQL数据需要处理
2025-07-22 22:31:31,136 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81FHAXD67QD25VTDXIFTCI2SLG0AEDM6T
2025-07-22 22:31:31,636 - INFO - 更新表单数据成功: FINST-N3G66S81FHAXD67QD25VTDXIFTCI2SLG0AEDM6T
2025-07-22 22:31:31,636 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5228.0, 'new_value': 4453.15}, {'field': 'total_amount', 'old_value': 5228.0, 'new_value': 4453.15}, {'field': 'order_count', 'old_value': 580, 'new_value': 314}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/e73fa7bf6e5640a69316d479348dc0c2.jpg?Expires=2067580947&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=U9HXB07HYNb%2FaNIIdf8IaGHsyxQ%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/ef6d423292c346f88af8a00b0ce55361.jpeg?Expires=2067580947&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=DvPdPcxBZbdyoU2ebvAgXK3zfys%3D'}]
2025-07-22 22:31:31,636 - INFO - 开始批量插入 63 条新记录
2025-07-22 22:31:31,933 - INFO - 批量插入响应状态码: 200
2025-07-22 22:31:31,933 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 22 Jul 2025 14:31:27 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '884D2B10-F8A3-7EA7-8A9D-FBD5F1DDCD44', 'x-acs-trace-id': '40137b9ddbd46f86098612c8ef3098bd', 'etag': '2iAZaLdGitYzsmA3l0hC8sA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-22 22:31:31,933 - INFO - 批量插入响应体: {'result': ['FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMNE', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMOE', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMPE', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMQE', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMRE', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMSE', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMTE', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMUE', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMVE', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMWE', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMXE', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMYE', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMZE', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDM0F', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDM1F', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDM2F', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDM3F', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDM4F', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDM5F', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDM6F', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDM7F', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDM8F', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDM9F', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMAF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMBF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMCF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMDF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMEF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMFF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMGF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMHF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMIF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMJF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMKF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMLF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMMF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMNF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMOF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMPF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMQF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMRF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMSF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMTF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMUF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMVF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMWF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMXF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMYF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMZF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDM0G']}
2025-07-22 22:31:31,933 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-07-22 22:31:31,933 - INFO - 成功插入的数据ID: ['FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMNE', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMOE', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMPE', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMQE', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMRE', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMSE', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMTE', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMUE', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMVE', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMWE', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMXE', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMYE', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMZE', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDM0F', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDM1F', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDM2F', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDM3F', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDM4F', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDM5F', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDM6F', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDM7F', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDM8F', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDM9F', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMAF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMBF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMCF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMDF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMEF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMFF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMGF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMHF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMIF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMJF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMKF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMLF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMMF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMNF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMOF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMPF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMQF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMRF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMSF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMTF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMUF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMVF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMWF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMXF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMYF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDMZF', 'FINST-MLF66JA1XCCX2WT0DJ8YOBFHV02720JMUMEDM0G']
2025-07-22 22:31:37,138 - INFO - 批量插入响应状态码: 200
2025-07-22 22:31:37,138 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 22 Jul 2025 14:31:32 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '636', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0A89FB43-E0D3-7B2A-BEDB-71AC26683CDC', 'x-acs-trace-id': '0233ebdd6820410356f81c7b8b41175a', 'etag': '6um9zfM6mPvoQ9kTSCUJiqw6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-22 22:31:37,138 - INFO - 批量插入响应体: {'result': ['FINST-90E66JD1O7DXG0I7AB3MP6H9XUW82NJQUMEDMGA', 'FINST-90E66JD1O7DXG0I7AB3MP6H9XUW82OJQUMEDMHA', 'FINST-90E66JD1O7DXG0I7AB3MP6H9XUW82OJQUMEDMIA', 'FINST-90E66JD1O7DXG0I7AB3MP6H9XUW82OJQUMEDMJA', 'FINST-90E66JD1O7DXG0I7AB3MP6H9XUW82OJQUMEDMKA', 'FINST-90E66JD1O7DXG0I7AB3MP6H9XUW82OJQUMEDMLA', 'FINST-90E66JD1O7DXG0I7AB3MP6H9XUW82OJQUMEDMMA', 'FINST-90E66JD1O7DXG0I7AB3MP6H9XUW82OJQUMEDMNA', 'FINST-90E66JD1O7DXG0I7AB3MP6H9XUW82OJQUMEDMOA', 'FINST-90E66JD1O7DXG0I7AB3MP6H9XUW82OJQUMEDMPA', 'FINST-90E66JD1O7DXG0I7AB3MP6H9XUW82OJQUMEDMQA', 'FINST-90E66JD1O7DXG0I7AB3MP6H9XUW82OJQUMEDMRA', 'FINST-90E66JD1O7DXG0I7AB3MP6H9XUW82OJQUMEDMSA']}
2025-07-22 22:31:37,138 - INFO - 批量插入表单数据成功，批次 2，共 13 条记录
2025-07-22 22:31:37,138 - INFO - 成功插入的数据ID: ['FINST-90E66JD1O7DXG0I7AB3MP6H9XUW82NJQUMEDMGA', 'FINST-90E66JD1O7DXG0I7AB3MP6H9XUW82OJQUMEDMHA', 'FINST-90E66JD1O7DXG0I7AB3MP6H9XUW82OJQUMEDMIA', 'FINST-90E66JD1O7DXG0I7AB3MP6H9XUW82OJQUMEDMJA', 'FINST-90E66JD1O7DXG0I7AB3MP6H9XUW82OJQUMEDMKA', 'FINST-90E66JD1O7DXG0I7AB3MP6H9XUW82OJQUMEDMLA', 'FINST-90E66JD1O7DXG0I7AB3MP6H9XUW82OJQUMEDMMA', 'FINST-90E66JD1O7DXG0I7AB3MP6H9XUW82OJQUMEDMNA', 'FINST-90E66JD1O7DXG0I7AB3MP6H9XUW82OJQUMEDMOA', 'FINST-90E66JD1O7DXG0I7AB3MP6H9XUW82OJQUMEDMPA', 'FINST-90E66JD1O7DXG0I7AB3MP6H9XUW82OJQUMEDMQA', 'FINST-90E66JD1O7DXG0I7AB3MP6H9XUW82OJQUMEDMRA', 'FINST-90E66JD1O7DXG0I7AB3MP6H9XUW82OJQUMEDMSA']
2025-07-22 22:31:42,156 - INFO - 批量插入完成，共 63 条记录
2025-07-22 22:31:42,156 - INFO - 日期 2025-07-22 处理完成 - 更新: 1 条，插入: 63 条，错误: 0 条
2025-07-22 22:31:42,156 - INFO - 数据同步完成！更新: 1 条，插入: 63 条，错误: 2 条
2025-07-22 22:32:42,195 - INFO - 开始同步昨天与今天的销售数据: 2025-07-21 至 2025-07-22
2025-07-22 22:32:42,195 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-07-22 22:32:42,195 - INFO - 查询参数: ('2025-07-21', '2025-07-22')
2025-07-22 22:32:42,367 - INFO - MySQL查询成功，时间段: 2025-07-21 至 2025-07-22，共获取 613 条记录
2025-07-22 22:32:42,367 - INFO - 获取到 2 个日期需要处理: ['2025-07-21', '2025-07-22']
2025-07-22 22:32:42,367 - INFO - 开始处理日期: 2025-07-21
2025-07-22 22:32:42,367 - INFO - Request Parameters - Page 1:
2025-07-22 22:32:42,367 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:32:42,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:32:43,118 - INFO - Response - Page 1:
2025-07-22 22:32:43,118 - INFO - 第 1 页获取到 50 条记录
2025-07-22 22:32:43,633 - INFO - Request Parameters - Page 2:
2025-07-22 22:32:43,633 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:32:43,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:32:44,337 - INFO - Response - Page 2:
2025-07-22 22:32:44,337 - INFO - 第 2 页获取到 50 条记录
2025-07-22 22:32:44,837 - INFO - Request Parameters - Page 3:
2025-07-22 22:32:44,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:32:44,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:32:45,556 - INFO - Response - Page 3:
2025-07-22 22:32:45,556 - INFO - 第 3 页获取到 50 条记录
2025-07-22 22:32:46,072 - INFO - Request Parameters - Page 4:
2025-07-22 22:32:46,072 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:32:46,072 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:32:46,791 - INFO - Response - Page 4:
2025-07-22 22:32:46,791 - INFO - 第 4 页获取到 50 条记录
2025-07-22 22:32:47,291 - INFO - Request Parameters - Page 5:
2025-07-22 22:32:47,291 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:32:47,291 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:32:47,932 - INFO - Response - Page 5:
2025-07-22 22:32:47,932 - INFO - 第 5 页获取到 50 条记录
2025-07-22 22:32:48,448 - INFO - Request Parameters - Page 6:
2025-07-22 22:32:48,448 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:32:48,448 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:32:49,167 - INFO - Response - Page 6:
2025-07-22 22:32:49,167 - INFO - 第 6 页获取到 50 条记录
2025-07-22 22:32:49,667 - INFO - Request Parameters - Page 7:
2025-07-22 22:32:49,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:32:49,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:32:50,370 - INFO - Response - Page 7:
2025-07-22 22:32:50,370 - INFO - 第 7 页获取到 50 条记录
2025-07-22 22:32:50,871 - INFO - Request Parameters - Page 8:
2025-07-22 22:32:50,871 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:32:50,871 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:32:51,527 - INFO - Response - Page 8:
2025-07-22 22:32:51,527 - INFO - 第 8 页获取到 50 条记录
2025-07-22 22:32:52,027 - INFO - Request Parameters - Page 9:
2025-07-22 22:32:52,027 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:32:52,027 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:32:52,684 - INFO - Response - Page 9:
2025-07-22 22:32:52,684 - INFO - 第 9 页获取到 50 条记录
2025-07-22 22:32:53,200 - INFO - Request Parameters - Page 10:
2025-07-22 22:32:53,200 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:32:53,200 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:32:53,856 - INFO - Response - Page 10:
2025-07-22 22:32:53,856 - INFO - 第 10 页获取到 50 条记录
2025-07-22 22:32:54,372 - INFO - Request Parameters - Page 11:
2025-07-22 22:32:54,372 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:32:54,372 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:32:55,122 - INFO - Response - Page 11:
2025-07-22 22:32:55,122 - INFO - 第 11 页获取到 29 条记录
2025-07-22 22:32:55,638 - INFO - 查询完成，共获取到 529 条记录
2025-07-22 22:32:55,638 - INFO - 获取到 529 条表单数据
2025-07-22 22:32:55,638 - INFO - 当前日期 2025-07-21 有 529 条MySQL数据需要处理
2025-07-22 22:32:55,654 - INFO - 日期 2025-07-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-07-22 22:32:55,654 - INFO - 开始处理日期: 2025-07-22
2025-07-22 22:32:55,654 - INFO - Request Parameters - Page 1:
2025-07-22 22:32:55,654 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:32:55,654 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:32:56,420 - INFO - Response - Page 1:
2025-07-22 22:32:56,420 - INFO - 第 1 页获取到 50 条记录
2025-07-22 22:32:56,936 - INFO - Request Parameters - Page 2:
2025-07-22 22:32:56,936 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-07-22 22:32:56,936 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753113600000, 1753199999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-07-22 22:32:57,498 - INFO - Response - Page 2:
2025-07-22 22:32:57,498 - INFO - 第 2 页获取到 16 条记录
2025-07-22 22:32:57,998 - INFO - 查询完成，共获取到 66 条记录
2025-07-22 22:32:57,998 - INFO - 获取到 66 条表单数据
2025-07-22 22:32:57,998 - INFO - 当前日期 2025-07-22 有 68 条MySQL数据需要处理
2025-07-22 22:32:57,998 - INFO - 开始批量插入 2 条新记录
2025-07-22 22:32:58,155 - INFO - 批量插入响应状态码: 200
2025-07-22 22:32:58,155 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 22 Jul 2025 14:32:53 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B98A8D8A-6709-7B88-90D3-A2B5FAB9DFBA', 'x-acs-trace-id': 'e107671705f6328d47780c0eb2ff144f', 'etag': '1vep8pqsYYkxoySrOQVKbSg8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-07-22 22:32:58,155 - INFO - 批量插入响应体: {'result': ['FINST-OY8665C1H3CX2D159O5AF9U0GENP2E1HWMEDMVC', 'FINST-OY8665C1H3CX2D159O5AF9U0GENP2E1HWMEDMWC']}
2025-07-22 22:32:58,155 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-07-22 22:32:58,155 - INFO - 成功插入的数据ID: ['FINST-OY8665C1H3CX2D159O5AF9U0GENP2E1HWMEDMVC', 'FINST-OY8665C1H3CX2D159O5AF9U0GENP2E1HWMEDMWC']
2025-07-22 22:33:03,172 - INFO - 批量插入完成，共 2 条记录
2025-07-22 22:33:03,172 - INFO - 日期 2025-07-22 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-07-22 22:33:03,172 - INFO - 数据同步完成！更新: 0 条，插入: 2 条，错误: 0 条
2025-07-22 22:33:03,172 - INFO - 同步完成
