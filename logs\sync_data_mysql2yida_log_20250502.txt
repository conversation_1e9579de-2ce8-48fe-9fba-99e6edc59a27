2025-05-02 00:30:34,585 - INFO - 使用默认增量同步（当天更新数据）
2025-05-02 00:30:34,585 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 00:30:34,585 - INFO - 查询参数: ('2025-05-02',)
2025-05-02 00:30:34,632 - INFO - MySQL查询成功，增量数据（日期: 2025-05-02），共获取 6 条记录
2025-05-02 00:30:34,632 - INFO - 获取到 1 个日期需要处理: ['2025-05-01']
2025-05-02 00:30:34,632 - INFO - 开始处理日期: 2025-05-01
2025-05-02 00:30:34,648 - INFO - Request Parameters - Page 1:
2025-05-02 00:30:34,648 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 00:30:34,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 00:30:42,751 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 7F0F6410-A9D1-7F9D-8E70-E62857CF19D9 Response: {'code': 'ServiceUnavailable', 'requestid': '7F0F6410-A9D1-7F9D-8E70-E62857CF19D9', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 7F0F6410-A9D1-7F9D-8E70-E62857CF19D9)
2025-05-02 00:30:42,751 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-02 00:31:42,834 - INFO - 开始同步昨天与今天的销售数据: 20250501 至 20250502
2025-05-02 00:31:42,834 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 00:31:42,834 - INFO - 查询参数: ('20250501', '20250502')
2025-05-02 00:31:42,881 - INFO - MySQL查询成功，时间段: 20250501 至 20250502，共获取 0 条记录
2025-05-02 00:31:42,881 - ERROR - 未获取到MySQL数据
2025-05-02 00:31:42,881 - INFO - 同步完成
2025-05-02 01:30:34,496 - INFO - 使用默认增量同步（当天更新数据）
2025-05-02 01:30:34,496 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 01:30:34,496 - INFO - 查询参数: ('2025-05-02',)
2025-05-02 01:30:34,559 - INFO - MySQL查询成功，增量数据（日期: 2025-05-02），共获取 8 条记录
2025-05-02 01:30:34,559 - INFO - 获取到 1 个日期需要处理: ['2025-05-01']
2025-05-02 01:30:34,559 - INFO - 开始处理日期: 2025-05-01
2025-05-02 01:30:34,559 - INFO - Request Parameters - Page 1:
2025-05-02 01:30:34,559 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 01:30:34,559 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 01:30:42,693 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 06565D3D-E6B6-7ED4-8C7D-84545962D0AC Response: {'code': 'ServiceUnavailable', 'requestid': '06565D3D-E6B6-7ED4-8C7D-84545962D0AC', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 06565D3D-E6B6-7ED4-8C7D-84545962D0AC)
2025-05-02 01:30:42,693 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-02 01:31:42,776 - INFO - 开始同步昨天与今天的销售数据: 20250501 至 20250502
2025-05-02 01:31:42,776 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 01:31:42,776 - INFO - 查询参数: ('20250501', '20250502')
2025-05-02 01:31:42,823 - INFO - MySQL查询成功，时间段: 20250501 至 20250502，共获取 0 条记录
2025-05-02 01:31:42,823 - ERROR - 未获取到MySQL数据
2025-05-02 01:31:42,823 - INFO - 同步完成
2025-05-02 02:30:34,610 - INFO - 使用默认增量同步（当天更新数据）
2025-05-02 02:30:34,610 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 02:30:34,610 - INFO - 查询参数: ('2025-05-02',)
2025-05-02 02:30:34,657 - INFO - MySQL查询成功，增量数据（日期: 2025-05-02），共获取 8 条记录
2025-05-02 02:30:34,657 - INFO - 获取到 1 个日期需要处理: ['2025-05-01']
2025-05-02 02:30:34,657 - INFO - 开始处理日期: 2025-05-01
2025-05-02 02:30:34,672 - INFO - Request Parameters - Page 1:
2025-05-02 02:30:34,672 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 02:30:34,672 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 02:30:42,791 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 47B2C338-C623-7189-B8D4-57FAA910C322 Response: {'code': 'ServiceUnavailable', 'requestid': '47B2C338-C623-7189-B8D4-57FAA910C322', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 47B2C338-C623-7189-B8D4-57FAA910C322)
2025-05-02 02:30:42,791 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-02 02:31:42,874 - INFO - 开始同步昨天与今天的销售数据: 20250501 至 20250502
2025-05-02 02:31:42,874 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 02:31:42,874 - INFO - 查询参数: ('20250501', '20250502')
2025-05-02 02:31:42,921 - INFO - MySQL查询成功，时间段: 20250501 至 20250502，共获取 0 条记录
2025-05-02 02:31:42,921 - ERROR - 未获取到MySQL数据
2025-05-02 02:31:42,921 - INFO - 同步完成
2025-05-02 03:30:34,458 - INFO - 使用默认增量同步（当天更新数据）
2025-05-02 03:30:34,458 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 03:30:34,458 - INFO - 查询参数: ('2025-05-02',)
2025-05-02 03:30:34,520 - INFO - MySQL查询成功，增量数据（日期: 2025-05-02），共获取 16 条记录
2025-05-02 03:30:34,520 - INFO - 获取到 1 个日期需要处理: ['2025-05-01']
2025-05-02 03:30:34,520 - INFO - 开始处理日期: 2025-05-01
2025-05-02 03:30:34,520 - INFO - Request Parameters - Page 1:
2025-05-02 03:30:34,520 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 03:30:34,520 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 03:30:42,639 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 9621FD15-D139-72FE-B104-CFF5093729B0 Response: {'code': 'ServiceUnavailable', 'requestid': '9621FD15-D139-72FE-B104-CFF5093729B0', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 9621FD15-D139-72FE-B104-CFF5093729B0)
2025-05-02 03:30:42,639 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-02 03:31:42,722 - INFO - 开始同步昨天与今天的销售数据: 20250501 至 20250502
2025-05-02 03:31:42,722 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 03:31:42,722 - INFO - 查询参数: ('20250501', '20250502')
2025-05-02 03:31:42,769 - INFO - MySQL查询成功，时间段: 20250501 至 20250502，共获取 0 条记录
2025-05-02 03:31:42,769 - ERROR - 未获取到MySQL数据
2025-05-02 03:31:42,769 - INFO - 同步完成
2025-05-02 04:30:34,447 - INFO - 使用默认增量同步（当天更新数据）
2025-05-02 04:30:34,447 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 04:30:34,447 - INFO - 查询参数: ('2025-05-02',)
2025-05-02 04:30:34,509 - INFO - MySQL查询成功，增量数据（日期: 2025-05-02），共获取 17 条记录
2025-05-02 04:30:34,509 - INFO - 获取到 1 个日期需要处理: ['2025-05-01']
2025-05-02 04:30:34,509 - INFO - 开始处理日期: 2025-05-01
2025-05-02 04:30:34,509 - INFO - Request Parameters - Page 1:
2025-05-02 04:30:34,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 04:30:34,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 04:30:42,643 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 8ED75F84-836C-73C4-B4C7-3E000DABB0D5 Response: {'code': 'ServiceUnavailable', 'requestid': '8ED75F84-836C-73C4-B4C7-3E000DABB0D5', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 8ED75F84-836C-73C4-B4C7-3E000DABB0D5)
2025-05-02 04:30:42,643 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-02 04:31:42,727 - INFO - 开始同步昨天与今天的销售数据: 20250501 至 20250502
2025-05-02 04:31:42,727 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 04:31:42,727 - INFO - 查询参数: ('20250501', '20250502')
2025-05-02 04:31:42,789 - INFO - MySQL查询成功，时间段: 20250501 至 20250502，共获取 0 条记录
2025-05-02 04:31:42,789 - ERROR - 未获取到MySQL数据
2025-05-02 04:31:42,789 - INFO - 同步完成
2025-05-02 05:30:34,451 - INFO - 使用默认增量同步（当天更新数据）
2025-05-02 05:30:34,451 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 05:30:34,451 - INFO - 查询参数: ('2025-05-02',)
2025-05-02 05:30:34,514 - INFO - MySQL查询成功，增量数据（日期: 2025-05-02），共获取 17 条记录
2025-05-02 05:30:34,514 - INFO - 获取到 1 个日期需要处理: ['2025-05-01']
2025-05-02 05:30:34,514 - INFO - 开始处理日期: 2025-05-01
2025-05-02 05:30:34,514 - INFO - Request Parameters - Page 1:
2025-05-02 05:30:34,514 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 05:30:34,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 05:30:42,632 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3BE3A802-36D8-7FAD-BAA5-C87757D513F1 Response: {'code': 'ServiceUnavailable', 'requestid': '3BE3A802-36D8-7FAD-BAA5-C87757D513F1', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3BE3A802-36D8-7FAD-BAA5-C87757D513F1)
2025-05-02 05:30:42,632 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-02 05:31:42,715 - INFO - 开始同步昨天与今天的销售数据: 20250501 至 20250502
2025-05-02 05:31:42,715 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 05:31:42,715 - INFO - 查询参数: ('20250501', '20250502')
2025-05-02 05:31:42,762 - INFO - MySQL查询成功，时间段: 20250501 至 20250502，共获取 0 条记录
2025-05-02 05:31:42,762 - ERROR - 未获取到MySQL数据
2025-05-02 05:31:42,762 - INFO - 同步完成
2025-05-02 06:30:34,612 - INFO - 使用默认增量同步（当天更新数据）
2025-05-02 06:30:34,612 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 06:30:34,612 - INFO - 查询参数: ('2025-05-02',)
2025-05-02 06:30:34,675 - INFO - MySQL查询成功，增量数据（日期: 2025-05-02），共获取 20 条记录
2025-05-02 06:30:34,675 - INFO - 获取到 1 个日期需要处理: ['2025-05-01']
2025-05-02 06:30:34,675 - INFO - 开始处理日期: 2025-05-01
2025-05-02 06:30:34,675 - INFO - Request Parameters - Page 1:
2025-05-02 06:30:34,675 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 06:30:34,675 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 06:30:42,793 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2DD5C775-2BEC-7366-981B-4FC552391391 Response: {'code': 'ServiceUnavailable', 'requestid': '2DD5C775-2BEC-7366-981B-4FC552391391', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2DD5C775-2BEC-7366-981B-4FC552391391)
2025-05-02 06:30:42,793 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-02 06:31:42,876 - INFO - 开始同步昨天与今天的销售数据: 20250501 至 20250502
2025-05-02 06:31:42,876 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 06:31:42,876 - INFO - 查询参数: ('20250501', '20250502')
2025-05-02 06:31:42,923 - INFO - MySQL查询成功，时间段: 20250501 至 20250502，共获取 0 条记录
2025-05-02 06:31:42,923 - ERROR - 未获取到MySQL数据
2025-05-02 06:31:42,923 - INFO - 同步完成
2025-05-02 07:30:34,396 - INFO - 使用默认增量同步（当天更新数据）
2025-05-02 07:30:34,396 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 07:30:34,396 - INFO - 查询参数: ('2025-05-02',)
2025-05-02 07:30:34,459 - INFO - MySQL查询成功，增量数据（日期: 2025-05-02），共获取 24 条记录
2025-05-02 07:30:34,459 - INFO - 获取到 1 个日期需要处理: ['2025-05-01']
2025-05-02 07:30:34,459 - INFO - 开始处理日期: 2025-05-01
2025-05-02 07:30:34,459 - INFO - Request Parameters - Page 1:
2025-05-02 07:30:34,459 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 07:30:34,459 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 07:30:42,608 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D664AF39-6BDF-7FDB-A305-FCBAF9788AF9 Response: {'code': 'ServiceUnavailable', 'requestid': 'D664AF39-6BDF-7FDB-A305-FCBAF9788AF9', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D664AF39-6BDF-7FDB-A305-FCBAF9788AF9)
2025-05-02 07:30:42,608 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-02 07:31:42,713 - INFO - 开始同步昨天与今天的销售数据: 20250501 至 20250502
2025-05-02 07:31:42,713 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 07:31:42,713 - INFO - 查询参数: ('20250501', '20250502')
2025-05-02 07:31:42,760 - INFO - MySQL查询成功，时间段: 20250501 至 20250502，共获取 0 条记录
2025-05-02 07:31:42,760 - ERROR - 未获取到MySQL数据
2025-05-02 07:31:42,760 - INFO - 同步完成
2025-05-02 08:30:33,942 - INFO - 使用默认增量同步（当天更新数据）
2025-05-02 08:30:33,942 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 08:30:33,942 - INFO - 查询参数: ('2025-05-02',)
2025-05-02 08:30:33,989 - INFO - MySQL查询成功，增量数据（日期: 2025-05-02），共获取 35 条记录
2025-05-02 08:30:33,989 - INFO - 获取到 1 个日期需要处理: ['2025-05-01']
2025-05-02 08:30:34,005 - INFO - 开始处理日期: 2025-05-01
2025-05-02 08:30:34,005 - INFO - Request Parameters - Page 1:
2025-05-02 08:30:34,005 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 08:30:34,005 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 08:30:42,114 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3F19F4B1-4A43-73B8-9921-D1712D444FD6 Response: {'code': 'ServiceUnavailable', 'requestid': '3F19F4B1-4A43-73B8-9921-D1712D444FD6', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3F19F4B1-4A43-73B8-9921-D1712D444FD6)
2025-05-02 08:30:42,114 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-02 08:31:42,129 - INFO - 开始同步昨天与今天的销售数据: 20250501 至 20250502
2025-05-02 08:31:42,129 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 08:31:42,129 - INFO - 查询参数: ('20250501', '20250502')
2025-05-02 08:31:42,176 - INFO - MySQL查询成功，时间段: 20250501 至 20250502，共获取 0 条记录
2025-05-02 08:31:42,176 - ERROR - 未获取到MySQL数据
2025-05-02 08:31:42,176 - INFO - 同步完成
2025-05-02 09:30:34,170 - INFO - 使用默认增量同步（当天更新数据）
2025-05-02 09:30:34,170 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 09:30:34,170 - INFO - 查询参数: ('2025-05-02',)
2025-05-02 09:30:34,233 - INFO - MySQL查询成功，增量数据（日期: 2025-05-02），共获取 99 条记录
2025-05-02 09:30:34,233 - INFO - 获取到 1 个日期需要处理: ['2025-05-01']
2025-05-02 09:30:34,233 - INFO - 开始处理日期: 2025-05-01
2025-05-02 09:30:34,248 - INFO - Request Parameters - Page 1:
2025-05-02 09:30:34,248 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 09:30:34,248 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 09:30:42,358 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 7FCE5C3A-5EBD-7A30-BDCC-425C9C5297ED Response: {'code': 'ServiceUnavailable', 'requestid': '7FCE5C3A-5EBD-7A30-BDCC-425C9C5297ED', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 7FCE5C3A-5EBD-7A30-BDCC-425C9C5297ED)
2025-05-02 09:30:42,358 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-02 09:31:42,373 - INFO - 开始同步昨天与今天的销售数据: 20250501 至 20250502
2025-05-02 09:31:42,373 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 09:31:42,373 - INFO - 查询参数: ('20250501', '20250502')
2025-05-02 09:31:42,420 - INFO - MySQL查询成功，时间段: 20250501 至 20250502，共获取 0 条记录
2025-05-02 09:31:42,420 - ERROR - 未获取到MySQL数据
2025-05-02 09:31:42,420 - INFO - 同步完成
2025-05-02 10:30:33,898 - INFO - 使用默认增量同步（当天更新数据）
2025-05-02 10:30:33,898 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 10:30:33,913 - INFO - 查询参数: ('2025-05-02',)
2025-05-02 10:30:33,960 - INFO - MySQL查询成功，增量数据（日期: 2025-05-02），共获取 119 条记录
2025-05-02 10:30:33,960 - INFO - 获取到 1 个日期需要处理: ['2025-05-01']
2025-05-02 10:30:33,960 - INFO - 开始处理日期: 2025-05-01
2025-05-02 10:30:33,976 - INFO - Request Parameters - Page 1:
2025-05-02 10:30:33,976 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 10:30:33,976 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 10:30:42,116 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 44E90B47-0C8D-763A-A8EC-5593E42CC5EF Response: {'code': 'ServiceUnavailable', 'requestid': '44E90B47-0C8D-763A-A8EC-5593E42CC5EF', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 44E90B47-0C8D-763A-A8EC-5593E42CC5EF)
2025-05-02 10:30:42,116 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-02 10:31:42,132 - INFO - 开始同步昨天与今天的销售数据: 20250501 至 20250502
2025-05-02 10:31:42,132 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 10:31:42,132 - INFO - 查询参数: ('20250501', '20250502')
2025-05-02 10:31:42,179 - INFO - MySQL查询成功，时间段: 20250501 至 20250502，共获取 0 条记录
2025-05-02 10:31:42,179 - ERROR - 未获取到MySQL数据
2025-05-02 10:31:42,179 - INFO - 同步完成
2025-05-02 11:30:33,938 - INFO - 使用默认增量同步（当天更新数据）
2025-05-02 11:30:33,938 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 11:30:33,938 - INFO - 查询参数: ('2025-05-02',)
2025-05-02 11:30:33,985 - INFO - MySQL查询成功，增量数据（日期: 2025-05-02），共获取 125 条记录
2025-05-02 11:30:33,985 - INFO - 获取到 1 个日期需要处理: ['2025-05-01']
2025-05-02 11:30:33,985 - INFO - 开始处理日期: 2025-05-01
2025-05-02 11:30:34,000 - INFO - Request Parameters - Page 1:
2025-05-02 11:30:34,000 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 11:30:34,000 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 11:30:42,110 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B5075EAE-7393-7157-B476-51FB68E89198 Response: {'code': 'ServiceUnavailable', 'requestid': 'B5075EAE-7393-7157-B476-51FB68E89198', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B5075EAE-7393-7157-B476-51FB68E89198)
2025-05-02 11:30:42,110 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-02 11:31:42,125 - INFO - 开始同步昨天与今天的销售数据: 20250501 至 20250502
2025-05-02 11:31:42,125 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 11:31:42,125 - INFO - 查询参数: ('20250501', '20250502')
2025-05-02 11:31:42,172 - INFO - MySQL查询成功，时间段: 20250501 至 20250502，共获取 0 条记录
2025-05-02 11:31:42,172 - ERROR - 未获取到MySQL数据
2025-05-02 11:31:42,172 - INFO - 同步完成
2025-05-02 12:30:33,759 - INFO - 使用默认增量同步（当天更新数据）
2025-05-02 12:30:33,759 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 12:30:33,759 - INFO - 查询参数: ('2025-05-02',)
2025-05-02 12:30:33,821 - INFO - MySQL查询成功，增量数据（日期: 2025-05-02），共获取 127 条记录
2025-05-02 12:30:33,821 - INFO - 获取到 1 个日期需要处理: ['2025-05-01']
2025-05-02 12:30:33,821 - INFO - 开始处理日期: 2025-05-01
2025-05-02 12:30:33,821 - INFO - Request Parameters - Page 1:
2025-05-02 12:30:33,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 12:30:33,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 12:30:41,946 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C694A6A3-2A5D-772A-A49F-7A487B46E339 Response: {'code': 'ServiceUnavailable', 'requestid': 'C694A6A3-2A5D-772A-A49F-7A487B46E339', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C694A6A3-2A5D-772A-A49F-7A487B46E339)
2025-05-02 12:30:41,946 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-02 12:31:41,962 - INFO - 开始同步昨天与今天的销售数据: 20250501 至 20250502
2025-05-02 12:31:41,962 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 12:31:41,962 - INFO - 查询参数: ('20250501', '20250502')
2025-05-02 12:31:42,009 - INFO - MySQL查询成功，时间段: 20250501 至 20250502，共获取 0 条记录
2025-05-02 12:31:42,009 - ERROR - 未获取到MySQL数据
2025-05-02 12:31:42,009 - INFO - 同步完成
2025-05-02 13:30:33,940 - INFO - 使用默认增量同步（当天更新数据）
2025-05-02 13:30:33,940 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 13:30:33,940 - INFO - 查询参数: ('2025-05-02',)
2025-05-02 13:30:34,002 - INFO - MySQL查询成功，增量数据（日期: 2025-05-02），共获取 127 条记录
2025-05-02 13:30:34,002 - INFO - 获取到 1 个日期需要处理: ['2025-05-01']
2025-05-02 13:30:34,002 - INFO - 开始处理日期: 2025-05-01
2025-05-02 13:30:34,018 - INFO - Request Parameters - Page 1:
2025-05-02 13:30:34,018 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 13:30:34,018 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 13:30:42,143 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5CDA9C60-44AC-769F-8828-6883EA4F521E Response: {'code': 'ServiceUnavailable', 'requestid': '5CDA9C60-44AC-769F-8828-6883EA4F521E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 5CDA9C60-44AC-769F-8828-6883EA4F521E)
2025-05-02 13:30:42,143 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-02 13:31:42,158 - INFO - 开始同步昨天与今天的销售数据: 20250501 至 20250502
2025-05-02 13:31:42,158 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 13:31:42,158 - INFO - 查询参数: ('20250501', '20250502')
2025-05-02 13:31:42,205 - INFO - MySQL查询成功，时间段: 20250501 至 20250502，共获取 0 条记录
2025-05-02 13:31:42,205 - ERROR - 未获取到MySQL数据
2025-05-02 13:31:42,205 - INFO - 同步完成
2025-05-02 14:30:33,808 - INFO - 使用默认增量同步（当天更新数据）
2025-05-02 14:30:33,808 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 14:30:33,808 - INFO - 查询参数: ('2025-05-02',)
2025-05-02 14:30:33,855 - INFO - MySQL查询成功，增量数据（日期: 2025-05-02），共获取 128 条记录
2025-05-02 14:30:33,855 - INFO - 获取到 1 个日期需要处理: ['2025-05-01']
2025-05-02 14:30:33,870 - INFO - 开始处理日期: 2025-05-01
2025-05-02 14:30:33,870 - INFO - Request Parameters - Page 1:
2025-05-02 14:30:33,870 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 14:30:33,870 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 14:30:41,979 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: DA4C8C1F-1AA6-7BBA-A26F-848D2BE76345 Response: {'code': 'ServiceUnavailable', 'requestid': 'DA4C8C1F-1AA6-7BBA-A26F-848D2BE76345', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: DA4C8C1F-1AA6-7BBA-A26F-848D2BE76345)
2025-05-02 14:30:41,995 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-02 14:31:42,010 - INFO - 开始同步昨天与今天的销售数据: 20250501 至 20250502
2025-05-02 14:31:42,010 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 14:31:42,010 - INFO - 查询参数: ('20250501', '20250502')
2025-05-02 14:31:42,057 - INFO - MySQL查询成功，时间段: 20250501 至 20250502，共获取 0 条记录
2025-05-02 14:31:42,057 - ERROR - 未获取到MySQL数据
2025-05-02 14:31:42,057 - INFO - 同步完成
2025-05-02 15:30:33,785 - INFO - 使用默认增量同步（当天更新数据）
2025-05-02 15:30:33,785 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 15:30:33,785 - INFO - 查询参数: ('2025-05-02',)
2025-05-02 15:30:33,848 - INFO - MySQL查询成功，增量数据（日期: 2025-05-02），共获取 128 条记录
2025-05-02 15:30:33,848 - INFO - 获取到 1 个日期需要处理: ['2025-05-01']
2025-05-02 15:30:33,848 - INFO - 开始处理日期: 2025-05-01
2025-05-02 15:30:33,848 - INFO - Request Parameters - Page 1:
2025-05-02 15:30:33,848 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 15:30:33,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 15:30:41,972 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 00531CE3-844C-7B00-99FC-D44D1F0F689D Response: {'code': 'ServiceUnavailable', 'requestid': '00531CE3-844C-7B00-99FC-D44D1F0F689D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 00531CE3-844C-7B00-99FC-D44D1F0F689D)
2025-05-02 15:30:41,972 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-02 15:31:41,988 - INFO - 开始同步昨天与今天的销售数据: 20250501 至 20250502
2025-05-02 15:31:41,988 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 15:31:41,988 - INFO - 查询参数: ('20250501', '20250502')
2025-05-02 15:31:42,035 - INFO - MySQL查询成功，时间段: 20250501 至 20250502，共获取 0 条记录
2025-05-02 15:31:42,035 - ERROR - 未获取到MySQL数据
2025-05-02 15:31:42,035 - INFO - 同步完成
2025-05-02 16:30:33,762 - INFO - 使用默认增量同步（当天更新数据）
2025-05-02 16:30:33,762 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 16:30:33,762 - INFO - 查询参数: ('2025-05-02',)
2025-05-02 16:30:33,809 - INFO - MySQL查询成功，增量数据（日期: 2025-05-02），共获取 128 条记录
2025-05-02 16:30:33,809 - INFO - 获取到 1 个日期需要处理: ['2025-05-01']
2025-05-02 16:30:33,825 - INFO - 开始处理日期: 2025-05-01
2025-05-02 16:30:33,825 - INFO - Request Parameters - Page 1:
2025-05-02 16:30:33,825 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 16:30:33,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 16:30:41,934 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: BF8FD951-C677-7495-95F2-56E6CEA7D12A Response: {'code': 'ServiceUnavailable', 'requestid': 'BF8FD951-C677-7495-95F2-56E6CEA7D12A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: BF8FD951-C677-7495-95F2-56E6CEA7D12A)
2025-05-02 16:30:41,934 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-02 16:31:41,950 - INFO - 开始同步昨天与今天的销售数据: 20250501 至 20250502
2025-05-02 16:31:41,950 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 16:31:41,950 - INFO - 查询参数: ('20250501', '20250502')
2025-05-02 16:31:41,996 - INFO - MySQL查询成功，时间段: 20250501 至 20250502，共获取 0 条记录
2025-05-02 16:31:41,996 - ERROR - 未获取到MySQL数据
2025-05-02 16:31:41,996 - INFO - 同步完成
2025-05-02 17:30:33,943 - INFO - 使用默认增量同步（当天更新数据）
2025-05-02 17:30:33,943 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 17:30:33,943 - INFO - 查询参数: ('2025-05-02',)
2025-05-02 17:30:33,990 - INFO - MySQL查询成功，增量数据（日期: 2025-05-02），共获取 128 条记录
2025-05-02 17:30:33,990 - INFO - 获取到 1 个日期需要处理: ['2025-05-01']
2025-05-02 17:30:33,990 - INFO - 开始处理日期: 2025-05-01
2025-05-02 17:30:34,006 - INFO - Request Parameters - Page 1:
2025-05-02 17:30:34,006 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 17:30:34,006 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 17:30:42,115 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: FE5C6B92-655A-728E-AD87-17270B90E2C0 Response: {'code': 'ServiceUnavailable', 'requestid': 'FE5C6B92-655A-728E-AD87-17270B90E2C0', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: FE5C6B92-655A-728E-AD87-17270B90E2C0)
2025-05-02 17:30:42,115 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-02 17:31:42,130 - INFO - 开始同步昨天与今天的销售数据: 20250501 至 20250502
2025-05-02 17:31:42,130 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 17:31:42,130 - INFO - 查询参数: ('20250501', '20250502')
2025-05-02 17:31:42,177 - INFO - MySQL查询成功，时间段: 20250501 至 20250502，共获取 0 条记录
2025-05-02 17:31:42,177 - ERROR - 未获取到MySQL数据
2025-05-02 17:31:42,177 - INFO - 同步完成
2025-05-02 18:30:33,968 - INFO - 使用默认增量同步（当天更新数据）
2025-05-02 18:30:33,968 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 18:30:33,968 - INFO - 查询参数: ('2025-05-02',)
2025-05-02 18:30:34,030 - INFO - MySQL查询成功，增量数据（日期: 2025-05-02），共获取 128 条记录
2025-05-02 18:30:34,030 - INFO - 获取到 1 个日期需要处理: ['2025-05-01']
2025-05-02 18:30:34,030 - INFO - 开始处理日期: 2025-05-01
2025-05-02 18:30:34,030 - INFO - Request Parameters - Page 1:
2025-05-02 18:30:34,030 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 18:30:34,030 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 18:30:42,171 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B7FD030E-A7C2-7E3A-907E-B51E2D3E03A3 Response: {'code': 'ServiceUnavailable', 'requestid': 'B7FD030E-A7C2-7E3A-907E-B51E2D3E03A3', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B7FD030E-A7C2-7E3A-907E-B51E2D3E03A3)
2025-05-02 18:30:42,171 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-02 18:31:42,186 - INFO - 开始同步昨天与今天的销售数据: 20250501 至 20250502
2025-05-02 18:31:42,186 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 18:31:42,186 - INFO - 查询参数: ('20250501', '20250502')
2025-05-02 18:31:42,233 - INFO - MySQL查询成功，时间段: 20250501 至 20250502，共获取 0 条记录
2025-05-02 18:31:42,233 - ERROR - 未获取到MySQL数据
2025-05-02 18:31:42,233 - INFO - 同步完成
2025-05-02 19:30:33,908 - INFO - 使用默认增量同步（当天更新数据）
2025-05-02 19:30:33,908 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 19:30:33,908 - INFO - 查询参数: ('2025-05-02',)
2025-05-02 19:30:33,971 - INFO - MySQL查询成功，增量数据（日期: 2025-05-02），共获取 128 条记录
2025-05-02 19:30:33,971 - INFO - 获取到 1 个日期需要处理: ['2025-05-01']
2025-05-02 19:30:33,971 - INFO - 开始处理日期: 2025-05-01
2025-05-02 19:30:33,971 - INFO - Request Parameters - Page 1:
2025-05-02 19:30:33,971 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 19:30:33,971 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 19:30:42,111 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 28A570FE-BBDF-74FF-92C5-20736F535BEF Response: {'code': 'ServiceUnavailable', 'requestid': '28A570FE-BBDF-74FF-92C5-20736F535BEF', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 28A570FE-BBDF-74FF-92C5-20736F535BEF)
2025-05-02 19:30:42,111 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-02 19:31:42,127 - INFO - 开始同步昨天与今天的销售数据: 20250501 至 20250502
2025-05-02 19:31:42,127 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 19:31:42,127 - INFO - 查询参数: ('20250501', '20250502')
2025-05-02 19:31:42,173 - INFO - MySQL查询成功，时间段: 20250501 至 20250502，共获取 0 条记录
2025-05-02 19:31:42,173 - ERROR - 未获取到MySQL数据
2025-05-02 19:31:42,173 - INFO - 同步完成
2025-05-02 20:30:33,855 - INFO - 使用默认增量同步（当天更新数据）
2025-05-02 20:30:33,855 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 20:30:33,855 - INFO - 查询参数: ('2025-05-02',)
2025-05-02 20:30:33,917 - INFO - MySQL查询成功，增量数据（日期: 2025-05-02），共获取 128 条记录
2025-05-02 20:30:33,917 - INFO - 获取到 1 个日期需要处理: ['2025-05-01']
2025-05-02 20:30:33,917 - INFO - 开始处理日期: 2025-05-01
2025-05-02 20:30:33,933 - INFO - Request Parameters - Page 1:
2025-05-02 20:30:33,933 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 20:30:33,933 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 20:30:42,058 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: ********-46BF-7316-AA04-3D44C8724623 Response: {'code': 'ServiceUnavailable', 'requestid': '********-46BF-7316-AA04-3D44C8724623', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: ********-46BF-7316-AA04-3D44C8724623)
2025-05-02 20:30:42,058 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-05-02 20:31:42,073 - INFO - 开始同步昨天与今天的销售数据: 20250501 至 20250502
2025-05-02 20:31:42,073 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 20:31:42,073 - INFO - 查询参数: ('20250501', '20250502')
2025-05-02 20:31:42,120 - INFO - MySQL查询成功，时间段: 20250501 至 20250502，共获取 0 条记录
2025-05-02 20:31:42,120 - ERROR - 未获取到MySQL数据
2025-05-02 20:31:42,120 - INFO - 同步完成
2025-05-02 21:30:33,707 - INFO - 使用默认增量同步（当天更新数据）
2025-05-02 21:30:33,707 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 21:30:33,707 - INFO - 查询参数: ('2025-05-02',)
2025-05-02 21:30:33,770 - INFO - MySQL查询成功，增量数据（日期: 2025-05-02），共获取 131 条记录
2025-05-02 21:30:33,770 - INFO - 获取到 2 个日期需要处理: ['2025-05-01', '2025-05-02']
2025-05-02 21:30:33,770 - INFO - 开始处理日期: 2025-05-01
2025-05-02 21:30:33,770 - INFO - Request Parameters - Page 1:
2025-05-02 21:30:33,770 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:30:33,770 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:30:41,879 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: FFAC17FC-AEB3-7D9F-AF7C-B782CDE228A7 Response: {'code': 'ServiceUnavailable', 'requestid': 'FFAC17FC-AEB3-7D9F-AF7C-B782CDE228A7', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: FFAC17FC-AEB3-7D9F-AF7C-B782CDE228A7)
2025-05-02 21:30:41,879 - INFO - 开始处理日期: 2025-05-02
2025-05-02 21:30:41,879 - INFO - Request Parameters - Page 1:
2025-05-02 21:30:41,879 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 21:30:41,879 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 21:30:50,020 - ERROR - 处理日期 2025-05-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E5BF9E3E-03DB-7DF4-89D0-93A132EFA046 Response: {'code': 'ServiceUnavailable', 'requestid': 'E5BF9E3E-03DB-7DF4-89D0-93A132EFA046', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E5BF9E3E-03DB-7DF4-89D0-93A132EFA046)
2025-05-02 21:30:50,020 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-05-02 21:31:50,035 - INFO - 开始同步昨天与今天的销售数据: 20250501 至 20250502
2025-05-02 21:31:50,035 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 21:31:50,035 - INFO - 查询参数: ('20250501', '20250502')
2025-05-02 21:31:50,082 - INFO - MySQL查询成功，时间段: 20250501 至 20250502，共获取 0 条记录
2025-05-02 21:31:50,082 - ERROR - 未获取到MySQL数据
2025-05-02 21:31:50,082 - INFO - 同步完成
2025-05-02 22:30:33,890 - INFO - 使用默认增量同步（当天更新数据）
2025-05-02 22:30:33,890 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 22:30:33,890 - INFO - 查询参数: ('2025-05-02',)
2025-05-02 22:30:33,952 - INFO - MySQL查询成功，增量数据（日期: 2025-05-02），共获取 212 条记录
2025-05-02 22:30:33,952 - INFO - 获取到 3 个日期需要处理: ['2025-04-22', '2025-05-01', '2025-05-02']
2025-05-02 22:30:33,952 - INFO - 开始处理日期: 2025-04-22
2025-05-02 22:30:33,968 - INFO - Request Parameters - Page 1:
2025-05-02 22:30:33,968 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 22:30:33,968 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745251200000, 1745337599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 22:30:42,093 - ERROR - 处理日期 2025-04-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 9E409D88-A70F-794C-BD8D-9C6DFC3F1F37 Response: {'code': 'ServiceUnavailable', 'requestid': '9E409D88-A70F-794C-BD8D-9C6DFC3F1F37', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 9E409D88-A70F-794C-BD8D-9C6DFC3F1F37)
2025-05-02 22:30:42,093 - INFO - 开始处理日期: 2025-05-01
2025-05-02 22:30:42,108 - INFO - Request Parameters - Page 1:
2025-05-02 22:30:42,108 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 22:30:42,108 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 22:30:42,921 - INFO - Response - Page 1:
2025-05-02 22:30:42,921 - INFO - 第 1 页获取到 100 条记录
2025-05-02 22:30:43,124 - INFO - Request Parameters - Page 2:
2025-05-02 22:30:43,124 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 22:30:43,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 22:30:51,249 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 147C56AF-1040-7162-8D9F-9D6B3AC9025F Response: {'code': 'ServiceUnavailable', 'requestid': '147C56AF-1040-7162-8D9F-9D6B3AC9025F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 147C56AF-1040-7162-8D9F-9D6B3AC9025F)
2025-05-02 22:30:51,249 - INFO - 开始处理日期: 2025-05-02
2025-05-02 22:30:51,249 - INFO - Request Parameters - Page 1:
2025-05-02 22:30:51,249 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 22:30:51,249 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 22:30:51,390 - ERROR - 处理日期 2025-05-02 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: CB4A199B-7A5A-7BD4-BCD7-E43C706F63E1 Response: {'requestid': 'CB4A199B-7A5A-7BD4-BCD7-E43C706F63E1', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: CB4A199B-7A5A-7BD4-BCD7-E43C706F63E1)
2025-05-02 22:30:51,390 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 3 条
2025-05-02 22:31:51,405 - INFO - 开始同步昨天与今天的销售数据: 20250501 至 20250502
2025-05-02 22:31:51,405 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 22:31:51,405 - INFO - 查询参数: ('20250501', '20250502')
2025-05-02 22:31:51,452 - INFO - MySQL查询成功，时间段: 20250501 至 20250502，共获取 0 条记录
2025-05-02 22:31:51,452 - ERROR - 未获取到MySQL数据
2025-05-02 22:31:51,452 - INFO - 同步完成
2025-05-02 23:30:34,586 - INFO - 使用默认增量同步（当天更新数据）
2025-05-02 23:30:34,586 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 23:30:34,586 - INFO - 查询参数: ('2025-05-02',)
2025-05-02 23:30:34,665 - INFO - MySQL查询成功，增量数据（日期: 2025-05-02），共获取 260 条记录
2025-05-02 23:30:34,665 - INFO - 获取到 3 个日期需要处理: ['2025-04-22', '2025-05-01', '2025-05-02']
2025-05-02 23:30:34,665 - INFO - 开始处理日期: 2025-04-22
2025-05-02 23:30:34,665 - INFO - Request Parameters - Page 1:
2025-05-02 23:30:34,665 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 23:30:34,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1745251200000, 1745337599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 23:30:42,783 - ERROR - 处理日期 2025-04-22 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A57E80B0-6D85-79E7-B06F-C518A40C23C5 Response: {'code': 'ServiceUnavailable', 'requestid': 'A57E80B0-6D85-79E7-B06F-C518A40C23C5', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A57E80B0-6D85-79E7-B06F-C518A40C23C5)
2025-05-02 23:30:42,783 - INFO - 开始处理日期: 2025-05-01
2025-05-02 23:30:42,783 - INFO - Request Parameters - Page 1:
2025-05-02 23:30:42,783 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 23:30:42,783 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746028800000, 1746115199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 23:30:42,939 - ERROR - 处理日期 2025-05-01 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 1ADE6FAB-BD82-7065-AA23-36154AA5BF79 Response: {'requestid': '1ADE6FAB-BD82-7065-AA23-36154AA5BF79', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: 1ADE6FAB-BD82-7065-AA23-36154AA5BF79)
2025-05-02 23:30:42,939 - INFO - 开始处理日期: 2025-05-02
2025-05-02 23:30:42,939 - INFO - Request Parameters - Page 1:
2025-05-02 23:30:42,939 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-02 23:30:42,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1746115200000, 1746201599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-02 23:30:43,096 - ERROR - 处理日期 2025-05-02 时发生错误: 获取表单数据失败: Error: innerError code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: EEF99032-C128-7385-B381-07D6F83BD04F Response: {'requestid': 'EEF99032-C128-7385-B381-07D6F83BD04F', 'code': 'innerError', 'message': '宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试', 'statusCode': 500} (错误码: innerError, 错误信息: code: 500, 宜搭服务内部异常信息:当前并发访问人数过多，请稍后重试 request id: EEF99032-C128-7385-B381-07D6F83BD04F)
2025-05-02 23:30:43,096 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 3 条
2025-05-02 23:31:43,175 - INFO - 开始同步昨天与今天的销售数据: 20250501 至 20250502
2025-05-02 23:31:43,175 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code, b.name as project_name,
                    a.store_code, c.name as store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') as sales_date,
                    SUM(a.online_amount) as online_amount,
                    SUM(a.offline_amount) as offline_amount,
                    SUM(a.total_amount) as total_amount,
                    SUM(order_count) as order_count
                FROM yx_b_sales_record a
                JOIN yx_b_projects b ON a.project_code = b.code
                JOIN yx_b_tenants c ON a.store_code = c.code
                WHERE a.status = 2 
                    AND a.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
                GROUP BY a.project_code, b.name, a.store_code, c.name, 
                         DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d')
            
2025-05-02 23:31:43,175 - INFO - 查询参数: ('20250501', '20250502')
2025-05-02 23:31:43,222 - INFO - MySQL查询成功，时间段: 20250501 至 20250502，共获取 0 条记录
2025-05-02 23:31:43,222 - ERROR - 未获取到MySQL数据
2025-05-02 23:31:43,222 - INFO - 同步完成
