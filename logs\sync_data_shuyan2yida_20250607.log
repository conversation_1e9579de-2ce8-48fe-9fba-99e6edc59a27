2025-06-07 08:00:03,495 - INFO - ==================================================
2025-06-07 08:00:03,495 - INFO - 程序启动 - 版本 v1.0.0
2025-06-07 08:00:03,495 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250607.log
2025-06-07 08:00:03,495 - INFO - ==================================================
2025-06-07 08:00:03,495 - INFO - 程序入口点: __main__
2025-06-07 08:00:03,495 - INFO - ==================================================
2025-06-07 08:00:03,495 - INFO - 程序启动 - 版本 v1.0.1
2025-06-07 08:00:03,495 - INFO - 日志文件: logs\sync_data_shuyan2yida_20250607.log
2025-06-07 08:00:03,495 - INFO - ==================================================
2025-06-07 08:00:03,792 - INFO - 数据库文件已存在: data\sales_data.db
2025-06-07 08:00:03,792 - INFO - sales_data表已存在，无需创建
2025-06-07 08:00:03,792 - INFO - 月度汇总表 'sales_data_month' 已存在
2025-06-07 08:00:03,792 - INFO - DataSyncManager初始化完成
2025-06-07 08:00:03,792 - INFO - 未提供日期参数，使用默认值
2025-06-07 08:00:03,792 - INFO - 开始执行综合数据同步，参数: start_date=None, end_date=None
2025-06-07 08:00:03,792 - INFO - 开始综合数据同步流程...
2025-06-07 08:00:03,792 - INFO - 正在获取数衍平台日销售数据...
2025-06-07 08:00:03,792 - INFO - 查询数衍平台数据，时间段为: 2025-04-07, 2025-06-06
2025-06-07 08:00:03,792 - INFO - 正在获取********至********的数据
2025-06-07 08:00:03,792 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-07 08:00:03,792 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'B9E97135C86F0237087FA13D3327758F'}
2025-06-07 08:00:05,651 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-07 08:00:05,651 - INFO - 过滤后保留 426 条记录
2025-06-07 08:00:07,651 - INFO - 正在获取********至********的数据
2025-06-07 08:00:07,651 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-07 08:00:07,651 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '********************************'}
2025-06-07 08:00:08,855 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-07 08:00:08,870 - INFO - 过滤后保留 423 条记录
2025-06-07 08:00:10,886 - INFO - 正在获取********至********的数据
2025-06-07 08:00:10,886 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-07 08:00:10,886 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '********************************'}
2025-06-07 08:00:11,886 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-07 08:00:11,886 - INFO - 过滤后保留 432 条记录
2025-06-07 08:00:13,901 - INFO - 正在获取********至********的数据
2025-06-07 08:00:13,901 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-07 08:00:13,901 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'CB24AE243C09D022C94ADDD8F0A1EF39'}
2025-06-07 08:00:14,854 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-07 08:00:14,854 - INFO - 过滤后保留 434 条记录
2025-06-07 08:00:16,870 - INFO - 正在获取********至********的数据
2025-06-07 08:00:16,870 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-07 08:00:16,870 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '53D46293D4C552E7DAF805FAE3040E80'}
2025-06-07 08:00:17,745 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-07 08:00:17,745 - INFO - 过滤后保留 424 条记录
2025-06-07 08:00:19,761 - INFO - 正在获取********至********的数据
2025-06-07 08:00:19,761 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-07 08:00:19,761 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '8EDB54641990BE7E8699720DFC7E01C1'}
2025-06-07 08:00:20,636 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-07 08:00:20,636 - INFO - 过滤后保留 436 条记录
2025-06-07 08:00:22,636 - INFO - 正在获取********至********的数据
2025-06-07 08:00:22,636 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-07 08:00:22,636 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '6A7A2AC22FF74BEC344000A03EE08EBF'}
2025-06-07 08:00:23,511 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-07 08:00:23,526 - INFO - 过滤后保留 431 条记录
2025-06-07 08:00:25,542 - INFO - 正在获取********至********的数据
2025-06-07 08:00:25,542 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-07 08:00:25,542 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'B3702A808110345887DE22E1D33C26B5'}
2025-06-07 08:00:26,417 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-07 08:00:26,417 - INFO - 过滤后保留 425 条记录
2025-06-07 08:00:28,433 - INFO - 正在获取********至********的数据
2025-06-07 08:00:28,433 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-07 08:00:28,433 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '142D5812A8E0E29416D05E23F424DCA3'}
2025-06-07 08:00:29,354 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-07 08:00:29,354 - INFO - 过滤后保留 414 条记录
2025-06-07 08:00:31,370 - INFO - 正在获取********至********的数据
2025-06-07 08:00:31,370 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-07 08:00:31,370 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'CEC7D1955D04590FE20F17AEA73E9B7C'}
2025-06-07 08:00:32,183 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-07 08:00:32,198 - INFO - 过滤后保留 427 条记录
2025-06-07 08:00:34,198 - INFO - 正在获取********至********的数据
2025-06-07 08:00:34,198 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-07 08:00:34,198 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '7E04D2AB1FBB787892CB54E0293EDB14'}
2025-06-07 08:00:34,964 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-07 08:00:34,964 - INFO - 过滤后保留 428 条记录
2025-06-07 08:00:36,979 - INFO - 正在获取********至********的数据
2025-06-07 08:00:36,979 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-07 08:00:36,979 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '69428C7E79A1E4ED7E3D3CB9807515BB'}
2025-06-07 08:00:37,995 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-07 08:00:37,995 - INFO - 过滤后保留 429 条记录
2025-06-07 08:00:40,011 - INFO - 正在获取********至********的数据
2025-06-07 08:00:40,011 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-07 08:00:40,011 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'E1CCB3E0ADE8CE071D5E2CF9FDD5D7D3'}
2025-06-07 08:00:40,901 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-07 08:00:40,917 - INFO - 过滤后保留 427 条记录
2025-06-07 08:00:42,932 - INFO - 正在获取********至********的数据
2025-06-07 08:00:42,932 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-07 08:00:42,932 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'B69BE9A89AFAA6EDA412FC571FFD7250'}
2025-06-07 08:00:43,776 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-07 08:00:43,792 - INFO - 过滤后保留 428 条记录
2025-06-07 08:00:45,792 - INFO - 正在获取********至********的数据
2025-06-07 08:00:45,792 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-07 08:00:45,792 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'D3E7E1F5ED78F1136D2F28F986ACF60C'}
2025-06-07 08:00:46,667 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-07 08:00:46,667 - INFO - 过滤后保留 417 条记录
2025-06-07 08:00:48,682 - INFO - 正在获取********至********的数据
2025-06-07 08:00:48,682 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-07 08:00:48,682 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '133E62F8440D2C117721C4A6AE0E9447'}
2025-06-07 08:00:49,448 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-07 08:00:49,448 - INFO - 过滤后保留 415 条记录
2025-06-07 08:00:51,464 - INFO - 正在获取********至********的数据
2025-06-07 08:00:51,464 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-07 08:00:51,464 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'EEC828933DC38AB2A11D9BE4B556B454'}
2025-06-07 08:00:52,307 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-07 08:00:52,307 - INFO - 过滤后保留 433 条记录
2025-06-07 08:00:54,307 - INFO - 正在获取********至********的数据
2025-06-07 08:00:54,307 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-07 08:00:54,307 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'F719117032B872C7A0296C1F8576D4A3'}
2025-06-07 08:00:55,089 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-07 08:00:55,089 - INFO - 过滤后保留 433 条记录
2025-06-07 08:00:57,104 - INFO - 正在获取********至********的数据
2025-06-07 08:00:57,104 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-07 08:00:57,104 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'F12B1153CCC3399DB69963076DEB2CD9'}
2025-06-07 08:00:57,885 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-07 08:00:57,885 - INFO - 过滤后保留 417 条记录
2025-06-07 08:00:59,901 - INFO - 正在获取********至********的数据
2025-06-07 08:00:59,901 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-07 08:00:59,901 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'A7818AB77E46F26F236100361A26C092'}
2025-06-07 08:01:00,979 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-07 08:01:00,979 - INFO - 过滤后保留 420 条记录
2025-06-07 08:01:02,995 - INFO - 正在获取********至********的数据
2025-06-07 08:01:02,995 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-07 08:01:02,995 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '99B5DE0F6BD3602847FC8C582B98CB8F'}
2025-06-07 08:01:04,010 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-07 08:01:04,010 - INFO - 过滤后保留 431 条记录
2025-06-07 08:01:06,026 - INFO - 正在获取********至********的数据
2025-06-07 08:01:06,026 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-07 08:01:06,026 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '16964037FC2570BA637B036B3836527A'}
2025-06-07 08:01:06,792 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-07 08:01:06,792 - INFO - 过滤后保留 423 条记录
2025-06-07 08:01:08,807 - INFO - 正在获取********至********的数据
2025-06-07 08:01:08,807 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-07 08:01:08,807 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'D262D8BA6AC905EBC17D93B164B34FED'}
2025-06-07 08:01:09,714 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-07 08:01:09,714 - INFO - 过滤后保留 416 条记录
2025-06-07 08:01:11,729 - INFO - 正在获取********至********的数据
2025-06-07 08:01:11,729 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-07 08:01:11,729 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '8CAC1346805C8BD4E957C6E524DB1B59'}
2025-06-07 08:01:12,495 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-07 08:01:12,495 - INFO - 过滤后保留 423 条记录
2025-06-07 08:01:14,510 - INFO - 正在获取********至********的数据
2025-06-07 08:01:14,510 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-07 08:01:14,510 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '2964C351EB3D9A1D177471DF0A3E53D1'}
2025-06-07 08:01:15,229 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-07 08:01:15,229 - INFO - 过滤后保留 414 条记录
2025-06-07 08:01:17,245 - INFO - 正在获取********至********的数据
2025-06-07 08:01:17,245 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-07 08:01:17,245 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '25EAD7DAE12497CAEDB4F908B7361DF7'}
2025-06-07 08:01:18,135 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-07 08:01:18,135 - INFO - 过滤后保留 413 条记录
2025-06-07 08:01:20,151 - INFO - 正在获取********至********的数据
2025-06-07 08:01:20,151 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-07 08:01:20,151 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '30CEAAC885CD21B6B23327CE9F65D3D1'}
2025-06-07 08:01:21,010 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-07 08:01:21,010 - INFO - 过滤后保留 414 条记录
2025-06-07 08:01:23,026 - INFO - 正在获取********至********的数据
2025-06-07 08:01:23,026 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-07 08:01:23,026 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'A090BC759DACAF147BC5DD5EBAD89F7D'}
2025-06-07 08:01:23,807 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-07 08:01:23,807 - INFO - 过滤后保留 415 条记录
2025-06-07 08:01:25,823 - INFO - 正在获取********至********的数据
2025-06-07 08:01:25,823 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-07 08:01:25,823 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '9ECAC3D58FEA8841B475F14992692277'}
2025-06-07 08:01:26,620 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-07 08:01:26,635 - INFO - 过滤后保留 400 条记录
2025-06-07 08:01:28,651 - INFO - 正在获取********至********的数据
2025-06-07 08:01:28,651 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-07 08:01:28,651 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': '8B461C816C7699A9A06E5FCD420D98E5'}
2025-06-07 08:01:29,510 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-07 08:01:29,526 - INFO - 过滤后保留 396 条记录
2025-06-07 08:01:31,526 - INFO - 正在获取********至********的数据
2025-06-07 08:01:31,526 - INFO - Request URL: http://api.gooagoo.com/oapi/rest
2025-06-07 08:01:31,526 - INFO - Request Params: {'appId': 'a5274b7e5d9a41939346c33c2c3443db', 'appKey': '2c9a5a628e7dab16018f5b055f3d0002', 'method': 'gogo.open.auto.routing', 'lowerMethod': 'com.gooagoo.open.api.salequery', 'timestamp': '**************', 'messageFormat': 'Json', 'v': '1.0', 'signMethod': 'MD5', 'data': '{"fromDate": "********", "toDate": "********", "shopIds": ["1ETDLFB9DIMQME7Q2OVD93ISAI00189O", "1GD9P85P7PPH4L7Q2OV3OBNS49001KPE", "1HFLOR99TBR11L6UBHOUTGCK1C001A3F", "1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV", "1HRIS0CDPQR2GM6E7AERKQ83MS0014QU", "1HRIS7255PESAA7AV8LHQQGIH8001KNH", "1HSJMRFR3MBRV37AV8LHQQGIT4001C3D"]}', 'sign': 'BE75FCCB32EDE782C7977E6EFF69DBC6'}
2025-06-07 08:01:32,213 - INFO - Response: {'rescode': '0', 'resmsg': '成功'}
2025-06-07 08:01:32,213 - INFO - 过滤后保留 202 条记录
2025-06-07 08:01:34,213 - INFO - 开始保存数据到SQLite数据库，共 12866 条记录待处理
2025-06-07 08:01:34,541 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCE9EG92S1SB0I86N3H2U188001EHE, sale_time=2025-04-28
2025-06-07 08:01:34,541 - INFO - 变更字段: amount: 2507 -> 3434, count: 6 -> 7, instore_amount: 2507.0 -> 3434.0, instore_count: 6 -> 7
2025-06-07 08:01:34,573 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGERRPAJGP042F6DB81RHB6001P7E, sale_time=2025-05-01
2025-06-07 08:01:34,573 - INFO - 变更字段: recommend_amount: 0.0 -> 4284.56, daily_bill_amount: 0.0 -> 4284.56
2025-06-07 08:01:34,573 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE85EF5FB90I86N3H2U1U9001F7F, sale_time=2025-05-01
2025-06-07 08:01:34,573 - INFO - 变更字段: recommend_amount: 938.7 -> 42043.02, daily_bill_amount: 938.7 -> 42043.02
2025-06-07 08:01:34,573 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDEISQT8KE0I86N3H2U1GT001EQ3, sale_time=2025-05-01
2025-06-07 08:01:34,573 - INFO - 变更字段: amount: 35084 -> 30815, instore_amount: 37535.5 -> 33266.8
2025-06-07 08:01:34,604 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDEISQT8KE0I86N3H2U1GT001EQ3, sale_time=2025-05-04
2025-06-07 08:01:34,604 - INFO - 变更字段: amount: 19821 -> 18084, instore_amount: 21764.1 -> 18085.6
2025-06-07 08:01:34,620 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGERRPAJGP042F6DB81RHB6001P7E, sale_time=2025-05-05
2025-06-07 08:01:34,620 - INFO - 变更字段: recommend_amount: 2724.6 -> 3275.41, daily_bill_amount: 2724.6 -> 3275.41
2025-06-07 08:01:34,620 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDEISQT8KE0I86N3H2U1GT001EQ3, sale_time=2025-05-06
2025-06-07 08:01:34,620 - INFO - 变更字段: amount: 5414 -> 4185, instore_amount: 5414.3 -> 4185.5
2025-06-07 08:01:34,620 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDEISQT8KE0I86N3H2U1GT001EQ3, sale_time=2025-05-05
2025-06-07 08:01:34,620 - INFO - 变更字段: amount: 49794 -> 48594, instore_amount: 49794.05 -> 48594.05
2025-06-07 08:01:34,635 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDEISQT8KE0I86N3H2U1GT001EQ3, sale_time=2025-05-07
2025-06-07 08:01:34,635 - INFO - 变更字段: amount: 3674 -> 2876, instore_amount: 3674.7 -> 2876.2
2025-06-07 08:01:34,635 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDSC7N3PHM0I86N3H2U10T001EA3, sale_time=2025-05-07
2025-06-07 08:01:34,635 - INFO - 变更字段: amount: 2657 -> 2643
2025-06-07 08:01:34,651 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDEISQT8KE0I86N3H2U1GT001EQ3, sale_time=2025-05-10
2025-06-07 08:01:34,651 - INFO - 变更字段: amount: 6018 -> 4617, instore_amount: 6678.1 -> 5277.0
2025-06-07 08:01:34,666 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDSC7N3PHM0I86N3H2U10T001EA3, sale_time=2025-05-10
2025-06-07 08:01:34,666 - INFO - 变更字段: amount: 4571 -> 4609, count: 245 -> 247, online_amount: 3318.92 -> 3356.12, online_count: 157 -> 159
2025-06-07 08:01:34,666 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGERRPAJGP042F6DB81RHB6001P7E, sale_time=2025-05-12
2025-06-07 08:01:34,666 - INFO - 变更字段: recommend_amount: 0.0 -> 2708.3, daily_bill_amount: 0.0 -> 2708.3
2025-06-07 08:01:34,682 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIS7OTCALF7AV8LHQQGIDO001EK1, sale_time=2025-05-11
2025-06-07 08:01:34,682 - INFO - 变更字段: recommend_amount: 44434.01 -> 23674.85, daily_bill_amount: 44434.01 -> 23674.85
2025-06-07 08:01:34,713 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGERRPAJGP042F6DB81RHB6001P7E, sale_time=2025-05-16
2025-06-07 08:01:34,713 - INFO - 变更字段: recommend_amount: 4265.0 -> 4758.34, daily_bill_amount: 4265.0 -> 4758.34
2025-06-07 08:01:34,713 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDEISQT8KE0I86N3H2U1GT001EQ3, sale_time=2025-05-16
2025-06-07 08:01:34,713 - INFO - 变更字段: amount: 16683 -> 13022, instore_amount: 17522.4 -> 13862.1
2025-06-07 08:01:34,713 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDEISQT8KE0I86N3H2U1GT001EQ3, sale_time=2025-05-15
2025-06-07 08:01:34,713 - INFO - 变更字段: amount: 7357 -> 5973, instore_amount: 7357.8 -> 5973.8
2025-06-07 08:01:34,729 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGERRPAJGP042F6DB81RHB6001P7E, sale_time=2025-05-17
2025-06-07 08:01:34,729 - INFO - 变更字段: recommend_amount: 0.0 -> 4916.23, daily_bill_amount: 0.0 -> 4916.23
2025-06-07 08:01:34,760 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDEISQT8KE0I86N3H2U1GT001EQ3, sale_time=2025-05-19
2025-06-07 08:01:34,760 - INFO - 变更字段: amount: 13951 -> 12520, instore_amount: 15457.2 -> 14026.0
2025-06-07 08:01:34,791 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDEISQT8KE0I86N3H2U1GT001EQ3, sale_time=2025-05-24
2025-06-07 08:01:34,791 - INFO - 变更字段: amount: 13559 -> 12435, count: 17 -> 19, instore_amount: 13559.3 -> 12435.7, instore_count: 17 -> 19
2025-06-07 08:01:34,807 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGERRPAJGP042F6DB81RHB6001P7E, sale_time=2025-05-26
2025-06-07 08:01:34,807 - INFO - 变更字段: recommend_amount: 1597.0 -> 1725.94, daily_bill_amount: 1597.0 -> 1725.94
2025-06-07 08:01:34,807 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEAIN5DMKK0I86N3H2U1VH001F8N, sale_time=2025-05-25
2025-06-07 08:01:34,807 - INFO - 变更字段: amount: -16866 -> -17175
2025-06-07 08:01:34,823 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGERRPAJGP042F6DB81RHB6001P7E, sale_time=2025-05-28
2025-06-07 08:01:34,823 - INFO - 变更字段: recommend_amount: 0.0 -> 3087.0, daily_bill_amount: 0.0 -> 3087.0
2025-06-07 08:01:34,838 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDEISQT8KE0I86N3H2U1GT001EQ3, sale_time=2025-05-27
2025-06-07 08:01:34,838 - INFO - 变更字段: amount: 8991 -> 7827, instore_amount: 8991.4 -> 7827.4
2025-06-07 08:01:34,838 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGERRPAJGP042F6DB81RHB6001P7E, sale_time=2025-05-29
2025-06-07 08:01:34,838 - INFO - 变更字段: recommend_amount: 0.0 -> 1419.0, daily_bill_amount: 0.0 -> 1419.0
2025-06-07 08:01:34,854 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDEISQT8KE0I86N3H2U1GT001EQ3, sale_time=2025-05-29
2025-06-07 08:01:34,854 - INFO - 变更字段: amount: 5941 -> 4432, instore_amount: 5941.1 -> 4432.7
2025-06-07 08:01:34,870 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGERRPAJGP042F6DB81RHB6001P7E, sale_time=2025-05-31
2025-06-07 08:01:34,870 - INFO - 变更字段: recommend_amount: 5408.5 -> 5967.46, daily_bill_amount: 5408.5 -> 5967.46
2025-06-07 08:01:34,870 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDP34FLR400I86N3H2U1MG001EVM, sale_time=2025-06-01
2025-06-07 08:01:34,870 - INFO - 变更字段: recommend_amount: 20077.44 -> 20384.46, amount: 19939 -> 20384, count: 135 -> 136, instore_amount: 19939.46 -> 20384.46, instore_count: 135 -> 136
2025-06-07 08:01:34,870 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDEISQT8KE0I86N3H2U1GT001EQ3, sale_time=2025-05-31
2025-06-07 08:01:34,870 - INFO - 变更字段: amount: 21734 -> 17071, instore_amount: 21978.7 -> 17315.9
2025-06-07 08:01:34,885 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEKSIE6DGB42F6DB81RH77001P3F, sale_time=2025-06-02
2025-06-07 08:01:34,885 - INFO - 变更字段: amount: 2383 -> 2267, instore_amount: 2383.3 -> 2267.3
2025-06-07 08:01:34,901 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=6692283A183D432BAE322E1032539CE8, sale_time=2025-06-05
2025-06-07 08:01:34,901 - INFO - 变更字段: daily_bill_amount: 0.0 -> 7617.0
2025-06-07 08:01:34,901 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EE1H27HPQN7Q2OV4FVC7EO0014B9, sale_time=2025-06-05
2025-06-07 08:01:34,901 - INFO - 变更字段: recommend_amount: 0.0 -> 3183.0, daily_bill_amount: 0.0 -> 3183.0
2025-06-07 08:01:34,901 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1H31EDMMR23CHP7Q2OV4FVC79C00145T, sale_time=2025-06-04
2025-06-07 08:01:34,901 - INFO - 变更字段: amount: 652 -> 671, count: 27 -> 29, online_amount: 550.91 -> 570.59, online_count: 24 -> 26
2025-06-07 08:01:34,901 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G, sale_time=2025-06-05
2025-06-07 08:01:34,901 - INFO - 变更字段: recommend_amount: 0.0 -> 4101.27, daily_bill_amount: 0.0 -> 4101.27
2025-06-07 08:01:34,901 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S, sale_time=2025-06-05
2025-06-07 08:01:34,901 - INFO - 变更字段: amount: 732 -> 4180, count: 3 -> 4, instore_amount: 732.0 -> 4180.0, instore_count: 3 -> 4
2025-06-07 08:01:34,901 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINTAKS3N4EI7Q2OVBN4IS76001D38, sale_time=2025-06-05
2025-06-07 08:01:34,901 - INFO - 变更字段: amount: 9341 -> 32700, count: 6 -> 7, instore_amount: 9341.0 -> 32700.0, instore_count: 6 -> 7
2025-06-07 08:01:34,901 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINT72H2L6227Q2OVBN4IS62001D24, sale_time=2025-06-05
2025-06-07 08:01:34,901 - INFO - 变更字段: recommend_amount: 0.0 -> 9230.8, daily_bill_amount: 0.0 -> 9230.8
2025-06-07 08:01:34,901 - INFO - 更新记录成功: shop_id=1GD9P85P7PPH4L7Q2OV3OBNS49001KPE, shop_entity_id=1GDINSC23H1J3M7Q2OV392410L00148H, sale_time=2025-06-05
2025-06-07 08:01:34,901 - INFO - 变更字段: amount: 1441 -> 5130, count: 3 -> 12, instore_amount: 1441.0 -> 5130.0, instore_count: 3 -> 12
2025-06-07 08:01:34,901 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEQIR94JOQ42F6DB81RHAE001P6M, sale_time=2025-06-05
2025-06-07 08:01:34,901 - INFO - 变更字段: recommend_amount: 2253.4 -> 2275.41, amount: 2253 -> 2275, count: 132 -> 134, online_amount: 1775.7 -> 1797.71, online_count: 105 -> 107
2025-06-07 08:01:34,901 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEO8LGKTTH42F6DB81RH8V001P57, sale_time=2025-06-05
2025-06-07 08:01:34,901 - INFO - 变更字段: recommend_amount: 2034.31 -> 2045.31, amount: 2034 -> 2045, instore_amount: 2051.55 -> 2062.55
2025-06-07 08:01:34,916 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-06-05
2025-06-07 08:01:34,916 - INFO - 变更字段: recommend_amount: 9116.24 -> 9431.24, amount: 9116 -> 9431, count: 178 -> 182, instore_amount: 8419.21 -> 8734.21, instore_count: 164 -> 168
2025-06-07 08:01:34,916 - INFO - 更新记录成功: shop_id=1HSJMRFR3MBRV37AV8LHQQGIT4001C3D, shop_entity_id=1HTVGEJQAOTB2542F6DB81RH6O001P30, sale_time=2025-06-04
2025-06-07 08:01:34,916 - INFO - 变更字段: recommend_amount: 6868.78 -> 6970.48, amount: 6868 -> 6970, count: 138 -> 139, instore_amount: 5698.46 -> 5800.16, instore_count: 111 -> 112
2025-06-07 08:01:34,916 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HO90F90E71NK12I1UUTD5AE7C001O7G, sale_time=2025-06-05
2025-06-07 08:01:34,916 - INFO - 变更字段: recommend_amount: 5986.81 -> 6067.41, amount: 5986 -> 6067, count: 248 -> 252, online_amount: 4859.11 -> 4939.71, online_count: 192 -> 196
2025-06-07 08:01:34,916 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEC0U8246I0I86N3H2U10A001F9G, sale_time=2025-06-05
2025-06-07 08:01:34,916 - INFO - 变更字段: amount: 2151 -> 2178, count: 66 -> 67, instore_amount: 521.0 -> 547.3, instore_count: 7 -> 8
2025-06-07 08:01:34,916 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEB9O4F53S0I86N3H2U1VT001F93, sale_time=2025-06-05
2025-06-07 08:01:34,916 - INFO - 变更字段: recommend_amount: 0.0 -> 19210.71, daily_bill_amount: 0.0 -> 19210.71
2025-06-07 08:01:34,916 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEAIN5DMKK0I86N3H2U1VH001F8N, sale_time=2025-06-05
2025-06-07 08:01:34,916 - INFO - 变更字段: amount: -3713 -> -3708, count: 20 -> 21, online_amount: 338.9 -> 343.5, online_count: 16 -> 17
2025-06-07 08:01:34,916 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEEA4041D6F0I86N3H2U1V9001F8F, sale_time=2025-06-05
2025-06-07 08:01:34,916 - INFO - 变更字段: amount: 14370 -> 14680, count: 154 -> 155, instore_amount: 12275.89 -> 12585.89, instore_count: 81 -> 82
2025-06-07 08:01:34,916 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE85EF5FB90I86N3H2U1U9001F7F, sale_time=2025-06-05
2025-06-07 08:01:34,916 - INFO - 变更字段: recommend_amount: 0.0 -> 9713.35, daily_bill_amount: 0.0 -> 9713.35
2025-06-07 08:01:34,916 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE7UKVCV6D0I86N3H2U1U5001F7B, sale_time=2025-06-05
2025-06-07 08:01:34,916 - INFO - 变更字段: amount: 21888 -> 22516, count: 178 -> 181, instore_amount: 7790.5 -> 8418.5, instore_count: 48 -> 51
2025-06-07 08:01:34,916 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEE0A2N9KG60I86N3H2U1QB001F3H, sale_time=2025-06-05
2025-06-07 08:01:34,916 - INFO - 变更字段: amount: 2769 -> 2776, count: 163 -> 164, instore_amount: 1233.01 -> 1240.01, instore_count: 83 -> 84
2025-06-07 08:01:34,916 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDVQHT7NSH0I86N3H2U1Q2001F38, sale_time=2025-06-05
2025-06-07 08:01:34,916 - INFO - 变更字段: amount: 4665 -> 4695, count: 221 -> 223, online_amount: 3453.39 -> 3483.49, online_count: 152 -> 154
2025-06-07 08:01:34,916 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDJP082LC00I86N3H2U1JM001ESS, sale_time=2025-06-05
2025-06-07 08:01:34,916 - INFO - 变更字段: recommend_amount: 117.6 -> 546.4, amount: 117 -> 546, count: 1 -> 2, instore_amount: 117.6 -> 546.4, instore_count: 1 -> 2
2025-06-07 08:01:34,916 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDEQ2M9E710I86N3H2U1H1001EQ7, sale_time=2025-06-05
2025-06-07 08:01:34,916 - INFO - 变更字段: amount: 2765 -> 2862, count: 24 -> 25, online_amount: 556.4 -> 653.03, online_count: 3 -> 4
2025-06-07 08:01:34,916 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCEDCE3748SO0I86N3H2U1FP001EOV, sale_time=2025-06-05
2025-06-07 08:01:34,916 - INFO - 变更字段: recommend_amount: 360.0 -> 648.0, amount: 360 -> 648, count: 1 -> 2, instore_amount: 360.0 -> 648.0, instore_count: 1 -> 2
2025-06-07 08:01:34,916 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDUDM4PLNS0I86N3H2U121001EB7, sale_time=2025-06-05
2025-06-07 08:01:34,916 - INFO - 变更字段: amount: 4035 -> 3970
2025-06-07 08:01:34,916 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDU5QKVU3D0I86N3H2U11T001EB3, sale_time=2025-06-05
2025-06-07 08:01:34,916 - INFO - 变更字段: amount: 3404 -> 3511, count: 240 -> 259, online_amount: 3249.52 -> 3357.13, online_count: 227 -> 246
2025-06-07 08:01:34,916 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDU5QKVU3D0I86N3H2U11T001EB3, sale_time=2025-06-04
2025-06-07 08:01:34,916 - INFO - 变更字段: amount: 3792 -> 3798, count: 251 -> 253, online_amount: 3823.52 -> 3830.22, online_count: 244 -> 246
2025-06-07 08:01:34,916 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQQG9THS10I86N3H2U108001E9E, sale_time=2025-06-05
2025-06-07 08:01:34,916 - INFO - 变更字段: amount: 4574 -> 4899, count: 167 -> 168, instore_amount: 4648.35 -> 4972.85, instore_count: 167 -> 168
2025-06-07 08:01:34,916 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDQJKO8O0D0I86N3H2U104001E9A, sale_time=2025-06-05
2025-06-07 08:01:34,916 - INFO - 变更字段: amount: 4650 -> 4656, count: 338 -> 342, instore_amount: 3298.73 -> 3309.8, instore_count: 231 -> 236, online_amount: 1422.4 -> 1417.2, online_count: 107 -> 106
2025-06-07 08:01:34,932 - INFO - 更新记录成功: shop_id=1HFLOR99TBR11L6UBHOUTGCK1C001A3F, shop_entity_id=1HGCCDNMK1P3900I86N3H2U1UO001E7U, sale_time=2025-06-05
2025-06-07 08:01:34,932 - INFO - 变更字段: amount: 3009 -> 3052, count: 71 -> 72, instore_amount: 2435.6 -> 2478.5, instore_count: 53 -> 54
2025-06-07 08:01:34,932 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B, sale_time=2025-06-05
2025-06-07 08:01:34,932 - INFO - 变更字段: recommend_amount: 5246.46 -> 5285.55, amount: 5246 -> 5285, count: 308 -> 311, instore_amount: 2563.93 -> 2595.5, instore_count: 153 -> 155, online_amount: 2682.53 -> 2690.05, online_count: 155 -> 156
2025-06-07 08:01:34,932 - INFO - 更新记录成功: shop_id=1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV, shop_entity_id=1HKQQ0NLURMV9D3IKSIOCDI7R2001G23, sale_time=2025-06-05
2025-06-07 08:01:34,932 - INFO - 变更字段: recommend_amount: 0.0 -> 3779.48, daily_bill_amount: 0.0 -> 3779.48, amount: 1374 -> 1396, count: 33 -> 34, instore_amount: 904.82 -> 926.62, instore_count: 26 -> 27
2025-06-07 08:01:34,932 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK, sale_time=2025-06-05
2025-06-07 08:01:34,932 - INFO - 变更字段: count: 59 -> 60, instore_count: 47 -> 48
2025-06-07 08:01:34,932 - INFO - 更新记录成功: shop_id=1HRIS7255PESAA7AV8LHQQGIH8001KNH, shop_entity_id=1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8, sale_time=2025-06-05
2025-06-07 08:01:34,932 - INFO - 变更字段: amount: 25953 -> 30461, count: 117 -> 119, instore_amount: 25831.16 -> 30339.1, instore_count: 67 -> 69
2025-06-07 08:01:34,932 - INFO - 更新记录成功: shop_id=1HRIS0CDPQR2GM6E7AERKQ83MS0014QU, shop_entity_id=1HRKUIS7OTCALF7AV8LHQQGIDO001EK1, sale_time=2025-06-05
2025-06-07 08:01:34,932 - INFO - 变更字段: amount: 25442 -> 26018, count: 129 -> 130, instore_amount: 22373.6 -> 22949.6, instore_count: 102 -> 103
2025-06-07 08:01:35,198 - INFO - SQLite数据保存完成，统计信息：
2025-06-07 08:01:35,198 - INFO - - 总记录数: 12866
2025-06-07 08:01:35,198 - INFO - - 成功插入: 209
2025-06-07 08:01:35,198 - INFO - - 成功更新: 65
2025-06-07 08:01:35,198 - INFO - - 无需更新: 12592
2025-06-07 08:01:35,198 - INFO - - 处理失败: 0
2025-06-07 08:01:40,557 - INFO - 数据已保存到Excel文件: logs\数衍平台数据导出_20250607.xlsx
2025-06-07 08:01:40,557 - INFO - 成功获取数衍平台数据，共 12866 条记录
2025-06-07 08:01:40,557 - INFO - 正在更新SQLite月度汇总数据...
2025-06-07 08:01:40,573 - INFO - 月度数据sqllite清空完成
2025-06-07 08:01:40,854 - INFO - 月度汇总数据更新完成，处理了 1402 条汇总记录
2025-06-07 08:01:40,854 - INFO - 成功更新月度汇总数据，共 1402 条记录
2025-06-07 08:01:40,854 - INFO - 正在获取宜搭日销售表单数据...
2025-06-07 08:01:40,854 - INFO - 开始获取宜搭每日表单数据，总时间范围: 2025-04-07 00:00:00 至 2025-06-06 23:59:59
2025-06-07 08:01:40,854 - INFO - 查询分段 1: 2025-04-07 至 2025-04-08
2025-06-07 08:01:40,854 - INFO - 查询日期范围: 2025-04-07 至 2025-04-08，使用分页查询，每页 100 条记录
2025-06-07 08:01:40,854 - INFO - Request Parameters - Page 1:
2025-06-07 08:01:40,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:01:40,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200854, 1744041600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:01:45,338 - INFO - API请求耗时: 4484ms
2025-06-07 08:01:45,338 - INFO - Response - Page 1
2025-06-07 08:01:45,338 - INFO - 第 1 页获取到 100 条记录
2025-06-07 08:01:45,854 - INFO - Request Parameters - Page 2:
2025-06-07 08:01:45,854 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:01:45,854 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200854, 1744041600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:01:52,401 - INFO - API请求耗时: 6547ms
2025-06-07 08:01:52,401 - INFO - Response - Page 2
2025-06-07 08:01:52,401 - INFO - 第 2 页获取到 100 条记录
2025-06-07 08:01:52,901 - INFO - Request Parameters - Page 3:
2025-06-07 08:01:52,901 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:01:52,901 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200854, 1744041600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:01:53,573 - INFO - API请求耗时: 672ms
2025-06-07 08:01:53,573 - INFO - Response - Page 3
2025-06-07 08:01:53,573 - INFO - 第 3 页获取到 100 条记录
2025-06-07 08:01:54,088 - INFO - Request Parameters - Page 4:
2025-06-07 08:01:54,088 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:01:54,088 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200854, 1744041600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:01:54,963 - INFO - API请求耗时: 875ms
2025-06-07 08:01:54,963 - INFO - Response - Page 4
2025-06-07 08:01:54,963 - INFO - 第 4 页获取到 100 条记录
2025-06-07 08:01:55,463 - INFO - Request Parameters - Page 5:
2025-06-07 08:01:55,463 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:01:55,463 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200854, 1744041600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:01:56,260 - INFO - API请求耗时: 797ms
2025-06-07 08:01:56,260 - INFO - Response - Page 5
2025-06-07 08:01:56,260 - INFO - 第 5 页获取到 100 条记录
2025-06-07 08:01:56,776 - INFO - Request Parameters - Page 6:
2025-06-07 08:01:56,776 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:01:56,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200854, 1744041600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:01:57,479 - INFO - API请求耗时: 703ms
2025-06-07 08:01:57,479 - INFO - Response - Page 6
2025-06-07 08:01:57,479 - INFO - 第 6 页获取到 100 条记录
2025-06-07 08:01:57,994 - INFO - Request Parameters - Page 7:
2025-06-07 08:01:57,994 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:01:57,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1743955200854, 1744041600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:01:58,557 - INFO - API请求耗时: 563ms
2025-06-07 08:01:58,557 - INFO - Response - Page 7
2025-06-07 08:01:58,557 - INFO - 第 7 页获取到 42 条记录
2025-06-07 08:01:58,557 - INFO - 查询完成，共获取到 642 条记录
2025-06-07 08:01:58,557 - INFO - 分段 1 查询成功，获取到 642 条记录
2025-06-07 08:01:59,573 - INFO - 查询分段 2: 2025-04-09 至 2025-04-10
2025-06-07 08:01:59,573 - INFO - 查询日期范围: 2025-04-09 至 2025-04-10，使用分页查询，每页 100 条记录
2025-06-07 08:01:59,573 - INFO - Request Parameters - Page 1:
2025-06-07 08:01:59,573 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:01:59,573 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000854, 1744214400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:00,291 - INFO - API请求耗时: 719ms
2025-06-07 08:02:00,291 - INFO - Response - Page 1
2025-06-07 08:02:00,291 - INFO - 第 1 页获取到 100 条记录
2025-06-07 08:02:00,791 - INFO - Request Parameters - Page 2:
2025-06-07 08:02:00,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:00,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000854, 1744214400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:01,479 - INFO - API请求耗时: 687ms
2025-06-07 08:02:01,479 - INFO - Response - Page 2
2025-06-07 08:02:01,479 - INFO - 第 2 页获取到 100 条记录
2025-06-07 08:02:01,994 - INFO - Request Parameters - Page 3:
2025-06-07 08:02:01,994 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:01,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000854, 1744214400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:02,776 - INFO - API请求耗时: 781ms
2025-06-07 08:02:02,776 - INFO - Response - Page 3
2025-06-07 08:02:02,776 - INFO - 第 3 页获取到 100 条记录
2025-06-07 08:02:03,291 - INFO - Request Parameters - Page 4:
2025-06-07 08:02:03,291 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:03,291 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000854, 1744214400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:03,932 - INFO - API请求耗时: 641ms
2025-06-07 08:02:03,932 - INFO - Response - Page 4
2025-06-07 08:02:03,932 - INFO - 第 4 页获取到 100 条记录
2025-06-07 08:02:04,448 - INFO - Request Parameters - Page 5:
2025-06-07 08:02:04,448 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:04,448 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000854, 1744214400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:05,229 - INFO - API请求耗时: 781ms
2025-06-07 08:02:05,229 - INFO - Response - Page 5
2025-06-07 08:02:05,229 - INFO - 第 5 页获取到 100 条记录
2025-06-07 08:02:05,729 - INFO - Request Parameters - Page 6:
2025-06-07 08:02:05,729 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:05,729 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000854, 1744214400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:06,416 - INFO - API请求耗时: 687ms
2025-06-07 08:02:06,432 - INFO - Response - Page 6
2025-06-07 08:02:06,432 - INFO - 第 6 页获取到 100 条记录
2025-06-07 08:02:06,947 - INFO - Request Parameters - Page 7:
2025-06-07 08:02:06,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:06,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744128000854, 1744214400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:07,494 - INFO - API请求耗时: 547ms
2025-06-07 08:02:07,494 - INFO - Response - Page 7
2025-06-07 08:02:07,510 - INFO - 第 7 页获取到 33 条记录
2025-06-07 08:02:07,510 - INFO - 查询完成，共获取到 633 条记录
2025-06-07 08:02:07,510 - INFO - 分段 2 查询成功，获取到 633 条记录
2025-06-07 08:02:08,526 - INFO - 查询分段 3: 2025-04-11 至 2025-04-12
2025-06-07 08:02:08,526 - INFO - 查询日期范围: 2025-04-11 至 2025-04-12，使用分页查询，每页 100 条记录
2025-06-07 08:02:08,526 - INFO - Request Parameters - Page 1:
2025-06-07 08:02:08,526 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:08,526 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800854, 1744387200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:09,198 - INFO - API请求耗时: 672ms
2025-06-07 08:02:09,198 - INFO - Response - Page 1
2025-06-07 08:02:09,198 - INFO - 第 1 页获取到 100 条记录
2025-06-07 08:02:09,713 - INFO - Request Parameters - Page 2:
2025-06-07 08:02:09,713 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:09,713 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800854, 1744387200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:10,447 - INFO - API请求耗时: 734ms
2025-06-07 08:02:10,447 - INFO - Response - Page 2
2025-06-07 08:02:10,447 - INFO - 第 2 页获取到 100 条记录
2025-06-07 08:02:10,963 - INFO - Request Parameters - Page 3:
2025-06-07 08:02:10,963 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:10,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800854, 1744387200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:11,713 - INFO - API请求耗时: 750ms
2025-06-07 08:02:11,713 - INFO - Response - Page 3
2025-06-07 08:02:11,713 - INFO - 第 3 页获取到 100 条记录
2025-06-07 08:02:12,229 - INFO - Request Parameters - Page 4:
2025-06-07 08:02:12,229 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:12,229 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800854, 1744387200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:12,916 - INFO - API请求耗时: 687ms
2025-06-07 08:02:12,916 - INFO - Response - Page 4
2025-06-07 08:02:12,916 - INFO - 第 4 页获取到 100 条记录
2025-06-07 08:02:13,416 - INFO - Request Parameters - Page 5:
2025-06-07 08:02:13,416 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:13,416 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800854, 1744387200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:14,135 - INFO - API请求耗时: 719ms
2025-06-07 08:02:14,135 - INFO - Response - Page 5
2025-06-07 08:02:14,135 - INFO - 第 5 页获取到 100 条记录
2025-06-07 08:02:14,635 - INFO - Request Parameters - Page 6:
2025-06-07 08:02:14,635 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:14,635 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800854, 1744387200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:15,385 - INFO - API请求耗时: 750ms
2025-06-07 08:02:15,385 - INFO - Response - Page 6
2025-06-07 08:02:15,385 - INFO - 第 6 页获取到 100 条记录
2025-06-07 08:02:15,901 - INFO - Request Parameters - Page 7:
2025-06-07 08:02:15,901 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:15,901 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744300800854, 1744387200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:16,604 - INFO - API请求耗时: 703ms
2025-06-07 08:02:16,604 - INFO - Response - Page 7
2025-06-07 08:02:16,604 - INFO - 第 7 页获取到 63 条记录
2025-06-07 08:02:16,604 - INFO - 查询完成，共获取到 663 条记录
2025-06-07 08:02:16,604 - INFO - 分段 3 查询成功，获取到 663 条记录
2025-06-07 08:02:17,604 - INFO - 查询分段 4: 2025-04-13 至 2025-04-14
2025-06-07 08:02:17,604 - INFO - 查询日期范围: 2025-04-13 至 2025-04-14，使用分页查询，每页 100 条记录
2025-06-07 08:02:17,604 - INFO - Request Parameters - Page 1:
2025-06-07 08:02:17,604 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:17,604 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600854, 1744560000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:18,276 - INFO - API请求耗时: 672ms
2025-06-07 08:02:18,276 - INFO - Response - Page 1
2025-06-07 08:02:18,276 - INFO - 第 1 页获取到 100 条记录
2025-06-07 08:02:18,776 - INFO - Request Parameters - Page 2:
2025-06-07 08:02:18,776 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:18,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600854, 1744560000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:19,479 - INFO - API请求耗时: 703ms
2025-06-07 08:02:19,479 - INFO - Response - Page 2
2025-06-07 08:02:19,479 - INFO - 第 2 页获取到 100 条记录
2025-06-07 08:02:19,994 - INFO - Request Parameters - Page 3:
2025-06-07 08:02:19,994 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:19,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600854, 1744560000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:20,697 - INFO - API请求耗时: 703ms
2025-06-07 08:02:20,697 - INFO - Response - Page 3
2025-06-07 08:02:20,697 - INFO - 第 3 页获取到 100 条记录
2025-06-07 08:02:21,213 - INFO - Request Parameters - Page 4:
2025-06-07 08:02:21,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:21,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600854, 1744560000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:21,979 - INFO - API请求耗时: 766ms
2025-06-07 08:02:21,979 - INFO - Response - Page 4
2025-06-07 08:02:21,979 - INFO - 第 4 页获取到 100 条记录
2025-06-07 08:02:22,479 - INFO - Request Parameters - Page 5:
2025-06-07 08:02:22,479 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:22,479 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600854, 1744560000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:23,151 - INFO - API请求耗时: 672ms
2025-06-07 08:02:23,151 - INFO - Response - Page 5
2025-06-07 08:02:23,151 - INFO - 第 5 页获取到 100 条记录
2025-06-07 08:02:23,651 - INFO - Request Parameters - Page 6:
2025-06-07 08:02:23,651 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:23,651 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600854, 1744560000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:24,385 - INFO - API请求耗时: 734ms
2025-06-07 08:02:24,385 - INFO - Response - Page 6
2025-06-07 08:02:24,385 - INFO - 第 6 页获取到 100 条记录
2025-06-07 08:02:24,885 - INFO - Request Parameters - Page 7:
2025-06-07 08:02:24,885 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:24,885 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744473600854, 1744560000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:25,494 - INFO - API请求耗时: 609ms
2025-06-07 08:02:25,494 - INFO - Response - Page 7
2025-06-07 08:02:25,494 - INFO - 第 7 页获取到 45 条记录
2025-06-07 08:02:25,494 - INFO - 查询完成，共获取到 645 条记录
2025-06-07 08:02:25,494 - INFO - 分段 4 查询成功，获取到 645 条记录
2025-06-07 08:02:26,494 - INFO - 查询分段 5: 2025-04-15 至 2025-04-16
2025-06-07 08:02:26,494 - INFO - 查询日期范围: 2025-04-15 至 2025-04-16，使用分页查询，每页 100 条记录
2025-06-07 08:02:26,494 - INFO - Request Parameters - Page 1:
2025-06-07 08:02:26,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:26,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400854, 1744732800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:27,197 - INFO - API请求耗时: 703ms
2025-06-07 08:02:27,197 - INFO - Response - Page 1
2025-06-07 08:02:27,197 - INFO - 第 1 页获取到 100 条记录
2025-06-07 08:02:27,713 - INFO - Request Parameters - Page 2:
2025-06-07 08:02:27,713 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:27,713 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400854, 1744732800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:28,416 - INFO - API请求耗时: 703ms
2025-06-07 08:02:28,416 - INFO - Response - Page 2
2025-06-07 08:02:28,416 - INFO - 第 2 页获取到 100 条记录
2025-06-07 08:02:28,916 - INFO - Request Parameters - Page 3:
2025-06-07 08:02:28,916 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:28,916 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400854, 1744732800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:29,572 - INFO - API请求耗时: 656ms
2025-06-07 08:02:29,572 - INFO - Response - Page 3
2025-06-07 08:02:29,572 - INFO - 第 3 页获取到 100 条记录
2025-06-07 08:02:30,072 - INFO - Request Parameters - Page 4:
2025-06-07 08:02:30,072 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:30,072 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400854, 1744732800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:30,744 - INFO - API请求耗时: 672ms
2025-06-07 08:02:30,744 - INFO - Response - Page 4
2025-06-07 08:02:30,744 - INFO - 第 4 页获取到 100 条记录
2025-06-07 08:02:31,260 - INFO - Request Parameters - Page 5:
2025-06-07 08:02:31,260 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:31,260 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400854, 1744732800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:31,932 - INFO - API请求耗时: 672ms
2025-06-07 08:02:31,932 - INFO - Response - Page 5
2025-06-07 08:02:31,932 - INFO - 第 5 页获取到 100 条记录
2025-06-07 08:02:32,432 - INFO - Request Parameters - Page 6:
2025-06-07 08:02:32,432 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:32,432 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400854, 1744732800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:33,182 - INFO - API请求耗时: 750ms
2025-06-07 08:02:33,182 - INFO - Response - Page 6
2025-06-07 08:02:33,182 - INFO - 第 6 页获取到 100 条记录
2025-06-07 08:02:33,697 - INFO - Request Parameters - Page 7:
2025-06-07 08:02:33,697 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:33,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744646400854, 1744732800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:34,197 - INFO - API请求耗时: 500ms
2025-06-07 08:02:34,197 - INFO - Response - Page 7
2025-06-07 08:02:34,197 - INFO - 第 7 页获取到 30 条记录
2025-06-07 08:02:34,197 - INFO - 查询完成，共获取到 630 条记录
2025-06-07 08:02:34,197 - INFO - 分段 5 查询成功，获取到 630 条记录
2025-06-07 08:02:35,213 - INFO - 查询分段 6: 2025-04-17 至 2025-04-18
2025-06-07 08:02:35,213 - INFO - 查询日期范围: 2025-04-17 至 2025-04-18，使用分页查询，每页 100 条记录
2025-06-07 08:02:35,213 - INFO - Request Parameters - Page 1:
2025-06-07 08:02:35,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:35,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200854, 1744905600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:35,947 - INFO - API请求耗时: 734ms
2025-06-07 08:02:35,947 - INFO - Response - Page 1
2025-06-07 08:02:35,947 - INFO - 第 1 页获取到 100 条记录
2025-06-07 08:02:36,463 - INFO - Request Parameters - Page 2:
2025-06-07 08:02:36,463 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:36,463 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200854, 1744905600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:37,197 - INFO - API请求耗时: 734ms
2025-06-07 08:02:37,197 - INFO - Response - Page 2
2025-06-07 08:02:37,197 - INFO - 第 2 页获取到 100 条记录
2025-06-07 08:02:37,713 - INFO - Request Parameters - Page 3:
2025-06-07 08:02:37,713 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:37,713 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200854, 1744905600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:38,572 - INFO - API请求耗时: 859ms
2025-06-07 08:02:38,572 - INFO - Response - Page 3
2025-06-07 08:02:38,572 - INFO - 第 3 页获取到 100 条记录
2025-06-07 08:02:39,088 - INFO - Request Parameters - Page 4:
2025-06-07 08:02:39,088 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:39,088 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200854, 1744905600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:39,744 - INFO - API请求耗时: 656ms
2025-06-07 08:02:39,744 - INFO - Response - Page 4
2025-06-07 08:02:39,744 - INFO - 第 4 页获取到 100 条记录
2025-06-07 08:02:40,260 - INFO - Request Parameters - Page 5:
2025-06-07 08:02:40,260 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:40,260 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200854, 1744905600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:41,072 - INFO - API请求耗时: 812ms
2025-06-07 08:02:41,072 - INFO - Response - Page 5
2025-06-07 08:02:41,072 - INFO - 第 5 页获取到 100 条记录
2025-06-07 08:02:41,588 - INFO - Request Parameters - Page 6:
2025-06-07 08:02:41,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:41,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200854, 1744905600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:42,307 - INFO - API请求耗时: 719ms
2025-06-07 08:02:42,307 - INFO - Response - Page 6
2025-06-07 08:02:42,307 - INFO - 第 6 页获取到 100 条记录
2025-06-07 08:02:42,807 - INFO - Request Parameters - Page 7:
2025-06-07 08:02:42,807 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:42,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744819200854, 1744905600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:43,416 - INFO - API请求耗时: 609ms
2025-06-07 08:02:43,416 - INFO - Response - Page 7
2025-06-07 08:02:43,416 - INFO - 第 7 页获取到 51 条记录
2025-06-07 08:02:43,416 - INFO - 查询完成，共获取到 651 条记录
2025-06-07 08:02:43,416 - INFO - 分段 6 查询成功，获取到 651 条记录
2025-06-07 08:02:44,432 - INFO - 查询分段 7: 2025-04-19 至 2025-04-20
2025-06-07 08:02:44,432 - INFO - 查询日期范围: 2025-04-19 至 2025-04-20，使用分页查询，每页 100 条记录
2025-06-07 08:02:44,432 - INFO - Request Parameters - Page 1:
2025-06-07 08:02:44,432 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:44,432 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744992000854, 1745078400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:45,166 - INFO - API请求耗时: 734ms
2025-06-07 08:02:45,166 - INFO - Response - Page 1
2025-06-07 08:02:45,166 - INFO - 第 1 页获取到 100 条记录
2025-06-07 08:02:45,666 - INFO - Request Parameters - Page 2:
2025-06-07 08:02:45,666 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:45,666 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744992000854, 1745078400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:46,432 - INFO - API请求耗时: 766ms
2025-06-07 08:02:46,432 - INFO - Response - Page 2
2025-06-07 08:02:46,432 - INFO - 第 2 页获取到 100 条记录
2025-06-07 08:02:46,947 - INFO - Request Parameters - Page 3:
2025-06-07 08:02:46,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:46,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744992000854, 1745078400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:47,619 - INFO - API请求耗时: 672ms
2025-06-07 08:02:47,619 - INFO - Response - Page 3
2025-06-07 08:02:47,619 - INFO - 第 3 页获取到 100 条记录
2025-06-07 08:02:48,135 - INFO - Request Parameters - Page 4:
2025-06-07 08:02:48,135 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:48,135 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744992000854, 1745078400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:48,838 - INFO - API请求耗时: 703ms
2025-06-07 08:02:48,838 - INFO - Response - Page 4
2025-06-07 08:02:48,838 - INFO - 第 4 页获取到 100 条记录
2025-06-07 08:02:49,338 - INFO - Request Parameters - Page 5:
2025-06-07 08:02:49,338 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:49,338 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744992000854, 1745078400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:50,041 - INFO - API请求耗时: 703ms
2025-06-07 08:02:50,041 - INFO - Response - Page 5
2025-06-07 08:02:50,041 - INFO - 第 5 页获取到 100 条记录
2025-06-07 08:02:50,557 - INFO - Request Parameters - Page 6:
2025-06-07 08:02:50,557 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:50,557 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744992000854, 1745078400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:51,260 - INFO - API请求耗时: 703ms
2025-06-07 08:02:51,260 - INFO - Response - Page 6
2025-06-07 08:02:51,260 - INFO - 第 6 页获取到 100 条记录
2025-06-07 08:02:51,775 - INFO - Request Parameters - Page 7:
2025-06-07 08:02:51,775 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:51,775 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1744992000854, 1745078400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:52,432 - INFO - API请求耗时: 656ms
2025-06-07 08:02:52,432 - INFO - Response - Page 7
2025-06-07 08:02:52,432 - INFO - 第 7 页获取到 48 条记录
2025-06-07 08:02:52,432 - INFO - 查询完成，共获取到 648 条记录
2025-06-07 08:02:52,432 - INFO - 分段 7 查询成功，获取到 648 条记录
2025-06-07 08:02:53,447 - INFO - 查询分段 8: 2025-04-21 至 2025-04-22
2025-06-07 08:02:53,447 - INFO - 查询日期范围: 2025-04-21 至 2025-04-22，使用分页查询，每页 100 条记录
2025-06-07 08:02:53,447 - INFO - Request Parameters - Page 1:
2025-06-07 08:02:53,447 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:53,447 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800854, 1745251200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:54,197 - INFO - API请求耗时: 750ms
2025-06-07 08:02:54,197 - INFO - Response - Page 1
2025-06-07 08:02:54,213 - INFO - 第 1 页获取到 100 条记录
2025-06-07 08:02:54,728 - INFO - Request Parameters - Page 2:
2025-06-07 08:02:54,728 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:54,728 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800854, 1745251200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:55,416 - INFO - API请求耗时: 687ms
2025-06-07 08:02:55,416 - INFO - Response - Page 2
2025-06-07 08:02:55,416 - INFO - 第 2 页获取到 100 条记录
2025-06-07 08:02:55,916 - INFO - Request Parameters - Page 3:
2025-06-07 08:02:55,916 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:55,916 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800854, 1745251200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:56,619 - INFO - API请求耗时: 703ms
2025-06-07 08:02:56,619 - INFO - Response - Page 3
2025-06-07 08:02:56,619 - INFO - 第 3 页获取到 100 条记录
2025-06-07 08:02:57,135 - INFO - Request Parameters - Page 4:
2025-06-07 08:02:57,135 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:57,135 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800854, 1745251200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:57,900 - INFO - API请求耗时: 766ms
2025-06-07 08:02:57,900 - INFO - Response - Page 4
2025-06-07 08:02:57,900 - INFO - 第 4 页获取到 100 条记录
2025-06-07 08:02:58,400 - INFO - Request Parameters - Page 5:
2025-06-07 08:02:58,400 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:58,400 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800854, 1745251200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:02:59,150 - INFO - API请求耗时: 750ms
2025-06-07 08:02:59,150 - INFO - Response - Page 5
2025-06-07 08:02:59,150 - INFO - 第 5 页获取到 100 条记录
2025-06-07 08:02:59,666 - INFO - Request Parameters - Page 6:
2025-06-07 08:02:59,666 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:02:59,666 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800854, 1745251200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:00,416 - INFO - API请求耗时: 750ms
2025-06-07 08:03:00,416 - INFO - Response - Page 6
2025-06-07 08:03:00,416 - INFO - 第 6 页获取到 100 条记录
2025-06-07 08:03:00,932 - INFO - Request Parameters - Page 7:
2025-06-07 08:03:00,932 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:00,932 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745164800854, 1745251200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:01,494 - INFO - API请求耗时: 563ms
2025-06-07 08:03:01,510 - INFO - Response - Page 7
2025-06-07 08:03:01,510 - INFO - 第 7 页获取到 36 条记录
2025-06-07 08:03:01,510 - INFO - 查询完成，共获取到 636 条记录
2025-06-07 08:03:01,510 - INFO - 分段 8 查询成功，获取到 636 条记录
2025-06-07 08:03:02,525 - INFO - 查询分段 9: 2025-04-23 至 2025-04-24
2025-06-07 08:03:02,525 - INFO - 查询日期范围: 2025-04-23 至 2025-04-24，使用分页查询，每页 100 条记录
2025-06-07 08:03:02,525 - INFO - Request Parameters - Page 1:
2025-06-07 08:03:02,525 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:02,525 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600854, 1745424000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:03,275 - INFO - API请求耗时: 750ms
2025-06-07 08:03:03,275 - INFO - Response - Page 1
2025-06-07 08:03:03,275 - INFO - 第 1 页获取到 100 条记录
2025-06-07 08:03:03,791 - INFO - Request Parameters - Page 2:
2025-06-07 08:03:03,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:03,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600854, 1745424000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:04,510 - INFO - API请求耗时: 719ms
2025-06-07 08:03:04,510 - INFO - Response - Page 2
2025-06-07 08:03:04,510 - INFO - 第 2 页获取到 100 条记录
2025-06-07 08:03:05,010 - INFO - Request Parameters - Page 3:
2025-06-07 08:03:05,010 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:05,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600854, 1745424000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:05,760 - INFO - API请求耗时: 750ms
2025-06-07 08:03:05,760 - INFO - Response - Page 3
2025-06-07 08:03:05,760 - INFO - 第 3 页获取到 100 条记录
2025-06-07 08:03:06,275 - INFO - Request Parameters - Page 4:
2025-06-07 08:03:06,275 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:06,275 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600854, 1745424000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:07,010 - INFO - API请求耗时: 734ms
2025-06-07 08:03:07,010 - INFO - Response - Page 4
2025-06-07 08:03:07,010 - INFO - 第 4 页获取到 100 条记录
2025-06-07 08:03:07,510 - INFO - Request Parameters - Page 5:
2025-06-07 08:03:07,510 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:07,510 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600854, 1745424000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:08,338 - INFO - API请求耗时: 828ms
2025-06-07 08:03:08,338 - INFO - Response - Page 5
2025-06-07 08:03:08,338 - INFO - 第 5 页获取到 100 条记录
2025-06-07 08:03:08,853 - INFO - Request Parameters - Page 6:
2025-06-07 08:03:08,853 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:08,853 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600854, 1745424000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:09,572 - INFO - API请求耗时: 719ms
2025-06-07 08:03:09,572 - INFO - Response - Page 6
2025-06-07 08:03:09,572 - INFO - 第 6 页获取到 100 条记录
2025-06-07 08:03:10,088 - INFO - Request Parameters - Page 7:
2025-06-07 08:03:10,088 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:10,088 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745337600854, 1745424000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:10,619 - INFO - API请求耗时: 531ms
2025-06-07 08:03:10,619 - INFO - Response - Page 7
2025-06-07 08:03:10,619 - INFO - 第 7 页获取到 21 条记录
2025-06-07 08:03:10,619 - INFO - 查询完成，共获取到 621 条记录
2025-06-07 08:03:10,619 - INFO - 分段 9 查询成功，获取到 621 条记录
2025-06-07 08:03:11,635 - INFO - 查询分段 10: 2025-04-25 至 2025-04-26
2025-06-07 08:03:11,635 - INFO - 查询日期范围: 2025-04-25 至 2025-04-26，使用分页查询，每页 100 条记录
2025-06-07 08:03:11,635 - INFO - Request Parameters - Page 1:
2025-06-07 08:03:11,635 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:11,635 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400854, 1745596800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:12,338 - INFO - API请求耗时: 703ms
2025-06-07 08:03:12,338 - INFO - Response - Page 1
2025-06-07 08:03:12,338 - INFO - 第 1 页获取到 100 条记录
2025-06-07 08:03:12,853 - INFO - Request Parameters - Page 2:
2025-06-07 08:03:12,853 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:12,853 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400854, 1745596800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:13,525 - INFO - API请求耗时: 672ms
2025-06-07 08:03:13,525 - INFO - Response - Page 2
2025-06-07 08:03:13,525 - INFO - 第 2 页获取到 100 条记录
2025-06-07 08:03:14,041 - INFO - Request Parameters - Page 3:
2025-06-07 08:03:14,041 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:14,041 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400854, 1745596800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:14,760 - INFO - API请求耗时: 719ms
2025-06-07 08:03:14,760 - INFO - Response - Page 3
2025-06-07 08:03:14,760 - INFO - 第 3 页获取到 100 条记录
2025-06-07 08:03:15,260 - INFO - Request Parameters - Page 4:
2025-06-07 08:03:15,260 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:15,260 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400854, 1745596800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:16,088 - INFO - API请求耗时: 828ms
2025-06-07 08:03:16,088 - INFO - Response - Page 4
2025-06-07 08:03:16,088 - INFO - 第 4 页获取到 100 条记录
2025-06-07 08:03:16,588 - INFO - Request Parameters - Page 5:
2025-06-07 08:03:16,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:16,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400854, 1745596800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:17,260 - INFO - API请求耗时: 672ms
2025-06-07 08:03:17,260 - INFO - Response - Page 5
2025-06-07 08:03:17,260 - INFO - 第 5 页获取到 100 条记录
2025-06-07 08:03:17,775 - INFO - Request Parameters - Page 6:
2025-06-07 08:03:17,775 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:17,775 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400854, 1745596800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:18,431 - INFO - API请求耗时: 656ms
2025-06-07 08:03:18,431 - INFO - Response - Page 6
2025-06-07 08:03:18,431 - INFO - 第 6 页获取到 100 条记录
2025-06-07 08:03:18,947 - INFO - Request Parameters - Page 7:
2025-06-07 08:03:18,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:18,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745510400854, 1745596800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:19,541 - INFO - API请求耗时: 594ms
2025-06-07 08:03:19,541 - INFO - Response - Page 7
2025-06-07 08:03:19,541 - INFO - 第 7 页获取到 54 条记录
2025-06-07 08:03:19,541 - INFO - 查询完成，共获取到 654 条记录
2025-06-07 08:03:19,541 - INFO - 分段 10 查询成功，获取到 654 条记录
2025-06-07 08:03:20,556 - INFO - 查询分段 11: 2025-04-27 至 2025-04-28
2025-06-07 08:03:20,556 - INFO - 查询日期范围: 2025-04-27 至 2025-04-28，使用分页查询，每页 100 条记录
2025-06-07 08:03:20,556 - INFO - Request Parameters - Page 1:
2025-06-07 08:03:20,556 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:20,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200854, 1745769600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:21,197 - INFO - API请求耗时: 641ms
2025-06-07 08:03:21,197 - INFO - Response - Page 1
2025-06-07 08:03:21,197 - INFO - 第 1 页获取到 100 条记录
2025-06-07 08:03:21,713 - INFO - Request Parameters - Page 2:
2025-06-07 08:03:21,713 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:21,713 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200854, 1745769600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:22,369 - INFO - API请求耗时: 656ms
2025-06-07 08:03:22,369 - INFO - Response - Page 2
2025-06-07 08:03:22,369 - INFO - 第 2 页获取到 100 条记录
2025-06-07 08:03:22,885 - INFO - Request Parameters - Page 3:
2025-06-07 08:03:22,885 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:22,885 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200854, 1745769600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:23,588 - INFO - API请求耗时: 703ms
2025-06-07 08:03:23,588 - INFO - Response - Page 3
2025-06-07 08:03:23,588 - INFO - 第 3 页获取到 100 条记录
2025-06-07 08:03:24,103 - INFO - Request Parameters - Page 4:
2025-06-07 08:03:24,103 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:24,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200854, 1745769600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:24,838 - INFO - API请求耗时: 734ms
2025-06-07 08:03:24,838 - INFO - Response - Page 4
2025-06-07 08:03:24,838 - INFO - 第 4 页获取到 100 条记录
2025-06-07 08:03:25,353 - INFO - Request Parameters - Page 5:
2025-06-07 08:03:25,353 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:25,353 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200854, 1745769600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:26,025 - INFO - API请求耗时: 672ms
2025-06-07 08:03:26,025 - INFO - Response - Page 5
2025-06-07 08:03:26,025 - INFO - 第 5 页获取到 100 条记录
2025-06-07 08:03:26,525 - INFO - Request Parameters - Page 6:
2025-06-07 08:03:26,525 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:26,525 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200854, 1745769600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:27,431 - INFO - API请求耗时: 906ms
2025-06-07 08:03:27,431 - INFO - Response - Page 6
2025-06-07 08:03:27,431 - INFO - 第 6 页获取到 100 条记录
2025-06-07 08:03:27,931 - INFO - Request Parameters - Page 7:
2025-06-07 08:03:27,931 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:27,931 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745683200854, 1745769600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:28,463 - INFO - API请求耗时: 531ms
2025-06-07 08:03:28,463 - INFO - Response - Page 7
2025-06-07 08:03:28,463 - INFO - 第 7 页获取到 36 条记录
2025-06-07 08:03:28,463 - INFO - 查询完成，共获取到 636 条记录
2025-06-07 08:03:28,463 - INFO - 分段 11 查询成功，获取到 636 条记录
2025-06-07 08:03:29,478 - INFO - 查询分段 12: 2025-04-29 至 2025-04-30
2025-06-07 08:03:29,478 - INFO - 查询日期范围: 2025-04-29 至 2025-04-30，使用分页查询，每页 100 条记录
2025-06-07 08:03:29,478 - INFO - Request Parameters - Page 1:
2025-06-07 08:03:29,478 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:29,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000854, 1745942400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:30,244 - INFO - API请求耗时: 766ms
2025-06-07 08:03:30,244 - INFO - Response - Page 1
2025-06-07 08:03:30,244 - INFO - 第 1 页获取到 100 条记录
2025-06-07 08:03:30,744 - INFO - Request Parameters - Page 2:
2025-06-07 08:03:30,744 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:30,744 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000854, 1745942400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:31,431 - INFO - API请求耗时: 688ms
2025-06-07 08:03:31,431 - INFO - Response - Page 2
2025-06-07 08:03:31,431 - INFO - 第 2 页获取到 100 条记录
2025-06-07 08:03:31,947 - INFO - Request Parameters - Page 3:
2025-06-07 08:03:31,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:31,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000854, 1745942400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:32,634 - INFO - API请求耗时: 688ms
2025-06-07 08:03:32,634 - INFO - Response - Page 3
2025-06-07 08:03:32,634 - INFO - 第 3 页获取到 100 条记录
2025-06-07 08:03:33,150 - INFO - Request Parameters - Page 4:
2025-06-07 08:03:33,150 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:33,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000854, 1745942400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:33,822 - INFO - API请求耗时: 672ms
2025-06-07 08:03:33,822 - INFO - Response - Page 4
2025-06-07 08:03:33,822 - INFO - 第 4 页获取到 100 条记录
2025-06-07 08:03:34,322 - INFO - Request Parameters - Page 5:
2025-06-07 08:03:34,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:34,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000854, 1745942400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:35,041 - INFO - API请求耗时: 719ms
2025-06-07 08:03:35,041 - INFO - Response - Page 5
2025-06-07 08:03:35,041 - INFO - 第 5 页获取到 100 条记录
2025-06-07 08:03:35,541 - INFO - Request Parameters - Page 6:
2025-06-07 08:03:35,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:35,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000854, 1745942400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:36,213 - INFO - API请求耗时: 672ms
2025-06-07 08:03:36,213 - INFO - Response - Page 6
2025-06-07 08:03:36,213 - INFO - 第 6 页获取到 100 条记录
2025-06-07 08:03:36,713 - INFO - Request Parameters - Page 7:
2025-06-07 08:03:36,713 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:36,713 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1745856000854, 1745942400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:37,306 - INFO - API请求耗时: 594ms
2025-06-07 08:03:37,306 - INFO - Response - Page 7
2025-06-07 08:03:37,306 - INFO - 第 7 页获取到 51 条记录
2025-06-07 08:03:37,306 - INFO - 查询完成，共获取到 651 条记录
2025-06-07 08:03:37,306 - INFO - 分段 12 查询成功，获取到 651 条记录
2025-06-07 08:03:38,322 - INFO - 查询分段 13: 2025-05-01 至 2025-05-02
2025-06-07 08:03:38,322 - INFO - 查询日期范围: 2025-05-01 至 2025-05-02，使用分页查询，每页 100 条记录
2025-06-07 08:03:38,322 - INFO - Request Parameters - Page 1:
2025-06-07 08:03:38,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:38,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800854, 1746115200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:39,056 - INFO - API请求耗时: 734ms
2025-06-07 08:03:39,056 - INFO - Response - Page 1
2025-06-07 08:03:39,056 - INFO - 第 1 页获取到 100 条记录
2025-06-07 08:03:39,572 - INFO - Request Parameters - Page 2:
2025-06-07 08:03:39,572 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:39,572 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800854, 1746115200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:40,228 - INFO - API请求耗时: 656ms
2025-06-07 08:03:40,228 - INFO - Response - Page 2
2025-06-07 08:03:40,228 - INFO - 第 2 页获取到 100 条记录
2025-06-07 08:03:40,728 - INFO - Request Parameters - Page 3:
2025-06-07 08:03:40,728 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:40,728 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800854, 1746115200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:41,431 - INFO - API请求耗时: 703ms
2025-06-07 08:03:41,431 - INFO - Response - Page 3
2025-06-07 08:03:41,431 - INFO - 第 3 页获取到 100 条记录
2025-06-07 08:03:41,947 - INFO - Request Parameters - Page 4:
2025-06-07 08:03:41,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:41,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800854, 1746115200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:42,713 - INFO - API请求耗时: 766ms
2025-06-07 08:03:42,713 - INFO - Response - Page 4
2025-06-07 08:03:42,713 - INFO - 第 4 页获取到 100 条记录
2025-06-07 08:03:43,213 - INFO - Request Parameters - Page 5:
2025-06-07 08:03:43,213 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:43,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800854, 1746115200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:43,931 - INFO - API请求耗时: 719ms
2025-06-07 08:03:43,931 - INFO - Response - Page 5
2025-06-07 08:03:43,931 - INFO - 第 5 页获取到 100 条记录
2025-06-07 08:03:44,447 - INFO - Request Parameters - Page 6:
2025-06-07 08:03:44,447 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:44,447 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800854, 1746115200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:45,134 - INFO - API请求耗时: 687ms
2025-06-07 08:03:45,134 - INFO - Response - Page 6
2025-06-07 08:03:45,134 - INFO - 第 6 页获取到 100 条记录
2025-06-07 08:03:45,634 - INFO - Request Parameters - Page 7:
2025-06-07 08:03:45,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:45,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746028800854, 1746115200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:46,181 - INFO - API请求耗时: 547ms
2025-06-07 08:03:46,181 - INFO - Response - Page 7
2025-06-07 08:03:46,181 - INFO - 第 7 页获取到 29 条记录
2025-06-07 08:03:46,181 - INFO - 查询完成，共获取到 629 条记录
2025-06-07 08:03:46,181 - INFO - 分段 13 查询成功，获取到 629 条记录
2025-06-07 08:03:47,197 - INFO - 查询分段 14: 2025-05-03 至 2025-05-04
2025-06-07 08:03:47,197 - INFO - 查询日期范围: 2025-05-03 至 2025-05-04，使用分页查询，每页 100 条记录
2025-06-07 08:03:47,197 - INFO - Request Parameters - Page 1:
2025-06-07 08:03:47,197 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:47,197 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746201600854, 1746288000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:47,994 - INFO - API请求耗时: 797ms
2025-06-07 08:03:47,994 - INFO - Response - Page 1
2025-06-07 08:03:47,994 - INFO - 第 1 页获取到 100 条记录
2025-06-07 08:03:48,494 - INFO - Request Parameters - Page 2:
2025-06-07 08:03:48,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:48,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746201600854, 1746288000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:49,150 - INFO - API请求耗时: 656ms
2025-06-07 08:03:49,150 - INFO - Response - Page 2
2025-06-07 08:03:49,150 - INFO - 第 2 页获取到 100 条记录
2025-06-07 08:03:49,650 - INFO - Request Parameters - Page 3:
2025-06-07 08:03:49,650 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:49,650 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746201600854, 1746288000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:50,337 - INFO - API请求耗时: 688ms
2025-06-07 08:03:50,337 - INFO - Response - Page 3
2025-06-07 08:03:50,337 - INFO - 第 3 页获取到 100 条记录
2025-06-07 08:03:50,853 - INFO - Request Parameters - Page 4:
2025-06-07 08:03:50,853 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:50,853 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746201600854, 1746288000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:51,634 - INFO - API请求耗时: 781ms
2025-06-07 08:03:51,634 - INFO - Response - Page 4
2025-06-07 08:03:51,634 - INFO - 第 4 页获取到 100 条记录
2025-06-07 08:03:52,150 - INFO - Request Parameters - Page 5:
2025-06-07 08:03:52,150 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:52,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746201600854, 1746288000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:52,806 - INFO - API请求耗时: 656ms
2025-06-07 08:03:52,806 - INFO - Response - Page 5
2025-06-07 08:03:52,806 - INFO - 第 5 页获取到 100 条记录
2025-06-07 08:03:53,306 - INFO - Request Parameters - Page 6:
2025-06-07 08:03:53,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:53,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746201600854, 1746288000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:53,994 - INFO - API请求耗时: 687ms
2025-06-07 08:03:53,994 - INFO - Response - Page 6
2025-06-07 08:03:53,994 - INFO - 第 6 页获取到 100 条记录
2025-06-07 08:03:54,509 - INFO - Request Parameters - Page 7:
2025-06-07 08:03:54,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:54,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746201600854, 1746288000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:55,119 - INFO - API请求耗时: 609ms
2025-06-07 08:03:55,134 - INFO - Response - Page 7
2025-06-07 08:03:55,134 - INFO - 第 7 页获取到 42 条记录
2025-06-07 08:03:55,134 - INFO - 查询完成，共获取到 642 条记录
2025-06-07 08:03:55,134 - INFO - 分段 14 查询成功，获取到 642 条记录
2025-06-07 08:03:56,150 - INFO - 查询分段 15: 2025-05-05 至 2025-05-06
2025-06-07 08:03:56,150 - INFO - 查询日期范围: 2025-05-05 至 2025-05-06，使用分页查询，每页 100 条记录
2025-06-07 08:03:56,150 - INFO - Request Parameters - Page 1:
2025-06-07 08:03:56,150 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:56,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400854, 1746460800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:56,853 - INFO - API请求耗时: 703ms
2025-06-07 08:03:56,853 - INFO - Response - Page 1
2025-06-07 08:03:56,853 - INFO - 第 1 页获取到 100 条记录
2025-06-07 08:03:57,353 - INFO - Request Parameters - Page 2:
2025-06-07 08:03:57,353 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:57,353 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400854, 1746460800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:58,087 - INFO - API请求耗时: 734ms
2025-06-07 08:03:58,087 - INFO - Response - Page 2
2025-06-07 08:03:58,103 - INFO - 第 2 页获取到 100 条记录
2025-06-07 08:03:58,603 - INFO - Request Parameters - Page 3:
2025-06-07 08:03:58,603 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:58,603 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400854, 1746460800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:03:59,369 - INFO - API请求耗时: 766ms
2025-06-07 08:03:59,369 - INFO - Response - Page 3
2025-06-07 08:03:59,369 - INFO - 第 3 页获取到 100 条记录
2025-06-07 08:03:59,884 - INFO - Request Parameters - Page 4:
2025-06-07 08:03:59,884 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:03:59,884 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400854, 1746460800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:00,603 - INFO - API请求耗时: 719ms
2025-06-07 08:04:00,603 - INFO - Response - Page 4
2025-06-07 08:04:00,603 - INFO - 第 4 页获取到 100 条记录
2025-06-07 08:04:01,119 - INFO - Request Parameters - Page 5:
2025-06-07 08:04:01,119 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:01,119 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400854, 1746460800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:02,041 - INFO - API请求耗时: 922ms
2025-06-07 08:04:02,041 - INFO - Response - Page 5
2025-06-07 08:04:02,041 - INFO - 第 5 页获取到 100 条记录
2025-06-07 08:04:02,556 - INFO - Request Parameters - Page 6:
2025-06-07 08:04:02,556 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:02,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400854, 1746460800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:03,244 - INFO - API请求耗时: 687ms
2025-06-07 08:04:03,244 - INFO - Response - Page 6
2025-06-07 08:04:03,244 - INFO - 第 6 页获取到 100 条记录
2025-06-07 08:04:03,759 - INFO - Request Parameters - Page 7:
2025-06-07 08:04:03,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:03,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746374400854, 1746460800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:04,228 - INFO - API请求耗时: 469ms
2025-06-07 08:04:04,228 - INFO - Response - Page 7
2025-06-07 08:04:04,228 - INFO - 第 7 页获取到 6 条记录
2025-06-07 08:04:04,228 - INFO - 查询完成，共获取到 606 条记录
2025-06-07 08:04:04,228 - INFO - 分段 15 查询成功，获取到 606 条记录
2025-06-07 08:04:05,244 - INFO - 查询分段 16: 2025-05-07 至 2025-05-08
2025-06-07 08:04:05,244 - INFO - 查询日期范围: 2025-05-07 至 2025-05-08，使用分页查询，每页 100 条记录
2025-06-07 08:04:05,244 - INFO - Request Parameters - Page 1:
2025-06-07 08:04:05,244 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:05,244 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200854, 1746633600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:05,994 - INFO - API请求耗时: 750ms
2025-06-07 08:04:05,994 - INFO - Response - Page 1
2025-06-07 08:04:05,994 - INFO - 第 1 页获取到 100 条记录
2025-06-07 08:04:06,494 - INFO - Request Parameters - Page 2:
2025-06-07 08:04:06,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:06,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200854, 1746633600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:07,259 - INFO - API请求耗时: 766ms
2025-06-07 08:04:07,259 - INFO - Response - Page 2
2025-06-07 08:04:07,259 - INFO - 第 2 页获取到 100 条记录
2025-06-07 08:04:07,775 - INFO - Request Parameters - Page 3:
2025-06-07 08:04:07,775 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:07,775 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200854, 1746633600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:08,447 - INFO - API请求耗时: 672ms
2025-06-07 08:04:08,447 - INFO - Response - Page 3
2025-06-07 08:04:08,447 - INFO - 第 3 页获取到 100 条记录
2025-06-07 08:04:08,962 - INFO - Request Parameters - Page 4:
2025-06-07 08:04:08,962 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:08,962 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200854, 1746633600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:09,712 - INFO - API请求耗时: 750ms
2025-06-07 08:04:09,712 - INFO - Response - Page 4
2025-06-07 08:04:09,712 - INFO - 第 4 页获取到 100 条记录
2025-06-07 08:04:10,228 - INFO - Request Parameters - Page 5:
2025-06-07 08:04:10,228 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:10,228 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200854, 1746633600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:10,900 - INFO - API请求耗时: 672ms
2025-06-07 08:04:10,900 - INFO - Response - Page 5
2025-06-07 08:04:10,900 - INFO - 第 5 页获取到 100 条记录
2025-06-07 08:04:11,415 - INFO - Request Parameters - Page 6:
2025-06-07 08:04:11,415 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:11,415 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200854, 1746633600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:12,134 - INFO - API请求耗时: 719ms
2025-06-07 08:04:12,134 - INFO - Response - Page 6
2025-06-07 08:04:12,134 - INFO - 第 6 页获取到 100 条记录
2025-06-07 08:04:12,650 - INFO - Request Parameters - Page 7:
2025-06-07 08:04:12,650 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:12,650 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746547200854, 1746633600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:13,290 - INFO - API请求耗时: 641ms
2025-06-07 08:04:13,290 - INFO - Response - Page 7
2025-06-07 08:04:13,290 - INFO - 第 7 页获取到 22 条记录
2025-06-07 08:04:13,290 - INFO - 查询完成，共获取到 622 条记录
2025-06-07 08:04:13,290 - INFO - 分段 16 查询成功，获取到 622 条记录
2025-06-07 08:04:14,290 - INFO - 查询分段 17: 2025-05-09 至 2025-05-10
2025-06-07 08:04:14,290 - INFO - 查询日期范围: 2025-05-09 至 2025-05-10，使用分页查询，每页 100 条记录
2025-06-07 08:04:14,290 - INFO - Request Parameters - Page 1:
2025-06-07 08:04:14,290 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:14,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000854, 1746806400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:15,103 - INFO - API请求耗时: 812ms
2025-06-07 08:04:15,103 - INFO - Response - Page 1
2025-06-07 08:04:15,103 - INFO - 第 1 页获取到 100 条记录
2025-06-07 08:04:15,603 - INFO - Request Parameters - Page 2:
2025-06-07 08:04:15,603 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:15,603 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000854, 1746806400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:16,337 - INFO - API请求耗时: 734ms
2025-06-07 08:04:16,337 - INFO - Response - Page 2
2025-06-07 08:04:16,337 - INFO - 第 2 页获取到 100 条记录
2025-06-07 08:04:16,837 - INFO - Request Parameters - Page 3:
2025-06-07 08:04:16,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:16,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000854, 1746806400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:17,775 - INFO - API请求耗时: 937ms
2025-06-07 08:04:17,775 - INFO - Response - Page 3
2025-06-07 08:04:17,775 - INFO - 第 3 页获取到 100 条记录
2025-06-07 08:04:18,290 - INFO - Request Parameters - Page 4:
2025-06-07 08:04:18,290 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:18,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000854, 1746806400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:19,072 - INFO - API请求耗时: 781ms
2025-06-07 08:04:19,072 - INFO - Response - Page 4
2025-06-07 08:04:19,072 - INFO - 第 4 页获取到 100 条记录
2025-06-07 08:04:19,587 - INFO - Request Parameters - Page 5:
2025-06-07 08:04:19,587 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:19,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000854, 1746806400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:20,290 - INFO - API请求耗时: 703ms
2025-06-07 08:04:20,290 - INFO - Response - Page 5
2025-06-07 08:04:20,290 - INFO - 第 5 页获取到 100 条记录
2025-06-07 08:04:20,790 - INFO - Request Parameters - Page 6:
2025-06-07 08:04:20,790 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:20,790 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000854, 1746806400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:21,540 - INFO - API请求耗时: 750ms
2025-06-07 08:04:21,540 - INFO - Response - Page 6
2025-06-07 08:04:21,540 - INFO - 第 6 页获取到 100 条记录
2025-06-07 08:04:22,056 - INFO - Request Parameters - Page 7:
2025-06-07 08:04:22,056 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:22,056 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746720000854, 1746806400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:22,728 - INFO - API请求耗时: 672ms
2025-06-07 08:04:22,728 - INFO - Response - Page 7
2025-06-07 08:04:22,728 - INFO - 第 7 页获取到 54 条记录
2025-06-07 08:04:22,728 - INFO - 查询完成，共获取到 654 条记录
2025-06-07 08:04:22,728 - INFO - 分段 17 查询成功，获取到 654 条记录
2025-06-07 08:04:23,728 - INFO - 查询分段 18: 2025-05-11 至 2025-05-12
2025-06-07 08:04:23,728 - INFO - 查询日期范围: 2025-05-11 至 2025-05-12，使用分页查询，每页 100 条记录
2025-06-07 08:04:23,728 - INFO - Request Parameters - Page 1:
2025-06-07 08:04:23,728 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:23,728 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800854, 1746979200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:24,447 - INFO - API请求耗时: 719ms
2025-06-07 08:04:24,447 - INFO - Response - Page 1
2025-06-07 08:04:24,447 - INFO - 第 1 页获取到 100 条记录
2025-06-07 08:04:24,962 - INFO - Request Parameters - Page 2:
2025-06-07 08:04:24,962 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:24,962 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800854, 1746979200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:25,728 - INFO - API请求耗时: 766ms
2025-06-07 08:04:25,728 - INFO - Response - Page 2
2025-06-07 08:04:25,728 - INFO - 第 2 页获取到 100 条记录
2025-06-07 08:04:26,243 - INFO - Request Parameters - Page 3:
2025-06-07 08:04:26,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:26,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800854, 1746979200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:26,900 - INFO - API请求耗时: 656ms
2025-06-07 08:04:26,900 - INFO - Response - Page 3
2025-06-07 08:04:26,900 - INFO - 第 3 页获取到 100 条记录
2025-06-07 08:04:27,400 - INFO - Request Parameters - Page 4:
2025-06-07 08:04:27,400 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:27,400 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800854, 1746979200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:28,103 - INFO - API请求耗时: 703ms
2025-06-07 08:04:28,103 - INFO - Response - Page 4
2025-06-07 08:04:28,103 - INFO - 第 4 页获取到 100 条记录
2025-06-07 08:04:28,603 - INFO - Request Parameters - Page 5:
2025-06-07 08:04:28,603 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:28,603 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800854, 1746979200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:29,275 - INFO - API请求耗时: 672ms
2025-06-07 08:04:29,275 - INFO - Response - Page 5
2025-06-07 08:04:29,275 - INFO - 第 5 页获取到 100 条记录
2025-06-07 08:04:29,790 - INFO - Request Parameters - Page 6:
2025-06-07 08:04:29,790 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:29,790 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800854, 1746979200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:30,462 - INFO - API请求耗时: 672ms
2025-06-07 08:04:30,462 - INFO - Response - Page 6
2025-06-07 08:04:30,462 - INFO - 第 6 页获取到 100 条记录
2025-06-07 08:04:30,978 - INFO - Request Parameters - Page 7:
2025-06-07 08:04:30,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:30,978 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1746892800854, 1746979200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:31,493 - INFO - API请求耗时: 516ms
2025-06-07 08:04:31,493 - INFO - Response - Page 7
2025-06-07 08:04:31,493 - INFO - 第 7 页获取到 44 条记录
2025-06-07 08:04:31,493 - INFO - 查询完成，共获取到 644 条记录
2025-06-07 08:04:31,493 - INFO - 分段 18 查询成功，获取到 644 条记录
2025-06-07 08:04:32,509 - INFO - 查询分段 19: 2025-05-13 至 2025-05-14
2025-06-07 08:04:32,509 - INFO - 查询日期范围: 2025-05-13 至 2025-05-14，使用分页查询，每页 100 条记录
2025-06-07 08:04:32,509 - INFO - Request Parameters - Page 1:
2025-06-07 08:04:32,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:32,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600854, 1747152000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:33,212 - INFO - API请求耗时: 703ms
2025-06-07 08:04:33,212 - INFO - Response - Page 1
2025-06-07 08:04:33,212 - INFO - 第 1 页获取到 100 条记录
2025-06-07 08:04:33,712 - INFO - Request Parameters - Page 2:
2025-06-07 08:04:33,712 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:33,712 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600854, 1747152000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:34,337 - INFO - API请求耗时: 625ms
2025-06-07 08:04:34,337 - INFO - Response - Page 2
2025-06-07 08:04:34,353 - INFO - 第 2 页获取到 100 条记录
2025-06-07 08:04:34,868 - INFO - Request Parameters - Page 3:
2025-06-07 08:04:34,868 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:34,868 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600854, 1747152000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:35,540 - INFO - API请求耗时: 672ms
2025-06-07 08:04:35,540 - INFO - Response - Page 3
2025-06-07 08:04:35,540 - INFO - 第 3 页获取到 100 条记录
2025-06-07 08:04:36,040 - INFO - Request Parameters - Page 4:
2025-06-07 08:04:36,040 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:36,040 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600854, 1747152000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:36,775 - INFO - API请求耗时: 734ms
2025-06-07 08:04:36,775 - INFO - Response - Page 4
2025-06-07 08:04:36,775 - INFO - 第 4 页获取到 100 条记录
2025-06-07 08:04:37,290 - INFO - Request Parameters - Page 5:
2025-06-07 08:04:37,290 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:37,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600854, 1747152000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:37,993 - INFO - API请求耗时: 703ms
2025-06-07 08:04:37,993 - INFO - Response - Page 5
2025-06-07 08:04:37,993 - INFO - 第 5 页获取到 100 条记录
2025-06-07 08:04:38,509 - INFO - Request Parameters - Page 6:
2025-06-07 08:04:38,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:38,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600854, 1747152000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:39,290 - INFO - API请求耗时: 781ms
2025-06-07 08:04:39,290 - INFO - Response - Page 6
2025-06-07 08:04:39,290 - INFO - 第 6 页获取到 100 条记录
2025-06-07 08:04:39,806 - INFO - Request Parameters - Page 7:
2025-06-07 08:04:39,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:39,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747065600854, 1747152000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:40,384 - INFO - API请求耗时: 578ms
2025-06-07 08:04:40,384 - INFO - Response - Page 7
2025-06-07 08:04:40,384 - INFO - 第 7 页获取到 21 条记录
2025-06-07 08:04:40,384 - INFO - 查询完成，共获取到 621 条记录
2025-06-07 08:04:40,384 - INFO - 分段 19 查询成功，获取到 621 条记录
2025-06-07 08:04:41,400 - INFO - 查询分段 20: 2025-05-15 至 2025-05-16
2025-06-07 08:04:41,400 - INFO - 查询日期范围: 2025-05-15 至 2025-05-16，使用分页查询，每页 100 条记录
2025-06-07 08:04:41,400 - INFO - Request Parameters - Page 1:
2025-06-07 08:04:41,400 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:41,400 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400854, 1747324800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:42,165 - INFO - API请求耗时: 766ms
2025-06-07 08:04:42,165 - INFO - Response - Page 1
2025-06-07 08:04:42,165 - INFO - 第 1 页获取到 100 条记录
2025-06-07 08:04:42,681 - INFO - Request Parameters - Page 2:
2025-06-07 08:04:42,681 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:42,681 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400854, 1747324800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:43,431 - INFO - API请求耗时: 750ms
2025-06-07 08:04:43,431 - INFO - Response - Page 2
2025-06-07 08:04:43,431 - INFO - 第 2 页获取到 100 条记录
2025-06-07 08:04:43,946 - INFO - Request Parameters - Page 3:
2025-06-07 08:04:43,946 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:43,946 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400854, 1747324800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:44,618 - INFO - API请求耗时: 672ms
2025-06-07 08:04:44,618 - INFO - Response - Page 3
2025-06-07 08:04:44,618 - INFO - 第 3 页获取到 100 条记录
2025-06-07 08:04:45,118 - INFO - Request Parameters - Page 4:
2025-06-07 08:04:45,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:45,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400854, 1747324800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:45,790 - INFO - API请求耗时: 672ms
2025-06-07 08:04:45,790 - INFO - Response - Page 4
2025-06-07 08:04:45,790 - INFO - 第 4 页获取到 100 条记录
2025-06-07 08:04:46,306 - INFO - Request Parameters - Page 5:
2025-06-07 08:04:46,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:46,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400854, 1747324800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:47,118 - INFO - API请求耗时: 812ms
2025-06-07 08:04:47,118 - INFO - Response - Page 5
2025-06-07 08:04:47,118 - INFO - 第 5 页获取到 100 条记录
2025-06-07 08:04:47,618 - INFO - Request Parameters - Page 6:
2025-06-07 08:04:47,618 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:47,618 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400854, 1747324800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:48,462 - INFO - API请求耗时: 844ms
2025-06-07 08:04:48,462 - INFO - Response - Page 6
2025-06-07 08:04:48,462 - INFO - 第 6 页获取到 100 条记录
2025-06-07 08:04:48,978 - INFO - Request Parameters - Page 7:
2025-06-07 08:04:48,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:48,978 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747238400854, 1747324800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:49,587 - INFO - API请求耗时: 609ms
2025-06-07 08:04:49,587 - INFO - Response - Page 7
2025-06-07 08:04:49,587 - INFO - 第 7 页获取到 30 条记录
2025-06-07 08:04:49,587 - INFO - 查询完成，共获取到 630 条记录
2025-06-07 08:04:49,587 - INFO - 分段 20 查询成功，获取到 630 条记录
2025-06-07 08:04:50,603 - INFO - 查询分段 21: 2025-05-17 至 2025-05-18
2025-06-07 08:04:50,603 - INFO - 查询日期范围: 2025-05-17 至 2025-05-18，使用分页查询，每页 100 条记录
2025-06-07 08:04:50,603 - INFO - Request Parameters - Page 1:
2025-06-07 08:04:50,603 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:50,603 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747411200854, 1747497600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:51,290 - INFO - API请求耗时: 688ms
2025-06-07 08:04:51,290 - INFO - Response - Page 1
2025-06-07 08:04:51,290 - INFO - 第 1 页获取到 100 条记录
2025-06-07 08:04:51,790 - INFO - Request Parameters - Page 2:
2025-06-07 08:04:51,790 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:51,790 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747411200854, 1747497600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:52,400 - INFO - API请求耗时: 609ms
2025-06-07 08:04:52,400 - INFO - Response - Page 2
2025-06-07 08:04:52,400 - INFO - 第 2 页获取到 100 条记录
2025-06-07 08:04:52,900 - INFO - Request Parameters - Page 3:
2025-06-07 08:04:52,900 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:52,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747411200854, 1747497600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:53,587 - INFO - API请求耗时: 687ms
2025-06-07 08:04:53,587 - INFO - Response - Page 3
2025-06-07 08:04:53,587 - INFO - 第 3 页获取到 100 条记录
2025-06-07 08:04:54,103 - INFO - Request Parameters - Page 4:
2025-06-07 08:04:54,103 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:54,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747411200854, 1747497600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:54,759 - INFO - API请求耗时: 656ms
2025-06-07 08:04:54,759 - INFO - Response - Page 4
2025-06-07 08:04:54,759 - INFO - 第 4 页获取到 100 条记录
2025-06-07 08:04:55,275 - INFO - Request Parameters - Page 5:
2025-06-07 08:04:55,275 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:55,275 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747411200854, 1747497600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:55,978 - INFO - API请求耗时: 703ms
2025-06-07 08:04:55,978 - INFO - Response - Page 5
2025-06-07 08:04:55,978 - INFO - 第 5 页获取到 100 条记录
2025-06-07 08:04:56,493 - INFO - Request Parameters - Page 6:
2025-06-07 08:04:56,493 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:56,493 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747411200854, 1747497600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:57,196 - INFO - API请求耗时: 703ms
2025-06-07 08:04:57,196 - INFO - Response - Page 6
2025-06-07 08:04:57,196 - INFO - 第 6 页获取到 100 条记录
2025-06-07 08:04:57,696 - INFO - Request Parameters - Page 7:
2025-06-07 08:04:57,696 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:57,696 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747411200854, 1747497600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:04:58,275 - INFO - API请求耗时: 578ms
2025-06-07 08:04:58,290 - INFO - Response - Page 7
2025-06-07 08:04:58,290 - INFO - 第 7 页获取到 48 条记录
2025-06-07 08:04:58,290 - INFO - 查询完成，共获取到 648 条记录
2025-06-07 08:04:58,290 - INFO - 分段 21 查询成功，获取到 648 条记录
2025-06-07 08:04:59,306 - INFO - 查询分段 22: 2025-05-19 至 2025-05-20
2025-06-07 08:04:59,306 - INFO - 查询日期范围: 2025-05-19 至 2025-05-20，使用分页查询，每页 100 条记录
2025-06-07 08:04:59,306 - INFO - Request Parameters - Page 1:
2025-06-07 08:04:59,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:04:59,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747584000854, 1747670400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:00,243 - INFO - API请求耗时: 937ms
2025-06-07 08:05:00,243 - INFO - Response - Page 1
2025-06-07 08:05:00,243 - INFO - 第 1 页获取到 100 条记录
2025-06-07 08:05:00,759 - INFO - Request Parameters - Page 2:
2025-06-07 08:05:00,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:00,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747584000854, 1747670400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:01,587 - INFO - API请求耗时: 828ms
2025-06-07 08:05:01,587 - INFO - Response - Page 2
2025-06-07 08:05:01,587 - INFO - 第 2 页获取到 100 条记录
2025-06-07 08:05:02,103 - INFO - Request Parameters - Page 3:
2025-06-07 08:05:02,103 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:02,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747584000854, 1747670400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:02,837 - INFO - API请求耗时: 734ms
2025-06-07 08:05:02,837 - INFO - Response - Page 3
2025-06-07 08:05:02,837 - INFO - 第 3 页获取到 100 条记录
2025-06-07 08:05:03,337 - INFO - Request Parameters - Page 4:
2025-06-07 08:05:03,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:03,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747584000854, 1747670400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:04,118 - INFO - API请求耗时: 781ms
2025-06-07 08:05:04,118 - INFO - Response - Page 4
2025-06-07 08:05:04,118 - INFO - 第 4 页获取到 100 条记录
2025-06-07 08:05:04,634 - INFO - Request Parameters - Page 5:
2025-06-07 08:05:04,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:04,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747584000854, 1747670400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:05,368 - INFO - API请求耗时: 734ms
2025-06-07 08:05:05,368 - INFO - Response - Page 5
2025-06-07 08:05:05,368 - INFO - 第 5 页获取到 100 条记录
2025-06-07 08:05:05,884 - INFO - Request Parameters - Page 6:
2025-06-07 08:05:05,884 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:05,884 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747584000854, 1747670400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:06,540 - INFO - API请求耗时: 656ms
2025-06-07 08:05:06,540 - INFO - Response - Page 6
2025-06-07 08:05:06,540 - INFO - 第 6 页获取到 100 条记录
2025-06-07 08:05:07,040 - INFO - Request Parameters - Page 7:
2025-06-07 08:05:07,040 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:07,040 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747584000854, 1747670400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:07,634 - INFO - API请求耗时: 594ms
2025-06-07 08:05:07,634 - INFO - Response - Page 7
2025-06-07 08:05:07,634 - INFO - 第 7 页获取到 39 条记录
2025-06-07 08:05:07,634 - INFO - 查询完成，共获取到 639 条记录
2025-06-07 08:05:07,634 - INFO - 分段 22 查询成功，获取到 639 条记录
2025-06-07 08:05:08,649 - INFO - 查询分段 23: 2025-05-21 至 2025-05-22
2025-06-07 08:05:08,649 - INFO - 查询日期范围: 2025-05-21 至 2025-05-22，使用分页查询，每页 100 条记录
2025-06-07 08:05:08,649 - INFO - Request Parameters - Page 1:
2025-06-07 08:05:08,649 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:08,649 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747756800854, 1747843200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:09,321 - INFO - API请求耗时: 672ms
2025-06-07 08:05:09,321 - INFO - Response - Page 1
2025-06-07 08:05:09,321 - INFO - 第 1 页获取到 100 条记录
2025-06-07 08:05:09,821 - INFO - Request Parameters - Page 2:
2025-06-07 08:05:09,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:09,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747756800854, 1747843200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:10,540 - INFO - API请求耗时: 719ms
2025-06-07 08:05:10,540 - INFO - Response - Page 2
2025-06-07 08:05:10,540 - INFO - 第 2 页获取到 100 条记录
2025-06-07 08:05:11,056 - INFO - Request Parameters - Page 3:
2025-06-07 08:05:11,056 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:11,056 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747756800854, 1747843200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:11,821 - INFO - API请求耗时: 766ms
2025-06-07 08:05:11,821 - INFO - Response - Page 3
2025-06-07 08:05:11,821 - INFO - 第 3 页获取到 100 条记录
2025-06-07 08:05:12,337 - INFO - Request Parameters - Page 4:
2025-06-07 08:05:12,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:12,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747756800854, 1747843200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:13,009 - INFO - API请求耗时: 672ms
2025-06-07 08:05:13,009 - INFO - Response - Page 4
2025-06-07 08:05:13,009 - INFO - 第 4 页获取到 100 条记录
2025-06-07 08:05:13,524 - INFO - Request Parameters - Page 5:
2025-06-07 08:05:13,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:13,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747756800854, 1747843200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:14,290 - INFO - API请求耗时: 766ms
2025-06-07 08:05:14,290 - INFO - Response - Page 5
2025-06-07 08:05:14,290 - INFO - 第 5 页获取到 100 条记录
2025-06-07 08:05:14,806 - INFO - Request Parameters - Page 6:
2025-06-07 08:05:14,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:14,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747756800854, 1747843200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:15,509 - INFO - API请求耗时: 703ms
2025-06-07 08:05:15,509 - INFO - Response - Page 6
2025-06-07 08:05:15,509 - INFO - 第 6 页获取到 100 条记录
2025-06-07 08:05:16,009 - INFO - Request Parameters - Page 7:
2025-06-07 08:05:16,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:16,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747756800854, 1747843200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:16,509 - INFO - API请求耗时: 500ms
2025-06-07 08:05:16,509 - INFO - Response - Page 7
2025-06-07 08:05:16,509 - INFO - 第 7 页获取到 24 条记录
2025-06-07 08:05:16,509 - INFO - 查询完成，共获取到 624 条记录
2025-06-07 08:05:16,509 - INFO - 分段 23 查询成功，获取到 624 条记录
2025-06-07 08:05:17,509 - INFO - 查询分段 24: 2025-05-23 至 2025-05-24
2025-06-07 08:05:17,509 - INFO - 查询日期范围: 2025-05-23 至 2025-05-24，使用分页查询，每页 100 条记录
2025-06-07 08:05:17,509 - INFO - Request Parameters - Page 1:
2025-06-07 08:05:17,509 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:17,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747929600854, 1748016000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:18,149 - INFO - API请求耗时: 641ms
2025-06-07 08:05:18,149 - INFO - Response - Page 1
2025-06-07 08:05:18,149 - INFO - 第 1 页获取到 100 条记录
2025-06-07 08:05:18,649 - INFO - Request Parameters - Page 2:
2025-06-07 08:05:18,649 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:18,649 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747929600854, 1748016000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:19,399 - INFO - API请求耗时: 750ms
2025-06-07 08:05:19,415 - INFO - Response - Page 2
2025-06-07 08:05:19,415 - INFO - 第 2 页获取到 100 条记录
2025-06-07 08:05:19,915 - INFO - Request Parameters - Page 3:
2025-06-07 08:05:19,915 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:19,915 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747929600854, 1748016000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:20,634 - INFO - API请求耗时: 719ms
2025-06-07 08:05:20,634 - INFO - Response - Page 3
2025-06-07 08:05:20,634 - INFO - 第 3 页获取到 100 条记录
2025-06-07 08:05:21,149 - INFO - Request Parameters - Page 4:
2025-06-07 08:05:21,149 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:21,149 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747929600854, 1748016000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:21,868 - INFO - API请求耗时: 719ms
2025-06-07 08:05:21,868 - INFO - Response - Page 4
2025-06-07 08:05:21,868 - INFO - 第 4 页获取到 100 条记录
2025-06-07 08:05:22,368 - INFO - Request Parameters - Page 5:
2025-06-07 08:05:22,368 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:22,368 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747929600854, 1748016000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:23,102 - INFO - API请求耗时: 734ms
2025-06-07 08:05:23,102 - INFO - Response - Page 5
2025-06-07 08:05:23,102 - INFO - 第 5 页获取到 100 条记录
2025-06-07 08:05:23,618 - INFO - Request Parameters - Page 6:
2025-06-07 08:05:23,618 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:23,618 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747929600854, 1748016000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:24,337 - INFO - API请求耗时: 719ms
2025-06-07 08:05:24,337 - INFO - Response - Page 6
2025-06-07 08:05:24,337 - INFO - 第 6 页获取到 100 条记录
2025-06-07 08:05:24,852 - INFO - Request Parameters - Page 7:
2025-06-07 08:05:24,852 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:24,852 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1747929600854, 1748016000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:25,509 - INFO - API请求耗时: 656ms
2025-06-07 08:05:25,509 - INFO - Response - Page 7
2025-06-07 08:05:25,509 - INFO - 第 7 页获取到 45 条记录
2025-06-07 08:05:25,509 - INFO - 查询完成，共获取到 645 条记录
2025-06-07 08:05:25,509 - INFO - 分段 24 查询成功，获取到 645 条记录
2025-06-07 08:05:26,524 - INFO - 查询分段 25: 2025-05-25 至 2025-05-26
2025-06-07 08:05:26,524 - INFO - 查询日期范围: 2025-05-25 至 2025-05-26，使用分页查询，每页 100 条记录
2025-06-07 08:05:26,524 - INFO - Request Parameters - Page 1:
2025-06-07 08:05:26,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:26,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748102400854, 1748188800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:27,181 - INFO - API请求耗时: 656ms
2025-06-07 08:05:27,196 - INFO - Response - Page 1
2025-06-07 08:05:27,196 - INFO - 第 1 页获取到 100 条记录
2025-06-07 08:05:27,712 - INFO - Request Parameters - Page 2:
2025-06-07 08:05:27,712 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:27,712 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748102400854, 1748188800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:28,399 - INFO - API请求耗时: 688ms
2025-06-07 08:05:28,399 - INFO - Response - Page 2
2025-06-07 08:05:28,399 - INFO - 第 2 页获取到 100 条记录
2025-06-07 08:05:28,915 - INFO - Request Parameters - Page 3:
2025-06-07 08:05:28,915 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:28,915 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748102400854, 1748188800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:29,618 - INFO - API请求耗时: 703ms
2025-06-07 08:05:29,634 - INFO - Response - Page 3
2025-06-07 08:05:29,634 - INFO - 第 3 页获取到 100 条记录
2025-06-07 08:05:30,149 - INFO - Request Parameters - Page 4:
2025-06-07 08:05:30,149 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:30,149 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748102400854, 1748188800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:30,790 - INFO - API请求耗时: 641ms
2025-06-07 08:05:30,790 - INFO - Response - Page 4
2025-06-07 08:05:30,790 - INFO - 第 4 页获取到 100 条记录
2025-06-07 08:05:31,306 - INFO - Request Parameters - Page 5:
2025-06-07 08:05:31,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:31,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748102400854, 1748188800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:32,009 - INFO - API请求耗时: 703ms
2025-06-07 08:05:32,009 - INFO - Response - Page 5
2025-06-07 08:05:32,009 - INFO - 第 5 页获取到 100 条记录
2025-06-07 08:05:32,524 - INFO - Request Parameters - Page 6:
2025-06-07 08:05:32,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:32,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748102400854, 1748188800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:33,212 - INFO - API请求耗时: 687ms
2025-06-07 08:05:33,212 - INFO - Response - Page 6
2025-06-07 08:05:33,212 - INFO - 第 6 页获取到 100 条记录
2025-06-07 08:05:33,727 - INFO - Request Parameters - Page 7:
2025-06-07 08:05:33,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:33,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748102400854, 1748188800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:34,196 - INFO - API请求耗时: 469ms
2025-06-07 08:05:34,196 - INFO - Response - Page 7
2025-06-07 08:05:34,196 - INFO - 第 7 页获取到 15 条记录
2025-06-07 08:05:34,196 - INFO - 查询完成，共获取到 615 条记录
2025-06-07 08:05:34,196 - INFO - 分段 25 查询成功，获取到 615 条记录
2025-06-07 08:05:35,196 - INFO - 查询分段 26: 2025-05-27 至 2025-05-28
2025-06-07 08:05:35,196 - INFO - 查询日期范围: 2025-05-27 至 2025-05-28，使用分页查询，每页 100 条记录
2025-06-07 08:05:35,196 - INFO - Request Parameters - Page 1:
2025-06-07 08:05:35,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:35,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748275200854, 1748361600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:35,899 - INFO - API请求耗时: 703ms
2025-06-07 08:05:35,899 - INFO - Response - Page 1
2025-06-07 08:05:35,899 - INFO - 第 1 页获取到 100 条记录
2025-06-07 08:05:36,415 - INFO - Request Parameters - Page 2:
2025-06-07 08:05:36,415 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:36,415 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748275200854, 1748361600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:37,134 - INFO - API请求耗时: 719ms
2025-06-07 08:05:37,134 - INFO - Response - Page 2
2025-06-07 08:05:37,134 - INFO - 第 2 页获取到 100 条记录
2025-06-07 08:05:37,634 - INFO - Request Parameters - Page 3:
2025-06-07 08:05:37,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:37,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748275200854, 1748361600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:38,337 - INFO - API请求耗时: 703ms
2025-06-07 08:05:38,337 - INFO - Response - Page 3
2025-06-07 08:05:38,337 - INFO - 第 3 页获取到 100 条记录
2025-06-07 08:05:38,837 - INFO - Request Parameters - Page 4:
2025-06-07 08:05:38,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:38,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748275200854, 1748361600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:39,571 - INFO - API请求耗时: 734ms
2025-06-07 08:05:39,571 - INFO - Response - Page 4
2025-06-07 08:05:39,571 - INFO - 第 4 页获取到 100 条记录
2025-06-07 08:05:40,071 - INFO - Request Parameters - Page 5:
2025-06-07 08:05:40,071 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:40,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748275200854, 1748361600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:40,727 - INFO - API请求耗时: 656ms
2025-06-07 08:05:40,727 - INFO - Response - Page 5
2025-06-07 08:05:40,727 - INFO - 第 5 页获取到 100 条记录
2025-06-07 08:05:41,243 - INFO - Request Parameters - Page 6:
2025-06-07 08:05:41,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:41,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748275200854, 1748361600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:41,993 - INFO - API请求耗时: 750ms
2025-06-07 08:05:41,993 - INFO - Response - Page 6
2025-06-07 08:05:41,993 - INFO - 第 6 页获取到 100 条记录
2025-06-07 08:05:42,493 - INFO - Request Parameters - Page 7:
2025-06-07 08:05:42,493 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:42,493 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748275200854, 1748361600854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:43,024 - INFO - API请求耗时: 531ms
2025-06-07 08:05:43,024 - INFO - Response - Page 7
2025-06-07 08:05:43,024 - INFO - 第 7 页获取到 24 条记录
2025-06-07 08:05:43,024 - INFO - 查询完成，共获取到 624 条记录
2025-06-07 08:05:43,024 - INFO - 分段 26 查询成功，获取到 624 条记录
2025-06-07 08:05:44,040 - INFO - 查询分段 27: 2025-05-29 至 2025-05-30
2025-06-07 08:05:44,040 - INFO - 查询日期范围: 2025-05-29 至 2025-05-30，使用分页查询，每页 100 条记录
2025-06-07 08:05:44,040 - INFO - Request Parameters - Page 1:
2025-06-07 08:05:44,040 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:44,040 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748448000854, 1748534400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:44,712 - INFO - API请求耗时: 672ms
2025-06-07 08:05:44,712 - INFO - Response - Page 1
2025-06-07 08:05:44,712 - INFO - 第 1 页获取到 100 条记录
2025-06-07 08:05:45,212 - INFO - Request Parameters - Page 2:
2025-06-07 08:05:45,212 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:45,212 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748448000854, 1748534400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:45,884 - INFO - API请求耗时: 672ms
2025-06-07 08:05:45,884 - INFO - Response - Page 2
2025-06-07 08:05:45,884 - INFO - 第 2 页获取到 100 条记录
2025-06-07 08:05:46,384 - INFO - Request Parameters - Page 3:
2025-06-07 08:05:46,384 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:46,384 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748448000854, 1748534400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:47,040 - INFO - API请求耗时: 656ms
2025-06-07 08:05:47,040 - INFO - Response - Page 3
2025-06-07 08:05:47,040 - INFO - 第 3 页获取到 100 条记录
2025-06-07 08:05:47,555 - INFO - Request Parameters - Page 4:
2025-06-07 08:05:47,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:47,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748448000854, 1748534400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:48,274 - INFO - API请求耗时: 719ms
2025-06-07 08:05:48,274 - INFO - Response - Page 4
2025-06-07 08:05:48,274 - INFO - 第 4 页获取到 100 条记录
2025-06-07 08:05:48,790 - INFO - Request Parameters - Page 5:
2025-06-07 08:05:48,790 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:48,790 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748448000854, 1748534400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:49,493 - INFO - API请求耗时: 703ms
2025-06-07 08:05:49,493 - INFO - Response - Page 5
2025-06-07 08:05:49,493 - INFO - 第 5 页获取到 100 条记录
2025-06-07 08:05:49,993 - INFO - Request Parameters - Page 6:
2025-06-07 08:05:49,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:49,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748448000854, 1748534400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:50,774 - INFO - API请求耗时: 781ms
2025-06-07 08:05:50,774 - INFO - Response - Page 6
2025-06-07 08:05:50,774 - INFO - 第 6 页获取到 100 条记录
2025-06-07 08:05:51,290 - INFO - Request Parameters - Page 7:
2025-06-07 08:05:51,290 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:51,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748448000854, 1748534400854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:51,805 - INFO - API请求耗时: 516ms
2025-06-07 08:05:51,805 - INFO - Response - Page 7
2025-06-07 08:05:51,805 - INFO - 第 7 页获取到 24 条记录
2025-06-07 08:05:51,805 - INFO - 查询完成，共获取到 624 条记录
2025-06-07 08:05:51,805 - INFO - 分段 27 查询成功，获取到 624 条记录
2025-06-07 08:05:52,821 - INFO - 查询分段 28: 2025-05-31 至 2025-06-01
2025-06-07 08:05:52,821 - INFO - 查询日期范围: 2025-05-31 至 2025-06-01，使用分页查询，每页 100 条记录
2025-06-07 08:05:52,821 - INFO - Request Parameters - Page 1:
2025-06-07 08:05:52,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:52,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748620800854, 1748707200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:53,524 - INFO - API请求耗时: 703ms
2025-06-07 08:05:53,524 - INFO - Response - Page 1
2025-06-07 08:05:53,524 - INFO - 第 1 页获取到 100 条记录
2025-06-07 08:05:54,040 - INFO - Request Parameters - Page 2:
2025-06-07 08:05:54,040 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:54,040 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748620800854, 1748707200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:54,837 - INFO - API请求耗时: 797ms
2025-06-07 08:05:54,837 - INFO - Response - Page 2
2025-06-07 08:05:54,837 - INFO - 第 2 页获取到 100 条记录
2025-06-07 08:05:55,337 - INFO - Request Parameters - Page 3:
2025-06-07 08:05:55,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:55,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748620800854, 1748707200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:56,009 - INFO - API请求耗时: 672ms
2025-06-07 08:05:56,009 - INFO - Response - Page 3
2025-06-07 08:05:56,009 - INFO - 第 3 页获取到 100 条记录
2025-06-07 08:05:56,524 - INFO - Request Parameters - Page 4:
2025-06-07 08:05:56,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:56,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748620800854, 1748707200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:57,227 - INFO - API请求耗时: 703ms
2025-06-07 08:05:57,227 - INFO - Response - Page 4
2025-06-07 08:05:57,227 - INFO - 第 4 页获取到 100 条记录
2025-06-07 08:05:57,727 - INFO - Request Parameters - Page 5:
2025-06-07 08:05:57,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:57,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748620800854, 1748707200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:58,477 - INFO - API请求耗时: 750ms
2025-06-07 08:05:58,477 - INFO - Response - Page 5
2025-06-07 08:05:58,477 - INFO - 第 5 页获取到 100 条记录
2025-06-07 08:05:58,993 - INFO - Request Parameters - Page 6:
2025-06-07 08:05:58,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:05:58,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748620800854, 1748707200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:05:59,727 - INFO - API请求耗时: 734ms
2025-06-07 08:05:59,727 - INFO - Response - Page 6
2025-06-07 08:05:59,727 - INFO - 第 6 页获取到 100 条记录
2025-06-07 08:06:00,227 - INFO - Request Parameters - Page 7:
2025-06-07 08:06:00,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:06:00,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748620800854, 1748707200854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:06:00,696 - INFO - API请求耗时: 469ms
2025-06-07 08:06:00,696 - INFO - Response - Page 7
2025-06-07 08:06:00,696 - INFO - 第 7 页获取到 16 条记录
2025-06-07 08:06:00,696 - INFO - 查询完成，共获取到 616 条记录
2025-06-07 08:06:00,696 - INFO - 分段 28 查询成功，获取到 616 条记录
2025-06-07 08:06:01,712 - INFO - 查询分段 29: 2025-06-02 至 2025-06-03
2025-06-07 08:06:01,712 - INFO - 查询日期范围: 2025-06-02 至 2025-06-03，使用分页查询，每页 100 条记录
2025-06-07 08:06:01,712 - INFO - Request Parameters - Page 1:
2025-06-07 08:06:01,712 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:06:01,712 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748793600854, 1748880000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:06:02,446 - INFO - API请求耗时: 734ms
2025-06-07 08:06:02,446 - INFO - Response - Page 1
2025-06-07 08:06:02,446 - INFO - 第 1 页获取到 100 条记录
2025-06-07 08:06:02,962 - INFO - Request Parameters - Page 2:
2025-06-07 08:06:02,962 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:06:02,962 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748793600854, 1748880000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:06:03,696 - INFO - API请求耗时: 734ms
2025-06-07 08:06:03,696 - INFO - Response - Page 2
2025-06-07 08:06:03,696 - INFO - 第 2 页获取到 100 条记录
2025-06-07 08:06:04,196 - INFO - Request Parameters - Page 3:
2025-06-07 08:06:04,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:06:04,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748793600854, 1748880000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:06:04,883 - INFO - API请求耗时: 687ms
2025-06-07 08:06:04,883 - INFO - Response - Page 3
2025-06-07 08:06:04,883 - INFO - 第 3 页获取到 100 条记录
2025-06-07 08:06:05,383 - INFO - Request Parameters - Page 4:
2025-06-07 08:06:05,383 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:06:05,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748793600854, 1748880000854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:06:06,087 - INFO - API请求耗时: 703ms
2025-06-07 08:06:06,102 - INFO - Response - Page 4
2025-06-07 08:06:06,102 - INFO - 第 4 页获取到 98 条记录
2025-06-07 08:06:06,102 - INFO - 查询完成，共获取到 398 条记录
2025-06-07 08:06:06,102 - INFO - 分段 29 查询成功，获取到 398 条记录
2025-06-07 08:06:07,118 - INFO - 查询分段 30: 2025-06-04 至 2025-06-05
2025-06-07 08:06:07,118 - INFO - 查询日期范围: 2025-06-04 至 2025-06-05，使用分页查询，每页 100 条记录
2025-06-07 08:06:07,118 - INFO - Request Parameters - Page 1:
2025-06-07 08:06:07,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:06:07,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748966400854, 1749052800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:06:07,899 - INFO - API请求耗时: 781ms
2025-06-07 08:06:07,899 - INFO - Response - Page 1
2025-06-07 08:06:07,899 - INFO - 第 1 页获取到 100 条记录
2025-06-07 08:06:08,399 - INFO - Request Parameters - Page 2:
2025-06-07 08:06:08,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:06:08,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748966400854, 1749052800854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:06:09,337 - INFO - API请求耗时: 938ms
2025-06-07 08:06:09,337 - INFO - Response - Page 2
2025-06-07 08:06:09,337 - INFO - 第 2 页获取到 98 条记录
2025-06-07 08:06:09,337 - INFO - 查询完成，共获取到 198 条记录
2025-06-07 08:06:09,337 - INFO - 分段 30 查询成功，获取到 198 条记录
2025-06-07 08:06:10,352 - INFO - 查询分段 31: 2025-06-06 至 2025-06-06
2025-06-07 08:06:10,352 - INFO - 查询日期范围: 2025-06-06 至 2025-06-06，使用分页查询，每页 100 条记录
2025-06-07 08:06:10,352 - INFO - Request Parameters - Page 1:
2025-06-07 08:06:10,352 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:06:10,352 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-888A8AFDCE0A46FDA86BF92183AF6D4CJN3P', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1749139200854, 1749225599854], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:06:10,727 - INFO - API请求耗时: 375ms
2025-06-07 08:06:10,727 - INFO - Response - Page 1
2025-06-07 08:06:10,727 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-07 08:06:10,727 - INFO - 查询完成，共获取到 0 条记录
2025-06-07 08:06:10,727 - WARNING - 分段 31 查询返回空数据
2025-06-07 08:06:11,743 - INFO - 宜搭每日表单数据查询完成，共 31 个分段，成功获取 18389 条记录，失败 0 次
2025-06-07 08:06:11,743 - INFO - 成功获取宜搭日销售表单数据，共 18389 条记录
2025-06-07 08:06:11,743 - INFO - 宜搭日销售数据项结构示例: ['formInstanceId', 'formData']
2025-06-07 08:06:11,743 - INFO - 开始对比和同步日销售数据...
2025-06-07 08:06:12,243 - INFO - 成功创建宜搭日销售数据索引，共 6331 条记录
2025-06-07 08:06:12,243 - INFO - 开始处理数衍数据，共 12866 条记录
2025-06-07 08:06:12,868 - INFO - 更新表单数据成功: FINST-3PF66O71I9VVGJJ57EM1SACOQMIW32RQXKEBMBD
2025-06-07 08:06:12,868 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EG92S1SB0I86N3H2U188001EHE_********, 变更字段: [{'field': 'amount', 'old_value': 2507.0, 'new_value': 3434.0}, {'field': 'count', 'old_value': 6, 'new_value': 7}, {'field': 'instoreAmount', 'old_value': 2507.0, 'new_value': 3434.0}, {'field': 'instoreCount', 'old_value': 6, 'new_value': 7}]
2025-06-07 08:06:13,430 - INFO - 更新表单数据成功: FINST-T9D66B810AVVP9XIFPYE34WEQSBO31REYKEBM24
2025-06-07 08:06:13,430 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDULBESLOI0I86N3H2U1PE001F2K_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 20739.9}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 20739.9}]
2025-06-07 08:06:13,868 - INFO - 更新表单数据成功: FINST-T9D66B810AVVP9XIFPYE34WEQSBO32REYKEBMB5
2025-06-07 08:06:13,868 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVSS4V3460I86N3H2U12P001EBV_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 3672.91}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 3672.91}]
2025-06-07 08:06:14,352 - INFO - 更新表单数据成功: FINST-AI866781PPXVA2K467VLNAJT99EL20WMYKEBMSK
2025-06-07 08:06:14,352 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 1540.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 1540.0}]
2025-06-07 08:06:14,852 - INFO - 更新表单数据成功: FINST-8LC66GC1EUVVDVBV6H44M8Q4ARX62FHPYKEBM27
2025-06-07 08:06:14,852 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDULBESLOI0I86N3H2U1PE001F2K_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 16842.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 16842.0}]
2025-06-07 08:06:15,337 - INFO - 更新表单数据成功: FINST-8LC66GC1EUVVDVBV6H44M8Q4ARX62FHPYKEBMR7
2025-06-07 08:06:15,337 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_********, 变更字段: [{'field': 'amount', 'old_value': 19821.6, 'new_value': 18084.6}, {'field': 'instoreAmount', 'old_value': 21764.1, 'new_value': 18085.6}]
2025-06-07 08:06:15,790 - INFO - 更新表单数据成功: FINST-1PF66VA1VKUVECLJ8740NCX0EP2Y3Z40ZKEBM3I
2025-06-07 08:06:15,790 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_********, 变更字段: [{'field': 'amount', 'old_value': 5414.3, 'new_value': 4185.5}, {'field': 'instoreAmount', 'old_value': 5414.3, 'new_value': 4185.5}]
2025-06-07 08:06:16,305 - INFO - 更新表单数据成功: FINST-HJ966H81W8UV8S9G7NMHS7QD45DD2SMDZKEBMM
2025-06-07 08:06:16,305 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVSS4V3460I86N3H2U12P001EBV_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 2770.96}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 2770.96}]
2025-06-07 08:06:16,790 - INFO - 更新表单数据成功: FINST-HJ966H81W8UV8S9G7NMHS7QD45DD2TMDZKEBM13
2025-06-07 08:06:16,790 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD2F2MCTBD6AJB6QM8HA650011P3_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 43386.95, 'new_value': 43896.95}, {'field': 'amount', 'old_value': 43386.95, 'new_value': 43896.95}, {'field': 'count', 'old_value': 128, 'new_value': 129}, {'field': 'instoreAmount', 'old_value': 43386.95, 'new_value': 43896.95}, {'field': 'instoreCount', 'old_value': 128, 'new_value': 129}]
2025-06-07 08:06:17,274 - INFO - 更新表单数据成功: FINST-L8D665C1D6SV6WR4EYE7L9A17AWH27PLZKEBMXV
2025-06-07 08:06:17,274 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDULBESLOI0I86N3H2U1PE001F2K_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 15502.6}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 15502.6}]
2025-06-07 08:06:17,696 - INFO - 更新表单数据成功: FINST-8SG66JA1SNUVPZXOFFPP77J8Y9CX2BCOZKEBMU5
2025-06-07 08:06:17,696 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_********, 变更字段: [{'field': 'amount', 'old_value': 6018.700000000001, 'new_value': 4617.6}, {'field': 'instoreAmount', 'old_value': 6678.1, 'new_value': 5277.0}]
2025-06-07 08:06:18,149 - INFO - 更新表单数据成功: FINST-8SG66JA1SNUVPZXOFFPP77J8Y9CX2CCOZKEBMT6
2025-06-07 08:06:18,149 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_********, 变更字段: [{'field': 'amount', 'old_value': 4571.84, 'new_value': 4609.04}, {'field': 'count', 'old_value': 245, 'new_value': 247}, {'field': 'onlineAmount', 'old_value': 3318.92, 'new_value': 3356.12}, {'field': 'onlineCount', 'old_value': 157, 'new_value': 159}]
2025-06-07 08:06:18,649 - INFO - 更新表单数据成功: FINST-3PF66X616BVVT8UG9XAO48AGNFR42B8WZKEBMJD
2025-06-07 08:06:18,649 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERRPAJGP042F6DB81RHB6001P7E_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 2708.3}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 2708.3}]
2025-06-07 08:06:19,118 - INFO - 更新表单数据成功: FINST-COC668A1REVVQ2Y7FIDUY6XN89OF27WYZKEBMGO
2025-06-07 08:06:19,118 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDULBESLOI0I86N3H2U1PE001F2K_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 4090.06}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 4090.06}]
2025-06-07 08:06:19,586 - INFO - 更新表单数据成功: FINST-68E66TC1PNOVFN8G8BQWK6JFZQH525H90LEBMRW1
2025-06-07 08:06:19,586 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDULBESLOI0I86N3H2U1PE001F2K_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 3236.76}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 3236.76}]
2025-06-07 08:06:20,071 - INFO - 更新表单数据成功: FINST-68E66TC1PNOVFN8G8BQWK6JFZQH525H90LEBMZX1
2025-06-07 08:06:20,071 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVSS4V3460I86N3H2U12P001EBV_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 2822.77}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 2822.77}]
2025-06-07 08:06:20,524 - INFO - 更新表单数据成功: FINST-2FD66I71X8VVZ6ZFEIR6JDTAKMJ62BHH0LEBMXL
2025-06-07 08:06:20,524 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERRPAJGP042F6DB81RHB6001P7E_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 4265.0, 'new_value': 4758.34}, {'field': 'dailyBillAmount', 'old_value': 4265.0, 'new_value': 4758.34}]
2025-06-07 08:06:21,024 - INFO - 更新表单数据成功: FINST-X2F66HC1A6SV3H8GEK8K8C26E2O8373K0LEBMIQ
2025-06-07 08:06:21,024 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 4230.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 4230.0}]
2025-06-07 08:06:21,508 - INFO - 更新表单数据成功: FINST-X2F66HC1A6SV3H8GEK8K8C26E2O8383K0LEBMFS
2025-06-07 08:06:21,508 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_********, 变更字段: [{'field': 'amount', 'old_value': 16683.100000000002, 'new_value': 13022.800000000001}, {'field': 'instoreAmount', 'old_value': 17522.4, 'new_value': 13862.1}]
2025-06-07 08:06:21,993 - INFO - 更新表单数据成功: FINST-MLF669B1DCVVELABDZ63O84EN5EL2PE51LEBM7N
2025-06-07 08:06:21,993 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HLFG4AKMT96P82UAQ9ONTBKHO001HHS_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 3542.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 3542.0}]
2025-06-07 08:06:22,493 - INFO - 更新表单数据成功: FINST-NWE664C1UCTVIBSCEHTRWAM5ELCC3SDT1LEBMXO
2025-06-07 08:06:22,493 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_********, 变更字段: [{'field': 'amount', 'old_value': 13559.3, 'new_value': 12435.7}, {'field': 'count', 'old_value': 17, 'new_value': 19}, {'field': 'instoreAmount', 'old_value': 13559.3, 'new_value': 12435.7}, {'field': 'instoreCount', 'old_value': 17, 'new_value': 19}]
2025-06-07 08:06:22,883 - INFO - 更新表单数据成功: FINST-3PF66O71R9VV8NWIATMOQAUPDNL82SD12LEBM0O
2025-06-07 08:06:22,883 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERRPAJGP042F6DB81RHB6001P7E_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 1597.0, 'new_value': 1725.94}, {'field': 'dailyBillAmount', 'old_value': 1597.0, 'new_value': 1725.94}]
2025-06-07 08:06:23,274 - INFO - 更新表单数据成功: FINST-IOC66G71LCYVYDGKCHUXA9AOYGNQ3O0C2LEBMK2
2025-06-07 08:06:23,274 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERRPAJGP042F6DB81RHB6001P7E_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 3087.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 3087.0}]
2025-06-07 08:06:23,758 - INFO - 更新表单数据成功: FINST-737662B1HOUVTCBWDF3FW5NRH8I63ZL23LEBM89
2025-06-07 08:06:23,758 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 20077.44, 'new_value': 20384.46}, {'field': 'amount', 'old_value': 19939.46, 'new_value': 20384.46}, {'field': 'count', 'old_value': 135, 'new_value': 136}, {'field': 'instoreAmount', 'old_value': 19939.46, 'new_value': 20384.46}, {'field': 'instoreCount', 'old_value': 135, 'new_value': 136}]
2025-06-07 08:06:24,227 - INFO - 更新表单数据成功: FINST-737662B1HOUVTCBWDF3FW5NRH8I63ZL23LEBM7B
2025-06-07 08:06:24,227 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_********, 变更字段: [{'field': 'amount', 'old_value': 87306.31, 'new_value': 94039.09}, {'field': 'count', 'old_value': 1457, 'new_value': 1549}, {'field': 'instoreAmount', 'old_value': 83521.52, 'new_value': 90254.3}, {'field': 'instoreCount', 'old_value': 1391, 'new_value': 1483}]
2025-06-07 08:06:24,743 - INFO - 更新表单数据成功: FINST-ZNE66RC1JPZVOXWFDLZXPC9G5LFM24LQY6HBMYB
2025-06-07 08:06:24,743 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 8702.24, 'new_value': 8750.15}, {'field': 'amount', 'old_value': 8702.24, 'new_value': 8750.15}, {'field': 'count', 'old_value': 158, 'new_value': 160}, {'field': 'instoreAmount', 'old_value': 8121.6, 'new_value': 8169.51}, {'field': 'instoreCount', 'old_value': 140, 'new_value': 142}]
2025-06-07 08:06:25,196 - INFO - 更新表单数据成功: FINST-ZNE66RC1JPZVOXWFDLZXPC9G5LFM24LQY6HBMCC
2025-06-07 08:06:25,211 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAIN5DMKK0I86N3H2U1VH001F8N_********, 变更字段: [{'field': 'amount', 'old_value': -3821.9, 'new_value': -3768.0}, {'field': 'count', 'old_value': 12, 'new_value': 13}, {'field': 'onlineAmount', 'old_value': 208.1, 'new_value': 262.0}, {'field': 'onlineCount', 'old_value': 11, 'new_value': 12}]
2025-06-07 08:06:25,649 - INFO - 更新表单数据成功: FINST-EEC66XC1QPZVJTZS9X6WV5IF7RKB395TY6HBMBA
2025-06-07 08:06:25,649 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 37841.55}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 37841.55}]
2025-06-07 08:06:26,102 - INFO - 更新表单数据成功: FINST-S0E660A1S82WPO3D9JE576VAE5OC2ZMSV1KBMV4
2025-06-07 08:06:26,102 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6692283A183D432BAE322E1032539CE8_********, 变更字段: [{'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 7617.0}]
2025-06-07 08:06:26,508 - INFO - 更新表单数据成功: FINST-S0E660A1S82WPO3D9JE576VAE5OC2ZMSV1KBMA5
2025-06-07 08:06:26,508 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE1H27HPQN7Q2OV4FVC7EO0014B9_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 3183.0}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 3183.0}]
2025-06-07 08:06:26,930 - INFO - 更新表单数据成功: FINST-S0E660A1S82WPO3D9JE576VAE5OC2ZMSV1KBMU5
2025-06-07 08:06:26,930 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 4101.27}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 4101.27}]
2025-06-07 08:06:27,477 - INFO - 更新表单数据成功: FINST-S0E660A1S82WPO3D9JE576VAE5OC2ZMSV1KBMX5
2025-06-07 08:06:27,477 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_********, 变更字段: [{'field': 'amount', 'old_value': 732.0, 'new_value': 4180.0}, {'field': 'count', 'old_value': 3, 'new_value': 4}, {'field': 'instoreAmount', 'old_value': 732.0, 'new_value': 4180.0}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 4}]
2025-06-07 08:06:27,946 - INFO - 更新表单数据成功: FINST-S0E660A1S82WPO3D9JE576VAE5OC2ZMSV1KBMZ5
2025-06-07 08:06:27,946 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTAKS3N4EI7Q2OVBN4IS76001D38_********, 变更字段: [{'field': 'amount', 'old_value': 9341.0, 'new_value': 32700.0}, {'field': 'count', 'old_value': 6, 'new_value': 7}, {'field': 'instoreAmount', 'old_value': 9341.0, 'new_value': 32700.0}, {'field': 'instoreCount', 'old_value': 6, 'new_value': 7}]
2025-06-07 08:06:28,415 - INFO - 更新表单数据成功: FINST-XMC66R91I82WALUIE6PUR6H5O84829CVV1KBMO2
2025-06-07 08:06:28,415 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 9230.8}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 9230.8}]
2025-06-07 08:06:28,821 - INFO - 更新表单数据成功: FINST-XMC66R91I82WALUIE6PUR6H5O84829CVV1KBMS2
2025-06-07 08:06:28,821 - INFO - 更新日销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_********, 变更字段: [{'field': 'amount', 'old_value': 1441.0, 'new_value': 5130.0}, {'field': 'count', 'old_value': 3, 'new_value': 12}, {'field': 'instoreAmount', 'old_value': 1441.0, 'new_value': 5130.0}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 12}]
2025-06-07 08:06:29,258 - INFO - 更新表单数据成功: FINST-XMC66R91I82WALUIE6PUR6H5O8482ACVV1KBM33
2025-06-07 08:06:29,258 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2253.4, 'new_value': 2275.41}, {'field': 'amount', 'old_value': 2253.4, 'new_value': 2275.4100000000003}, {'field': 'count', 'old_value': 132, 'new_value': 134}, {'field': 'onlineAmount', 'old_value': 1775.7, 'new_value': 1797.71}, {'field': 'onlineCount', 'old_value': 105, 'new_value': 107}]
2025-06-07 08:06:29,758 - INFO - 更新表单数据成功: FINST-XMC66R91I82WALUIE6PUR6H5O8482ACVV1KBM73
2025-06-07 08:06:29,758 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 2034.31, 'new_value': 2045.31}, {'field': 'amount', 'old_value': 2034.3100000000002, 'new_value': 2045.3100000000002}, {'field': 'instoreAmount', 'old_value': 2051.55, 'new_value': 2062.55}]
2025-06-07 08:06:30,196 - INFO - 更新表单数据成功: FINST-XMC66R91I82WALUIE6PUR6H5O8482ACVV1KBME3
2025-06-07 08:06:30,196 - INFO - 更新日销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 9116.24, 'new_value': 9431.24}, {'field': 'amount', 'old_value': 9116.24, 'new_value': 9431.24}, {'field': 'count', 'old_value': 178, 'new_value': 182}, {'field': 'instoreAmount', 'old_value': 8419.21, 'new_value': 8734.21}, {'field': 'instoreCount', 'old_value': 164, 'new_value': 168}]
2025-06-07 08:06:30,633 - INFO - 更新表单数据成功: FINST-XMC66R91I82WALUIE6PUR6H5O8482ACVV1KBMM3
2025-06-07 08:06:30,633 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 5986.81, 'new_value': 6067.41}, {'field': 'amount', 'old_value': 5986.81, 'new_value': 6067.41}, {'field': 'count', 'old_value': 248, 'new_value': 252}, {'field': 'onlineAmount', 'old_value': 4859.11, 'new_value': 4939.71}, {'field': 'onlineCount', 'old_value': 192, 'new_value': 196}]
2025-06-07 08:06:31,055 - INFO - 更新表单数据成功: FINST-XMC66R91I82WALUIE6PUR6H5O8482ACVV1KBMN3
2025-06-07 08:06:31,055 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_********, 变更字段: [{'field': 'amount', 'old_value': 2151.71, 'new_value': 2178.01}, {'field': 'count', 'old_value': 66, 'new_value': 67}, {'field': 'instoreAmount', 'old_value': 521.0, 'new_value': 547.3}, {'field': 'instoreCount', 'old_value': 7, 'new_value': 8}]
2025-06-07 08:06:31,477 - INFO - 更新表单数据成功: FINST-XMC66R91I82WALUIE6PUR6H5O8482ACVV1KBMQ3
2025-06-07 08:06:31,477 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 19210.71}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 19210.71}]
2025-06-07 08:06:31,993 - INFO - 更新表单数据成功: FINST-XMC66R91I82WALUIE6PUR6H5O8482ACVV1KBMR3
2025-06-07 08:06:31,993 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAIN5DMKK0I86N3H2U1VH001F8N_********, 变更字段: [{'field': 'amount', 'old_value': -3713.1, 'new_value': -3708.5}, {'field': 'count', 'old_value': 20, 'new_value': 21}, {'field': 'onlineAmount', 'old_value': 338.9, 'new_value': 343.5}, {'field': 'onlineCount', 'old_value': 16, 'new_value': 17}]
2025-06-07 08:06:32,508 - INFO - 更新表单数据成功: FINST-XMC66R91I82WALUIE6PUR6H5O8482ACVV1KBMT3
2025-06-07 08:06:32,508 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEA4041D6F0I86N3H2U1V9001F8F_********, 变更字段: [{'field': 'amount', 'old_value': 14370.68, 'new_value': 14680.68}, {'field': 'count', 'old_value': 154, 'new_value': 155}, {'field': 'instoreAmount', 'old_value': 12275.89, 'new_value': 12585.89}, {'field': 'instoreCount', 'old_value': 81, 'new_value': 82}]
2025-06-07 08:06:32,930 - INFO - 更新表单数据成功: FINST-XMC66R91I82WALUIE6PUR6H5O8482ACVV1KBMU3
2025-06-07 08:06:32,930 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 9713.35}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 9713.35}]
2025-06-07 08:06:33,399 - INFO - 更新表单数据成功: FINST-XMC66R91I82WALUIE6PUR6H5O8482ACVV1KBMV3
2025-06-07 08:06:33,399 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_********, 变更字段: [{'field': 'amount', 'old_value': 21888.65, 'new_value': 22516.65}, {'field': 'count', 'old_value': 178, 'new_value': 181}, {'field': 'instoreAmount', 'old_value': 7790.5, 'new_value': 8418.5}, {'field': 'instoreCount', 'old_value': 48, 'new_value': 51}]
2025-06-07 08:06:33,790 - INFO - 更新表单数据成功: FINST-XMC66R91I82WALUIE6PUR6H5O8482ACVV1KBM44
2025-06-07 08:06:33,790 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_********, 变更字段: [{'field': 'amount', 'old_value': 2769.31, 'new_value': 2776.31}, {'field': 'count', 'old_value': 163, 'new_value': 164}, {'field': 'instoreAmount', 'old_value': 1233.01, 'new_value': 1240.01}, {'field': 'instoreCount', 'old_value': 83, 'new_value': 84}]
2025-06-07 08:06:34,258 - INFO - 更新表单数据成功: FINST-XMC66R91I82WALUIE6PUR6H5O8482ACVV1KBM54
2025-06-07 08:06:34,258 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_********, 变更字段: [{'field': 'amount', 'old_value': 4665.84, 'new_value': 4695.94}, {'field': 'count', 'old_value': 221, 'new_value': 223}, {'field': 'onlineAmount', 'old_value': 3453.39, 'new_value': 3483.49}, {'field': 'onlineCount', 'old_value': 152, 'new_value': 154}]
2025-06-07 08:06:34,743 - INFO - 更新表单数据成功: FINST-XMC66R91I82WALUIE6PUR6H5O8482ACVV1KBMD4
2025-06-07 08:06:34,743 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJP082LC00I86N3H2U1JM001ESS_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 117.6, 'new_value': 546.4}, {'field': 'amount', 'old_value': 117.6, 'new_value': 546.4}, {'field': 'count', 'old_value': 1, 'new_value': 2}, {'field': 'instoreAmount', 'old_value': 117.6, 'new_value': 546.4}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 2}]
2025-06-07 08:06:35,196 - INFO - 更新表单数据成功: FINST-XMC66R91I82WALUIE6PUR6H5O8482BCVV1KBML4
2025-06-07 08:06:35,196 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEQ2M9E710I86N3H2U1H1001EQ7_********, 变更字段: [{'field': 'amount', 'old_value': 2765.5, 'new_value': 2862.13}, {'field': 'count', 'old_value': 24, 'new_value': 25}, {'field': 'onlineAmount', 'old_value': 556.4, 'new_value': 653.03}, {'field': 'onlineCount', 'old_value': 3, 'new_value': 4}]
2025-06-07 08:06:35,571 - INFO - 更新表单数据成功: FINST-XMC66R91I82WALUIE6PUR6H5O8482BCVV1KBMN4
2025-06-07 08:06:35,571 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDCE3748SO0I86N3H2U1FP001EOV_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 360.0, 'new_value': 648.0}, {'field': 'amount', 'old_value': 360.0, 'new_value': 648.0}, {'field': 'count', 'old_value': 1, 'new_value': 2}, {'field': 'instoreAmount', 'old_value': 360.0, 'new_value': 648.0}, {'field': 'instoreCount', 'old_value': 1, 'new_value': 2}]
2025-06-07 08:06:36,024 - INFO - 更新表单数据成功: FINST-XMC66R91I82WALUIE6PUR6H5O8482BCVV1KBMZ4
2025-06-07 08:06:36,024 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_********, 变更字段: [{'field': 'amount', 'old_value': 4035.8900000000003, 'new_value': 3970.9900000000002}]
2025-06-07 08:06:36,493 - INFO - 更新表单数据成功: FINST-XMC66R91I82WALUIE6PUR6H5O8482BCVV1KBM05
2025-06-07 08:06:36,493 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_********, 变更字段: [{'field': 'amount', 'old_value': 3404.22, 'new_value': 3511.83}, {'field': 'count', 'old_value': 240, 'new_value': 259}, {'field': 'onlineAmount', 'old_value': 3249.52, 'new_value': 3357.13}, {'field': 'onlineCount', 'old_value': 227, 'new_value': 246}]
2025-06-07 08:06:37,008 - INFO - 更新表单数据成功: FINST-XMC66R91I82WALUIE6PUR6H5O8482BCVV1KBM65
2025-06-07 08:06:37,008 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQQG9THS10I86N3H2U108001E9E_********, 变更字段: [{'field': 'amount', 'old_value': 4574.650000000001, 'new_value': 4899.150000000001}, {'field': 'count', 'old_value': 167, 'new_value': 168}, {'field': 'instoreAmount', 'old_value': 4648.35, 'new_value': 4972.85}, {'field': 'instoreCount', 'old_value': 167, 'new_value': 168}]
2025-06-07 08:06:37,477 - INFO - 更新表单数据成功: FINST-XMC66R91I82WALUIE6PUR6H5O8482BCVV1KBM75
2025-06-07 08:06:37,477 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_********, 变更字段: [{'field': 'amount', 'old_value': 4650.38, 'new_value': 4656.25}, {'field': 'count', 'old_value': 338, 'new_value': 342}, {'field': 'instoreAmount', 'old_value': 3298.73, 'new_value': 3309.8}, {'field': 'instoreCount', 'old_value': 231, 'new_value': 236}, {'field': 'onlineAmount', 'old_value': 1422.4, 'new_value': 1417.2}, {'field': 'onlineCount', 'old_value': 107, 'new_value': 106}]
2025-06-07 08:06:37,914 - INFO - 更新表单数据成功: FINST-XMC66R91I82WALUIE6PUR6H5O8482BCVV1KBMC5
2025-06-07 08:06:37,914 - INFO - 更新日销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_********, 变更字段: [{'field': 'amount', 'old_value': 3009.87, 'new_value': 3052.77}, {'field': 'count', 'old_value': 71, 'new_value': 72}, {'field': 'instoreAmount', 'old_value': 2435.6, 'new_value': 2478.5}, {'field': 'instoreCount', 'old_value': 53, 'new_value': 54}]
2025-06-07 08:06:38,352 - INFO - 更新表单数据成功: FINST-RN766181M82WVPCBDE39H4UX2E9C3DWXV1KBMX
2025-06-07 08:06:38,352 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 5246.46, 'new_value': 5285.55}, {'field': 'amount', 'old_value': 5246.46, 'new_value': 5285.55}, {'field': 'count', 'old_value': 308, 'new_value': 311}, {'field': 'instoreAmount', 'old_value': 2563.93, 'new_value': 2595.5}, {'field': 'instoreCount', 'old_value': 153, 'new_value': 155}, {'field': 'onlineAmount', 'old_value': 2682.53, 'new_value': 2690.05}, {'field': 'onlineCount', 'old_value': 155, 'new_value': 156}]
2025-06-07 08:06:38,821 - INFO - 更新表单数据成功: FINST-RN766181M82WVPCBDE39H4UX2E9C3DWXV1KBMY
2025-06-07 08:06:38,821 - INFO - 更新日销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_********, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 3779.48}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 3779.48}, {'field': 'amount', 'old_value': 1374.46, 'new_value': 1396.26}, {'field': 'count', 'old_value': 33, 'new_value': 34}, {'field': 'instoreAmount', 'old_value': 904.82, 'new_value': 926.62}, {'field': 'instoreCount', 'old_value': 26, 'new_value': 27}]
2025-06-07 08:06:39,368 - INFO - 更新表单数据成功: FINST-RN766181M82WVPCBDE39H4UX2E9C3DWXV1KBM51
2025-06-07 08:06:39,368 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK_********, 变更字段: [{'field': 'count', 'old_value': 59, 'new_value': 60}, {'field': 'instoreCount', 'old_value': 47, 'new_value': 48}]
2025-06-07 08:06:39,821 - INFO - 更新表单数据成功: FINST-RN766181M82WVPCBDE39H4UX2E9C3DWXV1KBM71
2025-06-07 08:06:39,821 - INFO - 更新日销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_********, 变更字段: [{'field': 'amount', 'old_value': 25953.73, 'new_value': 30461.670000000002}, {'field': 'count', 'old_value': 117, 'new_value': 119}, {'field': 'instoreAmount', 'old_value': 25831.16, 'new_value': 30339.1}, {'field': 'instoreCount', 'old_value': 67, 'new_value': 69}]
2025-06-07 08:06:40,305 - INFO - 更新表单数据成功: FINST-RN766181M82WVPCBDE39H4UX2E9C3DWXV1KBMF1
2025-06-07 08:06:40,305 - INFO - 更新日销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_********, 变更字段: [{'field': 'amount', 'old_value': 25442.1, 'new_value': 26018.1}, {'field': 'count', 'old_value': 129, 'new_value': 130}, {'field': 'instoreAmount', 'old_value': 22373.6, 'new_value': 22949.6}, {'field': 'instoreCount', 'old_value': 102, 'new_value': 103}]
2025-06-07 08:06:40,446 - INFO - 正在批量插入每日数据，批次 1/66，共 100 条记录
2025-06-07 08:06:40,961 - INFO - 批量插入每日数据成功，批次 1，100 条记录
2025-06-07 08:06:43,977 - INFO - 正在批量插入每日数据，批次 2/66，共 100 条记录
2025-06-07 08:06:44,493 - INFO - 批量插入每日数据成功，批次 2，100 条记录
2025-06-07 08:06:47,508 - INFO - 正在批量插入每日数据，批次 3/66，共 100 条记录
2025-06-07 08:06:47,961 - INFO - 批量插入每日数据成功，批次 3，100 条记录
2025-06-07 08:06:50,961 - INFO - 正在批量插入每日数据，批次 4/66，共 100 条记录
2025-06-07 08:06:51,399 - INFO - 批量插入每日数据成功，批次 4，100 条记录
2025-06-07 08:06:54,414 - INFO - 正在批量插入每日数据，批次 5/66，共 100 条记录
2025-06-07 08:06:54,883 - INFO - 批量插入每日数据成功，批次 5，100 条记录
2025-06-07 08:06:57,883 - INFO - 正在批量插入每日数据，批次 6/66，共 100 条记录
2025-06-07 08:06:58,383 - INFO - 批量插入每日数据成功，批次 6，100 条记录
2025-06-07 08:07:01,399 - INFO - 正在批量插入每日数据，批次 7/66，共 100 条记录
2025-06-07 08:07:01,867 - INFO - 批量插入每日数据成功，批次 7，100 条记录
2025-06-07 08:07:04,883 - INFO - 正在批量插入每日数据，批次 8/66，共 100 条记录
2025-06-07 08:07:05,258 - INFO - 批量插入每日数据成功，批次 8，100 条记录
2025-06-07 08:07:08,274 - INFO - 正在批量插入每日数据，批次 9/66，共 100 条记录
2025-06-07 08:07:08,774 - INFO - 批量插入每日数据成功，批次 9，100 条记录
2025-06-07 08:07:11,789 - INFO - 正在批量插入每日数据，批次 10/66，共 100 条记录
2025-06-07 08:07:12,133 - INFO - 批量插入每日数据成功，批次 10，100 条记录
2025-06-07 08:07:15,149 - INFO - 正在批量插入每日数据，批次 11/66，共 100 条记录
2025-06-07 08:07:15,539 - INFO - 批量插入每日数据成功，批次 11，100 条记录
2025-06-07 08:07:18,555 - INFO - 正在批量插入每日数据，批次 12/66，共 100 条记录
2025-06-07 08:07:18,977 - INFO - 批量插入每日数据成功，批次 12，100 条记录
2025-06-07 08:07:21,992 - INFO - 正在批量插入每日数据，批次 13/66，共 100 条记录
2025-06-07 08:07:22,461 - INFO - 批量插入每日数据成功，批次 13，100 条记录
2025-06-07 08:07:25,477 - INFO - 正在批量插入每日数据，批次 14/66，共 100 条记录
2025-06-07 08:07:25,945 - INFO - 批量插入每日数据成功，批次 14，100 条记录
2025-06-07 08:07:28,961 - INFO - 正在批量插入每日数据，批次 15/66，共 100 条记录
2025-06-07 08:07:29,367 - INFO - 批量插入每日数据成功，批次 15，100 条记录
2025-06-07 08:07:32,383 - INFO - 正在批量插入每日数据，批次 16/66，共 100 条记录
2025-06-07 08:07:32,789 - INFO - 批量插入每日数据成功，批次 16，100 条记录
2025-06-07 08:07:35,805 - INFO - 正在批量插入每日数据，批次 17/66，共 100 条记录
2025-06-07 08:07:36,273 - INFO - 批量插入每日数据成功，批次 17，100 条记录
2025-06-07 08:07:39,289 - INFO - 正在批量插入每日数据，批次 18/66，共 100 条记录
2025-06-07 08:07:39,820 - INFO - 批量插入每日数据成功，批次 18，100 条记录
2025-06-07 08:07:42,836 - INFO - 正在批量插入每日数据，批次 19/66，共 100 条记录
2025-06-07 08:07:43,305 - INFO - 批量插入每日数据成功，批次 19，100 条记录
2025-06-07 08:07:46,320 - INFO - 正在批量插入每日数据，批次 20/66，共 100 条记录
2025-06-07 08:07:46,758 - INFO - 批量插入每日数据成功，批次 20，100 条记录
2025-06-07 08:07:49,773 - INFO - 正在批量插入每日数据，批次 21/66，共 100 条记录
2025-06-07 08:07:50,289 - INFO - 批量插入每日数据成功，批次 21，100 条记录
2025-06-07 08:07:53,289 - INFO - 正在批量插入每日数据，批次 22/66，共 100 条记录
2025-06-07 08:07:53,711 - INFO - 批量插入每日数据成功，批次 22，100 条记录
2025-06-07 08:07:56,726 - INFO - 正在批量插入每日数据，批次 23/66，共 100 条记录
2025-06-07 08:07:57,101 - INFO - 批量插入每日数据成功，批次 23，100 条记录
2025-06-07 08:08:00,133 - INFO - 正在批量插入每日数据，批次 24/66，共 100 条记录
2025-06-07 08:08:00,508 - INFO - 批量插入每日数据成功，批次 24，100 条记录
2025-06-07 08:08:03,523 - INFO - 正在批量插入每日数据，批次 25/66，共 100 条记录
2025-06-07 08:08:04,008 - INFO - 批量插入每日数据成功，批次 25，100 条记录
2025-06-07 08:08:07,023 - INFO - 正在批量插入每日数据，批次 26/66，共 100 条记录
2025-06-07 08:08:07,398 - INFO - 批量插入每日数据成功，批次 26，100 条记录
2025-06-07 08:08:10,414 - INFO - 正在批量插入每日数据，批次 27/66，共 100 条记录
2025-06-07 08:08:10,789 - INFO - 批量插入每日数据成功，批次 27，100 条记录
2025-06-07 08:08:13,804 - INFO - 正在批量插入每日数据，批次 28/66，共 100 条记录
2025-06-07 08:08:14,226 - INFO - 批量插入每日数据成功，批次 28，100 条记录
2025-06-07 08:08:17,242 - INFO - 正在批量插入每日数据，批次 29/66，共 100 条记录
2025-06-07 08:08:17,758 - INFO - 批量插入每日数据成功，批次 29，100 条记录
2025-06-07 08:08:20,773 - INFO - 正在批量插入每日数据，批次 30/66，共 100 条记录
2025-06-07 08:08:21,273 - INFO - 批量插入每日数据成功，批次 30，100 条记录
2025-06-07 08:08:24,289 - INFO - 正在批量插入每日数据，批次 31/66，共 100 条记录
2025-06-07 08:08:24,711 - INFO - 批量插入每日数据成功，批次 31，100 条记录
2025-06-07 08:08:27,726 - INFO - 正在批量插入每日数据，批次 32/66，共 100 条记录
2025-06-07 08:08:28,242 - INFO - 批量插入每日数据成功，批次 32，100 条记录
2025-06-07 08:08:31,257 - INFO - 正在批量插入每日数据，批次 33/66，共 100 条记录
2025-06-07 08:08:31,695 - INFO - 批量插入每日数据成功，批次 33，100 条记录
2025-06-07 08:08:34,711 - INFO - 正在批量插入每日数据，批次 34/66，共 100 条记录
2025-06-07 08:08:35,179 - INFO - 批量插入每日数据成功，批次 34，100 条记录
2025-06-07 08:08:38,195 - INFO - 正在批量插入每日数据，批次 35/66，共 100 条记录
2025-06-07 08:08:38,679 - INFO - 批量插入每日数据成功，批次 35，100 条记录
2025-06-07 08:08:41,695 - INFO - 正在批量插入每日数据，批次 36/66，共 100 条记录
2025-06-07 08:08:42,132 - INFO - 批量插入每日数据成功，批次 36，100 条记录
2025-06-07 08:08:45,148 - INFO - 正在批量插入每日数据，批次 37/66，共 100 条记录
2025-06-07 08:08:45,617 - INFO - 批量插入每日数据成功，批次 37，100 条记录
2025-06-07 08:08:48,632 - INFO - 正在批量插入每日数据，批次 38/66，共 100 条记录
2025-06-07 08:08:49,101 - INFO - 批量插入每日数据成功，批次 38，100 条记录
2025-06-07 08:08:52,117 - INFO - 正在批量插入每日数据，批次 39/66，共 100 条记录
2025-06-07 08:08:52,648 - INFO - 批量插入每日数据成功，批次 39，100 条记录
2025-06-07 08:08:55,664 - INFO - 正在批量插入每日数据，批次 40/66，共 100 条记录
2025-06-07 08:08:56,117 - INFO - 批量插入每日数据成功，批次 40，100 条记录
2025-06-07 08:08:59,132 - INFO - 正在批量插入每日数据，批次 41/66，共 100 条记录
2025-06-07 08:08:59,554 - INFO - 批量插入每日数据成功，批次 41，100 条记录
2025-06-07 08:09:02,570 - INFO - 正在批量插入每日数据，批次 42/66，共 100 条记录
2025-06-07 08:09:03,023 - INFO - 批量插入每日数据成功，批次 42，100 条记录
2025-06-07 08:09:06,039 - INFO - 正在批量插入每日数据，批次 43/66，共 100 条记录
2025-06-07 08:09:06,460 - INFO - 批量插入每日数据成功，批次 43，100 条记录
2025-06-07 08:09:09,476 - INFO - 正在批量插入每日数据，批次 44/66，共 100 条记录
2025-06-07 08:09:09,992 - INFO - 批量插入每日数据成功，批次 44，100 条记录
2025-06-07 08:09:13,007 - INFO - 正在批量插入每日数据，批次 45/66，共 100 条记录
2025-06-07 08:09:13,460 - INFO - 批量插入每日数据成功，批次 45，100 条记录
2025-06-07 08:09:16,476 - INFO - 正在批量插入每日数据，批次 46/66，共 100 条记录
2025-06-07 08:09:16,851 - INFO - 批量插入每日数据成功，批次 46，100 条记录
2025-06-07 08:09:19,867 - INFO - 正在批量插入每日数据，批次 47/66，共 100 条记录
2025-06-07 08:09:20,320 - INFO - 批量插入每日数据成功，批次 47，100 条记录
2025-06-07 08:09:23,335 - INFO - 正在批量插入每日数据，批次 48/66，共 100 条记录
2025-06-07 08:09:23,710 - INFO - 批量插入每日数据成功，批次 48，100 条记录
2025-06-07 08:09:26,726 - INFO - 正在批量插入每日数据，批次 49/66，共 100 条记录
2025-06-07 08:09:27,179 - INFO - 批量插入每日数据成功，批次 49，100 条记录
2025-06-07 08:09:30,195 - INFO - 正在批量插入每日数据，批次 50/66，共 100 条记录
2025-06-07 08:09:30,632 - INFO - 批量插入每日数据成功，批次 50，100 条记录
2025-06-07 08:09:33,648 - INFO - 正在批量插入每日数据，批次 51/66，共 100 条记录
2025-06-07 08:09:34,038 - INFO - 批量插入每日数据成功，批次 51，100 条记录
2025-06-07 08:09:37,054 - INFO - 正在批量插入每日数据，批次 52/66，共 100 条记录
2025-06-07 08:09:37,413 - INFO - 批量插入每日数据成功，批次 52，100 条记录
2025-06-07 08:09:40,429 - INFO - 正在批量插入每日数据，批次 53/66，共 100 条记录
2025-06-07 08:09:40,820 - INFO - 批量插入每日数据成功，批次 53，100 条记录
2025-06-07 08:09:43,835 - INFO - 正在批量插入每日数据，批次 54/66，共 100 条记录
2025-06-07 08:09:44,288 - INFO - 批量插入每日数据成功，批次 54，100 条记录
2025-06-07 08:09:47,304 - INFO - 正在批量插入每日数据，批次 55/66，共 100 条记录
2025-06-07 08:09:47,694 - INFO - 批量插入每日数据成功，批次 55，100 条记录
2025-06-07 08:09:50,710 - INFO - 正在批量插入每日数据，批次 56/66，共 100 条记录
2025-06-07 08:09:51,179 - INFO - 批量插入每日数据成功，批次 56，100 条记录
2025-06-07 08:09:54,194 - INFO - 正在批量插入每日数据，批次 57/66，共 100 条记录
2025-06-07 08:09:54,616 - INFO - 批量插入每日数据成功，批次 57，100 条记录
2025-06-07 08:09:57,632 - INFO - 正在批量插入每日数据，批次 58/66，共 100 条记录
2025-06-07 08:09:58,101 - INFO - 批量插入每日数据成功，批次 58，100 条记录
2025-06-07 08:10:01,116 - INFO - 正在批量插入每日数据，批次 59/66，共 100 条记录
2025-06-07 08:10:01,569 - INFO - 批量插入每日数据成功，批次 59，100 条记录
2025-06-07 08:10:04,585 - INFO - 正在批量插入每日数据，批次 60/66，共 100 条记录
2025-06-07 08:10:05,116 - INFO - 批量插入每日数据成功，批次 60，100 条记录
2025-06-07 08:10:08,132 - INFO - 正在批量插入每日数据，批次 61/66，共 100 条记录
2025-06-07 08:10:08,585 - INFO - 批量插入每日数据成功，批次 61，100 条记录
2025-06-07 08:10:11,601 - INFO - 正在批量插入每日数据，批次 62/66，共 100 条记录
2025-06-07 08:10:12,007 - INFO - 批量插入每日数据成功，批次 62，100 条记录
2025-06-07 08:10:15,022 - INFO - 正在批量插入每日数据，批次 63/66，共 100 条记录
2025-06-07 08:10:15,491 - INFO - 批量插入每日数据成功，批次 63，100 条记录
2025-06-07 08:10:18,507 - INFO - 正在批量插入每日数据，批次 64/66，共 100 条记录
2025-06-07 08:10:18,897 - INFO - 批量插入每日数据成功，批次 64，100 条记录
2025-06-07 08:10:21,913 - INFO - 正在批量插入每日数据，批次 65/66，共 100 条记录
2025-06-07 08:10:22,366 - INFO - 批量插入每日数据成功，批次 65，100 条记录
2025-06-07 08:10:25,382 - INFO - 正在批量插入每日数据，批次 66/66，共 35 条记录
2025-06-07 08:10:25,632 - INFO - 批量插入每日数据成功，批次 66，35 条记录
2025-06-07 08:10:28,647 - INFO - 批量插入每日数据完成: 总计 6535 条，成功 6535 条，失败 0 条
2025-06-07 08:10:28,647 - INFO - 批量插入日销售数据完成，共 6535 条记录
2025-06-07 08:10:28,647 - INFO - 日销售数据同步完成！更新: 60 条，插入: 6535 条，错误: 0 条，跳过: 6271 条
2025-06-07 08:10:28,647 - INFO - 正在获取宜搭月销售表单数据...
2025-06-07 08:10:28,647 - INFO - 开始获取宜搭月度表单数据，总时间范围: 2024-06-01 至 2025-06-30
2025-06-07 08:10:28,647 - INFO - 查询月度分段 1: 2024-06-01 至 2024-08-31
2025-06-07 08:10:28,647 - INFO - 查询日期范围: 2024-06-01 至 2024-08-31，使用分页查询，每页 100 条记录
2025-06-07 08:10:28,647 - INFO - Request Parameters - Page 1:
2025-06-07 08:10:28,647 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:10:28,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1717171200000, 1725033600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:10:29,132 - INFO - API请求耗时: 484ms
2025-06-07 08:10:29,132 - INFO - Response - Page 1
2025-06-07 08:10:29,132 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-07 08:10:29,132 - INFO - 查询完成，共获取到 0 条记录
2025-06-07 08:10:29,132 - WARNING - 月度分段 1 查询返回空数据
2025-06-07 08:10:29,132 - INFO - 尝试将月度分段 1 再细分为单月查询
2025-06-07 08:10:29,132 - INFO - 查询日期范围: 2024-06-01 至 2024-06-30，使用分页查询，每页 100 条记录
2025-06-07 08:10:29,132 - INFO - Request Parameters - Page 1:
2025-06-07 08:10:29,132 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:10:29,132 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1717171200000, 1719676800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:10:29,632 - INFO - API请求耗时: 500ms
2025-06-07 08:10:29,632 - INFO - Response - Page 1
2025-06-07 08:10:29,632 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-07 08:10:29,632 - INFO - 查询完成，共获取到 0 条记录
2025-06-07 08:10:29,632 - WARNING - 单月查询返回空数据: 2024-06
2025-06-07 08:10:30,147 - INFO - 查询日期范围: 2024-07-01 至 2024-07-31，使用分页查询，每页 100 条记录
2025-06-07 08:10:30,147 - INFO - Request Parameters - Page 1:
2025-06-07 08:10:30,147 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:10:30,147 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1719763200000, 1722355200000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:10:30,350 - INFO - API请求耗时: 203ms
2025-06-07 08:10:30,350 - INFO - Response - Page 1
2025-06-07 08:10:30,350 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-07 08:10:30,350 - INFO - 查询完成，共获取到 0 条记录
2025-06-07 08:10:30,350 - WARNING - 单月查询返回空数据: 2024-07
2025-06-07 08:10:30,866 - INFO - 查询日期范围: 2024-08-01 至 2024-08-31，使用分页查询，每页 100 条记录
2025-06-07 08:10:30,866 - INFO - Request Parameters - Page 1:
2025-06-07 08:10:30,866 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:10:30,866 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1722441600000, 1725033600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:10:31,069 - INFO - API请求耗时: 203ms
2025-06-07 08:10:31,069 - INFO - Response - Page 1
2025-06-07 08:10:31,069 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-07 08:10:31,069 - INFO - 查询完成，共获取到 0 条记录
2025-06-07 08:10:31,069 - WARNING - 单月查询返回空数据: 2024-08
2025-06-07 08:10:32,600 - INFO - 查询月度分段 2: 2024-09-01 至 2024-11-30
2025-06-07 08:10:32,600 - INFO - 查询日期范围: 2024-09-01 至 2024-11-30，使用分页查询，每页 100 条记录
2025-06-07 08:10:32,600 - INFO - Request Parameters - Page 1:
2025-06-07 08:10:32,600 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:10:32,600 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1725120000000, 1732896000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:10:32,804 - INFO - API请求耗时: 203ms
2025-06-07 08:10:32,804 - INFO - Response - Page 1
2025-06-07 08:10:32,804 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-07 08:10:32,804 - INFO - 查询完成，共获取到 0 条记录
2025-06-07 08:10:32,804 - WARNING - 月度分段 2 查询返回空数据
2025-06-07 08:10:32,804 - INFO - 尝试将月度分段 2 再细分为单月查询
2025-06-07 08:10:32,804 - INFO - 查询日期范围: 2024-09-01 至 2024-09-30，使用分页查询，每页 100 条记录
2025-06-07 08:10:32,804 - INFO - Request Parameters - Page 1:
2025-06-07 08:10:32,804 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:10:32,804 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1725120000000, 1727625600000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:10:32,991 - INFO - API请求耗时: 188ms
2025-06-07 08:10:32,991 - INFO - Response - Page 1
2025-06-07 08:10:32,991 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-07 08:10:32,991 - INFO - 查询完成，共获取到 0 条记录
2025-06-07 08:10:32,991 - WARNING - 单月查询返回空数据: 2024-09
2025-06-07 08:10:33,507 - INFO - 查询日期范围: 2024-10-01 至 2024-10-31，使用分页查询，每页 100 条记录
2025-06-07 08:10:33,507 - INFO - Request Parameters - Page 1:
2025-06-07 08:10:33,507 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:10:33,507 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1727712000000, 1730304000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:10:33,710 - INFO - API请求耗时: 203ms
2025-06-07 08:10:33,710 - INFO - Response - Page 1
2025-06-07 08:10:33,710 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-07 08:10:33,710 - INFO - 查询完成，共获取到 0 条记录
2025-06-07 08:10:33,710 - WARNING - 单月查询返回空数据: 2024-10
2025-06-07 08:10:34,225 - INFO - 查询日期范围: 2024-11-01 至 2024-11-30，使用分页查询，每页 100 条记录
2025-06-07 08:10:34,225 - INFO - Request Parameters - Page 1:
2025-06-07 08:10:34,225 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:10:34,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1730390400000, 1732896000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:10:34,444 - INFO - API请求耗时: 219ms
2025-06-07 08:10:34,444 - INFO - Response - Page 1
2025-06-07 08:10:34,444 - INFO - 第 1 页没有数据，已到达最后一页
2025-06-07 08:10:34,444 - INFO - 查询完成，共获取到 0 条记录
2025-06-07 08:10:34,444 - WARNING - 单月查询返回空数据: 2024-11
2025-06-07 08:10:35,975 - INFO - 查询月度分段 3: 2024-12-01 至 2025-02-28
2025-06-07 08:10:35,975 - INFO - 查询日期范围: 2024-12-01 至 2025-02-28，使用分页查询，每页 100 条记录
2025-06-07 08:10:35,975 - INFO - Request Parameters - Page 1:
2025-06-07 08:10:35,975 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:10:35,975 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:10:36,507 - INFO - API请求耗时: 531ms
2025-06-07 08:10:36,507 - INFO - Response - Page 1
2025-06-07 08:10:36,507 - INFO - 第 1 页获取到 100 条记录
2025-06-07 08:10:37,007 - INFO - Request Parameters - Page 2:
2025-06-07 08:10:37,007 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:10:37,007 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:10:37,554 - INFO - API请求耗时: 547ms
2025-06-07 08:10:37,554 - INFO - Response - Page 2
2025-06-07 08:10:37,554 - INFO - 第 2 页获取到 100 条记录
2025-06-07 08:10:38,054 - INFO - Request Parameters - Page 3:
2025-06-07 08:10:38,054 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:10:38,054 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:10:38,569 - INFO - API请求耗时: 516ms
2025-06-07 08:10:38,569 - INFO - Response - Page 3
2025-06-07 08:10:38,569 - INFO - 第 3 页获取到 100 条记录
2025-06-07 08:10:39,085 - INFO - Request Parameters - Page 4:
2025-06-07 08:10:39,085 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:10:39,085 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:10:39,647 - INFO - API请求耗时: 563ms
2025-06-07 08:10:39,647 - INFO - Response - Page 4
2025-06-07 08:10:39,647 - INFO - 第 4 页获取到 100 条记录
2025-06-07 08:10:40,163 - INFO - Request Parameters - Page 5:
2025-06-07 08:10:40,163 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:10:40,163 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1732982400000, 1740672000000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:10:40,725 - INFO - API请求耗时: 562ms
2025-06-07 08:10:40,725 - INFO - Response - Page 5
2025-06-07 08:10:40,725 - INFO - 第 5 页获取到 94 条记录
2025-06-07 08:10:40,725 - INFO - 查询完成，共获取到 494 条记录
2025-06-07 08:10:40,725 - INFO - 月度分段 3 查询成功，获取到 494 条记录
2025-06-07 08:10:41,741 - INFO - 查询月度分段 4: 2025-03-01 至 2025-05-31
2025-06-07 08:10:41,741 - INFO - 查询日期范围: 2025-03-01 至 2025-05-31，使用分页查询，每页 100 条记录
2025-06-07 08:10:41,741 - INFO - Request Parameters - Page 1:
2025-06-07 08:10:41,741 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:10:41,741 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:10:42,288 - INFO - API请求耗时: 547ms
2025-06-07 08:10:42,288 - INFO - Response - Page 1
2025-06-07 08:10:42,288 - INFO - 第 1 页获取到 100 条记录
2025-06-07 08:10:42,788 - INFO - Request Parameters - Page 2:
2025-06-07 08:10:42,788 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:10:42,788 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:10:43,475 - INFO - API请求耗时: 687ms
2025-06-07 08:10:43,475 - INFO - Response - Page 2
2025-06-07 08:10:43,475 - INFO - 第 2 页获取到 100 条记录
2025-06-07 08:10:43,991 - INFO - Request Parameters - Page 3:
2025-06-07 08:10:43,991 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:10:43,991 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:10:44,444 - INFO - API请求耗时: 453ms
2025-06-07 08:10:44,444 - INFO - Response - Page 3
2025-06-07 08:10:44,444 - INFO - 第 3 页获取到 100 条记录
2025-06-07 08:10:44,960 - INFO - Request Parameters - Page 4:
2025-06-07 08:10:44,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:10:44,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:10:45,538 - INFO - API请求耗时: 578ms
2025-06-07 08:10:45,538 - INFO - Response - Page 4
2025-06-07 08:10:45,538 - INFO - 第 4 页获取到 100 条记录
2025-06-07 08:10:46,053 - INFO - Request Parameters - Page 5:
2025-06-07 08:10:46,053 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:10:46,053 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:10:46,803 - INFO - API请求耗时: 750ms
2025-06-07 08:10:46,803 - INFO - Response - Page 5
2025-06-07 08:10:46,803 - INFO - 第 5 页获取到 100 条记录
2025-06-07 08:10:47,319 - INFO - Request Parameters - Page 6:
2025-06-07 08:10:47,319 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:10:47,319 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:10:47,835 - INFO - API请求耗时: 516ms
2025-06-07 08:10:47,835 - INFO - Response - Page 6
2025-06-07 08:10:47,835 - INFO - 第 6 页获取到 100 条记录
2025-06-07 08:10:48,350 - INFO - Request Parameters - Page 7:
2025-06-07 08:10:48,350 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:10:48,350 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1740758400000, 1748620800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:10:49,022 - INFO - API请求耗时: 672ms
2025-06-07 08:10:49,022 - INFO - Response - Page 7
2025-06-07 08:10:49,022 - INFO - 第 7 页获取到 98 条记录
2025-06-07 08:10:49,022 - INFO - 查询完成，共获取到 698 条记录
2025-06-07 08:10:49,022 - INFO - 月度分段 4 查询成功，获取到 698 条记录
2025-06-07 08:10:50,038 - INFO - 查询月度分段 5: 2025-06-01 至 2025-06-30
2025-06-07 08:10:50,038 - INFO - 查询日期范围: 2025-06-01 至 2025-06-30，使用分页查询，每页 100 条记录
2025-06-07 08:10:50,038 - INFO - Request Parameters - Page 1:
2025-06-07 08:10:50,038 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:10:50,038 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:10:50,569 - INFO - API请求耗时: 531ms
2025-06-07 08:10:50,569 - INFO - Response - Page 1
2025-06-07 08:10:50,569 - INFO - 第 1 页获取到 100 条记录
2025-06-07 08:10:51,085 - INFO - Request Parameters - Page 2:
2025-06-07 08:10:51,085 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:10:51,085 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:10:51,600 - INFO - API请求耗时: 516ms
2025-06-07 08:10:51,600 - INFO - Response - Page 2
2025-06-07 08:10:51,600 - INFO - 第 2 页获取到 100 条记录
2025-06-07 08:10:52,132 - INFO - Request Parameters - Page 3:
2025-06-07 08:10:52,132 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-06-07 08:10:52,132 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-B485DB81D8E94A5487DF601F30226AECLXCE', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9dkdkoz", "value": [1748707200000, 1751212800000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-07 08:10:52,538 - INFO - API请求耗时: 406ms
2025-06-07 08:10:52,538 - INFO - Response - Page 3
2025-06-07 08:10:52,538 - INFO - 第 3 页获取到 10 条记录
2025-06-07 08:10:52,538 - INFO - 查询完成，共获取到 210 条记录
2025-06-07 08:10:52,538 - INFO - 月度分段 5 查询成功，获取到 210 条记录
2025-06-07 08:10:53,538 - INFO - 宜搭月度表单数据查询完成，共 5 个分段，成功获取 1402 条记录，失败 0 次
2025-06-07 08:10:53,538 - INFO - 成功获取宜搭月销售表单数据，共 1402 条记录
2025-06-07 08:10:53,538 - INFO - 宜搭月销售数据项结构示例: ['formInstanceId', 'formData']
2025-06-07 08:10:53,538 - INFO - 正在从SQLite获取月度汇总数据...
2025-06-07 08:10:53,538 - INFO - 成功获取SQLite月度汇总数据，共 1402 条记录
2025-06-07 08:10:53,616 - INFO - 成功创建宜搭月销售数据索引，共 1402 条记录
2025-06-07 08:10:53,616 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-07 08:10:54,085 - INFO - 更新表单数据成功: FINST-4OD66CC1B2WUB26YD9IZECBFC82620IVMXX9MYD
2025-06-07 08:10:54,085 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_2025-04, 变更字段: [{'field': 'shopEntityName', 'old_value': 'SUPA FAMA', 'new_value': 'SUPA FAMA 农场西餐'}]
2025-06-07 08:10:54,085 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-07 08:10:54,569 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC822O6D16AMOJ
2025-06-07 08:10:54,569 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_2025-05, 变更字段: [{'field': 'shopEntityName', 'old_value': 'SUPA FAMA', 'new_value': 'SUPA FAMA 农场西餐'}]
2025-06-07 08:10:54,569 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:10:55,022 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM1I
2025-06-07 08:10:55,022 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MO2IE70S367Q2OVAE57DLH001Q7I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 42210.13, 'new_value': 50714.41}, {'field': 'dailyBillAmount', 'old_value': 42210.13, 'new_value': 50714.41}, {'field': 'amount', 'old_value': 1339.8, 'new_value': 1831.1}, {'field': 'count', 'old_value': 17, 'new_value': 24}, {'field': 'onlineAmount', 'old_value': 1339.8, 'new_value': 1831.1}, {'field': 'onlineCount', 'old_value': 17, 'new_value': 24}]
2025-06-07 08:10:55,022 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:10:55,507 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM2I
2025-06-07 08:10:55,507 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MR50JEM3SR7Q2OVAE57DM4001Q85_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 92871.33, 'new_value': 111741.49}, {'field': 'dailyBillAmount', 'old_value': 92871.33, 'new_value': 111741.49}, {'field': 'amount', 'old_value': 55213.0, 'new_value': 66370.8}, {'field': 'count', 'old_value': 512, 'new_value': 607}, {'field': 'instoreAmount', 'old_value': 23776.600000000002, 'new_value': 29072.9}, {'field': 'instoreCount', 'old_value': 178, 'new_value': 218}, {'field': 'onlineAmount', 'old_value': 31437.2, 'new_value': 37298.7}, {'field': 'onlineCount', 'old_value': 334, 'new_value': 389}]
2025-06-07 08:10:55,507 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:10:55,960 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM3I
2025-06-07 08:10:55,975 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MRVUM0P77G7Q2OV78BKOG4001PUK_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 57785.38, 'new_value': 71116.39}, {'field': 'dailyBillAmount', 'old_value': 57785.38, 'new_value': 71116.39}, {'field': 'amount', 'old_value': 58475.159999999996, 'new_value': 71922.16}, {'field': 'count', 'old_value': 380, 'new_value': 469}, {'field': 'instoreAmount', 'old_value': 54201.33, 'new_value': 66820.63}, {'field': 'instoreCount', 'old_value': 318, 'new_value': 395}, {'field': 'onlineAmount', 'old_value': 4332.81, 'new_value': 5164.51}, {'field': 'onlineCount', 'old_value': 62, 'new_value': 74}]
2025-06-07 08:10:55,975 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:10:56,397 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM4I
2025-06-07 08:10:56,397 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FE0MSM5BP3BCN7Q2OV78BKOGJ001PV3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 131098.66, 'new_value': 160031.16}, {'field': 'dailyBillAmount', 'old_value': 131098.66, 'new_value': 160031.16}, {'field': 'amount', 'old_value': 100044.4, 'new_value': 121669.8}, {'field': 'count', 'old_value': 477, 'new_value': 575}, {'field': 'instoreAmount', 'old_value': 100044.5, 'new_value': 121669.9}, {'field': 'instoreCount', 'old_value': 477, 'new_value': 575}]
2025-06-07 08:10:56,397 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:10:56,882 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM5I
2025-06-07 08:10:56,882 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FHI5VTHC3RHRI7Q2OVAE57DT4001C39_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 95331.66, 'new_value': 112189.58}, {'field': 'dailyBillAmount', 'old_value': 95331.66, 'new_value': 112189.58}, {'field': 'amount', 'old_value': 145487.8, 'new_value': 176942.8}, {'field': 'count', 'old_value': 509, 'new_value': 619}, {'field': 'instoreAmount', 'old_value': 144454.0, 'new_value': 175909.0}, {'field': 'instoreCount', 'old_value': 504, 'new_value': 614}, {'field': 'shopEntityName', 'old_value': 'SUPA FAMA', 'new_value': 'SUPA FAMA 农场西餐'}]
2025-06-07 08:10:56,882 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:10:57,366 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM6I
2025-06-07 08:10:57,366 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1FLB1FMEPQ1B7K7Q2OV6M1P2IS001EHS_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 9107.6, 'new_value': 10269.2}, {'field': 'dailyBillAmount', 'old_value': 9107.6, 'new_value': 10269.2}, {'field': 'amount', 'old_value': 11000.6, 'new_value': 12541.8}, {'field': 'count', 'old_value': 64, 'new_value': 76}, {'field': 'instoreAmount', 'old_value': 3826.0, 'new_value': 4442.9}, {'field': 'instoreCount', 'old_value': 6, 'new_value': 7}, {'field': 'onlineAmount', 'old_value': 7563.2, 'new_value': 8487.5}, {'field': 'onlineCount', 'old_value': 58, 'new_value': 69}]
2025-06-07 08:10:57,366 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:10:57,819 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM7I
2025-06-07 08:10:57,819 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GPFAB4PUHT7OL7Q2OV4FVC7US001R67_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 27570.1, 'new_value': 28570.1}, {'field': 'amount', 'old_value': 27570.0, 'new_value': 28570.0}, {'field': 'count', 'old_value': 23, 'new_value': 27}, {'field': 'instoreAmount', 'old_value': 27570.1, 'new_value': 28570.1}, {'field': 'instoreCount', 'old_value': 23, 'new_value': 27}]
2025-06-07 08:10:57,819 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:10:58,225 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM8I
2025-06-07 08:10:58,225 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_1GRNC18BRI7HUJ7Q2OV4FVC7AV001VJJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 134813.6, 'new_value': 166359.65}, {'field': 'dailyBillAmount', 'old_value': 134813.6, 'new_value': 166359.65}, {'field': 'amount', 'old_value': 84231.39, 'new_value': 102347.48}, {'field': 'count', 'old_value': 601, 'new_value': 750}, {'field': 'instoreAmount', 'old_value': 76892.19, 'new_value': 92852.39}, {'field': 'instoreCount', 'old_value': 330, 'new_value': 405}, {'field': 'onlineAmount', 'old_value': 9177.7, 'new_value': 11461.7}, {'field': 'onlineCount', 'old_value': 271, 'new_value': 345}]
2025-06-07 08:10:58,225 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:10:58,757 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM9I
2025-06-07 08:10:58,757 - INFO - 更新月销售记录成功: 1ETDLFB9DIMQME7Q2OVD93ISAI00189O_3F059827C9E04DEAA6B50797867EC52B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 119292.15, 'new_value': 147511.71}, {'field': 'dailyBillAmount', 'old_value': 119292.15, 'new_value': 147511.71}, {'field': 'amount', 'old_value': 22004.82, 'new_value': 27026.37}, {'field': 'count', 'old_value': 115, 'new_value': 140}, {'field': 'instoreAmount', 'old_value': 22004.82, 'new_value': 27026.37}, {'field': 'instoreCount', 'old_value': 115, 'new_value': 140}]
2025-06-07 08:10:58,757 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:10:59,178 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMAI
2025-06-07 08:10:59,178 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINPMIJNMLUJ7Q2OV392410G00148C_2025-06, 变更字段: [{'field': 'amount', 'old_value': 3066.0, 'new_value': 3191.0}, {'field': 'count', 'old_value': 3, 'new_value': 4}, {'field': 'instoreAmount', 'old_value': 3066.0, 'new_value': 3191.0}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 4}]
2025-06-07 08:10:59,178 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:10:59,647 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMBI
2025-06-07 08:10:59,647 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINSC23H1J3M7Q2OV392410L00148H_2025-06, 变更字段: [{'field': 'amount', 'old_value': 14644.0, 'new_value': 32445.0}, {'field': 'count', 'old_value': 23, 'new_value': 51}, {'field': 'instoreAmount', 'old_value': 14644.0, 'new_value': 32445.0}, {'field': 'instoreCount', 'old_value': 23, 'new_value': 51}]
2025-06-07 08:10:59,647 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:00,194 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMCI
2025-06-07 08:11:00,194 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6B2LVJOC7Q2OVBN4IS5M001D1O_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 225231.87, 'new_value': 264926.47}, {'field': 'dailyBillAmount', 'old_value': 225231.87, 'new_value': 264926.47}, {'field': 'amount', 'old_value': -94577.15000000001, 'new_value': -118140.14000000001}, {'field': 'count', 'old_value': 210, 'new_value': 240}, {'field': 'instoreAmount', 'old_value': 129060.67, 'new_value': 142891.44}, {'field': 'instoreCount', 'old_value': 210, 'new_value': 240}]
2025-06-07 08:11:00,194 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:00,647 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMDI
2025-06-07 08:11:00,647 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6IR3CUQ17Q2OVBN4IS5Q001D1S_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 56293.0, 'new_value': 76605.0}, {'field': 'amount', 'old_value': 56293.0, 'new_value': 76605.0}, {'field': 'count', 'old_value': 227, 'new_value': 288}, {'field': 'instoreAmount', 'old_value': 56293.0, 'new_value': 76605.0}, {'field': 'instoreCount', 'old_value': 227, 'new_value': 288}]
2025-06-07 08:11:00,663 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:01,100 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMEI
2025-06-07 08:11:01,100 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT6QJG5GQO7Q2OVBN4IS5U001D20_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 75630.45999999999, 'new_value': 92338.84999999999}, {'field': 'dailyBillAmount', 'old_value': 67089.86, 'new_value': 82658.25}, {'field': 'amount', 'old_value': 75630.45999999999, 'new_value': 92338.84999999999}, {'field': 'count', 'old_value': 248, 'new_value': 298}, {'field': 'instoreAmount', 'old_value': 75630.45999999999, 'new_value': 92338.84999999999}, {'field': 'instoreCount', 'old_value': 248, 'new_value': 298}]
2025-06-07 08:11:01,100 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:01,553 - INFO - 更新表单数据成功: FINST-AI866781XLYVAQQR8XBGXCAD2PZM3ON8IRFBMG1
2025-06-07 08:11:01,553 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT72H2L6227Q2OVBN4IS62001D24_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 23194.2, 'new_value': 32425.0}, {'field': 'dailyBillAmount', 'old_value': 23194.2, 'new_value': 32425.0}, {'field': 'count', 'old_value': 12, 'new_value': 13}, {'field': 'instoreCount', 'old_value': 12, 'new_value': 13}]
2025-06-07 08:11:01,553 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:02,022 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMFI
2025-06-07 08:11:02,022 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINT8VPFOREN7Q2OVBN4IS6Q001D2S_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 18849.68, 'new_value': 20908.08}, {'field': 'dailyBillAmount', 'old_value': 18849.68, 'new_value': 20908.08}, {'field': 'amount', 'old_value': 10438.63, 'new_value': 11476.33}, {'field': 'count', 'old_value': 185, 'new_value': 208}, {'field': 'instoreAmount', 'old_value': 10739.88, 'new_value': 11777.58}, {'field': 'instoreCount', 'old_value': 185, 'new_value': 208}]
2025-06-07 08:11:02,022 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:02,444 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMGI
2025-06-07 08:11:02,444 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTA575PTKJ7Q2OVBN4IS72001D34_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 29796.55, 'new_value': 36686.0}, {'field': 'dailyBillAmount', 'old_value': 10902.77, 'new_value': 17528.34}, {'field': 'amount', 'old_value': 29796.19, 'new_value': 36685.64}, {'field': 'count', 'old_value': 983, 'new_value': 1213}, {'field': 'instoreAmount', 'old_value': 27035.440000000002, 'new_value': 33062.46}, {'field': 'instoreCount', 'old_value': 905, 'new_value': 1113}, {'field': 'onlineAmount', 'old_value': 2761.11, 'new_value': 3623.54}, {'field': 'onlineCount', 'old_value': 78, 'new_value': 100}]
2025-06-07 08:11:02,444 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:02,897 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMHI
2025-06-07 08:11:02,897 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTAKS3N4EI7Q2OVBN4IS76001D38_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 63887.83, 'new_value': 71812.83}, {'field': 'dailyBillAmount', 'old_value': 63846.0, 'new_value': 71771.0}, {'field': 'amount', 'old_value': 32246.83, 'new_value': 60170.83}, {'field': 'count', 'old_value': 43, 'new_value': 55}, {'field': 'instoreAmount', 'old_value': 32069.0, 'new_value': 59993.0}, {'field': 'instoreCount', 'old_value': 41, 'new_value': 53}]
2025-06-07 08:11:02,897 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:03,350 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMII
2025-06-07 08:11:03,350 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTBJPIR4ME7Q2OVBN4IS7E001D3G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 72857.38, 'new_value': 108552.59}, {'field': 'dailyBillAmount', 'old_value': 72857.38, 'new_value': 108552.59}, {'field': 'amount', 'old_value': 72857.38, 'new_value': 108552.59}, {'field': 'count', 'old_value': 79, 'new_value': 105}, {'field': 'instoreAmount', 'old_value': 72857.38, 'new_value': 108552.59}, {'field': 'instoreCount', 'old_value': 79, 'new_value': 105}]
2025-06-07 08:11:03,350 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:03,881 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMJI
2025-06-07 08:11:03,881 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCB8VI1JE7Q2OVBN4IS7Q001D3S_2025-06, 变更字段: [{'field': 'amount', 'old_value': 5988.0, 'new_value': 12878.0}, {'field': 'count', 'old_value': 10, 'new_value': 14}, {'field': 'instoreAmount', 'old_value': 5988.0, 'new_value': 12878.0}, {'field': 'instoreCount', 'old_value': 10, 'new_value': 14}]
2025-06-07 08:11:03,881 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:04,350 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMKI
2025-06-07 08:11:04,350 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTCIR7D5JD7Q2OVBN4IS7U001D40_2025-06, 变更字段: [{'field': 'amount', 'old_value': 18395.5, 'new_value': 21603.1}, {'field': 'count', 'old_value': 56, 'new_value': 64}, {'field': 'instoreAmount', 'old_value': 18395.5, 'new_value': 21603.1}, {'field': 'instoreCount', 'old_value': 56, 'new_value': 64}]
2025-06-07 08:11:04,350 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:04,835 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMLI
2025-06-07 08:11:04,835 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTD2B070E67Q2OVBN4IS86001D48_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 13159.0, 'new_value': 18997.94}, {'field': 'amount', 'old_value': 13159.0, 'new_value': 18997.94}, {'field': 'count', 'old_value': 25, 'new_value': 31}, {'field': 'instoreAmount', 'old_value': 13159.0, 'new_value': 18997.94}, {'field': 'instoreCount', 'old_value': 25, 'new_value': 31}]
2025-06-07 08:11:04,835 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:05,303 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMMI
2025-06-07 08:11:05,303 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTDHDC0PTU7Q2OVBN4IS8E001D4G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 39740.22, 'new_value': 51472.66}, {'field': 'dailyBillAmount', 'old_value': 39740.22, 'new_value': 51472.66}, {'field': 'amount', 'old_value': 59345.9, 'new_value': 69158.6}, {'field': 'count', 'old_value': 302, 'new_value': 358}, {'field': 'instoreAmount', 'old_value': 59346.2, 'new_value': 69158.9}, {'field': 'instoreCount', 'old_value': 302, 'new_value': 358}]
2025-06-07 08:11:05,303 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:05,756 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMNI
2025-06-07 08:11:05,756 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GDINTE3QTM66G7Q2OVBN4IS8M001D4O_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 33396.88, 'new_value': 41481.54}, {'field': 'dailyBillAmount', 'old_value': 33396.88, 'new_value': 41481.54}, {'field': 'amount', 'old_value': 3164.55, 'new_value': 4295.219999999999}, {'field': 'count', 'old_value': 284, 'new_value': 352}, {'field': 'instoreAmount', 'old_value': 4020.34, 'new_value': 5273.91}, {'field': 'instoreCount', 'old_value': 284, 'new_value': 352}]
2025-06-07 08:11:05,756 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:06,241 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMPI
2025-06-07 08:11:06,241 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1GMFA53VGVIAPH7Q2OV4FVC7GS0017K2_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 51228.3, 'new_value': 57526.46}, {'field': 'dailyBillAmount', 'old_value': 51228.3, 'new_value': 57526.46}, {'field': 'amount', 'old_value': 51284.3, 'new_value': 57582.46}, {'field': 'count', 'old_value': 129, 'new_value': 153}, {'field': 'instoreAmount', 'old_value': 51284.3, 'new_value': 57582.46}, {'field': 'instoreCount', 'old_value': 129, 'new_value': 153}]
2025-06-07 08:11:06,241 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:06,850 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMQI
2025-06-07 08:11:06,850 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDJ5HVP5F47Q2OV4FVC77O001449_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 50198.09, 'new_value': 59342.75}, {'field': 'dailyBillAmount', 'old_value': 50198.09, 'new_value': 59342.75}, {'field': 'amount', 'old_value': 14718.3, 'new_value': 16138.3}, {'field': 'count', 'old_value': 38, 'new_value': 41}, {'field': 'instoreAmount', 'old_value': 14718.3, 'new_value': 16138.3}, {'field': 'instoreCount', 'old_value': 38, 'new_value': 41}]
2025-06-07 08:11:06,850 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:07,303 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMRI
2025-06-07 08:11:07,303 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDKDOANT3H7Q2OV4FVC78800144P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 69851.64, 'new_value': 83509.98}, {'field': 'dailyBillAmount', 'old_value': 69851.64, 'new_value': 83509.98}, {'field': 'amount', 'old_value': 27467.9, 'new_value': 32320.4}, {'field': 'count', 'old_value': 114, 'new_value': 131}, {'field': 'instoreAmount', 'old_value': 27467.9, 'new_value': 32320.4}, {'field': 'instoreCount', 'old_value': 114, 'new_value': 131}]
2025-06-07 08:11:07,303 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:07,741 - INFO - 更新表单数据成功: FINST-AI866781XLYVAQQR8XBGXCAD2PZM3ON8IRFBMH1
2025-06-07 08:11:07,741 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDL7K17VIP7Q2OV4FVC78K001455_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 4589.0, 'new_value': 5787.0}, {'field': 'amount', 'old_value': 4589.0, 'new_value': 5787.0}, {'field': 'count', 'old_value': 5, 'new_value': 7}, {'field': 'instoreAmount', 'old_value': 4589.0, 'new_value': 5787.0}, {'field': 'instoreCount', 'old_value': 5, 'new_value': 7}]
2025-06-07 08:11:07,741 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:08,178 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMSI
2025-06-07 08:11:08,178 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDMMR23CHP7Q2OV4FVC79C00145T_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 18385.57, 'new_value': 22576.25}, {'field': 'dailyBillAmount', 'old_value': 18385.57, 'new_value': 22576.25}, {'field': 'amount', 'old_value': 3842.65, 'new_value': 4779.58}, {'field': 'count', 'old_value': 148, 'new_value': 186}, {'field': 'instoreAmount', 'old_value': 946.9, 'new_value': 1210.5}, {'field': 'instoreCount', 'old_value': 24, 'new_value': 34}, {'field': 'onlineAmount', 'old_value': 2896.55, 'new_value': 3570.56}, {'field': 'onlineCount', 'old_value': 124, 'new_value': 152}]
2025-06-07 08:11:08,178 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:08,678 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMTI
2025-06-07 08:11:08,678 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDN6L4LMS87Q2OV4FVC79K001465_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 31739.989999999998, 'new_value': 37552.16}, {'field': 'dailyBillAmount', 'old_value': 31739.989999999998, 'new_value': 37552.16}, {'field': 'amount', 'old_value': 5879.87, 'new_value': 6800.31}, {'field': 'count', 'old_value': 145, 'new_value': 171}, {'field': 'instoreAmount', 'old_value': 5022.65, 'new_value': 5762.05}, {'field': 'instoreCount', 'old_value': 122, 'new_value': 144}, {'field': 'onlineAmount', 'old_value': 868.29, 'new_value': 1049.33}, {'field': 'onlineCount', 'old_value': 23, 'new_value': 27}]
2025-06-07 08:11:08,678 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:09,100 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMUI
2025-06-07 08:11:09,100 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDOE3O1J9R7Q2OV4FVC7A800146P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7750.6, 'new_value': 7820.6}, {'field': 'dailyBillAmount', 'old_value': 7750.6, 'new_value': 7820.6}, {'field': 'amount', 'old_value': 7543.6, 'new_value': 7613.6}, {'field': 'count', 'old_value': 188, 'new_value': 211}, {'field': 'instoreAmount', 'old_value': 7643.6, 'new_value': 7713.6}, {'field': 'instoreCount', 'old_value': 188, 'new_value': 211}]
2025-06-07 08:11:09,100 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:09,538 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMVI
2025-06-07 08:11:09,538 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDQDVL1EG67Q2OV4FVC7B800147P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 11011.74, 'new_value': 12862.74}, {'field': 'dailyBillAmount', 'old_value': 11011.74, 'new_value': 12862.74}, {'field': 'amount', 'old_value': 5548.24, 'new_value': 6601.34}, {'field': 'count', 'old_value': 302, 'new_value': 356}, {'field': 'instoreAmount', 'old_value': 2038.12, 'new_value': 2191.12}, {'field': 'instoreCount', 'old_value': 84, 'new_value': 90}, {'field': 'onlineAmount', 'old_value': 3735.54, 'new_value': 4653.64}, {'field': 'onlineCount', 'old_value': 218, 'new_value': 266}]
2025-06-07 08:11:09,538 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:09,944 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMWI
2025-06-07 08:11:09,960 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDRLHCKFK97Q2OV4FVC7BS00148D_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 65461.29, 'new_value': 82387.31}, {'field': 'dailyBillAmount', 'old_value': 65461.29, 'new_value': 82387.31}, {'field': 'amount', 'old_value': 37178.97, 'new_value': 49358.97}, {'field': 'count', 'old_value': 158, 'new_value': 201}, {'field': 'instoreAmount', 'old_value': 38058.520000000004, 'new_value': 50238.520000000004}, {'field': 'instoreCount', 'old_value': 158, 'new_value': 201}]
2025-06-07 08:11:09,960 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:10,475 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMXI
2025-06-07 08:11:10,475 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSD911K3E7Q2OV4FVC7C800148P_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 37271.61, 'new_value': 45141.54}, {'field': 'dailyBillAmount', 'old_value': 37271.61, 'new_value': 45141.54}, {'field': 'amount', 'old_value': 17776.52, 'new_value': 21965.49}, {'field': 'count', 'old_value': 842, 'new_value': 1009}, {'field': 'instoreAmount', 'old_value': 18315.14, 'new_value': 22514.93}, {'field': 'instoreCount', 'old_value': 842, 'new_value': 1009}]
2025-06-07 08:11:10,475 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:10,897 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMYI
2025-06-07 08:11:10,897 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSM3EAQJB7Q2OV4FVC7CC00148T_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 105084.5, 'new_value': 124399.7}, {'field': 'dailyBillAmount', 'old_value': 105084.5, 'new_value': 124399.7}, {'field': 'amount', 'old_value': 105084.5, 'new_value': 123267.7}, {'field': 'count', 'old_value': 131, 'new_value': 156}, {'field': 'instoreAmount', 'old_value': 105084.5, 'new_value': 123267.7}, {'field': 'instoreCount', 'old_value': 131, 'new_value': 156}]
2025-06-07 08:11:10,897 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:11,335 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMZI
2025-06-07 08:11:11,335 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDSTUTUPTG7Q2OV4FVC7CG001491_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 62890.3, 'new_value': 70349.0}, {'field': 'dailyBillAmount', 'old_value': 62890.3, 'new_value': 70349.0}, {'field': 'amount', 'old_value': 43535.4, 'new_value': 48175.299999999996}, {'field': 'count', 'old_value': 108, 'new_value': 121}, {'field': 'instoreAmount', 'old_value': 43535.4, 'new_value': 48175.299999999996}, {'field': 'instoreCount', 'old_value': 108, 'new_value': 121}]
2025-06-07 08:11:11,335 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:11,756 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM0J
2025-06-07 08:11:11,756 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDT8J32K6E7Q2OV4FVC7CK001495_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 11006.0, 'new_value': 13595.0}, {'field': 'dailyBillAmount', 'old_value': 11006.0, 'new_value': 13595.0}, {'field': 'amount', 'old_value': 10971.0, 'new_value': 13560.0}, {'field': 'count', 'old_value': 212, 'new_value': 260}, {'field': 'instoreAmount', 'old_value': 10971.0, 'new_value': 13560.0}, {'field': 'instoreCount', 'old_value': 212, 'new_value': 260}]
2025-06-07 08:11:11,756 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:12,272 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM1J
2025-06-07 08:11:12,272 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDTG15Q4SH7Q2OV4FVC7CO001499_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 17287.5, 'new_value': 21521.46}, {'field': 'dailyBillAmount', 'old_value': 17287.5, 'new_value': 21521.46}, {'field': 'amount', 'old_value': 17878.66, 'new_value': 22206.72}, {'field': 'count', 'old_value': 963, 'new_value': 1198}, {'field': 'instoreAmount', 'old_value': 9598.79, 'new_value': 12218.75}, {'field': 'instoreCount', 'old_value': 502, 'new_value': 638}, {'field': 'onlineAmount', 'old_value': 8663.6, 'new_value': 10384.7}, {'field': 'onlineCount', 'old_value': 461, 'new_value': 560}]
2025-06-07 08:11:12,272 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:12,710 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM2J
2025-06-07 08:11:12,710 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV0D9P6J27Q2OV4FVC7DG0014A1_2025-06, 变更字段: [{'field': 'amount', 'old_value': 9501.26, 'new_value': 10153.949999999999}, {'field': 'count', 'old_value': 370, 'new_value': 400}, {'field': 'onlineAmount', 'old_value': 3961.66, 'new_value': 4614.35}, {'field': 'onlineCount', 'old_value': 187, 'new_value': 217}]
2025-06-07 08:11:12,710 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:13,131 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM3J
2025-06-07 08:11:13,131 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDV84M589R7Q2OV4FVC7DK0014A5_2025-06, 变更字段: [{'field': 'amount', 'old_value': 11483.4, 'new_value': 13565.73}, {'field': 'count', 'old_value': 140, 'new_value': 174}, {'field': 'instoreAmount', 'old_value': 11484.289999999999, 'new_value': 13566.619999999999}, {'field': 'instoreCount', 'old_value': 140, 'new_value': 174}]
2025-06-07 08:11:13,131 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:13,553 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM4J
2025-06-07 08:11:13,553 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVG061A0H7Q2OV4FVC7DO0014A9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 16892.6, 'new_value': 18915.4}, {'field': 'amount', 'old_value': 16891.8, 'new_value': 18914.6}, {'field': 'count', 'old_value': 388, 'new_value': 453}, {'field': 'instoreAmount', 'old_value': 17169.7, 'new_value': 19192.5}, {'field': 'instoreCount', 'old_value': 388, 'new_value': 453}]
2025-06-07 08:11:13,553 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:13,975 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM5J
2025-06-07 08:11:13,975 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EDVNVP6LRA7Q2OV4FVC7DS0014AD_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 66293.5, 'new_value': 73341.5}, {'field': 'dailyBillAmount', 'old_value': 66293.5, 'new_value': 73341.5}, {'field': 'amount', 'old_value': 26751.5, 'new_value': 28941.5}, {'field': 'count', 'old_value': 78, 'new_value': 85}, {'field': 'instoreAmount', 'old_value': 26751.5, 'new_value': 28941.5}, {'field': 'instoreCount', 'old_value': 78, 'new_value': 85}]
2025-06-07 08:11:13,975 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:14,475 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM6J
2025-06-07 08:11:14,475 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE0PKSKM647Q2OV4FVC7EC0014AT_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 16460.04, 'new_value': 18719.04}, {'field': 'dailyBillAmount', 'old_value': 16460.04, 'new_value': 18719.04}, {'field': 'amount', 'old_value': 16007.880000000001, 'new_value': 18266.88}, {'field': 'count', 'old_value': 68, 'new_value': 83}, {'field': 'instoreAmount', 'old_value': 16247.04, 'new_value': 18506.04}, {'field': 'instoreCount', 'old_value': 68, 'new_value': 83}]
2025-06-07 08:11:14,475 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:14,928 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM7J
2025-06-07 08:11:14,928 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE1H27HPQN7Q2OV4FVC7EO0014B9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 9387.0, 'new_value': 15570.0}, {'field': 'dailyBillAmount', 'old_value': 9387.0, 'new_value': 15570.0}, {'field': 'amount', 'old_value': 14672.0, 'new_value': 17662.0}, {'field': 'count', 'old_value': 21, 'new_value': 25}, {'field': 'instoreAmount', 'old_value': 14672.0, 'new_value': 18230.0}, {'field': 'instoreCount', 'old_value': 21, 'new_value': 25}]
2025-06-07 08:11:14,928 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:15,428 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM8J
2025-06-07 08:11:15,428 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE29HIJ7QK7Q2OV4FVC7F40014BL_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 17451.2, 'new_value': 21529.2}, {'field': 'dailyBillAmount', 'old_value': 17451.2, 'new_value': 21529.2}, {'field': 'amount', 'old_value': 17451.1, 'new_value': 21529.1}, {'field': 'count', 'old_value': 43, 'new_value': 55}, {'field': 'instoreAmount', 'old_value': 18261.8, 'new_value': 22458.8}, {'field': 'instoreCount', 'old_value': 43, 'new_value': 55}]
2025-06-07 08:11:15,428 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:15,881 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM9J
2025-06-07 08:11:15,881 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H31EE2TORA7KI7Q2OV4FVC7FC0014BT_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 30137.0, 'new_value': 35749.95}, {'field': 'dailyBillAmount', 'old_value': 30137.0, 'new_value': 35749.95}, {'field': 'amount', 'old_value': 17926.48, 'new_value': 20690.16}, {'field': 'count', 'old_value': 427, 'new_value': 502}, {'field': 'instoreAmount', 'old_value': 16525.56, 'new_value': 18738.67}, {'field': 'instoreCount', 'old_value': 368, 'new_value': 429}, {'field': 'onlineAmount', 'old_value': 2260.74, 'new_value': 2811.31}, {'field': 'onlineCount', 'old_value': 59, 'new_value': 73}]
2025-06-07 08:11:15,881 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:16,350 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMAJ
2025-06-07 08:11:16,350 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9PGR3703D752ASKKUBQUM50018DU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 31679.09, 'new_value': 38146.09}, {'field': 'dailyBillAmount', 'old_value': 30960.25, 'new_value': 37328.22}, {'field': 'amount', 'old_value': 31678.1, 'new_value': 38145.1}, {'field': 'count', 'old_value': 411, 'new_value': 498}, {'field': 'instoreAmount', 'old_value': 30838.62, 'new_value': 37110.62}, {'field': 'instoreCount', 'old_value': 401, 'new_value': 485}, {'field': 'onlineAmount', 'old_value': 840.47, 'new_value': 1035.47}, {'field': 'onlineCount', 'old_value': 10, 'new_value': 13}]
2025-06-07 08:11:16,350 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:16,819 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMBJ
2025-06-07 08:11:16,819 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9S64J8E8R652ASKKUBQUMU0018EN_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 19226.55, 'new_value': 23329.98}, {'field': 'dailyBillAmount', 'old_value': 19226.55, 'new_value': 23329.98}, {'field': 'amount', 'old_value': 19955.92, 'new_value': 24111.69}, {'field': 'count', 'old_value': 104, 'new_value': 119}, {'field': 'instoreAmount', 'old_value': 18368.32, 'new_value': 22490.49}, {'field': 'instoreCount', 'old_value': 89, 'new_value': 103}, {'field': 'onlineAmount', 'old_value': 1588.9299999999998, 'new_value': 1622.53}, {'field': 'onlineCount', 'old_value': 15, 'new_value': 16}]
2025-06-07 08:11:16,819 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:17,272 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMCJ
2025-06-07 08:11:17,272 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1H9P9UCRQKEOIF52ASKKUBQUNH0018FA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 38352.8, 'new_value': 43493.7}, {'field': 'dailyBillAmount', 'old_value': 38352.8, 'new_value': 43493.7}, {'field': 'amount', 'old_value': 42396.8, 'new_value': 49313.7}, {'field': 'count', 'old_value': 155, 'new_value': 179}, {'field': 'instoreAmount', 'old_value': 43208.8, 'new_value': 50221.7}, {'field': 'instoreCount', 'old_value': 155, 'new_value': 179}]
2025-06-07 08:11:17,272 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:17,803 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMDJ
2025-06-07 08:11:17,803 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HB8D7KAC5K1G3723F7K257LIN001TSQ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6245.0, 'new_value': 7497.0}, {'field': 'dailyBillAmount', 'old_value': 6245.0, 'new_value': 7497.0}, {'field': 'amount', 'old_value': 6245.0, 'new_value': 7497.0}, {'field': 'count', 'old_value': 17, 'new_value': 21}, {'field': 'instoreAmount', 'old_value': 6324.0, 'new_value': 7576.0}, {'field': 'instoreCount', 'old_value': 17, 'new_value': 21}]
2025-06-07 08:11:17,803 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:18,256 - INFO - 更新表单数据成功: FINST-90D66XA12PZVBSKCD49BM87RNO2X30KE17HBMG6
2025-06-07 08:11:18,256 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HHDRIR0P38R666U1G19QP11V00018CE_2025-06, 变更字段: [{'field': 'amount', 'old_value': 1441.4, 'new_value': 2100.2}, {'field': 'count', 'old_value': 3, 'new_value': 5}, {'field': 'instoreAmount', 'old_value': 1441.4, 'new_value': 2100.2}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 5}]
2025-06-07 08:11:18,256 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:18,694 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMEJ
2025-06-07 08:11:18,694 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HINIR4GO8E5NM5U25UDHUFEGO001L3K_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 13201.36, 'new_value': 16120.95}, {'field': 'amount', 'old_value': 13201.150000000001, 'new_value': 16120.740000000002}, {'field': 'count', 'old_value': 598, 'new_value': 722}, {'field': 'instoreAmount', 'old_value': 13868.43, 'new_value': 16859.93}, {'field': 'instoreCount', 'old_value': 598, 'new_value': 722}]
2025-06-07 08:11:18,694 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:19,194 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMFJ
2025-06-07 08:11:19,194 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HMVHOKFUN7GGQ26TEPQN7CSO4001IA6_2025-06, 变更字段: [{'field': 'amount', 'old_value': 18532.38, 'new_value': 22066.38}, {'field': 'count', 'old_value': 76, 'new_value': 91}, {'field': 'instoreAmount', 'old_value': 18378.6, 'new_value': 21912.6}, {'field': 'instoreCount', 'old_value': 73, 'new_value': 88}]
2025-06-07 08:11:19,194 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:19,678 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMGJ
2025-06-07 08:11:19,678 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HOE1A3UTAESD606LODAUCEHAF001M2A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 73880.35, 'new_value': 85347.08}, {'field': 'dailyBillAmount', 'old_value': 73880.35, 'new_value': 85347.08}, {'field': 'amount', 'old_value': 30071.58, 'new_value': 35646.24}, {'field': 'count', 'old_value': 301, 'new_value': 358}, {'field': 'instoreAmount', 'old_value': 17202.34, 'new_value': 18504.399999999998}, {'field': 'instoreCount', 'old_value': 124, 'new_value': 133}, {'field': 'onlineAmount', 'old_value': 12869.91, 'new_value': 17142.510000000002}, {'field': 'onlineCount', 'old_value': 177, 'new_value': 225}]
2025-06-07 08:11:19,678 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:20,100 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMHJ
2025-06-07 08:11:20,100 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HV6P9SGUVGG9S36QDA69ST70J0015SA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 41070.7, 'new_value': 48803.36}, {'field': 'dailyBillAmount', 'old_value': 41070.7, 'new_value': 48803.36}, {'field': 'amount', 'old_value': 51868.0, 'new_value': 60781.0}, {'field': 'count', 'old_value': 273, 'new_value': 320}, {'field': 'instoreAmount', 'old_value': 51868.8, 'new_value': 60781.8}, {'field': 'instoreCount', 'old_value': 273, 'new_value': 320}]
2025-06-07 08:11:20,100 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:20,538 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMJJ
2025-06-07 08:11:20,538 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1HVORJ88U7D2IL1AIB692RTFU8001185_2025-06, 变更字段: [{'field': 'amount', 'old_value': 14097.6, 'new_value': 14196.24}, {'field': 'count', 'old_value': 165, 'new_value': 168}, {'field': 'instoreAmount', 'old_value': 13656.2, 'new_value': 13699.34}, {'field': 'instoreCount', 'old_value': 141, 'new_value': 142}, {'field': 'onlineAmount', 'old_value': 780.4, 'new_value': 835.9}, {'field': 'onlineCount', 'old_value': 24, 'new_value': 26}]
2025-06-07 08:11:20,538 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:20,991 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMKJ
2025-06-07 08:11:20,991 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1I6E0VAU3IFJEQ22MH147FMU0M0013E8_2025-06, 变更字段: [{'field': 'amount', 'old_value': 709.6, 'new_value': 758.6}, {'field': 'count', 'old_value': 11, 'new_value': 12}, {'field': 'instoreAmount', 'old_value': 710.3, 'new_value': 759.3}, {'field': 'instoreCount', 'old_value': 11, 'new_value': 12}]
2025-06-07 08:11:20,991 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:21,444 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMLJ
2025-06-07 08:11:21,444 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_1IBBJ3RNCSAVNO7A70STAEF09Q001IPV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 40060.79, 'new_value': 48215.05}, {'field': 'dailyBillAmount', 'old_value': 40060.79, 'new_value': 48215.05}, {'field': 'amount', 'old_value': 40060.79, 'new_value': 48215.05}, {'field': 'count', 'old_value': 163, 'new_value': 200}, {'field': 'instoreAmount', 'old_value': 40060.79, 'new_value': 48215.05}, {'field': 'instoreCount', 'old_value': 163, 'new_value': 200}]
2025-06-07 08:11:21,444 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:21,913 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMMJ
2025-06-07 08:11:21,913 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_3BB9A16AE8544997965802FAA3B83381_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7870.97, 'new_value': 9802.23}, {'field': 'dailyBillAmount', 'old_value': 7870.97, 'new_value': 9802.23}, {'field': 'amount', 'old_value': 8677.39, 'new_value': 10912.15}, {'field': 'count', 'old_value': 236, 'new_value': 297}, {'field': 'instoreAmount', 'old_value': 8678.36, 'new_value': 10913.12}, {'field': 'instoreCount', 'old_value': 236, 'new_value': 297}]
2025-06-07 08:11:21,913 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:22,272 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMNJ
2025-06-07 08:11:22,272 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6692283A183D432BAE322E1032539CE8_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 57144.0, 'new_value': 68366.0}, {'field': 'dailyBillAmount', 'old_value': 35732.55, 'new_value': 43349.55}, {'field': 'amount', 'old_value': 57144.0, 'new_value': 68366.0}, {'field': 'count', 'old_value': 98, 'new_value': 114}, {'field': 'instoreAmount', 'old_value': 57144.0, 'new_value': 68366.0}, {'field': 'instoreCount', 'old_value': 98, 'new_value': 114}]
2025-06-07 08:11:22,272 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:22,709 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMOJ
2025-06-07 08:11:22,709 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_6B7571A27AF84C73B4FC04CCBDB83D9B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7878.24, 'new_value': 9746.64}, {'field': 'amount', 'old_value': 7877.3, 'new_value': 9745.7}, {'field': 'count', 'old_value': 70, 'new_value': 87}, {'field': 'instoreAmount', 'old_value': 7878.24, 'new_value': 9746.64}, {'field': 'instoreCount', 'old_value': 70, 'new_value': 87}]
2025-06-07 08:11:22,709 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:23,194 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMPJ
2025-06-07 08:11:23,194 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78655ECA4A32471AB7842F8DE2018120_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 66570.0, 'new_value': 86967.0}, {'field': 'amount', 'old_value': 66570.0, 'new_value': 86967.0}, {'field': 'count', 'old_value': 16, 'new_value': 18}, {'field': 'instoreAmount', 'old_value': 66570.0, 'new_value': 86967.0}, {'field': 'instoreCount', 'old_value': 16, 'new_value': 18}]
2025-06-07 08:11:23,194 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:23,616 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMQJ
2025-06-07 08:11:23,616 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_78C3CF1DDB8443E8A4A4D425F7CA9756_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 40388.0, 'new_value': 47473.04}, {'field': 'dailyBillAmount', 'old_value': 34194.0, 'new_value': 41271.04}, {'field': 'amount', 'old_value': 40388.0, 'new_value': 47473.04}, {'field': 'count', 'old_value': 206, 'new_value': 240}, {'field': 'instoreAmount', 'old_value': 40388.0, 'new_value': 47473.04}, {'field': 'instoreCount', 'old_value': 206, 'new_value': 240}]
2025-06-07 08:11:23,616 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:24,038 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMRJ
2025-06-07 08:11:24,038 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_A93C60005A8F41B092F6C5A8C21577CB_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 0.0, 'new_value': 11222.9}, {'field': 'dailyBillAmount', 'old_value': 0.0, 'new_value': 11222.9}, {'field': 'amount', 'old_value': 11401.1, 'new_value': 23372.21}, {'field': 'count', 'old_value': 110, 'new_value': 133}, {'field': 'instoreAmount', 'old_value': 11401.9, 'new_value': 23373.010000000002}, {'field': 'instoreCount', 'old_value': 110, 'new_value': 133}]
2025-06-07 08:11:24,053 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:24,491 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMSJ
2025-06-07 08:11:24,491 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AA76628FACEC4C13BD44C8280B45416D_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 2810.2000000000003, 'new_value': 4114.7}, {'field': 'dailyBillAmount', 'old_value': 2810.2000000000003, 'new_value': 4114.7}, {'field': 'amount', 'old_value': 2810.2000000000003, 'new_value': 4114.7}, {'field': 'count', 'old_value': 5, 'new_value': 7}, {'field': 'instoreAmount', 'old_value': 2810.2000000000003, 'new_value': 4114.7}, {'field': 'instoreCount', 'old_value': 5, 'new_value': 7}]
2025-06-07 08:11:24,491 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:24,897 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMTJ
2025-06-07 08:11:24,897 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_AC07B70DB49845A8A52846E099EBC515_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 60370.64, 'new_value': 75835.64}, {'field': 'dailyBillAmount', 'old_value': 60370.64, 'new_value': 75835.64}, {'field': 'amount', 'old_value': 72345.92, 'new_value': 87810.92}, {'field': 'count', 'old_value': 227, 'new_value': 275}, {'field': 'instoreAmount', 'old_value': 72346.64, 'new_value': 87811.64}, {'field': 'instoreCount', 'old_value': 227, 'new_value': 275}]
2025-06-07 08:11:24,897 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:25,334 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMUJ
2025-06-07 08:11:25,334 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_BF554F536BF14762AEB7110E7BD583B7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 111269.69, 'new_value': 145510.69}, {'field': 'dailyBillAmount', 'old_value': 111269.69, 'new_value': 145510.69}, {'field': 'amount', 'old_value': 180036.62, 'new_value': 216941.33000000002}, {'field': 'count', 'old_value': 240, 'new_value': 286}, {'field': 'instoreAmount', 'old_value': 180036.62, 'new_value': 216941.33000000002}, {'field': 'instoreCount', 'old_value': 240, 'new_value': 286}]
2025-06-07 08:11:25,334 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:25,850 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMVJ
2025-06-07 08:11:25,850 - INFO - 更新月销售记录成功: 1GD9P85P7PPH4L7Q2OV3OBNS49001KPE_F56E8E23D2584556A30D1378611DF4AE_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 18044.4, 'new_value': 22235.2}, {'field': 'dailyBillAmount', 'old_value': 18044.4, 'new_value': 22235.2}, {'field': 'amount', 'old_value': 6959.9, 'new_value': 7728.8}, {'field': 'count', 'old_value': 29, 'new_value': 33}, {'field': 'instoreAmount', 'old_value': 6959.9, 'new_value': 7728.8}, {'field': 'instoreCount', 'old_value': 29, 'new_value': 33}]
2025-06-07 08:11:25,850 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-07 08:11:26,428 - INFO - 更新表单数据成功: FINST-8PF66V71F2WUF93GERNDRA2XP25A2BHBNXX9MGA
2025-06-07 08:11:26,428 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EG92S1SB0I86N3H2U188001EHE_2025-04, 变更字段: [{'field': 'amount', 'old_value': 48457.0, 'new_value': 49384.0}, {'field': 'count', 'old_value': 114, 'new_value': 115}, {'field': 'instoreAmount', 'old_value': 48723.0, 'new_value': 49650.0}, {'field': 'instoreCount', 'old_value': 114, 'new_value': 115}]
2025-06-07 08:11:26,428 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-07 08:11:26,850 - INFO - 更新表单数据成功: FINST-UW966371W41VCEQDE23SU5FHZDC823O6D16AMYL
2025-06-07 08:11:26,850 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_2025-05, 变更字段: [{'field': 'amount', 'old_value': 112238.0, 'new_value': 112261.62}, {'field': 'count', 'old_value': 5538, 'new_value': 5540}, {'field': 'onlineAmount', 'old_value': 90323.24, 'new_value': 90360.44}, {'field': 'onlineCount', 'old_value': 3883, 'new_value': 3885}]
2025-06-07 08:11:26,850 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-07 08:11:27,319 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3O99D16AM871
2025-06-07 08:11:27,319 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_2025-05, 变更字段: [{'field': 'amount', 'old_value': 397271.1, 'new_value': 371698.2}, {'field': 'count', 'old_value': 496, 'new_value': 498}, {'field': 'instoreAmount', 'old_value': 411668.66000000003, 'new_value': 384158.76}, {'field': 'instoreCount', 'old_value': 496, 'new_value': 498}]
2025-06-07 08:11:27,319 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-07 08:11:27,772 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM081
2025-06-07 08:11:27,772 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 502198.33, 'new_value': 543302.65}, {'field': 'dailyBillAmount', 'old_value': 502198.33, 'new_value': 543302.65}]
2025-06-07 08:11:27,772 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-07 08:11:28,241 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM381
2025-06-07 08:11:28,241 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAIN5DMKK0I86N3H2U1VH001F8N_2025-05, 变更字段: [{'field': 'amount', 'old_value': -332930.98, 'new_value': -333239.28}]
2025-06-07 08:11:28,241 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:28,694 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMWJ
2025-06-07 08:11:28,694 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDMFCJQF4F0I86N3H2U1UC001E7I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 44942.34, 'new_value': 51140.68}, {'field': 'amount', 'old_value': 44941.409999999996, 'new_value': 51139.75}, {'field': 'count', 'old_value': 489, 'new_value': 554}, {'field': 'instoreAmount', 'old_value': 31425.89, 'new_value': 36047.04}, {'field': 'instoreCount', 'old_value': 295, 'new_value': 337}, {'field': 'onlineAmount', 'old_value': 14607.93, 'new_value': 16465.42}, {'field': 'onlineCount', 'old_value': 194, 'new_value': 217}]
2025-06-07 08:11:28,694 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:29,256 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMXJ
2025-06-07 08:11:29,256 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNEJCRN7K0I86N3H2U1UK001E7Q_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 66781.54000000001, 'new_value': 78484.76}, {'field': 'dailyBillAmount', 'old_value': 66781.54000000001, 'new_value': 78484.76}, {'field': 'amount', 'old_value': 5227.74, 'new_value': 5817.74}, {'field': 'count', 'old_value': 161, 'new_value': 173}, {'field': 'instoreAmount', 'old_value': 5798.54, 'new_value': 6406.24}, {'field': 'instoreCount', 'old_value': 161, 'new_value': 173}]
2025-06-07 08:11:29,256 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:29,678 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMYJ
2025-06-07 08:11:29,678 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDNMK1P3900I86N3H2U1UO001E7U_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 72538.04, 'new_value': 80465.64}, {'field': 'dailyBillAmount', 'old_value': 72538.04, 'new_value': 80465.64}, {'field': 'amount', 'old_value': 32054.71, 'new_value': 39664.51}, {'field': 'count', 'old_value': 676, 'new_value': 786}, {'field': 'instoreAmount', 'old_value': 29782.41, 'new_value': 36374.1}, {'field': 'instoreCount', 'old_value': 617, 'new_value': 705}, {'field': 'onlineAmount', 'old_value': 2881.8900000000003, 'new_value': 3960.57}, {'field': 'onlineCount', 'old_value': 59, 'new_value': 81}]
2025-06-07 08:11:29,678 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:30,131 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBMZJ
2025-06-07 08:11:30,131 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOM98AG2E0I86N3H2U1V8001E8E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 51982.2, 'new_value': 60440.0}, {'field': 'amount', 'old_value': 51981.1, 'new_value': 60438.9}, {'field': 'count', 'old_value': 196, 'new_value': 226}, {'field': 'instoreAmount', 'old_value': 53660.9, 'new_value': 62118.7}, {'field': 'instoreCount', 'old_value': 196, 'new_value': 226}]
2025-06-07 08:11:30,131 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:30,569 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM0K
2025-06-07 08:11:30,584 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDOU3MQ7UF0I86N3H2U1VC001E8I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 152106.43, 'new_value': 169687.35}, {'field': 'dailyBillAmount', 'old_value': 152106.43, 'new_value': 169687.35}, {'field': 'amount', 'old_value': 134567.72, 'new_value': 141684.08}, {'field': 'count', 'old_value': 2274, 'new_value': 2464}, {'field': 'instoreAmount', 'old_value': 126209.13, 'new_value': 131740.85}, {'field': 'instoreCount', 'old_value': 2115, 'new_value': 2264}, {'field': 'onlineAmount', 'old_value': 8787.45, 'new_value': 10539.19}, {'field': 'onlineCount', 'old_value': 159, 'new_value': 200}]
2025-06-07 08:11:30,584 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:31,038 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM1K
2025-06-07 08:11:31,038 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDPG4SK9DE0I86N3H2U1VK001E8Q_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 101369.21, 'new_value': 122975.46}, {'field': 'amount', 'old_value': 101368.35, 'new_value': 122974.6}, {'field': 'count', 'old_value': 2304, 'new_value': 2807}, {'field': 'instoreAmount', 'old_value': 74402.86, 'new_value': 90951.36}, {'field': 'instoreCount', 'old_value': 1551, 'new_value': 1916}, {'field': 'onlineAmount', 'old_value': 26966.35, 'new_value': 32024.1}, {'field': 'onlineCount', 'old_value': 753, 'new_value': 891}]
2025-06-07 08:11:31,038 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:31,444 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM2K
2025-06-07 08:11:31,444 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQ4GA7M630I86N3H2U1VS001E92_2025-06, 变更字段: [{'field': 'amount', 'old_value': -2222.98, 'new_value': -2632.83}, {'field': 'count', 'old_value': 23, 'new_value': 25}, {'field': 'onlineAmount', 'old_value': 443.0, 'new_value': 479.0}, {'field': 'onlineCount', 'old_value': 18, 'new_value': 20}]
2025-06-07 08:11:31,444 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:31,850 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM3K
2025-06-07 08:11:31,850 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQJKO8O0D0I86N3H2U104001E9A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 12211.65, 'new_value': 21146.73}, {'field': 'dailyBillAmount', 'old_value': 12211.65, 'new_value': 21146.73}, {'field': 'amount', 'old_value': 29161.63, 'new_value': 37068.9}, {'field': 'count', 'old_value': 1916, 'new_value': 2351}, {'field': 'instoreAmount', 'old_value': 21828.8, 'new_value': 27912.24}, {'field': 'instoreCount', 'old_value': 1320, 'new_value': 1610}, {'field': 'onlineAmount', 'old_value': 7938.85, 'new_value': 9883.05}, {'field': 'onlineCount', 'old_value': 596, 'new_value': 741}]
2025-06-07 08:11:31,866 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:32,397 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM4K
2025-06-07 08:11:32,397 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDQQG9THS10I86N3H2U108001E9E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 55513.369999999995, 'new_value': 61397.869999999995}, {'field': 'dailyBillAmount', 'old_value': 55513.369999999995, 'new_value': 61397.869999999995}, {'field': 'amount', 'old_value': 53062.509999999995, 'new_value': 55908.399999999994}, {'field': 'count', 'old_value': 1590, 'new_value': 1664}, {'field': 'instoreAmount', 'old_value': 53282.71, 'new_value': 56128.75}, {'field': 'instoreCount', 'old_value': 1590, 'new_value': 1664}]
2025-06-07 08:11:32,397 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:32,772 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM5K
2025-06-07 08:11:32,772 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDRR6FJ7A60I86N3H2U10L001E9R_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 22486.06, 'new_value': 25702.13}, {'field': 'amount', 'old_value': 22486.02, 'new_value': 25702.09}, {'field': 'count', 'old_value': 1266, 'new_value': 1464}, {'field': 'instoreAmount', 'old_value': 11544.42, 'new_value': 12962.289999999999}, {'field': 'instoreCount', 'old_value': 765, 'new_value': 876}, {'field': 'onlineAmount', 'old_value': 10941.64, 'new_value': 12739.84}, {'field': 'onlineCount', 'old_value': 501, 'new_value': 588}]
2025-06-07 08:11:32,772 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:33,256 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM6K
2025-06-07 08:11:33,256 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDS341MMSU0I86N3H2U10P001E9V_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 29706.32, 'new_value': 33884.71}, {'field': 'dailyBillAmount', 'old_value': 29706.32, 'new_value': 33884.71}, {'field': 'amount', 'old_value': 6035.54, 'new_value': 7306.4400000000005}, {'field': 'count', 'old_value': 198, 'new_value': 232}, {'field': 'instoreAmount', 'old_value': 6135.68, 'new_value': 7429.48}, {'field': 'instoreCount', 'old_value': 198, 'new_value': 232}]
2025-06-07 08:11:33,256 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:33,725 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2TTN3LEBM7K
2025-06-07 08:11:33,725 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSC7N3PHM0I86N3H2U10T001EA3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 27163.45, 'new_value': 32953.53}, {'field': 'dailyBillAmount', 'old_value': 27163.45, 'new_value': 32953.53}, {'field': 'amount', 'old_value': 19133.440000000002, 'new_value': 23461.41}, {'field': 'count', 'old_value': 895, 'new_value': 1071}, {'field': 'instoreAmount', 'old_value': 3969.13, 'new_value': 4852.3}, {'field': 'instoreCount', 'old_value': 240, 'new_value': 283}, {'field': 'onlineAmount', 'old_value': 15545.7, 'new_value': 19008.4}, {'field': 'onlineCount', 'old_value': 655, 'new_value': 788}]
2025-06-07 08:11:33,725 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:34,178 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBM8K
2025-06-07 08:11:34,178 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDSK489TE20I86N3H2U114001EAA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 25870.13, 'new_value': 28072.24}, {'field': 'amount', 'old_value': 25869.58, 'new_value': 28071.690000000002}, {'field': 'count', 'old_value': 677, 'new_value': 763}, {'field': 'instoreAmount', 'old_value': 24794.96, 'new_value': 26842.57}, {'field': 'instoreCount', 'old_value': 662, 'new_value': 745}, {'field': 'onlineAmount', 'old_value': 1082.47, 'new_value': 1236.97}, {'field': 'onlineCount', 'old_value': 15, 'new_value': 18}]
2025-06-07 08:11:34,178 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:34,522 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBM9K
2025-06-07 08:11:34,522 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDTTV5HD0Q0I86N3H2U11P001EAV_2025-06, 变更字段: [{'field': 'amount', 'old_value': 33336.61, 'new_value': 41192.12}, {'field': 'count', 'old_value': 1764, 'new_value': 2085}, {'field': 'instoreAmount', 'old_value': 35081.23, 'new_value': 43538.64}, {'field': 'instoreCount', 'old_value': 1751, 'new_value': 2072}]
2025-06-07 08:11:34,522 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:34,991 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMAK
2025-06-07 08:11:34,991 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDU5QKVU3D0I86N3H2U11T001EB3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 34875.479999999996, 'new_value': 40833.2}, {'field': 'dailyBillAmount', 'old_value': 34875.479999999996, 'new_value': 40833.2}, {'field': 'amount', 'old_value': 21313.22, 'new_value': 25214.31}, {'field': 'count', 'old_value': 1515, 'new_value': 1794}, {'field': 'instoreAmount', 'old_value': 1284.5, 'new_value': 1557.5}, {'field': 'instoreCount', 'old_value': 89, 'new_value': 105}, {'field': 'onlineAmount', 'old_value': 20850.43, 'new_value': 24702.94}, {'field': 'onlineCount', 'old_value': 1426, 'new_value': 1689}]
2025-06-07 08:11:34,991 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:35,428 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMBK
2025-06-07 08:11:35,428 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDUDM4PLNS0I86N3H2U121001EB7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 40630.7, 'new_value': 46001.81}, {'field': 'dailyBillAmount', 'old_value': 40630.7, 'new_value': 46001.81}, {'field': 'amount', 'old_value': 33545.11, 'new_value': 37906.4}, {'field': 'count', 'old_value': 992, 'new_value': 1154}, {'field': 'instoreAmount', 'old_value': 19394.74, 'new_value': 21674.64}, {'field': 'instoreCount', 'old_value': 756, 'new_value': 866}, {'field': 'onlineAmount', 'old_value': 15772.599999999999, 'new_value': 18594.9}, {'field': 'onlineCount', 'old_value': 236, 'new_value': 288}]
2025-06-07 08:11:35,428 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:35,866 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMDK
2025-06-07 08:11:35,866 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCDVSS4V3460I86N3H2U12P001EBV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 13313.77, 'new_value': 15936.5}, {'field': 'dailyBillAmount', 'old_value': 13313.77, 'new_value': 15936.5}, {'field': 'amount', 'old_value': 15750.79, 'new_value': 18686.04}, {'field': 'count', 'old_value': 535, 'new_value': 639}, {'field': 'instoreAmount', 'old_value': 5958.2, 'new_value': 6933.150000000001}, {'field': 'instoreCount', 'old_value': 195, 'new_value': 227}, {'field': 'onlineAmount', 'old_value': 9950.66, 'new_value': 11910.96}, {'field': 'onlineCount', 'old_value': 340, 'new_value': 412}]
2025-06-07 08:11:35,866 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:36,303 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMEK
2025-06-07 08:11:36,319 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE0JS3BF7R0I86N3H2U135001ECB_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 22933.77, 'new_value': 25419.739999999998}, {'field': 'dailyBillAmount', 'old_value': 22933.77, 'new_value': 25419.739999999998}, {'field': 'amount', 'old_value': 23948.58, 'new_value': 26579.03}, {'field': 'count', 'old_value': 803, 'new_value': 907}, {'field': 'instoreAmount', 'old_value': 23928.82, 'new_value': 26559.27}, {'field': 'instoreCount', 'old_value': 801, 'new_value': 905}]
2025-06-07 08:11:36,319 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:36,788 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMFK
2025-06-07 08:11:36,788 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCCE13J2R9CI0I86N3H2U13D001ECJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 53629.0, 'new_value': 57701.0}, {'field': 'dailyBillAmount', 'old_value': 53629.0, 'new_value': 57701.0}, {'field': 'amount', 'old_value': 62172.0, 'new_value': 66394.0}, {'field': 'count', 'old_value': 51, 'new_value': 57}, {'field': 'instoreAmount', 'old_value': 65268.0, 'new_value': 70470.0}, {'field': 'instoreCount', 'old_value': 51, 'new_value': 57}]
2025-06-07 08:11:36,788 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:37,241 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMGK
2025-06-07 08:11:37,241 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9D9OBM41R0I86N3H2U17K001EGQ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 36096.0, 'new_value': 38635.4}, {'field': 'dailyBillAmount', 'old_value': 36096.0, 'new_value': 38635.4}, {'field': 'amount', 'old_value': 36348.4, 'new_value': 38887.8}, {'field': 'count', 'old_value': 78, 'new_value': 88}, {'field': 'instoreAmount', 'old_value': 37332.61, 'new_value': 39872.01}, {'field': 'instoreCount', 'old_value': 78, 'new_value': 88}]
2025-06-07 08:11:37,241 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:37,678 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMHK
2025-06-07 08:11:37,678 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9DHNUM3T50I86N3H2U17O001EGU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6164.0, 'new_value': 6992.0}, {'field': 'amount', 'old_value': 6164.0, 'new_value': 6992.0}, {'field': 'count', 'old_value': 20, 'new_value': 23}, {'field': 'instoreAmount', 'old_value': 6164.0, 'new_value': 6992.0}, {'field': 'instoreCount', 'old_value': 20, 'new_value': 23}]
2025-06-07 08:11:37,678 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:38,178 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMJK
2025-06-07 08:11:38,178 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9EN1GSFF80I86N3H2U18C001EHI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 10507.0, 'new_value': 11574.0}, {'field': 'dailyBillAmount', 'old_value': 5223.0, 'new_value': 6290.0}, {'field': 'amount', 'old_value': 9569.0, 'new_value': 10636.0}, {'field': 'count', 'old_value': 11, 'new_value': 13}, {'field': 'instoreAmount', 'old_value': 9569.0, 'new_value': 10636.0}, {'field': 'instoreCount', 'old_value': 11, 'new_value': 13}]
2025-06-07 08:11:38,178 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:38,631 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMKK
2025-06-07 08:11:38,631 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9ETPVO71I0I86N3H2U18G001EHM_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 14353.5, 'new_value': 17024.5}, {'field': 'amount', 'old_value': 14353.5, 'new_value': 17024.5}, {'field': 'count', 'old_value': 35, 'new_value': 43}, {'field': 'instoreAmount', 'old_value': 14353.5, 'new_value': 17024.5}, {'field': 'instoreCount', 'old_value': 35, 'new_value': 43}]
2025-06-07 08:11:38,631 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:39,100 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMLK
2025-06-07 08:11:39,116 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9G31FV3GL0I86N3H2U190001EI6_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 84809.0, 'new_value': 113304.0}, {'field': 'dailyBillAmount', 'old_value': 84809.0, 'new_value': 113304.0}, {'field': 'amount', 'old_value': 101556.0, 'new_value': 130051.0}, {'field': 'count', 'old_value': 15, 'new_value': 19}, {'field': 'instoreAmount', 'old_value': 101556.0, 'new_value': 130051.0}, {'field': 'instoreCount', 'old_value': 15, 'new_value': 19}]
2025-06-07 08:11:39,116 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:39,616 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMMK
2025-06-07 08:11:39,616 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9GHTM38HU0I86N3H2U198001EIE_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 26185.0, 'new_value': 32544.0}, {'field': 'amount', 'old_value': 26185.0, 'new_value': 32544.0}, {'field': 'count', 'old_value': 10, 'new_value': 11}, {'field': 'instoreAmount', 'old_value': 26185.0, 'new_value': 32544.0}, {'field': 'instoreCount', 'old_value': 10, 'new_value': 11}]
2025-06-07 08:11:39,616 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:40,037 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMNK
2025-06-07 08:11:40,037 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCE9H11376450I86N3H2U19G001EIM_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6059.0, 'new_value': 6737.0}, {'field': 'amount', 'old_value': 6059.0, 'new_value': 6737.0}, {'field': 'count', 'old_value': 11, 'new_value': 13}, {'field': 'instoreAmount', 'old_value': 6059.0, 'new_value': 6737.0}, {'field': 'instoreCount', 'old_value': 11, 'new_value': 13}]
2025-06-07 08:11:40,037 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:40,381 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMOK
2025-06-07 08:11:40,381 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDCE3748SO0I86N3H2U1FP001EOV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7474.0, 'new_value': 12866.0}, {'field': 'amount', 'old_value': 7474.0, 'new_value': 12866.0}, {'field': 'count', 'old_value': 9, 'new_value': 14}, {'field': 'instoreAmount', 'old_value': 7474.0, 'new_value': 12866.0}, {'field': 'instoreCount', 'old_value': 9, 'new_value': 14}]
2025-06-07 08:11:40,381 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:40,850 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMPK
2025-06-07 08:11:40,850 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEISQT8KE0I86N3H2U1GT001EQ3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 50076.1, 'new_value': 56007.3}, {'field': 'dailyBillAmount', 'old_value': 50076.1, 'new_value': 56007.3}, {'field': 'amount', 'old_value': 49242.5, 'new_value': 55173.7}, {'field': 'count', 'old_value': 69, 'new_value': 74}, {'field': 'instoreAmount', 'old_value': 50219.9, 'new_value': 56151.1}, {'field': 'instoreCount', 'old_value': 69, 'new_value': 74}]
2025-06-07 08:11:40,850 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:41,303 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMQK
2025-06-07 08:11:41,303 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDEQ2M9E710I86N3H2U1H1001EQ7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6953.299999999999, 'new_value': 9409.8}, {'field': 'dailyBillAmount', 'old_value': 6953.299999999999, 'new_value': 9409.8}, {'field': 'amount', 'old_value': 6704.3, 'new_value': 8964.9}, {'field': 'count', 'old_value': 57, 'new_value': 90}, {'field': 'instoreAmount', 'old_value': 5347.5, 'new_value': 7364.6}, {'field': 'instoreCount', 'old_value': 42, 'new_value': 71}, {'field': 'onlineAmount', 'old_value': 1358.7, 'new_value': 1602.33}, {'field': 'onlineCount', 'old_value': 15, 'new_value': 19}]
2025-06-07 08:11:41,303 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:41,756 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMRK
2025-06-07 08:11:41,756 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF11AKIN30I86N3H2U1H5001EQB_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 2592.0, 'new_value': 2880.0}, {'field': 'amount', 'old_value': 2592.0, 'new_value': 2880.0}, {'field': 'count', 'old_value': 6, 'new_value': 7}, {'field': 'instoreAmount', 'old_value': 2592.0, 'new_value': 2880.0}, {'field': 'instoreCount', 'old_value': 6, 'new_value': 7}]
2025-06-07 08:11:41,756 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:42,209 - INFO - 更新表单数据成功: FINST-3PF66X61XETVWJDSCO22VBVU948M2UTN3LEBMSK
2025-06-07 08:11:42,209 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDF8HFHI690I86N3H2U1H9001EQF_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3977.0, 'new_value': 4516.0}, {'field': 'dailyBillAmount', 'old_value': 3977.0, 'new_value': 4516.0}, {'field': 'amount', 'old_value': 3972.0, 'new_value': 4511.0}, {'field': 'count', 'old_value': 18, 'new_value': 21}, {'field': 'instoreAmount', 'old_value': 4502.0, 'new_value': 5041.0}, {'field': 'instoreCount', 'old_value': 18, 'new_value': 21}]
2025-06-07 08:11:42,225 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:42,662 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMRR
2025-06-07 08:11:42,662 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDFFVIG6FU0I86N3H2U1HD001EQJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7726.700000000001, 'new_value': 8479.7}, {'field': 'amount', 'old_value': 7725.9, 'new_value': 8478.9}, {'field': 'count', 'old_value': 44, 'new_value': 48}, {'field': 'instoreAmount', 'old_value': 7834.700000000001, 'new_value': 8587.7}, {'field': 'instoreCount', 'old_value': 44, 'new_value': 48}]
2025-06-07 08:11:42,662 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:43,053 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMSR
2025-06-07 08:11:43,053 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDG7SC9VNO0I86N3H2U1HP001EQV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 1799.0, 'new_value': 1999.0}, {'field': 'dailyBillAmount', 'old_value': 1799.0, 'new_value': 1999.0}, {'field': 'amount', 'old_value': 11761.0, 'new_value': 12391.0}, {'field': 'count', 'old_value': 27, 'new_value': 32}, {'field': 'instoreAmount', 'old_value': 11761.0, 'new_value': 12589.0}, {'field': 'instoreCount', 'old_value': 27, 'new_value': 32}]
2025-06-07 08:11:43,053 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:43,522 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMTR
2025-06-07 08:11:43,522 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDGEMRE5GJ0I86N3H2U1HT001ER3_2025-06, 变更字段: [{'field': 'amount', 'old_value': 15233.3, 'new_value': 16162.11}, {'field': 'count', 'old_value': 160, 'new_value': 168}, {'field': 'instoreAmount', 'old_value': 11998.039999999999, 'new_value': 12566.939999999999}, {'field': 'instoreCount', 'old_value': 116, 'new_value': 118}, {'field': 'onlineAmount', 'old_value': 3353.54, 'new_value': 3713.45}, {'field': 'onlineCount', 'old_value': 44, 'new_value': 50}]
2025-06-07 08:11:43,522 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:43,991 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMUR
2025-06-07 08:11:43,991 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJ943QH9Q0I86N3H2U1JD001ESJ_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 10788.0, 'new_value': 11387.0}, {'field': 'amount', 'old_value': 10788.0, 'new_value': 11387.0}, {'field': 'count', 'old_value': 17, 'new_value': 19}, {'field': 'instoreAmount', 'old_value': 11586.0, 'new_value': 12185.0}, {'field': 'instoreCount', 'old_value': 17, 'new_value': 19}]
2025-06-07 08:11:43,991 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:44,381 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMVR
2025-06-07 08:11:44,381 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJGUIA2QR0I86N3H2U1JH001ESN_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3238.0, 'new_value': 3755.0}, {'field': 'amount', 'old_value': 3238.0, 'new_value': 3755.0}, {'field': 'count', 'old_value': 6, 'new_value': 8}, {'field': 'instoreAmount', 'old_value': 3238.0, 'new_value': 3755.0}, {'field': 'instoreCount', 'old_value': 6, 'new_value': 8}]
2025-06-07 08:11:44,381 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:44,881 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMWR
2025-06-07 08:11:44,881 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDJP082LC00I86N3H2U1JM001ESS_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7942.599999999999, 'new_value': 8918.8}, {'field': 'amount', 'old_value': 7942.599999999999, 'new_value': 8918.4}, {'field': 'count', 'old_value': 22, 'new_value': 26}, {'field': 'instoreAmount', 'old_value': 7942.599999999999, 'new_value': 8918.8}, {'field': 'instoreCount', 'old_value': 22, 'new_value': 26}]
2025-06-07 08:11:44,881 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:45,272 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMXR
2025-06-07 08:11:45,272 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDOR4RUGJG0I86N3H2U1MC001EVI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 5019.0, 'new_value': 7946.0}, {'field': 'dailyBillAmount', 'old_value': 5019.0, 'new_value': 7946.0}, {'field': 'amount', 'old_value': 5072.0, 'new_value': 7999.0}, {'field': 'count', 'old_value': 16, 'new_value': 20}, {'field': 'instoreAmount', 'old_value': 6707.0, 'new_value': 9634.0}, {'field': 'instoreCount', 'old_value': 16, 'new_value': 20}]
2025-06-07 08:11:45,272 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:45,772 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMYR
2025-06-07 08:11:45,772 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDP34FLR400I86N3H2U1MG001EVM_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 52278.04, 'new_value': 56338.56}, {'field': 'dailyBillAmount', 'old_value': 44994.009999999995, 'new_value': 48747.509999999995}, {'field': 'amount', 'old_value': 50142.99, 'new_value': 53939.45}, {'field': 'count', 'old_value': 279, 'new_value': 301}, {'field': 'instoreAmount', 'old_value': 50143.63, 'new_value': 54837.549999999996}, {'field': 'instoreCount', 'old_value': 279, 'new_value': 301}]
2025-06-07 08:11:45,772 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:46,209 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMZR
2025-06-07 08:11:46,209 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDPJQDDVRC0I86N3H2U1MO001EVU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 29889.0, 'new_value': 30585.0}, {'field': 'amount', 'old_value': 29889.0, 'new_value': 30585.0}, {'field': 'count', 'old_value': 154, 'new_value': 162}, {'field': 'instoreAmount', 'old_value': 30933.0, 'new_value': 31861.0}, {'field': 'instoreCount', 'old_value': 154, 'new_value': 162}]
2025-06-07 08:11:46,209 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:46,662 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM0S
2025-06-07 08:11:46,662 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDQR0OPQVI0I86N3H2U1NE001F0K_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 10280.0, 'new_value': 25330.0}, {'field': 'amount', 'old_value': 10280.0, 'new_value': 25330.0}, {'field': 'count', 'old_value': 3, 'new_value': 6}, {'field': 'instoreAmount', 'old_value': 10280.0, 'new_value': 25330.0}, {'field': 'instoreCount', 'old_value': 3, 'new_value': 6}]
2025-06-07 08:11:46,662 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:47,116 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM1S
2025-06-07 08:11:47,116 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDULBESLOI0I86N3H2U1PE001F2K_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 50069.5, 'new_value': 58951.4}, {'field': 'dailyBillAmount', 'old_value': 50069.5, 'new_value': 58951.4}, {'field': 'amount', 'old_value': 49773.5, 'new_value': 58497.4}, {'field': 'count', 'old_value': 282, 'new_value': 334}, {'field': 'instoreAmount', 'old_value': 49773.5, 'new_value': 58497.4}, {'field': 'instoreCount', 'old_value': 282, 'new_value': 334}]
2025-06-07 08:11:47,116 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:47,584 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM2S
2025-06-07 08:11:47,584 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDUSSKMEM60I86N3H2U1PI001F2O_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 16126.23, 'new_value': 19194.3}, {'field': 'dailyBillAmount', 'old_value': 16126.23, 'new_value': 19194.3}, {'field': 'amount', 'old_value': 9188.73, 'new_value': 10599.17}, {'field': 'count', 'old_value': 815, 'new_value': 947}, {'field': 'instoreAmount', 'old_value': 9465.83, 'new_value': 10908.95}, {'field': 'instoreCount', 'old_value': 815, 'new_value': 947}]
2025-06-07 08:11:47,584 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:48,037 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM3S
2025-06-07 08:11:48,037 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVBILA3OJ0I86N3H2U1PQ001F30_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 118357.17, 'new_value': 133759.59}, {'field': 'dailyBillAmount', 'old_value': 118357.17, 'new_value': 133759.59}, {'field': 'amount', 'old_value': 116428.31, 'new_value': 131627.97}, {'field': 'count', 'old_value': 1274, 'new_value': 1458}, {'field': 'instoreAmount', 'old_value': 97434.5, 'new_value': 108583.90000000001}, {'field': 'instoreCount', 'old_value': 497, 'new_value': 551}, {'field': 'onlineAmount', 'old_value': 19605.2, 'new_value': 23737.96}, {'field': 'onlineCount', 'old_value': 777, 'new_value': 907}]
2025-06-07 08:11:48,037 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:48,569 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM4S
2025-06-07 08:11:48,569 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVIE6UN1G0I86N3H2U1PU001F34_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 41322.2, 'new_value': 45270.9}, {'field': 'amount', 'old_value': 41322.2, 'new_value': 45270.9}, {'field': 'count', 'old_value': 249, 'new_value': 278}, {'field': 'instoreAmount', 'old_value': 41322.2, 'new_value': 45270.9}, {'field': 'instoreCount', 'old_value': 249, 'new_value': 278}]
2025-06-07 08:11:48,569 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:49,084 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM5S
2025-06-07 08:11:49,084 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEDVQHT7NSH0I86N3H2U1Q2001F38_2025-06, 变更字段: [{'field': 'amount', 'old_value': 25946.68, 'new_value': 31228.1}, {'field': 'count', 'old_value': 1067, 'new_value': 1281}, {'field': 'instoreAmount', 'old_value': 7216.22, 'new_value': 8719.380000000001}, {'field': 'instoreCount', 'old_value': 301, 'new_value': 365}, {'field': 'onlineAmount', 'old_value': 19271.71, 'new_value': 23078.41}, {'field': 'onlineCount', 'old_value': 766, 'new_value': 916}]
2025-06-07 08:11:49,084 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:49,616 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM6S
2025-06-07 08:11:49,616 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0A2N9KG60I86N3H2U1QB001F3H_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 15386.35, 'new_value': 18256.04}, {'field': 'dailyBillAmount', 'old_value': 15386.35, 'new_value': 18256.04}, {'field': 'amount', 'old_value': 22437.35, 'new_value': 26805.43}, {'field': 'count', 'old_value': 1119, 'new_value': 1329}, {'field': 'instoreAmount', 'old_value': 12406.14, 'new_value': 14195.33}, {'field': 'instoreCount', 'old_value': 677, 'new_value': 786}, {'field': 'onlineAmount', 'old_value': 10386.82, 'new_value': 13065.22}, {'field': 'onlineCount', 'old_value': 442, 'new_value': 543}]
2025-06-07 08:11:49,616 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:50,116 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM7S
2025-06-07 08:11:50,116 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE0VODGC6J0I86N3H2U1QP001F3V_2025-06, 变更字段: [{'field': 'count', 'old_value': 39, 'new_value': 40}, {'field': 'instoreCount', 'old_value': 39, 'new_value': 40}]
2025-06-07 08:11:50,116 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:50,553 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM8S
2025-06-07 08:11:50,553 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1EFF1UHG0I86N3H2U1R1001F47_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 10227.0, 'new_value': 14302.0}, {'field': 'amount', 'old_value': 10227.0, 'new_value': 14302.0}, {'field': 'count', 'old_value': 6, 'new_value': 9}, {'field': 'instoreAmount', 'old_value': 10227.0, 'new_value': 14302.0}, {'field': 'instoreCount', 'old_value': 6, 'new_value': 9}]
2025-06-07 08:11:50,553 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:51,116 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM9S
2025-06-07 08:11:51,116 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE1T4E94FK0I86N3H2U1R9001F4F_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 22754.3, 'new_value': 26069.3}, {'field': 'dailyBillAmount', 'old_value': 22754.3, 'new_value': 26069.3}, {'field': 'amount', 'old_value': 10730.03, 'new_value': 12846.53}, {'field': 'count', 'old_value': 706, 'new_value': 876}, {'field': 'instoreAmount', 'old_value': 1943.1, 'new_value': 2438.9}, {'field': 'instoreCount', 'old_value': 87, 'new_value': 110}, {'field': 'onlineAmount', 'old_value': 8787.53, 'new_value': 10408.23}, {'field': 'onlineCount', 'old_value': 619, 'new_value': 766}]
2025-06-07 08:11:51,116 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:51,600 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMAS
2025-06-07 08:11:51,600 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE25DAIM3B0I86N3H2U1RD001F4J_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 71771.08, 'new_value': 83711.2}, {'field': 'dailyBillAmount', 'old_value': 71771.08, 'new_value': 83711.2}, {'field': 'amount', 'old_value': 67679.81, 'new_value': 79360.92}, {'field': 'count', 'old_value': 578, 'new_value': 699}, {'field': 'instoreAmount', 'old_value': 55013.22, 'new_value': 64897.43}, {'field': 'instoreCount', 'old_value': 340, 'new_value': 417}, {'field': 'onlineAmount', 'old_value': 12668.6, 'new_value': 14465.5}, {'field': 'onlineCount', 'old_value': 238, 'new_value': 282}]
2025-06-07 08:11:51,600 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:52,006 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMBS
2025-06-07 08:11:52,006 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE2CVHLBFV0I86N3H2U1RH001F4N_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 81699.43000000001, 'new_value': 92639.93000000001}, {'field': 'dailyBillAmount', 'old_value': 81699.43000000001, 'new_value': 92639.93000000001}, {'field': 'amount', 'old_value': 74774.7, 'new_value': 83914.0}, {'field': 'count', 'old_value': 412, 'new_value': 468}, {'field': 'instoreAmount', 'old_value': 75280.2, 'new_value': 84666.5}, {'field': 'instoreCount', 'old_value': 412, 'new_value': 468}]
2025-06-07 08:11:52,006 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:52,506 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMCS
2025-06-07 08:11:52,506 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE39GOJVP40I86N3H2U1RR001F51_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 190784.85, 'new_value': 220674.59}, {'field': 'dailyBillAmount', 'old_value': 190784.85, 'new_value': 220674.59}, {'field': 'amount', 'old_value': 209344.74, 'new_value': 241075.75}, {'field': 'count', 'old_value': 1216, 'new_value': 1412}, {'field': 'instoreAmount', 'old_value': 162654.06, 'new_value': 184976.63999999998}, {'field': 'instoreCount', 'old_value': 655, 'new_value': 747}, {'field': 'onlineAmount', 'old_value': 47262.86, 'new_value': 56989.56}, {'field': 'onlineCount', 'old_value': 561, 'new_value': 665}]
2025-06-07 08:11:52,506 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:53,006 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMDS
2025-06-07 08:11:53,006 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE3PJ2TE9A0I86N3H2U1S3001F59_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 63401.670000000006, 'new_value': 72133.83}, {'field': 'dailyBillAmount', 'old_value': 63401.670000000006, 'new_value': 72133.83}, {'field': 'amount', 'old_value': 87231.7, 'new_value': 100798.92}, {'field': 'count', 'old_value': 417, 'new_value': 498}, {'field': 'instoreAmount', 'old_value': 81065.0, 'new_value': 93830.6}, {'field': 'instoreCount', 'old_value': 319, 'new_value': 381}, {'field': 'onlineAmount', 'old_value': 6412.01, 'new_value': 7213.63}, {'field': 'onlineCount', 'old_value': 98, 'new_value': 117}]
2025-06-07 08:11:53,006 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:53,506 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMES
2025-06-07 08:11:53,506 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE48KDQ6AS0I86N3H2U1SB001F5H_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 75816.42, 'new_value': 87285.78}, {'field': 'dailyBillAmount', 'old_value': 75816.42, 'new_value': 87285.78}, {'field': 'amount', 'old_value': 72284.5, 'new_value': 83163.2}, {'field': 'count', 'old_value': 300, 'new_value': 350}, {'field': 'instoreAmount', 'old_value': 73301.4, 'new_value': 84180.1}, {'field': 'instoreCount', 'old_value': 300, 'new_value': 350}]
2025-06-07 08:11:53,522 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:53,975 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMFS
2025-06-07 08:11:53,975 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE4GES1U770I86N3H2U1SF001F5L_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 162315.93, 'new_value': 189863.55}, {'field': 'amount', 'old_value': 162315.73, 'new_value': 189863.35}, {'field': 'count', 'old_value': 1214, 'new_value': 1439}, {'field': 'instoreAmount', 'old_value': 162315.93, 'new_value': 189863.55}, {'field': 'instoreCount', 'old_value': 1214, 'new_value': 1439}]
2025-06-07 08:11:53,975 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:54,459 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMGS
2025-06-07 08:11:54,459 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE7UKVCV6D0I86N3H2U1U5001F7B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 152827.07, 'new_value': 178486.24}, {'field': 'dailyBillAmount', 'old_value': 152827.07, 'new_value': 178486.24}, {'field': 'amount', 'old_value': 175858.91, 'new_value': 203939.28}, {'field': 'count', 'old_value': 1224, 'new_value': 1430}, {'field': 'instoreAmount', 'old_value': 97882.0, 'new_value': 111598.6}, {'field': 'instoreCount', 'old_value': 503, 'new_value': 585}, {'field': 'onlineAmount', 'old_value': 80141.8, 'new_value': 94941.0}, {'field': 'onlineCount', 'old_value': 721, 'new_value': 845}]
2025-06-07 08:11:54,459 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:54,897 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMHS
2025-06-07 08:11:54,897 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEE85EF5FB90I86N3H2U1U9001F7F_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 88180.28, 'new_value': 108261.96}, {'field': 'dailyBillAmount', 'old_value': 88180.28, 'new_value': 108261.96}, {'field': 'amount', 'old_value': 96963.19, 'new_value': 106836.31}, {'field': 'count', 'old_value': 947, 'new_value': 1071}, {'field': 'instoreAmount', 'old_value': 72858.76, 'new_value': 78941.16}, {'field': 'instoreCount', 'old_value': 466, 'new_value': 511}, {'field': 'onlineAmount', 'old_value': 24445.73, 'new_value': 28260.45}, {'field': 'onlineCount', 'old_value': 481, 'new_value': 560}]
2025-06-07 08:11:54,897 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:55,303 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMIS
2025-06-07 08:11:55,303 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEA4041D6F0I86N3H2U1V9001F8F_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 110407.58, 'new_value': 127001.45}, {'field': 'dailyBillAmount', 'old_value': 110407.58, 'new_value': 127001.45}, {'field': 'amount', 'old_value': 110617.45999999999, 'new_value': 127596.94}, {'field': 'count', 'old_value': 926, 'new_value': 1082}, {'field': 'instoreAmount', 'old_value': 98980.78, 'new_value': 114157.88}, {'field': 'instoreCount', 'old_value': 528, 'new_value': 616}, {'field': 'onlineAmount', 'old_value': 11685.61, 'new_value': 13491.640000000001}, {'field': 'onlineCount', 'old_value': 398, 'new_value': 466}]
2025-06-07 08:11:55,303 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:55,725 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMJS
2025-06-07 08:11:55,725 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAB2RFI0N0I86N3H2U1VD001F8J_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 22022.4, 'new_value': 25682.4}, {'field': 'amount', 'old_value': 22022.4, 'new_value': 25682.4}, {'field': 'count', 'old_value': 115, 'new_value': 131}, {'field': 'instoreAmount', 'old_value': 22022.4, 'new_value': 25682.4}, {'field': 'instoreCount', 'old_value': 115, 'new_value': 131}]
2025-06-07 08:11:55,725 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:56,241 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMKS
2025-06-07 08:11:56,241 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEAIN5DMKK0I86N3H2U1VH001F8N_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 87781.14, 'new_value': 95788.72}, {'field': 'dailyBillAmount', 'old_value': 87781.14, 'new_value': 95788.72}, {'field': 'amount', 'old_value': -58967.28, 'new_value': -66558.7}, {'field': 'count', 'old_value': 172, 'new_value': 198}, {'field': 'instoreAmount', 'old_value': 1670.5, 'new_value': 1768.5}, {'field': 'instoreCount', 'old_value': 67, 'new_value': 75}, {'field': 'onlineAmount', 'old_value': 2768.34, 'new_value': 3210.92}, {'field': 'onlineCount', 'old_value': 105, 'new_value': 123}]
2025-06-07 08:11:56,241 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:56,741 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMLS
2025-06-07 08:11:56,741 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEB9O4F53S0I86N3H2U1VT001F93_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 64007.100000000006, 'new_value': 83217.81}, {'field': 'dailyBillAmount', 'old_value': 64007.100000000006, 'new_value': 83217.81}, {'field': 'amount', 'old_value': 79298.78, 'new_value': 91523.38}, {'field': 'count', 'old_value': 355, 'new_value': 405}, {'field': 'instoreAmount', 'old_value': 79299.54, 'new_value': 91524.14}, {'field': 'instoreCount', 'old_value': 355, 'new_value': 405}]
2025-06-07 08:11:56,741 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:57,240 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMMS
2025-06-07 08:11:57,240 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBHLLHVNF0I86N3H2U102001F98_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 113057.34, 'new_value': 127365.40000000001}, {'field': 'dailyBillAmount', 'old_value': 113057.34, 'new_value': 127365.40000000001}, {'field': 'amount', 'old_value': 36411.9, 'new_value': 38919.9}, {'field': 'count', 'old_value': 149, 'new_value': 161}, {'field': 'instoreAmount', 'old_value': 36925.0, 'new_value': 39445.1}, {'field': 'instoreCount', 'old_value': 143, 'new_value': 153}, {'field': 'onlineAmount', 'old_value': 526.9, 'new_value': 610.8000000000001}, {'field': 'onlineCount', 'old_value': 6, 'new_value': 8}]
2025-06-07 08:11:57,240 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:57,850 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMNS
2025-06-07 08:11:57,850 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEBP5Q03GB0I86N3H2U106001F9C_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 68634.98000000001, 'new_value': 78709.21}, {'field': 'dailyBillAmount', 'old_value': 68634.98000000001, 'new_value': 78709.21}, {'field': 'amount', 'old_value': 67617.34999999999, 'new_value': 77179.2}, {'field': 'count', 'old_value': 388, 'new_value': 453}, {'field': 'instoreAmount', 'old_value': 65281.77, 'new_value': 74295.97}, {'field': 'instoreCount', 'old_value': 323, 'new_value': 367}, {'field': 'onlineAmount', 'old_value': 2335.86, 'new_value': 2914.8}, {'field': 'onlineCount', 'old_value': 65, 'new_value': 86}]
2025-06-07 08:11:57,850 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:58,334 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMOS
2025-06-07 08:11:58,334 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HGCEEC0U8246I0I86N3H2U10A001F9G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 70328.19, 'new_value': 79518.38}, {'field': 'dailyBillAmount', 'old_value': 70328.19, 'new_value': 79518.38}, {'field': 'amount', 'old_value': 30855.47, 'new_value': 34596.97}, {'field': 'count', 'old_value': 436, 'new_value': 486}, {'field': 'instoreAmount', 'old_value': 21758.690000000002, 'new_value': 24308.99}, {'field': 'instoreCount', 'old_value': 143, 'new_value': 153}, {'field': 'onlineAmount', 'old_value': 9098.77, 'new_value': 10289.98}, {'field': 'onlineCount', 'old_value': 293, 'new_value': 333}]
2025-06-07 08:11:58,334 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:58,740 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMPS
2025-06-07 08:11:58,740 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1HO90F90E71NK12I1UUTD5AE7C001O7G_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 28885.65, 'new_value': 34018.81}, {'field': 'amount', 'old_value': 28884.65, 'new_value': 34017.4}, {'field': 'count', 'old_value': 1373, 'new_value': 1634}, {'field': 'instoreAmount', 'old_value': 6903.0, 'new_value': 8491.11}, {'field': 'instoreCount', 'old_value': 296, 'new_value': 356}, {'field': 'onlineAmount', 'old_value': 22479.14, 'new_value': 26192.850000000002}, {'field': 'onlineCount', 'old_value': 1077, 'new_value': 1278}]
2025-06-07 08:11:58,740 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:59,162 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMQS
2025-06-07 08:11:59,162 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRPJDJBMAPL77QBECDAL3H1H001J5I_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 10487.0, 'new_value': 12580.0}, {'field': 'amount', 'old_value': 10487.0, 'new_value': 12580.0}, {'field': 'count', 'old_value': 45, 'new_value': 50}, {'field': 'instoreAmount', 'old_value': 10487.0, 'new_value': 12580.0}, {'field': 'instoreCount', 'old_value': 45, 'new_value': 50}]
2025-06-07 08:11:59,162 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:11:59,600 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMRS
2025-06-07 08:11:59,600 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_1IFRR231GUB4QN7QBECDAL3H28001J69_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 81044.73, 'new_value': 94355.83}, {'field': 'dailyBillAmount', 'old_value': 81044.73, 'new_value': 94355.83}, {'field': 'amount', 'old_value': 34272.0, 'new_value': 38230.6}, {'field': 'count', 'old_value': 618, 'new_value': 706}, {'field': 'instoreAmount', 'old_value': 34508.3, 'new_value': 38466.9}, {'field': 'instoreCount', 'old_value': 618, 'new_value': 706}]
2025-06-07 08:11:59,600 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:00,069 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMTS
2025-06-07 08:12:00,069 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_B5A5FB25D4B04323BCABB528AF5E427E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8770.86, 'new_value': 10043.3}, {'field': 'amount', 'old_value': 8768.98, 'new_value': 10041.42}, {'field': 'count', 'old_value': 482, 'new_value': 551}, {'field': 'instoreAmount', 'old_value': 3688.1, 'new_value': 4009.2999999999997}, {'field': 'instoreCount', 'old_value': 184, 'new_value': 197}, {'field': 'onlineAmount', 'old_value': 5296.27, 'new_value': 6247.51}, {'field': 'onlineCount', 'old_value': 298, 'new_value': 354}]
2025-06-07 08:12:00,069 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:00,569 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMUS
2025-06-07 08:12:00,569 - INFO - 更新月销售记录成功: 1HFLOR99TBR11L6UBHOUTGCK1C001A3F_E214E9E9A4534AE1943BBACB09056E2E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 14477.1, 'new_value': 14836.9}, {'field': 'amount', 'old_value': 14477.1, 'new_value': 14836.9}, {'field': 'count', 'old_value': 38, 'new_value': 40}, {'field': 'instoreAmount', 'old_value': 14477.1, 'new_value': 14836.9}, {'field': 'instoreCount', 'old_value': 38, 'new_value': 40}]
2025-06-07 08:12:00,569 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:01,022 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMVS
2025-06-07 08:12:01,022 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ09TJTBAAB3IKSIOCDI7KQ001FRR_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 38332.12, 'new_value': 43191.23}, {'field': 'dailyBillAmount', 'old_value': 31080.0, 'new_value': 34802.3}, {'field': 'amount', 'old_value': 38331.75, 'new_value': 43190.86}, {'field': 'count', 'old_value': 516, 'new_value': 610}, {'field': 'instoreAmount', 'old_value': 36630.5, 'new_value': 40903.3}, {'field': 'instoreCount', 'old_value': 439, 'new_value': 509}, {'field': 'onlineAmount', 'old_value': 1730.62, 'new_value': 2316.93}, {'field': 'onlineCount', 'old_value': 77, 'new_value': 101}]
2025-06-07 08:12:01,022 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:01,428 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMWS
2025-06-07 08:12:01,428 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0BI1TJFBK3IKSIOCDI7LH001FSI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6601.2, 'new_value': 7475.2}, {'field': 'amount', 'old_value': 6600.86, 'new_value': 7474.86}, {'field': 'count', 'old_value': 292, 'new_value': 332}, {'field': 'instoreAmount', 'old_value': 5718.0, 'new_value': 6481.6}, {'field': 'instoreCount', 'old_value': 264, 'new_value': 300}, {'field': 'onlineAmount', 'old_value': 917.6, 'new_value': 1028.0}, {'field': 'onlineCount', 'old_value': 28, 'new_value': 32}]
2025-06-07 08:12:01,428 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:01,834 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMXS
2025-06-07 08:12:01,834 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0II4K19DA3IKSIOCDI7OQ001FVR_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 86352.83, 'new_value': 96511.39}, {'field': 'dailyBillAmount', 'old_value': 86352.83, 'new_value': 96511.39}, {'field': 'amount', 'old_value': 110730.49, 'new_value': 125387.18000000001}, {'field': 'count', 'old_value': 931, 'new_value': 1116}, {'field': 'instoreAmount', 'old_value': 106117.24, 'new_value': 119839.9}, {'field': 'instoreCount', 'old_value': 656, 'new_value': 771}, {'field': 'onlineAmount', 'old_value': 6638.19, 'new_value': 8287.82}, {'field': 'onlineCount', 'old_value': 275, 'new_value': 345}]
2025-06-07 08:12:01,834 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:02,319 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMYS
2025-06-07 08:12:02,319 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0NLURMV9D3IKSIOCDI7R2001G23_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 23074.260000000002, 'new_value': 26853.74}, {'field': 'dailyBillAmount', 'old_value': 23074.260000000002, 'new_value': 26853.74}, {'field': 'amount', 'old_value': 8842.19, 'new_value': 9790.45}, {'field': 'count', 'old_value': 123, 'new_value': 148}, {'field': 'instoreAmount', 'old_value': 6313.76, 'new_value': 6885.3099999999995}, {'field': 'instoreCount', 'old_value': 80, 'new_value': 96}, {'field': 'onlineAmount', 'old_value': 2560.88, 'new_value': 2943.67}, {'field': 'onlineCount', 'old_value': 43, 'new_value': 52}]
2025-06-07 08:12:02,319 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:02,912 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMZS
2025-06-07 08:12:02,912 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0RRE5KJE33IKSIOCDI7TA001G4B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 24511.870000000003, 'new_value': 29961.550000000003}, {'field': 'dailyBillAmount', 'old_value': 24637.27, 'new_value': 30075.96}, {'field': 'amount', 'old_value': 24511.6, 'new_value': 29960.73}, {'field': 'count', 'old_value': 1345, 'new_value': 1661}, {'field': 'instoreAmount', 'old_value': 11778.98, 'new_value': 14558.519999999999}, {'field': 'instoreCount', 'old_value': 593, 'new_value': 755}, {'field': 'onlineAmount', 'old_value': 12988.240000000002, 'new_value': 15658.380000000001}, {'field': 'onlineCount', 'old_value': 752, 'new_value': 906}]
2025-06-07 08:12:02,912 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:03,428 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM0T
2025-06-07 08:12:03,428 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0T31TMM9K3IKSIOCDI7TQ001G4R_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 14352.73, 'new_value': 16788.21}, {'field': 'amount', 'old_value': 14352.52, 'new_value': 16788.0}, {'field': 'count', 'old_value': 907, 'new_value': 1066}, {'field': 'instoreAmount', 'old_value': 7771.3099999999995, 'new_value': 9092.82}, {'field': 'instoreCount', 'old_value': 423, 'new_value': 506}, {'field': 'onlineAmount', 'old_value': 7544.6900000000005, 'new_value': 8709.45}, {'field': 'onlineCount', 'old_value': 484, 'new_value': 560}]
2025-06-07 08:12:03,428 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:03,881 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM1T
2025-06-07 08:12:03,881 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TAQB69KB3IKSIOCDI7TU001G4V_2025-06, 变更字段: [{'field': 'amount', 'old_value': 38202.479999999996, 'new_value': 43831.88}, {'field': 'count', 'old_value': 385, 'new_value': 446}, {'field': 'instoreAmount', 'old_value': 38215.96, 'new_value': 43845.36}, {'field': 'instoreCount', 'old_value': 385, 'new_value': 446}]
2025-06-07 08:12:03,881 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:04,444 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM2T
2025-06-07 08:12:04,444 - INFO - 更新月销售记录成功: 1HHBU5V9BMJHVK3M2I8SNPGMO8001CEV_1HKQQ0TQNGGEI53IKSIOCDI7U6001G57_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 20016.25, 'new_value': 23312.34}, {'field': 'dailyBillAmount', 'old_value': 20867.0, 'new_value': 24349.34}, {'field': 'amount', 'old_value': 20015.7, 'new_value': 23311.79}, {'field': 'count', 'old_value': 543, 'new_value': 629}, {'field': 'instoreAmount', 'old_value': 18113.64, 'new_value': 21157.74}, {'field': 'instoreCount', 'old_value': 381, 'new_value': 446}, {'field': 'onlineAmount', 'old_value': 1914.21, 'new_value': 2166.2}, {'field': 'onlineCount', 'old_value': 162, 'new_value': 183}]
2025-06-07 08:12:04,444 - WARNING - 时间戳字符串包含非数字字符: 2025-01
2025-06-07 08:12:04,850 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3N4ENXX9MH2
2025-06-07 08:12:04,850 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-01, 变更字段: [{'field': 'shopEntityName', 'old_value': '泡鲜森·新食感汤泡饭', 'new_value': '泡鲜森'}]
2025-06-07 08:12:04,850 - WARNING - 时间戳字符串包含非数字字符: 2025-01
2025-06-07 08:12:05,350 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3N4ENXX9ML2
2025-06-07 08:12:05,350 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_2025-01, 变更字段: [{'field': 'shopEntityName', 'old_value': '茶二·现萃茶', 'new_value': '茶二'}]
2025-06-07 08:12:05,350 - WARNING - 时间戳字符串包含非数字字符: 2025-01
2025-06-07 08:12:05,803 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3N4ENXX9MN2
2025-06-07 08:12:05,803 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_2025-01, 变更字段: [{'field': 'shopEntityName', 'old_value': 'mo labo coffee', 'new_value': '墨拿'}]
2025-06-07 08:12:05,803 - WARNING - 时间戳字符串包含非数字字符: 2025-01
2025-06-07 08:12:06,240 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3N4ENXX9MO2
2025-06-07 08:12:06,240 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_2025-01, 变更字段: [{'field': 'shopEntityName', 'old_value': "Peet's Coffee", 'new_value': '皮爷咖啡'}]
2025-06-07 08:12:06,240 - WARNING - 时间戳字符串包含非数字字符: 2025-01
2025-06-07 08:12:06,694 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3N4ENXX9MP2
2025-06-07 08:12:06,694 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_2025-01, 变更字段: [{'field': 'shopEntityName', 'old_value': '胖哥俩肉蟹煲', 'new_value': '胖哥俩'}]
2025-06-07 08:12:06,694 - WARNING - 时间戳字符串包含非数字字符: 2025-01
2025-06-07 08:12:07,178 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3N4ENXX9MX2
2025-06-07 08:12:07,178 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_2025-01, 变更字段: [{'field': 'shopEntityName', 'old_value': '橘焱胡同烤肉', 'new_value': '橘焱胡同烧肉'}]
2025-06-07 08:12:07,178 - WARNING - 时间戳字符串包含非数字字符: 2025-01
2025-06-07 08:12:07,647 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3N4ENXX9MY2
2025-06-07 08:12:07,647 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_2025-01, 变更字段: [{'field': 'shopEntityName', 'old_value': '多经- 猴新奇零食', 'new_value': '猴新奇'}]
2025-06-07 08:12:07,647 - WARNING - 时间戳字符串包含非数字字符: 2025-01
2025-06-07 08:12:08,100 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3N4ENXX9M03
2025-06-07 08:12:08,100 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_2025-01, 变更字段: [{'field': 'shopEntityName', 'old_value': '酵社面包', 'new_value': '酵社'}]
2025-06-07 08:12:08,100 - WARNING - 时间戳字符串包含非数字字符: 2025-02
2025-06-07 08:12:08,490 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9M83
2025-06-07 08:12:08,490 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-02, 变更字段: [{'field': 'shopEntityName', 'old_value': '泡鲜森·新食感汤泡饭', 'new_value': '泡鲜森'}]
2025-06-07 08:12:08,490 - WARNING - 时间戳字符串包含非数字字符: 2025-02
2025-06-07 08:12:08,912 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9MC3
2025-06-07 08:12:08,912 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_2025-02, 变更字段: [{'field': 'shopEntityName', 'old_value': '茶二·现萃茶', 'new_value': '茶二'}]
2025-06-07 08:12:08,912 - WARNING - 时间戳字符串包含非数字字符: 2025-02
2025-06-07 08:12:09,350 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9ME3
2025-06-07 08:12:09,350 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_2025-02, 变更字段: [{'field': 'shopEntityName', 'old_value': 'mo labo coffee', 'new_value': '墨拿'}]
2025-06-07 08:12:09,350 - WARNING - 时间戳字符串包含非数字字符: 2025-02
2025-06-07 08:12:09,772 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9MF3
2025-06-07 08:12:09,772 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_2025-02, 变更字段: [{'field': 'shopEntityName', 'old_value': "Peet's Coffee", 'new_value': '皮爷咖啡'}]
2025-06-07 08:12:09,772 - WARNING - 时间戳字符串包含非数字字符: 2025-02
2025-06-07 08:12:10,209 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9MG3
2025-06-07 08:12:10,209 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_2025-02, 变更字段: [{'field': 'shopEntityName', 'old_value': '胖哥俩肉蟹煲', 'new_value': '胖哥俩'}]
2025-06-07 08:12:10,209 - WARNING - 时间戳字符串包含非数字字符: 2025-02
2025-06-07 08:12:10,678 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9MO3
2025-06-07 08:12:10,678 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_2025-02, 变更字段: [{'field': 'shopEntityName', 'old_value': '橘焱胡同烤肉', 'new_value': '橘焱胡同烧肉'}]
2025-06-07 08:12:10,678 - WARNING - 时间戳字符串包含非数字字符: 2025-02
2025-06-07 08:12:11,256 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9MP3
2025-06-07 08:12:11,256 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_2025-02, 变更字段: [{'field': 'shopEntityName', 'old_value': '多经- 猴新奇零食', 'new_value': '猴新奇'}]
2025-06-07 08:12:11,256 - WARNING - 时间戳字符串包含非数字字符: 2025-02
2025-06-07 08:12:11,647 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9MR3
2025-06-07 08:12:11,647 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_2025-02, 变更字段: [{'field': 'shopEntityName', 'old_value': '酵社面包', 'new_value': '酵社'}]
2025-06-07 08:12:11,647 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-07 08:12:12,037 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9MZ3
2025-06-07 08:12:12,037 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-03, 变更字段: [{'field': 'shopEntityName', 'old_value': '泡鲜森·新食感汤泡饭', 'new_value': '泡鲜森'}]
2025-06-07 08:12:12,037 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-07 08:12:12,506 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9M34
2025-06-07 08:12:12,506 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_2025-03, 变更字段: [{'field': 'shopEntityName', 'old_value': '茶二·现萃茶', 'new_value': '茶二'}]
2025-06-07 08:12:12,506 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-07 08:12:12,912 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9M54
2025-06-07 08:12:12,912 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_2025-03, 变更字段: [{'field': 'shopEntityName', 'old_value': 'mo labo coffee', 'new_value': '墨拿'}]
2025-06-07 08:12:12,928 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-07 08:12:13,365 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9M64
2025-06-07 08:12:13,365 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_2025-03, 变更字段: [{'field': 'shopEntityName', 'old_value': "Peet's Coffee", 'new_value': '皮爷咖啡'}]
2025-06-07 08:12:13,365 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-07 08:12:13,803 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9M74
2025-06-07 08:12:13,803 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_2025-03, 变更字段: [{'field': 'shopEntityName', 'old_value': '胖哥俩肉蟹煲', 'new_value': '胖哥俩'}]
2025-06-07 08:12:13,803 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-07 08:12:14,334 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9ME4
2025-06-07 08:12:14,334 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_2025-03, 变更字段: [{'field': 'shopEntityName', 'old_value': '橘焱胡同烤肉', 'new_value': '橘焱胡同烧肉'}]
2025-06-07 08:12:14,334 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-07 08:12:14,819 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9MF4
2025-06-07 08:12:14,834 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_2025-03, 变更字段: [{'field': 'shopEntityName', 'old_value': '多经- 猴新奇零食', 'new_value': '猴新奇'}]
2025-06-07 08:12:14,834 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-07 08:12:15,256 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9MH4
2025-06-07 08:12:15,256 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_2025-03, 变更字段: [{'field': 'shopEntityName', 'old_value': '酵社面包', 'new_value': '酵社'}]
2025-06-07 08:12:15,272 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-07 08:12:15,662 - INFO - 更新表单数据成功: FINST-X2F66HC1X1WUEM4X6TBUH8HHVPTA3O4ENXX9MN4
2025-06-07 08:12:15,662 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-04, 变更字段: [{'field': 'shopEntityName', 'old_value': '泡鲜森·新食感汤泡饭', 'new_value': '泡鲜森'}]
2025-06-07 08:12:15,662 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-07 08:12:16,068 - INFO - 更新表单数据成功: FINST-OLF665813NVUOH19CCHMM8B7MMTW2NSGNXX9MRA
2025-06-07 08:12:16,068 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_2025-04, 变更字段: [{'field': 'shopEntityName', 'old_value': '茶二·现萃茶', 'new_value': '茶二'}]
2025-06-07 08:12:16,068 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-07 08:12:16,522 - INFO - 更新表单数据成功: FINST-OLF665813NVUOH19CCHMM8B7MMTW2NSGNXX9MTA
2025-06-07 08:12:16,522 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_2025-04, 变更字段: [{'field': 'shopEntityName', 'old_value': 'mo labo coffee', 'new_value': '墨拿'}]
2025-06-07 08:12:16,522 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-07 08:12:16,975 - INFO - 更新表单数据成功: FINST-OLF665813NVUOH19CCHMM8B7MMTW2NSGNXX9MUA
2025-06-07 08:12:16,975 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_2025-04, 变更字段: [{'field': 'shopEntityName', 'old_value': "Peet's Coffee", 'new_value': '皮爷咖啡'}]
2025-06-07 08:12:16,975 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-07 08:12:17,381 - INFO - 更新表单数据成功: FINST-OLF665813NVUOH19CCHMM8B7MMTW2NSGNXX9MVA
2025-06-07 08:12:17,381 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_2025-04, 变更字段: [{'field': 'shopEntityName', 'old_value': '胖哥俩肉蟹煲', 'new_value': '胖哥俩'}]
2025-06-07 08:12:17,381 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-07 08:12:17,818 - INFO - 更新表单数据成功: FINST-OLF665813NVUOH19CCHMM8B7MMTW2NSGNXX9M2B
2025-06-07 08:12:17,818 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_2025-04, 变更字段: [{'field': 'shopEntityName', 'old_value': '橘焱胡同烤肉', 'new_value': '橘焱胡同烧肉'}]
2025-06-07 08:12:17,818 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-07 08:12:18,287 - INFO - 更新表单数据成功: FINST-OLF665813NVUOH19CCHMM8B7MMTW2NSGNXX9M3B
2025-06-07 08:12:18,287 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_2025-04, 变更字段: [{'field': 'shopEntityName', 'old_value': '多经- 猴新奇零食', 'new_value': '猴新奇'}]
2025-06-07 08:12:18,287 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-07 08:12:18,709 - INFO - 更新表单数据成功: FINST-OLF665813NVUOH19CCHMM8B7MMTW2NSGNXX9M5B
2025-06-07 08:12:18,709 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_2025-04, 变更字段: [{'field': 'shopEntityName', 'old_value': '酵社面包', 'new_value': '酵社'}]
2025-06-07 08:12:18,709 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-07 08:12:19,131 - INFO - 更新表单数据成功: FINST-VRA66VA1DNZUAADJA7KJPA7PQK7Q37OC8W8AM7W
2025-06-07 08:12:19,131 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-05, 变更字段: [{'field': 'shopEntityName', 'old_value': '泡鲜森·新食感汤泡饭', 'new_value': '泡鲜森'}]
2025-06-07 08:12:19,131 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-07 08:12:19,553 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMX81
2025-06-07 08:12:19,553 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_2025-05, 变更字段: [{'field': 'shopEntityName', 'old_value': '茶二·现萃茶', 'new_value': '茶二'}]
2025-06-07 08:12:19,553 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-07 08:12:19,928 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMZ81
2025-06-07 08:12:19,928 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_2025-05, 变更字段: [{'field': 'shopEntityName', 'old_value': 'mo labo coffee', 'new_value': '墨拿'}]
2025-06-07 08:12:19,928 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-07 08:12:20,365 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM091
2025-06-07 08:12:20,365 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_2025-05, 变更字段: [{'field': 'shopEntityName', 'old_value': "Peet's Coffee", 'new_value': '皮爷咖啡'}]
2025-06-07 08:12:20,365 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-07 08:12:20,818 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM191
2025-06-07 08:12:20,818 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_2025-05, 变更字段: [{'field': 'shopEntityName', 'old_value': '胖哥俩肉蟹煲', 'new_value': '胖哥俩'}]
2025-06-07 08:12:20,818 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-07 08:12:21,272 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM691
2025-06-07 08:12:21,272 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 980259.83, 'new_value': 959500.67}, {'field': 'dailyBillAmount', 'old_value': 980259.83, 'new_value': 959500.67}]
2025-06-07 08:12:21,272 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-07 08:12:21,662 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM891
2025-06-07 08:12:21,662 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_2025-05, 变更字段: [{'field': 'shopEntityName', 'old_value': '橘焱胡同烤肉', 'new_value': '橘焱胡同烧肉'}]
2025-06-07 08:12:21,662 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-07 08:12:22,100 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AM991
2025-06-07 08:12:22,100 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_2025-05, 变更字段: [{'field': 'shopEntityName', 'old_value': '多经- 猴新奇零食', 'new_value': '猴新奇'}]
2025-06-07 08:12:22,100 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-07 08:12:22,506 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMB91
2025-06-07 08:12:22,506 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_2025-05, 变更字段: [{'field': 'shopEntityName', 'old_value': '酵社面包', 'new_value': '酵社'}]
2025-06-07 08:12:22,506 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:22,975 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM3T
2025-06-07 08:12:22,975 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_14EB204A0BDE44888B43308269C1626A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 40318.22, 'new_value': 51224.53}, {'field': 'dailyBillAmount', 'old_value': 40318.22, 'new_value': 51224.53}, {'field': 'amount', 'old_value': 5488.59, 'new_value': 6635.97}, {'field': 'count', 'old_value': 201, 'new_value': 235}, {'field': 'instoreAmount', 'old_value': 6110.860000000001, 'new_value': 7348.400000000001}, {'field': 'instoreCount', 'old_value': 201, 'new_value': 235}]
2025-06-07 08:12:22,975 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:23,365 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM4T
2025-06-07 08:12:23,365 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIP3GSKTFR6E7AERKQ83J2001UN4_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 106751.03, 'new_value': 126949.43}, {'field': 'dailyBillAmount', 'old_value': 106751.03, 'new_value': 126949.43}, {'field': 'amount', 'old_value': 10310.8, 'new_value': 11721.6}, {'field': 'count', 'old_value': 57, 'new_value': 65}, {'field': 'instoreAmount', 'old_value': 10310.8, 'new_value': 11721.6}, {'field': 'instoreCount', 'old_value': 57, 'new_value': 65}]
2025-06-07 08:12:23,365 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:23,818 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM5T
2025-06-07 08:12:23,818 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPC0VTE5M6E7AERKQ83J5001UN7_2025-06, 变更字段: [{'field': 'amount', 'old_value': 3667.66, 'new_value': 4698.360000000001}, {'field': 'count', 'old_value': 185, 'new_value': 236}, {'field': 'onlineAmount', 'old_value': 3691.34, 'new_value': 4741.22}, {'field': 'onlineCount', 'old_value': 185, 'new_value': 236}]
2025-06-07 08:12:23,818 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:24,240 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM6T
2025-06-07 08:12:24,240 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPGJC2SUF6E7AERKQ83J8001UNA_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 70015.61, 'new_value': 87534.20999999999}, {'field': 'amount', 'old_value': 70014.7, 'new_value': 87533.3}, {'field': 'count', 'old_value': 730, 'new_value': 914}, {'field': 'instoreAmount', 'old_value': 64795.3, 'new_value': 81850.70000000001}, {'field': 'instoreCount', 'old_value': 579, 'new_value': 723}, {'field': 'onlineAmount', 'old_value': 5523.55, 'new_value': 6409.25}, {'field': 'onlineCount', 'old_value': 151, 'new_value': 191}]
2025-06-07 08:12:24,240 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:24,647 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM7T
2025-06-07 08:12:24,647 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPK0KE3MN6E7AERKQ83JB001UND_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 33633.55, 'new_value': 42841.54}, {'field': 'dailyBillAmount', 'old_value': 30016.350000000002, 'new_value': 39224.340000000004}, {'field': 'amount', 'old_value': 24048.760000000002, 'new_value': 30461.16}, {'field': 'count', 'old_value': 723, 'new_value': 928}, {'field': 'instoreAmount', 'old_value': 5063.68, 'new_value': 6021.88}, {'field': 'instoreCount', 'old_value': 109, 'new_value': 139}, {'field': 'onlineAmount', 'old_value': 19105.239999999998, 'new_value': 24559.44}, {'field': 'onlineCount', 'old_value': 614, 'new_value': 789}]
2025-06-07 08:12:24,647 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:25,100 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM8T
2025-06-07 08:12:25,100 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPOL2K5B16E7AERKQ83JE001UNG_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 22647.91, 'new_value': 28651.84}, {'field': 'dailyBillAmount', 'old_value': 22647.91, 'new_value': 28651.84}, {'field': 'amount', 'old_value': 1484.2, 'new_value': 1720.3700000000001}, {'field': 'count', 'old_value': 54, 'new_value': 65}, {'field': 'instoreAmount', 'old_value': 1484.5, 'new_value': 1720.67}, {'field': 'instoreCount', 'old_value': 54, 'new_value': 65}, {'field': 'shopEntityName', 'old_value': '泡鲜森·新食感汤泡饭', 'new_value': '泡鲜森'}]
2025-06-07 08:12:25,100 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:25,553 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBM9T
2025-06-07 08:12:25,553 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIPTG3IHNP7AV8LHQQGICT001EJ6_2025-06, 变更字段: [{'field': 'amount', 'old_value': 1434.3200000000002, 'new_value': 1652.41}, {'field': 'count', 'old_value': 69, 'new_value': 79}, {'field': 'onlineAmount', 'old_value': 1488.39, 'new_value': 1706.48}, {'field': 'onlineCount', 'old_value': 69, 'new_value': 79}]
2025-06-07 08:12:25,553 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:26,037 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMAT
2025-06-07 08:12:26,037 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQ716B84L7AV8LHQQGID0001EJ9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 19081.11, 'new_value': 23134.72}, {'field': 'dailyBillAmount', 'old_value': 10212.32, 'new_value': 12032.12}, {'field': 'amount', 'old_value': 19080.22, 'new_value': 23133.83}, {'field': 'count', 'old_value': 501, 'new_value': 597}, {'field': 'instoreAmount', 'old_value': 11075.69, 'new_value': 12908.79}, {'field': 'instoreCount', 'old_value': 280, 'new_value': 325}, {'field': 'onlineAmount', 'old_value': 8416.86, 'new_value': 10637.369999999999}, {'field': 'onlineCount', 'old_value': 221, 'new_value': 272}]
2025-06-07 08:12:26,037 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:26,522 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMBT
2025-06-07 08:12:26,522 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQG1BD1C36E7AERKQ83JN001UNP_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 26476.37, 'new_value': 34727.32}, {'field': 'amount', 'old_value': 26476.1, 'new_value': 34727.049999999996}, {'field': 'count', 'old_value': 1225, 'new_value': 1611}, {'field': 'instoreAmount', 'old_value': 26831.81, 'new_value': 35303.13}, {'field': 'instoreCount', 'old_value': 1225, 'new_value': 1611}]
2025-06-07 08:12:26,522 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:26,959 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMCT
2025-06-07 08:12:26,959 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQP8527T06E7AERKQ83JQ001UNS_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 10971.58, 'new_value': 14303.49}, {'field': 'dailyBillAmount', 'old_value': 10971.58, 'new_value': 14303.49}, {'field': 'amount', 'old_value': 6608.53, 'new_value': 9171.76}, {'field': 'count', 'old_value': 323, 'new_value': 437}, {'field': 'instoreAmount', 'old_value': 2401.93, 'new_value': 3552.29}, {'field': 'instoreCount', 'old_value': 88, 'new_value': 122}, {'field': 'onlineAmount', 'old_value': 4207.55, 'new_value': 5620.42}, {'field': 'onlineCount', 'old_value': 235, 'new_value': 315}, {'field': 'shopEntityName', 'old_value': '茶二·现萃茶', 'new_value': '茶二'}]
2025-06-07 08:12:26,959 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:27,381 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMDT
2025-06-07 08:12:27,381 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIQTRHD5AN6E7AERKQ83JT001UNV_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 12432.97, 'new_value': 15426.64}, {'field': 'amount', 'old_value': 12432.949999999999, 'new_value': 15426.619999999999}, {'field': 'count', 'old_value': 420, 'new_value': 514}, {'field': 'instoreAmount', 'old_value': 6208.32, 'new_value': 7465.76}, {'field': 'instoreCount', 'old_value': 272, 'new_value': 324}, {'field': 'onlineAmount', 'old_value': 6224.65, 'new_value': 7960.88}, {'field': 'onlineCount', 'old_value': 148, 'new_value': 190}]
2025-06-07 08:12:27,381 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:27,803 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMET
2025-06-07 08:12:27,803 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR2ETOHPO6E7AERKQ83K0001UO2_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8497.49, 'new_value': 10322.630000000001}, {'field': 'amount', 'old_value': 8496.65, 'new_value': 10321.79}, {'field': 'count', 'old_value': 212, 'new_value': 262}, {'field': 'instoreAmount', 'old_value': 7252.7, 'new_value': 8233.0}, {'field': 'instoreCount', 'old_value': 184, 'new_value': 220}, {'field': 'onlineAmount', 'old_value': 1504.79, 'new_value': 2349.63}, {'field': 'onlineCount', 'old_value': 28, 'new_value': 42}, {'field': 'shopEntityName', 'old_value': 'mo labo coffee', 'new_value': '墨拿'}]
2025-06-07 08:12:27,803 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:28,287 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMFT
2025-06-07 08:12:28,287 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIR7B0BL957AV8LHQQGID6001EJF_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 45156.0, 'new_value': 56385.61}, {'field': 'dailyBillAmount', 'old_value': 45156.0, 'new_value': 56385.61}, {'field': 'amount', 'old_value': 29816.629999999997, 'new_value': 37431.5}, {'field': 'count', 'old_value': 747, 'new_value': 930}, {'field': 'instoreAmount', 'old_value': 19018.120000000003, 'new_value': 24092.120000000003}, {'field': 'instoreCount', 'old_value': 394, 'new_value': 489}, {'field': 'onlineAmount', 'old_value': 13483.78, 'new_value': 17120.63}, {'field': 'onlineCount', 'old_value': 353, 'new_value': 441}, {'field': 'shopEntityName', 'old_value': "Peet's Coffee", 'new_value': '皮爷咖啡'}]
2025-06-07 08:12:28,287 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:28,803 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMGT
2025-06-07 08:12:28,803 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRBVE2CQT7AV8LHQQGID9001EJI_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 156281.35, 'new_value': 187677.26}, {'field': 'dailyBillAmount', 'old_value': 156281.35, 'new_value': 187677.26}, {'field': 'amount', 'old_value': 118529.9, 'new_value': 138848.6}, {'field': 'count', 'old_value': 782, 'new_value': 927}, {'field': 'instoreAmount', 'old_value': 89385.8, 'new_value': 102454.5}, {'field': 'instoreCount', 'old_value': 645, 'new_value': 756}, {'field': 'onlineAmount', 'old_value': 29144.899999999998, 'new_value': 36394.9}, {'field': 'onlineCount', 'old_value': 137, 'new_value': 171}, {'field': 'shopEntityName', 'old_value': '胖哥俩肉蟹煲', 'new_value': '胖哥俩'}]
2025-06-07 08:12:28,803 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:29,240 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMHT
2025-06-07 08:12:29,240 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRGKS2JA27AV8LHQQGIDC001EJL_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 202808.36000000002, 'new_value': 247799.82}, {'field': 'amount', 'old_value': 202808.06, 'new_value': 247799.52}, {'field': 'count', 'old_value': 702, 'new_value': 848}, {'field': 'instoreAmount', 'old_value': 202665.36000000002, 'new_value': 247656.82}, {'field': 'instoreCount', 'old_value': 701, 'new_value': 847}]
2025-06-07 08:12:29,240 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:29,678 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMIT
2025-06-07 08:12:29,678 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRL782OM57AV8LHQQGIDF001EJO_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 100908.63, 'new_value': 126020.59}, {'field': 'dailyBillAmount', 'old_value': 89180.22, 'new_value': 112164.58}, {'field': 'amount', 'old_value': 100907.89, 'new_value': 126019.85}, {'field': 'count', 'old_value': 680, 'new_value': 831}, {'field': 'instoreAmount', 'old_value': 90801.0, 'new_value': 114149.51}, {'field': 'instoreCount', 'old_value': 395, 'new_value': 485}, {'field': 'onlineAmount', 'old_value': 10201.52, 'new_value': 11964.970000000001}, {'field': 'onlineCount', 'old_value': 285, 'new_value': 346}]
2025-06-07 08:12:29,678 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:30,100 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMJT
2025-06-07 08:12:30,100 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIRUG3QSLE7AV8LHQQGIDI001EJR_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 69420.43, 'new_value': 84849.39}, {'field': 'dailyBillAmount', 'old_value': 55603.34, 'new_value': 70935.0}, {'field': 'amount', 'old_value': 69420.43, 'new_value': 84849.39}, {'field': 'count', 'old_value': 212, 'new_value': 266}, {'field': 'instoreAmount', 'old_value': 62764.4, 'new_value': 77150.8}, {'field': 'instoreCount', 'old_value': 159, 'new_value': 204}, {'field': 'onlineAmount', 'old_value': 6822.860000000001, 'new_value': 7865.42}, {'field': 'onlineCount', 'old_value': 53, 'new_value': 62}]
2025-06-07 08:12:30,100 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:30,537 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMKT
2025-06-07 08:12:30,537 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS34G4B697AV8LHQQGIDL001EJU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 137207.53, 'new_value': 168712.03}, {'field': 'amount', 'old_value': 137206.58, 'new_value': 168711.08}, {'field': 'count', 'old_value': 863, 'new_value': 1065}, {'field': 'instoreAmount', 'old_value': 124471.19, 'new_value': 153617.19}, {'field': 'instoreCount', 'old_value': 453, 'new_value': 564}, {'field': 'onlineAmount', 'old_value': 12736.55, 'new_value': 15095.05}, {'field': 'onlineCount', 'old_value': 410, 'new_value': 501}]
2025-06-07 08:12:30,537 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:31,022 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMLT
2025-06-07 08:12:31,022 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUIS7OTCALF7AV8LHQQGIDO001EK1_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 138030.02, 'new_value': 169745.63}, {'field': 'dailyBillAmount', 'old_value': 138030.02, 'new_value': 169745.63}, {'field': 'amount', 'old_value': 126966.1, 'new_value': 155821.19}, {'field': 'count', 'old_value': 662, 'new_value': 820}, {'field': 'instoreAmount', 'old_value': 114597.04, 'new_value': 140971.63999999998}, {'field': 'instoreCount', 'old_value': 535, 'new_value': 661}, {'field': 'onlineAmount', 'old_value': 12629.220000000001, 'new_value': 15193.51}, {'field': 'onlineCount', 'old_value': 127, 'new_value': 159}]
2025-06-07 08:12:31,022 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:31,490 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMMT
2025-06-07 08:12:31,490 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISLI3BD9P7AV8LHQQGIDR001EK4_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 36973.8, 'new_value': 39415.8}, {'field': 'dailyBillAmount', 'old_value': 36973.8, 'new_value': 39415.8}, {'field': 'amount', 'old_value': 36126.0, 'new_value': 38466.0}, {'field': 'count', 'old_value': 60, 'new_value': 66}, {'field': 'instoreAmount', 'old_value': 36126.0, 'new_value': 38466.0}, {'field': 'instoreCount', 'old_value': 60, 'new_value': 66}]
2025-06-07 08:12:31,490 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:31,943 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMNT
2025-06-07 08:12:31,943 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_1HRKUISOVAPU1P7AV8LHQQGIDU001EK7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 24146.48, 'new_value': 30092.35}, {'field': 'dailyBillAmount', 'old_value': 24146.48, 'new_value': 30092.35}, {'field': 'amount', 'old_value': 22681.36, 'new_value': 26296.07}, {'field': 'count', 'old_value': 28, 'new_value': 32}, {'field': 'instoreAmount', 'old_value': 25744.25, 'new_value': 29094.95}, {'field': 'instoreCount', 'old_value': 26, 'new_value': 29}, {'field': 'onlineAmount', 'old_value': 322.8, 'new_value': 586.81}, {'field': 'onlineCount', 'old_value': 2, 'new_value': 3}, {'field': 'shopEntityName', 'old_value': '橘焱胡同烤肉', 'new_value': '橘焱胡同烧肉'}]
2025-06-07 08:12:31,943 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:32,381 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMOT
2025-06-07 08:12:32,381 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_41D3E4ED4CEA49C09C36DE504B997534_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 3795.79, 'new_value': 4614.86}, {'field': 'amount', 'old_value': 3795.55, 'new_value': 4614.62}, {'field': 'count', 'old_value': 73, 'new_value': 96}, {'field': 'instoreAmount', 'old_value': 3795.79, 'new_value': 4624.64}, {'field': 'instoreCount', 'old_value': 73, 'new_value': 96}, {'field': 'shopEntityName', 'old_value': '多经- 猴新奇零食', 'new_value': '猴新奇'}]
2025-06-07 08:12:32,381 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:32,818 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMPT
2025-06-07 08:12:32,818 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_C207460918D74AAAB2E154B47B74F863_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 12429.94, 'new_value': 15960.59}, {'field': 'amount', 'old_value': 12428.95, 'new_value': 15959.6}, {'field': 'count', 'old_value': 119, 'new_value': 153}, {'field': 'instoreAmount', 'old_value': 12429.94, 'new_value': 16276.75}, {'field': 'instoreCount', 'old_value': 119, 'new_value': 153}]
2025-06-07 08:12:32,818 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:33,287 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMQT
2025-06-07 08:12:33,287 - INFO - 更新月销售记录成功: 1HRIS0CDPQR2GM6E7AERKQ83MS0014QU_E79F261889C1492982227C207062C267_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 68419.4, 'new_value': 85518.11}, {'field': 'dailyBillAmount', 'old_value': 68419.4, 'new_value': 85518.11}, {'field': 'amount', 'old_value': 71195.22, 'new_value': 88384.79000000001}, {'field': 'count', 'old_value': 1969, 'new_value': 2449}, {'field': 'instoreAmount', 'old_value': 66770.85, 'new_value': 82231.92}, {'field': 'instoreCount', 'old_value': 1714, 'new_value': 2111}, {'field': 'onlineAmount', 'old_value': 5494.79, 'new_value': 7362.42}, {'field': 'onlineCount', 'old_value': 255, 'new_value': 338}, {'field': 'shopEntityName', 'old_value': '酵社面包', 'new_value': '酵社'}]
2025-06-07 08:12:33,287 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:33,818 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMRT
2025-06-07 08:12:33,818 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD0GVFB5C86AJB6QM8HA590011O7_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 125605.4, 'new_value': 163522.36}, {'field': 'dailyBillAmount', 'old_value': 125605.4, 'new_value': 163522.36}, {'field': 'amount', 'old_value': 116201.62, 'new_value': 152407.04}, {'field': 'count', 'old_value': 350, 'new_value': 417}, {'field': 'instoreAmount', 'old_value': 119902.84, 'new_value': 156836.88}, {'field': 'instoreCount', 'old_value': 287, 'new_value': 340}, {'field': 'onlineAmount', 'old_value': 1824.83, 'new_value': 2048.57}, {'field': 'onlineCount', 'old_value': 63, 'new_value': 77}]
2025-06-07 08:12:33,818 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:34,334 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMST
2025-06-07 08:12:34,334 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRD2F2MCTBD6AJB6QM8HA650011P3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 211570.69, 'new_value': 259764.49}, {'field': 'amount', 'old_value': 211570.64, 'new_value': 259764.44}, {'field': 'count', 'old_value': 698, 'new_value': 849}, {'field': 'instoreAmount', 'old_value': 213515.69, 'new_value': 261709.49}, {'field': 'instoreCount', 'old_value': 698, 'new_value': 849}]
2025-06-07 08:12:34,334 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:34,787 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH27HQ3LEBMTT
2025-06-07 08:12:34,787 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE1OIPJJ7N6AJB6QM8HA7A0011Q8_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 181766.95, 'new_value': 227492.46}, {'field': 'dailyBillAmount', 'old_value': 181766.95, 'new_value': 227492.46}, {'field': 'amount', 'old_value': 134844.12, 'new_value': 171017.86}, {'field': 'count', 'old_value': 527, 'new_value': 634}, {'field': 'instoreAmount', 'old_value': 133716.41, 'new_value': 169495.22}, {'field': 'instoreCount', 'old_value': 312, 'new_value': 379}, {'field': 'onlineAmount', 'old_value': 6863.3, 'new_value': 7860.4}, {'field': 'onlineCount', 'old_value': 215, 'new_value': 255}]
2025-06-07 08:12:34,787 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:35,209 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMUT
2025-06-07 08:12:35,209 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE29O5UH0D6AJB6QM8HA7I0011QG_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 339190.76, 'new_value': 417465.54}, {'field': 'dailyBillAmount', 'old_value': 339190.76, 'new_value': 417465.54}, {'field': 'amount', 'old_value': 277493.0, 'new_value': 352641.0}, {'field': 'count', 'old_value': 643, 'new_value': 822}, {'field': 'instoreAmount', 'old_value': 293490.0, 'new_value': 369749.0}, {'field': 'instoreCount', 'old_value': 643, 'new_value': 822}]
2025-06-07 08:12:35,209 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:35,646 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMVT
2025-06-07 08:12:35,646 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2HKMNBLO6AJB6QM8HA7M0011QK_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 55814.54, 'new_value': 69285.55}, {'field': 'dailyBillAmount', 'old_value': 55814.54, 'new_value': 69285.55}, {'field': 'amount', 'old_value': 55341.2, 'new_value': 68812.21}, {'field': 'count', 'old_value': 290, 'new_value': 352}, {'field': 'instoreAmount', 'old_value': 52442.1, 'new_value': 65204.9}, {'field': 'instoreCount', 'old_value': 240, 'new_value': 291}, {'field': 'onlineAmount', 'old_value': 3523.17, 'new_value': 4231.38}, {'field': 'onlineCount', 'old_value': 50, 'new_value': 61}]
2025-06-07 08:12:35,646 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:36,146 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMWT
2025-06-07 08:12:36,146 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE2PLB45826AJB6QM8HA7Q0011QO_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 152365.43, 'new_value': 196144.88}, {'field': 'dailyBillAmount', 'old_value': 152365.43, 'new_value': 196144.88}, {'field': 'amount', 'old_value': 188627.3, 'new_value': 232406.75}, {'field': 'count', 'old_value': 831, 'new_value': 1016}, {'field': 'instoreAmount', 'old_value': 188627.35, 'new_value': 232406.8}, {'field': 'instoreCount', 'old_value': 831, 'new_value': 1016}]
2025-06-07 08:12:36,146 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:36,568 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMXT
2025-06-07 08:12:36,568 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE38FF3LOL6AJB6QM8HA820011R0_2025-06, 变更字段: [{'field': 'amount', 'old_value': 107559.4, 'new_value': 132863.34}, {'field': 'count', 'old_value': 185, 'new_value': 233}, {'field': 'instoreAmount', 'old_value': 106334.8, 'new_value': 131308.04}, {'field': 'instoreCount', 'old_value': 177, 'new_value': 223}, {'field': 'onlineAmount', 'old_value': 1225.8999999999999, 'new_value': 1556.6}, {'field': 'onlineCount', 'old_value': 8, 'new_value': 10}]
2025-06-07 08:12:36,568 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:37,053 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMYT
2025-06-07 08:12:37,053 - INFO - 更新月销售记录成功: 1HRIS7255PESAA7AV8LHQQGIH8001KNH_1HRKRE3ODLGMBD6AJB6QM8HA8A0011R8_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 52862.59, 'new_value': 59843.03}, {'field': 'dailyBillAmount', 'old_value': 52862.59, 'new_value': 59843.03}, {'field': 'amount', 'old_value': 57130.3, 'new_value': 64256.3}, {'field': 'count', 'old_value': 380, 'new_value': 447}, {'field': 'instoreAmount', 'old_value': 57296.3, 'new_value': 64778.3}, {'field': 'instoreCount', 'old_value': 380, 'new_value': 447}]
2025-06-07 08:12:37,053 - WARNING - 时间戳字符串包含非数字字符: 2025-01
2025-06-07 08:12:37,506 - INFO - 更新表单数据成功: FINST-OLF665813NVUOH19CCHMM8B7MMTW2OSGNXX9MLC
2025-06-07 08:12:37,506 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_2025-01, 变更字段: [{'field': 'shopEntityName', 'old_value': '恰饭叔', 'new_value': '恰饭英雄'}]
2025-06-07 08:12:37,506 - WARNING - 时间戳字符串包含非数字字符: 2025-02
2025-06-07 08:12:38,021 - INFO - 更新表单数据成功: FINST-OLF665813NVUOH19CCHMM8B7MMTW2PSGNXX9MCD
2025-06-07 08:12:38,021 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_2025-02, 变更字段: [{'field': 'shopEntityName', 'old_value': '恰饭叔', 'new_value': '恰饭英雄'}]
2025-06-07 08:12:38,021 - WARNING - 时间戳字符串包含非数字字符: 2025-03
2025-06-07 08:12:38,475 - INFO - 更新表单数据成功: FINST-X0G66U8100WULBDP7CXOPDDF85T830DJNXX9MS4
2025-06-07 08:12:38,475 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_2025-03, 变更字段: [{'field': 'shopEntityName', 'old_value': '恰饭叔', 'new_value': '恰饭英雄'}]
2025-06-07 08:12:38,475 - WARNING - 时间戳字符串包含非数字字符: 2025-04
2025-06-07 08:12:38,943 - INFO - 更新表单数据成功: FINST-X0G66U8100WULBDP7CXOPDDF85T831DJNXX9MI5
2025-06-07 08:12:38,943 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_2025-04, 变更字段: [{'field': 'shopEntityName', 'old_value': '恰饭叔', 'new_value': '恰饭英雄'}]
2025-06-07 08:12:38,943 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-07 08:12:39,428 - INFO - 更新表单数据成功: FINST-6IF66PC16U6V68R0BB26F8EED84D2Z20F1GAMDC
2025-06-07 08:12:39,428 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_101F34500A0D43DF833463DEFB95F423_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 229320.55, 'new_value': 290908.58}, {'field': 'dailyBillAmount', 'old_value': 229320.55, 'new_value': 290908.58}]
2025-06-07 08:12:39,428 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-07 08:12:39,881 - INFO - 更新表单数据成功: FINST-ZNE66RC1GZZUHK8WCK0PLCRJYNWR3P99D16AMT91
2025-06-07 08:12:39,881 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_2025-05, 变更字段: [{'field': 'shopEntityName', 'old_value': '恰饭叔', 'new_value': '恰饭英雄'}]
2025-06-07 08:12:39,881 - WARNING - 时间戳字符串包含非数字字符: 2025-05
2025-06-07 08:12:40,412 - INFO - 更新表单数据成功: FINST-7PF66H71T11VMHODDNVEXD1NY9DL23QBD16AMW7
2025-06-07 08:12:40,412 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERRPAJGP042F6DB81RHB6001P7E_2025-05, 变更字段: [{'field': 'recommendAmount', 'old_value': 80926.1, 'new_value': 99073.24}, {'field': 'dailyBillAmount', 'old_value': 80926.1, 'new_value': 99073.24}]
2025-06-07 08:12:40,428 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:40,865 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMZT
2025-06-07 08:12:40,865 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_101F34500A0D43DF833463DEFB95F423_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 50805.76, 'new_value': 57159.020000000004}, {'field': 'dailyBillAmount', 'old_value': 50805.76, 'new_value': 57159.020000000004}, {'field': 'amount', 'old_value': 39971.2, 'new_value': 46087.2}, {'field': 'count', 'old_value': 246, 'new_value': 288}, {'field': 'instoreAmount', 'old_value': 41406.0, 'new_value': 47559.0}, {'field': 'instoreCount', 'old_value': 240, 'new_value': 281}, {'field': 'onlineAmount', 'old_value': 362.2, 'new_value': 386.2}, {'field': 'onlineCount', 'old_value': 6, 'new_value': 7}]
2025-06-07 08:12:40,865 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:41,287 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM0U
2025-06-07 08:12:41,287 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_17590B062D954DB088AC6EE572EFECE9_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 4612.2, 'new_value': 6414.1}, {'field': 'dailyBillAmount', 'old_value': 4612.2, 'new_value': 6414.1}]
2025-06-07 08:12:41,287 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:41,803 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM1U
2025-06-07 08:12:41,803 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJ92AGQAT42F6DB81RH6G001P2O_2025-06, 变更字段: [{'field': 'amount', 'old_value': 17492.59, 'new_value': 21395.73}, {'field': 'count', 'old_value': 973, 'new_value': 1173}, {'field': 'instoreAmount', 'old_value': 2400.82, 'new_value': 2974.26}, {'field': 'instoreCount', 'old_value': 219, 'new_value': 258}, {'field': 'onlineAmount', 'old_value': 16009.0, 'new_value': 19427.6}, {'field': 'onlineCount', 'old_value': 754, 'new_value': 915}]
2025-06-07 08:12:41,803 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:42,271 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM2U
2025-06-07 08:12:42,271 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEJQAOTB2542F6DB81RH6O001P30_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 37846.32, 'new_value': 50071.6}, {'field': 'amount', 'old_value': 37844.24, 'new_value': 50069.58}, {'field': 'count', 'old_value': 727, 'new_value': 928}, {'field': 'instoreAmount', 'old_value': 32964.13, 'new_value': 43245.39}, {'field': 'instoreCount', 'old_value': 614, 'new_value': 782}, {'field': 'onlineAmount', 'old_value': 4882.19, 'new_value': 6826.21}, {'field': 'onlineCount', 'old_value': 113, 'new_value': 146}]
2025-06-07 08:12:42,271 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:42,865 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM3U
2025-06-07 08:12:42,865 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKLLRRBCQ42F6DB81RH73001P3B_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 7350.0, 'new_value': 8429.6}, {'field': 'amount', 'old_value': 7349.6, 'new_value': 8429.2}, {'field': 'count', 'old_value': 43, 'new_value': 52}, {'field': 'instoreAmount', 'old_value': 7350.0, 'new_value': 8429.6}, {'field': 'instoreCount', 'old_value': 43, 'new_value': 52}]
2025-06-07 08:12:42,865 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:43,318 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM4U
2025-06-07 08:12:43,318 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEKSIE6DGB42F6DB81RH77001P3F_2025-06, 变更字段: [{'field': 'amount', 'old_value': 6105.6, 'new_value': 7064.8}, {'field': 'count', 'old_value': 58, 'new_value': 66}, {'field': 'instoreAmount', 'old_value': 6273.9, 'new_value': 7233.1}, {'field': 'instoreCount', 'old_value': 58, 'new_value': 66}]
2025-06-07 08:12:43,318 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:43,865 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM5U
2025-06-07 08:12:43,865 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEM2RU37BD42F6DB81RH7L001P3T_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8888.0, 'new_value': 10135.0}, {'field': 'dailyBillAmount', 'old_value': 8888.0, 'new_value': 10135.0}]
2025-06-07 08:12:43,865 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:44,318 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM6U
2025-06-07 08:12:44,318 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMFCSIPU442F6DB81RH7T001P45_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 45807.5, 'new_value': 53421.6}, {'field': 'dailyBillAmount', 'old_value': 45807.5, 'new_value': 53421.6}, {'field': 'amount', 'old_value': 33056.31, 'new_value': 39473.62}, {'field': 'count', 'old_value': 1029, 'new_value': 1280}, {'field': 'instoreAmount', 'old_value': 31059.82, 'new_value': 37089.23}, {'field': 'instoreCount', 'old_value': 971, 'new_value': 1213}, {'field': 'onlineAmount', 'old_value': 2218.88, 'new_value': 2606.7799999999997}, {'field': 'onlineCount', 'old_value': 58, 'new_value': 67}]
2025-06-07 08:12:44,318 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:44,740 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM7U
2025-06-07 08:12:44,740 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEMMCI75GD42F6DB81RH81001P49_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 9486.8, 'new_value': 10501.3}, {'field': 'dailyBillAmount', 'old_value': 9486.8, 'new_value': 10501.3}, {'field': 'amount', 'old_value': 8985.9, 'new_value': 10000.4}, {'field': 'count', 'old_value': 57, 'new_value': 64}, {'field': 'instoreAmount', 'old_value': 9514.3, 'new_value': 10528.8}, {'field': 'instoreCount', 'old_value': 57, 'new_value': 64}]
2025-06-07 08:12:44,740 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:45,209 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM8U
2025-06-07 08:12:45,209 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO2283NPC42F6DB81RH8Q001P52_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 12630.92, 'new_value': 15915.83}, {'field': 'dailyBillAmount', 'old_value': 12630.92, 'new_value': 15915.83}]
2025-06-07 08:12:45,209 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:45,662 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBM9U
2025-06-07 08:12:45,662 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEO8LGKTTH42F6DB81RH8V001P57_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 10415.949999999999, 'new_value': 12316.619999999999}, {'field': 'amount', 'old_value': 10414.54, 'new_value': 12314.900000000001}, {'field': 'count', 'old_value': 628, 'new_value': 743}, {'field': 'instoreAmount', 'old_value': 10493.16, 'new_value': 12393.83}, {'field': 'instoreCount', 'old_value': 628, 'new_value': 743}, {'field': 'shopEntityName', 'old_value': '恰饭叔', 'new_value': '恰饭英雄'}]
2025-06-07 08:12:45,662 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:46,146 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMAU
2025-06-07 08:12:46,146 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEOTOKMGDD42F6DB81RH9E001P5M_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 14438.2, 'new_value': 18286.87}, {'field': 'dailyBillAmount', 'old_value': 14438.2, 'new_value': 18286.87}, {'field': 'amount', 'old_value': 14630.369999999999, 'new_value': 18658.14}, {'field': 'count', 'old_value': 735, 'new_value': 943}, {'field': 'instoreAmount', 'old_value': 13427.5, 'new_value': 17149.6}, {'field': 'instoreCount', 'old_value': 667, 'new_value': 856}, {'field': 'onlineAmount', 'old_value': 1280.67, 'new_value': 1676.93}, {'field': 'onlineCount', 'old_value': 68, 'new_value': 87}]
2025-06-07 08:12:46,146 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:46,553 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMBU
2025-06-07 08:12:46,553 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEPVAL5TUK42F6DB81RHA2001P6A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 10982.27, 'new_value': 12997.710000000001}, {'field': 'amount', 'old_value': 10982.12, 'new_value': 12997.560000000001}, {'field': 'count', 'old_value': 514, 'new_value': 621}, {'field': 'instoreAmount', 'old_value': 6460.01, 'new_value': 7553.45}, {'field': 'instoreCount', 'old_value': 320, 'new_value': 382}, {'field': 'onlineAmount', 'old_value': 4560.08, 'new_value': 5482.08}, {'field': 'onlineCount', 'old_value': 194, 'new_value': 239}]
2025-06-07 08:12:46,553 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:46,928 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMCU
2025-06-07 08:12:46,928 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQ5682UCT42F6DB81RHA6001P6E_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 8480.34, 'new_value': 10337.99}, {'field': 'dailyBillAmount', 'old_value': 8480.34, 'new_value': 10337.99}, {'field': 'amount', 'old_value': 5849.71, 'new_value': 7098.01}, {'field': 'count', 'old_value': 226, 'new_value': 276}, {'field': 'instoreAmount', 'old_value': 5963.85, 'new_value': 7212.15}, {'field': 'instoreCount', 'old_value': 226, 'new_value': 276}]
2025-06-07 08:12:46,928 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:47,412 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMDU
2025-06-07 08:12:47,412 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGEQIR94JOQ42F6DB81RHAE001P6M_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 11831.92, 'new_value': 15269.1}, {'field': 'amount', 'old_value': 11831.4, 'new_value': 15268.17}, {'field': 'count', 'old_value': 730, 'new_value': 921}, {'field': 'instoreAmount', 'old_value': 2881.95, 'new_value': 3666.0099999999998}, {'field': 'instoreCount', 'old_value': 144, 'new_value': 178}, {'field': 'onlineAmount', 'old_value': 9229.84, 'new_value': 11992.36}, {'field': 'onlineCount', 'old_value': 586, 'new_value': 743}]
2025-06-07 08:12:47,412 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:47,928 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMEU
2025-06-07 08:12:47,928 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERF6AAOTL42F6DB81RHAU001P76_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 22517.73, 'new_value': 28344.95}, {'field': 'dailyBillAmount', 'old_value': 22517.73, 'new_value': 28344.95}, {'field': 'amount', 'old_value': 17508.7, 'new_value': 22106.5}, {'field': 'count', 'old_value': 149, 'new_value': 197}, {'field': 'instoreAmount', 'old_value': 17509.2, 'new_value': 22107.0}, {'field': 'instoreCount', 'old_value': 149, 'new_value': 197}]
2025-06-07 08:12:47,928 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:48,412 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMFU
2025-06-07 08:12:48,412 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERL0RJ5BM42F6DB81RHB2001P7A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 25353.8, 'new_value': 29915.8}, {'field': 'dailyBillAmount', 'old_value': 25353.8, 'new_value': 29915.8}, {'field': 'amount', 'old_value': 30025.0, 'new_value': 35431.0}, {'field': 'count', 'old_value': 118, 'new_value': 143}, {'field': 'instoreAmount', 'old_value': 30025.0, 'new_value': 35431.0}, {'field': 'instoreCount', 'old_value': 118, 'new_value': 143}]
2025-06-07 08:12:48,412 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:48,850 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMGU
2025-06-07 08:12:48,850 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGERRPAJGP042F6DB81RHB6001P7E_2025-06, 变更字段: [{'field': 'amount', 'old_value': 10994.0, 'new_value': 12166.0}, {'field': 'count', 'old_value': 58, 'new_value': 63}, {'field': 'instoreAmount', 'old_value': 10994.0, 'new_value': 12166.0}, {'field': 'instoreCount', 'old_value': 58, 'new_value': 63}]
2025-06-07 08:12:48,865 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:49,334 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMHU
2025-06-07 08:12:49,350 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1HTVGETHMN6B8P42F6DB81RHC2001P8A_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 21088.0, 'new_value': 25160.0}, {'field': 'amount', 'old_value': 21088.0, 'new_value': 25160.0}, {'field': 'count', 'old_value': 243, 'new_value': 298}, {'field': 'instoreAmount', 'old_value': 21088.0, 'new_value': 25160.0}, {'field': 'instoreCount', 'old_value': 243, 'new_value': 298}]
2025-06-07 08:12:49,350 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:49,850 - INFO - 更新表单数据成功: FINST-2K666OB12FVV7Y4XEP7HJ6BF2PJH28HQ3LEBMIU
2025-06-07 08:12:49,850 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_1I16GTCIOI81KU0UR9LEHSI4JM001PFU_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 4772.98, 'new_value': 6203.45}, {'field': 'dailyBillAmount', 'old_value': 4772.98, 'new_value': 6203.45}, {'field': 'amount', 'old_value': 466.09999999999997, 'new_value': 527.29}, {'field': 'count', 'old_value': 16, 'new_value': 19}, {'field': 'instoreAmount', 'old_value': 677.4, 'new_value': 748.49}, {'field': 'instoreCount', 'old_value': 16, 'new_value': 19}]
2025-06-07 08:12:49,850 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:50,303 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMMW
2025-06-07 08:12:50,303 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6D1F9FC749FA44C6B70CA818C3E7FB77_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 6356.0, 'new_value': 8569.0}, {'field': 'dailyBillAmount', 'old_value': 6356.0, 'new_value': 8569.0}, {'field': 'amount', 'old_value': 8591.0, 'new_value': 10804.0}, {'field': 'count', 'old_value': 43, 'new_value': 53}, {'field': 'instoreAmount', 'old_value': 8591.0, 'new_value': 10804.0}, {'field': 'instoreCount', 'old_value': 43, 'new_value': 53}]
2025-06-07 08:12:50,303 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:50,740 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMNW
2025-06-07 08:12:50,740 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_6FEB527E4B354363BD1420A3FF0FB3E3_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 14594.98, 'new_value': 18077.59}, {'field': 'dailyBillAmount', 'old_value': 14594.98, 'new_value': 18077.59}, {'field': 'amount', 'old_value': 11307.34, 'new_value': 14371.74}, {'field': 'count', 'old_value': 362, 'new_value': 458}, {'field': 'instoreAmount', 'old_value': 11050.87, 'new_value': 14097.37}, {'field': 'instoreCount', 'old_value': 350, 'new_value': 445}, {'field': 'onlineAmount', 'old_value': 256.9, 'new_value': 274.8}, {'field': 'onlineCount', 'old_value': 12, 'new_value': 13}]
2025-06-07 08:12:50,756 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:51,225 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMOW
2025-06-07 08:12:51,225 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_7987833E6DE549FCBAC0AAF7A1D27E61_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 16268.84, 'new_value': 21615.21}, {'field': 'dailyBillAmount', 'old_value': 16268.84, 'new_value': 21615.21}, {'field': 'amount', 'old_value': 15930.900000000001, 'new_value': 21229.4}, {'field': 'count', 'old_value': 86, 'new_value': 110}, {'field': 'instoreAmount', 'old_value': 15871.1, 'new_value': 21051.3}, {'field': 'instoreCount', 'old_value': 82, 'new_value': 102}, {'field': 'onlineAmount', 'old_value': 140.6, 'new_value': 258.9}, {'field': 'onlineCount', 'old_value': 4, 'new_value': 8}]
2025-06-07 08:12:51,225 - WARNING - 时间戳字符串包含非数字字符: 2025-06
2025-06-07 08:12:51,709 - INFO - 更新表单数据成功: FINST-90D66XA18IUV8AOA99CKX9UBI82G31XS3LEBMPW
2025-06-07 08:12:51,709 - INFO - 更新月销售记录成功: 1HSJMRFR3MBRV37AV8LHQQGIT4001C3D_A48FB8F8F66644F59454F3E73DFCEB92_2025-06, 变更字段: [{'field': 'recommendAmount', 'old_value': 37901.85, 'new_value': 45131.45}, {'field': 'dailyBillAmount', 'old_value': 37901.85, 'new_value': 45131.45}, {'field': 'amount', 'old_value': 40169.3, 'new_value': 47619.3}, {'field': 'count', 'old_value': 270, 'new_value': 313}, {'field': 'instoreAmount', 'old_value': 38383.0, 'new_value': 45484.0}, {'field': 'instoreCount', 'old_value': 234, 'new_value': 271}, {'field': 'onlineAmount', 'old_value': 1818.3, 'new_value': 2167.3}, {'field': 'onlineCount', 'old_value': 36, 'new_value': 42}]
2025-06-07 08:12:51,709 - INFO - 月销售数据同步完成！更新: 257 条，插入: 0 条，错误: 0 条，跳过: 1145 条
2025-06-07 08:12:51,709 - INFO - 开始导出月度汇总数据到Excel，时间范围: 2024-7 至 2025-6
2025-06-07 08:12:52,365 - INFO - 月度汇总数据已导出到Excel文件: logs/数衍平台月度数据_20250607.xlsx
2025-06-07 08:12:52,365 - INFO - 综合数据同步流程完成！
2025-06-07 08:12:52,428 - INFO - 综合数据同步完成
2025-06-07 08:12:52,428 - INFO - ==================================================
2025-06-07 08:12:52,428 - INFO - 程序退出
2025-06-07 08:12:52,428 - INFO - ==================================================
