2025-05-22 00:00:01,894 - INFO - =================使用默认全量同步=============
2025-05-22 00:00:03,316 - INFO - MySQL查询成功，共获取 3297 条记录
2025-05-22 00:00:03,316 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-22 00:00:03,331 - INFO - 开始处理日期: 2025-01
2025-05-22 00:00:03,347 - INFO - Request Parameters - Page 1:
2025-05-22 00:00:03,347 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 00:00:03,347 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 00:00:04,675 - INFO - Response - Page 1:
2025-05-22 00:00:04,878 - INFO - 第 1 页获取到 100 条记录
2025-05-22 00:00:04,878 - INFO - Request Parameters - Page 2:
2025-05-22 00:00:04,878 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 00:00:04,878 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 00:00:05,456 - INFO - Response - Page 2:
2025-05-22 00:00:05,659 - INFO - 第 2 页获取到 100 条记录
2025-05-22 00:00:05,659 - INFO - Request Parameters - Page 3:
2025-05-22 00:00:05,659 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 00:00:05,659 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 00:00:06,159 - INFO - Response - Page 3:
2025-05-22 00:00:06,362 - INFO - 第 3 页获取到 100 条记录
2025-05-22 00:00:06,362 - INFO - Request Parameters - Page 4:
2025-05-22 00:00:06,362 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 00:00:06,362 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 00:00:06,894 - INFO - Response - Page 4:
2025-05-22 00:00:07,097 - INFO - 第 4 页获取到 100 条记录
2025-05-22 00:00:07,097 - INFO - Request Parameters - Page 5:
2025-05-22 00:00:07,097 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 00:00:07,097 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 00:00:07,565 - INFO - Response - Page 5:
2025-05-22 00:00:07,768 - INFO - 第 5 页获取到 100 条记录
2025-05-22 00:00:07,768 - INFO - Request Parameters - Page 6:
2025-05-22 00:00:07,768 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 00:00:07,768 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 00:00:08,315 - INFO - Response - Page 6:
2025-05-22 00:00:08,518 - INFO - 第 6 页获取到 100 条记录
2025-05-22 00:00:08,518 - INFO - Request Parameters - Page 7:
2025-05-22 00:00:08,518 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 00:00:08,518 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 00:00:08,956 - INFO - Response - Page 7:
2025-05-22 00:00:09,159 - INFO - 第 7 页获取到 82 条记录
2025-05-22 00:00:09,159 - INFO - 查询完成，共获取到 682 条记录
2025-05-22 00:00:09,159 - INFO - 获取到 682 条表单数据
2025-05-22 00:00:09,159 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-22 00:00:09,175 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-22 00:00:09,175 - INFO - 开始处理日期: 2025-02
2025-05-22 00:00:09,175 - INFO - Request Parameters - Page 1:
2025-05-22 00:00:09,175 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 00:00:09,175 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 00:00:09,721 - INFO - Response - Page 1:
2025-05-22 00:00:09,925 - INFO - 第 1 页获取到 100 条记录
2025-05-22 00:00:09,925 - INFO - Request Parameters - Page 2:
2025-05-22 00:00:09,925 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 00:00:09,925 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 00:00:10,362 - INFO - Response - Page 2:
2025-05-22 00:00:10,565 - INFO - 第 2 页获取到 100 条记录
2025-05-22 00:00:10,565 - INFO - Request Parameters - Page 3:
2025-05-22 00:00:10,565 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 00:00:10,565 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 00:00:11,034 - INFO - Response - Page 3:
2025-05-22 00:00:11,237 - INFO - 第 3 页获取到 100 条记录
2025-05-22 00:00:11,237 - INFO - Request Parameters - Page 4:
2025-05-22 00:00:11,237 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 00:00:11,237 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 00:00:11,721 - INFO - Response - Page 4:
2025-05-22 00:00:11,924 - INFO - 第 4 页获取到 100 条记录
2025-05-22 00:00:11,924 - INFO - Request Parameters - Page 5:
2025-05-22 00:00:11,924 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 00:00:11,924 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 00:00:12,393 - INFO - Response - Page 5:
2025-05-22 00:00:12,596 - INFO - 第 5 页获取到 100 条记录
2025-05-22 00:00:12,596 - INFO - Request Parameters - Page 6:
2025-05-22 00:00:12,596 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 00:00:12,596 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 00:00:13,096 - INFO - Response - Page 6:
2025-05-22 00:00:13,299 - INFO - 第 6 页获取到 100 条记录
2025-05-22 00:00:13,299 - INFO - Request Parameters - Page 7:
2025-05-22 00:00:13,299 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 00:00:13,299 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 00:00:13,862 - INFO - Response - Page 7:
2025-05-22 00:00:14,065 - INFO - 第 7 页获取到 70 条记录
2025-05-22 00:00:14,065 - INFO - 查询完成，共获取到 670 条记录
2025-05-22 00:00:14,065 - INFO - 获取到 670 条表单数据
2025-05-22 00:00:14,065 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-22 00:00:14,081 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-22 00:00:14,081 - INFO - 开始处理日期: 2025-03
2025-05-22 00:00:14,081 - INFO - Request Parameters - Page 1:
2025-05-22 00:00:14,081 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 00:00:14,081 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 00:00:14,659 - INFO - Response - Page 1:
2025-05-22 00:00:14,862 - INFO - 第 1 页获取到 100 条记录
2025-05-22 00:00:14,862 - INFO - Request Parameters - Page 2:
2025-05-22 00:00:14,862 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 00:00:14,862 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 00:00:15,346 - INFO - Response - Page 2:
2025-05-22 00:00:15,549 - INFO - 第 2 页获取到 100 条记录
2025-05-22 00:00:15,549 - INFO - Request Parameters - Page 3:
2025-05-22 00:00:15,549 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 00:00:15,549 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 00:00:16,002 - INFO - Response - Page 3:
2025-05-22 00:00:16,205 - INFO - 第 3 页获取到 100 条记录
2025-05-22 00:00:16,205 - INFO - Request Parameters - Page 4:
2025-05-22 00:00:16,205 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 00:00:16,205 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 00:00:16,690 - INFO - Response - Page 4:
2025-05-22 00:00:16,893 - INFO - 第 4 页获取到 100 条记录
2025-05-22 00:00:16,893 - INFO - Request Parameters - Page 5:
2025-05-22 00:00:16,893 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 00:00:16,893 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 00:00:17,330 - INFO - Response - Page 5:
2025-05-22 00:00:17,533 - INFO - 第 5 页获取到 100 条记录
2025-05-22 00:00:17,533 - INFO - Request Parameters - Page 6:
2025-05-22 00:00:17,533 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 00:00:17,533 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 00:00:17,971 - INFO - Response - Page 6:
2025-05-22 00:00:18,174 - INFO - 第 6 页获取到 100 条记录
2025-05-22 00:00:18,174 - INFO - Request Parameters - Page 7:
2025-05-22 00:00:18,174 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 00:00:18,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 00:00:18,565 - INFO - Response - Page 7:
2025-05-22 00:00:18,768 - INFO - 第 7 页获取到 61 条记录
2025-05-22 00:00:18,768 - INFO - 查询完成，共获取到 661 条记录
2025-05-22 00:00:18,768 - INFO - 获取到 661 条表单数据
2025-05-22 00:00:18,768 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-22 00:00:18,783 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-22 00:00:18,783 - INFO - 开始处理日期: 2025-04
2025-05-22 00:00:18,783 - INFO - Request Parameters - Page 1:
2025-05-22 00:00:18,783 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 00:00:18,783 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 00:00:19,440 - INFO - Response - Page 1:
2025-05-22 00:00:19,643 - INFO - 第 1 页获取到 100 条记录
2025-05-22 00:00:19,643 - INFO - Request Parameters - Page 2:
2025-05-22 00:00:19,643 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 00:00:19,643 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 00:00:20,236 - INFO - Response - Page 2:
2025-05-22 00:00:20,440 - INFO - 第 2 页获取到 100 条记录
2025-05-22 00:00:20,440 - INFO - Request Parameters - Page 3:
2025-05-22 00:00:20,440 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 00:00:20,440 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 00:00:20,846 - INFO - Response - Page 3:
2025-05-22 00:00:21,049 - INFO - 第 3 页获取到 100 条记录
2025-05-22 00:00:21,049 - INFO - Request Parameters - Page 4:
2025-05-22 00:00:21,049 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 00:00:21,049 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 00:00:21,752 - INFO - Response - Page 4:
2025-05-22 00:00:21,955 - INFO - 第 4 页获取到 100 条记录
2025-05-22 00:00:21,955 - INFO - Request Parameters - Page 5:
2025-05-22 00:00:21,955 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 00:00:21,955 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 00:00:22,424 - INFO - Response - Page 5:
2025-05-22 00:00:22,627 - INFO - 第 5 页获取到 100 条记录
2025-05-22 00:00:22,627 - INFO - Request Parameters - Page 6:
2025-05-22 00:00:22,627 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 00:00:22,627 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 00:00:23,080 - INFO - Response - Page 6:
2025-05-22 00:00:23,283 - INFO - 第 6 页获取到 100 条记录
2025-05-22 00:00:23,283 - INFO - Request Parameters - Page 7:
2025-05-22 00:00:23,283 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 00:00:23,283 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 00:00:23,705 - INFO - Response - Page 7:
2025-05-22 00:00:23,908 - INFO - 第 7 页获取到 56 条记录
2025-05-22 00:00:23,908 - INFO - 查询完成，共获取到 656 条记录
2025-05-22 00:00:23,908 - INFO - 获取到 656 条表单数据
2025-05-22 00:00:23,908 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-22 00:00:23,924 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-22 00:00:23,924 - INFO - 开始处理日期: 2025-05
2025-05-22 00:00:23,924 - INFO - Request Parameters - Page 1:
2025-05-22 00:00:23,924 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 00:00:23,924 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 00:00:24,392 - INFO - Response - Page 1:
2025-05-22 00:00:24,596 - INFO - 第 1 页获取到 100 条记录
2025-05-22 00:00:24,596 - INFO - Request Parameters - Page 2:
2025-05-22 00:00:24,596 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 00:00:24,596 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 00:00:25,158 - INFO - Response - Page 2:
2025-05-22 00:00:25,361 - INFO - 第 2 页获取到 100 条记录
2025-05-22 00:00:25,361 - INFO - Request Parameters - Page 3:
2025-05-22 00:00:25,361 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 00:00:25,361 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 00:00:25,970 - INFO - Response - Page 3:
2025-05-22 00:00:26,174 - INFO - 第 3 页获取到 100 条记录
2025-05-22 00:00:26,174 - INFO - Request Parameters - Page 4:
2025-05-22 00:00:26,174 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 00:00:26,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 00:00:26,627 - INFO - Response - Page 4:
2025-05-22 00:00:26,830 - INFO - 第 4 页获取到 100 条记录
2025-05-22 00:00:26,830 - INFO - Request Parameters - Page 5:
2025-05-22 00:00:26,830 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 00:00:26,830 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 00:00:27,392 - INFO - Response - Page 5:
2025-05-22 00:00:27,595 - INFO - 第 5 页获取到 100 条记录
2025-05-22 00:00:27,595 - INFO - Request Parameters - Page 6:
2025-05-22 00:00:27,595 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 00:00:27,595 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 00:00:28,048 - INFO - Response - Page 6:
2025-05-22 00:00:28,252 - INFO - 第 6 页获取到 100 条记录
2025-05-22 00:00:28,252 - INFO - Request Parameters - Page 7:
2025-05-22 00:00:28,252 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 00:00:28,252 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 00:00:28,642 - INFO - Response - Page 7:
2025-05-22 00:00:28,845 - INFO - 第 7 页获取到 28 条记录
2025-05-22 00:00:28,845 - INFO - 查询完成，共获取到 628 条记录
2025-05-22 00:00:28,845 - INFO - 获取到 628 条表单数据
2025-05-22 00:00:28,845 - INFO - 当前日期 2025-05 有 628 条MySQL数据需要处理
2025-05-22 00:00:28,845 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-22 00:00:29,283 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHC
2025-05-22 00:00:29,283 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 97282.0, 'new_value': 100763.0}, {'field': 'offline_amount', 'old_value': 104848.28, 'new_value': 111682.28}, {'field': 'total_amount', 'old_value': 202130.28, 'new_value': 212445.28}, {'field': 'order_count', 'old_value': 4316, 'new_value': 4550}]
2025-05-22 00:00:29,283 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-22 00:00:29,798 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBD
2025-05-22 00:00:29,798 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22257.2, 'new_value': 23331.2}, {'field': 'offline_amount', 'old_value': 128335.54, 'new_value': 133148.44}, {'field': 'total_amount', 'old_value': 150592.74, 'new_value': 156479.64}, {'field': 'order_count', 'old_value': 211, 'new_value': 219}]
2025-05-22 00:00:29,798 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-22 00:00:30,220 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTD
2025-05-22 00:00:30,236 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42991.76, 'new_value': 44296.01}, {'field': 'offline_amount', 'old_value': 560632.59, 'new_value': 588100.21}, {'field': 'total_amount', 'old_value': 603624.35, 'new_value': 632396.22}, {'field': 'order_count', 'old_value': 2556, 'new_value': 2658}]
2025-05-22 00:00:30,236 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-22 00:00:30,658 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJL
2025-05-22 00:00:30,658 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1442849.38, 'new_value': 1509288.58}, {'field': 'total_amount', 'old_value': 1442849.38, 'new_value': 1509288.58}, {'field': 'order_count', 'old_value': 11728, 'new_value': 12290}]
2025-05-22 00:00:30,658 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-22 00:00:31,126 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQM
2025-05-22 00:00:31,126 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1400.0, 'new_value': 1650.0}, {'field': 'offline_amount', 'old_value': 18808.63, 'new_value': 19569.08}, {'field': 'total_amount', 'old_value': 20208.63, 'new_value': 21219.08}, {'field': 'order_count', 'old_value': 387, 'new_value': 407}]
2025-05-22 00:00:31,142 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-22 00:00:31,579 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMZD
2025-05-22 00:00:31,579 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 595829.9, 'new_value': 621050.95}, {'field': 'total_amount', 'old_value': 595829.9, 'new_value': 621050.95}, {'field': 'order_count', 'old_value': 4131, 'new_value': 4312}]
2025-05-22 00:00:31,579 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-22 00:00:31,986 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7F
2025-05-22 00:00:31,986 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 166961.33, 'new_value': 174584.87}, {'field': 'offline_amount', 'old_value': 311946.92, 'new_value': 326946.92}, {'field': 'total_amount', 'old_value': 478908.25, 'new_value': 501531.79}, {'field': 'order_count', 'old_value': 1166, 'new_value': 1226}]
2025-05-22 00:00:31,986 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-22 00:00:32,439 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCG
2025-05-22 00:00:32,439 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 262868.0, 'new_value': 269530.0}, {'field': 'total_amount', 'old_value': 267592.0, 'new_value': 274254.0}, {'field': 'order_count', 'old_value': 77, 'new_value': 80}]
2025-05-22 00:00:32,439 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-05-22 00:00:32,892 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAMK2
2025-05-22 00:00:32,892 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4047.22, 'new_value': 4202.23}, {'field': 'offline_amount', 'old_value': 33094.86, 'new_value': 36233.83}, {'field': 'total_amount', 'old_value': 37142.08, 'new_value': 40436.06}, {'field': 'order_count', 'old_value': 1472, 'new_value': 1629}]
2025-05-22 00:00:32,892 - INFO - 日期 2025-05 处理完成 - 更新: 9 条，插入: 0 条，错误: 0 条
2025-05-22 00:00:32,892 - INFO - 数据同步完成！更新: 9 条，插入: 0 条，错误: 0 条
2025-05-22 00:00:32,892 - INFO - =================同步完成====================
2025-05-22 03:00:01,872 - INFO - =================使用默认全量同步=============
2025-05-22 03:00:03,279 - INFO - MySQL查询成功，共获取 3297 条记录
2025-05-22 03:00:03,279 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-22 03:00:03,310 - INFO - 开始处理日期: 2025-01
2025-05-22 03:00:03,310 - INFO - Request Parameters - Page 1:
2025-05-22 03:00:03,310 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 03:00:03,310 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 03:00:04,435 - INFO - Response - Page 1:
2025-05-22 03:00:04,638 - INFO - 第 1 页获取到 100 条记录
2025-05-22 03:00:04,638 - INFO - Request Parameters - Page 2:
2025-05-22 03:00:04,638 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 03:00:04,638 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 03:00:05,450 - INFO - Response - Page 2:
2025-05-22 03:00:05,654 - INFO - 第 2 页获取到 100 条记录
2025-05-22 03:00:05,654 - INFO - Request Parameters - Page 3:
2025-05-22 03:00:05,654 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 03:00:05,654 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 03:00:06,107 - INFO - Response - Page 3:
2025-05-22 03:00:06,325 - INFO - 第 3 页获取到 100 条记录
2025-05-22 03:00:06,325 - INFO - Request Parameters - Page 4:
2025-05-22 03:00:06,325 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 03:00:06,325 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 03:00:06,950 - INFO - Response - Page 4:
2025-05-22 03:00:07,153 - INFO - 第 4 页获取到 100 条记录
2025-05-22 03:00:07,153 - INFO - Request Parameters - Page 5:
2025-05-22 03:00:07,153 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 03:00:07,153 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 03:00:07,622 - INFO - Response - Page 5:
2025-05-22 03:00:07,825 - INFO - 第 5 页获取到 100 条记录
2025-05-22 03:00:07,825 - INFO - Request Parameters - Page 6:
2025-05-22 03:00:07,825 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 03:00:07,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 03:00:08,278 - INFO - Response - Page 6:
2025-05-22 03:00:08,481 - INFO - 第 6 页获取到 100 条记录
2025-05-22 03:00:08,481 - INFO - Request Parameters - Page 7:
2025-05-22 03:00:08,481 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 03:00:08,481 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 03:00:08,981 - INFO - Response - Page 7:
2025-05-22 03:00:09,185 - INFO - 第 7 页获取到 82 条记录
2025-05-22 03:00:09,185 - INFO - 查询完成，共获取到 682 条记录
2025-05-22 03:00:09,185 - INFO - 获取到 682 条表单数据
2025-05-22 03:00:09,185 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-22 03:00:09,200 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-22 03:00:09,200 - INFO - 开始处理日期: 2025-02
2025-05-22 03:00:09,200 - INFO - Request Parameters - Page 1:
2025-05-22 03:00:09,200 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 03:00:09,200 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 03:00:09,747 - INFO - Response - Page 1:
2025-05-22 03:00:09,950 - INFO - 第 1 页获取到 100 条记录
2025-05-22 03:00:09,950 - INFO - Request Parameters - Page 2:
2025-05-22 03:00:09,950 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 03:00:09,950 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 03:00:10,403 - INFO - Response - Page 2:
2025-05-22 03:00:10,606 - INFO - 第 2 页获取到 100 条记录
2025-05-22 03:00:10,606 - INFO - Request Parameters - Page 3:
2025-05-22 03:00:10,606 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 03:00:10,606 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 03:00:11,075 - INFO - Response - Page 3:
2025-05-22 03:00:11,278 - INFO - 第 3 页获取到 100 条记录
2025-05-22 03:00:11,278 - INFO - Request Parameters - Page 4:
2025-05-22 03:00:11,278 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 03:00:11,278 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 03:00:11,794 - INFO - Response - Page 4:
2025-05-22 03:00:11,997 - INFO - 第 4 页获取到 100 条记录
2025-05-22 03:00:11,997 - INFO - Request Parameters - Page 5:
2025-05-22 03:00:11,997 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 03:00:11,997 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 03:00:12,497 - INFO - Response - Page 5:
2025-05-22 03:00:12,700 - INFO - 第 5 页获取到 100 条记录
2025-05-22 03:00:12,700 - INFO - Request Parameters - Page 6:
2025-05-22 03:00:12,700 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 03:00:12,700 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 03:00:13,216 - INFO - Response - Page 6:
2025-05-22 03:00:13,419 - INFO - 第 6 页获取到 100 条记录
2025-05-22 03:00:13,419 - INFO - Request Parameters - Page 7:
2025-05-22 03:00:13,419 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 03:00:13,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 03:00:14,012 - INFO - Response - Page 7:
2025-05-22 03:00:14,215 - INFO - 第 7 页获取到 70 条记录
2025-05-22 03:00:14,215 - INFO - 查询完成，共获取到 670 条记录
2025-05-22 03:00:14,215 - INFO - 获取到 670 条表单数据
2025-05-22 03:00:14,215 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-22 03:00:14,231 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-22 03:00:14,231 - INFO - 开始处理日期: 2025-03
2025-05-22 03:00:14,231 - INFO - Request Parameters - Page 1:
2025-05-22 03:00:14,231 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 03:00:14,231 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 03:00:14,684 - INFO - Response - Page 1:
2025-05-22 03:00:14,887 - INFO - 第 1 页获取到 100 条记录
2025-05-22 03:00:14,887 - INFO - Request Parameters - Page 2:
2025-05-22 03:00:14,887 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 03:00:14,887 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 03:00:15,356 - INFO - Response - Page 2:
2025-05-22 03:00:15,559 - INFO - 第 2 页获取到 100 条记录
2025-05-22 03:00:15,559 - INFO - Request Parameters - Page 3:
2025-05-22 03:00:15,559 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 03:00:15,559 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 03:00:16,043 - INFO - Response - Page 3:
2025-05-22 03:00:16,247 - INFO - 第 3 页获取到 100 条记录
2025-05-22 03:00:16,247 - INFO - Request Parameters - Page 4:
2025-05-22 03:00:16,247 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 03:00:16,247 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 03:00:16,747 - INFO - Response - Page 4:
2025-05-22 03:00:16,950 - INFO - 第 4 页获取到 100 条记录
2025-05-22 03:00:16,950 - INFO - Request Parameters - Page 5:
2025-05-22 03:00:16,950 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 03:00:16,950 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 03:00:17,528 - INFO - Response - Page 5:
2025-05-22 03:00:17,731 - INFO - 第 5 页获取到 100 条记录
2025-05-22 03:00:17,731 - INFO - Request Parameters - Page 6:
2025-05-22 03:00:17,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 03:00:17,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 03:00:18,231 - INFO - Response - Page 6:
2025-05-22 03:00:18,434 - INFO - 第 6 页获取到 100 条记录
2025-05-22 03:00:18,434 - INFO - Request Parameters - Page 7:
2025-05-22 03:00:18,434 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 03:00:18,434 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 03:00:18,825 - INFO - Response - Page 7:
2025-05-22 03:00:19,028 - INFO - 第 7 页获取到 61 条记录
2025-05-22 03:00:19,028 - INFO - 查询完成，共获取到 661 条记录
2025-05-22 03:00:19,028 - INFO - 获取到 661 条表单数据
2025-05-22 03:00:19,028 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-22 03:00:19,043 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-22 03:00:19,043 - INFO - 开始处理日期: 2025-04
2025-05-22 03:00:19,043 - INFO - Request Parameters - Page 1:
2025-05-22 03:00:19,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 03:00:19,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 03:00:19,621 - INFO - Response - Page 1:
2025-05-22 03:00:19,824 - INFO - 第 1 页获取到 100 条记录
2025-05-22 03:00:19,824 - INFO - Request Parameters - Page 2:
2025-05-22 03:00:19,824 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 03:00:19,824 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 03:00:20,293 - INFO - Response - Page 2:
2025-05-22 03:00:20,496 - INFO - 第 2 页获取到 100 条记录
2025-05-22 03:00:20,496 - INFO - Request Parameters - Page 3:
2025-05-22 03:00:20,496 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 03:00:20,496 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 03:00:21,028 - INFO - Response - Page 3:
2025-05-22 03:00:21,231 - INFO - 第 3 页获取到 100 条记录
2025-05-22 03:00:21,231 - INFO - Request Parameters - Page 4:
2025-05-22 03:00:21,231 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 03:00:21,231 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 03:00:21,699 - INFO - Response - Page 4:
2025-05-22 03:00:21,902 - INFO - 第 4 页获取到 100 条记录
2025-05-22 03:00:21,902 - INFO - Request Parameters - Page 5:
2025-05-22 03:00:21,902 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 03:00:21,902 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 03:00:22,387 - INFO - Response - Page 5:
2025-05-22 03:00:22,590 - INFO - 第 5 页获取到 100 条记录
2025-05-22 03:00:22,590 - INFO - Request Parameters - Page 6:
2025-05-22 03:00:22,590 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 03:00:22,590 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 03:00:23,090 - INFO - Response - Page 6:
2025-05-22 03:00:23,293 - INFO - 第 6 页获取到 100 条记录
2025-05-22 03:00:23,293 - INFO - Request Parameters - Page 7:
2025-05-22 03:00:23,293 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 03:00:23,293 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 03:00:23,777 - INFO - Response - Page 7:
2025-05-22 03:00:23,980 - INFO - 第 7 页获取到 56 条记录
2025-05-22 03:00:23,980 - INFO - 查询完成，共获取到 656 条记录
2025-05-22 03:00:23,980 - INFO - 获取到 656 条表单数据
2025-05-22 03:00:23,980 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-22 03:00:23,996 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-22 03:00:23,996 - INFO - 开始处理日期: 2025-05
2025-05-22 03:00:23,996 - INFO - Request Parameters - Page 1:
2025-05-22 03:00:23,996 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 03:00:23,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 03:00:24,465 - INFO - Response - Page 1:
2025-05-22 03:00:24,668 - INFO - 第 1 页获取到 100 条记录
2025-05-22 03:00:24,668 - INFO - Request Parameters - Page 2:
2025-05-22 03:00:24,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 03:00:24,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 03:00:25,121 - INFO - Response - Page 2:
2025-05-22 03:00:25,324 - INFO - 第 2 页获取到 100 条记录
2025-05-22 03:00:25,324 - INFO - Request Parameters - Page 3:
2025-05-22 03:00:25,324 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 03:00:25,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 03:00:25,840 - INFO - Response - Page 3:
2025-05-22 03:00:26,043 - INFO - 第 3 页获取到 100 条记录
2025-05-22 03:00:26,043 - INFO - Request Parameters - Page 4:
2025-05-22 03:00:26,043 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 03:00:26,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 03:00:26,496 - INFO - Response - Page 4:
2025-05-22 03:00:26,699 - INFO - 第 4 页获取到 100 条记录
2025-05-22 03:00:26,699 - INFO - Request Parameters - Page 5:
2025-05-22 03:00:26,699 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 03:00:26,699 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 03:00:27,590 - INFO - Response - Page 5:
2025-05-22 03:00:27,793 - INFO - 第 5 页获取到 100 条记录
2025-05-22 03:00:27,793 - INFO - Request Parameters - Page 6:
2025-05-22 03:00:27,793 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 03:00:27,793 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 03:00:28,324 - INFO - Response - Page 6:
2025-05-22 03:00:28,527 - INFO - 第 6 页获取到 100 条记录
2025-05-22 03:00:28,527 - INFO - Request Parameters - Page 7:
2025-05-22 03:00:28,527 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 03:00:28,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 03:00:28,871 - INFO - Response - Page 7:
2025-05-22 03:00:29,074 - INFO - 第 7 页获取到 28 条记录
2025-05-22 03:00:29,074 - INFO - 查询完成，共获取到 628 条记录
2025-05-22 03:00:29,074 - INFO - 获取到 628 条表单数据
2025-05-22 03:00:29,090 - INFO - 当前日期 2025-05 有 628 条MySQL数据需要处理
2025-05-22 03:00:29,090 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGC
2025-05-22 03:00:29,496 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGC
2025-05-22 03:00:29,496 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74654.0, 'new_value': 80754.0}, {'field': 'total_amount', 'old_value': 74654.0, 'new_value': 80754.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-05-22 03:00:29,496 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJC
2025-05-22 03:00:30,058 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJC
2025-05-22 03:00:30,058 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2157.6, 'new_value': 2436.6}, {'field': 'offline_amount', 'old_value': 24928.8, 'new_value': 31353.8}, {'field': 'total_amount', 'old_value': 27086.4, 'new_value': 33790.4}, {'field': 'order_count', 'old_value': 33, 'new_value': 37}]
2025-05-22 03:00:30,058 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-22 03:00:30,480 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM0D
2025-05-22 03:00:30,480 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 906330.0, 'new_value': 945219.0}, {'field': 'offline_amount', 'old_value': 274329.0, 'new_value': 283997.0}, {'field': 'total_amount', 'old_value': 1180659.0, 'new_value': 1229216.0}, {'field': 'order_count', 'old_value': 1361, 'new_value': 1419}]
2025-05-22 03:00:30,480 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM1D
2025-05-22 03:00:30,902 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM1D
2025-05-22 03:00:30,902 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45877.0, 'new_value': 47476.0}, {'field': 'total_amount', 'old_value': 83477.0, 'new_value': 85076.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-05-22 03:00:30,902 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-22 03:00:31,355 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM4D
2025-05-22 03:00:31,355 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 230200.0, 'new_value': 234280.0}, {'field': 'total_amount', 'old_value': 260200.0, 'new_value': 264280.0}, {'field': 'order_count', 'old_value': 42, 'new_value': 43}]
2025-05-22 03:00:31,355 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-22 03:00:31,824 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM5D
2025-05-22 03:00:31,824 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 293620.89, 'new_value': 303420.89}, {'field': 'total_amount', 'old_value': 293620.89, 'new_value': 303420.89}, {'field': 'order_count', 'old_value': 50, 'new_value': 51}]
2025-05-22 03:00:31,824 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-22 03:00:32,246 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM6D
2025-05-22 03:00:32,246 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 252280.13, 'new_value': 262080.13}, {'field': 'total_amount', 'old_value': 291640.13, 'new_value': 301440.13}, {'field': 'order_count', 'old_value': 42, 'new_value': 43}]
2025-05-22 03:00:32,246 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-22 03:00:32,683 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFD
2025-05-22 03:00:32,683 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34428.86, 'new_value': 37790.54}, {'field': 'total_amount', 'old_value': 34428.86, 'new_value': 37790.54}, {'field': 'order_count', 'old_value': 2543, 'new_value': 2740}]
2025-05-22 03:00:32,683 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-22 03:00:33,121 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMID
2025-05-22 03:00:33,121 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35612.0, 'new_value': 37303.0}, {'field': 'total_amount', 'old_value': 40988.0, 'new_value': 42679.0}, {'field': 'order_count', 'old_value': 181, 'new_value': 190}]
2025-05-22 03:00:33,121 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-22 03:00:33,574 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLD
2025-05-22 03:00:33,574 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54567.12, 'new_value': 55555.12}, {'field': 'total_amount', 'old_value': 54567.12, 'new_value': 55555.12}, {'field': 'order_count', 'old_value': 96, 'new_value': 98}]
2025-05-22 03:00:33,574 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-22 03:00:34,042 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQD
2025-05-22 03:00:34,042 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45007.3, 'new_value': 46954.9}, {'field': 'total_amount', 'old_value': 45019.2, 'new_value': 46966.8}, {'field': 'order_count', 'old_value': 253, 'new_value': 268}]
2025-05-22 03:00:34,042 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-22 03:00:34,480 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVD
2025-05-22 03:00:34,495 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67276.28, 'new_value': 71651.54}, {'field': 'offline_amount', 'old_value': 34769.16, 'new_value': 36071.46}, {'field': 'total_amount', 'old_value': 102045.44, 'new_value': 107723.0}, {'field': 'order_count', 'old_value': 3490, 'new_value': 3705}]
2025-05-22 03:00:34,495 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E
2025-05-22 03:00:34,917 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM1E
2025-05-22 03:00:34,917 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44784.0, 'new_value': 47000.0}, {'field': 'total_amount', 'old_value': 44784.0, 'new_value': 47000.0}, {'field': 'order_count', 'old_value': 55, 'new_value': 58}]
2025-05-22 03:00:34,917 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-22 03:00:35,370 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM4E
2025-05-22 03:00:35,370 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 104387.0, 'new_value': 106763.0}, {'field': 'offline_amount', 'old_value': 51674.0, 'new_value': 62672.0}, {'field': 'total_amount', 'old_value': 156061.0, 'new_value': 169435.0}, {'field': 'order_count', 'old_value': 60, 'new_value': 65}]
2025-05-22 03:00:35,370 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-22 03:00:35,808 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM7E
2025-05-22 03:00:35,808 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3457.25, 'new_value': 3616.05}, {'field': 'offline_amount', 'old_value': 114273.91, 'new_value': 118685.98}, {'field': 'total_amount', 'old_value': 117731.16, 'new_value': 122302.03}, {'field': 'order_count', 'old_value': 543, 'new_value': 566}]
2025-05-22 03:00:35,808 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-22 03:00:36,292 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUL
2025-05-22 03:00:36,292 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 80755.03, 'new_value': 84583.96}, {'field': 'offline_amount', 'old_value': 59330.48, 'new_value': 62135.34}, {'field': 'total_amount', 'old_value': 140085.51, 'new_value': 146719.3}, {'field': 'order_count', 'old_value': 5871, 'new_value': 6189}]
2025-05-22 03:00:36,292 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-22 03:00:36,698 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWL
2025-05-22 03:00:36,698 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 93720.0, 'new_value': 96460.0}, {'field': 'total_amount', 'old_value': 93720.0, 'new_value': 96460.0}, {'field': 'order_count', 'old_value': 4571, 'new_value': 4644}]
2025-05-22 03:00:36,698 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-05-22 03:00:37,151 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYM
2025-05-22 03:00:37,151 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9719.0, 'new_value': 11047.0}, {'field': 'total_amount', 'old_value': 9719.0, 'new_value': 11047.0}, {'field': 'order_count', 'old_value': 73, 'new_value': 78}]
2025-05-22 03:00:37,151 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-22 03:00:37,605 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUD
2025-05-22 03:00:37,605 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 70521.97, 'new_value': 74829.97}, {'field': 'offline_amount', 'old_value': 189720.12, 'new_value': 197907.12}, {'field': 'total_amount', 'old_value': 260242.09, 'new_value': 272737.09}, {'field': 'order_count', 'old_value': 12436, 'new_value': 13109}]
2025-05-22 03:00:37,605 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-22 03:00:38,151 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSE
2025-05-22 03:00:38,151 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 263914.0, 'new_value': 267286.0}, {'field': 'total_amount', 'old_value': 268014.0, 'new_value': 271386.0}, {'field': 'order_count', 'old_value': 179, 'new_value': 184}]
2025-05-22 03:00:38,151 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-22 03:00:38,558 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4F
2025-05-22 03:00:38,558 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27133.0, 'new_value': 34504.0}, {'field': 'total_amount', 'old_value': 27133.0, 'new_value': 34504.0}, {'field': 'order_count', 'old_value': 151, 'new_value': 156}]
2025-05-22 03:00:38,558 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-22 03:00:39,042 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLG
2025-05-22 03:00:39,042 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20284.3, 'new_value': 20968.4}, {'field': 'offline_amount', 'old_value': 152960.1, 'new_value': 160715.2}, {'field': 'total_amount', 'old_value': 173244.4, 'new_value': 181683.6}, {'field': 'order_count', 'old_value': 5345, 'new_value': 5573}]
2025-05-22 03:00:39,042 - INFO - 日期 2025-05 处理完成 - 更新: 22 条，插入: 0 条，错误: 0 条
2025-05-22 03:00:39,042 - INFO - 数据同步完成！更新: 22 条，插入: 0 条，错误: 0 条
2025-05-22 03:00:39,042 - INFO - =================同步完成====================
2025-05-22 06:00:01,790 - INFO - =================使用默认全量同步=============
2025-05-22 06:00:03,197 - INFO - MySQL查询成功，共获取 3297 条记录
2025-05-22 06:00:03,212 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-22 06:00:03,228 - INFO - 开始处理日期: 2025-01
2025-05-22 06:00:03,243 - INFO - Request Parameters - Page 1:
2025-05-22 06:00:03,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 06:00:03,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 06:00:04,134 - INFO - Response - Page 1:
2025-05-22 06:00:04,337 - INFO - 第 1 页获取到 100 条记录
2025-05-22 06:00:04,337 - INFO - Request Parameters - Page 2:
2025-05-22 06:00:04,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 06:00:04,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 06:00:05,181 - INFO - Response - Page 2:
2025-05-22 06:00:05,384 - INFO - 第 2 页获取到 100 条记录
2025-05-22 06:00:05,384 - INFO - Request Parameters - Page 3:
2025-05-22 06:00:05,384 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 06:00:05,384 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 06:00:05,853 - INFO - Response - Page 3:
2025-05-22 06:00:06,056 - INFO - 第 3 页获取到 100 条记录
2025-05-22 06:00:06,056 - INFO - Request Parameters - Page 4:
2025-05-22 06:00:06,056 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 06:00:06,056 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 06:00:06,603 - INFO - Response - Page 4:
2025-05-22 06:00:06,806 - INFO - 第 4 页获取到 100 条记录
2025-05-22 06:00:06,806 - INFO - Request Parameters - Page 5:
2025-05-22 06:00:06,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 06:00:06,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 06:00:07,337 - INFO - Response - Page 5:
2025-05-22 06:00:07,540 - INFO - 第 5 页获取到 100 条记录
2025-05-22 06:00:07,540 - INFO - Request Parameters - Page 6:
2025-05-22 06:00:07,540 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 06:00:07,540 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 06:00:08,102 - INFO - Response - Page 6:
2025-05-22 06:00:08,306 - INFO - 第 6 页获取到 100 条记录
2025-05-22 06:00:08,306 - INFO - Request Parameters - Page 7:
2025-05-22 06:00:08,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 06:00:08,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 06:00:08,790 - INFO - Response - Page 7:
2025-05-22 06:00:08,993 - INFO - 第 7 页获取到 82 条记录
2025-05-22 06:00:08,993 - INFO - 查询完成，共获取到 682 条记录
2025-05-22 06:00:08,993 - INFO - 获取到 682 条表单数据
2025-05-22 06:00:08,993 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-22 06:00:09,009 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-22 06:00:09,009 - INFO - 开始处理日期: 2025-02
2025-05-22 06:00:09,009 - INFO - Request Parameters - Page 1:
2025-05-22 06:00:09,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 06:00:09,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 06:00:09,446 - INFO - Response - Page 1:
2025-05-22 06:00:09,649 - INFO - 第 1 页获取到 100 条记录
2025-05-22 06:00:09,649 - INFO - Request Parameters - Page 2:
2025-05-22 06:00:09,649 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 06:00:09,649 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 06:00:10,149 - INFO - Response - Page 2:
2025-05-22 06:00:10,352 - INFO - 第 2 页获取到 100 条记录
2025-05-22 06:00:10,352 - INFO - Request Parameters - Page 3:
2025-05-22 06:00:10,352 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 06:00:10,352 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 06:00:10,852 - INFO - Response - Page 3:
2025-05-22 06:00:11,055 - INFO - 第 3 页获取到 100 条记录
2025-05-22 06:00:11,055 - INFO - Request Parameters - Page 4:
2025-05-22 06:00:11,055 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 06:00:11,055 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 06:00:11,524 - INFO - Response - Page 4:
2025-05-22 06:00:11,727 - INFO - 第 4 页获取到 100 条记录
2025-05-22 06:00:11,727 - INFO - Request Parameters - Page 5:
2025-05-22 06:00:11,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 06:00:11,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 06:00:12,227 - INFO - Response - Page 5:
2025-05-22 06:00:12,430 - INFO - 第 5 页获取到 100 条记录
2025-05-22 06:00:12,430 - INFO - Request Parameters - Page 6:
2025-05-22 06:00:12,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 06:00:12,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 06:00:12,915 - INFO - Response - Page 6:
2025-05-22 06:00:13,118 - INFO - 第 6 页获取到 100 条记录
2025-05-22 06:00:13,118 - INFO - Request Parameters - Page 7:
2025-05-22 06:00:13,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 06:00:13,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 06:00:13,602 - INFO - Response - Page 7:
2025-05-22 06:00:13,805 - INFO - 第 7 页获取到 70 条记录
2025-05-22 06:00:13,805 - INFO - 查询完成，共获取到 670 条记录
2025-05-22 06:00:13,805 - INFO - 获取到 670 条表单数据
2025-05-22 06:00:13,805 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-22 06:00:13,821 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-22 06:00:13,821 - INFO - 开始处理日期: 2025-03
2025-05-22 06:00:13,821 - INFO - Request Parameters - Page 1:
2025-05-22 06:00:13,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 06:00:13,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 06:00:14,336 - INFO - Response - Page 1:
2025-05-22 06:00:14,539 - INFO - 第 1 页获取到 100 条记录
2025-05-22 06:00:14,539 - INFO - Request Parameters - Page 2:
2025-05-22 06:00:14,539 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 06:00:14,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 06:00:14,993 - INFO - Response - Page 2:
2025-05-22 06:00:15,196 - INFO - 第 2 页获取到 100 条记录
2025-05-22 06:00:15,196 - INFO - Request Parameters - Page 3:
2025-05-22 06:00:15,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 06:00:15,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 06:00:15,758 - INFO - Response - Page 3:
2025-05-22 06:00:15,961 - INFO - 第 3 页获取到 100 条记录
2025-05-22 06:00:15,961 - INFO - Request Parameters - Page 4:
2025-05-22 06:00:15,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 06:00:15,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 06:00:16,446 - INFO - Response - Page 4:
2025-05-22 06:00:16,649 - INFO - 第 4 页获取到 100 条记录
2025-05-22 06:00:16,649 - INFO - Request Parameters - Page 5:
2025-05-22 06:00:16,649 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 06:00:16,649 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 06:00:17,414 - INFO - Response - Page 5:
2025-05-22 06:00:17,617 - INFO - 第 5 页获取到 100 条记录
2025-05-22 06:00:17,617 - INFO - Request Parameters - Page 6:
2025-05-22 06:00:17,617 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 06:00:17,617 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 06:00:18,383 - INFO - Response - Page 6:
2025-05-22 06:00:18,586 - INFO - 第 6 页获取到 100 条记录
2025-05-22 06:00:18,586 - INFO - Request Parameters - Page 7:
2025-05-22 06:00:18,586 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 06:00:18,586 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 06:00:19,039 - INFO - Response - Page 7:
2025-05-22 06:00:19,242 - INFO - 第 7 页获取到 61 条记录
2025-05-22 06:00:19,242 - INFO - 查询完成，共获取到 661 条记录
2025-05-22 06:00:19,242 - INFO - 获取到 661 条表单数据
2025-05-22 06:00:19,242 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-22 06:00:19,258 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-22 06:00:19,258 - INFO - 开始处理日期: 2025-04
2025-05-22 06:00:19,258 - INFO - Request Parameters - Page 1:
2025-05-22 06:00:19,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 06:00:19,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 06:00:19,789 - INFO - Response - Page 1:
2025-05-22 06:00:19,992 - INFO - 第 1 页获取到 100 条记录
2025-05-22 06:00:19,992 - INFO - Request Parameters - Page 2:
2025-05-22 06:00:19,992 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 06:00:19,992 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 06:00:20,570 - INFO - Response - Page 2:
2025-05-22 06:00:20,773 - INFO - 第 2 页获取到 100 条记录
2025-05-22 06:00:20,773 - INFO - Request Parameters - Page 3:
2025-05-22 06:00:20,773 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 06:00:20,773 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 06:00:21,273 - INFO - Response - Page 3:
2025-05-22 06:00:21,476 - INFO - 第 3 页获取到 100 条记录
2025-05-22 06:00:21,476 - INFO - Request Parameters - Page 4:
2025-05-22 06:00:21,476 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 06:00:21,476 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 06:00:22,023 - INFO - Response - Page 4:
2025-05-22 06:00:22,226 - INFO - 第 4 页获取到 100 条记录
2025-05-22 06:00:22,226 - INFO - Request Parameters - Page 5:
2025-05-22 06:00:22,226 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 06:00:22,226 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 06:00:22,726 - INFO - Response - Page 5:
2025-05-22 06:00:22,929 - INFO - 第 5 页获取到 100 条记录
2025-05-22 06:00:22,929 - INFO - Request Parameters - Page 6:
2025-05-22 06:00:22,929 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 06:00:22,929 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 06:00:23,554 - INFO - Response - Page 6:
2025-05-22 06:00:23,758 - INFO - 第 6 页获取到 100 条记录
2025-05-22 06:00:23,758 - INFO - Request Parameters - Page 7:
2025-05-22 06:00:23,758 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 06:00:23,758 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 06:00:24,179 - INFO - Response - Page 7:
2025-05-22 06:00:24,382 - INFO - 第 7 页获取到 56 条记录
2025-05-22 06:00:24,382 - INFO - 查询完成，共获取到 656 条记录
2025-05-22 06:00:24,382 - INFO - 获取到 656 条表单数据
2025-05-22 06:00:24,382 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-22 06:00:24,398 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-22 06:00:24,398 - INFO - 开始处理日期: 2025-05
2025-05-22 06:00:24,398 - INFO - Request Parameters - Page 1:
2025-05-22 06:00:24,398 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 06:00:24,398 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 06:00:24,929 - INFO - Response - Page 1:
2025-05-22 06:00:25,132 - INFO - 第 1 页获取到 100 条记录
2025-05-22 06:00:25,132 - INFO - Request Parameters - Page 2:
2025-05-22 06:00:25,132 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 06:00:25,132 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 06:00:25,585 - INFO - Response - Page 2:
2025-05-22 06:00:25,789 - INFO - 第 2 页获取到 100 条记录
2025-05-22 06:00:25,789 - INFO - Request Parameters - Page 3:
2025-05-22 06:00:25,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 06:00:25,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 06:00:26,289 - INFO - Response - Page 3:
2025-05-22 06:00:26,492 - INFO - 第 3 页获取到 100 条记录
2025-05-22 06:00:26,492 - INFO - Request Parameters - Page 4:
2025-05-22 06:00:26,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 06:00:26,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 06:00:26,960 - INFO - Response - Page 4:
2025-05-22 06:00:27,163 - INFO - 第 4 页获取到 100 条记录
2025-05-22 06:00:27,163 - INFO - Request Parameters - Page 5:
2025-05-22 06:00:27,163 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 06:00:27,163 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 06:00:27,648 - INFO - Response - Page 5:
2025-05-22 06:00:27,851 - INFO - 第 5 页获取到 100 条记录
2025-05-22 06:00:27,851 - INFO - Request Parameters - Page 6:
2025-05-22 06:00:27,851 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 06:00:27,851 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 06:00:28,320 - INFO - Response - Page 6:
2025-05-22 06:00:28,523 - INFO - 第 6 页获取到 100 条记录
2025-05-22 06:00:28,523 - INFO - Request Parameters - Page 7:
2025-05-22 06:00:28,523 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 06:00:28,523 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 06:00:28,913 - INFO - Response - Page 7:
2025-05-22 06:00:29,116 - INFO - 第 7 页获取到 28 条记录
2025-05-22 06:00:29,116 - INFO - 查询完成，共获取到 628 条记录
2025-05-22 06:00:29,116 - INFO - 获取到 628 条表单数据
2025-05-22 06:00:29,116 - INFO - 当前日期 2025-05 有 628 条MySQL数据需要处理
2025-05-22 06:00:29,132 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-22 06:00:29,132 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-05-22 06:00:29,132 - INFO - =================同步完成====================
2025-05-22 09:00:01,853 - INFO - =================使用默认全量同步=============
2025-05-22 09:00:03,291 - INFO - MySQL查询成功，共获取 3297 条记录
2025-05-22 09:00:03,291 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-22 09:00:03,306 - INFO - 开始处理日期: 2025-01
2025-05-22 09:00:03,322 - INFO - Request Parameters - Page 1:
2025-05-22 09:00:03,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 09:00:03,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 09:00:04,572 - INFO - Response - Page 1:
2025-05-22 09:00:04,775 - INFO - 第 1 页获取到 100 条记录
2025-05-22 09:00:04,775 - INFO - Request Parameters - Page 2:
2025-05-22 09:00:04,775 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 09:00:04,775 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 09:00:05,338 - INFO - Response - Page 2:
2025-05-22 09:00:05,541 - INFO - 第 2 页获取到 100 条记录
2025-05-22 09:00:05,541 - INFO - Request Parameters - Page 3:
2025-05-22 09:00:05,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 09:00:05,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 09:00:06,103 - INFO - Response - Page 3:
2025-05-22 09:00:06,306 - INFO - 第 3 页获取到 100 条记录
2025-05-22 09:00:06,306 - INFO - Request Parameters - Page 4:
2025-05-22 09:00:06,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 09:00:06,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 09:00:06,853 - INFO - Response - Page 4:
2025-05-22 09:00:07,056 - INFO - 第 4 页获取到 100 条记录
2025-05-22 09:00:07,056 - INFO - Request Parameters - Page 5:
2025-05-22 09:00:07,056 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 09:00:07,056 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 09:00:07,619 - INFO - Response - Page 5:
2025-05-22 09:00:07,822 - INFO - 第 5 页获取到 100 条记录
2025-05-22 09:00:07,822 - INFO - Request Parameters - Page 6:
2025-05-22 09:00:07,822 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 09:00:07,822 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 09:00:08,416 - INFO - Response - Page 6:
2025-05-22 09:00:08,619 - INFO - 第 6 页获取到 100 条记录
2025-05-22 09:00:08,619 - INFO - Request Parameters - Page 7:
2025-05-22 09:00:08,619 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 09:00:08,619 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 09:00:09,041 - INFO - Response - Page 7:
2025-05-22 09:00:09,244 - INFO - 第 7 页获取到 82 条记录
2025-05-22 09:00:09,244 - INFO - 查询完成，共获取到 682 条记录
2025-05-22 09:00:09,244 - INFO - 获取到 682 条表单数据
2025-05-22 09:00:09,244 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-22 09:00:09,259 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-22 09:00:09,259 - INFO - 开始处理日期: 2025-02
2025-05-22 09:00:09,259 - INFO - Request Parameters - Page 1:
2025-05-22 09:00:09,259 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 09:00:09,259 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 09:00:09,744 - INFO - Response - Page 1:
2025-05-22 09:00:09,947 - INFO - 第 1 页获取到 100 条记录
2025-05-22 09:00:09,947 - INFO - Request Parameters - Page 2:
2025-05-22 09:00:09,947 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 09:00:09,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 09:00:10,384 - INFO - Response - Page 2:
2025-05-22 09:00:10,588 - INFO - 第 2 页获取到 100 条记录
2025-05-22 09:00:10,588 - INFO - Request Parameters - Page 3:
2025-05-22 09:00:10,588 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 09:00:10,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 09:00:11,369 - INFO - Response - Page 3:
2025-05-22 09:00:11,572 - INFO - 第 3 页获取到 100 条记录
2025-05-22 09:00:11,572 - INFO - Request Parameters - Page 4:
2025-05-22 09:00:11,572 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 09:00:11,572 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 09:00:12,119 - INFO - Response - Page 4:
2025-05-22 09:00:12,322 - INFO - 第 4 页获取到 100 条记录
2025-05-22 09:00:12,322 - INFO - Request Parameters - Page 5:
2025-05-22 09:00:12,322 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 09:00:12,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 09:00:12,853 - INFO - Response - Page 5:
2025-05-22 09:00:13,056 - INFO - 第 5 页获取到 100 条记录
2025-05-22 09:00:13,056 - INFO - Request Parameters - Page 6:
2025-05-22 09:00:13,056 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 09:00:13,056 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 09:00:13,619 - INFO - Response - Page 6:
2025-05-22 09:00:13,822 - INFO - 第 6 页获取到 100 条记录
2025-05-22 09:00:13,822 - INFO - Request Parameters - Page 7:
2025-05-22 09:00:13,822 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 09:00:13,822 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 09:00:14,306 - INFO - Response - Page 7:
2025-05-22 09:00:14,509 - INFO - 第 7 页获取到 70 条记录
2025-05-22 09:00:14,509 - INFO - 查询完成，共获取到 670 条记录
2025-05-22 09:00:14,509 - INFO - 获取到 670 条表单数据
2025-05-22 09:00:14,509 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-22 09:00:14,525 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-22 09:00:14,525 - INFO - 开始处理日期: 2025-03
2025-05-22 09:00:14,525 - INFO - Request Parameters - Page 1:
2025-05-22 09:00:14,525 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 09:00:14,525 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 09:00:15,150 - INFO - Response - Page 1:
2025-05-22 09:00:15,353 - INFO - 第 1 页获取到 100 条记录
2025-05-22 09:00:15,353 - INFO - Request Parameters - Page 2:
2025-05-22 09:00:15,353 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 09:00:15,353 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 09:00:15,947 - INFO - Response - Page 2:
2025-05-22 09:00:16,150 - INFO - 第 2 页获取到 100 条记录
2025-05-22 09:00:16,150 - INFO - Request Parameters - Page 3:
2025-05-22 09:00:16,150 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 09:00:16,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 09:00:16,806 - INFO - Response - Page 3:
2025-05-22 09:00:17,009 - INFO - 第 3 页获取到 100 条记录
2025-05-22 09:00:17,009 - INFO - Request Parameters - Page 4:
2025-05-22 09:00:17,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 09:00:17,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 09:00:17,494 - INFO - Response - Page 4:
2025-05-22 09:00:17,697 - INFO - 第 4 页获取到 100 条记录
2025-05-22 09:00:17,697 - INFO - Request Parameters - Page 5:
2025-05-22 09:00:17,697 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 09:00:17,697 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 09:00:18,197 - INFO - Response - Page 5:
2025-05-22 09:00:18,400 - INFO - 第 5 页获取到 100 条记录
2025-05-22 09:00:18,400 - INFO - Request Parameters - Page 6:
2025-05-22 09:00:18,400 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 09:00:18,400 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 09:00:18,916 - INFO - Response - Page 6:
2025-05-22 09:00:19,119 - INFO - 第 6 页获取到 100 条记录
2025-05-22 09:00:19,119 - INFO - Request Parameters - Page 7:
2025-05-22 09:00:19,119 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 09:00:19,119 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 09:00:19,572 - INFO - Response - Page 7:
2025-05-22 09:00:19,775 - INFO - 第 7 页获取到 61 条记录
2025-05-22 09:00:19,775 - INFO - 查询完成，共获取到 661 条记录
2025-05-22 09:00:19,775 - INFO - 获取到 661 条表单数据
2025-05-22 09:00:19,775 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-22 09:00:19,791 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-22 09:00:19,791 - INFO - 开始处理日期: 2025-04
2025-05-22 09:00:19,791 - INFO - Request Parameters - Page 1:
2025-05-22 09:00:19,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 09:00:19,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 09:00:20,431 - INFO - Response - Page 1:
2025-05-22 09:00:20,634 - INFO - 第 1 页获取到 100 条记录
2025-05-22 09:00:20,634 - INFO - Request Parameters - Page 2:
2025-05-22 09:00:20,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 09:00:20,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 09:00:21,103 - INFO - Response - Page 2:
2025-05-22 09:00:21,306 - INFO - 第 2 页获取到 100 条记录
2025-05-22 09:00:21,306 - INFO - Request Parameters - Page 3:
2025-05-22 09:00:21,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 09:00:21,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 09:00:21,838 - INFO - Response - Page 3:
2025-05-22 09:00:22,041 - INFO - 第 3 页获取到 100 条记录
2025-05-22 09:00:22,041 - INFO - Request Parameters - Page 4:
2025-05-22 09:00:22,041 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 09:00:22,041 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 09:00:22,572 - INFO - Response - Page 4:
2025-05-22 09:00:22,775 - INFO - 第 4 页获取到 100 条记录
2025-05-22 09:00:22,775 - INFO - Request Parameters - Page 5:
2025-05-22 09:00:22,775 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 09:00:22,775 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 09:00:23,275 - INFO - Response - Page 5:
2025-05-22 09:00:23,478 - INFO - 第 5 页获取到 100 条记录
2025-05-22 09:00:23,478 - INFO - Request Parameters - Page 6:
2025-05-22 09:00:23,478 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 09:00:23,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 09:00:23,978 - INFO - Response - Page 6:
2025-05-22 09:00:24,181 - INFO - 第 6 页获取到 100 条记录
2025-05-22 09:00:24,181 - INFO - Request Parameters - Page 7:
2025-05-22 09:00:24,181 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 09:00:24,181 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 09:00:24,587 - INFO - Response - Page 7:
2025-05-22 09:00:24,791 - INFO - 第 7 页获取到 56 条记录
2025-05-22 09:00:24,791 - INFO - 查询完成，共获取到 656 条记录
2025-05-22 09:00:24,791 - INFO - 获取到 656 条表单数据
2025-05-22 09:00:24,791 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-22 09:00:24,806 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-22 09:00:24,806 - INFO - 开始处理日期: 2025-05
2025-05-22 09:00:24,806 - INFO - Request Parameters - Page 1:
2025-05-22 09:00:24,806 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 09:00:24,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 09:00:25,291 - INFO - Response - Page 1:
2025-05-22 09:00:25,494 - INFO - 第 1 页获取到 100 条记录
2025-05-22 09:00:25,494 - INFO - Request Parameters - Page 2:
2025-05-22 09:00:25,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 09:00:25,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 09:00:26,009 - INFO - Response - Page 2:
2025-05-22 09:00:26,212 - INFO - 第 2 页获取到 100 条记录
2025-05-22 09:00:26,212 - INFO - Request Parameters - Page 3:
2025-05-22 09:00:26,212 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 09:00:26,212 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 09:00:26,650 - INFO - Response - Page 3:
2025-05-22 09:00:26,853 - INFO - 第 3 页获取到 100 条记录
2025-05-22 09:00:26,853 - INFO - Request Parameters - Page 4:
2025-05-22 09:00:26,853 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 09:00:26,853 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 09:00:27,400 - INFO - Response - Page 4:
2025-05-22 09:00:27,603 - INFO - 第 4 页获取到 100 条记录
2025-05-22 09:00:27,603 - INFO - Request Parameters - Page 5:
2025-05-22 09:00:27,603 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 09:00:27,603 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 09:00:28,166 - INFO - Response - Page 5:
2025-05-22 09:00:28,369 - INFO - 第 5 页获取到 100 条记录
2025-05-22 09:00:28,369 - INFO - Request Parameters - Page 6:
2025-05-22 09:00:28,369 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 09:00:28,369 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 09:00:28,994 - INFO - Response - Page 6:
2025-05-22 09:00:29,197 - INFO - 第 6 页获取到 100 条记录
2025-05-22 09:00:29,197 - INFO - Request Parameters - Page 7:
2025-05-22 09:00:29,197 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 09:00:29,197 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 09:00:29,541 - INFO - Response - Page 7:
2025-05-22 09:00:29,744 - INFO - 第 7 页获取到 28 条记录
2025-05-22 09:00:29,744 - INFO - 查询完成，共获取到 628 条记录
2025-05-22 09:00:29,744 - INFO - 获取到 628 条表单数据
2025-05-22 09:00:29,744 - INFO - 当前日期 2025-05 有 628 条MySQL数据需要处理
2025-05-22 09:00:29,744 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-22 09:00:30,228 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3O
2025-05-22 09:00:30,228 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1752.0, 'new_value': 1867.0}, {'field': 'offline_amount', 'old_value': 33193.0, 'new_value': 34073.0}, {'field': 'total_amount', 'old_value': 34945.0, 'new_value': 35940.0}, {'field': 'order_count', 'old_value': 470, 'new_value': 486}]
2025-05-22 09:00:30,228 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-22 09:00:30,634 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4O
2025-05-22 09:00:30,634 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 313753.0, 'new_value': 330041.0}, {'field': 'total_amount', 'old_value': 313753.0, 'new_value': 330041.0}, {'field': 'order_count', 'old_value': 233, 'new_value': 248}]
2025-05-22 09:00:30,634 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-22 09:00:31,181 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMST
2025-05-22 09:00:31,181 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8000000.0, 'new_value': 8400000.0}, {'field': 'total_amount', 'old_value': 8100000.0, 'new_value': 8500000.0}, {'field': 'order_count', 'old_value': 39, 'new_value': 41}]
2025-05-22 09:00:31,181 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-22 09:00:31,634 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5O
2025-05-22 09:00:31,634 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37630.0, 'new_value': 40545.0}, {'field': 'total_amount', 'old_value': 39220.0, 'new_value': 42135.0}, {'field': 'order_count', 'old_value': 148, 'new_value': 159}]
2025-05-22 09:00:31,634 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-22 09:00:32,087 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMBC
2025-05-22 09:00:32,087 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46477.78, 'new_value': 49089.78}, {'field': 'total_amount', 'old_value': 46477.78, 'new_value': 49089.78}, {'field': 'order_count', 'old_value': 100, 'new_value': 106}]
2025-05-22 09:00:32,087 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-22 09:00:32,556 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMCC
2025-05-22 09:00:32,556 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 285910.0, 'new_value': 300620.0}, {'field': 'total_amount', 'old_value': 285910.0, 'new_value': 300620.0}, {'field': 'order_count', 'old_value': 166, 'new_value': 175}]
2025-05-22 09:00:32,556 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-22 09:00:33,072 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMFC
2025-05-22 09:00:33,072 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77930.0, 'new_value': 79694.0}, {'field': 'total_amount', 'old_value': 77930.0, 'new_value': 79694.0}, {'field': 'order_count', 'old_value': 93, 'new_value': 97}]
2025-05-22 09:00:33,072 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-22 09:00:33,494 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMIC
2025-05-22 09:00:33,494 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39740.0, 'new_value': 41560.0}, {'field': 'total_amount', 'old_value': 43860.0, 'new_value': 45680.0}, {'field': 'order_count', 'old_value': 430, 'new_value': 451}]
2025-05-22 09:00:33,494 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMOC
2025-05-22 09:00:34,056 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMOC
2025-05-22 09:00:34,056 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2848800.0, 'new_value': 3073700.0}, {'field': 'total_amount', 'old_value': 2848800.0, 'new_value': 3073700.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-05-22 09:00:34,056 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-22 09:00:34,541 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMPC
2025-05-22 09:00:34,541 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 151406.0, 'new_value': 220923.0}, {'field': 'total_amount', 'old_value': 151406.0, 'new_value': 220923.0}, {'field': 'order_count', 'old_value': 44, 'new_value': 49}]
2025-05-22 09:00:34,541 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-22 09:00:34,994 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMQC
2025-05-22 09:00:34,994 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46537.04, 'new_value': 49477.73}, {'field': 'offline_amount', 'old_value': 92021.45, 'new_value': 96419.25}, {'field': 'total_amount', 'old_value': 138558.49, 'new_value': 145896.98}, {'field': 'order_count', 'old_value': 1614, 'new_value': 1690}]
2025-05-22 09:00:34,994 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-22 09:00:35,353 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMRC
2025-05-22 09:00:35,353 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17850.44, 'new_value': 18674.88}, {'field': 'offline_amount', 'old_value': 21920.0, 'new_value': 22775.66}, {'field': 'total_amount', 'old_value': 39770.44, 'new_value': 41450.54}, {'field': 'order_count', 'old_value': 1932, 'new_value': 2017}]
2025-05-22 09:00:35,353 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-22 09:00:35,806 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMTC
2025-05-22 09:00:35,806 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 263557.3, 'new_value': 267937.6}, {'field': 'total_amount', 'old_value': 378577.0, 'new_value': 382957.3}, {'field': 'order_count', 'old_value': 2864, 'new_value': 2957}]
2025-05-22 09:00:35,806 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-22 09:00:36,228 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMUC
2025-05-22 09:00:36,228 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83262.0, 'new_value': 87523.0}, {'field': 'total_amount', 'old_value': 83262.0, 'new_value': 87523.0}, {'field': 'order_count', 'old_value': 4515, 'new_value': 4763}]
2025-05-22 09:00:36,228 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMYC
2025-05-22 09:00:36,681 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMYC
2025-05-22 09:00:36,681 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15378.0, 'new_value': 15977.0}, {'field': 'total_amount', 'old_value': 15378.0, 'new_value': 15977.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 10}]
2025-05-22 09:00:36,681 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-22 09:00:37,150 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMZC
2025-05-22 09:00:37,150 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117444.18, 'new_value': 123612.0}, {'field': 'total_amount', 'old_value': 117444.18, 'new_value': 123612.0}, {'field': 'order_count', 'old_value': 1350, 'new_value': 1429}]
2025-05-22 09:00:37,150 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-22 09:00:37,650 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM2D
2025-05-22 09:00:37,650 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6438.95, 'new_value': 6558.75}, {'field': 'offline_amount', 'old_value': 75223.7, 'new_value': 75263.7}, {'field': 'total_amount', 'old_value': 81662.65, 'new_value': 81822.45}, {'field': 'order_count', 'old_value': 1790, 'new_value': 1792}]
2025-05-22 09:00:37,650 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-22 09:00:38,072 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM9D
2025-05-22 09:00:38,072 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18583.0, 'new_value': 19153.0}, {'field': 'total_amount', 'old_value': 24384.0, 'new_value': 24954.0}, {'field': 'order_count', 'old_value': 63, 'new_value': 64}]
2025-05-22 09:00:38,072 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-22 09:00:38,494 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMAD
2025-05-22 09:00:38,494 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 153181.0, 'new_value': 156443.0}, {'field': 'total_amount', 'old_value': 153181.0, 'new_value': 156443.0}, {'field': 'order_count', 'old_value': 293, 'new_value': 304}]
2025-05-22 09:00:38,494 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-22 09:00:38,962 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMED
2025-05-22 09:00:38,962 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63193.0, 'new_value': 66046.0}, {'field': 'total_amount', 'old_value': 63193.0, 'new_value': 66046.0}, {'field': 'order_count', 'old_value': 550, 'new_value': 575}]
2025-05-22 09:00:38,962 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-22 09:00:39,462 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMGD
2025-05-22 09:00:39,462 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 151167.53, 'new_value': 154925.89}, {'field': 'offline_amount', 'old_value': 25709.66, 'new_value': 26999.24}, {'field': 'total_amount', 'old_value': 176877.19, 'new_value': 181925.13}, {'field': 'order_count', 'old_value': 644, 'new_value': 665}]
2025-05-22 09:00:39,462 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-22 09:00:39,900 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMHD
2025-05-22 09:00:39,900 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 141944.0, 'new_value': 144202.0}, {'field': 'offline_amount', 'old_value': 52263.22, 'new_value': 54077.3}, {'field': 'total_amount', 'old_value': 194207.22, 'new_value': 198279.3}, {'field': 'order_count', 'old_value': 1200, 'new_value': 1264}]
2025-05-22 09:00:39,900 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-22 09:00:40,337 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMJD
2025-05-22 09:00:40,337 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7356.0, 'new_value': 7854.0}, {'field': 'total_amount', 'old_value': 8849.0, 'new_value': 9347.0}, {'field': 'order_count', 'old_value': 169, 'new_value': 180}]
2025-05-22 09:00:40,337 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-22 09:00:40,744 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKD
2025-05-22 09:00:40,744 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71635.89, 'new_value': 76069.21}, {'field': 'total_amount', 'old_value': 71635.89, 'new_value': 76069.21}, {'field': 'order_count', 'old_value': 1908, 'new_value': 2026}]
2025-05-22 09:00:40,744 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-22 09:00:41,181 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOD
2025-05-22 09:00:41,181 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 131883.0, 'new_value': 134508.0}, {'field': 'offline_amount', 'old_value': 56594.72, 'new_value': 59720.72}, {'field': 'total_amount', 'old_value': 188477.72, 'new_value': 194228.72}, {'field': 'order_count', 'old_value': 1330, 'new_value': 1373}]
2025-05-22 09:00:41,181 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-22 09:00:41,619 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPD
2025-05-22 09:00:41,619 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8932.87, 'new_value': 9190.2}, {'field': 'offline_amount', 'old_value': 143364.33, 'new_value': 150710.32}, {'field': 'total_amount', 'old_value': 152297.2, 'new_value': 159900.52}, {'field': 'order_count', 'old_value': 1652, 'new_value': 1740}]
2025-05-22 09:00:41,619 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-22 09:00:42,041 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRD
2025-05-22 09:00:42,041 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1450.0, 'new_value': 1550.0}, {'field': 'offline_amount', 'old_value': 24552.0, 'new_value': 25980.0}, {'field': 'total_amount', 'old_value': 26002.0, 'new_value': 27530.0}, {'field': 'order_count', 'old_value': 92, 'new_value': 97}]
2025-05-22 09:00:42,041 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-22 09:00:42,541 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSD
2025-05-22 09:00:42,541 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6917.88, 'new_value': 6940.47}, {'field': 'offline_amount', 'old_value': 89704.52, 'new_value': 92317.76}, {'field': 'total_amount', 'old_value': 96622.4, 'new_value': 99258.23}, {'field': 'order_count', 'old_value': 2287, 'new_value': 2353}]
2025-05-22 09:00:42,541 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-22 09:00:42,978 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYT
2025-05-22 09:00:42,978 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5084.32, 'new_value': 5417.32}, {'field': 'total_amount', 'old_value': 58852.32, 'new_value': 59185.32}, {'field': 'order_count', 'old_value': 448, 'new_value': 449}]
2025-05-22 09:00:42,978 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-22 09:00:43,447 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUD
2025-05-22 09:00:43,447 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 167568.99, 'new_value': 176098.53}, {'field': 'total_amount', 'old_value': 167568.99, 'new_value': 176098.53}, {'field': 'order_count', 'old_value': 559, 'new_value': 590}]
2025-05-22 09:00:43,447 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-22 09:00:43,900 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMWD
2025-05-22 09:00:43,900 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 116766.0, 'new_value': 122254.0}, {'field': 'total_amount', 'old_value': 116766.0, 'new_value': 122254.0}, {'field': 'order_count', 'old_value': 2909, 'new_value': 3060}]
2025-05-22 09:00:43,900 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-22 09:00:44,415 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZD
2025-05-22 09:00:44,415 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18633.79, 'new_value': 19479.81}, {'field': 'total_amount', 'old_value': 18633.79, 'new_value': 19479.81}, {'field': 'order_count', 'old_value': 102, 'new_value': 118}]
2025-05-22 09:00:44,415 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-22 09:00:44,869 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0E
2025-05-22 09:00:44,869 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 135407.0, 'new_value': 142327.0}, {'field': 'total_amount', 'old_value': 135407.0, 'new_value': 142327.0}, {'field': 'order_count', 'old_value': 5040, 'new_value': 5317}]
2025-05-22 09:00:44,869 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-22 09:00:45,290 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM2E
2025-05-22 09:00:45,290 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20357.0, 'new_value': 21441.0}, {'field': 'offline_amount', 'old_value': 404662.0, 'new_value': 434362.0}, {'field': 'total_amount', 'old_value': 425019.0, 'new_value': 455803.0}, {'field': 'order_count', 'old_value': 78, 'new_value': 79}]
2025-05-22 09:00:45,290 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-22 09:00:45,759 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM3E
2025-05-22 09:00:45,759 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112114.75, 'new_value': 118070.09}, {'field': 'total_amount', 'old_value': 112114.75, 'new_value': 118070.09}, {'field': 'order_count', 'old_value': 4098, 'new_value': 4303}]
2025-05-22 09:00:45,759 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-22 09:00:46,197 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2U
2025-05-22 09:00:46,212 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46410.0, 'new_value': 48574.15}, {'field': 'offline_amount', 'old_value': 83595.31, 'new_value': 85991.01}, {'field': 'total_amount', 'old_value': 130005.31, 'new_value': 134565.16}, {'field': 'order_count', 'old_value': 4478, 'new_value': 4664}]
2025-05-22 09:00:46,212 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-05-22 09:00:46,619 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-05-22 09:00:46,619 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25685.28, 'new_value': 25690.28}, {'field': 'total_amount', 'old_value': 25685.28, 'new_value': 25690.28}, {'field': 'order_count', 'old_value': 36, 'new_value': 38}]
2025-05-22 09:00:46,619 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-05-22 09:00:47,072 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM6E
2025-05-22 09:00:47,072 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16554.14, 'new_value': 17531.14}, {'field': 'total_amount', 'old_value': 16554.14, 'new_value': 17531.14}, {'field': 'order_count', 'old_value': 30, 'new_value': 31}]
2025-05-22 09:00:47,072 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-22 09:00:47,540 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3U
2025-05-22 09:00:47,540 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 251316.0, 'new_value': 279223.0}, {'field': 'total_amount', 'old_value': 251316.0, 'new_value': 279223.0}, {'field': 'order_count', 'old_value': 150, 'new_value': 164}]
2025-05-22 09:00:47,540 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-22 09:00:47,994 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM8E
2025-05-22 09:00:47,994 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50514.0, 'new_value': 50682.0}, {'field': 'total_amount', 'old_value': 50514.0, 'new_value': 50682.0}, {'field': 'order_count', 'old_value': 96, 'new_value': 98}]
2025-05-22 09:00:47,994 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-22 09:00:48,431 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM9E
2025-05-22 09:00:48,431 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 61768.29, 'new_value': 66825.89}, {'field': 'offline_amount', 'old_value': 411580.3, 'new_value': 447045.3}, {'field': 'total_amount', 'old_value': 473348.59, 'new_value': 513871.19}, {'field': 'order_count', 'old_value': 686, 'new_value': 720}]
2025-05-22 09:00:48,431 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-22 09:00:48,947 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMAE
2025-05-22 09:00:48,947 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 154306.52, 'new_value': 162027.38}, {'field': 'offline_amount', 'old_value': 368930.5, 'new_value': 376272.67}, {'field': 'total_amount', 'old_value': 523237.02, 'new_value': 538300.05}, {'field': 'order_count', 'old_value': 3790, 'new_value': 3944}]
2025-05-22 09:00:48,947 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-22 09:00:49,400 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMCE
2025-05-22 09:00:49,400 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5253.41, 'new_value': 5527.2}, {'field': 'offline_amount', 'old_value': 157588.07, 'new_value': 168780.41}, {'field': 'total_amount', 'old_value': 162841.48, 'new_value': 174307.61}, {'field': 'order_count', 'old_value': 1086, 'new_value': 1128}]
2025-05-22 09:00:49,400 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-22 09:00:49,853 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5U
2025-05-22 09:00:49,853 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22539.4, 'new_value': 22545.4}, {'field': 'total_amount', 'old_value': 22604.95, 'new_value': 22610.95}, {'field': 'order_count', 'old_value': 198, 'new_value': 199}]
2025-05-22 09:00:49,853 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-22 09:00:50,353 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMEE
2025-05-22 09:00:50,353 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1438606.28, 'new_value': 1480239.88}, {'field': 'total_amount', 'old_value': 1492051.38, 'new_value': 1533684.98}, {'field': 'order_count', 'old_value': 2660, 'new_value': 2744}]
2025-05-22 09:00:50,353 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-22 09:00:50,806 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMGE
2025-05-22 09:00:50,806 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32174.9, 'new_value': 32591.9}, {'field': 'total_amount', 'old_value': 32174.9, 'new_value': 32591.9}, {'field': 'order_count', 'old_value': 145, 'new_value': 147}]
2025-05-22 09:00:50,806 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-22 09:00:51,306 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMHE
2025-05-22 09:00:51,306 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48567.0, 'new_value': 49467.0}, {'field': 'total_amount', 'old_value': 77213.0, 'new_value': 78113.0}, {'field': 'order_count', 'old_value': 37, 'new_value': 38}]
2025-05-22 09:00:51,306 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-22 09:00:51,697 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMJE
2025-05-22 09:00:51,697 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104136.0, 'new_value': 106918.0}, {'field': 'total_amount', 'old_value': 104138.0, 'new_value': 106920.0}, {'field': 'order_count', 'old_value': 44, 'new_value': 45}]
2025-05-22 09:00:51,697 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-22 09:00:52,134 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMKE
2025-05-22 09:00:52,134 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5335.44, 'new_value': 6312.77}, {'field': 'offline_amount', 'old_value': 13207.79, 'new_value': 13797.59}, {'field': 'total_amount', 'old_value': 18543.23, 'new_value': 20110.36}, {'field': 'order_count', 'old_value': 646, 'new_value': 678}]
2025-05-22 09:00:52,134 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-22 09:00:52,556 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMLE
2025-05-22 09:00:52,556 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 146823.82, 'new_value': 154669.99}, {'field': 'offline_amount', 'old_value': 117228.13, 'new_value': 125550.14}, {'field': 'total_amount', 'old_value': 264051.95, 'new_value': 280220.13}, {'field': 'order_count', 'old_value': 2324, 'new_value': 2467}]
2025-05-22 09:00:52,556 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMME
2025-05-22 09:00:53,009 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMME
2025-05-22 09:00:53,009 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34846.0, 'new_value': 38845.0}, {'field': 'total_amount', 'old_value': 34846.0, 'new_value': 38845.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-05-22 09:00:53,009 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-22 09:00:53,509 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMNE
2025-05-22 09:00:53,509 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60706.81, 'new_value': 68567.73}, {'field': 'total_amount', 'old_value': 60710.11, 'new_value': 68571.03}, {'field': 'order_count', 'old_value': 36, 'new_value': 38}]
2025-05-22 09:00:53,509 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-22 09:00:53,931 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMOE
2025-05-22 09:00:53,931 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 299481.6, 'new_value': 314603.6}, {'field': 'offline_amount', 'old_value': 79876.4, 'new_value': 82489.2}, {'field': 'total_amount', 'old_value': 379358.0, 'new_value': 397092.8}, {'field': 'order_count', 'old_value': 476, 'new_value': 501}]
2025-05-22 09:00:53,931 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-22 09:00:54,369 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMPE
2025-05-22 09:00:54,369 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23131.0, 'new_value': 23470.0}, {'field': 'total_amount', 'old_value': 23131.0, 'new_value': 23470.0}, {'field': 'order_count', 'old_value': 67, 'new_value': 68}]
2025-05-22 09:00:54,369 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-22 09:00:54,806 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMQE
2025-05-22 09:00:54,806 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61574.0, 'new_value': 64710.0}, {'field': 'total_amount', 'old_value': 61574.0, 'new_value': 64710.0}, {'field': 'order_count', 'old_value': 51, 'new_value': 53}]
2025-05-22 09:00:54,806 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-22 09:00:55,275 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMTE
2025-05-22 09:00:55,275 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69622.29, 'new_value': 72152.19}, {'field': 'total_amount', 'old_value': 73391.39, 'new_value': 75921.29}, {'field': 'order_count', 'old_value': 383, 'new_value': 386}]
2025-05-22 09:00:55,275 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-22 09:00:55,697 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMUE
2025-05-22 09:00:55,697 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85461.64, 'new_value': 89159.64}, {'field': 'total_amount', 'old_value': 90801.64, 'new_value': 94499.64}, {'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-05-22 09:00:55,697 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-22 09:00:56,244 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMVE
2025-05-22 09:00:56,244 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 486176.04, 'new_value': 487312.04}, {'field': 'offline_amount', 'old_value': 208918.1, 'new_value': 208938.1}, {'field': 'total_amount', 'old_value': 695094.14, 'new_value': 696250.14}, {'field': 'order_count', 'old_value': 6233, 'new_value': 6244}]
2025-05-22 09:00:56,244 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-22 09:00:56,681 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMXE
2025-05-22 09:00:56,681 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 62560.5, 'new_value': 65560.5}, {'field': 'offline_amount', 'old_value': 4383.95, 'new_value': 4844.95}, {'field': 'total_amount', 'old_value': 66944.45, 'new_value': 70405.45}, {'field': 'order_count', 'old_value': 205, 'new_value': 213}]
2025-05-22 09:00:56,681 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-22 09:00:57,150 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBU
2025-05-22 09:00:57,150 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 136903.0, 'new_value': 137738.0}, {'field': 'total_amount', 'old_value': 170649.15, 'new_value': 171484.15}, {'field': 'order_count', 'old_value': 29, 'new_value': 30}]
2025-05-22 09:00:57,150 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-22 09:00:57,603 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYE
2025-05-22 09:00:57,603 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59252.04, 'new_value': 60581.24}, {'field': 'total_amount', 'old_value': 59252.04, 'new_value': 60581.24}, {'field': 'order_count', 'old_value': 1661, 'new_value': 1715}]
2025-05-22 09:00:57,603 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-22 09:00:58,134 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMZE
2025-05-22 09:00:58,134 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7064.4, 'new_value': 7658.4}, {'field': 'offline_amount', 'old_value': 39055.0, 'new_value': 41013.0}, {'field': 'total_amount', 'old_value': 46119.4, 'new_value': 48671.4}, {'field': 'order_count', 'old_value': 60, 'new_value': 63}]
2025-05-22 09:00:58,134 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-22 09:00:58,634 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM0F
2025-05-22 09:00:58,634 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 353315.82, 'new_value': 372572.22}, {'field': 'total_amount', 'old_value': 353315.82, 'new_value': 372572.22}, {'field': 'order_count', 'old_value': 472, 'new_value': 482}]
2025-05-22 09:00:58,634 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-22 09:00:59,290 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEU
2025-05-22 09:00:59,290 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44114.0, 'new_value': 45748.0}, {'field': 'total_amount', 'old_value': 44462.0, 'new_value': 46096.0}, {'field': 'order_count', 'old_value': 92, 'new_value': 97}]
2025-05-22 09:00:59,290 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-22 09:00:59,744 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMJK
2025-05-22 09:00:59,744 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12822.36, 'new_value': 13430.44}, {'field': 'offline_amount', 'old_value': 339811.32, 'new_value': 353578.76}, {'field': 'total_amount', 'old_value': 352633.68, 'new_value': 367009.2}, {'field': 'order_count', 'old_value': 1468, 'new_value': 1535}]
2025-05-22 09:00:59,744 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-22 09:01:00,275 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12OL7XJ5AMKK
2025-05-22 09:01:00,275 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 73084.0, 'new_value': 73369.0}, {'field': 'offline_amount', 'old_value': 62757.46, 'new_value': 66281.46}, {'field': 'total_amount', 'old_value': 135841.46, 'new_value': 139650.46}, {'field': 'order_count', 'old_value': 164, 'new_value': 172}]
2025-05-22 09:01:00,275 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-22 09:01:00,759 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLK
2025-05-22 09:01:00,759 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 135176.0, 'new_value': 136296.4}, {'field': 'total_amount', 'old_value': 135176.0, 'new_value': 136296.4}, {'field': 'order_count', 'old_value': 312, 'new_value': 317}]
2025-05-22 09:01:00,759 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-22 09:01:01,197 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGU
2025-05-22 09:01:01,197 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 187852.05, 'new_value': 196738.57}, {'field': 'offline_amount', 'old_value': 96326.25, 'new_value': 98125.45}, {'field': 'total_amount', 'old_value': 284178.3, 'new_value': 294864.02}, {'field': 'order_count', 'old_value': 1055, 'new_value': 1097}]
2025-05-22 09:01:01,197 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-22 09:01:01,650 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMK
2025-05-22 09:01:01,650 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 190680.87, 'new_value': 196609.93}, {'field': 'total_amount', 'old_value': 263524.96, 'new_value': 269454.02}, {'field': 'order_count', 'old_value': 2807, 'new_value': 2883}]
2025-05-22 09:01:01,650 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-22 09:01:02,165 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNK
2025-05-22 09:01:02,165 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9236.0, 'new_value': 9632.0}, {'field': 'total_amount', 'old_value': 9236.0, 'new_value': 9632.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-05-22 09:01:02,165 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-22 09:01:02,587 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHU
2025-05-22 09:01:02,587 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18687.45, 'new_value': 19174.29}, {'field': 'offline_amount', 'old_value': 254741.59, 'new_value': 261713.64}, {'field': 'total_amount', 'old_value': 273429.04, 'new_value': 280887.93}, {'field': 'order_count', 'old_value': 1271, 'new_value': 1311}]
2025-05-22 09:01:02,587 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPK
2025-05-22 09:01:03,103 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPK
2025-05-22 09:01:03,103 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40862.0, 'new_value': 43364.0}, {'field': 'total_amount', 'old_value': 40862.0, 'new_value': 43364.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-05-22 09:01:03,103 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-22 09:01:03,603 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIU
2025-05-22 09:01:03,603 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29087.6, 'new_value': 30530.29}, {'field': 'offline_amount', 'old_value': 457092.01, 'new_value': 469286.61}, {'field': 'total_amount', 'old_value': 486179.61, 'new_value': 499816.9}, {'field': 'order_count', 'old_value': 2647, 'new_value': 2714}]
2025-05-22 09:01:03,603 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-22 09:01:04,040 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQK
2025-05-22 09:01:04,040 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 72024.06, 'new_value': 74053.06}, {'field': 'offline_amount', 'old_value': 918072.12, 'new_value': 964005.6}, {'field': 'total_amount', 'old_value': 990096.18, 'new_value': 1038058.66}, {'field': 'order_count', 'old_value': 8002, 'new_value': 8428}]
2025-05-22 09:01:04,040 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-22 09:01:04,478 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUK
2025-05-22 09:01:04,478 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 68977.75, 'new_value': 69591.75}, {'field': 'total_amount', 'old_value': 68977.75, 'new_value': 69591.75}, {'field': 'order_count', 'old_value': 388, 'new_value': 394}]
2025-05-22 09:01:04,478 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-22 09:01:05,040 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZK
2025-05-22 09:01:05,040 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6926.22, 'new_value': 7025.82}, {'field': 'offline_amount', 'old_value': 24001.0, 'new_value': 25576.0}, {'field': 'total_amount', 'old_value': 30927.22, 'new_value': 32601.82}, {'field': 'order_count', 'old_value': 173, 'new_value': 182}]
2025-05-22 09:01:05,040 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-22 09:01:05,556 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3L
2025-05-22 09:01:05,556 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39090.07, 'new_value': 40029.47}, {'field': 'total_amount', 'old_value': 39286.87, 'new_value': 40226.27}, {'field': 'order_count', 'old_value': 333, 'new_value': 342}]
2025-05-22 09:01:05,556 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-22 09:01:05,978 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5L
2025-05-22 09:01:05,978 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4554.0, 'new_value': 4714.0}, {'field': 'offline_amount', 'old_value': 20333.8, 'new_value': 20938.0}, {'field': 'total_amount', 'old_value': 24887.8, 'new_value': 25652.0}, {'field': 'order_count', 'old_value': 988, 'new_value': 1027}]
2025-05-22 09:01:05,978 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-22 09:01:06,431 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6L
2025-05-22 09:01:06,431 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77346.86, 'new_value': 77849.46}, {'field': 'total_amount', 'old_value': 77346.86, 'new_value': 77849.46}, {'field': 'order_count', 'old_value': 280, 'new_value': 286}]
2025-05-22 09:01:06,431 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-22 09:01:08,103 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM8L
2025-05-22 09:01:08,103 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23078.8, 'new_value': 23537.4}, {'field': 'offline_amount', 'old_value': 18060.6, 'new_value': 18195.6}, {'field': 'total_amount', 'old_value': 41139.4, 'new_value': 41733.0}, {'field': 'order_count', 'old_value': 220, 'new_value': 224}]
2025-05-22 09:01:08,103 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMCL
2025-05-22 09:01:08,525 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMCL
2025-05-22 09:01:08,525 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79587.0, 'new_value': 85586.0}, {'field': 'total_amount', 'old_value': 79587.0, 'new_value': 85586.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-05-22 09:01:08,525 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-22 09:01:08,915 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMEL
2025-05-22 09:01:08,915 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 169323.7, 'new_value': 173840.5}, {'field': 'total_amount', 'old_value': 169323.7, 'new_value': 173840.5}, {'field': 'order_count', 'old_value': 627, 'new_value': 644}]
2025-05-22 09:01:08,915 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-22 09:01:09,353 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFL
2025-05-22 09:01:09,368 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41006.0, 'new_value': 41861.0}, {'field': 'total_amount', 'old_value': 43050.0, 'new_value': 43905.0}, {'field': 'order_count', 'old_value': 176, 'new_value': 178}]
2025-05-22 09:01:09,368 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-22 09:01:09,806 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMU
2025-05-22 09:01:09,806 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 272468.0, 'new_value': 280017.0}, {'field': 'total_amount', 'old_value': 272468.0, 'new_value': 280017.0}, {'field': 'order_count', 'old_value': 220, 'new_value': 229}]
2025-05-22 09:01:09,806 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-22 09:01:10,368 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHL
2025-05-22 09:01:10,368 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13203.27, 'new_value': 14227.21}, {'field': 'offline_amount', 'old_value': 216960.64, 'new_value': 226788.84}, {'field': 'total_amount', 'old_value': 230163.91, 'new_value': 241016.05}, {'field': 'order_count', 'old_value': 12652, 'new_value': 13305}]
2025-05-22 09:01:10,368 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-22 09:01:10,931 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOU
2025-05-22 09:01:10,931 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 256484.4, 'new_value': 260628.3}, {'field': 'total_amount', 'old_value': 256484.4, 'new_value': 260628.3}, {'field': 'order_count', 'old_value': 2831, 'new_value': 2875}]
2025-05-22 09:01:10,931 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-22 09:01:11,368 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLL
2025-05-22 09:01:11,368 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43967.53, 'new_value': 44906.99}, {'field': 'offline_amount', 'old_value': 26962.0, 'new_value': 28851.0}, {'field': 'total_amount', 'old_value': 70929.53, 'new_value': 73757.99}, {'field': 'order_count', 'old_value': 877, 'new_value': 911}]
2025-05-22 09:01:11,368 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-22 09:01:11,790 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMML
2025-05-22 09:01:11,790 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122161.68, 'new_value': 126975.4}, {'field': 'total_amount', 'old_value': 122161.68, 'new_value': 126975.4}, {'field': 'order_count', 'old_value': 603, 'new_value': 627}]
2025-05-22 09:01:11,790 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-22 09:01:12,290 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNL
2025-05-22 09:01:12,290 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18564.92, 'new_value': 19386.73}, {'field': 'offline_amount', 'old_value': 33312.58, 'new_value': 35271.58}, {'field': 'total_amount', 'old_value': 51877.5, 'new_value': 54658.31}, {'field': 'order_count', 'old_value': 1863, 'new_value': 1964}]
2025-05-22 09:01:12,290 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-22 09:01:12,884 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOL
2025-05-22 09:01:12,884 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57502.0, 'new_value': 61559.0}, {'field': 'total_amount', 'old_value': 59910.0, 'new_value': 63967.0}, {'field': 'order_count', 'old_value': 247, 'new_value': 260}]
2025-05-22 09:01:12,884 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQL
2025-05-22 09:01:13,322 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMQL
2025-05-22 09:01:13,322 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49158.0, 'new_value': 51240.0}, {'field': 'total_amount', 'old_value': 49158.0, 'new_value': 51240.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-05-22 09:01:13,322 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-22 09:01:13,822 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRL
2025-05-22 09:01:13,822 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18754.7, 'new_value': 18990.7}, {'field': 'offline_amount', 'old_value': 48064.46, 'new_value': 49729.96}, {'field': 'total_amount', 'old_value': 66819.16, 'new_value': 68720.66}, {'field': 'order_count', 'old_value': 757, 'new_value': 785}]
2025-05-22 09:01:13,822 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-22 09:01:14,228 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRU
2025-05-22 09:01:14,228 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 202627.0, 'new_value': 204553.5}, {'field': 'total_amount', 'old_value': 202627.0, 'new_value': 204553.5}, {'field': 'order_count', 'old_value': 354, 'new_value': 361}]
2025-05-22 09:01:14,228 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-22 09:01:14,650 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSU
2025-05-22 09:01:14,650 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 78819.47, 'new_value': 95822.79}, {'field': 'total_amount', 'old_value': 508125.72, 'new_value': 525129.04}, {'field': 'order_count', 'old_value': 2159, 'new_value': 2204}]
2025-05-22 09:01:14,650 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTU
2025-05-22 09:01:15,150 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTU
2025-05-22 09:01:15,150 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100128.0, 'new_value': 108627.0}, {'field': 'total_amount', 'old_value': 100128.0, 'new_value': 108627.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-05-22 09:01:15,150 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-22 09:01:15,587 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUU
2025-05-22 09:01:15,587 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 168529.0, 'new_value': 172966.0}, {'field': 'total_amount', 'old_value': 168662.0, 'new_value': 173099.0}, {'field': 'order_count', 'old_value': 115, 'new_value': 121}]
2025-05-22 09:01:15,587 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-22 09:01:16,118 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSL
2025-05-22 09:01:16,118 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 83198.6, 'new_value': 84288.0}, {'field': 'offline_amount', 'old_value': 113709.79, 'new_value': 118736.57}, {'field': 'total_amount', 'old_value': 196908.39, 'new_value': 203024.57}, {'field': 'order_count', 'old_value': 1345, 'new_value': 1359}]
2025-05-22 09:01:16,118 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-22 09:01:16,572 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVU
2025-05-22 09:01:16,572 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 88008.19, 'new_value': 91229.46}, {'field': 'total_amount', 'old_value': 93619.71, 'new_value': 96840.98}, {'field': 'order_count', 'old_value': 8411, 'new_value': 8676}]
2025-05-22 09:01:16,572 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-22 09:01:17,087 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXL
2025-05-22 09:01:17,087 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26215.7, 'new_value': 27311.77}, {'field': 'total_amount', 'old_value': 28002.6, 'new_value': 29098.67}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-05-22 09:01:17,087 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-22 09:01:17,493 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7O
2025-05-22 09:01:17,493 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42747.0, 'new_value': 44123.0}, {'field': 'total_amount', 'old_value': 43096.0, 'new_value': 44472.0}, {'field': 'order_count', 'old_value': 77, 'new_value': 81}]
2025-05-22 09:01:17,493 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-22 09:01:17,947 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWU
2025-05-22 09:01:17,947 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64325.0, 'new_value': 64935.0}, {'field': 'total_amount', 'old_value': 64325.0, 'new_value': 64935.0}, {'field': 'order_count', 'old_value': 85, 'new_value': 86}]
2025-05-22 09:01:17,947 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-22 09:01:18,415 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8O
2025-05-22 09:01:18,415 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97879.48, 'new_value': 100813.08}, {'field': 'total_amount', 'old_value': 97879.48, 'new_value': 100813.08}, {'field': 'order_count', 'old_value': 2858, 'new_value': 2959}]
2025-05-22 09:01:18,415 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-22 09:01:18,822 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9O
2025-05-22 09:01:18,822 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 98790.36, 'new_value': 105457.07}, {'field': 'offline_amount', 'old_value': 220425.81, 'new_value': 229020.51}, {'field': 'total_amount', 'old_value': 319216.17, 'new_value': 334477.58}, {'field': 'order_count', 'old_value': 3801, 'new_value': 3976}]
2025-05-22 09:01:18,822 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-22 09:01:19,275 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1V
2025-05-22 09:01:19,275 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 319395.21, 'new_value': 326257.71}, {'field': 'total_amount', 'old_value': 319395.21, 'new_value': 326257.71}, {'field': 'order_count', 'old_value': 1559, 'new_value': 1599}]
2025-05-22 09:01:19,275 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-22 09:01:19,775 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBO
2025-05-22 09:01:19,775 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 180096.0, 'new_value': 189696.0}, {'field': 'total_amount', 'old_value': 180096.0, 'new_value': 189696.0}, {'field': 'order_count', 'old_value': 15008, 'new_value': 15808}]
2025-05-22 09:01:19,775 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-22 09:01:20,275 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDO
2025-05-22 09:01:20,275 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36282.0, 'new_value': 38117.0}, {'field': 'total_amount', 'old_value': 36282.0, 'new_value': 38117.0}, {'field': 'order_count', 'old_value': 52, 'new_value': 55}]
2025-05-22 09:01:20,275 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-22 09:01:20,759 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMHM
2025-05-22 09:01:20,759 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1546.5, 'new_value': 1746.5}, {'field': 'offline_amount', 'old_value': 37502.0, 'new_value': 39362.0}, {'field': 'total_amount', 'old_value': 39048.5, 'new_value': 41108.5}, {'field': 'order_count', 'old_value': 123, 'new_value': 129}]
2025-05-22 09:01:20,759 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-22 09:01:21,228 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEO
2025-05-22 09:01:21,228 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36568.95, 'new_value': 38219.6}, {'field': 'total_amount', 'old_value': 36568.95, 'new_value': 38219.6}, {'field': 'order_count', 'old_value': 1611, 'new_value': 1688}]
2025-05-22 09:01:21,228 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-22 09:01:21,681 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFO
2025-05-22 09:01:21,697 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 86731.8, 'new_value': 93157.8}, {'field': 'total_amount', 'old_value': 201697.05, 'new_value': 208123.05}, {'field': 'order_count', 'old_value': 5267, 'new_value': 5468}]
2025-05-22 09:01:21,697 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-22 09:01:22,134 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMNM
2025-05-22 09:01:22,134 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 216586.0, 'new_value': 227918.0}, {'field': 'total_amount', 'old_value': 216586.0, 'new_value': 227918.0}, {'field': 'order_count', 'old_value': 103, 'new_value': 106}]
2025-05-22 09:01:22,134 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-22 09:01:22,571 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4V
2025-05-22 09:01:22,571 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59971.8, 'new_value': 62989.73}, {'field': 'offline_amount', 'old_value': 210554.14, 'new_value': 216853.22}, {'field': 'total_amount', 'old_value': 270525.94, 'new_value': 279842.95}, {'field': 'order_count', 'old_value': 3378, 'new_value': 3449}]
2025-05-22 09:01:22,571 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-22 09:01:23,025 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5V
2025-05-22 09:01:23,025 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56522.9, 'new_value': 58700.5}, {'field': 'total_amount', 'old_value': 56522.9, 'new_value': 58700.5}, {'field': 'order_count', 'old_value': 1606, 'new_value': 1611}]
2025-05-22 09:01:23,025 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-22 09:01:23,540 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMSM
2025-05-22 09:01:23,540 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36214.79, 'new_value': 38112.1}, {'field': 'total_amount', 'old_value': 36214.79, 'new_value': 38112.1}, {'field': 'order_count', 'old_value': 146, 'new_value': 154}]
2025-05-22 09:01:23,540 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-22 09:01:24,040 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHO
2025-05-22 09:01:24,040 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45586.1, 'new_value': 48083.92}, {'field': 'offline_amount', 'old_value': 27494.43, 'new_value': 28988.41}, {'field': 'total_amount', 'old_value': 73080.53, 'new_value': 77072.33}, {'field': 'order_count', 'old_value': 3974, 'new_value': 4191}]
2025-05-22 09:01:24,040 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-22 09:01:24,478 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTM
2025-05-22 09:01:24,478 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86224.0, 'new_value': 86323.0}, {'field': 'total_amount', 'old_value': 107481.41, 'new_value': 107580.41}, {'field': 'order_count', 'old_value': 89, 'new_value': 90}]
2025-05-22 09:01:24,478 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-22 09:01:24,900 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIO
2025-05-22 09:01:24,900 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18041.18, 'new_value': 18579.18}, {'field': 'total_amount', 'old_value': 18041.18, 'new_value': 18579.18}, {'field': 'order_count', 'old_value': 152, 'new_value': 155}]
2025-05-22 09:01:24,900 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-22 09:01:25,259 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8V
2025-05-22 09:01:25,259 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 587248.15, 'new_value': 599111.15}, {'field': 'total_amount', 'old_value': 587248.15, 'new_value': 599111.15}, {'field': 'order_count', 'old_value': 1539, 'new_value': 1580}]
2025-05-22 09:01:25,259 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-22 09:01:25,728 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJO
2025-05-22 09:01:25,728 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 347703.24, 'new_value': 365790.1}, {'field': 'total_amount', 'old_value': 347703.24, 'new_value': 365790.1}, {'field': 'order_count', 'old_value': 1249, 'new_value': 1310}]
2025-05-22 09:01:25,728 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-22 09:01:26,196 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKO
2025-05-22 09:01:26,196 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 253775.6, 'new_value': 261970.5}, {'field': 'total_amount', 'old_value': 253775.6, 'new_value': 261970.5}, {'field': 'order_count', 'old_value': 6375, 'new_value': 6642}]
2025-05-22 09:01:26,196 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-22 09:01:26,790 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLO
2025-05-22 09:01:26,790 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31103.98, 'new_value': 32631.04}, {'field': 'total_amount', 'old_value': 31103.98, 'new_value': 32631.04}, {'field': 'order_count', 'old_value': 3991, 'new_value': 4195}]
2025-05-22 09:01:26,790 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-22 09:01:27,290 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9V
2025-05-22 09:01:27,290 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 627468.0, 'new_value': 640075.0}, {'field': 'total_amount', 'old_value': 627468.0, 'new_value': 640075.0}, {'field': 'order_count', 'old_value': 94, 'new_value': 97}]
2025-05-22 09:01:27,290 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-22 09:01:27,837 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMO
2025-05-22 09:01:27,837 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 20661.17, 'new_value': 21561.07}, {'field': 'offline_amount', 'old_value': 26544.87, 'new_value': 27469.46}, {'field': 'total_amount', 'old_value': 47206.04, 'new_value': 49030.53}, {'field': 'order_count', 'old_value': 2104, 'new_value': 2199}]
2025-05-22 09:01:27,837 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-22 09:01:28,243 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNO
2025-05-22 09:01:28,243 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67679.0, 'new_value': 70472.0}, {'field': 'total_amount', 'old_value': 72880.0, 'new_value': 75673.0}, {'field': 'order_count', 'old_value': 209, 'new_value': 216}]
2025-05-22 09:01:28,243 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-22 09:01:28,681 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOO
2025-05-22 09:01:28,681 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 243678.8, 'new_value': 244304.8}, {'field': 'total_amount', 'old_value': 243678.8, 'new_value': 244304.8}, {'field': 'order_count', 'old_value': 59, 'new_value': 60}]
2025-05-22 09:01:28,681 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-22 09:01:29,118 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPO
2025-05-22 09:01:29,118 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 367260.88, 'new_value': 379569.88}, {'field': 'total_amount', 'old_value': 367260.88, 'new_value': 379569.88}, {'field': 'order_count', 'old_value': 1828, 'new_value': 1898}]
2025-05-22 09:01:29,118 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-22 09:01:29,681 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDV
2025-05-22 09:01:29,681 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1252.0, 'new_value': 1596.0}, {'field': 'offline_amount', 'old_value': 518851.0, 'new_value': 559986.0}, {'field': 'total_amount', 'old_value': 520103.0, 'new_value': 561582.0}, {'field': 'order_count', 'old_value': 231, 'new_value': 238}]
2025-05-22 09:01:29,681 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-22 09:01:30,071 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQO
2025-05-22 09:01:30,071 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 131551.0, 'new_value': 131580.0}, {'field': 'total_amount', 'old_value': 131551.0, 'new_value': 131580.0}, {'field': 'order_count', 'old_value': 77, 'new_value': 78}]
2025-05-22 09:01:30,071 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-22 09:01:30,556 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4N
2025-05-22 09:01:30,556 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25310.9, 'new_value': 25409.9}, {'field': 'total_amount', 'old_value': 34521.2, 'new_value': 34620.2}, {'field': 'order_count', 'old_value': 85, 'new_value': 86}]
2025-05-22 09:01:30,556 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-22 09:01:30,978 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEV
2025-05-22 09:01:30,978 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 162855.0, 'new_value': 166265.0}, {'field': 'total_amount', 'old_value': 162855.0, 'new_value': 166265.0}, {'field': 'order_count', 'old_value': 245, 'new_value': 252}]
2025-05-22 09:01:30,978 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO
2025-05-22 09:01:31,431 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSO
2025-05-22 09:01:31,431 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 962895.0, 'new_value': 983084.0}, {'field': 'total_amount', 'old_value': 962895.0, 'new_value': 983084.0}, {'field': 'order_count', 'old_value': 116, 'new_value': 119}]
2025-05-22 09:01:31,431 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-22 09:01:31,931 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTO
2025-05-22 09:01:31,931 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34787.1, 'new_value': 40013.4}, {'field': 'offline_amount', 'old_value': 46860.37, 'new_value': 47092.57}, {'field': 'total_amount', 'old_value': 81647.47, 'new_value': 87105.97}, {'field': 'order_count', 'old_value': 276, 'new_value': 295}]
2025-05-22 09:01:31,931 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-22 09:01:32,431 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGV
2025-05-22 09:01:32,431 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 256970.0, 'new_value': 262365.0}, {'field': 'total_amount', 'old_value': 256970.0, 'new_value': 262365.0}, {'field': 'order_count', 'old_value': 63, 'new_value': 66}]
2025-05-22 09:01:32,431 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUO
2025-05-22 09:01:32,853 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMUO
2025-05-22 09:01:32,853 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72000.0, 'new_value': 77400.0}, {'field': 'total_amount', 'old_value': 72000.0, 'new_value': 77400.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-05-22 09:01:32,853 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-22 09:01:33,353 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAN
2025-05-22 09:01:33,353 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 276698.03, 'new_value': 285284.69}, {'field': 'total_amount', 'old_value': 276698.03, 'new_value': 285284.69}, {'field': 'order_count', 'old_value': 2559, 'new_value': 2659}]
2025-05-22 09:01:33,353 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-22 09:01:33,743 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVO
2025-05-22 09:01:33,743 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 269192.92, 'new_value': 277378.92}, {'field': 'total_amount', 'old_value': 275858.42, 'new_value': 284044.42}, {'field': 'order_count', 'old_value': 2373, 'new_value': 2441}]
2025-05-22 09:01:33,743 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-22 09:01:34,165 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWO
2025-05-22 09:01:34,165 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16726.0, 'new_value': 17798.0}, {'field': 'total_amount', 'old_value': 16726.0, 'new_value': 17798.0}, {'field': 'order_count', 'old_value': 85, 'new_value': 93}]
2025-05-22 09:01:34,165 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-22 09:01:34,603 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMXO
2025-05-22 09:01:34,603 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 22096.4, 'new_value': 23304.8}, {'field': 'total_amount', 'old_value': 22096.4, 'new_value': 23304.8}, {'field': 'order_count', 'old_value': 609, 'new_value': 640}]
2025-05-22 09:01:34,603 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-22 09:01:35,040 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYO
2025-05-22 09:01:35,040 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6219.1, 'new_value': 6298.1}, {'field': 'offline_amount', 'old_value': 31865.95, 'new_value': 32556.65}, {'field': 'total_amount', 'old_value': 38085.05, 'new_value': 38854.75}, {'field': 'order_count', 'old_value': 441, 'new_value': 449}]
2025-05-22 09:01:35,040 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-22 09:01:35,525 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQD
2025-05-22 09:01:35,525 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5560.0, 'new_value': 6096.0}, {'field': 'offline_amount', 'old_value': 27519.0, 'new_value': 29001.0}, {'field': 'total_amount', 'old_value': 33079.0, 'new_value': 35097.0}, {'field': 'order_count', 'old_value': 262, 'new_value': 273}]
2025-05-22 09:01:35,525 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-22 09:01:35,993 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZO
2025-05-22 09:01:35,993 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14258.0, 'new_value': 26832.8}, {'field': 'total_amount', 'old_value': 27847.0, 'new_value': 40421.8}, {'field': 'order_count', 'old_value': 77, 'new_value': 80}]
2025-05-22 09:01:35,993 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-22 09:01:36,634 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0P
2025-05-22 09:01:36,634 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25211.72, 'new_value': 27063.93}, {'field': 'total_amount', 'old_value': 25211.72, 'new_value': 27063.93}, {'field': 'order_count', 'old_value': 934, 'new_value': 1003}]
2025-05-22 09:01:36,634 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-22 09:01:37,118 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXD
2025-05-22 09:01:37,118 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49538.65, 'new_value': 51650.38}, {'field': 'offline_amount', 'old_value': 371837.65, 'new_value': 382638.05}, {'field': 'total_amount', 'old_value': 421376.3, 'new_value': 434288.43}, {'field': 'order_count', 'old_value': 1992, 'new_value': 2060}]
2025-05-22 09:01:37,118 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-22 09:01:37,540 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIV
2025-05-22 09:01:37,540 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 647234.0, 'new_value': 654709.0}, {'field': 'total_amount', 'old_value': 647234.0, 'new_value': 654709.0}, {'field': 'order_count', 'old_value': 152, 'new_value': 154}]
2025-05-22 09:01:37,540 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-22 09:01:39,040 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1P
2025-05-22 09:01:39,040 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 703141.0, 'new_value': 727764.0}, {'field': 'total_amount', 'old_value': 703141.0, 'new_value': 727764.0}, {'field': 'order_count', 'old_value': 3161, 'new_value': 3282}]
2025-05-22 09:01:39,040 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-22 09:01:39,493 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM2P
2025-05-22 09:01:39,493 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28068.0, 'new_value': 29062.0}, {'field': 'total_amount', 'old_value': 28068.0, 'new_value': 29062.0}, {'field': 'order_count', 'old_value': 14, 'new_value': 15}]
2025-05-22 09:01:39,493 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-22 09:01:39,993 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJV
2025-05-22 09:01:39,993 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65496.0, 'new_value': 66867.0}, {'field': 'total_amount', 'old_value': 65496.0, 'new_value': 66867.0}, {'field': 'order_count', 'old_value': 163, 'new_value': 168}]
2025-05-22 09:01:39,993 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-22 09:01:40,431 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM1E
2025-05-22 09:01:40,431 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39626.12, 'new_value': 41849.23}, {'field': 'offline_amount', 'old_value': 285511.15, 'new_value': 294588.03}, {'field': 'total_amount', 'old_value': 325137.27, 'new_value': 336437.26}, {'field': 'order_count', 'old_value': 2057, 'new_value': 2126}]
2025-05-22 09:01:40,431 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-22 09:01:40,946 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2E
2025-05-22 09:01:40,946 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 65845.0, 'new_value': 67125.0}, {'field': 'total_amount', 'old_value': 65845.0, 'new_value': 67125.0}, {'field': 'order_count', 'old_value': 1944, 'new_value': 1982}]
2025-05-22 09:01:40,946 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-22 09:01:41,446 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMMV
2025-05-22 09:01:41,446 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106603.5, 'new_value': 110973.1}, {'field': 'total_amount', 'old_value': 106603.5, 'new_value': 110973.1}, {'field': 'order_count', 'old_value': 203, 'new_value': 212}]
2025-05-22 09:01:41,446 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-22 09:01:41,931 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3P
2025-05-22 09:01:41,931 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 118459.0, 'new_value': 120294.0}, {'field': 'total_amount', 'old_value': 118459.0, 'new_value': 120294.0}, {'field': 'order_count', 'old_value': 3783, 'new_value': 3843}]
2025-05-22 09:01:41,931 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-22 09:01:42,384 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4P
2025-05-22 09:01:42,384 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 100387.16, 'new_value': 108953.71}, {'field': 'offline_amount', 'old_value': 300275.44, 'new_value': 304657.43}, {'field': 'total_amount', 'old_value': 400662.6, 'new_value': 413611.14}, {'field': 'order_count', 'old_value': 3429, 'new_value': 3619}]
2025-05-22 09:01:42,384 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-22 09:01:42,853 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5P
2025-05-22 09:01:42,853 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 76100.07, 'new_value': 80207.06}, {'field': 'offline_amount', 'old_value': 89888.01, 'new_value': 94318.03}, {'field': 'total_amount', 'old_value': 165988.08, 'new_value': 174525.09}, {'field': 'order_count', 'old_value': 6698, 'new_value': 7029}]
2025-05-22 09:01:42,853 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-22 09:01:43,353 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6P
2025-05-22 09:01:43,353 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 202931.72, 'new_value': 208271.42}, {'field': 'offline_amount', 'old_value': 85402.78, 'new_value': 85937.78}, {'field': 'total_amount', 'old_value': 288334.5, 'new_value': 294209.2}, {'field': 'order_count', 'old_value': 514, 'new_value': 528}]
2025-05-22 09:01:43,353 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-22 09:01:43,790 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7P
2025-05-22 09:01:43,790 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47471.0, 'new_value': 48087.0}, {'field': 'total_amount', 'old_value': 47471.0, 'new_value': 48087.0}, {'field': 'order_count', 'old_value': 102, 'new_value': 104}]
2025-05-22 09:01:43,790 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-22 09:01:44,212 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNV
2025-05-22 09:01:44,212 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 96279.8, 'new_value': 98659.8}, {'field': 'offline_amount', 'old_value': 64885.56, 'new_value': 66883.88}, {'field': 'total_amount', 'old_value': 161165.36, 'new_value': 165543.68}, {'field': 'order_count', 'old_value': 1084, 'new_value': 1110}]
2025-05-22 09:01:44,212 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-22 09:01:44,696 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMOV
2025-05-22 09:01:44,696 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 293196.0, 'new_value': 303723.2}, {'field': 'total_amount', 'old_value': 293196.0, 'new_value': 303723.2}, {'field': 'order_count', 'old_value': 362, 'new_value': 376}]
2025-05-22 09:01:44,696 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-22 09:01:45,118 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMPV
2025-05-22 09:01:45,118 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 765716.0, 'new_value': 797051.0}, {'field': 'total_amount', 'old_value': 765716.0, 'new_value': 797051.0}, {'field': 'order_count', 'old_value': 852, 'new_value': 888}]
2025-05-22 09:01:45,118 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-22 09:01:45,759 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8P
2025-05-22 09:01:45,759 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 567104.72, 'new_value': 587612.72}, {'field': 'total_amount', 'old_value': 567104.72, 'new_value': 587612.72}, {'field': 'order_count', 'old_value': 4326, 'new_value': 4496}]
2025-05-22 09:01:45,759 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-22 09:01:46,165 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMKE
2025-05-22 09:01:46,165 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21253.0, 'new_value': 23645.0}, {'field': 'total_amount', 'old_value': 21253.0, 'new_value': 23645.0}, {'field': 'order_count', 'old_value': 114, 'new_value': 130}]
2025-05-22 09:01:46,165 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-22 09:01:46,649 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMQV
2025-05-22 09:01:46,649 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 531203.0, 'new_value': 555299.0}, {'field': 'total_amount', 'old_value': 531203.0, 'new_value': 555299.0}, {'field': 'order_count', 'old_value': 59, 'new_value': 62}]
2025-05-22 09:01:46,649 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-22 09:01:47,103 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMRV
2025-05-22 09:01:47,103 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 276102.45, 'new_value': 283517.58}, {'field': 'offline_amount', 'old_value': 1046282.83, 'new_value': 1078327.22}, {'field': 'total_amount', 'old_value': 1322385.28, 'new_value': 1361844.8}, {'field': 'order_count', 'old_value': 6632, 'new_value': 6830}]
2025-05-22 09:01:47,103 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9P
2025-05-22 09:01:47,556 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9P
2025-05-22 09:01:47,556 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28210.0, 'new_value': 33514.0}, {'field': 'total_amount', 'old_value': 28210.0, 'new_value': 33514.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 48}]
2025-05-22 09:01:47,556 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-22 09:01:47,978 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAP
2025-05-22 09:01:47,978 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47265.45, 'new_value': 49110.45}, {'field': 'offline_amount', 'old_value': 37550.33, 'new_value': 38686.91}, {'field': 'total_amount', 'old_value': 84815.78, 'new_value': 87797.36}, {'field': 'order_count', 'old_value': 1671, 'new_value': 1739}]
2025-05-22 09:01:47,978 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCP
2025-05-22 09:01:48,462 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCP
2025-05-22 09:01:48,462 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 163600.0, 'new_value': 172400.0}, {'field': 'total_amount', 'old_value': 163600.0, 'new_value': 172400.0}, {'field': 'order_count', 'old_value': 21, 'new_value': 23}]
2025-05-22 09:01:48,462 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-22 09:01:48,915 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTV
2025-05-22 09:01:48,915 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 111383.0, 'new_value': 115880.3}, {'field': 'offline_amount', 'old_value': 89164.8, 'new_value': 92709.8}, {'field': 'total_amount', 'old_value': 200547.8, 'new_value': 208590.1}, {'field': 'order_count', 'old_value': 4709, 'new_value': 4915}]
2025-05-22 09:01:48,915 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-22 09:01:49,368 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJF
2025-05-22 09:01:49,368 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 154550.2, 'new_value': 159997.0}, {'field': 'total_amount', 'old_value': 154550.2, 'new_value': 159997.0}, {'field': 'order_count', 'old_value': 1978, 'new_value': 2065}]
2025-05-22 09:01:49,368 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-22 09:01:49,774 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMVV
2025-05-22 09:01:49,774 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 215771.73, 'new_value': 218912.53}, {'field': 'total_amount', 'old_value': 215771.73, 'new_value': 218912.53}, {'field': 'order_count', 'old_value': 1315, 'new_value': 1339}]
2025-05-22 09:01:49,774 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-22 09:01:50,274 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDP
2025-05-22 09:01:50,274 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78774.83, 'new_value': 87799.83}, {'field': 'total_amount', 'old_value': 78774.83, 'new_value': 87799.83}, {'field': 'order_count', 'old_value': 102, 'new_value': 111}]
2025-05-22 09:01:50,274 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-22 09:01:50,774 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMF
2025-05-22 09:01:50,774 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33185.04, 'new_value': 34392.14}, {'field': 'offline_amount', 'old_value': 905102.45, 'new_value': 937005.3}, {'field': 'total_amount', 'old_value': 938287.49, 'new_value': 971397.44}, {'field': 'order_count', 'old_value': 4549, 'new_value': 4725}]
2025-05-22 09:01:50,774 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-22 09:01:51,259 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNF
2025-05-22 09:01:51,259 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43482.85, 'new_value': 45113.7}, {'field': 'total_amount', 'old_value': 43482.85, 'new_value': 45113.7}, {'field': 'order_count', 'old_value': 164, 'new_value': 171}]
2025-05-22 09:01:51,259 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEP
2025-05-22 09:01:51,587 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEP
2025-05-22 09:01:51,587 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94764.0, 'new_value': 101464.0}, {'field': 'total_amount', 'old_value': 94764.0, 'new_value': 101464.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-05-22 09:01:51,587 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-22 09:01:52,024 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTE
2025-05-22 09:01:52,024 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 174192.6, 'new_value': 176047.8}, {'field': 'offline_amount', 'old_value': 404925.2, 'new_value': 408113.2}, {'field': 'total_amount', 'old_value': 579117.8, 'new_value': 584161.0}, {'field': 'order_count', 'old_value': 3856, 'new_value': 3912}]
2025-05-22 09:01:52,024 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-22 09:01:52,478 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXV
2025-05-22 09:01:52,478 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 186075.52, 'new_value': 192970.24}, {'field': 'offline_amount', 'old_value': 625971.55, 'new_value': 645817.26}, {'field': 'total_amount', 'old_value': 812047.07, 'new_value': 838787.5}, {'field': 'order_count', 'old_value': 4751, 'new_value': 4962}]
2025-05-22 09:01:52,478 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-22 09:01:52,931 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMYV
2025-05-22 09:01:52,931 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26229.85, 'new_value': 27431.33}, {'field': 'offline_amount', 'old_value': 291771.45, 'new_value': 299033.95}, {'field': 'total_amount', 'old_value': 318001.3, 'new_value': 326465.28}, {'field': 'order_count', 'old_value': 9448, 'new_value': 9499}]
2025-05-22 09:01:52,931 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-22 09:01:53,353 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMFP
2025-05-22 09:01:53,353 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 459045.0, 'new_value': 479882.0}, {'field': 'total_amount', 'old_value': 459045.0, 'new_value': 479882.0}, {'field': 'order_count', 'old_value': 404, 'new_value': 428}]
2025-05-22 09:01:53,353 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-22 09:01:53,790 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGP
2025-05-22 09:01:53,790 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 218999.92, 'new_value': 224918.52}, {'field': 'offline_amount', 'old_value': 124609.49, 'new_value': 127595.29}, {'field': 'total_amount', 'old_value': 343609.41, 'new_value': 352513.81}, {'field': 'order_count', 'old_value': 2901, 'new_value': 2930}]
2025-05-22 09:01:53,790 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-22 09:01:54,228 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHP
2025-05-22 09:01:54,228 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73432.5, 'new_value': 74127.5}, {'field': 'total_amount', 'old_value': 75161.3, 'new_value': 75856.3}, {'field': 'order_count', 'old_value': 464, 'new_value': 471}]
2025-05-22 09:01:54,228 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-22 09:01:54,634 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIP
2025-05-22 09:01:54,634 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44480.0, 'new_value': 45644.0}, {'field': 'total_amount', 'old_value': 44480.0, 'new_value': 45644.0}, {'field': 'order_count', 'old_value': 102, 'new_value': 106}]
2025-05-22 09:01:54,634 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-22 09:01:55,118 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJP
2025-05-22 09:01:55,118 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 75068.0, 'new_value': 79006.0}, {'field': 'offline_amount', 'old_value': 872629.0, 'new_value': 918591.0}, {'field': 'total_amount', 'old_value': 947697.0, 'new_value': 997597.0}, {'field': 'order_count', 'old_value': 23564, 'new_value': 24999}]
2025-05-22 09:01:55,118 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-22 09:01:55,681 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKP
2025-05-22 09:01:55,681 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 320632.61, 'new_value': 332984.65}, {'field': 'total_amount', 'old_value': 334143.09, 'new_value': 346495.13}, {'field': 'order_count', 'old_value': 1090, 'new_value': 1127}]
2025-05-22 09:01:55,681 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-22 09:01:56,196 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLP
2025-05-22 09:01:56,196 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29908.0, 'new_value': 32271.0}, {'field': 'offline_amount', 'old_value': 220754.0, 'new_value': 234834.0}, {'field': 'total_amount', 'old_value': 250662.0, 'new_value': 267105.0}, {'field': 'order_count', 'old_value': 229, 'new_value': 242}]
2025-05-22 09:01:56,196 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-22 09:01:56,821 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0W
2025-05-22 09:01:56,821 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29963.0, 'new_value': 31878.0}, {'field': 'total_amount', 'old_value': 29963.0, 'new_value': 31878.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 48}]
2025-05-22 09:01:56,821 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-22 09:01:57,243 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMP
2025-05-22 09:01:57,243 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14796.2, 'new_value': 14975.9}, {'field': 'total_amount', 'old_value': 51406.8, 'new_value': 51586.5}, {'field': 'order_count', 'old_value': 533, 'new_value': 536}]
2025-05-22 09:01:57,243 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-22 09:01:57,665 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNP
2025-05-22 09:01:57,665 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85704.0, 'new_value': 88804.0}, {'field': 'total_amount', 'old_value': 85704.0, 'new_value': 88804.0}, {'field': 'order_count', 'old_value': 368, 'new_value': 383}]
2025-05-22 09:01:57,665 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-22 09:01:58,118 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM2W
2025-05-22 09:01:58,118 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54802.1, 'new_value': 55122.1}, {'field': 'total_amount', 'old_value': 55607.1, 'new_value': 55927.1}, {'field': 'order_count', 'old_value': 16288, 'new_value': 16292}]
2025-05-22 09:01:58,118 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPF
2025-05-22 09:01:58,540 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPF
2025-05-22 09:01:58,540 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104333.0, 'new_value': 105641.0}, {'field': 'total_amount', 'old_value': 104333.0, 'new_value': 105641.0}, {'field': 'order_count', 'old_value': 15, 'new_value': 16}]
2025-05-22 09:01:58,540 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-22 09:01:59,024 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPP
2025-05-22 09:01:59,024 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 142928.24, 'new_value': 147559.42}, {'field': 'total_amount', 'old_value': 142928.24, 'new_value': 147559.42}, {'field': 'order_count', 'old_value': 7318, 'new_value': 7630}]
2025-05-22 09:01:59,024 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-22 09:01:59,446 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQP
2025-05-22 09:01:59,446 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 134537.8, 'new_value': 139283.3}, {'field': 'total_amount', 'old_value': 134537.8, 'new_value': 139283.3}, {'field': 'order_count', 'old_value': 597, 'new_value': 616}]
2025-05-22 09:01:59,446 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-22 09:01:59,868 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRP
2025-05-22 09:01:59,884 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 117263.4, 'new_value': 119149.4}, {'field': 'total_amount', 'old_value': 117263.4, 'new_value': 119149.4}, {'field': 'order_count', 'old_value': 3231, 'new_value': 3284}]
2025-05-22 09:01:59,884 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-22 09:02:00,259 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM5W
2025-05-22 09:02:00,259 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1639214.0, 'new_value': 1673074.0}, {'field': 'total_amount', 'old_value': 1639214.0, 'new_value': 1673074.0}, {'field': 'order_count', 'old_value': 6391, 'new_value': 6549}]
2025-05-22 09:02:00,259 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-22 09:02:00,759 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTP
2025-05-22 09:02:00,759 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 86047.9, 'new_value': 88749.5}, {'field': 'total_amount', 'old_value': 86047.9, 'new_value': 88749.5}, {'field': 'order_count', 'old_value': 415, 'new_value': 426}]
2025-05-22 09:02:00,759 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-22 09:02:01,212 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM8W
2025-05-22 09:02:01,212 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 251890.16, 'new_value': 253017.76}, {'field': 'total_amount', 'old_value': 251890.16, 'new_value': 253017.76}, {'field': 'order_count', 'old_value': 1407, 'new_value': 1414}]
2025-05-22 09:02:01,212 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-22 09:02:01,727 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9W
2025-05-22 09:02:01,727 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49423.28, 'new_value': 51852.47}, {'field': 'offline_amount', 'old_value': 38007.6, 'new_value': 38719.28}, {'field': 'total_amount', 'old_value': 87430.88, 'new_value': 90571.75}, {'field': 'order_count', 'old_value': 7368, 'new_value': 7623}]
2025-05-22 09:02:01,727 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-22 09:02:02,196 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMVP
2025-05-22 09:02:02,196 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74202.0, 'new_value': 74668.0}, {'field': 'total_amount', 'old_value': 74202.0, 'new_value': 74668.0}, {'field': 'order_count', 'old_value': 1690, 'new_value': 1691}]
2025-05-22 09:02:02,196 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-22 09:02:02,696 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMBW
2025-05-22 09:02:02,696 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80518.56, 'new_value': 83103.56}, {'field': 'total_amount', 'old_value': 80518.56, 'new_value': 83103.56}, {'field': 'order_count', 'old_value': 4162, 'new_value': 4281}]
2025-05-22 09:02:02,696 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-22 09:02:03,165 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMWP
2025-05-22 09:02:03,165 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1260.43, 'new_value': 1874.54}, {'field': 'offline_amount', 'old_value': 73067.55, 'new_value': 74351.15}, {'field': 'total_amount', 'old_value': 74327.98, 'new_value': 76225.69}, {'field': 'order_count', 'old_value': 340, 'new_value': 359}]
2025-05-22 09:02:03,165 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-22 09:02:03,743 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCW
2025-05-22 09:02:03,743 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 530938.0, 'new_value': 533179.0}, {'field': 'total_amount', 'old_value': 530938.0, 'new_value': 533179.0}, {'field': 'order_count', 'old_value': 83, 'new_value': 84}]
2025-05-22 09:02:03,743 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-22 09:02:04,149 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDW
2025-05-22 09:02:04,149 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 557503.0, 'new_value': 569024.0}, {'field': 'total_amount', 'old_value': 557503.0, 'new_value': 569024.0}, {'field': 'order_count', 'old_value': 402, 'new_value': 414}]
2025-05-22 09:02:04,149 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-22 09:02:04,681 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM0Q
2025-05-22 09:02:04,681 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43756.5, 'new_value': 45660.78}, {'field': 'offline_amount', 'old_value': 41250.1, 'new_value': 44123.35}, {'field': 'total_amount', 'old_value': 85006.6, 'new_value': 89784.13}, {'field': 'order_count', 'old_value': 4337, 'new_value': 4571}]
2025-05-22 09:02:04,681 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-22 09:02:05,149 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM1Q
2025-05-22 09:02:05,149 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 83453.34, 'new_value': 88063.27}, {'field': 'offline_amount', 'old_value': 92555.62, 'new_value': 98348.71}, {'field': 'total_amount', 'old_value': 176008.96, 'new_value': 186411.98}, {'field': 'order_count', 'old_value': 4436, 'new_value': 4680}]
2025-05-22 09:02:05,149 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3Q
2025-05-22 09:02:05,571 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM3Q
2025-05-22 09:02:05,571 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 44640.0, 'new_value': 55440.0}, {'field': 'total_amount', 'old_value': 44640.0, 'new_value': 55440.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 8}]
2025-05-22 09:02:05,571 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-22 09:02:06,056 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM4Q
2025-05-22 09:02:06,056 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 794693.0, 'new_value': 824258.0}, {'field': 'total_amount', 'old_value': 794693.0, 'new_value': 824258.0}, {'field': 'order_count', 'old_value': 925, 'new_value': 967}]
2025-05-22 09:02:06,056 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-22 09:02:06,462 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM5Q
2025-05-22 09:02:06,462 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 165899.5, 'new_value': 170954.3}, {'field': 'total_amount', 'old_value': 171849.8, 'new_value': 176904.6}, {'field': 'order_count', 'old_value': 319, 'new_value': 330}]
2025-05-22 09:02:06,462 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-22 09:02:06,931 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM7Q
2025-05-22 09:02:06,931 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35522.05, 'new_value': 36700.85}, {'field': 'offline_amount', 'old_value': 89949.0, 'new_value': 99337.0}, {'field': 'total_amount', 'old_value': 125471.05, 'new_value': 136037.85}, {'field': 'order_count', 'old_value': 1438, 'new_value': 1496}]
2025-05-22 09:02:06,931 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-22 09:02:07,493 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM8Q
2025-05-22 09:02:07,493 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 110806.0, 'new_value': 115700.0}, {'field': 'offline_amount', 'old_value': 79030.0, 'new_value': 80852.0}, {'field': 'total_amount', 'old_value': 189836.0, 'new_value': 196552.0}, {'field': 'order_count', 'old_value': 2435, 'new_value': 2542}]
2025-05-22 09:02:07,493 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-22 09:02:07,993 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM9Q
2025-05-22 09:02:07,993 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7487.4, 'new_value': 7566.4}, {'field': 'offline_amount', 'old_value': 18603.21, 'new_value': 20664.67}, {'field': 'total_amount', 'old_value': 26090.61, 'new_value': 28231.07}, {'field': 'order_count', 'old_value': 273, 'new_value': 285}]
2025-05-22 09:02:07,993 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-22 09:02:08,509 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAQ
2025-05-22 09:02:08,509 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8678.46, 'new_value': 9010.48}, {'field': 'offline_amount', 'old_value': 131010.0, 'new_value': 136436.0}, {'field': 'total_amount', 'old_value': 139688.46, 'new_value': 145446.48}, {'field': 'order_count', 'old_value': 61, 'new_value': 65}]
2025-05-22 09:02:08,509 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-22 09:02:09,009 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMBQ
2025-05-22 09:02:09,009 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26078.1, 'new_value': 26876.82}, {'field': 'total_amount', 'old_value': 53678.72, 'new_value': 54477.44}, {'field': 'order_count', 'old_value': 236, 'new_value': 242}]
2025-05-22 09:02:09,009 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-22 09:02:09,681 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMQF
2025-05-22 09:02:09,681 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3026.0, 'new_value': 3551.0}, {'field': 'offline_amount', 'old_value': 46261.0, 'new_value': 47401.0}, {'field': 'total_amount', 'old_value': 49287.0, 'new_value': 50952.0}, {'field': 'order_count', 'old_value': 394, 'new_value': 408}]
2025-05-22 09:02:09,681 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-22 09:02:10,165 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMCQ
2025-05-22 09:02:10,165 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 180172.5, 'new_value': 186187.5}, {'field': 'total_amount', 'old_value': 180172.5, 'new_value': 186187.5}, {'field': 'order_count', 'old_value': 888, 'new_value': 922}]
2025-05-22 09:02:10,165 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-22 09:02:10,587 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMDQ
2025-05-22 09:02:10,587 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42759.14, 'new_value': 44941.14}, {'field': 'total_amount', 'old_value': 46923.14, 'new_value': 49105.14}, {'field': 'order_count', 'old_value': 414, 'new_value': 432}]
2025-05-22 09:02:10,587 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-22 09:02:11,040 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMEQ
2025-05-22 09:02:11,040 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 157311.6, 'new_value': 166463.3}, {'field': 'total_amount', 'old_value': 157311.6, 'new_value': 166463.3}, {'field': 'order_count', 'old_value': 582, 'new_value': 623}]
2025-05-22 09:02:11,040 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-22 09:02:11,446 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMGQ
2025-05-22 09:02:11,446 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 56866.0, 'new_value': 57912.0}, {'field': 'offline_amount', 'old_value': 248289.0, 'new_value': 263144.0}, {'field': 'total_amount', 'old_value': 305155.0, 'new_value': 321056.0}, {'field': 'order_count', 'old_value': 1216, 'new_value': 1266}]
2025-05-22 09:02:11,446 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-22 09:02:11,962 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMIQ
2025-05-22 09:02:11,962 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 236908.0, 'new_value': 246508.0}, {'field': 'total_amount', 'old_value': 236908.0, 'new_value': 246508.0}, {'field': 'order_count', 'old_value': 293, 'new_value': 305}]
2025-05-22 09:02:11,962 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-22 09:02:12,446 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMKQ
2025-05-22 09:02:12,446 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 125771.7, 'new_value': 136669.7}, {'field': 'total_amount', 'old_value': 269426.48, 'new_value': 280324.48}]
2025-05-22 09:02:12,446 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-22 09:02:12,852 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMMQ
2025-05-22 09:02:12,852 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43547.5, 'new_value': 45197.0}, {'field': 'total_amount', 'old_value': 43547.5, 'new_value': 45197.0}, {'field': 'order_count', 'old_value': 227, 'new_value': 237}]
2025-05-22 09:02:12,852 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-22 09:02:13,290 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMNQ
2025-05-22 09:02:13,290 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 162821.0, 'new_value': 166647.0}, {'field': 'offline_amount', 'old_value': 141615.0, 'new_value': 150557.0}, {'field': 'total_amount', 'old_value': 304436.0, 'new_value': 317204.0}, {'field': 'order_count', 'old_value': 831, 'new_value': 860}]
2025-05-22 09:02:13,290 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-22 09:02:13,790 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMTC
2025-05-22 09:02:13,790 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 589321.51, 'new_value': 632456.42}, {'field': 'total_amount', 'old_value': 589321.51, 'new_value': 632456.42}, {'field': 'order_count', 'old_value': 3430, 'new_value': 3549}]
2025-05-22 09:02:13,790 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-22 09:02:14,227 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMUC
2025-05-22 09:02:14,227 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 116695.07, 'new_value': 120490.54}, {'field': 'total_amount', 'old_value': 116695.07, 'new_value': 120490.54}, {'field': 'order_count', 'old_value': 8033, 'new_value': 8310}]
2025-05-22 09:02:14,227 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-22 09:02:14,681 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMVC
2025-05-22 09:02:14,681 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 385678.0, 'new_value': 389876.0}, {'field': 'total_amount', 'old_value': 385678.0, 'new_value': 389876.0}, {'field': 'order_count', 'old_value': 8729, 'new_value': 8827}]
2025-05-22 09:02:14,681 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-22 09:02:15,056 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOQ
2025-05-22 09:02:15,056 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82744.0, 'new_value': 85657.0}, {'field': 'total_amount', 'old_value': 82744.0, 'new_value': 85657.0}, {'field': 'order_count', 'old_value': 5599, 'new_value': 5773}]
2025-05-22 09:02:15,056 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-22 09:02:15,462 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMPQ
2025-05-22 09:02:15,462 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 115021.0, 'new_value': 122382.0}, {'field': 'total_amount', 'old_value': 115021.0, 'new_value': 122382.0}, {'field': 'order_count', 'old_value': 8476, 'new_value': 9037}]
2025-05-22 09:02:15,462 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-22 09:02:16,056 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMTQ
2025-05-22 09:02:16,056 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 21517.4, 'new_value': 23314.0}, {'field': 'offline_amount', 'old_value': 49938.6, 'new_value': 52468.9}, {'field': 'total_amount', 'old_value': 71456.0, 'new_value': 75782.9}, {'field': 'order_count', 'old_value': 2690, 'new_value': 2838}]
2025-05-22 09:02:16,056 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-22 09:02:16,446 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMCV
2025-05-22 09:02:16,446 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 265245.0, 'new_value': 279876.0}, {'field': 'total_amount', 'old_value': 265245.0, 'new_value': 279876.0}, {'field': 'order_count', 'old_value': 5756, 'new_value': 6077}]
2025-05-22 09:02:16,446 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMDV
2025-05-22 09:02:16,931 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMDV
2025-05-22 09:02:16,931 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25242.0, 'new_value': 27970.0}, {'field': 'total_amount', 'old_value': 25269.9, 'new_value': 27997.9}, {'field': 'order_count', 'old_value': 24, 'new_value': 26}]
2025-05-22 09:02:16,931 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G
2025-05-22 09:02:17,524 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4G
2025-05-22 09:02:17,524 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 9, 'new_value': 13}]
2025-05-22 09:02:17,524 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-22 09:02:17,946 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDG
2025-05-22 09:02:17,946 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 257232.26, 'new_value': 264897.2}, {'field': 'total_amount', 'old_value': 257232.26, 'new_value': 264897.2}, {'field': 'order_count', 'old_value': 713, 'new_value': 735}]
2025-05-22 09:02:17,946 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-22 09:02:18,431 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIG
2025-05-22 09:02:18,431 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 110651.0, 'new_value': 115821.0}, {'field': 'total_amount', 'old_value': 110651.0, 'new_value': 115821.0}, {'field': 'order_count', 'old_value': 444, 'new_value': 471}]
2025-05-22 09:02:18,431 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-22 09:02:18,852 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMMG
2025-05-22 09:02:18,852 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40293.0, 'new_value': 42849.0}, {'field': 'total_amount', 'old_value': 40293.0, 'new_value': 42849.0}, {'field': 'order_count', 'old_value': 776, 'new_value': 826}]
2025-05-22 09:02:18,852 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-22 09:02:19,306 - INFO - 更新表单数据成功: FINST-7PF66H71TWAVNVL07L2WS61V8FGR37P9O4JAML2
2025-05-22 09:02:19,306 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19859.14, 'new_value': 23188.14}, {'field': 'offline_amount', 'old_value': 45504.29, 'new_value': 47810.29}, {'field': 'total_amount', 'old_value': 65363.43, 'new_value': 70998.43}, {'field': 'order_count', 'old_value': 299, 'new_value': 321}]
2025-05-22 09:02:19,306 - INFO - 日期 2025-05 处理完成 - 更新: 230 条，插入: 0 条，错误: 0 条
2025-05-22 09:02:19,306 - INFO - 数据同步完成！更新: 230 条，插入: 0 条，错误: 0 条
2025-05-22 09:02:19,321 - INFO - =================同步完成====================
2025-05-22 12:00:02,952 - INFO - =================使用默认全量同步=============
2025-05-22 12:00:04,413 - INFO - MySQL查询成功，共获取 3297 条记录
2025-05-22 12:00:04,414 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-22 12:00:04,443 - INFO - 开始处理日期: 2025-01
2025-05-22 12:00:04,450 - INFO - Request Parameters - Page 1:
2025-05-22 12:00:04,450 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 12:00:04,450 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 12:00:05,592 - INFO - Response - Page 1:
2025-05-22 12:00:05,792 - INFO - 第 1 页获取到 100 条记录
2025-05-22 12:00:05,792 - INFO - Request Parameters - Page 2:
2025-05-22 12:00:05,792 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 12:00:05,792 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 12:00:06,325 - INFO - Response - Page 2:
2025-05-22 12:00:06,526 - INFO - 第 2 页获取到 100 条记录
2025-05-22 12:00:06,526 - INFO - Request Parameters - Page 3:
2025-05-22 12:00:06,526 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 12:00:06,526 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 12:00:07,038 - INFO - Response - Page 3:
2025-05-22 12:00:07,245 - INFO - 第 3 页获取到 100 条记录
2025-05-22 12:00:07,245 - INFO - Request Parameters - Page 4:
2025-05-22 12:00:07,245 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 12:00:07,245 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 12:00:07,770 - INFO - Response - Page 4:
2025-05-22 12:00:07,970 - INFO - 第 4 页获取到 100 条记录
2025-05-22 12:00:07,970 - INFO - Request Parameters - Page 5:
2025-05-22 12:00:07,970 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 12:00:07,970 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 12:00:08,489 - INFO - Response - Page 5:
2025-05-22 12:00:08,690 - INFO - 第 5 页获取到 100 条记录
2025-05-22 12:00:08,690 - INFO - Request Parameters - Page 6:
2025-05-22 12:00:08,690 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 12:00:08,690 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 12:00:09,260 - INFO - Response - Page 6:
2025-05-22 12:00:09,460 - INFO - 第 6 页获取到 100 条记录
2025-05-22 12:00:09,460 - INFO - Request Parameters - Page 7:
2025-05-22 12:00:09,460 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 12:00:09,460 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 12:00:09,936 - INFO - Response - Page 7:
2025-05-22 12:00:10,136 - INFO - 第 7 页获取到 82 条记录
2025-05-22 12:00:10,136 - INFO - 查询完成，共获取到 682 条记录
2025-05-22 12:00:10,136 - INFO - 获取到 682 条表单数据
2025-05-22 12:00:10,148 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-22 12:00:10,160 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-22 12:00:10,160 - INFO - 开始处理日期: 2025-02
2025-05-22 12:00:10,160 - INFO - Request Parameters - Page 1:
2025-05-22 12:00:10,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 12:00:10,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 12:00:10,660 - INFO - Response - Page 1:
2025-05-22 12:00:10,861 - INFO - 第 1 页获取到 100 条记录
2025-05-22 12:00:10,861 - INFO - Request Parameters - Page 2:
2025-05-22 12:00:10,861 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 12:00:10,861 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 12:00:11,446 - INFO - Response - Page 2:
2025-05-22 12:00:11,646 - INFO - 第 2 页获取到 100 条记录
2025-05-22 12:00:11,646 - INFO - Request Parameters - Page 3:
2025-05-22 12:00:11,646 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 12:00:11,646 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 12:00:12,228 - INFO - Response - Page 3:
2025-05-22 12:00:12,431 - INFO - 第 3 页获取到 100 条记录
2025-05-22 12:00:12,431 - INFO - Request Parameters - Page 4:
2025-05-22 12:00:12,431 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 12:00:12,431 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 12:00:13,034 - INFO - Response - Page 4:
2025-05-22 12:00:13,235 - INFO - 第 4 页获取到 100 条记录
2025-05-22 12:00:13,235 - INFO - Request Parameters - Page 5:
2025-05-22 12:00:13,235 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 12:00:13,235 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 12:00:13,822 - INFO - Response - Page 5:
2025-05-22 12:00:14,022 - INFO - 第 5 页获取到 100 条记录
2025-05-22 12:00:14,022 - INFO - Request Parameters - Page 6:
2025-05-22 12:00:14,022 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 12:00:14,022 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 12:00:14,564 - INFO - Response - Page 6:
2025-05-22 12:00:14,764 - INFO - 第 6 页获取到 100 条记录
2025-05-22 12:00:14,764 - INFO - Request Parameters - Page 7:
2025-05-22 12:00:14,764 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 12:00:14,764 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 12:00:15,187 - INFO - Response - Page 7:
2025-05-22 12:00:15,390 - INFO - 第 7 页获取到 70 条记录
2025-05-22 12:00:15,390 - INFO - 查询完成，共获取到 670 条记录
2025-05-22 12:00:15,390 - INFO - 获取到 670 条表单数据
2025-05-22 12:00:15,390 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-22 12:00:15,406 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-22 12:00:15,406 - INFO - 开始处理日期: 2025-03
2025-05-22 12:00:15,406 - INFO - Request Parameters - Page 1:
2025-05-22 12:00:15,406 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 12:00:15,406 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 12:00:15,952 - INFO - Response - Page 1:
2025-05-22 12:00:16,156 - INFO - 第 1 页获取到 100 条记录
2025-05-22 12:00:16,156 - INFO - Request Parameters - Page 2:
2025-05-22 12:00:16,156 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 12:00:16,156 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 12:00:16,610 - INFO - Response - Page 2:
2025-05-22 12:00:16,810 - INFO - 第 2 页获取到 100 条记录
2025-05-22 12:00:16,810 - INFO - Request Parameters - Page 3:
2025-05-22 12:00:16,810 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 12:00:16,810 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 12:00:17,317 - INFO - Response - Page 3:
2025-05-22 12:00:17,517 - INFO - 第 3 页获取到 100 条记录
2025-05-22 12:00:17,517 - INFO - Request Parameters - Page 4:
2025-05-22 12:00:17,517 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 12:00:17,517 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 12:00:18,063 - INFO - Response - Page 4:
2025-05-22 12:00:18,263 - INFO - 第 4 页获取到 100 条记录
2025-05-22 12:00:18,263 - INFO - Request Parameters - Page 5:
2025-05-22 12:00:18,263 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 12:00:18,263 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 12:00:18,730 - INFO - Response - Page 5:
2025-05-22 12:00:18,933 - INFO - 第 5 页获取到 100 条记录
2025-05-22 12:00:18,933 - INFO - Request Parameters - Page 6:
2025-05-22 12:00:18,933 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 12:00:18,933 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 12:00:19,485 - INFO - Response - Page 6:
2025-05-22 12:00:19,685 - INFO - 第 6 页获取到 100 条记录
2025-05-22 12:00:19,685 - INFO - Request Parameters - Page 7:
2025-05-22 12:00:19,685 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 12:00:19,685 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 12:00:20,143 - INFO - Response - Page 7:
2025-05-22 12:00:20,343 - INFO - 第 7 页获取到 61 条记录
2025-05-22 12:00:20,343 - INFO - 查询完成，共获取到 661 条记录
2025-05-22 12:00:20,343 - INFO - 获取到 661 条表单数据
2025-05-22 12:00:20,357 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-22 12:00:20,370 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-22 12:00:20,370 - INFO - 开始处理日期: 2025-04
2025-05-22 12:00:20,370 - INFO - Request Parameters - Page 1:
2025-05-22 12:00:20,370 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 12:00:20,370 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 12:00:20,975 - INFO - Response - Page 1:
2025-05-22 12:00:21,186 - INFO - 第 1 页获取到 100 条记录
2025-05-22 12:00:21,186 - INFO - Request Parameters - Page 2:
2025-05-22 12:00:21,186 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 12:00:21,186 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 12:00:22,264 - INFO - Response - Page 2:
2025-05-22 12:00:22,467 - INFO - 第 2 页获取到 100 条记录
2025-05-22 12:00:22,467 - INFO - Request Parameters - Page 3:
2025-05-22 12:00:22,467 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 12:00:22,467 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 12:00:23,170 - INFO - Response - Page 3:
2025-05-22 12:00:23,373 - INFO - 第 3 页获取到 100 条记录
2025-05-22 12:00:23,373 - INFO - Request Parameters - Page 4:
2025-05-22 12:00:23,373 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 12:00:23,373 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 12:00:23,920 - INFO - Response - Page 4:
2025-05-22 12:00:24,123 - INFO - 第 4 页获取到 100 条记录
2025-05-22 12:00:24,123 - INFO - Request Parameters - Page 5:
2025-05-22 12:00:24,123 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 12:00:24,123 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 12:00:24,623 - INFO - Response - Page 5:
2025-05-22 12:00:24,826 - INFO - 第 5 页获取到 100 条记录
2025-05-22 12:00:24,826 - INFO - Request Parameters - Page 6:
2025-05-22 12:00:24,826 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 12:00:24,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 12:00:25,326 - INFO - Response - Page 6:
2025-05-22 12:00:25,530 - INFO - 第 6 页获取到 100 条记录
2025-05-22 12:00:25,530 - INFO - Request Parameters - Page 7:
2025-05-22 12:00:25,530 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 12:00:25,530 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 12:00:26,014 - INFO - Response - Page 7:
2025-05-22 12:00:26,217 - INFO - 第 7 页获取到 56 条记录
2025-05-22 12:00:26,217 - INFO - 查询完成，共获取到 656 条记录
2025-05-22 12:00:26,217 - INFO - 获取到 656 条表单数据
2025-05-22 12:00:26,233 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-22 12:00:26,233 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M75
2025-05-22 12:00:26,717 - INFO - 更新表单数据成功: FINST-EZD66RB1YLVUUMG9B5G675NRBIN43SDRFMW9M75
2025-05-22 12:00:26,717 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 66656.27, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 684446.2, 'new_value': 749168.8}, {'field': 'total_amount', 'old_value': 751102.47, 'new_value': 749168.8}]
2025-05-22 12:00:26,733 - INFO - 日期 2025-04 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-22 12:00:26,733 - INFO - 开始处理日期: 2025-05
2025-05-22 12:00:26,733 - INFO - Request Parameters - Page 1:
2025-05-22 12:00:26,733 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 12:00:26,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 12:00:27,233 - INFO - Response - Page 1:
2025-05-22 12:00:27,436 - INFO - 第 1 页获取到 100 条记录
2025-05-22 12:00:27,436 - INFO - Request Parameters - Page 2:
2025-05-22 12:00:27,436 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 12:00:27,436 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 12:00:28,014 - INFO - Response - Page 2:
2025-05-22 12:00:28,217 - INFO - 第 2 页获取到 100 条记录
2025-05-22 12:00:28,217 - INFO - Request Parameters - Page 3:
2025-05-22 12:00:28,217 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 12:00:28,217 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 12:00:28,686 - INFO - Response - Page 3:
2025-05-22 12:00:28,889 - INFO - 第 3 页获取到 100 条记录
2025-05-22 12:00:28,889 - INFO - Request Parameters - Page 4:
2025-05-22 12:00:28,889 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 12:00:28,889 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 12:00:29,436 - INFO - Response - Page 4:
2025-05-22 12:00:29,639 - INFO - 第 4 页获取到 100 条记录
2025-05-22 12:00:29,639 - INFO - Request Parameters - Page 5:
2025-05-22 12:00:29,639 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 12:00:29,639 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 12:00:30,123 - INFO - Response - Page 5:
2025-05-22 12:00:30,326 - INFO - 第 5 页获取到 100 条记录
2025-05-22 12:00:30,326 - INFO - Request Parameters - Page 6:
2025-05-22 12:00:30,326 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 12:00:30,326 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 12:00:30,811 - INFO - Response - Page 6:
2025-05-22 12:00:31,014 - INFO - 第 6 页获取到 100 条记录
2025-05-22 12:00:31,014 - INFO - Request Parameters - Page 7:
2025-05-22 12:00:31,014 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 12:00:31,014 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 12:00:31,389 - INFO - Response - Page 7:
2025-05-22 12:00:31,592 - INFO - 第 7 页获取到 28 条记录
2025-05-22 12:00:31,592 - INFO - 查询完成，共获取到 628 条记录
2025-05-22 12:00:31,592 - INFO - 获取到 628 条表单数据
2025-05-22 12:00:31,592 - INFO - 当前日期 2025-05 有 628 条MySQL数据需要处理
2025-05-22 12:00:31,592 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-22 12:00:32,045 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMTT
2025-05-22 12:00:32,045 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 592551.98, 'new_value': 615746.98}, {'field': 'total_amount', 'old_value': 592551.98, 'new_value': 615746.98}, {'field': 'order_count', 'old_value': 1788, 'new_value': 1864}]
2025-05-22 12:00:32,045 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-22 12:00:32,592 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-22 12:00:32,592 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13203.0, 'new_value': 13467.0}, {'field': 'total_amount', 'old_value': 16100.0, 'new_value': 16364.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-05-22 12:00:32,592 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-22 12:00:33,030 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM2A
2025-05-22 12:00:33,030 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25925.33, 'new_value': 27418.58}, {'field': 'offline_amount', 'old_value': 12872.03, 'new_value': 13595.93}, {'field': 'total_amount', 'old_value': 38797.36, 'new_value': 41014.51}, {'field': 'order_count', 'old_value': 2001, 'new_value': 2115}]
2025-05-22 12:00:33,030 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-22 12:00:33,483 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM3A
2025-05-22 12:00:33,483 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 337107.0, 'new_value': 351507.0}, {'field': 'offline_amount', 'old_value': 249787.0, 'new_value': 288207.0}, {'field': 'total_amount', 'old_value': 586894.0, 'new_value': 639714.0}, {'field': 'order_count', 'old_value': 637, 'new_value': 663}]
2025-05-22 12:00:33,483 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4F
2025-05-22 12:00:33,951 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM4F
2025-05-22 12:00:33,951 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 350.0, 'new_value': 992.0}, {'field': 'offline_amount', 'old_value': 16006.8, 'new_value': 18821.8}, {'field': 'total_amount', 'old_value': 16356.8, 'new_value': 19813.8}, {'field': 'order_count', 'old_value': 1193, 'new_value': 1460}]
2025-05-22 12:00:33,951 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-22 12:00:34,451 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMNC
2025-05-22 12:00:34,451 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40123.1, 'new_value': 42943.1}, {'field': 'total_amount', 'old_value': 44083.1, 'new_value': 46903.1}, {'field': 'order_count', 'old_value': 315, 'new_value': 336}]
2025-05-22 12:00:34,451 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-22 12:00:34,873 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMVC
2025-05-22 12:00:34,873 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56972.0, 'new_value': 58212.0}, {'field': 'total_amount', 'old_value': 56972.0, 'new_value': 58212.0}, {'field': 'order_count', 'old_value': 94, 'new_value': 97}]
2025-05-22 12:00:34,873 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-22 12:00:35,233 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AMXC
2025-05-22 12:00:35,233 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 341340.48, 'new_value': 395188.71}, {'field': 'total_amount', 'old_value': 341340.48, 'new_value': 395188.71}, {'field': 'order_count', 'old_value': 367, 'new_value': 386}]
2025-05-22 12:00:35,233 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5F
2025-05-22 12:00:35,592 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5F
2025-05-22 12:00:35,592 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37926.0, 'new_value': 41580.0}, {'field': 'offline_amount', 'old_value': 134787.0, 'new_value': 156840.98}, {'field': 'total_amount', 'old_value': 172713.0, 'new_value': 198420.98}, {'field': 'order_count', 'old_value': 1162, 'new_value': 1336}]
2025-05-22 12:00:35,592 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6F
2025-05-22 12:00:36,045 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6F
2025-05-22 12:00:36,045 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10976.65, 'new_value': 11447.37}, {'field': 'offline_amount', 'old_value': 31062.64, 'new_value': 31726.64}, {'field': 'total_amount', 'old_value': 42039.29, 'new_value': 43174.01}, {'field': 'order_count', 'old_value': 738, 'new_value': 771}]
2025-05-22 12:00:36,045 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-22 12:00:36,467 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM7D
2025-05-22 12:00:36,467 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 657794.3, 'new_value': 671486.9}, {'field': 'total_amount', 'old_value': 700430.5, 'new_value': 714123.1}, {'field': 'order_count', 'old_value': 75, 'new_value': 78}]
2025-05-22 12:00:36,467 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7F
2025-05-22 12:00:36,905 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7F
2025-05-22 12:00:36,905 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18317.53, 'new_value': 21341.07}, {'field': 'offline_amount', 'old_value': 74567.94, 'new_value': 90747.25}, {'field': 'total_amount', 'old_value': 92885.47, 'new_value': 112088.32}, {'field': 'order_count', 'old_value': 1286, 'new_value': 1522}]
2025-05-22 12:00:36,905 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-22 12:00:37,420 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUT
2025-05-22 12:00:37,420 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 145786.18, 'new_value': 146085.18}, {'field': 'total_amount', 'old_value': 145786.18, 'new_value': 146085.18}, {'field': 'order_count', 'old_value': 202, 'new_value': 210}]
2025-05-22 12:00:37,420 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-22 12:00:37,854 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-22 12:00:37,854 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42971.37, 'new_value': 46271.37}, {'field': 'total_amount', 'old_value': 123651.55, 'new_value': 126951.55}, {'field': 'order_count', 'old_value': 6970, 'new_value': 6971}]
2025-05-22 12:00:37,855 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8F
2025-05-22 12:00:38,316 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM8F
2025-05-22 12:00:38,316 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 43593.9, 'new_value': 45575.6}, {'field': 'offline_amount', 'old_value': 435599.85, 'new_value': 456927.23}, {'field': 'total_amount', 'old_value': 479193.75, 'new_value': 502502.83}, {'field': 'order_count', 'old_value': 1528, 'new_value': 1606}]
2025-05-22 12:00:38,316 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9F
2025-05-22 12:00:38,705 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM9F
2025-05-22 12:00:38,705 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18678.85, 'new_value': 22279.77}, {'field': 'offline_amount', 'old_value': 16283.67, 'new_value': 18436.94}, {'field': 'total_amount', 'old_value': 34962.52, 'new_value': 40716.71}, {'field': 'order_count', 'old_value': 1974, 'new_value': 2324}]
2025-05-22 12:00:38,705 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-22 12:00:39,168 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWT
2025-05-22 12:00:39,168 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 70452.0, 'new_value': 119196.0}, {'field': 'offline_amount', 'old_value': 55008.0, 'new_value': 70731.0}, {'field': 'total_amount', 'old_value': 125460.0, 'new_value': 189927.0}, {'field': 'order_count', 'old_value': 46, 'new_value': 58}]
2025-05-22 12:00:39,169 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAF
2025-05-22 12:00:39,576 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAF
2025-05-22 12:00:39,577 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3741.49, 'new_value': 4447.39}, {'field': 'offline_amount', 'old_value': 86095.76, 'new_value': 99159.78}, {'field': 'total_amount', 'old_value': 89837.25, 'new_value': 103607.17}, {'field': 'order_count', 'old_value': 1449, 'new_value': 1699}]
2025-05-22 12:00:39,577 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMBF
2025-05-22 12:00:40,048 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMBF
2025-05-22 12:00:40,048 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56276.49, 'new_value': 65499.6}, {'field': 'total_amount', 'old_value': 56276.49, 'new_value': 65499.6}, {'field': 'order_count', 'old_value': 2906, 'new_value': 3387}]
2025-05-22 12:00:40,049 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-22 12:00:40,489 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZT
2025-05-22 12:00:40,489 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 68447.1, 'new_value': 72201.3}, {'field': 'offline_amount', 'old_value': 710564.98, 'new_value': 752017.38}, {'field': 'total_amount', 'old_value': 779012.08, 'new_value': 824218.68}, {'field': 'order_count', 'old_value': 2534, 'new_value': 2664}]
2025-05-22 12:00:40,489 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-22 12:00:40,934 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMYD
2025-05-22 12:00:40,934 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25937.0, 'new_value': 26205.0}, {'field': 'total_amount', 'old_value': 25937.0, 'new_value': 26205.0}, {'field': 'order_count', 'old_value': 44, 'new_value': 47}]
2025-05-22 12:00:40,934 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-22 12:00:41,369 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM0U
2025-05-22 12:00:41,369 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 570958.51, 'new_value': 594636.09}, {'field': 'total_amount', 'old_value': 570958.51, 'new_value': 594636.09}]
2025-05-22 12:00:41,369 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-05-22 12:00:42,007 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-05-22 12:00:42,007 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 500.0}, {'field': 'total_amount', 'old_value': 25690.28, 'new_value': 26190.28}, {'field': 'order_count', 'old_value': 38, 'new_value': 40}]
2025-05-22 12:00:42,007 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCF
2025-05-22 12:00:42,452 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMCF
2025-05-22 12:00:42,452 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 243498.99, 'new_value': 260004.33}, {'field': 'total_amount', 'old_value': 244307.99, 'new_value': 260813.33}, {'field': 'order_count', 'old_value': 2785, 'new_value': 2970}]
2025-05-22 12:00:42,452 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-22 12:00:42,951 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4U
2025-05-22 12:00:42,951 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 145761.62, 'new_value': 152389.59}, {'field': 'total_amount', 'old_value': 145761.62, 'new_value': 152389.59}, {'field': 'order_count', 'old_value': 826, 'new_value': 868}]
2025-05-22 12:00:42,951 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-22 12:00:43,393 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMBE
2025-05-22 12:00:43,393 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24483.7, 'new_value': 25035.57}, {'field': 'total_amount', 'old_value': 24483.7, 'new_value': 25035.57}, {'field': 'order_count', 'old_value': 135, 'new_value': 142}]
2025-05-22 12:00:43,394 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-22 12:00:43,933 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6U
2025-05-22 12:00:43,933 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 166272.65, 'new_value': 174622.65}, {'field': 'offline_amount', 'old_value': 92368.0, 'new_value': 98775.0}, {'field': 'total_amount', 'old_value': 258640.65, 'new_value': 273397.65}, {'field': 'order_count', 'old_value': 1415, 'new_value': 1557}]
2025-05-22 12:00:43,934 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDF
2025-05-22 12:00:44,351 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMDF
2025-05-22 12:00:44,352 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33298.8, 'new_value': 45689.6}, {'field': 'total_amount', 'old_value': 33298.8, 'new_value': 45689.6}, {'field': 'order_count', 'old_value': 150, 'new_value': 198}]
2025-05-22 12:00:44,352 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-22 12:00:44,812 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMRE
2025-05-22 12:00:44,812 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 138953.0, 'new_value': 141231.0}, {'field': 'total_amount', 'old_value': 146528.8, 'new_value': 148806.8}, {'field': 'order_count', 'old_value': 22, 'new_value': 23}]
2025-05-22 12:00:44,812 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM4Q
2025-05-22 12:00:45,188 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM4Q
2025-05-22 12:00:45,188 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 179555.18, 'new_value': 189962.18}, {'field': 'total_amount', 'old_value': 186470.84, 'new_value': 196877.84}, {'field': 'order_count', 'old_value': 3897, 'new_value': 3898}]
2025-05-22 12:00:45,188 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-22 12:00:45,656 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AMSE
2025-05-22 12:00:45,656 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36556.39, 'new_value': 38237.21}, {'field': 'offline_amount', 'old_value': 77388.17, 'new_value': 79724.97}, {'field': 'total_amount', 'old_value': 113944.56, 'new_value': 117962.18}, {'field': 'order_count', 'old_value': 4140, 'new_value': 4295}]
2025-05-22 12:00:45,656 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-22 12:00:46,101 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7U
2025-05-22 12:00:46,102 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45013.0, 'new_value': 48454.0}, {'field': 'offline_amount', 'old_value': 143705.0, 'new_value': 147813.0}, {'field': 'total_amount', 'old_value': 188718.0, 'new_value': 196267.0}, {'field': 'order_count', 'old_value': 4099, 'new_value': 4294}]
2025-05-22 12:00:46,102 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-22 12:00:46,543 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM9U
2025-05-22 12:00:46,543 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 240321.0, 'new_value': 253280.0}, {'field': 'total_amount', 'old_value': 266705.0, 'new_value': 279664.0}, {'field': 'order_count', 'old_value': 73, 'new_value': 78}]
2025-05-22 12:00:46,543 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-22 12:00:46,934 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAU
2025-05-22 12:00:46,934 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 122270.55, 'new_value': 125489.89}, {'field': 'offline_amount', 'old_value': 97863.45, 'new_value': 99683.45}, {'field': 'total_amount', 'old_value': 220134.0, 'new_value': 225173.34}, {'field': 'order_count', 'old_value': 2204, 'new_value': 2263}]
2025-05-22 12:00:46,934 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-22 12:00:47,371 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-22 12:00:47,371 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 460301.0, 'new_value': 509081.0}, {'field': 'total_amount', 'old_value': 460301.0, 'new_value': 509081.0}, {'field': 'order_count', 'old_value': 48, 'new_value': 53}]
2025-05-22 12:00:47,371 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-22 12:00:47,793 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMDU
2025-05-22 12:00:47,793 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 101250.34, 'new_value': 107921.54}, {'field': 'offline_amount', 'old_value': 349649.35, 'new_value': 367847.76}, {'field': 'total_amount', 'old_value': 450899.69, 'new_value': 475769.3}, {'field': 'order_count', 'old_value': 2525, 'new_value': 2597}]
2025-05-22 12:00:47,793 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-22 12:00:48,276 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFU
2025-05-22 12:00:48,276 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 696716.0, 'new_value': 721490.0}, {'field': 'total_amount', 'old_value': 696716.0, 'new_value': 721490.0}, {'field': 'order_count', 'old_value': 122, 'new_value': 127}]
2025-05-22 12:00:48,276 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-22 12:00:48,744 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOK
2025-05-22 12:00:48,744 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80127.41, 'new_value': 81973.44}, {'field': 'total_amount', 'old_value': 80127.41, 'new_value': 81973.44}, {'field': 'order_count', 'old_value': 3075, 'new_value': 3156}]
2025-05-22 12:00:48,744 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-22 12:00:49,234 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJU
2025-05-22 12:00:49,234 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67198.94, 'new_value': 81191.02}, {'field': 'total_amount', 'old_value': 67198.94, 'new_value': 81191.02}, {'field': 'order_count', 'old_value': 68, 'new_value': 71}]
2025-05-22 12:00:49,235 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-22 12:00:49,739 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRK
2025-05-22 12:00:49,739 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8867.21, 'new_value': 9239.13}, {'field': 'total_amount', 'old_value': 20388.19, 'new_value': 20760.11}, {'field': 'order_count', 'old_value': 87, 'new_value': 89}]
2025-05-22 12:00:49,739 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-22 12:00:50,141 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMLU
2025-05-22 12:00:50,141 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4863.0, 'new_value': 6249.0}, {'field': 'total_amount', 'old_value': 4863.0, 'new_value': 6249.0}, {'field': 'order_count', 'old_value': 29, 'new_value': 32}]
2025-05-22 12:00:50,142 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-22 12:00:50,661 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWK
2025-05-22 12:00:50,661 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84361.6, 'new_value': 87018.2}, {'field': 'total_amount', 'old_value': 84361.6, 'new_value': 87018.2}, {'field': 'order_count', 'old_value': 258, 'new_value': 267}]
2025-05-22 12:00:50,661 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-22 12:00:51,115 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXK
2025-05-22 12:00:51,115 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 149717.18, 'new_value': 159132.14}, {'field': 'offline_amount', 'old_value': 289755.54, 'new_value': 298185.09}, {'field': 'total_amount', 'old_value': 439472.72, 'new_value': 457317.23}, {'field': 'order_count', 'old_value': 12132, 'new_value': 12753}]
2025-05-22 12:00:51,115 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-22 12:00:51,647 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYK
2025-05-22 12:00:51,647 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38405.0, 'new_value': 38573.0}, {'field': 'total_amount', 'old_value': 38405.0, 'new_value': 38573.0}, {'field': 'order_count', 'old_value': 100, 'new_value': 102}]
2025-05-22 12:00:51,647 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0L
2025-05-22 12:00:52,265 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0L
2025-05-22 12:00:52,265 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 901305.94, 'new_value': 905247.94}, {'field': 'total_amount', 'old_value': 901305.94, 'new_value': 905247.94}, {'field': 'order_count', 'old_value': 547, 'new_value': 548}]
2025-05-22 12:00:52,265 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-22 12:00:52,712 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2L
2025-05-22 12:00:52,713 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30717.0, 'new_value': 31307.0}, {'field': 'total_amount', 'old_value': 30717.0, 'new_value': 31307.0}, {'field': 'order_count', 'old_value': 95, 'new_value': 97}]
2025-05-22 12:00:52,713 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4L
2025-05-22 12:00:53,169 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4L
2025-05-22 12:00:53,169 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4020.6, 'new_value': 4195.9}, {'field': 'total_amount', 'old_value': 4020.6, 'new_value': 4195.9}, {'field': 'order_count', 'old_value': 256, 'new_value': 271}]
2025-05-22 12:00:53,169 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-22 12:00:53,584 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-22 12:00:53,584 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1018000.0, 'new_value': 1055000.0}, {'field': 'total_amount', 'old_value': 1018000.0, 'new_value': 1055000.0}, {'field': 'order_count', 'old_value': 339, 'new_value': 340}]
2025-05-22 12:00:53,585 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEF
2025-05-22 12:00:54,015 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEF
2025-05-22 12:00:54,015 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15032.94, 'new_value': 15870.44}, {'field': 'offline_amount', 'old_value': 26004.18, 'new_value': 27077.73}, {'field': 'total_amount', 'old_value': 41037.12, 'new_value': 42948.17}, {'field': 'order_count', 'old_value': 1822, 'new_value': 1927}]
2025-05-22 12:00:54,015 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-05-22 12:00:54,434 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIL
2025-05-22 12:00:54,435 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5727.0, 'new_value': 6024.0}, {'field': 'total_amount', 'old_value': 5727.0, 'new_value': 6024.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 30}]
2025-05-22 12:00:54,435 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-22 12:00:54,948 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMNU
2025-05-22 12:00:54,948 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 238726.72, 'new_value': 249355.25}, {'field': 'total_amount', 'old_value': 238726.72, 'new_value': 249355.25}, {'field': 'order_count', 'old_value': 6479, 'new_value': 6785}]
2025-05-22 12:00:54,949 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-22 12:00:55,446 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVL
2025-05-22 12:00:55,446 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7748.81, 'new_value': 7878.81}, {'field': 'offline_amount', 'old_value': 73469.17, 'new_value': 74867.34}, {'field': 'total_amount', 'old_value': 81217.98, 'new_value': 82746.15}, {'field': 'order_count', 'old_value': 2262, 'new_value': 2311}]
2025-05-22 12:00:55,446 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-22 12:00:55,868 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AM6O
2025-05-22 12:00:55,868 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17035.51, 'new_value': 18067.51}, {'field': 'offline_amount', 'old_value': 32848.1, 'new_value': 34656.64}, {'field': 'total_amount', 'old_value': 49883.61, 'new_value': 52724.15}, {'field': 'order_count', 'old_value': 2637, 'new_value': 2780}]
2025-05-22 12:00:55,868 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-22 12:00:56,415 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMYL
2025-05-22 12:00:56,415 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7639.84, 'new_value': 8682.9}, {'field': 'offline_amount', 'old_value': 10218.83, 'new_value': 10618.73}, {'field': 'total_amount', 'old_value': 17858.67, 'new_value': 19301.63}, {'field': 'order_count', 'old_value': 1412, 'new_value': 1487}]
2025-05-22 12:00:56,415 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-22 12:00:56,853 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-22 12:00:56,853 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5117351.0, 'new_value': 5327351.0}, {'field': 'total_amount', 'old_value': 5117351.0, 'new_value': 5327351.0}, {'field': 'order_count', 'old_value': 86212, 'new_value': 86213}]
2025-05-22 12:00:56,853 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-22 12:00:57,337 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXU
2025-05-22 12:00:57,337 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43229.0, 'new_value': 43772.0}, {'field': 'total_amount', 'old_value': 43229.0, 'new_value': 43772.0}, {'field': 'order_count', 'old_value': 307, 'new_value': 313}]
2025-05-22 12:00:57,337 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-22 12:00:57,884 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0M
2025-05-22 12:00:57,884 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41801.11, 'new_value': 43817.39}, {'field': 'offline_amount', 'old_value': 332111.74, 'new_value': 338040.07}, {'field': 'total_amount', 'old_value': 373912.85, 'new_value': 381857.46}, {'field': 'order_count', 'old_value': 3125, 'new_value': 3199}]
2025-05-22 12:00:57,884 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-22 12:00:58,337 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZU
2025-05-22 12:00:58,337 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45253.98, 'new_value': 45869.98}, {'field': 'offline_amount', 'old_value': 387017.5, 'new_value': 401017.5}, {'field': 'total_amount', 'old_value': 432271.48, 'new_value': 446887.48}, {'field': 'order_count', 'old_value': 3321, 'new_value': 3458}]
2025-05-22 12:00:58,337 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM8
2025-05-22 12:00:58,790 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM8
2025-05-22 12:00:58,790 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75489.74, 'new_value': 78489.74}, {'field': 'total_amount', 'old_value': 75489.74, 'new_value': 78489.74}, {'field': 'order_count', 'old_value': 2009, 'new_value': 2010}]
2025-05-22 12:00:58,790 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO
2025-05-22 12:00:59,228 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMAO
2025-05-22 12:00:59,228 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25042.18, 'new_value': 28037.88}, {'field': 'total_amount', 'old_value': 25042.18, 'new_value': 28037.88}, {'field': 'order_count', 'old_value': 26, 'new_value': 28}]
2025-05-22 12:00:59,228 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-22 12:00:59,712 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-22 12:00:59,712 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94123.0, 'new_value': 97723.0}, {'field': 'total_amount', 'old_value': 94123.0, 'new_value': 97723.0}, {'field': 'order_count', 'old_value': 3482, 'new_value': 3483}]
2025-05-22 12:00:59,712 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-22 12:01:00,165 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3M
2025-05-22 12:01:00,165 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 70702.86, 'new_value': 74416.02}, {'field': 'offline_amount', 'old_value': 34441.41, 'new_value': 35061.19}, {'field': 'total_amount', 'old_value': 105144.27, 'new_value': 109477.21}, {'field': 'order_count', 'old_value': 6569, 'new_value': 6814}]
2025-05-22 12:01:00,181 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-22 12:01:00,603 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-22 12:01:00,603 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24594.07, 'new_value': 25494.07}, {'field': 'total_amount', 'old_value': 24594.07, 'new_value': 25494.07}, {'field': 'order_count', 'old_value': 2392, 'new_value': 2393}]
2025-05-22 12:01:00,603 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFF
2025-05-22 12:01:01,056 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMFF
2025-05-22 12:01:01,056 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 138465.16, 'new_value': 144057.26}, {'field': 'total_amount', 'old_value': 138465.16, 'new_value': 144057.26}, {'field': 'order_count', 'old_value': 233, 'new_value': 243}]
2025-05-22 12:01:01,056 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-22 12:01:01,524 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9M
2025-05-22 12:01:01,524 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 93137.98, 'new_value': 94441.98}, {'field': 'total_amount', 'old_value': 93137.98, 'new_value': 94441.98}, {'field': 'order_count', 'old_value': 803, 'new_value': 825}]
2025-05-22 12:01:01,524 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-22 12:01:02,009 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMAM
2025-05-22 12:01:02,009 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 77383.87, 'new_value': 80725.01}, {'field': 'offline_amount', 'old_value': 206222.46, 'new_value': 212825.04}, {'field': 'total_amount', 'old_value': 283606.33, 'new_value': 293550.05}, {'field': 'order_count', 'old_value': 9331, 'new_value': 9722}]
2025-05-22 12:01:02,009 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-22 12:01:02,399 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-22 12:01:02,399 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5092100.0, 'new_value': 5127100.0}, {'field': 'total_amount', 'old_value': 5092100.0, 'new_value': 5127100.0}, {'field': 'order_count', 'old_value': 63, 'new_value': 64}]
2025-05-22 12:01:02,399 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-22 12:01:02,884 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMGM
2025-05-22 12:01:02,884 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69032.8, 'new_value': 69042.8}, {'field': 'total_amount', 'old_value': 75956.8, 'new_value': 75966.8}, {'field': 'order_count', 'old_value': 36, 'new_value': 37}]
2025-05-22 12:01:02,884 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGF
2025-05-22 12:01:03,337 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGF
2025-05-22 12:01:03,337 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57137.0, 'new_value': 65959.0}, {'field': 'total_amount', 'old_value': 57137.0, 'new_value': 65959.0}, {'field': 'order_count', 'old_value': 3252, 'new_value': 3772}]
2025-05-22 12:01:03,337 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-22 12:01:03,790 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMIM
2025-05-22 12:01:03,790 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 254578.61, 'new_value': 263170.45}, {'field': 'offline_amount', 'old_value': 15884.77, 'new_value': 16966.62}, {'field': 'total_amount', 'old_value': 270463.38, 'new_value': 280137.07}, {'field': 'order_count', 'old_value': 10595, 'new_value': 11235}]
2025-05-22 12:01:03,790 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-22 12:01:04,290 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMJM
2025-05-22 12:01:04,290 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18108.1, 'new_value': 18127.9}, {'field': 'offline_amount', 'old_value': 96818.4, 'new_value': 101362.4}, {'field': 'total_amount', 'old_value': 114926.5, 'new_value': 119490.3}, {'field': 'order_count', 'old_value': 156, 'new_value': 164}]
2025-05-22 12:01:04,290 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-22 12:01:04,712 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKM
2025-05-22 12:01:04,712 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 386019.0, 'new_value': 388019.0}, {'field': 'total_amount', 'old_value': 394837.99, 'new_value': 396837.99}, {'field': 'order_count', 'old_value': 72, 'new_value': 73}]
2025-05-22 12:01:04,712 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-22 12:01:05,181 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMLM
2025-05-22 12:01:05,181 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 100511.83, 'new_value': 108229.38}, {'field': 'offline_amount', 'old_value': 225627.75, 'new_value': 226849.2}, {'field': 'total_amount', 'old_value': 326139.58, 'new_value': 335078.58}, {'field': 'order_count', 'old_value': 4152, 'new_value': 4262}]
2025-05-22 12:01:05,181 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-22 12:01:05,603 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMOM
2025-05-22 12:01:05,603 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 68139.8, 'new_value': 69674.5}, {'field': 'total_amount', 'old_value': 68139.8, 'new_value': 69674.5}, {'field': 'order_count', 'old_value': 499, 'new_value': 509}]
2025-05-22 12:01:05,603 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-22 12:01:06,056 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-22 12:01:06,056 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 341525.3, 'new_value': 344125.3}, {'field': 'total_amount', 'old_value': 341525.3, 'new_value': 344125.3}, {'field': 'order_count', 'old_value': 653, 'new_value': 654}]
2025-05-22 12:01:06,056 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM
2025-05-22 12:01:06,524 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMRM
2025-05-22 12:01:06,524 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15080.0, 'new_value': 15379.0}, {'field': 'total_amount', 'old_value': 15080.0, 'new_value': 15379.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-05-22 12:01:06,524 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-22 12:01:07,009 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6V
2025-05-22 12:01:07,009 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25475.0, 'new_value': 26463.0}, {'field': 'total_amount', 'old_value': 25475.0, 'new_value': 26463.0}, {'field': 'order_count', 'old_value': 248, 'new_value': 258}]
2025-05-22 12:01:07,009 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-22 12:01:07,462 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7V
2025-05-22 12:01:07,462 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64596.0, 'new_value': 67712.0}, {'field': 'total_amount', 'old_value': 69092.0, 'new_value': 72208.0}, {'field': 'order_count', 'old_value': 41, 'new_value': 43}]
2025-05-22 12:01:07,462 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-22 12:01:07,899 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMUM
2025-05-22 12:01:07,899 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65286.0, 'new_value': 65486.0}, {'field': 'total_amount', 'old_value': 65286.0, 'new_value': 65486.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-05-22 12:01:07,899 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHF
2025-05-22 12:01:08,368 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHF
2025-05-22 12:01:08,368 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80886.49, 'new_value': 86810.05}, {'field': 'total_amount', 'old_value': 80886.49, 'new_value': 86810.05}, {'field': 'order_count', 'old_value': 392, 'new_value': 427}]
2025-05-22 12:01:08,368 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-22 12:01:08,821 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMVM
2025-05-22 12:01:08,821 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25166.0, 'new_value': 26093.0}, {'field': 'total_amount', 'old_value': 25166.0, 'new_value': 26093.0}, {'field': 'order_count', 'old_value': 237, 'new_value': 252}]
2025-05-22 12:01:08,821 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-22 12:01:09,431 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMWM
2025-05-22 12:01:09,431 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 68009.27, 'new_value': 69589.78}, {'field': 'offline_amount', 'old_value': 105286.41, 'new_value': 106343.61}, {'field': 'total_amount', 'old_value': 173295.68, 'new_value': 175933.39}, {'field': 'order_count', 'old_value': 1750, 'new_value': 1789}]
2025-05-22 12:01:09,431 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-22 12:01:09,884 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAV
2025-05-22 12:01:09,884 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1355564.27, 'new_value': 1439212.52}, {'field': 'offline_amount', 'old_value': 153405.3, 'new_value': 153959.3}, {'field': 'total_amount', 'old_value': 1508969.57, 'new_value': 1593171.82}, {'field': 'order_count', 'old_value': 5149, 'new_value': 5442}]
2025-05-22 12:01:09,884 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-22 12:01:10,391 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-22 12:01:10,392 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26033.0, 'new_value': 27233.0}, {'field': 'total_amount', 'old_value': 27409.0, 'new_value': 28609.0}, {'field': 'order_count', 'old_value': 2852, 'new_value': 2853}]
2025-05-22 12:01:10,392 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-22 12:01:10,834 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCV
2025-05-22 12:01:10,834 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12421.3, 'new_value': 14421.7}, {'field': 'offline_amount', 'old_value': 38736.9, 'new_value': 40370.9}, {'field': 'total_amount', 'old_value': 51158.2, 'new_value': 54792.6}, {'field': 'order_count', 'old_value': 147, 'new_value': 157}]
2025-05-22 12:01:10,834 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-22 12:01:11,255 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM0N
2025-05-22 12:01:11,255 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8913.0, 'new_value': 9333.0}, {'field': 'total_amount', 'old_value': 10919.0, 'new_value': 11339.0}, {'field': 'order_count', 'old_value': 104, 'new_value': 109}]
2025-05-22 12:01:11,255 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-22 12:01:11,683 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM1N
2025-05-22 12:01:11,683 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24077.0, 'new_value': 24989.0}, {'field': 'total_amount', 'old_value': 24077.0, 'new_value': 24989.0}, {'field': 'order_count', 'old_value': 106, 'new_value': 111}]
2025-05-22 12:01:11,683 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-22 12:01:12,089 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2N
2025-05-22 12:01:12,089 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 79968.6, 'new_value': 81434.4}, {'field': 'offline_amount', 'old_value': 118909.2, 'new_value': 119594.9}, {'field': 'total_amount', 'old_value': 198877.8, 'new_value': 201029.3}, {'field': 'order_count', 'old_value': 4000, 'new_value': 4069}]
2025-05-22 12:01:12,090 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-22 12:01:12,503 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM3N
2025-05-22 12:01:12,503 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 393357.02, 'new_value': 403373.88}, {'field': 'total_amount', 'old_value': 393357.02, 'new_value': 403373.88}, {'field': 'order_count', 'old_value': 5348, 'new_value': 5521}]
2025-05-22 12:01:12,504 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-22 12:01:12,975 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFV
2025-05-22 12:01:12,975 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1238.49, 'new_value': 1913.05}, {'field': 'offline_amount', 'old_value': 490506.94, 'new_value': 517554.06}, {'field': 'total_amount', 'old_value': 491745.43, 'new_value': 519467.11}, {'field': 'order_count', 'old_value': 1144, 'new_value': 1211}]
2025-05-22 12:01:12,975 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-22 12:01:13,461 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRO
2025-05-22 12:01:13,461 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 139510.0, 'new_value': 148370.0}, {'field': 'total_amount', 'old_value': 139511.0, 'new_value': 148371.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-05-22 12:01:13,461 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-22 12:01:13,939 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM5N
2025-05-22 12:01:13,939 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73549.53, 'new_value': 75706.15}, {'field': 'total_amount', 'old_value': 73549.53, 'new_value': 75706.15}, {'field': 'order_count', 'old_value': 2267, 'new_value': 2332}]
2025-05-22 12:01:13,939 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-22 12:01:15,228 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM6N
2025-05-22 12:01:15,228 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7722.15, 'new_value': 8062.76}, {'field': 'offline_amount', 'old_value': 25092.29, 'new_value': 25741.89}, {'field': 'total_amount', 'old_value': 32814.44, 'new_value': 33804.65}, {'field': 'order_count', 'old_value': 1145, 'new_value': 1176}]
2025-05-22 12:01:15,228 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-22 12:01:15,680 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7N
2025-05-22 12:01:15,680 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 123480.04, 'new_value': 126123.61}, {'field': 'total_amount', 'old_value': 123480.04, 'new_value': 126123.61}, {'field': 'order_count', 'old_value': 3159, 'new_value': 3235}]
2025-05-22 12:01:15,680 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-22 12:01:16,047 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM9N
2025-05-22 12:01:16,047 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27853.69, 'new_value': 29270.0}, {'field': 'offline_amount', 'old_value': 264503.62, 'new_value': 269669.07}, {'field': 'total_amount', 'old_value': 292357.31, 'new_value': 298939.07}, {'field': 'order_count', 'old_value': 6790, 'new_value': 6943}]
2025-05-22 12:01:16,047 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-22 12:01:16,537 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMND
2025-05-22 12:01:16,537 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 358305.9, 'new_value': 366703.9}, {'field': 'total_amount', 'old_value': 358305.9, 'new_value': 366703.9}, {'field': 'order_count', 'old_value': 1788, 'new_value': 1824}]
2025-05-22 12:01:16,538 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-22 12:01:16,943 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOD
2025-05-22 12:01:16,943 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 113971.3, 'new_value': 117925.97}, {'field': 'offline_amount', 'old_value': 45448.2, 'new_value': 46437.38}, {'field': 'total_amount', 'old_value': 159419.5, 'new_value': 164363.35}, {'field': 'order_count', 'old_value': 9763, 'new_value': 10079}]
2025-05-22 12:01:16,943 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-22 12:01:17,393 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMSD
2025-05-22 12:01:17,393 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 168281.0, 'new_value': 174940.0}, {'field': 'total_amount', 'old_value': 168281.0, 'new_value': 174940.0}, {'field': 'order_count', 'old_value': 196, 'new_value': 202}]
2025-05-22 12:01:17,393 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-22 12:01:17,959 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMTD
2025-05-22 12:01:17,959 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 594099.93, 'new_value': 606775.07}, {'field': 'total_amount', 'old_value': 594099.93, 'new_value': 606775.07}, {'field': 'order_count', 'old_value': 11286, 'new_value': 11522}]
2025-05-22 12:01:17,959 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-22 12:01:18,383 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVD
2025-05-22 12:01:18,383 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 219476.38, 'new_value': 227649.39}, {'field': 'total_amount', 'old_value': 231527.35, 'new_value': 239700.36}, {'field': 'order_count', 'old_value': 9769, 'new_value': 10156}]
2025-05-22 12:01:18,383 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-22 12:01:18,834 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHV
2025-05-22 12:01:18,834 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 596138.61, 'new_value': 621996.53}, {'field': 'total_amount', 'old_value': 596138.61, 'new_value': 621996.53}, {'field': 'order_count', 'old_value': 4427, 'new_value': 4639}]
2025-05-22 12:01:18,834 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-22 12:01:19,226 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWD
2025-05-22 12:01:19,227 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 175317.0, 'new_value': 181137.0}, {'field': 'total_amount', 'old_value': 175317.0, 'new_value': 181137.0}, {'field': 'order_count', 'old_value': 535, 'new_value': 552}]
2025-05-22 12:01:19,227 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-22 12:01:19,651 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMYD
2025-05-22 12:01:19,651 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1466.59, 'new_value': 1531.94}, {'field': 'offline_amount', 'old_value': 19725.11, 'new_value': 20081.45}, {'field': 'total_amount', 'old_value': 21191.7, 'new_value': 21613.39}, {'field': 'order_count', 'old_value': 753, 'new_value': 768}]
2025-05-22 12:01:19,651 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-22 12:01:20,151 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM0E
2025-05-22 12:01:20,151 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5272.82, 'new_value': 5586.52}, {'field': 'offline_amount', 'old_value': 300164.54, 'new_value': 308013.04}, {'field': 'total_amount', 'old_value': 305437.36, 'new_value': 313599.56}, {'field': 'order_count', 'old_value': 14794, 'new_value': 15229}]
2025-05-22 12:01:20,151 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-22 12:01:20,623 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMKV
2025-05-22 12:01:20,623 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 167625.9, 'new_value': 176413.9}, {'field': 'total_amount', 'old_value': 167625.9, 'new_value': 176413.9}, {'field': 'order_count', 'old_value': 924, 'new_value': 971}]
2025-05-22 12:01:20,623 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-22 12:01:21,080 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3E
2025-05-22 12:01:21,081 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 51019.6, 'new_value': 53707.08}, {'field': 'offline_amount', 'old_value': 73338.88, 'new_value': 75693.72}, {'field': 'total_amount', 'old_value': 124358.48, 'new_value': 129400.8}, {'field': 'order_count', 'old_value': 5744, 'new_value': 5950}]
2025-05-22 12:01:21,081 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-22 12:01:21,548 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5E
2025-05-22 12:01:21,548 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 301074.62, 'new_value': 310548.15}, {'field': 'total_amount', 'old_value': 323237.74, 'new_value': 332711.27}, {'field': 'order_count', 'old_value': 13651, 'new_value': 14098}]
2025-05-22 12:01:21,549 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-22 12:01:21,971 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM6E
2025-05-22 12:01:21,971 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24163.82, 'new_value': 25033.39}, {'field': 'offline_amount', 'old_value': 193606.54, 'new_value': 198536.74}, {'field': 'total_amount', 'old_value': 217770.36, 'new_value': 223570.13}, {'field': 'order_count', 'old_value': 6829, 'new_value': 7022}]
2025-05-22 12:01:21,971 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-22 12:01:22,392 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-22 12:01:22,392 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 322619.1, 'new_value': 326119.1}, {'field': 'total_amount', 'old_value': 322619.1, 'new_value': 326119.1}, {'field': 'order_count', 'old_value': 2308, 'new_value': 2309}]
2025-05-22 12:01:22,392 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-22 12:01:22,784 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM8E
2025-05-22 12:01:22,784 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79974.0, 'new_value': 81218.0}, {'field': 'total_amount', 'old_value': 95178.0, 'new_value': 96422.0}, {'field': 'order_count', 'old_value': 2162, 'new_value': 2193}]
2025-05-22 12:01:22,784 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-22 12:01:23,329 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-22 12:01:23,329 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81650.0, 'new_value': 85650.0}, {'field': 'total_amount', 'old_value': 81650.0, 'new_value': 85650.0}, {'field': 'order_count', 'old_value': 555, 'new_value': 556}]
2025-05-22 12:01:23,329 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-22 12:01:23,813 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMHE
2025-05-22 12:01:23,813 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 195813.35, 'new_value': 200314.19}, {'field': 'total_amount', 'old_value': 214986.78, 'new_value': 219487.62}, {'field': 'order_count', 'old_value': 4421, 'new_value': 4524}]
2025-05-22 12:01:23,813 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-22 12:01:24,267 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMLE
2025-05-22 12:01:24,268 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65298.32, 'new_value': 67407.72}, {'field': 'total_amount', 'old_value': 65298.32, 'new_value': 67407.72}, {'field': 'order_count', 'old_value': 3696, 'new_value': 3836}]
2025-05-22 12:01:24,268 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIF
2025-05-22 12:01:24,678 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMIF
2025-05-22 12:01:24,679 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23479.0, 'new_value': 39823.0}, {'field': 'total_amount', 'old_value': 23479.0, 'new_value': 39823.0}, {'field': 'order_count', 'old_value': 349, 'new_value': 390}]
2025-05-22 12:01:24,679 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-22 12:01:25,149 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMSV
2025-05-22 12:01:25,149 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11864.45, 'new_value': 12396.45}, {'field': 'offline_amount', 'old_value': 225425.0, 'new_value': 235033.0}, {'field': 'total_amount', 'old_value': 237289.45, 'new_value': 247429.45}, {'field': 'order_count', 'old_value': 1258, 'new_value': 1318}]
2025-05-22 12:01:25,149 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-22 12:01:25,727 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMUV
2025-05-22 12:01:25,727 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 54400.0, 'new_value': 62400.0}, {'field': 'total_amount', 'old_value': 57400.0, 'new_value': 65400.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-05-22 12:01:25,727 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-22 12:01:26,172 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMOE
2025-05-22 12:01:26,173 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28818.7, 'new_value': 30561.7}, {'field': 'total_amount', 'old_value': 28818.7, 'new_value': 30561.7}, {'field': 'order_count', 'old_value': 166, 'new_value': 178}]
2025-05-22 12:01:26,173 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-22 12:01:26,645 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-22 12:01:26,645 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 246417.5, 'new_value': 251417.5}, {'field': 'total_amount', 'old_value': 246417.5, 'new_value': 251417.5}, {'field': 'order_count', 'old_value': 44, 'new_value': 45}]
2025-05-22 12:01:26,645 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLF
2025-05-22 12:01:27,037 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMLF
2025-05-22 12:01:27,037 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10590.0, 'new_value': 10714.4}, {'field': 'offline_amount', 'old_value': 23482.0, 'new_value': 24607.0}, {'field': 'total_amount', 'old_value': 34072.0, 'new_value': 35321.4}, {'field': 'order_count', 'old_value': 50, 'new_value': 55}]
2025-05-22 12:01:27,037 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-22 12:01:27,469 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMQE
2025-05-22 12:01:27,469 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 264836.0, 'new_value': 277096.0}, {'field': 'total_amount', 'old_value': 278611.0, 'new_value': 290871.0}, {'field': 'order_count', 'old_value': 5918, 'new_value': 6238}]
2025-05-22 12:01:27,469 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-22 12:01:27,876 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRE
2025-05-22 12:01:27,876 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 69911.04, 'new_value': 71314.74}, {'field': 'offline_amount', 'old_value': 157611.75, 'new_value': 161982.4}, {'field': 'total_amount', 'old_value': 227522.79, 'new_value': 233297.14}, {'field': 'order_count', 'old_value': 4216, 'new_value': 4366}]
2025-05-22 12:01:27,876 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-22 12:01:28,273 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMWV
2025-05-22 12:01:28,273 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 592622.9, 'new_value': 608246.17}, {'field': 'total_amount', 'old_value': 592622.9, 'new_value': 608246.17}, {'field': 'order_count', 'old_value': 6911, 'new_value': 7130}]
2025-05-22 12:01:28,273 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-22 12:01:28,703 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMUE
2025-05-22 12:01:28,703 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 155654.0, 'new_value': 156991.0}, {'field': 'total_amount', 'old_value': 155654.0, 'new_value': 156991.0}, {'field': 'order_count', 'old_value': 2601, 'new_value': 2626}]
2025-05-22 12:01:28,703 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-22 12:01:29,128 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMVE
2025-05-22 12:01:29,128 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 152087.49, 'new_value': 156021.29}, {'field': 'total_amount', 'old_value': 152087.49, 'new_value': 156021.29}, {'field': 'order_count', 'old_value': 6406, 'new_value': 6578}]
2025-05-22 12:01:29,128 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-22 12:01:29,545 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMWE
2025-05-22 12:01:29,545 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 192651.5, 'new_value': 198442.92}, {'field': 'total_amount', 'old_value': 192651.5, 'new_value': 198442.92}, {'field': 'order_count', 'old_value': 1473, 'new_value': 1517}]
2025-05-22 12:01:29,545 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-22 12:01:29,924 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMXE
2025-05-22 12:01:29,924 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 51501.6, 'new_value': 54501.6}, {'field': 'offline_amount', 'old_value': 83538.68, 'new_value': 83885.17}, {'field': 'total_amount', 'old_value': 135040.28, 'new_value': 138386.77}, {'field': 'order_count', 'old_value': 3753, 'new_value': 3849}]
2025-05-22 12:01:29,925 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-22 12:01:30,362 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMZV
2025-05-22 12:01:30,363 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 168073.11, 'new_value': 176487.16}, {'field': 'offline_amount', 'old_value': 21449.2, 'new_value': 21937.7}, {'field': 'total_amount', 'old_value': 189522.31, 'new_value': 198424.86}, {'field': 'order_count', 'old_value': 8917, 'new_value': 9202}]
2025-05-22 12:01:30,363 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-22 12:01:30,779 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM2F
2025-05-22 12:01:30,779 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30046.96, 'new_value': 31256.67}, {'field': 'offline_amount', 'old_value': 40033.54, 'new_value': 41033.54}, {'field': 'total_amount', 'old_value': 70080.5, 'new_value': 72290.21}, {'field': 'order_count', 'old_value': 3380, 'new_value': 3501}]
2025-05-22 12:01:30,779 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-22 12:01:31,235 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM1W
2025-05-22 12:01:31,235 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 18261.45, 'new_value': 19041.45}, {'field': 'offline_amount', 'old_value': 13315.16, 'new_value': 13711.16}, {'field': 'total_amount', 'old_value': 31576.61, 'new_value': 32752.61}, {'field': 'order_count', 'old_value': 1360, 'new_value': 1396}]
2025-05-22 12:01:31,235 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-22 12:01:31,694 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM3F
2025-05-22 12:01:31,694 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 246772.0, 'new_value': 259426.0}, {'field': 'total_amount', 'old_value': 246772.0, 'new_value': 259426.0}, {'field': 'order_count', 'old_value': 375, 'new_value': 386}]
2025-05-22 12:01:31,694 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOF
2025-05-22 12:01:32,254 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOF
2025-05-22 12:01:32,254 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8365.51, 'new_value': 9652.59}, {'field': 'offline_amount', 'old_value': 47509.14, 'new_value': 54310.03}, {'field': 'total_amount', 'old_value': 55874.65, 'new_value': 63962.62}, {'field': 'order_count', 'old_value': 1282, 'new_value': 1461}]
2025-05-22 12:01:32,254 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-22 12:01:32,707 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM5F
2025-05-22 12:01:32,707 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 91853.9, 'new_value': 97546.29}, {'field': 'offline_amount', 'old_value': 172871.66, 'new_value': 178230.45}, {'field': 'total_amount', 'old_value': 264725.56, 'new_value': 275776.74}, {'field': 'order_count', 'old_value': 8000, 'new_value': 8431}]
2025-05-22 12:01:32,707 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-22 12:01:33,176 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM3W
2025-05-22 12:01:33,176 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 142320.66, 'new_value': 149234.57}, {'field': 'offline_amount', 'old_value': 39224.79, 'new_value': 40317.02}, {'field': 'total_amount', 'old_value': 181545.45, 'new_value': 189551.59}, {'field': 'order_count', 'old_value': 10230, 'new_value': 10696}]
2025-05-22 12:01:33,176 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-22 12:01:33,606 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMOP
2025-05-22 12:01:33,606 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 224802.39, 'new_value': 229824.62}, {'field': 'total_amount', 'old_value': 247289.79, 'new_value': 252312.02}, {'field': 'order_count', 'old_value': 1343, 'new_value': 1383}]
2025-05-22 12:01:33,606 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-22 12:01:34,044 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMSP
2025-05-22 12:01:34,044 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9010.0, 'new_value': 10785.0}, {'field': 'total_amount', 'old_value': 17443.0, 'new_value': 19218.0}, {'field': 'order_count', 'old_value': 94, 'new_value': 100}]
2025-05-22 12:01:34,044 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-22 12:01:34,493 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM4W
2025-05-22 12:01:34,493 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 130396.83, 'new_value': 136794.55}, {'field': 'offline_amount', 'old_value': 241239.85, 'new_value': 248356.33}, {'field': 'total_amount', 'old_value': 371636.68, 'new_value': 385150.88}, {'field': 'order_count', 'old_value': 3075, 'new_value': 3211}]
2025-05-22 12:01:34,493 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-22 12:01:34,951 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM6W
2025-05-22 12:01:34,952 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34753.32, 'new_value': 36437.52}, {'field': 'offline_amount', 'old_value': 21146.25, 'new_value': 21935.72}, {'field': 'total_amount', 'old_value': 55899.57, 'new_value': 58373.24}, {'field': 'order_count', 'old_value': 2421, 'new_value': 2524}]
2025-05-22 12:01:34,952 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-22 12:01:35,447 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AM7W
2025-05-22 12:01:35,447 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12819.56, 'new_value': 13283.29}, {'field': 'offline_amount', 'old_value': 28907.2, 'new_value': 29197.4}, {'field': 'total_amount', 'old_value': 41726.76, 'new_value': 42480.69}, {'field': 'order_count', 'old_value': 1649, 'new_value': 1688}]
2025-05-22 12:01:35,447 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-22 12:01:35,948 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMAW
2025-05-22 12:01:35,948 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 288994.9, 'new_value': 316486.9}, {'field': 'total_amount', 'old_value': 288994.9, 'new_value': 316486.9}]
2025-05-22 12:01:35,948 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-22 12:01:36,498 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMYP
2025-05-22 12:01:36,498 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 647361.47, 'new_value': 661377.17}, {'field': 'total_amount', 'old_value': 647361.47, 'new_value': 661377.17}, {'field': 'order_count', 'old_value': 5113, 'new_value': 5271}]
2025-05-22 12:01:36,498 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-22 12:01:36,992 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMEW
2025-05-22 12:01:36,992 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 454483.0, 'new_value': 463102.0}, {'field': 'total_amount', 'old_value': 454483.0, 'new_value': 463102.0}, {'field': 'order_count', 'old_value': 2976, 'new_value': 3062}]
2025-05-22 12:01:36,992 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-22 12:01:37,476 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMZP
2025-05-22 12:01:37,476 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4255.0, 'new_value': 4355.0}, {'field': 'total_amount', 'old_value': 10683.0, 'new_value': 10783.0}, {'field': 'order_count', 'old_value': 102, 'new_value': 103}]
2025-05-22 12:01:37,476 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-22 12:01:37,945 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMFW
2025-05-22 12:01:37,945 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2875.0, 'new_value': 2962.0}, {'field': 'offline_amount', 'old_value': 24766.2, 'new_value': 25121.2}, {'field': 'total_amount', 'old_value': 27641.2, 'new_value': 28083.2}, {'field': 'order_count', 'old_value': 1009, 'new_value': 1026}]
2025-05-22 12:01:37,946 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-22 12:01:38,462 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMGW
2025-05-22 12:01:38,462 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 89050.01, 'new_value': 94057.15}, {'field': 'total_amount', 'old_value': 96279.08, 'new_value': 101286.22}, {'field': 'order_count', 'old_value': 512, 'new_value': 546}]
2025-05-22 12:01:38,463 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-22 12:01:39,030 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMHW
2025-05-22 12:01:39,030 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17404.0, 'new_value': 19184.0}, {'field': 'total_amount', 'old_value': 22022.0, 'new_value': 23802.0}, {'field': 'order_count', 'old_value': 175, 'new_value': 187}]
2025-05-22 12:01:39,030 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-22 12:01:39,483 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMIW
2025-05-22 12:01:39,483 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 865147.0, 'new_value': 890746.0}, {'field': 'total_amount', 'old_value': 865147.0, 'new_value': 890746.0}, {'field': 'order_count', 'old_value': 3779, 'new_value': 3900}]
2025-05-22 12:01:39,483 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-22 12:01:39,956 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMJW
2025-05-22 12:01:39,957 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10942122.0, 'new_value': 11186132.0}, {'field': 'total_amount', 'old_value': 10942122.0, 'new_value': 11186132.0}, {'field': 'order_count', 'old_value': 33569, 'new_value': 34505}]
2025-05-22 12:01:39,957 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-22 12:01:40,470 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMPC
2025-05-22 12:01:40,471 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3030320.39, 'new_value': 3186706.79}, {'field': 'total_amount', 'old_value': 3030320.39, 'new_value': 3186706.79}, {'field': 'order_count', 'old_value': 5240, 'new_value': 5466}]
2025-05-22 12:01:40,471 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-22 12:01:40,907 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMQC
2025-05-22 12:01:40,907 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 127983.49, 'new_value': 135377.18}, {'field': 'total_amount', 'old_value': 135423.13, 'new_value': 142816.82}, {'field': 'order_count', 'old_value': 9416, 'new_value': 9959}]
2025-05-22 12:01:40,907 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-22 12:01:41,420 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMRC
2025-05-22 12:01:41,420 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 267001.0, 'new_value': 280998.0}, {'field': 'total_amount', 'old_value': 267001.0, 'new_value': 280998.0}, {'field': 'order_count', 'old_value': 5572, 'new_value': 5922}]
2025-05-22 12:01:41,421 - INFO - 开始更新记录 - 表单实例ID: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-22 12:01:41,854 - INFO - 更新表单数据成功: FINST-T9D66B81F11V5BXNE0OIN580NGIJ2HRJRW5AMSC
2025-05-22 12:01:41,854 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 241244.86, 'new_value': 254789.37}, {'field': 'offline_amount', 'old_value': 172210.21, 'new_value': 177590.68}, {'field': 'total_amount', 'old_value': 413455.07, 'new_value': 432380.05}, {'field': 'order_count', 'old_value': 16589, 'new_value': 17289}]
2025-05-22 12:01:41,854 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-22 12:01:42,333 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMRF
2025-05-22 12:01:42,333 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 36872.9, 'new_value': 38515.88}, {'field': 'offline_amount', 'old_value': 58395.44, 'new_value': 60534.31}, {'field': 'total_amount', 'old_value': 95268.34, 'new_value': 99050.19}, {'field': 'order_count', 'old_value': 1976, 'new_value': 2073}]
2025-05-22 12:01:42,333 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJQ
2025-05-22 12:01:42,782 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMJQ
2025-05-22 12:01:42,783 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25080.0, 'new_value': 30130.0}, {'field': 'total_amount', 'old_value': 25080.0, 'new_value': 30130.0}, {'field': 'order_count', 'old_value': 7, 'new_value': 9}]
2025-05-22 12:01:42,783 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-22 12:01:43,216 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMLQ
2025-05-22 12:01:43,217 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 274578.0, 'new_value': 278258.4}, {'field': 'total_amount', 'old_value': 274578.0, 'new_value': 278258.4}, {'field': 'order_count', 'old_value': 5979, 'new_value': 6059}]
2025-05-22 12:01:43,217 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSF
2025-05-22 12:01:43,618 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMSF
2025-05-22 12:01:43,618 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40671.0, 'new_value': 49078.0}, {'field': 'total_amount', 'old_value': 40671.0, 'new_value': 49078.0}, {'field': 'order_count', 'old_value': 8599, 'new_value': 10546}]
2025-05-22 12:01:43,619 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTF
2025-05-22 12:01:44,111 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTF
2025-05-22 12:01:44,111 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59652.0, 'new_value': 72263.0}, {'field': 'total_amount', 'old_value': 59652.0, 'new_value': 72263.0}, {'field': 'order_count', 'old_value': 8599, 'new_value': 10546}]
2025-05-22 12:01:44,112 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-22 12:01:44,625 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMQQ
2025-05-22 12:01:44,626 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30121.0, 'new_value': 30727.0}, {'field': 'total_amount', 'old_value': 30121.0, 'new_value': 30727.0}, {'field': 'order_count', 'old_value': 35, 'new_value': 36}]
2025-05-22 12:01:44,626 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-22 12:01:45,065 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMRQ
2025-05-22 12:01:45,065 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66261.2, 'new_value': 66681.2}, {'field': 'total_amount', 'old_value': 66514.2, 'new_value': 66934.2}, {'field': 'order_count', 'old_value': 981, 'new_value': 991}]
2025-05-22 12:01:45,065 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-22 12:01:45,557 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM7V
2025-05-22 12:01:45,557 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4075207.51, 'new_value': 4236564.51}, {'field': 'total_amount', 'old_value': 4075207.51, 'new_value': 4236564.51}, {'field': 'order_count', 'old_value': 83827, 'new_value': 87399}]
2025-05-22 12:01:45,557 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-22 12:01:45,986 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AM8V
2025-05-22 12:01:45,986 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20817.93, 'new_value': 21252.93}, {'field': 'total_amount', 'old_value': 20817.93, 'new_value': 21252.93}, {'field': 'order_count', 'old_value': 94, 'new_value': 97}]
2025-05-22 12:01:45,986 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-22 12:01:46,464 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMAV
2025-05-22 12:01:46,464 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 452625.59, 'new_value': 467116.39}, {'field': 'total_amount', 'old_value': 458171.95, 'new_value': 472662.75}, {'field': 'order_count', 'old_value': 4989, 'new_value': 5114}]
2025-05-22 12:01:46,464 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-22 12:01:46,922 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMBV
2025-05-22 12:01:46,922 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 157557.05, 'new_value': 168512.15}, {'field': 'total_amount', 'old_value': 157557.05, 'new_value': 168512.15}, {'field': 'order_count', 'old_value': 2960, 'new_value': 3149}]
2025-05-22 12:01:46,922 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-22 12:01:47,354 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-22 12:01:47,354 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 269803.0, 'new_value': 301306.0}, {'field': 'total_amount', 'old_value': 269803.0, 'new_value': 301306.0}, {'field': 'order_count', 'old_value': 64, 'new_value': 70}]
2025-05-22 12:01:47,355 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-22 12:01:47,882 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMGV
2025-05-22 12:01:47,882 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 107147.36, 'new_value': 111578.41}, {'field': 'total_amount', 'old_value': 107147.36, 'new_value': 111578.41}, {'field': 'order_count', 'old_value': 2711, 'new_value': 2836}]
2025-05-22 12:01:47,882 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVF
2025-05-22 12:01:48,311 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVF
2025-05-22 12:01:48,311 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78526.0, 'new_value': 89691.0}, {'field': 'total_amount', 'old_value': 78526.0, 'new_value': 89691.0}, {'field': 'order_count', 'old_value': 207, 'new_value': 228}]
2025-05-22 12:01:48,312 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-22 12:01:48,775 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWF
2025-05-22 12:01:48,775 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25684.0, 'new_value': 27416.0}, {'field': 'total_amount', 'old_value': 25684.0, 'new_value': 27416.0}, {'field': 'order_count', 'old_value': 84, 'new_value': 87}]
2025-05-22 12:01:48,775 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-22 12:01:49,177 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMIV
2025-05-22 12:01:49,178 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 81895.96, 'new_value': 85352.16}, {'field': 'offline_amount', 'old_value': 347929.7, 'new_value': 354453.3}, {'field': 'total_amount', 'old_value': 429825.66, 'new_value': 439805.46}, {'field': 'order_count', 'old_value': 3001, 'new_value': 3111}]
2025-05-22 12:01:49,178 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-22 12:01:49,601 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMXF
2025-05-22 12:01:49,602 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 65638.0, 'new_value': 68373.0}, {'field': 'total_amount', 'old_value': 67488.0, 'new_value': 70223.0}, {'field': 'order_count', 'old_value': 391, 'new_value': 406}]
2025-05-22 12:01:49,602 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYF
2025-05-22 12:01:50,054 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYF
2025-05-22 12:01:50,054 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 114.0, 'new_value': 615.5}, {'field': 'offline_amount', 'old_value': 24049.6, 'new_value': 32612.6}, {'field': 'total_amount', 'old_value': 24163.6, 'new_value': 33228.1}, {'field': 'order_count', 'old_value': 145, 'new_value': 212}]
2025-05-22 12:01:50,054 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-22 12:01:50,450 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM0G
2025-05-22 12:01:50,451 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27442.72, 'new_value': 31217.95}, {'field': 'total_amount', 'old_value': 73864.29, 'new_value': 77639.52}, {'field': 'order_count', 'old_value': 4816, 'new_value': 5080}]
2025-05-22 12:01:50,451 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-22 12:01:50,966 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM1G
2025-05-22 12:01:50,966 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46416.81, 'new_value': 53866.09}, {'field': 'total_amount', 'old_value': 127798.81, 'new_value': 135248.09}, {'field': 'order_count', 'old_value': 8396, 'new_value': 8866}]
2025-05-22 12:01:50,966 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-22 12:01:51,426 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM2G
2025-05-22 12:01:51,426 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 947718.11, 'new_value': 1000434.96}, {'field': 'total_amount', 'old_value': 947718.11, 'new_value': 1000434.96}, {'field': 'order_count', 'old_value': 2790, 'new_value': 2927}]
2025-05-22 12:01:51,426 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-22 12:01:51,843 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3G
2025-05-22 12:01:51,843 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 142765.8, 'new_value': 151695.8}, {'field': 'total_amount', 'old_value': 142765.8, 'new_value': 151695.8}, {'field': 'order_count', 'old_value': 4981, 'new_value': 5299}]
2025-05-22 12:01:51,844 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-22 12:01:52,285 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM5G
2025-05-22 12:01:52,285 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 586113.97, 'new_value': 611844.92}, {'field': 'total_amount', 'old_value': 586113.97, 'new_value': 611844.92}, {'field': 'order_count', 'old_value': 2977, 'new_value': 3139}]
2025-05-22 12:01:52,285 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6G
2025-05-22 12:01:52,668 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM6G
2025-05-22 12:01:52,668 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 870441.3, 'new_value': 907884.92}, {'field': 'total_amount', 'old_value': 870441.3, 'new_value': 907884.92}, {'field': 'order_count', 'old_value': 3080, 'new_value': 3212}]
2025-05-22 12:01:52,668 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-22 12:01:53,121 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM7G
2025-05-22 12:01:53,121 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 662757.5, 'new_value': 691370.08}, {'field': 'total_amount', 'old_value': 662757.5, 'new_value': 691370.08}, {'field': 'order_count', 'old_value': 1939, 'new_value': 1999}]
2025-05-22 12:01:53,122 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAG
2025-05-22 12:01:53,587 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMAG
2025-05-22 12:01:53,587 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39103.62, 'new_value': 41116.22}, {'field': 'total_amount', 'old_value': 41947.62, 'new_value': 43960.22}, {'field': 'order_count', 'old_value': 304, 'new_value': 320}]
2025-05-22 12:01:53,587 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-22 12:01:54,094 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMEG
2025-05-22 12:01:54,094 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15029.0, 'new_value': 15505.0}, {'field': 'offline_amount', 'old_value': 47959.0, 'new_value': 49554.0}, {'field': 'total_amount', 'old_value': 62988.0, 'new_value': 65059.0}, {'field': 'order_count', 'old_value': 101, 'new_value': 109}]
2025-05-22 12:01:54,094 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGG
2025-05-22 12:01:54,605 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMGG
2025-05-22 12:01:54,605 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 109077.0, 'new_value': 115983.0}, {'field': 'offline_amount', 'old_value': 72258.0, 'new_value': 77949.0}, {'field': 'total_amount', 'old_value': 181335.0, 'new_value': 193932.0}, {'field': 'order_count', 'old_value': 7485, 'new_value': 7987}]
2025-05-22 12:01:54,605 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-22 12:01:55,132 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-22 12:01:55,132 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 92071.0, 'new_value': 96078.0}, {'field': 'total_amount', 'old_value': 92071.0, 'new_value': 96078.0}, {'field': 'order_count', 'old_value': 477, 'new_value': 478}]
2025-05-22 12:01:55,132 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-22 12:01:55,549 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMJG
2025-05-22 12:01:55,549 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 200485.0, 'new_value': 214285.0}, {'field': 'total_amount', 'old_value': 200485.0, 'new_value': 214285.0}, {'field': 'order_count', 'old_value': 476, 'new_value': 508}]
2025-05-22 12:01:55,549 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-22 12:01:56,031 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMNG
2025-05-22 12:01:56,031 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 152905.0, 'new_value': 164250.0}, {'field': 'total_amount', 'old_value': 152905.0, 'new_value': 164250.0}, {'field': 'order_count', 'old_value': 16058, 'new_value': 17313}]
2025-05-22 12:01:56,031 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-22 12:01:56,543 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMOG
2025-05-22 12:01:56,543 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 99332.0, 'new_value': 102653.0}, {'field': 'total_amount', 'old_value': 99332.0, 'new_value': 102653.0}, {'field': 'order_count', 'old_value': 920, 'new_value': 963}]
2025-05-22 12:01:56,543 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-22 12:01:57,023 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-22 12:01:57,023 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 131095.16, 'new_value': 133095.16}, {'field': 'total_amount', 'old_value': 131095.16, 'new_value': 133095.16}, {'field': 'order_count', 'old_value': 1104, 'new_value': 1105}]
2025-05-22 12:01:57,023 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTG
2025-05-22 12:01:57,477 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMTG
2025-05-22 12:01:57,477 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36816.0, 'new_value': 40016.0}, {'field': 'total_amount', 'old_value': 36816.0, 'new_value': 40016.0}, {'field': 'order_count', 'old_value': 11, 'new_value': 12}]
2025-05-22 12:01:57,477 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-22 12:01:57,927 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUG
2025-05-22 12:01:57,927 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33791.0, 'new_value': 33989.0}, {'field': 'offline_amount', 'old_value': 38406.4, 'new_value': 39228.4}, {'field': 'total_amount', 'old_value': 72197.4, 'new_value': 73217.4}, {'field': 'order_count', 'old_value': 98, 'new_value': 101}]
2025-05-22 12:01:57,927 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-22 12:01:58,345 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMVG
2025-05-22 12:01:58,345 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40312.76, 'new_value': 41614.76}, {'field': 'total_amount', 'old_value': 40312.76, 'new_value': 41614.76}, {'field': 'order_count', 'old_value': 678, 'new_value': 705}]
2025-05-22 12:01:58,345 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-22 12:01:58,835 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMWG
2025-05-22 12:01:58,836 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 105740.47, 'new_value': 110011.29}, {'field': 'offline_amount', 'old_value': 556365.11, 'new_value': 585259.35}, {'field': 'total_amount', 'old_value': 662105.58, 'new_value': 695270.64}, {'field': 'order_count', 'old_value': 1520, 'new_value': 1593}]
2025-05-22 12:01:58,836 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-22 12:01:59,284 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMYG
2025-05-22 12:01:59,284 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64856.6, 'new_value': 67300.6}, {'field': 'total_amount', 'old_value': 64856.6, 'new_value': 67300.6}, {'field': 'order_count', 'old_value': 40, 'new_value': 42}]
2025-05-22 12:01:59,284 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-22 12:01:59,739 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMZG
2025-05-22 12:01:59,739 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 70016.08, 'new_value': 74529.33}, {'field': 'offline_amount', 'old_value': 689487.6, 'new_value': 736320.03}, {'field': 'total_amount', 'old_value': 757629.35, 'new_value': 808975.03}, {'field': 'order_count', 'old_value': 3593, 'new_value': 3835}]
2025-05-22 12:01:59,739 - INFO - 开始更新记录 - 表单实例ID: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-22 12:02:00,179 - INFO - 更新表单数据成功: FINST-2FD66I71T41V9C5W7C4LZ8HU6NPD3NHX1G6AMAC
2025-05-22 12:02:00,179 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89085.0, 'new_value': 93699.0}, {'field': 'total_amount', 'old_value': 89085.0, 'new_value': 93699.0}, {'field': 'order_count', 'old_value': 301, 'new_value': 313}]
2025-05-22 12:02:00,179 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-05-22 12:02:00,588 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMJE
2025-05-22 12:02:00,588 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 66632.0, 'new_value': 67330.0}, {'field': 'total_amount', 'old_value': 71950.0, 'new_value': 72648.0}, {'field': 'order_count', 'old_value': 37, 'new_value': 39}]
2025-05-22 12:02:00,588 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-22 12:02:01,095 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMNE
2025-05-22 12:02:01,095 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14093218.76, 'new_value': 14758366.62}, {'field': 'total_amount', 'old_value': 14093218.76, 'new_value': 14758366.62}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-05-22 12:02:01,096 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-22 12:02:01,538 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMCQ
2025-05-22 12:02:01,538 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 146798.13, 'new_value': 153259.24}, {'field': 'total_amount', 'old_value': 146798.13, 'new_value': 153259.24}, {'field': 'order_count', 'old_value': 15272, 'new_value': 16011}]
2025-05-22 12:02:01,538 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-22 12:02:01,985 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMEQ
2025-05-22 12:02:01,985 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 16676.0, 'new_value': 16914.0}, {'field': 'total_amount', 'old_value': 16676.0, 'new_value': 16914.0}, {'field': 'order_count', 'old_value': 106, 'new_value': 108}]
2025-05-22 12:02:01,985 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-22 12:02:02,416 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM4H2
2025-05-22 12:02:02,416 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36587.58, 'new_value': 39535.6}, {'field': 'total_amount', 'old_value': 36587.58, 'new_value': 39535.6}, {'field': 'order_count', 'old_value': 1816, 'new_value': 2016}]
2025-05-22 12:02:02,416 - INFO - 日期 2025-05 处理完成 - 更新: 196 条，插入: 0 条，错误: 0 条
2025-05-22 12:02:02,416 - INFO - 数据同步完成！更新: 197 条，插入: 0 条，错误: 0 条
2025-05-22 12:02:02,419 - INFO - =================同步完成====================
2025-05-22 15:00:01,974 - INFO - =================使用默认全量同步=============
2025-05-22 15:00:03,380 - INFO - MySQL查询成功，共获取 3297 条记录
2025-05-22 15:00:03,380 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-22 15:00:03,411 - INFO - 开始处理日期: 2025-01
2025-05-22 15:00:03,411 - INFO - Request Parameters - Page 1:
2025-05-22 15:00:03,411 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 15:00:03,411 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 15:00:04,521 - INFO - Response - Page 1:
2025-05-22 15:00:04,724 - INFO - 第 1 页获取到 100 条记录
2025-05-22 15:00:04,724 - INFO - Request Parameters - Page 2:
2025-05-22 15:00:04,724 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 15:00:04,724 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 15:00:05,489 - INFO - Response - Page 2:
2025-05-22 15:00:05,693 - INFO - 第 2 页获取到 100 条记录
2025-05-22 15:00:05,693 - INFO - Request Parameters - Page 3:
2025-05-22 15:00:05,693 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 15:00:05,693 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 15:00:06,224 - INFO - Response - Page 3:
2025-05-22 15:00:06,427 - INFO - 第 3 页获取到 100 条记录
2025-05-22 15:00:06,427 - INFO - Request Parameters - Page 4:
2025-05-22 15:00:06,427 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 15:00:06,427 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 15:00:06,974 - INFO - Response - Page 4:
2025-05-22 15:00:07,177 - INFO - 第 4 页获取到 100 条记录
2025-05-22 15:00:07,177 - INFO - Request Parameters - Page 5:
2025-05-22 15:00:07,177 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 15:00:07,177 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 15:00:07,615 - INFO - Response - Page 5:
2025-05-22 15:00:07,818 - INFO - 第 5 页获取到 100 条记录
2025-05-22 15:00:07,818 - INFO - Request Parameters - Page 6:
2025-05-22 15:00:07,818 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 15:00:07,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 15:00:08,380 - INFO - Response - Page 6:
2025-05-22 15:00:08,583 - INFO - 第 6 页获取到 100 条记录
2025-05-22 15:00:08,583 - INFO - Request Parameters - Page 7:
2025-05-22 15:00:08,583 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 15:00:08,583 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 15:00:09,161 - INFO - Response - Page 7:
2025-05-22 15:00:09,364 - INFO - 第 7 页获取到 82 条记录
2025-05-22 15:00:09,364 - INFO - 查询完成，共获取到 682 条记录
2025-05-22 15:00:09,364 - INFO - 获取到 682 条表单数据
2025-05-22 15:00:09,364 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-22 15:00:09,380 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-22 15:00:09,380 - INFO - 开始处理日期: 2025-02
2025-05-22 15:00:09,380 - INFO - Request Parameters - Page 1:
2025-05-22 15:00:09,380 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 15:00:09,380 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 15:00:09,880 - INFO - Response - Page 1:
2025-05-22 15:00:10,083 - INFO - 第 1 页获取到 100 条记录
2025-05-22 15:00:10,083 - INFO - Request Parameters - Page 2:
2025-05-22 15:00:10,083 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 15:00:10,083 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 15:00:10,568 - INFO - Response - Page 2:
2025-05-22 15:00:10,771 - INFO - 第 2 页获取到 100 条记录
2025-05-22 15:00:10,771 - INFO - Request Parameters - Page 3:
2025-05-22 15:00:10,771 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 15:00:10,771 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 15:00:11,286 - INFO - Response - Page 3:
2025-05-22 15:00:11,489 - INFO - 第 3 页获取到 100 条记录
2025-05-22 15:00:11,489 - INFO - Request Parameters - Page 4:
2025-05-22 15:00:11,489 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 15:00:11,489 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 15:00:12,068 - INFO - Response - Page 4:
2025-05-22 15:00:12,271 - INFO - 第 4 页获取到 100 条记录
2025-05-22 15:00:12,271 - INFO - Request Parameters - Page 5:
2025-05-22 15:00:12,271 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 15:00:12,271 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 15:00:12,911 - INFO - Response - Page 5:
2025-05-22 15:00:13,114 - INFO - 第 5 页获取到 100 条记录
2025-05-22 15:00:13,114 - INFO - Request Parameters - Page 6:
2025-05-22 15:00:13,114 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 15:00:13,114 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 15:00:13,802 - INFO - Response - Page 6:
2025-05-22 15:00:14,005 - INFO - 第 6 页获取到 100 条记录
2025-05-22 15:00:14,005 - INFO - Request Parameters - Page 7:
2025-05-22 15:00:14,005 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 15:00:14,005 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 15:00:14,505 - INFO - Response - Page 7:
2025-05-22 15:00:14,708 - INFO - 第 7 页获取到 70 条记录
2025-05-22 15:00:14,708 - INFO - 查询完成，共获取到 670 条记录
2025-05-22 15:00:14,708 - INFO - 获取到 670 条表单数据
2025-05-22 15:00:14,708 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-22 15:00:14,724 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-22 15:00:14,724 - INFO - 开始处理日期: 2025-03
2025-05-22 15:00:14,724 - INFO - Request Parameters - Page 1:
2025-05-22 15:00:14,724 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 15:00:14,724 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 15:00:15,224 - INFO - Response - Page 1:
2025-05-22 15:00:15,427 - INFO - 第 1 页获取到 100 条记录
2025-05-22 15:00:15,427 - INFO - Request Parameters - Page 2:
2025-05-22 15:00:15,427 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 15:00:15,427 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 15:00:15,958 - INFO - Response - Page 2:
2025-05-22 15:00:16,161 - INFO - 第 2 页获取到 100 条记录
2025-05-22 15:00:16,161 - INFO - Request Parameters - Page 3:
2025-05-22 15:00:16,161 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 15:00:16,161 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 15:00:16,630 - INFO - Response - Page 3:
2025-05-22 15:00:16,833 - INFO - 第 3 页获取到 100 条记录
2025-05-22 15:00:16,833 - INFO - Request Parameters - Page 4:
2025-05-22 15:00:16,833 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 15:00:16,833 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 15:00:17,349 - INFO - Response - Page 4:
2025-05-22 15:00:17,552 - INFO - 第 4 页获取到 100 条记录
2025-05-22 15:00:17,552 - INFO - Request Parameters - Page 5:
2025-05-22 15:00:17,552 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 15:00:17,552 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 15:00:18,083 - INFO - Response - Page 5:
2025-05-22 15:00:18,286 - INFO - 第 5 页获取到 100 条记录
2025-05-22 15:00:18,286 - INFO - Request Parameters - Page 6:
2025-05-22 15:00:18,286 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 15:00:18,286 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 15:00:18,896 - INFO - Response - Page 6:
2025-05-22 15:00:19,099 - INFO - 第 6 页获取到 100 条记录
2025-05-22 15:00:19,099 - INFO - Request Parameters - Page 7:
2025-05-22 15:00:19,099 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 15:00:19,099 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 15:00:19,552 - INFO - Response - Page 7:
2025-05-22 15:00:19,755 - INFO - 第 7 页获取到 61 条记录
2025-05-22 15:00:19,755 - INFO - 查询完成，共获取到 661 条记录
2025-05-22 15:00:19,755 - INFO - 获取到 661 条表单数据
2025-05-22 15:00:19,755 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-22 15:00:19,771 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-22 15:00:19,771 - INFO - 开始处理日期: 2025-04
2025-05-22 15:00:19,771 - INFO - Request Parameters - Page 1:
2025-05-22 15:00:19,771 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 15:00:19,771 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 15:00:20,442 - INFO - Response - Page 1:
2025-05-22 15:00:20,646 - INFO - 第 1 页获取到 100 条记录
2025-05-22 15:00:20,646 - INFO - Request Parameters - Page 2:
2025-05-22 15:00:20,646 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 15:00:20,646 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 15:00:21,146 - INFO - Response - Page 2:
2025-05-22 15:00:21,349 - INFO - 第 2 页获取到 100 条记录
2025-05-22 15:00:21,349 - INFO - Request Parameters - Page 3:
2025-05-22 15:00:21,349 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 15:00:21,349 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 15:00:22,052 - INFO - Response - Page 3:
2025-05-22 15:00:22,255 - INFO - 第 3 页获取到 100 条记录
2025-05-22 15:00:22,255 - INFO - Request Parameters - Page 4:
2025-05-22 15:00:22,255 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 15:00:22,255 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 15:00:22,802 - INFO - Response - Page 4:
2025-05-22 15:00:23,005 - INFO - 第 4 页获取到 100 条记录
2025-05-22 15:00:23,005 - INFO - Request Parameters - Page 5:
2025-05-22 15:00:23,005 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 15:00:23,005 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 15:00:23,505 - INFO - Response - Page 5:
2025-05-22 15:00:23,708 - INFO - 第 5 页获取到 100 条记录
2025-05-22 15:00:23,708 - INFO - Request Parameters - Page 6:
2025-05-22 15:00:23,708 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 15:00:23,708 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 15:00:24,317 - INFO - Response - Page 6:
2025-05-22 15:00:24,521 - INFO - 第 6 页获取到 100 条记录
2025-05-22 15:00:24,521 - INFO - Request Parameters - Page 7:
2025-05-22 15:00:24,521 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 15:00:24,521 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 15:00:25,036 - INFO - Response - Page 7:
2025-05-22 15:00:25,239 - INFO - 第 7 页获取到 56 条记录
2025-05-22 15:00:25,239 - INFO - 查询完成，共获取到 656 条记录
2025-05-22 15:00:25,239 - INFO - 获取到 656 条表单数据
2025-05-22 15:00:25,255 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-22 15:00:25,255 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-22 15:00:25,255 - INFO - 开始处理日期: 2025-05
2025-05-22 15:00:25,255 - INFO - Request Parameters - Page 1:
2025-05-22 15:00:25,255 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 15:00:25,255 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 15:00:25,755 - INFO - Response - Page 1:
2025-05-22 15:00:25,958 - INFO - 第 1 页获取到 100 条记录
2025-05-22 15:00:25,958 - INFO - Request Parameters - Page 2:
2025-05-22 15:00:25,958 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 15:00:25,958 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 15:00:26,521 - INFO - Response - Page 2:
2025-05-22 15:00:26,724 - INFO - 第 2 页获取到 100 条记录
2025-05-22 15:00:26,724 - INFO - Request Parameters - Page 3:
2025-05-22 15:00:26,724 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 15:00:26,724 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 15:00:27,192 - INFO - Response - Page 3:
2025-05-22 15:00:27,396 - INFO - 第 3 页获取到 100 条记录
2025-05-22 15:00:27,396 - INFO - Request Parameters - Page 4:
2025-05-22 15:00:27,396 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 15:00:27,396 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 15:00:27,958 - INFO - Response - Page 4:
2025-05-22 15:00:28,161 - INFO - 第 4 页获取到 100 条记录
2025-05-22 15:00:28,161 - INFO - Request Parameters - Page 5:
2025-05-22 15:00:28,161 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 15:00:28,161 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 15:00:28,708 - INFO - Response - Page 5:
2025-05-22 15:00:28,911 - INFO - 第 5 页获取到 100 条记录
2025-05-22 15:00:28,911 - INFO - Request Parameters - Page 6:
2025-05-22 15:00:28,911 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 15:00:28,911 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 15:00:29,411 - INFO - Response - Page 6:
2025-05-22 15:00:29,614 - INFO - 第 6 页获取到 100 条记录
2025-05-22 15:00:29,614 - INFO - Request Parameters - Page 7:
2025-05-22 15:00:29,614 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 15:00:29,614 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 15:00:30,036 - INFO - Response - Page 7:
2025-05-22 15:00:30,239 - INFO - 第 7 页获取到 28 条记录
2025-05-22 15:00:30,239 - INFO - 查询完成，共获取到 628 条记录
2025-05-22 15:00:30,239 - INFO - 获取到 628 条表单数据
2025-05-22 15:00:30,239 - INFO - 当前日期 2025-05 有 628 条MySQL数据需要处理
2025-05-22 15:00:30,239 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-22 15:00:30,677 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AM3F
2025-05-22 15:00:30,677 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 145388.48, 'new_value': 154655.94}, {'field': 'total_amount', 'old_value': 145388.48, 'new_value': 154655.94}, {'field': 'order_count', 'old_value': 5678, 'new_value': 5967}]
2025-05-22 15:00:30,677 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-22 15:00:31,161 - INFO - 更新表单数据成功: FINST-VOC66Y918N0V7394A4TFE8KOR13T3B1OHD5AM1A
2025-05-22 15:00:31,161 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 13467.0, 'new_value': 53459.0}, {'field': 'total_amount', 'old_value': 16364.0, 'new_value': 56356.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 27}]
2025-05-22 15:00:31,161 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM1Q
2025-05-22 15:00:31,614 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM1Q
2025-05-22 15:00:31,614 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 53633.95, 'new_value': 57765.62}, {'field': 'total_amount', 'old_value': 58222.67, 'new_value': 62354.34}, {'field': 'order_count', 'old_value': 2075, 'new_value': 2228}]
2025-05-22 15:00:31,614 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMO3
2025-05-22 15:00:32,036 - INFO - 更新表单数据成功: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMO3
2025-05-22 15:00:32,036 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73997.98, 'new_value': 78356.33}, {'field': 'total_amount', 'old_value': 100337.91, 'new_value': 104696.26}, {'field': 'order_count', 'old_value': 2298, 'new_value': 2402}]
2025-05-22 15:00:32,036 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM4A
2025-05-22 15:00:32,474 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM4A
2025-05-22 15:00:32,474 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3428.97, 'new_value': 3588.73}, {'field': 'offline_amount', 'old_value': 68051.29, 'new_value': 70264.43}, {'field': 'total_amount', 'old_value': 71480.26, 'new_value': 73853.16}, {'field': 'order_count', 'old_value': 2771, 'new_value': 2883}]
2025-05-22 15:00:32,474 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM2Q
2025-05-22 15:00:33,067 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM2Q
2025-05-22 15:00:33,067 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 193650.04, 'new_value': 202378.88}, {'field': 'offline_amount', 'old_value': 14634.15, 'new_value': 15460.15}, {'field': 'total_amount', 'old_value': 208284.19, 'new_value': 217839.03}, {'field': 'order_count', 'old_value': 4479, 'new_value': 4715}]
2025-05-22 15:00:33,067 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A
2025-05-22 15:00:33,521 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM5A
2025-05-22 15:00:33,521 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77955.51, 'new_value': 80373.85}, {'field': 'total_amount', 'old_value': 77955.51, 'new_value': 80373.85}, {'field': 'order_count', 'old_value': 2956, 'new_value': 3064}]
2025-05-22 15:00:33,521 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM7A
2025-05-22 15:00:33,927 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM7A
2025-05-22 15:00:33,927 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38042.22, 'new_value': 40240.27}, {'field': 'offline_amount', 'old_value': 42340.11, 'new_value': 43413.88}, {'field': 'total_amount', 'old_value': 80382.33, 'new_value': 83654.15}, {'field': 'order_count', 'old_value': 4162, 'new_value': 4373}]
2025-05-22 15:00:33,927 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM8A
2025-05-22 15:00:34,411 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM8A
2025-05-22 15:00:34,411 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7794.67, 'new_value': 8284.85}, {'field': 'offline_amount', 'old_value': 102196.21, 'new_value': 105491.57}, {'field': 'total_amount', 'old_value': 109990.88, 'new_value': 113776.42}, {'field': 'order_count', 'old_value': 1773, 'new_value': 1835}]
2025-05-22 15:00:34,411 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-22 15:00:34,927 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMXT
2025-05-22 15:00:34,927 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 166165.0, 'new_value': 175402.0}, {'field': 'total_amount', 'old_value': 166165.0, 'new_value': 175402.0}, {'field': 'order_count', 'old_value': 855, 'new_value': 901}]
2025-05-22 15:00:34,927 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-05-22 15:00:35,427 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223H25XJ5AM5E
2025-05-22 15:00:35,427 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25690.28, 'new_value': 59258.5}, {'field': 'offline_amount', 'old_value': 500.0, 'new_value': 33568.22}, {'field': 'total_amount', 'old_value': 26190.28, 'new_value': 92826.72}, {'field': 'order_count', 'old_value': 40, 'new_value': 66}]
2025-05-22 15:00:35,427 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM3Q
2025-05-22 15:00:35,849 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM3Q
2025-05-22 15:00:35,849 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 92155.32, 'new_value': 98905.05}, {'field': 'offline_amount', 'old_value': 76346.56, 'new_value': 80607.47}, {'field': 'total_amount', 'old_value': 168501.88, 'new_value': 179512.52}, {'field': 'order_count', 'old_value': 6081, 'new_value': 6526}]
2025-05-22 15:00:35,849 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM9A
2025-05-22 15:00:36,333 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM9A
2025-05-22 15:00:36,333 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58092.49, 'new_value': 60691.79}, {'field': 'total_amount', 'old_value': 58092.49, 'new_value': 60691.79}, {'field': 'order_count', 'old_value': 2655, 'new_value': 2770}]
2025-05-22 15:00:36,333 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM5Q
2025-05-22 15:00:36,739 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM5Q
2025-05-22 15:00:36,739 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 420589.09, 'new_value': 437474.79}, {'field': 'total_amount', 'old_value': 420589.09, 'new_value': 437474.79}, {'field': 'order_count', 'old_value': 4256, 'new_value': 4421}]
2025-05-22 15:00:36,739 - INFO - 开始更新记录 - 表单实例ID: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-22 15:00:37,271 - INFO - 更新表单数据成功: FINST-X3E66X81E3ZUP0FJDPYUL8616GW039IDCQ5AMCU
2025-05-22 15:00:37,271 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 509081.0, 'new_value': 514081.0}, {'field': 'total_amount', 'old_value': 509081.0, 'new_value': 514081.0}]
2025-05-22 15:00:37,271 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM7
2025-05-22 15:00:37,744 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM7
2025-05-22 15:00:37,744 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2131.42, 'new_value': 2247.4}, {'field': 'offline_amount', 'old_value': 19533.72, 'new_value': 20153.72}, {'field': 'total_amount', 'old_value': 21665.14, 'new_value': 22401.12}, {'field': 'order_count', 'old_value': 983, 'new_value': 1023}]
2025-05-22 15:00:37,745 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM5H2
2025-05-22 15:00:38,195 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM5H2
2025-05-22 15:00:38,195 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 710878.16, 'new_value': 739328.16}, {'field': 'total_amount', 'old_value': 710878.16, 'new_value': 739328.16}, {'field': 'order_count', 'old_value': 2816, 'new_value': 2942}]
2025-05-22 15:00:38,195 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-22 15:00:38,725 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMAA
2025-05-22 15:00:38,726 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24706.0, 'new_value': 25449.7}, {'field': 'offline_amount', 'old_value': 205245.4, 'new_value': 210311.6}, {'field': 'total_amount', 'old_value': 229951.4, 'new_value': 235761.3}, {'field': 'order_count', 'old_value': 1831, 'new_value': 1893}]
2025-05-22 15:00:38,726 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-22 15:00:39,202 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPL
2025-05-22 15:00:39,202 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 112497.08, 'new_value': 118424.08}, {'field': 'total_amount', 'old_value': 112497.08, 'new_value': 118424.08}, {'field': 'order_count', 'old_value': 10148, 'new_value': 10696}]
2025-05-22 15:00:39,203 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-22 15:00:39,632 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMGA
2025-05-22 15:00:39,632 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 99540.1, 'new_value': 100766.1}, {'field': 'total_amount', 'old_value': 99540.1, 'new_value': 100766.1}, {'field': 'order_count', 'old_value': 1009, 'new_value': 1019}]
2025-05-22 15:00:39,632 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM6Q
2025-05-22 15:00:40,061 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM6Q
2025-05-22 15:00:40,061 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 47343.97, 'new_value': 48414.29}, {'field': 'offline_amount', 'old_value': 200879.63, 'new_value': 211350.73}, {'field': 'total_amount', 'old_value': 248223.6, 'new_value': 259765.02}, {'field': 'order_count', 'old_value': 7697, 'new_value': 8089}]
2025-05-22 15:00:40,062 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMBA
2025-05-22 15:00:40,598 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMBA
2025-05-22 15:00:40,598 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35799.3, 'new_value': 37754.3}, {'field': 'offline_amount', 'old_value': 24518.64, 'new_value': 25763.64}, {'field': 'total_amount', 'old_value': 60317.94, 'new_value': 63517.94}, {'field': 'order_count', 'old_value': 8039, 'new_value': 8482}]
2025-05-22 15:00:40,598 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM9
2025-05-22 15:00:41,105 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM9
2025-05-22 15:00:41,105 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 88018.57, 'new_value': 91144.53}, {'field': 'offline_amount', 'old_value': 262136.05, 'new_value': 270147.64}, {'field': 'total_amount', 'old_value': 350154.62, 'new_value': 361292.17}, {'field': 'order_count', 'old_value': 4256, 'new_value': 4472}]
2025-05-22 15:00:41,106 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMCA
2025-05-22 15:00:41,622 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMCA
2025-05-22 15:00:41,622 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38732.5, 'new_value': 40621.5}, {'field': 'total_amount', 'old_value': 38732.5, 'new_value': 40621.5}, {'field': 'order_count', 'old_value': 1956, 'new_value': 2055}]
2025-05-22 15:00:41,622 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMDA
2025-05-22 15:00:42,145 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMDA
2025-05-22 15:00:42,146 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28346.72, 'new_value': 31802.17}, {'field': 'offline_amount', 'old_value': 201186.2, 'new_value': 211027.03}, {'field': 'total_amount', 'old_value': 229532.92, 'new_value': 242829.2}, {'field': 'order_count', 'old_value': 3704, 'new_value': 4049}]
2025-05-22 15:00:42,146 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMA
2025-05-22 15:00:42,613 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMA
2025-05-22 15:00:42,613 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34164.0, 'new_value': 37712.0}, {'field': 'total_amount', 'old_value': 34164.0, 'new_value': 37712.0}, {'field': 'order_count', 'old_value': 188, 'new_value': 196}]
2025-05-22 15:00:42,613 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMM
2025-05-22 15:00:43,066 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMMM
2025-05-22 15:00:43,066 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30877.0, 'new_value': 32835.0}, {'field': 'offline_amount', 'old_value': 233197.0, 'new_value': 247587.0}, {'field': 'total_amount', 'old_value': 264074.0, 'new_value': 280422.0}, {'field': 'order_count', 'old_value': 239, 'new_value': 254}]
2025-05-22 15:00:43,066 - INFO - 开始更新记录 - 表单实例ID: FINST-8LC66GC10PZUU9MTETB405L5IF663D3CEZ6AMYB
2025-05-22 15:00:43,491 - INFO - 更新表单数据成功: FINST-8LC66GC10PZUU9MTETB405L5IF663D3CEZ6AMYB
2025-05-22 15:00:43,491 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 250162.46, 'new_value': 261918.26}, {'field': 'total_amount', 'old_value': 283242.67, 'new_value': 294998.47}, {'field': 'order_count', 'old_value': 11914, 'new_value': 12434}]
2025-05-22 15:00:43,491 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM7Q
2025-05-22 15:00:43,937 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM7Q
2025-05-22 15:00:43,937 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 88150.66, 'new_value': 92190.99}, {'field': 'total_amount', 'old_value': 88150.66, 'new_value': 92190.99}, {'field': 'order_count', 'old_value': 2579, 'new_value': 2698}]
2025-05-22 15:00:43,937 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM8Q
2025-05-22 15:00:44,421 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM8Q
2025-05-22 15:00:44,422 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 160065.59, 'new_value': 170596.59}, {'field': 'total_amount', 'old_value': 160065.59, 'new_value': 170596.59}, {'field': 'order_count', 'old_value': 6765, 'new_value': 7239}]
2025-05-22 15:00:44,422 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMFA
2025-05-22 15:00:44,864 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMFA
2025-05-22 15:00:44,864 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 35038.45, 'new_value': 39474.44}, {'field': 'offline_amount', 'old_value': 109095.38, 'new_value': 111640.65}, {'field': 'total_amount', 'old_value': 144133.83, 'new_value': 151115.09}, {'field': 'order_count', 'old_value': 8039, 'new_value': 8414}]
2025-05-22 15:00:44,865 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM9Q
2025-05-22 15:00:45,293 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AM9Q
2025-05-22 15:00:45,293 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 27210.71, 'new_value': 27770.68}, {'field': 'offline_amount', 'old_value': 53026.96, 'new_value': 54512.66}, {'field': 'total_amount', 'old_value': 80237.67, 'new_value': 82283.34}, {'field': 'order_count', 'old_value': 677, 'new_value': 702}]
2025-05-22 15:00:45,294 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMB
2025-05-22 15:00:45,732 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMB
2025-05-22 15:00:45,732 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 64990.0, 'new_value': 68200.0}, {'field': 'total_amount', 'old_value': 64990.0, 'new_value': 68200.0}, {'field': 'order_count', 'old_value': 457, 'new_value': 477}]
2025-05-22 15:00:45,732 - INFO - 开始更新记录 - 表单实例ID: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMP3
2025-05-22 15:00:46,191 - INFO - 更新表单数据成功: FINST-2PF66CD17T6VUPX0E95LY9OI39Y82AQVT5CAMP3
2025-05-22 15:00:46,191 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6579.81, 'new_value': 6964.82}, {'field': 'offline_amount', 'old_value': 102022.3, 'new_value': 105943.1}, {'field': 'total_amount', 'old_value': 108602.11, 'new_value': 112907.92}, {'field': 'order_count', 'old_value': 5822, 'new_value': 6012}]
2025-05-22 15:00:46,192 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMC
2025-05-22 15:00:46,635 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAMC
2025-05-22 15:00:46,636 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16061.5, 'new_value': 16435.38}, {'field': 'offline_amount', 'old_value': 109024.71, 'new_value': 111682.61}, {'field': 'total_amount', 'old_value': 125086.21, 'new_value': 128117.99}, {'field': 'order_count', 'old_value': 3720, 'new_value': 3804}]
2025-05-22 15:00:46,636 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMEA
2025-05-22 15:00:47,056 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMEA
2025-05-22 15:00:47,057 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83241.3, 'new_value': 86695.1}, {'field': 'total_amount', 'old_value': 83241.3, 'new_value': 86695.1}, {'field': 'order_count', 'old_value': 4101, 'new_value': 4288}]
2025-05-22 15:00:47,057 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-22 15:00:47,579 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMRD
2025-05-22 15:00:47,580 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100641.07, 'new_value': 109100.23}, {'field': 'total_amount', 'old_value': 176713.51, 'new_value': 185172.67}, {'field': 'order_count', 'old_value': 7562, 'new_value': 7984}]
2025-05-22 15:00:47,580 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-22 15:00:48,080 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMIA
2025-05-22 15:00:48,080 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 744284.0, 'new_value': 780852.0}, {'field': 'total_amount', 'old_value': 744284.0, 'new_value': 780852.0}, {'field': 'order_count', 'old_value': 1600, 'new_value': 1669}]
2025-05-22 15:00:48,081 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMHA
2025-05-22 15:00:48,543 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AMHA
2025-05-22 15:00:48,544 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10409.0, 'new_value': 10909.0}, {'field': 'total_amount', 'old_value': 10409.0, 'new_value': 10909.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-05-22 15:00:48,544 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMCE
2025-05-22 15:00:49,134 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMCE
2025-05-22 15:00:49,134 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 156033.21, 'new_value': 161564.16}, {'field': 'total_amount', 'old_value': 156033.21, 'new_value': 161564.16}, {'field': 'order_count', 'old_value': 1993, 'new_value': 2065}]
2025-05-22 15:00:49,134 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-22 15:00:49,548 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMFE
2025-05-22 15:00:49,548 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 536.0}, {'field': 'offline_amount', 'old_value': 234342.46, 'new_value': 236868.46}, {'field': 'total_amount', 'old_value': 234342.46, 'new_value': 237404.46}, {'field': 'order_count', 'old_value': 41, 'new_value': 43}]
2025-05-22 15:00:49,548 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMAQ
2025-05-22 15:00:50,002 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMAQ
2025-05-22 15:00:50,002 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 77755.0, 'new_value': 83873.0}, {'field': 'total_amount', 'old_value': 77755.0, 'new_value': 83873.0}, {'field': 'order_count', 'old_value': 1880, 'new_value': 2045}]
2025-05-22 15:00:50,003 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPE
2025-05-22 15:00:50,576 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMPE
2025-05-22 15:00:50,576 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11872.0, 'new_value': 11972.0}, {'field': 'offline_amount', 'old_value': 7708.0, 'new_value': 7908.0}, {'field': 'total_amount', 'old_value': 19580.0, 'new_value': 19880.0}, {'field': 'order_count', 'old_value': 70, 'new_value': 75}]
2025-05-22 15:00:50,577 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMBQ
2025-05-22 15:00:51,045 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMBQ
2025-05-22 15:00:51,045 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 106767.52, 'new_value': 115004.52}, {'field': 'total_amount', 'old_value': 106775.52, 'new_value': 115012.52}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-05-22 15:00:51,045 - INFO - 开始更新记录 - 表单实例ID: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-22 15:00:51,497 - INFO - 更新表单数据成功: FINST-VOC66Y91D21VB3QEBQ73I7RII1DN369X636AMHQ
2025-05-22 15:00:51,497 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 184836.0, 'new_value': 187066.0}, {'field': 'total_amount', 'old_value': 184836.0, 'new_value': 187066.0}, {'field': 'order_count', 'old_value': 406, 'new_value': 416}]
2025-05-22 15:00:51,497 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-22 15:00:51,931 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMUF
2025-05-22 15:00:51,931 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50590.03, 'new_value': 52864.18}, {'field': 'total_amount', 'old_value': 55950.0, 'new_value': 58224.15}, {'field': 'order_count', 'old_value': 864, 'new_value': 885}]
2025-05-22 15:00:51,931 - INFO - 开始更新记录 - 表单实例ID: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-22 15:00:52,372 - INFO - 更新表单数据成功: FINST-XL866HB1FAZUKON87NT5AD8JFOL324PZ636AMFV
2025-05-22 15:00:52,373 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 301306.0, 'new_value': 304306.0}, {'field': 'total_amount', 'old_value': 301306.0, 'new_value': 304306.0}]
2025-05-22 15:00:52,373 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-22 15:00:52,881 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMME
2025-05-22 15:00:52,881 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102172.0, 'new_value': 109784.0}, {'field': 'total_amount', 'old_value': 102252.0, 'new_value': 109864.0}, {'field': 'order_count', 'old_value': 9968, 'new_value': 10801}]
2025-05-22 15:00:52,881 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-22 15:00:53,335 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMHG
2025-05-22 15:00:53,336 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96078.0, 'new_value': 99487.0}, {'field': 'total_amount', 'old_value': 96078.0, 'new_value': 99487.0}, {'field': 'order_count', 'old_value': 478, 'new_value': 516}]
2025-05-22 15:00:53,336 - INFO - 日期 2025-05 处理完成 - 更新: 49 条，插入: 0 条，错误: 0 条
2025-05-22 15:00:53,337 - INFO - 数据同步完成！更新: 49 条，插入: 0 条，错误: 0 条
2025-05-22 15:00:53,338 - INFO - =================同步完成====================
2025-05-22 18:00:02,065 - INFO - =================使用默认全量同步=============
2025-05-22 18:00:03,584 - INFO - MySQL查询成功，共获取 3297 条记录
2025-05-22 18:00:03,585 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-22 18:00:03,614 - INFO - 开始处理日期: 2025-01
2025-05-22 18:00:03,617 - INFO - Request Parameters - Page 1:
2025-05-22 18:00:03,617 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 18:00:03,617 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 18:00:04,833 - INFO - Response - Page 1:
2025-05-22 18:00:05,033 - INFO - 第 1 页获取到 100 条记录
2025-05-22 18:00:05,033 - INFO - Request Parameters - Page 2:
2025-05-22 18:00:05,033 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 18:00:05,033 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 18:00:06,189 - INFO - Response - Page 2:
2025-05-22 18:00:06,390 - INFO - 第 2 页获取到 100 条记录
2025-05-22 18:00:06,390 - INFO - Request Parameters - Page 3:
2025-05-22 18:00:06,390 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 18:00:06,390 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 18:00:06,965 - INFO - Response - Page 3:
2025-05-22 18:00:07,166 - INFO - 第 3 页获取到 100 条记录
2025-05-22 18:00:07,166 - INFO - Request Parameters - Page 4:
2025-05-22 18:00:07,166 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 18:00:07,166 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 18:00:07,702 - INFO - Response - Page 4:
2025-05-22 18:00:07,902 - INFO - 第 4 页获取到 100 条记录
2025-05-22 18:00:07,902 - INFO - Request Parameters - Page 5:
2025-05-22 18:00:07,902 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 18:00:07,902 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 18:00:08,442 - INFO - Response - Page 5:
2025-05-22 18:00:08,642 - INFO - 第 5 页获取到 100 条记录
2025-05-22 18:00:08,642 - INFO - Request Parameters - Page 6:
2025-05-22 18:00:08,642 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 18:00:08,642 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 18:00:09,144 - INFO - Response - Page 6:
2025-05-22 18:00:09,344 - INFO - 第 6 页获取到 100 条记录
2025-05-22 18:00:09,344 - INFO - Request Parameters - Page 7:
2025-05-22 18:00:09,344 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 18:00:09,344 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 18:00:09,895 - INFO - Response - Page 7:
2025-05-22 18:00:10,095 - INFO - 第 7 页获取到 82 条记录
2025-05-22 18:00:10,095 - INFO - 查询完成，共获取到 682 条记录
2025-05-22 18:00:10,095 - INFO - 获取到 682 条表单数据
2025-05-22 18:00:10,107 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-22 18:00:10,119 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-22 18:00:10,119 - INFO - 开始处理日期: 2025-02
2025-05-22 18:00:10,120 - INFO - Request Parameters - Page 1:
2025-05-22 18:00:10,120 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 18:00:10,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 18:00:10,592 - INFO - Response - Page 1:
2025-05-22 18:00:10,792 - INFO - 第 1 页获取到 100 条记录
2025-05-22 18:00:10,792 - INFO - Request Parameters - Page 2:
2025-05-22 18:00:10,792 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 18:00:10,792 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 18:00:11,441 - INFO - Response - Page 2:
2025-05-22 18:00:11,641 - INFO - 第 2 页获取到 100 条记录
2025-05-22 18:00:11,641 - INFO - Request Parameters - Page 3:
2025-05-22 18:00:11,641 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 18:00:11,641 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 18:00:12,104 - INFO - Response - Page 3:
2025-05-22 18:00:12,305 - INFO - 第 3 页获取到 100 条记录
2025-05-22 18:00:12,305 - INFO - Request Parameters - Page 4:
2025-05-22 18:00:12,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 18:00:12,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 18:00:12,833 - INFO - Response - Page 4:
2025-05-22 18:00:13,034 - INFO - 第 4 页获取到 100 条记录
2025-05-22 18:00:13,034 - INFO - Request Parameters - Page 5:
2025-05-22 18:00:13,034 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 18:00:13,034 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 18:00:13,535 - INFO - Response - Page 5:
2025-05-22 18:00:13,735 - INFO - 第 5 页获取到 100 条记录
2025-05-22 18:00:13,735 - INFO - Request Parameters - Page 6:
2025-05-22 18:00:13,735 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 18:00:13,735 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 18:00:14,315 - INFO - Response - Page 6:
2025-05-22 18:00:14,515 - INFO - 第 6 页获取到 100 条记录
2025-05-22 18:00:14,515 - INFO - Request Parameters - Page 7:
2025-05-22 18:00:14,515 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 18:00:14,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 18:00:15,093 - INFO - Response - Page 7:
2025-05-22 18:00:15,293 - INFO - 第 7 页获取到 70 条记录
2025-05-22 18:00:15,293 - INFO - 查询完成，共获取到 670 条记录
2025-05-22 18:00:15,293 - INFO - 获取到 670 条表单数据
2025-05-22 18:00:15,306 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-22 18:00:15,318 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-22 18:00:15,318 - INFO - 开始处理日期: 2025-03
2025-05-22 18:00:15,318 - INFO - Request Parameters - Page 1:
2025-05-22 18:00:15,318 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 18:00:15,319 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 18:00:15,793 - INFO - Response - Page 1:
2025-05-22 18:00:15,994 - INFO - 第 1 页获取到 100 条记录
2025-05-22 18:00:15,994 - INFO - Request Parameters - Page 2:
2025-05-22 18:00:15,994 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 18:00:15,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 18:00:16,450 - INFO - Response - Page 2:
2025-05-22 18:00:16,650 - INFO - 第 2 页获取到 100 条记录
2025-05-22 18:00:16,650 - INFO - Request Parameters - Page 3:
2025-05-22 18:00:16,650 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 18:00:16,650 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 18:00:17,152 - INFO - Response - Page 3:
2025-05-22 18:00:17,353 - INFO - 第 3 页获取到 100 条记录
2025-05-22 18:00:17,353 - INFO - Request Parameters - Page 4:
2025-05-22 18:00:17,353 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 18:00:17,353 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 18:00:17,863 - INFO - Response - Page 4:
2025-05-22 18:00:18,063 - INFO - 第 4 页获取到 100 条记录
2025-05-22 18:00:18,063 - INFO - Request Parameters - Page 5:
2025-05-22 18:00:18,063 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 18:00:18,063 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 18:00:18,601 - INFO - Response - Page 5:
2025-05-22 18:00:18,802 - INFO - 第 5 页获取到 100 条记录
2025-05-22 18:00:18,802 - INFO - Request Parameters - Page 6:
2025-05-22 18:00:18,802 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 18:00:18,802 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 18:00:19,380 - INFO - Response - Page 6:
2025-05-22 18:00:19,580 - INFO - 第 6 页获取到 100 条记录
2025-05-22 18:00:19,580 - INFO - Request Parameters - Page 7:
2025-05-22 18:00:19,580 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 18:00:19,580 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 18:00:20,400 - INFO - Response - Page 7:
2025-05-22 18:00:20,601 - INFO - 第 7 页获取到 61 条记录
2025-05-22 18:00:20,601 - INFO - 查询完成，共获取到 661 条记录
2025-05-22 18:00:20,601 - INFO - 获取到 661 条表单数据
2025-05-22 18:00:20,615 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-22 18:00:20,628 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-22 18:00:20,629 - INFO - 开始处理日期: 2025-04
2025-05-22 18:00:20,629 - INFO - Request Parameters - Page 1:
2025-05-22 18:00:20,629 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 18:00:20,629 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 18:00:21,142 - INFO - Response - Page 1:
2025-05-22 18:00:21,343 - INFO - 第 1 页获取到 100 条记录
2025-05-22 18:00:21,343 - INFO - Request Parameters - Page 2:
2025-05-22 18:00:21,343 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 18:00:21,343 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 18:00:21,893 - INFO - Response - Page 2:
2025-05-22 18:00:22,093 - INFO - 第 2 页获取到 100 条记录
2025-05-22 18:00:22,093 - INFO - Request Parameters - Page 3:
2025-05-22 18:00:22,093 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 18:00:22,093 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 18:00:22,593 - INFO - Response - Page 3:
2025-05-22 18:00:22,793 - INFO - 第 3 页获取到 100 条记录
2025-05-22 18:00:22,793 - INFO - Request Parameters - Page 4:
2025-05-22 18:00:22,793 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 18:00:22,793 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 18:00:23,224 - INFO - Response - Page 4:
2025-05-22 18:00:23,424 - INFO - 第 4 页获取到 100 条记录
2025-05-22 18:00:23,424 - INFO - Request Parameters - Page 5:
2025-05-22 18:00:23,424 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 18:00:23,424 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 18:00:23,959 - INFO - Response - Page 5:
2025-05-22 18:00:24,160 - INFO - 第 5 页获取到 100 条记录
2025-05-22 18:00:24,160 - INFO - Request Parameters - Page 6:
2025-05-22 18:00:24,160 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 18:00:24,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 18:00:24,706 - INFO - Response - Page 6:
2025-05-22 18:00:24,907 - INFO - 第 6 页获取到 100 条记录
2025-05-22 18:00:24,907 - INFO - Request Parameters - Page 7:
2025-05-22 18:00:24,907 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 18:00:24,907 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 18:00:25,377 - INFO - Response - Page 7:
2025-05-22 18:00:25,578 - INFO - 第 7 页获取到 56 条记录
2025-05-22 18:00:25,578 - INFO - 查询完成，共获取到 656 条记录
2025-05-22 18:00:25,578 - INFO - 获取到 656 条表单数据
2025-05-22 18:00:25,590 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-22 18:00:25,602 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-22 18:00:25,602 - INFO - 开始处理日期: 2025-05
2025-05-22 18:00:25,602 - INFO - Request Parameters - Page 1:
2025-05-22 18:00:25,602 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 18:00:25,602 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 18:00:26,087 - INFO - Response - Page 1:
2025-05-22 18:00:26,287 - INFO - 第 1 页获取到 100 条记录
2025-05-22 18:00:26,287 - INFO - Request Parameters - Page 2:
2025-05-22 18:00:26,287 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 18:00:26,287 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 18:00:26,934 - INFO - Response - Page 2:
2025-05-22 18:00:27,134 - INFO - 第 2 页获取到 100 条记录
2025-05-22 18:00:27,134 - INFO - Request Parameters - Page 3:
2025-05-22 18:00:27,134 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 18:00:27,134 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 18:00:27,594 - INFO - Response - Page 3:
2025-05-22 18:00:27,794 - INFO - 第 3 页获取到 100 条记录
2025-05-22 18:00:27,794 - INFO - Request Parameters - Page 4:
2025-05-22 18:00:27,794 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 18:00:27,794 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 18:00:28,283 - INFO - Response - Page 4:
2025-05-22 18:00:28,483 - INFO - 第 4 页获取到 100 条记录
2025-05-22 18:00:28,483 - INFO - Request Parameters - Page 5:
2025-05-22 18:00:28,483 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 18:00:28,483 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 18:00:29,012 - INFO - Response - Page 5:
2025-05-22 18:00:29,212 - INFO - 第 5 页获取到 100 条记录
2025-05-22 18:00:29,212 - INFO - Request Parameters - Page 6:
2025-05-22 18:00:29,212 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 18:00:29,212 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 18:00:29,738 - INFO - Response - Page 6:
2025-05-22 18:00:29,938 - INFO - 第 6 页获取到 100 条记录
2025-05-22 18:00:29,938 - INFO - Request Parameters - Page 7:
2025-05-22 18:00:29,938 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 18:00:29,938 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 18:00:30,330 - INFO - Response - Page 7:
2025-05-22 18:00:30,532 - INFO - 第 7 页获取到 28 条记录
2025-05-22 18:00:30,532 - INFO - 查询完成，共获取到 628 条记录
2025-05-22 18:00:30,532 - INFO - 获取到 628 条表单数据
2025-05-22 18:00:30,543 - INFO - 当前日期 2025-05 有 628 条MySQL数据需要处理
2025-05-22 18:00:30,544 - INFO - 开始更新记录 - 表单实例ID: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-22 18:00:31,084 - INFO - 更新表单数据成功: FINST-N3G66S81AMZURG9X5FDO1AWE1Q7H3SEIAU9AM6A
2025-05-22 18:00:31,084 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46271.37, 'new_value': 48357.16}, {'field': 'total_amount', 'old_value': 126951.55, 'new_value': 129037.34}, {'field': 'order_count', 'old_value': 6971, 'new_value': 7258}]
2025-05-22 18:00:31,086 - INFO - 开始更新记录 - 表单实例ID: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM6H2
2025-05-22 18:00:31,566 - INFO - 更新表单数据成功: FINST-DOA66K91130VM2OB9L6E1DO55EH03N84Q9BAM6H2
2025-05-22 18:00:31,567 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10274.8, 'new_value': 10452.8}, {'field': 'total_amount', 'old_value': 10984.8, 'new_value': 11162.8}, {'field': 'order_count', 'old_value': 33, 'new_value': 34}]
2025-05-22 18:00:31,567 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-22 18:00:32,123 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMTK
2025-05-22 18:00:32,124 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94714.07, 'new_value': 96574.79}, {'field': 'total_amount', 'old_value': 94714.07, 'new_value': 96574.79}, {'field': 'order_count', 'old_value': 632, 'new_value': 655}]
2025-05-22 18:00:32,124 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-22 18:00:32,631 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM7L
2025-05-22 18:00:32,631 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1055000.0, 'new_value': 1058000.0}, {'field': 'total_amount', 'old_value': 1055000.0, 'new_value': 1058000.0}]
2025-05-22 18:00:32,631 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDL
2025-05-22 18:00:33,075 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMDL
2025-05-22 18:00:33,075 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75893.0, 'new_value': 80693.0}, {'field': 'total_amount', 'old_value': 75893.0, 'new_value': 80693.0}, {'field': 'order_count', 'old_value': 12, 'new_value': 13}]
2025-05-22 18:00:33,076 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-22 18:00:33,517 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMKL
2025-05-22 18:00:33,517 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 37130.0, 'new_value': 40430.0}, {'field': 'total_amount', 'old_value': 37130.0, 'new_value': 40430.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-05-22 18:00:33,518 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-22 18:00:33,982 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMZL
2025-05-22 18:00:33,983 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5327351.0, 'new_value': 5320167.0}, {'field': 'total_amount', 'old_value': 5327351.0, 'new_value': 5320167.0}, {'field': 'order_count', 'old_value': 86213, 'new_value': 90157}]
2025-05-22 18:00:33,983 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM8
2025-05-22 18:00:34,551 - INFO - 更新表单数据成功: FINST-PPA66671I57VPAVRD0MPUDK89GMN3A6A9CCAM8
2025-05-22 18:00:34,552 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 78489.74, 'new_value': 75489.74}, {'field': 'total_amount', 'old_value': 78489.74, 'new_value': 75489.74}, {'field': 'order_count', 'old_value': 2010, 'new_value': 2009}]
2025-05-22 18:00:34,552 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-22 18:00:35,096 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM2M
2025-05-22 18:00:35,096 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 97723.0, 'new_value': 98319.0}, {'field': 'total_amount', 'old_value': 97723.0, 'new_value': 98319.0}, {'field': 'order_count', 'old_value': 3483, 'new_value': 3723}]
2025-05-22 18:00:35,096 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-22 18:00:35,541 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AM4M
2025-05-22 18:00:35,541 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25494.07, 'new_value': 25752.07}, {'field': 'total_amount', 'old_value': 25494.07, 'new_value': 25752.07}, {'field': 'order_count', 'old_value': 2393, 'new_value': 2506}]
2025-05-22 18:00:35,542 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-22 18:00:35,947 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMFM
2025-05-22 18:00:35,948 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5127100.0, 'new_value': 5150300.0}, {'field': 'total_amount', 'old_value': 5127100.0, 'new_value': 5150300.0}]
2025-05-22 18:00:35,948 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-22 18:00:36,405 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMPM
2025-05-22 18:00:36,405 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 344125.3, 'new_value': 354045.01}, {'field': 'total_amount', 'old_value': 344125.3, 'new_value': 354045.01}, {'field': 'order_count', 'old_value': 654, 'new_value': 677}]
2025-05-22 18:00:36,406 - INFO - 开始更新记录 - 表单实例ID: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-22 18:00:36,858 - INFO - 更新表单数据成功: FINST-XBF66071JQ0VMRCOBCXS24WVWBT12PL7XJ5AMXM
2025-05-22 18:00:36,858 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27233.0, 'new_value': 27180.0}, {'field': 'total_amount', 'old_value': 28609.0, 'new_value': 28556.0}, {'field': 'order_count', 'old_value': 2853, 'new_value': 2964}]
2025-05-22 18:00:36,860 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4E
2025-05-22 18:00:37,311 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM4E
2025-05-22 18:00:37,312 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69834.0, 'new_value': 74654.0}, {'field': 'total_amount', 'old_value': 69834.0, 'new_value': 74654.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-05-22 18:00:37,312 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-22 18:00:37,797 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AM7E
2025-05-22 18:00:37,798 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 326119.1, 'new_value': 332304.74}, {'field': 'total_amount', 'old_value': 326119.1, 'new_value': 332304.74}, {'field': 'order_count', 'old_value': 2309, 'new_value': 2396}]
2025-05-22 18:00:37,798 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-22 18:00:38,200 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMBE
2025-05-22 18:00:38,200 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85650.0, 'new_value': 83974.0}, {'field': 'total_amount', 'old_value': 85650.0, 'new_value': 83974.0}, {'field': 'order_count', 'old_value': 556, 'new_value': 573}]
2025-05-22 18:00:38,200 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-22 18:00:38,696 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMDE
2025-05-22 18:00:38,696 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 20462.0, 'new_value': 21142.0}, {'field': 'total_amount', 'old_value': 20462.0, 'new_value': 21142.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-05-22 18:00:38,696 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-22 18:00:39,142 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMGE
2025-05-22 18:00:39,142 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 576700.0, 'new_value': 580300.0}, {'field': 'total_amount', 'old_value': 576700.0, 'new_value': 580300.0}, {'field': 'order_count', 'old_value': 49, 'new_value': 50}]
2025-05-22 18:00:39,142 - INFO - 开始更新记录 - 表单实例ID: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-22 18:00:39,592 - INFO - 更新表单数据成功: FINST-VME66K81SNZU76F3B5F6C42WFFR9313AXJ5AMIE
2025-05-22 18:00:39,592 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9038.67, 'new_value': 9501.67}, {'field': 'total_amount', 'old_value': 9038.67, 'new_value': 9501.67}, {'field': 'order_count', 'old_value': 267, 'new_value': 280}]
2025-05-22 18:00:39,592 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMAQ
2025-05-22 18:00:40,055 - INFO - 更新表单数据成功: FINST-7PF66BA1WNZULPSBAQ40F8HEVYIQ22ICI49AMAQ
2025-05-22 18:00:40,056 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83873.0, 'new_value': 90012.0}, {'field': 'total_amount', 'old_value': 83873.0, 'new_value': 90012.0}, {'field': 'order_count', 'old_value': 2045, 'new_value': 2203}]
2025-05-22 18:00:40,056 - INFO - 开始更新记录 - 表单实例ID: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-22 18:00:40,524 - INFO - 更新表单数据成功: FINST-3ME66E81UNZUKMZ97MM9M53GG8W63E2R3P7AMKE
2025-05-22 18:00:40,524 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 251417.5, 'new_value': 270986.5}, {'field': 'total_amount', 'old_value': 251417.5, 'new_value': 270986.5}, {'field': 'order_count', 'old_value': 45, 'new_value': 46}]
2025-05-22 18:00:40,528 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-22 18:00:40,998 - INFO - 更新表单数据成功: FINST-00D66K71T40VV1JX9R5EI9GI1A6S2N0IM96AMPG
2025-05-22 18:00:40,998 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 133095.16, 'new_value': 134703.68}, {'field': 'total_amount', 'old_value': 133095.16, 'new_value': 134703.68}, {'field': 'order_count', 'old_value': 1105, 'new_value': 1141}]
2025-05-22 18:00:40,999 - INFO - 日期 2025-05 处理完成 - 更新: 22 条，插入: 0 条，错误: 0 条
2025-05-22 18:00:40,999 - INFO - 数据同步完成！更新: 22 条，插入: 0 条，错误: 0 条
2025-05-22 18:00:41,000 - INFO - =================同步完成====================
2025-05-22 21:00:01,632 - INFO - =================使用默认全量同步=============
2025-05-22 21:00:03,071 - INFO - MySQL查询成功，共获取 3297 条记录
2025-05-22 21:00:03,071 - INFO - 获取到 5 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05']
2025-05-22 21:00:03,101 - INFO - 开始处理日期: 2025-01
2025-05-22 21:00:03,101 - INFO - Request Parameters - Page 1:
2025-05-22 21:00:03,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 21:00:03,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 21:00:03,898 - INFO - Response - Page 1:
2025-05-22 21:00:04,101 - INFO - 第 1 页获取到 100 条记录
2025-05-22 21:00:04,101 - INFO - Request Parameters - Page 2:
2025-05-22 21:00:04,101 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 21:00:04,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 21:00:05,086 - INFO - Response - Page 2:
2025-05-22 21:00:05,289 - INFO - 第 2 页获取到 100 条记录
2025-05-22 21:00:05,289 - INFO - Request Parameters - Page 3:
2025-05-22 21:00:05,289 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 21:00:05,289 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 21:00:05,867 - INFO - Response - Page 3:
2025-05-22 21:00:06,070 - INFO - 第 3 页获取到 100 条记录
2025-05-22 21:00:06,070 - INFO - Request Parameters - Page 4:
2025-05-22 21:00:06,070 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 21:00:06,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 21:00:06,555 - INFO - Response - Page 4:
2025-05-22 21:00:06,758 - INFO - 第 4 页获取到 100 条记录
2025-05-22 21:00:06,758 - INFO - Request Parameters - Page 5:
2025-05-22 21:00:06,758 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 21:00:06,758 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 21:00:07,258 - INFO - Response - Page 5:
2025-05-22 21:00:07,461 - INFO - 第 5 页获取到 100 条记录
2025-05-22 21:00:07,461 - INFO - Request Parameters - Page 6:
2025-05-22 21:00:07,461 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 21:00:07,461 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 21:00:08,102 - INFO - Response - Page 6:
2025-05-22 21:00:08,305 - INFO - 第 6 页获取到 100 条记录
2025-05-22 21:00:08,305 - INFO - Request Parameters - Page 7:
2025-05-22 21:00:08,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 21:00:08,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 21:00:08,836 - INFO - Response - Page 7:
2025-05-22 21:00:09,039 - INFO - 第 7 页获取到 82 条记录
2025-05-22 21:00:09,039 - INFO - 查询完成，共获取到 682 条记录
2025-05-22 21:00:09,039 - INFO - 获取到 682 条表单数据
2025-05-22 21:00:09,039 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-05-22 21:00:09,055 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-22 21:00:09,055 - INFO - 开始处理日期: 2025-02
2025-05-22 21:00:09,055 - INFO - Request Parameters - Page 1:
2025-05-22 21:00:09,055 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 21:00:09,055 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 21:00:09,602 - INFO - Response - Page 1:
2025-05-22 21:00:09,805 - INFO - 第 1 页获取到 100 条记录
2025-05-22 21:00:09,805 - INFO - Request Parameters - Page 2:
2025-05-22 21:00:09,805 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 21:00:09,805 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 21:00:10,258 - INFO - Response - Page 2:
2025-05-22 21:00:10,462 - INFO - 第 2 页获取到 100 条记录
2025-05-22 21:00:10,462 - INFO - Request Parameters - Page 3:
2025-05-22 21:00:10,462 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 21:00:10,462 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 21:00:10,993 - INFO - Response - Page 3:
2025-05-22 21:00:11,196 - INFO - 第 3 页获取到 100 条记录
2025-05-22 21:00:11,196 - INFO - Request Parameters - Page 4:
2025-05-22 21:00:11,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 21:00:11,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 21:00:11,743 - INFO - Response - Page 4:
2025-05-22 21:00:11,946 - INFO - 第 4 页获取到 100 条记录
2025-05-22 21:00:11,946 - INFO - Request Parameters - Page 5:
2025-05-22 21:00:11,946 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 21:00:11,946 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 21:00:12,384 - INFO - Response - Page 5:
2025-05-22 21:00:12,587 - INFO - 第 5 页获取到 100 条记录
2025-05-22 21:00:12,587 - INFO - Request Parameters - Page 6:
2025-05-22 21:00:12,587 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 21:00:12,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 21:00:13,056 - INFO - Response - Page 6:
2025-05-22 21:00:13,259 - INFO - 第 6 页获取到 100 条记录
2025-05-22 21:00:13,259 - INFO - Request Parameters - Page 7:
2025-05-22 21:00:13,259 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 21:00:13,259 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 21:00:13,712 - INFO - Response - Page 7:
2025-05-22 21:00:13,915 - INFO - 第 7 页获取到 70 条记录
2025-05-22 21:00:13,915 - INFO - 查询完成，共获取到 670 条记录
2025-05-22 21:00:13,915 - INFO - 获取到 670 条表单数据
2025-05-22 21:00:13,915 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-05-22 21:00:13,931 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-22 21:00:13,931 - INFO - 开始处理日期: 2025-03
2025-05-22 21:00:13,931 - INFO - Request Parameters - Page 1:
2025-05-22 21:00:13,931 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 21:00:13,931 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 21:00:14,415 - INFO - Response - Page 1:
2025-05-22 21:00:14,619 - INFO - 第 1 页获取到 100 条记录
2025-05-22 21:00:14,619 - INFO - Request Parameters - Page 2:
2025-05-22 21:00:14,619 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 21:00:14,619 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 21:00:15,103 - INFO - Response - Page 2:
2025-05-22 21:00:15,306 - INFO - 第 2 页获取到 100 条记录
2025-05-22 21:00:15,306 - INFO - Request Parameters - Page 3:
2025-05-22 21:00:15,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 21:00:15,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 21:00:16,025 - INFO - Response - Page 3:
2025-05-22 21:00:16,228 - INFO - 第 3 页获取到 100 条记录
2025-05-22 21:00:16,228 - INFO - Request Parameters - Page 4:
2025-05-22 21:00:16,228 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 21:00:16,228 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 21:00:16,728 - INFO - Response - Page 4:
2025-05-22 21:00:16,931 - INFO - 第 4 页获取到 100 条记录
2025-05-22 21:00:16,931 - INFO - Request Parameters - Page 5:
2025-05-22 21:00:16,931 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 21:00:16,931 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 21:00:17,463 - INFO - Response - Page 5:
2025-05-22 21:00:17,666 - INFO - 第 5 页获取到 100 条记录
2025-05-22 21:00:17,666 - INFO - Request Parameters - Page 6:
2025-05-22 21:00:17,666 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 21:00:17,666 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 21:00:18,135 - INFO - Response - Page 6:
2025-05-22 21:00:18,338 - INFO - 第 6 页获取到 100 条记录
2025-05-22 21:00:18,338 - INFO - Request Parameters - Page 7:
2025-05-22 21:00:18,338 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 21:00:18,338 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 21:00:18,807 - INFO - Response - Page 7:
2025-05-22 21:00:19,010 - INFO - 第 7 页获取到 61 条记录
2025-05-22 21:00:19,010 - INFO - 查询完成，共获取到 661 条记录
2025-05-22 21:00:19,010 - INFO - 获取到 661 条表单数据
2025-05-22 21:00:19,010 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-05-22 21:00:19,026 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-22 21:00:19,026 - INFO - 开始处理日期: 2025-04
2025-05-22 21:00:19,026 - INFO - Request Parameters - Page 1:
2025-05-22 21:00:19,026 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 21:00:19,026 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 21:00:19,541 - INFO - Response - Page 1:
2025-05-22 21:00:19,744 - INFO - 第 1 页获取到 100 条记录
2025-05-22 21:00:19,744 - INFO - Request Parameters - Page 2:
2025-05-22 21:00:19,744 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 21:00:19,744 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 21:00:20,323 - INFO - Response - Page 2:
2025-05-22 21:00:20,526 - INFO - 第 2 页获取到 100 条记录
2025-05-22 21:00:20,526 - INFO - Request Parameters - Page 3:
2025-05-22 21:00:20,526 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 21:00:20,526 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 21:00:21,057 - INFO - Response - Page 3:
2025-05-22 21:00:21,260 - INFO - 第 3 页获取到 100 条记录
2025-05-22 21:00:21,260 - INFO - Request Parameters - Page 4:
2025-05-22 21:00:21,260 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 21:00:21,260 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 21:00:21,745 - INFO - Response - Page 4:
2025-05-22 21:00:21,948 - INFO - 第 4 页获取到 100 条记录
2025-05-22 21:00:21,948 - INFO - Request Parameters - Page 5:
2025-05-22 21:00:21,948 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 21:00:21,948 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 21:00:22,448 - INFO - Response - Page 5:
2025-05-22 21:00:22,651 - INFO - 第 5 页获取到 100 条记录
2025-05-22 21:00:22,651 - INFO - Request Parameters - Page 6:
2025-05-22 21:00:22,651 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 21:00:22,651 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 21:00:23,120 - INFO - Response - Page 6:
2025-05-22 21:00:23,323 - INFO - 第 6 页获取到 100 条记录
2025-05-22 21:00:23,323 - INFO - Request Parameters - Page 7:
2025-05-22 21:00:23,323 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 21:00:23,323 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 21:00:23,792 - INFO - Response - Page 7:
2025-05-22 21:00:23,995 - INFO - 第 7 页获取到 56 条记录
2025-05-22 21:00:23,995 - INFO - 查询完成，共获取到 656 条记录
2025-05-22 21:00:23,995 - INFO - 获取到 656 条表单数据
2025-05-22 21:00:23,995 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-05-22 21:00:24,011 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-05-22 21:00:24,011 - INFO - 开始处理日期: 2025-05
2025-05-22 21:00:24,011 - INFO - Request Parameters - Page 1:
2025-05-22 21:00:24,011 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 21:00:24,011 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 21:00:24,527 - INFO - Response - Page 1:
2025-05-22 21:00:24,745 - INFO - 第 1 页获取到 100 条记录
2025-05-22 21:00:24,745 - INFO - Request Parameters - Page 2:
2025-05-22 21:00:24,745 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 21:00:24,745 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 21:00:25,292 - INFO - Response - Page 2:
2025-05-22 21:00:25,495 - INFO - 第 2 页获取到 100 条记录
2025-05-22 21:00:25,495 - INFO - Request Parameters - Page 3:
2025-05-22 21:00:25,495 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 21:00:25,495 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 21:00:26,074 - INFO - Response - Page 3:
2025-05-22 21:00:26,277 - INFO - 第 3 页获取到 100 条记录
2025-05-22 21:00:26,277 - INFO - Request Parameters - Page 4:
2025-05-22 21:00:26,277 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 21:00:26,277 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 21:00:26,777 - INFO - Response - Page 4:
2025-05-22 21:00:26,980 - INFO - 第 4 页获取到 100 条记录
2025-05-22 21:00:26,980 - INFO - Request Parameters - Page 5:
2025-05-22 21:00:26,980 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 21:00:26,980 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 21:00:27,465 - INFO - Response - Page 5:
2025-05-22 21:00:27,668 - INFO - 第 5 页获取到 100 条记录
2025-05-22 21:00:27,668 - INFO - Request Parameters - Page 6:
2025-05-22 21:00:27,668 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 21:00:27,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 21:00:28,246 - INFO - Response - Page 6:
2025-05-22 21:00:28,449 - INFO - 第 6 页获取到 100 条记录
2025-05-22 21:00:28,449 - INFO - Request Parameters - Page 7:
2025-05-22 21:00:28,449 - INFO - Headers: {'x-acs-dingtalk-access-token': 'cac695fcfd88384da9ffb1676e326eb1'}
2025-05-22 21:00:28,449 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-05-22 21:00:28,918 - INFO - Response - Page 7:
2025-05-22 21:00:29,121 - INFO - 第 7 页获取到 28 条记录
2025-05-22 21:00:29,121 - INFO - 查询完成，共获取到 628 条记录
2025-05-22 21:00:29,121 - INFO - 获取到 628 条表单数据
2025-05-22 21:00:29,121 - INFO - 当前日期 2025-05 有 628 条MySQL数据需要处理
2025-05-22 21:00:29,121 - INFO - 开始更新记录 - 表单实例ID: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-22 21:00:29,606 - INFO - 更新表单数据成功: FINST-OIF66RB1621VVF0W5CJMO4Y70Y223G25XJ5AM3D
2025-05-22 21:00:29,606 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 68004.0, 'new_value': 72691.0}, {'field': 'offline_amount', 'old_value': 89624.0, 'new_value': 93514.0}, {'field': 'total_amount', 'old_value': 157628.0, 'new_value': 166205.0}, {'field': 'order_count', 'old_value': 3678, 'new_value': 3834}]
2025-05-22 21:00:29,621 - INFO - 日期 2025-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-05-22 21:00:29,621 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-05-22 21:00:29,621 - INFO - =================同步完成====================
