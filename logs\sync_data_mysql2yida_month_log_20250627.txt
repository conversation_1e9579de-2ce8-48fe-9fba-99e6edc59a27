2025-06-27 00:00:03,418 - INFO - =================使用默认全量同步=============
2025-06-27 00:00:05,243 - INFO - MySQL查询成功，共获取 3960 条记录
2025-06-27 00:00:05,244 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-27 00:00:05,283 - INFO - 开始处理日期: 2025-01
2025-06-27 00:00:05,286 - INFO - Request Parameters - Page 1:
2025-06-27 00:00:05,286 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:05,286 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:06,388 - INFO - Response - Page 1:
2025-06-27 00:00:06,591 - INFO - 第 1 页获取到 100 条记录
2025-06-27 00:00:06,591 - INFO - Request Parameters - Page 2:
2025-06-27 00:00:06,591 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:06,591 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:07,669 - INFO - Response - Page 2:
2025-06-27 00:00:07,872 - INFO - 第 2 页获取到 100 条记录
2025-06-27 00:00:07,872 - INFO - Request Parameters - Page 3:
2025-06-27 00:00:07,872 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:07,872 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:08,529 - INFO - Response - Page 3:
2025-06-27 00:00:08,732 - INFO - 第 3 页获取到 100 条记录
2025-06-27 00:00:08,732 - INFO - Request Parameters - Page 4:
2025-06-27 00:00:08,732 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:08,732 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:09,216 - INFO - Response - Page 4:
2025-06-27 00:00:09,419 - INFO - 第 4 页获取到 100 条记录
2025-06-27 00:00:09,419 - INFO - Request Parameters - Page 5:
2025-06-27 00:00:09,419 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:09,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:09,904 - INFO - Response - Page 5:
2025-06-27 00:00:10,107 - INFO - 第 5 页获取到 100 条记录
2025-06-27 00:00:10,107 - INFO - Request Parameters - Page 6:
2025-06-27 00:00:10,107 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:10,107 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:10,591 - INFO - Response - Page 6:
2025-06-27 00:00:10,794 - INFO - 第 6 页获取到 100 条记录
2025-06-27 00:00:10,794 - INFO - Request Parameters - Page 7:
2025-06-27 00:00:10,794 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:10,794 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:11,279 - INFO - Response - Page 7:
2025-06-27 00:00:11,482 - INFO - 第 7 页获取到 82 条记录
2025-06-27 00:00:11,482 - INFO - 查询完成，共获取到 682 条记录
2025-06-27 00:00:11,482 - INFO - 获取到 682 条表单数据
2025-06-27 00:00:11,482 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-27 00:00:11,498 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 00:00:11,498 - INFO - 开始处理日期: 2025-02
2025-06-27 00:00:11,498 - INFO - Request Parameters - Page 1:
2025-06-27 00:00:11,498 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:11,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:12,013 - INFO - Response - Page 1:
2025-06-27 00:00:12,216 - INFO - 第 1 页获取到 100 条记录
2025-06-27 00:00:12,216 - INFO - Request Parameters - Page 2:
2025-06-27 00:00:12,216 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:12,216 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:12,795 - INFO - Response - Page 2:
2025-06-27 00:00:12,998 - INFO - 第 2 页获取到 100 条记录
2025-06-27 00:00:12,998 - INFO - Request Parameters - Page 3:
2025-06-27 00:00:12,998 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:12,998 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:13,545 - INFO - Response - Page 3:
2025-06-27 00:00:13,748 - INFO - 第 3 页获取到 100 条记录
2025-06-27 00:00:13,748 - INFO - Request Parameters - Page 4:
2025-06-27 00:00:13,748 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:13,748 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:14,263 - INFO - Response - Page 4:
2025-06-27 00:00:14,467 - INFO - 第 4 页获取到 100 条记录
2025-06-27 00:00:14,467 - INFO - Request Parameters - Page 5:
2025-06-27 00:00:14,467 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:14,467 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:15,060 - INFO - Response - Page 5:
2025-06-27 00:00:15,264 - INFO - 第 5 页获取到 100 条记录
2025-06-27 00:00:15,264 - INFO - Request Parameters - Page 6:
2025-06-27 00:00:15,264 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:15,264 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:15,732 - INFO - Response - Page 6:
2025-06-27 00:00:15,935 - INFO - 第 6 页获取到 100 条记录
2025-06-27 00:00:15,935 - INFO - Request Parameters - Page 7:
2025-06-27 00:00:15,935 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:15,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:16,389 - INFO - Response - Page 7:
2025-06-27 00:00:16,592 - INFO - 第 7 页获取到 70 条记录
2025-06-27 00:00:16,592 - INFO - 查询完成，共获取到 670 条记录
2025-06-27 00:00:16,592 - INFO - 获取到 670 条表单数据
2025-06-27 00:00:16,607 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-27 00:00:16,623 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 00:00:16,623 - INFO - 开始处理日期: 2025-03
2025-06-27 00:00:16,623 - INFO - Request Parameters - Page 1:
2025-06-27 00:00:16,623 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:16,623 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:17,201 - INFO - Response - Page 1:
2025-06-27 00:00:17,404 - INFO - 第 1 页获取到 100 条记录
2025-06-27 00:00:17,404 - INFO - Request Parameters - Page 2:
2025-06-27 00:00:17,404 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:17,404 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:17,967 - INFO - Response - Page 2:
2025-06-27 00:00:18,170 - INFO - 第 2 页获取到 100 条记录
2025-06-27 00:00:18,170 - INFO - Request Parameters - Page 3:
2025-06-27 00:00:18,170 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:18,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:18,654 - INFO - Response - Page 3:
2025-06-27 00:00:18,858 - INFO - 第 3 页获取到 100 条记录
2025-06-27 00:00:18,858 - INFO - Request Parameters - Page 4:
2025-06-27 00:00:18,858 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:18,858 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:19,530 - INFO - Response - Page 4:
2025-06-27 00:00:19,733 - INFO - 第 4 页获取到 100 条记录
2025-06-27 00:00:19,733 - INFO - Request Parameters - Page 5:
2025-06-27 00:00:19,733 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:19,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:20,842 - INFO - Response - Page 5:
2025-06-27 00:00:21,045 - INFO - 第 5 页获取到 100 条记录
2025-06-27 00:00:21,045 - INFO - Request Parameters - Page 6:
2025-06-27 00:00:21,045 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:21,045 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:21,733 - INFO - Response - Page 6:
2025-06-27 00:00:21,936 - INFO - 第 6 页获取到 100 条记录
2025-06-27 00:00:21,936 - INFO - Request Parameters - Page 7:
2025-06-27 00:00:21,936 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:21,936 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:22,405 - INFO - Response - Page 7:
2025-06-27 00:00:22,608 - INFO - 第 7 页获取到 61 条记录
2025-06-27 00:00:22,608 - INFO - 查询完成，共获取到 661 条记录
2025-06-27 00:00:22,608 - INFO - 获取到 661 条表单数据
2025-06-27 00:00:22,608 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-27 00:00:22,624 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 00:00:22,624 - INFO - 开始处理日期: 2025-04
2025-06-27 00:00:22,624 - INFO - Request Parameters - Page 1:
2025-06-27 00:00:22,624 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:22,624 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:23,139 - INFO - Response - Page 1:
2025-06-27 00:00:23,342 - INFO - 第 1 页获取到 100 条记录
2025-06-27 00:00:23,342 - INFO - Request Parameters - Page 2:
2025-06-27 00:00:23,342 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:23,342 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:23,858 - INFO - Response - Page 2:
2025-06-27 00:00:24,061 - INFO - 第 2 页获取到 100 条记录
2025-06-27 00:00:24,061 - INFO - Request Parameters - Page 3:
2025-06-27 00:00:24,061 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:24,061 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:24,592 - INFO - Response - Page 3:
2025-06-27 00:00:24,796 - INFO - 第 3 页获取到 100 条记录
2025-06-27 00:00:24,796 - INFO - Request Parameters - Page 4:
2025-06-27 00:00:24,796 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:24,796 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:25,389 - INFO - Response - Page 4:
2025-06-27 00:00:25,593 - INFO - 第 4 页获取到 100 条记录
2025-06-27 00:00:25,593 - INFO - Request Parameters - Page 5:
2025-06-27 00:00:25,593 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:25,593 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:26,124 - INFO - Response - Page 5:
2025-06-27 00:00:26,327 - INFO - 第 5 页获取到 100 条记录
2025-06-27 00:00:26,327 - INFO - Request Parameters - Page 6:
2025-06-27 00:00:26,327 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:26,327 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:26,858 - INFO - Response - Page 6:
2025-06-27 00:00:27,061 - INFO - 第 6 页获取到 100 条记录
2025-06-27 00:00:27,061 - INFO - Request Parameters - Page 7:
2025-06-27 00:00:27,061 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:27,061 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:27,530 - INFO - Response - Page 7:
2025-06-27 00:00:27,733 - INFO - 第 7 页获取到 56 条记录
2025-06-27 00:00:27,733 - INFO - 查询完成，共获取到 656 条记录
2025-06-27 00:00:27,733 - INFO - 获取到 656 条表单数据
2025-06-27 00:00:27,733 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-27 00:00:27,749 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 00:00:27,749 - INFO - 开始处理日期: 2025-05
2025-06-27 00:00:27,749 - INFO - Request Parameters - Page 1:
2025-06-27 00:00:27,749 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:27,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:28,265 - INFO - Response - Page 1:
2025-06-27 00:00:28,468 - INFO - 第 1 页获取到 100 条记录
2025-06-27 00:00:28,468 - INFO - Request Parameters - Page 2:
2025-06-27 00:00:28,468 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:28,468 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:29,030 - INFO - Response - Page 2:
2025-06-27 00:00:29,233 - INFO - 第 2 页获取到 100 条记录
2025-06-27 00:00:29,233 - INFO - Request Parameters - Page 3:
2025-06-27 00:00:29,233 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:29,233 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:29,702 - INFO - Response - Page 3:
2025-06-27 00:00:29,905 - INFO - 第 3 页获取到 100 条记录
2025-06-27 00:00:29,905 - INFO - Request Parameters - Page 4:
2025-06-27 00:00:29,905 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:29,905 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:30,421 - INFO - Response - Page 4:
2025-06-27 00:00:30,624 - INFO - 第 4 页获取到 100 条记录
2025-06-27 00:00:30,624 - INFO - Request Parameters - Page 5:
2025-06-27 00:00:30,624 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:30,624 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:31,140 - INFO - Response - Page 5:
2025-06-27 00:00:31,343 - INFO - 第 5 页获取到 100 条记录
2025-06-27 00:00:31,343 - INFO - Request Parameters - Page 6:
2025-06-27 00:00:31,343 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:31,343 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:31,890 - INFO - Response - Page 6:
2025-06-27 00:00:32,093 - INFO - 第 6 页获取到 100 条记录
2025-06-27 00:00:32,093 - INFO - Request Parameters - Page 7:
2025-06-27 00:00:32,093 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:32,093 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:32,531 - INFO - Response - Page 7:
2025-06-27 00:00:32,734 - INFO - 第 7 页获取到 65 条记录
2025-06-27 00:00:32,734 - INFO - 查询完成，共获取到 665 条记录
2025-06-27 00:00:32,734 - INFO - 获取到 665 条表单数据
2025-06-27 00:00:32,734 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-27 00:00:32,749 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 00:00:32,749 - INFO - 开始处理日期: 2025-06
2025-06-27 00:00:32,749 - INFO - Request Parameters - Page 1:
2025-06-27 00:00:32,749 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:32,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:33,453 - INFO - Response - Page 1:
2025-06-27 00:00:33,656 - INFO - 第 1 页获取到 100 条记录
2025-06-27 00:00:33,656 - INFO - Request Parameters - Page 2:
2025-06-27 00:00:33,656 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:33,656 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:34,140 - INFO - Response - Page 2:
2025-06-27 00:00:34,343 - INFO - 第 2 页获取到 100 条记录
2025-06-27 00:00:34,343 - INFO - Request Parameters - Page 3:
2025-06-27 00:00:34,343 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:34,343 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:34,875 - INFO - Response - Page 3:
2025-06-27 00:00:35,078 - INFO - 第 3 页获取到 100 条记录
2025-06-27 00:00:35,078 - INFO - Request Parameters - Page 4:
2025-06-27 00:00:35,078 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:35,078 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:35,734 - INFO - Response - Page 4:
2025-06-27 00:00:35,937 - INFO - 第 4 页获取到 100 条记录
2025-06-27 00:00:35,937 - INFO - Request Parameters - Page 5:
2025-06-27 00:00:35,937 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:35,937 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:36,468 - INFO - Response - Page 5:
2025-06-27 00:00:36,672 - INFO - 第 5 页获取到 100 条记录
2025-06-27 00:00:36,672 - INFO - Request Parameters - Page 6:
2025-06-27 00:00:36,672 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:36,672 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:37,156 - INFO - Response - Page 6:
2025-06-27 00:00:37,359 - INFO - 第 6 页获取到 100 条记录
2025-06-27 00:00:37,359 - INFO - Request Parameters - Page 7:
2025-06-27 00:00:37,359 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 00:00:37,359 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 00:00:37,765 - INFO - Response - Page 7:
2025-06-27 00:00:37,969 - INFO - 第 7 页获取到 26 条记录
2025-06-27 00:00:37,969 - INFO - 查询完成，共获取到 626 条记录
2025-06-27 00:00:37,969 - INFO - 获取到 626 条表单数据
2025-06-27 00:00:37,969 - INFO - 当前日期 2025-06 有 626 条MySQL数据需要处理
2025-06-27 00:00:37,969 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGZ
2025-06-27 00:00:38,437 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGZ
2025-06-27 00:00:38,437 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 837439.0, 'new_value': 876996.0}, {'field': 'total_amount', 'old_value': 837439.0, 'new_value': 876996.0}, {'field': 'order_count', 'old_value': 172, 'new_value': 186}]
2025-06-27 00:00:38,437 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM001
2025-06-27 00:00:38,969 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM001
2025-06-27 00:00:38,969 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1435867.0, 'new_value': 1488073.9}, {'field': 'total_amount', 'old_value': 1435867.0, 'new_value': 1488073.9}, {'field': 'order_count', 'old_value': 15955, 'new_value': 16613}]
2025-06-27 00:00:38,969 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM401
2025-06-27 00:00:39,406 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM401
2025-06-27 00:00:39,406 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 64249.91, 'new_value': 66443.97}, {'field': 'offline_amount', 'old_value': 89264.56, 'new_value': 92254.44}, {'field': 'total_amount', 'old_value': 153514.47, 'new_value': 158698.41}, {'field': 'order_count', 'old_value': 5183, 'new_value': 5369}]
2025-06-27 00:00:39,406 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTB
2025-06-27 00:00:39,859 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTB
2025-06-27 00:00:39,859 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25007.0, 'new_value': 47585.0}, {'field': 'total_amount', 'old_value': 622798.0, 'new_value': 645376.0}, {'field': 'order_count', 'old_value': 192, 'new_value': 204}]
2025-06-27 00:00:39,859 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9C
2025-06-27 00:00:40,313 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9C
2025-06-27 00:00:40,313 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44833.91, 'new_value': 46635.13}, {'field': 'offline_amount', 'old_value': 675283.68, 'new_value': 712309.88}, {'field': 'total_amount', 'old_value': 720117.59, 'new_value': 758945.01}, {'field': 'order_count', 'old_value': 2907, 'new_value': 3032}]
2025-06-27 00:00:40,313 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLC
2025-06-27 00:00:40,734 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLC
2025-06-27 00:00:40,734 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 470627.0, 'new_value': 481432.0}, {'field': 'total_amount', 'old_value': 474543.0, 'new_value': 485348.0}, {'field': 'order_count', 'old_value': 74, 'new_value': 76}]
2025-06-27 00:00:40,734 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMPC
2025-06-27 00:00:41,109 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMPC
2025-06-27 00:00:41,109 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 192987.5, 'new_value': 211164.5}, {'field': 'total_amount', 'old_value': 192987.5, 'new_value': 211164.5}, {'field': 'order_count', 'old_value': 52, 'new_value': 54}]
2025-06-27 00:00:41,109 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWC
2025-06-27 00:00:41,563 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWC
2025-06-27 00:00:41,563 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31817.0, 'new_value': 32954.0}, {'field': 'total_amount', 'old_value': 32294.0, 'new_value': 33431.0}, {'field': 'order_count', 'old_value': 73, 'new_value': 77}]
2025-06-27 00:00:41,563 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMZC
2025-06-27 00:00:42,000 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMZC
2025-06-27 00:00:42,000 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 102265.0, 'new_value': 105465.0}, {'field': 'total_amount', 'old_value': 106763.05, 'new_value': 109963.05}, {'field': 'order_count', 'old_value': 5612, 'new_value': 5613}]
2025-06-27 00:00:42,000 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM2D
2025-06-27 00:00:42,422 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM2D
2025-06-27 00:00:42,422 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 153561.0, 'new_value': 188352.0}, {'field': 'total_amount', 'old_value': 214252.55, 'new_value': 249043.55}, {'field': 'order_count', 'old_value': 92, 'new_value': 97}]
2025-06-27 00:00:42,422 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5D
2025-06-27 00:00:42,828 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5D
2025-06-27 00:00:42,828 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62405.0, 'new_value': 64381.0}, {'field': 'total_amount', 'old_value': 62405.0, 'new_value': 64381.0}, {'field': 'order_count', 'old_value': 164, 'new_value': 170}]
2025-06-27 00:00:42,828 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCD
2025-06-27 00:00:43,203 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCD
2025-06-27 00:00:43,203 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57007.36, 'new_value': 60588.91}, {'field': 'offline_amount', 'old_value': 448670.87, 'new_value': 460918.53}, {'field': 'total_amount', 'old_value': 505678.23, 'new_value': 521507.44}, {'field': 'order_count', 'old_value': 4803, 'new_value': 5068}]
2025-06-27 00:00:43,203 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMED
2025-06-27 00:00:43,563 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMED
2025-06-27 00:00:43,563 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 247147.53, 'new_value': 259366.53}, {'field': 'total_amount', 'old_value': 247147.53, 'new_value': 259366.53}, {'field': 'order_count', 'old_value': 147, 'new_value': 154}]
2025-06-27 00:00:43,563 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLD
2025-06-27 00:00:44,016 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMLD
2025-06-27 00:00:44,016 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 56468.0, 'new_value': 58362.0}, {'field': 'total_amount', 'old_value': 56468.0, 'new_value': 58362.0}, {'field': 'order_count', 'old_value': 90, 'new_value': 93}]
2025-06-27 00:00:44,016 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXD
2025-06-27 00:00:44,485 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXD
2025-06-27 00:00:44,485 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 31813.0, 'new_value': 32913.0}, {'field': 'total_amount', 'old_value': 31813.0, 'new_value': 32913.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-06-27 00:00:44,485 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYD
2025-06-27 00:00:44,954 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYD
2025-06-27 00:00:44,954 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52674.25, 'new_value': 55051.58}, {'field': 'offline_amount', 'old_value': 311941.6, 'new_value': 320498.2}, {'field': 'total_amount', 'old_value': 364615.85, 'new_value': 375549.78}, {'field': 'order_count', 'old_value': 2854, 'new_value': 2969}]
2025-06-27 00:00:44,954 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5R
2025-06-27 00:00:45,532 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5R
2025-06-27 00:00:45,532 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 280778.9, 'new_value': 284815.1}, {'field': 'total_amount', 'old_value': 280778.9, 'new_value': 284815.1}, {'field': 'order_count', 'old_value': 2851, 'new_value': 2900}]
2025-06-27 00:00:45,532 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7R
2025-06-27 00:00:45,922 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7R
2025-06-27 00:00:45,922 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 60164.5, 'new_value': 62117.5}, {'field': 'total_amount', 'old_value': 66789.9, 'new_value': 68742.9}, {'field': 'order_count', 'old_value': 163, 'new_value': 168}]
2025-06-27 00:00:45,922 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGR
2025-06-27 00:00:46,329 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGR
2025-06-27 00:00:46,329 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 799332.0, 'new_value': 836772.0}, {'field': 'total_amount', 'old_value': 871924.0, 'new_value': 909364.0}, {'field': 'order_count', 'old_value': 934, 'new_value': 974}]
2025-06-27 00:00:46,329 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLR
2025-06-27 00:00:46,751 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLR
2025-06-27 00:00:46,751 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 101628.9, 'new_value': 104689.76}, {'field': 'total_amount', 'old_value': 101628.9, 'new_value': 104689.76}, {'field': 'order_count', 'old_value': 8862, 'new_value': 9188}]
2025-06-27 00:00:46,751 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPR
2025-06-27 00:00:47,188 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPR
2025-06-27 00:00:47,188 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 385387.46, 'new_value': 399540.63}, {'field': 'total_amount', 'old_value': 464596.55, 'new_value': 478749.72}, {'field': 'order_count', 'old_value': 1488, 'new_value': 1522}]
2025-06-27 00:00:47,188 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCS
2025-06-27 00:00:47,641 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCS
2025-06-27 00:00:47,641 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 376278.0, 'new_value': 430883.0}, {'field': 'total_amount', 'old_value': 386078.0, 'new_value': 440683.0}, {'field': 'order_count', 'old_value': 93, 'new_value': 98}]
2025-06-27 00:00:47,641 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDS
2025-06-27 00:00:48,048 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDS
2025-06-27 00:00:48,048 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25359.64, 'new_value': 26258.19}, {'field': 'offline_amount', 'old_value': 244311.19, 'new_value': 250436.37}, {'field': 'total_amount', 'old_value': 269670.83, 'new_value': 276694.56}, {'field': 'order_count', 'old_value': 1304, 'new_value': 1338}]
2025-06-27 00:00:48,048 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMES
2025-06-27 00:00:48,501 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMES
2025-06-27 00:00:48,501 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 144111.81, 'new_value': 147412.51}, {'field': 'total_amount', 'old_value': 144111.81, 'new_value': 147412.51}, {'field': 'order_count', 'old_value': 323, 'new_value': 331}]
2025-06-27 00:00:48,501 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGS
2025-06-27 00:00:48,876 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGS
2025-06-27 00:00:48,876 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 268248.33, 'new_value': 269598.44}, {'field': 'offline_amount', 'old_value': 74055.51, 'new_value': 83222.71}, {'field': 'total_amount', 'old_value': 342303.84, 'new_value': 352821.15}, {'field': 'order_count', 'old_value': 2149, 'new_value': 2231}]
2025-06-27 00:00:48,876 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHS
2025-06-27 00:00:49,423 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHS
2025-06-27 00:00:49,423 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 122890.0, 'new_value': 126096.0}, {'field': 'total_amount', 'old_value': 127457.0, 'new_value': 130663.0}, {'field': 'order_count', 'old_value': 4347, 'new_value': 4352}]
2025-06-27 00:00:49,423 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYS
2025-06-27 00:00:49,860 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYS
2025-06-27 00:00:49,860 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42491.0, 'new_value': 43858.0}, {'field': 'offline_amount', 'old_value': 134031.07, 'new_value': 149921.07}, {'field': 'total_amount', 'old_value': 176522.07, 'new_value': 193779.07}, {'field': 'order_count', 'old_value': 258, 'new_value': 274}]
2025-06-27 00:00:49,860 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7T
2025-06-27 00:00:50,329 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7T
2025-06-27 00:00:50,329 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 621730.0, 'new_value': 641728.0}, {'field': 'total_amount', 'old_value': 621730.0, 'new_value': 641728.0}, {'field': 'order_count', 'old_value': 61, 'new_value': 63}]
2025-06-27 00:00:50,329 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGT
2025-06-27 00:00:50,720 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMGT
2025-06-27 00:00:50,720 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 112214.82, 'new_value': 116595.76}, {'field': 'offline_amount', 'old_value': 220535.36, 'new_value': 225024.96}, {'field': 'total_amount', 'old_value': 332750.18, 'new_value': 341620.72}, {'field': 'order_count', 'old_value': 4664, 'new_value': 4706}]
2025-06-27 00:00:50,735 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCK
2025-06-27 00:00:51,188 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCK
2025-06-27 00:00:51,188 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 228761.0, 'new_value': 237944.0}, {'field': 'total_amount', 'old_value': 228761.0, 'new_value': 237944.0}, {'field': 'order_count', 'old_value': 233, 'new_value': 243}]
2025-06-27 00:00:51,188 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRK
2025-06-27 00:00:51,657 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRK
2025-06-27 00:00:51,657 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 99195.7, 'new_value': 101100.7}, {'field': 'offline_amount', 'old_value': 56802.9, 'new_value': 57653.7}, {'field': 'total_amount', 'old_value': 155998.6, 'new_value': 158754.4}, {'field': 'order_count', 'old_value': 1029, 'new_value': 1047}]
2025-06-27 00:00:51,657 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0L
2025-06-27 00:00:52,048 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0L
2025-06-27 00:00:52,048 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 23427.63, 'new_value': 24627.63}, {'field': 'total_amount', 'old_value': 23492.23, 'new_value': 24692.23}, {'field': 'order_count', 'old_value': 145, 'new_value': 155}]
2025-06-27 00:00:52,048 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8L
2025-06-27 00:00:52,423 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8L
2025-06-27 00:00:52,423 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 254195.39, 'new_value': 262538.08}, {'field': 'offline_amount', 'old_value': 676150.82, 'new_value': 693373.61}, {'field': 'total_amount', 'old_value': 930346.21, 'new_value': 955911.69}, {'field': 'order_count', 'old_value': 5859, 'new_value': 6023}]
2025-06-27 00:00:52,423 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIL
2025-06-27 00:00:52,876 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIL
2025-06-27 00:00:52,876 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 756707.46, 'new_value': 779682.44}, {'field': 'total_amount', 'old_value': 756707.46, 'new_value': 779682.44}, {'field': 'order_count', 'old_value': 5087, 'new_value': 5251}]
2025-06-27 00:00:52,876 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNL
2025-06-27 00:00:53,282 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNL
2025-06-27 00:00:53,282 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38402.67, 'new_value': 40015.84}, {'field': 'offline_amount', 'old_value': 41557.53, 'new_value': 42670.45}, {'field': 'total_amount', 'old_value': 79960.2, 'new_value': 82686.29}, {'field': 'order_count', 'old_value': 6859, 'new_value': 7084}]
2025-06-27 00:00:53,282 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSL
2025-06-27 00:00:53,814 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSL
2025-06-27 00:00:53,814 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 100814.3, 'new_value': 102856.6}, {'field': 'total_amount', 'old_value': 100814.3, 'new_value': 102856.6}, {'field': 'order_count', 'old_value': 213, 'new_value': 218}]
2025-06-27 00:00:53,814 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFM
2025-06-27 00:00:54,236 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFM
2025-06-27 00:00:54,236 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 277386.39, 'new_value': 282758.45}, {'field': 'offline_amount', 'old_value': 1359475.08, 'new_value': 1402155.14}, {'field': 'total_amount', 'old_value': 1636861.47, 'new_value': 1684913.59}, {'field': 'order_count', 'old_value': 7852, 'new_value': 8107}]
2025-06-27 00:00:54,236 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIM
2025-06-27 00:00:54,861 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIM
2025-06-27 00:00:54,861 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 639945.0, 'new_value': 647925.0}, {'field': 'total_amount', 'old_value': 639945.0, 'new_value': 647925.0}, {'field': 'order_count', 'old_value': 434, 'new_value': 440}]
2025-06-27 00:00:54,861 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLM
2025-06-27 00:00:55,298 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMLM
2025-06-27 00:00:55,298 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 545204.65, 'new_value': 566092.65}, {'field': 'total_amount', 'old_value': 545204.65, 'new_value': 566092.65}, {'field': 'order_count', 'old_value': 2102, 'new_value': 2202}]
2025-06-27 00:00:55,298 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWM
2025-06-27 00:00:55,814 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWM
2025-06-27 00:00:55,814 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13995.0, 'new_value': 13996.0}, {'field': 'offline_amount', 'old_value': 270263.17, 'new_value': 271162.17}, {'field': 'total_amount', 'old_value': 284258.17, 'new_value': 285158.17}, {'field': 'order_count', 'old_value': 70, 'new_value': 71}]
2025-06-27 00:00:55,814 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP01
2025-06-27 00:00:56,361 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP01
2025-06-27 00:00:56,361 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 128838.1, 'new_value': 133231.8}, {'field': 'offline_amount', 'old_value': 96952.8, 'new_value': 99691.4}, {'field': 'total_amount', 'old_value': 225790.9, 'new_value': 232923.2}, {'field': 'order_count', 'old_value': 5467, 'new_value': 5645}]
2025-06-27 00:00:56,361 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP11
2025-06-27 00:00:56,829 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP11
2025-06-27 00:00:56,829 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 227919.0, 'new_value': 228028.0}, {'field': 'total_amount', 'old_value': 240506.0, 'new_value': 240615.0}, {'field': 'order_count', 'old_value': 106, 'new_value': 107}]
2025-06-27 00:00:56,829 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM731
2025-06-27 00:00:57,236 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM731
2025-06-27 00:00:57,236 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 82438.0, 'new_value': 87109.0}, {'field': 'offline_amount', 'old_value': 165409.0, 'new_value': 171284.0}, {'field': 'total_amount', 'old_value': 247847.0, 'new_value': 258393.0}, {'field': 'order_count', 'old_value': 4940, 'new_value': 5143}]
2025-06-27 00:00:57,236 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM82
2025-06-27 00:00:57,642 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM82
2025-06-27 00:00:57,642 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 44793.8, 'new_value': 45811.8}, {'field': 'offline_amount', 'old_value': 342130.2, 'new_value': 364324.2}, {'field': 'total_amount', 'old_value': 386924.0, 'new_value': 410136.0}, {'field': 'order_count', 'old_value': 125, 'new_value': 133}]
2025-06-27 00:00:57,658 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66271XO5WRCRF89PMI7Q6G2YE3F4HBOSBM0D
2025-06-27 00:00:58,048 - INFO - 更新表单数据成功: FINST-3PF66271XO5WRCRF89PMI7Q6G2YE3F4HBOSBM0D
2025-06-27 00:00:58,048 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54779.6, 'new_value': 62074.3}, {'field': 'total_amount', 'old_value': 54779.6, 'new_value': 62074.3}, {'field': 'order_count', 'old_value': 651, 'new_value': 683}]
2025-06-27 00:00:58,048 - INFO - 开始更新记录 - 表单实例ID: FINST-QVA66B817T9WNS6B7DFZMBGX9WJ93LEPBFWBM6C
2025-06-27 00:00:58,455 - INFO - 更新表单数据成功: FINST-QVA66B817T9WNS6B7DFZMBGX9WJ93LEPBFWBM6C
2025-06-27 00:00:58,455 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54876.32, 'new_value': 59263.32}, {'field': 'total_amount', 'old_value': 54876.32, 'new_value': 59263.32}, {'field': 'order_count', 'old_value': 324, 'new_value': 352}]
2025-06-27 00:00:58,455 - INFO - 日期 2025-06 处理完成 - 更新: 46 条，插入: 0 条，错误: 0 条
2025-06-27 00:00:58,455 - INFO - 数据同步完成！更新: 46 条，插入: 0 条，错误: 0 条
2025-06-27 00:00:58,455 - INFO - =================同步完成====================
2025-06-27 03:00:03,248 - INFO - =================使用默认全量同步=============
2025-06-27 03:00:04,982 - INFO - MySQL查询成功，共获取 3960 条记录
2025-06-27 03:00:04,982 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-27 03:00:05,013 - INFO - 开始处理日期: 2025-01
2025-06-27 03:00:05,013 - INFO - Request Parameters - Page 1:
2025-06-27 03:00:05,013 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:05,013 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:06,513 - INFO - Response - Page 1:
2025-06-27 03:00:06,717 - INFO - 第 1 页获取到 100 条记录
2025-06-27 03:00:06,717 - INFO - Request Parameters - Page 2:
2025-06-27 03:00:06,717 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:06,717 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:07,201 - INFO - Response - Page 2:
2025-06-27 03:00:07,404 - INFO - 第 2 页获取到 100 条记录
2025-06-27 03:00:07,404 - INFO - Request Parameters - Page 3:
2025-06-27 03:00:07,404 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:07,404 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:07,935 - INFO - Response - Page 3:
2025-06-27 03:00:08,139 - INFO - 第 3 页获取到 100 条记录
2025-06-27 03:00:08,139 - INFO - Request Parameters - Page 4:
2025-06-27 03:00:08,139 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:08,139 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:08,732 - INFO - Response - Page 4:
2025-06-27 03:00:08,935 - INFO - 第 4 页获取到 100 条记录
2025-06-27 03:00:08,935 - INFO - Request Parameters - Page 5:
2025-06-27 03:00:08,935 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:08,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:09,451 - INFO - Response - Page 5:
2025-06-27 03:00:09,654 - INFO - 第 5 页获取到 100 条记录
2025-06-27 03:00:09,654 - INFO - Request Parameters - Page 6:
2025-06-27 03:00:09,654 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:09,654 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:10,107 - INFO - Response - Page 6:
2025-06-27 03:00:10,311 - INFO - 第 6 页获取到 100 条记录
2025-06-27 03:00:10,311 - INFO - Request Parameters - Page 7:
2025-06-27 03:00:10,311 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:10,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:10,779 - INFO - Response - Page 7:
2025-06-27 03:00:10,983 - INFO - 第 7 页获取到 82 条记录
2025-06-27 03:00:10,983 - INFO - 查询完成，共获取到 682 条记录
2025-06-27 03:00:10,983 - INFO - 获取到 682 条表单数据
2025-06-27 03:00:10,983 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-27 03:00:10,998 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 03:00:10,998 - INFO - 开始处理日期: 2025-02
2025-06-27 03:00:10,998 - INFO - Request Parameters - Page 1:
2025-06-27 03:00:10,998 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:10,998 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:11,529 - INFO - Response - Page 1:
2025-06-27 03:00:11,733 - INFO - 第 1 页获取到 100 条记录
2025-06-27 03:00:11,733 - INFO - Request Parameters - Page 2:
2025-06-27 03:00:11,733 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:11,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:12,264 - INFO - Response - Page 2:
2025-06-27 03:00:12,467 - INFO - 第 2 页获取到 100 条记录
2025-06-27 03:00:12,467 - INFO - Request Parameters - Page 3:
2025-06-27 03:00:12,467 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:12,467 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:13,061 - INFO - Response - Page 3:
2025-06-27 03:00:13,264 - INFO - 第 3 页获取到 100 条记录
2025-06-27 03:00:13,264 - INFO - Request Parameters - Page 4:
2025-06-27 03:00:13,264 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:13,264 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:13,764 - INFO - Response - Page 4:
2025-06-27 03:00:13,967 - INFO - 第 4 页获取到 100 条记录
2025-06-27 03:00:13,967 - INFO - Request Parameters - Page 5:
2025-06-27 03:00:13,967 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:13,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:14,452 - INFO - Response - Page 5:
2025-06-27 03:00:14,655 - INFO - 第 5 页获取到 100 条记录
2025-06-27 03:00:14,655 - INFO - Request Parameters - Page 6:
2025-06-27 03:00:14,655 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:14,655 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:15,280 - INFO - Response - Page 6:
2025-06-27 03:00:15,483 - INFO - 第 6 页获取到 100 条记录
2025-06-27 03:00:15,483 - INFO - Request Parameters - Page 7:
2025-06-27 03:00:15,483 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:15,483 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:15,983 - INFO - Response - Page 7:
2025-06-27 03:00:16,186 - INFO - 第 7 页获取到 70 条记录
2025-06-27 03:00:16,186 - INFO - 查询完成，共获取到 670 条记录
2025-06-27 03:00:16,186 - INFO - 获取到 670 条表单数据
2025-06-27 03:00:16,186 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-27 03:00:16,202 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 03:00:16,202 - INFO - 开始处理日期: 2025-03
2025-06-27 03:00:16,202 - INFO - Request Parameters - Page 1:
2025-06-27 03:00:16,202 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:16,202 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:16,811 - INFO - Response - Page 1:
2025-06-27 03:00:17,014 - INFO - 第 1 页获取到 100 条记录
2025-06-27 03:00:17,014 - INFO - Request Parameters - Page 2:
2025-06-27 03:00:17,014 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:17,014 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:17,514 - INFO - Response - Page 2:
2025-06-27 03:00:17,717 - INFO - 第 2 页获取到 100 条记录
2025-06-27 03:00:17,717 - INFO - Request Parameters - Page 3:
2025-06-27 03:00:17,717 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:17,717 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:18,186 - INFO - Response - Page 3:
2025-06-27 03:00:18,389 - INFO - 第 3 页获取到 100 条记录
2025-06-27 03:00:18,389 - INFO - Request Parameters - Page 4:
2025-06-27 03:00:18,389 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:18,389 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:18,936 - INFO - Response - Page 4:
2025-06-27 03:00:19,139 - INFO - 第 4 页获取到 100 条记录
2025-06-27 03:00:19,139 - INFO - Request Parameters - Page 5:
2025-06-27 03:00:19,139 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:19,139 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:19,655 - INFO - Response - Page 5:
2025-06-27 03:00:19,858 - INFO - 第 5 页获取到 100 条记录
2025-06-27 03:00:19,858 - INFO - Request Parameters - Page 6:
2025-06-27 03:00:19,858 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:19,858 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:20,421 - INFO - Response - Page 6:
2025-06-27 03:00:20,624 - INFO - 第 6 页获取到 100 条记录
2025-06-27 03:00:20,624 - INFO - Request Parameters - Page 7:
2025-06-27 03:00:20,624 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:20,624 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:21,093 - INFO - Response - Page 7:
2025-06-27 03:00:21,296 - INFO - 第 7 页获取到 61 条记录
2025-06-27 03:00:21,296 - INFO - 查询完成，共获取到 661 条记录
2025-06-27 03:00:21,296 - INFO - 获取到 661 条表单数据
2025-06-27 03:00:21,296 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-27 03:00:21,311 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 03:00:21,311 - INFO - 开始处理日期: 2025-04
2025-06-27 03:00:21,311 - INFO - Request Parameters - Page 1:
2025-06-27 03:00:21,311 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:21,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:21,827 - INFO - Response - Page 1:
2025-06-27 03:00:22,030 - INFO - 第 1 页获取到 100 条记录
2025-06-27 03:00:22,030 - INFO - Request Parameters - Page 2:
2025-06-27 03:00:22,030 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:22,030 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:22,499 - INFO - Response - Page 2:
2025-06-27 03:00:22,702 - INFO - 第 2 页获取到 100 条记录
2025-06-27 03:00:22,702 - INFO - Request Parameters - Page 3:
2025-06-27 03:00:22,702 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:22,702 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:23,234 - INFO - Response - Page 3:
2025-06-27 03:00:23,437 - INFO - 第 3 页获取到 100 条记录
2025-06-27 03:00:23,437 - INFO - Request Parameters - Page 4:
2025-06-27 03:00:23,437 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:23,437 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:24,046 - INFO - Response - Page 4:
2025-06-27 03:00:24,249 - INFO - 第 4 页获取到 100 条记录
2025-06-27 03:00:24,249 - INFO - Request Parameters - Page 5:
2025-06-27 03:00:24,249 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:24,249 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:24,749 - INFO - Response - Page 5:
2025-06-27 03:00:24,952 - INFO - 第 5 页获取到 100 条记录
2025-06-27 03:00:24,952 - INFO - Request Parameters - Page 6:
2025-06-27 03:00:24,952 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:24,952 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:25,531 - INFO - Response - Page 6:
2025-06-27 03:00:25,734 - INFO - 第 6 页获取到 100 条记录
2025-06-27 03:00:25,734 - INFO - Request Parameters - Page 7:
2025-06-27 03:00:25,734 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:25,734 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:26,187 - INFO - Response - Page 7:
2025-06-27 03:00:26,390 - INFO - 第 7 页获取到 56 条记录
2025-06-27 03:00:26,390 - INFO - 查询完成，共获取到 656 条记录
2025-06-27 03:00:26,390 - INFO - 获取到 656 条表单数据
2025-06-27 03:00:26,390 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-27 03:00:26,406 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 03:00:26,406 - INFO - 开始处理日期: 2025-05
2025-06-27 03:00:26,406 - INFO - Request Parameters - Page 1:
2025-06-27 03:00:26,406 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:26,406 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:26,968 - INFO - Response - Page 1:
2025-06-27 03:00:27,171 - INFO - 第 1 页获取到 100 条记录
2025-06-27 03:00:27,171 - INFO - Request Parameters - Page 2:
2025-06-27 03:00:27,171 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:27,171 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:27,718 - INFO - Response - Page 2:
2025-06-27 03:00:27,921 - INFO - 第 2 页获取到 100 条记录
2025-06-27 03:00:27,921 - INFO - Request Parameters - Page 3:
2025-06-27 03:00:27,921 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:27,921 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:28,406 - INFO - Response - Page 3:
2025-06-27 03:00:28,609 - INFO - 第 3 页获取到 100 条记录
2025-06-27 03:00:28,609 - INFO - Request Parameters - Page 4:
2025-06-27 03:00:28,609 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:28,609 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:29,156 - INFO - Response - Page 4:
2025-06-27 03:00:29,359 - INFO - 第 4 页获取到 100 条记录
2025-06-27 03:00:29,359 - INFO - Request Parameters - Page 5:
2025-06-27 03:00:29,359 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:29,359 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:29,859 - INFO - Response - Page 5:
2025-06-27 03:00:30,062 - INFO - 第 5 页获取到 100 条记录
2025-06-27 03:00:30,062 - INFO - Request Parameters - Page 6:
2025-06-27 03:00:30,062 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:30,062 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:30,594 - INFO - Response - Page 6:
2025-06-27 03:00:30,797 - INFO - 第 6 页获取到 100 条记录
2025-06-27 03:00:30,797 - INFO - Request Parameters - Page 7:
2025-06-27 03:00:30,797 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:30,797 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:31,312 - INFO - Response - Page 7:
2025-06-27 03:00:31,515 - INFO - 第 7 页获取到 65 条记录
2025-06-27 03:00:31,515 - INFO - 查询完成，共获取到 665 条记录
2025-06-27 03:00:31,515 - INFO - 获取到 665 条表单数据
2025-06-27 03:00:31,515 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-27 03:00:31,531 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 03:00:31,531 - INFO - 开始处理日期: 2025-06
2025-06-27 03:00:31,531 - INFO - Request Parameters - Page 1:
2025-06-27 03:00:31,531 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:31,531 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:32,047 - INFO - Response - Page 1:
2025-06-27 03:00:32,250 - INFO - 第 1 页获取到 100 条记录
2025-06-27 03:00:32,250 - INFO - Request Parameters - Page 2:
2025-06-27 03:00:32,250 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:32,250 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:32,719 - INFO - Response - Page 2:
2025-06-27 03:00:32,922 - INFO - 第 2 页获取到 100 条记录
2025-06-27 03:00:32,922 - INFO - Request Parameters - Page 3:
2025-06-27 03:00:32,922 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:32,922 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:33,406 - INFO - Response - Page 3:
2025-06-27 03:00:33,609 - INFO - 第 3 页获取到 100 条记录
2025-06-27 03:00:33,609 - INFO - Request Parameters - Page 4:
2025-06-27 03:00:33,609 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:33,609 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:34,281 - INFO - Response - Page 4:
2025-06-27 03:00:34,484 - INFO - 第 4 页获取到 100 条记录
2025-06-27 03:00:34,484 - INFO - Request Parameters - Page 5:
2025-06-27 03:00:34,484 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:34,484 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:35,047 - INFO - Response - Page 5:
2025-06-27 03:00:35,250 - INFO - 第 5 页获取到 100 条记录
2025-06-27 03:00:35,250 - INFO - Request Parameters - Page 6:
2025-06-27 03:00:35,250 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:35,250 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:35,781 - INFO - Response - Page 6:
2025-06-27 03:00:35,985 - INFO - 第 6 页获取到 100 条记录
2025-06-27 03:00:35,985 - INFO - Request Parameters - Page 7:
2025-06-27 03:00:35,985 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 03:00:35,985 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 03:00:36,391 - INFO - Response - Page 7:
2025-06-27 03:00:36,594 - INFO - 第 7 页获取到 26 条记录
2025-06-27 03:00:36,594 - INFO - 查询完成，共获取到 626 条记录
2025-06-27 03:00:36,594 - INFO - 获取到 626 条表单数据
2025-06-27 03:00:36,594 - INFO - 当前日期 2025-06 有 626 条MySQL数据需要处理
2025-06-27 03:00:36,610 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDL
2025-06-27 03:00:37,141 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDL
2025-06-27 03:00:37,141 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 176204.14, 'new_value': 181107.22}, {'field': 'offline_amount', 'old_value': 209308.71, 'new_value': 214308.71}, {'field': 'total_amount', 'old_value': 385512.85, 'new_value': 395415.93}, {'field': 'order_count', 'old_value': 1236, 'new_value': 1278}]
2025-06-27 03:00:37,141 - INFO - 日期 2025-06 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-06-27 03:00:37,141 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-06-27 03:00:37,141 - INFO - =================同步完成====================
2025-06-27 06:00:03,231 - INFO - =================使用默认全量同步=============
2025-06-27 06:00:05,012 - INFO - MySQL查询成功，共获取 3960 条记录
2025-06-27 06:00:05,012 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-27 06:00:05,044 - INFO - 开始处理日期: 2025-01
2025-06-27 06:00:05,059 - INFO - Request Parameters - Page 1:
2025-06-27 06:00:05,059 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:05,059 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:06,341 - INFO - Response - Page 1:
2025-06-27 06:00:06,544 - INFO - 第 1 页获取到 100 条记录
2025-06-27 06:00:06,544 - INFO - Request Parameters - Page 2:
2025-06-27 06:00:06,544 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:06,544 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:07,075 - INFO - Response - Page 2:
2025-06-27 06:00:07,278 - INFO - 第 2 页获取到 100 条记录
2025-06-27 06:00:07,278 - INFO - Request Parameters - Page 3:
2025-06-27 06:00:07,278 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:07,278 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:07,840 - INFO - Response - Page 3:
2025-06-27 06:00:08,044 - INFO - 第 3 页获取到 100 条记录
2025-06-27 06:00:08,044 - INFO - Request Parameters - Page 4:
2025-06-27 06:00:08,044 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:08,044 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:08,544 - INFO - Response - Page 4:
2025-06-27 06:00:08,747 - INFO - 第 4 页获取到 100 条记录
2025-06-27 06:00:08,747 - INFO - Request Parameters - Page 5:
2025-06-27 06:00:08,747 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:08,747 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:09,309 - INFO - Response - Page 5:
2025-06-27 06:00:09,512 - INFO - 第 5 页获取到 100 条记录
2025-06-27 06:00:09,512 - INFO - Request Parameters - Page 6:
2025-06-27 06:00:09,512 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:09,512 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:10,356 - INFO - Response - Page 6:
2025-06-27 06:00:10,559 - INFO - 第 6 页获取到 100 条记录
2025-06-27 06:00:10,559 - INFO - Request Parameters - Page 7:
2025-06-27 06:00:10,559 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:10,559 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:11,059 - INFO - Response - Page 7:
2025-06-27 06:00:11,262 - INFO - 第 7 页获取到 82 条记录
2025-06-27 06:00:11,262 - INFO - 查询完成，共获取到 682 条记录
2025-06-27 06:00:11,262 - INFO - 获取到 682 条表单数据
2025-06-27 06:00:11,278 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-27 06:00:11,278 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 06:00:11,278 - INFO - 开始处理日期: 2025-02
2025-06-27 06:00:11,278 - INFO - Request Parameters - Page 1:
2025-06-27 06:00:11,278 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:11,278 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:11,825 - INFO - Response - Page 1:
2025-06-27 06:00:12,028 - INFO - 第 1 页获取到 100 条记录
2025-06-27 06:00:12,028 - INFO - Request Parameters - Page 2:
2025-06-27 06:00:12,028 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:12,028 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:12,637 - INFO - Response - Page 2:
2025-06-27 06:00:12,840 - INFO - 第 2 页获取到 100 条记录
2025-06-27 06:00:12,840 - INFO - Request Parameters - Page 3:
2025-06-27 06:00:12,840 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:12,840 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:13,403 - INFO - Response - Page 3:
2025-06-27 06:00:13,606 - INFO - 第 3 页获取到 100 条记录
2025-06-27 06:00:13,606 - INFO - Request Parameters - Page 4:
2025-06-27 06:00:13,606 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:13,606 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:14,122 - INFO - Response - Page 4:
2025-06-27 06:00:14,325 - INFO - 第 4 页获取到 100 条记录
2025-06-27 06:00:14,325 - INFO - Request Parameters - Page 5:
2025-06-27 06:00:14,325 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:14,325 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:14,903 - INFO - Response - Page 5:
2025-06-27 06:00:15,106 - INFO - 第 5 页获取到 100 条记录
2025-06-27 06:00:15,106 - INFO - Request Parameters - Page 6:
2025-06-27 06:00:15,106 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:15,106 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:15,622 - INFO - Response - Page 6:
2025-06-27 06:00:15,825 - INFO - 第 6 页获取到 100 条记录
2025-06-27 06:00:15,825 - INFO - Request Parameters - Page 7:
2025-06-27 06:00:15,825 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:15,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:16,294 - INFO - Response - Page 7:
2025-06-27 06:00:16,497 - INFO - 第 7 页获取到 70 条记录
2025-06-27 06:00:16,497 - INFO - 查询完成，共获取到 670 条记录
2025-06-27 06:00:16,497 - INFO - 获取到 670 条表单数据
2025-06-27 06:00:16,497 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-27 06:00:16,512 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 06:00:16,512 - INFO - 开始处理日期: 2025-03
2025-06-27 06:00:16,512 - INFO - Request Parameters - Page 1:
2025-06-27 06:00:16,512 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:16,512 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:17,122 - INFO - Response - Page 1:
2025-06-27 06:00:17,325 - INFO - 第 1 页获取到 100 条记录
2025-06-27 06:00:17,325 - INFO - Request Parameters - Page 2:
2025-06-27 06:00:17,325 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:17,325 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:17,794 - INFO - Response - Page 2:
2025-06-27 06:00:17,997 - INFO - 第 2 页获取到 100 条记录
2025-06-27 06:00:17,997 - INFO - Request Parameters - Page 3:
2025-06-27 06:00:17,997 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:17,997 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:18,512 - INFO - Response - Page 3:
2025-06-27 06:00:18,715 - INFO - 第 3 页获取到 100 条记录
2025-06-27 06:00:18,715 - INFO - Request Parameters - Page 4:
2025-06-27 06:00:18,715 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:18,715 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:19,262 - INFO - Response - Page 4:
2025-06-27 06:00:19,465 - INFO - 第 4 页获取到 100 条记录
2025-06-27 06:00:19,465 - INFO - Request Parameters - Page 5:
2025-06-27 06:00:19,465 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:19,465 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:20,012 - INFO - Response - Page 5:
2025-06-27 06:00:20,215 - INFO - 第 5 页获取到 100 条记录
2025-06-27 06:00:20,215 - INFO - Request Parameters - Page 6:
2025-06-27 06:00:20,215 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:20,215 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:20,747 - INFO - Response - Page 6:
2025-06-27 06:00:20,950 - INFO - 第 6 页获取到 100 条记录
2025-06-27 06:00:20,950 - INFO - Request Parameters - Page 7:
2025-06-27 06:00:20,950 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:20,950 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:21,403 - INFO - Response - Page 7:
2025-06-27 06:00:21,606 - INFO - 第 7 页获取到 61 条记录
2025-06-27 06:00:21,606 - INFO - 查询完成，共获取到 661 条记录
2025-06-27 06:00:21,606 - INFO - 获取到 661 条表单数据
2025-06-27 06:00:21,606 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-27 06:00:21,622 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 06:00:21,622 - INFO - 开始处理日期: 2025-04
2025-06-27 06:00:21,622 - INFO - Request Parameters - Page 1:
2025-06-27 06:00:21,622 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:21,622 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:22,153 - INFO - Response - Page 1:
2025-06-27 06:00:22,356 - INFO - 第 1 页获取到 100 条记录
2025-06-27 06:00:22,356 - INFO - Request Parameters - Page 2:
2025-06-27 06:00:22,356 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:22,356 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:22,919 - INFO - Response - Page 2:
2025-06-27 06:00:23,122 - INFO - 第 2 页获取到 100 条记录
2025-06-27 06:00:23,122 - INFO - Request Parameters - Page 3:
2025-06-27 06:00:23,122 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:23,122 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:23,809 - INFO - Response - Page 3:
2025-06-27 06:00:24,012 - INFO - 第 3 页获取到 100 条记录
2025-06-27 06:00:24,012 - INFO - Request Parameters - Page 4:
2025-06-27 06:00:24,012 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:24,012 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:24,559 - INFO - Response - Page 4:
2025-06-27 06:00:24,762 - INFO - 第 4 页获取到 100 条记录
2025-06-27 06:00:24,762 - INFO - Request Parameters - Page 5:
2025-06-27 06:00:24,762 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:24,762 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:25,231 - INFO - Response - Page 5:
2025-06-27 06:00:25,434 - INFO - 第 5 页获取到 100 条记录
2025-06-27 06:00:25,434 - INFO - Request Parameters - Page 6:
2025-06-27 06:00:25,434 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:25,434 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:25,919 - INFO - Response - Page 6:
2025-06-27 06:00:26,122 - INFO - 第 6 页获取到 100 条记录
2025-06-27 06:00:26,122 - INFO - Request Parameters - Page 7:
2025-06-27 06:00:26,122 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:26,122 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:26,512 - INFO - Response - Page 7:
2025-06-27 06:00:26,715 - INFO - 第 7 页获取到 56 条记录
2025-06-27 06:00:26,715 - INFO - 查询完成，共获取到 656 条记录
2025-06-27 06:00:26,715 - INFO - 获取到 656 条表单数据
2025-06-27 06:00:26,715 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-27 06:00:26,731 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 06:00:26,731 - INFO - 开始处理日期: 2025-05
2025-06-27 06:00:26,731 - INFO - Request Parameters - Page 1:
2025-06-27 06:00:26,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:26,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:27,247 - INFO - Response - Page 1:
2025-06-27 06:00:27,450 - INFO - 第 1 页获取到 100 条记录
2025-06-27 06:00:27,450 - INFO - Request Parameters - Page 2:
2025-06-27 06:00:27,450 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:27,450 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:27,950 - INFO - Response - Page 2:
2025-06-27 06:00:28,153 - INFO - 第 2 页获取到 100 条记录
2025-06-27 06:00:28,153 - INFO - Request Parameters - Page 3:
2025-06-27 06:00:28,153 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:28,153 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:28,684 - INFO - Response - Page 3:
2025-06-27 06:00:28,887 - INFO - 第 3 页获取到 100 条记录
2025-06-27 06:00:28,887 - INFO - Request Parameters - Page 4:
2025-06-27 06:00:28,887 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:28,887 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:29,450 - INFO - Response - Page 4:
2025-06-27 06:00:29,653 - INFO - 第 4 页获取到 100 条记录
2025-06-27 06:00:29,653 - INFO - Request Parameters - Page 5:
2025-06-27 06:00:29,653 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:29,653 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:30,200 - INFO - Response - Page 5:
2025-06-27 06:00:30,403 - INFO - 第 5 页获取到 100 条记录
2025-06-27 06:00:30,403 - INFO - Request Parameters - Page 6:
2025-06-27 06:00:30,403 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:30,403 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:30,918 - INFO - Response - Page 6:
2025-06-27 06:00:31,122 - INFO - 第 6 页获取到 100 条记录
2025-06-27 06:00:31,122 - INFO - Request Parameters - Page 7:
2025-06-27 06:00:31,122 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:31,122 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:31,637 - INFO - Response - Page 7:
2025-06-27 06:00:31,840 - INFO - 第 7 页获取到 65 条记录
2025-06-27 06:00:31,840 - INFO - 查询完成，共获取到 665 条记录
2025-06-27 06:00:31,840 - INFO - 获取到 665 条表单数据
2025-06-27 06:00:31,840 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-27 06:00:31,856 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 06:00:31,856 - INFO - 开始处理日期: 2025-06
2025-06-27 06:00:31,856 - INFO - Request Parameters - Page 1:
2025-06-27 06:00:31,856 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:31,856 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:32,481 - INFO - Response - Page 1:
2025-06-27 06:00:32,684 - INFO - 第 1 页获取到 100 条记录
2025-06-27 06:00:32,684 - INFO - Request Parameters - Page 2:
2025-06-27 06:00:32,684 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:32,684 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:33,215 - INFO - Response - Page 2:
2025-06-27 06:00:33,418 - INFO - 第 2 页获取到 100 条记录
2025-06-27 06:00:33,418 - INFO - Request Parameters - Page 3:
2025-06-27 06:00:33,418 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:33,418 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:33,918 - INFO - Response - Page 3:
2025-06-27 06:00:34,122 - INFO - 第 3 页获取到 100 条记录
2025-06-27 06:00:34,122 - INFO - Request Parameters - Page 4:
2025-06-27 06:00:34,122 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:34,122 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:34,637 - INFO - Response - Page 4:
2025-06-27 06:00:34,840 - INFO - 第 4 页获取到 100 条记录
2025-06-27 06:00:34,840 - INFO - Request Parameters - Page 5:
2025-06-27 06:00:34,840 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:34,840 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:35,293 - INFO - Response - Page 5:
2025-06-27 06:00:35,497 - INFO - 第 5 页获取到 100 条记录
2025-06-27 06:00:35,497 - INFO - Request Parameters - Page 6:
2025-06-27 06:00:35,497 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:35,497 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:36,059 - INFO - Response - Page 6:
2025-06-27 06:00:36,262 - INFO - 第 6 页获取到 100 条记录
2025-06-27 06:00:36,262 - INFO - Request Parameters - Page 7:
2025-06-27 06:00:36,262 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 06:00:36,262 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 06:00:36,668 - INFO - Response - Page 7:
2025-06-27 06:00:36,872 - INFO - 第 7 页获取到 26 条记录
2025-06-27 06:00:36,872 - INFO - 查询完成，共获取到 626 条记录
2025-06-27 06:00:36,872 - INFO - 获取到 626 条表单数据
2025-06-27 06:00:36,872 - INFO - 当前日期 2025-06 有 626 条MySQL数据需要处理
2025-06-27 06:00:36,887 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC31
2025-06-27 06:00:37,309 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC31
2025-06-27 06:00:37,309 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 85091.0, 'new_value': 88417.0}, {'field': 'total_amount', 'old_value': 85091.0, 'new_value': 88417.0}, {'field': 'order_count', 'old_value': 437, 'new_value': 452}]
2025-06-27 06:00:37,309 - INFO - 日期 2025-06 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-06-27 06:00:37,309 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-06-27 06:00:37,309 - INFO - =================同步完成====================
2025-06-27 09:00:03,214 - INFO - =================使用默认全量同步=============
2025-06-27 09:00:04,994 - INFO - MySQL查询成功，共获取 3960 条记录
2025-06-27 09:00:04,994 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-27 09:00:05,025 - INFO - 开始处理日期: 2025-01
2025-06-27 09:00:05,041 - INFO - Request Parameters - Page 1:
2025-06-27 09:00:05,041 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:05,041 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:06,494 - INFO - Response - Page 1:
2025-06-27 09:00:06,694 - INFO - 第 1 页获取到 100 条记录
2025-06-27 09:00:06,694 - INFO - Request Parameters - Page 2:
2025-06-27 09:00:06,694 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:06,694 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:07,299 - INFO - Response - Page 2:
2025-06-27 09:00:07,510 - INFO - 第 2 页获取到 100 条记录
2025-06-27 09:00:07,510 - INFO - Request Parameters - Page 3:
2025-06-27 09:00:07,510 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:07,510 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:08,025 - INFO - Response - Page 3:
2025-06-27 09:00:08,228 - INFO - 第 3 页获取到 100 条记录
2025-06-27 09:00:08,228 - INFO - Request Parameters - Page 4:
2025-06-27 09:00:08,228 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:08,228 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:08,775 - INFO - Response - Page 4:
2025-06-27 09:00:08,978 - INFO - 第 4 页获取到 100 条记录
2025-06-27 09:00:08,978 - INFO - Request Parameters - Page 5:
2025-06-27 09:00:08,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:08,978 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:09,525 - INFO - Response - Page 5:
2025-06-27 09:00:09,728 - INFO - 第 5 页获取到 100 条记录
2025-06-27 09:00:09,728 - INFO - Request Parameters - Page 6:
2025-06-27 09:00:09,728 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:09,728 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:10,275 - INFO - Response - Page 6:
2025-06-27 09:00:10,478 - INFO - 第 6 页获取到 100 条记录
2025-06-27 09:00:10,478 - INFO - Request Parameters - Page 7:
2025-06-27 09:00:10,478 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:10,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:11,041 - INFO - Response - Page 7:
2025-06-27 09:00:11,244 - INFO - 第 7 页获取到 82 条记录
2025-06-27 09:00:11,244 - INFO - 查询完成，共获取到 682 条记录
2025-06-27 09:00:11,244 - INFO - 获取到 682 条表单数据
2025-06-27 09:00:11,244 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-27 09:00:11,259 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 09:00:11,259 - INFO - 开始处理日期: 2025-02
2025-06-27 09:00:11,259 - INFO - Request Parameters - Page 1:
2025-06-27 09:00:11,259 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:11,259 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:11,978 - INFO - Response - Page 1:
2025-06-27 09:00:12,181 - INFO - 第 1 页获取到 100 条记录
2025-06-27 09:00:12,181 - INFO - Request Parameters - Page 2:
2025-06-27 09:00:12,181 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:12,181 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:12,791 - INFO - Response - Page 2:
2025-06-27 09:00:12,994 - INFO - 第 2 页获取到 100 条记录
2025-06-27 09:00:12,994 - INFO - Request Parameters - Page 3:
2025-06-27 09:00:12,994 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:12,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:13,588 - INFO - Response - Page 3:
2025-06-27 09:00:13,791 - INFO - 第 3 页获取到 100 条记录
2025-06-27 09:00:13,791 - INFO - Request Parameters - Page 4:
2025-06-27 09:00:13,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:13,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:14,353 - INFO - Response - Page 4:
2025-06-27 09:00:14,556 - INFO - 第 4 页获取到 100 条记录
2025-06-27 09:00:14,556 - INFO - Request Parameters - Page 5:
2025-06-27 09:00:14,556 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:14,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:15,103 - INFO - Response - Page 5:
2025-06-27 09:00:15,306 - INFO - 第 5 页获取到 100 条记录
2025-06-27 09:00:15,306 - INFO - Request Parameters - Page 6:
2025-06-27 09:00:15,306 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:15,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:15,791 - INFO - Response - Page 6:
2025-06-27 09:00:15,994 - INFO - 第 6 页获取到 100 条记录
2025-06-27 09:00:15,994 - INFO - Request Parameters - Page 7:
2025-06-27 09:00:15,994 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:15,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:16,431 - INFO - Response - Page 7:
2025-06-27 09:00:16,634 - INFO - 第 7 页获取到 70 条记录
2025-06-27 09:00:16,634 - INFO - 查询完成，共获取到 670 条记录
2025-06-27 09:00:16,634 - INFO - 获取到 670 条表单数据
2025-06-27 09:00:16,634 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-27 09:00:16,650 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 09:00:16,650 - INFO - 开始处理日期: 2025-03
2025-06-27 09:00:16,650 - INFO - Request Parameters - Page 1:
2025-06-27 09:00:16,650 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:16,650 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:17,353 - INFO - Response - Page 1:
2025-06-27 09:00:17,556 - INFO - 第 1 页获取到 100 条记录
2025-06-27 09:00:17,556 - INFO - Request Parameters - Page 2:
2025-06-27 09:00:17,556 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:17,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:18,025 - INFO - Response - Page 2:
2025-06-27 09:00:18,228 - INFO - 第 2 页获取到 100 条记录
2025-06-27 09:00:18,228 - INFO - Request Parameters - Page 3:
2025-06-27 09:00:18,228 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:18,228 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:18,916 - INFO - Response - Page 3:
2025-06-27 09:00:19,119 - INFO - 第 3 页获取到 100 条记录
2025-06-27 09:00:19,119 - INFO - Request Parameters - Page 4:
2025-06-27 09:00:19,119 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:19,119 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:19,666 - INFO - Response - Page 4:
2025-06-27 09:00:19,869 - INFO - 第 4 页获取到 100 条记录
2025-06-27 09:00:19,869 - INFO - Request Parameters - Page 5:
2025-06-27 09:00:19,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:19,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:20,400 - INFO - Response - Page 5:
2025-06-27 09:00:20,603 - INFO - 第 5 页获取到 100 条记录
2025-06-27 09:00:20,603 - INFO - Request Parameters - Page 6:
2025-06-27 09:00:20,603 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:20,603 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:21,134 - INFO - Response - Page 6:
2025-06-27 09:00:21,338 - INFO - 第 6 页获取到 100 条记录
2025-06-27 09:00:21,338 - INFO - Request Parameters - Page 7:
2025-06-27 09:00:21,338 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:21,338 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:21,759 - INFO - Response - Page 7:
2025-06-27 09:00:21,963 - INFO - 第 7 页获取到 61 条记录
2025-06-27 09:00:21,963 - INFO - 查询完成，共获取到 661 条记录
2025-06-27 09:00:21,963 - INFO - 获取到 661 条表单数据
2025-06-27 09:00:21,963 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-27 09:00:21,978 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 09:00:21,978 - INFO - 开始处理日期: 2025-04
2025-06-27 09:00:21,978 - INFO - Request Parameters - Page 1:
2025-06-27 09:00:21,978 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:21,978 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:22,541 - INFO - Response - Page 1:
2025-06-27 09:00:22,744 - INFO - 第 1 页获取到 100 条记录
2025-06-27 09:00:22,744 - INFO - Request Parameters - Page 2:
2025-06-27 09:00:22,744 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:22,744 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:23,244 - INFO - Response - Page 2:
2025-06-27 09:00:23,447 - INFO - 第 2 页获取到 100 条记录
2025-06-27 09:00:23,447 - INFO - Request Parameters - Page 3:
2025-06-27 09:00:23,447 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:23,447 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:24,134 - INFO - Response - Page 3:
2025-06-27 09:00:24,338 - INFO - 第 3 页获取到 100 条记录
2025-06-27 09:00:24,338 - INFO - Request Parameters - Page 4:
2025-06-27 09:00:24,338 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:24,338 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:24,884 - INFO - Response - Page 4:
2025-06-27 09:00:25,088 - INFO - 第 4 页获取到 100 条记录
2025-06-27 09:00:25,088 - INFO - Request Parameters - Page 5:
2025-06-27 09:00:25,088 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:25,088 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:25,588 - INFO - Response - Page 5:
2025-06-27 09:00:25,791 - INFO - 第 5 页获取到 100 条记录
2025-06-27 09:00:25,791 - INFO - Request Parameters - Page 6:
2025-06-27 09:00:25,791 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:25,791 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:26,291 - INFO - Response - Page 6:
2025-06-27 09:00:26,494 - INFO - 第 6 页获取到 100 条记录
2025-06-27 09:00:26,494 - INFO - Request Parameters - Page 7:
2025-06-27 09:00:26,494 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:26,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:26,963 - INFO - Response - Page 7:
2025-06-27 09:00:27,166 - INFO - 第 7 页获取到 56 条记录
2025-06-27 09:00:27,166 - INFO - 查询完成，共获取到 656 条记录
2025-06-27 09:00:27,166 - INFO - 获取到 656 条表单数据
2025-06-27 09:00:27,166 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-27 09:00:27,181 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 09:00:27,181 - INFO - 开始处理日期: 2025-05
2025-06-27 09:00:27,181 - INFO - Request Parameters - Page 1:
2025-06-27 09:00:27,181 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:27,181 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:27,791 - INFO - Response - Page 1:
2025-06-27 09:00:27,994 - INFO - 第 1 页获取到 100 条记录
2025-06-27 09:00:27,994 - INFO - Request Parameters - Page 2:
2025-06-27 09:00:27,994 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:27,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:28,509 - INFO - Response - Page 2:
2025-06-27 09:00:28,713 - INFO - 第 2 页获取到 100 条记录
2025-06-27 09:00:28,713 - INFO - Request Parameters - Page 3:
2025-06-27 09:00:28,713 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:28,713 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:29,228 - INFO - Response - Page 3:
2025-06-27 09:00:29,431 - INFO - 第 3 页获取到 100 条记录
2025-06-27 09:00:29,431 - INFO - Request Parameters - Page 4:
2025-06-27 09:00:29,431 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:29,431 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:30,025 - INFO - Response - Page 4:
2025-06-27 09:00:30,228 - INFO - 第 4 页获取到 100 条记录
2025-06-27 09:00:30,228 - INFO - Request Parameters - Page 5:
2025-06-27 09:00:30,228 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:30,228 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:30,666 - INFO - Response - Page 5:
2025-06-27 09:00:30,869 - INFO - 第 5 页获取到 100 条记录
2025-06-27 09:00:30,869 - INFO - Request Parameters - Page 6:
2025-06-27 09:00:30,869 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:30,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:31,337 - INFO - Response - Page 6:
2025-06-27 09:00:31,541 - INFO - 第 6 页获取到 100 条记录
2025-06-27 09:00:31,541 - INFO - Request Parameters - Page 7:
2025-06-27 09:00:31,541 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:31,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:32,009 - INFO - Response - Page 7:
2025-06-27 09:00:32,212 - INFO - 第 7 页获取到 65 条记录
2025-06-27 09:00:32,212 - INFO - 查询完成，共获取到 665 条记录
2025-06-27 09:00:32,212 - INFO - 获取到 665 条表单数据
2025-06-27 09:00:32,212 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-27 09:00:32,228 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 09:00:32,228 - INFO - 开始处理日期: 2025-06
2025-06-27 09:00:32,228 - INFO - Request Parameters - Page 1:
2025-06-27 09:00:32,228 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:32,228 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:32,822 - INFO - Response - Page 1:
2025-06-27 09:00:33,025 - INFO - 第 1 页获取到 100 条记录
2025-06-27 09:00:33,025 - INFO - Request Parameters - Page 2:
2025-06-27 09:00:33,025 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:33,025 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:33,541 - INFO - Response - Page 2:
2025-06-27 09:00:33,744 - INFO - 第 2 页获取到 100 条记录
2025-06-27 09:00:33,744 - INFO - Request Parameters - Page 3:
2025-06-27 09:00:33,744 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:33,744 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:34,197 - INFO - Response - Page 3:
2025-06-27 09:00:34,400 - INFO - 第 3 页获取到 100 条记录
2025-06-27 09:00:34,400 - INFO - Request Parameters - Page 4:
2025-06-27 09:00:34,400 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:34,400 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:34,884 - INFO - Response - Page 4:
2025-06-27 09:00:35,087 - INFO - 第 4 页获取到 100 条记录
2025-06-27 09:00:35,087 - INFO - Request Parameters - Page 5:
2025-06-27 09:00:35,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:35,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:35,556 - INFO - Response - Page 5:
2025-06-27 09:00:35,759 - INFO - 第 5 页获取到 100 条记录
2025-06-27 09:00:35,759 - INFO - Request Parameters - Page 6:
2025-06-27 09:00:35,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:35,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:36,244 - INFO - Response - Page 6:
2025-06-27 09:00:36,447 - INFO - 第 6 页获取到 100 条记录
2025-06-27 09:00:36,447 - INFO - Request Parameters - Page 7:
2025-06-27 09:00:36,447 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 09:00:36,447 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 09:00:36,837 - INFO - Response - Page 7:
2025-06-27 09:00:37,041 - INFO - 第 7 页获取到 26 条记录
2025-06-27 09:00:37,041 - INFO - 查询完成，共获取到 626 条记录
2025-06-27 09:00:37,041 - INFO - 获取到 626 条表单数据
2025-06-27 09:00:37,041 - INFO - 当前日期 2025-06 有 626 条MySQL数据需要处理
2025-06-27 09:00:37,056 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOK
2025-06-27 09:00:37,509 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOK
2025-06-27 09:00:37,509 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 498306.0, 'new_value': 521350.0}, {'field': 'total_amount', 'old_value': 498306.0, 'new_value': 521350.0}, {'field': 'order_count', 'old_value': 114, 'new_value': 123}]
2025-06-27 09:00:37,509 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRM
2025-06-27 09:00:37,962 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRM
2025-06-27 09:00:37,962 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1476074.0, 'new_value': 1523173.0}, {'field': 'total_amount', 'old_value': 1476074.0, 'new_value': 1523173.0}, {'field': 'order_count', 'old_value': 6852, 'new_value': 7051}]
2025-06-27 09:00:37,962 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML11
2025-06-27 09:00:38,431 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML11
2025-06-27 09:00:38,431 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 204609.13, 'new_value': 206557.92}, {'field': 'offline_amount', 'old_value': 16247.3, 'new_value': 16470.3}, {'field': 'total_amount', 'old_value': 220856.43, 'new_value': 223028.22}, {'field': 'order_count', 'old_value': 15030, 'new_value': 15189}]
2025-06-27 09:00:38,431 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW11
2025-06-27 09:00:38,837 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW11
2025-06-27 09:00:38,837 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71008.0, 'new_value': 74028.0}, {'field': 'total_amount', 'old_value': 71008.0, 'new_value': 74028.0}, {'field': 'order_count', 'old_value': 3172, 'new_value': 3288}]
2025-06-27 09:00:38,837 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMY11
2025-06-27 09:00:39,259 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMY11
2025-06-27 09:00:39,259 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 243252.96, 'new_value': 246161.59}, {'field': 'total_amount', 'old_value': 243252.96, 'new_value': 246161.59}, {'field': 'order_count', 'old_value': 1086, 'new_value': 1106}]
2025-06-27 09:00:39,259 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG1
2025-06-27 09:00:39,697 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG1
2025-06-27 09:00:39,697 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34604.2, 'new_value': 37256.2}, {'field': 'total_amount', 'old_value': 35087.2, 'new_value': 37739.2}, {'field': 'order_count', 'old_value': 171, 'new_value': 175}]
2025-06-27 09:00:39,697 - INFO - 日期 2025-06 处理完成 - 更新: 6 条，插入: 0 条，错误: 0 条
2025-06-27 09:00:39,697 - INFO - 数据同步完成！更新: 6 条，插入: 0 条，错误: 0 条
2025-06-27 09:00:39,697 - INFO - =================同步完成====================
2025-06-27 12:00:03,227 - INFO - =================使用默认全量同步=============
2025-06-27 12:00:05,009 - INFO - MySQL查询成功，共获取 3963 条记录
2025-06-27 12:00:05,009 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-27 12:00:05,040 - INFO - 开始处理日期: 2025-01
2025-06-27 12:00:05,040 - INFO - Request Parameters - Page 1:
2025-06-27 12:00:05,040 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:05,040 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:06,571 - INFO - Response - Page 1:
2025-06-27 12:00:06,774 - INFO - 第 1 页获取到 100 条记录
2025-06-27 12:00:06,774 - INFO - Request Parameters - Page 2:
2025-06-27 12:00:06,774 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:06,774 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:07,352 - INFO - Response - Page 2:
2025-06-27 12:00:07,556 - INFO - 第 2 页获取到 100 条记录
2025-06-27 12:00:07,556 - INFO - Request Parameters - Page 3:
2025-06-27 12:00:07,556 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:07,556 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:08,259 - INFO - Response - Page 3:
2025-06-27 12:00:08,462 - INFO - 第 3 页获取到 100 条记录
2025-06-27 12:00:08,462 - INFO - Request Parameters - Page 4:
2025-06-27 12:00:08,462 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:08,462 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:09,071 - INFO - Response - Page 4:
2025-06-27 12:00:09,274 - INFO - 第 4 页获取到 100 条记录
2025-06-27 12:00:09,274 - INFO - Request Parameters - Page 5:
2025-06-27 12:00:09,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:09,274 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:09,806 - INFO - Response - Page 5:
2025-06-27 12:00:10,009 - INFO - 第 5 页获取到 100 条记录
2025-06-27 12:00:10,009 - INFO - Request Parameters - Page 6:
2025-06-27 12:00:10,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:10,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:10,556 - INFO - Response - Page 6:
2025-06-27 12:00:10,759 - INFO - 第 6 页获取到 100 条记录
2025-06-27 12:00:10,759 - INFO - Request Parameters - Page 7:
2025-06-27 12:00:10,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:10,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:11,306 - INFO - Response - Page 7:
2025-06-27 12:00:11,509 - INFO - 第 7 页获取到 82 条记录
2025-06-27 12:00:11,509 - INFO - 查询完成，共获取到 682 条记录
2025-06-27 12:00:11,509 - INFO - 获取到 682 条表单数据
2025-06-27 12:00:11,509 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-27 12:00:11,524 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 12:00:11,524 - INFO - 开始处理日期: 2025-02
2025-06-27 12:00:11,524 - INFO - Request Parameters - Page 1:
2025-06-27 12:00:11,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:11,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:12,055 - INFO - Response - Page 1:
2025-06-27 12:00:12,259 - INFO - 第 1 页获取到 100 条记录
2025-06-27 12:00:12,259 - INFO - Request Parameters - Page 2:
2025-06-27 12:00:12,259 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:12,259 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:12,790 - INFO - Response - Page 2:
2025-06-27 12:00:12,993 - INFO - 第 2 页获取到 100 条记录
2025-06-27 12:00:12,993 - INFO - Request Parameters - Page 3:
2025-06-27 12:00:12,993 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:12,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:13,540 - INFO - Response - Page 3:
2025-06-27 12:00:13,743 - INFO - 第 3 页获取到 100 条记录
2025-06-27 12:00:13,743 - INFO - Request Parameters - Page 4:
2025-06-27 12:00:13,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:13,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:14,321 - INFO - Response - Page 4:
2025-06-27 12:00:14,524 - INFO - 第 4 页获取到 100 条记录
2025-06-27 12:00:14,524 - INFO - Request Parameters - Page 5:
2025-06-27 12:00:14,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:14,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:15,134 - INFO - Response - Page 5:
2025-06-27 12:00:15,337 - INFO - 第 5 页获取到 100 条记录
2025-06-27 12:00:15,337 - INFO - Request Parameters - Page 6:
2025-06-27 12:00:15,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:15,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:15,915 - INFO - Response - Page 6:
2025-06-27 12:00:16,118 - INFO - 第 6 页获取到 100 条记录
2025-06-27 12:00:16,118 - INFO - Request Parameters - Page 7:
2025-06-27 12:00:16,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:16,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:16,618 - INFO - Response - Page 7:
2025-06-27 12:00:16,821 - INFO - 第 7 页获取到 70 条记录
2025-06-27 12:00:16,821 - INFO - 查询完成，共获取到 670 条记录
2025-06-27 12:00:16,821 - INFO - 获取到 670 条表单数据
2025-06-27 12:00:16,821 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-27 12:00:16,837 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 12:00:16,837 - INFO - 开始处理日期: 2025-03
2025-06-27 12:00:16,837 - INFO - Request Parameters - Page 1:
2025-06-27 12:00:16,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:16,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:17,430 - INFO - Response - Page 1:
2025-06-27 12:00:17,634 - INFO - 第 1 页获取到 100 条记录
2025-06-27 12:00:17,634 - INFO - Request Parameters - Page 2:
2025-06-27 12:00:17,634 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:17,634 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:18,134 - INFO - Response - Page 2:
2025-06-27 12:00:18,337 - INFO - 第 2 页获取到 100 条记录
2025-06-27 12:00:18,337 - INFO - Request Parameters - Page 3:
2025-06-27 12:00:18,337 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:18,337 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:18,946 - INFO - Response - Page 3:
2025-06-27 12:00:19,149 - INFO - 第 3 页获取到 100 条记录
2025-06-27 12:00:19,149 - INFO - Request Parameters - Page 4:
2025-06-27 12:00:19,149 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:19,149 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:19,696 - INFO - Response - Page 4:
2025-06-27 12:00:19,899 - INFO - 第 4 页获取到 100 条记录
2025-06-27 12:00:19,899 - INFO - Request Parameters - Page 5:
2025-06-27 12:00:19,899 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:19,899 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:20,493 - INFO - Response - Page 5:
2025-06-27 12:00:20,696 - INFO - 第 5 页获取到 100 条记录
2025-06-27 12:00:20,696 - INFO - Request Parameters - Page 6:
2025-06-27 12:00:20,696 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:20,696 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:21,368 - INFO - Response - Page 6:
2025-06-27 12:00:21,571 - INFO - 第 6 页获取到 100 条记录
2025-06-27 12:00:21,571 - INFO - Request Parameters - Page 7:
2025-06-27 12:00:21,571 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:21,571 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:22,024 - INFO - Response - Page 7:
2025-06-27 12:00:22,227 - INFO - 第 7 页获取到 61 条记录
2025-06-27 12:00:22,227 - INFO - 查询完成，共获取到 661 条记录
2025-06-27 12:00:22,227 - INFO - 获取到 661 条表单数据
2025-06-27 12:00:22,227 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-27 12:00:22,243 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 12:00:22,243 - INFO - 开始处理日期: 2025-04
2025-06-27 12:00:22,243 - INFO - Request Parameters - Page 1:
2025-06-27 12:00:22,243 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:22,243 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:22,759 - INFO - Response - Page 1:
2025-06-27 12:00:22,962 - INFO - 第 1 页获取到 100 条记录
2025-06-27 12:00:22,962 - INFO - Request Parameters - Page 2:
2025-06-27 12:00:22,962 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:22,962 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:23,477 - INFO - Response - Page 2:
2025-06-27 12:00:23,680 - INFO - 第 2 页获取到 100 条记录
2025-06-27 12:00:23,680 - INFO - Request Parameters - Page 3:
2025-06-27 12:00:23,680 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:23,680 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:24,227 - INFO - Response - Page 3:
2025-06-27 12:00:24,430 - INFO - 第 3 页获取到 100 条记录
2025-06-27 12:00:24,430 - INFO - Request Parameters - Page 4:
2025-06-27 12:00:24,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:24,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:24,899 - INFO - Response - Page 4:
2025-06-27 12:00:25,102 - INFO - 第 4 页获取到 100 条记录
2025-06-27 12:00:25,102 - INFO - Request Parameters - Page 5:
2025-06-27 12:00:25,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:25,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:25,555 - INFO - Response - Page 5:
2025-06-27 12:00:25,759 - INFO - 第 5 页获取到 100 条记录
2025-06-27 12:00:25,759 - INFO - Request Parameters - Page 6:
2025-06-27 12:00:25,759 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:25,759 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:26,259 - INFO - Response - Page 6:
2025-06-27 12:00:26,462 - INFO - 第 6 页获取到 100 条记录
2025-06-27 12:00:26,462 - INFO - Request Parameters - Page 7:
2025-06-27 12:00:26,462 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:26,462 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:26,884 - INFO - Response - Page 7:
2025-06-27 12:00:27,087 - INFO - 第 7 页获取到 56 条记录
2025-06-27 12:00:27,087 - INFO - 查询完成，共获取到 656 条记录
2025-06-27 12:00:27,087 - INFO - 获取到 656 条表单数据
2025-06-27 12:00:27,087 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-27 12:00:27,102 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 12:00:27,102 - INFO - 开始处理日期: 2025-05
2025-06-27 12:00:27,102 - INFO - Request Parameters - Page 1:
2025-06-27 12:00:27,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:27,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:27,634 - INFO - Response - Page 1:
2025-06-27 12:00:27,837 - INFO - 第 1 页获取到 100 条记录
2025-06-27 12:00:27,837 - INFO - Request Parameters - Page 2:
2025-06-27 12:00:27,837 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:27,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:28,337 - INFO - Response - Page 2:
2025-06-27 12:00:28,540 - INFO - 第 2 页获取到 100 条记录
2025-06-27 12:00:28,540 - INFO - Request Parameters - Page 3:
2025-06-27 12:00:28,540 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:28,540 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:29,196 - INFO - Response - Page 3:
2025-06-27 12:00:29,415 - INFO - 第 3 页获取到 100 条记录
2025-06-27 12:00:29,415 - INFO - Request Parameters - Page 4:
2025-06-27 12:00:29,415 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:29,415 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:29,915 - INFO - Response - Page 4:
2025-06-27 12:00:30,118 - INFO - 第 4 页获取到 100 条记录
2025-06-27 12:00:30,118 - INFO - Request Parameters - Page 5:
2025-06-27 12:00:30,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:30,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:30,587 - INFO - Response - Page 5:
2025-06-27 12:00:30,790 - INFO - 第 5 页获取到 100 条记录
2025-06-27 12:00:30,790 - INFO - Request Parameters - Page 6:
2025-06-27 12:00:30,790 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:30,790 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:31,337 - INFO - Response - Page 6:
2025-06-27 12:00:31,540 - INFO - 第 6 页获取到 100 条记录
2025-06-27 12:00:31,540 - INFO - Request Parameters - Page 7:
2025-06-27 12:00:31,540 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:31,540 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:31,993 - INFO - Response - Page 7:
2025-06-27 12:00:32,196 - INFO - 第 7 页获取到 65 条记录
2025-06-27 12:00:32,196 - INFO - 查询完成，共获取到 665 条记录
2025-06-27 12:00:32,196 - INFO - 获取到 665 条表单数据
2025-06-27 12:00:32,196 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-27 12:00:32,212 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 12:00:32,212 - INFO - 开始处理日期: 2025-06
2025-06-27 12:00:32,212 - INFO - Request Parameters - Page 1:
2025-06-27 12:00:32,212 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:32,212 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:32,696 - INFO - Response - Page 1:
2025-06-27 12:00:32,899 - INFO - 第 1 页获取到 100 条记录
2025-06-27 12:00:32,899 - INFO - Request Parameters - Page 2:
2025-06-27 12:00:32,899 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:32,899 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:33,415 - INFO - Response - Page 2:
2025-06-27 12:00:33,618 - INFO - 第 2 页获取到 100 条记录
2025-06-27 12:00:33,618 - INFO - Request Parameters - Page 3:
2025-06-27 12:00:33,618 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:33,618 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:34,102 - INFO - Response - Page 3:
2025-06-27 12:00:34,305 - INFO - 第 3 页获取到 100 条记录
2025-06-27 12:00:34,305 - INFO - Request Parameters - Page 4:
2025-06-27 12:00:34,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:34,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:34,883 - INFO - Response - Page 4:
2025-06-27 12:00:35,087 - INFO - 第 4 页获取到 100 条记录
2025-06-27 12:00:35,087 - INFO - Request Parameters - Page 5:
2025-06-27 12:00:35,087 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:35,087 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:35,602 - INFO - Response - Page 5:
2025-06-27 12:00:35,805 - INFO - 第 5 页获取到 100 条记录
2025-06-27 12:00:35,805 - INFO - Request Parameters - Page 6:
2025-06-27 12:00:35,805 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:35,805 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:36,305 - INFO - Response - Page 6:
2025-06-27 12:00:36,508 - INFO - 第 6 页获取到 100 条记录
2025-06-27 12:00:36,508 - INFO - Request Parameters - Page 7:
2025-06-27 12:00:36,508 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 12:00:36,508 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 12:00:36,915 - INFO - Response - Page 7:
2025-06-27 12:00:37,118 - INFO - 第 7 页获取到 26 条记录
2025-06-27 12:00:37,118 - INFO - 查询完成，共获取到 626 条记录
2025-06-27 12:00:37,118 - INFO - 获取到 626 条表单数据
2025-06-27 12:00:37,118 - INFO - 当前日期 2025-06 有 629 条MySQL数据需要处理
2025-06-27 12:00:37,118 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMGX
2025-06-27 12:00:37,727 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMGX
2025-06-27 12:00:37,727 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 161903.0, 'new_value': 164293.0}, {'field': 'total_amount', 'old_value': 161903.0, 'new_value': 164293.0}, {'field': 'order_count', 'old_value': 2681, 'new_value': 2727}]
2025-06-27 12:00:37,727 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMHX
2025-06-27 12:00:38,258 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMHX
2025-06-27 12:00:38,258 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 719007.0, 'new_value': 743885.0}, {'field': 'total_amount', 'old_value': 719007.0, 'new_value': 743885.0}, {'field': 'order_count', 'old_value': 5093, 'new_value': 5281}]
2025-06-27 12:00:38,258 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMIX
2025-06-27 12:00:38,758 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMIX
2025-06-27 12:00:38,758 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 131960.0, 'new_value': 146960.0}, {'field': 'total_amount', 'old_value': 131960.0, 'new_value': 146960.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-06-27 12:00:38,758 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMJX
2025-06-27 12:00:39,212 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMJX
2025-06-27 12:00:39,212 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 136007.51, 'new_value': 138479.78}, {'field': 'total_amount', 'old_value': 136007.51, 'new_value': 138479.78}, {'field': 'order_count', 'old_value': 3560, 'new_value': 3633}]
2025-06-27 12:00:39,212 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMLX
2025-06-27 12:00:39,712 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMLX
2025-06-27 12:00:39,712 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 83902.12, 'new_value': 87901.06}, {'field': 'offline_amount', 'old_value': 923430.34, 'new_value': 956841.47}, {'field': 'total_amount', 'old_value': 1003339.97, 'new_value': 1040750.04}, {'field': 'order_count', 'old_value': 4859, 'new_value': 5067}]
2025-06-27 12:00:39,712 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMMX
2025-06-27 12:00:40,274 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMMX
2025-06-27 12:00:40,274 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 225400.0, 'new_value': 229400.0}, {'field': 'total_amount', 'old_value': 225400.0, 'new_value': 229400.0}]
2025-06-27 12:00:40,274 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMOX
2025-06-27 12:00:40,696 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMOX
2025-06-27 12:00:40,696 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26370.9, 'new_value': 29823.3}, {'field': 'total_amount', 'old_value': 26370.9, 'new_value': 29823.3}, {'field': 'order_count', 'old_value': 158, 'new_value': 172}]
2025-06-27 12:00:40,696 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMPX
2025-06-27 12:00:41,180 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMPX
2025-06-27 12:00:41,180 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36074.02, 'new_value': 42031.76}, {'field': 'total_amount', 'old_value': 36074.02, 'new_value': 42031.76}, {'field': 'order_count', 'old_value': 7130, 'new_value': 8321}]
2025-06-27 12:00:41,180 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMQX
2025-06-27 12:00:41,618 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMQX
2025-06-27 12:00:41,618 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 355670.0, 'new_value': 390670.0}, {'field': 'total_amount', 'old_value': 377850.0, 'new_value': 412850.0}, {'field': 'order_count', 'old_value': 251, 'new_value': 270}]
2025-06-27 12:00:41,618 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMSX
2025-06-27 12:00:42,071 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMSX
2025-06-27 12:00:42,071 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26811.27, 'new_value': 28050.27}, {'field': 'offline_amount', 'old_value': 15255.57, 'new_value': 15791.57}, {'field': 'total_amount', 'old_value': 42066.84, 'new_value': 43841.84}, {'field': 'order_count', 'old_value': 1752, 'new_value': 1825}]
2025-06-27 12:00:42,071 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMVX
2025-06-27 12:00:42,571 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMVX
2025-06-27 12:00:42,571 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52880.5, 'new_value': 61880.5}, {'field': 'total_amount', 'old_value': 94880.5, 'new_value': 103880.5}, {'field': 'order_count', 'old_value': 16, 'new_value': 18}]
2025-06-27 12:00:42,571 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMWX
2025-06-27 12:00:43,008 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMWX
2025-06-27 12:00:43,008 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 190320.0, 'new_value': 196880.0}, {'field': 'total_amount', 'old_value': 190320.0, 'new_value': 196880.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-06-27 12:00:43,008 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMYX
2025-06-27 12:00:43,555 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMYX
2025-06-27 12:00:43,555 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 26445.64, 'new_value': 26645.64}, {'field': 'total_amount', 'old_value': 26445.64, 'new_value': 26645.64}, {'field': 'order_count', 'old_value': 88, 'new_value': 90}]
2025-06-27 12:00:43,555 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Y
2025-06-27 12:00:44,024 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Y
2025-06-27 12:00:44,024 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 7185.0, 'new_value': 7683.0}, {'field': 'offline_amount', 'old_value': 100028.0, 'new_value': 103502.0}, {'field': 'total_amount', 'old_value': 107213.0, 'new_value': 111185.0}, {'field': 'order_count', 'old_value': 119, 'new_value': 124}]
2025-06-27 12:00:44,024 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM9Y
2025-06-27 12:00:44,477 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM9Y
2025-06-27 12:00:44,477 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9200.0, 'new_value': 11940.0}, {'field': 'total_amount', 'old_value': 60500.0, 'new_value': 63240.0}, {'field': 'order_count', 'old_value': 665, 'new_value': 699}]
2025-06-27 12:00:44,477 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAY
2025-06-27 12:00:44,962 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAY
2025-06-27 12:00:44,962 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 184341.0, 'new_value': 188421.0}, {'field': 'total_amount', 'old_value': 184341.0, 'new_value': 188421.0}, {'field': 'order_count', 'old_value': 43, 'new_value': 45}]
2025-06-27 12:00:44,962 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMDY
2025-06-27 12:00:45,524 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMDY
2025-06-27 12:00:45,524 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 266265.1, 'new_value': 272034.8}, {'field': 'total_amount', 'old_value': 266265.1, 'new_value': 272034.8}, {'field': 'order_count', 'old_value': 3933, 'new_value': 4033}]
2025-06-27 12:00:45,524 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGY
2025-06-27 12:00:46,024 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMGY
2025-06-27 12:00:46,024 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5898.7, 'new_value': 6471.5}, {'field': 'offline_amount', 'old_value': 51071.87, 'new_value': 52854.67}, {'field': 'total_amount', 'old_value': 56970.57, 'new_value': 59326.17}, {'field': 'order_count', 'old_value': 497, 'new_value': 522}]
2025-06-27 12:00:46,024 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMIY
2025-06-27 12:00:46,446 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMIY
2025-06-27 12:00:46,446 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 96541.37, 'new_value': 108949.37}, {'field': 'offline_amount', 'old_value': 301811.99, 'new_value': 346427.99}, {'field': 'total_amount', 'old_value': 398353.36, 'new_value': 455377.36}, {'field': 'order_count', 'old_value': 16222, 'new_value': 18614}]
2025-06-27 12:00:46,446 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJY
2025-06-27 12:00:47,008 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJY
2025-06-27 12:00:47,008 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 52410.88, 'new_value': 54306.07}, {'field': 'offline_amount', 'old_value': 117899.77, 'new_value': 124464.77}, {'field': 'total_amount', 'old_value': 170310.65, 'new_value': 178770.84}, {'field': 'order_count', 'old_value': 1899, 'new_value': 1995}]
2025-06-27 12:00:47,008 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLY
2025-06-27 12:00:47,508 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLY
2025-06-27 12:00:47,508 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46864.7, 'new_value': 49522.6}, {'field': 'total_amount', 'old_value': 46864.7, 'new_value': 49522.6}, {'field': 'order_count', 'old_value': 445, 'new_value': 449}]
2025-06-27 12:00:47,508 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOY
2025-06-27 12:00:48,024 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMOY
2025-06-27 12:00:48,024 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 34964.0, 'new_value': 36144.0}, {'field': 'offline_amount', 'old_value': 56852.08, 'new_value': 57486.43}, {'field': 'total_amount', 'old_value': 91816.08, 'new_value': 93630.43}, {'field': 'order_count', 'old_value': 122, 'new_value': 127}]
2025-06-27 12:00:48,024 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPY
2025-06-27 12:00:48,524 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPY
2025-06-27 12:00:48,524 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 62448.0, 'new_value': 64757.0}, {'field': 'total_amount', 'old_value': 62448.0, 'new_value': 64757.0}, {'field': 'order_count', 'old_value': 1187, 'new_value': 1233}]
2025-06-27 12:00:48,524 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRY
2025-06-27 12:00:48,977 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRY
2025-06-27 12:00:48,977 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 145031.0, 'new_value': 148551.0}, {'field': 'offline_amount', 'old_value': 49266.07, 'new_value': 51265.97}, {'field': 'total_amount', 'old_value': 194297.07, 'new_value': 199816.97}, {'field': 'order_count', 'old_value': 1314, 'new_value': 1393}]
2025-06-27 12:00:48,977 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTY
2025-06-27 12:00:49,446 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTY
2025-06-27 12:00:49,446 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5579.0, 'new_value': 6272.0}, {'field': 'total_amount', 'old_value': 9771.0, 'new_value': 10464.0}, {'field': 'order_count', 'old_value': 175, 'new_value': 185}]
2025-06-27 12:00:49,446 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMVY
2025-06-27 12:00:49,961 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMVY
2025-06-27 12:00:49,961 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 228041.0, 'new_value': 233502.0}, {'field': 'total_amount', 'old_value': 228041.0, 'new_value': 233502.0}, {'field': 'order_count', 'old_value': 1193, 'new_value': 1255}]
2025-06-27 12:00:49,961 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMWY
2025-06-27 12:00:50,461 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMWY
2025-06-27 12:00:50,461 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9075.08, 'new_value': 14771.08}, {'field': 'offline_amount', 'old_value': 136900.0, 'new_value': 153458.0}, {'field': 'total_amount', 'old_value': 145975.08, 'new_value': 168229.08}, {'field': 'order_count', 'old_value': 2349, 'new_value': 2713}]
2025-06-27 12:00:50,461 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYY
2025-06-27 12:00:50,946 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYY
2025-06-27 12:00:50,946 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124071.08, 'new_value': 127359.22}, {'field': 'total_amount', 'old_value': 124071.08, 'new_value': 127359.22}, {'field': 'order_count', 'old_value': 236, 'new_value': 249}]
2025-06-27 12:00:50,946 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM0Z
2025-06-27 12:00:51,352 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM0Z
2025-06-27 12:00:51,352 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 441815.6, 'new_value': 443195.6}, {'field': 'total_amount', 'old_value': 497308.6, 'new_value': 498688.6}, {'field': 'order_count', 'old_value': 84, 'new_value': 85}]
2025-06-27 12:00:51,352 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM2Z
2025-06-27 12:00:51,821 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM2Z
2025-06-27 12:00:51,821 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24444.22, 'new_value': 25211.85}, {'field': 'offline_amount', 'old_value': 28114.11, 'new_value': 29191.41}, {'field': 'total_amount', 'old_value': 52558.33, 'new_value': 54403.26}, {'field': 'order_count', 'old_value': 2649, 'new_value': 2764}]
2025-06-27 12:00:51,821 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM5Z
2025-06-27 12:00:52,258 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM5Z
2025-06-27 12:00:52,258 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11042.5, 'new_value': 11077.5}, {'field': 'total_amount', 'old_value': 13392.4, 'new_value': 13427.4}, {'field': 'order_count', 'old_value': 136, 'new_value': 137}]
2025-06-27 12:00:52,258 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM8Z
2025-06-27 12:00:52,727 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM8Z
2025-06-27 12:00:52,727 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21529.0, 'new_value': 22219.0}, {'field': 'total_amount', 'old_value': 33208.0, 'new_value': 33898.0}, {'field': 'order_count', 'old_value': 5239, 'new_value': 5244}]
2025-06-27 12:00:52,727 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM9Z
2025-06-27 12:00:53,196 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM9Z
2025-06-27 12:00:53,196 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15590.0, 'new_value': 19671.0}, {'field': 'offline_amount', 'old_value': 18275.34, 'new_value': 22356.34}, {'field': 'total_amount', 'old_value': 33865.34, 'new_value': 42027.34}, {'field': 'order_count', 'old_value': 15331, 'new_value': 17877}]
2025-06-27 12:00:53,196 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAZ
2025-06-27 12:00:53,633 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMAZ
2025-06-27 12:00:53,633 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 176202.0, 'new_value': 182271.0}, {'field': 'total_amount', 'old_value': 176202.0, 'new_value': 182271.0}, {'field': 'order_count', 'old_value': 318, 'new_value': 326}]
2025-06-27 12:00:53,633 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMBZ
2025-06-27 12:00:54,118 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMBZ
2025-06-27 12:00:54,118 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10115.93, 'new_value': 10486.99}, {'field': 'offline_amount', 'old_value': 153839.62, 'new_value': 160578.8}, {'field': 'total_amount', 'old_value': 163955.55, 'new_value': 171065.79}, {'field': 'order_count', 'old_value': 1842, 'new_value': 1928}]
2025-06-27 12:00:54,118 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMCZ
2025-06-27 12:00:54,571 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMCZ
2025-06-27 12:00:54,571 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6460.4, 'new_value': 6573.4}, {'field': 'offline_amount', 'old_value': 80773.0, 'new_value': 80783.0}, {'field': 'total_amount', 'old_value': 87233.4, 'new_value': 87356.4}, {'field': 'order_count', 'old_value': 76, 'new_value': 78}]
2025-06-27 12:00:54,571 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMEZ
2025-06-27 12:00:55,024 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMEZ
2025-06-27 12:00:55,024 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 144180.71, 'new_value': 148292.71}, {'field': 'total_amount', 'old_value': 170955.42, 'new_value': 175067.42}, {'field': 'order_count', 'old_value': 30, 'new_value': 31}]
2025-06-27 12:00:55,024 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMHZ
2025-06-27 12:00:55,493 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMHZ
2025-06-27 12:00:55,493 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28935.69, 'new_value': 31251.69}, {'field': 'total_amount', 'old_value': 40624.77, 'new_value': 42940.77}, {'field': 'order_count', 'old_value': 892, 'new_value': 922}]
2025-06-27 12:00:55,493 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJZ
2025-06-27 12:00:55,915 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMJZ
2025-06-27 12:00:55,915 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11110.66, 'new_value': 11848.1}, {'field': 'total_amount', 'old_value': 11110.66, 'new_value': 11848.1}, {'field': 'order_count', 'old_value': 22, 'new_value': 24}]
2025-06-27 12:00:55,915 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMKZ
2025-06-27 12:00:56,383 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMKZ
2025-06-27 12:00:56,383 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 141598.52, 'new_value': 147170.97}, {'field': 'total_amount', 'old_value': 141598.52, 'new_value': 147170.97}, {'field': 'order_count', 'old_value': 717, 'new_value': 743}]
2025-06-27 12:00:56,383 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLZ
2025-06-27 12:00:56,805 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMLZ
2025-06-27 12:00:56,805 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28195.0, 'new_value': 29739.0}, {'field': 'total_amount', 'old_value': 28195.0, 'new_value': 29739.0}, {'field': 'order_count', 'old_value': 132, 'new_value': 138}]
2025-06-27 12:00:56,821 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPZ
2025-06-27 12:00:57,321 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMPZ
2025-06-27 12:00:57,321 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 185362.0, 'new_value': 193437.0}, {'field': 'total_amount', 'old_value': 185362.0, 'new_value': 193437.0}, {'field': 'order_count', 'old_value': 7152, 'new_value': 7458}]
2025-06-27 12:00:57,321 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMQZ
2025-06-27 12:00:57,711 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMQZ
2025-06-27 12:00:57,711 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 149929.04, 'new_value': 156057.24}, {'field': 'total_amount', 'old_value': 149929.04, 'new_value': 156057.24}, {'field': 'order_count', 'old_value': 598, 'new_value': 621}]
2025-06-27 12:00:57,711 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRZ
2025-06-27 12:00:58,102 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMRZ
2025-06-27 12:00:58,102 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 128564.5, 'new_value': 131547.5}, {'field': 'offline_amount', 'old_value': 50922.06, 'new_value': 52014.06}, {'field': 'total_amount', 'old_value': 179486.56, 'new_value': 183561.56}, {'field': 'order_count', 'old_value': 1290, 'new_value': 1323}]
2025-06-27 12:00:58,102 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMSZ
2025-06-27 12:00:58,571 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMSZ
2025-06-27 12:00:58,571 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 254513.0, 'new_value': 254711.0}, {'field': 'total_amount', 'old_value': 263375.0, 'new_value': 263573.0}, {'field': 'order_count', 'old_value': 66, 'new_value': 68}]
2025-06-27 12:00:58,571 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTZ
2025-06-27 12:00:58,977 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMTZ
2025-06-27 12:00:58,977 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 134982.51, 'new_value': 139617.31}, {'field': 'total_amount', 'old_value': 134982.51, 'new_value': 139617.31}, {'field': 'order_count', 'old_value': 4786, 'new_value': 4969}]
2025-06-27 12:00:58,977 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMUZ
2025-06-27 12:00:59,461 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMUZ
2025-06-27 12:00:59,461 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 494.0, 'new_value': 516.9}, {'field': 'offline_amount', 'old_value': 17332.0, 'new_value': 22410.0}, {'field': 'total_amount', 'old_value': 17826.0, 'new_value': 22926.9}, {'field': 'order_count', 'old_value': 14, 'new_value': 18}]
2025-06-27 12:00:59,461 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMWZ
2025-06-27 12:00:59,961 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMWZ
2025-06-27 12:00:59,961 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5263.0, 'new_value': 6263.0}, {'field': 'total_amount', 'old_value': 13631.0, 'new_value': 14631.0}, {'field': 'order_count', 'old_value': 82, 'new_value': 85}]
2025-06-27 12:00:59,961 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMXZ
2025-06-27 12:01:00,524 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMXZ
2025-06-27 12:01:00,524 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36292.5, 'new_value': 37048.4}, {'field': 'total_amount', 'old_value': 36292.5, 'new_value': 37048.4}, {'field': 'order_count', 'old_value': 32, 'new_value': 35}]
2025-06-27 12:01:00,524 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYZ
2025-06-27 12:01:00,961 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYZ
2025-06-27 12:01:00,961 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 175230.0, 'new_value': 184273.0}, {'field': 'total_amount', 'old_value': 234788.0, 'new_value': 243831.0}, {'field': 'order_count', 'old_value': 182, 'new_value': 190}]
2025-06-27 12:01:00,961 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZZ
2025-06-27 12:01:01,399 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZZ
2025-06-27 12:01:01,399 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17975.65, 'new_value': 18077.69}, {'field': 'offline_amount', 'old_value': 387974.66, 'new_value': 398716.87}, {'field': 'total_amount', 'old_value': 405950.31, 'new_value': 416794.56}, {'field': 'order_count', 'old_value': 1950, 'new_value': 2002}]
2025-06-27 12:01:01,399 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM201
2025-06-27 12:01:01,883 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM201
2025-06-27 12:01:01,883 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 33153.83, 'new_value': 35282.66}, {'field': 'offline_amount', 'old_value': 35434.42, 'new_value': 36685.68}, {'field': 'total_amount', 'old_value': 68588.25, 'new_value': 71968.34}, {'field': 'order_count', 'old_value': 3369, 'new_value': 3570}]
2025-06-27 12:01:01,883 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM301
2025-06-27 12:01:02,383 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM301
2025-06-27 12:01:02,383 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54930.0, 'new_value': 55878.0}, {'field': 'total_amount', 'old_value': 61600.0, 'new_value': 62548.0}, {'field': 'order_count', 'old_value': 510, 'new_value': 513}]
2025-06-27 12:01:02,383 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM601
2025-06-27 12:01:02,899 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM601
2025-06-27 12:01:02,899 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 115160.0, 'new_value': 121000.0}, {'field': 'total_amount', 'old_value': 115160.0, 'new_value': 121000.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-06-27 12:01:02,899 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBB
2025-06-27 12:01:03,352 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBB
2025-06-27 12:01:03,352 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 155064.63, 'new_value': 161457.63}, {'field': 'total_amount', 'old_value': 155064.63, 'new_value': 161457.63}, {'field': 'order_count', 'old_value': 4074, 'new_value': 4235}]
2025-06-27 12:01:03,352 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCB
2025-06-27 12:01:03,883 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCB
2025-06-27 12:01:03,883 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 560140.01, 'new_value': 565140.01}, {'field': 'total_amount', 'old_value': 653140.01, 'new_value': 658140.01}, {'field': 'order_count', 'old_value': 70, 'new_value': 71}]
2025-06-27 12:01:03,883 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMEB
2025-06-27 12:01:04,383 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMEB
2025-06-27 12:01:04,383 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 338545.86, 'new_value': 358011.93}, {'field': 'total_amount', 'old_value': 339872.86, 'new_value': 359338.93}, {'field': 'order_count', 'old_value': 5013, 'new_value': 5228}]
2025-06-27 12:01:04,383 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMFB
2025-06-27 12:01:04,805 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMFB
2025-06-27 12:01:04,805 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 59670.48, 'new_value': 63461.99}, {'field': 'offline_amount', 'old_value': 459303.26, 'new_value': 479103.01}, {'field': 'total_amount', 'old_value': 518973.74, 'new_value': 542565.0}, {'field': 'order_count', 'old_value': 1615, 'new_value': 1683}]
2025-06-27 12:01:04,805 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGB
2025-06-27 12:01:05,305 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGB
2025-06-27 12:01:05,305 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 127505.21, 'new_value': 134068.05}, {'field': 'total_amount', 'old_value': 127505.21, 'new_value': 134068.05}, {'field': 'order_count', 'old_value': 1610, 'new_value': 1683}]
2025-06-27 12:01:05,305 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHB
2025-06-27 12:01:05,789 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHB
2025-06-27 12:01:05,789 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5688.0, 'new_value': 5985.0}, {'field': 'total_amount', 'old_value': 5688.0, 'new_value': 5985.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 30}]
2025-06-27 12:01:05,789 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJB
2025-06-27 12:01:06,196 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJB
2025-06-27 12:01:06,196 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 67839.61, 'new_value': 69531.51}, {'field': 'total_amount', 'old_value': 67839.61, 'new_value': 69531.51}, {'field': 'order_count', 'old_value': 291, 'new_value': 300}]
2025-06-27 12:01:06,196 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKB
2025-06-27 12:01:06,649 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKB
2025-06-27 12:01:06,649 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9900.0, 'new_value': 10379.0}, {'field': 'total_amount', 'old_value': 19255.9, 'new_value': 19734.9}, {'field': 'order_count', 'old_value': 64, 'new_value': 65}]
2025-06-27 12:01:06,649 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMB
2025-06-27 12:01:07,086 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMB
2025-06-27 12:01:07,086 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13609.29, 'new_value': 13758.29}, {'field': 'offline_amount', 'old_value': 24476.83, 'new_value': 25707.83}, {'field': 'total_amount', 'old_value': 38086.12, 'new_value': 39466.12}, {'field': 'order_count', 'old_value': 1294, 'new_value': 1343}]
2025-06-27 12:01:07,086 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMNB
2025-06-27 12:01:07,555 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMNB
2025-06-27 12:01:07,555 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 96970.0, 'new_value': 106170.0}, {'field': 'total_amount', 'old_value': 96970.0, 'new_value': 106170.0}, {'field': 'order_count', 'old_value': 3941, 'new_value': 4397}]
2025-06-27 12:01:07,555 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQB
2025-06-27 12:01:08,039 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQB
2025-06-27 12:01:08,039 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 194874.3, 'new_value': 203964.49}, {'field': 'offline_amount', 'old_value': 374922.23, 'new_value': 384099.11}, {'field': 'total_amount', 'old_value': 569796.53, 'new_value': 588063.6}, {'field': 'order_count', 'old_value': 4358, 'new_value': 4542}]
2025-06-27 12:01:08,039 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRB
2025-06-27 12:01:08,586 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRB
2025-06-27 12:01:08,586 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 23454.01, 'new_value': 24181.01}, {'field': 'offline_amount', 'old_value': 3545.0, 'new_value': 3546.0}, {'field': 'total_amount', 'old_value': 26999.01, 'new_value': 27727.01}, {'field': 'order_count', 'old_value': 113, 'new_value': 118}]
2025-06-27 12:01:08,586 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSB
2025-06-27 12:01:09,039 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSB
2025-06-27 12:01:09,055 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40974.0, 'new_value': 41372.0}, {'field': 'total_amount', 'old_value': 40974.0, 'new_value': 41372.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 21}]
2025-06-27 12:01:09,055 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUB
2025-06-27 12:01:09,461 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUB
2025-06-27 12:01:09,461 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 395917.4, 'new_value': 407384.2}, {'field': 'offline_amount', 'old_value': 84934.2, 'new_value': 85014.0}, {'field': 'total_amount', 'old_value': 480851.6, 'new_value': 492398.2}, {'field': 'order_count', 'old_value': 603, 'new_value': 619}]
2025-06-27 12:01:09,461 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMVB
2025-06-27 12:01:09,883 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMVB
2025-06-27 12:01:09,883 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 379473.0, 'new_value': 427513.0}, {'field': 'total_amount', 'old_value': 379473.0, 'new_value': 427513.0}, {'field': 'order_count', 'old_value': 10399, 'new_value': 11851}]
2025-06-27 12:01:09,883 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWB
2025-06-27 12:01:10,305 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMWB
2025-06-27 12:01:10,305 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10475.0, 'new_value': 12458.0}, {'field': 'total_amount', 'old_value': 10475.0, 'new_value': 12458.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 27}]
2025-06-27 12:01:10,305 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXB
2025-06-27 12:01:10,852 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXB
2025-06-27 12:01:10,852 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29357.16, 'new_value': 30466.16}, {'field': 'total_amount', 'old_value': 29357.16, 'new_value': 30466.16}, {'field': 'order_count', 'old_value': 1096, 'new_value': 1134}]
2025-06-27 12:01:10,852 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYB
2025-06-27 12:01:11,274 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYB
2025-06-27 12:01:11,274 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21746.3, 'new_value': 22034.3}, {'field': 'total_amount', 'old_value': 21746.3, 'new_value': 22034.3}, {'field': 'order_count', 'old_value': 115, 'new_value': 118}]
2025-06-27 12:01:11,274 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0C
2025-06-27 12:01:11,711 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0C
2025-06-27 12:01:11,711 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89849.0, 'new_value': 89907.0}, {'field': 'total_amount', 'old_value': 89849.0, 'new_value': 89907.0}, {'field': 'order_count', 'old_value': 322, 'new_value': 323}]
2025-06-27 12:01:11,711 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM2C
2025-06-27 12:01:12,180 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM2C
2025-06-27 12:01:12,180 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 84189.26, 'new_value': 95549.14}, {'field': 'offline_amount', 'old_value': 180743.4, 'new_value': 205680.29}, {'field': 'total_amount', 'old_value': 264932.66, 'new_value': 301229.43}, {'field': 'order_count', 'old_value': 12294, 'new_value': 14042}]
2025-06-27 12:01:12,180 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM4C
2025-06-27 12:01:12,774 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM4C
2025-06-27 12:01:12,774 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-06-27 12:01:12,774 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5C
2025-06-27 12:01:13,258 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM5C
2025-06-27 12:01:13,258 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 103091.88, 'new_value': 108377.42}, {'field': 'offline_amount', 'old_value': 502212.98, 'new_value': 519008.76}, {'field': 'total_amount', 'old_value': 605304.86, 'new_value': 627386.18}, {'field': 'order_count', 'old_value': 1692, 'new_value': 1754}]
2025-06-27 12:01:13,258 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6C
2025-06-27 12:01:13,680 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6C
2025-06-27 12:01:13,680 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43594.0, 'new_value': 44413.0}, {'field': 'total_amount', 'old_value': 43594.0, 'new_value': 44413.0}, {'field': 'order_count', 'old_value': 62, 'new_value': 63}]
2025-06-27 12:01:13,680 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7C
2025-06-27 12:01:14,133 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM7C
2025-06-27 12:01:14,133 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104593.04, 'new_value': 109204.5}, {'field': 'total_amount', 'old_value': 104593.04, 'new_value': 109204.5}, {'field': 'order_count', 'old_value': 2954, 'new_value': 3078}]
2025-06-27 12:01:14,133 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM8C
2025-06-27 12:01:14,711 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM8C
2025-06-27 12:01:14,711 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 126905.18, 'new_value': 130382.16}, {'field': 'total_amount', 'old_value': 127909.18, 'new_value': 131386.16}, {'field': 'order_count', 'old_value': 208, 'new_value': 213}]
2025-06-27 12:01:14,711 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBC
2025-06-27 12:01:15,133 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBC
2025-06-27 12:01:15,133 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3395.0, 'new_value': 3513.0}, {'field': 'offline_amount', 'old_value': 20226.0, 'new_value': 23096.0}, {'field': 'total_amount', 'old_value': 23621.0, 'new_value': 26609.0}, {'field': 'order_count', 'old_value': 148, 'new_value': 166}]
2025-06-27 12:01:15,133 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCC
2025-06-27 12:01:15,586 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMCC
2025-06-27 12:01:15,586 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 399.0}, {'field': 'total_amount', 'old_value': 158962.71, 'new_value': 159361.71}]
2025-06-27 12:01:15,586 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMFC
2025-06-27 12:01:16,039 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMFC
2025-06-27 12:01:16,055 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50637.54, 'new_value': 54055.86}, {'field': 'total_amount', 'old_value': 56724.7, 'new_value': 60143.02}, {'field': 'order_count', 'old_value': 3648, 'new_value': 3905}]
2025-06-27 12:01:16,055 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHC
2025-06-27 12:01:16,539 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMHC
2025-06-27 12:01:16,539 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 24262.0, 'new_value': 25162.0}, {'field': 'total_amount', 'old_value': 24262.0, 'new_value': 25162.0}, {'field': 'order_count', 'old_value': 96, 'new_value': 100}]
2025-06-27 12:01:16,539 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMC
2025-06-27 12:01:17,008 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMC
2025-06-27 12:01:17,008 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 135281.0, 'new_value': 142580.0}, {'field': 'total_amount', 'old_value': 135281.0, 'new_value': 142580.0}, {'field': 'order_count', 'old_value': 185, 'new_value': 186}]
2025-06-27 12:01:17,008 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQC
2025-06-27 12:01:17,493 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQC
2025-06-27 12:01:17,493 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 366914.9, 'new_value': 380967.67}, {'field': 'total_amount', 'old_value': 366914.9, 'new_value': 380967.67}, {'field': 'order_count', 'old_value': 1170, 'new_value': 1214}]
2025-06-27 12:01:17,493 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRC
2025-06-27 12:01:17,946 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRC
2025-06-27 12:01:17,946 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43808.0, 'new_value': 46274.0}, {'field': 'total_amount', 'old_value': 44177.0, 'new_value': 46643.0}, {'field': 'order_count', 'old_value': 68, 'new_value': 72}]
2025-06-27 12:01:17,946 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSC
2025-06-27 12:01:18,399 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSC
2025-06-27 12:01:18,399 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11837.77, 'new_value': 12404.97}, {'field': 'total_amount', 'old_value': 21269.27, 'new_value': 21836.47}, {'field': 'order_count', 'old_value': 99, 'new_value': 103}]
2025-06-27 12:01:18,399 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUC
2025-06-27 12:01:18,930 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMUC
2025-06-27 12:01:18,930 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59862.0, 'new_value': 60080.0}, {'field': 'total_amount', 'old_value': 68067.85, 'new_value': 68285.85}, {'field': 'order_count', 'old_value': 1573, 'new_value': 1585}]
2025-06-27 12:01:18,930 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXC
2025-06-27 12:01:19,414 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMXC
2025-06-27 12:01:19,414 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 73887.0, 'new_value': 76887.0}, {'field': 'offline_amount', 'old_value': 23685.0, 'new_value': 24424.7}, {'field': 'total_amount', 'old_value': 97572.0, 'new_value': 101311.7}, {'field': 'order_count', 'old_value': 336, 'new_value': 348}]
2025-06-27 12:01:19,414 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYC
2025-06-27 12:01:19,899 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMYC
2025-06-27 12:01:19,899 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 347380.49, 'new_value': 357725.67}, {'field': 'total_amount', 'old_value': 347380.49, 'new_value': 357725.67}, {'field': 'order_count', 'old_value': 1757, 'new_value': 1818}]
2025-06-27 12:01:19,899 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0D
2025-06-27 12:01:20,336 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM0D
2025-06-27 12:01:20,336 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16117.0, 'new_value': 16258.0}, {'field': 'offline_amount', 'old_value': 68661.25, 'new_value': 70213.05}, {'field': 'total_amount', 'old_value': 84778.25, 'new_value': 86471.05}, {'field': 'order_count', 'old_value': 850, 'new_value': 881}]
2025-06-27 12:01:20,336 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1D
2025-06-27 12:01:20,789 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1D
2025-06-27 12:01:20,789 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6253.0, 'new_value': 6581.0}, {'field': 'offline_amount', 'old_value': 29858.4, 'new_value': 31105.3}, {'field': 'total_amount', 'old_value': 36111.4, 'new_value': 37686.3}, {'field': 'order_count', 'old_value': 1261, 'new_value': 1316}]
2025-06-27 12:01:20,789 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM3D
2025-06-27 12:01:21,305 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM3D
2025-06-27 12:01:21,305 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 25076.77, 'new_value': 26196.28}, {'field': 'offline_amount', 'old_value': 47590.36, 'new_value': 49565.86}, {'field': 'total_amount', 'old_value': 72667.13, 'new_value': 75762.14}, {'field': 'order_count', 'old_value': 2693, 'new_value': 2814}]
2025-06-27 12:01:21,305 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6D
2025-06-27 12:01:21,821 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM6D
2025-06-27 12:01:21,821 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24643.2, 'new_value': 27399.1}, {'field': 'offline_amount', 'old_value': 103057.6, 'new_value': 104257.6}, {'field': 'total_amount', 'old_value': 127700.8, 'new_value': 131656.7}, {'field': 'order_count', 'old_value': 158, 'new_value': 165}]
2025-06-27 12:01:21,821 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM8D
2025-06-27 12:01:22,289 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM8D
2025-06-27 12:01:22,289 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 18841.0, 'new_value': 20449.0}, {'field': 'total_amount', 'old_value': 18841.0, 'new_value': 20449.0}, {'field': 'order_count', 'old_value': 106, 'new_value': 115}]
2025-06-27 12:01:22,289 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9D
2025-06-27 12:01:22,743 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM9D
2025-06-27 12:01:22,743 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21736.8, 'new_value': 21781.7}, {'field': 'total_amount', 'old_value': 21736.8, 'new_value': 21781.7}, {'field': 'order_count', 'old_value': 52, 'new_value': 53}]
2025-06-27 12:01:22,743 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAD
2025-06-27 12:01:23,180 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMAD
2025-06-27 12:01:23,180 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 94568.76, 'new_value': 98250.16}, {'field': 'offline_amount', 'old_value': 137294.54, 'new_value': 139240.54}, {'field': 'total_amount', 'old_value': 231863.3, 'new_value': 237490.7}, {'field': 'order_count', 'old_value': 1284, 'new_value': 1302}]
2025-06-27 12:01:23,180 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBD
2025-06-27 12:01:23,727 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMBD
2025-06-27 12:01:23,727 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 74854.38, 'new_value': 77411.5}, {'field': 'total_amount', 'old_value': 74854.38, 'new_value': 77411.5}, {'field': 'order_count', 'old_value': 2030, 'new_value': 2109}]
2025-06-27 12:01:23,727 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDD
2025-06-27 12:01:24,180 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDD
2025-06-27 12:01:24,180 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39564.76, 'new_value': 42316.82}, {'field': 'offline_amount', 'old_value': 56343.5, 'new_value': 56440.74}, {'field': 'total_amount', 'old_value': 95908.26, 'new_value': 98757.56}, {'field': 'order_count', 'old_value': 571, 'new_value': 581}]
2025-06-27 12:01:24,180 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMFD
2025-06-27 12:01:24,633 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMFD
2025-06-27 12:01:24,633 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 57947.0, 'new_value': 58446.0}, {'field': 'total_amount', 'old_value': 57947.0, 'new_value': 58446.0}, {'field': 'order_count', 'old_value': 16, 'new_value': 17}]
2025-06-27 12:01:24,633 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGD
2025-06-27 12:01:25,196 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMGD
2025-06-27 12:01:25,196 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 17434.0, 'new_value': 18055.0}, {'field': 'total_amount', 'old_value': 17434.0, 'new_value': 18055.0}, {'field': 'order_count', 'old_value': 51, 'new_value': 56}]
2025-06-27 12:01:25,196 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKD
2025-06-27 12:01:25,696 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKD
2025-06-27 12:01:25,696 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41129.1, 'new_value': 44087.6}, {'field': 'total_amount', 'old_value': 41129.1, 'new_value': 44087.6}, {'field': 'order_count', 'old_value': 45, 'new_value': 49}]
2025-06-27 12:01:25,696 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMD
2025-06-27 12:01:26,211 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMMD
2025-06-27 12:01:26,211 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45140.0, 'new_value': 46680.0}, {'field': 'total_amount', 'old_value': 45140.0, 'new_value': 46680.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 39}]
2025-06-27 12:01:26,211 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMND
2025-06-27 12:01:26,649 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMND
2025-06-27 12:01:26,649 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 311440.72, 'new_value': 324746.69}, {'field': 'total_amount', 'old_value': 311440.72, 'new_value': 324746.69}, {'field': 'order_count', 'old_value': 9158, 'new_value': 9573}]
2025-06-27 12:01:26,649 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOD
2025-06-27 12:01:27,102 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMOD
2025-06-27 12:01:27,102 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 86751.6, 'new_value': 89142.6}, {'field': 'total_amount', 'old_value': 86751.6, 'new_value': 89142.6}, {'field': 'order_count', 'old_value': 285, 'new_value': 294}]
2025-06-27 12:01:27,102 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQD
2025-06-27 12:01:27,664 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMQD
2025-06-27 12:01:27,664 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 174876.25, 'new_value': 184696.14}, {'field': 'offline_amount', 'old_value': 148857.61, 'new_value': 157285.21}, {'field': 'total_amount', 'old_value': 323733.86, 'new_value': 341981.35}, {'field': 'order_count', 'old_value': 3065, 'new_value': 3224}]
2025-06-27 12:01:27,664 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRD
2025-06-27 12:01:28,149 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMRD
2025-06-27 12:01:28,149 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10555.0, 'new_value': 11275.0}, {'field': 'total_amount', 'old_value': 10555.0, 'new_value': 11275.0}, {'field': 'order_count', 'old_value': 55, 'new_value': 58}]
2025-06-27 12:01:28,149 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSD
2025-06-27 12:01:28,602 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMSD
2025-06-27 12:01:28,602 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 42288.0, 'new_value': 43268.0}, {'field': 'total_amount', 'old_value': 77494.0, 'new_value': 78474.0}, {'field': 'order_count', 'old_value': 95, 'new_value': 97}]
2025-06-27 12:01:28,602 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTD
2025-06-27 12:01:29,024 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMTD
2025-06-27 12:01:29,024 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6886.0, 'new_value': 16575.0}, {'field': 'total_amount', 'old_value': 6886.0, 'new_value': 16575.0}, {'field': 'order_count', 'old_value': 1387, 'new_value': 1388}]
2025-06-27 12:01:29,024 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1E
2025-06-27 12:01:29,508 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1E
2025-06-27 12:01:29,508 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87892.0, 'new_value': 101801.2}, {'field': 'total_amount', 'old_value': 87892.0, 'new_value': 101801.2}, {'field': 'order_count', 'old_value': 883, 'new_value': 884}]
2025-06-27 12:01:29,508 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTQ
2025-06-27 12:01:29,930 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTQ
2025-06-27 12:01:29,930 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 75438.26, 'new_value': 77307.08}, {'field': 'total_amount', 'old_value': 75438.26, 'new_value': 77307.08}, {'field': 'order_count', 'old_value': 2438, 'new_value': 2505}]
2025-06-27 12:01:29,930 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUQ
2025-06-27 12:01:30,367 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUQ
2025-06-27 12:01:30,367 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48612.0, 'new_value': 49271.0}, {'field': 'total_amount', 'old_value': 48612.0, 'new_value': 49271.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 34}]
2025-06-27 12:01:30,367 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWQ
2025-06-27 12:01:30,821 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWQ
2025-06-27 12:01:30,821 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 69252.0, 'new_value': 69905.9}, {'field': 'total_amount', 'old_value': 77854.5, 'new_value': 78508.4}, {'field': 'order_count', 'old_value': 66, 'new_value': 69}]
2025-06-27 12:01:30,821 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZQ
2025-06-27 12:01:31,274 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZQ
2025-06-27 12:01:31,274 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45196.0, 'new_value': 46020.0}, {'field': 'total_amount', 'old_value': 45196.0, 'new_value': 46020.0}, {'field': 'order_count', 'old_value': 111, 'new_value': 113}]
2025-06-27 12:01:31,274 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1R
2025-06-27 12:01:31,774 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1R
2025-06-27 12:01:31,774 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 150792.2, 'new_value': 160792.2}, {'field': 'total_amount', 'old_value': 178450.0, 'new_value': 188450.0}, {'field': 'order_count', 'old_value': 1594, 'new_value': 1694}]
2025-06-27 12:01:31,774 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2R
2025-06-27 12:01:32,196 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2R
2025-06-27 12:01:32,196 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 127094.0, 'new_value': 131781.45}, {'field': 'total_amount', 'old_value': 127094.0, 'new_value': 131781.45}, {'field': 'order_count', 'old_value': 3639, 'new_value': 3788}]
2025-06-27 12:01:32,196 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4R
2025-06-27 12:01:32,664 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4R
2025-06-27 12:01:32,664 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 32527.69, 'new_value': 34154.19}, {'field': 'total_amount', 'old_value': 32527.69, 'new_value': 34154.19}, {'field': 'order_count', 'old_value': 193, 'new_value': 202}]
2025-06-27 12:01:32,664 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6R
2025-06-27 12:01:33,149 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6R
2025-06-27 12:01:33,149 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 15998.0, 'new_value': 16404.0}, {'field': 'total_amount', 'old_value': 15998.0, 'new_value': 16404.0}, {'field': 'order_count', 'old_value': 273, 'new_value': 280}]
2025-06-27 12:01:33,149 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBR
2025-06-27 12:01:33,571 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMBR
2025-06-27 12:01:33,571 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 151745.0, 'new_value': 154515.0}, {'field': 'total_amount', 'old_value': 151745.0, 'new_value': 154515.0}, {'field': 'order_count', 'old_value': 573, 'new_value': 588}]
2025-06-27 12:01:33,571 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMER
2025-06-27 12:01:34,024 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMER
2025-06-27 12:01:34,024 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70327.46, 'new_value': 83634.16}, {'field': 'total_amount', 'old_value': 70327.46, 'new_value': 83634.16}, {'field': 'order_count', 'old_value': 43, 'new_value': 47}]
2025-06-27 12:01:34,024 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKR
2025-06-27 12:01:34,477 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMKR
2025-06-27 12:01:34,477 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 218518.76, 'new_value': 227702.76}, {'field': 'total_amount', 'old_value': 218518.76, 'new_value': 227702.76}, {'field': 'order_count', 'old_value': 1219, 'new_value': 1268}]
2025-06-27 12:01:34,477 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMMR
2025-06-27 12:01:34,914 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMMR
2025-06-27 12:01:34,914 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 344930.0, 'new_value': 356046.0}, {'field': 'total_amount', 'old_value': 344930.0, 'new_value': 356046.0}]
2025-06-27 12:01:34,914 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNR
2025-06-27 12:01:35,399 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNR
2025-06-27 12:01:35,399 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 97721.23, 'new_value': 102430.28}, {'field': 'offline_amount', 'old_value': 50127.68, 'new_value': 51524.93}, {'field': 'total_amount', 'old_value': 147848.91, 'new_value': 153955.21}, {'field': 'order_count', 'old_value': 8545, 'new_value': 8880}]
2025-06-27 12:01:35,399 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMOR
2025-06-27 12:01:35,914 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMOR
2025-06-27 12:01:35,914 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 105196.6, 'new_value': 107326.4}, {'field': 'total_amount', 'old_value': 105196.6, 'new_value': 107326.4}, {'field': 'order_count', 'old_value': 263, 'new_value': 269}]
2025-06-27 12:01:35,914 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQR
2025-06-27 12:01:36,367 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQR
2025-06-27 12:01:36,367 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 71929.25, 'new_value': 83632.15}, {'field': 'total_amount', 'old_value': 71929.25, 'new_value': 83632.15}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-06-27 12:01:36,367 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSR
2025-06-27 12:01:36,821 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMSR
2025-06-27 12:01:36,821 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 25654.7, 'new_value': 26922.7}, {'field': 'total_amount', 'old_value': 25654.7, 'new_value': 26922.7}, {'field': 'order_count', 'old_value': 244, 'new_value': 258}]
2025-06-27 12:01:36,821 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTR
2025-06-27 12:01:37,258 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMTR
2025-06-27 12:01:37,258 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52511.6, 'new_value': 53071.4}, {'field': 'total_amount', 'old_value': 52511.6, 'new_value': 53071.4}, {'field': 'order_count', 'old_value': 319, 'new_value': 322}]
2025-06-27 12:01:37,258 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUR
2025-06-27 12:01:37,742 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUR
2025-06-27 12:01:37,742 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 58110.69, 'new_value': 60378.32}, {'field': 'offline_amount', 'old_value': 77001.97, 'new_value': 78719.49}, {'field': 'total_amount', 'old_value': 135112.66, 'new_value': 139097.81}, {'field': 'order_count', 'old_value': 5319, 'new_value': 5496}]
2025-06-27 12:01:37,742 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYR
2025-06-27 12:01:38,180 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMYR
2025-06-27 12:01:38,180 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87159.09, 'new_value': 88890.61}, {'field': 'total_amount', 'old_value': 87159.09, 'new_value': 88890.61}, {'field': 'order_count', 'old_value': 3250, 'new_value': 3328}]
2025-06-27 12:01:38,180 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZR
2025-06-27 12:01:38,774 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZR
2025-06-27 12:01:38,774 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 378975.88, 'new_value': 388787.28}, {'field': 'total_amount', 'old_value': 378975.88, 'new_value': 388787.28}, {'field': 'order_count', 'old_value': 1997, 'new_value': 2039}]
2025-06-27 12:01:38,774 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0S
2025-06-27 12:01:39,227 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0S
2025-06-27 12:01:39,227 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 24704.19, 'new_value': 26480.19}, {'field': 'total_amount', 'old_value': 25204.19, 'new_value': 26980.19}, {'field': 'order_count', 'old_value': 113, 'new_value': 120}]
2025-06-27 12:01:39,227 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1S
2025-06-27 12:01:39,696 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1S
2025-06-27 12:01:39,696 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 41474.85, 'new_value': 43739.81}, {'field': 'offline_amount', 'old_value': 19299.16, 'new_value': 19576.06}, {'field': 'total_amount', 'old_value': 60774.01, 'new_value': 63315.87}, {'field': 'order_count', 'old_value': 2733, 'new_value': 2858}]
2025-06-27 12:01:39,696 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2S
2025-06-27 12:01:40,133 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM2S
2025-06-27 12:01:40,133 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 46534.89, 'new_value': 48191.19}, {'field': 'total_amount', 'old_value': 46543.89, 'new_value': 48200.19}, {'field': 'order_count', 'old_value': 1918, 'new_value': 1986}]
2025-06-27 12:01:40,133 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3S
2025-06-27 12:01:40,586 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3S
2025-06-27 12:01:40,586 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17468.08, 'new_value': 18408.09}, {'field': 'offline_amount', 'old_value': 10849.79, 'new_value': 10962.29}, {'field': 'total_amount', 'old_value': 28317.87, 'new_value': 29370.38}, {'field': 'order_count', 'old_value': 2291, 'new_value': 2386}]
2025-06-27 12:01:40,586 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4S
2025-06-27 12:01:41,086 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM4S
2025-06-27 12:01:41,086 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 346918.92, 'new_value': 356390.96}, {'field': 'total_amount', 'old_value': 382902.92, 'new_value': 392374.96}, {'field': 'order_count', 'old_value': 2011, 'new_value': 2071}]
2025-06-27 12:01:41,086 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5S
2025-06-27 12:01:41,539 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5S
2025-06-27 12:01:41,539 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 281182.0, 'new_value': 290145.0}, {'field': 'total_amount', 'old_value': 316182.0, 'new_value': 325145.0}, {'field': 'order_count', 'old_value': 7223, 'new_value': 7332}]
2025-06-27 12:01:41,539 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6S
2025-06-27 12:01:42,008 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6S
2025-06-27 12:01:42,008 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 73754.0, 'new_value': 76999.0}, {'field': 'total_amount', 'old_value': 76569.0, 'new_value': 79814.0}, {'field': 'order_count', 'old_value': 296, 'new_value': 310}]
2025-06-27 12:01:42,008 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7S
2025-06-27 12:01:42,477 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM7S
2025-06-27 12:01:42,477 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 620872.66, 'new_value': 636492.51}, {'field': 'total_amount', 'old_value': 620872.66, 'new_value': 636492.51}, {'field': 'order_count', 'old_value': 10664, 'new_value': 10921}]
2025-06-27 12:01:42,477 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8S
2025-06-27 12:01:42,977 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8S
2025-06-27 12:01:42,977 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 40092.25, 'new_value': 42388.66}, {'field': 'offline_amount', 'old_value': 265391.16, 'new_value': 271839.7}, {'field': 'total_amount', 'old_value': 305483.41, 'new_value': 314228.36}, {'field': 'order_count', 'old_value': 36130, 'new_value': 36314}]
2025-06-27 12:01:42,977 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9S
2025-06-27 12:01:43,430 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9S
2025-06-27 12:01:43,430 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 187198.55, 'new_value': 195663.31}, {'field': 'offline_amount', 'old_value': 42057.35, 'new_value': 43909.49}, {'field': 'total_amount', 'old_value': 229255.9, 'new_value': 239572.8}, {'field': 'order_count', 'old_value': 920, 'new_value': 959}]
2025-06-27 12:01:43,430 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFS
2025-06-27 12:01:43,914 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFS
2025-06-27 12:01:43,914 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 59429.0, 'new_value': 69808.0}, {'field': 'total_amount', 'old_value': 59429.0, 'new_value': 69808.0}, {'field': 'order_count', 'old_value': 42, 'new_value': 50}]
2025-06-27 12:01:43,914 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLS
2025-06-27 12:01:44,367 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMLS
2025-06-27 12:01:44,367 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43159.1, 'new_value': 43227.1}, {'field': 'total_amount', 'old_value': 43159.1, 'new_value': 43227.1}, {'field': 'order_count', 'old_value': 104, 'new_value': 105}]
2025-06-27 12:01:44,367 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNS
2025-06-27 12:01:44,836 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMNS
2025-06-27 12:01:44,836 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38053.0, 'new_value': 38494.0}, {'field': 'total_amount', 'old_value': 38781.0, 'new_value': 39222.0}, {'field': 'order_count', 'old_value': 144, 'new_value': 146}]
2025-06-27 12:01:44,836 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPS
2025-06-27 12:01:45,289 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMPS
2025-06-27 12:01:45,289 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 98866.0, 'new_value': 102580.0}, {'field': 'offline_amount', 'old_value': 432636.0, 'new_value': 450627.0}, {'field': 'total_amount', 'old_value': 531502.0, 'new_value': 553207.0}, {'field': 'order_count', 'old_value': 691, 'new_value': 720}]
2025-06-27 12:01:45,289 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQS
2025-06-27 12:01:45,695 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMQS
2025-06-27 12:01:45,695 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 76764.01, 'new_value': 81217.32}, {'field': 'offline_amount', 'old_value': 319215.79, 'new_value': 328478.81}, {'field': 'total_amount', 'old_value': 395979.8, 'new_value': 409696.13}, {'field': 'order_count', 'old_value': 4559, 'new_value': 4687}]
2025-06-27 12:01:45,695 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMRS
2025-06-27 12:01:46,133 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMRS
2025-06-27 12:01:46,133 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9947.82, 'new_value': 10260.27}, {'field': 'offline_amount', 'old_value': 31194.29, 'new_value': 32094.03}, {'field': 'total_amount', 'old_value': 41142.11, 'new_value': 42354.3}, {'field': 'order_count', 'old_value': 1440, 'new_value': 1482}]
2025-06-27 12:01:46,133 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUS
2025-06-27 12:01:46,727 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMUS
2025-06-27 12:01:46,727 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 37712.76, 'new_value': 38837.22}, {'field': 'offline_amount', 'old_value': 33295.94, 'new_value': 34295.94}, {'field': 'total_amount', 'old_value': 71008.7, 'new_value': 73133.16}, {'field': 'order_count', 'old_value': 3387, 'new_value': 3503}]
2025-06-27 12:01:46,727 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVS
2025-06-27 12:01:47,180 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMVS
2025-06-27 12:01:47,180 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 230681.6, 'new_value': 233361.6}, {'field': 'total_amount', 'old_value': 230681.6, 'new_value': 233361.6}, {'field': 'order_count', 'old_value': 55, 'new_value': 57}]
2025-06-27 12:01:47,180 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWS
2025-06-27 12:01:47,680 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWS
2025-06-27 12:01:47,680 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 78230.35, 'new_value': 81102.48}, {'field': 'offline_amount', 'old_value': 31309.41, 'new_value': 32278.81}, {'field': 'total_amount', 'old_value': 109539.76, 'new_value': 113381.29}, {'field': 'order_count', 'old_value': 6312, 'new_value': 6503}]
2025-06-27 12:01:47,680 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMXS
2025-06-27 12:01:48,164 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMXS
2025-06-27 12:01:48,164 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 196852.0, 'new_value': 202832.0}, {'field': 'total_amount', 'old_value': 196852.0, 'new_value': 202832.0}, {'field': 'order_count', 'old_value': 35, 'new_value': 36}]
2025-06-27 12:01:48,164 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZS
2025-06-27 12:01:48,649 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMZS
2025-06-27 12:01:48,649 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 463351.27, 'new_value': 477903.58}, {'field': 'total_amount', 'old_value': 463351.27, 'new_value': 477903.58}, {'field': 'order_count', 'old_value': 6752, 'new_value': 6984}]
2025-06-27 12:01:48,649 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0T
2025-06-27 12:01:49,070 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM0T
2025-06-27 12:01:49,070 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1626459.41, 'new_value': 1690760.41}, {'field': 'total_amount', 'old_value': 1684938.11, 'new_value': 1749239.11}, {'field': 'order_count', 'old_value': 3215, 'new_value': 3355}]
2025-06-27 12:01:49,070 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1T
2025-06-27 12:01:49,477 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1T
2025-06-27 12:01:49,477 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30237.73, 'new_value': 32221.33}, {'field': 'total_amount', 'old_value': 30237.73, 'new_value': 32221.33}, {'field': 'order_count', 'old_value': 54, 'new_value': 58}]
2025-06-27 12:01:49,477 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3T
2025-06-27 12:01:49,930 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM3T
2025-06-27 12:01:49,930 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38142.3, 'new_value': 39373.0}, {'field': 'total_amount', 'old_value': 39549.3, 'new_value': 40780.0}, {'field': 'order_count', 'old_value': 129, 'new_value': 133}]
2025-06-27 12:01:49,930 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5T
2025-06-27 12:01:50,414 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM5T
2025-06-27 12:01:50,414 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 149627.21, 'new_value': 152898.35}, {'field': 'offline_amount', 'old_value': 102577.0, 'new_value': 105610.0}, {'field': 'total_amount', 'old_value': 252204.21, 'new_value': 258508.35}, {'field': 'order_count', 'old_value': 2572, 'new_value': 2642}]
2025-06-27 12:01:50,414 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6T
2025-06-27 12:01:50,883 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM6T
2025-06-27 12:01:50,883 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 571455.83, 'new_value': 593261.63}, {'field': 'total_amount', 'old_value': 571771.19, 'new_value': 593576.99}, {'field': 'order_count', 'old_value': 1529, 'new_value': 1588}]
2025-06-27 12:01:50,883 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8T
2025-06-27 12:01:51,367 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM8T
2025-06-27 12:01:51,367 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 195545.44, 'new_value': 204199.27}, {'field': 'total_amount', 'old_value': 255226.35, 'new_value': 263880.18}, {'field': 'order_count', 'old_value': 11365, 'new_value': 11793}]
2025-06-27 12:01:51,367 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9T
2025-06-27 12:01:51,883 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM9T
2025-06-27 12:01:51,883 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 78110.02, 'new_value': 80972.37}, {'field': 'offline_amount', 'old_value': 63079.98, 'new_value': 64425.33}, {'field': 'total_amount', 'old_value': 141190.0, 'new_value': 145397.7}, {'field': 'order_count', 'old_value': 6606, 'new_value': 6796}]
2025-06-27 12:01:51,883 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMAT
2025-06-27 12:01:52,352 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMAT
2025-06-27 12:01:52,352 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 276340.4, 'new_value': 287616.1}, {'field': 'total_amount', 'old_value': 276340.4, 'new_value': 287616.1}, {'field': 'order_count', 'old_value': 8360, 'new_value': 8733}]
2025-06-27 12:01:52,352 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCT
2025-06-27 12:01:52,742 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMCT
2025-06-27 12:01:52,742 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45206.4, 'new_value': 46349.4}, {'field': 'total_amount', 'old_value': 45206.4, 'new_value': 46349.4}, {'field': 'order_count', 'old_value': 607, 'new_value': 617}]
2025-06-27 12:01:52,742 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDT
2025-06-27 12:01:53,227 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDT
2025-06-27 12:01:53,227 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 260378.4, 'new_value': 269378.4}, {'field': 'total_amount', 'old_value': 260378.4, 'new_value': 269378.4}, {'field': 'order_count', 'old_value': 51, 'new_value': 54}]
2025-06-27 12:01:53,227 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMET
2025-06-27 12:01:53,680 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMET
2025-06-27 12:01:53,680 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 118709.0, 'new_value': 127457.0}, {'field': 'total_amount', 'old_value': 118709.0, 'new_value': 127457.0}, {'field': 'order_count', 'old_value': 31, 'new_value': 33}]
2025-06-27 12:01:53,680 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFT
2025-06-27 12:01:54,211 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMFT
2025-06-27 12:01:54,211 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 670366.0, 'new_value': 687039.0}, {'field': 'total_amount', 'old_value': 670366.0, 'new_value': 687039.0}, {'field': 'order_count', 'old_value': 3261, 'new_value': 3361}]
2025-06-27 12:01:54,211 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHT
2025-06-27 12:01:54,664 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMHT
2025-06-27 12:01:54,664 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 252057.51, 'new_value': 252857.51}, {'field': 'offline_amount', 'old_value': 82091.62, 'new_value': 86121.36}, {'field': 'total_amount', 'old_value': 334149.13, 'new_value': 338978.87}, {'field': 'order_count', 'old_value': 666, 'new_value': 676}]
2025-06-27 12:01:54,664 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9K
2025-06-27 12:01:55,055 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9K
2025-06-27 12:01:55,055 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 12025.58, 'new_value': 12586.44}, {'field': 'offline_amount', 'old_value': 29594.22, 'new_value': 30068.04}, {'field': 'total_amount', 'old_value': 41619.8, 'new_value': 42654.48}, {'field': 'order_count', 'old_value': 2116, 'new_value': 2186}]
2025-06-27 12:01:55,055 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBK
2025-06-27 12:01:55,602 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBK
2025-06-27 12:01:55,602 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 482653.0, 'new_value': 499476.0}, {'field': 'total_amount', 'old_value': 482653.0, 'new_value': 499476.0}, {'field': 'order_count', 'old_value': 502, 'new_value': 529}]
2025-06-27 12:01:55,602 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDK
2025-06-27 12:01:56,133 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDK
2025-06-27 12:01:56,133 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 34909.97, 'new_value': 37634.04}, {'field': 'total_amount', 'old_value': 37121.21, 'new_value': 39845.28}, {'field': 'order_count', 'old_value': 158, 'new_value': 168}]
2025-06-27 12:01:56,133 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEK
2025-06-27 12:01:56,555 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEK
2025-06-27 12:01:56,555 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15397.0, 'new_value': 15712.0}, {'field': 'total_amount', 'old_value': 15397.0, 'new_value': 15712.0}, {'field': 'order_count', 'old_value': 172, 'new_value': 178}]
2025-06-27 12:01:56,555 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGK
2025-06-27 12:01:57,024 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGK
2025-06-27 12:01:57,024 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 28796.01, 'new_value': 29919.21}, {'field': 'offline_amount', 'old_value': 26465.37, 'new_value': 27592.45}, {'field': 'total_amount', 'old_value': 55261.38, 'new_value': 57511.66}, {'field': 'order_count', 'old_value': 2562, 'new_value': 2656}]
2025-06-27 12:01:57,024 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIK
2025-06-27 12:01:57,477 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMIK
2025-06-27 12:01:57,492 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 132651.72, 'new_value': 139079.95}, {'field': 'total_amount', 'old_value': 154433.86, 'new_value': 160862.09}, {'field': 'order_count', 'old_value': 7560, 'new_value': 7820}]
2025-06-27 12:01:57,492 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJK
2025-06-27 12:01:57,945 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJK
2025-06-27 12:01:57,945 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 109891.0, 'new_value': 112836.5}, {'field': 'offline_amount', 'old_value': 130005.3, 'new_value': 130905.9}, {'field': 'total_amount', 'old_value': 239896.3, 'new_value': 243742.4}, {'field': 'order_count', 'old_value': 4350, 'new_value': 4389}]
2025-06-27 12:01:57,945 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKK
2025-06-27 12:01:58,430 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKK
2025-06-27 12:01:58,430 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57350.84, 'new_value': 59204.31}, {'field': 'offline_amount', 'old_value': 215185.29, 'new_value': 220649.31}, {'field': 'total_amount', 'old_value': 272536.13, 'new_value': 279853.62}, {'field': 'order_count', 'old_value': 6162, 'new_value': 6363}]
2025-06-27 12:01:58,430 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNK
2025-06-27 12:01:58,867 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNK
2025-06-27 12:01:58,867 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6078.97, 'new_value': 6321.55}, {'field': 'offline_amount', 'old_value': 345618.47, 'new_value': 355297.07}, {'field': 'total_amount', 'old_value': 351697.44, 'new_value': 361618.62}, {'field': 'order_count', 'old_value': 17507, 'new_value': 18024}]
2025-06-27 12:01:58,867 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQK
2025-06-27 12:01:59,305 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQK
2025-06-27 12:01:59,305 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 57880.55, 'new_value': 60240.79}, {'field': 'offline_amount', 'old_value': 37190.84, 'new_value': 38677.78}, {'field': 'total_amount', 'old_value': 95071.39, 'new_value': 98918.57}, {'field': 'order_count', 'old_value': 5617, 'new_value': 5862}]
2025-06-27 12:01:59,305 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSK
2025-06-27 12:01:59,805 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSK
2025-06-27 12:01:59,805 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22754.0, 'new_value': 23820.0}, {'field': 'total_amount', 'old_value': 22754.0, 'new_value': 23820.0}, {'field': 'order_count', 'old_value': 99, 'new_value': 102}]
2025-06-27 12:01:59,805 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVK
2025-06-27 12:02:00,242 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVK
2025-06-27 12:02:00,242 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 88900.74, 'new_value': 93502.13}, {'field': 'offline_amount', 'old_value': 91843.52, 'new_value': 95912.61}, {'field': 'total_amount', 'old_value': 180744.26, 'new_value': 189414.74}, {'field': 'order_count', 'old_value': 7463, 'new_value': 7797}]
2025-06-27 12:02:00,242 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXK
2025-06-27 12:02:00,664 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXK
2025-06-27 12:02:00,664 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29996.58, 'new_value': 30694.8}, {'field': 'offline_amount', 'old_value': 202828.7, 'new_value': 209055.7}, {'field': 'total_amount', 'old_value': 232825.28, 'new_value': 239750.5}, {'field': 'order_count', 'old_value': 7544, 'new_value': 7767}]
2025-06-27 12:02:00,664 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYK
2025-06-27 12:02:01,133 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYK
2025-06-27 12:02:01,133 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 110579.0, 'new_value': 111298.0}, {'field': 'total_amount', 'old_value': 110579.0, 'new_value': 111298.0}, {'field': 'order_count', 'old_value': 70, 'new_value': 71}]
2025-06-27 12:02:01,133 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZK
2025-06-27 12:02:01,539 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZK
2025-06-27 12:02:01,539 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120240.0, 'new_value': 124772.0}, {'field': 'total_amount', 'old_value': 120240.0, 'new_value': 124772.0}, {'field': 'order_count', 'old_value': 441, 'new_value': 459}]
2025-06-27 12:02:01,539 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1L
2025-06-27 12:02:01,961 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1L
2025-06-27 12:02:01,961 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 292106.8, 'new_value': 298987.5}, {'field': 'offline_amount', 'old_value': 109371.6, 'new_value': 113628.3}, {'field': 'total_amount', 'old_value': 401478.4, 'new_value': 412615.8}, {'field': 'order_count', 'old_value': 1246, 'new_value': 1280}]
2025-06-27 12:02:01,961 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM5L
2025-06-27 12:02:02,430 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM5L
2025-06-27 12:02:02,430 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 248220.0, 'new_value': 259056.0}, {'field': 'total_amount', 'old_value': 248220.0, 'new_value': 259056.0}, {'field': 'order_count', 'old_value': 20685, 'new_value': 21588}]
2025-06-27 12:02:02,430 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9L
2025-06-27 12:02:02,898 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9L
2025-06-27 12:02:02,898 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 112829.6, 'new_value': 117665.92}, {'field': 'offline_amount', 'old_value': 231999.34, 'new_value': 238614.25}, {'field': 'total_amount', 'old_value': 344828.94, 'new_value': 356280.17}, {'field': 'order_count', 'old_value': 12400, 'new_value': 12866}]
2025-06-27 12:02:02,898 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBL
2025-06-27 12:02:03,414 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBL
2025-06-27 12:02:03,414 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38762.5, 'new_value': 40406.5}, {'field': 'total_amount', 'old_value': 39966.02, 'new_value': 41610.02}, {'field': 'order_count', 'old_value': 268, 'new_value': 278}]
2025-06-27 12:02:03,414 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFL
2025-06-27 12:02:03,945 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFL
2025-06-27 12:02:03,945 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 171820.0, 'new_value': 178992.0}, {'field': 'total_amount', 'old_value': 171820.0, 'new_value': 178992.0}, {'field': 'order_count', 'old_value': 114, 'new_value': 132}]
2025-06-27 12:02:03,945 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGL
2025-06-27 12:02:04,430 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGL
2025-06-27 12:02:04,430 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 140766.99, 'new_value': 147350.57}, {'field': 'offline_amount', 'old_value': 302348.23, 'new_value': 305636.42}, {'field': 'total_amount', 'old_value': 443115.22, 'new_value': 452986.99}, {'field': 'order_count', 'old_value': 3730, 'new_value': 3898}]
2025-06-27 12:02:04,430 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMHL
2025-06-27 12:02:04,945 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMHL
2025-06-27 12:02:04,945 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 113556.0, 'new_value': 117956.0}, {'field': 'total_amount', 'old_value': 114056.0, 'new_value': 118456.0}, {'field': 'order_count', 'old_value': 10, 'new_value': 11}]
2025-06-27 12:02:04,945 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJL
2025-06-27 12:02:05,352 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJL
2025-06-27 12:02:05,352 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54175.0, 'new_value': 54313.0}, {'field': 'total_amount', 'old_value': 54175.0, 'new_value': 54313.0}, {'field': 'order_count', 'old_value': 19, 'new_value': 20}]
2025-06-27 12:02:05,352 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMML
2025-06-27 12:02:05,867 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMML
2025-06-27 12:02:05,867 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 215681.02, 'new_value': 223377.92}, {'field': 'total_amount', 'old_value': 215681.02, 'new_value': 223377.92}, {'field': 'order_count', 'old_value': 774, 'new_value': 806}]
2025-06-27 12:02:05,867 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPL
2025-06-27 12:02:06,305 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPL
2025-06-27 12:02:06,305 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52848.0, 'new_value': 53736.0}, {'field': 'total_amount', 'old_value': 55460.0, 'new_value': 56348.0}, {'field': 'order_count', 'old_value': 129, 'new_value': 131}]
2025-06-27 12:02:06,305 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQL
2025-06-27 12:02:06,820 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQL
2025-06-27 12:02:06,820 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 11324.0, 'new_value': 11663.0}, {'field': 'total_amount', 'old_value': 11324.0, 'new_value': 11663.0}, {'field': 'order_count', 'old_value': 28, 'new_value': 29}]
2025-06-27 12:02:06,820 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRL
2025-06-27 12:02:07,273 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMRL
2025-06-27 12:02:07,273 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 150381.97, 'new_value': 154565.83}, {'field': 'total_amount', 'old_value': 150381.97, 'new_value': 154565.83}, {'field': 'order_count', 'old_value': 6688, 'new_value': 6867}]
2025-06-27 12:02:07,273 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVL
2025-06-27 12:02:07,742 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMVL
2025-06-27 12:02:07,742 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 339209.16, 'new_value': 347347.58}, {'field': 'total_amount', 'old_value': 339209.16, 'new_value': 347347.58}, {'field': 'order_count', 'old_value': 1164, 'new_value': 1202}]
2025-06-27 12:02:07,742 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWL
2025-06-27 12:02:08,133 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWL
2025-06-27 12:02:08,133 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 736.9, 'new_value': 895.9}, {'field': 'offline_amount', 'old_value': 56795.0, 'new_value': 58087.7}, {'field': 'total_amount', 'old_value': 57531.9, 'new_value': 58983.6}, {'field': 'order_count', 'old_value': 417, 'new_value': 426}]
2025-06-27 12:02:08,133 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXL
2025-06-27 12:02:08,648 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMXL
2025-06-27 12:02:08,648 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 213792.19, 'new_value': 219897.45}, {'field': 'total_amount', 'old_value': 213792.19, 'new_value': 219897.45}, {'field': 'order_count', 'old_value': 1725, 'new_value': 1776}]
2025-06-27 12:02:08,648 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZL
2025-06-27 12:02:09,133 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZL
2025-06-27 12:02:09,133 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 159700.37, 'new_value': 177337.34}, {'field': 'total_amount', 'old_value': 159700.37, 'new_value': 177337.34}, {'field': 'order_count', 'old_value': 3550, 'new_value': 3967}]
2025-06-27 12:02:09,133 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0M
2025-06-27 12:02:09,617 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM0M
2025-06-27 12:02:09,617 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 163622.0, 'new_value': 172124.0}, {'field': 'total_amount', 'old_value': 172241.0, 'new_value': 180743.0}, {'field': 'order_count', 'old_value': 12611, 'new_value': 13291}]
2025-06-27 12:02:09,617 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1M
2025-06-27 12:02:10,070 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM1M
2025-06-27 12:02:10,070 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 81547.54, 'new_value': 84539.32}, {'field': 'offline_amount', 'old_value': 866396.27, 'new_value': 896911.67}, {'field': 'total_amount', 'old_value': 947943.81, 'new_value': 981450.99}, {'field': 'order_count', 'old_value': 3960, 'new_value': 4119}]
2025-06-27 12:02:10,070 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM2M
2025-06-27 12:02:10,508 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM2M
2025-06-27 12:02:10,508 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 241879.52, 'new_value': 243221.52}, {'field': 'offline_amount', 'old_value': 221363.79, 'new_value': 225363.79}, {'field': 'total_amount', 'old_value': 463243.31, 'new_value': 468585.31}, {'field': 'order_count', 'old_value': 3086, 'new_value': 3108}]
2025-06-27 12:02:10,508 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM4M
2025-06-27 12:02:10,945 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM4M
2025-06-27 12:02:10,945 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 253735.97, 'new_value': 259186.72}, {'field': 'total_amount', 'old_value': 253735.97, 'new_value': 259186.72}, {'field': 'order_count', 'old_value': 5359, 'new_value': 5476}]
2025-06-27 12:02:10,945 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM5M
2025-06-27 12:02:11,414 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM5M
2025-06-27 12:02:11,414 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 67222.0, 'new_value': 74763.0}, {'field': 'offline_amount', 'old_value': 19751.0, 'new_value': 22974.0}, {'field': 'total_amount', 'old_value': 86973.0, 'new_value': 97737.0}, {'field': 'order_count', 'old_value': 5123, 'new_value': 5748}]
2025-06-27 12:02:11,414 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM6M
2025-06-27 12:02:11,883 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM6M
2025-06-27 12:02:11,883 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 27695.92, 'new_value': 28947.92}, {'field': 'total_amount', 'old_value': 28253.92, 'new_value': 29505.92}, {'field': 'order_count', 'old_value': 66, 'new_value': 69}]
2025-06-27 12:02:11,883 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8M
2025-06-27 12:02:12,367 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM8M
2025-06-27 12:02:12,367 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 9113.0, 'new_value': 10454.0}, {'field': 'offline_amount', 'old_value': 2383.0, 'new_value': 2408.0}, {'field': 'total_amount', 'old_value': 11496.0, 'new_value': 12862.0}, {'field': 'order_count', 'old_value': 110, 'new_value': 119}]
2025-06-27 12:02:12,367 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9M
2025-06-27 12:02:12,852 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBM9M
2025-06-27 12:02:12,852 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 393569.93, 'new_value': 403542.16}, {'field': 'total_amount', 'old_value': 393569.93, 'new_value': 403542.16}, {'field': 'order_count', 'old_value': 16058, 'new_value': 16491}]
2025-06-27 12:02:12,852 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMAM
2025-06-27 12:02:13,305 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMAM
2025-06-27 12:02:13,305 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1750000.0, 'new_value': 1800000.0}, {'field': 'total_amount', 'old_value': 1850000.0, 'new_value': 1900000.0}, {'field': 'order_count', 'old_value': 377, 'new_value': 378}]
2025-06-27 12:02:13,305 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBM
2025-06-27 12:02:13,773 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMBM
2025-06-27 12:02:13,773 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 140221.9, 'new_value': 143586.9}, {'field': 'total_amount', 'old_value': 140221.9, 'new_value': 143586.9}, {'field': 'order_count', 'old_value': 4464, 'new_value': 4562}]
2025-06-27 12:02:13,773 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCM
2025-06-27 12:02:14,227 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCM
2025-06-27 12:02:14,227 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 244230.28, 'new_value': 249855.9}, {'field': 'total_amount', 'old_value': 244230.28, 'new_value': 249855.9}, {'field': 'order_count', 'old_value': 1535, 'new_value': 1580}]
2025-06-27 12:02:14,227 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDM
2025-06-27 12:02:14,727 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMDM
2025-06-27 12:02:14,727 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 151670.0, 'new_value': 159225.0}, {'field': 'offline_amount', 'old_value': 102922.0, 'new_value': 106650.0}, {'field': 'total_amount', 'old_value': 254592.0, 'new_value': 265875.0}, {'field': 'order_count', 'old_value': 3655, 'new_value': 3817}]
2025-06-27 12:02:14,727 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEM
2025-06-27 12:02:15,195 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMEM
2025-06-27 12:02:15,195 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1518242.3, 'new_value': 1579434.46}, {'field': 'offline_amount', 'old_value': 337137.8, 'new_value': 349443.8}, {'field': 'total_amount', 'old_value': 1855380.1, 'new_value': 1928878.26}, {'field': 'order_count', 'old_value': 6884, 'new_value': 7169}]
2025-06-27 12:02:15,195 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGM
2025-06-27 12:02:15,758 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMGM
2025-06-27 12:02:15,758 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26426.94, 'new_value': 27200.94}, {'field': 'offline_amount', 'old_value': 31531.46, 'new_value': 31665.46}, {'field': 'total_amount', 'old_value': 57958.4, 'new_value': 58866.4}, {'field': 'order_count', 'old_value': 6698, 'new_value': 6703}]
2025-06-27 12:02:15,758 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMHM
2025-06-27 12:02:16,273 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMHM
2025-06-27 12:02:16,273 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 49435.43, 'new_value': 51203.24}, {'field': 'offline_amount', 'old_value': 457561.34, 'new_value': 470760.08}, {'field': 'total_amount', 'old_value': 506996.77, 'new_value': 521963.32}, {'field': 'order_count', 'old_value': 2249, 'new_value': 2319}]
2025-06-27 12:02:16,273 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJM
2025-06-27 12:02:16,852 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMJM
2025-06-27 12:02:16,852 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 175681.9, 'new_value': 193299.2}, {'field': 'total_amount', 'old_value': 309346.4, 'new_value': 326963.7}, {'field': 'order_count', 'old_value': 8372, 'new_value': 8892}]
2025-06-27 12:02:16,852 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKM
2025-06-27 12:02:17,273 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMKM
2025-06-27 12:02:17,273 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 50000.0, 'new_value': 60000.0}, {'field': 'total_amount', 'old_value': 50000.0, 'new_value': 60000.0}, {'field': 'order_count', 'old_value': 9, 'new_value': 11}]
2025-06-27 12:02:17,273 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNM
2025-06-27 12:02:17,711 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMNM
2025-06-27 12:02:17,711 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29790.0, 'new_value': 30069.0}, {'field': 'total_amount', 'old_value': 29790.0, 'new_value': 30069.0}, {'field': 'order_count', 'old_value': 47, 'new_value': 48}]
2025-06-27 12:02:17,711 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOM
2025-06-27 12:02:18,164 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMOM
2025-06-27 12:02:18,164 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 321871.6, 'new_value': 325551.4}, {'field': 'total_amount', 'old_value': 321871.6, 'new_value': 325551.4}, {'field': 'order_count', 'old_value': 374, 'new_value': 379}]
2025-06-27 12:02:18,164 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPM
2025-06-27 12:02:18,664 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMPM
2025-06-27 12:02:18,664 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 363088.4, 'new_value': 368998.8}, {'field': 'total_amount', 'old_value': 494705.6, 'new_value': 500616.0}, {'field': 'order_count', 'old_value': 3565, 'new_value': 3570}]
2025-06-27 12:02:18,664 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQM
2025-06-27 12:02:19,195 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMQM
2025-06-27 12:02:19,195 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 32544.65, 'new_value': 34815.45}, {'field': 'offline_amount', 'old_value': 28705.3, 'new_value': 29474.1}, {'field': 'total_amount', 'old_value': 61249.95, 'new_value': 64289.55}, {'field': 'order_count', 'old_value': 331, 'new_value': 345}]
2025-06-27 12:02:19,195 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSM
2025-06-27 12:02:19,617 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMSM
2025-06-27 12:02:19,617 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 318891.06, 'new_value': 327765.73}, {'field': 'total_amount', 'old_value': 318891.06, 'new_value': 327765.73}, {'field': 'order_count', 'old_value': 3042, 'new_value': 3152}]
2025-06-27 12:02:19,617 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTM
2025-06-27 12:02:20,148 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTM
2025-06-27 12:02:20,148 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 53209.49, 'new_value': 54381.95}, {'field': 'offline_amount', 'old_value': 350267.01, 'new_value': 358431.91}, {'field': 'total_amount', 'old_value': 403476.5, 'new_value': 412813.86}, {'field': 'order_count', 'old_value': 3345, 'new_value': 3439}]
2025-06-27 12:02:20,148 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUM
2025-06-27 12:02:20,711 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMUM
2025-06-27 12:02:20,711 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 169540.55, 'new_value': 177263.7}, {'field': 'offline_amount', 'old_value': 253979.64, 'new_value': 266059.12}, {'field': 'total_amount', 'old_value': 423520.19, 'new_value': 443322.82}, {'field': 'order_count', 'old_value': 3837, 'new_value': 4009}]
2025-06-27 12:02:20,711 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYM
2025-06-27 12:02:21,226 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYM
2025-06-27 12:02:21,226 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 46008.01, 'new_value': 48224.31}, {'field': 'offline_amount', 'old_value': 56075.09, 'new_value': 57346.59}, {'field': 'total_amount', 'old_value': 102083.1, 'new_value': 105570.9}, {'field': 'order_count', 'old_value': 2577, 'new_value': 2667}]
2025-06-27 12:02:21,226 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZM
2025-06-27 12:02:21,742 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMZM
2025-06-27 12:02:21,742 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 108080.0, 'new_value': 112517.0}, {'field': 'total_amount', 'old_value': 108080.0, 'new_value': 112517.0}, {'field': 'order_count', 'old_value': 273, 'new_value': 283}]
2025-06-27 12:02:21,742 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO01
2025-06-27 12:02:22,211 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO01
2025-06-27 12:02:22,211 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 284305.57, 'new_value': 292987.4}, {'field': 'offline_amount', 'old_value': 29793.49, 'new_value': 30013.39}, {'field': 'total_amount', 'old_value': 314099.06, 'new_value': 323000.79}, {'field': 'order_count', 'old_value': 11914, 'new_value': 12205}]
2025-06-27 12:02:22,211 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ01
2025-06-27 12:02:22,633 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ01
2025-06-27 12:02:22,633 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 10460000.0, 'new_value': 10860000.0}, {'field': 'total_amount', 'old_value': 10460000.0, 'new_value': 10860000.0}, {'field': 'order_count', 'old_value': 55, 'new_value': 57}]
2025-06-27 12:02:22,633 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR01
2025-06-27 12:02:23,086 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR01
2025-06-27 12:02:23,086 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 118725.0, 'new_value': 125551.0}, {'field': 'offline_amount', 'old_value': 113696.0, 'new_value': 118369.0}, {'field': 'total_amount', 'old_value': 232421.0, 'new_value': 243920.0}, {'field': 'order_count', 'old_value': 9139, 'new_value': 9626}]
2025-06-27 12:02:23,086 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT01
2025-06-27 12:02:23,680 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT01
2025-06-27 12:02:23,680 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 795695.0, 'new_value': 835253.0}, {'field': 'total_amount', 'old_value': 795695.0, 'new_value': 835253.0}, {'field': 'order_count', 'old_value': 1021, 'new_value': 1055}]
2025-06-27 12:02:23,680 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU01
2025-06-27 12:02:24,242 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU01
2025-06-27 12:02:24,242 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 93772.8, 'new_value': 98810.3}, {'field': 'total_amount', 'old_value': 93772.8, 'new_value': 98810.3}, {'field': 'order_count', 'old_value': 428, 'new_value': 455}]
2025-06-27 12:02:24,242 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW01
2025-06-27 12:02:24,742 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW01
2025-06-27 12:02:24,742 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39828.86, 'new_value': 41585.99}, {'field': 'offline_amount', 'old_value': 45509.84, 'new_value': 47222.79}, {'field': 'total_amount', 'old_value': 85338.7, 'new_value': 88808.78}, {'field': 'order_count', 'old_value': 4435, 'new_value': 4623}]
2025-06-27 12:02:24,742 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX01
2025-06-27 12:02:25,211 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX01
2025-06-27 12:02:25,211 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 125098.0, 'new_value': 129256.0}, {'field': 'total_amount', 'old_value': 125098.0, 'new_value': 129256.0}, {'field': 'order_count', 'old_value': 225, 'new_value': 232}]
2025-06-27 12:02:25,211 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM011
2025-06-27 12:02:25,695 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM011
2025-06-27 12:02:25,695 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3834.0, 'new_value': 3974.0}, {'field': 'offline_amount', 'old_value': 24795.4, 'new_value': 25265.2}, {'field': 'total_amount', 'old_value': 28629.4, 'new_value': 29239.2}, {'field': 'order_count', 'old_value': 974, 'new_value': 997}]
2025-06-27 12:02:25,695 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM111
2025-06-27 12:02:26,180 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM111
2025-06-27 12:02:26,180 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72772.76, 'new_value': 83105.71}, {'field': 'total_amount', 'old_value': 72772.76, 'new_value': 83105.71}, {'field': 'order_count', 'old_value': 8198, 'new_value': 9401}]
2025-06-27 12:02:26,180 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM211
2025-06-27 12:02:26,711 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM211
2025-06-27 12:02:26,711 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 138432.2, 'new_value': 142110.9}, {'field': 'total_amount', 'old_value': 138432.2, 'new_value': 142110.9}, {'field': 'order_count', 'old_value': 660, 'new_value': 677}]
2025-06-27 12:02:26,711 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM311
2025-06-27 12:02:27,195 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM311
2025-06-27 12:02:27,195 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 228760.0, 'new_value': 229360.0}, {'field': 'total_amount', 'old_value': 228760.0, 'new_value': 229360.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 46}]
2025-06-27 12:02:27,195 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM411
2025-06-27 12:02:27,711 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM411
2025-06-27 12:02:27,711 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47691.5, 'new_value': 48406.5}, {'field': 'total_amount', 'old_value': 47691.5, 'new_value': 48406.5}, {'field': 'order_count', 'old_value': 61, 'new_value': 62}]
2025-06-27 12:02:27,711 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM611
2025-06-27 12:02:28,164 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM611
2025-06-27 12:02:28,164 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16051.66, 'new_value': 16507.71}, {'field': 'offline_amount', 'old_value': 284950.1, 'new_value': 294521.1}, {'field': 'total_amount', 'old_value': 301001.76, 'new_value': 311028.81}, {'field': 'order_count', 'old_value': 1583, 'new_value': 1642}]
2025-06-27 12:02:28,164 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM911
2025-06-27 12:02:28,648 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM911
2025-06-27 12:02:28,648 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 17295.93, 'new_value': 17553.95}, {'field': 'offline_amount', 'old_value': 141527.0, 'new_value': 142807.0}, {'field': 'total_amount', 'old_value': 158822.93, 'new_value': 160360.95}, {'field': 'order_count', 'old_value': 84, 'new_value': 86}]
2025-06-27 12:02:28,648 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB11
2025-06-27 12:02:29,148 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB11
2025-06-27 12:02:29,148 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 147672.31, 'new_value': 154235.53}, {'field': 'offline_amount', 'old_value': 203455.19, 'new_value': 209907.96}, {'field': 'total_amount', 'old_value': 351127.5, 'new_value': 364143.49}, {'field': 'order_count', 'old_value': 11875, 'new_value': 12358}]
2025-06-27 12:02:29,148 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC11
2025-06-27 12:02:29,664 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMC11
2025-06-27 12:02:29,664 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 249256.41, 'new_value': 257917.76}, {'field': 'offline_amount', 'old_value': 308021.39, 'new_value': 315873.64}, {'field': 'total_amount', 'old_value': 557277.8, 'new_value': 573791.4}, {'field': 'order_count', 'old_value': 17748, 'new_value': 18310}]
2025-06-27 12:02:29,664 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME11
2025-06-27 12:02:30,226 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME11
2025-06-27 12:02:30,226 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 158864.1, 'new_value': 167892.2}, {'field': 'total_amount', 'old_value': 158864.1, 'new_value': 167892.2}, {'field': 'order_count', 'old_value': 5161, 'new_value': 5451}]
2025-06-27 12:02:30,226 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMG11
2025-06-27 12:02:30,805 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMG11
2025-06-27 12:02:30,805 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 224670.97, 'new_value': 233443.97}, {'field': 'total_amount', 'old_value': 235297.97, 'new_value': 244070.97}, {'field': 'order_count', 'old_value': 980, 'new_value': 1027}]
2025-06-27 12:02:30,805 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMH11
2025-06-27 12:02:31,258 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMH11
2025-06-27 12:02:31,258 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 26033.83, 'new_value': 29056.75}, {'field': 'offline_amount', 'old_value': 27408.27, 'new_value': 31016.52}, {'field': 'total_amount', 'old_value': 53442.1, 'new_value': 60073.27}, {'field': 'order_count', 'old_value': 2519, 'new_value': 2855}]
2025-06-27 12:02:31,258 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMI11
2025-06-27 12:02:31,711 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMI11
2025-06-27 12:02:31,711 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6628.63, 'new_value': 6950.13}, {'field': 'offline_amount', 'old_value': 17053.03, 'new_value': 17179.03}, {'field': 'total_amount', 'old_value': 23681.66, 'new_value': 24129.16}, {'field': 'order_count', 'old_value': 253, 'new_value': 260}]
2025-06-27 12:02:31,711 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ11
2025-06-27 12:02:32,148 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ11
2025-06-27 12:02:32,148 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 29195.01, 'new_value': 35797.41}, {'field': 'total_amount', 'old_value': 194392.73, 'new_value': 200995.13}, {'field': 'order_count', 'old_value': 10931, 'new_value': 11311}]
2025-06-27 12:02:32,148 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK11
2025-06-27 12:02:32,617 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK11
2025-06-27 12:02:32,617 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35133.62, 'new_value': 36133.62}, {'field': 'total_amount', 'old_value': 35133.62, 'new_value': 36133.62}, {'field': 'order_count', 'old_value': 770, 'new_value': 780}]
2025-06-27 12:02:32,617 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMM11
2025-06-27 12:02:33,101 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMM11
2025-06-27 12:02:33,101 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 297507.7, 'new_value': 308287.3}, {'field': 'total_amount', 'old_value': 361613.63, 'new_value': 372393.23}, {'field': 'order_count', 'old_value': 4999, 'new_value': 5128}]
2025-06-27 12:02:33,101 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN11
2025-06-27 12:02:33,711 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN11
2025-06-27 12:02:33,711 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 338536.0, 'new_value': 350544.0}, {'field': 'total_amount', 'old_value': 352094.0, 'new_value': 364102.0}, {'field': 'order_count', 'old_value': 296, 'new_value': 307}]
2025-06-27 12:02:33,711 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ11
2025-06-27 12:02:34,180 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ11
2025-06-27 12:02:34,180 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 58053.51, 'new_value': 61678.01}, {'field': 'total_amount', 'old_value': 62730.01, 'new_value': 66354.51}, {'field': 'order_count', 'old_value': 384, 'new_value': 396}]
2025-06-27 12:02:34,180 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS11
2025-06-27 12:02:34,586 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS11
2025-06-27 12:02:34,586 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 140467.22, 'new_value': 146391.75}, {'field': 'offline_amount', 'old_value': 43794.44, 'new_value': 44964.27}, {'field': 'total_amount', 'old_value': 184261.66, 'new_value': 191356.02}, {'field': 'order_count', 'old_value': 10983, 'new_value': 11418}]
2025-06-27 12:02:34,586 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT11
2025-06-27 12:02:35,023 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT11
2025-06-27 12:02:35,023 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 68839.2, 'new_value': 70563.45}, {'field': 'offline_amount', 'old_value': 92545.08, 'new_value': 94057.93}, {'field': 'total_amount', 'old_value': 161384.28, 'new_value': 164621.38}, {'field': 'order_count', 'old_value': 1877, 'new_value': 1926}]
2025-06-27 12:02:35,023 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU11
2025-06-27 12:02:35,492 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMU11
2025-06-27 12:02:35,492 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 134974.13, 'new_value': 143617.5}, {'field': 'total_amount', 'old_value': 134974.13, 'new_value': 143617.5}, {'field': 'order_count', 'old_value': 657, 'new_value': 690}]
2025-06-27 12:02:35,492 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV11
2025-06-27 12:02:35,945 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV11
2025-06-27 12:02:35,945 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 353331.8, 'new_value': 374669.7}, {'field': 'total_amount', 'old_value': 353331.8, 'new_value': 374669.7}, {'field': 'order_count', 'old_value': 414, 'new_value': 432}]
2025-06-27 12:02:35,945 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX11
2025-06-27 12:02:36,398 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMX11
2025-06-27 12:02:36,398 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 66140.0, 'new_value': 67695.0}, {'field': 'offline_amount', 'old_value': 305502.0, 'new_value': 318408.0}, {'field': 'total_amount', 'old_value': 371642.0, 'new_value': 386103.0}, {'field': 'order_count', 'old_value': 1443, 'new_value': 1498}]
2025-06-27 12:02:36,398 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ11
2025-06-27 12:02:36,804 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ11
2025-06-27 12:02:36,804 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 284073.0, 'new_value': 290925.0}, {'field': 'total_amount', 'old_value': 284073.0, 'new_value': 290925.0}, {'field': 'order_count', 'old_value': 364, 'new_value': 373}]
2025-06-27 12:02:36,804 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM021
2025-06-27 12:02:37,273 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM021
2025-06-27 12:02:37,273 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 408221.99, 'new_value': 414488.69}, {'field': 'total_amount', 'old_value': 408221.99, 'new_value': 414488.69}]
2025-06-27 12:02:37,273 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM121
2025-06-27 12:02:37,726 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM121
2025-06-27 12:02:37,726 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 101925.18, 'new_value': 106465.97}, {'field': 'offline_amount', 'old_value': 132264.23, 'new_value': 138543.17}, {'field': 'total_amount', 'old_value': 234189.41, 'new_value': 245009.14}, {'field': 'order_count', 'old_value': 9182, 'new_value': 9465}]
2025-06-27 12:02:37,726 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM221
2025-06-27 12:02:38,117 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM221
2025-06-27 12:02:38,117 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 184282.39, 'new_value': 188781.71}, {'field': 'total_amount', 'old_value': 184282.39, 'new_value': 188781.71}, {'field': 'order_count', 'old_value': 783, 'new_value': 802}]
2025-06-27 12:02:38,117 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM321
2025-06-27 12:02:38,586 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM321
2025-06-27 12:02:38,586 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3208.8, 'new_value': 8188.8}, {'field': 'total_amount', 'old_value': 53208.8, 'new_value': 58188.8}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-06-27 12:02:38,586 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM521
2025-06-27 12:02:39,023 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM521
2025-06-27 12:02:39,023 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 202326.43, 'new_value': 212492.43}, {'field': 'total_amount', 'old_value': 284041.63, 'new_value': 294207.63}, {'field': 'order_count', 'old_value': 451, 'new_value': 468}]
2025-06-27 12:02:39,023 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM621
2025-06-27 12:02:39,429 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM621
2025-06-27 12:02:39,429 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 87438.74, 'new_value': 89825.84}, {'field': 'total_amount', 'old_value': 87438.74, 'new_value': 89825.84}, {'field': 'order_count', 'old_value': 5675, 'new_value': 5858}]
2025-06-27 12:02:39,429 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM721
2025-06-27 12:02:39,867 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM721
2025-06-27 12:02:39,867 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2875.0, 'new_value': 2975.0}, {'field': 'offline_amount', 'old_value': 30285.0, 'new_value': 30785.0}, {'field': 'total_amount', 'old_value': 33160.0, 'new_value': 33760.0}, {'field': 'order_count', 'old_value': 420, 'new_value': 428}]
2025-06-27 12:02:39,867 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM821
2025-06-27 12:02:40,414 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM821
2025-06-27 12:02:40,414 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 136585.0, 'new_value': 141976.0}, {'field': 'offline_amount', 'old_value': 170316.0, 'new_value': 183780.0}, {'field': 'total_amount', 'old_value': 306901.0, 'new_value': 325756.0}, {'field': 'order_count', 'old_value': 179552, 'new_value': 198407}]
2025-06-27 12:02:40,414 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM921
2025-06-27 12:02:40,914 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM921
2025-06-27 12:02:40,914 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 71025.12, 'new_value': 74938.29}, {'field': 'offline_amount', 'old_value': 72255.25, 'new_value': 75077.81}, {'field': 'total_amount', 'old_value': 143280.37, 'new_value': 150016.1}, {'field': 'order_count', 'old_value': 7524, 'new_value': 7889}]
2025-06-27 12:02:40,914 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA21
2025-06-27 12:02:41,414 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA21
2025-06-27 12:02:41,414 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 10375.39, 'new_value': 10380.39}, {'field': 'offline_amount', 'old_value': 26583.68, 'new_value': 26781.68}, {'field': 'total_amount', 'old_value': 36959.07, 'new_value': 37162.07}, {'field': 'order_count', 'old_value': 112, 'new_value': 114}]
2025-06-27 12:02:41,414 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME21
2025-06-27 12:02:41,867 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBME21
2025-06-27 12:02:41,867 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 229294.0, 'new_value': 241359.0}, {'field': 'total_amount', 'old_value': 229294.0, 'new_value': 241359.0}, {'field': 'order_count', 'old_value': 24237, 'new_value': 25552}]
2025-06-27 12:02:41,867 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMF21
2025-06-27 12:02:42,367 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMF21
2025-06-27 12:02:42,367 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 162731.78, 'new_value': 187151.7}, {'field': 'total_amount', 'old_value': 162731.78, 'new_value': 187151.7}, {'field': 'order_count', 'old_value': 11783, 'new_value': 13652}]
2025-06-27 12:02:42,367 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMG21
2025-06-27 12:02:42,929 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMG21
2025-06-27 12:02:42,929 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 579314.0, 'new_value': 736538.0}, {'field': 'total_amount', 'old_value': 579314.0, 'new_value': 736538.0}, {'field': 'order_count', 'old_value': 74, 'new_value': 97}]
2025-06-27 12:02:42,929 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ21
2025-06-27 12:02:43,445 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMJ21
2025-06-27 12:02:43,445 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 94407.0, 'new_value': 98216.0}, {'field': 'total_amount', 'old_value': 94407.0, 'new_value': 98216.0}, {'field': 'order_count', 'old_value': 8840, 'new_value': 9090}]
2025-06-27 12:02:43,445 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK21
2025-06-27 12:02:43,976 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK21
2025-06-27 12:02:43,976 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 163181.0, 'new_value': 169110.0}, {'field': 'offline_amount', 'old_value': 1154002.0, 'new_value': 1197773.0}, {'field': 'total_amount', 'old_value': 1317183.0, 'new_value': 1366883.0}, {'field': 'order_count', 'old_value': 45255, 'new_value': 46646}]
2025-06-27 12:02:43,976 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML21
2025-06-27 12:02:44,445 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBML21
2025-06-27 12:02:44,445 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 304232.0, 'new_value': 315863.0}, {'field': 'total_amount', 'old_value': 304232.0, 'new_value': 315863.0}, {'field': 'order_count', 'old_value': 7007, 'new_value': 7299}]
2025-06-27 12:02:44,445 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN21
2025-06-27 12:02:44,914 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMN21
2025-06-27 12:02:44,914 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 6423.0, 'new_value': 6892.5}, {'field': 'total_amount', 'old_value': 14699.5, 'new_value': 15169.0}, {'field': 'order_count', 'old_value': 126, 'new_value': 131}]
2025-06-27 12:02:44,914 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP21
2025-06-27 12:02:45,492 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMP21
2025-06-27 12:02:45,492 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 45401.95, 'new_value': 47180.75}, {'field': 'total_amount', 'old_value': 45401.95, 'new_value': 47180.75}, {'field': 'order_count', 'old_value': 1177, 'new_value': 1236}]
2025-06-27 12:02:45,492 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ21
2025-06-27 12:02:45,929 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMQ21
2025-06-27 12:02:45,929 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 106487.7, 'new_value': 118140.4}, {'field': 'offline_amount', 'old_value': 30393.7, 'new_value': 33050.9}, {'field': 'total_amount', 'old_value': 136881.4, 'new_value': 151191.3}, {'field': 'order_count', 'old_value': 10190, 'new_value': 11388}]
2025-06-27 12:02:45,929 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR21
2025-06-27 12:02:46,383 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMR21
2025-06-27 12:02:46,383 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43412.0, 'new_value': 46700.0}, {'field': 'total_amount', 'old_value': 43412.0, 'new_value': 46700.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-06-27 12:02:46,383 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS21
2025-06-27 12:02:46,851 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMS21
2025-06-27 12:02:46,851 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1016449.71, 'new_value': 1051928.61}, {'field': 'total_amount', 'old_value': 1016449.71, 'new_value': 1051928.61}, {'field': 'order_count', 'old_value': 3486, 'new_value': 3599}]
2025-06-27 12:02:46,851 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT21
2025-06-27 12:02:47,336 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMT21
2025-06-27 12:02:47,336 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 49628.1, 'new_value': 52580.8}, {'field': 'total_amount', 'old_value': 49628.1, 'new_value': 52580.8}, {'field': 'order_count', 'old_value': 257, 'new_value': 267}]
2025-06-27 12:02:47,351 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV21
2025-06-27 12:02:47,820 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMV21
2025-06-27 12:02:47,820 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 683403.59, 'new_value': 700120.29}, {'field': 'total_amount', 'old_value': 683403.59, 'new_value': 700120.29}, {'field': 'order_count', 'old_value': 5884, 'new_value': 6059}]
2025-06-27 12:02:47,820 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW21
2025-06-27 12:02:48,304 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMW21
2025-06-27 12:02:48,304 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 252999.9, 'new_value': 262907.9}, {'field': 'offline_amount', 'old_value': 21763.5, 'new_value': 23305.5}, {'field': 'total_amount', 'old_value': 274763.4, 'new_value': 286213.4}, {'field': 'order_count', 'old_value': 3286, 'new_value': 3529}]
2025-06-27 12:02:48,304 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ21
2025-06-27 12:02:48,758 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMZ21
2025-06-27 12:02:48,758 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 31809.3, 'new_value': 32042.1}, {'field': 'offline_amount', 'old_value': 55760.7, 'new_value': 57872.9}, {'field': 'total_amount', 'old_value': 87570.0, 'new_value': 89915.0}, {'field': 'order_count', 'old_value': 3451, 'new_value': 3544}]
2025-06-27 12:02:48,758 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM031
2025-06-27 12:02:49,164 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM031
2025-06-27 12:02:49,164 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 16197.13, 'new_value': 18316.13}, {'field': 'offline_amount', 'old_value': 113126.19, 'new_value': 128165.68}, {'field': 'total_amount', 'old_value': 129323.32, 'new_value': 146481.81}, {'field': 'order_count', 'old_value': 3590, 'new_value': 4054}]
2025-06-27 12:02:49,164 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM231
2025-06-27 12:02:49,570 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM231
2025-06-27 12:02:49,570 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 45421.58, 'new_value': 51421.58}, {'field': 'total_amount', 'old_value': 123651.33, 'new_value': 129651.33}, {'field': 'order_count', 'old_value': 7328, 'new_value': 7408}]
2025-06-27 12:02:49,570 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM331
2025-06-27 12:02:50,070 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM331
2025-06-27 12:02:50,070 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33675.84, 'new_value': 34256.12}, {'field': 'total_amount', 'old_value': 34441.84, 'new_value': 35022.12}, {'field': 'order_count', 'old_value': 324, 'new_value': 336}]
2025-06-27 12:02:50,070 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM431
2025-06-27 12:02:50,508 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM431
2025-06-27 12:02:50,508 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 35017.26, 'new_value': 35997.59}, {'field': 'total_amount', 'old_value': 35017.26, 'new_value': 35997.59}, {'field': 'order_count', 'old_value': 108, 'new_value': 112}]
2025-06-27 12:02:50,508 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM531
2025-06-27 12:02:50,961 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM531
2025-06-27 12:02:50,961 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 383256.71, 'new_value': 395271.71}, {'field': 'total_amount', 'old_value': 415660.64, 'new_value': 427675.64}, {'field': 'order_count', 'old_value': 534, 'new_value': 552}]
2025-06-27 12:02:50,961 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM831
2025-06-27 12:02:51,398 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM831
2025-06-27 12:02:51,398 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 227358.0, 'new_value': 278816.0}, {'field': 'total_amount', 'old_value': 227507.0, 'new_value': 278965.0}, {'field': 'order_count', 'old_value': 41, 'new_value': 46}]
2025-06-27 12:02:51,398 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM931
2025-06-27 12:02:51,820 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM931
2025-06-27 12:02:51,820 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5226426.49, 'new_value': 5363925.49}, {'field': 'total_amount', 'old_value': 5226426.49, 'new_value': 5363925.49}, {'field': 'order_count', 'old_value': 104860, 'new_value': 108194}]
2025-06-27 12:02:51,820 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA31
2025-06-27 12:02:52,351 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMA31
2025-06-27 12:02:52,351 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1064305.4, 'new_value': 1104339.76}, {'field': 'total_amount', 'old_value': 1064305.4, 'new_value': 1104339.76}, {'field': 'order_count', 'old_value': 4244, 'new_value': 4357}]
2025-06-27 12:02:52,351 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB31
2025-06-27 12:02:52,867 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMB31
2025-06-27 12:02:52,867 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 682637.85, 'new_value': 712823.25}, {'field': 'total_amount', 'old_value': 682637.85, 'new_value': 712823.25}, {'field': 'order_count', 'old_value': 1757, 'new_value': 1829}]
2025-06-27 12:02:52,867 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMF31
2025-06-27 12:02:53,320 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMF31
2025-06-27 12:02:53,320 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 71731.11, 'new_value': 75950.82}, {'field': 'total_amount', 'old_value': 100128.23, 'new_value': 104347.94}, {'field': 'order_count', 'old_value': 6619, 'new_value': 6903}]
2025-06-27 12:02:53,320 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM01
2025-06-27 12:02:53,789 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM01
2025-06-27 12:02:53,789 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 126559.77, 'new_value': 133832.71}, {'field': 'total_amount', 'old_value': 179035.6, 'new_value': 186308.54}, {'field': 'order_count', 'old_value': 11903, 'new_value': 12386}]
2025-06-27 12:02:53,789 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM11
2025-06-27 12:02:54,289 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM11
2025-06-27 12:02:54,289 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42036.0, 'new_value': 42164.0}, {'field': 'total_amount', 'old_value': 49491.0, 'new_value': 49619.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 39}]
2025-06-27 12:02:54,289 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM21
2025-06-27 12:02:54,789 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM21
2025-06-27 12:02:54,789 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 79748.0, 'new_value': 82038.0}, {'field': 'total_amount', 'old_value': 98588.0, 'new_value': 100878.0}, {'field': 'order_count', 'old_value': 26, 'new_value': 27}]
2025-06-27 12:02:54,789 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM31
2025-06-27 12:02:55,242 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM31
2025-06-27 12:02:55,242 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2079588.0, 'new_value': 2129588.0}, {'field': 'total_amount', 'old_value': 2079588.0, 'new_value': 2129588.0}, {'field': 'order_count', 'old_value': 3086, 'new_value': 3087}]
2025-06-27 12:02:55,242 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM51
2025-06-27 12:02:55,601 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM51
2025-06-27 12:02:55,601 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 63000.0, 'new_value': 93000.0}, {'field': 'total_amount', 'old_value': 69000.0, 'new_value': 99000.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 6}]
2025-06-27 12:02:55,601 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM61
2025-06-27 12:02:56,101 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM61
2025-06-27 12:02:56,101 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 185000.0, 'new_value': 190000.0}, {'field': 'total_amount', 'old_value': 185000.0, 'new_value': 190000.0}, {'field': 'order_count', 'old_value': 799, 'new_value': 800}]
2025-06-27 12:02:56,101 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM71
2025-06-27 12:02:56,554 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM71
2025-06-27 12:02:56,554 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 185000.0, 'new_value': 190000.0}, {'field': 'total_amount', 'old_value': 195000.0, 'new_value': 200000.0}, {'field': 'order_count', 'old_value': 928, 'new_value': 929}]
2025-06-27 12:02:56,554 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM81
2025-06-27 12:02:57,007 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM81
2025-06-27 12:02:57,007 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 113314.0, 'new_value': 125202.0}, {'field': 'total_amount', 'old_value': 113314.0, 'new_value': 125202.0}, {'field': 'order_count', 'old_value': 25, 'new_value': 26}]
2025-06-27 12:02:57,007 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM91
2025-06-27 12:02:57,554 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM91
2025-06-27 12:02:57,554 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 11162.14, 'new_value': 11591.39}, {'field': 'offline_amount', 'old_value': 79776.82, 'new_value': 81964.66}, {'field': 'total_amount', 'old_value': 90938.96, 'new_value': 93556.05}, {'field': 'order_count', 'old_value': 2821, 'new_value': 2887}]
2025-06-27 12:02:57,554 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMB1
2025-06-27 12:02:58,007 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMB1
2025-06-27 12:02:58,007 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 39558.35, 'new_value': 45139.02}, {'field': 'total_amount', 'old_value': 39558.35, 'new_value': 45139.02}, {'field': 'order_count', 'old_value': 1751, 'new_value': 2007}]
2025-06-27 12:02:58,007 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC1
2025-06-27 12:02:58,414 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC1
2025-06-27 12:02:58,414 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 950302.41, 'new_value': 968755.36}, {'field': 'total_amount', 'old_value': 950302.41, 'new_value': 968755.36}, {'field': 'order_count', 'old_value': 4431, 'new_value': 4535}]
2025-06-27 12:02:58,414 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME1
2025-06-27 12:02:58,976 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME1
2025-06-27 12:02:58,976 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 461276.86, 'new_value': 476296.86}, {'field': 'total_amount', 'old_value': 461276.86, 'new_value': 476296.86}, {'field': 'order_count', 'old_value': 3176, 'new_value': 3301}]
2025-06-27 12:02:58,976 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF1
2025-06-27 12:02:59,461 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF1
2025-06-27 12:02:59,461 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 40786.8, 'new_value': 40964.8}, {'field': 'total_amount', 'old_value': 47518.45, 'new_value': 47696.45}, {'field': 'order_count', 'old_value': 362, 'new_value': 363}]
2025-06-27 12:02:59,461 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH1
2025-06-27 12:02:59,961 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH1
2025-06-27 12:02:59,961 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 250662.18, 'new_value': 261641.98}, {'field': 'total_amount', 'old_value': 250662.18, 'new_value': 261641.98}, {'field': 'order_count', 'old_value': 7209, 'new_value': 7494}]
2025-06-27 12:02:59,961 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ1
2025-06-27 12:03:00,429 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ1
2025-06-27 12:03:00,429 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 143480.86, 'new_value': 148329.16}, {'field': 'total_amount', 'old_value': 143480.86, 'new_value': 148329.16}, {'field': 'order_count', 'old_value': 10172, 'new_value': 10511}]
2025-06-27 12:03:00,429 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK1
2025-06-27 12:03:00,820 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK1
2025-06-27 12:03:00,820 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 270400.0, 'new_value': 285700.0}, {'field': 'total_amount', 'old_value': 270400.0, 'new_value': 285700.0}, {'field': 'order_count', 'old_value': 639, 'new_value': 674}]
2025-06-27 12:03:00,836 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML1
2025-06-27 12:03:01,320 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML1
2025-06-27 12:03:01,320 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 160687.0, 'new_value': 167362.97}, {'field': 'offline_amount', 'old_value': 50402.14, 'new_value': 52733.94}, {'field': 'total_amount', 'old_value': 211089.14, 'new_value': 220096.91}, {'field': 'order_count', 'old_value': 909, 'new_value': 957}]
2025-06-27 12:03:01,320 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM1
2025-06-27 12:03:01,742 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM1
2025-06-27 12:03:01,742 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 30421.18, 'new_value': 32108.08}, {'field': 'offline_amount', 'old_value': 119079.0, 'new_value': 123035.0}, {'field': 'total_amount', 'old_value': 149500.18, 'new_value': 155143.08}, {'field': 'order_count', 'old_value': 1823, 'new_value': 1893}]
2025-06-27 12:03:01,742 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMN1
2025-06-27 12:03:02,179 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMN1
2025-06-27 12:03:02,179 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 185000.0, 'new_value': 190000.0}, {'field': 'total_amount', 'old_value': 185000.0, 'new_value': 190000.0}, {'field': 'order_count', 'old_value': 594, 'new_value': 595}]
2025-06-27 12:03:02,179 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO1
2025-06-27 12:03:02,664 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO1
2025-06-27 12:03:02,664 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 217111.49, 'new_value': 229094.77}, {'field': 'total_amount', 'old_value': 217111.49, 'new_value': 229094.77}, {'field': 'order_count', 'old_value': 3286, 'new_value': 3461}]
2025-06-27 12:03:02,664 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMP1
2025-06-27 12:03:03,148 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMP1
2025-06-27 12:03:03,148 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 299076.0, 'new_value': 304076.0}, {'field': 'total_amount', 'old_value': 299076.0, 'new_value': 304076.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-06-27 12:03:03,148 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ1
2025-06-27 12:03:03,711 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ1
2025-06-27 12:03:03,711 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 564978.95, 'new_value': 591169.78}, {'field': 'total_amount', 'old_value': 564978.95, 'new_value': 591169.78}, {'field': 'order_count', 'old_value': 525, 'new_value': 548}]
2025-06-27 12:03:03,711 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS1
2025-06-27 12:03:04,148 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS1
2025-06-27 12:03:04,148 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 427840.0, 'new_value': 435464.0}, {'field': 'total_amount', 'old_value': 465496.0, 'new_value': 473120.0}, {'field': 'order_count', 'old_value': 9687, 'new_value': 9854}]
2025-06-27 12:03:04,148 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMT1
2025-06-27 12:03:04,617 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMT1
2025-06-27 12:03:04,617 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 43614.7, 'new_value': 44528.8}, {'field': 'total_amount', 'old_value': 43614.7, 'new_value': 44528.8}, {'field': 'order_count', 'old_value': 357, 'new_value': 363}]
2025-06-27 12:03:04,617 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMV1
2025-06-27 12:03:05,039 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMV1
2025-06-27 12:03:05,039 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 70809.49, 'new_value': 76809.49}, {'field': 'total_amount', 'old_value': 70809.49, 'new_value': 76809.49}, {'field': 'order_count', 'old_value': 2798, 'new_value': 2878}]
2025-06-27 12:03:05,039 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW1
2025-06-27 12:03:05,586 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW1
2025-06-27 12:03:05,586 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 129052.55, 'new_value': 134558.55}, {'field': 'total_amount', 'old_value': 129052.55, 'new_value': 134558.55}, {'field': 'order_count', 'old_value': 3309, 'new_value': 3464}]
2025-06-27 12:03:05,586 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX1
2025-06-27 12:03:06,007 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX1
2025-06-27 12:03:06,007 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 194973.43, 'new_value': 203299.83}, {'field': 'total_amount', 'old_value': 296239.62, 'new_value': 304566.02}, {'field': 'order_count', 'old_value': 2375, 'new_value': 2458}]
2025-06-27 12:03:06,007 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY1
2025-06-27 12:03:06,461 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY1
2025-06-27 12:03:06,461 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 83153.2, 'new_value': 84990.2}, {'field': 'total_amount', 'old_value': 83153.2, 'new_value': 84990.2}, {'field': 'order_count', 'old_value': 748, 'new_value': 764}]
2025-06-27 12:03:06,461 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMZ1
2025-06-27 12:03:06,898 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMZ1
2025-06-27 12:03:06,898 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 152038.0, 'new_value': 160038.0}, {'field': 'total_amount', 'old_value': 152038.0, 'new_value': 160038.0}, {'field': 'order_count', 'old_value': 13627, 'new_value': 13717}]
2025-06-27 12:03:06,898 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM02
2025-06-27 12:03:07,398 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM02
2025-06-27 12:03:07,398 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 81834.0, 'new_value': 84313.0}, {'field': 'total_amount', 'old_value': 81834.0, 'new_value': 84313.0}, {'field': 'order_count', 'old_value': 1288, 'new_value': 1338}]
2025-06-27 12:03:07,398 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM12
2025-06-27 12:03:07,929 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM12
2025-06-27 12:03:07,929 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 61890.0, 'new_value': 62089.0}, {'field': 'total_amount', 'old_value': 61890.0, 'new_value': 62089.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-06-27 12:03:07,929 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM22
2025-06-27 12:03:08,367 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM22
2025-06-27 12:03:08,367 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 103957.63, 'new_value': 109117.63}, {'field': 'total_amount', 'old_value': 170515.33, 'new_value': 175675.33}, {'field': 'order_count', 'old_value': 4093, 'new_value': 4225}]
2025-06-27 12:03:08,367 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM32
2025-06-27 12:03:08,914 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM32
2025-06-27 12:03:08,914 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 126711.0, 'new_value': 128967.0}, {'field': 'total_amount', 'old_value': 126711.0, 'new_value': 128967.0}, {'field': 'order_count', 'old_value': 539, 'new_value': 549}]
2025-06-27 12:03:08,914 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM52
2025-06-27 12:03:09,351 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM52
2025-06-27 12:03:09,351 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 111901.4, 'new_value': 112497.4}, {'field': 'total_amount', 'old_value': 111901.4, 'new_value': 112497.4}]
2025-06-27 12:03:09,351 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM62
2025-06-27 12:03:09,742 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM62
2025-06-27 12:03:09,742 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 28185.4, 'new_value': 32935.4}, {'field': 'total_amount', 'old_value': 28185.4, 'new_value': 32935.4}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-27 12:03:09,742 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM92
2025-06-27 12:03:10,195 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM92
2025-06-27 12:03:10,195 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 72875.0, 'new_value': 76055.0}, {'field': 'total_amount', 'old_value': 76320.0, 'new_value': 79500.0}, {'field': 'order_count', 'old_value': 288, 'new_value': 300}]
2025-06-27 12:03:10,195 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMB2
2025-06-27 12:03:10,648 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMB2
2025-06-27 12:03:10,648 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47363.05, 'new_value': 49295.35}, {'field': 'total_amount', 'old_value': 47363.05, 'new_value': 49295.35}, {'field': 'order_count', 'old_value': 5978, 'new_value': 6225}]
2025-06-27 12:03:10,648 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMD2
2025-06-27 12:03:11,117 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMD2
2025-06-27 12:03:11,117 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 314257.17, 'new_value': 327020.15}, {'field': 'total_amount', 'old_value': 314257.17, 'new_value': 327020.15}, {'field': 'order_count', 'old_value': 869, 'new_value': 908}]
2025-06-27 12:03:11,117 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME2
2025-06-27 12:03:11,586 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME2
2025-06-27 12:03:11,586 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 321572.0, 'new_value': 336203.0}, {'field': 'total_amount', 'old_value': 321572.0, 'new_value': 336203.0}, {'field': 'order_count', 'old_value': 7222, 'new_value': 7526}]
2025-06-27 12:03:11,586 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF2
2025-06-27 12:03:12,023 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMF2
2025-06-27 12:03:12,023 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 113254.0, 'new_value': 117870.0}, {'field': 'total_amount', 'old_value': 113254.0, 'new_value': 117870.0}, {'field': 'order_count', 'old_value': 6438, 'new_value': 6704}]
2025-06-27 12:03:12,023 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG2
2025-06-27 12:03:12,617 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG2
2025-06-27 12:03:12,617 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 311816.0, 'new_value': 327915.0}, {'field': 'total_amount', 'old_value': 311816.0, 'new_value': 327915.0}, {'field': 'order_count', 'old_value': 80, 'new_value': 83}]
2025-06-27 12:03:12,617 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMI2
2025-06-27 12:03:13,179 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMI2
2025-06-27 12:03:13,179 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5742.0, 'new_value': 5822.0}, {'field': 'offline_amount', 'old_value': 19973.0, 'new_value': 20661.0}, {'field': 'total_amount', 'old_value': 25715.0, 'new_value': 26483.0}, {'field': 'order_count', 'old_value': 111, 'new_value': 116}]
2025-06-27 12:03:13,179 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO2
2025-06-27 12:03:13,585 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMO2
2025-06-27 12:03:13,585 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 221919.9, 'new_value': 232519.9}, {'field': 'total_amount', 'old_value': 221919.9, 'new_value': 232519.9}, {'field': 'order_count', 'old_value': 7633, 'new_value': 7960}]
2025-06-27 12:03:13,585 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ2
2025-06-27 12:03:14,039 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMQ2
2025-06-27 12:03:14,039 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 240761.85, 'new_value': 247264.85}, {'field': 'total_amount', 'old_value': 274963.95, 'new_value': 281466.95}, {'field': 'order_count', 'old_value': 827, 'new_value': 874}]
2025-06-27 12:03:14,039 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMR2
2025-06-27 12:03:14,476 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMR2
2025-06-27 12:03:14,476 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15092814.0, 'new_value': 15721681.25}, {'field': 'total_amount', 'old_value': 15092814.0, 'new_value': 15721681.25}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-06-27 12:03:14,476 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS2
2025-06-27 12:03:14,960 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMS2
2025-06-27 12:03:14,960 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 185996.0, 'new_value': 195896.0}, {'field': 'total_amount', 'old_value': 185996.0, 'new_value': 195896.0}, {'field': 'order_count', 'old_value': 796, 'new_value': 842}]
2025-06-27 12:03:14,960 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMT2
2025-06-27 12:03:15,507 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMT2
2025-06-27 12:03:15,507 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 5627.0, 'new_value': 5715.0}, {'field': 'offline_amount', 'old_value': 50959.0, 'new_value': 51876.0}, {'field': 'total_amount', 'old_value': 56586.0, 'new_value': 57591.0}, {'field': 'order_count', 'old_value': 79, 'new_value': 85}]
2025-06-27 12:03:15,507 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW2
2025-06-27 12:03:15,976 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW2
2025-06-27 12:03:15,976 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 47987.1, 'new_value': 52987.1}, {'field': 'total_amount', 'old_value': 47987.1, 'new_value': 52987.1}, {'field': 'order_count', 'old_value': 3982, 'new_value': 4072}]
2025-06-27 12:03:15,976 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX2
2025-06-27 12:03:16,476 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMX2
2025-06-27 12:03:16,476 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1201000.0, 'new_value': 1248000.0}, {'field': 'total_amount', 'old_value': 1201000.0, 'new_value': 1248000.0}, {'field': 'order_count', 'old_value': 59, 'new_value': 60}]
2025-06-27 12:03:16,476 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY2
2025-06-27 12:03:16,977 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMY2
2025-06-27 12:03:16,977 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 340700.0, 'new_value': 353700.0}, {'field': 'total_amount', 'old_value': 340700.0, 'new_value': 353700.0}, {'field': 'order_count', 'old_value': 9701, 'new_value': 9959}]
2025-06-27 12:03:16,977 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM33
2025-06-27 12:03:17,430 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM33
2025-06-27 12:03:17,430 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52245.0, 'new_value': 54045.0}, {'field': 'total_amount', 'old_value': 52245.0, 'new_value': 54045.0}, {'field': 'order_count', 'old_value': 45, 'new_value': 46}]
2025-06-27 12:03:17,430 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM43
2025-06-27 12:03:17,899 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM43
2025-06-27 12:03:17,899 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 128438.0, 'new_value': 131897.0}, {'field': 'total_amount', 'old_value': 128438.0, 'new_value': 131897.0}, {'field': 'order_count', 'old_value': 3548, 'new_value': 3630}]
2025-06-27 12:03:17,899 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA3
2025-06-27 12:03:18,383 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA3
2025-06-27 12:03:18,383 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 154000.0, 'new_value': 161000.0}, {'field': 'total_amount', 'old_value': 154000.0, 'new_value': 161000.0}, {'field': 'order_count', 'old_value': 22, 'new_value': 23}]
2025-06-27 12:03:18,383 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME3
2025-06-27 12:03:18,821 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBME3
2025-06-27 12:03:18,821 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 120982.98, 'new_value': 124842.98}, {'field': 'total_amount', 'old_value': 120982.98, 'new_value': 124842.98}, {'field': 'order_count', 'old_value': 4427, 'new_value': 4503}]
2025-06-27 12:03:18,821 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG3
2025-06-27 12:03:19,290 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG3
2025-06-27 12:03:19,290 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 98386.0, 'new_value': 104386.0}, {'field': 'total_amount', 'old_value': 98386.0, 'new_value': 104386.0}, {'field': 'order_count', 'old_value': 702, 'new_value': 732}]
2025-06-27 12:03:19,290 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK3
2025-06-27 12:03:19,758 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK3
2025-06-27 12:03:19,758 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33920.0, 'new_value': 34678.0}, {'field': 'total_amount', 'old_value': 33920.0, 'new_value': 34678.0}, {'field': 'order_count', 'old_value': 2902, 'new_value': 3004}]
2025-06-27 12:03:19,758 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML3
2025-06-27 12:03:20,227 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML3
2025-06-27 12:03:20,243 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12135.0, 'new_value': 13142.0}, {'field': 'total_amount', 'old_value': 12135.0, 'new_value': 13142.0}, {'field': 'order_count', 'old_value': 1106, 'new_value': 1133}]
2025-06-27 12:03:20,243 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMO3
2025-06-27 12:03:20,618 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMO3
2025-06-27 12:03:20,618 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 267410.35, 'new_value': 278410.35}, {'field': 'total_amount', 'old_value': 267410.35, 'new_value': 278410.35}, {'field': 'order_count', 'old_value': 1964, 'new_value': 2064}]
2025-06-27 12:03:20,618 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMP3
2025-06-27 12:03:21,102 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMP3
2025-06-27 12:03:21,102 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6562.0, 'new_value': 6661.0}, {'field': 'total_amount', 'old_value': 6562.0, 'new_value': 6661.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-06-27 12:03:21,102 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMKP
2025-06-27 12:03:21,540 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMKP
2025-06-27 12:03:21,540 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6031.9, 'new_value': 8430.9}, {'field': 'total_amount', 'old_value': 6031.9, 'new_value': 8430.9}, {'field': 'order_count', 'old_value': 4, 'new_value': 5}]
2025-06-27 12:03:21,540 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMMP
2025-06-27 12:03:21,993 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMMP
2025-06-27 12:03:21,993 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 82104.0, 'new_value': 89104.0}, {'field': 'total_amount', 'old_value': 82104.0, 'new_value': 89104.0}, {'field': 'order_count', 'old_value': 33, 'new_value': 36}]
2025-06-27 12:03:21,993 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMOP
2025-06-27 12:03:22,493 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMOP
2025-06-27 12:03:22,493 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 207617.22, 'new_value': 215617.22}, {'field': 'total_amount', 'old_value': 207617.22, 'new_value': 215617.22}, {'field': 'order_count', 'old_value': 389, 'new_value': 392}]
2025-06-27 12:03:22,493 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMQP
2025-06-27 12:03:23,008 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMQP
2025-06-27 12:03:23,008 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8979.5, 'new_value': 9879.5}, {'field': 'total_amount', 'old_value': 8979.5, 'new_value': 9879.5}, {'field': 'order_count', 'old_value': 64, 'new_value': 67}]
2025-06-27 12:03:23,008 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMRP
2025-06-27 12:03:23,446 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMRP
2025-06-27 12:03:23,446 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5138610.0, 'new_value': 5233610.0}, {'field': 'total_amount', 'old_value': 5138610.0, 'new_value': 5233610.0}, {'field': 'order_count', 'old_value': 388, 'new_value': 453}]
2025-06-27 12:03:23,446 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMSP
2025-06-27 12:03:23,852 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMSP
2025-06-27 12:03:23,852 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 412334.0, 'new_value': 426357.0}, {'field': 'total_amount', 'old_value': 412334.0, 'new_value': 426357.0}, {'field': 'order_count', 'old_value': 481, 'new_value': 496}]
2025-06-27 12:03:23,852 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMTP
2025-06-27 12:03:24,274 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMTP
2025-06-27 12:03:24,274 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38914.08, 'new_value': 39878.32}, {'field': 'total_amount', 'old_value': 38914.08, 'new_value': 39878.32}, {'field': 'order_count', 'old_value': 1546, 'new_value': 1581}]
2025-06-27 12:03:24,274 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMXP
2025-06-27 12:03:24,711 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMXP
2025-06-27 12:03:24,711 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 547000.0, 'new_value': 577000.0}, {'field': 'total_amount', 'old_value': 547000.0, 'new_value': 577000.0}, {'field': 'order_count', 'old_value': 17, 'new_value': 18}]
2025-06-27 12:03:24,711 - INFO - 开始更新记录 - 表单实例ID: FINST-RTA66X610KDWQ1WBCR65QD898OM630PY6J0CMY1
2025-06-27 12:03:25,165 - INFO - 更新表单数据成功: FINST-RTA66X610KDWQ1WBCR65QD898OM630PY6J0CMY1
2025-06-27 12:03:25,165 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2100.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2100.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-06-27 12:03:25,165 - INFO - 开始更新记录 - 表单实例ID: FINST-QZE668D1KUJWFAZLAFQRH7N1JKBG3MTF10ACME1
2025-06-27 12:03:25,602 - INFO - 更新表单数据成功: FINST-QZE668D1KUJWFAZLAFQRH7N1JKBG3MTF10ACME1
2025-06-27 12:03:25,602 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12505.4, 'new_value': 18758.1}, {'field': 'total_amount', 'old_value': 12505.4, 'new_value': 18758.1}, {'field': 'order_count', 'old_value': 2, 'new_value': 3}]
2025-06-27 12:03:25,602 - INFO - 开始更新记录 - 表单实例ID: FINST-QZE668D1KUJWFAZLAFQRH7N1JKBG3MTF10ACMF1
2025-06-27 12:03:26,086 - INFO - 更新表单数据成功: FINST-QZE668D1KUJWFAZLAFQRH7N1JKBG3MTF10ACMF1
2025-06-27 12:03:26,086 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 99864.0, 'new_value': 104025.0}, {'field': 'total_amount', 'old_value': 99864.0, 'new_value': 104025.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-06-27 12:03:26,086 - INFO - 开始批量插入 3 条新记录
2025-06-27 12:03:26,274 - INFO - 批量插入响应状态码: 200
2025-06-27 12:03:26,274 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 27 Jun 2025 04:03:15 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '156', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7095BAD9-D417-7D6E-9102-21CF09B1DED5', 'x-acs-trace-id': 'cde185e5cf30b620b14ee3ddacfe5501', 'etag': '1cxkHJ+MgUixAx5Ss/v2Ztw6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-06-27 12:03:26,274 - INFO - 批量插入响应体: {'result': ['FINST-EWE66Z91Q5NWVLK1C5VXFAUHTRM73VWGEAECMJ9', 'FINST-EWE66Z91Q5NWVLK1C5VXFAUHTRM73VWGEAECMK9', 'FINST-EWE66Z91Q5NWVLK1C5VXFAUHTRM73VWGEAECML9']}
2025-06-27 12:03:26,274 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-06-27 12:03:26,274 - INFO - 成功插入的数据ID: ['FINST-EWE66Z91Q5NWVLK1C5VXFAUHTRM73VWGEAECMJ9', 'FINST-EWE66Z91Q5NWVLK1C5VXFAUHTRM73VWGEAECMK9', 'FINST-EWE66Z91Q5NWVLK1C5VXFAUHTRM73VWGEAECML9']
2025-06-27 12:03:29,289 - INFO - 批量插入完成，共 3 条记录
2025-06-27 12:03:29,289 - INFO - 日期 2025-06 处理完成 - 更新: 357 条，插入: 3 条，错误: 0 条
2025-06-27 12:03:29,289 - INFO - 数据同步完成！更新: 357 条，插入: 3 条，错误: 0 条
2025-06-27 12:03:29,289 - INFO - =================同步完成====================
2025-06-27 15:00:04,259 - INFO - =================使用默认全量同步=============
2025-06-27 15:00:06,071 - INFO - MySQL查询成功，共获取 3963 条记录
2025-06-27 15:00:06,071 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-27 15:00:06,102 - INFO - 开始处理日期: 2025-01
2025-06-27 15:00:06,102 - INFO - Request Parameters - Page 1:
2025-06-27 15:00:06,102 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:06,102 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:07,290 - INFO - Response - Page 1:
2025-06-27 15:00:07,493 - INFO - 第 1 页获取到 100 条记录
2025-06-27 15:00:07,493 - INFO - Request Parameters - Page 2:
2025-06-27 15:00:07,493 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:07,493 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:08,321 - INFO - Response - Page 2:
2025-06-27 15:00:08,524 - INFO - 第 2 页获取到 100 条记录
2025-06-27 15:00:08,524 - INFO - Request Parameters - Page 3:
2025-06-27 15:00:08,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:08,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:09,118 - INFO - Response - Page 3:
2025-06-27 15:00:09,321 - INFO - 第 3 页获取到 100 条记录
2025-06-27 15:00:09,321 - INFO - Request Parameters - Page 4:
2025-06-27 15:00:09,321 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:09,321 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:09,993 - INFO - Response - Page 4:
2025-06-27 15:00:10,196 - INFO - 第 4 页获取到 100 条记录
2025-06-27 15:00:10,196 - INFO - Request Parameters - Page 5:
2025-06-27 15:00:10,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:10,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:10,821 - INFO - Response - Page 5:
2025-06-27 15:00:11,024 - INFO - 第 5 页获取到 100 条记录
2025-06-27 15:00:11,024 - INFO - Request Parameters - Page 6:
2025-06-27 15:00:11,024 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:11,024 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:11,540 - INFO - Response - Page 6:
2025-06-27 15:00:11,743 - INFO - 第 6 页获取到 100 条记录
2025-06-27 15:00:11,743 - INFO - Request Parameters - Page 7:
2025-06-27 15:00:11,743 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:11,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:12,227 - INFO - Response - Page 7:
2025-06-27 15:00:12,430 - INFO - 第 7 页获取到 82 条记录
2025-06-27 15:00:12,430 - INFO - 查询完成，共获取到 682 条记录
2025-06-27 15:00:12,430 - INFO - 获取到 682 条表单数据
2025-06-27 15:00:12,430 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-27 15:00:12,446 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 15:00:12,446 - INFO - 开始处理日期: 2025-02
2025-06-27 15:00:12,446 - INFO - Request Parameters - Page 1:
2025-06-27 15:00:12,446 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:12,446 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:13,009 - INFO - Response - Page 1:
2025-06-27 15:00:13,212 - INFO - 第 1 页获取到 100 条记录
2025-06-27 15:00:13,212 - INFO - Request Parameters - Page 2:
2025-06-27 15:00:13,212 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:13,212 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:13,821 - INFO - Response - Page 2:
2025-06-27 15:00:14,024 - INFO - 第 2 页获取到 100 条记录
2025-06-27 15:00:14,024 - INFO - Request Parameters - Page 3:
2025-06-27 15:00:14,024 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:14,024 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:14,602 - INFO - Response - Page 3:
2025-06-27 15:00:14,805 - INFO - 第 3 页获取到 100 条记录
2025-06-27 15:00:14,805 - INFO - Request Parameters - Page 4:
2025-06-27 15:00:14,805 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:14,805 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:15,321 - INFO - Response - Page 4:
2025-06-27 15:00:15,524 - INFO - 第 4 页获取到 100 条记录
2025-06-27 15:00:15,524 - INFO - Request Parameters - Page 5:
2025-06-27 15:00:15,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:15,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:16,102 - INFO - Response - Page 5:
2025-06-27 15:00:16,305 - INFO - 第 5 页获取到 100 条记录
2025-06-27 15:00:16,305 - INFO - Request Parameters - Page 6:
2025-06-27 15:00:16,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:16,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:16,852 - INFO - Response - Page 6:
2025-06-27 15:00:17,055 - INFO - 第 6 页获取到 100 条记录
2025-06-27 15:00:17,055 - INFO - Request Parameters - Page 7:
2025-06-27 15:00:17,055 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:17,055 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:17,571 - INFO - Response - Page 7:
2025-06-27 15:00:17,774 - INFO - 第 7 页获取到 70 条记录
2025-06-27 15:00:17,774 - INFO - 查询完成，共获取到 670 条记录
2025-06-27 15:00:17,774 - INFO - 获取到 670 条表单数据
2025-06-27 15:00:17,774 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-27 15:00:17,790 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 15:00:17,790 - INFO - 开始处理日期: 2025-03
2025-06-27 15:00:17,790 - INFO - Request Parameters - Page 1:
2025-06-27 15:00:17,790 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:17,790 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:18,337 - INFO - Response - Page 1:
2025-06-27 15:00:18,540 - INFO - 第 1 页获取到 100 条记录
2025-06-27 15:00:18,540 - INFO - Request Parameters - Page 2:
2025-06-27 15:00:18,540 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:18,540 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:19,102 - INFO - Response - Page 2:
2025-06-27 15:00:19,305 - INFO - 第 2 页获取到 100 条记录
2025-06-27 15:00:19,305 - INFO - Request Parameters - Page 3:
2025-06-27 15:00:19,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:19,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:19,759 - INFO - Response - Page 3:
2025-06-27 15:00:19,962 - INFO - 第 3 页获取到 100 条记录
2025-06-27 15:00:19,962 - INFO - Request Parameters - Page 4:
2025-06-27 15:00:19,962 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:19,962 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:20,509 - INFO - Response - Page 4:
2025-06-27 15:00:20,712 - INFO - 第 4 页获取到 100 条记录
2025-06-27 15:00:20,712 - INFO - Request Parameters - Page 5:
2025-06-27 15:00:20,712 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:20,712 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:21,227 - INFO - Response - Page 5:
2025-06-27 15:00:21,430 - INFO - 第 5 页获取到 100 条记录
2025-06-27 15:00:21,430 - INFO - Request Parameters - Page 6:
2025-06-27 15:00:21,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:21,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:21,915 - INFO - Response - Page 6:
2025-06-27 15:00:22,118 - INFO - 第 6 页获取到 100 条记录
2025-06-27 15:00:22,118 - INFO - Request Parameters - Page 7:
2025-06-27 15:00:22,118 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:22,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:22,571 - INFO - Response - Page 7:
2025-06-27 15:00:22,774 - INFO - 第 7 页获取到 61 条记录
2025-06-27 15:00:22,774 - INFO - 查询完成，共获取到 661 条记录
2025-06-27 15:00:22,774 - INFO - 获取到 661 条表单数据
2025-06-27 15:00:22,774 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-27 15:00:22,790 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 15:00:22,790 - INFO - 开始处理日期: 2025-04
2025-06-27 15:00:22,790 - INFO - Request Parameters - Page 1:
2025-06-27 15:00:22,790 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:22,790 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:23,337 - INFO - Response - Page 1:
2025-06-27 15:00:23,540 - INFO - 第 1 页获取到 100 条记录
2025-06-27 15:00:23,540 - INFO - Request Parameters - Page 2:
2025-06-27 15:00:23,540 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:23,540 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:24,071 - INFO - Response - Page 2:
2025-06-27 15:00:24,274 - INFO - 第 2 页获取到 100 条记录
2025-06-27 15:00:24,274 - INFO - Request Parameters - Page 3:
2025-06-27 15:00:24,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:24,274 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:24,805 - INFO - Response - Page 3:
2025-06-27 15:00:25,009 - INFO - 第 3 页获取到 100 条记录
2025-06-27 15:00:25,009 - INFO - Request Parameters - Page 4:
2025-06-27 15:00:25,009 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:25,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:25,524 - INFO - Response - Page 4:
2025-06-27 15:00:25,727 - INFO - 第 4 页获取到 100 条记录
2025-06-27 15:00:25,727 - INFO - Request Parameters - Page 5:
2025-06-27 15:00:25,727 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:25,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:26,243 - INFO - Response - Page 5:
2025-06-27 15:00:26,446 - INFO - 第 5 页获取到 100 条记录
2025-06-27 15:00:26,446 - INFO - Request Parameters - Page 6:
2025-06-27 15:00:26,446 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:26,446 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:26,962 - INFO - Response - Page 6:
2025-06-27 15:00:27,165 - INFO - 第 6 页获取到 100 条记录
2025-06-27 15:00:27,165 - INFO - Request Parameters - Page 7:
2025-06-27 15:00:27,165 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:27,165 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:27,602 - INFO - Response - Page 7:
2025-06-27 15:00:27,805 - INFO - 第 7 页获取到 56 条记录
2025-06-27 15:00:27,805 - INFO - 查询完成，共获取到 656 条记录
2025-06-27 15:00:27,805 - INFO - 获取到 656 条表单数据
2025-06-27 15:00:27,805 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-27 15:00:27,821 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 15:00:27,821 - INFO - 开始处理日期: 2025-05
2025-06-27 15:00:27,821 - INFO - Request Parameters - Page 1:
2025-06-27 15:00:27,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:27,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:28,415 - INFO - Response - Page 1:
2025-06-27 15:00:28,618 - INFO - 第 1 页获取到 100 条记录
2025-06-27 15:00:28,618 - INFO - Request Parameters - Page 2:
2025-06-27 15:00:28,618 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:28,618 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:29,196 - INFO - Response - Page 2:
2025-06-27 15:00:29,399 - INFO - 第 2 页获取到 100 条记录
2025-06-27 15:00:29,399 - INFO - Request Parameters - Page 3:
2025-06-27 15:00:29,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:29,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:30,071 - INFO - Response - Page 3:
2025-06-27 15:00:30,274 - INFO - 第 3 页获取到 100 条记录
2025-06-27 15:00:30,274 - INFO - Request Parameters - Page 4:
2025-06-27 15:00:30,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:30,274 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:30,743 - INFO - Response - Page 4:
2025-06-27 15:00:30,946 - INFO - 第 4 页获取到 100 条记录
2025-06-27 15:00:30,946 - INFO - Request Parameters - Page 5:
2025-06-27 15:00:30,946 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:30,946 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:31,477 - INFO - Response - Page 5:
2025-06-27 15:00:31,680 - INFO - 第 5 页获取到 100 条记录
2025-06-27 15:00:31,680 - INFO - Request Parameters - Page 6:
2025-06-27 15:00:31,680 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:31,680 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:32,196 - INFO - Response - Page 6:
2025-06-27 15:00:32,399 - INFO - 第 6 页获取到 100 条记录
2025-06-27 15:00:32,399 - INFO - Request Parameters - Page 7:
2025-06-27 15:00:32,399 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:32,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:32,837 - INFO - Response - Page 7:
2025-06-27 15:00:33,040 - INFO - 第 7 页获取到 65 条记录
2025-06-27 15:00:33,040 - INFO - 查询完成，共获取到 665 条记录
2025-06-27 15:00:33,040 - INFO - 获取到 665 条表单数据
2025-06-27 15:00:33,040 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-27 15:00:33,055 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 15:00:33,055 - INFO - 开始处理日期: 2025-06
2025-06-27 15:00:33,055 - INFO - Request Parameters - Page 1:
2025-06-27 15:00:33,055 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:33,055 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:33,602 - INFO - Response - Page 1:
2025-06-27 15:00:33,805 - INFO - 第 1 页获取到 100 条记录
2025-06-27 15:00:33,805 - INFO - Request Parameters - Page 2:
2025-06-27 15:00:33,805 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:33,805 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:34,274 - INFO - Response - Page 2:
2025-06-27 15:00:34,477 - INFO - 第 2 页获取到 100 条记录
2025-06-27 15:00:34,477 - INFO - Request Parameters - Page 3:
2025-06-27 15:00:34,477 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:34,477 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:35,071 - INFO - Response - Page 3:
2025-06-27 15:00:35,274 - INFO - 第 3 页获取到 100 条记录
2025-06-27 15:00:35,274 - INFO - Request Parameters - Page 4:
2025-06-27 15:00:35,274 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:35,274 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:35,805 - INFO - Response - Page 4:
2025-06-27 15:00:36,008 - INFO - 第 4 页获取到 100 条记录
2025-06-27 15:00:36,008 - INFO - Request Parameters - Page 5:
2025-06-27 15:00:36,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:36,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:36,493 - INFO - Response - Page 5:
2025-06-27 15:00:36,696 - INFO - 第 5 页获取到 100 条记录
2025-06-27 15:00:36,696 - INFO - Request Parameters - Page 6:
2025-06-27 15:00:36,696 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:36,696 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:37,227 - INFO - Response - Page 6:
2025-06-27 15:00:37,430 - INFO - 第 6 页获取到 100 条记录
2025-06-27 15:00:37,430 - INFO - Request Parameters - Page 7:
2025-06-27 15:00:37,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 15:00:37,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 15:00:37,758 - INFO - Response - Page 7:
2025-06-27 15:00:37,962 - INFO - 第 7 页获取到 29 条记录
2025-06-27 15:00:37,962 - INFO - 查询完成，共获取到 629 条记录
2025-06-27 15:00:37,962 - INFO - 获取到 629 条表单数据
2025-06-27 15:00:37,962 - INFO - 当前日期 2025-06 有 629 条MySQL数据需要处理
2025-06-27 15:00:37,962 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMUX
2025-06-27 15:00:38,430 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMUX
2025-06-27 15:00:38,430 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 154324.0, 'new_value': 159820.0}, {'field': 'total_amount', 'old_value': 547638.0, 'new_value': 553134.0}, {'field': 'order_count', 'old_value': 596, 'new_value': 616}]
2025-06-27 15:00:38,430 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMEY
2025-06-27 15:00:38,821 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMEY
2025-06-27 15:00:38,821 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 21946.0, 'new_value': 23118.0}, {'field': 'total_amount', 'old_value': 32546.0, 'new_value': 33718.0}, {'field': 'order_count', 'old_value': 13, 'new_value': 14}]
2025-06-27 15:00:38,821 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZY
2025-06-27 15:00:39,368 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMZY
2025-06-27 15:00:39,368 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 155513.19, 'new_value': 161828.45}, {'field': 'total_amount', 'old_value': 155513.19, 'new_value': 161828.45}, {'field': 'order_count', 'old_value': 1015, 'new_value': 1056}]
2025-06-27 15:00:39,368 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Z
2025-06-27 15:00:39,852 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM4Z
2025-06-27 15:00:39,852 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 532480.04, 'new_value': 556139.05}, {'field': 'total_amount', 'old_value': 532480.04, 'new_value': 556139.05}, {'field': 'order_count', 'old_value': 3847, 'new_value': 4078}]
2025-06-27 15:00:39,852 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM6Z
2025-06-27 15:00:40,274 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBM6Z
2025-06-27 15:00:40,274 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 881662.2, 'new_value': 920774.4}, {'field': 'total_amount', 'old_value': 966018.5, 'new_value': 1005130.7}, {'field': 'order_count', 'old_value': 84, 'new_value': 90}]
2025-06-27 15:00:40,274 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1C
2025-06-27 15:00:40,743 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBM1C
2025-06-27 15:00:40,743 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 495521.88, 'new_value': 518087.11}, {'field': 'total_amount', 'old_value': 495521.88, 'new_value': 518087.11}, {'field': 'order_count', 'old_value': 3782, 'new_value': 3960}]
2025-06-27 15:00:40,743 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDC
2025-06-27 15:00:41,196 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMDC
2025-06-27 15:00:41,196 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14789.8, 'new_value': 15149.2}, {'field': 'total_amount', 'old_value': 50520.6, 'new_value': 50880.0}, {'field': 'order_count', 'old_value': 166, 'new_value': 171}]
2025-06-27 15:00:41,196 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJC
2025-06-27 15:00:41,680 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJC
2025-06-27 15:00:41,680 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 186549.77, 'new_value': 193349.98}, {'field': 'offline_amount', 'old_value': 83911.95, 'new_value': 85730.95}, {'field': 'total_amount', 'old_value': 270461.72, 'new_value': 279080.93}, {'field': 'order_count', 'old_value': 1155, 'new_value': 1186}]
2025-06-27 15:00:41,680 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKC
2025-06-27 15:00:42,118 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMKC
2025-06-27 15:00:42,118 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 117616.0, 'new_value': 126014.0}, {'field': 'offline_amount', 'old_value': 262139.0, 'new_value': 266094.0}, {'field': 'total_amount', 'old_value': 379755.0, 'new_value': 392108.0}, {'field': 'order_count', 'old_value': 2822, 'new_value': 2923}]
2025-06-27 15:00:42,133 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMID
2025-06-27 15:00:42,571 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMID
2025-06-27 15:00:42,571 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 13856.2, 'new_value': 14443.04}, {'field': 'offline_amount', 'old_value': 210956.22, 'new_value': 218896.22}, {'field': 'total_amount', 'old_value': 224812.42, 'new_value': 233339.26}, {'field': 'order_count', 'old_value': 1503, 'new_value': 1562}]
2025-06-27 15:00:42,571 - INFO - 开始更新记录 - 表单实例ID: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJD
2025-06-27 15:00:43,024 - INFO - 更新表单数据成功: FINST-GRA66IC1ZC4WQQVCEDJFNCF6MLQ82RQ9NTOBMJD
2025-06-27 15:00:43,024 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2066.37, 'new_value': 2216.37}, {'field': 'offline_amount', 'old_value': 18179.3, 'new_value': 18849.68}, {'field': 'total_amount', 'old_value': 20245.67, 'new_value': 21066.05}, {'field': 'order_count', 'old_value': 396, 'new_value': 417}]
2025-06-27 15:00:43,024 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWR
2025-06-27 15:00:43,477 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMWR
2025-06-27 15:00:43,477 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 72309.69, 'new_value': 75564.08}, {'field': 'offline_amount', 'old_value': 88669.88, 'new_value': 96272.14}, {'field': 'total_amount', 'old_value': 160979.57, 'new_value': 171836.22}, {'field': 'order_count', 'old_value': 8377, 'new_value': 8848}]
2025-06-27 15:00:43,477 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIS
2025-06-27 15:00:44,133 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMIS
2025-06-27 15:00:44,133 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 574264.52, 'new_value': 597962.3}, {'field': 'total_amount', 'old_value': 574264.52, 'new_value': 597962.3}, {'field': 'order_count', 'old_value': 1854, 'new_value': 1931}]
2025-06-27 15:00:44,133 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFK
2025-06-27 15:00:44,555 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMFK
2025-06-27 15:00:44,555 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 68502.0, 'new_value': 73502.0}, {'field': 'offline_amount', 'old_value': 125630.0, 'new_value': 128450.0}, {'field': 'total_amount', 'old_value': 194132.0, 'new_value': 201952.0}, {'field': 'order_count', 'old_value': 4428, 'new_value': 4611}]
2025-06-27 15:00:44,555 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTK
2025-06-27 15:00:45,024 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMTK
2025-06-27 15:00:45,024 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 22866.0, 'new_value': 23474.0}, {'field': 'total_amount', 'old_value': 22866.0, 'new_value': 23474.0}, {'field': 'order_count', 'old_value': 214, 'new_value': 220}]
2025-06-27 15:00:45,024 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWK
2025-06-27 15:00:45,446 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMWK
2025-06-27 15:00:45,446 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 501972.03, 'new_value': 517986.99}, {'field': 'total_amount', 'old_value': 501972.03, 'new_value': 517986.99}, {'field': 'order_count', 'old_value': 5527, 'new_value': 5772}]
2025-06-27 15:00:45,446 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCL
2025-06-27 15:00:45,930 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMCL
2025-06-27 15:00:45,930 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 129861.37, 'new_value': 134659.92}, {'field': 'offline_amount', 'old_value': 341348.59, 'new_value': 356604.56}, {'field': 'total_amount', 'old_value': 471209.96, 'new_value': 491264.48}, {'field': 'order_count', 'old_value': 4638, 'new_value': 4831}]
2025-06-27 15:00:45,930 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYL
2025-06-27 15:00:46,352 - INFO - 更新表单数据成功: FINST-V7966QC1OS4WFOQCCF9647VLO1QV21VENTOBMYL
2025-06-27 15:00:46,352 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 38231.1, 'new_value': 39738.1}, {'field': 'total_amount', 'old_value': 38231.1, 'new_value': 39738.1}, {'field': 'order_count', 'old_value': 261, 'new_value': 272}]
2025-06-27 15:00:46,352 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM711
2025-06-27 15:00:46,790 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM711
2025-06-27 15:00:46,790 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 33122.0, 'new_value': 34531.0}, {'field': 'total_amount', 'old_value': 33122.0, 'new_value': 34531.0}, {'field': 'order_count', 'old_value': 99, 'new_value': 104}]
2025-06-27 15:00:46,790 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO11
2025-06-27 15:00:47,258 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMO11
2025-06-27 15:00:47,258 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 124176.0, 'new_value': 131265.0}, {'field': 'total_amount', 'old_value': 124344.0, 'new_value': 131433.0}, {'field': 'order_count', 'old_value': 364, 'new_value': 386}]
2025-06-27 15:00:47,258 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD21
2025-06-27 15:00:47,743 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMD21
2025-06-27 15:00:47,743 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19975.55, 'new_value': 20826.05}, {'field': 'offline_amount', 'old_value': 11655.84, 'new_value': 12191.84}, {'field': 'total_amount', 'old_value': 31631.39, 'new_value': 33017.89}, {'field': 'order_count', 'old_value': 1162, 'new_value': 1213}]
2025-06-27 15:00:47,758 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH2
2025-06-27 15:00:48,165 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMH2
2025-06-27 15:00:48,165 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2931897.55, 'new_value': 3042292.84}, {'field': 'total_amount', 'old_value': 2931897.55, 'new_value': 3042292.84}, {'field': 'order_count', 'old_value': 5529, 'new_value': 5771}]
2025-06-27 15:00:48,165 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ2
2025-06-27 15:00:48,665 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMJ2
2025-06-27 15:00:48,665 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 814941.0, 'new_value': 835384.0}, {'field': 'total_amount', 'old_value': 814941.0, 'new_value': 835384.0}, {'field': 'order_count', 'old_value': 4423, 'new_value': 4542}]
2025-06-27 15:00:48,665 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK2
2025-06-27 15:00:49,071 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMK2
2025-06-27 15:00:49,071 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9301763.73, 'new_value': 9572501.73}, {'field': 'total_amount', 'old_value': 9301763.73, 'new_value': 9572501.73}, {'field': 'order_count', 'old_value': 34845, 'new_value': 35874}]
2025-06-27 15:00:49,086 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML2
2025-06-27 15:00:49,540 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBML2
2025-06-27 15:00:49,540 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 169550.79, 'new_value': 177150.2}, {'field': 'total_amount', 'old_value': 169550.79, 'new_value': 177150.2}, {'field': 'order_count', 'old_value': 18656, 'new_value': 19522}]
2025-06-27 15:00:49,540 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM2
2025-06-27 15:00:50,008 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMM2
2025-06-27 15:00:50,008 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 183163.14, 'new_value': 190296.36}, {'field': 'offline_amount', 'old_value': 155322.46, 'new_value': 160972.0}, {'field': 'total_amount', 'old_value': 338485.6, 'new_value': 351268.36}, {'field': 'order_count', 'old_value': 14641, 'new_value': 15224}]
2025-06-27 15:00:50,008 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMUP
2025-06-27 15:00:50,508 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMUP
2025-06-27 15:00:50,508 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 185459.04, 'new_value': 194202.53}, {'field': 'total_amount', 'old_value': 185459.04, 'new_value': 194202.53}, {'field': 'order_count', 'old_value': 14014, 'new_value': 14680}]
2025-06-27 15:00:50,508 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMWP
2025-06-27 15:00:50,993 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMWP
2025-06-27 15:00:50,993 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 208567.24, 'new_value': 218657.24}, {'field': 'total_amount', 'old_value': 208567.24, 'new_value': 218657.24}, {'field': 'order_count', 'old_value': 13545, 'new_value': 13747}]
2025-06-27 15:00:50,993 - INFO - 日期 2025-06 处理完成 - 更新: 28 条，插入: 0 条，错误: 0 条
2025-06-27 15:00:50,993 - INFO - 数据同步完成！更新: 28 条，插入: 0 条，错误: 0 条
2025-06-27 15:00:50,993 - INFO - =================同步完成====================
2025-06-27 18:00:02,446 - INFO - =================使用默认全量同步=============
2025-06-27 18:00:04,196 - INFO - MySQL查询成功，共获取 3963 条记录
2025-06-27 18:00:04,196 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-27 18:00:04,227 - INFO - 开始处理日期: 2025-01
2025-06-27 18:00:04,227 - INFO - Request Parameters - Page 1:
2025-06-27 18:00:04,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:04,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:05,492 - INFO - Response - Page 1:
2025-06-27 18:00:05,696 - INFO - 第 1 页获取到 100 条记录
2025-06-27 18:00:05,696 - INFO - Request Parameters - Page 2:
2025-06-27 18:00:05,696 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:05,696 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:06,242 - INFO - Response - Page 2:
2025-06-27 18:00:06,446 - INFO - 第 2 页获取到 100 条记录
2025-06-27 18:00:06,446 - INFO - Request Parameters - Page 3:
2025-06-27 18:00:06,446 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:06,446 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:07,289 - INFO - Response - Page 3:
2025-06-27 18:00:07,492 - INFO - 第 3 页获取到 100 条记录
2025-06-27 18:00:07,492 - INFO - Request Parameters - Page 4:
2025-06-27 18:00:07,492 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:07,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:08,008 - INFO - Response - Page 4:
2025-06-27 18:00:08,211 - INFO - 第 4 页获取到 100 条记录
2025-06-27 18:00:08,211 - INFO - Request Parameters - Page 5:
2025-06-27 18:00:08,211 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:08,211 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:08,742 - INFO - Response - Page 5:
2025-06-27 18:00:08,946 - INFO - 第 5 页获取到 100 条记录
2025-06-27 18:00:08,946 - INFO - Request Parameters - Page 6:
2025-06-27 18:00:08,946 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:08,946 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:09,477 - INFO - Response - Page 6:
2025-06-27 18:00:09,680 - INFO - 第 6 页获取到 100 条记录
2025-06-27 18:00:09,680 - INFO - Request Parameters - Page 7:
2025-06-27 18:00:09,680 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:09,680 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:10,196 - INFO - Response - Page 7:
2025-06-27 18:00:10,399 - INFO - 第 7 页获取到 82 条记录
2025-06-27 18:00:10,399 - INFO - 查询完成，共获取到 682 条记录
2025-06-27 18:00:10,399 - INFO - 获取到 682 条表单数据
2025-06-27 18:00:10,414 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-27 18:00:10,430 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 18:00:10,430 - INFO - 开始处理日期: 2025-02
2025-06-27 18:00:10,430 - INFO - Request Parameters - Page 1:
2025-06-27 18:00:10,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:10,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:11,024 - INFO - Response - Page 1:
2025-06-27 18:00:11,227 - INFO - 第 1 页获取到 100 条记录
2025-06-27 18:00:11,227 - INFO - Request Parameters - Page 2:
2025-06-27 18:00:11,227 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:11,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:11,992 - INFO - Response - Page 2:
2025-06-27 18:00:12,196 - INFO - 第 2 页获取到 100 条记录
2025-06-27 18:00:12,196 - INFO - Request Parameters - Page 3:
2025-06-27 18:00:12,196 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:12,196 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:12,758 - INFO - Response - Page 3:
2025-06-27 18:00:12,961 - INFO - 第 3 页获取到 100 条记录
2025-06-27 18:00:12,961 - INFO - Request Parameters - Page 4:
2025-06-27 18:00:12,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:12,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:13,617 - INFO - Response - Page 4:
2025-06-27 18:00:13,821 - INFO - 第 4 页获取到 100 条记录
2025-06-27 18:00:13,821 - INFO - Request Parameters - Page 5:
2025-06-27 18:00:13,821 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:13,821 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:14,352 - INFO - Response - Page 5:
2025-06-27 18:00:14,555 - INFO - 第 5 页获取到 100 条记录
2025-06-27 18:00:14,555 - INFO - Request Parameters - Page 6:
2025-06-27 18:00:14,555 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:14,555 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:15,055 - INFO - Response - Page 6:
2025-06-27 18:00:15,258 - INFO - 第 6 页获取到 100 条记录
2025-06-27 18:00:15,258 - INFO - Request Parameters - Page 7:
2025-06-27 18:00:15,258 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:15,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:15,774 - INFO - Response - Page 7:
2025-06-27 18:00:15,977 - INFO - 第 7 页获取到 70 条记录
2025-06-27 18:00:15,977 - INFO - 查询完成，共获取到 670 条记录
2025-06-27 18:00:15,977 - INFO - 获取到 670 条表单数据
2025-06-27 18:00:15,977 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-27 18:00:15,992 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 18:00:15,992 - INFO - 开始处理日期: 2025-03
2025-06-27 18:00:15,992 - INFO - Request Parameters - Page 1:
2025-06-27 18:00:15,992 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:15,992 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:16,492 - INFO - Response - Page 1:
2025-06-27 18:00:16,696 - INFO - 第 1 页获取到 100 条记录
2025-06-27 18:00:16,696 - INFO - Request Parameters - Page 2:
2025-06-27 18:00:16,696 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:16,696 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:17,258 - INFO - Response - Page 2:
2025-06-27 18:00:17,461 - INFO - 第 2 页获取到 100 条记录
2025-06-27 18:00:17,461 - INFO - Request Parameters - Page 3:
2025-06-27 18:00:17,461 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:17,461 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:17,977 - INFO - Response - Page 3:
2025-06-27 18:00:18,180 - INFO - 第 3 页获取到 100 条记录
2025-06-27 18:00:18,180 - INFO - Request Parameters - Page 4:
2025-06-27 18:00:18,180 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:18,180 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:18,805 - INFO - Response - Page 4:
2025-06-27 18:00:19,008 - INFO - 第 4 页获取到 100 条记录
2025-06-27 18:00:19,008 - INFO - Request Parameters - Page 5:
2025-06-27 18:00:19,008 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:19,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:19,492 - INFO - Response - Page 5:
2025-06-27 18:00:19,696 - INFO - 第 5 页获取到 100 条记录
2025-06-27 18:00:19,696 - INFO - Request Parameters - Page 6:
2025-06-27 18:00:19,696 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:19,696 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:20,227 - INFO - Response - Page 6:
2025-06-27 18:00:20,430 - INFO - 第 6 页获取到 100 条记录
2025-06-27 18:00:20,430 - INFO - Request Parameters - Page 7:
2025-06-27 18:00:20,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:20,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:20,914 - INFO - Response - Page 7:
2025-06-27 18:00:21,117 - INFO - 第 7 页获取到 61 条记录
2025-06-27 18:00:21,117 - INFO - 查询完成，共获取到 661 条记录
2025-06-27 18:00:21,117 - INFO - 获取到 661 条表单数据
2025-06-27 18:00:21,117 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-27 18:00:21,133 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 18:00:21,133 - INFO - 开始处理日期: 2025-04
2025-06-27 18:00:21,133 - INFO - Request Parameters - Page 1:
2025-06-27 18:00:21,133 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:21,133 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:21,758 - INFO - Response - Page 1:
2025-06-27 18:00:21,961 - INFO - 第 1 页获取到 100 条记录
2025-06-27 18:00:21,961 - INFO - Request Parameters - Page 2:
2025-06-27 18:00:21,961 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:21,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:22,586 - INFO - Response - Page 2:
2025-06-27 18:00:22,789 - INFO - 第 2 页获取到 100 条记录
2025-06-27 18:00:22,789 - INFO - Request Parameters - Page 3:
2025-06-27 18:00:22,789 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:22,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:23,321 - INFO - Response - Page 3:
2025-06-27 18:00:23,524 - INFO - 第 3 页获取到 100 条记录
2025-06-27 18:00:23,524 - INFO - Request Parameters - Page 4:
2025-06-27 18:00:23,524 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:23,524 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:24,102 - INFO - Response - Page 4:
2025-06-27 18:00:24,305 - INFO - 第 4 页获取到 100 条记录
2025-06-27 18:00:24,305 - INFO - Request Parameters - Page 5:
2025-06-27 18:00:24,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:24,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:24,789 - INFO - Response - Page 5:
2025-06-27 18:00:24,992 - INFO - 第 5 页获取到 100 条记录
2025-06-27 18:00:24,992 - INFO - Request Parameters - Page 6:
2025-06-27 18:00:24,992 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:24,992 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:25,617 - INFO - Response - Page 6:
2025-06-27 18:00:25,820 - INFO - 第 6 页获取到 100 条记录
2025-06-27 18:00:25,820 - INFO - Request Parameters - Page 7:
2025-06-27 18:00:25,820 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:25,820 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:26,289 - INFO - Response - Page 7:
2025-06-27 18:00:26,492 - INFO - 第 7 页获取到 56 条记录
2025-06-27 18:00:26,492 - INFO - 查询完成，共获取到 656 条记录
2025-06-27 18:00:26,492 - INFO - 获取到 656 条表单数据
2025-06-27 18:00:26,492 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-27 18:00:26,508 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 18:00:26,508 - INFO - 开始处理日期: 2025-05
2025-06-27 18:00:26,508 - INFO - Request Parameters - Page 1:
2025-06-27 18:00:26,508 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:26,508 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:27,102 - INFO - Response - Page 1:
2025-06-27 18:00:27,305 - INFO - 第 1 页获取到 100 条记录
2025-06-27 18:00:27,305 - INFO - Request Parameters - Page 2:
2025-06-27 18:00:27,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:27,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:27,992 - INFO - Response - Page 2:
2025-06-27 18:00:28,195 - INFO - 第 2 页获取到 100 条记录
2025-06-27 18:00:28,195 - INFO - Request Parameters - Page 3:
2025-06-27 18:00:28,195 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:28,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:28,789 - INFO - Response - Page 3:
2025-06-27 18:00:28,992 - INFO - 第 3 页获取到 100 条记录
2025-06-27 18:00:28,992 - INFO - Request Parameters - Page 4:
2025-06-27 18:00:28,992 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:28,992 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:29,492 - INFO - Response - Page 4:
2025-06-27 18:00:29,695 - INFO - 第 4 页获取到 100 条记录
2025-06-27 18:00:29,695 - INFO - Request Parameters - Page 5:
2025-06-27 18:00:29,695 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:29,695 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:30,227 - INFO - Response - Page 5:
2025-06-27 18:00:30,430 - INFO - 第 5 页获取到 100 条记录
2025-06-27 18:00:30,430 - INFO - Request Parameters - Page 6:
2025-06-27 18:00:30,430 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:30,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:30,930 - INFO - Response - Page 6:
2025-06-27 18:00:31,133 - INFO - 第 6 页获取到 100 条记录
2025-06-27 18:00:31,133 - INFO - Request Parameters - Page 7:
2025-06-27 18:00:31,133 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:31,133 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:31,649 - INFO - Response - Page 7:
2025-06-27 18:00:31,852 - INFO - 第 7 页获取到 65 条记录
2025-06-27 18:00:31,852 - INFO - 查询完成，共获取到 665 条记录
2025-06-27 18:00:31,852 - INFO - 获取到 665 条表单数据
2025-06-27 18:00:31,852 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-27 18:00:31,867 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 18:00:31,867 - INFO - 开始处理日期: 2025-06
2025-06-27 18:00:31,867 - INFO - Request Parameters - Page 1:
2025-06-27 18:00:31,867 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:31,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:32,367 - INFO - Response - Page 1:
2025-06-27 18:00:32,570 - INFO - 第 1 页获取到 100 条记录
2025-06-27 18:00:32,570 - INFO - Request Parameters - Page 2:
2025-06-27 18:00:32,570 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:32,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:33,102 - INFO - Response - Page 2:
2025-06-27 18:00:33,305 - INFO - 第 2 页获取到 100 条记录
2025-06-27 18:00:33,305 - INFO - Request Parameters - Page 3:
2025-06-27 18:00:33,305 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:33,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:33,852 - INFO - Response - Page 3:
2025-06-27 18:00:34,055 - INFO - 第 3 页获取到 100 条记录
2025-06-27 18:00:34,055 - INFO - Request Parameters - Page 4:
2025-06-27 18:00:34,055 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:34,055 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:34,633 - INFO - Response - Page 4:
2025-06-27 18:00:34,836 - INFO - 第 4 页获取到 100 条记录
2025-06-27 18:00:34,836 - INFO - Request Parameters - Page 5:
2025-06-27 18:00:34,836 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:34,836 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:35,461 - INFO - Response - Page 5:
2025-06-27 18:00:35,664 - INFO - 第 5 页获取到 100 条记录
2025-06-27 18:00:35,664 - INFO - Request Parameters - Page 6:
2025-06-27 18:00:35,664 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:35,664 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:36,180 - INFO - Response - Page 6:
2025-06-27 18:00:36,383 - INFO - 第 6 页获取到 100 条记录
2025-06-27 18:00:36,383 - INFO - Request Parameters - Page 7:
2025-06-27 18:00:36,383 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 18:00:36,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 18:00:36,742 - INFO - Response - Page 7:
2025-06-27 18:00:36,945 - INFO - 第 7 页获取到 29 条记录
2025-06-27 18:00:36,945 - INFO - 查询完成，共获取到 629 条记录
2025-06-27 18:00:36,945 - INFO - 获取到 629 条表单数据
2025-06-27 18:00:36,945 - INFO - 当前日期 2025-06 有 629 条MySQL数据需要处理
2025-06-27 18:00:36,945 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMOX
2025-06-27 18:00:37,430 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23587NTOBMOX
2025-06-27 18:00:37,430 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 29823.3, 'new_value': 30796.5}, {'field': 'total_amount', 'old_value': 29823.3, 'new_value': 30796.5}, {'field': 'order_count', 'old_value': 172, 'new_value': 177}]
2025-06-27 18:00:37,430 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMWZ
2025-06-27 18:00:38,086 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMWZ
2025-06-27 18:00:38,086 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6263.0, 'new_value': 5263.0}, {'field': 'total_amount', 'old_value': 14631.0, 'new_value': 13631.0}, {'field': 'order_count', 'old_value': 85, 'new_value': 82}]
2025-06-27 18:00:38,102 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1R
2025-06-27 18:00:38,602 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBM1R
2025-06-27 18:00:38,602 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 160792.2, 'new_value': 156447.4}, {'field': 'total_amount', 'old_value': 188450.0, 'new_value': 184105.2}, {'field': 'order_count', 'old_value': 1694, 'new_value': 1661}]
2025-06-27 18:00:38,602 - INFO - 开始更新记录 - 表单实例ID: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDT
2025-06-27 18:00:39,086 - INFO - 更新表单数据成功: FINST-N79668C1ZS2WMKS36P8CNDSM5HEZ1FBCNTOBMDT
2025-06-27 18:00:39,086 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 269378.4, 'new_value': 295308.4}, {'field': 'total_amount', 'old_value': 269378.4, 'new_value': 295308.4}, {'field': 'order_count', 'old_value': 54, 'new_value': 55}]
2025-06-27 18:00:39,086 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK11
2025-06-27 18:00:39,586 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMK11
2025-06-27 18:00:39,602 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 36133.62, 'new_value': 36654.73}, {'field': 'total_amount', 'old_value': 36133.62, 'new_value': 36654.73}, {'field': 'order_count', 'old_value': 780, 'new_value': 806}]
2025-06-27 18:00:39,602 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMM21
2025-06-27 18:00:40,039 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBMM21
2025-06-27 18:00:40,039 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 80504.0, 'new_value': 89624.0}, {'field': 'total_amount', 'old_value': 80504.0, 'new_value': 89624.0}, {'field': 'order_count', 'old_value': 8, 'new_value': 9}]
2025-06-27 18:00:40,039 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM231
2025-06-27 18:00:40,508 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM231
2025-06-27 18:00:40,508 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 51421.58, 'new_value': 50714.04}, {'field': 'total_amount', 'old_value': 129651.33, 'new_value': 128943.79}, {'field': 'order_count', 'old_value': 7408, 'new_value': 7679}]
2025-06-27 18:00:40,508 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMV1
2025-06-27 18:00:40,992 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMV1
2025-06-27 18:00:40,992 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 76809.49, 'new_value': 73130.51}, {'field': 'total_amount', 'old_value': 76809.49, 'new_value': 73130.51}, {'field': 'order_count', 'old_value': 2878, 'new_value': 2904}]
2025-06-27 18:00:40,992 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMZ1
2025-06-27 18:00:41,445 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMZ1
2025-06-27 18:00:41,445 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 160038.0, 'new_value': 157649.0}, {'field': 'total_amount', 'old_value': 160038.0, 'new_value': 157649.0}, {'field': 'order_count', 'old_value': 13717, 'new_value': 14122}]
2025-06-27 18:00:41,445 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA2
2025-06-27 18:00:41,883 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA2
2025-06-27 18:00:41,883 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 14446.0, 'new_value': 14845.0}, {'field': 'total_amount', 'old_value': 16645.0, 'new_value': 17044.0}, {'field': 'order_count', 'old_value': 37, 'new_value': 38}]
2025-06-27 18:00:41,883 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW2
2025-06-27 18:00:42,305 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMW2
2025-06-27 18:00:42,305 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 52987.1, 'new_value': 51802.1}, {'field': 'total_amount', 'old_value': 52987.1, 'new_value': 51802.1}, {'field': 'order_count', 'old_value': 4072, 'new_value': 4268}]
2025-06-27 18:00:42,305 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM13
2025-06-27 18:00:42,852 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM13
2025-06-27 18:00:42,852 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 155472.0, 'new_value': 160272.0}, {'field': 'total_amount', 'old_value': 155472.0, 'new_value': 160272.0}, {'field': 'order_count', 'old_value': 18, 'new_value': 19}]
2025-06-27 18:00:42,852 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM33
2025-06-27 18:00:43,242 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM33
2025-06-27 18:00:43,242 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54045.0, 'new_value': 52245.0}, {'field': 'total_amount', 'old_value': 54045.0, 'new_value': 52245.0}, {'field': 'order_count', 'old_value': 46, 'new_value': 45}]
2025-06-27 18:00:43,242 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM63
2025-06-27 18:00:43,711 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM63
2025-06-27 18:00:43,711 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 264265.04, 'new_value': 267485.04}, {'field': 'total_amount', 'old_value': 264265.04, 'new_value': 267485.04}, {'field': 'order_count', 'old_value': 103, 'new_value': 104}]
2025-06-27 18:00:43,727 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM73
2025-06-27 18:00:44,180 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBM73
2025-06-27 18:00:44,180 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 19983.0, 'new_value': 21151.0}, {'field': 'total_amount', 'old_value': 19983.0, 'new_value': 21151.0}, {'field': 'order_count', 'old_value': 24, 'new_value': 25}]
2025-06-27 18:00:44,180 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA3
2025-06-27 18:00:44,664 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMA3
2025-06-27 18:00:44,664 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 161000.0, 'new_value': 154000.0}, {'field': 'total_amount', 'old_value': 161000.0, 'new_value': 154000.0}, {'field': 'order_count', 'old_value': 23, 'new_value': 22}]
2025-06-27 18:00:44,680 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMB3
2025-06-27 18:00:45,117 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMB3
2025-06-27 18:00:45,117 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 12150.0, 'new_value': 14049.0}, {'field': 'total_amount', 'old_value': 12150.0, 'new_value': 14049.0}, {'field': 'order_count', 'old_value': 6, 'new_value': 7}]
2025-06-27 18:00:45,117 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC3
2025-06-27 18:00:45,680 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMC3
2025-06-27 18:00:45,680 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 128388.0, 'new_value': 128938.0}, {'field': 'total_amount', 'old_value': 128388.0, 'new_value': 128938.0}, {'field': 'order_count', 'old_value': 38, 'new_value': 39}]
2025-06-27 18:00:45,680 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG3
2025-06-27 18:00:46,227 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3U5KNTOBMG3
2025-06-27 18:00:46,227 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 104386.0, 'new_value': 101548.0}, {'field': 'total_amount', 'old_value': 104386.0, 'new_value': 101548.0}, {'field': 'order_count', 'old_value': 732, 'new_value': 725}]
2025-06-27 18:00:46,227 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMO3
2025-06-27 18:00:46,633 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMO3
2025-06-27 18:00:46,633 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 278410.35, 'new_value': 277480.43}, {'field': 'total_amount', 'old_value': 278410.35, 'new_value': 277480.43}, {'field': 'order_count', 'old_value': 2064, 'new_value': 2061}]
2025-06-27 18:00:46,633 - INFO - 开始更新记录 - 表单实例ID: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMR3
2025-06-27 18:00:47,133 - INFO - 更新表单数据成功: FINST-W3B66L71RK2WCAQOCX08C7LP3E3T3V5KNTOBMR3
2025-06-27 18:00:47,133 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41003.0, 'new_value': 41808.0}, {'field': 'total_amount', 'old_value': 41003.0, 'new_value': 41808.0}, {'field': 'order_count', 'old_value': 41, 'new_value': 42}]
2025-06-27 18:00:47,133 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMMP
2025-06-27 18:00:47,617 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMMP
2025-06-27 18:00:47,617 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 89104.0, 'new_value': 83804.0}, {'field': 'total_amount', 'old_value': 89104.0, 'new_value': 83804.0}, {'field': 'order_count', 'old_value': 36, 'new_value': 34}]
2025-06-27 18:00:47,617 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMOP
2025-06-27 18:00:48,055 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMOP
2025-06-27 18:00:48,055 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 215617.22, 'new_value': 222131.12}, {'field': 'total_amount', 'old_value': 215617.22, 'new_value': 222131.12}, {'field': 'order_count', 'old_value': 392, 'new_value': 411}]
2025-06-27 18:00:48,055 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMQP
2025-06-27 18:00:48,523 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMQP
2025-06-27 18:00:48,523 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9879.5, 'new_value': 9595.5}, {'field': 'total_amount', 'old_value': 9879.5, 'new_value': 9595.5}, {'field': 'order_count', 'old_value': 67, 'new_value': 69}]
2025-06-27 18:00:48,523 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMSP
2025-06-27 18:00:48,977 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMSP
2025-06-27 18:00:48,977 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 426357.0, 'new_value': 420357.0}, {'field': 'total_amount', 'old_value': 426357.0, 'new_value': 420357.0}, {'field': 'order_count', 'old_value': 496, 'new_value': 491}]
2025-06-27 18:00:48,977 - INFO - 开始更新记录 - 表单实例ID: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMXP
2025-06-27 18:00:49,555 - INFO - 更新表单数据成功: FINST-PPA666718C2W22Z5FQKHDCAY4RGI2WMMNTOBMXP
2025-06-27 18:00:49,555 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 577000.0, 'new_value': 554488.0}, {'field': 'total_amount', 'old_value': 577000.0, 'new_value': 554488.0}]
2025-06-27 18:00:49,555 - INFO - 开始更新记录 - 表单实例ID: FINST-EWE66Z91Q5NWVLK1C5VXFAUHTRM73VWGEAECML9
2025-06-27 18:00:50,055 - INFO - 更新表单数据成功: FINST-EWE66Z91Q5NWVLK1C5VXFAUHTRM73VWGEAECML9
2025-06-27 18:00:50,055 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 114.0, 'new_value': 2000.0}, {'field': 'total_amount', 'old_value': 114.0, 'new_value': 2000.0}, {'field': 'order_count', 'old_value': 3, 'new_value': 1}]
2025-06-27 18:00:50,055 - INFO - 日期 2025-06 处理完成 - 更新: 27 条，插入: 0 条，错误: 0 条
2025-06-27 18:00:50,055 - INFO - 数据同步完成！更新: 27 条，插入: 0 条，错误: 0 条
2025-06-27 18:00:50,055 - INFO - =================同步完成====================
2025-06-27 21:00:03,854 - INFO - =================使用默认全量同步=============
2025-06-27 21:00:05,636 - INFO - MySQL查询成功，共获取 3963 条记录
2025-06-27 21:00:05,636 - INFO - 获取到 6 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06']
2025-06-27 21:00:05,667 - INFO - 开始处理日期: 2025-01
2025-06-27 21:00:05,667 - INFO - Request Parameters - Page 1:
2025-06-27 21:00:05,667 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:05,667 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:07,043 - INFO - Response - Page 1:
2025-06-27 21:00:07,246 - INFO - 第 1 页获取到 100 条记录
2025-06-27 21:00:07,246 - INFO - Request Parameters - Page 2:
2025-06-27 21:00:07,246 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:07,246 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:07,746 - INFO - Response - Page 2:
2025-06-27 21:00:07,949 - INFO - 第 2 页获取到 100 条记录
2025-06-27 21:00:07,949 - INFO - Request Parameters - Page 3:
2025-06-27 21:00:07,949 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:07,949 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:08,528 - INFO - Response - Page 3:
2025-06-27 21:00:08,731 - INFO - 第 3 页获取到 100 条记录
2025-06-27 21:00:08,731 - INFO - Request Parameters - Page 4:
2025-06-27 21:00:08,731 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:08,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:09,325 - INFO - Response - Page 4:
2025-06-27 21:00:09,528 - INFO - 第 4 页获取到 100 条记录
2025-06-27 21:00:09,528 - INFO - Request Parameters - Page 5:
2025-06-27 21:00:09,528 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:09,528 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:10,013 - INFO - Response - Page 5:
2025-06-27 21:00:10,216 - INFO - 第 5 页获取到 100 条记录
2025-06-27 21:00:10,216 - INFO - Request Parameters - Page 6:
2025-06-27 21:00:10,216 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:10,216 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:10,701 - INFO - Response - Page 6:
2025-06-27 21:00:10,904 - INFO - 第 6 页获取到 100 条记录
2025-06-27 21:00:10,904 - INFO - Request Parameters - Page 7:
2025-06-27 21:00:10,904 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:10,904 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:11,420 - INFO - Response - Page 7:
2025-06-27 21:00:11,623 - INFO - 第 7 页获取到 82 条记录
2025-06-27 21:00:11,623 - INFO - 查询完成，共获取到 682 条记录
2025-06-27 21:00:11,623 - INFO - 获取到 682 条表单数据
2025-06-27 21:00:11,623 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-06-27 21:00:11,638 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 21:00:11,638 - INFO - 开始处理日期: 2025-02
2025-06-27 21:00:11,638 - INFO - Request Parameters - Page 1:
2025-06-27 21:00:11,638 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:11,638 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:12,107 - INFO - Response - Page 1:
2025-06-27 21:00:12,311 - INFO - 第 1 页获取到 100 条记录
2025-06-27 21:00:12,311 - INFO - Request Parameters - Page 2:
2025-06-27 21:00:12,311 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:12,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:12,858 - INFO - Response - Page 2:
2025-06-27 21:00:13,061 - INFO - 第 2 页获取到 100 条记录
2025-06-27 21:00:13,061 - INFO - Request Parameters - Page 3:
2025-06-27 21:00:13,061 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:13,061 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:13,608 - INFO - Response - Page 3:
2025-06-27 21:00:13,811 - INFO - 第 3 页获取到 100 条记录
2025-06-27 21:00:13,811 - INFO - Request Parameters - Page 4:
2025-06-27 21:00:13,811 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:13,811 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:14,280 - INFO - Response - Page 4:
2025-06-27 21:00:14,483 - INFO - 第 4 页获取到 100 条记录
2025-06-27 21:00:14,483 - INFO - Request Parameters - Page 5:
2025-06-27 21:00:14,483 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:14,483 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:15,015 - INFO - Response - Page 5:
2025-06-27 21:00:15,218 - INFO - 第 5 页获取到 100 条记录
2025-06-27 21:00:15,218 - INFO - Request Parameters - Page 6:
2025-06-27 21:00:15,218 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:15,218 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:15,703 - INFO - Response - Page 6:
2025-06-27 21:00:15,906 - INFO - 第 6 页获取到 100 条记录
2025-06-27 21:00:15,906 - INFO - Request Parameters - Page 7:
2025-06-27 21:00:15,906 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:15,906 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:16,390 - INFO - Response - Page 7:
2025-06-27 21:00:16,593 - INFO - 第 7 页获取到 70 条记录
2025-06-27 21:00:16,593 - INFO - 查询完成，共获取到 670 条记录
2025-06-27 21:00:16,593 - INFO - 获取到 670 条表单数据
2025-06-27 21:00:16,593 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-06-27 21:00:16,609 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 21:00:16,609 - INFO - 开始处理日期: 2025-03
2025-06-27 21:00:16,609 - INFO - Request Parameters - Page 1:
2025-06-27 21:00:16,609 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:16,609 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:17,125 - INFO - Response - Page 1:
2025-06-27 21:00:17,328 - INFO - 第 1 页获取到 100 条记录
2025-06-27 21:00:17,328 - INFO - Request Parameters - Page 2:
2025-06-27 21:00:17,328 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:17,328 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:17,875 - INFO - Response - Page 2:
2025-06-27 21:00:18,078 - INFO - 第 2 页获取到 100 条记录
2025-06-27 21:00:18,078 - INFO - Request Parameters - Page 3:
2025-06-27 21:00:18,078 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:18,078 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:18,579 - INFO - Response - Page 3:
2025-06-27 21:00:18,782 - INFO - 第 3 页获取到 100 条记录
2025-06-27 21:00:18,782 - INFO - Request Parameters - Page 4:
2025-06-27 21:00:18,782 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:18,782 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:19,266 - INFO - Response - Page 4:
2025-06-27 21:00:19,470 - INFO - 第 4 页获取到 100 条记录
2025-06-27 21:00:19,470 - INFO - Request Parameters - Page 5:
2025-06-27 21:00:19,470 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:19,470 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:20,017 - INFO - Response - Page 5:
2025-06-27 21:00:20,220 - INFO - 第 5 页获取到 100 条记录
2025-06-27 21:00:20,220 - INFO - Request Parameters - Page 6:
2025-06-27 21:00:20,220 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:20,220 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:20,798 - INFO - Response - Page 6:
2025-06-27 21:00:21,017 - INFO - 第 6 页获取到 100 条记录
2025-06-27 21:00:21,017 - INFO - Request Parameters - Page 7:
2025-06-27 21:00:21,017 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:21,017 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:21,502 - INFO - Response - Page 7:
2025-06-27 21:00:21,705 - INFO - 第 7 页获取到 61 条记录
2025-06-27 21:00:21,705 - INFO - 查询完成，共获取到 661 条记录
2025-06-27 21:00:21,705 - INFO - 获取到 661 条表单数据
2025-06-27 21:00:21,705 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-06-27 21:00:21,721 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 21:00:21,721 - INFO - 开始处理日期: 2025-04
2025-06-27 21:00:21,721 - INFO - Request Parameters - Page 1:
2025-06-27 21:00:21,721 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:21,721 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:22,236 - INFO - Response - Page 1:
2025-06-27 21:00:22,440 - INFO - 第 1 页获取到 100 条记录
2025-06-27 21:00:22,440 - INFO - Request Parameters - Page 2:
2025-06-27 21:00:22,440 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:22,440 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:23,018 - INFO - Response - Page 2:
2025-06-27 21:00:23,221 - INFO - 第 2 页获取到 100 条记录
2025-06-27 21:00:23,221 - INFO - Request Parameters - Page 3:
2025-06-27 21:00:23,221 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:23,221 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:23,721 - INFO - Response - Page 3:
2025-06-27 21:00:23,925 - INFO - 第 3 页获取到 100 条记录
2025-06-27 21:00:23,925 - INFO - Request Parameters - Page 4:
2025-06-27 21:00:23,925 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:23,925 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:24,487 - INFO - Response - Page 4:
2025-06-27 21:00:24,690 - INFO - 第 4 页获取到 100 条记录
2025-06-27 21:00:24,690 - INFO - Request Parameters - Page 5:
2025-06-27 21:00:24,690 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:24,690 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:25,191 - INFO - Response - Page 5:
2025-06-27 21:00:25,394 - INFO - 第 5 页获取到 100 条记录
2025-06-27 21:00:25,394 - INFO - Request Parameters - Page 6:
2025-06-27 21:00:25,394 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:25,394 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:25,910 - INFO - Response - Page 6:
2025-06-27 21:00:26,113 - INFO - 第 6 页获取到 100 条记录
2025-06-27 21:00:26,113 - INFO - Request Parameters - Page 7:
2025-06-27 21:00:26,113 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:26,113 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:26,535 - INFO - Response - Page 7:
2025-06-27 21:00:26,738 - INFO - 第 7 页获取到 56 条记录
2025-06-27 21:00:26,738 - INFO - 查询完成，共获取到 656 条记录
2025-06-27 21:00:26,738 - INFO - 获取到 656 条表单数据
2025-06-27 21:00:26,738 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-06-27 21:00:26,754 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 21:00:26,754 - INFO - 开始处理日期: 2025-05
2025-06-27 21:00:26,754 - INFO - Request Parameters - Page 1:
2025-06-27 21:00:26,754 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:26,754 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:27,270 - INFO - Response - Page 1:
2025-06-27 21:00:27,473 - INFO - 第 1 页获取到 100 条记录
2025-06-27 21:00:27,473 - INFO - Request Parameters - Page 2:
2025-06-27 21:00:27,473 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:27,473 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:27,989 - INFO - Response - Page 2:
2025-06-27 21:00:28,192 - INFO - 第 2 页获取到 100 条记录
2025-06-27 21:00:28,192 - INFO - Request Parameters - Page 3:
2025-06-27 21:00:28,192 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:28,192 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:28,708 - INFO - Response - Page 3:
2025-06-27 21:00:28,911 - INFO - 第 3 页获取到 100 条记录
2025-06-27 21:00:28,911 - INFO - Request Parameters - Page 4:
2025-06-27 21:00:28,911 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:28,911 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:29,395 - INFO - Response - Page 4:
2025-06-27 21:00:29,599 - INFO - 第 4 页获取到 100 条记录
2025-06-27 21:00:29,599 - INFO - Request Parameters - Page 5:
2025-06-27 21:00:29,599 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:29,599 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:30,161 - INFO - Response - Page 5:
2025-06-27 21:00:30,365 - INFO - 第 5 页获取到 100 条记录
2025-06-27 21:00:30,365 - INFO - Request Parameters - Page 6:
2025-06-27 21:00:30,365 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:30,365 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:30,802 - INFO - Response - Page 6:
2025-06-27 21:00:31,005 - INFO - 第 6 页获取到 100 条记录
2025-06-27 21:00:31,005 - INFO - Request Parameters - Page 7:
2025-06-27 21:00:31,005 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:31,005 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:31,537 - INFO - Response - Page 7:
2025-06-27 21:00:31,740 - INFO - 第 7 页获取到 65 条记录
2025-06-27 21:00:31,740 - INFO - 查询完成，共获取到 665 条记录
2025-06-27 21:00:31,740 - INFO - 获取到 665 条表单数据
2025-06-27 21:00:31,740 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-06-27 21:00:31,756 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-06-27 21:00:31,756 - INFO - 开始处理日期: 2025-06
2025-06-27 21:00:31,756 - INFO - Request Parameters - Page 1:
2025-06-27 21:00:31,756 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:31,756 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:32,334 - INFO - Response - Page 1:
2025-06-27 21:00:32,537 - INFO - 第 1 页获取到 100 条记录
2025-06-27 21:00:32,537 - INFO - Request Parameters - Page 2:
2025-06-27 21:00:32,537 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:32,537 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:33,069 - INFO - Response - Page 2:
2025-06-27 21:00:33,272 - INFO - 第 2 页获取到 100 条记录
2025-06-27 21:00:33,272 - INFO - Request Parameters - Page 3:
2025-06-27 21:00:33,272 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:33,272 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:33,757 - INFO - Response - Page 3:
2025-06-27 21:00:33,960 - INFO - 第 3 页获取到 100 条记录
2025-06-27 21:00:33,960 - INFO - Request Parameters - Page 4:
2025-06-27 21:00:33,960 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:33,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:34,429 - INFO - Response - Page 4:
2025-06-27 21:00:34,632 - INFO - 第 4 页获取到 100 条记录
2025-06-27 21:00:34,632 - INFO - Request Parameters - Page 5:
2025-06-27 21:00:34,632 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:34,632 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:35,132 - INFO - Response - Page 5:
2025-06-27 21:00:35,335 - INFO - 第 5 页获取到 100 条记录
2025-06-27 21:00:35,335 - INFO - Request Parameters - Page 6:
2025-06-27 21:00:35,335 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:35,335 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:35,820 - INFO - Response - Page 6:
2025-06-27 21:00:36,023 - INFO - 第 6 页获取到 100 条记录
2025-06-27 21:00:36,023 - INFO - Request Parameters - Page 7:
2025-06-27 21:00:36,023 - INFO - Headers: {'x-acs-dingtalk-access-token': 'f6c7f093cdd03d5698ece5d126411f71'}
2025-06-27 21:00:36,023 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-06-27 21:00:36,351 - INFO - Response - Page 7:
2025-06-27 21:00:36,555 - INFO - 第 7 页获取到 29 条记录
2025-06-27 21:00:36,555 - INFO - 查询完成，共获取到 629 条记录
2025-06-27 21:00:36,555 - INFO - 获取到 629 条表单数据
2025-06-27 21:00:36,555 - INFO - 当前日期 2025-06 有 629 条MySQL数据需要处理
2025-06-27 21:00:36,555 - INFO - 开始更新记录 - 表单实例ID: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYY
2025-06-27 21:00:37,039 - INFO - 更新表单数据成功: FINST-HJ966H81NB2WAM8KCZ0ZEBW3ETP23687NTOBMYY
2025-06-27 21:00:37,039 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 127359.22, 'new_value': 127897.95}, {'field': 'total_amount', 'old_value': 127359.22, 'new_value': 127897.95}, {'field': 'order_count', 'old_value': 249, 'new_value': 261}]
2025-06-27 21:00:37,039 - INFO - 开始更新记录 - 表单实例ID: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM131
2025-06-27 21:00:37,492 - INFO - 更新表单数据成功: FINST-ZNE66RC12V2W10XGFC0T0DA6J3DD2LEHNTOBM131
2025-06-27 21:00:37,492 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 712120.4, 'new_value': 728254.14}, {'field': 'total_amount', 'old_value': 712120.4, 'new_value': 728254.14}, {'field': 'order_count', 'old_value': 4504, 'new_value': 4561}]
2025-06-27 21:00:37,492 - INFO - 日期 2025-06 处理完成 - 更新: 2 条，插入: 0 条，错误: 0 条
2025-06-27 21:00:37,492 - INFO - 数据同步完成！更新: 2 条，插入: 0 条，错误: 0 条
2025-06-27 21:00:37,508 - INFO - =================同步完成====================
